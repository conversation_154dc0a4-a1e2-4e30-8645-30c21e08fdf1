package com.ruoyi.common.utils;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/7/29
 */
public class YGUtils {
    public static boolean containsChinese(String str) {
        // 定义匹配中文的正则表达式
        String regex = "[\\u4e00-\\u9fa5]";
        // 使用 matches 方法结合正则表达式进行匹配
        return str.matches(".*" + regex + ".*");
    }
    private static boolean isValidPhoneNumber(String phoneNumber) {
        // 正则表达式：1开头，后面跟10位数字（总共11位）
        String regex = "^1\\d{10}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(phoneNumber);
        return matcher.matches();
    }

    private  static void getShipmentEquipments(){
        String token = "0b33f5454a4f41e9857a7d4d77993486";

        SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.MONTH,-3);
        String dayStr = sdfDay.format(cal.getTime());

        String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentInfos?defsort=CREATED_DATE%20DESC";
        Map<String,Object> listParams = new HashMap<>();
        listParams.put("_search","true");
        listParams.put("rows","200");
        listParams.put("page","1");
        listParams.put("sord","asc");
        listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"STATUS\",\"op\":\"eq\",\"data\":\"40\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
        String listRes = HttpRequest.post(listUrl)
                .header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
                .header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                .form(listParams).execute().body();
        JSONObject listJson = JSONObject.parseObject(listRes);
        JSONArray tableRows = listJson.getJSONArray("rows");
        for(int j = 0 ; j < tableRows.size() ; j++){
            String gid = tableRows.getJSONObject(j).getString("id");

            Map<String,Object> goodsParams = new HashMap<>();
            goodsParams.put("_search","false");
            goodsParams.put("rows","200");
            goodsParams.put("page","1");
            goodsParams.put("sord","asc");
            String goodsUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentEquipments?gid="+gid+"&type=&defsort=LINE_ID%20Asc";
            String goodsRes = HttpRequest.post(goodsUrl)
                    .header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(goodsParams).execute().body();
            System.out.println(goodsRes);
            JSONObject goodsResObj = JSONObject.parseObject(goodsRes);
            JSONArray rows = goodsResObj.getJSONArray("rows");
            for(int i = 0 ; i <rows.size() ; i++){
                String carNo = rows.getJSONObject(i).getJSONArray("cell").getString(1);
                System.out.println(carNo);
            }
        }

    }
    public static void main(String[] args) {
        getShipmentEquipments();
        //联系人联系方式
//        String uploadExcelURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/SaveImportData?businessAction=ShipmentEquipmentImport&typeName=SCM.TMS7.DTO.Shipment.ShipmentEquipmentImportDTO,SCM.Contract.TMS7";
//String token = "eb4524c77e444ce59030d540ff4bcafb";
//        Map<String, String> headersPost = new HashMap<>();
//        headersPost.put("Accept", "*/*");
//        headersPost.put("Accept-Encoding", "gzip, deflate, br, zstd");
//        headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
//        headersPost.put("Connection", "keep-alive");
//        headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
//        headersPost.put("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw8mwTIzOf6DW/jKZclMUUqI%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXjx9j77poCZ-uRiJPNoxfSsrwMXk2WI3YaoyQi9UlclShGHP610m-Q8-yiYyLUsp6vb84VXFwx0r5cUg8xFcxLspGhleEECbsP0yPKWihJQIgocSctuFXh7zImehtX_-8GlVuGgfZHyQ8sQuUiFwo2znKT6hLiSGmA38OCoRh-0HbTvEuh0MTd0fIPlrSNOe70g; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; AttachmentWidgetLayout=grid; SSID="+token+"; PowerTmsCookie=TmsUserID%3DOTQ61QsLxrz5YWMd6MFnhNgdGt6iW%2Bfx%2BkHkHRiqELw%3D");
//        headersPost.put("Origin", "https://tms.sungrowpower.com");
//        headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
//        headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//        headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
//        headersPost.put("sec-ch-ua-mobile", "?0");
//        headersPost.put("sec-ch-ua-platform", "\"Windows\"");
//
//        //上传回单附件
//        File file = new File("D:/static/sunshineCarDriver.xls");
//        HttpRequest request = HttpRequest.post(uploadExcelURL)
//                .headerMap(headersPost, true)
//                .form("files", file);
//        System.out.println(request);
//        HttpResponse response = request.execute();
//        System.out.println(response);
//        int statusCode = response.getStatus();
//        if (statusCode == 301 || statusCode == 302) {
//            String location = response.header("Location");
//            if (StrUtil.isNotBlank(location)) {
//                HttpRequest redirectRequest = HttpRequest.get("https://tms.sungrowpower.com"+location);
//                HttpResponse redirectResponse = redirectRequest.execute();
//                // 在此处处理重定向后的响应
//                System.out.println(redirectResponse);
//            }
//        }

        //test();
       /* try (FileInputStream fileInputStream = new FileInputStream("D:/A/sunshineCarDriver.xls");
             HSSFWorkbook workbook = new HSSFWorkbook(fileInputStream)) {
            Sheet sheet = workbook.getSheetAt(0);  // 获取第一个工作表
            Row row = sheet.getRow(1);  // 第二行，因为行索引从 0 开始
            if (row!= null) {
                // 运单ID
                row.getCell(0).setCellValue("New Text");
                // 设备ID
                row.getCell(1).setCellValue("设备ID");
                row.getCell(5).setCellValue("设备类型ID");
                row.getCell(8).setCellValue("司机名称");
                row.getCell(20).setCellValue("联系电话");
            }
            // 保存修改后的工作簿
            try (FileOutputStream fileOutputStream = new FileOutputStream("D:/A/sunshineCarDriver.xls")) {
                workbook.write(fileOutputStream);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }*/
//        String loginUrl = "https://tms.sungrowpower.com/scm.cloud.web/Login/Login";
//        Map<String,Object> params = new HashMap<>();
//        params.put("userid","8200007456");
//        params.put("password","123456aC*");
//        params.put("duration","0");
//        String body = HttpRequest.post(loginUrl)
//                .form(params)
//                .execute().body();
//        JSONObject jsonObject = JSONObject.parseObject(body);
//        if(StringUtils.isNotBlank(jsonObject.getString("ssid"))) {
//            String token = jsonObject.getString("ssid");
//
//            String excelPath = "D:/static/sunshineCarDriver.xls";
//            String uploadExcelURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/SaveImportData?businessAction=ShipmentEquipmentImport&typeName=SCM.TMS7.DTO.Shipment.ShipmentEquipmentImportDTO,SCM.Contract.TMS7";
//
//            Map<String, String> headersPost = new HashMap<>();
//            headersPost.put("Accept", "*/*");
//            headersPost.put("Accept-Encoding", "gzip, deflate, br, zstd");
//            headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
//            headersPost.put("Connection", "keep-alive");
//            headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
//            headersPost.put("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw8mwTIzOf6DW/jKZclMUUqI%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXjx9j77poCZ-uRiJPNoxfSsrwMXk2WI3YaoyQi9UlclShGHP610m-Q8-yiYyLUsp6vb84VXFwx0r5cUg8xFcxLspGhleEECbsP0yPKWihJQIgocSctuFXh7zImehtX_-8GlVuGgfZHyQ8sQuUiFwo2znKT6hLiSGmA38OCoRh-0HbTvEuh0MTd0fIPlrSNOe70g; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; AttachmentWidgetLayout=grid; SSID=" + jsonObject.getString("ssid") + "; PowerTmsCookie=TmsUserID%3DOTQ61QsLxrz5YWMd6MFnhNgdGt6iW%2Bfx%2BkHkHRiqELw%3D");
//            headersPost.put("Origin", "https://tms.sungrowpower.com");
//            headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
//            headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//            headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
//            headersPost.put("sec-ch-ua-mobile", "?0");
//            headersPost.put("sec-ch-ua-platform", "\"Windows\"");
//
//            //上传回单附件
//            File file = new File(excelPath);
//            HttpRequest request = HttpRequest.post(uploadExcelURL)
//                    .headerMap(headersPost, true)
//                    .form("files", file);
//            HttpResponse response = request.execute();
//            int statusCode = response.getStatus();
//            if (statusCode == 301 || statusCode == 302) {
//                String location = response.header("Location");
//                if (StrUtil.isNotBlank(location)) {
//                    //重定向请求
//                    HttpRequest redirectRequest = HttpRequest.get("https://tms.sungrowpower.com" + location);
//                    HttpResponse redirectResponse = redirectRequest.execute();
//                    System.out.println(redirectResponse.body());
//                }
//            }
//        }

            //获取保价金额
          /*  String url = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/Edit";
            HashMap listParams = new HashMap<>();
            listParams.put("gid", "ETS.SP2409110297");
            String listRes = HttpRequest.post(url)
                    .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token+ "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(listParams).execute().body();
            Document doc  = Jsoup.parse(listRes);
            Element inputElement = doc.select("input[name=INSURED_AMOUNT]").first();
            if (inputElement!= null) {
                String value = inputElement.attr("value");
               System.out.println(value);
            } else {

            }*/

            /*SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.MONTH,-3);
            String dayStr = sdfDay.format(cal.getTime());


            String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentInfos?udfDisplayForqueryGuid=8200007456ygdySCM.TMS7.Model.Models.shipment_SCM.TMS7.Model&defsort=CREATED_DATE%20DESC";
            Map<String,Object> listParams = new HashMap<>();
            listParams.put("_search","true");
            listParams.put("rows","200");
            listParams.put("page","1");
            listParams.put("sord","asc");
            listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"cn\",\"data\":\"80301794\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
            String listRes = HttpRequest.post(listUrl)
                    .header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(listParams).execute().body();
            JSONObject listJson = JSONObject.parseObject(listRes);
            JSONArray rows = listJson.getJSONArray("rows");
            for(int i = 0 ; i < rows.size(); i++) {
                JSONObject row = rows.getJSONObject(i);
                String gid = row.getString("id");
                Map<String,Object> goodsParams = new HashMap<>();
                goodsParams.put("_search","false");
                goodsParams.put("rows","200");
                goodsParams.put("page","1");
                goodsParams.put("sord","asc");
                String goodsUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentPackageItems?gid="+gid+"&defsort=LINE_ID%20Asc,OM_LINE_ID%20Asc";
                String goodsRes = HttpRequest.post(goodsUrl)
                        .header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
                        .header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+token+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                        .form(goodsParams).execute().body();
                JSONObject goodsResObj = JSONObject.parseObject(goodsRes);
                JSONArray goodsRows = goodsResObj.getJSONArray("rows");
                String goodsInfo = "";
                for(int j = 0 ; j < goodsRows.size(); j++) {
                    JSONArray goodsCell = goodsRows.getJSONObject(j).getJSONArray("cell");
                    goodsInfo = goodsInfo + goodsCell.getString(2) + " " + goodsCell.getString(3).replace(".00","") + "台\n";
                }
                System.out.println(goodsInfo);
            }
*/

        //}
//            String excelPath = "D:/static/sunshineCarDriver.xls";
//            String uploadExcelURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/SaveImportData?businessAction=ShipmentEquipmentImport&typeName=SCM.TMS7.DTO.Shipment.ShipmentEquipmentImportDTO,SCM.Contract.TMS7";
//
//            Map<String, String> headersPost = new HashMap<>();
//            headersPost.put("Accept", "*/*");
//            headersPost.put("Accept-Encoding", "gzip, deflate, br, zstd");
//            headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
//            headersPost.put("Connection", "keep-alive");
//            headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
//            headersPost.put("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw8mwTIzOf6DW/jKZclMUUqI%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXjx9j77poCZ-uRiJPNoxfSsrwMXk2WI3YaoyQi9UlclShGHP610m-Q8-yiYyLUsp6vb84VXFwx0r5cUg8xFcxLspGhleEECbsP0yPKWihJQIgocSctuFXh7zImehtX_-8GlVuGgfZHyQ8sQuUiFwo2znKT6hLiSGmA38OCoRh-0HbTvEuh0MTd0fIPlrSNOe70g; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; AttachmentWidgetLayout=grid; SSID="+ jsonObject.getString("ssid") +"; PowerTmsCookie=TmsUserID%3DOTQ61QsLxrz5YWMd6MFnhNgdGt6iW%2Bfx%2BkHkHRiqELw%3D");
//            headersPost.put("Origin", "https://tms.sungrowpower.com");
//            headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
//            headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//            headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
//            headersPost.put("sec-ch-ua-mobile", "?0");
//            headersPost.put("sec-ch-ua-platform", "\"Windows\"");
//
//            //上传回单附件
//            File file = new File(excelPath);
//            HttpRequest request = HttpRequest.post(uploadExcelURL)
//                    .headerMap(headersPost, true)
//                    .form("files", file);
//            HttpResponse response = request.execute();
//            int statusCode = response.getStatus();
//            if (statusCode == 301 || statusCode == 302) {
//                String location = response.header("Location");
//                if (StrUtil.isNotBlank(location)) {
//                    //重定向请求
//                    HttpRequest redirectRequest = HttpRequest.get("https://tms.sungrowpower.com"+location);
//                    HttpResponse redirectResponse = redirectRequest.execute();
//                    System.out.println(redirectResponse.body());
//                }
//            }


            //提货
            /*SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String pickURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/AllPickupOrArrival";
            Map<String, Object> arriParams = new HashMap<>();
            arriParams.put("gids", "SALES.SP2408260069");
            arriParams.put("operation", "pickup");
            arriParams.put("arrivalDate", "2024-08-26 23:13:43");
            arriParams.put("leaveDate", "2024-08-27 00:13:43");

            String pickRes = HttpRequest.post(pickURL)
                    .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + "123asdad" + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(arriParams).execute().body();
           System.out.println(pickRes);*/

          /*  String feeUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/ShipmentInvoice/SaveShipmentInvoiceLine?gid=SALES.PM2408200512";
            Map<String, Object> feeParam = new HashMap<>();
            feeParam.put("CHARGE_TYPE_PSC", "S50");
            feeParam.put("CHARGE", 1);
            feeParam.put("IS_TAX_INCLUDED", "Y");
            feeParam.put("REMARK", "异常费用");

            String listRes = HttpRequest.post(feeUrl)
                    .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + jsonObject.getString("ssid") + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(feeParam).execute().body();
            System.out.println(listRes);*/
            //成功

            //根据客户订单号查询
           /* SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.MONTH,-3);
            String dayStr = sdfDay.format(cal.getTime());
            //请求列表接口
            String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/ShipmentInvoice/GetShipmentInvoices?defsort=CREATED_DATE%20DESC";
            Map<String,Object> listParams = new HashMap<>();
            listParams.put("_search","true");
            listParams.put("rows","20");
            listParams.put("page","1");
            listParams.put("sord","asc");
            listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\"80293597\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
            String listRes = HttpRequest.post(listUrl)
                    .header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+jsonObject.getString("ssid")+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(listParams).execute().body();
            JSONObject listJson = JSONObject.parseObject(listRes);
            JSONArray rows = listJson.getJSONArray("rows");
            if(rows.size() == 1) {
                String gids = rows.getJSONObject(0).getString("id");

                JSONArray jsonArray = rows.getJSONObject(0).getJSONArray("cell");
                //PM单号
                String pmNum = jsonArray.getString(3);
                System.out.println(gids);
                System.out.println(pmNum);


            }*/

//            String picUploadURL = "https://tms.sungrowpower.com/SCM.Configration.WebUI/AttachmentInfo/Upload";
//
//            String filePath = "D:/A/1.png"; // 替换为您要上传的文件的本地路径
//
//            Map<String, String> headersPost = new HashMap<>();
//            headersPost.put("Accept", "*/*");
//            headersPost.put("Accept-Encoding","gzip, deflate, br, zstd");
//            headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
//            headersPost.put("Connection", "keep-alive");
//            headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
//            headersPost.put("Cookie", "cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; SSID="+jsonObject.getString("ssid")+"; PowerTmsCookie=TmsUserID%3DFTvM9f4X5v89fLttSlA3OQ%3D%3D");
//            headersPost.put("Origin", "https://tms.sungrowpower.com");
//            headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
//            headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//            headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
//            headersPost.put("sec-ch-ua-mobile", "?0");
//            headersPost.put("sec-ch-ua-platform", "\"Windows\"");
//
//            Map<String,Object> sendParams = new HashMap<String,Object>();
//            sendParams.put("Sys","TMS");
//            sendParams.put("Module","ShipmentInvoice");
//            sendParams.put("Id","PM2408200500");
//            sendParams.put("AttachType","附加费证明");
//            sendParams.put("RelatedKey","SALES.PM2408200500");
//            sendParams.put("PrimaryId","SALES.PM2408200500");
//            sendParams.put("files",new File(filePath));
//
//            HttpRequest request = HttpRequest.post(picUploadURL)
//                    .headerMap(headersPost, true)
//                    .form(sendParams);
//
//            cn.hutool.http.HttpResponse execute = request.execute();
//
//            String uploadRes = execute.body();
//            System.out.println(uploadRes);

/*

            SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.MONTH,-3);
            String dayStr = sdfDay.format(cal.getTime());
            //请求列表接口
            String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/GetShipmentInfos?udfDisplayForqueryGuid=8200007456ygdySCM.TMS7.Model.Models.shipment_SCM.TMS7.Model&defsort=CREATED_DATE%20DESC";
            Map<String,Object> listParams = new HashMap<>();
            listParams.put("_search","true");
            listParams.put("rows","20");
            listParams.put("page","1");
            listParams.put("sord","asc");
            listParams.put("filters","{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\"80293240\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\""+dayStr+" 00:00~*\"}]}");
            String listRes = HttpRequest.post(listUrl)
                    .header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+jsonObject.getString("ssid")+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(listParams).execute().body();
            JSONObject listJson = JSONObject.parseObject(listRes);
            JSONArray rows = listJson.getJSONArray("rows");
            if(rows.size() == 1) {
                String gids = rows.getJSONObject(0).getString("id");

                JSONArray jsonArray = rows.getJSONObject(0).getJSONArray("cell");
                //SP单号
                String spNum = jsonArray.getString(1);
                //判断状态
                String status = jsonArray.getString(2);

                System.out.println(gids);
                System.out.println(spNum);
                System.out.println(status);
            }
*/

//            String uploadExcelURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/SaveImportData?businessAction=ShipmentEquipmentImport&typeName=SCM.TMS7.DTO.Shipment.ShipmentEquipmentImportDTO,SCM.Contract.TMS7";
//
//            Map<String, String> headersPost = new HashMap<>();
//            headersPost.put("Accept", "*/*");
//            headersPost.put("Accept-Encoding", "gzip, deflate, br, zstd");
//            headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
//            headersPost.put("Connection", "keep-alive");
//            headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
//            headersPost.put("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw8mwTIzOf6DW/jKZclMUUqI%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXjx9j77poCZ-uRiJPNoxfSsrwMXk2WI3YaoyQi9UlclShGHP610m-Q8-yiYyLUsp6vb84VXFwx0r5cUg8xFcxLspGhleEECbsP0yPKWihJQIgocSctuFXh7zImehtX_-8GlVuGgfZHyQ8sQuUiFwo2znKT6hLiSGmA38OCoRh-0HbTvEuh0MTd0fIPlrSNOe70g; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; AttachmentWidgetLayout=grid; SSID="+jsonObject.getString("ssid")+"; PowerTmsCookie=TmsUserID%3DOTQ61QsLxrz5YWMd6MFnhNgdGt6iW%2Bfx%2BkHkHRiqELw%3D");
//            headersPost.put("Origin", "https://tms.sungrowpower.com");
//            headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
//            headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//            headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
//            headersPost.put("sec-ch-ua-mobile", "?0");
//            headersPost.put("sec-ch-ua-platform", "\"Windows\"");
//
//            //上传回单附件
//            File file = new File("D:/A/sunshineCarDriver.xls");
//            HttpRequest request = HttpRequest.post(uploadExcelURL)
//                    .headerMap(headersPost, true)
//                    .form("files", file);
//            System.out.println(request);
//            HttpResponse response = request.execute();
//            System.out.println(response);
//            int statusCode = response.getStatus();
//            if (statusCode == 301 || statusCode == 302) {
//                String location = response.header("Location");
//                if (StrUtil.isNotBlank(location)) {
//                    HttpRequest redirectRequest = HttpRequest.get("https://tms.sungrowpower.com"+location);
//                    HttpResponse redirectResponse = redirectRequest.execute();
//                    // 在此处处理重定向后的响应
//                    System.out.println(redirectResponse);
//                }
//            }
        //}
    }

    public static void confirm(){
        //登录验证
        String loginUrl = "https://tms.sungrowpower.com/scm.cloud.web/Login/Login";
        Map<String,Object> params = new HashMap<>();
        params.put("userid","8200007456");
        params.put("password","Jh8888..");
        params.put("duration","0");
        String body = HttpRequest.post(loginUrl)
                .form(params)
                .execute().body();
        JSONObject jsonObject = JSONObject.parseObject(body);
        if(StringUtils.isNotBlank(jsonObject.getString("ssid"))) {
            //确认
            String confirmURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/DeliveryConfirmation";
            Map<String, Object> listParams = new HashMap<>();
            listParams.put("gids", "SALES.SP2408120336");

            String listRes = HttpRequest.post(confirmURL)
                    .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + jsonObject.getString("ssid") + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(listParams).execute().body();
            System.out.println(listRes);
        }
    }

    public static void test(){
        String filePath = "D:/sfz/1.jpg";
        String fileExtension = filePath.substring(filePath.lastIndexOf("."));
        System.out.println(fileExtension);
//        String spNum = "SP2508050158";
//        String gids = "SALES.SP2508050158";
//        String token = "eb4524c77e444ce59030d540ff4bcafb";
//        String picUploadURL = "https://tms.sungrowpower.com/SCM.Configration.WebUI/AttachmentInfo/Upload";
//
//        Map<String, String> headersPost = new HashMap<>();
//        headersPost.put("Accept", "*/*");
//        headersPost.put("Accept-Encoding","gzip, deflate, br, zstd");
//        headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
//        headersPost.put("Connection", "keep-alive");
//        headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
//        headersPost.put("Cookie", "cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; SSID="+token+"; PowerTmsCookie=TmsUserID%3DFTvM9f4X5v89fLttSlA3OQ%3D%3D");
//        headersPost.put("Origin", "https://tms.sungrowpower.com");
//        headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
//        headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//        headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
//        headersPost.put("sec-ch-ua-mobile", "?0");
//        headersPost.put("sec-ch-ua-platform", "\"Windows\"");
//
//        Map<String,Object> sendParams = new HashMap<String,Object>();
//        sendParams.put("Sys","TMS");
//        sendParams.put("Module","Shipment");
//        sendParams.put("Id",spNum);
//        sendParams.put("AttachType","行驶证");
//        sendParams.put("Relatedtype","20");
//        sendParams.put("RelatedKey",gids);
//        sendParams.put("PrimaryId",gids);
//
//
//        String filePath = "D:/sfz/1.jpg" ;
//        File file = new File(filePath);
//       /* String directory = file.getParent();
//        String newFileName = "苏F98515";
//        File renamedFileInLogic = new File(directory, newFileName);*/
//
//        /*sendParams.put("files",renamedFileInLogic);*/
//
//        HttpRequest request = HttpRequest.post(picUploadURL)
//                .headerMap(headersPost, true)
//                .form(sendParams)
//                .form("files",file,"苏F98515.jpg");
//        String uploadRes = request.execute().body();
//
//        System.out.println(uploadRes);
//
//        JSONObject uploadJson = JSONObject.parseObject(uploadRes);
//
//        //上传成功调用
//        if(uploadRes.contains("true")) {
//            String afterUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Attachment/AfterPartialUpdateData";
//            sendParams = new HashMap<>();
//            sendParams.put("Sys", "TMS");
//            sendParams.put("Module", "Shipment");
//            sendParams.put("Id", spNum);
//            sendParams.put("AttachType", "行驶证");
//            sendParams.put("Relatedtype", "20");
//            sendParams.put("RelatedKey", gids);
//            sendParams.put("PrimaryId", gids);
//            sendParams.put("AttachmenIds", uploadJson.getJSONArray("Msg"));
//
//
//            uploadRes = HttpRequest.post(afterUrl)
//                    .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
//                    .header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + token + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
//                    .form(sendParams).execute().body();
//
//            System.out.println(uploadRes);
//        }
    }

    public static void getPrice(){
        //登录验证
        String loginUrl = "https://tms.sungrowpower.com/scm.cloud.web/Login/Login";
        Map<String,Object> params = new HashMap<>();
        params.put("userid","8200007456");
        params.put("password","Jh8888..");
        params.put("duration","0");
        String body = HttpRequest.post(loginUrl)
                .form(params)
                .execute().body();
        JSONObject jsonObject = JSONObject.parseObject(body);
        if(StringUtils.isNotBlank(jsonObject.getString("ssid"))){
            String url = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/Edit?gid=SALES.SP2408060091";
            Map<String, Object> listParams = new HashMap<>();
            listParams.put("gid", "SALES.SP2408060091");
            String listRes = HttpRequest.post(url)
                    .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + jsonObject.getString("ssid") + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(listParams).execute().body();
            System.out.println(listRes);

            Document doc  = Jsoup.parse(listRes);
            Element inputElement = doc.select("input[name=INSURED_AMOUNT]").first();
            if (inputElement!= null) {
                String value = inputElement.attr("value");
                System.out.println("INSURED_AMOUNT 的值: " + value);
            } else {
                System.out.println("未找到名为 INSURED_AMOUNT 的 input 元素");
            }
        }


    }

    public static void downloadPDF(String urlString, String fileName,JSONObject jsonObject) {
        try {
            URL url = new URL(urlString);
            URLConnection connection = url.openConnection();
            connection.setRequestProperty("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + jsonObject.getString("ssid") + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D");

            InputStream inputStream = connection.getInputStream();

            FileOutputStream outputStream = new FileOutputStream(fileName);

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer))!= -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            outputStream.close();
            inputStream.close();

            System.out.println("PDF 下载成功！");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    //到货
    public static void arrival(){
        //登录验证
        String loginUrl = "https://tms.sungrowpower.com/scm.cloud.web/Login/Login";
        Map<String,Object> params = new HashMap<>();
        params.put("userid","8200007456");
        params.put("password","Jh8888..");
        params.put("duration","0");
        String body = HttpRequest.post(loginUrl)
                .form(params)
                .execute().body();
        JSONObject jsonObject = JSONObject.parseObject(body);
        if(StringUtils.isNotBlank(jsonObject.getString("ssid"))) {
            SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            //请求列表接口
            String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Shipment/AllPickupOrArrival";
            Map<String, Object> listParams = new HashMap<>();
            listParams.put("gids", "SALES.SP2408050205");
            listParams.put("operation", "arrival");
            listParams.put("arrivalDate", "2024-08-06 10:00:00");
            listParams.put("leaveDate", "2024-08-06 11:00:00");

            String listRes = HttpRequest.post(listUrl)
                    .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + jsonObject.getString("ssid") + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(listParams).execute().body();
            System.out.println(listRes);
        }
    }

    public static void receipt(){
        //登录验证
        String loginUrl = "https://tms.sungrowpower.com/scm.cloud.web/Login/Login";
        Map<String,Object> params = new HashMap<>();
        params.put("userid","8200007456");
        params.put("password","123456aC*");
        params.put("duration","0");
        String body = HttpRequest.post(loginUrl)
                .form(params)
                .execute().body();
        JSONObject jsonObject = JSONObject.parseObject(body);
        if(StringUtils.isNotBlank(jsonObject.getString("ssid"))){

             /* SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

          String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/PodListSigned";
            Map<String,Object> listParams = new HashMap<>();
            listParams.put("signed_by","南通吉华物流有限公司");
            listParams.put("signed_date",sdfDay.format(new Date()));
            listParams.put("signed_status",10);
            listParams.put("guids","SALES.POD060308");

            String listRes = HttpRequest.post(listUrl)
                    .header("Content-Type","application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie","SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID="+jsonObject.getString("ssid")+"; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(listParams).execute().body();
            System.out.println(listRes);*/

            //查询回单ID
            SimpleDateFormat sdfDay = new SimpleDateFormat("yy/MM/dd");
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.MONTH,-3);
            String dayStr = sdfDay.format(cal.getTime());
            //请求列表接口
            String listUrl = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/GetPods?defsort=CREATED_DATE%20DESC";
            Map<String, Object> listParams = new HashMap<>();
            listParams.put("_search", "true");
            listParams.put("rows", "20");
            listParams.put("page", "1");
            listParams.put("sord", "asc");
            listParams.put("filters", "{\"groupOp\":\"AND\",\"rules\":[{\"field\":\"C_ORDER_NO\",\"op\":\"eq\",\"data\":\"MT2507050014\"},{\"field\":\"CREATED_DATE\",\"op\":\"bw\",\"data\":\"" + dayStr + " 00:00~*\"}]}");
            String listRes = HttpRequest.post(listUrl)
                    .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
                    .header("Cookie", "SECKEY_ABVK=mzuODm9MRee89OdNtrQWw36qeYBNp+hQqQf30iLV2JA%3D; BMAP_SECKEY=4L4NmEo2UwWUYLWTmyNXj5squ_zhN5kwM-OYqRhMfI83flf-85t6eKZFj2nsNIma6Ri1FmJROYiTn2EEOHiHw-QZacK1JcnkQ3DIqEIorSIgn13K-iPXCBgMv0uPqPsHsWV40wxheczSFkBRkpjCBhuTn-TZ02wIurutAK-79Zqc3gzaZWlB1HQKczhoNVLi; Lang=zh-cn; PowerTmsVersionCookie=Version%3D%2C; cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; SSID=" + jsonObject.getString("ssid") + "; PowerTmsCookie=TmsUserID%3DPryQoAmYt8z8wtHh92OgSw%3D%3D")
                    .form(listParams).execute().body();
            JSONObject listJson = JSONObject.parseObject(listRes);
            JSONArray rows = listJson.getJSONArray("rows");
            if(rows.size() == 1){
                String relatedkey = rows.getJSONObject(0).getString("id");
                String relatedID = rows.getJSONObject(0).getJSONArray("cell").getString(1);
                System.out.println(relatedkey);
                System.out.println(relatedID);
            }

//            String picUploadURL = "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/PartialUploadAttachment?relatedkey=SALES.POD060308&relatedID=POD060308&relatedtype=40&domainName=SALES";
//
//            String filePath = "D:/A/1.png"; // 替换为您要上传的文件的本地路径
//
//            Map<String, String> headersPost = new HashMap<>();
//            headersPost.put("Accept", "*/*");
//            headersPost.put("Accept-Encoding","gzip, deflate, br, zstd");
//            headersPost.put("Accept-Language", "zh-CN,zh;q=0.9");
//            headersPost.put("Connection", "keep-alive");
//            headersPost.put("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryjfPDtu6h3wMxaApO");
//            headersPost.put("Cookie", "cw_conversation=eyJhbGciOiJIUzI1NiJ9.eyJzb3VyY2VfaWQiOiI1NTBkZTBhNy05NjNiLTRhMzQtYTM4MS1jNzZlZjYwYTg5NDYiLCJpbmJveF9pZCI6Mn0.Dh5OH7xIWhwWFldFOQNgMlHzjz1Uj1teijswG4jT_EE; cw_user_N183jFc4G3HGNidKRvKBL9xC=1b2af2fc82b6a3a2caf8af52bd85e278; Lang=zh-cn; SSID="+jsonObject.getString("ssid")+"; PowerTmsCookie=TmsUserID%3DFTvM9f4X5v89fLttSlA3OQ%3D%3D");
//            headersPost.put("Origin", "https://tms.sungrowpower.com");
//            headersPost.put("Referer", "https://tms.sungrowpower.com/SCM.TMS7.WebUI/Pod/Edit?guid=SALES.POD060308");
//            headersPost.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//            headersPost.put("sec-ch-ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
//            headersPost.put("sec-ch-ua-mobile", "?0");
//            headersPost.put("sec-ch-ua-platform", "\"Windows\"");
//
//            HttpRequest request = HttpRequest.post(picUploadURL)
//                    .headerMap(headersPost, true)
//                    .form("files", new File(filePath));
//
//            cn.hutool.http.HttpResponse execute = request.execute();
//
//            String uploadRes = execute.body();
//            System.out.println(uploadRes);
        }
    }
}

