<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细申请审核')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-3 col-sm-3">
                        <div class="form-group">
                            <div class="col-sm-6">
                                <input name="invoiceVbillno" class="form-control" type="text" placeholder="请输入发货单/客户单号"
                                       maxlength="30"  aria-required="true">
                            </div>
                            <div class="col-sm-6">
                                <select name="salesDept" placeholder="运营组" id="salesDept" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运营组" multiple>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <!--                            <label class="col-sm-2">要求提货日期：</label>-->
                            <div class="">
                                <input type="text" placeholder="要求提货开始日" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateStart"  name="params[reqDeliDateStart]" autocomplete="off" th:value="${reqDeliDateStart}">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" placeholder="要求提货截止日" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateEnd"  name="params[reqDeliDateEnd]" autocomplete="off" th:value="${reqDeliDateEnd}">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-5" >
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">提货方地址：</label>-->
                            <div class="col-sm-6">
                                <div class="col-sm-4">
                                    <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliCityId" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliAreaId" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="col-sm-4">
                                    <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>

                        </div>
                    </div>



                    <div class="col-md-1 col-sm-1">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                <i class="fa fa-download"></i> 导出
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "goodsTrace";

    $(function () {
        $("#reqDeliDateStart").val(getFrontFormatDate(10));
        $("#reqDeliDateEnd").val(getFrontFormatDate(0));

        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            modalName: "货源跟踪",
            showToggle: false,
            showColumns: true,
            clickToSelect:true,
            height: 560,
            showFooter:true,
            uniqueId: "payDetailId",
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'invoiceVbillno',
                    title: '发货单号',
                    align: 'left',
                },
                {
                    field: 'reqDeliDate',
                    title: '要求提货日期',
                    align: 'left',
                },
                {
                    field: 'custAbbr',
                    title: '客户简称',
                    align: 'left',
                },
                {
                    field: 'custOrderno',
                    title: '客户单号',
                    align: 'left',
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let deliDetailAddress = row.deliProName + row.deliCityName + row.deliAreaName;
                        let arriDetailAddress = row.arriProName + row.arriCityName + row.arriAreaName;
                        return `<span class="label label-warning pa2">提</span>` + deliDetailAddress + `<br/><span class="label label-success pa2">到</span>` + arriDetailAddress;
                    }
                },
                {
                    field: 'carNo',
                    title: '车牌号',
                    align: 'left',
                },
                {
                    field: 'driverName',
                    title: '驾驶员',
                    align: 'left',
                },
                {
                    field: 'cardId',
                    title: '身份证',
                    align: 'left',
                },
                {
                    field: 'phone',
                    title: '手机号码',
                    align: 'left',
                },
                {
                    field: 'weight',
                    title: '货量',
                    align: 'left',
                    formatter: function status(value, row, index) {
                       let str = row.goodsName;
                       if(row.num != null && row.num != 0){
                           str = str + ' ' + row.num +'件'
                       }
                        if(row.weight != null && row.weight != 0){
                            str = str + ' ' + row.weight +'吨'
                        }
                        if(row.volume != null && row.volume != 0){
                            str = str + ' ' + row.volume +'方'
                        }
                        return str;
                    }
                },
               /* {
                    field: 'specificationModels',
                    title: '规格型号',
                    align: 'left',
                },*/
                {
                    field: 'estimatedArrivalTime',
                    title: '预计到场时间',
                    align: 'left',
                },
            ]
        };

        $.table.init(options);
    });



    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset()
        $("#reqDeliDateStart").val(getFrontFormatDate(10));
        $("#reqDeliDateEnd").val(getFrontFormatDate(0));
        searchPre();
    }

    layui.use('laydate', function(){
        var laydate = layui.laydate;
        var reqDeliDateStart = laydate.render({
            elem: '#reqDeliDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqDeliDateEnd = laydate.render({
            elem: '#reqDeliDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });

    });

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        data.salesDept = $.common.join($('#salesDept').selectpicker('val'));
        $.table.search('role-form', data);
    }

    function getValue(val){
        if(val == null){
            val = "/";
        }
        return val
    }


    function getFrontFormatDate(days) {
        var date = new Date();
        date.setDate(date.getDate()-days);
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() + 1 ;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate;


        return currentdate;
    }

</script>


</body>
</html>