<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应收明细')"/>
</head>
<style>
    .label-dangerT{
        color: #ed5565;
        background-color: transparent;
        border: 1px solid #ed5565;
    }

</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" id="receCheckSheetId" name="receCheckSheetId" th:value="${receCheckSheetId}">
                <input type="hidden" name="receSheetRecordId" th:value="${receSheetRecordId}">
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-5">发货单号：</label>-->
                            <div class="col-sm-12">
                                <input name="invoiceVbillno" id="invoiceVbillno" class="form-control" type="text"
                                       placeholder="请输入发货单号"
                                       maxlength="25" required="" aria-required="true">
                                <input id="hiddenText" type="text" style="display:none" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-5">客户订单号：</label>-->
                            <div class="col-sm-12">
                                <input name="custOrderno" id="custOrderno" class="form-control" type="text"
                                       placeholder="请输入客户订单号"
                                       maxlength="25" required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-2">要求提货日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="time-input form-control"
                                       id="reqDeliDateStart"  name="reqDeliDateStart" placeholder="要求提货开始日">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="time-input form-control"
                                       id="reqDeliDateEnd"  name="reqDeliDateEnd" placeholder="要求提货结束日">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger " th:if="${vbillstatus == 0}" onclick="remove()" shiro:hasAnyPermissions="finance:receCheckSheet:delete,finance:fleet:receCheckSheet:delete">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-primary single disabled" onclick="receiveDetail()" shiro:hasAnyPermissions="finance:receCheckSheet:receiveDetail,finance:fleet:receCheckSheet:receiveDetail">
                <i class="fa fa-calculator"></i> 应收详情
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasAnyPermissions="finance:receCheckSheet:export,finance:fleet:receCheckSheet:export">
                <i class="fa fa-download"></i> 导出
            </a>
<!--            <a class="btn btn-warning" onclick="adjustExport()" shiro:hasAnyPermissions="finance:receCheckSheet:export,finance:fleet:receCheckSheet:export">-->
<!--                <i class="fa fa-download"></i> 调整单导出-->
<!--            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var balatype = [[${@dict.getType('bala_type')}]];
    var receCheckSheetId = [[${receCheckSheetId}]];
    var receSheetRecordId = [[${receSheetRecordId}]];
    let _param = [];
    if (receCheckSheetId) {
        _param.push("receCheckSheetId=" + receCheckSheetId);
    }
    if (receSheetRecordId) {
        _param.push("receSheetRecordId=" + receSheetRecordId);
    }

    var prefix = ctx + "receCheckSheet";

    //合计
    var receiptAmountFreightTotal = 0;//运费
    var receiptAmountOnWayTotal = 0;//在途
    var receiptAmountTotal = 0;//对账总金额
    var invTransFeeCountTotal = 0;//发货单应收
    var costShareFreightTotal = 0;//应付运费
    var costShareOnWayTotal = 0;//应付在途
    var invOtherFeeTotal = 0;//三方
    var totalCostTotal = 0;//总成本
    // var adjustAmountTotal = 0;//调整金额
    // var gotAmountTotal = 0;//已收
    // var ungotAmountTotal = 0;//未收
    var costCountTotal = 0;//总成本
    var costCountFreightTotal = 0;//运费总成本
    var costCountOnWayTotal = 0;//在途总成本
    var otherFeeCountTotal = 0;//第三方应付
    var managerFeeCountTotal = 0;//第三方应付
    var grossProfitTotal = 0;//毛利

    $(function () {
        var options = {
            url: prefix + "/receiveList?"+_param.join("&"),//"/receiveList?receCheckSheetId="+receCheckSheetId,
            showToggle: false,
            showColumns: true,
            modalName: "应收明细",
            fixedColumns: true,
            exportUrl: prefix + "/importData?"+_param.join("&"),///importData?receCheckSheetId="+receCheckSheetId,
            fixedNumber: 2,
            showFooter: true,
            height: 500,
            clickToSelect: true,
            // uniqueId: "customerId",
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onPostBody: function () {
                getAmountCount();
            },
            columns: [{
                    checkbox: true,
                footerFormatter: function (row) {
                    return "运费:<nobr id='receiptAmountFreightTotal'>￥0</nobr>&nbsp&nbsp"
                        + "在途:<nobr id='receiptAmountOnWayTotal'>￥0</nobr>&nbsp&nbsp"
                        + "对账总金额:<nobr id='receiptAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        + "发货单应收:<nobr id='invTransFeeCountTotal'>￥0</nobr>&nbsp&nbsp"
                        /*[# th:if="${@permission.hasAnyPermissions('finance:receCheckSheet:revenue,finance:fleet:receCheckSheet:revenue')}"]*/
                        + "应付运费:<nobr id='costShareFreightTotal'>￥0</nobr>&nbsp&nbsp"
                        + "应付在途:<nobr id='costShareOnWayTotal'>￥0</nobr>&nbsp&nbsp"
                        + "三方:<nobr id='invOtherFeeTotal'>￥0</nobr>&nbsp&nbsp"
                        + "总成本:<nobr id='totalCostTotal'>￥0</nobr>&nbsp&nbsp"
                        /*[/]*/
                        // + "调整后金额:<nobr id='adjustAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        // + "已收:<nobr id='gotAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        // + "未收:<nobr id='ungotAmountTotal'>￥0</nobr>&nbsp&nbsp<br>" +
                        +"<br><b>总合计：</b>  总金额：<nobr id='allTransFeeCount'>￥0</nobr>&nbsp&nbsp" +
                        "调整后金额：<nobr id='allAdjustAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        +"运费类型金额：<nobr id='allFreightTotal'>￥0</nobr>&nbsp&nbsp"
                        +"在途类型金额：<nobr id='allOnWayTotal'>￥0</nobr>&nbsp&nbsp";
                }
                },
                {title: '发货单号', align: 'left', field: 'vbillno'},
                {title: '客户简称', align: 'left', field: 'custAbbr'},
                {title: '客户订单号', align: 'left', field: 'custOrderno'},
                {title: '要求提货日期', align: 'left', field: 'reqDeliDate'},
               /* {title: '提货到货省市区', align: 'left', field: 'address',
                    formatter: function status(value, row, index) {
                        let html = `${row.deliAddress}<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>`

                        if (row.isChangeAddress == 1) {
                            html = html + `<span class="label label-dangerT"
                              style="padding:2px;cursor: pointer;" data-toggle="tooltip"
                              data-placement="left" data-html="true" title="${row.arriAddress}">改</span>${row.caArriAddress}`;
                        }else {
                            html = html + row.arriAddress
                        }

                        return html;
                    }
                },*/
                {
                    title: '提货省市区',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let html = ``
                        let list = row.shippingAddressList;
                        for (const elem of list) {
                            if (elem.addressType == 0) {
                                let addr = '';
                                let addrDe = '';
                                if(elem.provinceName=="上海市"||elem.provinceName=="北京市"||elem.provinceName=="天津市"||elem.provinceName=="重庆市"){
                                    addr =  elem.provinceName + elem.cityName + elem.areaName;
                                    addrDe = elem.provinceName  + elem.areaName+ elem.detailAddr;
                                }else{
                                    addr = elem.provinceName + elem.cityName + elem.areaName;
                                    addrDe =  elem.cityName + elem.areaName+ elem.detailAddr;
                                }
                                html += `<span>${addr}</span></br>`;

                            }
                        }
                        return html;
                    }
                },
                {
                    title: '到货省市区',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let html = ``
                        let list = row.shippingAddressList;
                        for (const elem of list) {
                            if (elem.addressType == 1) {
                                let addr = '';
                                let addrDe = '';
                                let caAddr = '';
                                if(elem.isChangeAddress == 1){
                                    if(elem.caArriProName=="上海市"||elem.caArriProName=="北京市"||elem.caArriProName=="天津市"||elem.caArriProName=="重庆市"){
                                        addr =  elem.caArriProName+elem.caArriCityName   + elem.caArriAreaName;
                                        addrDe = elem.caArriProName  + elem.caArriAreaName+ elem.caArriDetailAddr;
                                    }else{
                                        addr =  elem.caArriProName+ elem.caArriCityName + elem.caArriAreaName;
                                        addrDe =  elem.caArriCityName + elem.caArriAreaName+ elem.caArriDetailAddr;
                                    }
                                    caAddr = elem.provinceName + elem.cityName + elem.areaName + elem.detailAddr;
                                    html += `<span class="label label-dangerT"
                                  style="padding:1px;cursor: pointer;" data-toggle="tooltip"
                                  data-placement="left" data-html="true" title="${caAddr}">改</span>`;
                                    html += `<span >${addr}</span></br>`;
                                }else{
                                    if(elem.provinceName=="上海市"||elem.provinceName=="北京市"||elem.provinceName=="天津市"||elem.provinceName=="重庆市"){
                                        addr = elem.provinceName  + elem.cityName + elem.areaName;
                                        addrDe = elem.provinceName  + elem.areaName+ elem.detailAddr;
                                    }else{
                                        addr =  elem.provinceName  + elem.cityName + elem.areaName;
                                        addrDe =  elem.cityName + elem.areaName+ elem.detailAddr;
                                    }
                                    html += `<span>${addr}</span></br>`;
                                }
                            }
                        }
                        return html;

                    }
                },

                {title: '不含税单价(元)',align: 'right',field: 'unitPriceIncludeTax',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }

                        // if (row.taxRate) {
                        //     value = value/row.taxRate
                        // }

                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {title: '运费(元)',align: 'right',field: 'receiptAmountFreight',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {title: '在途(元)',align: 'right',field: 'receiptAmountOnWay',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {title: '对账总金额(元)',align: 'right',field: 'receiptAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {title: '发货单应收(元)',align: 'right',field: 'ys',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                // {title: '应付运费(元)',align: 'right',field: 'costShareFreight',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                // {title: '应付在途(元)',align: 'right',field: 'costShareOnWay',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                /*[# th:if="${@permission.hasAnyPermissions('finance:receCheckSheet:revenue,finance:fleet:receCheckSheet:revenue')}"]*/
                {title: '应付(元)',align: 'right',field: 'yf',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {title: '三方(元)',align: 'right',field: 'dsf',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {title: '平台费(元)',align: 'right',field: 'ptf',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {title: '总成本(元)',align: 'right',field: 'dsfsf',
                    formatter: function (value, row, index) {
                        let cb = row.yf + row.dsf + row.ptf

                        return cb.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {title: '应付税金(元)',align: 'right',field: 'yfsf',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {title: '三方税金(元)',align: 'right',field: 'dsfsf',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                /*[/]*/

                // {title: '调整后金额(元)',align: 'right',field: 'adjustAmount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                // {title: '已收金额(元)',align: 'right',field: 'gotAmount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                // {title: '未收金额(元)',align: 'right',field: 'ungotAmount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                {title: '货品',field: 'goodsName'},
                {title: '总件数',field: 'numCountAdjust'},
                {title: '总重量',align: 'left',field: 'weightCountAdjust'},
                {title: '总体积',align: 'left',field: 'volumeCountAdjust'},
                {title: '车长车型', field: 'carLenTypeName', align: 'left'},
                {title: '结算客户', field: 'balaName', align: 'left'},
                {title: '结算方式',align: 'left',field: 'balatype',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(balatype, value);
                    }
                },
                {title: '结算公司', field: 'balaCorp', align: 'left'}
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key===13){
                //查询方法
                $.table.search();
            }
        });
    });

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $("#role-form").serializeArray();
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    //总金额
                    $("#allTransFeeCount").text(data.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //总金额
                    $("#allAdjustAmountTotal").text(data.adjustAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //运费类型金额
                    $("#allFreightTotal").text(data.freightTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //在途类型金额
                    $("#allOnWayTotal").text(data.onWayTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    function remove() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        var rows = $.table.selectColumns("invoiceId");
        var isClose = $.table.selectColumns("isClose");
       /* if(isClose == 1){
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }*/

        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.confirm("确认要删除选中的数据吗?", function() {
            var url = prefix + "/remove";
            var data = { "ids": rows.join(), "receCheckSheetId": receCheckSheetId };
            $.operate.submit(url, "post", "json", data);
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        receiptAmountFreightTotal = 0;//运费
        receiptAmountOnWayTotal = 0;//在途
        receiptAmountTotal = 0;//对账总金额
        invTransFeeCountTotal = 0;//发货单应收
        costShareFreightTotal = 0;//应付运费
        costShareOnWayTotal = 0;//应付在途
        invOtherFeeTotal = 0;//三方
        totalCostTotal = 0;//总成本
        // adjustAmountTotal = 0;//调整金额
        // gotAmountTotal = 0;//已收
        // ungotAmountTotal = 0;//未收
    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        receiptAmountFreightTotal = receiptAmountFreightTotal + row.receiptAmountFreight;//运费
        receiptAmountOnWayTotal = receiptAmountOnWayTotal + row.receiptAmountOnWay;//总金额
        receiptAmountTotal = receiptAmountTotal + row.receiptAmount;//对账总金额
        invTransFeeCountTotal = invTransFeeCountTotal + row.invTransFeeCount;//发货单应收
        costShareFreightTotal = costShareFreightTotal + row.costShareFreight;//应付运费
        costShareOnWayTotal = costShareOnWayTotal + row.costShareOnWay;//应付在途
        invOtherFeeTotal = invOtherFeeTotal + row.invOtherFee;//三方
        totalCostTotal = totalCostTotal + row.totalCost;//总成本


        // adjustAmountTotal = adjustAmountTotal + row.adjustAmount;//调整金额
        // gotAmountTotal = gotAmountTotal + row.gotAmount;//已收
        // ungotAmountTotal = ungotAmountTotal + row.ungotAmount;//未收
    }

    function subTotal(row) {
        receiptAmountFreightTotal = receiptAmountFreightTotal - row.receiptAmountFreight;//运费
        receiptAmountOnWayTotal = receiptAmountOnWayTotal - row.receiptAmountOnWay;//总金额
        receiptAmountTotal = receiptAmountTotal - row.receiptAmount;//对账总金额
        invTransFeeCountTotal = invTransFeeCountTotal - row.invTransFeeCount;//发货单应收
        costShareFreightTotal = costShareFreightTotal - row.costShareFreight;//应付运费
        costShareOnWayTotal = costShareOnWayTotal - row.costShareOnWay;//应付在途
        invOtherFeeTotal = invOtherFeeTotal - row.invOtherFee;//三方
        totalCostTotal = totalCostTotal - row.totalCost;//总成本
        // adjustAmountTotal = adjustAmountTotal - row.adjustAmount;//调整金额
        // gotAmountTotal = gotAmountTotal - row.gotAmount;//已收
        // ungotAmountTotal = ungotAmountTotal - row.ungotAmount;//未收
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#receiptAmountFreightTotal").text(receiptAmountFreightTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#receiptAmountOnWayTotal").text(receiptAmountOnWayTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#receiptAmountTotal").text(receiptAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#invTransFeeCountTotal").text(invTransFeeCountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#costShareFreightTotal").text(costShareFreightTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#costShareOnWayTotal").text(costShareOnWayTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#invOtherFeeTotal").text(invOtherFeeTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#totalCostTotal").text(totalCostTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        // $("#adjustAmountTotal").text(adjustAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        // $("#gotAmountTotal").text(gotAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        // $("#ungotAmountTotal").text(ungotAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }
    /**
     * 详情
     */
    function receiveDetail() {
        var id = $.table.selectColumns('invoiceId');
        $.modal.openTab("应收明细", prefix + "/receive_detail?invoiceId=" + id + "&vbillstatus=&isApply=0"
            + "&receCheckSheetId=" + $.table.selectColumns('receCheckSheetId'));
    }

    function adjustExport(){
        var receCheckSheetId = $("#receCheckSheetId").val();
        $.modal.confirm("确定导出所有" +
            $.table._option.modalName + "吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(prefix + "/exportAdjust?receCheckSheetId="+receCheckSheetId, null, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

</script>
<script>

    /**
     * 客户的选择框
     */
    function selectClient() {
        $.modal.open("选择客户", ctx + "client/related",'','',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                parent.$.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //客户id
            $("#balaName").val(rows[0]["customerId"]);
            //客户名称
            $("#balaCustomerId").val(rows[0]["custName"]);

            layer.close(index);
        });
    }


    /**
     * 跳转应收修改页面
     * @param id
     */
    function edit(id,receiveStatus) {
       if (receiveStatus != 0) {
           $.modal.alertWarning("只能修改新建状态的应收单");
           return;
       }
        var url = prefix + "/edit?receiveDetailId="+id;
        $.modal.openTab("应收明细修改", url);
    }



</script>

</body>
</html>