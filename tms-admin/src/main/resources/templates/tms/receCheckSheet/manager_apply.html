<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方支付')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <form id="role-form" class="form-horizontal">
            <input type="hidden" name="receCheckSheetId" id="receCheckSheetId" th:value="${receCheckSheetId}">
        </form>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary"  onclick="feeEntry()" shiro:hasPermission="finance:managerFee:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary"  onclick="apply()" shiro:hasPermission="finance:managerFee:apply">
                <i class="fa fa-check-circle-o"></i> 申请
            </a>
             <a class="btn btn-danger" onclick="remove()" shiro:hasPermission="finance:managerFee:delete">
                 <i class="fa fa-remove"></i> 删除
             </a>
        </div>
        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "receCheckSheet";

    //付款方式
    var payMethod = [[${@dict.getType('pay_method')}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        var options = {
            url: prefix + "/ManagerFeeList",
            removeUrl: prefix + "/delete",
            showToggle:false,
            showColumns:false,
            clickToSelect:true,
            showSearch: false,
            modalName: "管理费",
            uniqueId: "managerFeeId",
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="修改" onclick="edit(\'' + row.managerFeeId + '\',\'' + row.payStatus + '\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                        return actions.join('');
                    }

                },
                {
                    title: '管理费单据号',
                    align: 'left',
                    field: 'glfVbillno'
                },
                {
                    title: '应收对账单号',
                    align: 'left',
                    field: 'vbillno'
                },
                {
                    title: '客户简称',
                    align: 'left',
                    field: 'custAbbr'
                },
                {
                    title: '付款状态',
                    align: 'left',
                    field: 'payStatus',
                    formatter: function (value, row, index) {
                        if (value == 0) {
                            return '<span class="label label-default">新建</span>';
                        }else if(value == 1){
                            return '<span class="label label-success">已申请</span>';
                        }else if(value == 2){
                            return '<span class="label label-primary">已付款</span>';
                        }
                        return "";
                    }
                },
                {
                    title: '管理费',
                    align: 'right',
                    field: 'managerFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '付款方式',
                    align: 'left',
                    field: 'payMethod',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(payMethod, value.payMethod);
                    }
                },
                {
                    title: '收款人',
                    align: 'left',
                    field: 'receiveMan',
                },
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'receiveBank',

                },
                {
                    title: '收款账号',
                    align: 'left',
                    field: 'receiveCard',
                },
                {
                    title: '申请人',
                    align: 'left',
                    field: 'applyUser',
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'applyTime',
                },
                {
                    title: '退回原因',
                    align: 'left',
                    field: 'backMemo',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.backMemo);
                    }

                },
                {
                    title: '备注',
                    align: 'left',
                    field: 'memo',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.memo);
                    }

                }

            ]
        };

        $.table.init(options);
    });


    /**
     * 删除
     */
    function remove() {
        var rows = $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var payStatus = $.table.selectColumns("payStatus");
        for(var i=0 ; i< payStatus.length ; i++){
            if(payStatus[i] != 0){
                $.modal.alertWarning("只有新建状态的管理费记录可以删除")
                return false;
            }
        }
        $.modal.confirm("确认要删除选中的管理费吗?", function() {
            var data = {ids:rows.join()};
            $.operate.submit(prefix + "/deleteManagerFee", "post", "json", data);
        });
    }

    /**
     * 申请
     */
    function apply() {
        var payStatus = $.table.selectColumns("payStatus");
        for(var i=0 ; i< payStatus.length ; i++){
            if(payStatus[i] != 0){
                $.modal.alertWarning("请选择新建状态的记录")
                return false;
            }
        }
        var rows = $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要申请支付选中的管理费吗?", function() {
            var data = {ids:rows.join(),receCheckSheetId:$("#receCheckSheetId").val()};
            $.operate.submit(prefix + "/applyManagerFee", "post", "json", data);
        });
    }

    /**
     * 第三方费用录入
     */
    function feeEntry() {
        var id = $("#receCheckSheetId").val();
        layer.open({
            type: 2,
            area: ['60%', '80%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "管理费录入",
            content: prefix + "/feeEntry/" + id,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function(index) {
                return true;
            }
        });

    }

    function edit(id,status) {
        if(status != 0){
            $.modal.alertWarning("请选择新建状态的记录")
            return false;
        }
        layer.open({
            type: 2,
            area: ['60%', '80%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "管理费修改",
            content: prefix + "/editManagerFee/" + id ,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function(index) {
                return true;
            }
        });
    }
</script>
</body>
</html>