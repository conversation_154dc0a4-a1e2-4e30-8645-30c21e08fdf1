<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反确认')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-invoice-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="receCheckSheetId" name="receCheckSheetId" type="hidden" th:value="${receCheckSheetId}">
            <div class="panel-body">
                <!--基础信息 begin-->
                <div class="row">
                    <div class="col-xs-6">
                        <div class="form-group" style="display: -webkit-box">
                            <label class="col-sm-5">调账金额(差价)：</label>
                            <div class="col-sm-7">
                                <div style="display:inline-table;">
                                    <input id="adjustAmount" name="adjustAmount"  class="form-control"  type="number" required placeholder="请输入..." autocomplete="false">
                                    <span class="input-group-addon">元</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="form-group" style="display: -webkit-box">
                            <label class="col-sm-5" style="width: 120px">调账原因(哪些订单上调整的需要重点说明)：</label>
                            <div class="col-md-8">
                                <textarea name="adjustMemo" id="adjustMemo" class="form-control" type="text"
                                          maxlength="500" required="" aria-required="true" rows="5" style="width:380px"  placeholder="请输入..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "receCheckSheet";

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            let iframe = document.getElementById("print-frame");
            if (!iframe) {
                iframe = document.createElement('IFRAME');
                iframe.id = "print-frame"
                document.body.appendChild(iframe);
                iframe.setAttribute('style', 'display:none;');
            }
            let receCheckSheetId = $("#receCheckSheetId").val();
            let adjustAmount = $("#adjustAmount").val();
            let adjustMemo = $("#adjustMemo").text();
            var strContent = document.getElementById("adjustMemo").value;
            strContent = strContent.replace(/\r\n/g, 'brFlag'); //IE9、FF、chrome
            strContent = strContent.replace(/\n/g, 'brFlag'); //IE7-8
            strContent = strContent.replace(/\s/g, ' '); //空格处理
            console.log(strContent)
            iframe.src = encodeURI(prefix + "/adjustPayPrint?receCheckSheetId="+receCheckSheetId+"&adjustAmount="+adjustAmount+"&adjustMemo="+strContent);
            iframe.onload = function () { //解决图片显示不了的问题
                iframe.contentWindow.focus();
                iframe.contentWindow.print();
            };
        }
    }



</script>
</body>
</html>