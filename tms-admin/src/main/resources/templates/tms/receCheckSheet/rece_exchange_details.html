<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户往来明细')"/>
</head>

<body>
<div class="form-content">
    <form id="form-user-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">往来明细</a>
                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table"  >
                                <thead>
                                <tr>
                                    <th style="width: 8%;text-align: left">单据日期</th>
                                    <th style="width: 8%;text-align: left">单据编号</th>
                                    <!--<th style="width: 8%;">单据类型</th>-->
                                    <th style="width: 10%;text-align: left">期初金额</th>
                                    <th style="width: 12%;text-align: left">本期增加</th>
                                    <th style="width: 8%;text-align: left">本期减少</th>
                                    <th style="width: 8%;text-align: left">余额</th>

                                </tr>
                                </thead>
                                <tbody>

                                <tr th:each="receDealings,receDealingsStat: ${receDealingsList}">
                                    <td style="text-align: left">-</td>
                                    <td style="text-align: left">-</td>

                                    <td style="text-align: right" th:text="￥+${#numbers.formatDecimal(receDealings.startMoney,1,'COMMA',2,'POINT')}" id="startMoney"></td>
                                    <td style="text-align: right" th:text="￥+${#numbers.formatDecimal(receDealings.addMoney,1,'COMMA',2,'POINT')}"  id="addMoney"></td>
                                    <td style="text-align: right" th:text="￥+${#numbers.formatDecimal(receDealings.reduceMoney,1,'COMMA',2,'POINT')}" id="reduceMoney"></td>
                                    <td style="text-align: right" th:text="￥+${#numbers.formatDecimal(receDealings.balanceMoney,1,'COMMA',2,'POINT')}" id="balanceMoney"></td>
                                </tr>

                                <tr th:each="receDealingsDetail,receDealingsDetailStat : ${receDealingsDetailList}">
                                    <td style="text-align: left" th:text="${#dates.format(receDealingsDetail.vbillDate,'yyyy-MM-dd')}"></td>
                                    <td style="text-align: left" th:text="${receDealingsDetail.vbillno}"></td>
                                    <!--<td  th:if="${receDealingsDetail.vbillType !='' and receDealingsDetail.vbillType!=null}"
                                         th:text="${@dict.getLabel('rece_type',receDealingsDetail.vbillType)}"></td>-->
                                  <!--  <td th:if="${receDealingsDetail.vbillType !='' and receDealingsDetail.vbillType!=null}"
                                        th:text="${@dict.getLabel('rece_type',receDealingsDetail.vbillType)}"></td>-->
                                    <td style="text-align: right">-</td>
                                    <td style="text-align: right" th:text="￥+${#numbers.formatDecimal(receDealingsDetail.billAdd,1,'COMMA',2,'POINT')}"></td>
                                    <td style="text-align: right" th:text="￥+${#numbers.formatDecimal(receDealingsDetail.billReduce,1,'COMMA',2,'POINT')}"></td>
                                    <td style="text-align: right">-</td>
                                </tr>


                                <tr>
                                    <th style="width: 8%;text-align: left" >合计</th>
                                    <td style="text-align: left">-</td>

                                    <td id="sumStartMoney" style="text-align: right"></td>
                                    <td th:if="${addMoney != null}" style="text-align: right" th:text="￥+${#numbers.formatDecimal(addMoney,1,'COMMA',2,'POINT')}"|></td>
                                    <td th:if="${reduceMoney !=null}" style="text-align: right" th:text="￥+${#numbers.formatDecimal(reduceMoney,1,'COMMA',2,'POINT')}"|></td>
                                    <td id="sumBalanceMoney" style="text-align: right"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>

        </div>

        <div class="row">
             <div class="col-sm-offset-5 col-sm-10">
                 <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
                 </button>
             </div>
         </div>

    </form>
</div>


<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    //期初金额
    var startMoney = $("#startMoney").text();
    //总计期初金额
    $("#sumStartMoney").text(startMoney);
    //余额
    var balanceMoney = $("#balanceMoney").text();
    //总计余额
    $("#sumBalanceMoney").text(balanceMoney);

    //增加金额
    var addMoney =parseFloat($("#addMoney").text());
    //减少金额
    var reduceMoney = parseFloat($("#reduceMoney").text());
    if(isNaN(addMoney)){
        addMoney = 0;
    }

    //往来明细
    // var receDealingsDetailList = [[${receDealingsDetailList}]];
    // for(var i=0;i<receDealingsDetailList.length;i++){
    //     if(isNaN(parseFloat(receDealingsDetailList[i].billAdd))){
    //        receDealingsDetailList[i].billAdd= 0;
    //     }
    //     if(isNaN(parseFloat(receDealingsDetailList[i].billReduce))){
    //         receDealingsDetailList[i].billReduce = 0;
    //     }
    //     addMoney += parseFloat(receDealingsDetailList[i].billAdd);
    //     reduceMoney += parseFloat(receDealingsDetailList[i].billReduce);
    // }
    //合计
    // $("#sumAddMoney").text(addMoney);
    // $("#sumReduceMoney").text(reduceMoney);

    $(function () {
        $('#collapseOne').collapse('show');
    });

</script>
</body>

</html>