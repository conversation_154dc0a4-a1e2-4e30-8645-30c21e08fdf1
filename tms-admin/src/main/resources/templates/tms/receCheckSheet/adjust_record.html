<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款申请管理')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "receive/receive_detail";
    //运费费用类型
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    //在途费用类型
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];

    var receiveDetailStatus = [[${receiveDetailStatus}]];
    var receiveDetailId = [[${receiveDetailId}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: prefix + "/list?adjustReceiveDetailId="+receiveDetailId,
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            showToggle: false,
            showColumns: false,
            showSearch: false,
            showPageGo: false,
            showRefresh: false,
            modalName: "应收汇总",
            clickToSelect:true,
            height: 560,
            showFooter:true,
            uniqueId: "receiveDetailId",
            columns: [
                {field: 'vbillno',align: 'left',title: '应收单号'},
                {field: 'vbillstatus',title: '单据状态',align: 'left',
                    formatter: function status(value,row) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-primary">新建</span>'
                            case 1:
                                return '<span class="label label-warning">已确认</span>';
                            case 2:
                                return '<span class="label label-coral">已对账</span>';
                            case 3:
                                return '<span class="label label-info">部分核销 </label>';
                            case 4:
                                return '<span class="label label-success">已核销</span>';
                            case 5:
                                return '<span class="label label-inverse">关闭 </label>';
                            default:
                                break;
                        }
                    }
                },
                {field: 'freeType',title: '费用类型',align: 'left',
                    formatter: function status(value,row) {
                                    switch(value - 0) {
                                        case 0:
                                            return '<span>运费</label>';
                                        case 1:
                                            return '<span>在途费用</label>';
                                        case 2:
                                            return '<span>调整费用</label>';
                                        default:
                                            break;
                                    }
                             }
                },
                {field: 'costTypeOnWay',title: '费用类型明细',align: 'left',
                    formatter: function status(value,row) {
                                    switch(row.freeType - 0) {
                                        case 0:
                                            return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                                        case 1:
                                            return $.table.selectDictLabel(costTypeOnWay, value);
                                        case 2:
                                            return '<span>调整费用</label>';
                                        default:
                                            break;
                                    }
                                }
                },
                {field: 'transFeeCount',title: '金额',align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {field: 'applCheckAmount',title: '申请开票金额',align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {field: 'checkAmount',title: '已开票金额',align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {field: 'memo',title: '备注',align: 'left'},
                {field: 'isAdjust',title: '是否为调整单',align: 'left',
                    formatter: function (value, row, index) {
                                    if (value === 0) {
                                        return "否";
                                    } else {
                                        return "是";
                                    }
                                }
                },
                {field: 'adjustMemo',title: '调整原因',align: 'left'},
                {field: 'regDate',title: '创建时间',align: 'left'},
                {field: 'regUserId',title: '创建人',align: 'left'},
                {field: 'isApply',title: '是否单笔申请',align: 'left',
                    formatter: function status(value,row) {
                        if (value === 1) {
                            return '<span>已申请</label>';
                        } else {
                            return '<span>未申请</label>';
                        }
                    }
                },
                {field: 'replyMemo',title: '撤销审核备注',align: 'left'}
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });

    /**
     * 批量删除
     */
    function remove() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("只能删除新建状态下的应收单据");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应收单据");
                return;
            }
        }
        var rows = $.table.selectColumns($.table._option.uniqueId);
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要删除选中的数据吗?", function() {
            var url = ctx + "receive/remove";
            var data = { "ids": rows.join() };
            $.operate.submit(url, "post", "json", data);
        });
    }


    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;
    function receRecord(){
        var id = $.table.selectColumns('receiveDetailId');
        var url =  ctx + "receSheetRecord" + "/receRecord?receiveDetailId="+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "收款记录",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }


    /**
     * 跳转应收修改页面
     * @param id
     */
    function edit(id,vbillstatus,isClose) {
        if (vbillstatus != 0) {
            $.modal.alertWarning("只能修改新建状态的应收单");
            return;
        }
        if (isClose === '1') {
            $.modal.alertWarning("该应收单已关账");
            return;
        }
        var url = ctx + "receive/edit?receiveDetailId="+id;
        $.modal.open("应收明细修改", url, width, height);
    }

    /**
     * 收款申请
     */
    function receiptAppl(){
        //应收状态
        var vbillstatus = $.table.selectColumns('vbillstatus').join();
        //应收id
        var id = $.table.selectColumns('receiveDetailId');
        if (vbillstatus != 1) {
            $.modal.alertError("请选择已确认的数据");
            return;
        }
        var url = ctx + "receive/receiptAppl/"+id;
        $.modal.openTab('收款/开票申请',url);
    }

    /**
     *
     * 生成对账单的方法
     */
    function checking() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("请选择已确认的数据！");
                return;
            }

            if (bootstrapTable[i]["isApply"] === 1) {
                $.modal.alertWarning("请选择未单独申请的数据！");
                return;
            }

        }
        //应收明细id
        var receiveDetsailIds = $.table.selectColumns('receiveDetailId').join();

        $.modal.openTab("生成对账单", ctx + "receive/checking?invoiceId=&receiveDetsailIds=" + receiveDetsailIds);

    }

    /**
     * 加入对账单的方法
     */
    function insertChecking() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("请选择已确认的数据！");
                return;
            }

            if (bootstrapTable[i]["isApply"] === 1) {
                $.modal.alertWarning("请选择未单独申请的数据！");
                return;
            }
        }
        //承运商id
        var customerId =  bootstrapTable[0]["customerId"];
        //应收明细id
        var receiveDetsailIds = $.table.selectColumns('receiveDetailId').join();

        $.modal.open("加入对账单", ctx + "receive/insertChecking?customerId="
            + customerId + "&invoiceIds=&receiveDetsailIds=" + receiveDetsailIds);

    }
</script>


</body>
</html>