<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('退回')"/>
</head>

<body>
<div class="form-content">
    <form id="form-invoice-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <!--第三方费用id-->
            <input  name="managerFeeId" type="hidden" th:value="${managerFeeId}">
            <div class="panel-body">
                <input name="deptId" type="hidden" id="treeId">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5" style="color: red">退回说明：</label>
                            <div class="col-sm-12">
                                <textarea name="backMemo"  class="form-control" type="text" th:required="true"
                                          maxlength="500" required="" aria-required="true"></textarea>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "receCheckSheet";

    /**
     * 校验
     */
    $("#form-invoice-unconfirm").validate({
        onkeyup: false,
        focusCleanup: true,
    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-invoice-unconfirm").serializeArray();
            $.operate.save(prefix + "/saveBack", data);
        }
    }




</script>
</body>
</html>