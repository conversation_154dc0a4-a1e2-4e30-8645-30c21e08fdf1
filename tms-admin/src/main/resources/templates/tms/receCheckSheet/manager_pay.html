<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方支付')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
        <form id="role-form" class="form-horizontal">
            <div class="row">
                <div class="col-md-2 col-sm-4">
                    <div class="form-group">
<!--                        <label class="col-sm-4">管理费单据号：</label>-->
                        <div class="col-sm-12">
                            <input name="glfVbillno" placeholder="请输入管理费单据号" class="form-control" type="text"
                                   maxlength="30"  aria-required="true">
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4">
                    <div class="form-group">
<!--                        <label class="col-sm-4">对账单号：</label>-->
                        <div class="col-sm-12">
                            <input name="vbillno" placeholder="请输入应收对账单号" class="form-control" type="text"
                                   maxlength="30"  aria-required="true">
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-3">状态：</label>
                        <div class="col-sm-9">
                            <select class="form-control valid"  aria-invalid="false" name="payStatus">
                                <option value="1">已申请</option>
                                <option value="2">已付款</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-4">
                    <div class="form-group">
                        <label class="col-sm-4">付款方式：</label>
                        <div class="col-sm-8">
                            <select class="form-control valid"  aria-invalid="false"
                                    name="payMethod" th:with="type=${@dict.getType('pay_method')}" required>
                                <option></option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-2 col-sm-4">
                    <div class="form-group">
                        <!--                        <label class="col-sm-4">收款人：</label>-->
                        <div class="col-sm-12">
                            <input name="receiveMan" placeholder="请输入收款人" class="form-control" type="text"
                                   maxlength="30"  aria-required="true">
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4">
                    <div class="form-group">
<!--                        <label class="col-sm-4">支付人：</label>-->
                        <div class="col-sm-12">
                            <input name="payMan" placeholder="请输入支付人" class="form-control" type="text"
                                   maxlength="30"  aria-required="true">
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="form-group">
<!--                        <label class="col-sm-2">付款时间：</label>-->
                        <div class="col-sm-12">
                            <input type="text" style="width: 45%; float: left;" class="form-control"
                                   id="startDate"  name="regDate" placeholder="付款开始时间">
                            <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                            <input type="text" style="width: 45%; float: left;" class="form-control"
                                   id="endDate"  name="corDate" placeholder="付款结束时间">
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4">
                    <div class="form-group">
<!--                        <label class="col-sm-6"></label>-->
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </div>
                </div>
            </div>
        </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary"  onclick="pay()" shiro:hasPermission="finance:managerFee:pay">
                <i class="fa fa-dollar"></i> 付款
            </a>
            <a class="btn btn-danger single disabled" onclick="revocation()" shiro:hasPermission="finance:managerFee:back">
                <i class="fa fa-file"></i> 退回
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "receCheckSheet";

    //付款方式
    var payMethod = [[${@dict.getType('pay_method')}]];

    $(function () {
        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endDate',
                type: 'date',
                trigger: 'click'
            });
        });
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        var options = {
            url: prefix + "/ManagerFeePayList",
            removeUrl: prefix + "/delete",
            showToggle:false,
            showColumns:false,
            clickToSelect:true,
            showSearch: false,
            modalName: "管理费",
            uniqueId: "managerFeeId",
            columns: [{
                checkbox: true
            },
               /* {
                    title: '操作',
                    align: 'center',
                    field: 'otherFeeId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs ' + editFlag + '" href="javascript:void(0)" title="修改" onclick="edit(\'' + row.otherFeeId + '\',\''+ row.vbillstatus+'\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                        return actions.join('');
                    }

                },*/
                {
                    title: '管理费单据号',
                    align: 'left',
                    field: 'glfVbillno'
                },
                {
                    title: '应收对账单号',
                    align: 'left',
                    field: 'vbillno'
                },
                {
                    title: '客户简称',
                    align: 'left',
                    field: 'custAbbr'
                },
                {
                    title: '付款状态',
                    align: 'left',
                    field: 'payStatus',
                    formatter: function (value, row, index) {
                        if (value == 0) {
                            return '<span class="label label-default">新建</span>';
                        }else if(value == 1){
                            return '<span class="label label-success">已申请</span>';
                        }else if(value == 2){
                            return '<span class="label label-primary">已付款</span>';
                        }
                        return "";
                    }
                },
                {
                    title: '管理费',
                    align: 'right',
                    field: 'managerFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '付款方式',
                    align: 'left',
                    field: 'payMethod',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(payMethod, value.payMethod);
                    }
                },
                {
                    title: '收款人',
                    align: 'left',
                    field: 'receiveMan',
                },
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'receiveBank',

                },
                {
                    title: '收款账号',
                    align: 'left',
                    field: 'receiveCard',
                },{
                    title: '申请人',
                    align: 'left',
                    field: 'applyUser',
                },{
                    title: '申请时间',
                    align: 'left',
                    field: 'applyTime',
                },
                {
                    title: '付款人',
                    align: 'left',
                    field: 'payMan',
                },
                {
                    title: '付款时间',
                    align: 'left',
                    field: 'payDate',
                },
                {
                    title: '备注',
                    align: 'left',
                    field: 'memo',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.memo);
                    }

                }

            ]
        };

        $.table.init(options);
    });

    function pay() {
        var status = $.table.selectColumns("payStatus");
        for(var i = 0 ; i < status.length ; i++){
            if(status[i] != 1){
                $.modal.alertWarning("请选择已申请的记录");
                return false;
            }
        }

        var rows = $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.confirm("确认已支付所有选中的记录?", function() {
            var data = { "ids": rows.join() };
            $.operate.submit(prefix + "/payManagerFee", "post", "json", data);
        });
    }

    //退回
    function revocation() {
        var vbillstatus = $.table.selectColumns("payStatus");
        for(var i=0 ; i< vbillstatus.length ; i++){
            if(vbillstatus[i] != 1){
                $.modal.alertWarning("只能退回申请状态的账单")
                return false;
            }
        }
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var url = prefix+"/back?managerFeeId="+rows.join();
        $.modal.open("退回",url,500,300);

    }
</script>
</body>
</html>