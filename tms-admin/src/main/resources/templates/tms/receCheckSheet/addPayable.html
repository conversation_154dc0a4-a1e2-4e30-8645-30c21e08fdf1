<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增发票抬头')"/>
</head>
<body class="gray-bg">
<div class="form-content">
    <form id="form-add" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group">
            <div style="font-weight: bold;margin-bottom: 5px;color:#555;">
                追加“[(${custName})]”的开票信息：
            </div>
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="customerId" type="hidden" th:value="${customerId}">
                        <div class="row">
                            <input type="hidden" name="billingCorp" th:value="${balaCorp}">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">发票抬头：</label>
                                    <div class="col-sm-8" >
                                        <input type="text" name="billingPayable" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">纳税识别号：</label>
                                    <div class="col-sm-8" >
                                        <input type="text" name="taxIdentify" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">开户银行：</label>
                                    <div class="col-sm-8">
                                        <input name="bank" class="form-control valid">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">开票类型：</label>
                                    <div class="col-sm-8">
                                        <select name="billingType" class="form-control valid" th:with="type=${@dict.getType('billing_type')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">开户账号：</label>
                                    <div class="col-sm-8">
                                        <input name="bankAccount" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2">地址及电话：</label>
                                    <div class="col-sm-10">
                                        <input name="addressPhone" class="form-control">
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    function submitHandler(index, layero, callback) {
        if ($.validate.form()) {
            var data = $('#form-add').serialize();
            $.modal.confirm("确定提交保存吗？", function() {
                $.operate.saveModal(ctx + "receCheckSheet/savePayable", data, function(r){
                    if (r.code == 0) {
                        callback(index, layero, r.data)
                    }
                });
            });
        }
    }
</script>
</body>
</html>