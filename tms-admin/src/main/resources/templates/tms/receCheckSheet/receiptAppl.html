<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款申请')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .memo{
        outline: none;
        border:none
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;
    }
    .fcff{
        color: #ff1f1f;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .tc{
        text-align: center;
    }
    .bor{
        border: 1px #eee solid;
    }
    .over{
        overflow: hidden;

    }
    .fl{
        float: left;
        position: relative;
        padding: 2px 2px 0 0;
    }
    .fl2 {
        float: left;
        position: relative;
        padding: 2px 0 0 0;
    }
    .boxs{
        /*border-bottom: 1px #eee solid;*/
        /*padding: 0 0 5px 0;*/
        box-shadow: 0px 0px 2px #eef0e5 inset;
        margin-bottom: 3px;
        border-top: 3px #99e979 solid;
    }
    .mt10{
        margin-top: 5px;
    }
    .pad{
        padding-right: 10px;
        box-sizing: border-box;
    }
    .pad10{
        padding: 5px 5px 0 0;
        box-sizing: border-box;
    }
    .table>thead>tr>th{
        background: #eff3f9 !important;
    }
    .qd{
        /*padding-right: 3px;*/
    }
    .qd_title{
        padding: 0 6px;
        background-color: #fdfce2;
        font-weight: bold;
        line-height: 28px;
        display: flex;
        align-items: center;
        border: 1px #55fcd3 solid;
        border-bottom: none;
    }
    .bot{
        border-bottom: 1px #eee solid;
    }
    .padt{
        padding-bottom: 2px;
    }
    .table>tbody>tr>td{
        background-color: #fff !important;
    }
    label.error {
        max-width: max-content;
    }
    .file-preview {
        padding: 1px;
        height: 69px !important;
    }
    /*.file-input:hover {*/
    /*    position: absolute;*/
    /*    z-index: 1;*/
    /*}*/
    .file-drop-zone {
        height: 59px !important;
        overflow: auto;
        margin: 3px 3px 3px 3px;
    }
    .file-drop-zone:hover {
        height: auto !important;
        overflow: auto;
        max-height: 500px;
        position: absolute;
        z-index: 10000;
        right: 0;
        margin-right: 4px;
        width: calc(100% - 8px);
        background-color: #fff;
    }
    .kv-file-content {
        height: inherit !important;
        max-height: 80px !important;
        max-width: 100px !important;
        width: inherit !important;
        overflow: hidden;
    }
    .theme-explorer .file-thumb-progress .progress, .theme-explorer .explorer-caption {
        width: 100px !important;
    }
    .theme-explorer .file-actions-cell {
        width: 40px !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-receiptAppl-add" class="form-horizontal" novalidate="novalidate">
        <!--对账单id-->
        <input name="tReceCheckSheetId" type="hidden" th:value="${receCheckSheet?.receCheckSheetId}">
        <!--客户id-->
        <input id="customerId" name="customerId" type="hidden" th:value="${receCheckSheet?.balaCustomer}"><!--2021/1/6为了相同结算客户可以一起收款申请-->
        <!--结算客户id-->
        <input name="balaCustomerId" type="hidden" th:value="${receCheckSheet?.balaCustomer}">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">申请信息</a>
                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body" style="padding: 5px 10px 5px;">
                        <!--基础信息 begin-->
                        <input name="deptId" type="hidden" id="treeId">
                        <div class="row">
                            <div class="col-md-9 col-sm-9">
                                <div class="row">
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 申请金额：</label>
                                            <div class="flex_right">
                                                <input name="receivableAmount" id="receivableAmount" type="text"
                                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this);"  disabled="" class="form-control"
                                                       maxlength="15" required th:value="${receCheckSheet.totalAmount}- ${receCheckSheet.gotAmount}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 申请日期：</label>
                                            <div class="flex_right">
                                                <input name="receivableDate" id="receivableDate" class="form-control" disabled>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left">可申请余额：</label>
                                            <div class="flex_right">
                                                <input class="form-control" th:value="${receCheckSheet.totalAmount}- ${receCheckSheet.gotAmount}" disabled>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 调整金额：</label>
                                            <div class="flex_right">
                                                <input name="adjustAmount" id="adjustAmount" type="text"
                                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this);" class="form-control"
                                                       th:value="${receCheckSheet.totalAmount}- ${receCheckSheet.gotAmount}"
                                                       maxlength="15" required>

                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="fcff">*如果需要打折调整总金额，请输入调整后的金额</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left" id="isCostAdjustLabel">成本是否调整：</label>
                                            <div class="flex_right">
                                                <select type="text" name="isCostAdjust" id="isCostAdjust" class="form-control valid">
                                                    <option value=""></option>
                                                    <option value="是">是</option>
                                                    <option value="否">否</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left" id="adjustMemoLabel">调整原因：</label>
                                            <div class="flex_right">
                                            <textarea id="adjustMemo" name="adjustMemo" maxlength="500" class="form-control valid"
                                                      rows="3"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left" id="costAdjustMemoLabel">成本调整描述：</label>
                                            <div class="flex_right">
                                            <textarea id="costAdjustMemo" name="costAdjustMemo" maxlength="500" class="form-control valid"
                                                      rows="3"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <label style="color:#666;line-height: 27px;">附件(可多选)：</label>
                                <div style="">
                                    <input id="image" name="image" type="file" multiple accept="image/jpeg, image/png">
                                    <input id="tid" name="attachTid" type="hidden">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" id="panelTwo">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#panelTwo"
                           href="tabs_panels.html#collapseTwo">其他力资费</a>
                    </h4>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body" style="padding: 5px 10px 5px;">

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">费用：</label>
                                    <div class="flex_right">
                                        <input name="tariffAmount" id="tariffAmount" type="text"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);initTariffTax()" class="form-control" title="有成本时必须录入费用"
                                               maxlength="15" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">成本：</label>
                                    <div class="flex_right">
                                        <input name="tariffCost" id="tariffCost" oninput="$.numberUtil.onlyNumberTwoDecimal(this);$('#tariffAmount').prop('required',this.value.trim())" class="form-control" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="flex">
                                    <label class="flex_left">含税构成：</label>
                                    <div class="flex_right">
                                        <div id="tariffTax">无</div>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default" id="isShow">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#isShow"
                       href="tabs_panels.html#collapseThree">开票信息</a>
                    <i class="fa fa-plus-square" onclick="addPayable()" style="color:#007dcc;font-weight: bold;cursor: pointer"> 新增发票抬头</i>
                    <i class="fa fa-sliders" onclick="adjustTax()" style="color:#e38d13;font-weight: bold;cursor: pointer"> 调整税额占比</i>
                    <span id="taxText" style="color:#999"></span>
                    <span style="float: right">
                    <a th:href="@{/static/template/billing.xls?v=20220111}" target="_blank" style="color:rgb(105,158,255)">模板下载</a>
                    <a href="javascript:;" style="color:blue" onclick="$('#ipt_file').click()">导入</a>
                    <a href="javascript:;" style="color:red" onclick="emptyIpt()">清空已导入</a>
                    <div id="ipt_file_div" style="display: none">
                    <input type="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" id="ipt_file" onchange="readExcel()">
                    </div>
                    </span>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseThree">
                <div class="panel-body" style="padding: 5px">
                    <div id="infoTabThree">
                        <table border="0" class="custom-tab table" >

                            <thead>
                            <tr>
                                <th style="width: 3%;">
                                    <a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRowThree()" title="新增行">+</a>
                                </th>
                                <!--                                <th style="width: 5%;color: red" >是否开票</th>-->
                                <th style="width: 14%;">开票类型</th>
                                <th style="width: 13%;" class="isCheck">发票抬头</th>
                                <th style="width: 11%;" class="isCheck">开票公司</th>
                                <th style="width: 8%;color: red" class="isCheck">金额</th>
                                <th style="width: 11%;" class="isCheck">纳税识别号</th>
                                <th style="width: 14%;" class="isCheck">开户银行</th>

                                <th style="width: 10%;" class="isCheck">开户账号</th>
                                <th style="" class="isCheck">地址及电话</th>
                                <!--                                <th style="width: 10%;">开票备注</th>-->
                                <!--                                <th style="width: 10%;">备注</th>-->
                            </tr>

                            </thead>
                        </table>
                        <!--/*-->
                        <!--<div class="over boxs add" th:each="receBilling:${receBillingList}">
                            <div class="fl" style="width: 3%;">&nbsp;</div>
                            <div class="fl" style="width: 97%">
                                <div class="over">
                                    <div th:if="${receBilling.billingType == null || receBilling.billingType == ''}" style="width: 15%;">
                                    </div>
                                    <div class="fl" th:each="dict : ${@dict.getType('billing_type')}"
                                         th:if="${dict.dictValue} == ${receBilling.billingType}"
                                         th:text="${dict.dictLabel}" style="width: 15%;">
                                    </div>
                                    <div class="fl" th:text="${receBilling.billingPayable}" style="width: 13%;"></div>
                                    <div class="fl" th:if="${receBilling.billingCorp == null || receBilling.billingCorp == ''}" style="width: 11%;"></div>
                                    <div class="fl"  th:each="dict : ${@dict.getType('bala_corp')}"
                                         th:if="${dict.dictValue} == ${receBilling.billingCorp}"
                                         th:text="${dict.dictLabel}" style="width: 11%;">
                                    </div>
                                    <div class="fl" th:text="'¥'+${#numbers.formatDecimal(receBilling.billingAmount,1,'COMMA',2,'POINT')}" style="width: 9%;">
                                    </div>
                                    <div class="fl" th:text="${receBilling.taxIdentify}" style="width: 11%;word-break: break-all;">
                                    </div>
                                    <div class="fl" th:text="${receBilling.bank}" style="width: 15%;">
                                    </div>

                                    <div class="fl" th:text="${receBilling.bankAccount}" style="width: 11%;word-break: break-all;">
                                    </div>
                                    <div class="fl" th:text="${receBilling.addressPhone}" style="width: 15%;">
                                    </div>
                                </div>
                                <div class="over mt10 padt bot">
                                    <div class="fl pad" style="width: 50%;">
                                    <textarea type="text" readonly th:text="${receBilling.memo}" maxlength="500" class="form-control" placeholder="开票备注">
                                    </textarea>
                                    </div>
                                    <div class="fl pad" style="width: 50%;">
                                    <textarea type="text" readonly th:text="${receBilling.remark}" maxlength="500" class="form-control" placeholder="备注">
                                    </textarea>
                                    </div>
                                </div>
                                <div class="qd mt10">
                                    <div class="qd_title">发票清单</div>
                                    <table class="custom-tab tab table table-bordered">
                                        <thead style="background: #f4f6f7;">
                                        <tr>
                                            <th style="width: 3%;">
                                                <a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow(this)" title="新增行">+</a>
                                            </th>
                                            <th style="width: 20%;">货物或应税劳务名称</th>
                                            <th style="width: 10%;">规格型号</th>
                                            <th>单位</th>
                                            <th>数量</th>
                                            <th>单价</th>
                                            <th>金额</th>
                                            <th>税率</th>
                                            <th>税额</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td>
                                                <a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRow(this)" title="删除选择行"></a>
                                            </td>
                                            <td>
                                                <select type="text" class="form-control valid">
                                                    <option value=""></option>
                                                    <option value="1">是</option>
                                                    <option value="2">否</option>
                                                </select>
                                            </td>
                                            <td><input class="form-control valid"/></td>
                                            <td><input class="form-control valid"/></td>
                                            <td><input class="form-control valid"/></td>
                                            <td><input class="form-control valid"/></td>
                                            <td><input class="form-control valid"/></td>
                                            <td><input class="form-control valid"/></td>
                                            <td><input class="form-control valid"/></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>-->
                        <!--*/-->
                    </div>
                </div>
            </div>
        </div>
        <input type="hidden" name="taxSplitJson">
        <input type="hidden" name="tariffTaxTxt">
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" id="btn1" class="btn btn-sm btn-primary" th:if="${receCheckSheet.applicationStatus != 2}" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger"  onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/js/xlsx.full.min.js}"></script>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    //是否是车队
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/receCheckSheet" : ctx + "receCheckSheet";

    /**
     * 关键字提示查询 转入账户
     */
    $("#accountName").bsSuggest('init', {
        url: ctx + "finance/account/findAccount?paymentType=0&keyword=",
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["accountName"],
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#accountName").val(data.accountName);
        $("#inAccount").val(data.accountId);
    })


    /**
     * 申请日期默认当前日期
     */
    var time = new Date();
    var day = ("0" + time.getDate()).slice(-2);
    var month = ("0" + (time.getMonth() + 1)).slice(-2);
    var h=time.getHours();       //获取当前小时数(0-23)
    var m=time.getMinutes();     //获取当前分钟数(0-59)
    var s=time.getSeconds();     //获取当前秒数
    var today = time.getFullYear() + "-" + (month) + "-" + (day) + " "+h+":" +m+ ":"+s;
    $("#receivableDate").val(today);

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseThree').collapse('show');
        /** 校验 */
        $("#form-receiptAppl-add").validate({
            focusCleanup: true
        });
        //开票默认是 禁用不开票
        //$("#billingType0 option[value = '6']").attr("disabled",true);
        //开票类型默认为 增值税专用发票(9%)
        //$("#billingType0 option[value = '4']").attr("selected",true);
        //$("#billingType0").val(4);
        var picParam = {
            //maxFileCount: 1,
            publish: 'uploadDone',  //用于绑定下一步方法
            fileType: '' //文件类型非file
        };
        $.file.initAddFiles("image", "tid", picParam);

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#receivableDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        insertRowThree()

        getCheckSheetTax();
    });



    /**
     * 选择发票抬头 带出客户发票信息
     */
    /*function selectBillingPayable(billingIndex) {
        var id = $('#billingPayable'+billingIndex).find("option:selected").attr("id");//开票信息id
        var url = ctx + "receCheckSheet/selectCustBilling";
        var data = {custBillingId:id};
        $.ajax({
            url: url,
            data: data,
            method: 'post',
            success: function (data) {
                //带入开票信息
                $("#billingCorp"+billingIndex).val(data.billingCorp);//开票公司
                $("#billingType"+billingIndex).val(data.billingType);//发票类型
                $("#bank"+billingIndex).val(data.bank);//开户行
                $("#bankAccount"+billingIndex).val(data.bankAccount);//账号
                $("#taxIdentify"+billingIndex).val(data.taxIdentify);//纳税人识别号
                $("#addressPhone"+billingIndex).val(data.addressPhone);//地址及电话
            }
        });
    }*/
    var payableCache = {};
    function selectBillingPayable(billingIndex) {
        var id = $('#billingPayable'+billingIndex).find("option:selected").attr("id");//开票信息id
        if (!id) {
            if ($("#billingType"+billingIndex).val() != '4') {
                $("#billingType"+billingIndex).val(4).change()
                $.modal.msgWarning("开票类型已重置");
            }
            $("#billingCorp" + billingIndex).val('');//开票公司
            $("#billingCorpName" + billingIndex).text('');//开票公司
            $("#bank" + billingIndex).val('');//开户行
            $("#bankAccount" + billingIndex).val('');//账号
            $("#taxIdentify" + billingIndex).val('');//纳税人识别号
            $("#addressPhone" + billingIndex).val('');//地址及电话
        } else {
            var fun1 = function(data) {
                //带入开票信息
                $("#billingCorp" + billingIndex).val(data.billingCorp);//开票公司
                for (let i = 0; i < billingCorp.length; i++) {
                    if (billingCorp[i].dictValue == data.billingCorp) {
                        $("#billingCorpName" + billingIndex).text(billingCorp[i].dictLabel);
                        break;
                    }
                }
                if ($("#billingType" + billingIndex).val() != data.billingType) {
                    $.modal.msgWarning("开票类型已变更");
                }
                $("#billingType" + billingIndex).val(data.billingType).change();//发票类型
                $("#bank" + billingIndex).val(data.bank);//开户行
                $("#bankAccount" + billingIndex).val(data.bankAccount);//账号
                $("#taxIdentify" + billingIndex).val(data.taxIdentify);//纳税人识别号
                $("#addressPhone" + billingIndex).val(data.addressPhone);//地址及电话
                var fields = ['billingPayable', 'billingCorp', 'taxIdentify', 'bank', 'bankAccount', 'addressPhone'];
                for (let i = 0; i < fields.length; i++) {
                    $("#form-receiptAppl-add").validate().element($("#" + fields[i] + billingIndex))
                }

            }
            if (payableCache[id]) {
                fun1(payableCache[id])
            } else {
                var url = ctx + "receCheckSheet/selectCustBilling";
                var data = {custBillingId: id, time: new Date().getTime().toString().substring(0, 10)};
                $.ajax({
                    url: url,
                    data: data,
                    method: 'post',
                    async: false,
                    success: function (data) {
                        fun1(data)
                        payableCache[id] = data;
                        window.setTimeout(function(){
                            payableCache[id] = undefined;
                        }, 5000)
                    }
                });
            }
        }
    }

    /**
     * 动态新增开票信息
     */
    var billingType = [[${@dict.getType('billing_type')}]];//开票类型
    var billingTypeHTML = '';
    for (var i = 0; i < billingType.length; i++) {
        billingTypeHTML += '<option  value=' + billingType[i].dictValue + ' >' + billingType[i].dictLabel + '</option>'
    }
    var billingCorp = [[${@dict.getType('bala_corp')}]];//开票公司
    var billingCorpHTML = '';
    for (var i = 0; i < billingCorp.length; i++) {
        billingCorpHTML += '<option  value=' + billingCorp[i].dictValue + ' >' + billingCorp[i].dictLabel + '</option>'
    }



    var billingPayable = [[${custBillings}]];//发票抬头
    var billingPayableHTML = '';
    for (var i = 0; i < billingPayable.length; i++) {
        billingPayableHTML += '<option value="' + billingPayable[i].billingPayable + '" id="'+ billingPayable[i].custBillingId+'">' + billingPayable[i].billingPayable + '</option>'
    }

    //<option value="4">增值税专用发票（9%）</option>
    // <option value="3">增值税专用发票（6%）</option>
    // <option value="2">增值税普票发票（9%）</option>
    // <option value="5">增值税专用发票（3%）</option>
    // <option value="7">增值税专用发票（13%）</option>
    // <option value="6">不开票</option>
    var billingTax = {
        "4": 0.09, // 增值税专用发票（9%）
        "3": 0.06, // 增值税专用发票（6%）
        "2": 0.09, // 增值税普票发票（9%）
        "5": 0.03, // 增值税专用发票（3%）
        "7": 0.13, // 增值税专用发票（13%）
        "13": 0,   // 增值税普票发票（0%）
        "12": 0,   // 增值税专用发票（0%）
        "11": 0.01, // 增值税普票发票（1%）
        "10": 0.03, // 增值税普票发票（3%）
        "9": 0.06, // 增值税普票发票（6%）
        "8": 0.01  // 增值税专用发票（1%）
    }
    var sp = [{name:'运输费', bm:'3010102020100000000'},
        {name:'国内道路货物运输服务', bm:'3010102020100000000'},
        {name:'力资费', bm:'3040408000000000000'},
        {name:'仓储费', bm:'3040407990000000000'},
        {name:'装卸费', bm:'3040408000000000000'},
        {name:'仓储力资费', bm:'3040407030000000000'},
        {name:'进仓费', bm:'3040407990000000000'},
        {name:'其他加工劳务', bm:'2010500000000000000'},
        {name:'其他现代服务', bm:'3049900000000000000'}
    ]
    var spHtml = [];
    for (let i = 0; i < sp.length; i++) {
        spHtml.push('<option bm="',sp[i].bm,'">',sp[i].name,'</option>')
    }
    spHtml = spHtml.join("")

    function insertRowThree(obj) {
        var billingIndex = $('[billing-index]').length == 0 ? 0 : (parseInt($('[billing-index]:last').attr('billing-index')) + 1)
        var trTtml = '<div class="over boxs  add" billing-index="'+billingIndex+'"'+ (obj?" from-ipt":"") +'>' +
            '<div class="fl" style="width: 3%;text-align: center;margin-top: 10px">' +
            '<div row-index style="margin-bottom: 5px"></div>' +
            '<a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRowThree(this,'+billingIndex+')" title="删除选择行"></a>' +
            '<br><a class="collapse-link" style="font-size: 29px;color: #1ab394;font-weight: bold;" onclick="insertRowThree()" title="新增行">+</a>' +
            '</div>' +
            '<div class="fl" style="width:97%;padding-right: 0">'+
            '<div class="over">' +
            '<div class="fl " style="width: 15%;">'+
            '<input type="hidden" name="receBillingList['+billingIndex+'].ifCheck" id="ifCheck'+billingIndex+'">'+
            '<select title="切换时，优先保持清单含税额不变" required name="receBillingList['+billingIndex+'].billingType" id="billingType'+billingIndex+'" class="form-control valid" onchange="billingTypeChange('+billingIndex+')">'+
            billingTypeHTML+
            '</select>'+
            '</div>'+
            '<div class="fl " style="width: 13%;">' +
            '<select name="receBillingList['+billingIndex+'].billingPayable" onchange="selectBillingPayable('+billingIndex+')" id="billingPayable'+billingIndex+'" data-live-search="true" data-none-selected-text="请选择发票抬头" required class="form-control selectpicker valid" >'+
            '<option value=""></option>'+
            ' '+billingPayableHTML+' '+
            '</select>'+
            '</div>'+
            '<div class="fl " style="width: 11%;">'+
            '<input type="hidden" name="receBillingList['+billingIndex+'].billingCorp" id="billingCorp'+billingIndex+'" required class="form-control valid" >'+
            '<span id="billingCorpName'+billingIndex+'" class="form-control" style="line-height: 22px"></span>'+
            '</div>'+
            '<div class="fl " style="width: 9%;">' +
            '<input placeholder="金额" id="billingAmount'+billingIndex+'" name="receBillingList['+billingIndex+'].billingAmount" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" required billingAmountQdAmount="true" maxlength="25" class="form-control" autocomplete="off">' +
            '</div>'+
            '<div class="fl " style="width: 11%;"><textarea type="text" id="taxIdentify'+billingIndex+'" name="receBillingList['+billingIndex+'].taxIdentify" maxlength="25" required class="form-control isCheckVal" disabled></textarea></div>'+
            '<div class="fl " style="width: 15%;"><textarea type="text" id="bank'+billingIndex+'" name="receBillingList['+billingIndex+'].bank" maxlength="125" class="form-control isCheckVal" disabled></textarea></div>'+

            '<div class="fl " style="width: 11%;"><textarea type="text" id="bankAccount'+billingIndex+'" name="receBillingList['+billingIndex+'].bankAccount" maxlength="25" class="form-control isCheckVal" disabled></textarea></div>'+
            '<div class="fl2" style="width: 15%;"><textarea type="text" id="addressPhone'+billingIndex+'" name="receBillingList['+billingIndex+'].addressPhone" maxlength="125" class="form-control isCheckVal" disabled></textarea></div>'+
            '</div>' +
            '<div class="over padt">' +
            '<div class="fl " style="width: 50%;">' +
            //'<textarea type="text" id="memo'+billingIndex+'" name="receBillingList['+billingIndex+'].memo" maxlength="500" class="form-control valid"></textarea>' +
            '<textarea id="memo'+billingIndex+'" name="receBillingList['+billingIndex+'].memo" class="form-control valid" maxLength="500" rows="3" placeholder="开票备注"></textarea>'+
            '</div>' +
            '<div class="fl2" style="width: 50%;">' +
            //'<textarea type="text" id="remark'+billingIndex+'" name="receBillingList['+billingIndex+'].remark" maxlength="500" class="form-control valid"></textarea>' +
            '<textarea id="remark'+billingIndex+'" name="receBillingList['+billingIndex+'].remark"class="form-control valid" maxLength="500" rows="3" placeholder="备注"></textarea>'+
            '</div>' +
            '</div>' +
            '<div id="qd_div_'+billingIndex+'" class="qd">' +
            '<div class="qd_title"><span>发票清单</span>' +
            '<label style="color:#3732dd;display: flex;align-items: center;margin: 0">' +
            ' <input type="checkbox" value="1" style="margin: 4px 2px 5px 4px;" name="receBillingList['+billingIndex+'].qdPrint">' +
            ' <span>单独打印(清单数低于8条但又需要打印销货清单时请勾选)</span>' +
            '</label>' +
            '<span style="flex:1"></span>' +
            ' <span>总金额 <span id="sum_je_'+billingIndex+'" style="display: inline-block;color:rgb(255,132,0);margin-right: 10px">0</span>' +
            ' 总税额 <span id="sum_se_'+billingIndex+'" style="display: inline-block;color:rgb(255,132,0);margin-right: 10px">0</span>' +
            ' 价税总合计 <span id="sum_hj_'+billingIndex+'" style="display: inline-block;color:rgb(255,132,0)">0</span>' +
            '</span></div>' +
            '<table class="custom-tab tab table table-bordered" style="margin-bottom: 0">' +
            '<thead style="background: #f4f6f7;">' +
            '<tr>' +
            '<th style="width: 3%;padding: 0">' +
            '<a class="collapse-link" href="javascript:;" id="qdAddBtn'+billingIndex+'" style="font-size: 22px;color: #1ab394;" onclick="insertRow('+billingIndex+')" title="新增行">+</a>' +
            '</th>' +
            '<th><span class="fcff">*</span> 货物或应税劳务名称</th>' +
            '<th style="width: 10%;">规格型号</th>' +
            '<th style="width: 9%;">单位</th>' +
            '<th style="width: 10%;"><span class="fcff">*</span> 数量 <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-placement="top" data-container="body" title="可精确到小数点后16位"></i></th>' +
            '<th style="width: 13%;">单价 <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-placement="top" data-container="body" title="= 金额 / 数量（精确到小数点后16位）"></i></th>' +
            '<th style="width: 11%;">金额 <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-placement="top" data-container="body" title="= 含税额 / (1 + 税率)"></i></th>' +
            '<th style="width: 11%;">税额 <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-placement="top" data-container="body" title="= 含税额 - 金额"></i></th>' +
            '<th style="width: 12%;"><span class="fcff">*</span> 含税额</th>' +
            '</tr>' +
            '</thead>' +
            '<tbody>' +
            // '<tr>' +
            // '<td>' +
            // '<a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRow(this)" title="删除选择行"></a>' +
            // '</td>' +
            // '<td>' +
            // '<input type="hidden" name="receBillingList['+billingIndex+'].qdList[0].spbm" />' +
            // '<input name="receBillingList['+billingIndex+'].qdList[0].se" type="hidden"/>'+
            // '<select type="text" name="receBillingList['+billingIndex+'].qdList[0].spmc" required class="form-control valid">' +
            // //'<option value=""></option>' +
            // spHtml +
            // '</select>' +
            // '</td>' +
            // '<td><input name="receBillingList['+billingIndex+'].qdList[0].ggxh" class="form-control valid"/></td>' +
            // '<td><input name="receBillingList['+billingIndex+'].qdList[0].dw" class="form-control valid"/></td>' +
            // '<td><input name="receBillingList['+billingIndex+'].qdList[0].spsl" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" required class="form-control valid"/></td>' +
            // '<td><input name="receBillingList['+billingIndex+'].qdList[0].dj" oninput="$.numberUtil.onlyNumberCustom(this,999999999999,0,50,16);" title="输入金额和数量后自动计算" required class="form-control valid"/></td>' +
            // '<td><input name="receBillingList['+billingIndex+'].qdList[0].je" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" title="输入数量和单价后自动计算" required class="form-control valid"/></td>' +
            // '<td se></td>' +
            // '</tr>'+
            '</tbody>'+
            '</table>'+
            '</div>' +
            '</div>'

        $("#infoTabThree").append(trTtml);
        if (obj) {
            var qdList = obj.qdList;
            for (let i = 0; i < qdList.length; i++) {
                insertRow(billingIndex, qdList[i]);
            }
            $("#billingAmount" + billingIndex).val(obj.billingAmount);
            $("#memo" + billingIndex).val(obj.memo);
            $("#remark" + billingIndex).val(obj.remark);
            if (obj.billingPayable) {
                $("#billingPayable" + billingIndex).val(obj.billingPayable).change();
            } else {
                $("#billingType" + billingIndex).change();
            }
        } else {
            //默认增值税专用发票(9%)
            $("#billingType" + billingIndex).val(4).change();
            $("#qdAddBtn" + billingIndex).click()
        }
        //$("#ifCheck"+billingIndex).val(1);
        //billingIndex += 1;
        $('[row-index]').each(function(i){
            $(this).text('№' + (i + 1))
        })
        $('[billing-index='+billingIndex+']').find('.selectpicker').selectpicker({
            container: 'body'
        })
    }
    var billingLxdm = {
        "4": "004",
        "3": "004",
        "2": "007",
        "5": "004",
        "7": "004"
    }
    /*$.validator.addMethod("limitamount", function(value, element, param) {
        var div_idx = element.id.replace('billingAmount', '')
        if ($("#billingType" + div_idx).val() == '6') {
            return true;
        }
        var tax = billingTax[$("#billingType" + div_idx).val()];
        var fplxdm = billingLxdm[$("#billingType" + div_idx).val()];
        if (fplxdm === "004") {
            return new BigNumber(value).div(new BigNumber(1).plus(new BigNumber(tax))).lte(new BigNumber("1000000")) && new BigNumber(value).gt(new BigNumber(0))
        } else if (fplxdm === "007") {
            return new BigNumber(value).div(new BigNumber(1).plus(new BigNumber(tax))).lte(new BigNumber("100000")) && new BigNumber(value).gt(new BigNumber(0))
        }
        return false
    }, function(a,element){
        if (parseFloat($(element).val()) <= 0) {
            return "必须大于0"
        }
        var div_idx = element.id.replace('billingAmount', '')
        var fplxdm = billingLxdm[$("#billingType" + div_idx).val()];
        if (fplxdm === "004") {
            return "专票去除税额最多100万"
        } else if (fplxdm === "007") {
            return "普票去除税额最多10万"
        }
        return "未知票据类型"
    });*/
    $.validator.addMethod("billingAmountQdAmount", function(value, element, param) {
        var div_idx = element.id.replace('billingAmount', '')
        if ($("#billingType" + div_idx).val() == '6') {
            return true;
        }
        return new BigNumber(value).eq(new BigNumber($("#sum_hj_" + div_idx).text()))
    }, "与发票清单价税总合计不一致");
    $.validator.addMethod("min-x", function(value, element, param) {
        if (value !== '') {
            return parseFloat(value, 10) > parseFloat(param, 10)
        }
        return false
    }, "数字必须大于{0}");
    /* 自定义验证规则，内容的最大长度，一个汉字等于两个字符 */
    $.validator.addMethod("stringMaxLength",function(value,element,params){
        return true;//value.replace(/[^x00-xFF]/g, '**').length <= params;//将非ascii码转换为2个ascii码
    },"最大长度不能超过{0}个字符，一个汉字为两个字符")

    function insertRow(idx, obj) {
        var trCount = $('#qd_div_'+idx).find("tbody").find("tr").length;
        var lastIdx = trCount == 0 ? -1 : parseInt($('#qd_div_'+idx).find("tbody").find("tr:last").attr("idx"));
        lastIdx++;
        var trTtml = '<tr idx="'+lastIdx+'">' +
            '<td style="padding: 1px">' +
            '<a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRow(this,'+idx+')" title="删除选择行"></a>' +
            '</td>' +
            '<td style="padding: 1px">' +
            '<input type="hidden" name="receBillingList['+idx+'].qdList['+lastIdx+'].spbm" value="' + sp[0].bm + '" />'+
            '<input name="receBillingList['+idx+'].qdList['+lastIdx+'].dj" type="hidden"/>'+
            '<input name="receBillingList['+idx+'].qdList['+lastIdx+'].sl" type="hidden" value="' + billingTax[$("#billingType"+idx).val()] + '"/>'+
            '<input name="receBillingList['+idx+'].qdList['+lastIdx+'].se" type="hidden"/>'+
            '<select name="receBillingList['+idx+'].qdList['+lastIdx+'].spmc" onchange="$(\'[name=\\\'receBillingList['+idx+'].qdList['+lastIdx+'].spbm\\\']\').val($(this).find(\'option:selected\').attr(\'bm\'))" required class="form-control valid">' +
            //'<option value=""></option>' +
            spHtml +
            '</select>' +
            '</td>' +
            '<td style="padding: 1px"><input name="receBillingList['+idx+'].qdList['+lastIdx+'].ggxh" class="form-control" stringMaxLength="36" placeholder="规格型号"/></td>' +
            '<td style="padding: 1px"><input name="receBillingList['+idx+'].qdList['+lastIdx+'].dw" class="form-control valid" placeholder="单位"/></td>' +
            '<td style="padding: 1px"><input name="receBillingList['+idx+'].qdList['+lastIdx+'].spsl" title="数量必须大于0" placeholder="数量" oninput="$.numberUtil.onlyNumberCustom(this,999999999999,0,50,16);calc_dj('+idx+','+lastIdx+');" min-x="0" value="1" required class="form-control valid" autocomplete="off"/></td>' +
            '<td dj  style="padding: 1px"></td>' +
            '<td style="padding: 1px"><input name="receBillingList['+idx+'].qdList['+lastIdx+'].je" placeholder="金额" oninput="$.numberUtil.onlyNumberTwoDecimal(this);calc_by_je(this,'+idx+','+lastIdx+')" required class="form-control valid" autocomplete="off"/></td>' +
            '<td se  style="padding: 1px"></td>' +
            '<td  style="padding: 1px"><input name="receBillingList['+idx+'].qdList['+lastIdx+'].hj" placeholder="含税额" oninput="$.numberUtil.onlyNumberTwoDecimal(this);calc_all(this,'+idx+','+lastIdx+')" required class="form-control valid" autocomplete="off"/></td>' +
            '</tr>'
        $('#qd_div_'+idx).find('tbody').append(trTtml);
        if (obj) {
            if (obj.spmc) {
                $('[name="receBillingList['+idx+'].qdList['+lastIdx+'].spmc"]').val(obj.spmc).change()
            }
            if (obj.ggxh) {
                $('[name="receBillingList['+idx+'].qdList['+lastIdx+'].ggxh"]').val(obj.ggxh)
            }
            if (obj.dw) {
                $('[name="receBillingList['+idx+'].qdList['+lastIdx+'].dw"]').val(obj.dw)
            }
            if (obj.spsl) {
                $('[name="receBillingList['+idx+'].qdList['+lastIdx+'].spsl"]').val(obj.spsl)
            }
            if (obj.hj) {
                $('[name="receBillingList['+idx+'].qdList['+lastIdx+'].hj"]').val(obj.hj)
            }
        } else {
            $("#billingType" + idx).change() // 触发税率变化事件
        }
    }

    function calc_dj(idx1, idx2) {
        var $spsl = $("[name='receBillingList["+idx1+"].qdList["+idx2+"].spsl']");
        var $dj = $spsl.closest("tr").find('[dj]');
        var $dji = $("[name='receBillingList["+idx1+"].qdList["+idx2+"].dj']");
        var $jei = $("[name='receBillingList["+idx1+"].qdList["+idx2+"].je']");
        var spsl = new BigNumber($spsl.val());
        var je = new BigNumber($jei.val());
        if (spsl.isNaN() || spsl.eq(0) || je.isNaN()) {
            $dj.text("")
            $dji.val("")
        } else {
            var dj;
            for (var i=0;i<=8;i++) {
                dj = je.div(spsl).dp(i)
                if (spsl.multipliedBy(dj).dp(2).eq(je)) {
                    break;
                }
            }
            $dj.text(dj.toNumber())
            $dji.val(dj.toNumber())
        }
        sumqd(idx1)
    }


    function calc_by_je(obj, idx1, idx2) {
        $("#btn1").prop("disabled", true)
        var timeout_idx = $(obj).attr("timeout")
        if (timeout_idx != null) {
            window.clearTimeout(timeout_idx)
        }
        timeout_idx = window.setTimeout(function(){
            calc_je2hj(idx1, idx2)
            $(obj).removeAttr("timeout")
            if ($('[timeout]').length == 0) {
                $("#btn1").prop("disabled", false)
            }
        }, 1000)
        $(obj).attr("timeout", timeout_idx)
    }
    function calc_all(obj, idx1, idx2) {
        $("#btn1").prop("disabled", true)
        var timeout_idx = $(obj).attr("timeout")
        if (timeout_idx != null) {
            window.clearTimeout(timeout_idx)
        }
        timeout_idx = window.setTimeout(function(){
            calc_hj2je(idx1, idx2)
            $(obj).removeAttr("timeout")
            if ($('[timeout]').length == 0) {
                $("#btn1").prop("disabled", false)
            }
        }, 1000)
        $(obj).attr("timeout", timeout_idx)
    }

    function calc_je2hj(div_idx, tr_idx) {
        var tax = new BigNumber(billingTax[$("#billingType"+div_idx).val()])
        var $hj = $("[name='receBillingList["+div_idx+"].qdList["+tr_idx+"].hj']");
        var $jei = $("[name='receBillingList["+div_idx+"].qdList["+tr_idx+"].je']");
        var $se = $hj.closest("tr").find('[se]');
        var $sei = $("[name='receBillingList["+div_idx+"].qdList["+tr_idx+"].se']");
        var $dj = $hj.closest("tr").find('[dj]');
        var $dji = $("[name='receBillingList["+div_idx+"].qdList["+tr_idx+"].dj']");
        var je = new BigNumber($jei.val())
        var $spsl = $("[name='receBillingList[" + div_idx + "].qdList[" + tr_idx + "].spsl']");
        if (je.isNaN()) {
            $se.text("")
            $dj.text("")
            $jei.val("")
            $sei.val("")
            $dji.val("")
        } else {
            var se = je.multipliedBy(tax).dp(2)
            $se.text(se.toNumber())
            $sei.val(se.toNumber())
            var hj = je.plus(se)
            $hj.val(hj.toNumber())
            var spsl = new BigNumber($spsl.val());
            if (spsl.isNaN() || spsl.eq(0) || je.isNaN()) {
                $dj.text("")
                $dji.val("")
            } else {
                var dj;
                for (var i=0;i<=8;i++) {
                    dj = je.div(spsl).dp(i)
                    if (spsl.multipliedBy(dj).dp(2).eq(je)) {
                        break;
                    }
                }
                $dj.text(dj.toNumber())
                $dji.val(dj.toNumber())
            }
        }
        //计算总金额、总税额、价税总合计
        sumqd(div_idx)
    }

    function calc_hj2je(div_idx, tr_idx) {
        var tax = new BigNumber(billingTax[$("#billingType"+div_idx).val()])
        var $hj = $("[name='receBillingList["+div_idx+"].qdList["+tr_idx+"].hj']");
        var $jei = $("[name='receBillingList["+div_idx+"].qdList["+tr_idx+"].je']");
        var $se = $hj.closest("tr").find('[se]');
        var $sei = $("[name='receBillingList["+div_idx+"].qdList["+tr_idx+"].se']");
        var $dj = $hj.closest("tr").find('[dj]');
        var $dji = $("[name='receBillingList["+div_idx+"].qdList["+tr_idx+"].dj']");
        var hj = new BigNumber($hj.val());
        var $spsl = $("[name='receBillingList[" + div_idx + "].qdList[" + tr_idx + "].spsl']");
        if (hj.isNaN()) {
            $se.text("")
            $dj.text("")
            $jei.val("")
            $sei.val("")
            $dji.val("")
        } else {
            var je = hj.dividedBy(new BigNumber(1).plus(tax)).dp(2)
            $jei.val(je.toNumber())
            var se = hj.minus(je);//new BigNumber(je).multipliedBy(tax).dp(2)
            $se.text(se.toNumber())
            $sei.val(se.toNumber())
            /*var hj2 = je.plus(se)
            if (!hj.eq(hj2)) {
                $.modal.msgError("金额已由" + hj.toNumber() + "变为" + hj2.toNumber() + "，原因详见金额、税额、含税额计算方式")
                $hj.val(hj2.toNumber())
            }*/
            var spsl = new BigNumber($spsl.val());
            if (spsl.isNaN() || spsl.eq(0) || je.isNaN()) {
                $dj.text("")
                $dji.val("")
            } else {
                var dj;
                for (var i=0;i<=8;i++) {
                    dj = je.div(spsl).dp(i)
                    if (spsl.multipliedBy(dj).dp(2).eq(je)) {
                        break;
                    }
                }
                $dj.text(dj.toNumber())
                $dji.val(dj.toNumber())
            }
        }
        //计算总金额、总税额、价税总合计
        sumqd(div_idx)
    }

    function sumqd(div_idx) {
        var sum_je = new BigNumber(0);
        var sum_se = new BigNumber(0);
        var sum_hj = new BigNumber(0);
        $("#qd_div_" + div_idx).find("tbody").find("tr").each(function(){
            var $tr = $(this);
            var spsl = new BigNumber($tr.find('[name$=".spsl"]').val());
            var je = new BigNumber($tr.find('[name$=".je"]').val())
            var se = new BigNumber($tr.find('[se]').text())
            var hj = new BigNumber($tr.find('[name$=".hj"]').val())
            if (!je.isNaN()) { sum_je = sum_je.plus(je) }
            if (!je.isNaN()) { sum_se = sum_se.plus(se) }
            if (!je.isNaN()) { sum_hj = sum_hj.plus(hj) }
        })
        $("#sum_je_" + div_idx).text(sum_je.toNumber())
        $("#sum_se_" + div_idx).text(sum_se.toNumber())
        $("#sum_hj_" + div_idx).text(sum_hj.toNumber())
    }

    function removeRow(obj, div_idx){
        $(obj).closest("tr").remove();
        if ($("#qd_div_" + div_idx).find("tbody").find("tr").length == 0) {
            $.modal.msgWarning("发票清单至少保留一行");
            insertRow(div_idx)
        }
        //计算总金额、总税额、价税总合计
        sumqd(div_idx)
    }

    function billingTypeChange(idx) {
        var fields = ['billingPayable', 'billingCorp', 'taxIdentify'];
        var flag = $("#billingType"+idx).val() == '6'; // true=不开票
        $("#ifCheck"+idx).val(flag?0:1);
        for (let i = 0; i < fields.length; i++) {
            $("#" + fields[i] + idx).prop("required", !flag)
            if (flag) {
                $("#" + fields[i] + idx).val("")
                $("#form-receiptAppl-add").validate().element($("#" + fields[i] + idx))
            }
        }
        if (flag) {
            $("#qd_div_" + idx).hide();
        } else {
            if ($("#billingType"+idx).val() == '2') {
                //$("#bank" + idx).prop("required", false)
                //$("#bankAccount" + idx).prop("required", false)
            }
            $("#qd_div_" + idx).show();
            $("#qd_div_" + idx).find('tr').each(function(){
                $(this).find("[name$='.sl']").val(billingTax[$("#billingType"+idx).val()])
                calc_hj2je(idx, $(this).attr("idx"))
            })
        }
    }

    /*function removeRowThree(obj,index) {
        if ($("#infoTabThree tbody").find('tr').length > 1) {
            $("#infoTabThree tbody").find(obj).closest("tr").remove();
        } else {
            $("#billingCorp"+index).val("");
            $("#billingPayable"+index).val("");
            $("#taxIdentify"+index).val("");
            $("#bank"+index).val("");
            $("#billingType"+index).val("");
            $("#bankAccount"+index).val("");
            $("#addressPhone"+index).val("");
            $("#addressee"+index).val("");
            $("#addresseeContact"+index).val("");
            $("#addresseeAddr"+index).val("");
            $("#memo"+index).val("");
            $("#billingAmount"+index).val("");
        }
    }*/
    function removeRowThree(obj,index) {
        // if ($("#infoTabThree tbody").find('tr').length > 1) {
        //     $("#infoTabThree tbody").find(obj).closest("tr").remove();
        // }
        if ($("#infoTabThree").find('.add').length > 1) {
            $("#infoTabThree").find(obj).closest(".add").remove();
            $('[row-index]').each(function(i) {
                $(this).text('№' + (i+1));
            })
        } else {
            $(obj).closest(".add").remove();
            /*$("#billingCorp"+index).val("");
            $("#billingPayable"+index).val("");
            $("#taxIdentify"+index).val("");
            $("#bank"+index).val("");
            $("#billingType"+index).val("4").change();
            $("#bankAccount"+index).val("");
            $("#addressPhone"+index).val("");
            $("#addressee"+index).val("");
            $("#addresseeContact"+index).val("");
            $("#addresseeAddr"+index).val("");
            $("#memo"+index).val("");
            $("#remark"+index).val("");
            $("#billingAmount"+index).val("");
            */
            insertRowThree();
            $.modal.msgSuccess("本行已重置");
        }
    }

    /**
     * 是否开票：
     *      1.是：发票抬头 开票公司 必填  默认增值税专用发票
     *      2.否：
     */
    /*function selectIsCheck(index) {
        var ifCheck  = $('#ifCheck'+index).val();
        if(ifCheck == 0){
            //发票抬头
            $("#billingPayable"+index).attr("required",false);
            $("#billingPayable"+index).attr("disabled",true);
            //开票公司
            $("#billingCorp"+index).attr("required",false);
            //默认不开票
            $("#billingType"+index).val(6);



            $("#form-receiptAppl-add").validate().element($("#billingPayable"+index))
            $("#form-receiptAppl-add").validate().element($("#billingCorp"+index))
            $("#form-receiptAppl-add").validate().element($("#billingType"+index))
        }
        if(ifCheck == 1){
            //发票抬头
            $("#billingPayable"+index).attr("required",true);
            $("#billingPayable"+index).attr("disabled",false);
            //开票公司
            $("#billingCorp"+index).attr("required",true);
            //默认增值税专用发票(9%)
            $("#billingType"+index).val(4)
        }
    }*/

    //提交
    function submitHandler() {
        if ($("[err-tax]").length > 0) {
            $.modal.msgError("请调整力资费的含税构成");
            return;
        }
        $("[name='taxSplitJson']").val(adjustCache ? adjustCache : "");
        if (adjustCache) {
            // 调整含税后又改了调整金额，校验总金额是否一致
            if (taxTotalAmount != $("#adjustAmount").val().trim()) {
                $.modal.msgError("调整金额发生变动，请重新“调整税额占比”后再保存");
                return;
            }
        }
        $("[name='tariffTaxTxt']").val(tariffTaxTxt);
        $.modal.loading("正在验证数据...")
        // if ($("#tid").val() == "") {
        //     $.modal.alertError("请上传附件！");
        //     return;
        // }
        var dis = $(":disabled");

        // for(var i = 0;i<=billingIndex;i++){
        //     /*
        //     * 选择开票，且选择开票抬头不为空时
        //     *   1.开票金额 != 0
        //     *   2.对应开票信息必填
        //     */
        //     var isCheck  = $('#ifCheck'+i).find(":selected").val();
        //     var billingPayable  = $('#billingPayable'+i).find(":selected").val();
        //     if(isCheck == 1 && $.common.isNotEmpty(billingPayable)){
        //         var billingAmount = $("#billingAmount"+i).val();
        //         if(billingAmount == 0 || billingAmount == ""){
        //             $.modal.alertError("开票金额必填且不能为0");
        //             return ;
        //         }
        //     }
        //
        //     /*
        //      * 选择普通发票时 开户行和开户账号非必填
        //      */
        //     var billingType = $("#billingType"+i).val();
        //     if(billingType === '2'){
        //         $("#bank"+i).attr("required",false);
        //         $("#bankAccount"+i).attr("required",false);
        //     }
        // }
        var data = {};
        data.customerId = $("#customerId").val();
        data.receivableAmount = $("#receivableAmount").val();
        data.adjustAmount = $("#adjustAmount").val();

        const checkBillingAmount = function() {
            let totalBillingAmount = new BigNumber(0);
            // 计算总申请开票+不开票金额，并判断是否与申请金额(调整金额)一致
            $('[name$=".billingAmount"]').each(function(){
                let billingAmount = new BigNumber($(this).val());
                if (!billingAmount.isNaN()) {
                    totalBillingAmount = totalBillingAmount.plus(billingAmount);
                }
            });
            let tariffAmount = new BigNumber($('#tariffAmount').val());
            if (tariffAmount.isNaN()) {
                tariffAmount = 0;
            }
            if (!new BigNumber($("#adjustAmount").val()||$('#receivableAmount').val()).plus(tariffAmount).eq(totalBillingAmount)) {
                $.modal.alertWarning("各发票总金额必须等于申请(调整)金额+力资费用<br>即：申请多少金额就要提交多少发票，不再支持多次申请开票");
                return false;
            }
            return true;
        }

        /*$.ajax({
            type: "POST",
            url: ctx + "receCheckSheet/checkAdjustRate",
            //async: false,
            data:data,
            success: function(r) {
                if (r.code != 0) {
                    $.modal.closeLoadingImmediately()
                    $.modal.alertWarning(r.msg);
                } else {*/
                    //调整比例是否需要超过比例
                    // var data = {};
                    // data.customerId = $("#customerId").val();
                    // data.receivableAmount = $("#receivableAmount").val();
                    // data.adjustAmount = $("#adjustAmount").val();
                    if(new BigNumber($("#receivableAmount").val()).eq(new BigNumber($("#adjustAmount").val()))){ // 未调整金额时
                        $("#adjustMemoLabel").css("color","");
                        $("#adjustMemo").removeAttr("required");
                        $("#isCostAdjustLabel").css("color","");
                        $("#isCostAdjust").removeAttr("required");
                        $("#costAdjustMemoLabel").css("color","");
                        $("#costAdjustMemo").removeAttr("required");
                        dis.attr("disabled", false);
                        if ($.validate.form()) {
                            dis.attr("disabled", true);
                            $.modal.closeLoadingImmediately()
                            if (!checkBillingAmount()) return;
                            var kp = 0;
                            $('[name$="billingType"]').each(function () {
                                if (this.value != '6') {
                                    kp++;
                                }
                            })
                            $.modal.confirm("确认提交"+(kp>0?'该开票申请':'')+"吗?", function() {
                                $.modal.loading("正在提交数据，请稍候...")
                                $.unsubscribe("uploadDone");
                                var fun = function(){
                                    dis.attr("disabled", false);
                                    var paramx = $('#form-receiptAppl-add').serialize()
                                    dis.attr("disabled", true);
                                    $.operate.saveTab(prefix + "/saveReceiptAppl", paramx, function(){
                                        $(".kv-file-remove").click()
                                        $("#tid").val("")
                                        $.modal.closeLoadingImmediately()
                                    });
                                }
                                $.subscribe("uploadDone", fun)
                                $("#image").fileinput('upload')
                            });
                        } else {
                            dis.attr("disabled", true);
                            $.modal.closeLoadingImmediately()
                            $(":visible.error").eq(0).focus().click().blur()
                        }
                    }else{//有调整金额时
                        if ($("[name='tReceCheckSheetId']").val().split(",").length > 1) {
                            $.modal.alertError("调整金额时不支持多张对账单同时申请！");
                            $.modal.closeLoadingImmediately()
                            return;
                        }
                        $("#adjustMemoLabel").css("color","red");
                        $("#adjustMemo").attr("required","true");
                        $("#isCostAdjustLabel").css("color","red");
                        $("#isCostAdjust").attr("required","true");
                        if($("#isCostAdjust").val() == '是'){
                            $("#costAdjustMemoLabel").css("color","red");
                            $("#costAdjustMemo").attr("required","true");
                        }else{
                            $("#costAdjustMemoLabel").css("color","");
                            $("#costAdjustMemo").removeAttr("required");
                        }
                        // if(parseFloat($("#adjustAmount").val()) > parseFloat($("#receivableAmount").val())){/*应收调大时*/}
                        dis.attr("disabled", false);
                        if ($.validate.form()) {
                            dis.attr("disabled", true);
                            $.modal.closeLoadingImmediately()
                            if (!checkBillingAmount()) return;
                            $.modal.confirm("调整金额需要提交审核，是否提交？",function () {
                                $.modal.loading("正在提交数据，请稍候...")
                                $.unsubscribe("uploadDone");
                                var fun = function(){
                                    dis.attr("disabled", false);
                                    var paramx = $('#form-receiptAppl-add').serialize()
                                    dis.attr("disabled", true);
                                    $.operate.saveTab(prefix + "/saveReceiptApplCheck", paramx, function(){
                                        $(".kv-file-remove").click()
                                        $("#tid").val("")
                                        $.modal.closeLoadingImmediately()
                                    });
                                }
                                $.subscribe("uploadDone", fun)
                                $("#image").fileinput('upload')
                            });
                        } else {
                            dis.attr("disabled", true);
                            $.modal.closeLoadingImmediately()
                            $(":visible.error").eq(0).focus().click().blur()
                        }
                    }
                /*}
            }
        });*/

    }
    function readExcel() {
        var tt = $.modal.layerLoading("加载中，请稍候...");
        var files = $('#ipt_file')[0].files;
        var fileReader = new FileReader();
        fileReader.onload = function(ev) {
            try {
                var data = ev.target.result
                var workbook = XLSX.read(data, {
                    type: 'binary'
                }) // 以二进制流方式读取得到整份excel表格对象

            } catch (e) {
                console.log('%O',e)
                $.modal.alertError('文件类型不正确:' + e.message);
                layer.close(tt)
                return;
            }
            // 表格的表格范围，可用于判断表头是否数量是否正确
            var fromTo = '';
            console.log(workbook.Sheets)
            var list = null;
            var fields_names = ["发票抬头","开票备注","备注","货物或应税劳务名称","规格型号","单位","数量","含税额"]
            var idx = 0;
            // 遍历每张表读取
            for (var sheet in workbook.Sheets) {
                if (workbook.Sheets.hasOwnProperty(sheet)) {
                    var sht = workbook.Sheets[sheet]
                    fromTo = sht['!ref'];
                    var a1 = sht['A1'].v;
                    var b1 = sht['B1'].v;
                    var c1 = sht['C1'].v;
                    var d1 = sht['D1'].v;
                    var e1 = sht['E1'].v;
                    var f1 = sht['F1'].v;
                    var g1 = sht['G1'].v;
                    var h1 = sht['H1'].v;
                    if (a1 != fields_names[idx++] || b1 != fields_names[idx++] || c1 != fields_names[idx++] || d1 != fields_names[idx++] || e1 != fields_names[idx++] || f1 != fields_names[idx++]|| g1 != fields_names[idx++]|| h1 != fields_names[idx++]) {
                        $.modal.alertError("Sheet第一行必须依次是“" + fields_names.join('”、“') + "”");
                        layer.close(tt)
                        return;
                    }
                    list = XLSX.utils.sheet_to_json(workbook.Sheets[sheet], {raw:false});
                    break; // 如果只取第一张表，就取消注释这行
                }
            }
            //在控制台打印出来表格中的数据
            if (list == null || list.length == 0) {
                $.modal.alertError("未在第一个Sheet中找到数据")
                layer.close(tt)
                return;
            }

            var errMsgs = [];
            var fields = ["billingPayable","memo","remark","spmc","ggxh","dw","spsl","hj"]
            var rows = []
            var billingAmount = new BigNumber(0);
            for (let i = 0; i < list.length; i++) {
                var billingPayable = list[i][fields_names[0]];
                var memo = list[i][fields_names[1]];
                var remark = list[i][fields_names[2]];
                if (i == 0 && billingPayable == null) {
                    $.modal.alertError("第二行第一列请输入发票抬头");
                    layer.close(tt)
                    return;
                }
                var qd = {};
                for (let j = 3; j < fields.length; j++) {
                    qd[fields[j]] = list[i][fields_names[j]]
                }
                var row = null;
                if (billingPayable != undefined) { // 有抬头
                    if (rows.length > 0) {
                        rows[rows.length - 1].billingAmount = billingAmount.toNumber();
                        billingAmount = new BigNumber(0);
                    }
                    row = {billingPayable:billingPayable,memo:memo,remark:remark,qdList:[]}
                    // 新增开票申请
                    var payable = getBillingPayable(billingPayable);
                    if (payable == null) {
                        errMsgs.push("未匹配到第" + (list[i]['__rowNum__'] + 1) + "行“" + billingPayable + "”的开票抬头");
                        row['billingPayable'] = '';
                    }
                    row['qdList'].push(qd);
                    rows.push(row);
                } else {
                    row = rows[rows.length - 1];
                    row['qdList'].push(qd);
                }
                if (qd.spmc == null) {
                    errMsgs.push("第" + (list[i]['__rowNum__'] + 1) + "行“货物或应税劳务名称”未录入");
                } else {
                    var spbm = getSpbmBySpmc(qd.spmc)
                    if (spbm == null) {
                        errMsgs.push("未匹配到第" + (list[i]['__rowNum__'] + 1) + "行“"+qd.spmc+"”的货物或应税劳务名称");
                    }
                }
                if (qd.spsl != null) {
                    if (isNaN(qd.spsl)) {
                        errMsgs.push("第" + (list[i]['__rowNum__'] + 1) + "行数量“" + qd.spsl + "”填入了非数字");
                        qd['spsl'] = '';
                    } else {
                        qd.spsl = new BigNumber(qd.spsl).dp(2).toNumber()
                    }
                }
                if (qd.hj != null) {
                    if (isNaN(qd.hj)) {
                        errMsgs.push("第" + (list[i]['__rowNum__'] + 1) + "行含税额“" + qd.hj + "”填入了非数字");
                        qd['hj'] = '';
                    } else {
                        qd.hj = new BigNumber(qd.hj).dp(2).toNumber()
                        billingAmount = billingAmount.plus(new BigNumber(qd.hj))
                    }
                }
            }
            if (rows.length > 0) {
                rows[rows.length - 1].billingAmount = billingAmount.toNumber();
            }
            if (errMsgs.length > 0) {
                errMsgs.unshift("<span style='color:blue'>已导入完成：</span>")
                errMsgs.push("<span style='color:blue'>可在页面修改或清空已导入后重新导入</span>")
                window.setTimeout(function(){
                    $.modal.alertError(errMsgs.join("<br>"))
                }, 50)
            }
            for (let i = 0; i < rows.length; i++) {
                insertRowThree(rows[i]);
            }
            layer.close(tt)
        };
        // 以二进制方式打开文件
        fileReader.readAsBinaryString(files[0]);
        $('#ipt_file').remove();
        $("#ipt_file_div").append('<input type="file"' +
            ' accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"' +
            ' id="ipt_file" style="display: none" onChange="readExcel()">')
    }
    function getBillingPayable(payable) {
        for (let i = 0; i < billingPayable.length; i++) {
            if (billingPayable[i].billingPayable == payable) {
                return payable;
            }
        }
        return null;
    }
    function getSpbmBySpmc(mc) {
        for (let i = 0; i < sp.length; i++) {
            if (sp[i].name == mc) {
                return sp[i].bm
            }
        }
        return null;
    }
    function emptyIpt() {
        $.modal.confirm("即将清空已导入的发票申请，是否继续？", function () {
            $("[from-ipt]").remove()
            if ($("[billing-index]").length == 0) {
                insertRowThree();
            }
        })
    }
    function addPayable() {
        $.modal.openCallAfterSubmit("新增开票抬头", ctx + "receCheckSheet/addPayable?customerId=[(${receCheckSheet.balaCustomer})]",800,500, function (index, layero, custBilling) {
            billingPayable.push(custBilling)
            billingPayableHTML += '<option value="' + custBilling.billingPayable + '" id="'+ custBilling.custBillingId+'">' + custBilling.billingPayable + '</option>'
            $("select[name$='.billingPayable']").append('<option value="' + custBilling.billingPayable + '" id="'+ custBilling.custBillingId+'">' + custBilling.billingPayable + '</option>');
            layer.close(index)
            $('[billing-index]').find('.selectpicker').selectpicker('refresh')
            $.modal.msgSuccess("开票抬头新增成功");
        });
    }

    var totalAmount = [(${receCheckSheet.totalAmount})];
    var adjustCache = null;
    var taxTotalAmount = null;
    function adjustTax() {
        var adjustAmount = new BigNumber($("#adjustAmount").val());
        if (adjustAmount.isNaN()) {
            $.modal.msgError("调整金额请输入有效数字！");
            $("#adjustAmount").focus()
            return;
        }
        var pass = true;
        if(!new BigNumber($("#receivableAmount").val()).eq(new BigNumber($("#adjustAmount").val()))){
            if ($("[name='tReceCheckSheetId']").val().split(",").length > 1) {
                pass = false;
                $.modal.alertError("调整金额时不支持多张对账单同时申请！");
            } else {
                var data = {};
                data.customerId = $("#customerId").val();
                data.receivableAmount = $("#receivableAmount").val();
                data.adjustAmount = $("#adjustAmount").val();
                $.ajax({
                    type: "POST",
                    url: ctx + "receCheckSheet/checkAdjustRate",
                    async: false,
                    data: data,
                    success: function (r) {
                        if (r.code != 0) {
                            pass = false;
                            $.modal.alertWarning("调整金额超过预设比例！");
                        }
                    }
                })
            }
        }
        if (pass) {
            $.modal.openFull("调整税额占比", prefix + "/detailTax/[(${receCheckSheet.receCheckSheetId})]")
        }
    }

    var tariffTaxTxt = ""; // 4:10000
    function initTariffTax() {
        var tariffAmount = new BigNumber($("#tariffAmount").val())
        if (tariffAmount.isNaN()) {
            tariffTaxTxt = "";
            translateTariffTaxTxt()
        } else {
            if (tariffTaxTxt == "") {
                tariffTaxTxt = "4:" + tariffAmount.toNumber(); // 4=默认9%专票
            } else {
                if (tariffTaxTxt.split(",").length == 1) {
                    tariffTaxTxt = tariffTaxTxt.split(":")[0] + ":" + tariffAmount.toNumber();
                }
            }
            translateTariffTaxTxt()
        }
    }
    function translateTariffTaxTxt() {
        if (tariffTaxTxt == "") {
            $("#tariffTax").html("无");
        } else {
            var array = tariffTaxTxt.split(",");
            var temp = [];
            var sum = new BigNumber("0");
            for (let j = 0; j < array.length; j++) {
                var t = array[j].split(":");
                for (let i = 0; i < billingType.length; i++) {
                    if (billingType[i].dictValue == t[0]) {
                        temp.push("[", billingType[i].dictLabel, ":", t[1], "]")
                        sum = sum.plus(new BigNumber(t[1]))
                        break;
                    }
                }
            }
            temp.push(' <a href="javascript:changeTariffTaxTxt();">调</a>')
            if (!sum.eq(new BigNumber($("#tariffAmount").val()))) {
                temp.splice(0,0,"<span style='color:red' err-tax data-toggle='tooltip' title='合计不等于力资费, 请手动调整' data-trigger='manual'>")
                temp.push("</span>")
            }
            $("#tariffTax").html(temp.join(""))
            $('[err-tax]').tooltip('show')
        }
    }
    function changeTariffTaxTxt() {
        var tariffAmount = $("#tariffAmount").val()
        var tmp = [`<div style="padding: 8px 20px;">
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-sm-4 control-label">当前费用：</label>
							<div class="col-sm-3">
							  	<p class="form-control-static">${tariffAmount}</p>
							</div>
							<div class="col-sm-4">
							  	<p class="form-control-static" id="result" style="color:#ff0000"></p>
							</div>
							<div class="col-sm-1">
							    <i class="fa fa-plus-circle" style="height: 34px;vertical-align: middle;padding: 7px 0;color:#007dcc" onclick="addTariffTaxRow(this)"></i>
							</div>
						</div>`]
        var arr = tariffTaxTxt.split(",");
        for (let i = 0; i < arr.length; i++) {
            var tt = arr[i].split(":");
            tmp.push('<div class="form-group">')
            tmp.push('<label class="col-sm-7">')
            tmp.push('<select billingtype class="form-control">');
            for (let j = 0; j < billingType.length; j++) {
                tmp.push("<option value='",billingType[j].dictValue,"'",tt[0]==billingType[j].dictValue?' selected':'',">",billingType[j].dictLabel,"</option>");
            }
            tmp.push("</select>");
            tmp.push('</label>')
            tmp.push('<div class="col-sm-4">')
            tmp.push('<input class="form-control" tax oninput="$.numberUtil.onlyNumberTwoDecimal(this);check()" value="',tt[1],'" autocomplete="off">')
            tmp.push('</div>')
            tmp.push('<div class="col-sm-1">')
            tmp.push('<i class="fa fa-minus-circle" style="height: 34px;vertical-align: middle;padding: 7px 0;color:#ff0000" onclick="$(this).closest(\'.form-group\').remove();check()"></i>')
            tmp.push('</div>')
            tmp.push('</div>')
        }
        tmp.push('</form>')
        tmp.push('* 所有含税额合计必须<span style="color:blue;font-weight: bold;">等于当前费用</span>')
        tmp.push('</div>')
        layer.open({
            type: 1,
            area: ['450px', '403px'],
            fix: false,
            //不固定
            maxmin: false,
            shade: 0.3,
            shadeClose: false,
            title: '其他力资费含税额拆分',
            content: tmp.join(""),
            btn: ['<i class="fa fa-check"></i> 保存', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(idx, layero){
                var chk = check();
                var objTemp = {};//校验开票类型时候重复的临时对象
                var tmp = []
                if (chk) {
                    var amount_origin = new BigNumber(tariffAmount)
                    var flag = true;
                    $("[billingtype]").each(function(){
                        if (objTemp[$(this).val()] != null) {
                            $.modal.msgError("相同开票类型的请合并");
                            flag = false;
                            return;
                        } else {
                            objTemp[$(this).val()] = 1;
                            var n = new BigNumber($(this).closest('.form-group').find('[tax]').val()).toNumber();
                            if (n === 0 && amount_origin.toNumber() != 0) {
                                $.modal.msgError("请输入非0的数字");
                                flag = false;
                                return;
                            } else if (n < 0 && amount_origin.toNumber() > 0) {
                                $.modal.msgError("当前力资费用大于0，各税额必须都大于0");
                                flag = false;
                                return;
                            } else if (n > 0 && amount_origin.toNumber() < 0) {
                                $.modal.msgError("当前力资费用小于0，各税额必须都小于0");
                                flag = false;
                                return;
                            }
                            tmp.push($(this).val()+":"+$(this).closest('.form-group').find('[tax]').val())
                        }
                    })
                    if (flag) {
                        tariffTaxTxt = tmp.join(",")
                        translateTariffTaxTxt()
                        layer.close(idx)
                    }
                } else {
                    $.modal.msgError("所有含税额合计必须等于当前费用")
                }
            }
        })
        check()
    }
    function addTariffTaxRow(i){
        var tmp = [];
        tmp.push('<div class="form-group">')
        tmp.push('<label class="col-sm-7">')
        tmp.push('<select billingtype class="form-control">');
        for (let j = 0; j < billingType.length; j++) {
            tmp.push("<option value='",billingType[j].dictValue,"'>",billingType[j].dictLabel,"</option>");
        }
        tmp.push("</select>");
        tmp.push('</label>')
        tmp.push('<div class="col-sm-4">')
        tmp.push('<input class="form-control" tax oninput="$.numberUtil.onlyNumberTwoDecimal(this);check()" autocomplete="off">')
        tmp.push('</div>')
        tmp.push('<div class="col-sm-1">')
        tmp.push('<i class="fa fa-minus-circle" style="height: 34px;vertical-align: middle;padding: 7px 0;color:#ff0000" onclick="$(this).closest(\'.form-group\').remove();check()"></i>')
        tmp.push('</div>')
        tmp.push('</div>')
        $(i).closest("form").append(tmp.join(''))
    }
    function check() {
        var amount_origin = new BigNumber($("#tariffAmount").val())
        var sum = calc1();
        if (sum.eq(amount_origin)) {
            $("#result").text("")
            return true;
        } else if (sum.gt(amount_origin)) {
            $("#result").text("超" + sum.minus(amount_origin))
            return false;
        } else if (sum.lt(amount_origin)) {
            $("#result").text("低" + amount_origin.minus(sum))
            return false;
        }
    }
    function calc1() {
        var sum = new BigNumber("0")
        $("[tax]").each(function(){
            var amount = new BigNumber(this.value.trim());
            if (!amount.isNaN()) {
                sum = sum.plus(amount);
            }
        })
        return sum;
    }

    function getCheckSheetTax() {
        $.ajax({
            url: prefix + "/receCheckSheetTax",
            data: { "receCheckSheetId": [[${receCheckSheet.receCheckSheetId}]] },
            cache: false,
            success: function(res) {
                if (res.code == 0) {
                    var tmp = [];
                    for (let i = 0; i < res.data.length; i++) {
                        tmp.push('[',res.data[i].DICT_LABEL,':',res.data[i].AMOUNT,']')
                    }
                    showTaxText(tmp.join(''))
                } else {
                    $.modal.alertError(res.msg)
                }
            }
        })
    }
    function showTaxText(text) {
        $('#taxText').text(text)
    }

</script>
</body>
</html>