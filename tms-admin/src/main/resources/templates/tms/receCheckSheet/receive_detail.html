<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款申请管理')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="invoiceId" th:value="${invoiceId}">
                <input type="hidden" name="vbillstatus" th:value="${vbillstatus}">
                <input type="hidden" name="isApply" th:value="${isApply}">
                <input type="hidden" name="receCheckSheetId" th:value="${receCheckSheetId}">
            </form>
        </div>
<!--        <div class="btn-group-sm" id="toolbar" role="group">-->
<!--            <a class="btn btn-primary single disabled " onclick="adjust()" shiro:hasPermission="finance:receive:adjust">-->
<!--                <i class="fa fa-edit"></i> 调整-->
<!--            </a>-->
<!--        </div>-->

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var prefix = ctx + "receive/receive_detail";
    //运费费用类型
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    //在途费用类型
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];

    var receiveDetailStatus = [[${receiveDetailStatus}]];
    var isClose = [[${isClose}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchPre();
            }
        });
        var options = {
            url: prefix + "/listDetail",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            showToggle: false,
            showColumns: false,
            modalName: "应收汇总",
            clickToSelect: true,
            height: 560,
            showFooter: true,
            uniqueId: "receiveDetailId",
            columns: [
                {checkbox: true},
                // {
                //     title: '操作',
                //     align: 'left',
                //     field: 'receCheckSheetId',
                //     formatter: function(value, row, index) {
                //         var actions = [];
                //         actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="调整单明细" onclick="detail(\'' + row.receiveDetailId + '\')"><i  class="fa fa fa-list" style="font-size: 15px;" ></i></a>');
                //         return actions.join('');
                //     }
                // },
                {field: 'vbillno', align: 'left', title: '应收单号'},
                {
                    field: 'vbillstatus', title: '单据状态', align: 'left',
                    formatter: function status(row, value) {
                        var context = '';
                        receiveDetailStatus.forEach(function (v) {
                            if (v.value == value.vbillstatus) {
                                context = v.context;
                                return false;
                            }
                        });
                        return context;
                    }
                },
                {
                    field: 'freeType', title: '费用类型', align: 'left',
                    formatter: function status(value, row) {
                        switch (value - 0) {
                            case 0:
                                return '<span>运费</label>';
                            case 1:
                                return '<span>在途费用</label>';
                            case 2:
                                return '<span>调整费用</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    field: 'costTypeOnWay', title: '费用类型明细', align: 'left',
                    formatter: function status(value, row) {
                        switch (row.freeType - 0) {
                            case 0:
                                return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                            case 1:
                                return $.table.selectDictLabel(costTypeOnWay, value);
                            case 2:
                                return '<span>调整费用</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    field: 'managerFee', title: '管理费', align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    field: 'transFeeCount', title: '金额', align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {field: 'adjustAmount', title: '调整金额', align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {field: 'memo', title: '备注', align: 'left'},
                {
                    field: 'isAdjust', title: '是否为调整单', align: 'left',
                    formatter: function (value, row, index) {
                        if (value === 0) {
                            return "否";
                        } else {
                            return "是";
                        }
                    }
                },
                {field: 'adjustMemo', title: '调整原因', align: 'left'},
                {field: 'regDate', title: '创建时间', align: 'left'},
                {field: 'writeOffDate', title: '核销时间', align: 'left'},
                {field: 'regUserId', title: '创建人', align: 'left'}
            ]
        };

        $.table.init(options);
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                //查询方法
                $.table.search();
            }
        });
    });

    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;

    function receRecord() {
        var id = $.table.selectColumns('receiveDetailId');
        var url = ctx + "receSheetRecord" + "/receRecord?receiveDetailId=" + id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "收款记录",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 调整
     */
    function adjust() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (isClose != 1) {
            $.modal.alertWarning("请选择已关账的对账单");
            return;
        }
        $.modal.openTab("调整", ctx + "receive/adjust?receiveDetailId=" + bootstrapTable[0]["receiveDetailId"]);
    }


    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;

    function detail(receiveDetailId) {
        var url =  ctx + "receive/adjustRecord?receiveDetailId="+receiveDetailId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "调整单记录",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

</script>
</body>
</html>