<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户对账')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "receCheckSheet";

    var receCheckSheetId = [[${receCheckSheetId}]];//应收对账id
     var receivable_method = [[${@dict.getType('receivable_method')}]];//收款方式
    var receivable_type = [[${@dict.getType('receivable_type')}]];//收款类型

    $(function () {
        var options = {
            url: prefix + "/receRecordList/"+receCheckSheetId,
            showToggle:false,
            showColumns:false,
            showSearch: false,
            showRefresh:false,
            showFooter: true,
            modalName: "收款记录",
            fixedColumns: true,
            onPostBody:function () {
                getAmountCount();
            },
            columns: [{
                footerFormatter: function (row) {
                    return "总收款:<nobr id='sumTotalAmount'>￥0</nobr>&nbsp&nbsp";
                }
            },
                {
                    title: '单据号',
                    field: 'vbillno',
                    align: 'left'
                },
                {
                    title: '收款日期',
                    field: 'receivableDate',
                    align: 'left'
                },
                {
                    title: '收款类型',
                    field: 'receivableType',
                    align: 'left',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(receivable_type,value);
                    }
                },
                {
                    title: '收款方式',
                    field: 'receivableMethod',
                    align: 'left',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(receivable_method,value);
                    }
                },
                {
                    title: '收款金额(元)',
                    halign: "center",
                    field: 'receivableAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if(row.receivableAmount != null){
                            return row.receivableAmount.toLocaleString( {style: 'currency', currency: 'CNY'});
                        }

                    }
                },
                {
                    title: '备注',
                    field: 'memo',
                    align: 'left'
                },
            ]
        };

        $.table.init(options);
    });

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        $.ajax({
            url: prefix + "/receRecordListCount/"+receCheckSheetId,
            type: "post",
            dataType: "json",
            success: function(result) {
                console.log(result);
                if (result.code == 0) {
                    var data = result.data;
                    console.log(data);
                    //总应收
                    $("#sumTotalAmount").text(data.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

                }
            }
        });
    }

</script>

</body>
</html>