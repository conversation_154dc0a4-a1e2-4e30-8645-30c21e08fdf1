<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('管理费')"/>
</head>

<body>
<div class="form-content">
    <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
        <div class="panel-body">
            <!--基础信息 begin-->
            <input id="receCheckSheetId" name="receCheckSheetId" type="hidden" th:value="${receCheckSheetId}">
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red">管理费：</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" oninput="$.numberUtil.onlyNumber(this)" id="managerFee" name="managerFee"
                                   placeholder="管理费" autocomplete="off" maxlength="20" required>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red">付款方式：</label>
                        <div class="col-sm-8">
                            <select class="form-control valid" id="payMethod" aria-invalid="false"
                                    name="payMethod" th:with="type=${@dict.getType('pay_method')}" required>
                                <option value=""></option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4">收款人：</label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <input type="text" name="receiveMan" id="receiveMan" maxlength="25" class="form-control" autocomplete="off">
                                <div class="input-group-btn" z-index="999">
                                    <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4">收款账号：</label>
                        <div class="col-sm-8">
                            <input type="text" name="receiveCard" id="receiveCard" maxlength="25" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4">收款银行：</label>
                        <div class="col-sm-8">
                            <input type="text" name="receiveBank" id="receiveBank" maxlength="50" class="form-control">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-2" >备注：</label>
                        <div class="col-sm-10">
                           <textarea name="memo" id="memo" maxlength="200" class="form-control valid"  rows="4"></textarea>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">
    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-out-quote").serializeArray();
            $.operate.save(ctx + "receCheckSheet/insertFeeEntry", data);
        }
    }

    $(function () {
        //模糊查询
        $("#receiveMan").bsSuggest('init', {
            url: ctx + "receCheckSheet/findReceiveMan?receiveMan="+$("#receiveMan").val(),
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "memo",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["memo"],
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
            $("#receiveMan").val(data.receiveMan);
            $("#receiveCard").val(data.receiveCard);
            $("#receiveBank").val(data.receiveBank);
        })
    });
</script>
</body>
</html>