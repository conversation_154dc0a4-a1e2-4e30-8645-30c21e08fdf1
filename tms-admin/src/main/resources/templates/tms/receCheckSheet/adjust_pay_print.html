<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <meta charset="UTF-8">
    <title></title>
    <style>
        @page {
            /*size: 210mm 297mm;*/
            /*margin: 1.54cm 1.17cm 1.54cm 1.17cm;*/
            margin: 6mm;
            /*mso-header-margin: 1.5cm;
            mso-footer-margin: 1.75cm;
            mso-paper-source: 0;*/
        }
        html {
            /*width: 210mm;
            height: 297mm;*/
            border: 0px #000 solid;
            margin: 0;
            padding: 0;
        }
        body {
            margin: 0;
            padding: 0;
        }
        * {
            font-family: SimHei;
            font-size: 14px;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
        .right {
            text-align: right;
        }
        .center {
            text-align: center;
        }
        .dtl {
            width: 100%;
            border-left: 1px #000 solid;
            border-top: 1px #000 solid;
            border-collapse: collapse;
            margin-top: 0.5mm;
        }
        .dtl td {
            border-right: 1px #000 solid;
            border-bottom: 1px #000 solid;
            padding: 1px 3px;
            line-height: 45px;
        }
        .tdTitle {
            background-color: #dedede;
            width:25%;
            text-align: center;
            -webkit-print-color-adjust: exact; /*控制打印的时候有背景色*/
        }
        .bold {
            font-weight: bold;
        }
        .page-break {
            page-break-before: always;
        }
        .dtlTable {
            width: 100%;
            border-left: 1px #000 solid;
            border-top: 1px #000 solid;
            border-collapse: collapse;
        }
        .dtlTable th {
            background-color: #dedede;
            -webkit-print-color-adjust: exact; /*控制打印的时候有背景色*/
            border-right: 1px #000 solid;
            border-bottom: 1px #000 solid;
            font-weight: normal;
            padding: 3px 2px;
            font-size: 12px;
        }
        .dtlTable td {
            border-right: 1px #000 solid;
            border-bottom: 1px #000 solid;
            padding: 3px 1px;
            page-break-inside: avoid;
        }
    </style>
</head>
<body>
<div class="title">应收调账申请审批单(打包)</div>
<div class="right">申请日期：<span>[[${date}]]</span></div>
<table class="dtl">

    <tr>
        <td class="tdTitle">客户名称</td>
        <td style="width: auto;width: 25%">[[${receCheckSheet.custName}]]</td>
        <td class="tdTitle">运营专员</td>
        <td style="width: auto;width: 25%">[[${sysDept.deptName}]]</td>
    </tr>
    <tr>
        <td class="tdTitle">单据号</td>
        <td style="width: auto;width: 25%">[[${receCheckSheet.vbillno}]]</td>
        <td class="tdTitle">调整比例</td>
        <td style="width: auto">[[${rate}]]</td>
    </tr>
    <tr>
        <td class="tdTitle">系统金额</td>
        <td style="width: auto" >[[${receCheckSheet.totalAmount}]]元</td>
        <td class="tdTitle">系统成本</td>
        <td style="width: auto" >[[${totalCb}]]元</td>
    </tr>
    <tr>
        <td class="tdTitle">系统毛利</td>
        <td style="width: auto" >[[${totalMl}]]元</td>
        <td class="tdTitle">调整后毛利</td>
        <td style="width: auto" >[[${totalMl+adjustAmount}]]元</td>
    </tr>

    <tr>
        <td class="tdTitle">实际收款</td>
        <td style="width: auto">[[${receCheckSheet.totalAmount+adjustAmount}]]元</td>
        <td class="tdTitle">调整金额（差价）</td>
        <td style="width: auto;    font-size: 16px;">
            <span style="width: auto;font-size: 16px;" th:if="${adjustAmount > 0}">调增</span>
            <span style="width: auto;font-size: 16px;" th:if="${adjustAmount <= 0}">调减</span>
            [[${adjustAmount}]]元
        </td>
    </tr>
    <tr>
        <td class="tdTitle" rowspan="1">调整原因</td>
        <td colspan="3" id="memoTd"></td>
    </tr>
    <tr>
        <td class="tdTitle">部门负责人</td>
        <td style="width: auto" ></td>
        <td class="tdTitle">结算负责人</td>
        <td style="width: auto" ></td>
    </tr>
    <tr>
        <td class="tdTitle">总经理</td>
        <td style="width: auto" ></td>
        <td class="tdTitle">董事长</td>
        <td style="width: auto" ></td>
    </tr>

</table>
<!--/*
<div class="page-break"></div>
<div style="text-align: center;margin-bottom: 5px;font-weight: bold;font-size: 16px;">
    [[${receCheckSheet.custName}]]对账明细（[[${receCheckSheet.vbillno}]]）
</div>
<table class="dtlTable">
    <tr>
        <th style="width: 70px">要求提货<br />日期</th>
        <th style="width: 90px">发货单号</th>
        <th>提货市</th>
        <th>到货市</th>
        <th>货物</th>
        <th>件/吨/方</th>
        <th>车长车型</th>
        <th>运费</th>
        <th>在途</th>
        <th>对账<br/>金额</th>
        <th>发货单<br/>应收</th>
        <th>应付<br/>运费</th>
        <th>应付<br/>在途</th>
        <th>三方<br/>费用</th>
        <th>总成本</th>
        <th>应付<br/>税金</th>
        <th>三方<br/>税金</th>
        <th>利润</th>
    </tr>
    <tr th:each="item : ${receiveList}">
        <td style="text-align: center">[[${item.reqDeliDate}]]</td>
        <td style="text-align: center">[[${item.vbillno}]]</td>
        <td>[[${item.deliCityName=='市辖区'||item.deliCityName=='县' ? item.deliProName : item.deliCityName}]]</td>
        <td>[[${item.arriCityName=='市辖区'||item.arriCityName=='县' ? item.arriProName : item.arriCityName}]]</td>
        <td>[[${item.goodsName}]]</td>
        <td style="text-align: center">[[${item.numCount>0?item.numCount:'-'}]]/[[${item.weightCount>0?item.weightCount:'-'}]]/[[${item.volumeCount>0?item.volumeCount:'-'}]]</td>
        <td>[[${item.carLenName}]]-[[${item.carTypeName}]]</td>
        <td style="text-align: right">[[${item.receiptAmountFreight}]]</td>
        <td style="text-align: right">[[${item.receiptAmountOnWay}]]</td>
        <td style="text-align: right">[[${item.receiptAmount}]]</td>
        <td style="text-align: right">[[${item.invTransFeeCount}]]</td>
        <td style="text-align: right">[[${item.costShareFreight}]]</td>
        <td style="text-align: right">[[${item.costShareOnWay}]]</td>
        <td style="text-align: right">[[${item.invOtherFee}]]</td>
        <td style="text-align: right">[[${item.totalCost}]]</td>
        <td style="text-align: right">[[${item.yfsf}]]</td>
        <td style="text-align: right">[[${item.dsfsf}]]</td>
        <td style="text-align: right">[[${item.profit}]]</td>
    </tr>
    <tr>
        <td colspan="7" style="text-align: center;font-weight: bold">合 计</td>
        <td style="text-align: right">[[${total['receiptAmountFreight']}]]</td>
        <td style="text-align: right">[[${total['receiptAmountOnWay']}]]</td>
        <td style="text-align: right">[[${total['receiptAmount']}]]</td>
        <td style="text-align: right">[[${total['invTransFeeCount']}]]</td>
        <td style="text-align: right">[[${total['costShareFreight']}]]</td>
        <td style="text-align: right">[[${total['costShareOnWay']}]]</td>
        <td style="text-align: right">[[${total['invOtherFee']}]]</td>
        <td style="text-align: right">[[${total['totalCost']}]]</td>
        <td style="text-align: right">[[${total['yfsf']}]]</td>
        <td style="text-align: right">[[${total['dsfsf']}]]</td>
        <td style="text-align: right">[[${total['profit']}]]</td>
    </tr>
</table>*/-->

</body>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    let adjustMemo = [[${adjustMemo}]];
    $(function() {
        document.getElementById('memoTd').innerHTML = adjustMemo;
    });
</script>
</html>