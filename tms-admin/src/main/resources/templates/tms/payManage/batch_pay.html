<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('付款申请管理-分批收款')"/>
    <style>
        .icheckbox-blue{
            top: 3px
        }
        .felx{
            display: flex;
            align-items: center;
        }
        .piao{
            padding: 8px 30px 8px 10px;;
            background: url("/img/kd.png") no-repeat center 100%/100%;
        }
        .ed5565{
            color: #D61D1D;
        }
        .ml10{
            margin-left: 10px;
        }
    </style>
</head>

<body>
<div class="form-content">
    <form id="form-batchPay-add" class="form-horizontal" novalidate="novalidate">
        <!--应付对账id-->
        <input type="hidden" name="payCheckSheetId" id="payCheckSheetId" th:value="${paySheetRecord?.payCheckSheetId}">
        <!--应收单据号-->
        <input type="hidden" name="vbillno" th:value="${paySheetRecord?.vbillno}">
        <!--付款申请id-->
        <input type="hidden" name="paySheetRecordId" th:value="${paySheetRecord?.paySheetRecordId}">
        <!--油卡-->
        <input type="hidden" name="fuelCard" id="fuelCard" th:value="${paySheetRecord?.fuelCard}">
        <!--行号-->
        <input type="hidden" name="bankNo" id="bankNo" th:value="${paySheetRecord?.bankNo}">
        <!--未付金额-->
        <input type="hidden" name="ungotAmount" th:value="${paySheetRecord?.ungotAmount}">

        <!--收款信息-->
        <input type="hidden"  id="recAccount1" th:value="${paySheetRecord.recAccount}">
        <input type="hidden"  id="recCardNo1" th:value="${paySheetRecord.recCardNo}">
        <input type="hidden"  id="recBank1" th:value="${paySheetRecord.recBank}">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="deptId" type="hidden" id="treeId">
                        <input  type="hidden" id="advancePayMoney" th:value="${carrier.advancePayMoney}">
                        <input  type="hidden" id="advancePayMoneyXj" th:value="${carrier.advancePayMoneyXj}">
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总金额：</label>
                                    <input type="hidden" name="totalAmount"  th:value="${paySheetRecord?.payAmount}">
                                    <div class="col-sm-8" th:text="*{paySheetRecord?.payAmount}"></div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">可申请额度：</label>
                                    <input type="hidden" name="quota" id="quota" th:value="${paySheetRecord?.ungotAmount}">
                                    <div class="col-sm-8" name="quota" th:text="${paySheetRecord?.ungotAmount}">
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付款类型：</label>
                                    <div class="col-sm-8">
                                        <input name="payType" class="form-control" value="0" type="hidden">
                                        <input class="form-control" value="对账付款" disabled >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">本次付款金额：</label>
                                    <!--不符合无车承运人-->
                                    <div class="col-sm-8" th:if="${payCheckSheet.isNtocc == 0}">
                                        <input name="payAmount" id="payAmount" required type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this)"
                                               maxlength="15" th:value="${paySheetRecord?.ungotAmount}" class="form-control" onchange="payAmountChange()">
                                    </div>
                                    <!--符合无车承运人-->
                                    <div class="col-sm-8" th:if="${payCheckSheet.isNtocc == 1}">
                                        <input name="payAmount" id="payAmount" required type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this)"
                                               maxlength="15" th:value="${paySheetRecord?.ungotAmount}" class="form-control" disabled onchange="payAmountChange()">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">付款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="payMethod" id="payMethod" class="form-control" th:with="type=${@dict.getType('pay_method')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:disabled="${dict.dictValue=='77'}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6" id="psbcDiv" style="display:none;">
                                <input type="radio"  name="personFlag" value="0" checked>对公打款
                                <input type="radio"  name="personFlag" value="1" >对私打款
                            </div>
                            <div class="col-md-6 col-sm-6" id="qixinDiv" style="display:none;">
                                <label class="checkbox-inline check-box" style="padding-top: 1px">
                                    <input type="checkbox" id="qixinPass" name="qixinPass" value="1"  >琦欣中转</label>
                            </div>
                            <div class="col-md-6 col-sm-6" id="advanceDiv" style="display:none;" th:if="${carrier.advancePayMoney != null && carrier.advancePayMoney > 0}">
                                <div class="form-group">
                                    <div class="felx ed5565">
                                        <div class="piao">
                                            <div th:text="${carrier.advancePayMoney}"></div>
                                        </div>
                                        <div class="ml10">
                                            <div>实际支付 <span id="reality">0</span> </div>
                                            <div>系统会优先抵扣预付款</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6" id="advanceXjDiv" style="display:none;" th:if="${carrier.advancePayMoneyXj != null && carrier.advancePayMoneyXj > 0}">
                                <div class="form-group">
                                    <div class="felx ed5565">
                                        <div class="piao">
                                            <div th:text="${carrier.advancePayMoneyXj}"></div>
                                        </div>
                                        <div class="ml10">
                                            <div>实际支付 <span id="realityXj">0</span> </div>
                                            <div>系统会优先抵扣预付款</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付款日期：</label>
                                    <div class="col-sm-8">
                                        <input name="payDate" id="payDate" class="form-control">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">

                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 outAccountLabel" style="color: red">转出账户：</label>
                                    <div class="col-sm-8">
                                        <!--<select name="outAccount" id="outAccount" class="form-control" required onchange="changeRequired();vali();"
                                                th:field="${paySheetRecord.outAccount}">
                                            <option value=""></option>
                                            <option th:each="dict : ${account}"
                                                    th:text="${dict.accountName}"
                                                    th:id="${dict.accountCode}"
                                                    th:value="${dict.accountId}" >
                                            </option>
                                        </select>-->
                                        <div class="input-group">
                                            <!--账户名称-->
                                            <input name="accountName" id="accountName" th:value="${account?.accountName}"required onchange="changeRequired();vali();"
                                                   class="form-control valid"
                                                   type="text" aria-required="true" maxlength="50">
                                            <!--账户id-->
                                            <input name="outAccount" id="outAccount" th:value="${account?.accountId}" required class="form-control valid"
                                                   type="hidden" aria-required="true" maxlength="50">
                                            <!--账户编码-->
                                            <input name="accountCode" id="accountCode" th:value="${account?.accountCode}" required class="form-control valid"
                                                   type="hidden" aria-required="true" maxlength="50">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收款账户：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="recAccount" id="recAccount" class="form-control rec" disabled="" th:field="${paySheetRecord.recAccount}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款卡号：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="recCardNo" id="recCardNo" disabled="" class="form-control rec" th:field="${paySheetRecord.recCardNo}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >收款银行：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="recBank" id="recBank" disabled=""  class="form-control rec" th:field="${paySheetRecord.recBank}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-2">核销油卡：</label>
                                    <div class="col-sm-10">
                                        <input type="text"  class="form-control" th:value="${fuelcardTotal}">
                                    </div>
                                    <input type="hidden" id="writeFuelcardId" name="writeFuelcardId" th:value="${writeFuelcardId}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">付款备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" maxlength="30" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script>
    var prefix = ctx + "payManage";
    //额度
    var quota = parseFloat($("#quota").val());

    /**
     * 付款日期默认当前日期
     */
    var time = new Date();
    var day = ("0" + time.getDate()).slice(-2); //获取当前日期
    var month = ("0" + (time.getMonth() + 1)).slice(-2); //获取当前月份
    var h=time.getHours();       //获取当前小时数(0-23)
    var m=time.getMinutes();     //获取当前分钟数(0-59)
    var s=time.getSeconds();     //获取当前秒数
    var today = time.getFullYear() + "-" + (month) + "-" + (day) + " "+h+":" +m+ ":"+s;
    $("#payDate").val(today);

    var bala_corp = '[[${payCheckSheet.balaCorp}]]';
    var lotG7End = [[${payCheckSheet.lotG7End == null ? 'null' : payCheckSheet.lotG7End}]];


    $(function () {
        $('#collapseOne').collapse('show');

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#payDate',
                type: 'datetime',
                trigger: 'click'
            });
        });

        /** 校验 */
        $("#form-batchPay-add").validate({
            focusCleanup: true
        });

        /**
         * 付款方式为-油卡支付-带出油卡账户与油卡卡号
         */
        $('#payMethod').change(function(){
            var payMethod = $(this).find(":selected").val();//付款方式
            //先置空收款信息
            $("#recAccount").val("");//收款账户
            $("#recCardNo").val("");//收款卡号
            $("#recBank").val("");//收款银行
            //改变必填
            changeRequired();
            //付款方式为油卡支付
            if(payMethod === '91'){
                OilPay();
                $("#advanceDiv").css("display","block");
                $("#advanceXjDiv").css("display","none");
            }else{
                $("#recAccount").val($("#recAccount1").val());//收款账户
                $("#recCardNo").val($("#recCardNo1").val());//收款卡号
                $("#recBank").val($("#recBank1").val());//收款银行
                $("#advanceDiv").css("display","none");
                $("#advanceXjDiv").css("display","block");
            }

            if(payMethod == '39'){
                $("#qixinDiv").css("display","block");
            }else{
                $("#qixinDiv").css("display","none");
            }

            if(payMethod == '38'){
                $("#psbcDiv").css("display","block");
            }else{
                $("#psbcDiv").css("display","none");
            }

            vali();
            //$("#accountName").prop("disabled", $(this).val() == '77')
            $("#payAmount").prop("disabled", $(this).val() == '77')
            if (payMethod == '77') {
                $("#payAmount").val([[${paySheetRecord.ungotAmount}]]);
                /*var corp_account = {
                    MY: "江苏铭源物流有限公司",
                    JH: "南通吉华物流有限公司",
                    DH: "亿鼎物流集团鼎辉公司",
                    DW: "亿鼎物流集团鼎旺公司"
                }
                $.ajax({
                    url: ctx + "finance/account/findAccount?paymentType=1&keyword=",
                    type: 'get',
                    cache: false,
                    dataType: 'json',
                    success: function(res){
                        if (res.code == 200) {
                            var list = res.value
                            for(var i=0;i<list.length;i++) {
                                if (list[i].accountCode == corp_account[bala_corp]) {
                                    $("#accountName").val(list[i].accountName);
                                    $("#outAccount").val(list[i].accountId);
                                    $("#accountCode").val(list[i].accountCode);
                                    break;
                                }
                            }
                        }
                    }
                })*/
            }
        });

        //如果为0 设置默认为油卡类别
        var type = [[${type}]];
        if (type === 0) {
            $('#payMethod').val('91');
            $("#payMethod").trigger("change");
        }

        if (lotG7End == 2) {
            var g7option = $("#payMethod").find("option[value='77']");
            if (g7option.length == 1) {
                g7option.prop("disabled", false);
                $("#payMethod").val('77').change();
            }
        }

        payAmountChange();
    });



    function payAmountChange(){
        var payAmount = $("#payAmount").val();
        if($("#payMethod").val()== '91'){
            var advancePayMoney = $("#advancePayMoney").val();
            let num=0;
            if(Number(payAmount) > Number(advancePayMoney) ){
                num= Number(payAmount) - Number(advancePayMoney);
            }else{
                num=0;
            }
            $("#reality").text(num);
            if(Number(advancePayMoney) >= Number(payAmount)){
                $(".outAccountLabel").css("color","inherit");
                $("#accountName").attr("required", false);
                return false;
            }else{
                $(".outAccountLabel").css("color","red");
                $("#accountName").attr("required", true);
                return true;
            }

        }else{
            var advancePayMoneyXj = $("#advancePayMoneyXj").val();
            let num=0;
            if(Number(payAmount) > Number(advancePayMoneyXj) ){
                num= Number(payAmount) - Number(advancePayMoneyXj);
            }else{
                num=0;
            }
            $("#realityXj").text(num);
            if(Number(advancePayMoneyXj) >= Number(payAmount)){
                $(".outAccountLabel").css("color","inherit");
                $("#accountName").attr("required", false);
                return false;
            }else{
                $(".outAccountLabel").css("color","red");
                $("#accountName").attr("required", true);
                return true;
            }

        }
        return true;
    }

    /**
     * 不同选项，必填参数不同
     */
    function changeRequired() {
        var outAccount = $('#accountCode').val();//获取转出账户编码
        var payMethod =  $('#payMethod').find(":selected").val();//获取付款方式
        /**
         *  当转出账户与付款方式都为现金支付时 收款信息非必填 其余为必填
         */
        if((outAccount === '现金支付' && payMethod === '42' )|| (outAccount === '异常销账账户' && payMethod === '93')){
            $(".rec").attr("required", false);
        }else if(payMethod === '91') {
            $(".rec").attr("required", true);
            $("#recBank").attr("required", false);
        }else{
            $(".rec").attr("required", true);
        }
    }

    /**
     * 校验
     */
    function vali() {
        $("#form-batchPay-add").validate().element($("#recAccount"));
        $("#form-batchPay-add").validate().element($("#recCardNo"));
        $("#form-batchPay-add").validate().element($("#recBank"));
    }
    /**
     * 油卡支付 回显油卡信息
     * @constructor
     */
    function OilPay() {
        //应付对账id
        var fuelCard = $("#fuelCard").val();
        var data = {fuelCard:fuelCard};
        var url = ctx + "payManage/selectFuelCard";
        $.ajax({
            url : url,
            method : 'POST',
            data : data,
            success:function (data) {
                $("#recAccount").val(data.fuelcardName);
                $("#recCardNo").val(data.fuelcardNo);

                vali();
            }
        })
    }

    //提交
    function submitHandler() {
        //本次付款金额
        var payAmount = parseFloat($("#payAmount").val());
        if(payAmount>quota){
            $.modal.alertError("本次付款金额必须<="+quota);
            return ;
        }
        //转出账户
        if(payAmountChange()){
            var outAccount = $("#outAccount").val();
            if($.common.isEmpty(outAccount)){
                $.modal.alertError("请选择转出账户！");
                return ;
            }
        }

        var dis = $(":disabled");//移除disabled
        dis.attr("disabled", false);

        //校验
        if ($.validate.form()) {
            //默认未锁定
            var lock = false;
            layer.confirm("确认支付？", {
                btn: ["确认", "取消"]
            }, function (index, layero) {
                if(!lock){
                    lock = true;
                    layer.close(index);
                    $.operate.save(prefix + "/saveBatchPay", $('#form-batchPay-add').serialize(), function (result) {
                        if (result.code != 0) {
                            dis.attr("disabled", true);
                        }
                    });
                }
            }, function (index) {
                layer.close(index);
                dis.attr("disabled", true);
            });
        } else {
            dis.attr("disabled", true);
        }
    }

    /**
     * 关键字提示查询 转入账户
     */
    $("#accountName").bsSuggest('init', {
        url: ctx + "finance/account/findAccount?paymentType=1&balaCorp="+bala_corp,
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["accountName","accountCode","account"],
        effectiveFieldsAlias: {"accountName":"账户名称","accountCode":"账户信息","account":"账号"},
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#accountName").val(data.accountName);
        $("#outAccount").val(data.accountId);
        $("#accountCode").val(data.accountCode);
    })




</script>
</body>

</html>