<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('运单详情')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .taCl thead{
        background-color: #F7F8FA !important;
    }
    .table-responsive{
        width: 90vw;
        margin-left: 26px;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
    }
    .table>thead>tr>th:last-child {
        border-right: 1px #e7eaec solid !important;
    }
    .table-striped .table, .table-striped .table{
        border-top: 1px #e7eaec solid !important;
    }
    .label-successT {
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }

</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse" style="display: none">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="payCheckSheetId" th:value="${payCheckSheetId}">
                <input type="hidden" id="paySheetRecordVbillno" name="paySheetRecordVbillno" th:value="${paySheetRecordVbillno}">
                <!-- <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="lotno" id="lotno" class="form-control"
                                       placeholder="请输入运单号" maxlength="30">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="status" id="status" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="应付单状态" multiple>
                                    <option th:each="dict : ${payDetailStatusEnum}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>


                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="carrName" id="carrName" placeholder="请输入承运商名称" class="form-control valid">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="driverName" id="driverName" class="form-control" placeholder="请输入司机名称"
                                       maxlength="30">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="regUserName" id="regUserName" class="form-control"
                                       placeholder="请输入调度人" maxlength="25">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="invoiceVbillno"  class="form-control"
                                       placeholder="请输入发货单号" maxlength="25">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="custAbbr"  class="form-control"
                                       placeholder="请输入客户简称" maxlength="25">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="salesDeptName" id="salesDeptName" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运营组" multiple>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input type="text" style="width: 47%; float: left;" class="form-control"
                                       id="reqDeliDateStart"  name="reqDeliDateStart" placeholder="要求提货日期开始">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:6%;">-</span>
                                <input type="text" style="width: 47%; float: left;" class="form-control"
                                       id="reqDeliDateEnd"  name="reqDeliDateEnd" placeholder="要求提货日期结束">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group" style="text-align: center">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div> -->

            </form>
        </div>


        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var balatype = [[${@dict.getType('bala_type')}]];
    var prefix = ctx + "payManage";
    var paySheetRecordVbillno = $('#paySheetRecordVbillno').val()

    var ys = 0;
    var yf = 0;
    var yfsf = 0;
    var dsf = 0;
    var dsfsf = 0;
    var ptf = 0;
    var profit = 0;

    $(function () {
        var options = {
            url: ctx + "payManage/invoice/list",
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            fixedColumns: false,
            rememberSelected: false,
            fixedNumber: 0,
            clickToSelect:true,
            showFooter:true,
            onPostBody:function () {
                //合并页脚
                //merge_footer();
                //查询合计总金额
                getAmountCount();
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            detailView: true,
            uniqueId: "entrustLotId",
            onExpandRow: function (index, row, $detail) {
                InitSubTable(index, row, $detail);
            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "合计:&nbsp&nbsp"
                            + "应收:<nobr id='ys'>￥0</nobr>&nbsp&nbsp"
                            + "应付:<nobr id='yf'>￥0</nobr>&nbsp&nbsp"
                            + "应付税金:<nobr id='yfsf'>￥0</nobr>&nbsp&nbsp"
                            + "三方:<nobr id='dsf'>￥0</nobr>&nbsp&nbsp"
                            + "三方税金:<nobr id='dsfsf'>￥0</nobr>&nbsp&nbsp"
                            + "平台费:<nobr id='ptf'>￥0</nobr>&nbsp&nbsp"
                            + "利润:<nobr id='profit'>￥0</nobr>&nbsp&nbsp<br>"
                            +"总合计:&nbsp&nbsp"
                            + "应收:<nobr id='ysTotal'>￥0</nobr>&nbsp&nbsp"
                            + "应付:<nobr id='yfTotal'>￥0</nobr>&nbsp&nbsp"
                            + "应付税金:<nobr id='yfsfTotal'>￥0</nobr>&nbsp&nbsp"
                            + "三方:<nobr id='dsfTotal'>￥0</nobr>&nbsp&nbsp"
                            + "三方税金:<nobr id='dsfsfTotal'>￥0</nobr>&nbsp&nbsp"
                            + "平台费:<nobr id='ptfTotal'>￥0</nobr>&nbsp&nbsp"
                            + "利润:<nobr id='profitTotal'>￥0</nobr>&nbsp&nbsp"
                    }
                },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'vbillno'
                },
                {
                    title: '客户',
                    align: 'left',
                    field: 'custAbbr'
                },
                {
                    title: '提货|到货地址',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let deli="",arri="";
                        deli=row.deliProName+row.deliCityName+row.deliAreaName;
                        arri=row.arriProName+row.arriCityName+row.arriAreaName;

                        return deli+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+arri;
                    }
                },
                {
                     title: '要求车长车型',
                     align: 'left',
                    formatter: function status(value, row, index) {
                         return row.carLenName+row.carTypeName;
                    }
                 },
                {
                    title: '货量',
                    align: 'left',
                    field: 'goodsName',
                    formatter: function (value, row, index) {
                        let html=[]
                        if(row.numCount != null && row.numCount != ""&& row.numCount != 0){
                            html.push(row.numCount+"件")
                        }
                        if(row.weightCount != null && row.weightCount != ""&& row.weightCount != 0){
                            html.push(row.weightCount+"吨")
                        }
                        if(row.volumeCount != null && row.volumeCount != ""&& row.volumeCount != 0){
                            html.push(row.volumeCount+"方")
                        }
                        return row.goodsName+html.join("|");
                    }
                },
                {
                    title: '应收金额',
                    align: 'right',
                    field: 'ys',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                // {
                //     title: '未税总应收',
                //     align: 'right',
                //     field: 'netInfo.ys',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                {
                    title: '应付金额',
                    align: 'right',
                    field: 'yf',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '应付税金',
                    align: 'right',
                    field: 'yfsf',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '三方金额',
                    align: 'right',
                    field: 'dsf',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '三方税金',
                    align: 'right',
                    field: 'dsfsf',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '平台费',
                    align: 'right',
                    field: 'ptf',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '利润',
                    align: 'right',
                    field: 'profit',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '回单照片',
                    field: 'receiptUploadFiles',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var html = "<div class='picviewer'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html += `<img style="height:32px" src="`+element.filePath+`"/>`
                            });
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                }
            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
    });

    function InitSubTable(index, row, $detail) {
        var childTable = $detail.html('<div class="table-responsive"><table id="child-table" class="table table-bordered table-striped taCl"></table></div>').find('table');
        $(childTable).bootstrapTable({
            url: ctx + "payManage/invoice/allocation/list",
            method: 'post',
            sidePagination: "server",
            contentType: "application/x-www-form-urlencoded",
            queryParams:{
                invoiceId:row.invoiceId,
                payCheckSheetId:row.payCheckSheetId,
            },
            columns: [
                {
                    title: '应付单号',
                    field: 'payDetailVbillno',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var result = value;


                        if(row.isInCheckSheet == 1) {
                            result += ' <span class="label label-successT" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="本条应付包含在该月度付款申请单['+paySheetRecordVbillno+']中">包含</span>';
                        }
                        return result
                    }
                },
                {
                    title: '应付单状态',
                    field: 'payDetailVbillstatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == 0){
                            return '<span class="label label-primary">新建</span>';
                        }else if(value == 1){
                            return '<span class="label label-warning">已确认</span>';
                        }else if(value == 2){
                            return '<span class="label label-success">已对账</span>';
                        }else if(value == 3){
                            return '<span class="label label-info">部分核销</span>';
                        }else if(value == 4){
                            return '<span class="label label-info">已核销</span>';
                        }else if(value == 5){
                            return '<span class="label label-default">关闭</span>';
                        }else if(value == 6){
                            return '<span class="label label-success">已申请</span>';
                        }else if(value == 7){
                            return '<span class="label label-danger">核销中</span>';
                        } else if (value==8) {
                            return '<span class="label label-info">审核中</span>';
                        } else if (value==9) {
                            return '<span class="label label-success">复核通过</span>';
                        }
                    }
                },
                {
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value == 0&&row.costTypeFreight!=null) {
                            return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                        }else if (value == 1&&row.costTypeOnWay!=null){
                            return $.table.selectDictLabel(costTypeOnWay, row.costTypeOnWay);
                        }else{
                            return "运费"
                        }
                    }
                },
                {
                    title: '分摊金额',
                    field: 'costShare',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '总金额',
                    field: 'transFeeCount',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '已付金额',
                    field: 'gotAmount',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未付金额',
                    field: 'ungotAmount',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '承运商名称',
                    field: 'carrName',
                    align: 'left'
                },
                {
                    title: '司机/车牌',
                    field: 'driverName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return getValue(value) + '<br />' + getValue(row.carno);
                    }

                },


            ]
        });
    }

    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }



    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        // data.status = $.common.join($('#status').selectpicker('val'));
        // data.salesDeptName = $.common.join($('#salesDeptName').selectpicker('val'));
        //data.costTypeFreight = $.common.join($('#costTypeFreight').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        // $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: prefix + "/invoice/listCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $("#ysTotal").text(data.ysTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#yfTotal").text(data.yfTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#yfsfTotal").text(data.yfsfTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#dsfTotal").text(data.dsfTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#dsfsfTotal").text(data.dsfsfTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#ptfTotal").text(data.ptfTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#profitTotal").text(data.profitTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    if (data.profitTotal < 0) {
                        $("#profitTotal").css("color", "red");
                    }else {
                        $("#profitTotal").css("color", "");
                    }
                }
            }
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        ys = 0;
        yf = 0;
        yfsf = 0;
        dsf = 0;
        dsfsf = 0;
        ptf = 0;
        profit = 0;
    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        ys = ys + row.ys;
        yf = yf + row.yf;
        yfsf = yfsf + row.yfsf;
        dsf = dsf + row.dsf;
        dsfsf = dsfsf + row.dsfsf;
        ptf = ptf + row.ptf;
        profit = profit + row.profit;
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#ys").text(ys.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#yf").text(yf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#yfsf").text(yfsf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#dsf").text(dsf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#dsfsf").text(dsfsf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#ptf").text(ptf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#profit").text(profit.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }

    function subTotal(row) {
        ys = ys - row.ys;
        yf = yf - row.yf;
        yfsf = yfsf - row.yfsf;
        dsf = dsf - row.dsf;
        dsfsf = dsfsf - row.dsfsf;
        ptf = ptf - row.ptf;
        profit = profit - row.profit;
    }

</script>
</body>
</html>