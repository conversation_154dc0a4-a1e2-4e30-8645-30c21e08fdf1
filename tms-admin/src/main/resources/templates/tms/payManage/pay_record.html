<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('付款申请-付款记录')"/>
    <style type="text/css">
        .table-striped {
            height: calc(100% - 20px);
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped" >
            <input name="payDetailId" id="payDetailId" type="hidden" th:value="${payDetailId}">

            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "payManage";
    //付款记录id
    var paySheetRecordId = [[${paySheetRecordId}]];
    var url = prefix + "/payRecordList/"+paySheetRecordId;
    // 应付明细ID
    var payDetailId = $("#payDetailId").val();
    let lotId = $("#lotId").val();

    if (payDetailId !== null && payDetailId !== ''){
        url = prefix + "/payRecordList?payDetailId="+payDetailId;
    }

    var pay_method = [[${@dict.getTypeAll('pay_method')}]];//付款方式
    var pay_type = [[${@dict.getType('pay_type')}]];//付款类型

    $(function () {
        var options = {
            url: url,
            showToggle:false,
            showColumns:false,
            showSearch: false,
            showRefresh:false,
            modalName: "付款记录",
            fixedColumns: true,
            clickToSelect:true,
            columns: [
                {
                    title: '单据号',
                    field: 'vbillno',
                    align: 'left'
                },
                {
                    title: '付款日期',
                    field: 'payDate',
                    align: 'left'
                },
                {
                    title: '付款金额',
                    field: 'payAmount',
                    align: 'right',
                    halign: "center",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '付款类型',
                    field: 'payType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(pay_type, value);
                    }
                },
                {
                    title: '付款方式',
                    field: 'payMethod',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(pay_method, value);
                    }
                },


                {
                    title: '收款账户',
                    field: 'recAccount',
                    align: 'left'
                },
                {
                    title: '收款卡号',
                    field: 'recCardNo',
                    align: 'left'
                },
                {
                    title: '付款备注',
                    field: 'memo',
                    align: 'left'
                },
                {
                    title: '付款人',
                    field: 'payMan',
                    align: 'left'
                }


            ]
        };

        $.table.init(options);
    });

</script>

</body>
</html>