<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反确认')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
</head>

<body>
<div class="form-content">
    <form id="form-invoice-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="paySheetRecordId" name="paySheetRecordId" type="hidden" th:value="${paySheetRecordId}">
            <div class="panel-body">
                <!--基础信息 begin-->
                <input name="deptId" type="hidden" id="treeId">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">反核销人：</label>
                            <div class="col-sm-12">
                                <input type="text" class="form-control" maxlength="50" required name="backPerson">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">反核销类型：</label>
                            <div class="col-sm-7">
                                <select name="payBackType" id="payBackType" th:with="type=${@dict.getType('back_confirm_type')}" class="form-control valid" aria-invalid="false">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">反核销说明：</label>
                            <div class="col-sm-12">
                                <textarea name="payBackMemo" id="payBackMemo" class="form-control" type="text"
                                          maxlength="50" required="" aria-required="true"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">反核销证明：</label>
                            <div class="col-sm-12">
                                <input id="image" class="form-control"
                                       name="image" type="file" multiple>
                                <input id="tid" name="tid" type="hidden" >
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </form>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">
    $(function () {
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",  //用于绑定下一步方法
            fileType: "file"//文件类型
        };
        var tid = "tid";
        var imageId = "image";
        $.file.initAddFiles(imageId, tid, picParam);
    });

    var prefix = ctx + "payManage";
    /**
     * 校验
     */
    $("#form-invoice-unconfirm").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            unconfirmType:{
                required:true,
            },
            unconfirmMemo:{
                required:true,
            }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            $.modal.confirm("该操作将反核销该应付申请，确认吗？", function() {
                $("#image").fileinput('upload');
                jQuery.subscribe("cmt",commit);
            });
        }
    }

    function commit(){
        if($("#tid").val() == null || $("#tid").val() == '' ){
            $.modal.alertWarning("请上传反核销证明");
            return false;
        }

        var data = $("#form-invoice-unconfirm").serializeArray();
        $.operate.save(prefix + "/back_pay", data);
    }

</script>
</body>
</html>