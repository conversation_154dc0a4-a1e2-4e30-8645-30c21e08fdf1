<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('付款申请管理')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse,.f7f7{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }

    .table-striped {
        height: calc(100% - 70px);
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .label-errorT{
        color: #1c84c6;
        background-color: yellow;
        border: 1px solid #1c84c6;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">单据号：</label>-->
                            <div class="col-sm-12">
                                <input name="vbillno" class="form-control" type="text" placeholder="单据号"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">对账单号：</label>-->
                            <div class="col-sm-12">
                                <input name="reconNumber" class="form-control" type="text" placeholder="对账单号"
                                       maxlength="30" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">承运商名称：</label>-->
                            <div class="col-sm-12">
                                <input name="params[carrName]" class="form-control" type="text" placeholder="承运商名称"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">收款账户：</label>-->
                            <div class="col-sm-12">
                                <input name="recAccount" class="form-control" type="text" placeholder="收款账户"
                                       maxlength="30" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">单据状态：</label>-->
                            <div class="col-sm-12">
                                <select name="status" id="status" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="单据状态" multiple >
                                    <option th:each="dict : ${vbillnoStatus}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">申请类型：</label>-->
                            <div class="col-sm-12">
                                <select name="params[type]" id="type" data-none-selected-text="申请类型" class="form-control selectpicker" >
                                    <option></option>
                                    <option value="0">油卡</option>
                                    <option value="1">现金</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="params[payWay]" data-none-selected-text="支付途径" class="form-control selectpicker" >
                                    <option></option>
                                    <option value="g7">G7</option>
                                    <option value="yl">银联</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="payMan" class="form-control" type="text" placeholder="申请人"
                                       maxlength="30" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-4">
                    </div>
                    <div class="col-md-4 col-sm-4">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="finance:paySheetRecord:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-primary multiple disabled" onclick="check()" data-toggle="modal" id="check" shiro:hasPermission="finance:paySheetRecord:check">
                <i class="fa fa-check"></i>审核
            </a>
            <a class="btn btn-primary multiple disabled" onclick="checkReply()" shiro:hasPermission="finance:paySheetRecord:checkReply">
                <i class="fa fa-reply"></i>撤销审核通过
            </a>
            <a class="btn btn-primary single disabled" onclick="batchPay()" shiro:hasPermission="finance:payRecord:batchPay">
                <i class="fa fa-file-text-o"></i> 分批付款
            </a>
            <a class="btn btn-primary single disabled" onclick="payRecord()" shiro:hasPermission="finance:payRecord:payRecord">
                <i class="fa fa-calculator"></i> 付款记录
            </a>
            <a class="btn btn-danger single disabled" onclick="payBack()"  shiro:hasPermission="finance:payRecord:payBack">
                <i class="fa fa-reply"></i> 付款反核销
            </a>
            <a class="btn btn-primary multiple disabled" onclick="batchPayTogether()" shiro:hasPermission="finance:payRecord:batchPayTogether">
                <i class="fa fa-file-text-o"></i> 现金批量付款
            </a>
            <a class="btn btn-success" onclick="g7UPay()" shiro:hasPermission="finance:payRecord:batchPay">
                <i class="fa fa-money"></i> G7U盾支付
            </a>
            <a class="btn btn-danger" onclick="offlinePay()" shiro:hasPermission="finance:payRecord:batchPay">导出线下支付</a>

            <a class="btn btn-info single disabled" onclick="receiptView()">
                已上传发票
            </a>
        </div>

        <div class="modal inmodal fade " id="myModal6" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-sm checkClose">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span>
                        </button>
                        <h4 class="modal-title">审核</h4>
                    </div>
                    <div class="modal-body">
                        <button type="button" style="transform:translateX(50%)"  class="btn btn-sm btn-primary" name="refresh" data-dismiss="modal" onclick="check('pass')" ><i class="fa fa-check" ></i>通过
                        </button>
                        &nbsp;
                        <button type="button" style="transform:translateX(60%)"  class="btn btn-sm btn-danger" data-dismiss="modal" onclick="check('fail')" ><i class="fa fa-close"></i>不通过
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">

    var prefix = ctx + "payManage";
    var CloseAccountList = [[${CloseAccountList}]];//关账记录

    //付款方式
    var pay_type = [[${@dict.getType('pay_type')}]];
    //发票类型
    var billing_type = [[${@dict.getType('billing_type')}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    var collection_type = [[${@dict.getType('collection_type')}]];

    var transFeeCount = 0;
    var gotAmountCount = 0;
    var ungotAmountCount = 0;

    $(function () {
        let status = [[${status}]]
        $("#status").val([status]);
        $("#status").selectpicker('refresh');

        let type = [[${type}]]
        $("#type").val([type]);
        $("#type").selectpicker('refresh');

        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            exportUrl: prefix + "/export",
            showToggle: false,
            showColumns: true,
            fixedColumns: true,
            clickToSelect:true,
            fixedNumber:4,
            height: 580,
            showFooter:true,
            showExport:true,
            modalName: "月度付款",
            exportTypes:['excel','csv'],
            onPostBody:function () {
                //合并页脚
                merge_footer();
                //查询合计总金额
                getAmountCount();
            },
            onRefresh:function(params){
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
            },
            onCheck: function (row,$element) {
                var transFee = row.payAmount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数加上本行数值
                transFeeCount = transFeeCount + transFee;
                gotAmountCount = gotAmountCount + gotAmount;
                ungotAmountCount = ungotAmountCount + ungotAmount;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheck: function (row, $element) {
                var transFee = row.payAmount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数减去本行数值
                transFeeCount = transFeeCount - transFee;
                gotAmountCount = gotAmountCount - gotAmount;
                ungotAmountCount = ungotAmountCount - ungotAmount;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onCheckAll: function (rowsAfter) {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                //循环累加
                for (var row of rowsAfter) {
                    transFeeCount = transFeeCount + row.payAmount;
                    gotAmountCount = gotAmountCount + row.gotAmount;
                    ungotAmountCount = ungotAmountCount + row.ungotAmount;
                }
                //赋值
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheckAll: function () {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "总金额合计：<nobr id='transFeeCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "已付金额合计：<nobr id='gotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "未付金额合计：<nobr id='ungotAmountCountTotal'>￥0</nobr><br>"+
                        "总合计:总金额合计：<nobr id='sumPayAmountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "已付金额合计：<nobr id='sumGotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "未付金额合计：<nobr id='sumUngotAmountCountTotal'>￥0</nobr>";
                }
            },
                {
                    title: '操作',
                    align: 'left',
                    field: 'payCheckSheetId',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('finance:paySheetRecord:view')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="打印" onclick="prints(\''+row.paySheetRecordId+'\')"><i  class="fa fa-print" style="font-size: 15px;" ></i></a>');
                        }
                        if ([[${@permission.hasPermi('finance:paySheetRecord:view')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="应付明细" onclick="payDetail(\''+row.payCheckSheetId+'\')"><i  class="fa fa fa-list" style="font-size: 15px;" ></i></a>');
                        }
                        if ([[${@permission.hasPermi('finance:paySheetRecord:lot')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="运单详情" onclick="onLot(\''+row.payCheckSheetId+'\')"><i class="fa fa-bar-chart" style="font-size: 15px;"></i></a>');
                        }

                        if ([[${@permission.hasPermi('finance:paySheetRecord:invoice')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="发货单详情" onclick="onLotCopy(\'' + row.payCheckSheetId + '\',\''+row.vbillno+'\')"><i class="fa fa-bar-chart" style="font-size: 15px;"></i></a>');
                        }

                        return actions.join('');
                    }
                },
                {
                    title: '结算公司',
                    align: 'left',
                    field: 'balaCorp',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(balaCorp, value);
                    }
                },
                {
                    field: 'paySheetRecordId',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'vbillnoStatus',
                    title: '单据号/单据状态',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var result = row.vbillno;
                        var a = ''
                        let et = '';
                        if (row.lotG7End == 2) {
                            let auditStatusTxt = ['待提审','审核中','审核通过','审核失败'];
                            if (row.g7Exts) {
                                for (let i = 0; i < row.g7Exts.length; i++) {
                                    if (et) {
                                        et += "<br>"
                                    }
                                    if (row.g7Exts[i].G7_CAR_EXT) {
                                        let g7CarExt = JSON.parse(row.g7Exts[i].G7_CAR_EXT);
                                        if (g7CarExt.ysz) {
                                            let auditStatus = g7CarExt.ysz[row.g7Exts[i].BILLING_CORP.toLowerCase()];
                                            if (auditStatus != 2 && auditStatus != null) {
                                                let _st_t = auditStatusTxt[auditStatus]
                                                et = et + row.g7Exts[i].CARNO + "道路运输证【" + _st_t + "】；";
                                            }
                                        }
                                    }
                                    if (row.g7Exts[i].G7_DRIVER_EXT) {
                                        let g7DriverExt = JSON.parse(row.g7Exts[i].G7_DRIVER_EXT);
                                        if (g7DriverExt.zgz) {
                                            let auditStatus = g7DriverExt.zgz[row.g7Exts[i].BILLING_CORP.toLowerCase()];
                                            if (auditStatus != 2 && auditStatus != null) {
                                                let _st_t = auditStatusTxt[auditStatus]
                                                et = et + row.g7Exts[i].DRIVER_NAME +"从业资格证【" + _st_t + "】；";
                                            }
                                        }
                                    }

                                }
                            }
                            a = ' <span class="label '+ (et?'label-errorT':'label-success')+'"';
                            if (et) {
                                a += ' data-toggle="tooltip" data-container="body" data-placement="right" data-html="true" title="' + et + '"';
                            }
                            a = a + ' style="padding:1px;">G7</span>'
                        } else if (row.lotG7End == 3) {
                            a = ' <span class="label label-warning" title="预支" style="padding:1px 5px;">Y</span>'
                        }

                        let lotLockPay = ''
                        if (row.lockPay && row.lockPay == '1') {
                            lotLockPay = `<span class="label label-danger ml5" style="padding:2px;" data-toggle="tooltip"
                                    data-container="body" data-placement="top" data-html="true" title="存在锁定的应付"
                                    data-original-title="">锁</span>`
                        }

                        let carrLockPay = ''
                        if (row.carrLockPay && row.carrLockPay == '1') {
                            carrLockPay = `<span class="label label-danger ml5" style="padding:2px;" data-toggle="tooltip"
                                    data-container="body" data-placement="top" data-html="true" title="存在锁定的承运商"
                                    data-original-title="">锁</span>`
                        }


                        switch (value) {
                            case 0:
                                return result + '<br />'+'<span class="label label-default">待审核</span>'+ a + lotLockPay + carrLockPay
                            case 1:
                                return result + '<br />'+'<span class="label label-primary">审核通过</span>'+ a + lotLockPay + carrLockPay;
                            case 2:
                                return result + '<br />'+'<span class="label label-warning">审核未通过</span>'+ a + lotLockPay + carrLockPay;
                            case 3:
                                return result + '<br />'+'<span class="label label-info">部分付款</span>'+ a + lotLockPay + carrLockPay;
                            case 5:
                                return result + '<br />'+'<span class="label label-success">付款中</span>'+ a + lotLockPay + carrLockPay;
                            case 4:
                                return result + '<br />'+'<span class="label label-success">已付款</span>'+ a + lotLockPay + carrLockPay;
                            case 6:
                                return result + '<br />'+'<span class="label label-default">待业务审核</span>'+ a + lotLockPay + carrLockPay;
                            case 7:
                                return result + '<br />'+'<span class="label label-default">待总经办审核</span>'+ a + lotLockPay + carrLockPay;
                            default:
                                break;
                        }


                        return result + '<br />' + vbillnoStatus + a + lotLockPay + carrLockPay;
                    }
                },


                // {
                //     field: 'reconNumber',
                //     title: '对账单号',
                //     align: 'left',
                // },

                {
                    title: '对账单号/账户类型',
                    field: 'accountType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.reconNumber + '<br />'+ $.table.selectDictLabel(collection_type, value-0)
                    }

                },
                {
                    field: 'oilAccount',
                    title: '申请人/日期',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var a = getValue(row.payMan)+ '<br />' +getValue(row.payDate)
                        return a
                    }
                },
                {
                    title: '支付凭证',
                    align: 'left',
                    field : 'uploadFiles',
                    formatter: function status(value, row, index) {
                        var html = "<div class='picviewer'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html += `<img src="`+element.filePath+`"/>`
                            });
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                },
                // {
                //     field: 'oilAccount',
                //     title: '申请类型',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         if(row.isOil != null){
                //             if(row.isOil == 0){
                //                 return '现金'
                //             }else if(row.isOil == 1){
                //                 return '油卡'
                //             }
                //         }else{
                //             return value === null ? '现金':'油卡'
                //         }
                //     }
                // },
                // {
                //     field: 'payAmount',
                //     title: '申请金额',
                //     align: 'right',
                //     halign: "center",
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                {
                    field: 'gotAmount',
                    title: '金额明细',
                    align: 'right',
                    halign: "center",
                    formatter: function (value, row, index) {
                        let data=[];
                        var b = ''
                        if (row.payAmount === null) {
                            b='' ;
                        }else{
                            b= row.payAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }
                        if(row.isOil != null){
                            if(row.isOil == 0){
                                //return a + '<br />'+'现金' + '/' +b
                                data.push(b+`<span class="label badge-info pa2" style="background: #e65b58;margin-left: 5px" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="申请类型">现金</span> `)
                            }else if(row.isOil == 1){
                                //return a + '<br />'+'油卡' + '/' +b
                                data.push(b+`<span class="label badge-info pa2" style="background: #56b396;margin-left: 5px" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="申请类型">油卡</span> `)
                            }
                        }else{
                            if(row.payAmount===null){
                                data.push(b+`<span class="label badge-info pa2" style="background: #e65b58;margin-left: 5px" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="申请类型">现金</span> `)
                            }else{
                                data.push(b+`<span class="label badge-info pa2" style="background: #56b396;margin-left: 5px" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="申请类型">油卡</span> `)
                            }
                            //return value === null ? a + '<br />'+'现金'+ b:a + '<br />'+'油卡'+ '/' +b

                        }
                        // if(row.gotAmount){
                        //     data.push(`<span class="label badge-info pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="已付金额">已付</span> `+row.gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        // }
                        if(row.ungotAmount){
                            data.push(row.ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`<span class="label label-coral pa2" style="margin-left: 5px" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="未付金额">未付</span> `)
                        }

                        return data.join("<br/>")
                    }
                },
                 {
                     field: 'advancePayMoney',
                     title: '未抵扣余额',
                     align: 'right',
                     halign: "center",
                     formatter: function (value, row, index) {
                         if (value === null) {
                             return ;
                         }
                         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                     }
                 },
                // {
                //     field: 'ungotAmount',
                //     title: '未付金额',
                //     align: 'right',
                //     halign: "center",
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                // {
                //     field: 'fuelCard',
                //     title: '油卡卡号/名称',
                //     align: 'left',
                //     formatter: function (value, row, index) {
                //         return getValue(value) + '<br />'+getValue(row.fuelcardName);
                //     }
                // },
                // {
                //     field: 'fuelcardName',
                //     title: '油卡名称',
                //     align: 'left',
                // },
                {
                    field: 'params.carrName',
                    title: '承运商名称/身份证',
                    align: 'left',
                    formatter:function(value,row){
                        console.log(value)
                        if(row.balaType == 1 ){
                            return "单"+"-"+value+ '<br />'+getValue(row.legalCard);
                        }else if(row.balaType == 2){
                            return "月" + "-" + value+ '<br />'+getValue(row.legalCard);
                        }
                        return value+ '<br />'+getValue(row.legalCard);
                    }
                },
                // {
                //     field: 'legalCard',
                //     title: '承运商身份证',
                //     align: 'left',
                // },
                {
                    field: 'recAccount',
                    title: '收款信息',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(row.fuelCard){
                            return getValue(row.fuelCard) + '<br />'+getValue(row.fuelcardName)
                        }else{
                            return getValue(value) + '&nbsp;&nbsp;'+getValue(row.recCardNo)+ '<br />'+getValue(row.recBank);
                        }

                    }
                },
                // {
                //     field: 'recCardNo',
                //     title: '收款卡号',
                //     align: 'left',
                // },
                // {
                //     field: 'recBank',
                //     title: '收款银行',
                //     align: 'left',
                // },

                {
                    field: 'memo',
                    title: '备注',
                    align: 'left',
                },


            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }
        };

        $.table.init(options);
    });


    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }

    //审核
    function check(status) {
        //获取状态
        var vbillstatus = $.btTable.bootstrapTable('getSelections');
        //循环判断是否能够进行审核
        for(var i=0;i<vbillstatus.length;i++){
            if(vbillstatus[i]['vbillnoStatus'] !== 0 && vbillstatus[i]['vbillnoStatus'] !== 1){
                $.modal.alertWarning("只能在待审核状态下才能进行审核");
                return;
            }
        }
        var paySheetRecordIds = $.table.selectColumns("paySheetRecordId");
        if (paySheetRecordIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        //查询是否有应付明细
        var data = {paySheetRecordIds:paySheetRecordIds.join()};
        $.ajax({
            url: prefix + "/checkPayDetail",
            method: 'post',
            data: data,
            async:false,
            success: function(result) {
                if (result.code !== web_status.SUCCESS) {
                    $.modal.alertWarning("不存在应付明细，无法审核！");
                    return;
                } else {
                    $.modal.openTab("应付单据详情", prefix + "/detailViewCheck/" + paySheetRecordIds.join() );
                    return;
                }
            }
        });
        //弹出审核
        /*$("#check").attr("data-target","#myModal6");
        var paySheetRecordId = $.table.selectColumns('paySheetRecordId');
        var data = {};
        if(status=='pass'){
            var url = prefix+"/check/1/"+paySheetRecordId;
            $.operate.post(url,data,function(result){
                if (result.code == web_status.SUCCESS) {
                    location.reload();
                }
            });
        }
        if(status=='fail'){
            var url = prefix+"/check/2/"+paySheetRecordId;
            $.operate.post(url,data,function(result){
                if (result.code == web_status.SUCCESS) {
                    location.reload();
                }
            });
        }*/
    }

    //分批付款
    function batchPay(){
        var vbillnoStatus = $.table.selectColumns('vbillnoStatus');
        //判断是否能收款
        if(vbillnoStatus == 0){
            $.modal.alertError("该账单还未审批");
            return ;
        }else if(vbillnoStatus == 2){
            $.modal.alertError("该账单审批未通过");
            return ;
        }else if(vbillnoStatus == 4){
            $.modal.alertError("该账单付款完成");
            return ;
        }else if(vbillnoStatus == 5){
            $.modal.alertError("该账单已申请付款");
            return ;
        }else if(vbillnoStatus == 6){
            $.modal.alertError("该账单业务未审核");
            return ;
        }
        var id = $.table.selectColumns('paySheetRecordId');
        var url = prefix + "/batchPay/"+id;
        $.modal.open('分批付款',url);
    }

    //分批付款
    function batchPayTogether(){
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        var carrierId = bootstrapTable[0]["carrierId"];
        var recAccount = bootstrapTable[0]["recAccount"];
        for(var i = 0;i<bootstrapTable.length;i++){
            var vbillnoStatus = bootstrapTable[i]["vbillnoStatus"];
            //判断是否能收款
            if(vbillnoStatus != 1){
                $.modal.alertError("请勾选审核通过的账单");
                return false;
            }
            //判断现金支付
            var oilAccount = bootstrapTable[i]["oilAccount"];
            if(oilAccount != null){
                $.modal.alertError("请勾选现金账单");
                return false;
            }
            //判断同一承运商
            if(bootstrapTable[i]["carrierId"] != carrierId){
                $.modal.alertError("请勾选同一承运商的账单");
                return false;
            }
            //判断同一收款账号
            if(bootstrapTable[i]["recAccount"] != recAccount){
                $.modal.alertError("请勾选同一收款账户的账单");
                return false;
            }
        }

        var ids = $.table.selectColumns('paySheetRecordId');
        var url = prefix + "/batchPayTogether/"+ids;
        $.modal.open('现金批量付款',url);
    }


    //付款记录
    function payRecord() {
        var id = $.table.selectColumns('paySheetRecordId');//付款申请id
        var url = prefix + "/payRecord/"+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "付款记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 复核
     */
    function reexamine(payCheckSheetId) {
        var url = prefix + "/reexamine?payCheckSheetId=" + payCheckSheetId;
        $.modal.openTab("复核", url);
    }
    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $(".selectpicker").selectpicker('refresh');
        $("#role-form")[0].reset();
        searchPre();
    }


    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        data.status = $.common.join($('#status').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.status = $.common.join($('#status').selectpicker('val'));
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $("#sumPayAmountTotal").text(data.PAYAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumGotAmountCountTotal").text(data.GOTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumUngotAmountCountTotal").text(data.UNGOTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 撤销审核通过
     */
    function checkReply() {
        var url = prefix + "/saveCheckReply";
        //获取选择行
        var rows = $.btTable.bootstrapTable('getSelections');
        for(var i=0;i<rows.length;i++){
            if(rows[i]['vbillnoStatus'] != 1){
                $.modal.alertWarning("请选择审核通过的数据！")
                return;
            }
        }
        var paySheetRecordId = $.table.selectColumns('paySheetRecordId').join();
        $.modal.confirm("是否确认撤销审核通过选中的数据",function () {
            $.operate.submit(url, "post", "json", {"paySheetRecordId": paySheetRecordId});
        });

    }

    // 跳转对应的应付明细页面
    function payDetail(payCheckSheetId) {
        var url = ctx + "payCheckSheet/payDetail?payCheckSheetId="+payCheckSheetId;
        $.modal.openTab('应付明细',url);
    }

    function payBack(){
        var paySheetRecordId = $.table.selectColumns("paySheetRecordId");
        if (paySheetRecordId.length != 1 ) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        var vbillnoStatus = $.table.selectColumns("vbillnoStatus");
        if(vbillnoStatus != 4 && vbillnoStatus != 3){
            $.modal.alertWarning("请选择已付款或部分付款的应付对账单");
            return;
        }
        $.modal.open("支付反核销", prefix + "/back_pay/" + paySheetRecordId,600,500);
    }

    function g7UPay() {
        $.modal.openOptions({
            title: "G7U盾统一支付",
            url: prefix + "/g7UPay",
            width: 900,
            height: 600,
            btn: ['关闭'],
            callBack: function (index, layero) {
                //var iframeWin = layero.find('iframe')[0];
                //iframeWin.contentWindow.submitHandler(index, layero);
                layer.close(index)
            }
        });
    }

    function offlinePay() {
        if ($("[name='params[payWay]']").val() != 'yl') {
            $.modal.msgWarning("支付途径请选择银联");
            return
        }
        $.modal.confirm("确定导出吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            var search = $.common.formToJSON("role-form");
            console.log(search)
            // search.params = new Map();
            // search.params.invoice_vbillstatus = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
            // //search.params.receiveVbillstatus = $.common.join($('#receiveVbillstatus').selectpicker('val'));
            // search.salesDept = $.common.join($('#salesDept').selectpicker('val'));

            $.post(prefix + "/offlinePayExport", search, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    function receiptView() {
        var payCheckSheetId = $.table.selectColumns("payCheckSheetId");
        var paySheetRecordId = $.table.selectColumns("paySheetRecordId");

        $.ajax({
            url: prefix + "/checkSheetReceiptList?payCheckSheetId=" + payCheckSheetId, // + "&paySheetRecordId="+paySheetRecordId,
            cache: false,
            dataType: 'json',
            success: function(result) {
                if (result.code == 0) {
                    if (result.data.length == 0) {
                        $.modal.msg("当前对账单未上传发票");
                    } else {
                        var html = ["<div id='layerReceiptDiv' style='padding: 10px'>"];
                        for (let i = 0; i < result.data.length; i++) {
                            html.push("<span><img style='height:100px;margin:0 5px 0 0;' modal='zoomImg' src='",result.data[i].filePath,"'/></span>")
                        }
                        html.push("</div>")
                        layer.open({
                            type: 1,
                            area: ['900px', '600px'],
                            fix: false,
                            //不固定
                            maxmin: true,
                            shade: 0.3,
                            title: '已上传发票查看',
                            content: html.join(""),
                            btn: ['<i class="fa fa-remove"></i> 关闭'],
                            // 弹层外区域关闭
                            shadeClose: true,
                            success: function(index, layero) {
                                $("#layerReceiptDiv").find("[modal='zoomImg']").on("click", globalZoomFun)
                            },
                            btn1: function(index, layero){
                                layer.close(index)
                            }
                        });
                    }
                }

            }
        })

    }
    function onLot(payCheckSheetId) {
        var url = ctx + "payManage/lot?payCheckSheetId="+payCheckSheetId;
        $.modal.openTab('运单详情',url);
    }
    function onLotCopy(payCheckSheetId,paySheetRecordVbillno) {
        console.log(payCheckSheetId,paySheetRecordVbillno)
        var url = ctx + "payManage/invoice?payCheckSheetId="+payCheckSheetId+"&paySheetRecordVbillno=" + paySheetRecordVbillno;
        $.modal.openTab('发货单详情',url);
    }
    function prints(paySheetRecordId){
        var url =  ctx + "payCheckSheet/payPrintFee?paySheetRecordId="+paySheetRecordId;
        $.modal.openTab('月度对账费用申请单',url);
    }
</script>


</body>
</html>