<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('付款申请管理-分批收款')"/>
    <style>
        .icheckbox-blue{
            top: 3px
        }
    </style>
</head>

<body>
<div class="form-content">
    <form id="form-batchPay-add" class="form-horizontal" novalidate="novalidate">
        <!--应付对账id-->
        <input type="hidden" name="paySheetRecordId" th:value="${paySheetRecordId}">
        <input type="hidden" id="balaCorp" th:value="${balaCorp}">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="deptId" type="hidden" id="treeId">
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总金额：</label>
                                    <input type="hidden" name="totalAmount"  th:value="${totalAmount}">
                                    <div class="col-sm-8" th:text="${totalAmount}"></div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">可申请额度：</label>
                                    <input type="hidden" name="quota" id="quota" th:value="${totalUngotAmount}">
                                    <div class="col-sm-8" name="quota" th:text="${totalUngotAmount}">
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付款类型：</label>
                                    <div class="col-sm-8">
                                        <input name="payType" class="form-control" value="0" type="hidden">
                                        <input class="form-control" value="对账付款" disabled >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">本次付款金额：</label>
                                    <!--符合无车承运人-->
                                    <div class="col-sm-8" >
                                        <input name="payAmount" id="payAmount" required type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this)"
                                               maxlength="15" th:value="${totalUngotAmount}" class="form-control" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">付款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="payMethod" id="payMethod" class="form-control" th:with="type=${@dict.getType('pay_method')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:disabled="${dict.dictValue == '77'}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6" id="psbcDiv" style="display:none;">
                                <input type="radio"  name="personFlag" value="0" checked>对公打款
                                <input type="radio"  name="personFlag" value="1" >对私打款
                            </div>
                            <div class="col-md-6 col-sm-6" id="qixinDiv" style="display:none;">
                                <label class="checkbox-inline check-box" style="padding-top: 1px">
                                    <input type="checkbox" id="qixinPass" name="qixinPass" value="1"  >琦欣中转</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付款日期：</label>
                                    <div class="col-sm-8">
                                        <input name="payDate" id="payDate" class="form-control">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">

                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">转出账户：</label>
                                    <div class="col-sm-8">
                                        <!--<select name="outAccount" id="outAccount" class="form-control" required onchange="changeRequired();vali();"
                                                th:field="${paySheetRecord.outAccount}">
                                            <option value=""></option>
                                            <option th:each="dict : ${account}"
                                                    th:text="${dict.accountName}"
                                                    th:id="${dict.accountCode}"
                                                    th:value="${dict.accountId}" >
                                            </option>
                                        </select>-->
                                        <div class="input-group">
                                            <!--账户名称-->
                                            <input name="accountName" id="accountName" th:value="${account?.accountName}"required onchange="changeRequired();vali();"
                                                   class="form-control valid"
                                                   type="text" aria-required="true" maxlength="25">
                                            <!--账户id-->
                                            <input name="outAccount" id="outAccount" th:value="${account?.accountId}" required class="form-control valid"
                                                   type="hidden" aria-required="true" maxlength="25">
                                            <!--账户编码-->
                                            <input name="accountCode" id="accountCode" th:value="${account?.accountCode}" required class="form-control valid"
                                                   type="hidden" aria-required="true" maxlength="25">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>

                        <div class="row" th:each="paySheetRecordshow:${paySheetRecordList}">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收款账户：</label>
                                    <div class="col-sm-8">
                                        <input type="text" th:name="recAccount"  class="form-control rec" disabled="" th:value="${paySheetRecordshow.recAccount}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款卡号：</label>
                                    <div class="col-sm-8">
                                        <input type="text" th:name="recCardNo"  disabled="" class="form-control rec" th:value="${paySheetRecordshow.recCardNo}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >收款银行：</label>
                                    <div class="col-sm-8">
                                        <input type="text" th:name="recBank"  disabled=""  class="form-control rec" th:value="${paySheetRecordshow.recBank}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款金额：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="recAmount" disabled="" class="form-control recAmount disabled" th:value="${paySheetRecordshow.ungotAmount}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">

                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">付款备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" maxlength="30" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script>
    var prefix = ctx + "payManage";
    //额度
    var quota = parseFloat($("#quota").val());

    /**
     * 付款日期默认当前日期
     */
    var time = new Date();
    var day = ("0" + time.getDate()).slice(-2); //获取当前日期
    var month = ("0" + (time.getMonth() + 1)).slice(-2); //获取当前月份
    var h=time.getHours();       //获取当前小时数(0-23)
    var m=time.getMinutes();     //获取当前分钟数(0-59)
    var s=time.getSeconds();     //获取当前秒数
    var today = time.getFullYear() + "-" + (month) + "-" + (day) + " "+h+":" +m+ ":"+s;
    $("#payDate").val(today);

    $(function () {
        $('#collapseOne').collapse('show');

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#payDate',
                type: 'datetime',
                trigger: 'click'
            });
        });

        /** 校验 */
        $("#form-batchPay-add").validate({
            focusCleanup: true
        });

        /**
         * 付款方式为-油卡支付-带出油卡账户与油卡卡号
         */
        $('#payMethod').change(function(){
            var payMethod = $(this).find(":selected").val();//付款方式
            //先置空收款信息
  /*          $("#recAccount").val("");//收款账户
            $("#recCardNo").val("");//收款卡号
            $("#recBank").val("");//收款银行*/
            //改变必填
            changeRequired();

            if(payMethod == '39'){
                $("#qixinDiv").css("display","block");
            }else{
                $("#qixinDiv").css("display","none");
            }
            if(payMethod == '38'){
                $("#psbcDiv").css("display","block");
            }else{
                $("#psbcDiv").css("display","none");
            }

            //付款方式为油卡支付
           /* if(payMethod === '91'){
                OilPay();
            }else{
                $("#recAccount").val($("#recAccount1").val());//收款账户
                $("#recCardNo").val($("#recCardNo1").val());//收款卡号
                $("#recBank").val($("#recBank1").val());//收款银行
            }*/
            /*vali();*/
        });

    });

    /**
     * 不同选项，必填参数不同
     */
    function changeRequired() {
        var outAccount = $('#accountCode').val();//获取转出账户编码
        var payMethod =  $('#payMethod').find(":selected").val();//获取付款方式
        /**
         *  当转出账户与付款方式都为现金支付时 收款信息非必填 其余为必填
         */
        if(outAccount === '现金支付' && payMethod === '42'){
            $(".rec").attr("required", false);
        }else if(payMethod === '91') {
            $(".rec").attr("required", true);
      /*      $("#recBank").attr("required", false);*/
        }else{
            $(".rec").attr("required", true);
        }
    }

    /**
     * 校验
     */
    function vali() {
        $("#form-batchPay-add").validate().element($("#recAccount"));
        $("#form-batchPay-add").validate().element($("#recCardNo"));
        $("#form-batchPay-add").validate().element($("#recBank"));
    }
    /**
     * 油卡支付 回显油卡信息
     * @constructor
     */
    function OilPay() {
        //应付对账id
        var fuelCard = $("#fuelCard").val();
        var data = {fuelCard:fuelCard};
        var url = ctx + "payManage/selectFuelCard";
        $.ajax({
            url : url,
            method : 'POST',
            data : data,
            success:function (data) {
                $("#recAccount").val(data.fuelcardName);
                $("#recCardNo").val(data.fuelcardNo);

                vali();
            }
        })
    }

    //提交
    function submitHandler() {;
        //本次付款金额
        var payAmount = parseFloat($("#payAmount").val());
        if(payAmount>quota){
            $.modal.alertError("本次付款金额必须<="+quota);
            return ;
        }
        //转出账户
        var outAccount = $("#outAccount").val();
        if($.common.isEmpty(outAccount)){
            $.modal.alertError("请选择转出账户！");
            return ;
        }

        var payMethod =  $('#payMethod').find(":selected").val();//获取付款方式
        /**
         *  当转出账户与付款方式都为现金支付时 收款信息非必填 其余为必填
         */
        if(payMethod === '91'){
            $.modal.alertError("该页面不支持油卡支付");
            return ;
        }

        var dis = $(":disabled");//移除disabled
        dis.attr("disabled", false);

        //校验
        if ($.validate.form()) {
            //默认未锁定
            var lock = false;
            layer.confirm("确认支付？", {
                btn: ["确认", "取消"]
            }, function (index, layero) {
                if(!lock){
                    lock = true;
                    layer.close(index);
                    $.operate.saveModalAndRefush(prefix + "/saveBatchPayTogether", $('#form-batchPay-add').serialize(), function (result) {
                        if (result.code != 0) {
                            dis.attr("disabled", true);
                        }
                    });
                }
            }, function (index) {
                layer.close(index);
                dis.attr("disabled", true);
            });
        } else {
            dis.attr("disabled", true);
        }
    }

    /**
     * 关键字提示查询 转入账户
     */
    $("#accountName").bsSuggest('init', {
        url: ctx + "finance/account/findAccount?paymentType=1&balaCorp="+$("#balaCorp").val(),
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["accountName","accountCode","account"],
        effectiveFieldsAlias: {"accountName":"账户名称","accountCode":"账户信息","account":"账号"},
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#accountName").val(data.accountName);
        $("#outAccount").val(data.accountId);
        $("#accountCode").val(data.accountCode);
    })




</script>
</body>

</html>