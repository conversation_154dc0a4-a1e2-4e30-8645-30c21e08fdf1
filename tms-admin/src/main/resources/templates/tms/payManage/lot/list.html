<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('运单详情')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .taCl thead{
        background-color: #F7F8FA !important;
    }
    .table-responsive{
        width: 90vw;
        margin-left: 26px;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="payCheckSheetId" th:value="${payCheckSheetId}">
                <!-- <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="lotno" id="lotno" class="form-control"
                                       placeholder="请输入运单号" maxlength="30">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="status" id="status" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="应付单状态" multiple>
                                    <option th:each="dict : ${payDetailStatusEnum}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>


                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="carrName" id="carrName" placeholder="请输入承运商名称" class="form-control valid">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="driverName" id="driverName" class="form-control" placeholder="请输入司机名称"
                                       maxlength="30">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="regUserName" id="regUserName" class="form-control"
                                       placeholder="请输入调度人" maxlength="25">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="invoiceVbillno"  class="form-control"
                                       placeholder="请输入发货单号" maxlength="25">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="custAbbr"  class="form-control"
                                       placeholder="请输入客户简称" maxlength="25">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="salesDeptName" id="salesDeptName" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运营组" multiple>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input type="text" style="width: 47%; float: left;" class="form-control"
                                       id="reqDeliDateStart"  name="reqDeliDateStart" placeholder="要求提货日期开始">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:6%;">-</span>
                                <input type="text" style="width: 47%; float: left;" class="form-control"
                                       id="reqDeliDateEnd"  name="reqDeliDateEnd" placeholder="要求提货日期结束">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group" style="text-align: center">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div> -->

            </form>
        </div>
        

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var balatype = [[${@dict.getType('bala_type')}]];
    var prefix = ctx + "payManage";


    $(function () {
        var options = {
            url: ctx + "payManage/lot/list",
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            fixedColumns: false,
            rememberSelected: false,
            fixedNumber: 0,
            clickToSelect:true,
            showFooter:false,
            detailView: true,
            uniqueId: "entrustLotId",
            onExpandRow: function (index, row, $detail) {
                InitSubTable(index, row, $detail);
            },
            columns: [
                {
                    checkbox: true
                },
                {
                    title: '运单号',
                    align: 'left',
                    field: 'lot'
                },  
                {
                    title: '提货|到货地址',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let deli="",arri="";
                        deli=row.deliProvinceName+row.deliCityName+row.deliAreaName;
                        arri=row.arriProvinceName+row.arriCityName+row.arriAreaName;
                        
                        return deli+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+arri;
                    }
                },
                {
                    title: '承运商名称',
                    align: 'left',
                    field: 'carrierName'
                },
                {
                    title: '货量',
                    align: 'left',
                    field: 'goodsName',
                    formatter: function (value, row, index) {
                        let html=[]
                        if(row.numCount != null && row.numCount != ""&& row.numCount != 0){
                            html.push(row.numCount+"件")
                        }
                        if(row.weightCount != null && row.weightCount != ""&& row.weightCount != 0){
                            html.push(row.weightCount+"吨")
                        }
                        if(row.volumeCount != null && row.volumeCount != ""&& row.volumeCount != 0){
                            html.push(row.volumeCount+"方")
                        }
                        return row.goodsName+html.join("|");
                    }
                },
                {
                    title: '应收金额',
                    align: 'right',
                    field: 'receivableAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '应付金额',
                    align: 'right',
                    field: 'payAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '第三方金额',
                    align: 'right',
                    field: 'otherAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '毛利金额',
                    align: 'right',
                    field: 'volumeCount',
                    formatter: function (value, row, index) {
                        let num=Number(row.receivableAmount)-Number(row.payAmount)-Number(row.otherAmount);
                        if (num === null) {
                            return ;
                        }
                        return num.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '回单照片',
                    field: 'receiptUploadFiles',
                    align: 'left',
                     formatter: function(value, row, index) {
                        var html = "<div class='picviewer'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html += `<img style="height:32px" src="`+element.filePath+`"/>`
                            });
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                }
            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
    });

    function InitSubTable(index, row, $detail) {
        var childTable = $detail.html('<div class="table-responsive"><table id="child-table" class="table table-bordered table-striped taCl"></table></div>').find('table');
        $(childTable).bootstrapTable({
            url: ctx + "payManage/lot/payDetail/list",
            method: 'post',
            sidePagination: "server",
            contentType: "application/x-www-form-urlencoded",
            queryParams:{
                lotId:row.entrustLotId
            },
            columns: [
                {
                    title: '应付单号',
                    field: 'vbillno',
                    align: 'left'
                },
                {
                    title: '应付单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == 0){
                            return '<span class="label label-primary">新建</span>';
                        }else if(value == 1){
                            return '<span class="label label-warning">已确认</span>';
                        }else if(value == 2){
                            return '<span class="label label-success">已对账</span>';
                        }else if(value == 3){
                            return '<span class="label label-info">部分核销</span>';
                        }else if(value == 4){
                            return '<span class="label label-info">已核销</span>';
                        }else if(value == 5){
                            return '<span class="label label-default">关闭</span>';
                        }else if(value == 6){
                            return '<span class="label label-success">已申请</span>';
                        }else if(value == 7){
                            return '<span class="label label-danger">核销中</span>';
                        } else if (value==8) {
                            return '<span class="label label-info">审核中</span>';
                        } else if (value==9) {
                            return '<span class="label label-success">复核通过</span>';
                        }
                    }
                },
                {
                    title: '发货单号',
                    field: 'invoiceNo',
                    align: 'left'
                },
                {
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value == 0&&row.costTypeFreight!=null) {
                            return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                        }else if (value == 1&&row.costTypeOnWay!=null){
                            return $.table.selectDictLabel(costTypeOnWay, row.costTypeOnWay);
                        }else{
                            return "运费"
                        }
                    }
                },
                {
                    title: '总金额',
                    field: 'transFeeCount',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '已付金额',
                    field: 'gotAmount',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未付金额',
                    field: 'ungotAmount',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }
            ]
        });
    }

 

    

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        // data.status = $.common.join($('#status').selectpicker('val'));
        // data.salesDeptName = $.common.join($('#salesDeptName').selectpicker('val'));
        //data.costTypeFreight = $.common.join($('#costTypeFreight').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        // $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

</script>
</body>
</html>