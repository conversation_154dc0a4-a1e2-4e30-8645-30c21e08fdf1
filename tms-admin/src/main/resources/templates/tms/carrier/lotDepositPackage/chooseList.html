<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('非熟车定金对账包列表')"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>

</head>
<style>

</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-5 col-sm-5">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <input name="vbillno" id="vbillno" placeholder="对账单号" class="form-control" type="text" maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5 col-sm-5">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <input name="actualPayee" id="actualPayee" placeholder="实际收取人" class="form-control" type="text" maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <label class="col-sm-4"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="col-sm-12 select-table">
            <div>
<!--                <a class="btn btn-primary btn-sm" onclick="addGoods()">&nbsp;+ 新增货品</a>-->
            </div>

            <table id="table" class="table table-striped table-responsive table-bordered table-hover" >
            </table>

        </div>
    </div>

</div>
<div th:include="include :: footer"></div>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>

<script th:inline="javascript">

    $(function () {
        let options = initOptions();
        $.table.init(options);
    });

    //熟车
    function initOptions() {
        return {
            id: "table",
            // toolbar: "familiarCar-toolbar",
            formId: "form",
            url: `${ctx}lotDepositPackage/list?status=0`,
            showToggle:false,
            showColumns:false,
            showSearch:false,
            modalName: "非熟车定金对账包列表",
            uniqueId: "id",
            clickToSelect: true,
            columns:[
                {
                    radio: true,
                },
                {
                    title: '对账单号',
                    align: 'left',
                    field : 'vbillno',
                    formatter: function (value, row, index) {
                        return `${row.vbillno}`;
                    },
                },
                {
                    title: '对账单状态',
                    align: 'left',
                    field : 'status',
                    formatter: function (value, row, index) {
                        let recvStatus =''
                        if (row.status == 0) {
                            recvStatus = '<span class="label" style="margin-left: 2px;">新建</span>';
                        }else if (row.status == 1) {
                            recvStatus = '<span class="label label-success" style="margin-left: 2px;">申请</span>';
                        }else if (row.status == 2) {
                            recvStatus = '<span class="label label-primary" style="margin-left: 2px;">已收</span>';
                        }else if (row.status == 3) {
                            recvStatus = '<span class="label label-warning" style="margin-left: 2px;">未收</span>';
                        }

                        return recvStatus;
                    },
                },
                {
                    title: '实际收取人',
                    align: 'left',
                    field : 'actualPayeeId',
                    formatter: function (value, row, index) {
                        return row.actualPayee;
                    },
                },
                {
                    title: '金额',
                    align: 'left',
                    field : 'amount',
                    formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {
                            style: 'currency',
                            currency: 'CNY'
                        })
                    },
                },
            ]
        }

    }


    /** 获取选中行*/
    function getChecked() {
        return $.btTable.bootstrapTable('getSelections');
    }

</script>
</body>
</html>