<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('非熟车定金对账包列表')"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>

</head>
<style>
    .picviewer img{
        width: 32px;
        height: 32px;
        object-fit: scale-down;
    }

</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-3">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <input name="vbillno" id="vbillno" th:value="${packNo}" placeholder="对账单号" class="form-control" type="text" maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-3">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <input name="actualPayee" id="actualPayee" placeholder="实际收取人" class="form-control" type="text" maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-3">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <select name="status" id="status" class="form-control valid" aria-invalid="false">
                                    <option value="">-- 状态 --</option>
                                    <option value="0">新建</option>
                                    <option value="1">已申请</option>
                                    <option value="2">已收</option>
                                </select>


                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="col-sm-12 select-table">
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-primary single disabled" shiro:hasAnyPermissions="lotDeposit:package:apply"
                   onclick="apply()" >
                    <i class="fa fa-dollar"></i> 申请
                </a>
                <a class="btn btn-danger single disabled" shiro:hasAnyPermissions="lotDeposit:package:returnApply"
                   onclick="returnApply()" >
                    <i class="fa fa-dollar"></i> 退回申请
                </a>
                <a class="btn btn-danger multiple disabled" shiro:hasAnyPermissions="lotDeposit:package:rm"
                   onclick="rmPack()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-primary single disabled" shiro:hasAnyPermissions="lotDeposit:package:verified"
                   onclick="verified()" >
                    <i class="fa fa-dollar"></i> 核账
                </a>

            </div>

            <table id="table" class="table table-striped table-responsive table-bordered table-hover" >
            </table>

        </div>
    </div>

</div>


<script id="verifiedHtml" type="text/template">
    <div class="form-content">
        <form id="addForm" class="" novalidate="novalidate">
            <div class="">
                <div class="row mt10">
                    <div class="col-md-12 col-xs-12">
                        <div class="flex">
                            <label class="flex_left" style="color: #000000 !important">备注：</label>
                            <div class="flex_right">
                                <textarea name="writeOffMemo" id="writeOffMemo" maxlength="200" class="form-control valid"
                                          rows="3" placeholder="核帐备注"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row no-gutter mt10">
                    <div class="col-md-12 col-xs-12">
                        <div class="flex">
                            <label class="flex_left" style="color: #000000 !important">核帐凭证：</label>
                            <div class="flex_right">
                                <input id="writeOffDoc" type="file" name="writeOffDoc" multiple>
                                <input id="writeOffDocTid" type="hidden">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>
<div th:include="include :: footer"></div>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>
<th:block th:include="include :: bootstrap-fileinput553-css"/>
<th:block th:include="include :: bootstrap-fileinput553-js"/>

<th:block th:include="include :: bootstrap-fileinput-js"/>

<script th:inline="javascript">

    $(function () {
        let options = initOptions();
        $.table.init(options);
    });

    //
    function initOptions() {
        return {
            id: "table",
            toolbar: "toolbar",
            formId: "form",
            url: `${ctx}lotDepositPackage/list`,
            showToggle:false,
            showColumns:false,
            showSearch:false,
            modalName: "非熟车定金对账包列表",
            uniqueId: "id",
            clickToSelect: true,
            showFooter:true,
            onLoadSuccess: function (data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar: false,
                });
            },
            onCheck: function (row,$element) {
                updateFooter(this);
            },
            onUncheck: function (row, $element) {
                updateFooter(this);
            },
            onCheckAll: function (rowsAfter) {
                updateFooter(this);
            },
            onUncheckAll: function () {
                updateFooter(this);
            },

            columns:[
                {
                    checkbox: true,
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 20,
                    switchable:false,
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push(`<a class="btn btn-xs" href="javascript:void(0)" title="发货单明细"
                                            onclick="openDtl('${row.id}')"><i class="fa fa-list" style="font-size: 15px;"></i></a>`);

                        return actions.join('');
                    }
                },
                {
                    title: '对账单号',
                    align: 'left',
                    field : 'vbillno',
                    formatter: function (value, row, index) {
                        return `${row.vbillno}`;
                    },
                },
                {
                    title: '对账单状态',
                    align: 'left',
                    field : 'status',
                    formatter: function (value, row, index) {
                        let recvStatus =''
                        if (row.status == 0) {
                            recvStatus = '<span class="label" style="margin-left: 2px;">新建</span>';
                        }else if (row.status == 1) {
                            recvStatus = '<span class="label label-success" style="margin-left: 2px;">已申请</span>';
                        }else if (row.status == 2) {
                            recvStatus = '<span class="label label-primary" style="margin-left: 2px;">已收</span>';
                        }

                        return recvStatus;
                    },
                },
                {
                    title: '实际收取人',
                    align: 'left',
                    field : 'actualPayeeId',
                    formatter: function (value, row, index) {
                        return row.actualPayee;
                    },
                },
                {
                    title: '金额',
                    align: 'left',
                    field : 'amount',
                    formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {
                            style: 'currency',
                            currency: 'CNY'
                        })
                    },
                    footerFormatter:function (){
                        return '<div id="heji">¥0.00</div>'
                    }

                },
                {
                    title: '创建人',
                    align: 'left',
                    field : 'regUserName',
                    formatter: function (value, row, index) {
                        return row.regUserName + '<br/>' + row.regDate;
                    },
                },

                {
                    title: '核帐人',
                    align: 'left',
                    field : 'writeOffUserName',
                    formatter: function (value, row, index) {
                        let writeOffUserName = '-'
                        if (value) {
                            writeOffUserName = row.writeOffUserName
                        }

                        let writeOffDate = '-'
                        if (value) {
                            writeOffDate = row.writeOffDate
                        }

                        return `${writeOffUserName}<br/>${writeOffDate}`;
                    },
                },
                // {
                //     title: '核帐时间',
                //     align: 'left',
                //     field : 'writeOffDate',
                //     formatter: function (value, row, index) {
                //         let writeOffDate = '-'
                //         if (value) {
                //             writeOffDate = row.writeOffDate
                //         }
                //
                //         return `${writeOffDate}`;
                //     },
                // },
                {
                    title: '核帐备注',
                    align: 'left',
                    field : 'writeOffMemo',
                    formatter: function (value, row, index) {
                        let writeOffMemo = '-'
                        if (value) {
                            writeOffMemo = row.writeOffMemo
                        }

                        return `${writeOffMemo}`;
                    },
                },
                {
                    title: '核帐凭证',
                    align: 'left',
                    field : 'writeOffDocList',
                    formatter: function (value, row, index) {
                        const maxImages = 3;
                        let count = 0;

                        var html = "<div class='picviewer' style='display: flex;align-items: center;'>"
                        if(value != null && value != '') {
                            html += `<div>`

                            value.forEach(function (element, index) {
                                if (count < maxImages) {
                                    html += `<img style="display: inline-block;margin-right: 10px;" src="`+element.filePath+`"/>`
                                }
                                count++;
                            });

                            html += `</div>`

                            if (count > maxImages) {
                                let filePaths = value.map(item => item.filePath);
                                // filePaths = filePaths.slice(maxImages);
                                let pathsString = encodeURIComponent(JSON.stringify(filePaths));

                                let more = value.length - maxImages
                                // 如果超过最大图片数量，拼接“更多”按钮
                                html += `<div style="flex-shrink: 0;"><a onclick="openReceiptUploadFiles('${pathsString}')">更多(${more}张)</a></div>`;
                            }
                        }else {
                            html = '-';
                        }
                        html +="</div>"

                        return html;
                    },
                },
            ]
        }
    }

    function updateFooter($table) {
        var total = 0;
        var selectedRows = $.btTable.bootstrapTable('getSelections');
        selectedRows.forEach(function(row) {
            total += row.amount;
        });

        $("#heji").text(total.toLocaleString('zh', {
            style: 'currency',
            currency: 'CNY'
        }))
    }

    function apply() {
        let idArr = $.table.selectColumns("id");
        if (idArr.length == 0) {
            $.modal.msgWarning("请至少选择一条数据");
            return;
        }

        let bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i].status != 0) {
                $.modal.alertWarning("请选择“新建”状态下的数据。");
                return;
            }
        }

        layer.confirm('确定申请吗?', function (index) {
            var data = {}
            data['ids'] = idArr.join(',')

            $.ajax({
                url: ctx + "lotDepositPackage/apply",
                type: "post",
                dataType: "json",
                // contentType: "application/json; charset=utf-8",
                data: data,
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                    }else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();

                    $.table.refresh()
                }
            });

        });

    }

    function returnApply() {
        let idArr = $.table.selectColumns("id");
        if (idArr.length == 0) {
            $.modal.msgWarning("请至少选择一条数据");
            return;
        }

        let bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i].status != 1) {
                $.modal.alertWarning("请选择“已申请”状态下的数据。");
                return;
            }
        }

        layer.confirm('确定退回申请吗?', function (index) {
            var data = {}
            data['ids'] = idArr.join(',')

            $.ajax({
                url: ctx + "lotDepositPackage/returnApply",
                type: "post",
                dataType: "json",
                // contentType: "application/json; charset=utf-8",
                data: data,
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                    }else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();

                    $.table.refresh()
                }
            });

        });

    }

    function rmPack() {
        let idArr = $.table.selectColumns("id");
        if (idArr.length == 0) {
            $.modal.msgWarning("请至少选择一条数据");
            return;
        }

        let bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i].status != 0 || bootstrapTable[i].amount != 0 ) {
                $.modal.alertWarning("请选择“新建”状态下并且金额为零的数据。");
                return;
            }
        }

        layer.confirm('确定删除吗?', function (index) {
            var data = {}
            data['ids'] = idArr.join(',')

            $.ajax({
                url: ctx + "lotDepositPackage/rm",
                type: "post",
                dataType: "json",
                // contentType: "application/json; charset=utf-8",
                data: data,
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                    }else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();

                    $.table.refresh()
                }
            });

        });

    }

    function verified() {
        let idArr = $.table.selectColumns("id");
        if (idArr.length !== 1) {
            $.modal.msgWarning("请选择一条数据");
            return;
        }

        let bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i].status != 1 ) {
                $.modal.alertWarning("请选择“已申请”状态下并且金额为零的数据。");
                return;
            }
        }


        layer.open({
            type: 1,
            title: '核账',
            area: ['60%', '60%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: $('#verifiedHtml').html(),
            btn: ['保存','关闭'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {

                var control = $('#writeOffDoc');

                let fileQueue = []; // 用于存储待上传的文件队列
                let fileIds = {}; // 用于存储文件ID
                let isUploading = false;

                function uploadNextFile() {
                    if (fileQueue.length > 0 && !isUploading) {
                        isUploading = true;
                        var file = fileQueue.shift();
                        control.fileinput('upload', {
                            data: {file: file}
                        });
                    }
                }
                control.fileinput({
                    theme: "explorer",
                    language: 'zh',
                    uploadUrl: ctx + "common/uploadBatch",
                    uploadExtraData: function() {
                        return {
                            key: "writeOffDoc",
                            tid: $("#writeOffDocTid").val()
                        };
                    },
                    deleteExtraData: {id: $(this).attr('data-fileid')},
                    enctype: 'multipart/form-data',
                    allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"],
                    initialPreviewAsData: true,
                    overwriteInitial: false,
                    maxFileCount: 0,
                    showUpload: false,
                    minFileSize: 5,
                    previewFileIcon: '<i class="fa fa-file"></i>',
                    allowedPreviewTypes: ['image'],
                    showClose: false,
                    showRemove: false,
                    showCaption: false,
                    uploadAsync: false,
                    fileActionSettings: {
                        showUpload: false,
                        showDrag: false,
                    },
                });

                control.on('filebatchselected', function(event, files) {
                    fileQueue.push(files);
                    uploadNextFile();
                });

                control.on('filebatchuploaderror', function(event, data, msg){
                    $.modal.closeLoading();
                    $.modal.alertError("上传失败，错误信息：" + msg + " --> " + data.jqXHR.responseText + " --> " + data.jqXHR.status);
                });

                control.on('filebatchuploadsuccess ', function (event, data, previewId, index) {
                    var response = data.response;
                    if (response.code !== 0) {
                        $.modal.closeLoading();
                        $.modal.alertError("上传失败：" + response.msg);
                        isUploading = false;
                        uploadNextFile(); // 尝试上传下一个文件
                        return;
                    }
                    var tid = response.tid;
                    $("#writeOffDocTid").val(tid);

                    if (response.file && response.file.length > 0) {
                        response.file.forEach(function(serverFile, index) {
                            var fileId = serverFile.fileId;
                            var fileName = serverFile.fileName;

                            // 查找匹配的文件预览框
                            var $frame = $('.file-preview-frame').filter(function() {
                                return $(this).find('.kv-file-content img, .kv-file-content .file-preview-other').attr('title') === fileName;
                            });

                            if ($frame.length > 0) {
                                var previewId = $frame.attr('id');
                                fileIds[previewId] = fileId;
                                $frame.attr('data-fileid', fileId);
                                console.log("文件匹配成功:", fileName, "PreviewId:", previewId, "FileId:", fileId);
                            } else {
                                console.log("未找到匹配的预览框:", fileName);
                            }
                        });
                    }


                    // 上传下一个文件
                    isUploading = false;
                    uploadNextFile();


                });

                control.on('fileuploaderror', function(event, data, msg) {
                    $.modal.closeLoading();
                    $.modal.alertError("上传失败，错误信息：" + msg);
                    isUploading = false;
                    uploadNextFile(); // 尝试上传下一个文件
                });

                $(document).on('click', '.kv-file-remove', function(e) {
                    e.preventDefault();
                    var $frame = $(this).closest('.file-preview-frame');
                    var previewId = $frame.attr('id');


                    let attr = $("#" + previewId).attr('data-fileid');
                    var fileId = $frame.attr('data-fileid');

                    // var fileId = fileIds[previewId];

                    if (fileId) {
                        // 发送删除请求
                        $.ajax({
                            url: ctx + "common/delImg",
                            type: 'POST',
                            data: { id: fileId },
                            success: function(response) {
                                if (response.code === 0) {
                                    // 删除成功，移除预览框
                                    $frame.remove();
                                    delete fileIds[previewId];
                                } else {
                                    $.modal.alertError("删除失败：" + response.msg);
                                }
                            },
                            error: function() {
                                $.modal.alertError("删除请求失败");
                            }
                        });
                    }
                });

            },
            yes: function (index, layero) {

                let writeOffMemo = $("#writeOffMemo").val();
                let writeOffDoc = $("#writeOffDocTid").val();

                if (writeOffDoc === '') {
                    $.modal.msgWarning("请上传核帐凭证。");
                    return;

                }

                let data = {};
                data["id"] = idArr.join("")
                data["writeOffMemo"] = writeOffMemo
                data["writeOffDoc"] = writeOffDoc


                $.ajax({
                    url: ctx + "lotDepositPackage/verified",
                    type: "post",
                    dataType: "json",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(data),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function (result) {
                        if (result.code == 0) {
                            $.modal.msgSuccess(result.msg);
                        } else {
                            $.modal.alertError(result.msg);
                        }
                        $.modal.closeLoading();
                        $.modal.enable();
                        $.table.refresh()

                        layer.close(index);
                    }
                });
            },
            cancel: function (index) {
                return true;
            }
        });

    }

    /** 打开对账包详情*/
    function openDtl(packageId) {
        layer.open({
            type: 2,
            title: '定金对账单列表',
            closeBtn: 1,
            area: ['95%', '95%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: ctx + `lotDepositPackage/dtl/page?packageId=${packageId}`,
            btn: ['关闭'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {

            },
            btn1: function(index, layero, that){
                $.table.refresh()
                layer.close(index);
            },
            cancel: function(index, layero, that){
                $.table.refresh()
            }
        })
    }

    function openReceiptUploadFiles(filePaths) {
        let decodedPaths = JSON.parse(decodeURIComponent(filePaths));

        // 创建一个隐藏的图片容器，用于预览所有图片
        let imageContainer = $('<div style="display: none;"></div>');

        // 将所有图片添加到容器中
        decodedPaths.forEach(path => {
            let img = new Image();
            img.src = path;

            // 将图片添加到隐藏的容器中
            imageContainer.append(img);
        });

        // 使用 viewer-jquery 预览所有图片
        imageContainer.viewer({
            // 可以根据需要设置更多的配置选项
            // 例如：inline: false（默认为 true，表示以内联方式显示预览）
            inline: false,
            viewed() {
                // 设置预览初始缩放级别等
                // 这里可以添加自定义的预览逻辑
            }
        });
        $('body').append(imageContainer);

        // 触发预览，可以通过模拟点击的方式或其他方式来打开预览
        let firstImage = imageContainer.find('img').eq(0);
        if (firstImage.length > 0) {
            firstImage.click(); // 模拟点击第一张图片以触发 viewer-jquery 预览
        } else {
            console.error('No images found in the list.');
        }
    }
</script>
</body>
</html>