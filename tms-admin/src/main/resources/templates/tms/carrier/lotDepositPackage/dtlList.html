<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('非熟车定金对账包明细列表')"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>

</head>
<style>
 /*   #unfamiliarCar-table {
        table-layout: fixed;
    }

    #unfamiliarCar-table td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }*/
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="unfamiliarCar-form" class="form-horizontal">
                <div class="row no-gutter" >
<!--                    <div class="col-md-2 col-sm-2">-->
<!--                        <div class="form-group flex">-->
<!--                            <div class="col-sm-12">-->
<!--                                <input id="undateLimit" type="text" class=" form-control"-->
<!--                                       placeholder="起始时间 - 结束时间" autocomplete="off">-->
<!--                                <input id="unstartDate" name="startDate" value="" type="hidden">-->
<!--                                <input id="unendDate" name="endDate" value="" type="hidden">-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <input name="carrName" id="carrName" placeholder="承运商" class="form-control" type="text" maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group" style="text-align: left;">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="col-sm-12 select-table">
            <div class="btn-group-sm" id="unfamiliarCar-toolbar" role="group">
                <a class="btn btn-danger multiple disabled" shiro:hasAnyPermissions="lotDeposit:package:dtlRm" onclick="rmPackDtl()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>

            <table id="unfamiliarCar-table"
                   class="table table-striped table-responsive table-bordered table-hover" >
            </table>
        </div>
    </div>
</div>

<div th:include="include :: footer"></div>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>

<script th:inline="javascript">
    const packageId = [[${packageId}]];
    const payDetailStatusEnum = [[${payDetailStatusEnum}]];
    const depositWay = [[${@dict.getType('deposit_way')}]];

    $(function () {
        let options = initUnfamiliarCarOptions()
        $.table.init(options);
    });

    function initUnfamiliarCarOptions() {
        return {
            id: "unfamiliarCar-table",
            toolbar: "unfamiliarCar-toolbar",
            formId: "unfamiliarCar-form",
            url: ctx + `lotDepositPackage/dtl/list?packageId=${packageId}`,
            showToggle:false,
            showColumns:false,
            showSearch:false,
            modalName: "对账单明细",
            uniqueId: "id",
            clickToSelect: true,
            showFooter:true,
            onCheck: function (row,$element) {
                updateFooter(this);
            },
            onUncheck: function (row, $element) {
                updateFooter(this);
            },
            onCheckAll: function (rowsAfter) {
                updateFooter(this);
            },
            onUncheckAll: function () {
                updateFooter(this);
            },
            columns:[
                {
                    checkbox: true,
                    footerFormatter:function (){
                        return '合'
                    }
                },
                // {
                //     title: '操作',
                //     align: 'center',
                //     width: 20,
                //     switchable:false,
                //     formatter: function(value, row, index) {
                //         var actions = [];
                //         return actions.join('');
                //     }
                // },
                {
                    title: '承运商',
                    align: 'left',
                    field : 'carrName',
                    width: 150,
                    formatter: function (value, row, index) {
                        return `${row.carrName}<br/>${row.phone}`;
                    }
                },

                {
                    title: '金额',
                    align: 'left',
                    field : 'amount',
                    width: 70,
                    formatter: function (value, row, index) {
                        if (value) {
                            return value.toLocaleString('zh', {
                                style: 'currency',
                                currency: 'CNY'
                            });
                        }else {
                            return '-'
                        }
                    },
                    footerFormatter:function (){
                        return '<div id="heji">¥0.00</div>'
                    }
                },
                {
                    title: '实际收取人',
                    align: 'left',
                    field : 'actualPayee',
                    width: 150,
                    formatter: function (value, row, index) {
                        return value
                    }
                },
                {
                    title: '类型',
                    align: 'left',
                    field : 'type',
                    width: 150,
                    formatter: function (value, row, index) {
                        return row.type === 0 ? '熟车':'非熟车'
                    }
                },
                {
                    title: '运单号',
                    align: 'left',
                    field : 'lotno',
                    width: 150,
                    formatter: function (value, row, index) {
                        if (value) {
                            let lotDelFlag = row.lotDelFlag === 1 ? `<span class="label label-danger pa2">已删除</span>` : '';
                            return `${value}<br/>${lotDelFlag}`

                        }else {
                            return '-'
                        }
                    }
                },
            ]
        }
    }

    function rmPackDtl() {
        let idArr = $.table.selectColumns("id");
        if (idArr.length == 0) {
            $.modal.msgWarning("请至少选择一条数据");
            return;
        }

        layer.confirm('确定删除该条数据吗?', function (index) {
            var data = {}
            data['ids'] = idArr.join(',')

            $.ajax({
                url: ctx + "lotDepositPackage/dtl/rm",
                type: "post",
                dataType: "json",
                // contentType: "application/json; charset=utf-8",
                data: data,
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                    }else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();

                    $.table.refresh()
                }
            });

        });
    }

    function updateFooter($table) {
        var total = 0;
        var selectedRows = $.btTable.bootstrapTable('getSelections');
        selectedRows.forEach(function(row) {
            total += row.amount;
        });

        $("#heji").text(total.toLocaleString('zh', {
            style: 'currency',
            currency: 'CNY'
        }))
    }

    function searchPre() {
        var data = {};
        $.table.search(data);
    }

    function resetPre(type) {
        $("#unfamiliarCar-form")[0].reset();
        $("#unstartDate").val("")
        $("#unendDate").val("")

        searchPre();
    }




    layui.use('laydate', function () {
        var laydate = layui.laydate;
        //要求提货日期
        let myDate = laydate.render({
            elem: "#undateLimit",
            id: "undateLimit",
            range: true,
            rangeLinked: true,
            done: function(value, date, endDate){
                if (value !== '') {
                    let sDate = date.year + '-' + date.month + '-' + date.date;
                    let eDate = endDate.year + '-' + endDate.month + '-' + endDate.date;

                    $("#unstartDate").val(sDate)
                    $("#unendDate").val(eDate)

                }else {
                    $("#unstartDate").val("")
                    $("#unendDate").val("")

                }
            }
        });
    });


</script>
</body>
</html>