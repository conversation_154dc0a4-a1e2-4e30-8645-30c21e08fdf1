<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商入口-账单信息')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>

<script th:inline="javascript">

    var prefix = ctx + "carrier/bill";
    var payCheckSheetId = [[${payCheckSheetId}]];

        $(function () {
        var options = {
            url: prefix + "/billList?payCheckSheetId="+payCheckSheetId,
            detailUrl: prefix + "/detail",
            showToggle: false,
            modalName: "账单信息",
            showColumns:true,
            clickToSelect:true,
            fixedColumns: true,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    width: '250px',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="委托单列表" onclick="entrustList(\'' + row.payDetailId + '\')"><i  class="fa fa-newspaper-o" style="font-size: 15px;" ></i></a>');
                        return actions.join('');
                    }
                },
                {
                    title: '应付账单号',
                    width: '250px',
                    field: 'vbillno',
                    align: 'left'
                },
                {
                    title: '金额',
                    width: '250px',
                    field: 'transFeeCount',
                    align: 'right',
                    halign: 'center',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '日期',
                    width: '250px',
                    field: 'regDate',
                    align: 'left'
                }
            ]
        };

        $.table.init(options);
    });


    function entrustList(payDetailId) {
        var url = prefix + "/entrustList/"+payDetailId;
        $.modal.openTab("委托单列表", url);
    }

</script>

</body>
</html>