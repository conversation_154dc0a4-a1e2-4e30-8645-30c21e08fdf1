<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商入口-账单信息')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>

<script th:inline="javascript">

    var prefix = ctx + "carrier/bill";
    var lotId = [[${lotId}]];//运单id

        $(function () {
        var options = {
            url: prefix + "/selectPayDetailList/"+lotId,
            detailUrl: prefix + "/detail",
            showToggle: false,
            modalName: "账单信息",
            showColumns:true,
            fixedColumns: true,
            columns: [{
                checkbox: true
            },
                {
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left'
                },
                {
                    title: '付款类型',
                    field: 'costTypeOnWay',
                    align: 'left'
                },
                {
                    title: '总金额',
                    width: '250px',
                    field: 'transFeeCount',
                    align: 'right',
                    halign: 'center',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未付金额',
                    width: '250px',
                    field: 'ungotAmount',
                    align: 'right',
                    halign: 'center',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }
            ]
        };

        $.table.init(options);
    });


</script>

</body>
</html>