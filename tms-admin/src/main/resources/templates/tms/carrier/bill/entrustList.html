<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商入口-委托单列表')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>

<script th:inline="javascript">

    var prefix = ctx + "carrier/bill";

    var carType = [[${@dict.getType('car_type')}]];//车辆类型
    var carLen = [[${@dict.getType('car_len')}]];//车长
    var payDetailId = [[${payDetailId}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        var options = {
            url: prefix + "/entrustList?payDetailId="+payDetailId,
            showToggle: false,
            modalName: "委托单信息",
            showColumns:true,
            fixedColumns: true,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="委托单详情" onclick="entrust(\'' + row.entrustId + '\')"><i  class="fa fa-newspaper-o" style="font-size: 15px;" ></i></a>');
                        return actions.join('');
                    }
                },
                {
                    title: '委托单号',
                    field: 'vbillno',
                    align: 'left'
                },
                {
                    title: '提货地址',
                    field: 'deliDetailAddr',
                    align: 'left'
                },
                {
                    title: '到货地址',
                    field: 'arriDetailAddr',
                    align: 'left'
                },
                {
                    title: '要求提货时间',
                    field: 'reqDeliDate',
                    align: 'left'
                },
                {
                    title: '要求到货时间',
                    field: 'reqArriDate',
                    align: 'left'
                },
                {
                    title: '车长(米)',
                    field: 'carLenId',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(carLen, value);
                    }
                },
                {
                    title: '车型',
                    field: 'carTypeCode',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(carType, value);
                    }
                }
            ]
        };

        $.table.init(options);
    });

    /**
     * 委托单详情
     */
    function entrust(entrustId) {
        var url = prefix + "/entrustDetail/"+entrustId;
        $.modal.openTab("委托单详情", url);
    }

</script>

</body>
</html>