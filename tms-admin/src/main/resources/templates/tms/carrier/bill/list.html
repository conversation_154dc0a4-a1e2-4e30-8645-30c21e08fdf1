<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商入口-账单信息')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2">创建时间：</label>
                            <div class="col-sm-7">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  placeholder="开始时间" name="reqDeliDate">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" placeholder="结束时间" class="form-control"
                                       id="endDate"  name="reqArriDate">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
            </form>
        </div>
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>

<script th:inline="javascript">

    var prefix = ctx + "carrier/bill";
    var editFlag = [[${@permission.hasPermi('carrier:payDetail:view')}]];

        $(function () {
        var options = {
            url: prefix + "/billList",
            detailUrl: prefix + "/detail",
            showToggle: false,
            modalName: "账单",
            showColumns:true,
            fixedColumns: true,
            clickToSelect:true,
            height: 560,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    width: '250px',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs '+ editFlag +'" href="javascript:void(0)"  title="应收列表" onclick="payDetailList(\'' + row.lotId + '\')"><i  class="fa fa-exchange" style="font-size: 15px;" ></i></a>');
                        return actions.join('');
                    }
                },
                {
                    title: '要求提货时间',
                    field: 'reqDeliDate',
                    align: 'left'
                },
                {
                    title: '要求到货时间',
                    field: 'reqArriDate',
                    align: 'left',
                },
                {
                    title: '提货详细地址',
                    field: 'deliAddr',
                    align: 'left'
                },
                {
                    title: '到货详细地址',
                    field: 'arriAddr',
                    align: 'left'
                },
                {
                    title: '总金额',
                    field: 'totalAmount',
                    align: 'right',
                    halign: 'center',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未付',
                    field: 'ungotAmount',
                    align: 'right',
                    halign: 'center',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '创建时间',
                    field: 'regDate',
                    align: 'left',
                }
            ]
        };

        $.table.init(options);
    });

    /**
     * 初始化日期控件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#startDate',
            type: 'date',
            trigger: 'click'
        });
    });
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#endDate',
            type: 'date',
            trigger: 'click'
        });
    });

    function payDetailList(lotId) {
        var url = prefix + "/payDetailList/"+lotId;
        $.modal.openTab("应收列表", url);
    }

</script>

</body>
</html>