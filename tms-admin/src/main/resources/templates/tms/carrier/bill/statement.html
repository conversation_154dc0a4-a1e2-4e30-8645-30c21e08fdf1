<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商入口-对账单信息')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">年份：</label>
                            <div class="col-sm-8">
                                <input name="year" id="year"  placeholder="请输入年份" class="form-control valid" type="text"
                                       aria-required="true" autocomplete="off">

                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">月份：</label>
                            <div class="col-sm-8">
                                <input name="month" id="month" placeholder="请输入月份" class="form-control valid" type="text"
                                       aria-required="true"  autocomplete="off">

                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">

                        </div>
                    </div>

                    <div class=" col-md-3 col-sm-6">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>

<script th:inline="javascript">

    var prefix = ctx + "carrier/statement";


    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        var options = {
            url: prefix + "/list",
            detailUrl: prefix + "/detail",
            showToggle:false,
            modalName: "对账单信息",
            fixedColumns: true,
            fixedNumber:0,
            clickToSelect:true,
            uniqueId:'payCheckSheetId',
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    width: '100px',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="月账单" onclick="monthBill(\'' + row.payCheckSheetId + '\')"><i  class="fa fa-exchange" style="font-size: 15px;" ></i></a>');
                        return actions.join('');
                    }
                },
                /*{
                    title: '应付对账单号',
                    field: 'vbillno',
                    align: 'left'
                },*/

                {
                    title: '对账年份',
                    field: 'year',
                    align: 'left'
                },
                {
                    title: '对账月份',
                    field: 'month',
                    align: 'left'
                },
                {
                    title: '应付对账单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value) {
                            case 0:
                                return '<span>新建</label>';
                            case 1:
                                return '<span>已确认</label>';
                            case 2:
                                return '<span>部分核销</label>';
                            case 3:
                                return '<span>已核销</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '总金额',
                    field: 'totalAmount',
                    align: 'right',
                    halign:'center',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                }
                // {
                //
                //     title: '已付金额',
                //     field: 'gotAmount',
                //     align: 'right',
                //     halign:'center',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                //
                // },
                // {
                //     title: '未付金额',
                //     field: 'ungotAmount',
                //     align: 'right',
                //     halign:'center',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                //
                // },
                // {
                //     title: '已申请金额',
                //     field: 'applicationAmount',
                //     align: 'right',
                //     halign:'center',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                //
                // },
                //
                // {
                //     title: '是否手动核销',
                //     field: 'handVerification',
                //     align: 'left',
                //     formatter: function status(value,row,index) {
                //
                //         switch(value) {
                //             case '0':
                //                 return '<span>非手动核销</label>';
                //             case '1':
                //                 return '<span>手动核销</label>';
                //             default:
                //                 break;
                //         }
                //     }
                // }
            ]
        };

        $.table.init(options);
    });

    $("#distpicker1").distpicker();



    function detail() {
        var url = prefix + "/detail";
        $.modal.openTab($.table._option.modalName + "详细", url);
    }

    layui.use('laydate', function(){
        var laydate = layui.laydate;
        laydate.render({
            elem: '#month',
            type: 'month',
            format:"MM"
        });
        laydate.render({
            elem: '#year',
            type: 'year'
        });
    });

    /**
     * 月账单列表
     * @param id
     */
    function monthBill(payCheckSheetId) {
        var url = ctx + "carrier/statement/monthBill/"+payCheckSheetId;
        $.modal.openTab("月账单列表",url);
    }

</script>

</body>
</html>