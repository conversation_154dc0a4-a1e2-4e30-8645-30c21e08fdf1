<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商入口-账单信息')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>

<script th:inline="javascript">

    var prefix = ctx + "carrier/statement";
    var payCheckSheetId = [[${payCheckSheetId}]];//运单id

    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];

        $(function () {
        var options = {
            url: prefix + "/monthBillList/"+payCheckSheetId,
            detailUrl: prefix + "/detail",
            showToggle: false,
            modalName: "账单信息",
            showColumns:true,
            fixedColumns: true,
            columns: [{
                checkbox: true
            },
                {
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '<span>运费</label>';
                            case 1:
                                return '<span>在途费用</label>';
                            case 2:
                                return '<span>调整费用</label>';
                            default:
                                break;
                        }
                    }

                },
                {
                    title: '付款类型',
                    field: 'costTypeOnWay',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if (row.freeType === '0'){
                            return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                        }else if (row.freeType === '2')  {
                            return '调整费'
                        }
                        return $.table.selectDictLabel(costTypeOnWay, value);
                    }

                },

                {
                    title: '总金额',
                    width: '250px',
                    field: 'transFeeCount',
                    align: 'right',
                    halign: 'center',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未付金额',
                    width: '250px',
                    field: 'ungotAmount',
                    align: 'right',
                    halign: 'center',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }
            ]
        };

        $.table.init(options);
    });


</script>

</body>
</html>