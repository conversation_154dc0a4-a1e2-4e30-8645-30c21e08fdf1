<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('接单列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .fl{
        float: left;
    }
    .status1{
        width: 80px;
        background: #fff;
        border: 1px #eee solid;
        text-align: center;
        cursor: pointer;
        line-height: 20px;
        vertical-align: middle;
    }
    .act{
        background: #1ab492;
        color: #fff;
        border: 1px #1ab492 solid;
    }
    .lh26{
        line-height: 26px;
    }
    .fw{
        font-weight: 600;
    }
    .f16{
        font-size: 16px;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #f3f3f4;
        border: 0;
        box-shadow: none;
        padding:5px 0 0;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <div class="form-group" style="margin: 0 -10px 0;">
                <form id="role-form" class="form-horizontal">
                    <div class="row">
                        <div class="col-md-12 col-sm-12">
                            <div class="fl over">
                                <div class="fl status1 act" flag onclick="tabto(this,1)">进行中</div>
                                <div th:class="${vbillstatus != 2 ? 'fl status1':'fl status1 act'}" flag onclick="tabto(this,2)">已成功</div>
                                <div th:class="${vbillstatus != 3 ? 'fl status1':'fl status1 act'}" flag onclick="tabto(this,3)">已失败</div>
                                <div th:class="${vbillstatus != 4 ? 'fl status1':'fl status1 act'}" flag onclick="tabto(this,4)">关闭</div>
                            </div>
                            <input type="hidden" name="vbillstatus" id="vbillstatus" th:value="1">
                        </div>
                    </div>

                    <div class="row mt5">
                        <div class="col-md-3 col-sm-4">
                            <input type="text" class="form-control" placeholder="发货单号" name="vbillno">
                        </div>
                        <div class="col-md-3 col-sm-4">
                            <input type="text" class="time-input form-control" placeholder="请选择要求提货日期" name="reqDeliDate" readonly>
                        </div>
                        <div class="col-md-3 col-sm-4">
                            <select name="carLen" class="form-control selectpicker" 
                                aria-invalid="false" data-none-selected-text="车长" multiple th:with="type=${@dict.getType('car_len')}">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                        <div class="col-md-3 col-sm-4">
                            <select name="carType" class="form-control selectpicker" 
                                aria-invalid="false" data-none-selected-text="车型" multiple th:with="type=${@dict.getType('car_type')}">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>

                    <div class="row mt5">
                        <div class="col-md-4 col-sm-12">
                            <div class="form-group">
                                <div class="col-md-4 col-sm-4">
                                    <select name="deliProvinceId" id="provinceId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select name="deliCityId" id="cityId" class="form-control valid"
                                        aria-invalid="false"></select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select name="deliAreaId" id="areaId" class="form-control valid"
                                        aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-1" style="text-align: center;">
                            <i class="fa fa-arrow-circle-right" style="font-size:20px;color: #1ab394;line-height: 26px;"></i>
                        </div>
                        <div class="col-md-4 col-sm-12">
                            <div class="form-group">
                                <div class="col-md-4 col-sm-4">
                                    <select name="arriProvinceId" id="provinceId1" class="form-control valid"
                                            aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select name="arriCityId" id="cityId1" class="form-control valid"
                                        aria-invalid="false"></select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select name="arriAreaId" id="areaId1" class="form-control valid"
                                        aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-12">
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                        class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                        class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>


                </form>
            </div>
        </div>


        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">

    var prefix = ctx + "carrier/lotOut";
    //外发状态
    var out_vbillstatus = [[${@dict.getType('out_vbillstatus')}]];
    //外发类型
    var out_type = [[${@dict.getType('out_type')}]];
    //登录人承运商id
    var loginCarrId = [[${loginCarrId}]];

    var findCarLength = [[${@dict.getType('car_len')}]];
    //权限
    var detailFlag = [[${@permission.hasPermi('carrier:lotOut:detail')}]];
    var grabOrderFlag = [[${@permission.hasPermi('carrier:lotOut:grabOrder')}]];
    var quoteFlag = [[${@permission.hasPermi('carrier:lotOut:quote')}]];


    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            showToggle: false,
            showColumns: true,
            modalName: "询价",
            fixedColumns: true,
            clickToSelect:true,
            height: 560,
            fixedNumber: 4,
            columns: [{
                checkbox: true 
            },
                {
                    field: 'lotOutId',
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        let data=JSON.stringify({
                            lotOutId:row.lotOutId,
                            cutoffDate:row.cutoffDate
                        }).replace(/"/g, '&quot;');

                        if(loginCarrId == null){
                            actions.push('<a class="btn btn-xs ' + detailFlag + ' " href="javascript:void(0)" title="明细"  onclick="detail(\'' + row.lotOutId + '\')">明细</a>');
                        }
                        //判断登录人是否为承运商
                        if (loginCarrId != null) {
                            //判断外发报价单是否已报价
                            if (row.showButton == 1) {
                                actions.push('<a class="btn btn-xs  ' + quoteFlag + '" href="javascript:void(0)" title="报价" onclick="quote(\''+data + '\')">报价</a>');
                            } else if (row.showButton == 2) {
                                actions.push('<a class="btn btn-xs ' + grabOrderFlag + ' " href="javascript:void(0)" title="抢单" onclick="grabOrder(\'' + row.lotOutId + ',' + row.cutoffDate + ',' + row.vbillstatus + '\')">抢单</a>');
                            }
                            //判断是否有订金（1代表有订金）  2 代表支付成功
                            if (row.enquiryDtl.isDeposit == "1" && row.vbillstatus != '2') {
                                actions.push('<a class="btn btn-xs ' + detailFlag + ' " href="javascript:void(0)" title="缴纳订金"  onclick="deposit(\'' + row.enquiryDtl.lotOutDtlId + ',' + row.depositCutoffDate + ',' + row.depositDate + '\')">缴纳订金</a>');
                            }
                        }

                        return actions.join('');
                    }

                },
                // {
                //     field: 'depositCutoffDate',
                //     title: '订金截止时间',
                //     visible: 'true',
                //     align: 'left',
                // },
                // {
                //     field: 'deposit',
                //     title: '订金',
                //     visible: 'true',
                //     align: 'left',
                //
                // },
                {
                    title: '发货单号/截止时间',
                    align: 'left',
                    field: 'invoiceVbillno',
                    formatter: function (value, row, index) {

                        let html= $.table.tooltip(value) + '<br>'
                        if (row.cutoffDate == null) {
                            html+='';
                        } else {
                            let oldDate=new Date(row.cutoffDate);
                            oldDate=oldDate.setDate(oldDate.getDate()-1);
                            let newDate=new Date(oldDate)
                            let now = new Date();
                            if (newDate < now) {
                                html+= '<span class="label label-danger">' + row.cutoffDate + '</span>';
                            } else {
                                html+= row.cutoffDate;
                            }
                        }
                        return html;
                    }
                },

                // {
                //     field: 'lotOutStatus',
                //     title: '报价状态',
                //     align: 'left',
                // },
                // {
                //     field: 'vbillstatus',
                //     title: '外发状态',
                //     align: 'left',
                //     formatter: function status(row, value) {
                //         return $.table.selectDictLabel(out_vbillstatus, value.vbillstatus);
                //     }
                // },
                {
                    field: 'outType',
                    title: '外发类型',
                    align: 'left',
                    formatter: function status(row, value) {
                        return $.table.selectDictLabel(out_type, value.outType);
                    }
                },
                {
                    field: 'outPrice',
                    halign: 'center',
                    title: '运费',
                    align: 'right',
                    cellStyle:function (value, row, index) {
                        return {css: {"color": "#FF9008"}}; 
                    },
                    formatter: function (value, row, index) {
                        let text=""
                        if(row.outType==0){
                            if(row.enquiryDtl.quotedPrice != null){
                                text=row.enquiryDtl.quotedPrice.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            }else{
                                text+=$.table.tooltip(row.enquiryDtl.quotedPrice);
                            }
                        }else if(row.outType==1){
                            if (value != null) {
                                text=value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            }else{
                                text+=$.table.tooltip(value);
                            }
                        } 
                        return text;
                    }
                },
                {
                    field: 'lotOutContactList',
                    title: '联系信息',
                    align: 'left',
                    formatter: function status(value,row){
                        let text='';
                        if(value != null){
                            let list=value.map(item=>{
                                return item.contactName+"<br/>"+item.contactTel;
                            })
                            text=list.join('<br/>')
                        }
                        return text;
                    }
                },
                {
                    field: 'reqDeliDate',
                    title: '要求提货/到货日期',
                    align: 'left',
                    formatter: function status(value,row){
                        return $.table.tooltip(value)+"<br/>"+$.table.tooltip(row.reqArriDate);
                    }
                },
                {
                    field: 'deliAddr',
                    title: '装卸货地址',
                    align: 'left',
                    formatter: function status(row, value) {
                        if(value.isMultiple==1){
                            return value.shippingAddressList.sort(sortNum).map(item=>{
                                if(item.addressType == 0){
                                    return '<span class="label label-warning pa2">装</span>'+$.table.tooltip(item.provinceName+item.cityName+item.areaName);
                                }
                                if(item.addressType == 1){
                                    return '<span class="label label-success pa2">卸</span>'+$.table.tooltip(item.provinceName+item.cityName+item.areaName);
                                }
                            }).join("<br/>");
                        }else{
                            let deliAddr=value.deliProName+value.deliCityName+value.deliAreaName;
                            let arriAddr=value.arriProName+value.arriCityName+value.arriAreaName;
                            return '<span class="label label-warning pa2">装</span>'+$.table.tooltip(deliAddr)+'<br/><span class="label label-success pa2">卸</span>'+$.table.tooltip(arriAddr);
                        }
                     }
                },
                {
                    title: '货品信息',
                    align: 'left',
                    field: 'weightCount',
                    formatter: function (value, row, index) {
                        let text=$.table.tooltip(row.goodsName)+"<br/>";
                        if(row.weightCount != null && row.weightCountMost  != null){
                            let list=[];
                            if(row.weightCount != 0){
                                list.push($.table.tooltip(row.weightCount)+'吨')
                            }
                            if(row.weightCountMost != 0){
                                list.push($.table.tooltip(row.weightCountMost)+'吨')
                            }
                            text+= list.join("~")
                        }
                        if(row.volumeCount != null && row.volumeCountMost  != null){
                            let list=[];
                            if(row.volumeCount != 0){
                                list.push($.table.tooltip(row.volumeCount)+'m³')
                            }
                            if(row.volumeCountMost != 0){
                                list.push($.table.tooltip(row.volumeCountMost)+'m³')
                            }
                            text+= list.join("~")
                        }
                        return text;
                    }
                },
                {
                    title: '要求车长车型',
                    align: 'left',
                    field: 'carLen',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(findCarLength, value)+$.table.tooltip(row.carTypeName);
                    }
                },
                {
                    field: 'memo',
                    title: '备注',
                    align: 'left',
                },
            ]
        };

        $.table.init(options);
        // 初始化省市区
        $.provinces.init("provinceId", "cityId", "areaId");
        // 初始化省市区
        $.provinces.init("provinceId1", "cityId1", "areaId1");
    });
    
    // 地址排序
    function sortNum(a,b) {
        return a.addressType-b.addressType;
    }

    function detail(id) {
        var url = prefix + "/detail/" + id;
        $.modal.openTab("货源详细", url);
    }

    function NowTime() {
        var myDate = new Date();
        var y = myDate.getFullYear();
        var M = myDate.getMonth() + 1;     //获取当前月份(0-11,0代表1月)
        var d = myDate.getDate();        //获取当前日(1-31)
        var h = myDate.getHours();       //获取当前小时数(0-23)
        var m = myDate.getMinutes();     //获取当前分钟数(0-59)
        var s = myDate.getSeconds();     //获取当前秒数(0-59)

        //检查是否小于10
        M = check(M);
        d = check(d);
        h = check(h);
        m = check(m);
        s = check(s);
        var timestr = y + "-" + M + "-" + d + " " + h + ":" + m + ":" + s;
        return timestr;
    }

    //时间数字小于10，则在之前加个“0”补位。
    function check(i) {
        var num = (i < 10) ? ("0" + i) : i;
        return num;
    }


    //当前系统时间
    var date = new Date();
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    var hour = date.getHours();
    var minute = date.getMinutes();
    var second = date.getSeconds();
    var currDate = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
    //转换为毫秒数
    var currDateSeconds = Date.parse(currDate);

    //抢单
    function grabOrder(arr) {
        //分离
        var arr = arr.split(',');
        var id = arr[0]; //id
        var cutoffDate = arr[1];//抢单时间
        var vbillstatus = arr[2];//外发状态
        if (vbillstatus != 0) {
            $.modal.alertError("该单已抢单或抢单已结束");
            return;
        }
        var cutoffDateSeconds = Date.parse(cutoffDate);
        if (currDateSeconds > cutoffDateSeconds) {
            $.modal.alertError("抢单时间已经截止");
            return;
        }
        var url = prefix + "/grabOrder/" + id;
        $.modal.openTab("抢单", url);
    }

    //报价
    function quote(data) {
        var dataJson = JSON.parse(data);
        //分离id，时间
        var id = dataJson.lotOutId;
        var cutoffDate = dataJson.cutoffDate;
        var cutoffDateSeconds = Date.parse(cutoffDate);

        //判断报价截止时间
        if (currDateSeconds > cutoffDateSeconds) {
            $.modal.alertError("报价时间已经截止");
            return;
        }

        var url = prefix + "/quote/" + id;
        $.modal.open("报价", url,600,480);
    }

    //缴纳定金
    function deposit(arr) {
        var arr = arr.split(',');
        var id = arr[0];
        var depositCutoffDate = arr[1];//缴纳订金截止时间
        var depositDate = arr[2];//缴纳订金时间
        var depositCutoffDateSeconds = Date.parse(depositCutoffDate);//转为毫秒数
        if (currDateSeconds > depositCutoffDateSeconds) {
            $.modal.alertError("缴纳订金时间已经截止");
            return;
        }

        if (depositDate.length != 4) {
            $.modal.alertError("该账单订金已缴纳");
            return;
        }
        var url = prefix + "/pay/" + id;
        $.modal.confirm("是否确认支付订金", function deposit() {
            $.modal.openTab("支付订金", url)
        });

    }

    function tabto(btn,status) {
        if (status == null) {
            $("#vbillstatus").val('');
        } else {
            $("#vbillstatus").val(status);
        }
        $(".status1").removeClass("act");
        $(btn).addClass("act")
        $.table.search()
    }
</script>

</body>
</html>