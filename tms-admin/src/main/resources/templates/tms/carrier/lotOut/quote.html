<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('报价')"/>
</head>
<style>
    .lh26{
        line-height: 26px;
    }
    .fw{
        font-weight: 600;
    }
    .f16{
        font-size: 16px;
    }
    .tr{
        text-align: right;
    }
    .text-right{
        width: 100px;
        display: inline-block;
        text-align: right;
        color: #808080;
    }
    .inputH{
        line-height: 30px;
        height: 30px;
    }
</style>
<body>
<div class="form-content" style="padding-top: 0;">
    <form id="form-receipt-edit" class="form-horizontal" novalidate="novalidate" th:object="${invoice}">
        <!--外发运单id-->
        <input type="hidden" name="lotOutId" th:value="${lotOut?.lotOutId}">
        <!--抢单报价截止时间-->
        <input type="hidden" name="commitDate" th:value="${#dates.format(lotOut?.cutoffDate,'yyyy-MM-dd HH:mm:ss')}">
        <div class="row" style="background-color:#FAFBFC;padding: 20px 20px 10px;">
            <div class="col-md-12 fw f16 mt5">
                <span th:text="${lotOut.deliProName+lotOut.deliCityName+lotOut.deliAreaName}"></span>
                <span><i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394;"></i></span>
                <span th:text="${lotOut.arriProName+lotOut.arriCityName+lotOut.arriAreaName}"></span>
            </div>
            <div class="col-md-12 mt10">
                <span class="text-right">要提货时间：</span>
                <span th:text="${#dates.format(invoice?.reqDeliDate,'yyyy-MM-dd HH:mm:ss')}"></span>
            </div>
            <div class="col-md-12 mt10">
                <span class="text-right">要求车长车型：</span>
                <span th:text="${lotOut.carLenName}"></span>米
                <span th:text="${lotOut.carTypeName}"></span>
            </div>
            <div class="col-md-12 mt10">
                <span class="text-right">货品信息：</span>
                <span th:text="${lotOut.goodsName}"></span>
                <span th:text="|${lotOut.weightCount}吨|"></span>
                <span th:text="|~${lotOut.weightCountMost}吨|" th:if="${lotOut.weightCountMost != null}"></span>
            </div>
            <div class="col-md-12 mt10">
                <span class="text-right">备注信息：</span>
                <span th:text="${lotOut.memo}"></span>
            </div>
        </div>

        <div class="row" style="padding: 10px 20px ;">
            <div class="col-md-12 col-sm-12">
                <span class="text-right inputH" style="color: #000000;"><span style="color: red">*</span>报价金额(元):</span>
                <span class="inputH" th:if="${enquiryDtl?.quotedPrice}" th:text="|￥${#numbers.formatDecimal(enquiryDtl?.quotedPrice,0,'COMMA',2,'POINT')}"|></span>
                <span th:unless="${enquiryDtl?.quotedPrice}">
                    <input type="text" oninput="$.numberUtil.onlyNumber(this)" min="0" name="quotedPrice" maxlength="15" required class="from-control inputH"
                    placeholder="请输入期望价格">
                </span>
            </div>
        </div>


    </form>
</div>


<th:block th:include="include :: footer"/>
<script>
    var prefix = ctx + "carrier/lotOut";

    $(function () {
        // 表单验证
        $("#form-receipt-edit").validate({
            focusCleanup: true
        });
        
    });

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/saveQuote", $('#form-receipt-edit').serialize(),function(result){
                if(result.code==0){
                    $.modal.close();
                    parent.$.table.refresh();
                }
            });
        }
    }






</script>
</body>

</html>