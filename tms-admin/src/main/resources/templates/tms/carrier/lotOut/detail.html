<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('接单-明细')"/>
</head>
<style type="text/css">
    .custom-tab td{
        text-align: left;
    }

</style>
<body>
<div class="form-content">
    <form id="form-receipt-edit" class="form-horizontal" novalidate="novalidate" th:object="${invoice}">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">要求装货时间：</label>
                                        <div class="col-sm-8"
                                             th:text="${#dates.format(invoice?.reqDeliDate,'yyyy-MM-dd HH:mm:ss')}">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">要求卸货时间：</label>
                                        <div class="col-sm-8"
                                             th:text="${#dates.format(invoice?.reqArriDate,'yyyy-MM-dd HH:mm:ss')}">
                                        </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">车长(米)：</label>
                                    <div class="col-sm-8">
                                        <div class="col-sm-8" th:each="dict : ${@dict.getType('car_len')}"
                                             th:if="${dict.dictValue} == ${invoice?.carLen}"
                                             th:text="${dict.dictLabel}">
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">车型：</label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('car_type')}"
                                         th:if="${dict.dictValue} == *{carType}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-1">装货详址：</label>
                                    <div class="col-sm-11"
                                         th:text="${lotOut.deliProName+lotOut.deliCityName+lotOut.deliAreaName+lotOut.deliDetailAddr}">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-12 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-1">卸货详址：</label>
                                    <div class="col-sm-11"
                                         th:text="${lotOut.arriProName+lotOut.arriCityName+lotOut.arriAreaName+lotOut.arriDetailAddr}">
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10" th:text="${lotOut.memo}">

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <!--基础信息 end-->


        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">货品明细</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">总重量：</label>
                                <div class="col-sm-8" th:text="|${lotOut.weightCount}吨|">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width:10%; ">货品类型</th>
                                <th style="width:10%; ">货品名称</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="mapS,status:${segPackGoods}">
                                <td th:text="${mapS.goodsTypeName}"></td>
                                <td th:text="${mapS.goodsName}"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default" th:if="${lotOut.vbillstatus=='1'&&enquiryDtl?.lotOutDtlId != null}">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFive">付款信息</a>
                </h4>
            </div>
            <div id="collapseFive" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!-- 付款信息-->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">司机结算方式：</label>
                                <div class="col-sm-8" th:if="${balatype!=null}"
                                     th:text="${@dict.getLabel('driver_bala_type',balatype)}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">结算金额（￥）：</label>
                                <div class="col-sm-8" th:text="${lotOut.transFee}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 8%;">付款类型</th>
                                <th style="width: 10%;">金额（￥）</th>
                                <th style="width: 15%;">油卡号</th>
                                <th style="width: 15%;">收款账户</th>
                                <th style="width: 15%;">收款银行</th>
                                <th style="width: 15%;">收款卡号</th>
                                <th style="width: 20%;">备注</th>
                            </tr>
                            </thead>
                            <tr>
                            <tr th:if="${lotOut.isDeposit=='1'}"
                                th:each="paydetail : ${lotOutPayDetailList}">
                                <td th:text="${@dict.getLabel('cost_type_freight',paydetail.payType)}">
                                </td>
                                <td th:text="${paydetail.transFeeCount}">
                                </td>
                                <td th:text="${paydetail.oilCardNumber}">
                                </td>
                                <td th:text="${paydetail.recAccount}">
                                </td>
                                <td th:text="${paydetail.recBank}">
                                </td>
                                <td th:text="${paydetail.recCardNo}">
                                </td>
                                <td th:text="${paydetail.memo}">
                                </td>
                            </tr>

                            <tr th:if="${lotOut.isDeposit=='0'}"
                                th:each="item: ${payDetailList}">
                                <td th:text="${@dict.getLabel('cost_type_freight',item.costTypeFreight)}">
                                </td>
                                <td th:text="${item.transFeeCount}">
                                </td>
                                <td th:text="${item.oilCardNumber}">
                                </td>
                                <td th:text="${item.recAccount}">
                                </td>
                                <td th:text="${item.recBank}">
                                </td>
                                <td th:text="${item.recCardNo}">
                                </td>
                                <td th:text="${item.memo}">
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default" th:if="${lotOut.vbillstatus=='1'&&enquiryDtl?.lotOutDtlId != null}">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseThree">抢单报价信息</a>

                </h4>
            </div>
            <div id="collapseThree" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span>外发类型：</span></label>

                                <div class="col-sm-8" th:text="${enquiryDtl?.outType == 0? '报价':'抢单'}">
                                </div>

                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span>报价/抢单金额：</span></label>

                                <div class="col-sm-8" th:if="${enquiryDtl?.quotedPrice}"
                                     th:text="|￥${#numbers.formatDecimal(enquiryDtl?.quotedPrice,0,'COMMA',2,'POINT')}"
                                     |>
                                </div>

                            </div>
                        </div>


                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span>是否有订金：</span></label>

                                <div class="col-sm-8" th:if="${enquiryDtl?.isDeposit}"
                                     th:text="${enquiryDtl?.isDeposit == '0'? '否':'是'}">
                                </div>

                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span>订金金额：</span></label>

                                <div class="col-sm-8" th:if="${enquiryDtl?.deposit}"
                                     th:text="|￥${#numbers.formatDecimal(enquiryDtl?.deposit,0,'COMMA',2,'POINT')}" |>
                                </div>

                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span>订金截止时间：</span></label>

                                <div class="col-sm-8"
                                     th:text="${#dates.format(enquiryDtl?.depositCutoffDate, 'yyyy-MM-dd HH:mm:ss')}">
                                </div>

                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span>缴纳订金时间：</span></label>

                                <div class="col-sm-8"
                                     th:text="${#dates.format(enquiryDtl?.depositDate, 'yyyy-MM-dd HH:mm:ss')}">
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

            <div class="panel panel-default" th:if="${lotOut.vbillstatus=='0'&&dtl?.lotOutDtlId != null}">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseThree">抢单报价信息</a>

                    </h4>
                </div>
                <div id="collapseThree" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>报价/抢单金额：</span></label>

                                    <div class="col-sm-8" th:if="${dtl?.quotedPrice}"
                                         th:text="|￥${#numbers.formatDecimal(dtl?.quotedPrice,0,'COMMA',2,'POINT')}"
                                         |>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <!--<button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;-->
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script>
    var prefix = ctx + "carrier/orders";

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        var options = {};
        $.table.init(options);
    });

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/savegrabOrder", $('#form-receipt-edit').serialize());
        }
    }


</script>
</body>

</html>