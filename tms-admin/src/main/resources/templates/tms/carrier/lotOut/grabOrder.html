<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('抢单')"/>
</head>

<body>
<div class="form-content">
    <form id="form-receipt-edit" class="form-horizontal" novalidate="novalidate" th:object="${invoice}">

        <!--外发运单id-->
        <input type="hidden" name="lotOutId" th:value="${lotOut?.lotOutId}">
        <!--抢单报价截止时间-->
        <!--<input type="hidden" name="commitDate" th:value="${#dates.format(lotOut?.cutoffDate,'yyyy-MM-dd HH:mm:ss')}">-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">要求装货时间：</label>
                                        <div class="col-sm-8" th:text="${#dates.format(invoice?.reqDeliDate,'yyyy-MM-dd HH:mm:ss')}">
                                        </div>

                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">要求卸货时间：</label>
                                        <div class="col-sm-8" th:text="${#dates.format(invoice?.reqArriDate,'yyyy-MM-dd HH:mm:ss')}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">车长(米)：</label>
                                        <div class="col-sm-8" th:each="dict : ${@dict.getType('car_len')}" th:if="${dict.dictValue} == ${invoice?.carLen}" th:text="${dict.dictLabel}">
                                        </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">车型：</label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('car_type')}" th:if="${dict.dictValue} == *{carType}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>


                        </div>
                        <div class="row">
                            <div class="col-md-12 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-1">装货详址：</label>
                                    <div class="col-sm-11" th:text="${lotOut.deliProName+lotOut.deliCityName+lotOut.deliAreaName+lotOut.deliDetailAddr}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-1">卸货详址：</label>
                                    <div class="col-sm-11"
                                         th:text="${lotOut.arriProName+lotOut.arriCityName+lotOut.arriAreaName+lotOut.arriDetailAddr}">
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <!--基础信息 end-->


        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">货品明细</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">总毛重：</label>
                                <div class="col-sm-8" th:text="|${lotOut.grossWeight}吨|">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table" >
                            <thead>
                            <tr>
                                <th style="width:10%; ">货品类型</th>
                                <th style="width:10%; ">货品名称</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="mapS,status:${segPackGoods}">
                                <td th:text="${mapS.goodsTypeName}"></td>
                                <td th:text="${mapS.goodsName}"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseThree">抢单</a>

                </h4>
            </div>
            <div  id="collapseThree" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <input type="hidden" th:value="${lotOut?.outPrice}" name="quotedPrice">
                                <label class="col-sm-5"><span style="color: red">报价金额：</span></label>

                                <div class="col-sm-7" th:text="|￥${#numbers.formatDecimal(lotOut?.outPrice,0,'COMMA',2,'POINT')}"|>

                                </div>

                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6"></div>
                        <div class="col-md-3 col-sm-6"></div>
                        <div class="col-sm-3 col-sm-6" th:if="${lotOut.outType== 1}">
                            <label class="col-sm-5"></label>
                            <div class="col-sm-6">
                                <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>抢单</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <!--<button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;-->
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script>
    var prefix = ctx + "carrier/lotOut";

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        var options = {};
        $.table.init(options);

    });

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/savegrabOrder", $('#form-receipt-edit').serialize());
        }
    }






</script>
</body>

</html>