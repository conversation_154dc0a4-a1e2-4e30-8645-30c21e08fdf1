<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: deposit"/>
</head>

<body style="background:none;">
<div style=" width:800px; margin:50px auto;">
    <div  class="comm-pay-box">
        <!--外发抢单报价单 id-->
        <input type="hidden" name="lotOutDtlId" id="lotOutDtlId" th:value="${lotOutDtl?.lotOutDtlId}">
        <!--外发运单id-->
        <input type="hidden" name="lotOutId" id="lotOutId" th:value="${lotOutDtl?.lotOutId}">

        <div class="comm-pay-total">
            应付金额：&nbsp;&nbsp;<span>¥ <label>[[${payCost}]]</label></span>
        </div>

        <div class="clear comm-pay-others">
            <div class="comm-pay-tab">
                <span  class="over">支付宝支付</span>
                <span id="wxSpan">微信支付</span>
            </div>
            <div class="comm-pay-other">
                <ul>
                    <li >
                        <a href="#" onclick="alipay()" class="bank-alipay"></a>
                    </li>
                    <li>
                        <div style="margin-top: 7px;color: red;">
                            *点击前往支付宝支付
                        </div>
                    </li>
                </ul>
            </div>
            <div class="comm-pay-other" style=" display:none;">
                <div id="code" style="text-align: center;color:red">
                </div>
                <div style="text-align: center;margin-top: 20px">
                    <img th:src="@{/img/wxinfomsg.png}" />
                </div>
            </div>
        </div>
        <div class="comm-pay-btn clear"><input class="comm-btn-pay" name="commit" id="checkPayStatus" value="我已支付" type="button"></div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "carrier/lotOut";
    document.oncontextmenu = function () { return false; }//禁止右键
    document.onkeydown = function (e) {//键盘按键控制
        e = e || window.event;
        if ((e.ctrlKey&&e.keyCode==82) || //ctrl+R
            e.keyCode == 116) {//F5刷新，禁止
            //阻止默认刷新时间
            e.returnValue = false
            if (e.preventDefault) e.preventDefault();
            else e.keyCode = 0;
            //你的刷新IFrame代码
            return false;
        }
    }
    window.onbeforeunload = function (e) {
        return (e || window.event).returnValue = '确认要离开当页面？如果已经支付，请耐心等待！';
    }

    var orderPayId = [[${orderPayId}]];

    //是否请求过微信标记
    var wxPng = false;

    //跳转阿里支付
    function alipay(){
        var url = ctx + "alipay/pay?orderPayId="+orderPayId;
        window.open(url);
    }

    $(function () {
        $("#select-pay").click(function(){
            if($(this).is(':checked') ){
                $(".comm-pay-others").hide();
            }else{
                $(".comm-pay-others").show();
            }
        });

        $(".comm-pay-tab span").click(function(){
            var curIndex = $(this).index();
            $(this).siblings().removeClass("over");
            $(this).addClass("over");
            $(".comm-pay-other").hide();
            $(".comm-pay-other:eq("+curIndex+")").show();
        });

        $("#wxSpan").click(function () {
            if( wxPng == false ){
                //微信支付
                $.ajax({
                    url : ctx + 'wxpay/pay?orderPayId='+orderPayId,
                    method : 'POST',
                    success:function (data) {
                        if (data.code !== 0) {
                            $("#code").html(data.msg);
                        }else{
                            $("#code").qrcode({width: 260, height:260, text: data.data.code_url});
                            wxPng = true;
                        }
                    }
                });
            }
        });

        //判断是否已经支付
        function checkPayStatus(){
            var url = ctx + "alipay/isPay";
            var data = {};
            data.orderPayId = orderPayId;
            $.ajax({
                url : url,
                method : 'POST',
                data : data,
                success:function (data) {
                    if (data.payStatus == '1') {
                        //关闭定时器
                        window.clearInterval(checkPayStatus);
                        var lotOutId = $("#lotOutId").val();
                        var lotOutDtlId = $("#lotOutDtlId").val();
                        var data ={
                            lotOutId:lotOutId,
                            lotOutDtlId:lotOutDtlId
                        };
                        $.operate.saveTab(prefix + "/saveDeposit", data);
                    }
                }
            })
        }
        window.setInterval(checkPayStatus,5000);
    })
</script>
</body>
</html>