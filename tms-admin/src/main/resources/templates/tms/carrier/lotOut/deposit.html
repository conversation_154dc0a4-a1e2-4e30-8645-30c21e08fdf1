<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('支付订金')"/>
</head>

<body>
<div class="form-content">
    <form id="form-deposit-edit" class="form-horizontal" novalidate="novalidate" th:object="${invoice}">
        <!--外发抢单报价单 id-->
        <input type="hidden" name="lotOutDtlId" id="lotOutDtlId" th:value="${enquiryDtl?.lotOutDtlId}">
        <!--承运商 id-->
        <input type="hidden" name="carrierId" th:value="${enquiryDtl?.carrierId}">
        <!-- 发货单id-->
        <input type="hidden" name="lotOutId" th:value="${lotOut?.lotOutId}">
       <!-- 发货单id-->
        <input type="hidden" name="invoiceId" th:value="${lotOut?.invoiceId}">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">要求装货时间：</label>
                                        <div class="col-sm-8" th:text="${#dates.format(invoice?.reqDeliDate,'yyyy-MM-dd HH:mm:ss')}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">要求卸货时间：</label>
                                        <div class="col-sm-8" th:text="${#dates.format(invoice?.reqArriDate,'yyyy-MM-dd HH:mm:ss')}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">车长(米)：</label>
                                    <div class="col-sm-8">
                                        <div class="col-sm-8" th:each="dict : ${@dict.getType('car_len')}" th:if="${dict.dictValue} == ${invoice?.carLen}" th:text="${dict.dictLabel}">
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">车型：</label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('car_type')}" th:if="${dict.dictValue} == *{carType}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-1">装货详址：</label>
                                    <div class="col-sm-11"
                                         th:text="${lotOut.deliProName+lotOut.deliCityName+lotOut.deliAreaName+lotOut.deliDetailAddr}">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-12 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-1">卸货详址：</label>
                                    <div class="col-sm-11"
                                         th:text="${lotOut.arriProName+lotOut.arriCityName+lotOut.arriAreaName+lotOut.arriDetailAddr}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--基础信息 end-->

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">货品明细</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">总毛重：</label>
                                <div class="col-sm-8" th:text="|${lotOut.grossWeight}吨|">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table" >
                            <thead>
                            <tr>
                                <th style="width:10%; ">货品类型</th>
                                <th style="width:10%; ">货品名称</th>
                                <!--<th style="width:10%; ">货品编码</th>
                                <th style="width:10%; ">数量</th>

                                <th style="width:10%; ">毛重</th>
                                <th style="width:10%; ">体积</th>
                                <th style="width:10%; ">包装单位</th>-->
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="mapS,status:${segPackGoods}">
                                <td th:text="${mapS.goods.goodsTypeName}"></td>
                                <td th:text="${mapS.goodsName}"></td>
                               <!-- <td th:text="${mapS.goodsCode}"></td>
                                <td th:text="${mapS.num}"></td>

                                <td th:text="${mapS.weight}"></td>
                                <td th:text="${mapS.volume}"></td>
                                <td>
                                        <span class="form-control-static"
                                              th:text="${@dict.getLabel('package_type',mapS.packId)}"></span>
                                </td>-->


                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>



        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFour">抢单报价信息</a>
                </h4>
            </div>
            <div id="collapseFour" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">订金：</label>
                                <div class="col-sm-8"  th:if="${enquiryDtl?.deposit}" th:text="|￥${#numbers.formatDecimal(enquiryDtl?.deposit,0,'COMMA',2,'POINT')}">
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">缴纳订金截止时间：</label>
                                <div class="col-sm-8">
                                    <div class="input-group" th:text="${#dates.format(enquiryDtl?.depositCutoffDate, 'yyyy-MM-dd HH:hh:mm')}">
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>支付订金
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "carrier/lotOut";

    var lotOutDtlId = [[${enquiryDtl.lotOutDtlId}]];


    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        var options = {};
        $.table.init(options);

    });

    //校验支付状态
    function checkPayStatus(){
        var url = prefix + "/checkPayStatus";
        //外发报价单id
        var lotOutDtlId = $("#lotOutDtlId").val();
        var data = {};
        data.lotOutDtlId = lotOutDtlId;
        $.ajax({
            url : url,
            method : 'POST',
            data : data,
            success:function (data) {
                if (data.payStatus == 1) {
                    //支付成功，新增运单，委托单等
                    if ($.validate.form()) {
                        $.operate.saveTab(prefix + "/saveDeposit", $('#form-deposit-edit').serialize());
                    }
                }
            }
        })
    }

    //提交
    function submitHandler() {
        var url = prefix + "/pay/"+lotOutDtlId;
        $.modal.openTab("支付定金", url);
        checkPayStatus();
        //var myInterval = window.setInterval(function(){ checkPayStatus() },3000);
    }









</script>
</body>

</html>