<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车辆管理-新增')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .err-tips-box{ clear: both; width: 100%; float: left; padding: 10px 0px;}
    .err-tips{ background-color: #fffff7;width: 100%; float: left; border-radius: 4px; border: 1px #d9cab5 dashed; padding:5px 15px;}
    .err-tips h2{  font-size: 13px; margin: 0px !important; padding: 0px; font-weight: bold; line-height: 28px; padding-bottom: 5px;}
    .err-tips h2 .fa{ color: #b83d1b; font-size: 14px; margin-right: 3px;}
    .err-tips p{ font-size: 13px; margin: 0px !important; line-height: 22px; padding: 0px; padding-bottom: 5px; color: #8d6732; }
    .err-tips p:last-child{ padding-bottom: 0px;}
    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 20px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .table-bordered {
        border: 0px solid #EBEBEB;
    }
    .table-striped .table, .table-striped .table, .table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td {
        border-bottom: 0px solid #e7eaec!important;
    }
    .table-bordered td, .table-bordered th {
        border: 0px solid #ddd!important;
    }
    .input-group{
        margin-bottom: 10px;
    }
    .save{
        padding: 0px 0;
        position: fixed;
        bottom: 0px;
        left: 0;
        width: 100%;
        background: #fff;
        z-index: 99;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .input-group .form-control {
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .eye .file-drop-zone-title{
        background: url('../../img/eye.png') no-repeat center 10px;
        background-size: 40px 40px;
        height: 100%;
        line-height: 120px;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .fcff{
        color: #ff1f1f;
    }
    .flex_right select {
        margin-bottom: 10px;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
</style>
<body>
<div class="form-content">
    <form id="form-car-add" class="form-horizontal" novalidate="novalidate">
        <input name="carId" id="carId" type="hidden"/>
        <!--实际承运人id-->
        <input id="actualCarrierid" name="actualCarrierid" type="hidden" />
        <div class="panel-group" id="accordionFour">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordionThree"
                           href="tabs_panels.html#collapseTwo">车辆基本信息</a>
                    </h5>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="col-md-6 col-sm-6">
                                    <div>
                                        <div class="form-group">
                                            <label class="col-sm-12"><span class="fcff">*</span>行驶证：</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group eye">
                                            <div class="">
                                                <input id="image1" class="form-control"
                                                       name="image1" type="file" multiple>
                                                <input id="tid1" th:name="tid1" type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--<div class="col-md-6 col-sm-6">
                                    <div>
                                        <div class="form-group">
                                            <label class="col-sm-12">行驶证年检照片：</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <div class="">
                                                <input id="image6" class="form-control"
                                                       name="image6" type="file" multiple>

                                            </div>
                                        </div>
                                    </div>
                                </div>-->
                                <input id="tid6" name="tid6" type="hidden">
                                <div class="col-md-6 col-sm-6">
                                    <div>
                                        <div class="form-group">
                                            <label class="col-sm-12">道路运输证：</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <div class="">
                                                <input id="image2" class="form-control"
                                                       name="image2" type="file" multiple>
                                                <input id="tid2" name="tid2" type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div>
                                        <div class="form-group">
                                            <label class="col-sm-12">车辆照：</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <div class="">
                                                <input id="image3" class="form-control"
                                                       name="image3" type="file" multiple>
                                                <input id="tid3" name="tid3" type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <input id="tid5" name="tid5" type="hidden" >
                                <input id="tid7" name="tid7" type="hidden" >
                                <!--  <div class="col-md-6 col-sm-6">
                                      <div>
                                          <div class="form-group">
                                              <label class="col-sm-12">挂车行驶证:</label>
                                          </div>
                                      </div>
                                      <div class="col-sm-12">
                                          <div class="form-group">
                                              <div class="">
                                                  <input id="image5" class="form-control"
                                                         name="image5" type="file" multiple>
                                                  <input id="tid5" name="tid5" type="hidden" >
                                              </div>
                                          </div>
                                      </div>
                                  </div>
                                  <div class="col-md-6 col-sm-6">
                                      <div>
                                          <div class="form-group">
                                              <label class="col-sm-12">挂车行驶证年检照片:</label>
                                          </div>
                                      </div>
                                      <div class="col-sm-12">
                                          <div class="form-group">
                                              <div class="">
                                                  <input id="image7" class="form-control"
                                                         name="image7" type="file" multiple>
                                                  <input id="tid7" name="tid7" type="hidden" >
                                              </div>
                                          </div>
                                      </div>
                                  </div>-->
                            </div>
                            <!--                            <div class="col-sm-6">-->
                            <!--                                <div class="row">-->
                            <!--                                    <div class="col-md-3 col-sm-6">-->
                            <!--                                        <div class="flex">-->
                            <!--                                            <label class="flex_left"><span class="fcff">*</span>  是否符合无车承运人：</label>-->
                            <!--                                            <div class="flex_right">-->
                            <!--                                                <select name="isVehicleFreeCarrier" id="isVehicleFreeCarrier" required class="form-control valid" aria-invalid="false" onblur="checkTotalQuality()">-->
                            <!--                                                    <option value="">&#45;&#45; 请选择 &#45;&#45;</option>-->
                            <!--                                                    <option value="1">是</option>-->
                            <!--                                                    <option value="0">否</option>-->
                            <!--                                                </select>-->
                            <!--                                            </div>-->
                            <!--                                        </div>-->
                            <!--                                    </div>-->

                            <!--                                    &lt;!&ndash;<div class="col-md-3 col-sm-6">-->
                            <!--                                        <div class="form-group">-->
                            <!--                                            <a class="btn btn-primary btn-rounded btn-xs" onclick="searchCarInfo()"><i class="fa fa-search"></i>&nbsp;查询车辆承运人信息</a>-->
                            <!--                                        </div>-->
                            <!--                                    </div>&ndash;&gt;-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <div class="col-sm-6">
                                <div class="row">
                                    <div class="col-md-6 col-sm-6" style="display: none">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span>  承运商：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <input name="carrierId" id="carrierId" type="hidden" th:value="${carrId}">
                                                    <input name="carrName" id="carrierName" class="form-control checkVal" type="text"  required readonly="true"  onclick="selectCarrier()" th:value="${carrier.carrName}">
                                                    <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6" style="display: none">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span>  <span>车辆性质：</span></label>
                                            <div class="flex_right">
                                                <select id="carProp" name="carProp" class="form-control valid checkVal" aria-invalid="false" required th:with="type=${@dict.getType('car_nature')}">
<!--                                                    <option value="">&#45;&#45; 请选择 &#45;&#45;</option>-->
<!--                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${dict.dictValue=='2'}"></option>-->
                                                    <option value="2">外协</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span>  车牌号：</label>
                                            <div class="flex_right">
                                                <input id="carno" name="carno" class="form-control valid" type="text" required="true" aria-required="true" maxlength="25" onkeyup="findOneCar(this.value)">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left" ><span class="fcff">*</span>  所属省：</label>
                                            <div class="flex_right">
                                                <select name="countrysubdivisioncode" id="provinceCode"
                                                        class="form-control valid checkVal" aria-invalid="false" required>
                                                    <option value='' selected='selected'>-- 请选择 --</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span>  <span>车长：</span></label>
                                            <div class="flex_right">
                                                <select id="carLengthId" name="carLengthId" class="form-control valid checkVal" aria-invalid="false" required
                                                        th:with="type=${@dict.getType('car_len')}" onchange="setCarLenName()">
                                                    <option value="">-- 请选择 --</option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                                <input type="hidden" name="carLenName" id="carLenName"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span>  <span>车型：</span></label>
                                            <div class="flex_right">
                                                <select id="vehicleclassificationcode" name="vehicleclassificationcode" class="form-control valid checkVal" aria-invalid="false" required
                                                        th:with="type=${@dict.getType('car_type')}" onchange="setCarTypeName()">
                                                    <option value="">-- 请选择 --</option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                                <input type="hidden" name="carTypeName" id="carTypeName"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left "><span class="fcff">*</span>  营运车辆&nbsp;&nbsp;<br />类型代码：</label>
                                            <div class="flex_right">
                                                <select name="operationType" id="operationType" class="form-control ntoccVal checkVal" th:with="type=${@dict.getType('OPERATION_TYPE')}" required>
                                                    <option value="">-- 请选择 --</option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left checkTotalQualityColor" >使用性质：</label>
                                            <div class="flex_right">
                                                <input name="usercharacter" id="usercharacter" class="form-control valid checkTotalQuality"  type="text" maxlength="10">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ntocc">车牌类型：</label>
                                            <div class="flex_right">
                                                <select name="licenseplatetypecode" id="licenseplatetypecode" class="form-control" th:with="type=${@dict.getType('car_plate_type')}">
                                                    <option value="">-- 请选择 --</option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span>  车牌颜色：</label>
                                            <div class="flex_right">
                                                <select name="vehiclelicenseplatecolor" id="vehiclelicenseplatecolor" class="form-control checkVal" th:with="type=${@dict.getType('car_plate_color')}" required th:onblur="selectLicenseplatetypecode(this.value)">
                                                    <option value="">-- 请选择 --</option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ntocc">车轴数：</label>
                                            <div class="flex_right">
                                                <input name="axlenum" id="axlenum" class="form-control valid"  type="text" maxlength="2">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left">能源类型：</label>
                                            <div class="flex_right">
                                                <select id="vehicleenergytype" name="vehicleenergytype" class="form-control valid" aria-invalid="false"
                                                        th:with="type=${@dict.getType('vehicleenergytype')}" >
                                                    <option value="">-- 请选择 --</option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left checkTotalQualityColor"><span class="fcff">*</span>车辆识别代号：</label>
                                            <div class="flex_right ">
                                                <input name="vin" id="vin" class="form-control valid checkTotalQuality"  type="text" maxlength="50" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left carInfo" id="roadtransportcertificatenumberColor"><span class="fcff">*</span>车辆道路&nbsp;&nbsp;<br />运输证号：</label>
                                            <div class="flex_right">
                                                <input name="roadtransportcertificatenumber" id="roadtransportcertificatenumber"
                                                       class="form-control valid ntoccVal" type="text"  maxlength="30" required>
                                            </div>
                                        </div>
                                    </div>
                                    <!--                                    <div class="col-md-3 col-sm-6" id="checkTotalQualityIsShow" style="display:none">-->
                                    <!--                                        <a class="btn btn-warning btn-rounded btn-xs" onclick="searchProvinceCode()"><i class="fa fa-search"></i>&nbsp;查询行政区域代码</a>-->
                                    <!--                                    </div>-->

                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span>  车辆载质量&nbsp;&nbsp;<br />(核定载质量)：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <input name="vehicletonnage" id="vehicletonnage" type="text" class="form-control checkVal"  align="left" maxlength="12" required>
                                                    <span class="input-group-addon">吨</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span>  满载车辆质&nbsp;&nbsp;<br />量(总质量)：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <input name="vehicleladenweight" id="vehicleladenweight" type="text" class="form-control checkVal" align="right" maxlength="12" onblur="checkTotalQuality()" required>
                                                    <span class="input-group-addon">吨</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt10">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left checkTotalQualityColor"><span class="fcff">*</span>  年检有效期：</label>
                                            <div class="flex_right checkTotalQuality">
                                                <input name="yearlyInspectionDate" id="yearlyInspectionDate" class="time-input form-control valid checkVal"  type="text" maxlength="16" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left">注册日期：</label>
                                            <div class="flex_right">
                                                <input name="registerdate" id="registerdate"
                                                       class="time-input form-control valid" type="text"  readonly>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt10">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ">证件有效期起：</label>
                                            <div class="flex_right">
                                                <input name="effectiveFrom" id="effectiveFrom"
                                                       class="time-input form-control valid ntoccVal" type="text"  readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ">证件有效期至：</label>
                                            <div class="flex_right">
                                                <input name="validUntil" id="validUntil"
                                                       class="time-input form-control valid ntoccVal" type="text"  readonly>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="row mt10">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left checkTotalQualityColor" >发证日期：</label>
                                            <div class="flex_right checkTotalQuality">
                                                <input name="issuedate" id="issuedate" class="form-control valid checkTotalQuality"  type="text" maxlength="16">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left">品牌型号：</label>
                                            <div class="flex_right">
                                                <input id="brand" name="brand" class="form-control" type="text" maxlength="30">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt10">
                                    <div class="col-md-6 col-sm-6" style="display: none">
                                        <div class="flex">
                                            <label class="flex_left">长x宽x高：</label>
                                            <div class="flex_right">
                                                <div class="form-group">
                                                    <div class="col-sm-7" style="padding-right: 0">
                                                        <div class="col-sm-6" style="padding-left: 0;padding-right: 0;">
                                                            <div class="input-group">
                                                                <input style="width: calc(100% - 10px)" id="length" name="length" type="text" class="form-control" align="right" maxlength="6" oninput="$.numberUtil.onlyNumber(this)" >
                                                                <span class="" style="display: inline-block;width: 10px;text-align: center;line-height: 30px">x</span>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-6" style="padding-left: 0;padding-right: 0;">
                                                            <div class="input-group">
                                                                <input style="width: calc(100% - 10px)" name="width" id="width" type="text" class="form-control" align="right" maxlength="6" oninput="$.numberUtil.onlyNumber(this)" >
                                                                <span class="" style="display: inline-block;width: 10px;text-align: center;line-height: 30px">x</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-5" style="padding-left: 0">
                                                        <div class="input-group">
                                                            <input name="height" id="height" type="text" class="form-control" align="right" maxlength="6" oninput="$.numberUtil.onlyNumber(this)">
                                                            <span class="input-group-addon">米</span>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6" style="display: none">
                                        <div class="flex">
                                            <label class="flex_left">司机：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <input id="driverId" name="driverId" class="form-control valid" type="hidden">
                                                    <input id="driverName" name="driverName" class="form-control valid" type="text"
                                                           onclick="selectDriverList()" readonly>
                                                    <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--  <div class="row">
                                      <div class="col-md-6 col-sm-6">
                                          <div class="flex">
                                              <label class="flex_left">挂车车牌号：</label>
                                              <div class="flex_right">
                                                  <input name="trailerlicenceno" id="trailerlicenceno" class="form-control valid" type="text" maxlength="32">
                                              </div>
                                          </div>
                                      </div>
                                      <div class="col-md-6 col-sm-6">
                                          <div class="flex">
                                              <label class="flex_left">挂车年检&nbsp;&nbsp;<br />有效期：</label>
                                              <div class="flex_right">
                                                  <input name="yearlyInspectionGDate" class="time-input form-control valid" type="text">
                                              </div>
                                          </div>
                                      </div>

                                  </div>-->
                                <div class="row">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left">发证机关：</label>
                                            <div class="flex_right">
                                                <input name="issuingorganizations" id="issuingorganizations" class="form-control valid"  type="text" maxlength="16">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--                                <div class="row">-->
                                <!--                                    <div class="col-md-3 col-sm-6">-->
                                <!--                                        <div class="form-group">-->
                                <!--                                            <label class="col-md-4 col-sm-4">发动机号：</label>-->
                                <!--                                            <div class="col-md-8 col-sm-8">-->
                                <!--                                                <input name="engineNo" class="form-control" type="text"-->
                                <!--                                                       maxlength="30">-->
                                <!--                                            </div>-->
                                <!--                                        </div>-->
                                <!--                                    </div>-->
                                <!--                                    <div class="col-md-3 col-sm-6">-->
                                <!--                                        <div class="form-group">-->
                                <!--                                            <label class="col-md-4 col-sm-4">行驶证号：</label>-->
                                <!--                                            <div class="col-md-8 col-sm-8">-->
                                <!--                                                <input name="drivingNumber" class="form-control valid" type="text" maxlength="20">-->
                                <!--                                            </div>-->
                                <!--                                        </div>-->
                                <!--                                    </div>-->
                                <!--                                    <div class="col-md-3 col-sm-6">-->
                                <!--                                        <div class="form-group">-->
                                <!--                                            <label class="col-md-4 col-sm-4">保险到期日期：</label>-->
                                <!--                                            <div class="col-md-8 col-sm-8">-->
                                <!--                                                <input name="filingDate" class="time-input form-control valid" type="text">-->
                                <!--                                            </div>-->
                                <!--                                        </div>-->
                                <!--                                    </div>-->

                                <!--                                </div>-->
                                <!--                                <div class="row">-->

                                <!--                                    <div class="col-md-3 col-sm-6">-->
                                <!--                                        <div class="form-group">-->
                                <!--                                            <label class="col-md-4 col-sm-4">司机手机：</label>-->
                                <!--                                            <div class="col-md-8 col-sm-8">-->
                                <!--                                                <input id="phone" name="driverMobile" class="form-control" type="text" maxlength="200" readonly>-->
                                <!--                                            </div>-->
                                <!--                                        </div>-->
                                <!--                                    </div>-->
                                <!--                                </div>-->

                                <!--                                <div class="row">-->
                                <!--                                    <div class="col-sm-12">-->
                                <!--                                        <div class="form-group">-->
                                <!--                                            <label class="col-md-1 col-sm-2">备注：</label>-->
                                <!--                                            <div class="col-md-11 col-sm-10">-->
                                <!--                                            <textarea name="memo" maxlength="100" class="form-control valid"-->
                                <!--                                                      rows="3"></textarea>-->
                                <!--                                            </div>-->
                                <!--                                        </div>-->
                                <!--                                    </div>-->
                                <!--                                </div>-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordionThree">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordionThree"
                               href="tabs_panels.html#collapseThree">业户信息</a>
                        </h5>
                    </div>
                    <div id="collapseThree" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div>
                                        <div class="form-group">
                                            <label class="col-sm-12">道路运输经营许可证:</label>
                                        </div>
                                    </div>
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <div class="">
                                                <input id="image4" class="form-control"
                                                       name="image4" type="file" multiple>
                                                <input id="tid4" name="tid4" type="hidden" >
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-8">
                                    <div class="row">
                                        <div class="col-md-4 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 承运人(所有人)：</label>
                                                <div class="flex_right">
                                                    <div class="input-group">
                                                        <input name="carriername" id="vehicleFeeName" class="form-control valid"
                                                               type="text" aria-required="true" maxlength="25" required>
                                                        <div class="input-group-btn">
                                                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                                <span class="caret"></span>
                                                            </button>
                                                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left ntocc" ><span>承运人类别：</span></label>
                                                <div class="flex_right">
                                                    <select name="carriertype" id="carriertype" class="form-control"
                                                            th:with="type=${@dict.getType('carriertype')}">
                                                        <option value="">&#45;&#45;请选择&#45;&#45;</option>
                                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                                th:value="${dict.dictValue}"></option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left" style="width: 80px">道路运输经&nbsp;&nbsp;<br />经营许可证：</label>
                                                <div class="flex_right">
                                                    <div class="">
                                                        <div class="">
                                                            <input name="permitnumber" id="permitnumber" class="form-control valid"
                                                                   type="text" maxlength="25" style="display: inline-block;">
                                                            <!-- <a class="btn btn-warning btn-rounded btn-xs" style="width: 90px" onclick="searchCarrierInfo()"><i class="fa fa-search"></i>&nbsp;查询本系统</a>-->
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left" style="width: 80px">承运人证照号：</label>
                                                <div class="flex_right">
                                                    <div class="">
                                                        <div class="">
                                                            <input name="unifiedsocialcreditldentifier" id="unifiedsocialcreditldentifier" class="form-control valid"
                                                                   type="text" maxlength="25" style="display: inline-block;">
                                                            <!-- <a class="btn btn-warning btn-rounded btn-xs" style="width: 90px" onclick="searchCarrierInfo()"><i class="fa fa-search"></i>&nbsp;查询本系统</a>-->
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">注册日期：</label>
                                                <div class="flex_right">
                                                    <input name="registrationdatetime" id="registrationdatetime"
                                                           class="time-input form-control valid" type="text"  readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <!--                                        <div class="col-md-3 col-sm-6">-->
                                        <!--                                            <div class="form-group">-->
                                        <!--                                                <label class="col-md-4 col-sm-4 ">承运人证照号：</label>-->
                                        <!--                                                <div class="col-md-8 col-sm-8">-->
                                        <!--                                                    <input name="unifiedsocialcreditldentifier" id="unifiedsocialcreditldentifier"-->
                                        <!--                                                           class="form-control valid"  type="text" maxlength="25">-->
                                        <!--                                                </div>-->
                                        <!--                                            </div>-->
                                        <!--                                        </div>-->
                                        <div class="col-md-4 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">有效期起：</label>
                                                <div class="flex_right">
                                                    <input name="effectiveFromCarrier" id="effectiveFromCarrier"
                                                           class="time-input form-control valid ntoccVal" type="text"  readonly>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="row mt10">
                                        <div class="col-md-4 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left" style="width: 80px">有效期至：</label>
                                                <div class="flex_right">
                                                    <input name="validUntilCarrier" id="validUntilCarrier"
                                                           class="time-input form-control valid ntoccVal" type="text"  readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left  ntocc">联系人名称：</label>
                                                <div class="flex_right">
                                                    <input name="contactname" id="contactname"
                                                           class="form-control valid" type="text"  maxlength="25">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left ntocc">联系人手机：</label>
                                                <div class="flex_right">
                                                    <input name="contactmobiletelephonenumber" id="contactmobiletelephonenumber"
                                                           class="form-control valid" type="text"  maxlength="11">
                                                </div>
                                            </div>
                                        </div>
                                        <!-- <div class="col-md-3 col-sm-6">
                                             <div class="form-group">
                                                 <label class="col-md-4 col-sm-4"><span style="color: red; ">是否黑名单：</span></label>
                                                 <div class="col-md-8 col-sm-8">
                                                     <select name="isblaclklist" id="isblaclklist" class="form-control valid">
                                                         <option value="0">否</option>
                                                         <option value="1">是</option>
                                                     </select>
                                                 </div>
                                             </div>
                                         </div>-->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--说明提示-->
            <!--            <div class="panel-group" id="accordionSix">-->
            <!--                <div class="panel panel-default">-->

            <!--                    <div id="collapseSix" class="panel-collapse collapse in">-->
            <!--                        <div class="panel-body">-->
            <!--                            <div class="err-tips-box">-->
            <!--                                <div class="err-tips">-->
            <!--                                    <h2><i class="fa  fa-warning"></i> 说明提示：</h2>-->
            <!--                                    <p>1、承运人类别：承运人（车辆所有人）是“个人”，承运人类别选择“个人承运人”；承运人（车辆所有人）是“企业”，承运人类别选择“企业承运人”。</p>-->
            <!--                                    <p>2、承运人证照号： 承运人（车辆所有人）是“个人”，承运人证照号填写承运商身份证号；承运人（车辆所有人）是“企业”，承运人证照号填写企业社会统一代码。</p>-->
            <!--                                    <p>3、注册日期：“车辆道路运输证”上的注册日期。</p>-->
            <!--                                    <p>4、车辆能源类型：按车辆行驶证填写，例如：一般货车车辆能源类型为“柴油”。</p>-->
            <!--                                    <p>5、如果车辆行驶证中车辆类型为牵引车，请填写挂车车牌号，挂车无需另行注册；车辆载质量(核定载质量)、满载车辆质量(总质量)填写挂车行驶证相应数据。</p>-->
            <!--                                    <p>6、总质量4.5 吨及以下普通货运车辆：需要必填（使用性质、车辆识别代号、发证机关、注册日期、发证日期）；-->
            <!--                                        "车辆道路运输证号"、“道路运输许经营许可证号” 可填为“车籍地6 位行政区域代码+000000”。</p>-->
            <!--                                </div>-->
            <!--                            </div>-->
            <!--                        </div>-->
            <!--                    </div>-->
            <!--                </div>-->
            <!--            </div>-->


            <!--证件上传 start-->
            <div class="panel-group" id="accordionTwo">
                <!--                <div class="panel panel-default">-->
                <!--                    <div class="panel-heading">-->
                <!--                        <h5 class="panel-title">-->
                <!--                            <a data-toggle="collapse" data-parent="#accordion"-->
                <!--                               href="tabs_panels.html#collapseTwo">证件上传</a>-->
                <!--                        </h5>-->
                <!--                    </div>-->
                <!--                    -->
                <!--                </div>-->
            </div>
            <!--证件上传 end-->
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
<!--        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler(0)"><i class="fa fa-check"></i>保-->
<!--            存-->
<!--        </button>&nbsp;-->
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler(1)"><i class="fa fa-check"></i>保存并提交审核
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">

    var prefix = ctx + "basic/car";
    var carrierPrefix = ctx + "carrier/carInfo";

    //字典表照片类型
    var carPicType = [[${@dict.getType('car_pic_type')}]];
    //车辆类型
    var carType = [[${@dict.getType('car_type')}]];

    //新增校验逻辑
    var flag;
    var array;

    //TODO 引入
    function findOneCar(value) {
        let carno = $("#carno").val();
        var regExp = /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[A-Z])|([A-Z]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领超]))$/;
        //正则验证中文
        if(regExp.test(carno)) {
            var data = {};
            data.carno = carno;
            $.ajax({
                url: carrierPrefix + "/findOne",
                type: "post",
                dataType: "json",
                data: data,
                success: function (result) {
                    console.log(result.data)
                    if(result.data != null && result.data != '') {
                        //车辆id
                        let carId = result.data.carId;
                        $("#carId").val(carId)
                        //所属省
                        //初始化省市区
                        let provinceCode = result.data.provinceCode;
                        $("#provinceCode").val(provinceCode);
                        //车长
                        let carLengthId = result.data.carLengthId;
                        $("#carLengthId").val(carLengthId);
       
                        //车型
                        let vehicleclassificationcode = result.data.vehicleclassificationcode;
                        $("#vehicleclassificationcode").val(vehicleclassificationcode);
                        //车牌类型
                        let licenseplatetypecode = result.data.licenseplatetypecode;
                        $("#licenseplatetypecode").val(licenseplatetypecode);
                        //能源类型
                        let vehicleenergytype = result.data.vehicleenergytype;
                        $("#vehicleenergytype").val(vehicleenergytype);

                        //营运车辆类型代码
                        let operationType = result.data.operationType;
                        $("#operationType").val(operationType);
                        //车牌颜色
                        let vehiclelicenseplatecolor = result.data.vehiclelicenseplatecolor;
                        $("#vehiclelicenseplatecolor").val(vehiclelicenseplatecolor);
                        //车轴数
                        let axlenum = result.data.axlenum;
                        $("#axlenum").val(axlenum);


                        //车辆识别代号
                        $("#vin").val(result.data.vin)
                        //车辆道路运输证号
                        $("#roadtransportcertificatenumber").val(result.data.roadtransportcertificatenumber)
                        //车辆载质量(核定载质量)
                        $("#vehicletonnage").val(result.data.vehicletonnage);
                        // 满载车辆质量(总质量)
                        $("#vehicleladenweight").val(result.data.vehicleladenweight);
                        //年检有效期
                        if(result.data.yearlyInspectionDate != '' && result.data.yearlyInspectionDate != null) {
                            $("#yearlyInspectionDate").val(result.data.yearlyInspectionDate.substring(0,10));
                        }

                        //发证机关
                        $("#issuingorganizations").val(result.data.issuingorganizations)
                        //品牌型号
                        $("#brand").val(result.data.brand)
                        //有效期起  至
                        if(result.data.effectiveFrom != '' && result.data.effectiveFrom != null) {
                            $("#effectiveFrom").val(result.data.effectiveFrom.substring(0,10))
                        }
                        if(result.data.validUntil != '' && result.data.validUntil != null) {
                            $("#validUntil").val(result.data.validUntil.substring(0,10))
                        }
                        //注册日期  发证日期
                        if(result.data.registerdate != '' && result.data.registerdate != null) {
                            $("#registerdate").val(result.data.registerdate.substring(0,10))
                        }
                        if(result.data.issuedate != '' && result.data.issuedate != null) {
                            $("#issuedate").val(result.data.issuedate.substring(0,10))
                        }

                        //承运人(所有人)
                        $("#vehicleFeeName").val(result.data.actCarrierName)
                        if(result.data.carrierinfo != "" && result.data.carrierinfo != null) {
                            //承运人类别
                            let carriertype = result.data.carrierinfo.carriertype;
                            $("#carriertype option:contains('"+carriertype+"')").attr("selected",true);
                            //道路运输经营许可证
                            $("#permitnumber").val(result.data.carrierinfo.permitnumber)
                            //承运人证照号
                            $("#unifiedsocialcreditldentifier").val(result.data.carrierinfo.unifiedsocialcreditldentifier)
                            //联系人名称  手机
                            $("#contactname").val(result.data.carrierinfo.contactname)
                            $("#contactmobiletelephonenumber").val(result.data.carrierinfo.contactmobiletelephonenumber)
                            //有效期起  至
                            if(result.data.carrierinfo.effectiveFrom != '' && result.data.carrierinfo.effectiveFrom != null) {
                                $("#effectiveFromCarrier").val(result.data.carrierinfo.effectiveFrom.substring(0,10))
                            }
                            if(result.data.carrierinfo.validUntil != '' && result.data.carrierinfo.validUntil != null) {
                                $("#validUntilCarrier").val(result.data.carrierinfo.validUntil.substring(0,10))
                            }
                        }

                        //附件
                        let carPicList = result.data.sysUploadFileVOS;
                        for(var i=0 ; i< carPicList.length ; i++){
                            var picType = carPicList[i].picType;
                            $("#tid"+picType).val(carPicList[i].appendixId);
                            var publishFlag = "cmt_" + picType;
                            var param = {
                                maxFileCount: 1,
                                publish: publishFlag,  //用于绑定下一步方法
                                fileType: null//文件类型
                            };
                            var tid = "tid" + picType;
                            var imageId = "image" + picType;
                            $('#'+imageId).fileinput('destroy');
                            $.file.loadEditFilesOCR(imageId, tid, new Array(carPicList[i]), param);
                        }
                        //业户附件
                        let permitnumberFileList = result.data.permitnumberFileList;
                        if(permitnumberFileList != null) {
                            var param = {
                                maxFileCount: 0,
                                publish: "cmt_4",  //用于绑定下一步方法
                                fileType: null//文件类型
                            };
                            var tid = "tid4";
                            var imageId = "image4";
                            $('#'+imageId).fileinput('destroy');
                            $.file.loadEditFilesOCR(imageId, tid, permitnumberFileList, param);
                        }


                        //承运商
                        /*let carrierList = result.data.carrierList;
                        if(carrierList.length != 0) {
                            let carrierId = '';
                            $.each(carrierList, function (i, item) {
                                if(carrierId != '') {
                                    carrierId = carrierId + ',' + carrierList[i].carrierId;
                                }else {
                                    carrierId = carrierList[i].carrierId;
                                }
                            })
                            $("#carrierId").val(carrierId);
                        }*/
                    }
                }
            });
        }
    }

    //根据车牌号码和车辆颜色查询车辆信息
    function searchCarInfo() {

        var checkCarNo = /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领超]))$/;

        var carno = $("#carno").val();
        var color = $("#vehiclelicenseplatecolor").val();
        var vehicleFeeName = $("#vehicleFeeName").val();//无车承运人
        var provinceCode = $("#provinceCode").val();
        if(!checkCarNo.test(carno)){
            $.modal.alertWarning("请输入正确车牌号");
            return false;
        }
        if(color == null || color == ""){
            $.modal.alertWarning("请选择车牌颜色 ");
            return false;
        }
        if(color != '1' && color != '2'){
            $.modal.alertWarning("只有车牌颜色为蓝色或黄色的车辆可以查询车辆信息");
            return false;
        }
        if($("#isVehicleFreeCarrier").val()=='1'){
            if(vehicleFeeName == null || vehicleFeeName == ""){
                $.modal.alertWarning("请输入无车承运人");
                return false;
            }
        }
        if(provinceCode == null || provinceCode == ""){
            $.modal.alertWarning("请选择所属省");
            return false;
        }

        var data = {};
        data.carno = carno;
        data.color = color;
        data.vehicleFeeName = vehicleFeeName;
        data.provinceCode = provinceCode;

        $.ajax({
            url : prefix + "/getCarInfo",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if(result.code == web_status.SUCCESS){
                    var data = result.data;
                    var msg = "<br>品牌型号："+data.brand
                        +"<br>车架号/车辆识别代码："+data.frameNo
                        +"<br>车辆类型："+data.vclTpNm
                        +"<br>车主："+data.cmpNm
                        +"<br>联系人："+data.vclWnrNm
                        +"<br>联系人手机："+data.vclWnrPhn
                        +"<br>车辆载质量："+data.vehicletonnage
                        +"<br>满载车辆质量："+data.vehicleladenweight
                        +"<br>车轴数："+data.vehicleAxis
                        +"<br>长度："+data.length
                        +"<br>宽度："+data.width
                        +"<br>高度："+data.height
                        +"<br>行驶证发证日期："+data.drvLicDt
                        +"<br>行驶证有效年数："+data.drvLicVdy
                        +"<br>车辆道路运输证号："+data.roadTransport
                        +"<br>许可证有效期："+data.vdtTm
                        +"<br>车辆所有人道路运输经营许可证："+data.roadLicense;
                    layer.confirm("<label style='font-weight: 600'>是否将获取的数据填入文本框？</label>"+msg,{
                        btn:["确认","取消"]
                    },function (index, layero) {
                        //修改信息
                        $("#brand").val(data.brand);
                        $("#frameNo").val(data.frameNo);
                        $("#vin").val(data.frameNo);
                        $("#axlenum").val(data.vehicleAxis);
                        //车辆类型
                        inputCarType(data.vclTpNm);
                        //车载质量大于0取车载质量 否则取满载车辆质量
                        // if(data.vehicletonnage > 0 ){
                        //     $("#vehicletonnage").val(data.vehicletonnage);
                        // }else{
                        //     $("#vehicletonnage").val(data.vehicleladenweight);
                        // }
                        $("#vehicletonnage").val(data.vehicletonnage);
                        $("#vehicleladenweight").val(data.vehicleladenweight);
                        $("#length").val(data.length);
                        $("#width").val(data.width);
                        $("#height").val(data.height);
                        //初次登记日期 20200517
                        var drvLicDt = data.drvLicDt
                        if($.common.isNotEmpty(drvLicDt)){
                            var year = drvLicDt.substring(0,4);
                            var month = drvLicDt.substring(4,6);
                            var day = drvLicDt.substring(6,8);
                            $("#licDate").val(year+"-"+month+"-"+day);
                            $("#issuedate").val(year+"-"+month+"-"+day);
                        }
                        //许可证有效期
                        $("#expiryDate").val(data.vdtTm);
                        $("#roadtransportcertificatenumber").val(data.roadTransport);
                        $("#roadTransportBusinessLicense").val(data.roadLicense);
                        //联系人
                        $("#contactname").val(data.vclWnrNm);
                        //联系人手机
                        $("#contactmobiletelephonenumber").val(data.vclWnrPhn);
                        //车辆所有人道路运输经营许可证
                        $("#permitnumber").val(data.roadLicense);

                        layer.close(index);
                    },function (index) {
                        layer.close(index);
                    });
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
    }

    /**
     * 查询承运人信息
     */
    function searchCarrierInfo(){
        //无车承运人
        var vehicleFeeName = $("#vehicleFeeName").val();
        //车辆所有人道路运输经营许可证
        var permitnumber = $("#permitnumber").val();
        if(vehicleFeeName == null || vehicleFeeName == ""){
            $.modal.alertWarning("请填写承运人");
            return false;
        }
        if(permitnumber == null || permitnumber == ""){
            $.modal.alertWarning("请填写车辆所有人道路运输经营许可证");
            return false;
        }
        var data = {};
        data.carriername = vehicleFeeName;
        data.permitnumber = permitnumber;

        $.ajax({
            url : prefix + "/getCarrierInfo",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if(result.code == web_status.SUCCESS){
                    var data = result.data;
                    var carriertype = data.carriertype == 1 ? '个体承运人':'企业承运人'
                    var msg = "<br>承运人："+data.carriername
                        +"<br>车辆所有人道路运输经营许可证："+data.permitnumber
                        +"<br>无车承运人类别："+carriertype
                        +"<br>承运人证照号："+data.unifiedsocialcreditldentifier
                        +"<br>联系人："+data.contactname
                        +"<br>联系人手机："+data.contactmobiletelephonenumber
                    //+"<br>注册时间："+data.registrationdatetime

                    layer.confirm("<label style='font-weight: 600'>是否将获取的数据填入文本框？</label>"+msg,{
                        btn:["确认","取消"]
                    },function (index, layero) {
                        //修改信息
                        $("#vehicleFeeName").val(data.carriername);
                        $("#permitnumber").val(data.permitnumber);
                        $("#carriertype").val(data.carriertype);
                        $("#unifiedsocialcreditldentifier").val(data.unifiedsocialcreditldentifier);
                        $("#contactname").val(data.contactname);
                        $("#contactmobiletelephonenumber").val(data.contactmobiletelephonenumber);
                        $("#registrationdatetime").val(data.registrationdatetime);


                        layer.close(index);
                    },function (index) {
                        layer.close(index);
                    });
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });


    }

    /**
     *  带入车辆类型
     */
    function inputCarType(vclTpNm){
        for(var i = 0 ;i<carType.length;i++){
            if(vclTpNm === carType[i].dictLabel){
                $("#vehicleclassificationcode").val(carType[i].dictValue);
                //设置车型名称
                setCarTypeName();
            }
        }
    }

    /**
     * 总质量 4.5 吨及以下且符合无车承运人
     * 普通货运车辆必填，按照机动车行驶证填写。
     */
    function checkTotalQuality() {
        //是否符合无车承运人
        var isVehicleFreeCarrier = $("#isVehicleFreeCarrier").val();
        //满载车辆质量
        var vehicleladenweight = parseFloat($("#vehicleladenweight").val());

        if(isVehicleFreeCarrier == 1 && vehicleladenweight <= 4.5){
            $(".checkTotalQualityColor").css("color","red");
            $(".checkTotalQuality").attr("required","true");
            //查询行政区域代码 显示
            $("#checkTotalQualityIsShow").show();
        }else{
            $(".checkTotalQualityColor").css("color","");
            $(".checkTotalQuality").removeAttr("required");

            //查询行政区域代码 隐藏
            $("#checkTotalQualityIsShow").hide();
        }
    }

    function submitHandler(flag) {

        if(flag == 0){
            $(".fcff").css("display","none");
            $(".checkVal").removeAttr("required");
        }else{
            $(".fcff").css("display","-webkit-inline-box");
            $(".checkVal").attr("required","true");
            var roadtransportcertificatenumber = $("#roadtransportcertificatenumber").val();
            var vin = $("#vin").val();
            // if(roadtransportcertificatenumber == '' && vin == ''){
            //     $.modal.alertWarning("车辆道路运输证号或车辆识别代号必填一个!");
            //     return false;
            // }
            if($("#vehicletonnage").val() == 0 || $("#vehicleladenweight").val() == 0 ){
                $.modal.alertWarning("车辆载质量和车辆满载质量不能为0!");
                return false;
            }
        }

        //判断车辆类型是否为挂车
        /*var operationType = $("#operationType").val();
        if(operationType == 'Q11' || operationType == 'Q12' || operationType == 'Q21'
        || operationType == 'Q22' || operationType == 'Q31' || operationType == 'Q32'){ if($("#trailerlicenceno").val() == "" || $("#trailerlicenceno").val() == null
                || $("#image5").val() == "" || $("#image5").val() == null){
                $.modal.alertWarning("挂车请填写挂车车牌号并上传挂车行驶证!");
                return false;
             }
        }*/

        if ($.validate.form()) {
            //提前加遮罩层
            $.modal.loading("正在处理中，请稍后...");
            var data = $('#form-car-add').serializeArray();
            data.push({"name": "submitFlag", "value": flag});
            if($("#carId").val() != '') {
                $.operate.saveTab(carrierPrefix + "/pullInCar", data);
            }else  {
                $.operate.saveTab(prefix + "/add", data);
            }
        }
    }

    function cmtFile_1(){
        var data = {};
        data.tid = $("#tid1").val();
        data.side = 1;
        console.log("行驶证识别正面开始")
        $.ajax({
            url : ctx + "common/ocr/vehicleByTid",
            method : 'POST',
            data : data,
            success:function (result) {
                console.log(result);
                if(result.code == 0){
                    //车牌号码
                    $("#carno").val(result.data.plate_num);
                    //车辆识别代码
                    $("#vin").val(result.data.vin);
                    //所有人
                    $("#owner").val(result.data.owner);
                    $("#carriername").val(result.data.owner);
                    //营运车辆类型
                    var vehicle_type = result.data.vehicle_type;
                    $("#operationType option:contains('"+vehicle_type+"')").attr("selected",true);
                    //注册日期
                    var register_date = result.data.register_date;
                    if(register_date != null){
                        $("#registerdate").val(register_date.substring(0,4) + "-" + register_date.substring(4,6) + "-" + register_date.substring(6,8));
                    }
                    //发证日期
                    var issue_date = result.data.issue_date;
                    if(issue_date != null){
                        $("#issuedate").val(issue_date.substring(0,4) + "-" + issue_date.substring(4,6) + "-" + issue_date.substring(6,8));
                    }
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
        console.log("行驶证正面识别结束")
        console.log("行驶证识别反面开始")
        data.side = 2;
        console.log("行驶证识别正面开始")
        $.ajax({
            url : ctx + "common/ocr/vehicleByTid",
            method : 'POST',
            data : data,
            success:function (result) {
                console.log(result);
                if(result.code == 0){
                    //能源类型
                    var energy_type = result.data.energy_type;
                    $("#vehicleenergytype option:contains('"+energy_type+"')").attr("selected",true);
                    //车辆载质量
                    var vehicleTonnage = result.data.approved_load;
                    if(vehicleTonnage != ""){
                        var vehicleTonnage = parseFloat(vehicleTonnage.substring(0,vehicleTonnage.length-2)/1000).toFixed(2);
                        if(myIsNaN(vehicleTonnage)){
                            $("#vehicleTonnage").val(vehicleTonnage);
                        }
                    }
                    //车辆总质量
                    var vehicleLadenWeight = result.data.gross_mass;
                    if(vehicleLadenWeight != ""){
                        var vehicleLadenWeight = parseFloat(vehicleLadenWeight.substring(0,vehicleTonnage.length-2)/1000).toFixed(2);
                        if(myIsNaN(vehicleLadenWeight)){
                            $("#vehicleLadenWeight").val(vehicleLadenWeight);
                        }
                    }
                    //长宽高
                    var size = result.data.overall_dimension.split("X") //外廓尺寸
                    if(size.length == 3) {
                        var length = parseFloat(size[0] / 1000).toFixed(2);
                        if(myIsNaN(length)){
                            $("#length").val(length);
                        }
                        var width = parseFloat(size[1] / 1000).toFixed(2);
                        if(myIsNaN(width)) {
                            $("#width").val(width);
                        }
                        var heigth = parseFloat((size[2].substring(0,size[2].length-2)) / 1000).toFixed(2);
                        if(myIsNaN(heigth)){
                            $("#height").val(heigth);
                        }
                    }
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
        console.log("行驶证反面识别结束")
    }


    //新增提交
    function commit() {
        var data = $('#form-car-add').serializeArray();
        data.push({"name": "submitFlag", "value": "1"});
        $.operate.saveTab(prefix + "/add", data);
    }

    /**
     * 设置车牌类型代码
     */
    function selectLicenseplatetypecode(value){
        //黄色对应大型汽车号牌
        if(value == 2){
            $("#licenseplatetypecode").val("01");
        }
        //蓝色对应小型汽车号牌
        if(value == 1){
            $("#licenseplatetypecode").val("02");
        }
        //其他对应其他
        if(value == 9){
            $("#licenseplatetypecode").val("99");
        }
    }

    /**
     * 设置车长名称
     */
    function setCarLenName() {
        $("#carLenName").val($("#carLengthId option:selected").text());
    }


    /**
     * 设置车型名称
     */
    function setCarTypeName() {
        $("#carTypeName").val($("#vehicleclassificationcode option:selected").text());
    }

    //选择承运人
    function selectCarrier() {
        var PREFIX = ctx + "basic/carrier";

        //车辆性质
        var carProp = $("#carProp").val();
        if(carProp==""){
            $.modal.alertWarning("请先选择车辆性质!");
            return false;
        }
        //自有车辆
        if(carProp=="1"){
            //字典表承运商类型
            $.modal.open("选择承运人", PREFIX + "/findCarrier?carrType=0");
        }
        //外协车辆
        if(carProp=="2"){
            $.modal.open("选择承运人", PREFIX + "/findCarrier?carrType=1,2,3,4,5");
        }

    }

    //选择司机
    function selectDriverList() {
        //车辆性质
        var carProp = $("#carProp").val();
        if(carProp==""){
            $.modal.alertWarning("请先选择车辆性质!");
            return false;
        }

        var carrierId = $("#carrierId").val();
        var PREFIX = ctx + "basic/driver";

        //自有车辆，一个司机只能在一辆车
        if(carProp=="1"){
            //字典表承运商类型
            $.modal.open("选择司机", PREFIX + "/checkboxSelectDriver?type=2&carrierId="+ carrierId, 1000, '');
        }
        //外协车辆
        if(carProp=="2"){
            $.modal.open("选择司机", PREFIX + "/checkboxSelectDriver?type=1&carrierId="+ carrierId, 1000, '');
        }
    }


    function myIsNaN(value) {
        return !isNaN(value);
    }


    //初始化
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#expiryDate',
                type: 'date',
                trigger: 'click'
            });
        });

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#licDate',
                type: 'date',
                trigger: 'click'
            });
        });

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#issuedate',
                type: 'date',
                trigger: 'click'
            });
        });


        //初始化省市区
        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            success: function (result) {
                for (var i in result) {
                    $('#provinceCode').append("<option value='" + result[i].PROVINCE_CODE + "'>" + result[i].PROVINCE_NAME + "</option>")
                }
            }
        });

        //循环字典表中图片类型，初始化图片上传区域
        for (var i = 0; i < carPicType.length; i++) {
            var dictValue = carPicType[i].dictValue;
            var maxFileCount = 0 ;//最大上传文件数
            //道路运输证设置只能上传一张
            if(dictValue == 2){
                maxFileCount = 1;
            }
            var publishFlag = "cmt_" + dictValue;
            var picParam = {
                maxFileCount:maxFileCount,
                publish: publishFlag,  //用于绑定下一步方法
                fileType: null//文件类型
            };
            var tid = "tid" + dictValue;
            var imageId = "image" + dictValue;
            $.file.initAddFilesOCR(imageId, tid, picParam);
        }

        //初始化 车辆所有人道路运输经营许可证
        var picParam = {
            maxFileCount:0,
            publish: "cmt_4",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        var tid = "tid4";
        var imageId = "image4";
        $.file.initAddFilesOCR(imageId, tid, picParam);

        //符合无车承运人时：车牌颜色，满载车辆质量，车辆载质量，道路运输证号 必填且标红
        $('#isVehicleFreeCarrier').change(function(){
            var isVehicleFreeCarrier  = $(this).find(":selected").text();
            if(isVehicleFreeCarrier == '是'){
                $('.ntocc').css("color","red");
                //车牌颜色
                $("#vehiclelicenseplatecolor").attr("required","true");
                //满载车辆质量
                //$("#vehicleladenweight").attr("required","true");
                //车辆载质量
                //$("#vehicletonnage").attr("required","true");
                //道路运输证号
                //$("#roadtransportcertificatenumber").attr("required","true");
                //车牌类型
                $("#licenseplatetypecode").attr("required","true");
                //经营许可证
                //$("#roadTransportBusinessLicense").attr("required","true");

                //承运人
                //$("#vehicleFeeName").attr("required","true");
                //无车承运人类别
                $("#carriertype").attr("required","true");
                //承运人证照号
                //$("#unifiedsocialcreditldentifier").attr("required","true");
                //道路运输许经营许可证号
                //$("#permitnumber").attr("required","true");
                //联系人名称
                $("#contactname").attr("required","true");
                //联系人手机
                $("#contactmobiletelephonenumber").attr("required","true");
                //注册时间
                //$("#registrationdatetime").attr("required","true");
                //车轴数
                $("#axlenum").attr("required","true");
                //车辆能源类型
                //$("#vehicleenergytype").attr("required","true");
                //营运车辆类型代码
                //$("#operationType").attr("required","true");

                //有效期起
                //$("#effectiveFrom").attr("required","true");
                //有效期至
                //$("#validUntil").attr("required","true");

                //有效期起承运人
                //$("#effectiveFromCarrier").attr("required","true");
                //有效期至
                //$("#validUntilCarrier").attr("required","true");

            }
            if(isVehicleFreeCarrier == '否'){
                $('.ntocc').css("color","inherit");
                //车牌颜色
                $("#vehiclelicenseplatecolor").removeAttr("required");
                $("#form-car-add").validate().element($("#vehiclelicenseplatecolor"));
                //满载车辆质量
                //$("#vehicleladenweight").removeAttr("required");
                //$("#form-car-add").validate().element($("#vehicleladenweight"));
                //车辆载质量
                //$("#vehicletonnage").removeAttr("required");
                //$("#form-car-add").validate().element($("#vehicletonnage"));
                //道路运输证字号
                //$("#roadtransportcertificatenumber").removeAttr("required");
                //$("#form-car-add").validate().element($("#roadtransportcertificatenumber"));
                //车牌类型
                $("#licenseplatetypecode").removeAttr("required");
                $("#form-car-add").validate().element($("#licenseplatetypecode"));
                //经营许可证
                //$("#roadTransportBusinessLicense").removeAttr("required");
                //$("#form-car-add").validate().element($("#roadTransportBusinessLicense"));

                //承运人
                $("#vehicleFeeName").removeAttr("required");
                $("#form-car-add").validate().element($("#vehicleFeeName"));
                //无车承运人类别
                $("#carriertype").removeAttr("required");
                $("#form-car-add").validate().element($("#carriertype"));
                //承运人证照号
                $("#unifiedsocialcreditldentifier").removeAttr("required");
                $("#form-car-add").validate().element($("#unifiedsocialcreditldentifier"));
                //道路运输许经营许可证号
                $("#permitnumber").removeAttr("required");
                $("#form-car-add").validate().element($("#permitnumber"));
                //联系人名称
                $("#contactname").removeAttr("required");
                $("#form-car-add").validate().element($("#contactname"));
                //联系人手机
                $("#contactmobiletelephonenumber").removeAttr("required");
                $("#form-car-add").validate().element($("#contactmobiletelephonenumber"));
                //注册时间
                $("#registrationdatetime").removeAttr("required");
                $("#form-car-add").validate().element($("#registrationdatetime"));

                //车轴数
                $("#axlenum").removeAttr("required");
                $("#form-car-add").validate().element($("#axlenum"));
                //车辆能源类型
                //$("#vehicleenergytype").removeAttr("required");
                //$("#form-car-add").validate().element($("#vehicleenergytype"));

                //营运车辆类型代码
                $("#operationType").removeAttr("required");
                $("#form-car-add").validate().element($("#operationType"));

                //有效期起
                $("#effectiveFrom").removeAttr("required");
                $("#form-car-add").validate().element($("#effectiveFrom"));

                //有效期至
                $("#validUntil").removeAttr("required");
                $("#form-car-add").validate().element($("#validUntil"));

                //有效期起承运人
                $("#effectiveFromCarrier").removeAttr("required");
                $("#form-car-add").validate().element($("#effectiveFromCarrier"));

                //有效期至
                $("#validUntilCarrier").removeAttr("required");
                $("#form-car-add").validate().element($("#validUntilCarrier"));

            }
        });

        $("#form-car-add").validate({
            onkeyup: false,
            rules: {
                /*carno: {
                    remote: {
                        url: prefix + "/checkCarNo",
                        type: "post",
                        dataType: "json",
                        data: {
                            carNo: function () {
                                return $.common.trim($("#carno").val());
                            }
                        },
                        dataFilter: function (data, type) {
                            return $.validate.unique(data);
                        }
                    },
                    checkCarNo: true
                },*/
                // unifiedsocialcreditldentifier:{
                //     remote: {
                //         url: prefix + "/checkUnifiedsocialcreditldentifier",
                //         type: "post",
                //         dataType: "json",
                //         data: {
                //             unifiedsocialcreditldentifier: function () {
                //                 return $.common.trim($("#unifiedsocialcreditldentifier").val());
                //             }
                //         },
                //         dataFilter: function (data, type) {
                //             return $.validate.unique(data);
                //         }
                //     },
                // },
                contactmobiletelephonenumber:{
                    isPhone: true
                },
                length:{
                    max:99
                },
                width:{
                    max:99
                },
                height:{
                    max:99
                },
                vehicletonnage:{
                    max:999999
                },
                vehicleladenweight:{
                    max:999999
                },
                trailerlicenceno:{
                    checkCarNo: true
                }
            },
            messages: {
                // unifiedsocialcreditldentifier: {
                //     remote: "该统一社会信用代码已经存在"
                // },
                carno: {
                    remote: "该车牌号已经存在"
                },
                length:{
                    max:"最大值99"
                },
                width:{
                    max:"最大值99"
                },
                height:{
                    max:"最大值99"
                },
                vehicletonnage:{
                    max:"最大值999999"
                },
                vehicleladenweight:{
                    max:"最大值999999"
                },
            },
            focusCleanup: true
        });

        $.validator.addMethod("checkCarNo",function(value,element,params){
            /*
             * 纯电：D,A,B,C,E 优先用D
             * 非纯电：F,G,H,J,K 优先用F
             */
            var checkCarNo = /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[A-Z])|([A-Z]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领超]))$/;
            return this.optional(element)||(checkCarNo.test(value));
        },"*请输入正确的车牌号！");

        jQuery.subscribe("cmt_1",cmtFile_1);
    });

    /**
     * 查询行政区域代码
     */
    function searchProvinceCode() {
        //所属省
        var provinceCode = $("#provinceCode").val();
        if(provinceCode == null || provinceCode == ""){
            $.modal.alertWarning("请选择所属省");
            return false;
        }
        var msg = "<br>行政区域代码："+provinceCode;

        layer.confirm("<label style='font-weight: 600'>是否将获取的数据填入文本框？</label>"+msg,{
            btn:["确认","取消"]
        },function (index, layero) {
            $("#roadtransportcertificatenumber").val(provinceCode+"000000");
            layer.close(index);
        },function (index) {
            layer.close(index);
        });
    }


    /**
     * 关键字提示查询
     */
    $("#vehicleFeeName").bsSuggest('init', {
        url: ctx + "basic/personalDriver/findCarrierInfo?keyword=",
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "carriername",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["carriername","unifiedsocialcreditldentifier"],
        effectiveFieldsAlias: {"carriername":"承运人","unifiedsocialcreditldentifier":"身份证"},
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#vehicleFeeName").val(data.carriername);
        $("#carriertype").val(data.carriertype);
        $("#isVehicleFreeCarrier").val(data.isVehicleFreeCarrier);
        $("#permitnumber").val(data.permitnumber);
        $("#unifiedsocialcreditldentifier").val(data.unifiedsocialcreditldentifier);
        $("#registrationdatetime").val(data.registrationdatetime);
        $("#effectiveFromCarrier").val(data.effectiveFrom);
        $("#validUntilCarrier").val(data.validUntil);
        $("#contactname").val(data.contactname);
        $("#contactmobiletelephonenumber").val(data.contactmobiletelephonenumber);
    })
</script>
</body>

</html>