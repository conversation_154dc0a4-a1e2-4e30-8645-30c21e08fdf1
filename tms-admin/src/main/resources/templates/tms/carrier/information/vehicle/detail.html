<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车辆管理-修改')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: jasny-bootstrap-css" />
</head>

<body>
<div class="form-content">
    <form id="form-car-add" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">车牌号：</label>
                                    <div class="col-md-8 col-sm-8"  th:text="*{car.carno}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">车长：</label>
                                    <div class="col-md-8 col-sm-8" th:if="${car.carLengthId !='' and car.carLengthId!=null}"
                                         th:text="${@dict.getLabel('car_len',car.carLengthId)}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">车型：</label>
                                    <div class="col-md-8 col-sm-8" th:if="${car.vehicleclassificationcode !='' and car.vehicleclassificationcode!=null}"
                                         th:text="${@dict.getLabel('car_type',car.vehicleclassificationcode)}">
                                    </div>
                                </div>
                            </div>

                            <!--<div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4"><span
                                            >车辆性质：</span></label>
                                    <div class="col-md-8 col-sm-8" th:if="${car.carProp !='' and car.carProp!=null}"
                                         th:text="${@dict.getLabel('car_nature',car.carProp)}">
                                    </div>
                                </div>
                            </div>-->

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">牌照类型代码：</label>
                                    <div class="col-md-8 col-sm-8" th:if="${car.licenseplatetypecode !='' and car.licenseplatetypecode!=null}"
                                         th:text="${@dict.getLabel('car_plate_type',car.licenseplatetypecode)}">
                                    </div>
                                </div>
                            </div>


                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">车牌颜色：</label>
                                    <div class="col-md-8 col-sm-8" th:if="${car.vehiclelicenseplatecolor !='' and car.vehiclelicenseplatecolor!=null}"
                                         th:text="${@dict.getLabel('car_plate_color',car.vehiclelicenseplatecolor)}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">道路运输证字号：</label>
                                    <div class="col-md-8 col-sm-8"  th:text="*{car.roadtransportcertificatenumber}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--证件上传 start-->
        <div class="panel-group" id="accordionTwo">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">证件上传</a>
                    </h5>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body" th:with="type=${@dict.getType('car_pic_type')}">
                        <div class="row" th:each="dict:${type}" th:if="${dict.dictValue != '3'}">
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label class="col-sm-12" th:text="${dict.dictLabel}+'：'">
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <div class="col-sm-7">
                                        <div style="display:inline-block" th:each="pic:${carPic}"
                                             th:if="${pic.picType==dict.dictValue and pic.filePath!=null}">
                                            <img style="width:70px; height:50px" modal="zoomImg" th:src="@{${pic.filePath}}"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
       <!-- <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;-->
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    //初始化
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
    });
</script>
</body>

</html>