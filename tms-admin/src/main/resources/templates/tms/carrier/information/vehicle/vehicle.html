<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车辆信息维护')"/>

</head>
<style>
    .sm_icon{
        width: 18px;
        height: 18px;
        display: inline-block;
        line-height: 18px;
        text-align: center;
        font-size: 16px;
        color: #ed5565;
    }
    .sm_text{
        display: inline-block;
        margin-left: 10px;
        line-height: 30px;
        color: #ed5565;
    }
    .sm_btn{
        display: inline-block;
        width: 100px;
        height: 30px;
        line-height: 30px;
        margin-left: 20px;
        text-align: center;
        background: #fde6de;
        border: 1px #ff9999 solid;
        color: #ed5565;
        border-radius: 20px;
        cursor: pointer;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row sm">
                    <span class="sm_icon">
                        <i class="fa fa-bell-o"></i>
                    </span>
                    <span class="sm_text" id="warn_msg">
                        共<span class="fcff3" th:text="${temporaryCount}"></span>辆车辆证件已到期或未上传，请及时更新
                    </span>
                    <span class="sm_btn" onclick="selectTemporary()">
                        <input type="hidden" id="temporary" name="temporary"/>
                        快速筛选
                    </span>
                </div>
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">车牌号：</label>
                            <div class="col-sm-8">
                                <input name="carno" class="form-control" th:placeholder="请输入车牌号" type="text">
                            </div>
                        </div>
                    </div>
                    <input id="hiddenText" type="text" style="display:none" />

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">车辆类型：</label>
                            <div class="col-sm-8">
                                <select name="vehicleclassificationcode" class="form-control m-b" th:with="type=${@dict.getType('car_type')}">
                                    <option value="">-- 请选择 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">

                    </div>

                    <div class="col-md-3 col-sm-6">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" id="addCar" onclick="$.operate.addTab()" shiro:hasPermission="carrier:carInfo:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" id="removeCar" onclick="$.operate.removeAll()" shiro:hasPermission="	carrier:carInfo:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "carrier/carInfo";
    //权限
    var editFlag = [[${@permission.hasPermi('carrier:carInfo:edit')}]];
    var detailFlag = [[${@permission.hasPermi('carrier:carInfo:detail')}]];

    var carType = [[${@dict.getType('car_type')}]];//车辆类型
    var carNature = [[${@dict.getType('car_nature')}]];//车辆性质
    var carStatus = [[${@dict.getType('car_status')}]];//车辆状态
    var carPlateColor = [[${@dict.getType('car_plate_color')}]];//车牌颜色
    var carLen = [[${@dict.getType('car_len')}]];//车长

    var carrId = [[${carrId}]];

    if(null==carrId) {
        $("#addCar").addClass("disabled");
        $("#removeCar").addClass("disabled");
        $("#removeCar").removeClass("multiple");
    }


    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        var options = {
            url: prefix + "/list",
            createUrl:  prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            showToggle:false,
            showColumns:true,
            clickToSelect:true,
            modalName: "车辆信息",
            fixedColumns: true,
            fixedNumber:0,
            height: 560,
            columns: [{
                checkbox: true
            },
                {
                    field: 'carId',
                    title: '操作',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs ' + editFlag + '" href="javascript:void(0)"   title="修改" onclick="edit(\'' + row.carId + '\')" ><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                        actions.push('<a class="btn btn-xs  ' + detailFlag + '" href="javascript:void(0)" title="明细" onclick="detail(\'' + row.carId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        return actions.join('');
                    }

                },
                {
                    title: '车牌号',
                    align: 'left',
                    field: 'carno',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.carno);
                    }
                },
                {
                    title: '车长',
                    align: 'left',
                    field: 'carLengthId',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(carLen, value);
                    }
                },
                {
                    title: '车型',
                    align: 'left',
                    field: 'vehicleclassificationcode',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(carType, value);
                    }
                },
                // {
                //     title: '车辆性质',
                //     align: 'left',
                //     field: 'carProp',
                //     formatter: function status(value, row, index) {
                //         return $.table.selectDictLabel(carNature, value);
                //     }
                // },

                {
                    title: '车牌颜色',
                    align: 'left',
                    field: 'vehiclelicenseplatecolor',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(carPlateColor, value);
                    }
                },

                {
                    title: '车辆载质量（吨）',
                    align: 'left',
                    field: 'vehicletonnage'
                },
                {
                    title: '满载车辆质量（吨）',
                    align: 'left',
                    field: 'vehicleladenweight'
                },
                // {
                //     title: '承运商',
                //     align: 'left',
                //     field: 'carrName',
                //     formatter: function status(row,value) {
                //         return $.table.tooltip(value.carrName);
                //     }
                // },
                // {
                //     title: '司机',
                //     align: 'left',
                //     field: 'driverName',
                //     formatter: function status(row,value) {
                //         return $.table.tooltip(value.driverName);
                //     }
                // },
                {
                    title: '道路运输许可证号',
                    align: 'left',
                    field: 'permitnumber',
                },

                // {
                //     title: '是否符合无车承运人',
                //     align: 'left',
                //     field: 'isVehicleFreeCarrier',
                //     formatter: function status(value, row, index) {
                //         if(value==1){
                //             return '<span class="label label-primary">是</span>';
                //         }else if(value==0){
                //             return '<span class="label label-default">否</span>';
                //         }else{
                //             return '';
                //         }
                //     }
                // },
                {
                    title: '审核状态',
                    align: 'left',
                    field: 'checkStatus',
                    formatter: function status(value, row, index) {
                        if(value==0){
                            return '<span class="label label-warning">待审核</span>';
                        }else if(value==1){
                            return '<span class="label label-success">审核通过</span>';
                        }else if(value==2){
                            return '<span class="label label-danger">审核不通过</span>';
                        }else if(value==3){
                            return '<span class="label label-default">新建</span>';
                        }
                    }
                },
                // {
                //     title: '认证状态',
                //     align: 'left',
                //     field: 'authStatus',
                //     formatter: function status(value, row, index) {
                //         if(value==0){
                //             return '<span class="label label-default">待认证</span>';
                //         }else if(value==1){
                //             return '<span class="label label-success">认证通过</span>';
                //         }else if(value==2){
                //             return '<span class="label label-danger">认证失败</span>';
                //         }else if(value==3){
                //             return '<span class="label label-success">无需认证</span>';
                //         }
                //     }
                // },
                // {
                //     title: '原因',
                //     align: 'left',
                //     field: 'reason'
                // },
                // {
                //     title: '创建人',
                //     align: 'left',
                //     field: 'regUserId'
                // },
                // {
                //     title: '创建时间',
                //     align: 'left',
                //     field: 'regDate'
                // },
                {
                    title: '修改人',
                    align: 'left',
                    field: 'corUserId'
                },
                {
                    title: '修改时间',
                    align: 'left',
                    field: 'corDate'
                },

            ]
        };

        $.table.init(options);
    });

    function selectTemporary() {
        $("#temporary").val(1)
        $.table.search()
    }


    function detail(id) {
        var url =  ctx + "basic/car/detail/"+id;
        $.modal.openTab($.table._option.modalName + "详细", url);
    }

    function edit(id) {
        var url =  prefix + "/edit/"+id;
        $.modal.openTab($.table._option.modalName + "修改", url);
    }
</script>

</body>
</html>