<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('司机信息维护')"/>

</head>
<style>
    .sm_icon{
        width: 18px;
        height: 18px;
        display: inline-block;
        line-height: 18px;
        text-align: center;
        font-size: 16px;
        color: #ed5565;
    }
    .sm_text{
        display: inline-block;
        margin-left: 10px;
        line-height: 30px;
        color: #ed5565;
    }
    .sm_btn{
        display: inline-block;
        width: 100px;
        height: 30px;
        line-height: 30px;
        margin-left: 20px;
        text-align: center;
        background: #fde6de;
        border: 1px #ff9999 solid;
        color: #ed5565;
        border-radius: 20px;
        cursor: pointer;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row sm">
                    <span class="sm_icon">
                        <i class="fa fa-bell-o"></i>
                    </span>
                    <span class="sm_text" id="warn_msg">
                        共<span class="fcff3" th:text="${temporaryCount}"></span>位司机证件已到期或未上传，请及时更新
                    </span>
                    <span class="sm_btn" onclick="selectTemporary()">
                        <input type="hidden" id="temporary" name="temporary"/>
                        快速筛选
                    </span>
                </div>
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">司机编码：</label>
                            <div class="col-sm-7">
                                <div class="input-group">
                                    <input id="driverCode" name="driverCode" placeholder="请输入司机编码" class="form-control"
                                           type="text"
                                           required="" aria-required="true">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">司机名称：</label>
                            <div class="col-sm-7">
                                <div class="input-group">
                                    <input name="driverName" placeholder="请输入司机名称" class="form-control" type="text"
                                           required="" aria-required="true">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" id="addDriver" onclick="$.operate.addTab()" shiro:hasPermission="carrier:driver:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" id="removeDriver" onclick="$.operate.removeAll()" shiro:hasPermission="carrier:driver:remove">
                <i class="fa fa-remove"></i> 删除
            </a>

        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">

    var prefix = ctx + "carrier/driver";
    //权限
    var editFlag = [[${@permission.hasPermi('carrier:driver:edit')}]];
    var detailFlag = [[${@permission.hasPermi('carrier:driver:detail')}]];
    var driverTypeList = [[${driverTypeList}]];
    var isCarrier = [[${@dict.getType('is_vehicle_free_carrier')}]];
    var carrId = [[${carrId}]];
    if(null==carrId) {
        $("#addDriver").addClass("disabled");
        $("#removeDriver").addClass("disabled");
        $("#removeDriver").removeClass("multiple");
    }
    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            showToggle:false,
            showColumns:true,
            modalName: "司机信息",
            fixedColumns: true,
            clickToSelect:true,
            fixedNumber:0,
            height: 560,
            columns: [{
                checkbox: true
            },
                {
                    field: 'driverId',
                    title: '操作',
                    align: 'left',
                    width: '100px',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs ' + editFlag + '" href="javascript:void(0)" title="修改" onclick="edit(\'' + row.driverId + '\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                        actions.push('<a class="btn btn-xs ' + detailFlag + '" href="javascript:void(0)" title="明细"onclick="detail(\'' + row.driverId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        return actions.join('');
                    }
                },
                {
                    field: 'driverCode',
                    align: 'left',
                    title: '司机编码'
                },
                {
                    field: 'driverName',
                    align: 'left',
                    title: '司机名称'
                },
                // {
                //     field: 'carrName',
                //     align: 'left',
                //     title: '承运商名称',
                //     formatter: function status(row,value) {
                //         return $.table.tooltip(value.carrName);
                //     }
                // },
                {
                    field: 'phone',
                    align: 'left',
                    title: '手机号码'
                },
                // {
                //     field: 'driverType',
                //     align: 'left',
                //     title: '司机类型',
                //     formatter: function status(row, value) {
                //         var context = '';
                //         driverTypeList.forEach(function (v) {
                //             if (v.value == value.driverType) {
                //                 context = '<span class="label label-primary">' + v.context + '</span>';
                //                 return false;
                //             }
                //         });
                //         return context;
                //     }
                // },
                // {
                //     field: 'isVehicleFreeCarrier',
                //     align: 'left',
                //     title: '是否符合无车承运人',
                //     formatter: function (value, row, index) {
                //         return $.table.selectDictLabel(isCarrier, value);
                //     }
                // },
                {
                    title: '审核状态',
                    align: 'left',
                    field: 'checkStatus',
                    formatter: function status(value, row, index) {
                        if(value==0){
                            return '<span class="label label-warning">待审核</span>';
                        }else if(value==1){
                            return '<span class="label label-success">审核通过</span>';
                        }else if(value==2){
                            return '<span class="label label-danger">审核不通过</span>';
                        }else if(value==3){
                            return '<span class="label label-default">新建</span>';
                        }
                    }
                },
                // {
                //     title: '认证状态',
                //     align: 'left',
                //     field: 'authStatus',
                //     formatter: function status(value, row, index) {
                //         if(value==0){
                //             return '<span class="label label-default">待认证</span>';
                //         }else if(value==1){
                //             return '<span class="label label-success">认证通过</span>';
                //         }else if(value==2){
                //             return '<span class="label label-danger">认证失败</span>';
                //         }else if(value==3){
                //             return '<span class="label label-success">无需认证</span>';
                //         }
                //     }
                // },
                // {
                //     title: '原因',
                //     align: 'left',
                //     field: 'reason'
                // },
                // {
                //     title: '创建人',
                //     align: 'left',
                //     field: 'regUserId'
                // },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate'
                },
                {
                    title: '修改人',
                    align: 'left',
                    field: 'corUserId'
                },
                {
                    title: '修改时间',
                    align: 'left',
                    field: 'corDate'
                }


            ]
        };

        $.table.init(options);
    });

    function selectTemporary() {
        $("#temporary").val(1)
        $.table.search()
    }

    function edit(id){
        var url = prefix + "/edit/"+id;
        $.modal.openTab("司机信息修改",url);
    }

    function detail(id){
        var url = ctx + "basic/driver/detail/"+id;
        $.modal.openTab("司机信息明细",url);
    }
</script>

</body>
</html>