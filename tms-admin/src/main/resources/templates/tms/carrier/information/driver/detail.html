<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('司机信息维护-edit')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: jasny-bootstrap-css" />
</head>

<body>
<div class="form-content">
    <form id="form-driver-edit" class="form-horizontal" novalidate="novalidate" th:object="${driver}">
        <input id="driverId" name="driverId" type="hidden" th:field="*{driverId}">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">司机编码：</label>
                                    <div class="col-sm-8" th:text="${driver.driverCode }">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">司机名称：</label>
                                    <div class="col-sm-8">
                                        [[${driver.driverName }]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">身份证：</label>
                                    <div class="col-sm-8">
                                        [[${driver.cardId }]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">手机：</label>
                                    <div class="col-sm-8">
                                        [[${driver.phone }]]
                                    </div>
                                </div>
                            </div>
                          <!--  <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">驾驶证：</label>
                                    <div class="col-sm-8">
                                        [[${driver.driverLic }]]
                                    </div>
                                </div>
                            </div>-->
                        </div>
                        <div class="row">
                          <!--  <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">驾照类型：</label>
                                    <div class="col-sm-8" th:if="${driver.licType !='' and driver.licType!=null}" th:text="${@dict.getLabel('lic_type',driver.licType)}">
                                    </div>
                                </div>
                            </div>
-->
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">性别：</label>
                                    <div class="col-sm-8" th:if="${driver.sex !='' and driver.sex!=null}" th:text="${@dict.getLabel('sys_user_sex',driver.sex)}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">从业资格证：</label>
                                    <div class="col-sm-8">
                                        [[${driver.qualificationcertificatenumber }]]
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--证件上传 start-->
        <div class="panel-group" id="accordionTwo">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">证件上传</a>
                    </h5>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body" th:with="type=${@dict.getType('driver_pic_type')}">
                        <div class="row" th:each="dict : ${type}" th:if="${dict.dictValue != '4'}">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-9" th:text="${dict.dictLabel}+'：'">
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <div class="col-sm-7">
                                        <div style="display:inline-block" th:each="pic:${driverPic}"
                                             th:if="${pic.picType==dict.dictValue and pic.filePath!=null}">
                                            <img style="height:80px" modal="zoomImg" th:src="@{${pic.filePath}}"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--证件上传 end-->

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">

        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>


<script th:inline="javascript">
    var driverPicType = [[${@dict.getType('driver_pic_type')}]];
    var prefix = ctx + "carrier/driver";
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');

        var driverType = [[${driver.driverType}]];
        $("#driverType").find("option[value="+driverType+"]").attr("selected",true);
        if(driverType==2){
            $("#carrierName").attr("disabled", true);
            $("#carrierName").attr("style", "background-color: #EEEEEE");
        }

        //循环图片路径信息，初始化图片上传区域
        var imagePath = [[${imagePath}]];
        for (var key in imagePath) {
            var publishFlag = "done" + key;
            var param = {
                maxFileCount: 0,
                publish: "publishFlag",  //用于绑定下一步方法
                fileType: null//文件类型
            };
            var tid = "tid" + key;
            var imageId = "image" + key;
            $.file.loadEditFiles(imageId, tid, imagePath[key], param);
        }
    });

    $("#form-driver-edit").validate({
        onkeyup: false,
        rules: {
            age: {
                required: true
            },
            phone: {
                isPhone: true,
                remote: {
                    url: "/basic/driver/checkPhoneUnique",
                    type: "post",
                    dataType: "json",
                    data: {
                        name: function () {
                            return $.common.trim($("#phone").val());
                        }
                    },
                    dataFilter: function (data, type) {
                        return $.validate.unique(data);
                    }
                }
            },
        },
        messages: {
            "phone": {
                remote: "手机号码已经存在"
            }
        },
        focusCleanup: true
    });

    //上传完成标志位
    var flag;

    /*提交表单*/
    function submitHandler() {
        if ($.validate.form()) {
            //提交表单flag置空
            flag = "";
            if (driverPicType.length == 1) {
                var value = driverPicType[i].dictValue;
                //如果还没有上传图片，flag就是空的，直接上传第一个
                if ($("#image" + value).val() != "") {
                    $("#image" + value).fileinput('upload');
                    flag = "done" + value;
                    jQuery.subscribe(flag, setTimeout("commit()", "2000"));
                } else {
                    commit();
                }
            } else {
                //循环字典表图片类型
                for (var i = 0; i < driverPicType.length; i++) {
                    var value = driverPicType[i].dictValue;
                    //如果还没有上传图片，flag就是空的，直接上传第一个
                    if (flag == "" && i >= 0 && i < driverPicType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                        } else {
                            continue;
                        }
                    }
                    //所有都为空，直接提交表单;如果只上传最后一个，前面都没有上传，直接上传并提交表单，设置延时等待上传完成，不然来不及回调
                    if (flag == "" && i == driverPicType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                            jQuery.subscribe(flag, setTimeout("commit()", "2000"));
                        } else {
                            commit();
                        }
                    }
                    //如果前面有上传，且input框不空，执行上传
                    if (flag != "" && i > 0 && i < driverPicType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            jQuery.subscribe(flag, uploadPic(value));
                        } else {
                            continue;
                        }
                    }
                    //判断最后一个是否为空，为空直接提交表单，不为空上传完提交表单
                    if (flag != "" && i == driverPicType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                            jQuery.subscribe(flag, setTimeout("commit()", "2000"));
                        } else {
                            jQuery.subscribe(flag, setTimeout("commit()", "2000"));
                        }
                    }
                }
            }
        }
    }

    function uploadPic(dictValue) {
        $("#image" + dictValue).fileinput('upload');
        flag = "done" + dictValue;
    }

    function commit() {
        $.operate.saveTab(prefix + "/edit", $('#form-driver-edit').serialize());
    }






</script>
</body>

</html>