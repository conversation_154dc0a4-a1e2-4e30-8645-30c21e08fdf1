<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('司机管理-新增')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 140px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .fcff{
        color: #ff1f1f;
    }
    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .table-bordered {
        border: 0px solid #EBEBEB;
    }
    .table-striped .table, .table-striped .table, .table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td {
        border-bottom: 0px solid #e7eaec!important;
    }
    .table-bordered td, .table-bordered th {
        border: 0px solid #ddd!important;
    }
    .input-group{
        margin-bottom: 10px;
    }
    .save{
        padding: 0px 0;
        position: fixed;
        bottom: 0px;
        left: 0;
        width: 100%;
        background: #fff;
        z-index: 99;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .input-group .form-control {
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .eye .file-drop-zone-title{
        background: url('../../img/eye.png') no-repeat center 10px;
        background-size: 40px 40px;
        height: 100%;
        line-height: 120px;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-driver-add" class="form-horizontal">
        <input name="deptId" type="hidden" id="treeId"/>
        <input id="driverId" name="driverId" type="hidden">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">证件上传</a>
                    </h5>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body" th:with="type=${@dict.getType('driver_pic_type')}">
                        <div class="row" >
                            <div class="col-md-5 col-sm-5" th:each="dict : ${type}" th:if="${dict.dictLabel == '身份证正面'}">
                                <div class="form-group">
                                    <label class="col-sm-3" th:text="${dict.dictLabel}+'：'">
                                    </label>
                                    <div class="col-sm-9">
                                        <div class="form-group eye">
                                            <div class="col-sm-12">
                                                <input th:id="'image'+${dict.dictValue}" class="form-control"
                                                       th:name="'image'+${dict.dictValue}" type="file" multiple>
                                                <input th:id="'tid'+${dict.dictValue}" th:name="'tid'+${dict.dictValue}"
                                                       type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2" ></div>
                            <div class="col-md-5 col-sm-5" th:each="dict : ${type}" th:if="${dict.dictLabel == '身份证背面'}">
                                <div class="form-group">
                                    <label class="col-sm-3" th:text="${dict.dictLabel}+'：'">
                                    </label>
                                    <div class="col-sm-9">
                                        <div class="form-group eye">
                                            <div class="col-sm-12">
                                                <input th:id="'image'+${dict.dictValue}" class="form-control"
                                                       th:name="'image'+${dict.dictValue}" type="file" multiple>
                                                <input th:id="'tid'+${dict.dictValue}" th:name="'tid'+${dict.dictValue}"
                                                       type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" >
                            <div class="col-md-5 col-sm-5" th:each="dict : ${type}" th:if="${dict.dictLabel == '驾驶证'}">
                                <div class="form-group">
                                    <label class="col-sm-3" th:text="${dict.dictLabel}+'：'">
                                    </label>
                                    <div class="col-sm-9">
                                        <div class="form-group eye">
                                            <div class="col-sm-12">
                                                <input th:id="'image'+${dict.dictValue}" class="form-control"
                                                       th:name="'image'+${dict.dictValue}" type="file" multiple>
                                                <input th:id="'tid'+${dict.dictValue}" th:name="'tid'+${dict.dictValue}"
                                                       type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-2" ></div>
                            <div class="col-md-5 col-sm-5" th:each="dict : ${type}" th:if="${dict.dictLabel == '从业资格证'}">
                                <div class="form-group">
                                    <label class="col-sm-3" th:text="${dict.dictLabel}+'：'">
                                    </label>
                                    <div class="col-sm-9" th:if="${dict.dictLabel == '从业资格证'}">
                                        <div class="form-group">
                                            <div class="col-sm-12">
                                                <input th:id="'image'+${dict.dictValue}" class="form-control"
                                                       th:name="'image'+${dict.dictValue}" type="file" multiple>
                                                <input th:id="'tid'+${dict.dictValue}" th:name="'tid'+${dict.dictValue}"
                                                       type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>  司机类型：</label>
                                    <div class="flex_right">
                                        <select id="driverType" name="driverType" class="form-control valid"
                                                aria-invalid="false" required onchange="ifSelectCarrier()">
                                            <!--                                            <option value="" selected="selected"> &#45;&#45; 请选择 &#45;&#45;</option>-->
                                            <!--                                            <option th:each="driverType : ${driverTypeList}" th:text="${driverType.context}" th:value="${driverType.value}"></option>-->
                                            <option value="3">承运商司机</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6" style="display: none">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>  承运商：</label>
                                    <div class="flex_right">
                                        <div class="input-group">
                                            <input name="carrierId" id="carrierId" type="hidden" th:value="${carrier.carrierId}">
                                            <input name="carrierName" class="form-control" type="text" onclick="selectCarrier()" id="carrierName" required readonly="true" th:value="${carrier.carrName}">
                                            <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>  <span>司机名称：</span></label>
                                    <div class="flex_right">
                                        <input id="driverName" name="driverName" class="form-control" type="text" maxlength="25" required="true" aria-required="true" onkeyup="findOneDriver(this.value)">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="sex"><span class="fcff">*</span>  性别：</span></label>
                                    <div class="flex_right">
                                        <select id="sex" name="sex" class="form-control valid" aria-invalid="false"
                                                th:with="type=${@dict.getType('sys_user_sex')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff check" >*</span>  准驾车型：</label>
                                    <div class="flex_right">
                                        <select id="licType" name="licType" class="form-control valid checkVal"
                                                aria-invalid="false" th:with="type=${@dict.getType('lic_type')}"
                                                required>
                                            <option value="" selected="selected"> -- 请选择 --</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>  手机：</label>
                                    <div class="flex_right">
                                        <input id="phone" name="phone" class="form-control" type="text"
                                               maxlength="50" required="" aria-required="true">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff check">*</span>  身份证：</label>
                                    <div class="flex_right">
                                        <input name="cardId" id="cardId" maxlength="50" type="text" class="form-control checkVal" required onkeyup="findOneDriver(this.value)">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff check">*</span>  身份证有效期：</label>
                                    <div class="flex_right">
                                        <input type="text" class="time-input form-control checkVal" id="cardDate"
                                               name="cardDate" readonly  required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">所属省：</label>
                                    <div class="flex_right">
                                        <select id="provinceCode" name = "provinceCode"
                                                class="form-control valid" aria-invalid="false">
                                            <option value='' selected='selected'>-- 请选择 --</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left" >驾驶证号码：</label>
                                    <div class="flex_right">
                                        <input id="driverLic" name="driverLic" maxlength="50" type="text" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">驾驶证有效期：</label>
                                    <div class="flex_right">
                                        <input type="text" class="time-input form-control" id="driverLicExpiryDate"
                                               name="driverLicExpiryDate" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">发证机关：</label>
                                    <div class="flex_right">
                                        <input id="issuingorganizations" name="issuingorganizations" class="form-control" type="text"
                                               maxlength="64" aria-required="true">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">地址：</label>
                                    <div class="flex_right">
                                        <input id="addr" name="addr" class="form-control" type="text"
                                               maxlength="50" aria-required="true">
                                    </div>
                                </div>
                            </div>
                            <!--                            <div class="col-md-3 col-sm-6">-->
                            <!--                                <div class="flex">-->
                            <!--                                    <label class="flex_left">所属省：</label>-->
                            <!--                                    <div class="flex_right">-->
                            <!--                                        <select id="provinceCode" name = "provinceCode"-->
                            <!--                                                class="form-control valid" aria-invalid="false">-->
                            <!--                                            <option value='' selected='selected'>&#45;&#45; 请选择 &#45;&#45;</option>-->
                            <!--                                        </select>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                        </div>
                        <div class="row">
                            <!--                            <div class="col-md-3 col-sm-6">-->
                            <!--                                <div class="flex">-->
                            <!--                                    <label class="flex_left">地址：</label>-->
                            <!--                                    <div class="flex_right">-->
                            <!--                                        <input id="addr" name="addr" class="form-control" type="text"-->
                            <!--                                               maxlength="50" aria-required="true">-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="qnumber"><span class="fcff check">*</span>  从业资格证号：</span></label>
                                    <div class="flex_right">
                                        <input name="qualificationcertificatenumber" maxlength="18" type="text"
                                               id="qnumber"  class="form-control checkVal" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left" id="validperiodfromStyle" >从业资格证有效期起：</label>
                                    <div class="flex_right">
                                        <input type="text" class="time-input form-control" id="validperiodfrom"
                                               name="validperiodfrom" readonly >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left" id="validperiodtoStyle" >从业资格证有效期至：</label>
                                    <div class="flex_right">
                                        <input type="text" class="time-input form-control" id="validperiodto"
                                               name="validperiodto" readonly >
                                    </div>
                                </div>
                            </div>
                            <!--<div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>  是否符合无车承运人：</label>
                                    <div class="flex_right">
                                        <select id="isVehicleFreeCarrier" name="isVehicleFreeCarrier"
                                                class="form-control valid"
                                                aria-invalid="false" required
                                                th:with="type=${@dict.getType('is_vehicle_free_carrier')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>-->
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="flex">
                                    <label class="flex_left">备注：</label>
                                    <div class="flex_right">
                                            <textarea name="memo" maxlength="100" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--基础信息 end-->

        <!--证件上传 start-->
        <!--        <div class="panel-group" id="accordionTwo">-->
        <!--            <div class="panel panel-default">-->
        <!--                <div class="panel-heading">-->
        <!--                    <h5 class="panel-title">-->
        <!--                        <a data-toggle="collapse" data-parent="#accordion"-->
        <!--                           href="tabs_panels.html#collapseTwo">证件上传</a>-->
        <!--                    </h5>-->
        <!--                </div>-->
        <!--                -->
        <!--            </div>-->
        <!--        </div>-->
        <!--证件上传 end-->
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <!--        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler(0)"><i class="fa fa-check"></i>保-->
        <!--            存-->
        <!--        </button>&nbsp;-->
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler(1)"><i class="fa fa-check"></i>保存并提交审核
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">
    var driverPicType = [[${@dict.getType('driver_pic_type')}]];
    var prefix = ctx + "basic/driver";

    var carrierPrefix = ctx + "carrier/driver";

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $(".distpicker").distpicker();

        var options = {};
        $.table.init(options);

        $("#isVehicleFreeCarrier option[value = '1']").attr("selected",true);//是否符合无车承运人 默认为是

        //初始化省市区
        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            success: function (result) {
                for (var i in result) {
                    $('#provinceCode').append("<option value='" + result[i].PROVINCE_CODE + "'>" + result[i].PROVINCE_NAME + "</option>")
                }
            }
        });

        //$("#carrierName").attr("disabled", true);
        //$("#carrierName").attr("style", "background-color: #EEEEEE");

        //循环字典表中图片类型，初始化图片上传区域
        for (var i = 0; i < driverPicType.length; i++) {
            var dictValue = driverPicType[i].dictValue;
            var publishFlag = "cmt_" + dictValue;
            var maxFileCount = 0 ;//最大上传文件数
            var picParam = {
                maxFileCount: 1,
                publish: publishFlag,  //用于绑定下一步方法
                fileType: null//文件类型
            };
            var tid = "tid" + dictValue;
            var imageId = "image" + dictValue;
            $.file.initAddFilesOCR(imageId, tid, picParam);
        }

        //判断无车承运人必填项
        $('#isVehicleFreeCarrier').change(function(){
            isNtocc();
        });

        jQuery.subscribe("cmt_1",cmtFile_1);
        jQuery.subscribe("cmt_2",cmtFile_2);
        jQuery.subscribe("cmt_3",cmtFile_3);
    });

    function cmtFile_1(){
        console.log("驾驶证正面识别开始")
        var data = {};
        data.tid = $("#tid1").val();
        data.side = 1;
        $.ajax({
            url : ctx + "common/ocr/diverByTid",
            method : 'POST',
            data : data,
            success:function (result) {
                console.log(result);
                if(result.code == 0){
                    //驾驶证号码
                    $("#driverLic").val(result.data.num);
                    //姓名
                    $("#driverName").val(result.data.name);
                    //驾驶证有效期
                    var end_date = result.data.end_date;
                    if(end_date != ""){
                        $("#driverLicExpiryDate").val(end_date.substring(0,4) + "-" + end_date.substring(4,6) + "-" + end_date.substring(6,8));
                    }
                    //准假车型
                    var vehicle_type = result.data.vehicle_type;
                    $("#licType option:contains('"+vehicle_type+"')").attr("selected",true);
                    //性别
                    var sex = result.data.sex;
                    $("#sex option:contains('"+sex+"')").attr("selected",true);
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
        console.log("驾驶证正面识别结束")
    }

    function cmtFile_2(){
        console.log("身份证正面识别开始")
        var data = {};
        data.tid = $("#tid2").val();
        data.side = 1;
        $.ajax({
            url : ctx + "common/ocr/idcardByTid",
            method : 'POST',
            data : data,
            success:function (result) {
                console.log(result);
                if(result.code == 0){
                    //驾驶证号码
                    $("#driverName").val(result.data.name);
                    //身份证号码
                    $("#cardId").val(result.data.num);
                    $("#qnumber").val(result.data.num);
                    //性别
                    var sex = result.data.sex;
                    $("#sex option:contains('"+sex+"')").attr("selected",true);
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
        console.log("身份证正面识别结束")
    }

    function cmtFile_3(){
        console.log("身份证反面识别开始")
        var data = {};
        data.tid = $("#tid3").val();
        data.side = 2;
        $.ajax({
            url : ctx + "common/ocr/idcardByTid",
            method : 'POST',
            data : data,
            success:function (result) {
                console.log(result);
                if(result.code == 0){
                    //身份证有效期
                    var end_date = result.data.end_date;
                    if(end_date != "") {
                        $("#cardDate").val(end_date.substring(0, 4) + "-" + end_date.substring(4, 6) + "-" + end_date.substring(6, 8));
                    }
                    //发证机关
                    $("#issuingorganizations").val(result.data.issue);
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
        console.log("身份证反面识别结束")
    }
    //TODO 引入
    function findOneDriver(value) {
        let driverName = $("#driverName").val();
        let  cardId = $("#cardId").val();
        let regExp = /^[\u4e00-\u9fa5]{2,}$/;
        //正则验证中文
        if(regExp.test(driverName) && cardId != null && cardId != '') {
            //findOne
            var data = {};
            data.driverName = driverName;
            data.cardId = cardId;
            $.ajax({
                url: carrierPrefix + "/findOne",
                type: "post",
                dataType: "json",
                data:data,
                success: function (result) {
                    if(result.data != null && result.data != '') {
                        //司机id
                        let driverId = result.data.driverId;
                        $("#driverId").val(driverId);
                        //性别
                        var sex = result.data.sex;
                        $("#sex").val(sex);
                        //驾驶证号码
                        $("#driverLic").val(result.data.driverLic);
                        //手机号
                        $("#phone").val(result.data.phone)
                        var card_date = result.data.cardDate;
                        if(card_date != '' && card_date != null) {
                            $("#cardDate").val(card_date.substring(0,4) + "-" + card_date.substring(5,7) + "-" + card_date.substring(8,10));
                        }
                        //从业资格证号
                        $("#qnumber").val(result.data.qualificationcertificatenumber);
                        //驾驶证有效期
                        var end_date = result.data.driverLicExpiryDate;
                        if(end_date != "" && end_date != null){
                            $("#driverLicExpiryDate").val(end_date.substring(0,4) + "-" + end_date.substring(5,7) + "-" + end_date.substring(8,10));
                        }
                        //准驾车型
                        var vehicle_type = result.data.licType;
                        $("#licType").val(vehicle_type);
                        var validperiodfrom = result.data.validperiodfrom
                        if(validperiodfrom != "" && validperiodfrom != null){
                            $("#validperiodfrom").val(validperiodfrom.substring(0,4) + "-" + validperiodfrom.substring(5,7) + "-" + validperiodfrom.substring(8,10));
                        }
                        var validperiodto = result.data.validperiodto
                        if(validperiodto != "" && validperiodto != null){
                            $("#validperiodto").val(validperiodto.substring(0,4) + "-" + validperiodto.substring(5,7) + "-" + validperiodto.substring(8,10));
                        }
                        //发证机关
                        $("#issuingorganizations").val(result.data.issuingorganizations)
                        //地址
                        $("#addr").val(result.data.addr)
                        //所属省
                        var provinceCode = result.data.provinceCode;
                        $("#provinceCode").val(provinceCode);

                        //附件  有四个附件框  如果未找到那么需要new Array()
                        let driverPicList = result.data.sysUploadFileVOS;
                        if(driverPicList != null) {
                            for(var i=0 ; i< driverPicList.length ; i++){
                                var picType = driverPicList[i].picType;
                                $("#tid"+picType).val(driverPicList[i].appendixId);
                                var publishFlag = "cmt_" + picType;
                                var param = {
                                    maxFileCount: 1,
                                    publish: publishFlag,  //用于绑定下一步方法
                                    fileType: null//文件类型
                                };
                                var tid = "tid" + picType;
                                var imageId = "image" + picType;
                                $('#'+imageId).fileinput('destroy');
                                $.file.loadEditFilesOCR(imageId, tid, new Array(driverPicList[i]), param);
                            }
                        }
                    }
                }
            });
        }
    }

    /**
     * 从业资格证核验
     */
    function checkQualification(){
        var provinceCode = $("#provinceCode").val(); //省编码
        var driverName = $("#driverName").val();//司机姓名
        var cardId = $("#cardId").val();//身份证
        if(provinceCode == null || provinceCode == ""){
            $.modal.alertWarning("请选择所属省");
            return false;
        }
        if(driverName == null || driverName == ""){
            $.modal.alertWarning("请填写司机姓名");
            return false;
        }
        if(cardId == null || cardId == ""){
            $.modal.alertWarning("请填写身份证号码");
            return false;
        }

        var data = {};
        data.provinceCode = provinceCode;
        data.personName = driverName;
        data.citizenNo = cardId;
        $.ajax({
            url:ctx + "basic/personalDriver/checkQualification",
            type:"post",
            dataType:"json",
            data:data,
            success: function (result) {
                if(result.data.isExist === '否'){
                    $("#isVehicleFreeCarrier").val("0");
                    isNtocc();
                }else{
                    $("#isVehicleFreeCarrier").val("1")
                    isNtocc();
                }
            }
        });
    }

    $("#form-driver-add").validate({
        onkeyup: false,
        rules: {
            phone: {
                isPhone: true,
                remote: {
                    url: prefix + "/checkPhoneUnique",
                    type: "post",
                    dataType: "json",
                    data: {
                        phone: function () {
                            return $.common.trim($("#phone").val());
                        }
                    },
                    dataFilter: function (data, type) {
                        return $.validate.unique(data);
                    }
                }
            },
            /*cardId: {
                isIdentity: true,
                remote: {
                    url: prefix + "/checkCardIdUnique",
                    type: "post",
                    dataType: "json",
                    data: {
                        cardId: function () {
                            return $.common.trim($("#cardId").val());
                        }
                    },
                    dataFilter: function (data, type) {
                        return $.validate.unique(data);
                    }
                }
            }*/
        },
        messages: {
            "phone": {
                remote: "手机号码已经存在"
            },
            "cardId": {
                remote: "身份证号码已经存在"
            },
        },
        focusCleanup: true
    });

    /*选择承运人*/
    function selectCarrier() {
        var PREFIX = ctx + "basic/carrier";
        var driverType = $("#driverType").val();
        if (driverType == '4') {
            $.modal.open("选择承运人", PREFIX + "/findCarrier?carrType=0");
        } else {
            $.modal.open("选择承运人", PREFIX + "/findCarrier?carrType=1,2,3,4,5");
        }
    }

    /**
     * 选择实际承运人
     */
    function selectActualCarrier() {
        var url = prefix + "/selectActualCarrier";
        $.modal.open("选择实际承运人", url, "", "", function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            $("#actualCarrierid").val(rows[0]["carrierid"]);
            $("#actualCarrierName").val(rows[0]["carriername"]);

            layer.close(index);
            $("#form-driver-add").validate().element($("#actualCarrierName"))
        });
    }

    /*选择父部门树*/
    function selectDeptTree() {
        var options = {
            title: '部门选择',
            width: "380",
            url: prefix + "/selectDeptTree/" + 100,
            callBack: doSubmitDept
        };
        $.modal.openOptions(options);
    }

    /*选择部门后回调*/
    function doSubmitDept(index, layero) {
        var body = layer.getChildFrame('body', index);
        $("#deptid").val(body.find('#treeId').val());
        $("#deptName").val(body.find('#treeName').val());
        layer.close(index);
        $("#form-driver-add").validate().element($("#deptName"))
    }

    //上传完成标志位
    var flag;
    var array;

    function submitHandler(flag) {
        if(flag == 0 ){
            $('.check').css("display","none");
            $(".checkVal").removeAttr("required");
        }else{
            $('.check').css("display","initial");
            $(".checkVal").attr("required","true");
        }

        isNtocc();
        if ($.validate.form()) {
            /*flag = "";
            array = [];
            for (var i = 0; i < driverPicType.length; i++) {
                var value = driverPicType[i].dictValue;
                if ($("#image" + value).val() != "") {
                    array[i] = value;
                }
            }

            if (array.length == 0) {
                commit();
            }

            if (array.length == 1) {
                $("#image" + array[0]).fileinput('upload');
                flag = "done" + array[0];
                jQuery.subscribe(flag, commit);
            }

            if (array.length > 1) {
                $("#image" + array[0]).fileinput('upload');
                flag = "done" + array[0];
                for (var i = 0; i < array.length; i++) {
                    if (i < array.length - 1) {
                        jQuery.subscribe(flag, uploadPic(array[i + 1]));
                    }
                    if (i == array.length - 1) {
                        jQuery.subscribe(flag, commit);
                    }
                }
            }*/
            var data = $('#form-driver-add').serializeArray();
            data.push({"name": "submitFlag", "value": flag});
            if($("#driverId").val() != '') {
                $.operate.saveTab(carrierPrefix + "/pullInDriver", data);
            }else  {
                $.operate.saveTab(prefix + "/add", data);
            }
        }
    }

    function uploadPic(dictValue) {
        $("#image" + dictValue).fileinput('upload');
        flag = "done" + dictValue;
    }

    function commit() {
        var f = true;
        for (var i = 0; i < array.length; i++) {
            if ($("#tid" + array[i]).val() == "") {
                f = false;
                setTimeout(commit, 1000);
            }
        }
        if (f) {
            $.operate.saveTab(prefix + "/add", $('#form-driver-add').serialize());
        }
    }

    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#expiryDate', //指定元素
            format: 'yyyy-MM-dd',
            isInitValue: false,
            done: function (value, date, endDate) {
            }
        });
    });

    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#qualificationcertificateDate', //指定元素
            format: 'yyyy-MM-dd',
            isInitValue: false,
            done: function (value, date, endDate) {
            }
        });
    });

    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#driverLicExpiryDate', //指定元素
            format: 'yyyy-MM-dd',
            isInitValue: false,
            done: function (value, date, endDate) {
            }
        });
    });

    //能否选择承运商
    function ifSelectCarrier() {
        var driverType = $("#driverType").val();
        if (driverType == "") {
            $("#carrierName").val("");
            $("#carrierId").val("");
            $("#carrierName").attr("disabled", true);
            $("#carrierName").attr("style", "background-color: #EEEEEE");
        } else {
            $("#carrierName").val("");
            $("#carrierId").val("");
            $("#carrierName").attr("disabled", false);
            $("#carrierName").attr("style", "background-color: #FFFFFF none");
        }
    }

    /**
     * 判断符合无车承运人是，某些字段必填
     */
    function isNtocc(){
        var isVehicleFreeCarrier  = $("#isVehicleFreeCarrier").find(":selected").text();
        if(isVehicleFreeCarrier == '是'){
            //性别
            $('.sex').css("color","red");
            $("#sex").attr("required","true");
            //从业资格证
            $('.qnumber').css("color","red");
            $("#qnumber").attr("required","true");

            //$('.actualCarrierid').css("color","red");
            //$("#actualCarrierName").attr("required","true");

            /*$('.registrationdatetime').css("color","red");
            $("#registrationdatetime").attr("required","true");

            $("#validperiodfromStyle").css("color","red");
            $("#validperiodfrom").attr("required",true);

            $("#validperiodtoStyle").css("color","red");
            $("#validperiodto").attr("required",true);*/

        }
        if(isVehicleFreeCarrier == '否'){
            //性别
            $('.sex').css("color","inherit");
            $("#sex").removeAttr("required");
            $("#form-driver-add").validate().element($("#sex"));
            //从业资格证
            $('.qnumber').css("color","inherit");
            $("#qnumber").removeAttr("required");
            $("#form-driver-add").validate().element($("#qnumber"));

            //实际承运人
            //$('.actualCarrierid').css("color","inherit");
            //$("#actualCarrierName").removeAttr("required");
            //$("#form-driver-add").validate().element($("#actualCarrierName"));

            //注册时间
            /* $('.registrationdatetime').css("color","inherit");
             $("#registrationdatetime").removeAttr("required");
             $("#form-driver-add").validate().element($("#registrationdatetime"));

             $("#validperiodfromStyle").css("color","");
             $("#validperiodfrom").removeAttr("required");
             $("#form-driver-add").validate().element($("#validperiodfrom"));

             $("#validperiodtoStyle").css("color","");
             $("#validperiodto").removeAttr("required");
             $("#form-driver-add").validate().element($("#validperiodto"));*/
        }
    }
</script>
</body>

</html>