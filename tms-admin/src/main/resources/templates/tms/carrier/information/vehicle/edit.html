<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车辆管理-修改')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: jasny-bootstrap-css" />
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .err-tips-box{ clear: both; width: 100%; float: left; padding: 10px 0px;}
    .err-tips{ background-color: #fffff7;width: 100%; float: left; border-radius: 4px; border: 1px #d9cab5 dashed; padding:5px 15px;}
    .err-tips h2{  font-size: 13px; margin: 0px !important; padding: 0px; font-weight: bold; line-height: 28px; padding-bottom: 5px;}
    .err-tips h2 .fa{ color: #b83d1b; font-size: 14px; margin-right: 3px;}
    .err-tips p{ font-size: 13px; margin: 0px !important; line-height: 22px; padding: 0px; padding-bottom: 5px; color: #8d6732; }
    .err-tips p:last-child{ padding-bottom: 0px;}
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 20px;
        text-align: left;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .fcff{
        color: #ff1f1f;
    }
    .flex_right select {
        margin-bottom: 10px;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .table-bordered {
        border: 0px solid #EBEBEB;
    }
    .table-striped .table, .table-striped .table, .table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td {
        border-bottom: 0px solid #e7eaec!important;
    }
    .table-bordered td, .table-bordered th {
        border: 0px solid #ddd!important;
    }
    .input-group{
        margin-bottom: 10px;
    }
    .save{
        padding: 0px 0;
        position: fixed;
        bottom: 0px;
        left: 0;
        width: 100%;
        background: #fff;
        z-index: 99;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .eye .file-drop-zone-title{
        background: url('../../../img/eye.png') no-repeat center 10px;
        background-size: 40px 40px;
        height: 100%;
        line-height: 120px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-car-add" class="form-horizontal" novalidate="novalidate">

        <input name="carId" type="hidden" th:field="*{car.carId}"/>
        <input name="actualCarrierid" type="hidden" th:field="*{car.actualCarrierid}"/>
        <input name="corDate" type="hidden" th:value="${#dates.format(car.corDate, 'yyyy-MM-dd HH:mm:ss')}"/>


        <div class="panel-group" id="accordionFour">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordionThree"
                           href="tabs_panels.html#collapseTwo">车辆基本信息</a>
                    </h5>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="col-sm-7" th:with="type=${@dict.getType('car_pic_type')}">
                            <div class="col-md-6 col-sm-6" th:each="dict : ${type}">
                                <div>
                                    <div class="form-group">
<!--                                        <span class="fcff">*</span>-->
                                        <label class="col-sm-12" th:text="${dict.dictLabel}+': '">
                                        </label>
                                    </div>
                                </div>
                                <div class="col-sm-12" th:if="${dict.dictLabel == '行驶证'}">
                                    <div class="form-group eye">
                                        <div class="" >
                                            <input th:id="'image'+${dict.dictValue}" class="form-control"
                                                   th:name="'image'+${dict.dictValue}" type="file" multiple>
                                            <input th:id="'tid'+${dict.dictValue}" th:name="'tid'+${dict.dictValue}"
                                                   type="hidden" >
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12" th:if="${dict.dictLabel != '行驶证'}">
                                    <div class="form-group">
                                        <div class="" >
                                            <input th:id="'image'+${dict.dictValue}" class="form-control"
                                                   th:name="'image'+${dict.dictValue}" type="file" multiple>
                                            <input th:id="'tid'+${dict.dictValue}" th:name="'tid'+${dict.dictValue}"
                                                   type="hidden" >
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-5">
                            <div class="row">
                                <div class="col-md-6 col-sm-6" style="display: none">
                                    <div class="flex">
                                        <label class="flex_left"><span class="fcff">*</span>  <span>承运商：</span></label>
                                        <div class="flex_right">
                                            <input name="carrierId" id="carrierId" type="hidden" th:value="${carrierIds}">
                                            <div class="input-group">
                                                <input name="carrierName" id="carrierName" th:value="${carrierNames}" class="form-control valid checkVal" type="text"
                                                       required aria-invalid="false"  onclick="selectCarrier()" readonly>
                                                <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 col-sm-12" style="display: none">
                                    <div class="flex">
                                        <label class="flex_left"><span class="fcff">*</span>  <span>车辆性质：</span></label>
                                        <div class="flex_right">
                                            <select id="carProp" name="carProp" class="form-control valid checkVal"  aria-invalid="false" required th:with="type=${@dict.getType('car_nature')}">
                                                <option value="">-- 请选择 --</option>
                                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                        th:value="${dict.dictValue}" th:field="*{car.carProp}"></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left"><span class="fcff">*</span>  <span>车牌号：</span></label>
                                        <div class="flex_right">
                                            <input name="carno" class="form-control valid" type="text" required="true"
                                                   aria-required="true" maxlength="25" th:field="*{car.carno}" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left"><span class="fcff">*</span>  所属省：</label>
                                        <div class="flex_right">
                                            <select name="countrysubdivisioncode" id="provinceCode"  class="form-control valid checkVal" th:value="*{car.provinceCode}" aria-invalid="false" required>
                                                <option value='' selected='selected'>-- 请选择 --</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left"><span class="fcff">*</span>  <span>车长：</span></label>
                                        <div class="flex_right">
                                            <select id="carLengthId" name="carLengthId" class="form-control valid checkVal" aria-invalid="false" required
                                                    th:with="type=${@dict.getType('car_len')}" onchange="setCarLenName()">
                                                <option value="">-- 请选择 --</option>
                                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                                                        th:field="*{car.carLengthId}"></option>
                                            </select>
                                            <input type="hidden" name="carLenName" id="carLenName"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left"><span class="fcff">*</span>  <span>车型：</span></label>
                                        <div class="flex_right">
                                            <select id="vehicleclassificationcode" name="vehicleclassificationcode" class="form-control valid checkVal"
                                                    aria-invalid="false" required th:with="type=${@dict.getType('car_type')}" onchange="setCarTypeName()">
                                                <option value="">-- 请选择 --</option>
                                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                        th:value="${dict.dictValue}" th:field="*{car.vehicleclassificationcode}"></option>
                                            </select>
                                            <input type="hidden" name="carTypeName" id="carTypeName"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left"><span class="fcff">*</span>  营运车辆类型代码：</label>
                                        <div class="flex_right">
                                            <select name="operationType" id="operationType" class="form-control checkVal" th:with="type=${@dict.getType('OPERATION_TYPE')}" th:field="${car.operationType}" required>
                                                <option value="">-- 请选择 --</option>
                                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left checkTotalQualityColor" >使用性质：</label>
                                        <div class="flex_right">
                                            <input name="usercharacter" id="usercharacter" class="form-control valid checkTotalQuality"
                                                   type="text" maxlength="10" th:field="*{car.usercharacter}">
                                        </div>
                                    </div>
                                </div>

                                <!--<div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <a class="btn btn-primary btn-rounded btn-xs" onclick="searchCarInfo()"><i
                                                class="fa fa-search"></i>&nbsp;查询车辆承运人信息</a>
                                    </div>
                                </div>-->
                            </div>
                            <div class="row">
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left ntocc">车牌类型：</label>
                                        <div class="flex_right">
                                            <select name="licenseplatetypecode" id="licenseplatetypecode" class="form-control" th:with="type=${@dict.getType('car_plate_type')}">
                                                <option value="">-- 请选择 --</option>
                                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                                                        th:field="*{car.licenseplatetypecode}"></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left "><span class="fcff">*</span>  车牌颜色：</label>
                                        <div class="flex_right">
                                            <select name="vehiclelicenseplatecolor" id="vehiclelicenseplatecolor" class="form-control checkVal" th:with="type=${@dict.getType('car_plate_color')}" required th:onblur="selectLicenseplatetypecode(this.value)">
                                                <option value="">-- 请选择 --</option>
                                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                        th:value="${dict.dictValue}" th:field="*{car.vehiclelicenseplatecolor}"></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left ntocc">车轴数：</label>
                                        <div class="flex_right">
                                            <input name="axlenum" id="axlenum" class=" form-control valid"
                                                   type="text" maxlength="2" th:value="${car.axlenum}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left ntocc">能源类型：</label>
                                        <div class="flex_right">
                                            <select id="vehicleenergytype" name="vehicleenergytype" class="form-control valid" aria-invalid="false"
                                                    th:with="type=${@dict.getType('vehicleenergytype')}" th:field="${car.vehicleenergytype}" >
                                                <option value="">-- 请选择 --</option>
                                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left checkTotalQualityColor"><span class="fcff">*</span>车辆识别代号：</label>
                                        <div class="flex_right">
                                            <input name="vin" id="vin" class="form-control valid checkTotalQuality"  type="text"
                                                   maxlength="50" th:field="*{car.vin}" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left ntocc"><span class="fcff">*</span>车辆道路运输证号：</label>
                                        <div class="flex_right">
                                            <input name="roadtransportcertificatenumber" id="roadtransportcertificatenumber" class="form-control valid ntoccVal"
                                                   type="text" maxlength="30" th:field="*{car.roadtransportcertificatenumber}" required>
                                        </div>
                                    </div>
                                </div>
                                <!--                                <div class="col-md-3 col-sm-6" id="checkTotalQualityIsShow" style="display:none">-->
                                <!--                                    <a class="btn btn-warning btn-rounded btn-xs" onclick="searchProvinceCode()"><i class="fa fa-search"></i>&nbsp;查询行政区域代码</a>-->
                                <!--                                </div>-->
                            </div>
                            <div class="row">
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left "><span class="fcff">*</span>  车辆载质量(核定载质量)：</label>
                                        <div class="flex_right">
                                            <div class="input-group">
                                                <input name="vehicletonnage" id="vehicletonnage" type="text" class="form-control checkVal" align="left"
                                                       maxlength="12" th:field="*{car.vehicletonnage}" required>
                                                <span class="input-group-addon">吨</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left "><span class="fcff">*</span>  满载车辆质量(总质量)：</label>
                                        <div class="flex_right">
                                            <div class="input-group">
                                                <input name="vehicleladenweight" id="vehicleladenweight" type="text" class="form-control checkVal"
                                                       align="right" maxlength="12" th:field="*{car.vehicleladenweight}" onblur="checkTotalQuality()" required>
                                                <span class="input-group-addon">吨</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt10">
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left checkTotalQualityColor"><span class="fcff">*</span>  年检有效期：</label>
                                        <div class="flex_right checkTotalQuality">
                                            <input name="yearlyInspectionDate" id="yearlyInspectionDate" class="time-input form-control valid checkVal"
                                                   th:value="${#dates.format(car.yearlyInspectionDate, 'yyyy-MM-dd')}" type="text" maxlength="16" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left checkTotalQualityColor" >注册日期：</label>
                                        <div class="flex_right checkTotalQuality">
                                            <input name="registerdate" id="registerdate" class="time-input form-control valid checkTotalQuality"
                                                   type="text" maxlength="16" th:value="${#dates.format(car.registerdate, 'yyyy-MM-dd')}" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt10">
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left ntocc">有效期起：</label>
                                        <div class="flex_right">
                                            <input name="effectiveFrom" id="effectiveFrom"
                                                   class="time-input form-control valid" th:value="${#dates.format(car.effectiveFrom, 'yyyy-MM-dd')}" type="text"  >
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left ntocc">有效期至：</label>
                                        <div class="flex_right">
                                            <input name="validUntil" id="validUntil"
                                                   class="time-input form-control valid" th:value="${#dates.format(car.validUntil, 'yyyy-MM-dd')}"  type="text" >
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt10">
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left checkTotalQualityColor" >发证日期：</label>
                                        <div class="flex_right checkTotalQuality">
                                            <input name="issuedate" id="issuedate" class="form-control valid checkTotalQuality"
                                                   type="text" maxlength="16"  th:value="${#dates.format(car.issuedate, 'yyyy-MM-dd')}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left">品牌型号：</label>
                                        <div class="flex_right">
                                            <input id="brand" name="brand" class="form-control" type="text" maxlength="30"
                                                   th:field="*{car.brand}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt10">
                                <div class="col-md-6 col-sm-6" style="display: none">
                                    <div class="flex">
                                        <label class="flex_left">长x宽x高：</label>
                                        <div class="flex_right">
                                            <div class="col-md-7 col-sm-7" style="padding: 0;">
                                                <div class="col-md-6 col-sm-6" style="padding-left: 0;padding-right: 0;">
                                                    <div class="input-group">
                                                        <input id="length" style="width: calc(100% - 10px)"  name="length" type="text" class="form-control" align="right"
                                                               maxlength="6" th:field="*{car.length}" oninput="$.numberUtil.onlyNumber(this)">
                                                        <span class="" style="display: inline-block;width: 10px;text-align: center;line-height: 30px">x</span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-sm-6" style="padding-left: 0;padding-right: 0;">
                                                    <div class="input-group">
                                                        <input id="width" style="width: calc(100% - 10px)"  name="width" type="text" class="form-control" align="right"
                                                               maxlength="6" th:field="*{car.width}" oninput="$.numberUtil.onlyNumber(this)">
                                                        <span class="" style="display: inline-block;width: 10px;text-align: center;line-height: 30px">x</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-5 col-sm-5" style="padding: 0">
                                                <div class="input-group">
                                                    <input id="height" name="height" type="text" class="form-control" align="right"
                                                           maxlength="6" th:field="*{car.height}" oninput="$.numberUtil.onlyNumber(this)">
                                                    <span class="input-group-addon">米</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 col-sm-6" style="display: none">
                                    <div class="flex">
                                        <label class="flex_left">司机：</label>
                                        <div class="flex_right">
                                            <div class="input-group">
                                                <input id="driverId" name="driverId" class="form-control valid" type="hidden"
                                                       th:value="${driverIds}">
                                                <input id="driverName" name="driverName" class="form-control valid" type="text"
                                                       onclick="selectDriverList()" th:value="${driverNames}" readonly>
                                                <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <!--                            <div class="row">-->
                            <!--                                <div class="col-md-3 col-sm-6">-->
                            <!--                                    <div class="form-group">-->
                            <!--                                        <label class="col-md-4 col-sm-4">发动机号：</label>-->
                            <!--                                        <div class="col-md-8 col-sm-8">-->
                            <!--                                            <input name="engineNo" class="form-control" type="text"-->
                            <!--                                                   maxlength="30" th:field="*{car.engineNo}">-->
                            <!--                                        </div>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                                <div class="col-md-3 col-sm-6">-->
                            <!--                                    <div class="form-group">-->
                            <!--                                        <label class="col-md-4 col-sm-4">行驶证号：</label>-->
                            <!--                                        <div class="col-md-8 col-sm-8">-->
                            <!--                                            <input name="drivingNumber" class="form-control valid" type="text"-->
                            <!--                                                   maxlength="20" th:field="*{car.drivingNumber}">-->
                            <!--                                        </div>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                                <div class="col-md-3 col-sm-6">-->
                            <!--                                    <div class="form-group">-->
                            <!--                                        <label class="col-md-4 col-sm-4">保险到期日期：</label>-->
                            <!--                                        <div class="col-md-8 col-sm-8">-->
                            <!--                                            <input name="filingDate" class="time-input form-control valid" type="text"-->
                            <!--                                                   th:value="${#dates.format(car.filingDate, 'yyyy-MM-dd')}">-->
                            <!--                                        </div>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                                -->
                            <!--                            </div>-->
                            <!-- <div class="row">-->

                            <!--                                <div class="col-md-3 col-sm-6">-->
                            <!--                                    <div class="form-group">-->
                            <!--                                        <label class="col-md-4 col-sm-4">司机手机：</label>-->
                            <!--                                        <div class="col-md-8 col-sm-8">-->
                            <!--                                            <input id="phone" name="driverMobile" class="form-control" type="text" maxlength="200"-->
                            <!--                                                   readonly th:value="${driverPhones}">-->
                            <!--                                        </div>-->
                            <!--                                    </div>-->
                            <!--                                </div>
                                                            <div class="col-md-6 col-sm-6">
                                                                <div class="flex">
                                                                    <label class="flex_left">挂车车牌号：</label>
                                                                    <div class="flex_right">
                                                                        <input name="trailerlicenceno" class="form-control valid" type="text"
                                                                               maxlength="32" th:field="*{car.trailerlicenceno}">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6 col-sm-6">
                                                                <div class="flex">
                                                                    <label class="flex_left">挂车年检有效期：</label>
                                                                    <div class="flex_right">
                                                                        <input name="yearlyInspectionGDate" class="time-input form-control valid" type="text"
                                                                               th:value="${#dates.format(car.yearlyInspectionGDate, 'yyyy-MM-dd')}">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>-->
                            <div class="row">
                                <div class="col-md-6 col-sm-6">
                                    <div class="flex">
                                        <label class="flex_left checkTotalQualityColor" >发证机关：</label>
                                        <div class="flex_right">
                                            <input name="issuingorganizations" id="issuingorganizations" class="form-control
                                        valid checkTotalQuality"  type="text" maxlength="16" th:field="*{car.issuingorganizations}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--                            <div class="row">-->
                            <!--                                <div class="col-sm-12">-->
                            <!--                                    <div class="form-group">-->
                            <!--                                        <label class="col-md-1 col-sm-2">备注：</label>-->
                            <!--                                        <div class="col-md-11 col-sm-10">-->
                            <!--                                            <textarea name="memo" maxlength="100" class="form-control valid"-->
                            <!--                                                      rows="3"  th:field="*{car.memo}"></textarea>-->
                            <!--                                        </div>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->

                        </div>

                    </div>
                </div>
            </div>
        </div>


        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">业户信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-4 col-sm-4">
                                <div>
                                    <div class="form-group">
                                        <label class="col-sm-12">车辆所有人道路运输经营许可证：</label>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <div class="" >
                                            <input th:id="image4" class="form-control"
                                                   th:name="image4" type="file" multiple>
                                            <input th:id="tid4" th:name="tid4" th:value="${carrierinfo.qualificationTid}" type="hidden" >
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8 col-sm-8">
                                <div class="row">
                                    <!--                                    <div class="col-md-3 col-sm-6">-->
                                    <!--                                        <div class="form-group">-->
                                    <!--                                            <label class="col-md-4 col-sm-4" style="color: red">是否符合无车承运人：</label>-->
                                    <!--                                            <div class="col-md-8 col-sm-8">-->
                                    <!--                                                <select name="isVehicleFreeCarrier" id="isVehicleFreeCarrier" class="form-control valid" required aria-invalid="false" onblur="checkTotalQuality()">-->
                                    <!--                                                    <option value="">&#45;&#45; 请选择 &#45;&#45;</option>-->
                                    <!--                                                    <option value="1" th:field="*{car.isVehicleFreeCarrier}">是</option>-->
                                    <!--                                                    <option value="0" th:field="*{car.isVehicleFreeCarrier}">否</option>-->
                                    <!--                                                </select>-->
                                    <!--                                            </div>-->
                                    <!--                                        </div>-->
                                    <!--                                    </div>-->
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ntocc"><span class="fcff">*</span>承运人(所有人)：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <input name="carriername" id="vehicleFeeName" th:value="${carrierinfo.carriername}"
                                                           class="form-control valid" type="text" aria-required="true" maxlength="25" required>
                                                    <div class="input-group-btn">
                                                        <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                            <span class="caret"></span>
                                                        </button>
                                                        <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ntocc"><span>无车承运人类别：</span></label>
                                            <div class="flex_right">
                                                <select name="carriertype" id="carriertype" class="form-control"   th:field="${carrierinfo.carriertype}"
                                                        th:with="type=${@dict.getType('carriertype')}">
                                                    <option value="">&#45;&#45;请选择&#45;&#45;</option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ntocc" style="width: 80px">道路运输经营许可证：</label>
                                            <div class="flex_right">
                                                <input name="permitnumber" id="permitnumber" class="form-control valid"
                                                       th:value="${carrierinfo.permitnumber}"
                                                       type="text" maxlength="25" style="display: inline-block;">
                                                <!--<a class="btn btn-warning btn-rounded btn-xs" style="width: 90px" onclick="searchCarrierInfo()"><i class="fa fa-search"></i>&nbsp;查询本系统</a>-->
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">

                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ntocc">承运人证照号：</label>
                                            <div class="flex_right">
                                                <input name="unifiedsocialcreditldentifier" id="unifiedsocialcreditldentifier"
                                                       th:value="${carrierinfo.unifiedsocialcreditldentifier}"
                                                       class="form-control valid"  type="text" maxlength="25">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ntocc">注册日期：</label>
                                            <div class="flex_right">
                                                <input name="registrationdatetime" id="registrationdatetime" th:value="${#dates.format(carrierinfo.registrationdatetime, 'yyyy-MM-dd')}"
                                                       class="time-input form-control valid" type="text"  >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ntocc">有效期起：</label>
                                            <div class="flex_right">
                                                <input name="effectiveFromCarrier" id="effectiveFromCarrier" th:value="${#dates.format(carrierinfo.effectiveFrom, 'yyyy-MM-dd')}"
                                                       class="time-input form-control valid ntoccVal" type="text" >
                                            </div>
                                        </div>
                                    </div>


                                </div>
                                <div class="row mt10">
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ntocc" style="width: 80px">有效期至：</label>
                                            <div class="flex_right">
                                                <input name="validUntilCarrier" id="validUntilCarrier" th:value="${#dates.format(carrierinfo.validUntil, 'yyyy-MM-dd')}"
                                                       class="time-input form-control valid ntoccVal" type="text" >
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left carInfo ntocc" >联系人名称：</label>
                                            <div class="flex_right">
                                                <input name="contactname" id="contactname" th:value="${carrierinfo.contactname}"
                                                       class="form-control valid" type="text"  maxlength="25">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ntocc"><span >联系人手机：</span></label>
                                            <div class="flex_right">
                                                <input name="contactmobiletelephonenumber" id="contactmobiletelephonenumber" th:value="${carrierinfo.contactmobiletelephonenumber}"
                                                       class="form-control valid" type="text"  maxlength="11">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!--说明提示-->
        <!--        <div class="panel-group" id="accordionSix">-->
        <!--            <div class="panel panel-default">-->

        <!--                <div id="collapseSix" class="panel-collapse collapse in">-->
        <!--                    <div class="panel-body">-->
        <!--                        <div class="err-tips-box">-->
        <!--                            <div class="err-tips">-->
        <!--                                <h2><i class="fa  fa-warning"></i> 说明提示：</h2>-->
        <!--                                <p>1、承运人类别：承运人（车辆所有人）是“个人”，承运人类别选择“个人承运人”；承运人（车辆所有人）是“企业”，承运人类别选择“企业承运人”。</p>-->
        <!--                                <p>2、承运人证照号： 承运人（车辆所有人）是“个人”，承运人证照号填写承运商身份证号；承运人（车辆所有人）是“企业”，承运人证照号填写企业社会统一代码。</p>-->
        <!--                                <p>3、注册日期：“车辆道路运输证”上的注册日期。</p>-->
        <!--                                <p>4、车辆能源类型：按车辆行驶证填写，例如：一般货车车辆能源类型为“柴油”。</p>-->
        <!--                                <p>5、如果车辆行驶证中车辆类型为牵引车，请填写挂车车牌号，挂车无需另行注册；车辆载质量(核定载质量)、满载车辆质量(总质量)填写挂车行驶证相应数据。</p>-->
        <!--                                <p>6、总质量4.5 吨及以下普通货运车辆：需要必填（使用性质、车辆识别代号、发证机关、注册日期、发证日期）；-->
        <!--                                    "车辆道路运输证号"、“道路运输许经营许可证号” 可填为“车籍地6 位行政区域代码+000000”。</p>-->
        <!--                            </div>-->
        <!--                        </div>-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--            </div>-->
        <!--        </div>-->


        <!--证件上传 start-->
        <!--        <div class="panel-group" id="accordionTwo">-->
        <!--            <div class="panel panel-default">-->
        <!--                <div class="panel-heading">-->
        <!--                    <h5 class="panel-title">-->
        <!--                        <a data-toggle="collapse" data-parent="#accordion"-->
        <!--                           href="tabs_panels.html#collapseTwo">证件上传</a>-->
        <!--                    </h5>-->
        <!--                </div>-->
        <!--                -->
        <!--            </div>-->
        <!--        </div>-->
        <!--证件上传 end-->
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
<!--        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler(0)"><i class="fa fa-check"></i>保-->
<!--            存-->
<!--        </button>&nbsp;-->
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler(1)"><i class="fa fa-check"></i>保存并提交审核
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">

    var prefix = ctx + "basic/car";

    var sysUploadFiles = [[${sysUploadFiles}]]

    //字典表照片类型
    var carPicType = [[${@dict.getType('car_pic_type')}]];
    //车辆类型
    var carType = [[${@dict.getType('car_type')}]];

    //根据车牌号码和车辆颜色查询车辆信息
    function searchCarInfo() {

        var checkCarNo = /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[A-Z])|([A-Z]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领超]))$/;

        var carno = $("#carno").val();
        var color = $("#vehiclelicenseplatecolor").val();
        var vehicleFeeName = $("#vehicleFeeName").val();//无车承运人
        var provinceCode = $("#provinceCode").val();//所属省
        if(!checkCarNo.test(carno)){
            $.modal.alertWarning("请输入正确车牌号");
            return false;
        }
        if(color == null || color == ""){
            $.modal.alertWarning("请选择车牌颜色 ");
            return false;
        }
        if(color != '1' && color != '2'){
            $.modal.alertWarning("只有车牌颜色为蓝色或黄色的车辆可以查询车辆信息");
            return false;
        }
        if($("#isVehicleFreeCarrier").val()=='1'){
            if(vehicleFeeName == null || vehicleFeeName == ""){
                $.modal.alertWarning("请输入无车承运人");
                return false;
            }
        }
        if(provinceCode == null || provinceCode == ""){
            $.modal.alertWarning("请选择所属省");
            return false;
        }


        var data = {};
        data.carno = carno;
        data.color = color;
        data.vehicleFeeName = vehicleFeeName;
        data.provinceCode = provinceCode;

        $.ajax({
            url : prefix + "/getCarInfo",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if(result.code == web_status.SUCCESS){
                    var data = result.data;
                    var msg = "<br>品牌型号："+data.brand
                        +"<br>车架号："+data.frameNo
                        +"<br>车辆类型："+data.vclTpNm
                        +"<br>车主："+data.cmpNm
                        +"<br>联系人："+data.vclWnrNm
                        +"<br>联系人手机："+data.vclWnrPhn
                        +"<br>车辆载质量："+data.vehicletonnage
                        +"<br>满载车辆质量："+data.vehicleladenweight
                        +"<br>车轴数："+data.vehicleAxis
                        +"<br>长度："+data.length
                        +"<br>宽度："+data.width
                        +"<br>高度："+data.height
                        +"<br>行驶证发证日期："+data.drvLicDt
                        +"<br>行驶证有效年数："+data.drvLicVdy
                        +"<br>车辆道路运输证号："+data.roadTransport
                        +"<br>许可证有效期："+data.vdtTm
                        +"<br>车辆所有人道路运输经营许可证："+data.roadLicense;
                    layer.confirm("<label style='font-weight: 600'>是否将获取的数据填入文本框？</label>"+msg,{
                        btn:["确认","取消"]
                    },function (index, layero) {
                        //修改信息
                        $("#brand").val(data.brand);
                        $("#frameNo").val(data.frameNo);
                        $("#vin").val(data.frameNo);
                        $("#axlenum").val(data.vehicleAxis);
                        $("#vehicletonnage").val(data.vehicletonnage);
                        $("#vehicleladenweight").val(data.vehicleladenweight);
                        $("#length").val(data.length);
                        $("#width").val(data.width);
                        $("#height").val(data.height);
                        $("#roadtransportcertificatenumber").val(data.roadTransport);
                        $("#roadTransportBusinessLicense").val(data.roadLicense);
                        //联系人
                        $("#contactname").val(data.vclWnrNm);
                        //联系人手机
                        $("#contactmobiletelephonenumber").val(data.vclWnrPhn);
                        //车辆所有人道路运输经营许可证
                        $("#permitnumber").val(data.roadLicense);
                        //车辆类型
                        inputCarType(data.vclTpNm);
                        //车载质量大于0取车载质量 否则取满载车辆质量
                        // if(data.vehicletonnage > 0 ){
                        //     $("#vehicletonnage").val(data.vehicletonnage);
                        // }else{
                        //     $("#vehicletonnage").val(data.vehicleladenweight);
                        // }
                        //$("#vehicleladenweight").val(data.vehicleladenweight);
                        $("#length").val(data.length);
                        $("#width").val(data.width);
                        $("#height").val(data.height);

                        //初次登记日期 20200517
                        var drvLicDt = data.drvLicDt
                        if($.common.isNotEmpty(drvLicDt)){
                            var year = drvLicDt.substring(0,4);
                            var month = drvLicDt.substring(4,6);
                            var day = drvLicDt.substring(6,8);
                            $("#licDate").val(year+"-"+month+"-"+day);
                            //发证日期
                            $("#issuedate").val(year+"-"+month+"-"+day);
                        }
                        //许可证有效期
                        $("#expiryDate").val(data.vdtTm);
                        layer.close(index);
                    },function (index) {
                        layer.close(index);
                    });
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
    }

    /**
     * 查询承运人信息
     */
    function searchCarrierInfo(){
        //无车承运人
        var vehicleFeeName = $("#vehicleFeeName").val();
        //车辆所有人道路运输经营许可证
        var permitnumber = $("#permitnumber").val();
        if(vehicleFeeName == null || vehicleFeeName == ""){
            $.modal.alertWarning("请填写承运人");
            return false;
        }
        if(permitnumber == null || permitnumber == ""){
            $.modal.alertWarning("请填写车辆所有人道路运输经营许可证");
            return false;
        }
        var data = {};
        data.carriername = vehicleFeeName;
        data.permitnumber = permitnumber;

        $.ajax({
            url : prefix + "/getCarrierInfo",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if(result.code == web_status.SUCCESS){
                    var data = result.data;
                    var carriertype = data.carriertype == 1 ? '个体承运人':'企业承运人'
                    var msg = "<br>承运人："+data.carriername
                        +"<br>车辆所有人道路运输经营许可证："+data.permitnumber
                        +"<br>无车承运人类别："+carriertype
                        +"<br>承运人证照号："+data.unifiedsocialcreditldentifier
                        +"<br>联系人："+data.contactname
                        +"<br>联系人手机："+data.contactmobiletelephonenumber
                    //+"<br>注册时间："+data.registrationdatetime

                    layer.confirm("<label style='font-weight: 600'>是否将获取的数据填入文本框？</label>"+msg,{
                        btn:["确认","取消"]
                    },function (index, layero) {
                        //修改信息
                        $("#vehicleFeeName").val(data.carriername);
                        $("#permitnumber").val(data.permitnumber);
                        $("#carriertype").val(data.carriertype);
                        $("#unifiedsocialcreditldentifier").val(data.unifiedsocialcreditldentifier);
                        $("#contactname").val(data.contactname);
                        $("#contactmobiletelephonenumber").val(data.contactmobiletelephonenumber);
                        $("#registrationdatetime").val(data.registrationdatetime);

                        layer.close(index);
                    },function (index) {
                        layer.close(index);
                    });
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });


    }

    /**
     *  带入车辆类型
     */
    function inputCarType(vclTpNm){
        for(var i = 0 ;i<carType.length;i++){
            if(vclTpNm === carType[i].dictLabel){
                $("#vehicleclassificationcode").val(carType[i].dictValue);
                //设置车型名称
                setCarTypeName();
            }
        }
    }

    /**
     * 总质量 4.5 吨及以下且符合无车承运人
     * 普通货运车辆必填，按照机动车行驶证填写。
     */
    function checkTotalQuality() {
        //是否符合无车承运人
        var isVehicleFreeCarrier = $("#isVehicleFreeCarrier").val();
        //满载车辆质量
        var vehicleladenweight = parseFloat($("#vehicleladenweight").val());

        if(isVehicleFreeCarrier == 1 && vehicleladenweight <= 4.5){
            $(".checkTotalQ;ualityColor").css("color","red")
            $(".checkTotalQuality").attr("required","true");

            //查询行政区域代码 显示
            $("#checkTotalQualityIsShow").show();
        }else{
            $(".checkTotalQualityColor").css("color","");
            $(".checkTotalQuality").removeAttr("required");

            //查询行政区域代码 隐藏
            $("#checkTotalQualityIsShow").hide();
        }


    }

    //新增校验逻辑
    function submitHandler(flag) {

        if(flag == 0){
            $(".fcff").css("display","none");
            $(".checkVal").removeAttr("required");
        }else{
            $(".fcff").css("display","-webkit-inline-box");
            $(".checkVal").attr("required","true");
            var roadtransportcertificatenumber = $("#roadtransportcertificatenumber").val();
            var vin = $("#vin").val();
            if(roadtransportcertificatenumber == '' && vin == ''){
                $.modal.alertWarning("车辆道路运输证号或车辆识别代号必填一个!");
                return false;
            }
            if($("#vehicletonnage").val() == 0 || $("#vehicleladenweight").val() == 0 ){
                $.modal.alertWarning("车辆载质量和车辆满载质量不能为0!");
                return false;
            }
        }

        if ($.validate.form()) {
            //提前加遮罩层
            $.modal.loading("正在处理中，请稍后...");
            var data = $('#form-car-add').serializeArray();
            data.push({"name": "submitFlag", "value": flag});
            $.operate.saveTab(prefix + "/edit", data);
        }
    }

    //新增提交
    function commit() {
        $.operate.saveTab(prefix + "/edit", $('#form-car-add').serialize());
    }

    /**
     * 设置车牌类型代码
     */
    function selectLicenseplatetypecode(value){
        //黄色对应大型汽车号牌
        if(value == 2){
            $("#licenseplatetypecode").val("01");
        }
        //蓝色对应小型汽车号牌
        if(value == 1){
            $("#licenseplatetypecode").val("02");
        }
        //其他对应其他
        if(value == 9){
            $("#licenseplatetypecode").val("99");
        }
    }

    /**
     * 设置车长名称
     */
    function setCarLenName() {
        $("#carLenName").val($("#carLengthId option:selected").text());
    }

    /**
     * 设置车型名称
     */
    function setCarTypeName() {
        $("#carTypeName").val($("#vehicleclassificationcode option:selected").text());
    }

    //选择承运人
    function selectCarrier() {
        var PREFIX = ctx + "basic/carrier";

        //车辆性质
        var carProp = $("#carProp").val();
        if(carProp==""){
            $.modal.alertWarning("请先选择车辆性质!");
            return false;
        }
        //自有车辆
        if(carProp=="1"){
            //字典表承运商类型
            $.modal.open("选择承运人", PREFIX + "/findCarrier?carrType=0");
        }
        //外协车辆
        if(carProp=="2"){
            $.modal.open("选择承运人", PREFIX + "/findCarrier?carrType=1,2,3,4,5");
        }
    }

    //选择司机
    function selectDriverList() {
        //车辆性质
        var carProp = $("#carProp").val();
        if(carProp==""){
            $.modal.alertWarning("请先选择车辆性质!");
            return false;
        }

        var carrierId = $("#carrierId").val();
        var PREFIX = ctx + "basic/driver";

        //自有车辆，一个司机只能在一辆车
        if(carProp=="1"){
            //字典表承运商类型
            $.modal.open("选择司机", PREFIX + "/checkboxSelectDriver?type=2&carrierId="+ carrierId, 1000, '');
        }
        //外协车辆
        if(carProp=="2"){
            $.modal.open("选择司机", PREFIX + "/checkboxSelectDriver?type=1&carrierId="+ carrierId, 1000, '');
        }
    }

    //初始化
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#expiryDate',
                type: 'date',
                trigger: 'click'
            });
        });

        /* layui.use('laydate', function() {
             var laydate = layui.laydate;
             laydate.render({
                 elem: '#licDate',
                 type: 'date',
                 trigger: 'click'
             });
         });*/

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#issuedate',
                type: 'date',
                trigger: 'click'
            });
        });

        //获取TID数字
        var carPicList = [[${carPicList}]];
        for(var i=0 ; i<carPicList.length ; i++){
            var picType = carPicList[i]["picType"];
            $("#tid"+picType).val(carPicList[i]["appendixId"]);
        }

        //初始化省市区
        var province = [[${car.provinceCode}]];
        $.provinces.init("provinceCode", "cityId","areaId",province,0,0);


        //循环图片路径信息，初始化图片上传区域
        var imagePath = [[${carPic}]];
        for (var key in imagePath) {
            var publishFlag = "cmt_" + key;
            var maxFileCount = 0 ;//最大上传文件数
            //只能上传一张
            var param = {
                maxFileCount: 1,
                publish: publishFlag,  //用于绑定下一步方法
                fileType: null//文件类型
            };
            var tid = "tid" + key;
            var imageId = "image" + key;
            $.file.loadEditFilesOCR(imageId, tid, imagePath[key], param);
        }

        var param = {
            maxFileCount: 0,
            publish: "cmt_4",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        var tid = "tid4";
        var imageId = "image4";
        $.file.loadEditFilesOCR(imageId, tid, sysUploadFiles, param);


        $("#form-car-add").validate({
            onkeyup: false,
            rules: {
                // unifiedsocialcreditldentifier:{
                //     remote: {
                //         url: prefix + "/checkUnifiedsocialcreditldentifier",
                //         type: "post",
                //         dataType: "json",
                //         data: {
                //             unifiedsocialcreditldentifier: function () {
                //                 return $.common.trim($("#unifiedsocialcreditldentifier").val());
                //             },
                //             carrierid: function () {
                //                 return $.common.trim($("#carrierid").val());
                //             }
                //         },
                //         dataFilter: function (data, type) {
                //             return $.validate.unique(data);
                //         }
                //     },
                // },
                contactmobiletelephonenumber:{
                    isPhone: true
                },
                length:{
                    max:99
                },
                width:{
                    max:99
                },
                height:{
                    max:99
                },
                vehicletonnage:{
                    max:999999
                },
                vehicleladenweight:{
                    max:999999
                },
            },
            messages: {
                // unifiedsocialcreditldentifier: {
                //     remote: "该统一社会信用代码已经存在"
                // },
                length:{
                    max:"最大值99"
                },
                width:{
                    max:"最大值99"
                },
                height:{
                    max:"最大值99"
                },
                vehicletonnage:{
                    max:"最大值999999"
                },
                vehicleladenweight:{
                    max:"最大值999999"
                },
            },
            focusCleanup: true
        });

        var isVehicleFreeCarrier  = $("#isVehicleFreeCarrier").val();
        if(isVehicleFreeCarrier == 1){
            $('.ntocc').css("color","red");
        }
        //符合无车承运人时：车牌颜色，满载车辆质量，车辆载质量，道路运输证号 必填且标红
        $('#isVehicleFreeCarrier').change(function(){
            var isVehicleFreeCarrier  = $(this).find(":selected").text();
            if(isVehicleFreeCarrier == '是'){
                $('.ntocc').css("color","red");
                //车牌颜色
                $("#vehiclelicenseplatecolor").attr("required","true");
                //满载车辆质量
                $("#vehicleladenweight").attr("required","true");
                //车辆载质量
                $("#vehicletonnage").attr("required","true");
                //道路运输证号
                $("#roadtransportcertificatenumber").attr("required","true");
                //车牌类型
                $("#licenseplatetypecode").attr("required","true");
                //经营许可证
                //$("#roadTransportBusinessLicense").attr("required","true");
                checkTotalQuality();
                //承运人
                $("#vehicleFeeName").attr("required","true");
                //无车承运人类别
                $("#carriertype").attr("required","true");
                //承运人证照号
                $("#unifiedsocialcreditldentifier").attr("required","true");
                //道路运输许经营许可证号
                $("#permitnumber").attr("required","true");
                //联系人名称
                $("#contactname").attr("required","true");
                //联系人手机
                $("#contactmobiletelephonenumber").attr("required","true");
                //注册时间
                $("#registrationdatetime").attr("required","true");
                //车轴数
                $("#axlenum").attr("required","true");
                //车辆能源类型
                $("#vehicleenergytype").attr("required","true");
                //营运车辆类型代码
                $("#operationType").attr("required","true");
                //有效期起
                $("#effectiveFrom").attr("required","true");
                //有效期至
                $("#validUntil").attr("required","true");
                //有效期起承运人
                $("#effectiveFromCarrier").attr("required","true");
                //有效期至
                $("#validUntilCarrier").attr("required","true");
            }
            if(isVehicleFreeCarrier == '否'){
                $('.ntocc').css("color","inherit");
                //车牌颜色
                //$("#vehiclelicenseplatecolor").removeAttr("required");
                //$("#form-car-edit").validate().element($("#vehiclelicenseplatecolor"));
                //满载车辆质量
                $("#vehicleladenweight").removeAttr("required");
                $("#form-car-add").validate().element($("#vehicleladenweight"));
                //车辆载质量
                $("#vehicletonnage").removeAttr("required");
                $("#form-car-add").validate().element($("#vehicletonnage"));
                //道路运输证字号
                $("#roadtransportcertificatenumber").removeAttr("required");
                $("#form-car-add").validate().element($("#roadtransportcertificatenumber"));
                //车牌类型
                $("#licenseplatetypecode").removeAttr("required");
                $("#form-car-add").validate().element($("#licenseplatetypecode"));
                //经营许可证
                $("#roadTransportBusinessLicense").removeAttr("required");

                //承运人
                $("#vehicleFeeName").removeAttr("required");
                $("#form-car-add").validate().element($("#vehicleFeeName"));
                //无车承运人类别
                $("#carriertype").removeAttr("required");
                $("#form-car-add").validate().element($("#carriertype"));
                //承运人证照号
                $("#unifiedsocialcreditldentifier").removeAttr("required");
                $("#form-car-add").validate().element($("#unifiedsocialcreditldentifier"));
                //道路运输许经营许可证号
                $("#permitnumber").removeAttr("required");
                $("#form-car-add").validate().element($("#permitnumber"));
                //联系人名称
                $("#contactname").removeAttr("required");
                $("#form-car-add").validate().element($("#contactname"));
                //联系人手机
                $("#contactmobiletelephonenumber").removeAttr("required");
                $("#form-car-add").validate().element($("#contactmobiletelephonenumber"));
                //注册时间
                $("#registrationdatetime").removeAttr("required");
                $("#form-car-add").validate().element($("#registrationdatetime"));

                //车轴数
                $("#axlenum").removeAttr("required");
                $("#form-car-add").validate().element($("#axlenum"));
                //车辆能源类型
                $("#vehicleenergytype").removeAttr("required");
                $("#form-car-add").validate().element($("#vehicleenergytype"));

                //营运车辆类型代码
                $("#operationType").removeAttr("required");
                $("#form-car-add").validate().element($("#operationType"));

                //有效期起
                $("#effectiveFrom").removeAttr("required");
                $("#form-car-add").validate().element($("#effectiveFrom"));

                //有效期至
                $("#validUntil").removeAttr("required");
                $("#form-car-add").validate().element($("#validUntil"));

                //有效期起承运人
                $("#effectiveFromCarrier").removeAttr("required");
                $("#form-car-add").validate().element($("#effectiveFromCarrier"));

                //有效期至
                $("#validUntilCarrier").removeAttr("required");
                $("#form-car-add").validate().element($("#validUntilCarrier"));

            }
        });

        jQuery.subscribe("cmt_1",cmtFile_1);
    });

    function cmtFile_1(){
        var data = {};
        data.tid = $("#tid1").val();
        data.side = 1;
        console.log("行驶证识别正面开始")

        var diffCarNo = false;

        $.ajax({
            url : ctx + "common/ocr/vehicleByTid",
            method : 'POST',
            data : data,
            success:function (result) {
                console.log(result);
                if(result.code == 0){
                    var carno = $("#carno").val();
                    if(carno != result.data.plate_num){
                        diffCarNo = true;
                    }else{
                        //车牌号码
                        $("#carno").val(result.data.plate_num);
                        //车辆识别代码
                        $("#vin").val(result.data.vin);
                        //所有人
                        $("#owner").val(result.data.owner);
                        $("#carriername").val(result.data.owner);
                        //营运车辆类型
                        var vehicle_type = result.data.vehicle_type;
                        $("#operationType option:contains('"+vehicle_type+"')").attr("selected",true);
                        //注册日期
                        var register_date = result.data.register_date;
                        if(register_date != null){
                            $("#registerdate").val(register_date.substring(0,4) + "-" + register_date.substring(4,6) + "-" + register_date.substring(6,8));
                        }
                        //发证日期
                        var issue_date = result.data.issue_date;
                        if(issue_date != null){
                            $("#issuedate").val(issue_date.substring(0,4) + "-" + issue_date.substring(4,6) + "-" + issue_date.substring(6,8));
                        }
                    }
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
        console.log("行驶证正面识别结束")

        if(diffCarNo){
            $.modal.alertWarning("识别车牌号与原车牌号不一致，无法填入");
            return false;
        }

        console.log("行驶证识别反面开始")
        data.side = 2;
        console.log("行驶证识别正面开始")
        $.ajax({
            url : ctx + "common/ocr/vehicleByTid",
            method : 'POST',
            data : data,
            success:function (result) {
                console.log(result);
                if(result.code == 0){
                    //能源类型
                    var energy_type = result.data.energy_type;
                    $("#vehicleenergytype option:contains('"+energy_type+"')").attr("selected",true);
                    //车辆载质量
                    var vehicleTonnage = result.data.approved_load;
                    if(vehicleTonnage != ""){
                        $("#vehicleTonnage").val(parseFloat(vehicleTonnage.substring(0,vehicleTonnage.length-2)/1000).toFixed(2));
                    }
                    //车辆总质量
                    var vehicleLadenWeight = result.data.gross_mass;
                    if(vehicleLadenWeight != ""){
                        $("#vehicleLadenWeight").val(parseFloat(vehicleLadenWeight.substring(0,vehicleTonnage.length-2)/1000).toFixed(2));
                    }
                    //长宽高
                    var size = result.data.overall_dimension.split("X") //外廓尺寸
                    if(size.length == 3) {
                        $("#length").val(parseFloat(size[0] / 1000).toFixed(2));
                        $("#width").val(parseFloat(size[1] / 1000).toFixed(2));
                        $("#height").val(parseFloat((size[2].substring(0, size[2].length - 2)) / 1000).toFixed(2));
                    }
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
        console.log("行驶证反面识别结束")
    }


    /**
     * 查询行政区域代码
     */
    function searchProvinceCode() {
        //所属省
        var provinceCode = $("#provinceCode").val();
        if(provinceCode == null || provinceCode == ""){
            $.modal.alertWarning("请选择所属省");
            return false;
        }
        var msg = "<br>行政区域代码："+provinceCode;

        layer.confirm("<label style='font-weight: 600'>是否将获取的数据填入文本框？</label>"+msg,{
            btn:["确认","取消"]
        },function (index, layero) {
            $("#roadtransportcertificatenumber").val(provinceCode+"000000");
            layer.close(index);
        },function (index) {
            layer.close(index);
        });

    }

    /**
     * 关键字提示查询
     */
    $("#vehicleFeeName").bsSuggest('init', {
        url: ctx + "basic/personalDriver/findCarrierInfo?keyword=",
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "carriername",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["carriername"],
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#vehicleFeeName").val(data.carriername);
        $("#isVehicleFreeCarrier").val(data.isVehicleFreeCarrier);
        $("#permitnumber").val(data.permitnumber);
        $("#unifiedsocialcreditldentifier").val(data.unifiedsocialcreditldentifier);
        $("#registrationdatetime").val(data.registrationdatetime);
        $("#effectiveFromCarrier").val(data.effectiveFrom);
        $("#validUntilCarrier").val(data.validUntil);
        $("#contactname").val(data.contactname);
        $("#contactmobiletelephonenumber").val(data.contactmobiletelephonenumber);
    })





</script>
</body>

</html>