<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('分配司机')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="col-sm-12 search-collapse">
        <form id="role-form" class="form-horizontal">
            <input type="hidden" name="carrId" th:value="${carrId}">
            <input type="hidden" id="wdId" name="wdId" th:value="${wdId}">

            <div class="select-list">
                <ul>
                    <li>
                        司机名称：<input type="text" name="driverName"/>
                    </li>
                    <li><input id="hiddenText" type="text" style="display:none"/>
                    </li>
                    <li></li>
                    <li>
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                </ul>
            </div>
        </form>
        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table id="bootstrap-table" data-mobile-responsive="true" style="table-layout: fixed;"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "basic/driver";
    $(function () {
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                $.table.search();
            }
        });
        var options = {
            url: prefix + "/driverList",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            clickToSelect: true,
            columns: [{
                checkbox: true
            },
                {
                    field: 'driverId',
                    title: '司机ID',
                    visible: false
                },

                {
                    field: 'driverCode',
                    align: 'left',
                    title: '司机编码',
                    width: '150px',
                },
                {
                    field: 'driverName',
                    align: 'left',
                    title: '司机名称',
                    width: '100px',
                }
            ]
        };

        $.table.init(options);
    });


    /**
     * 选择司机的提交方法
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.close();
        //选中司机id
        var driverId = $.table.selectColumns('driverId').join();
        //保存选中的司机id
        var url = "/carrier/entrustLot/saveDriver";
        var data = {
            driverId: driverId,
            entrustLotId: $("#wdId").val()
        };
        $.operate.save(url, data);

    }


</script>
</body>
</html>