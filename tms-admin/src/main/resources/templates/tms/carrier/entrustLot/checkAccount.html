<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('对账信息')"/>

</head>
<body >
    <div class="form-content">
        <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
            <div class="panel-body">
                <!--基础信息 begin-->
                <input name="entrustLotId" type="hidden" th:value="${lotId}">

                <div class="row">
                    <div class="col-md-6 col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-2">到付/回付现金(元)：</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="carrierCashFee" th:value="${carrierCashFee}"
                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this)" placeholder="到付/回付现金" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <!--<div class="col-md-6 col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-2">油卡金额(元)：</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="carrierOilFee" th:value="${carrierOilFee}"
                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this)" placeholder="油卡金额" autocomplete="off">
                            </div>
                        </div>
                    </div>-->
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-2">在途费用(元)：</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="carrierOnWayFee" th:value="${carrierOnWayFee}"
                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this)" placeholder="在途费用" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>

                <!--<div class="row">
                    <div class="col-md-6 col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-2">数量(件)：</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="carrierNum"  th:value="${carrierNum}"
                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this)" placeholder="数量" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-2">重量(吨)：</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="carrierWeight" th:value="${carrierWeight}"
                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this)" placeholder="重量" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-2">体积(m3)：</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="carrierVolume" th:value="${carrierVolume}"
                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this)" placeholder="体积" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>-->
                
            </div>
        </form>
    </div>


<th:block th:include="include :: footer"/>
<script th:inline="javascript">
var prefix = ctx + "carrier/entrustLot";


function submitHandler() {
    if ($.validate.form()) {
        var data = $("#form-out-quote").serializeArray();
        console.log(data)
        $.operate.save(prefix + "/saveCarrierFeeCheck", data);
    }
}

</script>

</body>
</html>