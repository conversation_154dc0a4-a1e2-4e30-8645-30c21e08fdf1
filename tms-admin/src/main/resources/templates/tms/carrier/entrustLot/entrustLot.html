<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('运单列表')"/>

</head>
<style>
    .fl{
        float: left;
    }
    .status1{
        width: 80px;
        background: #fff;
        border: 1px #eee solid;
        text-align: center;
        cursor: pointer;
        line-height: 20px;
        vertical-align: middle;
    }
    .act{
        background: #1ab492;
        color: #fff;
        border: 1px #1ab492 solid;
    }
    .lh26{
        line-height: 26px;
    }
    .fw{
        font-weight: 600;
    }
    .f16{
        font-size: 16px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #f3f3f4;
        border: 0;
        box-shadow: none;
        padding:5px 0 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <div class="form-group" style="margin: 0 -10px 0;">
                <form id="role-form" class="form-horizontal">
                    <div class="row">
                        <div class="col-md-12 col-sm-12">
                            <div class="fl over">
                                 <div th:class="${vbillstatus != 1 ? 'fl status1':'fl status1 act'}" flag onclick="tabto(this,1)">待分配</div>
                                <div th:class="${vbillstatus != 2 ? 'fl status1':'fl status1 act'}" flag onclick="tabto(this,2)">待提货</div>
                                <div th:class="${vbillstatus != 3 ? 'fl status1':'fl status1 act'}" flag onclick="tabto(this,3)">待到货</div>
                                <div th:class="${vbillstatus != 4 ? 'fl status1':'fl status1 act'}" flag onclick="tabto(this,4)">待回单上传</div>
                                <div th:class="${vbillstatus != 5 ? 'fl status1':'fl status1 act'}" flag onclick="tabto(this,5)">全部</div>
                            </div>
                            <input type="hidden" name="vbillstatus" id="vbillstatus"  th:value="${vbillstatus}">
                        </div>
                    </div>

                    <div class="row mt10">
                        <div class="col-md-2 col-sm-4">
                            <input type="text" class="form-control" placeholder="运单号" name="lot">
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <input type="text" placeholder="要求提货日期" class="form-control" id="reqDeliDateStart"  name="reqDeliDateStart" th:value="${dateRange}">
                            <input type="hidden" name="startDate" id="startDate" th:value="${startDate}">
                            <input type="hidden" name="endDate" id="endDate" th:value="${endDate}">
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <input type="text" class="form-control" placeholder="车牌" name="carNo">
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <input type="text" class="form-control" placeholder="司机" name="driverName">
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <select name="carLen" class="form-control" th:with="type=${@dict.getType('car_len')}">
                                <option value="">-- 车长 --</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <select name="carType" class="form-control" th:with="type=${@dict.getType('car_type')}">
                                <option value="">-- 车型 --</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt10">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <div class="col-md-4 col-sm-4">
                                    <select id="deliProvince" name="deliProvince"
                                            class="form-control valid"
                                            aria-invalid="false"></select>
                                    </select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="deliCity" name="deliCity" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="deliArea" name="deliArea" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-1" onclick="changeDiv()">
                            <img th:src="@{/img/change.png}" style="width: 40px;height: 40px;display: block;margin: 0 auto">
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <div class="col-md-4 col-sm-4">
                                    <select id="arriProvince" name="arriProvince"
                                            class="form-control valid"
                                            aria-invalid="false"></select>
                                    </select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="arriCity" name="arriCity" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="arriArea" name="arriArea" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="col-md-4 col-sm-4">
                                <select id="carrierConfirmStatus" name="carrierConfirmStatus" class="form-control" placeholder="对账状态">
                                    <option value="">请选择</option>
                                    <option value="-1">未对账</option>
                                    <option value="0">已对账</option>
                                    <option value="1">已确认</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                        class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset();reset()"><i
                                        class="fa fa-refresh"></i>&nbsp;重置</a>  
                            </div>
                        </div>   
                    </div>

                    <!-- <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">运单号：</label>
                                <div class="col-sm-8">
                                    <input name="lot" placeholder="请输入运单号" class="form-control" type="text"
                                        aria-required="true">

                                </div>
                            </div>
                        </div>
                        <input id="hiddenText" type="text" style="display:none" />

                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">运单状态：</label>
                                <div class="col-sm-8">
                                    <select name="vbillstatus" class="form-control">
                                        <option value="">--请选择--</option>
                                        <option th:each="dict : ${vbillstatus}" th:text="${dict.context}" th:value="${dict.value}" ></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6">
                        </div>


                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-6"></label>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                        class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset();reset()"><i
                                        class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div> -->
                </form>
            </div>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <!-- <a class="btn btn-primary single disabled" id="disCar" onclick="disCar()"
               shiro:hasPermission="carrier:entrustLot:disCar">
                <i class="fa fa-ambulance"></i> 分配车
            </a>
            <a class="btn btn-primary single disabled" id="disDriver" onclick="disDriver()"
               shiro:hasPermission="carrier:entrustLot:disDriver">
                <i class="fa fa-user"></i> 分配司机
            </a> -->
            <a class="btn btn-warning" onclick="exportData();" ><i class="fa fa-download"></i> 导出 </a>
            <a class="btn btn-primary single disabled" onclick="checkAccounts()" ><i class="fa fa-download"></i> 对账 </a>
            <!--<a class="btn btn-warning" onclick="exportCheckAccountTemplate();" ><i class="fa fa-download"></i> 导出对账模板 </a>-->
            <a class="btn btn-success" onclick="$.table.importExcel();importCheckAccount();" ><i class="fa fa-download"></i> 导入对账数据 </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>


<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "carrier/entrustLot";
    var vbillstatus = [[${vbillstatus}]];
    var carrId = [[${carrId}]];

    var findCarLength = [[${@dict.getType('car_len')}]];
    var contractFlag = [[${@permission.hasPermi('carrier:contract:view')}]];
    var entrustFlag = [[${@permission.hasPermi('carrier:entrust:view')}]];

    if(null==carrId) {
        $("#disDriver").addClass("disabled");
        $("#disCar").addClass("disabled");
        $("#disDriver").removeClass("single");
        $("#disCar").removeClass("single");
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        $.provinces.init("deliProvince", "deliCity", "deliArea");
        $.provinces.init("arriProvince", "arriCity", "arriArea");

        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importCheckAccount",
            importTemplateUrl: prefix + "/exportCheckAccountTemplate",
            modalName : "运单",
            showToggle:false,
            showColumns:true,
            clickToSelect:true,
            fixedColumns: true, 
            fixedNumber: 3,
            height: 560,
            columns: [{
                checkbox: true
            },
               {
                    title: '操作',
                   align: 'left',
                   width: '40px',
                   field: 'entrustLotId',
                   formatter: function(value, row, index) {
                       var actions = [];
                       let vbillstatusRow=row.vbillstatus;

                       let carnoId = row.carnoId;
                       let driverId = row.driverId;

                       let ifAllReceiptUpload = row.ifAllReceiptUpload;
                       let ifAllConfirm = row.ifAllConfirm;

                       let vbillstatus = null;

                       switch(vbillstatusRow){
                           case 0:
                               if((carnoId == null || driverId == null)){
                                   vbillstatus = 1;
                               }else{
                                   vbillstatus = 2;
                               }
                               break;
                           case 1:
                               vbillstatus = 2;
                               break;
                           case 2:
                               vbillstatus = 3;
                               break;
                           case 3:
                               vbillstatus = 3;
                               break;
                           case 4:
                               //if(ifAllReceiptUpload == 0 || ifAllReceiptUpload == 1){
                                   vbillstatus = -1;
                               //}
                               //if(ifAllConfirm == 2){
                                   vbillstatus = 4;
                               //}
                               break;
                           default:
                               vbillstatus = null;
                       }





                        if(vbillstatus==2||vbillstatus==1){
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="一键提货"onclick="pick(\'' + row.entrustLotId + '\')"><i class="fa fa-upload" style="font-size: 15px;"></i></a>');
                        }else if(vbillstatus==3){
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="一键到货"onclick="arrive(\'' + row.entrustLotId + '\')"><i class="fa fa-download" style="font-size: 15px;"></i></a>');
                        }else if(vbillstatus==4){
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="一键回单"onclick="receipt(\'' + row.entrustLotId + '\',\'' + row.ifAllConfirm + '\')"><i class="fa fa-file" style="font-size: 15px;"></i></a>');
                        }

                        if((vbillstatus==1 ) && carrId != null){
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="分配调度"onclick="dispatch(\'' + row.entrustLotId + '\')"><i class="fa fa-transgender-alt" style="font-size: 15px;"></i></a>');
                        }
                        // if(row.vbillstatus==1 || row.vbillstatus==2){
                        //     actions.push('<a class="btn btn-xs ' + entrustFlag + '" href="javascript:void(0)" title="委托单"onclick="detail(\'' + row.entrustLotId + '\')"><i class="fa fa-clone" style="font-size: 15px;"></i></a>');
                        // }
                        //actions.push('<a class="btn btn-xs ' + contractFlag + ' " href="javascript:void(0)" title="合同" onclick="contract(\'' + row.entrustLotId + '\')"><i class="fa fa-map-o" style="font-size: 15px;"></i></a>');
                        return actions.join('');
                    }

                },

                {
                    visible: false,
                    field: 'carrierId'
                },

                {
                    title: '运单号',
                    align: 'left',
                    field: 'lot',
                    width: '100px'
                },
                {
                    title: '要求提货/到货日期',
                    field: 'reqDeliDate',
                    align: 'left',
                    width: '140px',
                    formatter: function status(value, row, index) {
                        return $.table.tooltip(value)+"<br/>"+$.table.tooltip(row.reqArriDate);
                    }
                },
                {
                    title: '装卸货地址',
                    align: 'left',
                    formatter: function status(value,row) {
                        let isMultiple=row.entrustList[0].isMultiple;
                        let shippingAddressList=row.entrustList[0].shippingAddressList;
                        if(isMultiple==1){
                            return shippingAddressList.sort(sortNum).map(item=>{
                                if(item.addressType == 0){
                                    return '<span class="label label-warning pa2">装</span>'+$.table.tooltip(item.provinceName+item.cityName+item.areaName+item.addrName);
                                }
                                if(item.addressType == 1){
                                    return '<span class="label label-success pa2">卸</span>'+$.table.tooltip(item.provinceName+item.cityName+item.areaName+item.addrName);
                                }
                            }).join("<br/>");
                        }else{
                            let deliAddr=row.deliProvinceName+row.deliCityName+row.deliAreaName+row.entrustList[0].deliDetailAddr;
                            let arriAddr=row.arriProvinceName+row.arriCityName+row.arriAreaName+row.entrustList[0].arriDetailAddr;
                            return '<span class="label label-warning pa2">装</span>'+$.table.tooltip(deliAddr)+'<br/><span class="label label-success pa2">卸</span>'+$.table.tooltip(arriAddr);
                        }
                    }
                },
                {
                    title: '货品信息',
                    align: 'left',
                    field: 'entGoodsList',
                    width: '140px',
                    formatter: function (value, row, index) {
                        
                        let goodsText=value.map(item=>{
                            let text=item.goodsName+"<br/>";
                            let goods=[]
                            if(item.num){
                                goods.push(item.num+'件');
                            }
                            if(item.weight){
                                goods.push(item.weight+'吨');
                            }
                            if(item.volume){
                                goods.push(item.volume+'m³');
                            }
                            return text+goods.join(' | ');
                        })

                        return goodsText.join("<br/>")
                    }
                },
                {
                    title: '要求车长车型',
                    align: 'left',
                    field: 'carLenName',
                    width: '140px',
                    formatter: function (value, row, index) {
                        let text=''
                        if(value) {
                            text=$.table.tooltip(value)+"米";
                        }
                        return text+$.table.tooltip(row.carTypeName);
                    }
                },
                {
                    title: '司机',
                    align: 'left',
                    field: 'driverName'
                },
                {
                    title: '车牌号',
                    align: 'left',
                    field: 'carNo'
                },
                {
                    title: '司机注意事项',
                    align: 'left',
                    field: 'appMemo'
                },

                {
                    title: '对账状态',
                    align: 'align',
                    field: 'carrierConfirmStatus',
                    width:'100px',
                    formatter: function(value,row){
                        var txt ='未对账';
                        if(value == 0){
                            txt = '已对账';
                        }
                        if(value == 1){
                            txt = '对账已确认';
                        }
                        return txt;
                    }
                },
                {
                    title: '对账金额',
                    align: 'align',
                    width:'100px',
                    formatter:function(value,row){
                        var txt = [];
                        if(row.carrierCashFee)txt.push(row.carrierCashFee)
                        if(row.carrierOilFee)txt.push(row.carrierOilFee);
                        if(row.carrierOnWayFee)txt.push(row.carrierOnWayFee);
                        return txt.join("/")
                    }
                },
                /*{
                    title: '对账货量',
                    align: 'align',
                    width:'100px',
                    formatter:function(value,row){
                        var txt = [];
                        if(row.carrierNum)   txt.push(row.carrierNum);
                        if(row.carrierWeight)txt.push(row.carrierWeight);
                        if(row.carrierVolume)txt.push(row.carrierVolume);
                        return txt.join("/")
                    }
                },*/
                {
                    title: '车载货源',
                    field: 'entrustCnt',
                    align: 'left',
                    width: '40px',
                    formatter: function status(value, row, index) {
                        return '<a class="btn btn-xs' + entrustFlag + '" href="javascript:void(0)" title="车载货源" onclick="detail(\'' + row.entrustLotId + '\')">'+$.table.tooltip(value)+'</a>';
                    }
                }
            ]
        };
        $.table.init(options);
    });

    /**
     * 日期插件
     */
     layui.use('laydate', function(){
        var laydate = layui.laydate;
         laydate.render({
             elem: '#reqDeliDateStart',
             type: 'date',
             range: true,
             done: function (value, date, endDate) {
                 let time=value.split(" - ");
                 $("#startDate").val(time[0]);
                 $("#endDate").val(time[1]);
             }
         });
     });

    // 地址排序
    function sortNum(a,b) {
        return a.addressType-b.addressType;
    }
    function changeDiv(){
        var deliProvince= $('#deliProvince').val()
        var deliCity= $('#deliCity').val()
        var deliArea= $('#deliArea').val()
        var arriProvince= $('#arriProvince').val()
        var arriCity= $('#arriCity').val()
        var arriArea= $('#arriArea').val()
        $.provinces.init("deliProvince", "deliCity", "deliArea", arriProvince, arriCity, arriArea);
        $.provinces.init("arriProvince", "arriCity", "arriArea", deliProvince, deliCity, deliArea);
        //searchPre()
        $.table.search()
    }

    //分配车
    function disCar() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["vbillstatus"] !== 1 || bootstrapTable[0]["vbillstatus"] !== 2) {
            $.modal.alertWarning("部分提货/已提货的运单不能分配车");
            return;
        }
        //运单id
        var wdId = $.table.selectColumns('entrustLotId');
        //承运商id
        var carrId = $.table.selectColumns('carrierId');
        var url = prefix + "/disCar?wdId="+ wdId+"&carrId="+carrId;
        $.modal.open("分配车", url);
    }

    //分配司机
    function disDriver() {
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["vbillstatus"] !== 1 || bootstrapTable[0]["vbillstatus"] !== 2) {
            $.modal.alertWarning("部分提货/已提货的运单不能分配司机");
            return;
        }
        //运单id
        var wdId = $.table.selectColumns('entrustLotId');
        //承运商id
        var carrId = $.table.selectColumns('carrierId');
        var url = prefix + "/disDriver?wdId="+ wdId+"&carrId="+carrId;
        $.modal.open("分配司机", url);
    }

    //委托单
    function detail(id) {
        let vbillstatus=$("#vbillstatus").val();
        // if(vbillstatus!=1){
        //     var url = ctx + "carrier/entrust/"+id;
        //     $.modal.openTab("委托单", url);
        // }
        var url = ctx + "carrier/entrust/"+id;
        $.modal.openTab("委托单", url);
    }

    //运单合同
    function contract(id) {
        var url = ctx + "basic/carrier/contractView/"+id;
        $.modal.openTab("运单合同" , url);
    }

    function tabto(btn,status) {
        if (status == null) {
            $("#vbillstatus").val('');
        } else {
            $("#vbillstatus").val(status);
        }
        $(".status1").removeClass("act");
        $(btn).addClass("act")
        $.table.search()
    }

    function dispatch(entrustLotId){
        var url = ctx + "basic/carrier/disDispatch/"+entrustLotId;
        $.modal.openTab("分配调度", url);
    }

    function pick(entrustLotId) {
        var url = ctx + "carrier/entrustLot/disPick/"+entrustLotId;
        $.modal.open("一键提货", url,900);
    }

    function arrive(entrustLotId) {
        var url = ctx + "carrier/entrustLot/disArrive/"+entrustLotId;
        $.modal.open("一键到货", url,900);
    }
    function receipt(entrustLotId,ifAllConfirm) {
        if(ifAllConfirm == 2){
            var url = ctx + "carrier/entrustLot/disReceipt1/"+entrustLotId;
            $.modal.open("一键回单", url,900);
        }else{
            var url = ctx + "carrier/entrustLot/disReceipt/"+entrustLotId;
            $.modal.open("一键回单", url,900);
        }

    }
    function checkAccounts() {
        var lotId =  $.table.selectColumns("entrustLotId").join();
        var url = ctx + "carrier/entrustLot/checkAccountView?lotId="+lotId;
        $.modal.open("对账", url,900);
    }

    /**
     * 导出数据
     */
    function exportData(){
        $.table._option.exportUrl = prefix + "/export";
        $.table._option.modalName = '运单';
        $.table.exportExcel();
    }

    /**
     * 导出模板数据
     */
    function exportCheckAccountTemplate(){
        $.table._option.exportUrl = prefix + "/exportCheckAccountTemplate";
        $.table._option.modalName = '对账';
        $.table.exportExcel();
    }

    /**
     * 导入对账数据
     */
    function importCheckAccount(){
    }


</script>

</body>

<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <!--<input type="checkbox" id="updateSupport" name="updateSupport" title="如果登录账户已经存在，更新这条数据。"> 是否更新已经存在的用户数据-->
                &nbsp;	<a onclick="exportCheckAccountTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>