<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('委托单列表')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运单号：</label>
                            <div class="col-sm-8">
                                <input name="lot" placeholder="请输入运单号" class="form-control" type="text"
                                       aria-required="true">

                            </div>
                        </div>
                    </div>
                    <div class="col-md-offset-6 col-md-3 col-sm-6">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table id="bootstrap-table" data-mobile-responsive="true" style="table-layout: fixed;"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>

<script th:inline="javascript">

    var prefix = ctx + "carrier/waybill";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            showToggle:false,
            showColumns:true,
            fixedColumns: true,
            fixedNumber:0,
            height: 550,
            columns: [{
                checkbox: true
            },
               {
                    title: '操作',
                    width: '130px',
                    align: 'center',
                   field: 'ENTRUST_LOT_ID',
                   formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="委托单明细"onclick="detail(\'' + row.ENTRUST_LOT_ID + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        return actions.join('');
                    }

                },

                {
                    title: 'id',
                    width: '100px',
                    visible: false,
                    field: 'ENTRUST_LOT_ID'
                },
                {
                    visible: false,
                    field: 'CARRIER_ID'
                },
                {
                    title: '运单号',
                    width: '100px',
                    align: 'left',
                    field: 'LOT'
                },

                {
                    title: '运单状态',
                    width: '100px',
                    align: 'left',
                    field: 'VBILLSTATUS'
                },
                {
                    title: '提货日期',
                    width: '100px',
                    align: 'left',

                },
                {
                    title: '到货日期',
                    width: '100px',
                    align: 'left',
                },
                {
                    title: '发货地址',
                    width: '100px',
                    align: 'left',
                },
                {
                    title: '到货地址',
                    width: '100px',
                    align: 'left',
                },
                {
                    title: '司机',
                    width: '100px',
                    align: 'left',
                    field: 'DRIVER_NAME'
                },
                {
                    title: '车牌号',
                    width: '100px',
                    align: 'left',
                    field: 'CARNO'
                },
                {
                    title: '备注',
                    width: '100px',
                    align: 'left',
                }
            ]
        };

        $.table.init(options);
    });


    /** 获取选中行*/
    function getChecked() {
        return $.btTable.bootstrapTable('getSelections');
    }

    /**
     * 选择承运人后的提交方法
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.close();

        // 选中的承运人ID
        parent.$("#driverId").val(rows.join());
        // 选中的司机名称
        parent.$("#driverName").val($.table.selectColumns("driverName"));
        parent.$("#phone").val($.table.selectColumns("phone"));
        parent.$("#driverMobile").val($.table.selectColumns("phone"));
    }





</script>

</body>
</html>