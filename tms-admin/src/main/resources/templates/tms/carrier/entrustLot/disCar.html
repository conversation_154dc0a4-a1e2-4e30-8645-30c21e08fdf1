<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('分配车辆')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="col-sm-12 search-collapse">
        <form id="role-form" class="form-horizontal">
            <input type="hidden" name="carrierId" th:value="${carrId}">
            <input type="hidden" id="wdId" name="wdId" th:value="${wdId}">

            <div class="select-list">
                <ul>
                    <li>
                        车牌号：<input type="text" name="carno"/>
                    </li>
                    <li><input id="hiddenText" type="text" style="display:none"/>
                    </li>
                    <li></li>
                    <li>
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                </ul>
            </div>
        </form>
        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table id="bootstrap-table" data-mobile-responsive="true" style="table-layout: fixed;"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var car_type = [[${@dict.getType('car_type')}]];//车辆类型
    var car_len = [[${@dict.getType('car_len')}]];

    var prefix = ctx + "basic/car";

    function detail() {
        var url = prefix + "/detail";
        $.modal.openTab($.table._option.modalName + "详细", url);
    }

    $(function () {
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                $.table.search();
            }
        });
        var options = {
            url: prefix + "/carList",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            clickToSelect: true,
            columns: [{
                radio: true
            },
                {
                    field: 'carId',
                    visible: false
                },
                {
                    title: '车牌号',
                    align: 'left',
                    field: 'carno'
                },
                {
                    title: '车长(米)',
                    align: 'left',
                    field: 'carLengthId',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(car_len, value);
                    }
                },
                {
                    title: '车型',
                    align: 'left',
                    field: 'vehicleclassificationcode',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(car_type, value);
                    }
                }
            ]
        };

        $.table.init(options);
    });

    /**
     * 选择司机的提交方法
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.close();
        //选中车辆id
        var carId = $.table.selectColumns('carId').join();
        var carno = $.table.selectColumns('carno').join();
        var url = "/carrier/entrustLot/saveCar";
        var data = {
            carId: carId,
            carno: carno,
            entrustLotId: $("#wdId").val()
        };
        //保存选中的司机id
        $.operate.save(url, data);
    }


</script>

</body>
</html>