<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('分配调度')" />
</head>
<style>
    .fl{
        float: left;
    }
    .lh26{
        line-height: 26px;
    }
    .fw{
        font-weight: 600;
    }
    .f14{
        font-size: 14px;
    }
    .sm{
        background: rgba(255,144,8,.2);
        /* border: 1px solid #FF9008; */
        border-radius: 5px;
        padding: 5px 10px;
        margin: 0 -5px 5px;
    }
    .sm_icon{
        width: 18px;
        height: 18px;
        background: #FF9008;
        color: #fff;
        border-radius: 50%;
        display: inline-block;
        line-height: 18px;
        text-align: center;
        font-size: 12px;
    }
    .sm_text{
        color: #FF9008;
        display: inline-block;
        margin-left: 10px;
        line-height: 20px;
    }
    .flex{
        display: flex;
        align-items:center;
        justify-content:space-between;
        color: #808080;
    }
    .flex_left{
        width: 80px;
        line-height: 26px;
        /* text-align: right; */
        color: #333333 !important;
        margin-bottom: 0;
    }
    .flex_right{
        min-width:0;
        flex:1;
    }
</style>
<body class="gray-bg">
<div class="container-div"> 
    <div class="row">
        <form id="form-enquiry-add" class="form-horizontal" novalidate="novalidate">
            <input id="carrierId" name="carrierId" th:value="${carrier.carrierId}" type="hidden">
            <input name="carrType" id="carrType" th:value="${carrier.carrType}" type="hidden">
            <input name="lotId" id="lotId" th:value="${entrustLotId}" type="hidden">

            <div class="col-sm-12 search-collapse">
                <div class="row">
                    <div class="col-md-12 col-sm-12">
                        <span class="f14 fw">委托单要求</span>
                    </div>
                </div>
                <div class="row mt10">
                    <div class="col-md-12 col-sm-12 flex" style="justify-content: flex-start;">
                        <span>
                            <div th:text="${#dates.format(entrustLot.reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                            <div th:text="${entrustLot.deliProvinceName+entrustLot.deliCityName+entrustLot.deliAreaName}"></div>
                        </span>
                        <span style="margin: 0 10px;"><i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394;"></i></span>
                        <span>
                            <div th:text="${#dates.format(entrustLot.reqArriDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                            <div th:text="${entrustLot.arriProvinceName+entrustLot.arriCityName+entrustLot.arriAreaName}"></div>
                        </span>
                       
                    </div>
                </div>
                <div class="row mt10">
                    <div class="col-md-3 col-sm-12">
                        <span> 要求车长车型：</span>
                        <span th:text="${@dict.getLabel('car_len',entrustLot.carLen)}"></span>米
                        <span th:text="${@dict.getLabel('car_type',entrustLot.carType)}"></span>
                    </div>
                    <div class="col-md-3 col-sm-12">
                        <span> 货品信息：</span>
                        <span th:text="${entrustLot.goodsName }"></span>
                        <span th:text="${entrustLot.numCount}+'件 |'" th:if="${entrustLot.numCount!=null}"></span>
                        <span th:text="${entrustLot.weightCount}+'吨 |'" th:if="${entrustLot.weightCount!=null}"></span>
                        <span th:text="${entrustLot.volumeCount}+'m³'" th:if="${entrustLot.volumeCount!=null}"></span>
                        <input type="hidden" name="numCount" th:value="${entrustLot.numCount}">
                        <input type="hidden" name="weightCount" th:value="${entrustLot.weightCount}">
                        <input type="hidden" name="volumeCount" th:value="${entrustLot.volumeCount}">
                    </div>
                </div>
                <div class="row mt10">
                    <div class="col-md-12 col-sm-12">
                        <div class="sm">
                            <span class="sm_text">客户备注：</span>
                            <span th:text="${entrustLot.memo}"></span>
                        </div>
                    </div>
                </div>
            </div>
        
            <div class="col-sm-12 search-collapse">
                <div class="row">
                    <div class="col-md-12 col-sm-12">
                        <span class="f14 fw">调度</span>
                    </div>
                </div>
    
                <div class="row mt10">
                    <div class="col-md-3 col-sm-6">
                        <div class="flex">
                            <label class="flex_left"><span style="color: red">*</span>车牌号：</label>
                            <div class="flex_right">
                                <input name="carno" id="carno" placeholder="请输入车牌号" class="form-control valid"
                                        type="text" onclick="selectCar()" autocomplete="off" readonly required>
                                <input name="carnoId" id="carnoId" type="hidden">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="flex">
                            <label class="flex_left"><span style="color: red">*</span>司机：</label>
                            <div class="flex_right">
                                <input name="driverName" id="driverName" placeholder="请输入司机" class="form-control valid" type="text"
                                        onclick="selectDriver()" readonly required>
                                <input name="driverId" id="driverId" type="hidden">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="flex">
                            <label class="flex_left">司机手机：</label>
                            <div class="flex_right">
                                <input name="driverMobile" id="driverMobile" type="text" class="form-control" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="flex">
                            <label class="flex_left"><span style="color: red">*</span>预计到达：</label>
                            <div class="flex_right">
                                <div class="input-group">
                                    <input name="estimatedArrivalTime" id="estimatedArrivalTime" type="text"
                                        class="time-input form-control" readonly autocomplete="off"
                                        placeholder="预计到达时间" required>
                                        <span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
                                </div>
                                <label class="error" for="estimatedArrivalTime" style="display: none;"></label>
                            </div>
                        </div>
                    </div>
                </div>
    
                <div class="row mt10">
                    <div class="col-md-3 col-sm-6">
                        <div class="flex">
                            <label class="flex_left">挂车：</label>
                            <div class="flex_right">
                                <div class="input-group">
                                    <input name="trailerNo" id="trailerNo" placeholder="请输入挂车"
                                            class="form-control" type="text" onclick="selectTrailerCar()" autocomplete="off" readonly>
                                    <input name="trailerId" id="trailerId" type="hidden">
                                    <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-9 col-sm-12">
                        <div class="flex">
                            <label class="flex_left">备注：</label>
                            <div class="flex_right">
                                <textarea name="memo"  placeholder="请输入" id="memo" th:text="${memo}" maxlength="200" class="form-control valid" rows="1"></textarea>
                            </div>
                        </div> 
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" id="confirm" class="btn btn-sm btn-primary" onclick="submitHandler()"><i
                class="fa fa-check"></i>确认
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer" />
<script th:inline="javascript">
     var segmentIds = [[${segmentIds}]];

    $(function () {

        
    })
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-enquiry-add").serializeArray();
            $.operate.saveTab(ctx + "carrier/entrustLot/dispatchCarAndDriver", data);
           /* $.ajax({
                url: ctx + "carrier/entrustLot/dispatchCarAndDriver",
                method: 'post',
                dataType: "json",
                data: data,
                success: function (result) {
                    if (result.code == 0) {
                        closeItem();
                    } else {

                    }

                }
            });*/
        }
    }
    function onMemo(data){

    }

     /**
     * 日期插件
     */
     layui.use('laydate', function(){
        var laydate = layui.laydate;
        //预计到达时间
        laydate.render({
            elem: '#estimatedArrivalTime',
            type: 'datetime',
            trigger: 'click'
        });
    });

    /**
     *  选择选择车辆
     */
    function selectCar(){
        var carrierId = $("#carrierId").val();
        var carrType = $("#carrType").val();

        $.modal.open("选择车辆", ctx + "basic/car/selectCarLicCarr?segmentIds=" + segmentIds + "&carrierId=" + carrierId + "&carrType=" + carrType + "&type=1", '', '', function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //清空车长
            //$("#carLenId").val("");
            //$("#carLenName").val("");
            //清空车型
            //$("#carTypeCode").val("");
            //$("#carTypeName").val("");
            //清空司机
            $("#driverName").val("");
            $("#driverId").val("");
            //清空司机手机
            $("#driverMobile").val("");

            //$("#cardId").val("");

            //车辆id
            $("#carnoId").val(rows[0]["carId"]);
            //车牌
            $("#carno").val(rows[0]["carno"]);

            //车长
            /*var carLengthId = rows[0]["carLengthId"];
            carLen.forEach(function (value, index, array) {
                if (value.dictValue == carLengthId) {
                    $("#carLenId").val(carLengthId);
                    $("#carLenName").val(value.dictLabel)
                }
            });*/
            //车型
            /*var carTypeId = rows[0]["vehicleclassificationcode"];
            carType.forEach(function (value, index, array) {
                if (value.dictValue == carTypeId) {
                    $("#carTypeCode").val(carTypeId);
                    $("#carTypeName").val(value.dictLabel)
                }
            });*/

            var carrType = $("#carrType").val();
            //根据承运商查询对应的司机
            $.ajax({
                url: ctx + "basic/driver/getDriverByCarrTypeAndCarId",
                type: "post",
                dataType: "json",
                data: {carId: rows[0]["carId"],carrType:carrType},
                success: function (result) {
                    if (result.code == 0) {
                        $("#driverName").val(result.data.driverName);
                        $("#driverId").val(result.data.driverId);
                        $("#driverMobile").val(result.data.driverMobile);
                        //$("#cardId").val(result.data.cardId);

                        //校验司机
                        //$("#form-dispatch-add").validate().element($("#driverName"));
                    }
                }
            });
            //校验车辆
            //$("#form-dispatch-add").validate().element($("#carno"));


            layer.close(index);
        });
    }

    /**
     * 选择司机
     */
    function selectDriver() {
        //承运商id
        var carrierId = $("#carrierId").val();
        $.modal.open("选择司机", ctx + "basic/driver/checkboxSelectDriverCarr?type=1&carrierId=" + carrierId, "", "", function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //清空数据
            $("#driverMobile").val("");

            //拼接司机名称
            var driverName = $.map(rows,function (row) {
                return row["driverName"];
            }).join(",");
            //拼接司机id
            var driverId = $.map(rows,function (row) {
                return row["driverId"];
            }).join(",");
            //拼接司机手机
            var driverMobile = $.map(rows,function (row) {
                return row["phone"];
            }).join(",");
            /*var cardId = $.map(rows,function(row){
                return row["cardId"]
            }).join(",");*/

            //回填数据
            $("#driverName").val(driverName);
            $("#driverId").val(driverId);
            $("#driverMobile").val(driverMobile);
            //$("#cardId").val(cardId);

            //$("#form-dispatch-add").validate().element($("#driverName"));
            //选择打款账户
            //selectPaymentAccount();
            layer.close(index);
        });
    }

   

    /**
     *  选择选择挂车
     */
     function selectTrailerCar(){
        //承运商id
        var carrierId = $("#carrierId").val();
        $.modal.open("选择挂车", ctx + "basic/car/selectCarLic?carrierId=" + carrierId + "&type=2", '', '', function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //车辆id
            $("#trailerId").val(rows[0]["carId"]);
            //车牌
            $("#trailerNo").val(rows[0]["carno"]);

            layer.close(index);
        });
    }
</script>
</body>
</html>