<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('下单')"/>
</head>

<body>
<div class="form-content">
    <form id="form-user-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="deptId" type="hidden" id="treeId">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>订单号：</label>
                                    <div class="col-sm-7">
                                        <input name="" class="form-control" type="text"
                                               maxlength="30" required="" aria-required="true">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>发货单号：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="time-input form-control"
                                               name="params[beginTime]" lay-key="1">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>运单状态：</label>
                                    <div class="col-sm-7">
                                        <select name="" class="form-control valid" aria-invalid="false">
                                            <option value="1">请选择</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>提货日期：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="time-input form-control"
                                               name="params[endTime]" lay-key="2">
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>到货日期：</label>
                                    <div class="col-sm-7">
                                        <input name="" class="form-control" type="text"
                                               maxlength="30" required="" aria-required="true">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>所属线路：</label>
                                    <div class="col-sm-7">
                                        <select name=""  class="form-control valid" aria-invalid="false"></select>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>客户名称：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="time-input form-control"
                                               name="params[beginTime]" lay-key="1">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>结算客户：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="time-input form-control"
                                               name="params[endTime]" lay-key="2">
                                    </div>
                                </div>
                            </div>


                        </div>

                        <div class="row">

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>车型：</label>
                                    <div class="col-sm-7">
                                        <select name=""  class="form-control valid" aria-invalid="false"></select>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>车长（米）：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="time-input form-control"
                                               name="params[beginTime]" lay-key="1">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>运输方式：</label>
                                    <div class="col-sm-7">
                                        <select name="" class="form-control valid" aria-invalid="false">
                                            <option value="1">请选择</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>紧急程度：</label>
                                    <div class="col-sm-7">
                                        <select name="" class="form-control valid" aria-invalid="false">
                                            <option value="1">请选择</option>
                                            <option value="1">普通</option>
                                            <option value="1">紧急</option>
                                        </select>
                                    </div>
                                </div>
                            </div>


                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span
                                            style="color: red; ">*</span>是否开票：</label>
                                    <div class="col-sm-7">
                                        <select name="" class="form-control valid" aria-invalid="false">
                                            <option value="1">请选择</option>
                                            <option value="1">是</option>
                                            <option value="1">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row">

                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2"><span
                                            style="color: red; ">*</span>备注：</label>
                                    <div class="col-md-10 col-sm-10">
                                            <textarea name="" maxlength="500" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <!--基础信息 end-->
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<script>
    /*用户管理-新增-选择部门树*/
    function selectDeptTree() {
        var treeId = $("#treeId").val();
        var deptId = $.common.isEmpty(treeId) ? "100" : $("#treeId").val();
        var url = '人员树.html';
        var options = {
            title: '选择人员',
            width: "380",
            url: url,
            callBack: function () {

            }
        };
        $.modal.openOptions(options);
    }
</script>

<script>
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');

    });






</script>
</body>

</html>