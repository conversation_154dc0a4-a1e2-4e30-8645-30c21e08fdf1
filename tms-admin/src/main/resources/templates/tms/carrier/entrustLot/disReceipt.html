<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('一键回单')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .fl{
        float: left;
    }
    .fcff8{
        color: #FF8900;
    }
    .lh26{
        line-height: 26px;
    }
    .file-drop-zone{
        overflow-x: auto;
    }
    .flex{
        display: flex;
        align-items:center;
        justify-content:flex-start;
        color: #808080;
    }
    .flex_left{
        width: 80px;
        text-align: right;
        color: #333333 !important;
        margin-bottom: 0;
    }
    .flex_right{
        min-width:0;
        flex:1;
    }
    .dictImg{
        display:inline-block;padding: 5px;height:80px;width: 80px;box-sizing: border-box;
    }
    .dictImg img{
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
</style>
<body>
<div class="form-content" style="padding-top: 0;">
    <form id="form-receipt" class="form-horizontal" novalidate="novalidate" th:object="${invoice}">
        <input name="lotId"  id="lotId" type="hidden" th:value="${entrustLot.entrustList[0].lotId}">
        <input name="carId" type="hidden" th:value="${entrustLot.carnoId}">
        <input name="carNo" type="hidden" th:value="${entrustLot.carNo}">
        <input name="driverId" type="hidden" th:value="${entrustLot.driverId}">
        <div class="row">
            <div class="col-xs-6">
                <div class="panel-body">
                    <div class="padt20">
                        <div class="row mt20">
                            <div class="col-md-12 col-sm-12 ">
                                <div class="flex">
                                    <label class="flex_left">运单号：</label>
                                    <div class="flex_right" th:text="${entrustLot.lot}"></div>
                                </div> 
                            </div>
                            <div class="col-md-12 col-sm-12 mt5">
                                <div class="flex">
                                    <label class="flex_left">司机：</label>
                                    <div class="flex_right" th:text="${entrustLot.driverName+'/'+entrustLot.driverMobile}"></div>
                                </div> 
                            </div>
                            <div class="col-md-12 col-sm-12 mt5">
                                <div class="flex" style="align-items: flex-start;">
                                    <label class="flex_left">货品：</label>
                                    <div class="flex_right">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr style="background-color: #f9f9f9;">
                                                    <th style="width: 25%;">货品名称</th>
                                                    <th style="width: 25%;">件数</th>
                                                    <th style="width: 25%;">重量(吨)</th>
                                                    <th style="width: 25%;">体积(m³)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr th:each="goods,stat:${entrustLot.entGoodsList}" style="background-color: #ffffff;">
                                                    <td style="text-align:center;"  th:text="${goods.goodsName}"></td>
                                                    <td style="text-align:center;"  th:text="${goods.num}"></td>
                                                    <td style="text-align:center;"  th:text="${goods.weight}"></td>
                                                    <td style="text-align:center;"  th:text="${goods.volume}"></td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr style="background: #FFFCD3;text-align: center">
                                                    <td>合计：</td>
                                                    <td th:text="${entrustLot.numCount}"></td>
                                                    <td th:text="${entrustLot.weightCount}"></td>
                                                    <td th:text="${entrustLot.volumeCount}"></td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div> 
                            </div>
                        </div>  

                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="row mt5">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-sm-12">
                                <span class="fcff8">* </span>回单照片：
                            </label>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="form-group">
                            <div class="col-xs-12" >
                                <input id="receipt" name="receipt"  class="form-control" type="file" multiple>
                                <input name="tid" id="tid" type="hidden" >
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 col-sm-12" th:each="dict:${pic}" th:if="${dict.context == '回单照片'}">
                        <div class="form-group">
                            <label class="col-md-12 col-sm-12" th:text="${dict.context}+'：'"></label>
                            <div class="col-xs-12">
                                <div class="dictImg" th:each="pic:${picList}"
                                     th:if="${pic.workAppendixType==dict.value and pic.filePath!=null}">
                                    <img modal="zoomImg" th:src="@{${pic.filePath}}"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt10" style="height: 27px" th:if="${tid != null && tid != ''}">
                    <div class="col-xs-12">
                        <a class="btn btn-danger btn-xs" onclick="removePic()">删除回单照片</a>
                    </div>
                </div>
            </div>

            </div>
        </div>
       


    </form>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script>
    $(function () {
        //图片功能
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: null
        };
        var tid = "tid";
        var imageId = "receipt";
        $.file.initAddFiles(imageId, tid, picParam);
    });

    function submitHandler() {
        $.modal.confirm("是否确认上传图片？", function () {
            $('#receipt').fileinput('upload');
            jQuery.subscribe("cmt", commit);
        });
    }

    function commit() {
        let imgVal=$("#tid").val();
        if (imgVal == null || imgVal == '') {
            $.modal.alertWarning("回单照片不能为空！");
            return false;
        }  

        var data = $("#form-receipt").serializeArray();
        console.log(data)

        $.operate.save(ctx + "carrier/entrustLot/oneKeySavePic", data);
    }

    function removePic() {
        $.modal.confirm("是否删除所有回单照片？", function () {
            var data = {};
            data.lotId = $("#lotId").val();
            $.operate.saveModalNoCloseAndRefush(ctx + "carrier/entrustLot/removePic", data,function(result){
                if(result.code == 0){
                    //alert(1);
                    //window.location.reload()
                }
            });
        });
    }


</script>
</body>

</html>