<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('一键提货')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    html,body{
        font-size: 14px;
    }
    .form-content{
        height: 100%;
    }
    .form-horizontal{
        height: 100%;
    }
    .fw{
        font-weight: bold;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fcff8{
        color: #FF8900;
    }
    .fc80{
        color: #808080;
    }
    .mt10{
        margin-top: 10px;
    }
    .lh26{
        line-height: 26px;
    }
    .file-drop-zone{
        overflow-x: auto;
    }
    .flex{
        display: flex;
        align-items:center;
        justify-content:flex-start;

    }
    .flex_left{
        width: 60px;
        text-align: right;
        color: #808080;
        margin-bottom: 0;
    }
    .flex_right{
        min-width:0;
        flex:1;
    }

    .lazur-bg{
        background: #f8ac59;
        width: 10px;
        height: 10px;
        border: none;
    }
    .timeline_vertical-container {
        position: relative;
        padding: 0;
        margin-top: 0em;
        margin-bottom: 0em;
    }
    .vertical-timeline-block{
        margin: 0;
    }
    .vertical-timeline-block::before {
        content: '';
        position: absolute;
        top: 0;
        left: 5px;
        width: 1px;
        height: 100%;
        background: #0ba687;
    }
    .fixedfooter{
        position: fixed;
        bottom: 0;
        left: 50%;
        width: 160px;
        margin-left: -80px;
    }
    .vertical-timeline-content {
        margin-left: 0px;
        padding: 1em;
    }
    .vertical-timeline-content {
        /*position: relative;*/
        /*margin-left: 60px;*/
        /*background: white;*/
        /*border-radius: 0.25em;*/
        padding: 0;
    }
    .vertical-container{
        width: 100%;
        max-width: none;
    }

    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .table-bordered {
        border: 0px solid #EBEBEB;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .input-group .form-control {
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
</style>
<body>
<div class="form-content" style="padding-top: 0;">
    <form id="form-receipt" class="form-horizontal" novalidate="novalidate">
        <input name="lotId" type="hidden" th:value="${entrustLot.entrustList[0].lotId}">
        <input name="carId" type="hidden" th:value="${entrustLot.carnoId}">
        <input name="carNo" type="hidden" th:value="${entrustLot.carNo}">
        <input name="driverId" type="hidden" th:value="${entrustLot.driverId}">
        <div class="over" style="height: 100%">
            <div style="width: 50%;height: 100%;background: #fafbfd" class="fl">
                <div class="panel-body">
                    <div class="padt20">
                        <div class="row mt20">
                            <div class="col-md-12 col-sm-12 ">
                                <div class="flex">
                                    <label class="flex_left">运单号：</label>
                                    <div class="flex_right" th:text="${entrustLot.lot}"></div>
                                </div> 
                            </div>
                            <div class="col-md-12 col-sm-12 mt5">
                                <div class="flex">
                                    <label class="flex_left">司机：</label>
                                    <div class="flex_right" th:text="${entrustLot.driverName+'/'+entrustLot.driverMobile}"></div>
                                </div> 
                            </div>
                            <div class="col-md-12 col-sm-12 mt5">
                                <div class="flex">
                                    <label class="flex_left">车辆：</label>
                                    <div class="flex_right">
                                        <span th:text="${entrustLot.carNo+'/'}"></span>
                                        <span th:text="${entrustLot.carLenName+'米'}"></span>
                                        <span th:text="${entrustLot.carTypeName}"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12 mt5">
                                <div class="flex" style="align-items: flex-start;">
                                    <label class="flex_left">货品：</label>
                                    <div class="flex_right">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr style="background-color: #f9f9f9;">
                                                    <th style="width: 25%;">货品名称</th>
                                                    <th style="width: 25%;">件数</th>
                                                    <th style="width: 25%;">重量(吨)</th>
                                                    <th style="width: 25%;">体积(m³)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr th:each="goods,stat:${entrustLot.entGoodsList}" style="background-color: #ffffff;">
                                                    <td style="text-align:center;"  th:text="${goods.goodsName}"></td>
                                                    <td style="text-align:center;"  th:text="${goods.num}"></td>
                                                    <td style="text-align:center;"  th:text="${goods.weight}"></td>
                                                    <td style="text-align:center;"  th:text="${goods.volume}"></td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr style="background: #FFFCD3;text-align: center">
                                                    <td>合计：</td>
                                                    <td th:text="${entrustLot.numCount}"></td>
                                                    <td th:text="${entrustLot.weightCount}"></td>
                                                    <td th:text="${entrustLot.volumeCount}"></td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div> 
                            </div>
                            <div class="mt10" style="padding-left: 30px;">
                                <div class="fc80 mt10">要求装货时间地址：</div>
                                <div class="mt5">
                                    <div class='vertical-container light-timeline'>
                                        <div class='vertical-timeline-block' th:each="entrust : ${entrustLot.entrustList}">
                                            <div class='vertical-timeline-icon lazur-bg' th:if="${entrustLot.entrustList==1}">
                                            </div>
                                            <div class='vertical-timeline-content'>
                                                <div th:text="${#dates.format(entrust.reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                                <div class="fw" th:text="${entrust.deliAddrName}"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>  

                    </div>
                </div>
            </div>
            <div style="width: 50%" class="fl">
                <div class="panel-body" th:with="type=${@dict.getType('pick_pic_type')}">
                    <div class="" style="padding-bottom: 20px">
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="flex">
                                    <div class="flex_left" style="width: 100px"><span class="fcff8">* </span>提货时间：</div>
                                    <div class="flex_right">
                                        <input placeholder="请选择时间" name="actLeaveDate" id="actLeaveDate"  type="text" class="timeInput form-control" readonly required="true" aria-required="true">
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-12 mt10" th:each="dict:${type}" th:if="${dict.dictValue == '2'}">
                                <div class="flex">
                                    <div class="flex_left" style="width: 100px">
                                        <span class="fcff8">* </span>
                                        <span>提货照片：</span>
                                    </div>
                                    <div class="flex_right">
                                        <span class="fcff8">(包括车辆到场，验车、开始装车、结束装车、确认发货照片)</span>
                                    </div>
                                </div>
                                <div class="flex mt10">
                                    <div class="flex_left" style="line-height: 24px;width: 100px">

                                    </div>
                                    <div class="flex_right">
                                        <div class="">
                                            <input th:id="'image'+${dict.dictValue}" class="form-control"
                                                   th:name="'image'+${dict.dictValue}" type="file" multiple>
                                            <input th:id="'tid'+${dict.dictValue}" th:name="'tid'+${dict.dictValue}"
                                                   type="hidden">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <span th:with="type=${@dict.getType('pick_pic_type')}">
                    <input  th:each="dict:${type}" th:if="${dict.dictValue != '2'}" th:id="'tid'+${dict.dictValue}" th:name="'tid'+${dict.dictValue}" type="hidden">
                </span>
                
              

                <div class="panel-body">
                    <div class="padt20">

                    </div>
                    <div class="col-xs-12">
                        <div class="row">
                            <div class="flex">
                                <div class="flex_left" style="width: 100px">
                                    备注：
                                </div>
                                <div class="flex_right">
                                    <textarea name="note" class=" form-control" maxlength="100" rows="3" placeholder="添加备注"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                  </div>
            </div>
        </div>

        

    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var pickPicType = [[ ${@dict.getType('pick_pic_type')} ]];

    $(function () {
        for (var i = 0; i < pickPicType.length; i++) {
            if(pickPicType[i].dictValue == '2'){
                var dictValue = pickPicType[i].dictValue;
                var publishFlag = "cmt_" + dictValue;
                var picParam = {
                    maxFileCount: 0,
                    publish: publishFlag,  //用于绑定下一步方法
                    fileType: null//文件类型
                };
                var tid = "tid" + dictValue;
                var imageId = "image" + dictValue;
                $.file.initAddFiles(imageId, tid, picParam);
            }   
        }
        
    });

    /**
     * 校验
     */
     $("#form-receipt").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            actLeaveDate:{
                required:true,
            }
        }
    });

    function submitHandler() {
        $.modal.loading("正在处理中，请稍后...");
        $("#image2").fileinput('upload');
        jQuery.subscribe("cmt_2",commit);    
    }

    function commit() {
        if ($.validate.form()) {
            var data = $("#form-receipt").serializeArray();
            $.operate.save(ctx + "carrier/entrustLot/pickOneKey ", data);
        }else{
            $.modal.closeLoading();
        }
    }

    layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#actLeaveDate', //指定元素
                type: 'datetime',
                trigger: 'click',
                max: 3
            });

        });


</script>
</body>
</html>