<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('运单列表')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-2 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <input name="lot" placeholder="请输入运单号" class="form-control" type="text"
                                       aria-required="true">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="startDate" placeholder="请输入提货开始日期" readonly>
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="endDate" placeholder="请输入提货结束日期" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 col-sm-6">
                        <div class="form-group">
                            <select name="payDetailStatus" id="payDetailStatus" class="form-control valid"
                                    aria-invalid="false" data-none-selected-text="应付单状态">
                                <option value="">-- 应付状态 --</option>
                                <option th:each="dict : ${payDetailStatusEnum}" th:text="${dict.context}"
                                        th:value="${dict.value}"></option>
                            </select>
                        </div>
                    </div>


                    
                </div>
                <div class="row">
                    <div class="col-sm-5">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <div class="form-group">

                                    <div class="col-sm-4">
                                        <div class="form-group">
                                            <select  name="deliProvince" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <select name="deliCity" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                                    </div>
                                    <div class="col-sm-4">
                                        <div class="form-group">
                                            <select name="deliArea" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                    </div>


                    <div class="col-sm-5">
                        <div class="form-group">
                            <div class="col-sm-2" onclick="changeDiv()">
                                <div class="form-group">
                                    <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                                </div>
                            </div>
                            <div class="col-sm-10">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <select  name="arriProvince" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCity" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <select name="arriArea" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
        
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <div style="display: flex; align-items: center;" shiro:hasPermission="tms:trace:abnormal:deduct">
                <div>扣款金额：</div>
                <div>
                    <div class="input-group">
                        <input id="deductionValue" placeholder="请输入扣款金额" class="form-control" type="text" aria-required="true">
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
                <div class="ml5"><a class="btn btn-primary" style="padding: 2px 4px;" onclick="deduction()"><i class="fa fa-search"></i>&nbsp;匹配价格</a></div>
                <div>
                    <span id="deductionValueSpan" style="margin-left: 10px;"></span>
                </div>
            </div>

        </div>
        <div class="col-sm-12 select-table table-striped" >
            <table id="bootstrap-table" data-mobile-responsive="true" style="table-layout: fixed;"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>

<script th:inline="javascript">
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var entrustLotStatusEnum = [[${entrustLotStatusEnum}]];
    //应付单map
    var entrustLotStatusMap = [[${entrustLotStatusMap}]];

    var payDetailStatusEnum = [[${payDetailStatusEnum}]];
    //应付明细状态map
    var payDetailStatusMap = [[${payDetailStatusMap}]];

    var prefix = ctx + "carrier/entrustLot";
    var carrierId = [[${carrierId}]];//承运商id
    var excludeLotIds = [[${excludeLotIds}]];//排除的运单
    $(function () {
        var options = {
            url: prefix + "/entrustLotListForException?carrierId=" + carrierId + "&excludeLotIds=" + excludeLotIds,
            createUrl: prefix + "/add",
            showToggle:false,
            showColumns:true,
            fixedColumns: true,
            fixedNumber:0,
            height: 550,
            uniqueId: 'entrustLotId',
            columns: [
                {
                    checkbox: true
                },
                /*{
                    title: '操作',
                    width: '130px',
                    align: 'center',
                    field: 'ENTRUST_LOT_ID',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="委托单明细"onclick="detail(\'' + row.ENTRUST_LOT_ID + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        return actions.join('');
                    }
                },*/
                {
                    title: 'id',
                    width: '100px',
                    visible: false,
                    field: 'entrustLotId'
                },
                {
                    visible: false,
                    field: 'carrierId'
                },
                {
                    title: '运单号',
                    width: '130px',
                    align: 'left',
                    field: 'lot'
                },

                {
                    title: '运单状态',
                    width: '100px',
                    align: 'left',
                    field: 'vbillstatus',
                    formatter: function (row, value, index) {
                        var context = '';
                        entrustLotStatusEnum.forEach(function (v) {
                            if (v.value == value.vbillstatus) {
                                context = '<span class="label label-primary">'+v.context+'</span>';
                            }
                        });
                        return context;
                    }
                },
                {
                    title: '司机/车辆',
                    align: 'left',
                    width: '150px',
                    field: 'driverName',
                    formatter: function (value, row, index){
                        return getValue(value) + '<br />' + getValue(row.carNo)
                    }
                },
                {
                    title: '提货|到货省市区',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(row.deliAddr == null || row.deliAddr == ""){
                            row.deliAddr = '';
                        }
                        if(row.arriAddr == null || row.arriAddr.arriAddr == ""){
                            row.arriAddr = ''
                        }
                        if(row.deliAddr == "" && row.arriAddr == ""){
                            return "";
                        }else{
                            return row.deliAddr+'<br/>'+row.arriAddr;
                        }
                    }
                },
                {
                    title: '应付明细',
                    align: 'left',
                    feiled: 'payDetailList',
                    formatter: function status(value, row, index) {
                        var context = ''
                        var PayDetailList = row.payDetailList;

                        PayDetailList.forEach(function (v) {
                            var objHtml = ''
                            payDetailStatusEnum.forEach(function (k) {
                                if(v.vbillstatus == k.value) {
                                    objHtml = '<div><span class="label label-primary">'+ k.context + '</span>';
                                }
                            })
                            //if (v.value == value.vbillstatus) {
                            objHtml = objHtml + '&nbsp' + v.vbillno;
                            let a = ''
                            if (v.freeType === '0'){
                                a = $.table.selectDictLabel(costTypeFreight, v.costTypeFreight);
                            }else if (v.freeType === '2')  {
                                a = '调整费'
                            }else{
                                a = $.table.selectDictLabel(costTypeOnWay, v.costTypeOnWay);
                            }
                            objHtml = objHtml + '&nbsp' + a + '&nbsp' + v.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}) + '</div><br>';
                            context = context + objHtml
                            //}
                        });
                        return context;
                    }
                },
            ]
        };
        $.table.init(options);

        $.provinces.init("deliProvinceId", "deliCityId", "deliAreaId");
        $.provinces.init("arriProvinceId", "arriCityId", "arriAreaId");
    });

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
       
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);

        searchPre()
    }


    /** 获取选中行*/
    function getChecked() {
        return $.btTable.bootstrapTable('getSelections');
    }

    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }

    function searchPre() {
        var data = {};
        //data.carLenId = $.common.join($('#carLenId').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 选择承运人后的提交方法
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.close();

        // 选中的承运人ID
        parent.$("#driverId").val(rows.join());
        // 选中的司机名称
        parent.$("#driverName").val($.table.selectColumns("driverName"));
        parent.$("#phone").val($.table.selectColumns("phone"));
        parent.$("#driverMobile").val($.table.selectColumns("phone"));
    }


    function deduction() {
        //需匹配的金额
        let deductionValue = $("#deductionValue").val();
        deductionValue = deductionValue == '' ? 0 : Number(deductionValue);
        if (deductionValue == 0) {
            return
        }

        let bootstrapTable = $.btTable.bootstrapTable('getData');
        let dataList = []

        //循环每行数据  并累加金额
        bootstrapTable.forEach((item, i) => {
            let feeTotal = 0
            let data = {}
            item.payDetailList.forEach(pay => {
                let fee = pay.transFeeCount == null || (pay.vbillstatus != 0 && pay.vbillstatus != 1) ? 0 : pay.transFeeCount;
                feeTotal += fee;
            });
            if (feeTotal != 0) {
                data.indx = i;
                data.feeTotal = feeTotal
                dataList.push(data)
            }
        })

        //拍断是否有大于输入金额的数据
        const hasLargeThanDeduction = dataList.some(item => item.feeTotal >= deductionValue);

        if (hasLargeThanDeduction) {
            //获取所有大于输入金额的数据 从小到大排序
            dataList = dataList.filter(item => item.feeTotal >= deductionValue);
            dataList.sort((a, b) => a.feeTotal - b.feeTotal);
        }else {
            //按金额排序
            dataList.sort((a, b) => a.feeTotal - b.feeTotal).reverse();
        }

        let checkList = []
        let total = 0;
        let b = true
        let sumFee = 0

        //循环累加计算匹配的条数
        dataList.forEach((item, i) => {
            total += item.feeTotal;
            if (total <= deductionValue) {
                checkList.push(item.indx)
                sumFee += item.feeTotal
            }
            if (total > deductionValue && b) {
                checkList.push(item.indx)
                sumFee += item.feeTotal
                b = false
            }
        });

        //表格自动勾选
        $.btTable.bootstrapTable('uncheckAll');
        checkList.forEach(item => {
            $.btTable.bootstrapTable('check',item);
        })

        let s = sumFee.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
        $("#deductionValueSpan").text(`已选择${checkList.length}条运单，总金额 ${s}`)
    }

</script>

</body>
</html>