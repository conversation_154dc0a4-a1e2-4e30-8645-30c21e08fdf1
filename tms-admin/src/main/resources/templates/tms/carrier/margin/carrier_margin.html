<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商保证金管理')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--客户id-->
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">承运商名称 ：</label>
                            <div class="col-sm-7">
                                <input name="carrName" class="form-control"  type="text" maxlength="20">
                            </div>
                        </div>
                    </div>
                    <input id="hiddenText" type="text" style="display:none" />
                    <div class="col-md-6 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="add()"  shiro:hasPermission="tms:margin:carrier:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-info" th:onclick="showSpSettings([[${templateId}]], [[${biz}]])" shiro:hasPermission="wecom:sp:setting">
                <i class="fa fa-cog"></i> 配置
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
    var prefix = ctx + "finance/carrierMargin";


    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });
        var options = {
            url: prefix + "/carrierMarginListByPage",
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            exportUrl: prefix + "/export",
            fixedNumber: 0,
            showToggle: false,
            showColumns: true,
            modalName: "承运商保证金",
            uniqueId: "marginId",
            height: 560,
            clickToSelect: true,
            showFooter: true,
            onPostBody: function () {
                clearTotal();
                //合并页脚
                var footer_tbody = $('.fixed-table-footer table tbody');
                var footer_tr = footer_tbody.find('>tr');
                var footer_td = footer_tr.find('>td');
                var footer_td_1 = footer_td.eq(0);
                //除了第一列其他都隐藏
                for(var i=1;i<footer_td.length;i++) {
                    footer_td.eq(i).hide();
                }
                footer_td_1.attr('colspan', 1).show();

                getAmountCount();
            },
            onRefresh: function (params) {
                clearTotal();
            },
            onCheck: function (row, $element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            columns: [{
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "余额：<nobr id='totalAmount'>¥0.00</nobr> 待核销：<nobr id='offAmount'>¥0.00</nobr> 待确认：<nobr id='confirmAmount'>¥0.00</nobr>" +
                            "<br>" +
                            "总合计：余额：<nobr id='allTotalAmount'>¥0.00</nobr> 待核销：<nobr id='allOffAmount'>¥0.00</nobr> 待确认：<nobr id='allConfirmAmount'>¥0.00</nobr>";
                    }
                },
                {
                    title: '操作',
                    align: 'left',
                    width:50,
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:margin:carrier:detail')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="明细"onclick="detail(\'' + row.carrierId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        }
                        return actions.join('');
                    },
                },
                {
                    title: '承运商名称',
                    field: 'carrName',
                    align: 'left'

                },
                {
                    title: '余额',
                    field: 'marginAmount',
                    align: 'left',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if(value == null){
                            return ''
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '待核销金额',
                    field: 'unoffAmount',
                    formatter: function (value, row, index) {
                        if (value) {
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }
                    }
                },
                {
                    title: '待确认金额',
                    field: 'bzjAmount',
                    align: 'left',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if(value == null){
                            return ''
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                /*{
                    title: '余额',
                    field: 'thisMonthLender',
                    align: 'right',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if(row.lastMonthAmount === null){
                            row.lastMonthAmount = 0;
                        }
                        var over = row.lastMonthAmount+row.thisMonthDebit - row.thisMonthLender
                        return over.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },*/
            ]
        };
        $.table.init(options);
    });

    /**
     * 明细
     * @param carrierId
     */
    function detail(carrierId) {
        $.modal.openTab('明细',prefix + "/carrierMarginDetail/"+carrierId)
    }


    /**
     * 管理
     */
    function add() {
        $.modal.openTab("保证金新增", prefix + "/toAddCarrierMargin");
    }


    let sumTotalAmount = 0;
    let sumOffAmount = 0;
    let sumConfirmAmount = 0;

    function clearTotal() {
        sumTotalAmount = 0;
        sumOffAmount = 0;
        sumConfirmAmount = 0;
    }

    function addTotal(row) {
        sumTotalAmount += row.marginAmount||0;
        sumOffAmount += row.unoffAmount||0;
        sumConfirmAmount += row.bzjAmount||0;
    }

    function setTotal() {
        $("#totalAmount").text(sumTotalAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
        $("#offAmount").text(sumOffAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
        $("#confirmAmount").text(sumConfirmAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
    }

    function subTotal(row) {
        sumTotalAmount -= row.marginAmount||0;
        sumOffAmount -= row.unoffAmount||0;
        sumConfirmAmount -= row.bzjAmount||0;
    }
    var to = 0;
    function getAmountCount() {
        if (to) {
            clearTimeout(to);
        }
        to = setTimeout(function(){
            var data = $.common.formToJSON("role-form");
            $.ajax({
                url: prefix + "/getCount",
                type: "post",
                dataType: "json",
                data: data,
                success: function(result) {
                    if (result.code == 0) {
                        var data = result.data;
                        $("#allTotalAmount").text((data && data.MARGIN_AMOUNT||0).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        $("#allOffAmount").text((data && data.UNOFF_AMOUNT||0).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        $("#allConfirmAmount").text((data && data.BZJ_AMOUNT||0).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    } else {
                        $.modal.msgError(result.msg);
                    }
                }
            });
        }, 200)

    }
</script>
</body>
</html>