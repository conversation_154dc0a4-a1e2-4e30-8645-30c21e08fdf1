<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商保证金详情')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 140px;
        line-height: 26px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 26px;
    }
    .fcff{
        color: #ff1f1f;
    }
    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .table-bordered {
        border: 0px solid #EBEBEB;
    }
    .table-striped .table, .table-striped .table, .table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td {
        border-bottom: 0px solid #e7eaec!important;
    }
    .table-bordered td, .table-bordered th {
        border: 0px solid #ddd!important;
    }
    .input-group{
        margin-bottom: 10px;
    }
    .save{
        padding: 0px 0;
        position: fixed;
        bottom: 0px;
        left: 0;
        width: 100%;
        background: #fff;
        z-index: 99;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .input-group .form-control {
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .eye .file-drop-zone-title{
        background: url('../../img/eye.png') no-repeat center 10px;
        background-size: 40px 40px;
        height: 100%;
        line-height: 120px;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
</style>
<body class="gray-bg">
<div class="form-content">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">承运商名称：</label>
                                    <div class="flex_right" >
                                        [[${carrier.carrName}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">承运商简称：</label>
                                    <div class="flex_right">
                                        [[${carrier.carrAbbr}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">身份证/信用代码：</label>
                                    <div class="flex_right">
                                        [[${carrier.legalCard}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">联系人/联系方式：</label>
                                    <div class="flex_right">
                                        [[${carrier.contact}]]/  [[${carrier.phone}]]
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">合同有效期：</label>
                                    <div class="flex_right">
                                        [[${contractDate}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">承运商类别：</label>
                                    <div class="flex_right" th:each="dict : ${carrierType}" th:if="${dict.value} == ${carrier.carrType}" th:text="${dict.context}">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">运输方式：</label>
                                    <div class="flex_right" th:text="${@dict.getLabel('transport_type',carrier.transportType)}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">结算公司：</label>
                                <div class="flex_right">
                                    [[${contractCarrierVO.partyA}]]
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">结算账期：</label>
                                <div class="flex_right">
                                    [[${contractCarrierVO.settlementPeriod}]]
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">结算方式：</label>
                                <span class="flex_right" th:if="${carrier.ifHasBill == 0}">
                                    不开票
                                </span>
                                <span class="flex_right" th:if="${carrier.ifHasBill == 1}">
                                    <span class="flex_right" th:text="${@dict.getLabel('billing_type',carrier.billingType)}"></span>
                                </span>
                                <span class="flex_right" th:if="${carrier.oilCardRate != null}">
                                    油卡比例：[[${carrier.oilCardRate}]]%
                                </span>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">特殊说明：</label>
                                <div class="flex_right">
                                    [[${contractCarrierVO.specialNotes}]]
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">合同附件：</label>
                                <div class="flex_right" th:each="file : ${sysUploadFileList}">
                                    <a th:onclick="openContract([[${file.filePath}]],[[${file.fileName}]])">[[${file.fileName}]]</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="panel-group" id="accordion1">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion1"
                           href="tabs_panels.html#collapseTwo">业务相关</a>
                    </h5>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">引荐人：</label>
                                    <div class="flex_right" >
                                        [[${carrier.recommendMan}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">引荐类别：</label>
                                    <div class="flex_right" th:text="${@dict.getLabel('recommend_type',carrier.recommendType)}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">承运标段：</label>
                                    <div class="flex_right">
                                        [[${carrier.carriageLot}]]
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">关注线路：</label>
                                    <div class="flex_right" >
                                        <span th:each="line,status:${carrLines}" style="display: block">[[${status.index+1}]]、[[${line.startProvinceName}]][[${line.startCityName}]][[${line.startAreaName}]]→[[${line.endProvinceName}]][[${line.endCityName}]][[${line.endAreaName}]]</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
        </div>

    <div class="panel-group" id="accordion5">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h5 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion5"
                       href="tabs_panels.html#collapseFour">运费转保证金审核</a>
                </h5>
            </div>
            <div id="collapseFour" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="fixed-table-body" style="margin: 10px 10px 10px 10px">
                            <table border="0" id="infoTabThree" class="custom-tab table"style= "border:1px solid #e7eaec;" >
                                <thead>
                                <tr style="height: 30px">
                                    <th style="width: 10%;">操作</th>
                                    <th style="width: 15%;">申请人</th>
                                    <th style="width: 15%;">申请时间</th>
                                    <th style="width: 15%;">申请金额</th>
                                    <th style="width: 15%;">来源</th>
                                    <th style="width: 15%;">付款方式</th>
                                    <th style="width: 15%;">溯源单号</th>
                                </tr>

                                </thead>
                                <tbody>
                                <tr th:each="target:${feeToBZJChecks}">
                                    <td style="border-left: 1px solid #e7eaec;  border-right: 1px solid #e7eaec;">
                                        <a class="btn btn-info btn-xs" href="javascript:void(0)" th:onclick="check([[${target.businessCheckId}]])" shiro:hasAnyPermissions="tms:margin:carrier:writeoff"><i class="fa fa-check"></i>审核</a>
                                    </td>
                                    <td style="border-left: 1px solid #e7eaec;  border-right: 1px solid #e7eaec;">[[${target.regUserName}]]</td>
                                    <td style="border-left: 1px solid #e7eaec;  border-right: 1px solid #e7eaec;">[[${#dates.format(target.regDate,'yyyy-MM-dd HH:mm:ss')}]]</td>
                                    <td style="border-left: 1px solid #e7eaec;  border-right: 1px solid #e7eaec;">¥[[${target.bzjAmount}]]</td>
                                    <td style="border-left: 1px solid #e7eaec;  border-right: 1px solid #e7eaec;">[[${target.checkType == 0?'打包对账' : '背靠背'}]]</td>
                                    <td style="border-left: 1px solid #e7eaec;  border-right: 1px solid #e7eaec;">运费转保证金</td>
                                    <td style="border-left: 1px solid #e7eaec;  border-right: 1px solid #e7eaec;">[[${target.sourceNo}]]</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>


                </div>

            </div>

        </div>
    </div>

        <div class="panel-group" id="accordion2">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion2"
                           href="tabs_panels.html#collapseThree">保证金记录</a>
                    </h5>
                </div>
                <div id="collapseThree" class="panel-collapse collapse in">
                    <input type="hidden" name="carrierId" th:value="${carrierId}">
                    <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
                </div>

            </div>
        </div>



</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">

        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<th:block th:include="include :: bootstrap-fileinput553-css"/>
<th:block th:include="include :: bootstrap-fileinput553-js"/>
<style type="text/css">
    .file-preview {
        padding: 0px!important;
        border: none!important;
    }
    .file-drop-zone {
        margin: 0!important;
        overflow: auto;
    }
    .file-thumb-progress .progress {
        margin-bottom: 5px;
    }
</style>
<script th:inline="javascript">
    var prefix = ctx + "finance/carrierMargin";
    var receivable_method = [[${@dict.getType('receivable_method')}]];//收款方式
    var removeFlag = [[${@permission.hasPermi('tms:margin:carrier:remove')}]];

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });
        var carrierId = $("input[name='carrierId']").val();
        var options = {
            url: prefix + "/carrierMarginDetailListByPage?carrierId=" + carrierId,
            removeUrl: prefix + "/removeCarrierMarginById",
            fixedColumns: false,
            exportUrl: prefix + "/export",
            fixedNumber: 0,
            showToggle: false,
            showColumns: false,
            showSearch: false,
            showRefresh: false,
            modalName: "承运商保证金",
            uniqueId: "marginId",
            height: 560,
            clickToSelect: true,
            onPostBody: function () {
                $('.imgPreview').viewer({url: 'data-original', title: false, navbar: false});
            },
            columns: [
                /*{
                    checkbox: true
                },*/
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:margin:carrier:remove')}]] != "hidden" && row.payReceiptMethod != 98) {
                            if (row.status == 3) {
                                actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a> ');
                            }
                        }
                        if (row.sourceType == 2) {
                            if ([[${@permission.hasPermi('tms:margin:carrier:writeoff')}]] != "hidden" && row.offStatus === 0) {
                                if (row.recPayType === 0 && row.status == 2) { // 付款、已审批通过
                                    actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="writeoff(' + index + ')"><i class="fa fa-pay"></i>付款核销</a> ');
                                } else if (row.recPayType === 1) { // 收款
                                    actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="writeoff(' + index + ')"><i class="fa fa-pay"></i>收款核销</a> ');
                                }
                            }
                        }
                        return actions.join('');
                    },
                },
                // {
                //     title: '主键',
                //     field: 'id',
                //     align: 'left'
                // },
                {
                    title: '序号',
                    formatter: function (value, row, index) {
                 	    return $.table.serialNumber(index);
                    }
                },
                {
                    title: '类型',
                    field: 'recPayType',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var text = ''
                        if(value == 1) {
                            text = '<span class="label label-warning pa2">收款</span>'
                        }else {
                            text = '<span class="label label-success pa2">付款</span>'
                            if (row.sourceType == 2) {
                                if (row.status == 1) {
                                    text += ' <a class="label label-info pa2" href="javascript:wecom_process(\'' + row.spNo + '\')">审批中</a>'
                                } else if (row.status == 2) {
                                    text += ' <a class="label label-warning pa2" href="javascript:wecom_process(\'' + row.spNo + '\')">已通过</a>'
                                } else if (row.status == 3) {
                                    text += ' <a class="label label-danger pa2" href="javascript:wecom_process(\'' + row.spNo + '\')">已驳回</a>'
                                }
                            }
                        }
                        return text
                    }
                },
                {
                    title: '金额',
                    field: 'marginAmount',
                    align: 'left',
                    halign: "left",
                    formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '收付款时间',
                    field: 'receivePayTime',
                    align: 'left'
                },
                {
                    title: '收款方式',
                    field: 'payReceiptMethod',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(receivable_method, value);
                    }
                },
                {
                    field: 'sysUploadFile',
                    title: '附件',
                    formatter: function(value, row, index) {
                        var html = "<div class='imgPreview'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                //if (index != 3) { //最多展示三张图片 页面美观
                                    html += ('<img src="' + element.filePath + '" class="img-circle img-xs"/>'); // $.table.imageView(element.filePath)
                                //}
                            });
                        }
                        html += '</html>';
                        return html;
                    }
                },
                {
                    title: '来源',
                    field: 'sourceType',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var text = ''
                        if(value == 0) {
                            text = `打包对账`
                        }else if(value == 1){
                            text = `背靠背`
                        }else if(value == 2){
                            text = `手动新建`
                        }
                        return text
                    }
                },
                {
                    title: '溯源单号',
                    field: 'sourceVbillno',
                    align: 'left'
                },
                {
                    title: '创建信息',
                    field: 'regUserId',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return (value||'') + "<br>" + (row.regDate||'');
                    }
                },
                {
                    title: '核销信息',
                    field: 'offUserName',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value) {
                            return (value ? ('<a href="javascript:offInfo(' + index + ')">' + value + '</a>') : '') + "<br>" + (row.offTime ? ('<a href="javascript:offInfo(' + index + ')">' + row.offTime + '</a>') : '');
                        } else if (row.offStatus == 1) {
                            return '已核销<br>往期数据'
                        }
                    }
                },
            ]
        };
        $.table.init(options);
    });

    /**
     * 明细
     * @param marginId
     */
    function detail(customerId) {
        $.modal.openTab("明细", prefix + "/business/detail?customerId="+customerId);
    }


    /**
     * 管理
     */
    function add() {
        $.modal.openTab("保证金管理", prefix + "/manageMargin");
    }

    function remove(id) {

    }


    function openContract(filePath, fileName) {
        window.open( "http://" + document.location.host + filePath, fileName);
    }

    function check(id){
        //$.modal.open("保证金管理", prefix + "/checkManageMargin");

        layer.open({
            type: 2,
            area: ['50%', '80%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "保证金管理审核",
            content: prefix + "/checkManageMargin?id="+id,
            btn: ['通过', '不通过','关闭'],
            shadeClose: true,            // 弹层外区域关闭
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitApprove(index, layero);
            },
            btn2: function(index, layero){
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitBack(index, layero);
                return false;
            },
            btn3: function (index, layero) {
                return true;
            }
        });

        /*var lock = false
        layer.confirm("请审核是否通过？", {
            icon: 3,
            title: "系统提示",
            btn: ['通过', '不通过','取消']
        }, function (index) {

            if (!lock) {
                let data = {"businessCheckId":id};
                lock = true;
                layer.close(index);
                data.checkStatus = 1;
                $.operate.saveAndRefreshAll(prefix + "/checkBzj",data);
            }
        }, function (index) {
            if (!lock) {
                let data = {"businessCheckId":id};
                lock = true;
                layer.close(index);
                data.checkStatus = 2;
                $.operate.saveAndRefreshAll(prefix + "/checkBzj",data);
            }
        });*/
    }
    function writeoff(idx) {
        let row = $('#bootstrap-table').bootstrapTable('getData')[idx];
        console.log(row)
        let uploadCache = {};
        layer.open({
            type: 1,
            title: (row.recPayType == 0 ? '付':'收') + '款核销',
            area: ['800px', '400px'],
            skin: '',
            offset: '15%',
            content: ['<form id="tForm" style="padding: 10px">',
                `<input type="hidden" name="id" value="${row.id}"/>`,
                '<div class="flex">',
                `<span class="flex_left" style="width: 90px">应${row.recPayType == 0 ? '付':'收'}款金额：</span>`,
                `<span class="flex_right">${row.marginAmount}</span>`,
                '</div>',
                '<div class="flex">',
                '<span class="flex_left" style="width: 90px"><span style="color:red">*</span> 核销凭证：</span>',
                '<span class="flex_right"><input type="file" name="offProof" multiple></span>',
                '</div>',
                '<div class="flex">',
                '<span class="flex_left" style="width: 90px">核销备注：</span>',
                '<span class="flex_right"><textarea class="form-control" name="offBz" rows="4"></textarea></span>',
                '</div>',
                '</form>'].join(''),
            success: function(layero) {
                let option = {
                    theme: "explorer-fa5", //主题
                    language: 'zh',
                    uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                    //deleteUrl: ctx + "common/deleteImage",
                    uploadExtraData: {key: "offProof"},   //上传id，传入后台的参数
                    deleteExtraData: {key: 'id'},
                    // extra" {key: ''}, // 上面两个一致则可使用该字段？
                    enctype: 'multipart/form-data',
                    allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                    initialPreviewAsData: true,
                    overwriteInitial: false,
                    //initialPreviewConfig: [
                    //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                    //],
                    //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                    maxFileCount: 0, // 0:不限制上传数
                    showUpload: false,  // 不显示上传按钮，选择后直接上传
                    //previewClass:"uploadPreview",
                    minFileSize: 5, // 5KB
                    previewFileIcon: '<i class="fa fa-file"></i>',
                    allowedPreviewTypes: ['image'],
                    showClose: false,  //是否显示右上角叉按钮
                    showUpload: false, //是否显示下方上传按钮
                    showRemove: false, // 是否显示下方移除按钮
                    //autoReplace: true,
                    //showPreview: false,//是否显示预览(false=只剩按钮)
                    showCaption: false,//底部上传按钮左侧文本
                    uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                    fileActionSettings: {
                        showUpload: false,		//每个文件的上传按钮
                        showDrag: false,
                        //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                    },
                }
                layero.find("[name='offProof']").fileinput(option).on("filebatchselected", function (e, files) {
                    $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                }).on("fileuploaded", function (event, data, previewId, index) {
                    //单个上传成功事件
                    //console.log("fileuploaded", event, data, previewId, index)
                    var code = data.response.code;
                    if (code !== 0) {
                        $.modal.closeLoading();
                        $.modal.alertError("上传失败：" + data.response.msg);
                        return;
                    }
                    uploadCache[previewId] = data.response.tid;
                }).on('filesuccessremove', function (event, previewId, index) {
                    //上传后删除事件
                    //console.log("filesuccessremove", event, previewId, index)
                    //delete cache[previewId] //bug
                    //fileArr.splice(index, 1) //bug
                    //$(this).fileinput('clear');
                    //$('[name="' + hideName + '"]').val('')
                    var tid = uploadCache[previewId];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        //console.log(result)
                    }, 'json')
                    delete uploadCache[previewId]
                });
            },
            btn: ['提交', '取消'],
            yes: function(index, layero) {
                if (Object.keys(uploadCache).length == 0) {
                    $.modal.msgWarning("请上传核销凭证");
                    return
                }
                let tids = [];
                for (var k in uploadCache) {
                    tids.push(uploadCache[k])
                }
                $.modal.confirm('确认提交吗？', function(){
                    let data = $('#tForm').serialize();
                    data += "&offProof=" + tids.join(',');
                    $.operate.submit(prefix + "/writeoff", "post", "json", data, function(res){
                        if (res.code == 0) {
                            layer.close(index)
                        }
                    });
                });
            }
        })
    }
    function offInfo(idx) {
        let row = $('#bootstrap-table').bootstrapTable('getData')[idx];
        if (row.offProof) {
            $.ajax({
                url: ctx + 'common/tid-files/' + row.offProof,
                cache: false,
                success: function (res) {
                    if (res.code != 0) {
                        $.modal.alertError(res.msg);
                        return;
                    }
                    let imgs = [];
                    for (let i = 0; i < res.data.length; i++) {
                        imgs.push('<img src="' , res.data[i].filePath, '" style="width:70px;height:50px;margin-right:10px"/>');
                    }
                    layer.open({
                        type: 1,
                        title: '核销明细',
                        area: ['600px', '400px'],
                        content: ['<div style="padding: 10px">',
                            '<div class="flex">',
                            '<span class="flex_left" style="width: 90px">核销时间：</span>',
                            `<span class="flex_right">${row.offTime}</span>`,
                            '</div>',
                            '<div class="flex">',
                            '<span class="flex_left" style="width: 90px">核销人：</span>',
                            `<span class="flex_right">${row.offUserName}</span>`,
                            '</div>',
                            '<div class="flex">',
                            '<span class="flex_left" style="width: 90px">核销凭证：</span>',
                            `<span class="flex_right imgPreview">${imgs.join('')}</span>`,
                            '</div>',
                            '<div class="flex">',
                            '<span class="flex_left" style="width: 90px">核销备注：</span>',
                            `<span class="flex_right">${row.offBz||''}</span>`,
                            '</div>',
                            '</div>'].join(''),
                        skin: '',
                        offset: '15%',
                        success: function(layero) {
                            $('.imgPreview').viewer({url: 'data-original', title: false, navbar: false});
                        },
                        btn: ['关闭']
                    })
                }
            })
        }
    }
</script>
</body>
</html>