<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商保证金新增')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .fcff{
        color: #ff1f1f;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 30px;
        text-align: right;
        color: #000!important;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
</style>
<body>
<div class="form-content" style="padding-right: 25px">
    <form id="form-margin-add" class="form-horizontal" novalidate="novalidate">
        
        <div class="panel-group">
            <div class="row">
                <div class="col-md-4 col-sm-6">
                    <div class="form-group flex">
                        <label class="flex_left"><span class="fcff">* </span>选择承运商：</label>
                        <div class="flex_right">
                            <input name="carrierId" id="carrierId" type="hidden">
                            <input id="carrierName" name="carrierName" class="form-control" type="text" onclick="selectCarrier()" required readonly="true">
                            <!--<span class="input-group-addon"><i class="fa fa-search"></i></span>-->
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="form-group flex">
                        <div class="flex_left"><span class="fcff">*</span>收付款时间：</div>
                        <div class="flex_right">
                            <input type="text" class="form-control" name="receivePayTime" id="receivePayTime" placeholder="请选择时间" readonly required aria-required="true">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4 col-sm-6">
                    <div class="form-group flex">
                        <label class="flex_left"><span class="fcff">* </span>保证金金额：</label>
                        <div class="flex_right">
                            <input name="marginAmount" id="marginAmount" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" class="form-control" required type="text" maxlength="20">
                        </div>
                    </div>
                </div>

                <div class="col-md-4 col-sm-6">
                    <div class="form-group flex">
                        <label class="flex_left"><span class="fcff">* </span>收付款类型：</label>
                        <div class="flex_right">
                            <select name="recPayType" class="form-control valid" required th:with="type=${@dict.getType('margin_type')}" onchange="sfkChange(this)">
                                <option value=""></option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 col-sm-6">
                    <div class="form-group flex">
                        <label class="flex_left"><span class="fcff">* </span>收付款方式：</label>
                        <div class="flex_right">
                            <select name="payReceiptMethod" class="form-control" th:with="type=${@dict.getType('receivable_method')}" required aria-required="true">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" ></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" style="display: none" id="skDiv">
                <div class="col-md-4 col-sm-6">
                    <div class="form-group flex">
                        <label class="flex_left"><span class="fcff">* </span>收款人：</label>
                        <div class="flex_right">
                            <input class="form-control" name="skr" required>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="form-group flex">
                        <label class="flex_left"><span class="fcff">* </span>收款账号：</label>
                        <div class="flex_right">
                            <input class="form-control" name="skzh" required>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="form-group flex">
                        <label class="flex_left"><span class="fcff">* </span>收款银行：</label>
                        <div class="flex_right">
                            <input class="form-control" name="skyh" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 备注 -->
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group flex">
                        <label class="flex_left" >备注：</label>
                        <div class="flex_right" style="margin-bottom: 10px">
                            <textarea name="remrk" id="remrk" maxlength="200" class="form-control valid" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>



            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="form-group flex">
                        <label class="flex_left" >附件：</label>
                        <div class="flex_right">
                            <input id="image" name="image" class="form-control" type="file" multiple>
                            <input id="appendixId" name="appendixId" type="hidden">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "finance/carrierMargin";
    $(function () {
        //时间初始化
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#receivePayTime', //指定元素
                type: 'date',
                trigger: 'click',
                value: new Date(),
                // ready: function (date) {
                //     var now = new Date();
                //     this.dateTime.hours = now.getHours();
                //     this.dateTime.minutes = now.getMinutes();
                //     this.dateTime.seconds = now.getSeconds();
                // },
                done: function (value, date, endDate) {
                    $("#receivePayTime").val(value);
                    //单独校验日期
                    $("#form-margin-add").validate().element($("#receivePayTime"))
                }
            });
        });

        $('#collapseOne').collapse('show');
        $('#collapsePic').collapse('show');
        $('#collapseThree').collapse('show');

        var image1 = {
            maxFileCount: 5,
            publish: "cmt_1",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        $.file.initAddFiles("image", "appendixId", image1);
    });

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗", function(){
                $.modal.loading("正在处理中，请稍后...");
                $("#image").fileinput('upload');
                jQuery.subscribe("cmt_1", commit);
            })
        }
    }

    function cmtFile_1() {
        $("#image1").fileinput('upload');
    }

    function commit() {
        $.operate.saveTab(prefix + "/addCarrierMargin", $('#form-margin-add').serialize());
    }

    function selectCarrier() {
        var PREFIX = ctx + "basic/carrier";
        $.modal.open("选择承运人", PREFIX + "/findCarrier?isRadio=1");
    }


    function sfkChange(select) {
        if ($(select).val() == '0') {
            $('#skDiv').show()
            $('#skDiv').find(':input').prop("disabled", false);
        } else {
            $('#skDiv').hide()
            $('#skDiv').find(':input').prop("disabled", true);
        }
    }
</script>
</body>

</html>