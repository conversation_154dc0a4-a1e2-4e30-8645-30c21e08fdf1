<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商保证金新增')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .fcff{
        color: #ff1f1f;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 90px;
        line-height: 30px;
        text-align: right;
        color: #000!important;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
</style>
<body>
<div class="form-content">
    <form id="form-margin-add" class="form-horizontal" novalidate="novalidate">
        
        <div class="panel-group">
            <input type="hidden" id="checkStatus" name="checkStatus">
            <input type="hidden" id="businessCheckId" name="businessCheckId" th:value="${businessCheckId}">
            <!-- 备注 -->
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">


                        <div class="col-sm-2">
                            <label class="flex_left" >审核备注：</label>
                        </div>
                        <div class="col-sm-10">
                            <textarea name="memo" id="memo" maxlength="200" class="form-control valid"rows="3"></textarea>
                        </div>
                    </div>
                    
                </div>
            </div>



            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="form-group">

                        <div class="col-sm-2">
                            <label class="flex_left" >附件：</label>
                        </div>
                        <div class="col-sm-10">
                            <input id="image" name="image" class="form-control" type="file" multiple>
                            <input id="appendixId" name="appendixId" type="hidden">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "finance/carrierMargin";
    $(function () {


        $('#collapseOne').collapse('show');
        $('#collapsePic').collapse('show');
        $('#collapseThree').collapse('show');

        var image1 = {
            maxFileCount: 5,
            publish: "cmt_1",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        $.file.initAddFiles("image", "appendixId", image1);
    });

    function submitApprove(){
        $("#checkStatus").val(1);
        submitHandler();
    }

    function submitBack(){
        $("#checkStatus").val(2);
        submitHandler();
    }

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.modal.loading("正在处理中，请稍后...");
            $("#image").fileinput('upload');
            jQuery.subscribe("cmt_1", commit);
        }
    }

    function cmtFile_1() {
        $("#image1").fileinput('upload');
    }

    function commit() {
        $.operate.saveModalAndRefush(prefix + "/checkBzj", $('#form-margin-add').serialize());
    }


</script>
</body>

</html>