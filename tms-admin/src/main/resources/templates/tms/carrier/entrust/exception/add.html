<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增异常跟踪')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: datetimepicker-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-adnormal-add" class="form-horizontal" novalidate="novalidate">
        <!--委托单id-->
        <input name="entrustId" type="hidden" th:value="${entrust.entrustId}">
        <!--承运商id-->
        <input name="carrierId" type="hidden" th:value="${entrust.carrierId}">
        <!--车辆id-->
        <input name="carnoId" type="hidden" th:value="${entrust.carnoId}">
        <!--车牌号-->
        <input name="carno" type="hidden" th:value="${entrust.carno}">
        <!--司机姓名-->
        <input name="driverName" type="hidden" th:value="${entrust.driverName}">
        <!--司机姓名-->
        <input name="driverMobile" type="hidden" th:value="${entrust.driverMobile}">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">异常分类：</label>
                                    <div class="col-sm-8">
                                        <select name="expType" class="form-control valid" aria-invalid="false"
                                                th:with="type=${@dict.getType('exp_type')}" required="">
                                            <option value="">-- 请选择 --</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">异常发生时间：</label>
                                    <div class="input-group date">
                                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                        <input type="text" class="form-control time-input" lay-key="1"
                                               autocomplete="off" id="expOccTime" name="expOccTime"
                                               readonly required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red"> 预计到达时间：</label>
                                    <div class="input-group date">
                                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                        <input type="text" class="form-control time-input" lay-key="2"
                                               autocomplete="off" id="estArrivalTime"
                                               name="estArrivalTime" readonly required>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">跟踪信息：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="note" maxlength="500" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">异常照片：</label>
                                    <div class="col-sm-4">
                                        <input id="image" class="form-control" name="image" type="file" multiple>
                                        <input id="appendixId" name="appendixId" type="hidden">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!--<div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class=" col-sm-3">跟踪信息：</label>
                                    <div class="col-sm-12">
                                            <textarea name="note" maxlength="500" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>-->
                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-offset-5 col-sm-10">
                <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i
                        class="fa fa-check"></i>保
                    存
                </button>&nbsp;
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i
                        class="fa fa-reply-all"></i>关 闭
                </button>
            </div>
        </div>


    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<!--<th:block th:include="include :: datetimepicker-js"/>-->
<script th:inline="javascript">
    var prefix = ctx + "carrier/entrust";
    $(function () {

        $('#collapseOne').collapse('show');

        var image = {
            maxFileCount: 0,
            publish: "imgDone",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        $.file.initAddFiles("image", "appendixId", image);

    });

    /**
     * 日期插件
     */
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        var expOccTime = laydate.render({
            elem: '#expOccTime', //指定元素
            format: 'yyyy-MM-dd HH:mm:ss', //指定时间格式
            isInitValue: false,
            type: 'datetime', // 可选择：年、月、日、时、分、秒
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours=now.getHours();
                this.dateTime.minutes=now.getMinutes();
                this.dateTime.seconds=now.getSeconds();
            },
            done: function(value, date, endDate){
                expOccTime.config.min = {
                        year: date.year,
                        month: date.month - 1,//关键
                        date: date.date,
                        hours: date.hours,
                        minutes: date.minutes,
                        seconds: date.seconds
                    };
                $("#expOccTime").val(value);
                //单独校验日期
                $("#form-adnormal-add").validate().element($("#expOccTime"))
            }
        });


        var estArrivalTime =  laydate.render({
            elem: '#estArrivalTime', //指定元素
            format: 'yyyy-MM-dd HH:mm:ss', //指定时间格式
            isInitValue: false,
            type: 'datetime', // 可选择：年、月、日、时、分、秒
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours=now.getHours();
                this.dateTime.minutes=now.getMinutes();
                this.dateTime.seconds=now.getSeconds();
            },
            done: function(value, date, endDate){
                estArrivalTime.config.min = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date,
                    hours: date.hours,
                    minutes: date.minutes,
                    seconds: date.seconds
                };
                $("#estArrivalTime").val(value);
                //单独校验日期
                $("#form-adnormal-add").validate().element($("#estArrivalTime"))
            }
        });
    });


    //新增校验逻辑
    function submitHandler() {
        if ($.validate.form()) {
            if ($("#image").val() != "") {
                $("#image").fileinput('upload');
                jQuery.subscribe("imgDone", commit);
            } else {
                commit();
            }
        }
    }

    //新增提交
    function commit() {
        $.operate.saveTab(prefix + "/addExp", $('#form-adnormal-add').serialize());
    }


</script>
</body>

</html>