<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('在途跟踪-edit')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style type="text/css">
    select {
        border: none;
        outline: none;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
    }
</style>
<body>
<div class="form-content">
    <form id="form-entrust-edit" class="form-horizontal" novalidate="novalidate" th:each="entTrackingStatuses:${entTrackingStatuses}" >
        <input name="entTrackingStatusId" id="entTrackingStatusId" th:value="${entTrackingStatuses?.entTrackingStatusId}" type="hidden"  />
        <input name="entrustId" id="entrustId" th:value="${entrustId}" type="hidden"  />
        <!--基础信息 begin-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <input name="deptId" type="hidden" id="treeId">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">在途状态：</label>
                                    <div class="col-sm-8" th:text="${@dict.getLabel('tracking_status',entTrackingStatuses.trackingStatus)}">
                                    </div>

                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">跟踪时间：</label>
                                    <div class="col-sm-8">
                                        [[${#dates.format(entTrackingStatuses.trackingTime, 'yyyy-MM-dd')}]]
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">地址：</label>
                                    <div class="col-sm-8">
                                        <input type="hidden" id="provinceId" th:value="${entTrackingStatuses?.currProvinceId}">
                                        <input type="hidden" id="cityId" th:value="${entTrackingStatuses?.currCityId}">
                                        <input type="hidden" id="areaId" th:value="${entTrackingStatuses?.currAreaId}">
                                        <select name="currProvinceId" id="province" disabled="disabled" aria-invalid="false"></select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-2">
                                <select name="currCityId" id="city" disabled="disabled" aria-invalid="false"></select>
                            </div>

                            <div class="col-sm-2">
                                <select name="currAreaId" id="area"  disabled="disabled" aria-invalid="false"></select>
                            </div>


                            <div class="col-sm-2">
                                [[${entTrackingStatuses.currDetailAddr}]]
                            </div>
                        </div>



                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10">

                                        [[${entTrackingStatuses?.trackingMemo}]]
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2"> 跟踪照片：</label>
                                    <div class="col-sm-4" th:each="pic:${sysUploadFileList}">
                                        <img style="width:70px; height:50px" modal="zoomImg" th:src="${pic.filePath}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--基础信息 end-->

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>

<script th:inline="javascript">
    var prefix = ctx + "carrier/entrust";
    //省市区value
    var currProvinceId = $("#provinceId").val();
    var currCityId = $("#cityId").val();
    var currAreaId = $("#areaId").val();

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');

        $.provinces.init("province","city","area",currProvinceId,currCityId,currAreaId);

        //初始化附件信息
        var photoExp = [[${sysUploadFileList}]];
        var photoExpParam = {
            maxFileCount: 0,
            publish:"imgDone",  //用于绑定下一步方法
            fileType:null	//文件类型
        };
        $.file.loadEditFiles("photo", "tid", photoExp, photoExpParam);

    });


    function submitHandler() {
        if ($.validate.form()  & $('#photo').fileinput('valid')) {
            //上传图片
            $('#photo').fileinput('upload');
            //图片上传完成后提交表单
            jQuery.subscribe("imgDone", commit);
        }
    }



    /**
     * 提交
     */
    function commit() {
        var entrustId = $("#entrustId").val();
        $.operate.saveTab(prefix + "/saveStatus/"+entrustId, $('#form-entrust-edit').serialize());
    }



</script>
</body>

</html>