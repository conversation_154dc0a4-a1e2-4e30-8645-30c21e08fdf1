<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('确认到达')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-entrust-edit" class="form-horizontal" novalidate="novalidate" >
        <input name="entTrackingStatusId" id="entTrackingStatusId"  type="hidden"  />
        <!--委托单id-->
        <input name="entrustId" id="entrustId" th:value="${entrust?.entrustId}" type="hidden"  />
        <!--运单id-->
        <input name="lotId" th:value="${entrust?.lotId}" type="hidden"  />
        <!--发货单id-->
        <input name="orderno" th:value="${entrust?.orderno}" type="hidden"  />

        <!--基础信息 begin-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <input name="deptId" type="hidden" id="treeId">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">

                            </div>

                        </div>
                        <!--<div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">当前地址：</label>
                                    <div class="col-sm-8">
                                        <select name="currProvinceId" id="provinceId"  class="form-control valid" aria-invalid="false"></select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-2">
                                <select name="currCityId" id="cityId"  class="form-control valid" aria-invalid="false"></select>
                            </div>

                            <div class="col-sm-2">
                                <select name="currAreaId" id="areaId"  class="form-control valid" aria-invalid="false"></select>
                            </div>


                            <div class="col-sm-5">
                                <input name="currDetailAddr"  placeholder="请输入详细地址" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>-->


                        <div class="row" th:each="dict : ${pic}" th:if="${dict.value != '3'}">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2" th:text="${dict.context}+'：'"> </label>
                                    <div class="col-sm-4">
                                        <input name="tid3" value="" type="hidden">
                                        <input th:id="'image'+${dict.value}" class="form-control"
                                               th:name="'image'+${dict.value}" type="file" multiple>
                                        <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}"
                                               type="hidden">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="note" id="note" maxlength="100" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <!--基础信息 end-->


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "carrier/entrust";
    //上传图片类型
    var carrPicType = [[${pic}]];

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        // 初始化省市区
        $.provinces.init("provinceId", "cityId","areaId");

        //循环字典表中图片类型，初始化图片上传区域
        for (var i = 0; i < carrPicType.length; i++) {
            var dictValue = carrPicType[i].value;
            var publishFlag = "done" + dictValue;
            var picParam = {
                maxFileCount: 0,
                publish: publishFlag,  //用于绑定下一步方法
                fileType: null//文件类型
            };
            var tid = "tid" + dictValue;
            var imageId = "image" + dictValue;
            $.file.initAddFiles(imageId, tid, picParam);
        }

    });


    function submitHandler() {
        if ($.validate.form()) {
            for (var i = 0; i < carrPicType.length; i++) {
                var dictValue = carrPicType[i].value
                if ($("#image" + dictValue).val() != "") {
                    $("#image" + dictValue).fileinput('upload');
                }
                jQuery.subscribe(setTimeout("commit()", "1000"));
            }
        }
    }

    function commit() {
        $.operate.saveTab(prefix + "/saveDeli", $('#form-entrust-edit').serialize());
    }



</script>
</body>

</html>