<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('异常跟踪')"/>
</head>
<body>
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="entrustId" th:value="${entrustId}">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">异常分类：</label>
                            <div class="col-sm-8">
                                <select name="expType" class="form-control valid" aria-invalid="false" th:with="type=${@dict.getType('exp_type')}">
                                    <option value="">-- 请选择 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">异常发生时间：</label>
                            <div class="col-sm-8">
                                <input name="expOccTime" class="time-input form-control" placeholder="请输入异常发生时间" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">预计到达时间：</label>
                            <div class="col-sm-8">
                                <input name="estArrivalTime" class="time-input form-control" placeholder="请输入预计到达时间" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
                <div class="row">

                </div>


            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" id="addException" onclick="insertRow()" shiro:hasPermission="carrier:entrustExp:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" id="removeException"  onclick="$.operate.removeAll()" shiro:hasPermission="carrier:entrustExp:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true" style="table-layout: fixed;"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "carrier/entrust";
    //权限
    var editFlag = [[${@permission.hasPermi('carrier:entrustExp:edit')}]];
    //委托单id
    var entrustId =[[${entrustId}]];
    //异常状态
    var expType = [[${@dict.getType('exp_type')}]];

    var carrId = [[${carrId}]];

    if(null==carrId) {
        $("#addException").addClass("disabled");
        $("#removeException").addClass("disabled");
        $("#removeException").removeClass("multiple");
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        var options = {
            url: prefix + "/expList/"+entrustId,
            removeUrl: prefix + "/remove",
            showSearch: true,
            showRefresh: true,
            pagination: false,
            showToggle:false,
            showColumns:true,
            clickToSelect:true,
            modalName: "异常跟踪",
            rememberSelected: true,
            height: 560,
            uniqueId: 'entrustExpId',
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    width: '80px',
                    field : 'entrustId',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs '+editFlag+'" href="javascript:void(0)"  title="修改" onclick="update(\''+row.entrustExpId+'\')"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>');
                        actions.push('<a class="btn  btn-xs '+editFlag+'" href="javascript:void(0)"  title="明细" onclick="detail(\''+row.entrustExpId+'\')"><i  class="fa fa-newspaper-o" style="font-size: 15px;" ></i></a>');
                        return actions.join('');

                    }
                },

                {
                    title: '异常分类',
                    align: 'center',
                    field : 'expType',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(expType, value);
                    }
                },

                {
                    title: '异常发生时间',
                    align: 'center',
                    field : 'expOccTime'
                },
                {
                    title: '预计到达时间',
                    align: 'center',
                    field : 'estArrivalTime'
                },
                {
                    title: '跟踪信息',
                    align: 'center',
                    field : 'note'
                },
                {
                    title: '跟踪人',
                    align: 'center',
                    field : 'corUserName'
                },
            ]
        };

        $.table.init(options);
    });

    //新增异常跟踪
    function insertRow(){
        var url = prefix + "/addExp/"+entrustId;
        $.modal.openTab("新增异常跟踪", url);
    }

    function update(id){
        var url = prefix + "/editExp/"+id;
        $.modal.openTab("修改异常跟踪", url);
    }

    function detail(id){
        var url = prefix + "/detailExp/"+id;
        $.modal.openTab("异常跟踪明细", url);
    }


</script>
</body>
</html>