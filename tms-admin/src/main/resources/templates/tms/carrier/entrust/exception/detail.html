<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('修改异常跟踪')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-adnormal-edit" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">

                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">异常分类：</label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('exp_type')}" th:if="${dict.dictValue} == ${entrustExp?.expType}" th:text="${dict.dictLabel}">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">异常发生时间：</label>
                                    <div class="col-sm-8" th:text="${#dates.format(entrustExp.expOccTime, 'yyyy-MM-dd HH:mm:ss')}">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"> 预计到达时间：</label>
                                    <div class="col-sm-8" th:text="${#dates.format(entrustExp.estArrivalTime, 'yyyy-MM-dd HH:mm:ss')}">
                                    </div>
                                </div>
                            </div>
                          <!--  <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">是否客户可见：</label>
                                    <div class="col-sm-8" th:text="${entrustExp.ifVisibleToCust== 0 ?'不可见':'可见'}">
                                    </div>
                                </div>
                            </div>-->
                        </div>

                        <div class="row">


                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">跟踪信息：</label>
                                    <div class="col-md-11 col-sm-10" th:text="${entrustExp.note}">

                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                异常照片：
                                <div class="form-group" th:each="files:${files}">
                                    <label class="col-md-1 col-sm-2"></label>
                                    <div class="col-sm-7" style="display:inline-block" >
                                        <img style="width:70px; height:50px" modal="zoomImg" th:src="@{${files.filePath}}"/>
                                    </div>
                                </div>
                            </div>

                        </div>


                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-offset-5 col-sm-10">
             <!--   <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
                    存
                </button>&nbsp;-->
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
                </button>
            </div>
        </div>


    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "carrier/entrust";

    //新增校验逻辑
    function submitHandler() {
        if ($.validate.form()) {
            if ($("#image").val() != "") {
                $("#image").fileinput('upload');
            }else{
                commit();
            }
        }
    }

    //新增提交
    function commit() {
        $.operate.saveTab(prefix + "/editSaveExp", $('#form-adnormal-edit').serialize());
    }

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');

        //循环图片路径信息，初始化图片上传区域
        var files = [[${files}]];

        var image = {
            maxFileCount: 0,
            publish: "publishFlag",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        if(files == null){
            $.file.initAddFiles("image", "appendixId", image);
        }else{
            $.file.loadEditFiles("image", "appendixId", files, image);
        }

        // 图片上传成功后
        $("#image").on('filebatchuploadsuccess', function (event, data) {
            var tid = data.response.tid;
            alert(tid);
            $("#appendixId").val(tid);
            commit();
        });
    });


</script>
</body>

</html>