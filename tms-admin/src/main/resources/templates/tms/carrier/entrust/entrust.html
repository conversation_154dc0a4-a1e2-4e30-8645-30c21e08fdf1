<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('委托单列表')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">

        <div class="btn-group-sm" id="toolbar" role="group">

            <!-- <a class="btn btn-primary single disabled" id="expTracking" onclick="selectExp()" shiro:hasPermission="carrier:entrustExp:view">
                <i class="fa fa-warning"></i> 异常跟踪
            </a> -->
            <a class="btn btn-primary single disabled" onclick="pick()" shiro:hasAnyPermissions="carrier:entrust:pick">
                <i class="fa fa-angle-left"></i> 提货作业
            </a>
            <a class="btn btn-primary single disabled" onclick="arrive()" shiro:hasAnyPermissions="carrier:entrust:arrive">
                <i class="fa fa-angle-right"></i> 到货作业
            </a>
           <!-- <a class="btn btn-primary single disabled" onclick="selectStatus('start')" shiro:hasPermission="carrier:entrust:status">
                <i class="fa fa-bell"></i> 出发
            </a>-->
            <!--<a class="btn btn-primary single disabled" onclick="arri()" shiro:hasPermission="carrier:entrust:arri">
                <i class="fa fa-bell"></i> 到达
            </a>
            <a class="btn btn-primary single disabled" onclick="deli()" shiro:hasPermission="carrier:entrust:deli">
                <i class="fa fa-bell"></i> 确认送达
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">

    var prefix = ctx + "carrier/entrust";
    var detailFlag = [[${@permission.hasPermi('carrier:entrust:detail')}]];//权限
    var wdId = [[${wdid}]];  //运单ID
    var vbillstatus = [[${vbillstatus}]]//委托单状态
    var carLen = [[${@dict.getType('car_len')}]];//车长
    var balaCorp = [[${@dict.getType('bala_corp')}]];//结算公司

    $(function () {
        var options = {
            url: prefix + "/list/"+wdId,
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            showToggle:false,
            showColumns:true,
            modalName: "委托单",
            fixedColumns: true,
            clickToSelect:true,
            fixedNumber:0,
            height: 560,
            columns: [{
                checkbox: true
            },

                {
                    title: 'id',
                    visible: false,
                    field: 'entrustId'
                },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs '+detailFlag+' " href="javascript:void(0)" title="明细"  onclick="detail(\'' + row.entrustId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');

                        //2(已提货)
                        if(row.vbillstatus == '2'){
                            //根据地址类型判断 3代表中转站
                            if(row.arriAddressType == "3"){
                                //送达
                                actions.push('<a class="btn btn-xs '+detailFlag+' " href="javascript:void(0)" title="送达"  onclick="arri(\'' + row.entrustId + '\')"><i class="fa fa-bell" style="font-size: 15px;"></i></a>');
                            }else{
                                //确认送达
                                actions.push('<a class="btn btn-xs '+detailFlag+' " href="javascript:void(0)" title="确认送达"  onclick="deli(\'' + row.entrustId + '\')"><i class="fa fa-bell" style="font-size: 15px;"></i></a>');
                                }
                        }
                        //3（已到货） 回单
                        if(row.vbillstatus == '3'){
                            if(row.arriAddressType != "3"){
                                //0:不是；1是
                                if(row.ifReceipt == '0'){
                                    actions.push('<a class="btn btn-xs '+detailFlag+' " href="javascript:void(0)" title="回单"  onclick="receipt(\'' + row.entrustId + '\')"><i class="fa fa-file" style="font-size: 15px;"></i></a>');
                                }
                            }
                        }
                        return actions.join('');
                    }
                },
                {
                    title: '发货单号',
                    field: 'invoiceVbillno',
                    align: 'left'
                },
                {
                    title: '委托单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(row,value) {
                        var context = '';
                        vbillstatus.forEach(function (v) {
                            if (v.value == value.vbillstatus) {
                                context = v.context;
                                return false;
                            }
                        });
                        return context;
                    }
                },
                {
                    title: '要求提货/到货日期',
                    field: 'reqDeliDate',
                    align: 'left',
                    width: '140px',
                    formatter: function status(value, row, index) {
                        return $.table.tooltip(value)+"<br/>"+$.table.tooltip(row.reqArriDate);
                    }
                },
                {
                    title: '装卸货地址',
                    align: 'left',
                    formatter: function status(value,row) {
                        let deliAddr=row.deliProName+row.deliCityName+row.deliAreaName;
                        let arriAddr=row.arriProName+row.arriCityName+row.arriAreaName;
                        return '<span class="label label-warning pa2">装</span>'+$.table.tooltip(deliAddr)+'<br/><span class="label label-success pa2">卸</span>'+$.table.tooltip(arriAddr);
                    }
                },
                {
                    title: '货品信息',
                    align: 'left',
                    field: 'entPackGoodsList',
                    width: '140px',
                    formatter: function (value, row, index) {
                        // let goods=[]
                        // if(row.numCount){
                        //     goods.push(row.numCount+'件');
                        // }
                        // if(row.weightCount){
                        //     goods.push(row.weightCount+'吨');
                        // }
                        // if(row.volumeCount){
                        //     goods.push(row.volumeCount+'m³');
                        // }
                        // return $.table.tooltip(value)+"<br/>"+goods.join(' | ');
                        let goodsText=value.map(item=>{
                            let text=item.goodsName+"<br/>";
                            let goods=[]
                            if(item.num){
                                goods.push(item.num+'件');
                            }
                            if(item.weight){
                                goods.push(item.weight+'吨');
                            }
                            if(item.volume){
                                goods.push(item.volume+'m³');
                            }
                            return text+goods.join(' | ');
                        })

                        return goodsText.join("<br/>")
                    }
                },
                {
                    title: '要求车长车型',
                    align: 'left',
                    field: 'carLenId',
                    width: '140px',
                    formatter: function (value, row, index) {
                        let text=''
                        if(value) {
                            text=$.table.selectDictLabel(carLen, value)+"米";
                        }
                        return text+$.table.tooltip(row.carTypeName);
                    }
                },
              /*  {
                    title: '结算公司',
                    align: 'left',
                    field: 'billingCorp',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(balaCorp, value.billingCorp);
                    }
                },*/
                // {
                //     title: '承运商名称',
                //     align: 'left',
                //     field: 'carrName'
                // },
                {
                    title: '司机',
                    align: 'left',
                    field: 'driverName',
                    formatter: function status(value, row, index) {
                        return $.table.tooltip(value)+"<br/>"+$.table.tooltip(row.driverMobile);
                    }
                },  
                {
                    title: '车辆',
                    align: 'left',
                    field: 'carno'
                },
               
                // {
                //     title: '实际提货时间',
                //     align: 'left',
                //     field: 'actDeliDate'
                // },
                // {
                //     title: '实际到货时间',
                //     align: 'left',
                //     field: 'actArriDate'
                // },

            ]
        };

        $.table.init(options);
    });

    //异常情况
    function selectExp() {
        var entrustId = $.table.selectColumns('entrustId');
        var url = prefix + "/expView/"+entrustId;
        $.modal.openTab("异常跟踪", url);
    }

    //明细
    function detail(entrustId) {
        var url = prefix + "/detail/"+entrustId;
        $.modal.openTab("委托单详细", url);
        // var url = ctx + "trustDeed" + "/detail?entrustId="+entrustId;
        // $.modal.openTab($.table._option.modalName + "详细", url);
    }

    //到达
    function arri(entrustId) {
        var url = prefix + "/arri";
        var vbillstatus = $.table.selectColumns("vbillstatus").join();
        if(vbillstatus == 3){
            $.modal.alertWarning("已到货");
            return;
        }
        var data = {entrustId:entrustId};
        $.modal.confirm("是否确认到达",function () {
            $.operate.post(url,data);
        });
    }

    //确认送达
    function deli(entrustId) {
        var url = prefix + "/arrival/"+entrustId;
        $.modal.openTab("到货作业", url);
        // var url = prefix + "/deli/"+entrustId;
        // $.modal.openTab("确认送达", url);
    }

    //回单
    function receipt(entrustId) {
        var url = prefix + "/receipt/"+entrustId;
        $.modal.openTab("回单", url);
    }

    //在途状态
    // function selectStatus(status) {
    //     var entrustId = $.table.selectColumns('entrustId');
    //     var url = prefix + "/status?entrustId=" +entrustId+ "&status=" +status;
    //     var title;
    //     if(status=="start"){
    //         title="出发";
    //     }else{
    //         title='到达';
    //     }
    //     $.modal.openTab(title, url);
    // }
    //提货作业
    function pick() {
        var  entrustIds = $.table.selectColumns("entrustId").join();

        //获取选中的行
        var rows = []
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法提货，请点击详情进行提货！");
                return;
            }
        }

        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);

        //判断勾选一条的情况
        if(rows.length == 1){
            var  vbillstatus = $.table.selectColumns("vbillstatus").join();
            //跳转提货作业详情页面
            if(vbillstatus == 1){
                var url = prefix + "/pick/"+entrustIds;
                $.modal.openTab("提货作业", url);
            }else{
                $.modal.alertWarning("委托单状态需要为已确认才能进行提货作业！");
            }
        }else{
            /*
            * 判断:
            *      如果都为公路零担的，要求提货日期相同
            */
            //所选择的列
            var rows2 = $.btTable.bootstrapTable('getSelections');
            //标记
            var flag = 0;
            for (var i = 0; i < rows2.length; i++) {
                //是否为公路零担
                if(rows2[i]['transCode'] === '1'){
                    flag++;
                }
            }
            //如果都为公路零担继续比较要求提货日
            if(flag == rows2.length){
                for(var i=0;i<rows2.length;i++){
                    var reqDeliDate0 = rows2[0]['reqDeliDate'];
                    var reqDeliDate1 = rows2[i]['reqDeliDate'];
                    if(reqDeliDate0 != '' && reqDeliDate0 != null){
                        reqDeliDate0 = reqDeliDate0.substring(0 ,10);
                    }
                    if(reqDeliDate1 != '' && reqDeliDate1 != null){
                        reqDeliDate1 = reqDeliDate1.substring(0 ,10);
                    }
                    if(reqDeliDate0 != reqDeliDate1){
                        $.modal.alertWarning("请选择要求提货日一致的委托单进行提货作业！");
                        return false;
                    }
                }
            }else{
                //判断状态是否一致
                var vbillstatus = $.table.selectColumns("vbillstatus");
                var status =  vbillstatus[0];
                for( var i = 0 ; i < vbillstatus.length ; i++ ){
                    if(vbillstatus[i] != status || status != 1 ){
                        $.modal.alertWarning("请选择状态一致且为已确认的委托单进行提货作业！");
                        return false;
                    }
                }
                //判断运单号是否一致
                var lotColum = $.table.selectColumns("lot");
                var lot =  lotColum[0];
                for( var i = 0 ; i < lotColum.length ; i++ ){
                    if(lotColum[i] != lot){
                        $.modal.alertWarning("请选择运单号一致的委托单进行提货作业！");
                        return false;
                    }
                }
                //判断提货地址是否一致
                var deliAddrName = $.table.selectColumns("deliAddrName");
                var deliAddr =  deliAddrName[0];
                for( var i = 0 ; i < deliAddrName.length ; i++ ){
                    if(deliAddrName[i] != deliAddr){
                        $.modal.alertWarning("请选择提货地址一致的委托单进行提货作业！");
                        return false;
                    }
                }
            }
            var url = prefix + "/pick/"+entrustIds;
            $.modal.openTab("提货作业", url);
        }
    }

    //到货作业
    function arrive() {
        var  entrustIds = $.table.selectColumns("entrustId").join();

        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法到货，请点击详情进行到货！");
                return;
            }
        }
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);

        //判断勾选一条的情况
        if(rows.length == 1) {
            var vbillstatus = $.table.selectColumns("vbillstatus");
            if (vbillstatus == 2) {
                var url = prefix + "/arrival/"+entrustIds;
                $.modal.openTab("到货作业", url);
            } else {
                $.modal.alertError("委托单状态需要为已提货才能进行到货作业！");
            }
        }else{
            //判断状态是否一致
            var vbillstatus = $.table.selectColumns("vbillstatus");
            var status =  vbillstatus[0];
            for( var i = 0 ; i < vbillstatus.length ; i++ ){
                if(vbillstatus[i] != status || status != 2 ){
                    $.modal.alertWarning("请选择状态一致且为已提货的委托单进行到货作业！");
                    return false;
                }
            }
            //判断运单号是否一致
            var lotColum = $.table.selectColumns("lot");
            var lot =  lotColum[0];
            for( var i = 0 ; i < lotColum.length ; i++ ){
                if(lotColum[i] != lot){
                    $.modal.alertWarning("请选择运单号一致的委托单进行到货作业！");
                    return false;
                }
            }
            //判断到货地址是否一致
            var arriAddrName = $.table.selectColumns("arriAddrName");
            var arriAddr =  arriAddrName[0];
            for( var i = 0 ; i < arriAddrName.length ; i++ ){
                if(arriAddrName[i] != arriAddr){
                    $.modal.alertWarning("请选择收货地址一致的委托单进行到货作业！");
                    return false;
                }
            }
            var url = prefix + "/arrival/"+entrustIds;
            $.modal.openTab("到货作业", url);
        }
    }


</script>

</body>
</html>