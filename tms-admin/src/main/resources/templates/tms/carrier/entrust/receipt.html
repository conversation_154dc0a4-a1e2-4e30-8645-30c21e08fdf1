<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('确认到达')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .fl{
        float: left;
    }
    .fcff8{
        color: #FF8900;
    }
    .lh26{
        line-height: 26px;
    }
    .file-drop-zone{
        overflow-x: auto;
    }
    .flex{
        display: flex;
        align-items:center;
        justify-content:flex-start;
        color: #808080;
    }
    .flex_left{
        width: 80px;
        text-align: right;
        color: #333333 !important;
        margin-bottom: 0;
    }
    .flex_right{
        min-width:0;
        flex:1;
    }

    .lazur-bg{
        background: #f8ac59;
        width: 10px;
        height: 10px;
        border: none;
    }
    .timeline_vertical-container {
        position: relative;
        padding: 0;
        margin-top: 0em;
        margin-bottom: 0em;
    }
    .vertical-timeline-block{
        margin: 0;
    }
    .vertical-timeline-block::before {
        content: '';
        position: absolute;
        top: 0;
        left: 5px;
        width: 1px;
        height: 100%;
        background: #0ba687;
    }
    .fixedfooter{
        position: fixed;
        bottom: 0;
        left: 50%;
        width: 160px;
        margin-left: -80px;
    }
    .vertical-timeline-content {
        margin-left: 5px;
        padding: 1em;
    }
    .vertical-timeline-content {
        /*position: relative;*/
        /*margin-left: 60px;*/
        /*background: white;*/
        /*border-radius: 0.25em;*/
        padding: 0;
    }
    .vertical-container{
        width: 100%;
        max-width: none;
    }

    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .table-bordered {
        border: 0px solid #EBEBEB;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .input-group .form-control {
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
</style>
<body>
<div class="form-content">
    <form id="form-entrust-edit" class="form-horizontal" novalidate="novalidate" >
        <input name="entTrackingStatusId" id="entTrackingStatusId"  type="hidden"  />
        <!--委托单id-->
        <input name="entrustId" id="entrustId" th:value="${entrust?.entrustId}" type="hidden"  />
        <!--运单id-->
        <input name="lotId" th:value="${entrust?.lotId}" type="hidden"  />
        <!--发货单id-->
        <input name="orderno" th:value="${entrust?.orderno}" type="hidden"  />

        <div class="row">
            <div class="col-xs-6">
                <div class="panel-body">
                    <div class="padt20">
                        <div class="row mt20">
                            <div class="col-md-12 col-sm-12 ">
                                <div class="flex">
                                    <label class="flex_left">运单号：</label>
                                    <div class="flex_right" th:text="${entrust.lot}"></div>
                                </div> 
                            </div>
                            <div class="col-md-12 col-sm-12 mt5">
                                <div class="flex">
                                    <label class="flex_left">司机：</label>
                                    <div class="flex_right" th:text="${entrust.driverName+'/'+entrust.driverMobile}"></div>
                                </div> 
                            </div>
                            <div class="col-md-12 col-sm-12 mt5">
                                <div class="flex" style="align-items: flex-start;">
                                    <label class="flex_left">货品：</label>
                                    <div class="flex_right">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr style="background-color: #f9f9f9;">
                                                    <th style="width: 25%;">货品名称</th>
                                                    <th style="width: 25%;">件数</th>
                                                    <th style="width: 25%;">重量(吨)</th>
                                                    <th style="width: 25%;">体积(m³)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            <tr th:each="goods,stat:${entPackGoodsList}" style="background-color: #ffffff;">
                                                <td style="text-align:center;"  th:text="${goods.goodsName}"></td>
                                                <td style="text-align:center;"  th:text="${goods.num}"></td>
                                                <td style="text-align:center;"  th:text="${goods.weight}"></td>
                                                <td style="text-align:center;"  th:text="${goods.volume}"></td>
                                            </tr>
                                            </tbody>
                                            <tfoot>
                                            <tr style="background: #FFFCD3;text-align: center">
                                                <td>合计：</td>
                                                <td th:text="${numSum}"></td>
                                                <td th:text="${weightSum}"></td>
                                                <td th:text="${volumeSum}"></td>
                                            </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div> 
                            </div>
                        </div>  

                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="panel-body">
                    <input name="deptId" type="hidden" id="treeId">

                    <div class="col-xs-12" th:each="dict : ${pic}" th:if="${dict.value == '3'}">
                        <div class="mt10" style="line-height: 24px">
                            <span th:text="${dict.context}+'：'"></span>
                        </div>
                        <div class="">
                            <input name="tid3" value="" type="hidden">
                            <input th:id="'image'+${dict.value}" class="form-control"
                                   th:name="'image'+${dict.value}" type="file" multiple>
                            <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}"
                                   type="hidden">
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "carrier/entrust";
    //上传图片类型
    //var carrPicType = [[${pic}]];

    var pickPicType = [[${pic}]];
    console.log('pickPicType',pickPicType)
    $(function () {
        //循环字典表中图片类型，初始化图片上传区域
        for (var i = 0; i < pickPicType.length; i++) {
            if(pickPicType[i].value == '3'){
                var dictValue = pickPicType[i].value;
                var publishFlag = "cmt_" + dictValue;
                var picParam = {
                    maxFileCount: 0,
                    publish: publishFlag,  //用于绑定下一步方法
                    fileType: null//文件类型
                };
                var tid = "tid" + dictValue;
                var imageId = "image" + dictValue;
                $.file.initAddFiles(imageId, tid, picParam);
            }
        }
    });


    //上传完成标志位
    var flag;
    var array;

    function submitHandler() {
        if ($.validate.form()) {
            $.modal.loading("正在处理中，请稍后...");
            $("#image3").fileinput('upload');
            jQuery.subscribe("cmt_3",commit);
            // flag = "";
            // array = [];
            // for (var i = 0; i < carrPicType.length; i++) {
            //     var value = carrPicType[i].value;
            //     if ($("#image" + value).val() != "") {
            //         array[i] = value;
            //     }
            // }
            //
            // if (array.length == 0) {
            //     commit();
            // }
            //
            // if (array.length == 1) {
            //     $("#image" + array[0]).fileinput('upload');
            //     flag = "done" + array[0];
            //     jQuery.subscribe(flag, commit);
            // }
            //
            // if (array.length > 1) {
            //     $("#image" + array[0]).fileinput('upload');
            //     flag = "done" + array[0];
            //     for (var i = 0; i < array.length; i++) {
            //         if (i < array.length - 1) {
            //             jQuery.subscribe(flag, uploadPic(array[i + 1]));
            //         }
            //         if (i == array.length - 1) {
            //             jQuery.subscribe(flag, commit);
            //         }
            //     }
            // }
        }
    }

    function uploadPic(dictValue) {
        $("#image" + dictValue).fileinput('upload');
        flag = "done" + dictValue;
    }

    function commit() {
        // var f = true;
        // for (var i = 0; i < array.length; i++) {
        //     if ($("#tid" + array[i]).val() == "") {
        //         f = false;
        //         setTimeout(commit, 1000);
        //     }
        // }
        // if (f) {
        //     $.operate.saveTab(prefix + "/saveReceipt", $('#form-entrust-edit').serialize());
        // }
        let isRefresh = [[${isRefresh}]]
        let openTab = [[${openTab}]]
        if ($.validate.form()) {
            // var data = {};
            // data.entrustId = $("#entrustId").val();
            // data.tid = $("#tid").val();
            // console.log(data)

            $.operate.saveTab(prefix + "/saveReceipt", $('#form-entrust-edit').serialize());
        }else{
            $.modal.closeLoading();
        }
    }



</script>
</body>

</html>