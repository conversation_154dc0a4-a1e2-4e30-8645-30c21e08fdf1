<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增在途跟踪')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-entrust-edit" class="form-horizontal" novalidate="novalidate" >
        <input name="entTrackingStatusId" id="entTrackingStatusId"  type="hidden"  />
        <!--委托单id-->
        <input name="entrustId" id="entrustId" th:value="${entrust?.entrustId}" type="hidden"  />
        <!--发货单号-->
        <input name="invoiceVbillno" th:value="${entrust?.orderno}" type="hidden"  />
        <!--委托单号-->
        <input name="entrustVbillno" th:value="${entrust?.vbillno}" type="hidden"  />
        <!--运单号-->
        <input name="lot" th:value="${entrust?.lot}" type="hidden"  />
        <!--公司id-->
        <input name="corpId" th:value="${entrust?.corpId}" type="hidden"  />
        <!--基础信息 begin-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <input name="deptId" type="hidden" id="treeId">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">在途状态：</label>
                                    <div class="col-sm-8">
                                        <div th:if="${status=='start'}" class="col-sm-8">
                                            <input type="hidden" name="trackingStatus" th:value="0">
                                            <div class="col-sm-8" th:text="出发">
                                            </div>
                                        </div>
                                        <div th:if="${status=='end'}" class="col-sm-8">
                                            <input type="hidden" name="trackingStatus" th:value="1">
                                            <div class="col-sm-8" th:text="到达">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">跟踪时间：</label>
                                    <div class="col-sm-8">
                                        <input name="trackingTime"   class="time-input form-control" type="text"
                                               maxlength="30" aria-required="true">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">当前地址：</label>
                                    <div class="col-sm-8">
                                        <select name="currProvinceId" id="provinceId"  class="form-control valid" aria-invalid="false"></select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-2">
                                <select name="currCityId" id="cityId"  class="form-control valid" aria-invalid="false"></select>
                            </div>

                            <div class="col-sm-2">
                                <select name="currAreaId" id="areaId"  class="form-control valid" aria-invalid="false"></select>
                            </div>


                            <div class="col-sm-5">
                                <input name="currDetailAddr"  placeholder="请输入详细地址" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>



                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                        <textarea name="trackingMemo"  maxlength="500" class="form-control"
                                                  rows="3">
                                        </textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2"> 跟踪照片：</label>
                                    <div class="col-sm-4">
                                        <input th:id="photo" th:name="photo" type="file" class="form-control" multiple>
                                        <input th:id="tid" th:name="appendixId"  type="hidden" >
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <!--基础信息 end-->


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "carrier/entrust";


    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        // 初始化省市区
        $.provinces.init("provinceId", "cityId","areaId");

        //初始化附件信息
        var photoExp = [[${sysUploadFileList}]];
        var photoExpParam = {
            maxFileCount: 0,
            publish:"imgDone",  //用于绑定下一步方法
            fileType:null	//文件类型
        };
        $.file.loadEditFiles("photo", "tid", photoExp, photoExpParam);

    });


    function submitHandler() {
        if ($.validate.form()  & $('#photo').fileinput('valid')) {
            //上传图片
            $('#photo').fileinput('upload');
            //图片上传完成后提交表单
            jQuery.subscribe("imgDone", commit);
        }
    }

    /**
     * 提交
     */
    function commit() {
        var entrustId = $("#entrustId").val();
        $.operate.saveTab(prefix + "/saveStatus/"+entrustId, $('#form-entrust-edit').serialize());
    }



</script>
</body>

</html>