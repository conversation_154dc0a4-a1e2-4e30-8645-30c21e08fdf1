<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('提货作业')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    html,body{
        font-size: 14px;
    }
    .fw{
        font-weight: bold;
    }
    .fl{
        float: left;
    }
    .fcff8{
        color: #FF8900;
    }
    .fc80{
        color: #808080;
    }
    .mt10{
        margin-top: 10px;
    }
    .lh26{
        line-height: 26px;
    }
    .file-drop-zone{
        overflow-x: auto;
    }
    .flex{
        display: flex;
        align-items:center;
        justify-content:flex-start;

    }
    .flex_left{
        width: 60px;
        text-align: right;
        color: #808080;
        margin-bottom: 0;
    }
    .flex_right{
        min-width:0;
        flex:1;
    }

    .lazur-bg{
        background: #f8ac59;
        width: 10px;
        height: 10px;
        border: none;
    }
    .timeline_vertical-container {
        position: relative;
        padding: 0;
        margin-top: 0em;
        margin-bottom: 0em;
    }
    .vertical-timeline-block{
        margin: 0;
    }
    .vertical-timeline-block::before {
        content: '';
        position: absolute;
        top: 0;
        left: 5px;
        width: 1px;
        height: 100%;
        background: #0ba687;
    }
    .fixedfooter{
        position: fixed;
        bottom: 0;
        left: 50%;
        width: 160px;
        margin-left: -80px;
    }
    .vertical-timeline-content {
        margin-left: 0px;
         padding: 1em;
    }
    .vertical-timeline-content {
        /*position: relative;*/
        /*margin-left: 60px;*/
        /*background: white;*/
        /*border-radius: 0.25em;*/
        padding: 0;
    }
    .vertical-container{
        width: 100%;
        max-width: none;
    }

    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .table-bordered {
        border: 0px solid #EBEBEB;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .input-group .form-control {
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
</style>
<body>
<div class="form-content" style="padding-top: 0;">
    <form id="form-receipt" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" id="entrustId" name="entrustId" th:value="${entrustDto.entrustId}">
        <input name="lotId" type="hidden" th:value="${entrustDto.lotId}">
        <input name="carId" type="hidden" th:value="${entrustDto.carnoId}">
        <input name="carNo" type="hidden" th:value="${entrustDto.carno}">
        <input name="driverId" type="hidden" th:value="${entrustDto.driverId}">
        <div class="row">
            <div class="col-xs-6">
                <div class="panel-body">
                    <div class="padt20">
                        <div class="row mt20">
                            <div class="col-md-12 col-sm-12 ">
                                <div class="flex">
                                    <label class="flex_left">运单号：</label>
                                    <div class="flex_right" th:text="${entrustDto.lot}"></div>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12 mt5">
                                <div class="flex">
                                    <label class="flex_left">司机：</label>
                                    <div class="flex_right" th:text="${entrustDto.driverName+'/'+entrustDto.driverMobile}"></div>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12 mt5">
                                <div class="flex">
                                    <label class="flex_left">车辆：</label>
                                    <div class="flex_right" th:text="${entrustDto.carno+'/'+entrustDto.carLen+'米'+entrustDto.carTypeName}"></div>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12 mt5">
                                <div class="flex" style="align-items: flex-start;">
                                    <label class="flex_left">货品：</label>
                                    <div class="flex_right">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr style="background-color: #f9f9f9;">
                                                    <th style="width: 25%;">货品名称</th>
                                                    <th style="width: 25%;">件数</th>
                                                    <th style="width: 25%;">重量(吨)</th>
                                                    <th style="width: 25%;">体积(m³)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr th:each="goods,stat:${entPackGoodsList}" style="background-color: #ffffff;">
                                                    <td style="text-align:center;"  th:text="${goods.goodsName}"></td>
                                                    <td style="text-align:center;"  th:text="${goods.num}"></td>
                                                    <td style="text-align:center;"  th:text="${goods.weight}"></td>
                                                    <td style="text-align:center;"  th:text="${goods.volume}"></td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr style="background: #FFFCD3;text-align: center">
                                                    <td>合计：</td>
                                                    <td th:text="${numSum}"></td>
                                                    <td th:text="${weightSum}"></td>
                                                    <td th:text="${volumeSum}"></td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div style="padding-left: 30px;" class="mt10">
                                <div class="mt10" >
                                    <span class="fc80">要求装货时间地址：</span>
                                </div>
                                <div class="mt5">
                                    <div class='vertical-container light-timeline'>
                                        <div class='vertical-timeline-block'>
                                            <!--                                        <div class='vertical-timeline-icon lazur-bg'>-->
                                            <!--                                        </div>-->
                                            <div class='vertical-timeline-content'>
                                                <div th:text="${#dates.format(entrustDto.reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                                <div class="fw" th:text="${entrustDto.deliAddrName}"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="panel-body">
                    <div class="padt20">

                    </div>
                  </div>

                <div class="panel-body" th:with="type=${@dict.getType('pick_pic_type')}">
                    <div class="" style="padding-bottom: 20px">
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="flex">
                                    <div class="flex_left" style="width: 100px"><span class="fcff8">* </span>提货时间：</div>
                                    <div class="flex_right">
                                        <input placeholder="请选择时间" name="actArriDate" id="actArriDate" type="text" th:value="${#dates.format(entrustDto.signTime, 'yyyy-MM-dd')}" class="timeInput form-control" readonly required="true" aria-required="true">
                                        <input name="actLeaveDate" id="actLeaveDate" type="hidden">
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-12 mt10" th:each="dict:${type}" th:if="${dict.dictValue == '2'}">
                                <div class="flex">
                                    <div class="flex_left" style="width: 100px">
                                        <span class="fcff8">* </span>
                                        <span>提货照片：</span>
                                    </div>
                                    <div class="flex_right">
                                        <span class="fcff8">(包括车辆到场，验车、开始装车、结束装车、确认发货照片)</span>
                                    </div>
                                </div>
                                <div class="flex mt10">
                                    <div class="flex_left" style="line-height: 24px;width: 100px">

                                    </div>
                                    <div class="flex_right">
                                        <div class="">
                                            <input th:id="'image'+${dict.dictValue}" class="form-control"
                                                   th:name="'image'+${dict.dictValue}" type="file" multiple>
                                            <input th:id="'tid'+${dict.dictValue}" th:name="'tid'+${dict.dictValue}"
                                                   type="hidden">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <span th:with="type=${@dict.getType('pick_pic_type')}">
                    <input  th:each="dict:${type}" th:if="${dict.dictValue != '2'}" th:id="'tid'+${dict.dictValue}" th:name="'tid'+${dict.dictValue}" type="hidden">
                </span>
                <div class="panel-body">
                    <div class="col-xs-12">
                        <div class="row">
                            <div class="flex">
                                <div class="flex_left" style="width: 100px">
                                    备注：
                                </div>
                                <div class="flex_right">
                                    <textarea name="note" class=" form-control" maxlength="100" rows="3" placeholder="添加备注"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                  </div>
            </div>
        </div>

        <div class="fixedfooter">
            <div class="">
                <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
                    存
                </button>&nbsp;
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
                </button>
            </div>
        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var pickPicType = [[ ${@dict.getType('pick_pic_type')} ]];

    $(function () {
        for (var i = 0; i < pickPicType.length; i++) {
            if(pickPicType[i].dictValue == '2'){
                var dictValue = pickPicType[i].dictValue;
                var publishFlag = "cmt_" + dictValue;
                var picParam = {
                    maxFileCount: 0,
                    publish: publishFlag,  //用于绑定下一步方法
                    fileType: null//文件类型
                };
                var tid = "tid" + dictValue;
                var imageId = "image" + dictValue;
                $.file.initAddFiles(imageId, tid, picParam);
            }   
        }
        
    });

    /**
     * 校验
     */
     $("#form-receipt").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            actArriDate:{
                required:true,
            }
        }
    });

    // function submitHandler() {
    //     $.modal.loading("正在处理中，请稍后...");
    //     $("#image2").fileinput('upload');
    //     jQuery.subscribe("cmt_2",commit);
    // }
    function submitHandler() {
        if ($.validate.form()) {
            //判读到厂时间和要求提货期
            var reqDeliDate = $("#actLeaveDate").val();
            var actArriDate = $("#actArriDate").val();

            let entrustId = $("#entrustId").val();

            $.ajax({
                type: "POST",
                url: ctx + "trace/check_driver_receipt_pick?entrustId="+entrustId,
                async: false,
                success: function(r){
                    let m = `注意：<br>`
                    let i = 0;
                    if(r.code == 0 && r.msg != null && r.msg != "") {
                        i++
                        m += i+`、`+r.msg+`<br>`
                    }
                    if(reqDeliDate != actArriDate.substring(0,10)){
                        i++
                        m += i+`、请核实提货时间，如需调整发货单要求提货时间，需要在提货之前调整。`+`<br>`
                    }
                    m += `确定要提货吗？`

                    let area = ['300px','200px']
                    if (i > 0) {
                        area= ['550px','260px']
                    }

                    var lock = false;
                    layer.confirm(m, {
                        area:area,
                        icon: 3,
                        title: "是否提货",
                        btn: ['提货', '取消']
                    }, function (index) {
                        if (!lock) {
                            lock = true
                            layer.close(index);

                            $.modal.loading("正在处理中，请稍后...");
                            $("#image2").fileinput('upload');
                            jQuery.subscribe("cmt_2",commit);
                        }
                    });
                }
            });
        }
    }

    function commit() {
        let isRefresh = [[${isRefresh}]]
        let openTab = [[${openTab}]]
        if ($.validate.form()) {
            var data = $("#form-receipt").serializeArray();
            $.operate.saveTab(ctx + "trace/savePick", data);
        }else{
            $.modal.closeLoading();
        }
    }

    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#actArriDate', //指定元素
            type: 'datetime',
            trigger: 'click',
            max: 3,
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours=now.getHours();
                this.dateTime.minutes=now.getMinutes();
                this.dateTime.seconds=now.getSeconds();
            },
            done: function(value, date, endDate){
                $("#actArriDate").val(value);
                $("#actLeaveDate").val(value);
                //单独校验日期
                $("#form-receipt").validate().element($("#actArriDate"))
                $("#form-receipt").validate().element($("#actLeaveDate"))
            }
        });
    });


</script>
</body>
</html>