<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
    <title>绑定小黑卡</title>
    <link rel="stylesheet" href="/js/mobile_dialog/dialog.css" />
    <style>
        *{
            padding: 0;
            margin: 0;
        }
        .title {
            margin: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .mt20 {
            margin-top: 20px;
        }

        .flex {
            display: flex;
            justify-content: space-between;
        }

        .f20 {
            font-size: 20px;
        }

        .label {
            display: inline;
            padding: 0.2em 0.6em 0.3em;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25em;
            background-color: #d1dade;
            color: #5e5e5e;
            font-size: 14px;
            font-weight: 600;
            padding: 3px 8px;
            text-shadow: none;
        }

        .pa2 {
            padding: 4px;
            font-weight: 100;
            display: inline-block;
            margin-right: 5px;
            vertical-align: middle;
        }

        .label-success {
            background-color: #1c84c6;
            color: #FFFFFF;
        }

        .label-warning {
            background-color: #f8ac59;
            color: #FFFFFF;
        }

        .boot {
            /* margin: 20px; */
            padding: 10px;
            border-top: 1px solid #ddd;
            text-align: center;
        }

        .btn {
            width: 80%;
            display: inline-block;
            padding: 10px 12px;
            margin-bottom: 0;
            font-size: 20px;
            font-weight: 400;
            line-height: 1.42857143;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -ms-touch-action: manipulation;
            touch-action: manipulation;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-image: none;
            border: 1px solid transparent;
            border-radius: 5px;
            text-decoration: none;
        }

        .btn-success {
            background-color: #1c84c6;
            border-color: #1c84c6;
            color: #FFFFFF;
        }
        .btn-disabled {
            background-color: lightgray;
            border-color: lightgray;
            color: #fff;
        }

        .mb85{
            margin-bottom: 85px;
        }
        .fixed{
            position: fixed;
            width: 100%;
            bottom: 0;
            left: 0;
            background-color: #ffffff;
            padding: 0 20px;
            box-sizing: border-box;
        }
    </style>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script src="/js/jquery.min.js"></script>
    <script src="/js/mobile_dialog/dialog.min.js"></script>
    <script th:inline="javascript">
        wx.config({
            debug: false,
            appId: [[${appId}]],
            timestamp: [[${timestamp}]],
            nonceStr: [[${noncestr}]],
            signature: [[${signature}]],
            jsApiList: ['scanQRCode']
        });
        wx.ready(() => {
            //alert('成功')
        })
        wx.error(function (res) {
            //alert('出错了：' + JSON.stringify(res)); //wx.config配置错误，会弹出窗口哪里错误，然后根据微信文档查询即可。
        });
        function scan() {
            wx.scanQRCode({
                needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有, ['qrCode','barCode']
                // ['qrCode']:即能扫二维码，也能扫(清晰)条形码，返回【目标码】
                // ['barCode']:只能扫二维码，返回【条码格式,目标码】
                // ['qrCode','barCode']: 扫条码时，随机返回【目标码】或【条码格式,目标码】
                success: function (res) {
                    //alert(JSON.stringify(res))
                    //alert(res.resultStr)
                    var code = res.resultStr.split(",");
                    if (code.length == 1) {
                        code = code[0];
                    } else if (code.length == 2) {
                        code = code[1];
                    } else {
                        alert("未识别的图形码");
                        return;
                    }
                    $('#imei').text(code)
                },
                cancel: function (res) {
                    // 取消扫码会触发?
                    //alert("cancel:" + JSON.stringify(res))
                },
                fail: function (res) {
                    alert("fail:" + JSON.stringify(res))
                },
            });
        }

        var submitting = false;
        function bind() {
            var imei = $('#imei').text().trim();
            if (!imei) {
                $(document).dialog({
                    type : 'toast',
                    infoIcon: '/js/mobile_dialog/fail.png',
                    infoText: '请先扫小黑卡上的二维码',
                    autoClose: 2000
                });
                return;
            }
            if (submitting) {
                return;
            }
            $(document).dialog({
                type : 'confirm',
                style: 'ios',  // default、ios、android
                titleText: '提交确认',
                content: '确认提交绑定吗？',
                onClickConfirmBtn: function(){
                    submitting = true
                    var loading = $(document).dialog({
                        type : 'toast',
                        infoIcon: '/js/mobile_dialog/loading.gif',
                        infoText: '提交中...',
                    });
                    $.ajax({
                        url: '/tq/bind',
                        data: 'entrustId=[(${entrust.entrustId})]&imei=' + imei,
                        type: 'post',
                        success: function(result){
                            loading.close();
                            if (result.code == 0) {
                                location.reload()
                            } else {
                                $(document).dialog({
                                    style: 'ios',
                                    content: result.msg
                                });
                            }
                        },
                        error: function(response) {
                            $(document).dialog({
                                style: 'ios',
                                content: "出错了：" + JSON.stringify(response)
                            });
                        },
                        complete: function() {
                            submitting = false;
                        }
                    })
                }
            });

        }

    </script>
</head>
<body>
<div class="title">
    <h2>基本信息</h2>

    <div class="mt20 flex f20">
        <div>单号</div>
        <div th:text="${entrust.invoiceVbillno}">JH202301105205</div>
    </div>

    <div class="mt20 flex f20">
        <div>发货地址</div>
        <div><span class="label label-success pa2">装</span> [[${entrust.deliProName+entrust.deliCityName+entrust.deliAreaName}]]</div>
    </div>

    <div class="mt20 flex f20">
        <div>收货地址</div>
        <div><span class="label label-warning pa2">卸</span> [[${entrust.arriProName+entrust.arriCityName+entrust.arriAreaName}]]</div>
    </div>

    <div class="mt20 flex f20">
        <div>货量</div>
        <div><span th:text="${entrust.goodsName}">光伏板</span>
            <span th:if="${entrust.numCount > 0}" th:text="${entrust.numCount + '件'}"></span>
            <span th:if="${entrust.numCount > 0 && entrust.weightCount > 0}">|</span>
            <span th:if="${entrust.weightCount > 0}" th:text="${entrust.weightCount + '吨'}"></span>
            <span th:if="${entrust.weightCount > 0 && entrust.volumeCount > 0}">|</span>
            <span th:if="${entrust.volumeCount > 0}" th:text="${entrust.volumeCount + 'm³'}"></span></div>
    </div>
</div>

<div th:if="${entrust.vbillstatus == '1' || entrust.vbillstatus == '2'}" class="title mb85">
    <div class="flex f20" style="align-items: center;">
        <div id="imei"></div>
        <div onclick="scan()">
            <svg t="1673398332606" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                 p-id="2709" width="50" height="50">
                <path
                        d="M160 416a32 32 0 0 1-32-32V256a128.1 128.1 0 0 1 128-128h128a32 32 0 1 1 0 64H256a64.1 64.1 0 0 0-64 64v128a32 32 0 0 1-32 32zM384 896H256a128.1 128.1 0 0 1-128-128V640a32 32 0 1 1 64 0v128a64.1 64.1 0 0 0 64 64h128a32 32 0 0 1 0 64zM768 896H640a32 32 0 0 1 0-64h128a64.1 64.1 0 0 0 64-64V640a32 32 0 1 1 64 0v128a128.1 128.1 0 0 1-128 128zM864 416a32 32 0 0 1-32-32V256a64.1 64.1 0 0 0-64-64H640a32 32 0 0 1 0-64h128a128.1 128.1 0 0 1 128 128v128a32 32 0 0 1-32 32zM800 544H224a32 32 0 0 1 0-64H800a32 32 0 0 1 0 64z"
                        fill="#333333" p-id="2710"></path>
            </svg>
        </div>
    </div>
</div>

<div class="fixed">
    <div th:if="${entrust.vbillstatus == '1' || entrust.vbillstatus == '2'}" class="boot">
        <a th:if="${imei == null}" class="btn btn-success" href="javascript:bind()">确认绑定</a>
        <a th:if="${imei != null}" class="btn btn-success" th:text="'已绑定' + ${imei}" href="javascript:bind()"></a>
    </div>
    <div th:if="${entrust.vbillstatus != '1' && entrust.vbillstatus != '2'}" class="boot">
        <a class="btn btn-disabled">单据不可绑卡状态</a>
    </div>
</div>

</body>
</html>