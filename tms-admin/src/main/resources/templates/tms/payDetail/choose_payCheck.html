<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('对账单选择页')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">

                    <div class="col-md-3 col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-3">年份：</label>
                            <div class="col-sm-9">

                                <input name="year" id="year" placeholder="请输入年份" class="form-control valid" type="text"
                                        aria-required="true" readonly>
                                <input name="isNtocc" id="isNtocc" type="hidden" th:value="${isNtocc}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-3">月份：</label>
                            <div class="col-sm-9">

                                <input name="month" id="month" placeholder="请输入月份" class="form-control valid" type="text"
                                        aria-required="true" readonly>

                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-4">
<!--                        <label class="col-sm-6"></label>-->
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm"  onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">

    var prefix = ctx + "payCheckSheet";

    var carrierId = [[${carrierId}]];
    var payDetailIds = [[${payDetailIds}]];
    $(function () {
        var options = {
            url: prefix + "/list?carrierId="+carrierId,
            createUrl: prefix + "/add",
            showToggle:false,
            showColumns:false,
            clickToSelect:true,
            modalName: "承运商对账",
            columns: [{
                radio: true
            },
                {
                    title: '应付对账单Id',
                    field: 'payCheckSheetId',
                    align: 'left',
                    visible: false
                },
                {
                    title: '应付对账单号',
                    field: 'vbillno',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var result = value;
                        if (row.lotG7End == 2) {
                            result += ' <span class="label label-success" style="padding:1px;">G7</span>'
                        } else if (row.lotG7End == 3) {
                            result += ' <span class="label label-warning" title="预支" style="padding:1px 5px;">Y</span>'
                        }
                        return result
                    }
                },{
                    title: '对账单名称',
                    field: 'payCheckSheetName',
                    align: 'left'
                },
                {
                    title: '应付对账单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value) {
                            case 0:
                                return '<span>新建</label>';
                            case 1:
                                return '<span>已确认</label>';
                            case 2:
                                return '<span>部分核销</label>';
                            case 3:
                                return '<span>已核销</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '承运商',
                    field: 'carrName',
                    align: 'left'
                },
                // {
                //     title: '是否符合无车承运人',
                //     align: 'left',
                //     field: 'isNtocc',
                //     formatter: function status(value, row, index) {
                //         if (value === 0) {
                //             return '否';
                //         }
                //         return '是';
                //     }
                // },
                {
                    title: '对账年份',
                    field: 'year',
                    align: 'left'
                },
                {
                    title: '对账月份',
                    field: 'month',
                    align: 'left'
                },
                {
                    title: '总金额',
                    field: 'totalAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
        layui.use('laydate', function(){
            var laydate = layui.laydate;
            laydate.render({
                elem: '#month',
                type: 'month',
                format:"MM"
            });
            laydate.render({
                elem: '#year',
                type: 'year'
            });
        })

    });


    /**
     * 选择一条应付对账单据后的回调方法
     */
    function submitHandler(index, layero, isCash) {
        var rows = $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        /*var isNtocc = $("#isNtocc").val();
        var name = isNtocc==0?"不符合无车承运人":"符合无车承运人";
        if (isNtocc != $.table.selectColumns("isNtocc")) {
            $.modal.alertWarning("请选择"+name+"的对账单");
            return;
        }*/
        var vbillstatus = $.table.selectColumns("vbillstatus");
        if (vbillstatus != 0) {
            $.modal.alertWarning("请选择新建状态的对账单");
            return;
        }


        //TODO 判断是否有耗材
        // var consumbleBack =  $.table.selectColumns("consumbleBack");
        // console.log(consumbleBack)
        // return
        /*var g7End = [[${param.g7End == null ? null : param.g7End[0]}]];
        if (g7End == "2" && $.btTable.bootstrapTable('getSelections')[0].lotG7End != 2) {
            $.modal.alertWarning("G7合规的单据不能加在非G7对账包内");
            return;
        } else if (g7End != "2" && $.btTable.bootstrapTable('getSelections')[0].lotG7End == 2) {
            $.modal.alertWarning("非G7合规的单据不能加在G7对账包内");
            return;
        }*/
        $.ajax({
            type: "post",
            url: ctx + "payDetail/saveChecking",
            data: "payCheckSheetId=" + rows.join() + "&payDetailIds=" + payDetailIds + "&isCash=" + isCash,
            success: function(result) {
                if(result.code == 0){
                    parent.$.table.refresh();
                    parent.$.modal.alertSuccess("已加入对账单");

                    $.modal.close();
                }else{
                    parent.$.modal.alertError(result.msg);
                }

            },
            error: function() {
                parent.$.modal.alertError("加入对账单失败");
            },
        });


    }

</script>

</body>
</html>