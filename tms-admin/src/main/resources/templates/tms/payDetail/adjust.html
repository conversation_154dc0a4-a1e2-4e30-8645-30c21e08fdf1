<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细调整')"/>
</head>

<body>
<div class="form-content">
    <form id="form-payDetail-edit" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">

                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        运单号：</label>
                                    <div class="col-sm-7">
                                        <input name="lotno" id="lotno" class="form-control dis" type="text"
                                               disabled th:value="${payDetail.lotno}">
                                        <input name="lotId" type="hidden" id="lotId" th:value="${payDetail.lotId}">
                                        <input name="isNtocc" type="hidden" id="isNtocc" th:value="${payDetail.isNtocc}">
                                        <input name="payDetailId" type="hidden" id="payDetailId" th:value="${payDetail.payDetailId}">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        应付单据号：</label>
                                    <div class="col-sm-7">
                                        <input name="vbillno" id="vbillno" class="form-control dis" type="text"
                                               disabled th:value="${payDetail.vbillno}">

                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        承运商：</label>
                                    <div class="col-sm-7">
                                        <input name="carrName" id="carrName" type="text"
                                               class="form-control dis" disabled th:value="${payDetail.carrName}" >
                                        <input name="carrierId" type="hidden" id="carrierId" th:value="${payDetail.carrierId}">
                                        <input name="carrCode" type="hidden" id="carrCode" th:value="${payDetail.carrCode}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        司机：</label>
                                    <div class="col-sm-7">
                                        <input name="driverName" id="driverName" class="form-control dis" type="text"
                                               maxlength="30" disabled th:value="${payDetail.driverName}">
                                        <input name="driverMobile" type="hidden" id="driverMobile" th:value="${payDetail.driverMobile}">
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        结算公司：</label>
                                    <div class="col-sm-7">
                                        <input class="form-control dis" type="text"
                                               maxlength="30" disabled th:value="${payDetail.balaCorpName}">
                                        <input name="balaCorp" id="balaCorp" class="form-control dis" type="hidden"
                                               maxlength="30"  th:value="${payDetail.balaCorp}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        提货日期：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control dis"
                                               name="reqDeliDate" id="reqDeliDate" autocomplete="off" disabled
                                               th:value="${#dates.format(payDetail.reqDeliDate, 'yyyy-MM-dd')}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        到货日期：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control dis"
                                               name="reqArriDate" id="reqArriDate" autocomplete="off" disabled
                                               th:value="${#dates.format(payDetail.reqArriDate, 'yyyy-MM-dd')}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        油卡卡号：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control "  maxlength="25"
                                               autocomplete="off" id="oilCardNumber" name="oilCardNumber"
                                               th:value="${payDetail.oilCardNumber}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        收款人：</label>
                                    <div class="col-sm-7">
                                        <input name="recAccount" id="recAccount" class="form-control " type="text"
                                               maxlength="10"  autocomplete="off" th:value="${payDetail.recAccount}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        收款银行：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control " maxlength="25"
                                               autocomplete="off" id="recBank" name="recBank"
                                               th:value="${payDetail.recBank}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        收款卡号：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control "  maxlength="25"
                                               autocomplete="off" name="recCardNo" id="recCardNo"
                                               th:value="${payDetail.recCardNo}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        调整金额：</label>
                                    <div class="col-sm-7">
                                        <input name="transFeeCount" id="transFeeCount" class="form-control" type="text"  oninput="$.numberUtil.onlyNumber(this)" min="0"
                                               required maxlength="10">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-3">调整原因：</label>
                                    <div class="col-sm-12">
                                            <textarea name="adjustMemo" id="adjustMemo" maxlength="250" class="form-control valid"
                                                      rows="3" ></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--基础信息 end-->
                    </div>
                </div>
            </div>

        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFour">地址信息</a>

                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseFour">

                <div class="panel-body">
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabThree" class="custom-tab table">
                            <thead>
                            <tr>

                                <th style="width: 10%;">提货地省</th>
                                <th style="width: 10%;">提货地市</th>
                                <th style="width: 10%;">提货地区</th>
                                <th style="width: 10%;">到货地省</th>
                                <th style="width: 10%;">到货地市</th>
                                <th style="width: 10%;">到货地区</th>

                            </tr>
                            </thead>
                            <tbody id="address">
                            <tr>
                                <td id="deliProvince" th:text="${entrustLot.deliProvinceName}"></td>
                                <td id="deliCity" th:text="${entrustLot.deliCityName}"></td>
                                <td id="deliArea" th:text="${entrustLot.deliAreaName}"></td>
                                <td id="arriProvince" th:text="${entrustLot.arriProvinceName}"></td>
                                <td id="arriCity" th:text="${entrustLot.arriCityName}"></td>
                                <td id="arriArea" th:text="${entrustLot.arriAreaName}"></td>

                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--end-->
                </div>

            </div>

        </div>

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="commit()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<script>
    var prefix = ctx + "payDetail";
    $(function () {

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');

        var options = {};
        $.table.init(options);

    });



    /**
     * 表单提交
     */
    function commit(){
        if ($.validate.form()) {
            $('.dis').removeAttr("disabled");
            // 表单提交
            $.operate.saveTab(prefix + "/insertAdjust", $('#form-payDetail-edit').serialize());
        }
    }

</script>
</body>

</html>