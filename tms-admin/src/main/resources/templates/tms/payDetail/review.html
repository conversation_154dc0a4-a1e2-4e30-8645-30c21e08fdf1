<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付复核')"/>
</head>

<body>
<div class="form-content">
    <form id="form-check" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <!--应付主键-->
            <input  name="payDetailId" type="hidden" th:value="${payDetailId}">
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">退回备注：</label>
                            <div class="col-sm-12">
                                <textarea id="memo" name="checkMemo" class="form-control" type="text" placeholder="如不通过请填写退回备注"
                                          maxlength="250" required="" aria-required="true"></textarea>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var url = ctx + "payDetail/saveReview";


    /**
     * 审核通过
     */
    function submitApprove() {
        var data = $("#form-check").serializeArray();
        data.push({"name": "checkStatus", "value": 1});
        $.operate.save(url, data);
    }
    /**
     * 审核不通过
     */
    function submitBack() {
        var data = $("#form-check").serializeArray();
        data.push({"name": "checkStatus", "value": 2});

        if ($("#memo").val() === '') {
            $.modal.msgWarning("请填写退回备注！");
            return;
        }

        $.operate.save(url, data);
    }




</script>
</body>
</html>