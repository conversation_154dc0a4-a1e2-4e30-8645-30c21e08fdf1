<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细列表')"/>
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <input type="hidden" id="payDetailIds" th:value="${payDetailIds}">
        <div class="row" th:each="entrustAndPayDetailVO:${entrustAndPayDetailVOList}">
            <div class="ibox-content m-xs">
                <div class="row">
                    <div class="col-sm-6">
                        <h2>运单编号 / 状态：<div class="text-navy" th:each="entrustLotStatusEnum:${entrustLotStatusEnumList}"
                                           th:if="${entrustAndPayDetailVO.vbillstatus} == ${entrustLotStatusEnum.value}"
                                           th:text="|${entrustAndPayDetailVO.lot} / ${entrustLotStatusEnum.context}|"></div></h2>
                    </div>
                    <div class="col-sm-6 text-right">
                        <h3 th:text="|承运商：${entrustAndPayDetailVO.carrierName}|"></h3>
                        <p>
                            <span th:utext="|<strong>司机 / 手机：</strong>${entrustAndPayDetailVO.driverName} / ${entrustAndPayDetailVO.driverPhone}|"></span>
                        </p>
                        <p>
                            <span th:utext="|<strong>车辆 / 车长：</strong>${entrustAndPayDetailVO.carNo==null?'':entrustAndPayDetailVO.carNo} / ${entrustAndPayDetailVO.carLenName==null?'':entrustAndPayDetailVO.carLenName}米|"></span>
                        </p>
                        <p>
                            <span  th:if="${entrustAndPayDetailVO.guidingPrice!=null and entrustAndPayDetailVO.guidingPrice!=''}"><strong>指导价：</strong> <strong style="color:#ff8b1b;font-size:18px" th:text="|&yen; ${entrustAndPayDetailVO.guidingPrice}|"></strong></span>
                            <span th:if="${entrustAndPayDetailVO.guidingPrice==null or entrustAndPayDetailVO.guidingPrice==''}"><strong>指导价：</strong> <strong style="color:#ff8b1b;font-size:18px">暂无</strong></span>
                        </p>
                    </div>
                </div>
                <div class="row">
                    <div class="fixed-table-body table-responsive">
                        <table class="table-hover table">
                            <thead>
                            <tr>
                                <th style="width: 10%;">应付单号</th>
                                <th style="width: 8%;">应付单状态</th>
                                <th style="width: 8%;">费用类型</th>
                                <th style="width: 20%;">发票号 / 抬头</th>
                                <th style="width: 15%;">油卡</th>
                                <th style="width: 15%;">银行卡</th>
                                <th style="width: 5%;">已收金额</th>
                                <th style="width: 5%;">未收金额</th>
                                <th style="width: 10%;">总金额</th>
                            </tr>
                            </thead>
                            <tbody>
                                <tr th:each="payDetail :${entrustAndPayDetailVO.payDetailList}">
                                    <td th:text="${payDetail.vbillno}"></td>
                                    <td th:each="payDetailStatus:${payDetailStatusEnumList}" th:if="${payDetail.vbillstatus} == ${payDetailStatus.value}"
                                        th:text="${payDetailStatus.context}"></td>
                                    <td th:each="costTypeFreight:${@dict.getType('cost_type_freight')}" th:if="${payDetail.freeType == '0' and costTypeFreight.dictValue==payDetail.costTypeFreight}"
                                        th:text="${costTypeFreight.dictLabel}"></td>
                                    <td th:if="${payDetail.freeType == '0' and payDetail.costTypeFreight==null}"></td>
                                    <td th:each="costTypeOnWay:${@dict.getType('cost_type_on_way')}" th:if="${payDetail.freeType == '1' and costTypeOnWay.dictValue==payDetail.costTypeOnWay}"
                                        th:text="${costTypeOnWay.dictLabel}"></td>
                                    <td th:text="|${payDetail.checkNo == null?'':payDetail.checkNo} / ${payDetail.checkHead==null?'':payDetail.checkHead}|"></td>
                                    <td th:text="${payDetail.oilCardNumber==null?'':payDetail.oilCardNumber}"> </td>
                                    <td th:text="${payDetail.recCardNo==null?'':payDetail.recCardNo}"></td>
                                    <td th:align="right" th:text="${payDetail.gotAmount}"></td>
                                    <td th:align="right" th:text="${payDetail.ungotAmount}">&yen;1.20</td>
                                    <td th:align="right">
                                        <!--<a class="btn  btn-xs" href="javascript:void(0)" th:data-id="${payDetail.payDetailId}" th:if="${payDetailStatusNew == payDetail.vbillstatus}"-->
                                           <!--onclick="editTransFeeCount(this.getAttribute('data-id'))" title="修改">-->
                                            <!--<i class="fa fa-edit" style="font-size: 15px;"></i>-->
                                        <!--</a>-->
                                        <div th:id="${payDetail.payDetailId}" th:data-pid="${entrustAndPayDetailVO.lot}" style="display:inline" th:text="${payDetail.transFeeCount}"></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-sm-12 text-right invoice-total">
                        <p>
                            <span><strong>已收总金额：</strong> <strong style="color:#ff8b1b;font-size:18px" th:text="|&yen; ${entrustAndPayDetailVO.payDetailGotAmountTotal}|"></strong></span>&nbsp;&nbsp;
                            <span><strong>未收总金额：</strong> <strong style="color:#ff8b1b;font-size:18px" th:text="|&yen; ${entrustAndPayDetailVO.payDetailUngotAmountTotal}|"></strong></span>&nbsp;&nbsp;
                            <span><strong>总金额：</strong> <strong th:id="${entrustAndPayDetailVO.lot}" style="color:#ff8b1b;font-size:18px" th:text="|&yen; ${entrustAndPayDetailVO.payDetailPriceTotal}|"></strong></span>
                        </p>
                    </div>
                </div>
                <div class="row">
                    <div class="fixed-table-body table-responsive">
                        <table class="table-hover table">
                            <thead>
                            <tr>
                                <th style="width: 10%;">委托单号</th>
                                <th style="width: 8%;">委托单状态</th>
                                <th style="width: 10%;">客户简称</th>
                                <th style="width: 5%;">是否回单</th>
                                <th style="width: 8%;">回单数</th>
                                <th style="width: 8%;">回单人</th>
                                <th style="width: 8%;">回单时间</th>
                                <th style="width: 15%;">回单备注</th>
                                <th style="width: 8%;">回单接收人</th>
                                <th style="width: 8%;">回单接收时间</th>
                                <th style="width: 5%;">回单附件</th>
                            </tr>
                            </thead>
                            <tbody>
                                <tr th:each="entrust :${entrustAndPayDetailVO.entrustList}">
                                    <td th:text="${entrust.vbillno}"></td>
                                    <td th:each="entrustStatus:${entrustStatusList}" th:if="${entrust.vbillstatus} == ${entrustStatus.value}"
                                        th:text="${entrustStatus.context}"></td>
                                    <td th:text="${entrust.custAbbr}"></td>
                                    <td th:text="${entrust.ifReceipt=='0'?'否':'是'}"></td>
                                    <td th:align="right" th:text="${entrust.receiptNum}"></td>
                                    <td th:text="${entrust.receiptMan}"></td>
                                    <td th:text="${#dates.format(entrust.receiptDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                    <td th:text="${entrust.receiptMemo}"></td>
                                    <td th:text="${entrust.receiptBookMan}"></td>
                                    <td th:text="${#dates.format(entrust.receiptBookTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                    <td th:if="${entrust.receiptAppendixId!=null and entrust.receiptAppendixId!=''}">
                                        <a class="btn btn-xs " href="javascript:void(0)" title="附件查看" th:data-id="${entrust.receiptAppendixId}" onclick="openDetailTab(this.getAttribute('data-id'))">
                                        <i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>
                                    </td>
                                    <td th:if="${entrust.receiptAppendixId==null or entrust.receiptAppendixId==''}"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="row">
                    <div class="fixed-table-body table-responsive">
                        <table class="table-hover table">
                            <thead>
                            <tr>
                                <th style="width: 15%;">货品名称</th>
                                <th style="width: 15%;">货品类型</th>
                                <th style="width: 10%;">重量(吨)</th>
                                <th style="width: 10%;">件数(m3)</th>
                                <th style="width: 10%;">体积</th>
                                <th style="width: 10%;">回单重量(吨)</th>
                                <th style="width: 10%;">回单件数</th>
                                <th style="width: 10%;">回单体积(m3)</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="entPackGoods :${entrustAndPayDetailVO.entPackGoodsList}">
                                <td th:text="${entPackGoods.goodsName}"></td>
                                <td th:text="${entPackGoods.goodsTypeName}"></td>
                                <td th:align="right" th:text="${entPackGoods.weight}"></td>
                                <td th:align="right" th:text="${entPackGoods.num}"></td>
                                <td th:align="right" th:text="${entPackGoods.volume}"></td>
                                <td th:align="right" th:text="${entPackGoods.receiptWeight}"></td>
                                <td th:align="right" th:text="${entPackGoods.receiptNum}"></td>
                                <td th:align="right" th:text="${entPackGoods.receiptVolume}"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-offset-5 col-sm-10">
            <button shiro:hasAnyPermissions="finance:payDetail:affirm,trace:payDetail:affirm" type="button" class="btn btn-sm btn-primary" onclick="submit()"><i class="fa fa-check"></i>全部费用确认</button>&nbsp;
            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
            </button>
        </div>
    </div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">

    /**
     * 查看回单附件
     *
     * @param tid
     */
    function openDetailTab(tid) {
        layer.open({
            type: 2,
            area: ['70%', '90%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "图片详情",
            content: ctx + "payDetail/receiptFileDetail/" + tid,
            btn: ['关闭'],
            shadeClose: true,            // 弹层外区域关闭
            cancel: function(index) {
                return true;
            }
        });
    }

    /**
     * 修改应付单总金额
     *
     * @param id 应付单id
     */
    function editTransFeeCount(id) {
        layer.open({
            type: 2,
            area: ['30%', '30%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "修改应付总金额",
            content: ctx + "payDetail/editTransFeeCount",
            btn: ['确定','关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero) {
                //获取页面值
                var $iframeDom=layero.find("iframe").eq(0).contents();
                var transFeeCount = $iframeDom.find('#transFeeCount').val();
                var form = $iframeDom.find('#form-edit-trans-feeCount');

                if (form.validate().form()) {
                    //关闭弹窗
                    layer.close(index);

                    $.ajax({
                        url: ctx + "payDetail/editTransFeeCount/",
                        type: "post",
                        dataType: "json",
                        data: {id: id, transFeeCount: transFeeCount},
                        beforeSend: function () {
                            $.modal.loading("正在处理中，请稍后...");
                            $.modal.disable();
                        },
                        success: function (result) {
                            if (result.code == web_status.SUCCESS) {
                                //修改总金额值
                                $("#" + id).html(transFeeCount);

                                //总金额合计的id
                                var pid = $("#" + id).data("pid");

                                //获取所有总金额，并相加
                                var feeList = $("div[data-pid='" + pid + "']");
                                var count = 0;
                                feeList.each(function () {
                                    if ($(this).html() != "") {
                                        count = parseFloat(decimal(count + parseFloat($(this).html()), 5));
                                    }
                                });
                                $("#" + pid).html(count);

                                $.modal.msgSuccess(result.msg);
                            } else {
                                $.modal.alertError(result.msg);
                            }
                            $.modal.closeLoading();
                        }
                    });
                }
            },
            cancel: function(index) {
                return true;
            }
        });
    };


    /**
     * 提交
     */
    function submit() {
        var payDetailIds = $("#payDetailIds").val();
        $.modal.confirm("是否确认？", function () {
            $.operate.saveTab(ctx + "payDetail/affirm", {"payDetailIds": payDetailIds});
        });
    }
    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }

</script>
</body>
</html>