<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('生成应付对账')"/>
</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
        color: #808080;
    }
    .flex_left{
        width: 100px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .tc{
        text-align: center;
        width: 100%;
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .form-control-tool {
        padding: 2px 4px;
        font-size: 13px;
        height: 30px;
        line-height: 30px;
        vertical-align: middle;
        color: inherit;
        background: none rgb(255, 255, 255);
        border: 1px solid rgb(229, 230, 231);
        border-image: initial;
        border-radius: 1px;
        transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
    }
    .hoverHighlight:hover {
        background-color: #eee;
    }
</style>
<body>
<div class="form-content">
    <form style="display: none" id="empty"><!--$.table默认用第一个form作为查询参数--></form>
    <form id="form-payCheck-add" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body" style="padding: 5px 10px 10px;">
                        <!--基础信息 begin-->
                        <input type="hidden" name="payDetailIds" id="payDetailIds" th:value="${payDetailIds}">
                        <input type="hidden" name="lotG7End" id="lotG7End" th:value="${lotG7End}">
                        <input type="hidden" name="handVerification" id="handVerification" value="0">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">承运商名称：</label>
                                    <div class="flex_right">
                                        <input type="text" class="form-control" name="carrName" id="carrName"
                                                th:value="${payDetail.carrName}" disabled>
                                        <input name="carrierId" type="hidden" id="carrierId"
                                               th:value="${payDetail.carrierId}">
                                        <input name="carrCode" type="hidden" id="carrCode"
                                               th:value="${payDetail.carrCode}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">* </span>对账单名称：</label>
                                    <div class="flex_right">
                                        <input type="text" class="form-control" name="payCheckSheetName"
                                               id="payCheckSheetName" required autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">* </span>对账年月：</label>
                                    <div class="flex_right">
                                        <input type="text" class="form-control" name="yearMonth" id="yearMonth" required
                                               autocomplete="off" readonly>
                                        <input type="hidden" name="year" id="year">
                                        <input type="hidden" name="month" id="month">
                                    </div>
                                </div>
                            </div>
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="form-group">-->
<!--                                    <label class="col-sm-5" style="color: red; ">对账月份：</label>-->
<!--                                    <div class="col-sm-7 ">-->
<!--                                        <input type="text" class="form-control" name="month" id="month" required-->
<!--                                               autocomplete="off" readonly>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">总金额(元)：</label>
                                    <div class="flex_right">
                                        <input type="text" class="form-control" id="totalAmount" name="totalAmount" th:value="${totalAmount}"  disabled>
                                    </div>
                                </div>
                            </div>
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="form-group">-->
<!--                                    <label class="col-sm-5">未付金额(元)：</label>-->
<!--                                    <div class="col-sm-7">-->
<!--                                        <input  class="form-control" id="ungotAmount" name="ungotAmount"-->
<!--                                                disabled th:value="${ungotAmount}">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="form-group">-->
<!--                                    <label class="col-sm-5">结算公司：</label>-->
<!--                                    <div class="col-sm-7">-->
<!--                                        <input  class="form-control"  id="balaCorpName" name="balaCorpName"-->
<!--                                                disabled>-->
<!--                                        <input  class="form-control"  id="balaCorp" name="balaCorp"-->
<!--                                                th:value="${payDetail.balaCorp}"  type="hidden">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

                        </div>
<!--                        <div class="row">-->

<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="form-group">-->
<!--                                    <label class="col-sm-5">是否手动核销：</label>-->
<!--                                    <div class="col-sm-7">-->
<!--                                        <select name="handVerification" id="handVerification"  class="form-control valid"-->
<!--                                                th:with="type=${@dict.getType('hand_verification')}">-->
<!--                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"-->
<!--                                                    th:value="${dict.dictValue}"></option>-->
<!--                                        </select>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="flex">
                                    <label class="flex_left">备注：</label>
                                    <div class="flex_right">
                                            <textarea name="memo" id="memo" maxlength="500" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--基础信息 end-->
                    </div>
                </div>
            </div>

            <div  class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFive">应付明细</a>
                    </h4>
                </div>
                <div id="collapseFive" class="panel-collapse collapse in">
                    <div class="panel-body">

                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabThree" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 35px;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow()" title="新增行">+</a></th>
                                    <th style="width: 170px;text-align:center">应付单据号</th>
                                    <th style="width: 145px;text-align:center">运单号</th>
                                    <th style="width: 45px">归属</th>
                                    <th style="width: 90px;text-align:center">是否存在耗材</th>
                                    <th style="width: 80px;text-align:center">提货日期</th>
                                    <th style="width: auto;text-align:center">提货地址</th>
                                    <th style="width: auto;text-align:center">到货地址</th>
<!--                                    <th style="width: 12%;text-align:center">承运商名称</th>-->
                                    <th style="width: 70px;text-align:center">费用类型</th>
<!--                                    <th style="width: 8%;text-align:center">司机名称</th>-->
<!--                                    <th style="width: 10%;text-align:center">司机电话</th>-->
                                    <th style="width: 80px;text-align:center">总金额</th>
<!--                                    <th style="width: 8%;text-align:center">已收金额</th>-->
<!--                                    <th style="width: 8%;text-align:center">未收金额</th>-->
                                    <th style="width: 80px;text-align:center">已付金额</th>
                                    <th style="width: 80px;text-align:center">未付金额</th>

                                </tr>
                                </thead>
                                <tbody id="body1">
                                <tr th:each="mapS,status:${payDetailIdsList}" class="hoverHighlight">
                                    <td><a class="close-link del-alink" style="background: #fd8481;border-radius: 50%" th:onclick="removeRow(this,'[(${mapS.payDetailId})]',[[${mapS.transFeeCount}]],[[${mapS.gotAmount}]])" title="删除选择行">x</a></td>

                                    <td>
                                        <span th:text="${mapS.vbillno}"></span>
                                        <span g7flag th:lot-g7-end="${mapS.lotG7End}" th:lot-g7-syn="${mapS.lotG7Syn}" th:lot-g7-msg="${mapS.lotG7Msg}"></span>
                                    </td>
                                    <td>
                                        <div class="input-group tc" th:text="${mapS.lotno}"></div>
                                    </td>
                                    <td th:bala-corp="${mapS.balaCorp}">[[${mapS.balaCorp}]]</td>
                                    <td>
                                        <div class="input-group tc" th:if="${mapS.consumbleBack == 1}" th:text="否"></div>
                                        <div class="input-group tc" th:unless="${mapS.consumbleBack == 1}" th:text="是"></div>
                                    </td>
                                    <td th:text="${#dates.format(mapS.reqDeliDate,'yyyy-MM-dd')}"></td>
                                    <td th:text="${mapS.deliAddr}"></td>
                                    <td th:text="${mapS.arriAddr}"></td>
<!--                                    <td>-->
<!--                                        <div class="input-group" th:text="${mapS.carrName}"></div>-->
<!--                                    </td>-->
                                    <td th:each="costTypeFreight:${@dict.getType('cost_type_freight')}"
                                        th:if="${mapS.freeType == '0' and costTypeFreight.dictValue==mapS.costTypeFreight}"
                                        th:text="${costTypeFreight.dictLabel}"></td>
                                    <td th:each="costTypeOnWay:${@dict.getType('cost_type_on_way')}"
                                        th:if="${payDetail.freeType == '1' and costTypeOnWay.dictValue==mapS.costTypeOnWay}"
                                        th:text="${costTypeOnWay.dictLabel}"></td>
<!--                                    <td>-->
<!--                                        <div class="input-group" th:text="${mapS.driverName}"></div>-->
<!--                                    </td>-->
<!--                                    <td>-->
<!--                                        <div class="input-group" th:text="${mapS.driverMobile}"></div>-->
<!--                                    </td>-->
                                    <td th:align="right" th:text="'¥'+${#numbers.formatDecimal(mapS.transFeeCount,1,'COMMA',2,'POINT')}"></td>
                                    <td th:align="right" th:text="'¥'+${#numbers.formatDecimal(mapS.gotAmount,1,'COMMA',2,'POINT')}"></td>
                                    <td th:align="right" th:text="'¥'+${#numbers.formatDecimal(mapS.ungotAmount,1,'COMMA',2,'POINT')}"></td>
                                </tr>
                                </tbody>
                                <tfoot>
                                <tr style="background: #FFFCD3;text-align: center">
                                    <td colspan="2">合计</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td class="fw" id="td_totalAmount" th:text="'¥'+${#numbers.formatDecimal(totalAmount,1,'COMMA',2,'POINT')}"></td>
                                    <td class="fw" id="td_gotAmount" th:text="'¥'+${#numbers.formatDecimal(gotAmount,1,'COMMA',2,'POINT')}">xx</td>
                                    <td class="fw" id="td_ungotAmount" th:text="'¥'+${#numbers.formatDecimal(ungotAmount,1,'COMMA',2,'POINT')}">xx</td>
                                </tr>
                                </tfoot>

                            </table>
                        </div>

                    </div>
                </div>

            </div>


        </div>
        <input type="hidden" class="form-control" id="ungotAmount" name="ungotAmount" th:value="${ungotAmount}">
        <input type="hidden" class="form-control" id="balaCorpName" name="balaCorpName">
        <input type="hidden" class="form-control" id="balaCorp" name="balaCorp">
    </form>
</div>
<div id="modal-body" style="padding: 0 10px;display: none;">
    <div class="btn-group-sm" id="toolbar" role="group">
        <form id="form2">
            <input name="reqDeliDateRange" id="reqDeliDate" class="form-control-tool" placeholder="要求提货日期" readonly style="width:180px">
            <input name="custAbbr" class="form-control-tool" placeholder="客户简称">
            <input name="invoiceVbillno" class="form-control-tool" placeholder="发货单号">
            <input name="carno" class="form-control-tool" placeholder="车牌号">
            <a class="btn btn-warning btn-sm" onclick="$.table.search('form2')"><i class="fa fa-download"></i> 搜索</a>
        </form>
    </div>
    <div class="table-striped">
        <table class="text-nowrap" id="bootstrap-table" data-advanced-search="false"></table>
    </div>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="commit()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">
    var totalAmount = new BigNumber([[${totalAmount}]]);
    var gotAmount = new BigNumber([[${gotAmount}]]);
    var ungotAmount = new BigNumber([[${ungotAmount}]]);
    var lotG7End = [[${lotG7End}]];
    var payDetailStatusEnum = [[${payDetailStatusEnum}]];
    var payDetailStatusMap = [[${payDetailStatusMap}]];
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    // 结算公司
    var corps = [[${@dict.getType('bala_corp')}]];
    var inited = false;

    var payDetailIdsList = [[${payDetailIdsList}]]
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');


        //for ( var i = 0; i < corps.length; i++) {
        //    if (corps[i].dictValue === $("#balaCorp").val()) {
        //        $("#balaCorpName").val(corps[i].dictLabel);
        //    }
        //}

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            laydate.render({
                elem: '#yearMonth',
                type: 'month',
                format:"yyyy-MM"
            });
            /*laydate.render({
                elem: '#month',
                type: 'month',
                format:"MM"
            });
            laydate.render({
                elem: '#year',
                type: 'year'
            });*/
            laydate.render({
                elem: '#reqDeliDate', //指定元素
                //format: 'yyyy-MM-dd',
                //isInitValue: false,
                type: 'date',
                range: true,
                done: function (value, date, endDate) {
                }
            });
        })

        $('[g7flag]').each(function () {
            renderG7FlagSpan(this);
        })
    });

    function renderG7FlagSpan(span) {
        let _lotG7End = $(span).attr('lot-g7-end') || null;
        let _lotG7Syn = $(span).attr('lot-g7-syn') || null;
        let _lotG7Msg = $(span).attr('lot-g7-msg') || null;
        if (_lotG7Syn != null) {
            $(span).text("G7")
            $(span).css("padding", "1px");
            if (_lotG7Syn == 2) {
                $(span).addClass("label label-success");
                if (_lotG7End == 2) {
                    $(span).after('<span style="border: 1px #ddd solid;" title="已到货">达</span>')
                } else {
                    $(span).after('<span style="border: 1px #ddd solid;" title="未到货">未</span>')
                }
            } else {
                $(span).addClass("label label-danger");
                $(span).attr('title', _lotG7Msg)
            }
        }
    }

    /**
     * 提交的方法
     */
    function commit() {
        if ($("#payDetailIds").val() == "") {
            $.modal.msgError("生成对账单至少需要一条应付明细")
            return;
        }
        //TODO 验证是否存在耗材费用的应付明细
        var vbillno = [];
        $.each(payDetailIdsList,function (index) {
            if(payDetailIdsList[index].consumbleBack == 0) {
                vbillno.push(payDetailIdsList[index].vbillno)
            }
        })
        if(vbillno.length > 0) {
            $.modal.alertWarning(vbillno + "应付单据存在耗材费用,请核实耗材是否归还!")
        }
        console.log(vbillno)

        let chk = checkLotG7End2();
        if (!chk) {

            return
        }

        let tmpBala = []
        $("[bala-corp]").each(function(){
            let corp = $(this).attr('bala-corp');
            if (tmpBala.indexOf(corp) < 0) {
                tmpBala.push(corp)
            }
        })
        if (tmpBala.length != 1) {
            $.modal.msgError("不同归属的应付单据不能一起打包");
            return
        }
        $('#balaCorp').val(tmpBala[0])
        for ( var i = 0; i < corps.length; i++) {
            if (corps[i].dictValue === $("#balaCorp").val()) {
                $("#balaCorpName").val(corps[i].dictLabel);
            }
        }
        if ($.validate.form("form-payCheck-add")){
            var yearMonth = $("#yearMonth").val().split("-");
            var year = yearMonth[0];
            var month = yearMonth[1];
            $("#year").val(year);
            $("#month").val(month);
            //验证对账单月份不能超过当前月份
            var curDate = new Date();
            if(year <= curDate.getFullYear()){
                if(year == curDate.getFullYear() && month > curDate.getMonth()+1){
                    $.modal.alertWarning("对账年月不能超过当前年月")
                    return false;
                }
            }else{
                $.modal.alertWarning("对账年月不能超过当前年月")
                return false;
            }
            $(":disabled").attr("disabled", false).addClass("tmp");
            var datax = $('#form-payCheck-add').serialize()
            $(".tmp").attr("disabled", true).removeClass("tmp")
            $.ajax({
                type: "post",
                dataType: "json",
                data:  $('#form-payCheck-add').serialize(),
                url:  ctx +"payDetail/checkPayCheckSheetByCarrierId",
                success: function(result) {
                    if (result === 0) {
                        $.operate.saveTab(ctx + "payDetail/addPayCheck", datax);
                    } else {
                        var title = year+"年"+month+"月对账单已存在，是否需要创建新的对账单?";
                        $.modal.confirm(title, function() {
                            $.operate.saveTab(ctx + "payDetail/addPayCheck", datax);
                        });
                    }
                }
            });

        }

    }

    function checkLotG7End2() {
        if (lotG7End == 2) { // 对账单G7属性
            let result = true;
            $('[g7flag]').each(function(){
                if ($(this).attr('lot-g7-end') != 2) {
                    $.modal.msgError("该对账单为G7对账单，只能添加蓝色G7且已到货应付明细");
                    result = false;
                    return false;
                }
            })
            return result;
        } else {

        }
        return true;
    }

    function insertRow() {
        layer.open({
            type: 1,
            title: '选择该承运商其它应付单',
            area: ['90%', '640px'],
            btn: ['确认选择', '取消'],
            zIndex: 1000,
            maxmin: true,
            content: $('#modal-body'), //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
            yes: function(index, layero){
                var rows = $.table.selectColumns("payDetailId");
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                var bootstrapTable = $.btTable.bootstrapTable('getSelections');
                //var  isNtocc = bootstrapTable[0]["isNtocc"];
                //var lotG7End = bootstrapTable[0]["lotG7End"];
                var consumableBackArray = [];
                for (var i = 0; i < bootstrapTable.length; i++) {console.log(bootstrapTable[i])
                    /*if (bootstrapTable[i]["isNtocc"] !== isNtocc ) {
                        $.modal.alertWarning("请选择同为符合或不符合无车承运人的应付单");
                        return;
                    }*/
                    let rowG7End = bootstrapTable[i]["lotG7End"] || '';
                    if (rowG7End !== lotG7End ) {
                        $.modal.alertWarning("G7与非G7(且区分到货成功与否)的应付单无法加入相同对账包");
                        return;
                    }
                    if (bootstrapTable[i]["vbillstatus"] !== 1 && bootstrapTable[i]["vbillstatus"] !== 0 ) {
                        $.modal.alertWarning("生成对账单的应付单据只能为新建或已确认状态");
                        return;
                    }
                    if (bootstrapTable[i]["freeType"] === '0') {
                        if (bootstrapTable[i]["costTypeFreight"] == '1' || bootstrapTable[i]["costTypeFreight"] == '3') {
                            $.modal.alertWarning("预/到付油卡类型无法生成对账单");
                            return;
                        }
                    }
                    //TODO  未归还耗材的应付明细单 排除 console.log(bootstrapTable[i]["consumbleBack"])
                    if (bootstrapTable[i]["consumbleBack"] === 0) {
                        consumableBackArray.push(bootstrapTable[i]["vbillno"])
                    }
                    if(consumableBackArray.length > 0) {
                        $.modal.alertWarning(consumableBackArray + "应付明细单存在耗材未归还,请先确认耗材归还情况!");
                        return;
                    }
                }
                otherPaydetail(rows, function(){
                    if (rows.length > 0) {
                        var ids = $("#payDetailIds").val().split(",");//已存在于应付明细列表中的id
                        $.ajax({
                            url: ctx + 'payDetail/listForPayCheckSheet',
                            data: {ids: rows.join(',')},
                            type: 'post',
                            success: function (r) {
                                if (r.code != 0) {
                                    $.modal.alertError(result.msg);
                                    return false;
                                }
                                let list = r.data;
                                for (let i = 0; i < list.length; i++) {
                                    var tmp = []
                                    // 1、生成列表html
                                    tmp.push('<tr>');
                                    tmp.push('<td><a class="close-link del-alink" style="background: #fd8481;border-radius: 50%" onclick="removeRow(this,\'',list[i].payDetailId,'\',',
                                        list[i].transFeeCount,',',list[i].gotAmount,')" title="删除选择行">x</a></td>')
                                    tmp.push('<td><span>',list[i].vbillno,'</span>')
                                    tmp.push(' <span g7flag lot-g7-end="',list[i].lotG7End,'" lot-g7-syn="',list[i].lotG7Syn,'" lot-g7-msg="',list[i].lotG7Msg,'"></span>')
                                    tmp.push('</td>')
                                    tmp.push('<td><div class="input-group tc">',list[i].lotno,'</div></td>')
                                    tmp.push('<td bala-corp="',list[i].balaCorp,'"><div class="input-group tc">',list[i].balaCorp,'</div></td>')


                                    //TODO  生成对账单 新增行  前面insertRow已经做了判断,判断了该应付明细是否归还了耗材,直接填否就可以
                                    tmp.push('<td>否</td>')
                                    tmp.push('<td>',list[i].reqDeliDate,'</td>');
                                    tmp.push('<td>',list[i].deliAddr,'</td>')
                                    tmp.push('<td>',list[i].arriAddr,'</td>')
                                    tmp.push('<td>')
                                    if (list[i].freeType == '0') {
                                        for (var j = 0; j < costTypeFreight.length; j++) {
                                            if (list[i].costTypeFreight == costTypeFreight[j].dictValue) {
                                                tmp.push(costTypeFreight[j].dictLabel)
                                                break;
                                            }
                                        }
                                    } else if (list[i].freeType == '1') {
                                        for (var j = 0; j < costTypeOnWay.length; j++) {
                                            if (list[i].costTypeOnWay == costTypeOnWay[j].dictValue) {
                                                tmp.push(costTypeOnWay[j].dictLabel)
                                                break;
                                            }
                                        }
                                    }
                                    tmp.push('</td>')
                                    tmp.push('<td>',list[i].transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}),'</td>');
                                    tmp.push('<td>',list[i].gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}),'</td>');
                                    let cur_ungotAmount = new BigNumber(list[i].transFeeCount).minus(new BigNumber(list[i].gotAmount));
                                    tmp.push('<td>',cur_ungotAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}),'</td>');
                                    tmp.push('</tr>');
                                    // 2、合计各金额
                                    totalAmount = totalAmount.plus(new BigNumber(list[i].transFeeCount));
                                    gotAmount = gotAmount.plus(new BigNumber(list[i].gotAmount))
                                    ungotAmount = totalAmount.minus(gotAmount);
                                    // 3、重新生成payDetailIds
                                    ids.push(list[i].payDetailId)
                                    $("#body1").append(tmp.join(''));
                                    renderG7FlagSpan($("#body1").find("tr:last").find('[g7flag]')[0])
                                }
                                $("#td_totalAmount").text(totalAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                                $("#totalAmount").val(totalAmount.toNumber())
                                $("#td_gotAmount").text(gotAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                                $("#td_ungotAmount").text(ungotAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                                $("#ungotAmount").val(ungotAmount.toNumber());
                                $("#payDetailIds").val(ids.join(','));
                                layer.close(index)
                            }
                        })
                    } else {
                        $.modal.alertWarning("本次选中的应付明细均已添加，请重新选择")
                    }
                })
            }
        });
        if (!inited) {
            var options = {
                url: ctx +"payDetail/forAddPayCheckSheet?carrierId=[(${payDetail.carrierId})]&status=0,1",
                showToggle: false,
                showColumns: false,
                fixedColumns: true,
                rememberSelected: false,
                fixedNumber: 3,
                clickToSelect:true,
                showSearch: false,
                uniqueId: 'payDetailId',
                columns: [
                    {
                        checkbox: true,
                    },
                    {
                        title: '应付单号',
                        field: 'vbillno',
                        align: 'left',
                        formatter: function (value, row, index) {
                            var result = value;
                            if (row.lotG7End == 2) {
                                result += ' <span class="label label-success" style="padding:1px;">G7</span>'
                            } else if (row.lotG7Syn != null) {
                                result += ' <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true" title="'
                                if (row.lotG7Syn == 0) {
                                    result += '等待G7审验'
                                } else if (row.lotG7Syn == 1) {
                                    result += row.lotG7Msg
                                } else if (row.lotG7Syn == 2) {
                                    if (row.lotG7Start == null || row.lotG7Start == 0) {
                                        result += '等待推送【发车】'
                                    } else if (row.lotG7Start == 1) {
                                        result += '【发车】推送失败'
                                    } else if (row.lotG7End == null || row.lotG7End == 0) {
                                        result += '等待推送【到达】'
                                    } else if (row.lotG7End == 1) {
                                        result += '【到达】推送失败'
                                    }
                                } else if (row.lotG7Syn == 7) {
                                    result += '运单已作废'
                                }
                                result += '">G7</span>'
                            }
                            return result
                        }
                    },
                    {
                        title: '应付单状态',
                        field: 'vbillstatus',
                        align: 'left',
                        formatter: function status(row,value) {
                            var context = '';
                            payDetailStatusEnum.forEach(function (v) {
                                if (v.value == value.vbillstatus) {
                                    if (value.vbillstatus == payDetailStatusMap.NEW) {
                                        //新建
                                        context = '<span class="label label-primary">'+v.context+'</span>';
                                    }else if (value.vbillstatus == payDetailStatusMap.AFFIRM) {
                                        //已确认
                                        context = '<span class="label label-warning">'+v.context+'</span>';
                                    }else if (value.vbillstatus == payDetailStatusMap.RECONCILED) {
                                        //已对账
                                        context = '<span class="label label-coral">'+v.context+'</span>';
                                    }else if (value.vbillstatus == payDetailStatusMap.PARTIAL_WRITE_OFF) {
                                        //部分核销
                                        context = '<span class="label label-info">'+v.context+'</span>';
                                    } else if(value.vbillstatus == payDetailStatusMap.HAS_BEEN_WRITTEN_OFF){
                                        //已核销
                                        context = '<span class="label label-success">' + v.context + '</span>';
                                    } else if(value.vbillstatus == payDetailStatusMap.APPLY){
                                        //已申请
                                        context = '<span class="label label-success">' + v.context + '</span>';
                                    } else if(value.vbillstatus == payDetailStatusMap.PARTIAL_WRITE_ing){
                                        //核销中
                                        context = '<span class="label label-info">' + v.context + '</span>';
                                    } else if(value.vbillstatus == payDetailStatusMap.CHECK_WRITE_ING){
                                        //审核中
                                        context = '<span class="label label-default">' + v.context + '</span>';
                                    }else{
                                        context = v.context;
                                    }
                                    return false;
                                }
                            });
                            return context;
                        }
                    },
                    {
                        title: '要求提货日期',
                        align: 'left',
                        field: 'lotReqDeliDate',
                        formatter: function status(value, row, index) {
                            if(value == "" || value == null || value == 'undefined'){
                                return "";
                            }
                            return value.substring(0,10);
                        }
                    },
                    {
                        title: '发货单号',
                        align: 'left',
                        field: 'invoiceVbillno'
                    },
                    {
                        title: '货品',
                        align: 'left',
                        field: 'goodsName'
                    },
                    {
                        title: '运营组',
                        align: 'left',
                        field: 'salesDeptName'
                    },
                    {
                        title: '客户简称',
                        align: 'left',
                        field: 'custAbbr'
                    },
                    {
                        title: '提货|到货省市区',
                        field: 'deliProName',
                        align: 'left',
                        formatter: function status(value, row, index) {
                            if(row.deliAddr == null || row.deliAddr == ""){
                                row.deliAddr = '';
                            }
                            if(row.arriAddr == null || row.arriAddr.arriAddr == ""){
                                row.arriAddr = ''
                            }
                            if(row.deliAddr == "" && row.arriAddr == ""){
                                return "";
                            }else{
                                return row.deliAddr+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriAddr;
                            }
                        }
                    },
                    {
                        title: '车牌号',
                        align: 'left',
                        field: 'carno'
                    },
                    {
                        title: '司机',
                        align: 'left',
                        field: 'driverName'
                    },
                    {
                        title: '司机电话',
                        align: 'left',
                        field: 'driverMobile'
                    },
                    {
                        title: '总金额',
                        align: 'right',
                        field: 'transFeeCount',
                        formatter: function (value, row, index) {
                            if (value === null) {
                                return ;
                            }
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }

                    },
                    {
                        title: '调整额',
                        align: 'right',
                        field: 'taxAmount',
                        formatter: function (value, row, index) {
                            if (value === null) {
                                return ;
                            }
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }

                    },
                    {
                        title: '已付金额',
                        align: 'right',
                        field: 'gotAmount',
                        formatter: function (value, row, index) {
                            if (value === null) {
                                return ;
                            }
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }
                    },
                    {
                        title: '费用类型',
                        field: 'freeType',
                        align: 'left',
                        formatter: function status(value, row, index) {
                            switch(value - 0) {
                                case 0:
                                    return '<span>运费</label>';
                                case 1:
                                    return '<span>在途费用</label>';
                                case 2:
                                    return '<span>调整费用</label>';
                                default:
                                    break;
                            }
                        }

                    },
                    {
                        title: '付款类型',
                        field: 'costTypeOnWay',
                        align: 'left',
                        formatter: function status(value, row, index) {
                            if (row.freeType === '0'){
                                return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                            }else if (row.freeType === '2')  {
                                return '调整费'
                            }
                            return $.table.selectDictLabel(costTypeOnWay, value);
                        }

                    },
                    {
                        title: '创建人',
                        align: 'left',
                        field: 'regUserId'
                    },
                    {
                        title: '创建时间',
                        align: 'left',
                        field: 'regDate'
                    },
                    {
                        title: '是否为调整单',
                        align: 'left',
                        field: 'isAdjust',
                        formatter: function (value, row, index) {
                            if (value === 0) {
                                return '否';
                            }
                            return '是';
                        }
                    },
                    {
                        title: '调整原因',
                        align: 'left',
                        field: 'adjustMemo'
                    },
                    {
                        title: '申请人',
                        align: 'left',
                        field: 'applyUser'
                    },
                    {
                        title: '申请时间',
                        align: 'left',
                        field: 'applyTime'
                    },
                    {
                        title: '结算方式',
                        align: 'left',
                        field: 'carrBalaType',
                        formatter: function status(value) {
                            if(value == 1 ){
                                return "单笔付款";
                            }else if(value == 2){
                                return "月度付款";
                            }
                        }
                    },
                    {
                        title: '备注',
                        align: 'left',
                        field: 'memo'
                    },
                    {
                        title: '运单号',
                        align: 'left',
                        field: 'lotno'
                    },
                    {
                        title: '提货日期',
                        align: 'left',
                        field: 'reqDeliDate',
                        formatter: function status(value, row, index) {
                            if(value == "" || value == null || value == 'undefined'){
                                return "";
                            }
                            return value.substring(0,10);
                        }
                    },
                    {
                        title: '到货日期',
                        align: 'left',
                        field: 'reqArriDate',
                        formatter: function status(value, row, index) {
                            if(value == "" || value == null || value == 'undefined'){
                                return "";
                            }
                            return value.substring(0,10);
                        }
                    },
                ]
            };
            $.table.init(options);
            inited = true;
        } else {
            // 去掉已打勾
            $('#bootstrap-table').bootstrapTable('uncheckBy', {field: 'payDetailId', values: $.table.selectColumns("payDetailId")});
        }
    }

    /**
     *
     * @param rows 弹出层勾选的应付明细
     * @param callback
     */
    function otherPaydetail(rows, callback) {
        var ids = $("#payDetailIds").val().split(",");//已存在于应付明细列表中的id
        $.ajax({
            url: ctx +"payDetailList/other-paydetail",
            data: {'ids': rows.join()},
            type: 'post',
            dataType: 'json',
            success: function (result) {//[{VBILLNO,LOTNO,PAY_DETAIL_ID,OTHER_IDS}]
                if (result.code != 0) {
                    $.modal.alertError(result.msg);
                    return false;
                }
                var msg = [];
                var data = result.data;
                for (var i = 0; i < data.length; i++) {
                    if (ids.indexOf(data[i]['PAY_DETAIL_ID']) >= 0) {
                        msg.push(data[i]['VBILLNO'], "已存在于当前列表中", "；<br>")
                        rows.splice(rows.indexOf(data[i]['PAY_DETAIL_ID']), 1);
                    }
                    if (data[i]['OTHER_IDS']) {
                        var arr = data[i]['OTHER_IDS'].split(',');
                        var cnt = 0;
                        for (var j = 0; j < arr.length; j++) {
                            if (rows.indexOf(arr[j]) < 0 && ids.indexOf(arr[j]) < 0) {
                                cnt++;
                                rows.push(arr[j]);
                            }
                        }
                        if (cnt > 0) {
                            msg.push(data[i]['VBILLNO'], "同运单", cnt, "单应付被引入", "；<br>");
                        }
                    }
                }
                if (msg.length > 0) {
                    $.modal.alertWarning(msg.join(""));
                }
                callback();
            }
        })
    }

    function removeRow(el, payDetailId, cur_total_amount, cur_got_amount) {
        var ids = $("#payDetailIds").val().split(",");
        for (var i = 0; i < ids.length; i++) {
            if (ids[i] == payDetailId) {
                ids.splice(i, 1);
                $(el).parents("tr:first").remove();
                $("#payDetailIds").val(ids.join(','))
                totalAmount = totalAmount.minus(new BigNumber(cur_total_amount));
                gotAmount = gotAmount.minus(new BigNumber(cur_got_amount))
                ungotAmount = totalAmount.minus(gotAmount);
                $("#td_totalAmount").text(totalAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#totalAmount").val(totalAmount.toNumber())
                $("#td_gotAmount").text(gotAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#td_ungotAmount").text(ungotAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#ungotAmount").val(ungotAmount.toNumber());
                break;
            }
        }
    }

</script>
</body>

</html>