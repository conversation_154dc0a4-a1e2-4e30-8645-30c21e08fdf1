<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('分批付款')"/>
    <style>
        .icheckbox-blue{
            top: 3px
        }
    </style>
</head>
<body>
<div class="form-content">
    <form id="form-batchPay-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="vbillno" th:value="${payDetail.vbillno}">
        <input type="hidden" name="payDetailId" th:value="${payDetail.payDetailId}">
        <input name="corDate" type="hidden" th:value="${#dates.format(payDetail.corDate, 'yyyy-MM-dd HH:mm:ss')}"/>
        <!--运单id-->
        <input name="lotId" id="lotId" type="hidden" th:value="${payDetail.lotId}" />
        <!--油卡卡号-->
        <input name="oilCardNumber" id="oilCardNumber" type="hidden" th:value="${payDetail.oilCardNumber}" />
        <!--行号-->
        <input type="hidden" name="bankNo" id="bankNo" th:value="${carrBank?.bankNo}">

        <!--收款信息-->
        <input type="hidden"  id="recAccount1" th:value="${payDetail.recAccount}">
        <input type="hidden"  id="recCardNo1" th:value="${payDetail.recCardNo}">
        <input type="hidden"  id="recBank1" th:value="${payDetail.recBank}">
        <input type="hidden"  id="balaCorp" th:value="${payDetail.balaCorp}">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总金额(元)：</label>
                                    <div class="col-sm-8">
                                    <input  id="totalAmount" class="form-control" name="totalAmount"
                                            th:value="${payDetail.transFeeCount}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">可申请额度：</label>
                                    <div class="col-sm-8">
                                    <input  name="quota" id="quota"  class="form-control"
                                            th:value="${payDetail.ungotAmount}" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付款类型：</label>
                                    <div class="col-sm-8">
                                        <input name="payType" class="form-control" value="1" type="hidden">
                                        <input class="form-control" value="直接付款" disabled >

                                        <!-- <select name="payMethod" class="form-control" th:with="type=${@dict.getType('pay_type')}">
                                             <option value=""></option>
                                             <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                         </select>-->
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">本次付款金额：</label>
                                    <!--不符合无车承运人-->
                                    <div class="col-sm-8" th:if="${payDetail.isNtocc == 0}">
                                        <input name="payAmount" id="payAmount" min="0" required maxlength="10"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this)"
                                               th:value="${payDetail.ungotAmount}" class="form-control">
                                    </div>
                                    <!--符合无车承运人-->
                                    <div class="col-sm-8" th:if="${payDetail.isNtocc == 1}">
                                        <input name="payAmount" id="payAmount" min="0" required maxlength="10"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this)"
                                               th:value="${payDetail.ungotAmount}" class="form-control" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">付款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="payMethod" onchange="changePayMethod(this)" id="payMethod" class="form-control" th:with="type=${@dict.getType('pay_method')}" required>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                                                    th:disabled="${dict.dictValue=='77'}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6" id="psbcDiv" style="display:none;">
                                <input type="radio"  name="personFlag" value="0" checked>对公打款
                                <input type="radio"  name="personFlag" value="1" >对私打款
                            </div>
                            <div class="col-md-6 col-sm-6" id="qixinDiv" style="display:none;">
                                <label class="checkbox-inline check-box" style="padding-top: 1px">
                                    <input type="checkbox" id="qixinPass" name="qixinPass" value="1" th:checked="${isMine == '1'}" >琦欣中转</label>
                            </div>
                            <input name="payDate" id="payDate" type="hidden" class="form-control">
                            <!--<div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付款日期：</label>
                                    <div class="col-sm-8">
                                        <input name="payDate" id="payDate" type="hidden" class="form-control">
                                    </div>
                                </div>
                            </div>-->

                        </div>

                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">转出账户：</label>
                                    <div class="col-sm-8">
                                        <!--<select name="outAccount" id="outAccount" class="form-control" required >
                                            <option value=""></option>
                                            <option th:each="dict : ${account}"
                                                    th:text="${dict.accountName}"
                                                    th:id="${dict.accountCode}"
                                                    th:value="${dict.accountId}" ></option>
                                        </select>-->
                                        <div class="input-group">
                                            <!--账户名称-->
                                            <input name="accountName" id="accountName" required onchange="changeRecAccountStyle()"
                                                   class="form-control valid"
                                                   type="text" aria-required="true">
                                            <!--账户id-->
                                            <input name="outAccount" id="outAccount"  class="form-control valid"
                                                   type="hidden" aria-required="true">
                                            <!--账户编码-->
                                            <input name="accountCode" id="accountCode" class="form-control valid"
                                                   type="hidden" aria-required="true">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red" id="recAccountStyle">收款账户：</label>
                                    <div class="col-sm-8">
                                        <!--<select name="recAccount" id="recAccount" class="form-control" required >
                                            <option value=""></option>
                                            <option th:each="dict : ${carrBankList}"
                                                    th:text="${dict.bankAccount}"
                                                    th:id="${dict.carrBankId}"
                                                    th:value="${dict.bankAccount}" th:field="${payDetail.recAccount}">
                                            </option>
                                        </select>-->
                                        <input type="text" name="recAccount" id="recAccount" class="form-control rec" disabled="" required th:field="${payDetail.recAccount}">
                                    </div>
                                </div>
                            </div>


                        </div>

                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款卡号：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="recCardNo" id="recCardNo" disabled="" class="form-control rec" th:field="${payDetail.recCardNo}" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款银行：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="recBank" id="recBank" disabled="" class="form-control" th:field="${payDetail.recBank}">
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">核销油卡：</label>
                                    <div class="col-sm-8">
                                        <input type="text" id="fuelcardNo" disabled="" class="form-control" th:value="${fuelcardNo}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">付款备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" maxlength="250" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <!--<button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">关闭</span>
                    </button>-->
                    <h4 class="modal-title">输入U盾支付口令完成支付</h4>
                </div>
                <div class="modal-body">
                    <form id="form-g7pay" class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3" style="text-align: right;line-height: 24px"><span id="g7corp_name"></span>U盾支付口令:</label>
                            <div class="col-sm-7">
                                <input id="g7PayLotId" type="hidden">
                                <input id="g7PayCorp" type="hidden">
                                <input id="g7PayPayee" type="hidden">
                                <input id="g7PayAmount" type="hidden">
                                <input id="g7PayRecordId" type="hidden">
                                <input class="form-control valid" required id="cipher"/>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" onclick="laterPay()">稍后统一支付</button>
                    <button type="button" class="btn btn-danger" onclick="doG7Pay()">立即支付</button>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">
    var lotG7Start = [[${lot==null?null:lot.g7Start}]]
    var lotG7End = [[${lot==null?null:lot.g7End}]]
    var g7Syn = [[${payDetail.g7Syn}]];
    var g7Pay = [[${payDetail.g7Pay}]];
    var bala_corp = [[${payDetail.balaCorp}]];
    var prefix = ctx + "payDetail";
    //额度
    var quota = parseFloat($("#quota").val());
    //默认当日日期
    var time = new Date();
    var day = ("0" + time.getDate()).slice(-2); //获取当前日期
    var month = ("0" + (time.getMonth() + 1)).slice(-2); //获取当前月份
    var h=time.getHours();       //获取当前小时数(0-23)
    var m=time.getMinutes();     //获取当前分钟数(0-59)
    var s=time.getSeconds();     //获取当前秒数
    var today = time.getFullYear() + "-" + (month) + "-" + (day) + " "+h+":" +m+ ":"+s;
    $("#payDate").val(today);

    //费用类型
    var freeType = [[${payDetail.freeType}]];
    //运费类型
    var costTypeFreight = [[${payDetail.costTypeFreight}]];


    $(function () {
        $('#collapseOne').collapse('show');

        /** 校验 */
        $("#form-batchPay-add").validate({
            focusCleanup: true
        });
        //加载收款信息
        // $('#recAccount').change(function(){
        //     var id = $(this).find(":selected").attr("id");
        //     var data = {carrBankId:id};
        //     var url = ctx + "payManage/selectReceInfo";
        //     $.ajax({
        //         url : url,
        //         method : 'POST',
        //         data : data,
        //         success:function (data) {
        //             $("#recCardNo").val(data.bankCard);
        //             $("#recBank").val(data.bankName);
        //             $("#bankNo").val(data.bankNo);
        //         }
        //     })
        // });

        /**
         * 当付款类型为 预付油卡 到付油卡 回付油卡  付款方式默认为油卡支付
         */
        if(freeType == 0 && (costTypeFreight == 1 || costTypeFreight == 3 || costTypeFreight == 5) ){
            $("#payMethod").val('91');
            OilPay();
        }

        /**
         * 付款方式 1.油卡支付-带出油卡账户与油卡卡号
         *         2.其他方式支付 带出收款信息
         */
        $('#payMethod').change(function(){
            var payMethod = $(this).find(":selected").val();//付款方式
            //先置空收款信息
            $("#recAccount").val("");//收款账户
            $("#recCardNo").val("");//收款卡号
            $("#recBank").val("");//收款银行
            //付款方式为油卡支付
            if(payMethod === '91'){
                OilPay();
            }else{
                $("#recAccount").val($("#recAccount1").val());//收款账户
                $("#recCardNo").val($("#recCardNo1").val());//收款卡号
                $("#recBank").val($("#recBank1").val());//收款银行
            }
            //获取转出账户编码
            var outAccount = $('#accountCode').val();
            //现金支付
            if((outAccount === '现金支付' && payMethod === '42') || (outAccount === '异常销账账户' && payMethod === '93')){
                $("#recAccountStyle").css("color", "");
            }else{
                $("#recAccountStyle").css("color", "red");
            }

        });
        var includeOnWay = true; // 在途是否允许走g7
        var g7option = $("#payMethod").find("option[value='77']");
        if (lotG7End == 2 && (
            (freeType == 0 && (costTypeFreight == 0 || costTypeFreight == 2 || costTypeFreight == 4))
            ||
            (includeOnWay && freeType == 1 && (costTypeFreight == null || costTypeFreight == 0 || costTypeFreight == 2 || costTypeFreight == 4))
        )) {
            if (g7Pay == null || g7Pay == 0 || g7Pay == 3) { // G7支付状态：0/未支付，1/支付中，2/支付成功，3/支付失败
                if (g7option.length == 1) {
                    g7option.prop("disabled", false);
                    $("#payMethod").val('77')
                }
            }
        }
        $("#payMethod").change() // 触发change事件
    });

    /**
     *  转出账户
     */
    function changeRecAccountStyle(){
        //获取转出账户编码
        var accountCode = $("#accountCode").val();
        //获取付款方式
        var payMethod =  $('#payMethod').find(":selected").val();
        if((accountCode === '现金支付' && payMethod === '42') || (accountCode === '异常销账账户' && payMethod === '93')){
            $("#recAccountStyle").css("color", "");
        }else{
            $("#recAccountStyle").css("color", "red");
        }
    }


    /**
     * 油卡支付 回显油卡信息
     * @constructor
     */
    function OilPay() {
        var oilCardNumber = $("#oilCardNumber").val();//油卡卡号
        var data = {oilCardNumber:oilCardNumber};
        var url = ctx + "payDetail/selectFuelCard";
        $.ajax({
            url : url,
            method : 'POST',
            data : data,
            success:function (data) {
                $("#recAccount").val(data.fuelcardName);//油卡账户
                $("#recCardNo").val(oilCardNumber);//油卡号
                $("#recBank").val("");//收款银行
            }
        })
    }
    function getG7CorpName(corp) {
        var balaCorp = [[${@dict.getType('bala_corp')}]];
        for (let i = 0; i < balaCorp.length; i++) {
            if (balaCorp[i].dictValue == corp) {
                return balaCorp[i].dictLabel
            }
        }
        return '未知';
    }
    //提交
    function submitHandler() {
        //本次付款金额
        var payAmount = parseFloat($("#payAmount").val());
        if(payAmount>quota){
            $.modal.alertError("本次付款金额必须<="+quota);
            return ;
        }
        $(":disabled").attr("disabled", false).addClass("tmp");//移除disabled
        //$("#recCardNo").attr("readonly",true);
        //$("#recAccount").attr("readonly",true);
        //$("#recBank").attr("readonly",true);

        //获取转出账户编码
        var accountCode = $("#accountCode").val();
        var payMethod =  $('#payMethod').find(":selected").val();//获取付款方式
        /**
         *  当转出账户与付款方式都为现金支付时 收款信息非必填 其余为必填
         */
        if((accountCode === '现金支付' && payMethod === '42') || (accountCode === '异常销账账户' && payMethod === '93')){
            $(".rec").attr("required", false);
        }else{
            $(".rec").attr("required", true);
        }

        let _formSer = $('#form-batchPay-add').serialize();
        $('.tmp').prop('disabled', true);
        if ($.validate.form()) {
            //默认未锁定
            var lock = false;
            layer.confirm("确认支付？",{
                btn:["确认","取消"]
            },function (index, layero) {
                if(!lock){
                    lock = true; // 锁定
                    layer.close(index);
                    $.operate.save(prefix + "/saveBatchPay", _formSer, function(result){
                        if ($("#payMethod").val() == '77') {
                            var parent = window.parent;
                            parent.$.modal.msgSuccess(result.msg);
                            var data = result.data;
                            if (data && data.success === true) {
                                $("#g7PayLotId").val(data.lotId)
                                $("#cipher").val("")
                                $("#g7PayCorp").val(data.corp)
                                $("#g7PayPayee").val(data.payee)
                                $("#g7corp_name").text(getG7CorpName(data.corp));
                                $("#g7PayAmount").val(data.amount)
                                $("#g7PayRecordId").val(data.record_id)
                                $('#myModal').modal({keyboard: false, backdrop: 'static'})
                                $.modal.disable();
                                //parent = window.parent;
                                if (parent.$.table._option.type == table_type.bootstrapTable) {
                                    parent.$.table.refresh();
                                } else if (parent.$.table._option.type == table_type.bootstrapTreeTable) {
                                    parent.$.treeTable.refresh();
                                }
                            } else {
                                //alert(result.msg)
                            }
                        } else if ($("#payMethod").val() == '88') {

                        }
                    });
                }
            },function (index) {
                $(".tmp").attr("disabled", true).removeClass("tmp")
                layer.close(index);
            });
        } else {
            $(".tmp").attr("disabled", true).removeClass("tmp")
        }
    }

    function laterPay() {
        $('#myModal').modal('hide')
        var parent = window.parent;
        if (parent.$.table._option.type == table_type.bootstrapTable) {
            $.modal.close();
            parent.$.table.refresh();
        } else if (parent.$.table._option.type == table_type.bootstrapTreeTable) {
            $.modal.close();
            parent.$.treeTable.refresh();
        } else {
            $.modal.msgReload("保存成功,正在刷新数据请稍后……", modal_status.SUCCESS);
        }
    }

    function doG7Pay() {
        if ($("#form-g7pay").validate().form()) {
            $.modal.confirm("即将付款给"+$("#g7PayPayee").val()+"，金额"+$("#g7PayAmount").val()+"，是否继续？", function(){
                $.operate.save(prefix + "/g7-pay", {
                    lotId: $('#g7PayLotId').val(),
                    record_id: $('#g7PayRecordId').val(),
                    cipher: $.trim($("#cipher").val()),
                    corp: $("#g7PayCorp").val()
                });
            })
        }
    }

    //选择账户
    function selectAccount(){
        var url = "/finance/account/selectAccount";
        $.modal.open("选择账户", url);
    }


    /**
     * 关键字提示查询 转入账户
     */
    $("#accountName").bsSuggest('init', {
        url: ctx + "finance/account/findAccount?paymentType=1&balaCorp="+$("#balaCorp").val(),
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["accountName","accountCode","account"],
        effectiveFieldsAlias: {"accountName":"账户名称","accountCode":"账户信息","account":"账号"},
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#accountName").val(data.accountName);
        $("#outAccount").val(data.accountId);
        $("#accountCode").val(data.accountCode);
    })

    function changePayMethod(sel) {
        //<!-- G7结算公司 -->
        /*var corp_account = {
            MY: "江苏铭源物流有限公司",
            JH: "南通吉华物流有限公司",
            DH: "亿鼎物流集团鼎辉公司",
            DW: "亿鼎物流集团鼎旺公司"
        }*/
        $("#payAmount").prop("disabled", $(sel).val() == '77' || $(sel).val() == '88')
        //$("#accountName").prop("disabled", $(sel).val() == '77' || $(sel).val() == '88')
        if ($(sel).val() == '77' || $(sel).val() == '88') {
            $("#payAmount").val('[[${payDetail.ungotAmount}]]');
            /*$.ajax({
                url: ctx + "finance/account/findAccount?paymentType=1&keyword=",
                type: 'get',
                cache: false,
                dataType: 'json',
                success: function(res){
                    if (res.code == 200) {
                        var list = res.value
                        for(var i=0;i<list.length;i++) {
                            if (list[i].accountCode == corp_account[bala_corp]) {
                                $("#accountName").val(list[i].accountName);
                                $("#outAccount").val(list[i].accountId);
                                $("#accountCode").val(list[i].accountCode);
                                break;
                            }
                        }
                    }
                }
            })*/
        }

        if($(sel).val() == '39'){
            $("#qixinDiv").css("display","block");
        }else{
            $("#qixinDiv").css("display","none");
        }

        if($(sel).val() == '38'){
            $("#psbcDiv").css("display","block");
        }else{
            $("#psbcDiv").css("display","none");
        }
    }

</script>
</body>

</html>