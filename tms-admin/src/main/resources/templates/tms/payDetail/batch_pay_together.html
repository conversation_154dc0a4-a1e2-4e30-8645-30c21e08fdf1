<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('现金合并付款')"/>
    <style>
        .pay-item {
            border: 1px solid #dddddd;
            background: #ffffff;
            padding-top: 5px;
            border-radius: 5px;
            -moz-border-radius: 5px;
            margin-bottom: 1px;
        }
        .pay-item:hover {
            background-color: #f1f1fe;
        }
        .pay-item.unableg7 {
            background-color: #eeeeee;
            color: #eeeeee;
        }
        .icheckbox-blue{
            top: 3px
        }
    </style>
</head>
<body>
<div class="form-content">
    <form id="form-batchPay-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="payDetailId" th:value="${payDetailId}">
        <input type="hidden" id="balaCorp" th:value="${balaCorp}">


        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总金额(元)：</label>
                                    <div class="col-sm-8">
                                    <input  id="totalAmount" class="form-control disabled" name="totalAmount"
                                            th:value="${totalAmount}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">可申请额度：</label>
                                    <div class="col-sm-8">
                                    <input  name="quota" id="quota"  class="form-control disabled"
                                            th:value="${totalUngotAmount}" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付款类型：</label>
                                    <div class="col-sm-8">
                                        <input name="payType" class="form-control" value="1" type="hidden">
                                        <input class="form-control disabled" value="直接付款" disabled >

                                        <!-- <select name="payMethod" class="form-control" th:with="type=${@dict.getType('pay_type')}">
                                             <option value=""></option>
                                             <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                         </select>-->
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">本次付款金额：</label>
                                    <!--符合无车承运人-->
                                    <div class="col-sm-8" >
                                        <input name="payAmount" id="payAmount" min="0" required maxlength="10"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this)"
                                               th:value="${totalUngotAmount}" class="form-control disabled" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">付款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="payMethod" id="payMethod" class="form-control" th:with="type=${@dict.getType('pay_method')}" required onchange="changePayMethod(this)">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6" id="psbcDiv" style="display:none;">
                                <input type="radio"  name="personFlag" value="0" checked>对公打款
                                <input type="radio"  name="personFlag" value="1" >对私打款
                            </div>
                            <div class="col-md-6 col-sm-6" id="qixinDiv" style="display:none;">
                                <label class="checkbox-inline check-box" style="padding-top: 1px">
                                    <input type="checkbox" id="qixinPass" name="qixinPass" value="1"  >琦欣中转</label>
                            </div>

                            <input name="payDate" id="payDate" type="hidden" class="form-control">
                            <!--<div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付款日期：</label>
                                    <div class="col-sm-8">
                                        <input name="payDate" id="payDate" type="hidden" class="form-control">
                                    </div>
                                </div>
                            </div>-->

                        </div>

                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">转出账户：</label>
                                    <div class="col-sm-8">
                                        <!--<select name="outAccount" id="outAccount" class="form-control" required >
                                            <option value=""></option>
                                            <option th:each="dict : ${account}"
                                                    th:text="${dict.accountName}"
                                                    th:id="${dict.accountCode}"
                                                    th:value="${dict.accountId}" ></option>
                                        </select>-->
                                        <div class="input-group">
                                            <!--账户名称-->
                                            <input name="accountName" id="accountName" required onchange="changeRecAccountStyle()"
                                                   class="form-control valid"
                                                   type="text" aria-required="true">
                                            <!--账户id-->
                                            <input name="outAccount" id="outAccount"  class="form-control valid"
                                                   type="hidden" aria-required="true">
                                            <!--账户编码-->
                                            <input name="accountCode" id="accountCode" class="form-control valid"
                                                   type="hidden" aria-required="true">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row pay-item" th:each="payDetailshow:${payDetailList}" th:pay-detail-id="${payDetailshow.payDetailId}">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 recAccountStyle" style="color: red">收款账户：</label>
                                    <div class="col-sm-8">
                                        <input type="text" th:name="'recAccount'+${payDetailshow.payDetailId}"  class="form-control rec recAccount disabled" disabled="" required th:value="${payDetailshow.recAccount}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款卡号：</label>
                                    <div class="col-sm-8">
                                        <input type="text" th:name="'recCardNo'+${payDetailshow.payDetailId}"  disabled="" class="form-control rec recCardNo disabled" th:value="${payDetailshow.recCardNo}" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款银行：</label>
                                    <div class="col-sm-8">
                                        <input type="text" th:name="'recBank'+${payDetailshow.payDetailId}" disabled="" class="form-control recBank disabled" th:value="${payDetailshow.recBank}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款金额：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="recAmount" disabled="" class="form-control recAmount disabled" th:value="${payDetailshow.ungotAmount}">
                                    </div>
                                </div>
                            </div>

                        </div>



                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">付款备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" maxlength="250" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">
    var prefix = ctx + "payDetail";
    //额度
    var quota = parseFloat($("#quota").val());
    //默认当日日期
    var time = new Date();
    var day = ("0" + time.getDate()).slice(-2); //获取当前日期
    var month = ("0" + (time.getMonth() + 1)).slice(-2); //获取当前月份
    var h=time.getHours();       //获取当前小时数(0-23)
    var m=time.getMinutes();     //获取当前分钟数(0-59)
    var s=time.getSeconds();     //获取当前秒数
    var today = time.getFullYear() + "-" + (month) + "-" + (day) + " "+h+":" +m+ ":"+s;
    $("#payDate").val(today);

    //[{"params":{},"payDetailId":"04f18c1f5796410aae978fb8b06f0ac7","freeType":"0","lotId":"f3a7f88ae2214a178dccbe22124d5158","balaCorp":"JH","costTypeFreight":"4","size":0}]
    var g7DetailList = [(${g7DetailList})];
    var req_payDetailId = [[${payDetailId}]];


    $(function () {
        $('#collapseOne').collapse('show');

        /** 校验 */
        $("#form-batchPay-add").validate({
            focusCleanup: true
        });
        //加载收款信息
        // $('#recAccount').change(function(){
        //     var id = $(this).find(":selected").attr("id");
        //     var data = {carrBankId:id};
        //     var url = ctx + "payManage/selectReceInfo";
        //     $.ajax({
        //         url : url,
        //         method : 'POST',
        //         data : data,
        //         success:function (data) {
        //             $("#recCardNo").val(data.bankCard);
        //             $("#recBank").val(data.bankName);
        //             $("#bankNo").val(data.bankNo);
        //         }
        //     })
        // });

        /**
         * 当付款类型为 预付油卡 到付油卡 回付油卡  付款方式默认为油卡支付
         */
/*        if(freeType == 0 && (costTypeFreight == 1 || costTypeFreight == 3 || costTypeFreight == 5) ){
            $("#payMethod").val('91');
            OilPay();
        }*/

        /**
         * 付款方式 1.油卡支付-带出油卡账户与油卡卡号
         *         2.其他方式支付 带出收款信息
         */
        $('#payMethod').change(function(){
            var payMethod = $(this).find(":selected").val();//付款方式
            //先置空收款信息
           /* $("#recAccount").val("");//收款账户
            $("#recCardNo").val("");//收款卡号
            $("#recBank").val("");//收款银行
            //付款方式为油卡支付
            if(payMethod === '91'){
                //OilPay();
            }else{
                $("#recAccount").val($("#recAccount1").val());//收款账户
                $("#recCardNo").val($("#recCardNo1").val());//收款卡号
                $("#recBank").val($("#recBank1").val());//收款银行
            }*/
            //获取转出账户编码
            var accountCode = $('#accountCode').val();
            //现金支付
            if(accountCode === '现金支付' && payMethod === '42'){
                $(".recAccountStyle").css("color", "");
            }else{
                $(".recAccountStyle").css("color", "red");
            }
        });

    });

    /**
     *  转出账户
     */
    function changeRecAccountStyle(){
        //获取转出账户编码
        var accountCode = $("#accountCode").val();
        //获取付款方式
        var payMethod =  $('#payMethod').find(":selected").val();
        if(accountCode === '现金支付' && payMethod === '42'){
            $(".recAccountStyle").css("color", "");
        }else{
            $(".recAccountStyle").css("color", "red");
        }
    }


    /**
     * 油卡支付 回显油卡信息
     * @constructor
     */
    function OilPay() {
        var oilCardNumber = $("#oilCardNumber").val();//油卡卡号
        var data = {oilCardNumber:oilCardNumber};
        var url = ctx + "payDetail/selectFuelCard";
        $.ajax({
            url : url,
            method : 'POST',
            data : data,
            success:function (data) {
                $("#recAccount").val(data.fuelcardName);//油卡账户
                $("#recCardNo").val(oilCardNumber);//油卡号
                $("#recBank").val("");//收款银行
            }
        })
    }

    let pIndex = null;
    //提交
    function submitHandler(idx) {
        pIndex = idx;
        //本次付款金额
        var payAmount = parseFloat($("#payAmount").val());
        if(payAmount>quota){
            $.modal.alertError("本次付款金额必须<="+quota);
            return ;
        }

        //获取转出账户编码
        var accountCode = $("#accountCode").val();
        var payMethod =  $('#payMethod').find(":selected").val();//获取付款方式
        /**
         *  当转出账户与付款方式都为现金支付时 收款信息非必填 其余为必填
         */
        if(payMethod === '91'){
            $.modal.alertError("该页面不支持油卡支付");
            return ;
        }

        if(accountCode === '现金支付' && payMethod === '42'){
            $(".rec").attr("required", false);
        }else{
            $(".rec").attr("required", true);
        }

        if (payMethod === '77') {
            var tmp = []
            for (let i = 0; i < g7DetailList.length; i++) {
                if (checkAbleG7(g7DetailList[i])) {
                    tmp.push(g7DetailList[i].payDetailId)
                }
            }
            if (tmp.length == 0) {
                $.modal.msgError("没有符合G7支付的应付明细！");
                return;
            }
            $("[name='payDetailId']").val(tmp.join(",")) // 只处理G7可支付的应付
        } else {
            $("[name='payDetailId']").val(req_payDetailId); // 还原成已选择的应付
        }

        //$(".disabled").attr("disabled", false);//移除disabled
        //$(".recCardNo").attr("readonly",true);
        //$(".recAccount").attr("readonly",true);
        //$(".recBank").attr("readonly",true);
        if ($.validate.form()) {
            //默认未锁定
            var lock = false;
            layer.confirm("确认支付？",{
                btn:["确认","取消"]
            },function (index, layero) {
                if(!lock){
                    lock = true; // 锁定
                    layer.close(index);
                    $(".disabled").prop("disabled", false).prop("readonly", true);
                    var p = $('#form-batchPay-add').serialize()
                    $(".disabled").prop("disabled", true).prop("readonly", false);
                    $.operate.saveModalAndRefush(prefix + "/saveBatchPayTogether", p);
                }
            },function (index) {
                layer.close(index);
                //$(".disabled").attr("disabled", true);
            });
        }else{
            //$(".disabled").attr("disabled", true);
        }
    }

    //选择账户
    function selectAccount(){
        var url = "/finance/account/selectAccount";
        $.modal.open("选择账户", url);
    }


    /**
     * 关键字提示查询 转入账户
     */
    $("#accountName").bsSuggest('init', {
        url: ctx + "finance/account/findAccount?paymentType=1&balaCorp="+$("#balaCorp").val(),
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["accountName","accountCode","account"],
        effectiveFieldsAlias: {"accountName":"账户名称","accountCode":"账户信息","account":"账号"},
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#accountName").val(data.accountName);
        $("#outAccount").val(data.accountId);
        $("#accountCode").val(data.accountCode);
    })

    // TODO 通过cost_type_freight,free_type,g7Pay等判断是否符合g7支付
    function checkAbleG7(dtl) {
        var includeOnWay = true; // 在途是否允许走g7
        if (dtl.lotG7End == 2 && (
            (dtl.freeType == 0 && (dtl.costTypeFreight == 0 || dtl.costTypeFreight == 2 || dtl.costTypeFreight == 4))
            ||
            (includeOnWay && dtl.freeType == 1 && (dtl.costTypeFreight == null || dtl.costTypeFreight == 0 || dtl.costTypeFreight == 2 || dtl.costTypeFreight == 4))
        )) {
            if (dtl.g7Pay == null || dtl.g7Pay == 0 || dtl.g7Pay == 3) {
                return true;
            }
        }
        return false;
    }

    function changePayMethod(el) {
        //<!-- G7结算公司 -->
        /*var corp_account = {
            MY: "江苏铭源物流有限公司",
            JH: "南通吉华物流有限公司",
            DH: "亿鼎物流集团鼎辉公司",
            DW: "亿鼎物流集团鼎旺公司"
        }*/
        //$("#accountName").prop("disabled", $(el).val() == '77')
        if (el.value == '77') {
            $(".pay-item").removeClass("unableg7")
            // 检查所有计划是否可G7支付
            var n = 0;
            for (let i = 0; i < g7DetailList.length; i++) {
                if (!checkAbleG7(g7DetailList[i])) {
                    n++;
                    $("[pay-detail-id='"+g7DetailList[i].payDetailId+"']").addClass("unableg7")
                }
            }
            if (n > 0) {
                $.modal.msgError("置灰的单据不支持G7支付！");
            }
            /*$.ajax({
                url: ctx + "finance/account/findAccount?paymentType=1&keyword=",
                type: 'get',
                cache: false,
                dataType: 'json',
                success: function(res){
                    if (res.code == 200) {
                        var list = res.value
                        for(var i=0;i<list.length;i++) {
                            if (list[i].accountCode == corp_account[g7DetailList[0].balaCorp]) {
                                $("#accountName").val(list[i].accountName);
                                $("#outAccount").val(list[i].accountId);
                                $("#accountCode").val(list[i].accountCode);
                                break;
                            }
                        }
                    }
                }
            })*/
        } else {
            $(".pay-item").removeClass("unableg7")
        }

        if($(el).val() == '39'){
            $("#qixinDiv").css("display","block");
        }else{
            $("#qixinDiv").css("display","none");
        }

        if($(el).val() == '38'){
            $("#psbcDiv").css("display","block");
        }else{
            $("#psbcDiv").css("display","none");
        }
    }

</script>
</body>

</html>