<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反复核')"/>
</head>

<body>
<div class="form-content">
    <form id="form-invoice-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="payDetailId" name="payDetailId" type="hidden" th:value="${payDetailId}">
            <div class="panel-body">
                <!--基础信息 begin-->
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5" style="color: red">反复核说明：</label>
                            <div class="col-sm-12">
                                <textarea name="checkMemo" id="checkMemo" class="form-control" type="text"
                                          maxlength="50" required="" aria-required="true"></textarea>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "payDetail";

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-invoice-unconfirm").serializeArray();
            $.operate.save(prefix + "/saveCancelReview", data);
        }
    }




</script>
</body>
</html>