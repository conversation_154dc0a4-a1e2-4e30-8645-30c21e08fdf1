<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('分批付款')"/>
</head>
<body>
<div class="form-content">
    <form id="form-batchPay-add" class="form-horizontal" novalidate="novalidate">
        <!--应付明细ids-->
        <input type="hidden" name="payDetailId" th:value="${payDetailIds}">
        <input type="hidden"  id="balaCorp" th:value="${balaCorp}">
        <!--行号-->
        <input type="hidden" name="bankNo" id="bankNo">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总金额(元)：</label>
                                    <div class="col-sm-8">
                                    <input  id="totalAmount" class="form-control" name="totalAmount"
                                            th:value="${payDetail.ungotAmount}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">可申请额度：</label>
                                    <div class="col-sm-8">
                                    <input  name="quota" id="quota"  class="form-control"
                                            th:value="${payDetail.ungotAmount}" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付款类型：</label>
                                    <div class="col-sm-8">
                                        <input name="payType" class="form-control" value="1" type="hidden">
                                        <input class="form-control" value="直接付款" disabled >
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">本次付款金额：</label>
                                    <div class="col-sm-8">
                                        <input name="payAmount" id="payAmount" min="0" required
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this)"
                                               th:value="${payDetail.ungotAmount}" class="form-control" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">付款方式：</label>
                                    <div class="col-sm-8">
                                        <input type="text" th:value="油卡支付" class="form-control" disabled>
                                        <input type="hidden" th:value="91" name="payMethod" class="form-control">

                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付款日期：</label>
                                    <div class="col-sm-8">
                                        <input name="payDate" id="payDate" class="form-control">
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">转出账户：</label>
                                    <div class="col-sm-8">
                                       <!-- <select name="outAccount" class="form-control" required >
                                            <option value=""></option>
                                            <option th:each="dict : ${account}" th:text="${dict.accountName}" th:value="${dict.accountId}" ></option>
                                        </select>-->
                                        <div class="input-group">
                                            <!--账户名称-->
                                            <input name="accountName" id="accountName" required onchange="changeRecAccountStyle()"
                                                   class="form-control valid"
                                                   type="text" aria-required="true">
                                            <!--账户id-->
                                            <input name="outAccount" id="outAccount"  class="form-control valid"
                                                   type="hidden" aria-required="true">
                                            <!--账户编码-->
                                            <input name="accountCode" id="accountCode" class="form-control valid"
                                                   type="hidden" aria-required="true">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收款账户：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="recAccount" id="recAccount" class="form-control" disabled="" th:value="${fuelcard.fuelcardName}">
                                    </div>
                                </div>
                            </div>


                        </div>

                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款卡号：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="recCardNo" id="recCardNo" disabled="" class="form-control" th:value="${fuelcard.fuelcardNo}">
                                    </div>
                                </div>
                            </div>


                        </div>



                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">付款备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" maxlength="250" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">
    var prefix = ctx + "payDetail";
    //额度
    var quota = parseFloat($("#quota").val());
    //默认当日日期
    var time = new Date();
    var day = ("0" + time.getDate()).slice(-2); //获取当前日期
    var month = ("0" + (time.getMonth() + 1)).slice(-2); //获取当前月份
    var h=time.getHours();       //获取当前小时数(0-23)
    var m=time.getMinutes();     //获取当前分钟数(0-59)
    var s=time.getSeconds();     //获取当前秒数
    var today = time.getFullYear() + "-" + (month) + "-" + (day) + " "+h+":" +m+ ":"+s;
    $("#payDate").val(today);


    $(function () {
        $('#collapseOne').collapse('show');
        /** 校验 */
        $("#form-batchPay-add").validate({
            focusCleanup: true
        });
    });


    //提交
    function submitHandler() {
        if ($.validate.form()) {
            //默认未锁定
            var lock = false;
            layer.confirm("确认支付？",{
                btn:["确认","取消"]
            },function (index, layero) {
                if(!lock){
                    lock = true;
                    layer.close(index);
                    $(":disabled").attr("disabled", false);//移除disabled
                    $.operate.save(prefix + "/saveOilCardBatchPay", $('#form-batchPay-add').serialize());
                }
            },function (index) {
                layer.close(index);
            });
        }
    }

    /**
     * 关键字提示查询 转入账户
     */
    $("#accountName").bsSuggest('init', {
        url: ctx + "finance/account/findAccount?paymentType=1&balaCorp="+$("#balaCorp").val(),
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["accountName"],
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#accountName").val(data.accountName);
        $("#outAccount").val(data.accountId);
        $("#accountCode").val(data.accountCode);
    })





</script>
</body>

</html>