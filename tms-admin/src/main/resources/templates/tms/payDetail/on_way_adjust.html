<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('在途费拆分')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background: #f5f7fa;
        padding: 20px;
    }

    .form-content {
        background: white;
        padding: 24px;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        margin: 0 auto;
    }

    .title {
        font-size: 16px;
        margin-bottom: 24px;
        color: #333;
    }

    .flex {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        width: 5em;
        color: #666;
        font-size: 14px;
    }

    .form-control {
        flex: 1;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.3s;
    }

    .form-control:focus {
        outline: none;
        border-color: #4a90e2;
        box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
    }

    .amount-info {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 6px;
        margin-top: 20px;
    }

    .amount-row {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        color: #666;
    }

    .amount-row:last-child {
        margin-bottom: 0;
    }

    .amount-label {
        width: 7em;
        font-size: 14px;
    }

    .amount-value {
        font-size: 16px;
        color: #333;
        font-weight: 500;
    }

    .amount-type {
        margin-left: 8px;
        background: #e6f0f9;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        color: #4a90e2;
    }

    .split-amount {
        color: #52c41a;
    }

    .remaining-amount {
        color: #f5a623;
    }

    .split-details {
        border-top: 1px dashed #ddd;
        margin-top: 12px;
        padding-top: 12px;
    }

    .error-message {
        color: #ff4d4f;
        font-size: 12px;
        margin-top: 4px;
        display: none;
    }
</style>
<body>
<div class="form-content">
    <form id="form-receive-add" class="form-horizontal">
        <input name="payDetailId" id="payDetailId" type="hidden" th:value="${payDetailId}">

        <div class="form-group">
            <div class="flex">
                <label class="form-label" for="transFeeCount">拆分金额：</label>
                <input
                        class="form-control"
                        name="transFeeCount"
                        id="transFeeCount"
                        step="0.01"
                        oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                        required
                        placeholder="请输入拆分金额"
                >
            </div>
            <div id="errorMessage" class="error-message">拆分金额不能超过原始金额</div>
        </div>

        <div class="amount-info">
            <div class="amount-row">
                <span class="amount-label">原始金额：</span>
                <span class="amount-value">[[${transFeeCount}]]</span>
                <span class="amount-type"
                      th:each="dict:${@dict.getType('cost_type_on_way')}"
                      th:if="${dict.dictValue == costTypeOnWay}"
                      th:text="${dict.dictLabel}"></span>
            </div>
            <div class="split-details">
                <div class="amount-row">
                    <span class="amount-label">拆分金额：</span>
                    <span id="splitAmount" class="amount-value split-amount">0.00</span>
                </div>
                <div class="amount-row">
                    <span class="amount-label">剩余金额：</span>
                    <span id="remainingAmount" class="amount-value remaining-amount">[[${transFeeCount}]]</span>
                </div>
            </div>
        </div>
    </form>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    let transFeeCountT = [[${transFeeCount}]];
    let payDetailId = [[${payDetailId}]];

    $(function() {
        // 初始化显示
        updateAmountDisplay(0);

        // 监听输入变化
        $("#transFeeCount").on('input', function() {
            const inputValue = parseFloat($(this).val()) || 0;
            updateAmountDisplay(inputValue);
        });
    });

    // 更新金额显示
    function updateAmountDisplay(inputValue) {
        if (inputValue > transFeeCountT) {
            $("#errorMessage").show();
            inputValue = transFeeCountT;
            $("#transFeeCount").val(transFeeCountT);
        } else {
            $("#errorMessage").hide();
        }

        $("#splitAmount").text(inputValue.toFixed(2));
        $("#remainingAmount").text((transFeeCountT - inputValue).toFixed(2));
    }


    //提交
    function submitHandler() {

        if ($.validate.form()) {
            let transFeeCount = $("#transFeeCount").val();
            if (transFeeCount <= 0) {
                $.modal.alertWarning("填写的金额不允许小于0");
                return;
            }
            if (transFeeCount > transFeeCountT) {
                $.modal.alertWarning("填写的金额不允许超过运费");
                return;
            }


            let data = {payDetailId:payDetailId, transFeeCount: transFeeCount}

            $.ajax({
                url: ctx + "payDetail/split_on_way",
                type: "post",
                dataType: "json",
                data: data,
                success: function (result) {
                    if (result.code === 0) {
                        var data = result.data;
                        //刷新
                        parent.$.btTable.bootstrapTable('refresh', {
                            silent: true
                        });

                        $.modal.close();
                        parent.$.modal.msgSuccess(result.msg)
                    } else {
                        $.modal.msgError(result.msg);
                    }
                }
            });


            // $.operate.save(prefix + "/split_on_way", $('#form-receive-add').serialize());
        }

    }

</script>
</body>
</html>