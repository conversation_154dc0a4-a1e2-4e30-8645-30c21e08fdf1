<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('运单选择页')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-sm-5">
                        <div class="form-group">
                            <label class="col-sm-4">运单号：</label>
                            <div class="col-sm-8">
                                <input name="lot" id="lot" placeholder="请输入运单号" class="form-control" type="text"
                                       aria-required="true">

                            </div>
                        </div>
                    </div>
                    <div class="col-sm-5">
                        <div class="form-group"></div>
                    </div>
                    <div class="col-sm-2">
                        <label class="col-sm-4"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "carrier/entrustLot";
    var carrierId = [[${carrierId}]];
    var vbillstatus = [[${vbillstatus}]];
    $(function () {
        var options = {
            url: prefix + "/entrustLotList?carrierId="+carrierId,
            showToggle:false,
            showColumns:false,
            clickToSelect:true,
            columns: [{
                radio: true
            },

                {
                    title: 'id',
                    visible: false,
                    field: 'entrustLotId'
                },
                {
                    title: '运单号',
                    align: 'left',
                    field: 'lot'

                },

                {
                    title: '运单状态',
                    align: 'left',
                    field: 'vbillstatus',
                    formatter: function(value, item, index) {
                        var context = '';
                        vbillstatus.forEach(function (v) {
                            if (v.value === item.vbillstatus) {
                                context = v.context;
                                return false;
                            }
                        });
                        return context;
                    }
                },

                {
                    title: '发货地址',
                    align: 'left',
                    field: 'deilAddr',
                    formatter: function status(row,value) {
                        if(value.deliProvinceName == null){
                            value.deliProvinceName = "";
                        }
                        if(value.deliCityName == null){
                            value.deliCityName = "";
                        }
                        if(value.deliAreaName == null){
                            value.deliAreaName = "";
                        }
                        return value.deliProvinceName + value.deliCityName + value.deliAreaName;
                    }
                },
                {
                    title: '到货地址',
                    align: 'left',
                    field: 'arriAddr',
                    formatter: function status(row,value) {
                        if(value.arriProvinceName == null){
                            value.arriProvinceName = "";
                        }
                        if(value.arriCityName == null){
                            value.arriCityName = "";
                        }
                        if(value.arriAreaName == null){
                            value.arriAreaName = "";
                        }
                        return value.arriProvinceName + value.arriCityName + value.arriAreaName;
                    }
                },
                {
                    title: '司机',
                    align: 'left',
                    field: 'driverName'
                },
                {
                    title: '车牌号',
                    align: 'left',
                    field: 'carNo'
                }

            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });


    /**
     * 选择运单后的提交方法
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.close();

        // 选中的运单号
        parent.$("#lotno").val($.table.selectColumns("lot").join());
        parent.$("#lotId").val($.table.selectColumns("entrustLotId").join());
        parent.$("#invoiceVbillno").val($.table.selectColumns("invoiceNo").join());

        parent.$("#deliProvince").text($.table.selectColumns("deliProvinceName").join());
        parent.$("#deliCity").text($.table.selectColumns("deliCityName").join());
        parent.$("#deliArea").text($.table.selectColumns("deliAreaName").join());
        parent.$("#arriProvince").text($.table.selectColumns("arriProvinceName").join());
        parent.$("#arriCity").text($.table.selectColumns("arriCityName").join());
        parent.$("#arriArea").text($.table.selectColumns("arriAreaName").join());
        // 结算公司
        var datas = [[${@dict.getType('bala_corp')}]];
        for ( var i = 0; i < datas.length; i++) {
            if (datas[i].dictValue === $.table.selectColumns("balaCorp").join()) {
                parent.$("#balaCorpName").val(datas[i].dictLabel);
                parent.$("#balaCorp").val(datas[i].dictValue);
            }
        }

        // 选中的承运商
        parent.$("#carrierId").val($.table.selectColumns("carrierId").join());
        parent.$("#carnoId").val($.table.selectColumns("carnoId").join());

        $.ajax({
            type: "get",
            async: false,
            dataType:"json",
            url:  ctx +"payDetail/getPayDetail?carrierId="+$.table.selectColumns("carrierId").join()
                + "&carnoId="+$.table.selectColumns("carnoId").join()
                +"&carLen="+$.table.selectColumns("carLen").join()
                +"&carType="+$.table.selectColumns("carType").join()
                +"&entrustLotId="+$.table.selectColumns("entrustLotId").join(),
            success: function(result) {
                // 清空历史
                parent.$("#carrName").val("");
                parent.$("#carrCode").val("");
                parent.$("#driverMobile").val("");
                parent.$("#driverName").val("");
                parent.$("#carrName").val(result["carrier"].carrName);
                parent.$("#carrCode").val(result["carrier"].carrCode);
                parent.$("#driverMobile").val(result["driver"].phone);
                parent.$("#driverName").val(result["driver"].driverName);
                // 清空历史
                parent.$("#car").html("");
                parent.$("#carno").text(result["car"].carno);
                parent.$("#carLenName").text($.table.selectColumns("carLenName").join());
                parent.$("#carTypeName").text($.table.selectColumns("carTypeName").join());

                // 清空历史
                parent.$("#reqDeliDate").val("");
                parent.$("#reqArriDate").val("");
                parent.$("#reqDeliDate").val(result["entrust"].actDeliDate);
                parent.$("#reqArriDate").val(result["entrust"].actArriDate);
            }
        });


        // 异常事故
        $.ajax({
            type: "get",
            async: false,
            url:  ctx +"payDetail/selectExp?entrustLotId="+$.table.selectColumns("entrustLotId").join(),
            success: function(result) {
                // 清空历史
                parent.$("#exp").html("");
                var html = '';
                for (var i = 0; i < result.length; i++) {
                    var estArrivalTime  = result[i].estArrivalTime === null ?   '' : result[i].estArrivalTime;
                    var note  = result[i].note === null ?   '' : result[i].note;
                    var expOccTime  = result[i].expOccTime === null ?   '' : result[i].expOccTime;
                    html += '<tr>' +
                        '<td>'+result[i].expType+'</td>' +
                        '<td>'+note+'</td>' +
                        '<td>'+expOccTime+'</td>' +
                        '<td>'+estArrivalTime+'</td>' +
                        '</tr>'
                }
                parent.$("#exp").append(html);
            }
        });
    }
</script>

</body>
</html>