<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细-复核列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-4">应付单号：</label>-->
                            <div class="col-sm-12">
                                <input name="vbillno" id="vbillno" th:value="${payDetail?.vbillno}" class="form-control"
                                       placeholder="请输入应付单号" maxlength="30">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-4">运营组：</label>-->
                            <div class="col-sm-12">
                                <select name="salesDeptName" id="salesDeptName"  class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运营组" multiple>
                                    <option th:each="dict : ${salesDeptList}"
                                            th:text="${dict.deptName}"
                                            th:value="${dict.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="review()" shiro:hasAnyPermissions="finance:payDetail:review,finance:fleet:payDetail:review">
                <i class="fa fa-check"></i> 复核
            </a>
            <!--<a class="btn btn-primary single disabled" onclick="reviewLot()" shiro:hasAnyPermissions="finance:payDetail:review,finance:fleet:payDetail:review">
                <i class="fa fa-check"></i> 复核运单
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];

    //是否是车队
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/payDetail" : ctx + "payDetail";

    //应付单状态
    var payDetailStatusEnum = [[${payDetailStatusEnum}]];
    //应付单map
    var payDetailStatusMap = [[${payDetailStatusMap}]];




    $(function () {
        var options = {
            url: prefix + "/listPayDetailReview",
            createUrl: ctx + "payDetail/add?carrierId="+$("#carrierId").val(),
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 3,
            height: 560,
            clickToSelect:true,
            showFooter:true,
            columns: [{
                checkbox: true,
            },
                {
                    title: '操作',
                    align: 'left',
                    field: 'payDetailId',
                    formatter: function (value, row, index) {
                        var actions = [];
                        // actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="结算公司" onclick="openBalaCorp(\'' + row.payDetailId + '\')"><i  class="fa fa-calculator" style="font-size: 15px;" ></i></a>');
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="detail(\'' + row.payDetailId + '\')"><i  class="fa fa-newspaper-o" style="font-size: 15px;" ></i></a>');

                        return actions.join('');
                    }
                },
                // {
                //     title: '应付单号',
                //     field: 'vbillno',
                //     align: 'left',
                // },
                // {
                //     title: '毛利',
                //     field: 'grossProfit',
                //     align: 'left',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }else if(value < 0){
                //             return '<span class="label label-danger">'+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+'</span>';
                //         }else{
                //             return '<span class="label label-primary">'+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+'</span>';
                //         }
                //
                //     }
                // },

                {
                    title: '应付单号/应付单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(row,value) {
                        var context = '';
                        payDetailStatusEnum.forEach(function (v) {
                            if (v.value == value.vbillstatus) {
                                if (value.vbillstatus == payDetailStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-default">'+v.context+'</span>';
                                }else if (value.vbillstatus == payDetailStatusMap.AFFIRM) {
                                    //已确认
                                    context = '<span class="label label-warning">'+v.context+'</span>';
                                } else if(value.vbillstatus == payDetailStatusMap.HAS_BEEN_WRITTEN_OFF){
                                    //已核销
                                    context = '<span class="label label-success">' + v.context + '</span>';
                                } else if(value.vbillstatus == payDetailStatusMap.APPLY){
                                    //已申请
                                    context = '<span class="label label-success">' + v.context + '</span>';
                                } else if(value.vbillstatus == payDetailStatusMap.REVIEW_PASS){
                                    //复核通过
                                    context = '<span class="label label-primary">' + v.context + '</span>';
                                }else{
                                    context = v.context;
                                }
                                return false;
                            }
                        });
                        return value.vbillno+ '<br />' +context;
                    }

                },
                // {
                //     title: '金额明细',
                //     align: 'left',
                //     field: 'transFeeCount',
                //     formatter: function (value, row, index) {
                //         // if (value === null) {
                //         //     return ;
                //         // }
                //         // return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //         let data=[];
                //         if(row.memo){
                //             if(row.transFeeCount){
                //                 data.push(`<span class="label label-primary pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="总金额">总额</span> `+row.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+ '<i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-placement="top" data-container="body" title="'+row.memo+'"></i>')
                //             }
                //         }else {
                //             if(row.transFeeCount){
                //                 data.push(`<span class="label label-primary pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="总金额">总额</span> `+row.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                //             }
                //         }
                //         if(row.gotAmount){
                //             data.push(`<span class="label badge-info pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="已付金额">已付</span> `+row.gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                //         }
                //         if(row.ungotAmount){
                //             data.push(`<span class="label label-coral pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="未付金额">未付</span> `+row.ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                //         }
                //         return data.join("<br/>")
                //     }
                //
                // },
                // {
                //     title: '总金额',
                //     align: 'right',
                //     field: 'transFeeCount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                //
                // },
                // {
                //     title: '已付金额',
                //     align: 'right',
                //     field: 'gotAmount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                // {
                //     title: '未付金额',
                //     align: 'right',
                //     field: 'ungotAmount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                {
                    title: '提货|到货日期',
                    field: 'reqDeliDate',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var res = ''
                        if(row.reqDeliDate == "" || row.reqDeliDate == null || row.reqDeliDate == 'undefined'){
                            row.reqDeliDate == "";
                            res += '-'
                        }else{
                            res += `<span class="label label-warning pa2">提</span>`+value.substring(0,10)

                        }
                        if(row.reqArriDate == "" || row.reqArriDate == null || row.reqArriDate == 'undefined'){
                            row.reqArriDate == "";
                            res += '<br/>-'
                        }else{
                            res +=`<br/><span class="label label-success pa2">到</span>`+row.reqArriDate.substring(0,10);
                        }
                        return res

                    }
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(row.deliAddr == null || row.deliAddr == ""){
                            row.deliAddr = '';
                        }
                        if(row.arriAddr == null || row.arriAddr.arriAddr == ""){
                            row.arriAddr = ''
                        }
                        if(row.deliAddr == "" && row.arriAddr == ""){
                            return "";
                        }else{
                            return `<span class="label label-warning pa2">提</span>`+row.deliAddr+`<br/><span class="label label-success pa2">到</span>`+row.arriAddr;
                        }

                    }
                },
                {
                    title: '金额明细',
                    field: 'freeType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var res = ''
                        if(row.memo){
                            if(row.transFeeCount){
                                res = row.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+ '<i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-placement="top" data-container="body" title="'+row.memo+'"></i>'
                            }
                        }else {
                            if(row.transFeeCount){
                                res =row.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
                            }
                        }
                        if(value == 0){
                            return  res+'<br />'+'<span class="label label-primary pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="费用类型">运费</span>'+$.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                        }
                        //在途费用类型
                        var costWay = row.costTypeOnWay;
                        // 异常费用
                        var flag2 = new Boolean(costWay == 18 || costWay == 17 || costWay == 14 || costWay == 13 ||
                            costWay == 12 || costWay == 10 || costWay == 9 || costWay == 8 || costWay == 6 ||
                            costWay == 5 || costWay == 3 || costWay == 21);
                        //在途登记费用
                        var flag1 = new Boolean(costWay == 16 || costWay == 15 || costWay == 7 || costWay == 4 ||
                            costWay == 2 || costWay == 1 || costWay == 22 || costWay == 20 );
                        if(value == 1 && flag1){
                            return res+'<br />' +'<span class="label label-warning pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="费用类型">在途</span>'+$.table.selectDictLabel(costTypeOnWay, row.costTypeOnWay);
                        }
                        if(value == 1 && flag2){
                            return res+'<br />' +'<span class="label label-coral pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="费用类型">异常</span>'+$.table.selectDictLabel(costTypeOnWay, row.costTypeOnWay);
                        }
                        if(value == 2){
                            return res+'<br />'+'<span class="label badge-info pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="费用类型">调整</span>' +'调整费';
                        }
                    }

                },
                // {
                //     title: '付款类型',
                //     field: 'costTypeOnWay',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         if (row.freeType === '0'){
                //             return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                //         }else if (row.freeType === '2')  {
                //             return '调整费'
                //         }
                //         return $.table.selectDictLabel(costTypeOnWay, value);
                //     }
                //
                // },
                // {
                //     title: '费用类型1',
                //     field: 'freeType',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         if(value == 0){
                //             return '<span>运费</label>'+'<br />'+$.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                //         }
                //         //在途费用类型
                //         var costWay = row.costTypeOnWay;
                //         // 异常费用
                //         var flag2 = new Boolean(costWay == 18 || costWay == 17 || costWay == 14 || costWay == 13 ||
                //             costWay == 12 || costWay == 10 || costWay == 9 || costWay == 8 || costWay == 6 ||
                //             costWay == 5 || costWay == 3 || costWay == 21);
                //         //在途登记费用
                //         var flag1 = new Boolean(costWay == 16 || costWay == 15 || costWay == 7 || costWay == 4 ||
                //             costWay == 2 || costWay == 1 || costWay == 22 || costWay == 20 );
                //         if(value == 1 && flag1){
                //             return '<span>在途费用</label>'+'<br />' +$.table.selectDictLabel(costTypeOnWay, row.costTypeOnWay);
                //         }
                //         if(value == 1 && flag2){
                //             return '<span>异常费用</label>'+'<br />' +$.table.selectDictLabel(costTypeOnWay, row.costTypeOnWay);
                //         }
                //         if(value == 2){
                //             return '<span>调整费用</label>调整费';
                //         }
                //     }
                //
                // },
                // {
                //     title: '付款类型',
                //     field: 'costTypeOnWay',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         if (row.freeType === '0'){
                //             return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                //         }else if (row.freeType === '2')  {
                //             return '调整费'
                //         }
                //         return $.table.selectDictLabel(costTypeOnWay, value);
                //     }
                //
                // },
                {
                    title: '要求支付日期',
                    align: 'left',
                    field: 'reqPayDate',
                    formatter: function status(row,value) {
                        if(value.reqPayDate != null){
                            var reqPayDate = new Date(Date.parse(value.reqPayDate));
                            value.reqPayDate.substr(0,10)
                            var now = new Date();
                            //如果要求支付日期小于当前时间,则标红
                            if (reqPayDate <= now) {
                                return '<span class="label label-danger">' + value.reqPayDate.substr(0,10) + '</span>';
                            } else {
                                return value.reqPayDate.substr(0,10);
                            }
                        }

                    }
                },
                {
                    title: '承运商/身份证',
                    align: 'left',
                    field: 'carrName',
                    formatter: function status(value, row, index) {
                        return value +'<br />'+getValue(row.legalCard)
                    }
                },
                // {
                //     title: '承运商身份证',
                //     align: 'left',
                //     field: 'legalCard'
                // },




                {
                    title: '油卡卡号',
                    align: 'left',
                    field: 'oilCardNumber'
                },
                {
                    title: '收款信息',
                    align: 'left',
                    field: 'recAccount',
                    formatter: function status(value, row, index) {
                        return getValue(value) + '/' + getValue(row.recCardNo)+'<br />'+getValue(row.recBank)
                    }
                },
                {
                    title: '申请信息',
                    align: 'left',
                    field: 'applyUser',
                    formatter: function status(value, row, index) {
                        return value + '&nbsp;&nbsp;' + getValue(row.applyTime)+'<br />'+getValue(row.applyMemo)
                    }
                },
                // {
                //     title: '申请时间',
                //     align: 'left',
                //     field: 'applyTime'
                // },
                // {
                //     title: '申请备注',
                //     align: 'left',
                //     field: 'applyMemo'
                // },
                {
                    title: '创建信息',
                    align: 'left',
                    field: 'regUserId',
                    formatter: function status(value, row, index) {
                        return getValue(value) +'<br />'+getValue(row.regDate)
                    }
                },
                // {
                //     title: '创建时间',
                //     align: 'left',
                //     field: 'regDate'
                // },




                // {
                //     title: '收款银行',
                //     align: 'left',
                //     field: 'recBank'
                // },
                // {
                //     title: '收款卡号',
                //     align: 'left',
                //     field: 'recCardNo'
                // },
                // {
                //     title: '备注',
                //     align: 'left',
                //     field: 'memo'
                // },
                // {
                //     title: '是否符合无车承运人',
                //     align: 'left',
                //     field: 'isNtocc',
                //     formatter: function status(value, row, index) {
                //         if (value === 0) {
                //             return '否';
                //         }
                //         return '是';
                //     }
                // },
                {
                    title: '结算公司/调度组',
                    align: 'left',
                    field: 'balaCorp',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(balaCorp, value) + '<br />'+getValue(row.params.transLineName);
                    }
                },
                // {
                //     title: '调度组',
                //     align: 'left',
                //     field: 'params.transLineName'
                // },
                {
                    title: '运营组',
                    align: 'left',
                    field: 'salesDeptName'
                },
                {
                    title: '运单号',
                    align: 'left',
                    field: 'lotno'
                },

                {
                    title: '是否为调整单',
                    align: 'left',
                    field: 'isAdjust',
                    formatter: function (value, row, index) {
                        if (value === 0) {
                            return '否';
                        }
                        return '是';
                    }
                },
                {
                    title: '调整原因',
                    align: 'left',
                    field: 'adjustMemo'
                },

            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });



        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#applyDateStart',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#applyDateEnd',
                type: 'date',
                trigger: 'click'
            });
        });

    });
    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }

    /**
     * 详情页
     */
    function detailTab() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 6) {
                $.modal.alertWarning("应付单据只能为已申请状态下才能确认");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应付单据");
                return;
            }
        }
        var rows = $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.openTab("应付单据详情", ctx + "payDetail/detailView/" + rows.join() );
    }

    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;
    /**
     * 结算公司
     */
    function openBalaCorp(id) {
        var url = ctx + "payDetail/balaCorp?payDetailId=" + id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "结算信息",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }


    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.salesDeptName = $.common.join($('#salesDeptName').selectpicker('val'));
        $.table.search('role-form', data);
    }



    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();

        //应付状态为已申请和部分核销
        var arr = [];
        arr.push(payDetailStatusEnum[6].value);
        $('#status').selectpicker('val',arr);
        searchPre();
    }

    /**
     * 复核
     */
    function review(){
        //应付明细id
        var payDetailId = $.table.selectColumns("payDetailId").join();
        //应付状态
        var vbillstatus = $.table.selectColumns("vbillstatus").join();
        if(vbillstatus != 6){
            $.modal.alertWarning("请选择已申请的应付单进行复核！");
            return;
        }
        var type = 'review';
        $.modal.openTab("应付明细", ctx + "payDetailCheck/check?payDetailId=" + payDetailId+"&type="+type);
    }


    /**
     * 复核
     */
    function reviewLot(){
        //应付明细id
        var payDetailId = $.table.selectColumns("payDetailId").join();
        //应付状态
        var vbillstatus = $.table.selectColumns("vbillstatus").join();
        if(vbillstatus != 6){
            $.modal.alertWarning("请选择已申请的应付单进行复核！");
            return;
        }
        var type = 'review';
        $.modal.openTab("应付明细", ctx + "payDetailCheck/checkByLotId?lotId=020a0419c9bc4292a3b9e059c502ff3d&type="+type);
    }



    function detail(payDetailId) {
        var type = 'payDetal';
        $.modal.openTab("应付明细", ctx + "payDetailCheck/check?payDetailId=" + payDetailId+"&type="+type);
    }



</script>
</body>
</html>