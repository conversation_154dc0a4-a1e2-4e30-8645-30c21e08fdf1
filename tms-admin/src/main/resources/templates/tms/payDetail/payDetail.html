<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse,.f7f7{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }

    .table-striped {
        height: calc(100% - 70px);
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .label-errorT{
        color: #1c84c6;
        background-color: yellow;
        border: 1px solid #1c84c6;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row no-gutter">
                    <div class="col-sm-6" style="padding: 0">
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">应付单状态：</label>-->
                                <div class="col-sm-12">
                                    <select name="status" id="status" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" data-none-selected-text="应付单状态" multiple>
                                        <option th:each="dict : ${payDetailStatusEnum}" th:text="${dict.context}"
                                                th:value="${dict.value}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">运单号：</label>-->
                                <div class="col-sm-12">
                                    <input name="lotno" id="lotno" class="form-control"
                                           placeholder="请输入运单号" maxlength="30">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">承运商名称：</label>-->
                                <div class="col-sm-12">
                                    <input name="carrName" id="carrName" placeholder="请输入承运商名称" class="form-control valid">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">司机名称：</label>-->
                                <div class="col-sm-12">
                                    <input name="driverName" id="driverName" class="form-control" placeholder="请输入司机名称"
                                           maxlength="30">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6" style="padding: 0">
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">应付单号：</label>-->
                                <div class="col-sm-12">
                                    <input name="vbillno" id="vbillno" th:value="${payDetail?.vbillno}" class="form-control"
                                           placeholder="请输入应付单号" maxlength="30">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">申请人：</label>-->
                                <div class="col-sm-12">
                                    <input name="applyUser"  class="form-control"
                                           placeholder="请输入申请人" maxlength="25">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">付款类型：</label>-->
                                <div class="col-sm-12">
                                    <select id="costTypeFreight" name="costTypeFreight" class="form-control noselect2 selectpicker" th:with="type=${@dict.getType('cost_type_freight')}"
                                            data-none-selected-text="付款类型" multiple>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">收款人：</label>-->
                                <div class="col-sm-12">
                                    <input name="recAccount"  class="form-control"
                                           placeholder="请输入收款人" maxlength="25">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row no-gutter">
                    <div class="col-sm-5" style="padding: 0">
                        <div style="padding-right: 0" class="col-md-6 col-sm-6">
                            <div class="form-group">
                                <!--                            <label class="col-sm-2">创建时间：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" style="width: 45%; float: left;" class="form-control"
                                           id="startDate"  name="startDate" placeholder="创建开始时间">
                                    <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                    <input type="text" style="width: 45%; float: left;" placeholder="创建结束时间" class="form-control"
                                           id="endtDate"  name="endtDate">
                                </div>
                            </div>
                        </div>
                        <div style="padding-right: 0" class="col-md-6 col-sm-6">
                            <div class="form-group">
                                <!--                            <label class="col-sm-2">申请时间：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" style="width: 45%; float: left;" placeholder="申请开始时间" class="form-control"
                                           id="applyDateStart"  name="params[applyDateStart]">
                                    <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                    <input type="text" style="width: 45%; float: left;" placeholder="申请结束时间" class="form-control"
                                           id="applyDateEnd"  name="params[applyDateEnd]">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-5" style="padding: 0">
                        <div class="col-md-4 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">调度组：</label>-->
                                <div class="col-sm-12">
                                    <select name="transLineId" id="transLineId"  class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" data-none-selected-text="调度组" multiple>
                                        <option th:each="dict : ${dispatcherDeptList}"
                                                th:text="${dict.deptName}"
                                                th:value="${dict.deptId}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">农行退回：</label>-->
                                <div class="col-sm-12">
                                    <select  name="bankBackFlag" class="form-control">
                                        <option value="">-- 银行退回 --</option>
                                        <option value="0">否</option>
                                        <option value="1">是</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select id="balaCorp" name="balaCorp" class="form-control selectpicker" data-none-selected-text="结算公司"
                                            th:with="type=${@dict.getType('bala_corp')}">
                                        <option value=""></option>
                                        <option th:each="dict : ${type}"
                                                th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="payWay" class="form-control selectpicker" data-none-selected-text="支付途径">
<!--                                        <option value="">&#45;&#45; 支付途径 &#45;&#45;</option>-->
                                        <option value=""></option>
                                        <option value="g7">G7</option>
                                        <option value="qx">琦欣</option>
                                        <option value="yl">银联</option>
                                        <option value="Y">预支</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <!--<a class="btn btn-primary" onclick="$.operate.addTab()" shiro:hasPermission="finance:payDetail:insert">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger" onclick="remove()" shiro:hasPermission="finance:payDetail:remove">
                <i class="fa fa-remove"></i> 删除
            </a>

            <a class="btn btn-primary" onclick="affirm()" shiro:hasPermission="finance:payDetail:affirm">
                <i class="fa fa-mail-reply"></i> 确认
            </a>-->

            <!--            <a class="btn btn-primary multiple disabled" onclick="detailTab()" shiro:hasPermission="finance:payDetail:detail">-->
            <!--                <i class="fa fa-newspaper-o"></i> 明细对账-->
            <!--            </a>-->
            <!-- <a class="btn btn-primary" onclick="oppositeAffirm()" shiro:hasPermission="finance:payDetail:oppositeAffirm">
                 <i class="fa fa-exclamation"></i> 反确认
             </a>-->

            <!-- <a class="btn btn-primary" onclick="checking()" shiro:hasPermission="finance:payDetail:checking">
                 <i class="fa fa-file-text-o"></i> 生成对账单
             </a>
             <a class="btn btn-primary" onclick="insertChecking()" shiro:hasPermission="finance:payDetail:insertChecking">
                 <i class="fa fa-file-text-o"></i> 加入对账单
             </a>
             <a class="btn btn-primary single disabled " onclick="adjust()" shiro:hasPermission="finance:payDetail:adjust">
                 <i class="fa fa-edit"></i> 调整
             </a>-->
            <!--            <a class="btn btn-primary single disabled" onclick="review()" shiro:hasAnyPermissions="finance:payDetail:review,finance:fleet:payDetail:review">-->
            <!--                <i class="fa fa-check"></i> 复核-->
            <!--            </a>-->
            <a class="btn btn-primary single disabled" onclick="batchPay()" shiro:hasPermission="finance:payDetail:batchPay">
                <i class="fa fa-file-text-o"></i> 分批付款
            </a>
            <a class="btn btn-warning multiple disabled" onclick="batchPayTogether()" shiro:hasPermission="finance:payDetail:batchPayTogether">
                <i class="fa fa-file-text-o"></i> 现金批量付款
            </a>
            <a class="btn btn-success" onclick="g7UPay()" shiro:hasPermission="finance:payDetail:batchPay">
                <i class="fa fa-money"></i> G7U盾支付
            </a>
            <a class="btn btn-danger" onclick="offlinePay()" shiro:hasPermission="finance:payDetail:batchPay">导出线下支付</a>
            <a class="btn btn-primary single disabled" onclick="payRecord()" shiro:hasPermission="finance:payDetail:payRecord">
                <i class="fa fa-calculator"></i> 付款记录
            </a>
            <a class="btn btn-danger single disabled" onclick="cancelReview()" shiro:hasPermission="finance:payDetail:cancelReview">
                <i class="fa fa-reply"></i> 反复核
            </a>

            <!--            <a class="btn btn-primary multiple disabled" onclick="oilBatchPay()" shiro:hasPermission="finance:payDetail:oilCardBatchPay">-->
            <!--                <i class="fa fa-file-text-o"></i> 油卡批量付款-->
            <!--            </a>-->

            <a class="btn btn-danger multiple disabled" onclick="cancelAppl()" shiro:hasPermission="finance:payDetail:cancelAppl">
                <i class="fa fa-reply"></i> 撤销申请
            </a>
            <!--            <a class="btn btn-primary single disabled" onclick="backWrite()"  shiro:hasPermission="finance:payCheckSheet:backWrite">-->
            <!--                <i class="fa fa-reply"></i> 油卡反核销-->
            <!--            </a>-->
            <a class="btn btn-danger single disabled" onclick="payBack()"  shiro:hasPermission="finance:payCheckSheet:payBack">
                <i class="fa fa-reply"></i> 付款反核销
            </a>


        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    var prefix = ctx + "payDetail";
    //应付单状态
    var payDetailStatusEnum = [[${payDetailStatusEnum}]];
    //应付单map
    var payDetailStatusMap = [[${payDetailStatusMap}]];
    //复核通过状态
    var reviewPassStatus = [[${reviewPassStatus}]];
    var collection_type = [[${@dict.getType('collection_type')}]];

    var transFeeCount = 0;
    var gotAmountCount = 0;
    var ungotAmountCount = 0;


    $(function () {
        var options = {
            url: prefix + "/listPayDetail",
            createUrl: prefix + "/add?carrierId="+$("#carrierId").val(),
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            // fixedColumns: true,
            rememberSelected: false,
            // fixedNumber: 3,
            height: 570,
            firstLoad: false,
            clickToSelect:true,
            showFooter:true,
            onPostBody:function () {
                //合并页脚
                merge_footer();
                //查询合计总金额
                getAmountCount();
            },
            onRefresh:function(params){
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
            },
            onCheck: function (row,$element) {
                onCheckbox(row);

                var transFee = row.transFeeCount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数加上本行数值
                transFeeCount = transFeeCount + transFee;
                gotAmountCount = gotAmountCount + gotAmount;
                ungotAmountCount = ungotAmountCount + ungotAmount;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheck: function (row, $element) {
                var transFee = row.transFeeCount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数减去本行数值
                transFeeCount = transFeeCount - transFee;
                gotAmountCount = gotAmountCount - gotAmount;
                ungotAmountCount = ungotAmountCount - ungotAmount;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onCheckAll: function (rowsAfter) {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                //循环累加
                for (var row of rowsAfter) {
                    transFeeCount = transFeeCount + row.transFeeCount;
                    gotAmountCount = gotAmountCount + row.gotAmount;
                    ungotAmountCount = ungotAmountCount + row.ungotAmount;
                }
                //赋值
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheckAll: function () {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },

            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "总金额合计：<nobr id='transFeeCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "已付金额合计：<nobr id='gotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "未付金额合计：<nobr id='ungotAmountCountTotal'>￥0</nobr><br>"+
                        "总合计:总金额合计：<nobr id='sumTransFeeCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "已付金额合计：<nobr id='sumGotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "未付金额合计：<nobr id='sumUngotAmountCountTotal'>￥0</nobr>";
                }
            },
                {
                    title: '操作',
                    align: 'left',
                    field: 'payDetailId',
                    formatter: function (value, row, index) {
                        var actions = [];
                        /*if ([[${@permission.hasPermi('finance:payDetail:edit')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="修改" onclick="edit(\'' + value + '\',\'' + row.vbillstatus + '\',\'' + row.isClose + '\')"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>');
                        }*/
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="结算公司" onclick="openBalaCorp(\'' + row.payDetailId + '\')"><i  class="fa fa-calculator" style="font-size: 15px;" ></i></a>');
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="detail(\'' + row.payDetailId + '\')"><i  class="fa fa-newspaper-o" style="font-size: 15px;" ></i></a>');

                        return actions.join('');
                    }
                },
                {
                    title: '结算公司/调度组',
                    align: 'left',
                    field: 'balaCorp',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(balaCorp, value) + '<br />'+getValue(row.params.transLineName);
                    }
                },
                // {
                //     title: '调度组',
                //     align: 'left',
                //     field: 'params.transLineName'
                // },
                {
                    title: '应付单号/应付单状态',
                    field: 'vbillno',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var result = value;

                        //return result

                        var context = '';
                        payDetailStatusEnum.forEach(function (v) {
                            if (v.value == row.vbillstatus) {
                                if (row.vbillstatus == payDetailStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-default">'+v.context+'</span>';
                                }else if (row.vbillstatus == payDetailStatusMap.AFFIRM) {
                                    //已确认
                                    context = '<span class="label label-warning">'+v.context+'</span>';
                                } else if(row.vbillstatus == payDetailStatusMap.HAS_BEEN_WRITTEN_OFF){
                                    //已核销
                                    context = '<span class="label label-success">' + v.context + '</span>';
                                } else if(row.vbillstatus == payDetailStatusMap.APPLY){
                                    //已申请
                                    context = '<span class="label label-success">' + v.context + '</span>';
                                    if(row.bankBackFlag == 1){
                                        context += '<span class="label label-danger" style="margin-left: 2px;" data-toggle="tooltip" data-html="true" title="'+row.opRetMsg+'">银退</span>';
                                    }
                                } else if(row.vbillstatus == payDetailStatusMap.REVIEW_PASS){
                                    //复核通过
                                    context = '<span class="label label-primary">' + v.context + '</span>';
                                    if (row.g7Pay == 3) {
                                        context += '<span class="label label-danger" style="margin-left: 2px;" data-toggle="tooltip" data-html="true" title="'+row.g7PayErr+'">g7退</span>'
                                    }
                                    if(row.bankBackFlag == 1){
                                        context += '<span class="label label-danger" style="margin-left: 2px;" data-toggle="tooltip" data-html="true" title="'+row.opRetMsg+'">银退</span>';
                                    }
                                }else{
                                    context = v.context;
                                }
                                return false;
                            }
                        });
                        let auditStatusTxt = ['待提审','审核中','审核通过','审核失败'];
                        if (row.lotG7End == 2) {
                            let et = '';
                            if (row.g7LotQst) {
                                et = row.g7LotQst + "；"
                            }
                            if (row.g7CarExt && row.g7Corp) {
                                let g7CarExt = JSON.parse(row.g7CarExt);
                                if (g7CarExt.ysz) {
                                    let auditStatus = g7CarExt.ysz[row.g7Corp.toLowerCase()];
                                    if (auditStatus != 2 && auditStatus != null) {
                                        let _st_t = auditStatusTxt[auditStatus]
                                        et = et + "车辆道路运输证【" + _st_t + "】；";
                                    }
                                }
                            }
                            if (row.g7DriverExt && row.g7Corp) {
                                let g7DriverExt = JSON.parse(row.g7DriverExt);
                                if (g7DriverExt.zgz) {
                                    let auditStatus = g7DriverExt.zgz[row.g7Corp.toLowerCase()];
                                    if (auditStatus != 2 && auditStatus != null) {
                                        let _st_t = auditStatusTxt[auditStatus]
                                        et = et + "司机从业资格证【" + _st_t + "】；";
                                    }
                                }
                            }
                            context += ' <span class="label '+ (et?'label-errorT':'label-success') +'" style="padding:1px;"';
                            if (et) {
                                context += ' data-toggle="tooltip" data-container="body" data-placement="right" data-html="true" title="' + et + '"';
                            }
                            context += '>G7</span>'
                            //context += ' <span class="label label-success" style="padding:1px;">G7</span>'
                        } else if (row.lotG7Syn != null) {
                            context += ' <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true" title="'
                            if (row.g7LotQst) {
                                context = context + row.g7LotQst + "；"
                            }
                            if (row.g7CarExt && row.g7Corp) {
                                let g7CarExt = JSON.parse(row.g7CarExt);
                                if (g7CarExt.ysz) {
                                    let auditStatus = g7CarExt.ysz[row.g7Corp.toLowerCase()];
                                    if (auditStatus != 2 && auditStatus != null) {
                                        let _st_t = auditStatusTxt[auditStatus]
                                        context = context + "车辆道路运输证【" + _st_t + "】；";
                                    }
                                }
                            }
                            if (row.g7DriverExt && row.g7Corp) {
                                let g7DriverExt = JSON.parse(row.g7DriverExt);
                                if (g7DriverExt.zgz) {
                                    let auditStatus = g7DriverExt.zgz[row.g7Corp.toLowerCase()];
                                    if (auditStatus != 2 && auditStatus != null) {
                                        let _st_t = auditStatusTxt[auditStatus]
                                        context = context + "司机从业资格证【" + _st_t + "】；";
                                    }
                                }
                            }
                            if (row.lotG7Syn == 0) {
                                context += '等待G7审验'
                            } else if (row.lotG7Syn == 1) {
                                context += row.lotG7Msg
                            } else if (row.lotG7Syn == 2) {
                                if (row.lotG7Start == null || row.lotG7Start == 0) {
                                    context += '等待推送【发车】'
                                } else if (row.lotG7Start == 1) {
                                    context += '【发车】推送失败'
                                } else if (row.lotG7End == null || row.lotG7End == 0) {
                                    context += '等待推送【到达】'
                                } else if (row.lotG7End == 1) {
                                    context += '【到达】推送失败'
                                }
                            } else if (row.lotG7Syn == 7) {
                                context += '运单已作废'
                            }
                            context += '">G7</span>'
                        }
                        var isMine = row.isMine;
                        if(isMine == 1){
                            context += ' <span class="label label-primary" title="琦欣" style="padding:1px 5px;">Q</span>'
                        }
                        if (row.payWay == 'Y') {
                            context += ' <span class="label label-warning" title="预支" style="padding:1px 5px;">Y</span>'
                        }

                        let lotLockPay = ''
                        if (row.lotLockPay && row.lotLockPay == 1) {
                            lotLockPay = `<span class="label label-danger ml5" style="padding:2px;" data-toggle="tooltip"
                                    data-container="body" data-placement="top" data-html="true" title="锁定应付"
                                    data-original-title="">锁</span>`

                        }
                        let carrLockPay = ''
                        if (row.carrLockPay && row.carrLockPay == 1) {
                            carrLockPay = `<span class="label label-danger ml5" style="padding:2px;" data-toggle="tooltip"
                                    data-container="body" data-placement="top" data-html="true" title="锁定承运商"
                                    data-original-title="">锁</span>`
                        }

                        return result + '<br />' + context + lotLockPay + carrLockPay;
                    }
                },
                // {
                //     title: '应付单状态',
                //     field: 'vbillstatus',
                //     align: 'left',
                //     formatter: function status(row,value) {
                //         var context = '';
                //         payDetailStatusEnum.forEach(function (v) {
                //             if (v.value == value.vbillstatus) {
                //                 if (value.vbillstatus == payDetailStatusMap.NEW) {
                //                     //新建
                //                     context = '<span class="label label-default">'+v.context+'</span>';
                //                 }else if (value.vbillstatus == payDetailStatusMap.AFFIRM) {
                //                     //已确认
                //                     context = '<span class="label label-warning">'+v.context+'</span>';
                //                 } else if(value.vbillstatus == payDetailStatusMap.HAS_BEEN_WRITTEN_OFF){
                //                     //已核销
                //                     context = '<span class="label label-success">' + v.context + '</span>';
                //                 } else if(value.vbillstatus == payDetailStatusMap.APPLY){
                //                     //已申请
                //                     context = '<span class="label label-success">' + v.context + '</span>';
                //                     if(value.bankBackFlag == 1){
                //                         context += '<span class="label label-danger" style="margin-left: 2px;">农退</span>';
                //                     }
                //                 } else if(value.vbillstatus == payDetailStatusMap.REVIEW_PASS){
                //                     //复核通过
                //                     context = '<span class="label label-primary">' + v.context + '</span>';
                //                     if (value.g7Pay == 3) {
                //                         context += '<span class="label label-danger" style="margin-left: 2px;" data-toggle="tooltip" data-html="true" title="'+value.g7PayErr+'">g7退</span>'
                //                     }
                //                 }else{
                //                     context = v.context;
                //                 }
                //                 return false;
                //             }
                //         });
                //
                //         return context;
                //     }
                //
                // },
                {
                    title: '申请信息',
                    align: 'left',
                    field: 'applyUser',
                    formatter: function status(value, row, index) {
                        return getValue(value) + '&nbsp;&nbsp;' + getValue(row.applyTime)+'<br />' + getValue(row.applyMemo)
                    }
                },
                {
                    title: '金额明细',
                    align: 'left',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        // if (value === null) {
                        //     return ;
                        // }
                        // return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        let data=[];
                        if(row.memo){
                            if(row.transFeeCount){
                                data.push(`<span class="label label-primary pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="总金额">总额</span> `+row.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+ '<i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-placement="top" data-container="body" title="'+row.memo+'"></i>')
                            }
                        }else {
                            if(row.transFeeCount){
                                data.push(`<span class="label label-primary pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="总金额">总额</span> `+row.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                            }
                        }
                        if(row.gotAmount){
                            data.push(`<span class="label badge-info pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="已付金额">已付</span> `+row.gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if(row.ungotAmount){
                            data.push(`<span class="label label-coral pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="未付金额">未付</span> `+row.ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        return data.join("<br/>")
                    }

                },
                // {
                //     title: '费用类型',
                //     field: 'freeType',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         if(value == 0){
                //             return '<span>运费</label>';
                //         }
                //         //在途费用类型
                //         var costWay = row.costTypeOnWay;
                //         // 异常费用
                //         var flag2 = costWay == 18 || costWay == 17 || costWay == 14 || costWay == 13 ||
                //             costWay == 12 || costWay == 10 || costWay == 9 || costWay == 8 || costWay == 6 ||
                //             costWay == 5 || costWay == 3 || costWay == 21;
                //         //在途登记费用
                //         var flag1 = costWay == 16 || costWay == 15 || costWay == 7 || costWay == 4 ||
                //             costWay == 2 || costWay == 1 || costWay == 22 || costWay == 20 ;
                //         if(value == 1 && flag1){
                //             return '<span>在途费用</label>';
                //         }
                //         if(value == 1 && flag2){
                //             return '<span>异常费用</label>';
                //         }
                //         if(value == 2){
                //             return '<span>调整费用</label>';
                //         }
                //     }
                //
                // },
                // {
                //     title: '付款类型',
                //     field: 'costTypeOnWay',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         if (row.freeType === '0'){
                //             return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                //         }else if (row.freeType === '2')  {
                //             return '调整费'
                //         }
                //         return $.table.selectDictLabel(costTypeOnWay, value);
                //     }
                //
                // },
                {
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == 0){
                            return '<span>运费</label>' + '<br />'+$.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                        }
                        //在途费用类型
                        var costWay = row.costTypeOnWay;
                        var costFreight = row.costTypeFreight;
                        let oilMsg = ''
                        if(costFreight == '1' || costFreight == '3' || costFreight == '5'){
                            oilMsg = '(油卡)'
                        }
                        // 异常费用
                        var flag2 = costWay == 18 || costWay == 17 || costWay == 14 || costWay == 13 ||
                            costWay == 12 || costWay == 10 || costWay == 9 || costWay == 8 || costWay == 6 ||
                            costWay == 5 || costWay == 3 || costWay == 21;
                        //在途登记费用
                        var flag1 = costWay == 16 || costWay == 15 || costWay == 7 || costWay == 4 ||
                            costWay == 2 || costWay == 1 || costWay == 22 || costWay == 20 || costWay == 36 ;
                        if(value == 1 && flag1){
                            return '<span>在途费用</label>'+ '<br />'+$.table.selectDictLabel(costTypeOnWay, row.costTypeOnWay)+oilMsg;
                        }
                        if(value == 1 && flag2){
                            return '<span>异常费用</label>'+ '<br />'+$.table.selectDictLabel(costTypeOnWay, row.costTypeOnWay)+oilMsg;
                        }
                        if(value == 2){
                            return '<span>调整费用</label>'+ '<br />调整费'+oilMsg;
                        }
                    }

                },



                // {
                //     title:'提货地址',
                //     align:'left',
                //     field:'deliAddr'
                // },
                // {
                //     title:'到货地址',
                //     align:'left',
                //     field:'arriAddr'
                // },

                // {
                //     title: '总金额',
                //     align: 'right',
                //     field: 'transFeeCount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                //
                // },
                // {
                //     title: '已付金额',
                //     align: 'right',
                //     field: 'gotAmount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                // {
                //     title: '未付金额',
                //     align: 'right',
                //     field: 'ungotAmount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },

                // {
                //     title: '油卡卡号',
                //     align: 'left',
                //     field: 'oilCardNumber',
                //
                // },
                {
                    title: '收款信息',
                    align: 'left',
                    field: 'recAccount',
                    formatter: function status(value, row, index) {
                       if(row.oilCardNumber!=null&&row.oilCardNumber!=undefined&&row.oilCardNumber!=''){
                           return row.oilCardNumber
                       }else{
                           return getValue(value) + '&nbsp;&nbsp;' + getValue(row.recCardNo)+ '<br />' + getValue(row.recBank)
                       }
                    }
                },
                {
                    title: '要求支付日期/账户类型',
                    align: 'left',
                    field: 'reqPayDate',
                    formatter: function status(row,value) {
                        if(value.reqPayDate != null){
                            var reqPayDate = new Date(Date.parse(value.reqPayDate));
                            value.reqPayDate.substr(0,10)
                            var now = new Date();
                            //如果要求支付日期小于当前时间,则标红
                            if (reqPayDate <= now) {
                                return '<span class="label label-danger">' + value.reqPayDate.substr(0,10) + '</span><br />' + $.table.selectDictLabel(collection_type, value.accountType-0);
                            } else {
                                return value.reqPayDate.substr(0,10)+ '<br />'+$.table.selectDictLabel(collection_type, value.accountType-0);
                            }
                        }

                    }
                },
                // {
                //     title: '账户类型',
                //     field: 'accountType',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         return $.table.selectDictLabel(collection_type, value-0)
                //     }
                //
                // },
                {
                    title: '承运商/身份证',
                    align: 'left',
                    field: 'carrName',
                    formatter: function status(value, row, index) {
                        return getValue(value) + '<br />' + getValue(row.legalCard)
                    }
                },

                // {
                //     title: '收款银行',
                //     align: 'left',
                //     field: 'recBank'
                // },
                // {
                //     title: '收款卡号',
                //     align: 'left',
                //     field: 'recCardNo'
                // },
                // {
                //     title: '申请人',
                //     align: 'left',
                //     field: 'applyUser'
                // },
                // {
                //     title: '申请时间',
                //     align: 'left',
                //     field: 'applyTime'
                // },
                {
                    title: '司机/电话',
                    align: 'left',
                    field: 'driverName',
                    formatter: function status(value, row, index) {
                        return  getValue(value) + '<br />' + getValue(row.driverMobile)
                    }
                },
                {
                    title: '创建人/时间',
                    align: 'left',
                    field: 'regUserId',
                    formatter: function status(value, row, index) {
                        return  getValue(value) + '<br />' + getValue(row.regDate)
                    }
                },

                // {
                //     title: '是否符合无车承运人',
                //     align: 'left',
                //     field: 'isNtocc',
                //     formatter: function status(value, row, index) {
                //         if (value === 0) {
                //             return '否';
                //         }
                //         return '是';
                //     }
                // },

                {
                    title: '运单号',
                    align: 'left',
                    field: 'lotno'
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(row.deliAddr == null || row.deliAddr == ""){
                            row.deliAddr = '';
                        }
                        if(row.arriAddr == null || row.arriAddr.arriAddr == ""){
                            row.arriAddr = ''
                        }
                        if(row.deliAddr == "" && row.arriAddr == ""){
                            return "";
                        }else{
                            return `<span class="label label-warning pa2">提</span>`+row.deliAddr+`<br/><span class="label label-success pa2">到</span>`+row.arriAddr;
                        }

                    }
                },
                {
                    title: '提货|到货日期',
                    field: 'reqDeliDate',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var res = ''
                        if(row.reqDeliDate == "" || row.reqDeliDate == null || row.reqDeliDate == 'undefined'){
                            row.reqDeliDate == "";
                            res += '-'
                        }else{
                            res += `<span class="label label-warning pa2">提</span>`+value.substring(0,10)

                        }
                        if(row.reqArriDate == "" || row.reqArriDate == null || row.reqArriDate == 'undefined'){
                            row.reqArriDate == "";
                            res += '<br/>-'
                        }else{
                            res +=`<br/><span class="label label-success pa2">到</span>`+row.reqArriDate.substring(0,10);
                        }
                        return res

                    }
                },
                // {
                //     title: '提货日期',
                //     align: 'left',
                //     field: 'reqDeliDate',
                //     formatter: function status(value, row, index) {
                //         if(value == "" || value == null || value == 'undefined'){
                //             return "";
                //         }
                //         return value.substring(0,10);
                //     }
                // },
                //
                // {
                //     title: '到货日期',
                //     align: 'left',
                //     field: 'reqArriDate',
                //     formatter: function status(value, row, index) {
                //         if(value == "" || value == null || value == 'undefined'){
                //             return "";
                //         }
                //         return value.substring(0,10);
                //     }
                // },

                // {
                //     title: '承运商身份证',
                //     align: 'left',
                //     field: 'legalCard'
                // },


                // {
                //     title: '申请备注',
                //     align: 'left',
                //     field: 'applyMemo'
                // },
                // {
                //     title: '备注',
                //     align: 'left',
                //     field: 'memo'
                // },
                {
                    title: '是否为调整单',
                    align: 'left',
                    field: 'isAdjust',
                    formatter: function (value, row, index) {
                        if (value === 0) {
                            return '否';
                        }
                        return '是';
                    }
                },
                {
                    title: '调整原因',
                    align: 'left',
                    field: 'adjustMemo'
                },
                {
                    title: '批次号',
                    align: 'left',
                    field: 'batchNo'
                },

            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        //应付状态为已申请和部分核销
        var arr = [];
        //arr.push(payDetailStatusEnum[6].value);
        arr.push(payDetailStatusEnum[9].value);
        $('#status').selectpicker('val',arr);

        let ctf = [[${costTypeFreight}]]
        if (ctf != null && ctf != '') {
            var a = [];
            let ctfList = ctf.split(',');
            for(var i =0 ;i<ctfList.length;i++){
                a.push(ctfList[i]);
            }
            $('#costTypeFreight').selectpicker('val',a);
        }

        searchPre();

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#applyDateStart',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#applyDateEnd',
                type: 'date',
                trigger: 'click'
            });
        });

    });
    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }

    /**
     * 详情页
     */
    function detailTab() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 6) {
                $.modal.alertWarning("应付单据只能为已申请状态下才能确认");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应付单据");
                return;
            }
        }
        var rows = $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.openTab("应付单据详情", prefix + "/detailView/" + rows.join() );
    }


    /**
     * 跳转应付修改页面
     * @param id
     */
    function edit(id,vbillstatus,isClose) {
        if (vbillstatus != 0) {
            $.modal.alertWarning("只能修改新建状态的应付单");
            return;
        }
        if (isClose === '1') {
            $.modal.alertWarning("该应付单已关账");
            return;
        }
        var url = prefix + "/edit?payDetailId=" + id;
        $.modal.openTab("应付明细修改", url);
    }
    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;

    /**
     * 结算公司
     */
    function openBalaCorp(id) {
        var url = prefix + "/balaCorp?payDetailId=" + id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "结算信息",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 调整
     */
    function  adjust() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["isClose"] === 0) {
            $.modal.alertWarning("请选择已关账的应付单据");
            return;
        }
        $.modal.openTab("调整", prefix + "/adjust?payDetailId="+bootstrapTable[0]["payDetailId"]);
    }
    /**
     * 确认应付明细
     */
    function affirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0) {
                $.modal.alertWarning("应付单据只能为新建状态下才能确认");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应付单据");
                return;
            }
        }
        var payDetailIds = $.table.selectColumns("payDetailId");
        if (payDetailIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("是否确认？", function () {
            $.operate.post(prefix + "/affirm", {"payDetailIds": payDetailIds.join()});
        });
    }

    /**
     * 跳转反确认页面
     */
    function oppositeAffirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("应付单据只能为已确认状态下才能反确认");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应付单据");
                return;
            }
        }

        var payDetailIds = $.table.selectColumns("payDetailId");
        if (payDetailIds.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.open("反确认", prefix + "/back_confirm/" + payDetailIds, 500, 300);
    }

    /**
     * 撤销申请
     */
    function cancelAppl(){
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 6) {
                $.modal.alertWarning("应付单据只能为已申请状态下才能撤销申请");
                return;
            }
            // if (bootstrapTable[i]["isClose"] === 1 ) {
            //     $.modal.alertWarning("请选择未在关账日期内的应付单据");
            //     return;
            // }
        }
        var payDetailIds = $.table.selectColumns("payDetailId");
        $.modal.confirm("确认要撤销申请选中的数据吗?", function () {
            $.operate.post(prefix + "/cancel_appl?payDetailIds=" + payDetailIds);
        });
    }


    /**
     * 生成对账单的方法
     */
    function checking() {

        var rows = $.table.selectColumns("payDetailId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var  isNtocc = bootstrapTable[0]["isNtocc"];

        for (var i = 0; i < bootstrapTable.length; i++) {
            /*if (bootstrapTable[i]["isNtocc"] !== isNtocc ) {
                $.modal.alertWarning("请选择同为符合或不符合无车承运人的应付单");
                return;
            }*/

            if (bootstrapTable[i]["vbillstatus"] !== 1 ) {
                $.modal.alertWarning("生成对账单的应付单据只能为已确认状态");
                return;
            }
        }

        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.openTab("生成对账单", prefix + "/checking?payDetailIds=" + rows.join());
    }


    /**
     * 加入对账单的方法
     */
    function insertChecking() {

        var rows = $.table.selectColumns("payDetailId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var  isNtocc = bootstrapTable[0]["isNtocc"];

        for (var i = 0; i < bootstrapTable.length; i++) {
            /*if (bootstrapTable[i]["isNtocc"] !== isNtocc ) {
                $.modal.alertWarning("请选择同为符合或不符合无车承运人的应付单");
                return;
            }*/
            if (bootstrapTable[i]["vbillstatus"] !== 1 ) {
                $.modal.alertWarning("加入对账单的应付单据只能为已确认状态");
                return;
            }

        }

        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var carrierId =  bootstrapTable[0]["carrierId"];
        $.modal.open("加入对账单", prefix + "/insertChecking?carrierId=" + carrierId + "&payDetailIds=" + rows + "&isNtocc=" + isNtocc);
    }

    /**
     * 批量删除
     */
    function remove() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("只能删除新建状态下的应付单据");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应付单据");
                return;
            }
        }
        var rows = $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要删除选中的数据吗?", function () {
            var url = prefix + "/remove";
            var data = {"ids": rows.join()};
            $.operate.submit(url, "post", "json", data);
        });
    }

    /**
     * 分批付款
     */
    function batchPay(){
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["vbillstatus"] !== 9 && bootstrapTable[0]["vbillstatus"] !== 3) {
            $.modal.alertWarning("请选择已复核/部分核销的应付单");
            return;
        }
        //总金额 == 已付金额 && vbillstatus == 已核销
        if (bootstrapTable[0]["transFeeCount"] === bootstrapTable[0]["gotAmount"] && bootstrapTable[0]["vbillstatus"] == 4) {
            $.modal.alertWarning("该应付单已完成");
            return;
        }
        //负数 加入对账单付款
        if(bootstrapTable[0]["transFeeCount"] < 0 ){
            $.modal.alertWarning("该应付单总金额为负数，请加入对账单付款");
            return;
        }
        var url = prefix + "/batchPay?payDetailId="+bootstrapTable[0]["payDetailId"];
        $.modal.open('分批付款',url);
    }

    /**
     * 合并付款
     */
    function batchPayTogether(){
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        var balaCorp = bootstrapTable[0]["balaCorp"];
        for(var i = 0;i<bootstrapTable.length;i++){
            if (bootstrapTable[i]["vbillstatus"] !== 9 && bootstrapTable[i]["vbillstatus"] !== 3) {
                $.modal.alertWarning("请选择已复核/部分核销的应付单");
                return;
            }
            //总金额 == 已付金额 && vbillstatus == 已核销
            if (bootstrapTable[i]["transFeeCount"] === bootstrapTable[i]["gotAmount"] && bootstrapTable[0]["vbillstatus"] == 4) {
                $.modal.alertWarning("该应付单已完成");
                return;
            }
            //负数 加入对账单付款
            /*if(bootstrapTable[i]["transFeeCount"] < 0 ){
                $.modal.alertWarning("该应付单总金额为负数，请加入对账单付款");
                return;
            }*/
            //判断结算公司
            if(bootstrapTable[i]["balaCorp"] != balaCorp){
                $.modal.alertWarning("请选择结算公司一致的应付单");
                return;
            }
            //只能勾选现金
            if(bootstrapTable[i]["costTypeFreight"] == '1' || bootstrapTable[i]["costTypeFreight"] == '3' || bootstrapTable[i]["costTypeFreight"] == '5'){
                $.modal.alertWarning("请选择现金应付单");
                return;
            }
        }
        var url = prefix + "/batchPayTogether?payDetailIds="+$.table.selectColumns('payDetailId');
        $.modal.open('现金批量付款',url);
    }

    /**
     * 油卡分批付款
     */
    function oilBatchPay(){
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["vbillstatus"] !== 9 && bootstrapTable[0]["vbillstatus"] !== 3) {
            $.modal.alertWarning("请选择已复核/部分核销的应付单");
            return;
        }
        if (bootstrapTable[0]["transFeeCount"] === bootstrapTable[0]["gotAmount"]) {
            $.modal.alertWarning("该应付单已完成");
            return;
        }
        //负数 加入对账单付款
        if(bootstrapTable[0]["transFeeCount"] < 0 ){
            $.modal.alertWarning("该应付单总金额为负数，请加入对账单付款");
            return;
        }
        //判断承运商 油卡号是否相同
        var oilCardNumber = bootstrapTable[0]['oilCardNumber'];//取第一条油卡号
        var carrierId = bootstrapTable[0]['carrierId'];//取第一条承运商id
        for(var i = 0;i<bootstrapTable.length;i++){
            if($.common.isEmpty(bootstrapTable[i]['oilCardNumber'])){
                $.modal.alertWarning("所选应付单油卡卡号不能为空");
                return ;
            }
            if(oilCardNumber !== bootstrapTable[i]['oilCardNumber']){
                $.modal.alertWarning("所选应付单油卡卡号不一致");
                return ;
            }
            if(carrierId !== bootstrapTable[i]['carrierId']){
                $.modal.alertWarning("所选应付单承运商不一致");
                return ;
            }
        }
        var payDetailIds = $.table.selectColumns('payDetailId');
        var url = prefix + "/oilCardBatchPay?payDetailIds="+payDetailIds;
        $.modal.open('油卡分批付款',url);
    }

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.status = $.common.join($('#status').selectpicker('val'));
        data.costTypeFreight = $.common.join($('#costTypeFreight').selectpicker('val'));
        data.params = new Map();
        data.params.transLineId = $.common.join($('#transLineId').selectpicker('val'));
        $.table.search('role-form', data);
    }


    //付款记录
    function payRecord() {
        var id = $.table.selectColumns('payDetailId');
        var url =  ctx + "payManage" + "/payRecord?payDetailId="+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "付款记录",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }
    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();

        //应付状态为已申请和部分核销
        var arr = [];
        arr.push(payDetailStatusEnum[6].value);
        $('#status').selectpicker('val',arr);
        searchPre();
    }

    /**
     * 复核
     */
    function review(){
        //应付明细id
        var payDetailId = $.table.selectColumns("payDetailId").join();
        //应付状态
        var vbillstatus = $.table.selectColumns("vbillstatus").join();
        if(vbillstatus != 6){
            $.modal.alertWarning("请选择已申请的应付单进行复核！");
            return;
        }
        var type = 'review';
        $.modal.openTab("应付明细", ctx + "payDetailCheck/check?payDetailId=" + payDetailId+"&type="+type);
    }

    /**
     * 反复核
     */
    function cancelReview(){
        //应付明细id
        var payDetailId = $.table.selectColumns("payDetailId").join();
        //应付状态
        var vbillstatus = $.table.selectColumns("vbillstatus").join();
        if(vbillstatus != reviewPassStatus){
            $.modal.alertWarning("请选择已复核通过的应付单进行反复核！");
            return;
        }
        $.modal.open("反复核", prefix + "/cancel_review?payDetailId=" + payDetailId,500,300);
    }

    /**
     * 跳转反确认页面
     */
    function backWrite() {
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["vbillstatus"] !== 3 && bootstrapTable[0]["vbillstatus"] !== 4 ) {
            $.modal.alertWarning("请选择已核销或部分核销的应付单");
            return;
        }
        if(bootstrapTable[0]["costTypeFreight"] != '1' && bootstrapTable[0]["costTypeFreight"] != '3' && bootstrapTable[0]["costTypeFreight"] != '5'){
            $.modal.alertWarning("请选择付款类型为油卡的应付单");
            return;
        }
        var payDetailId = $.table.selectColumns("payDetailId");
        $.modal.open("反核销", prefix + "/back_write/" + payDetailId,500,300);
    }

    function detail(payDetailId) {
        var type = 'payDetal';
        $.modal.openTab("应付明细", ctx + "payDetailCheck/check?payDetailId=" + payDetailId+"&type="+type);
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.status = $.common.join($('#status').selectpicker('val'));
        data.costTypeFreight = $.common.join($('#costTypeFreight').selectpicker('val'));
        data.params = new Map();
        data.params.transLineId = $.common.join($('#transLineId').selectpicker('val'));
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $("#sumTransFeeCountTotal").text(data.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumGotAmountCountTotal").text(data.gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumUngotAmountCountTotal").text(data.ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    function payBack(){
        var payDetailId = $.table.selectColumns("payDetailId");
        if (payDetailId.length != 1 ) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        var vbillstatus = $.table.selectColumns("vbillstatus");
        if(vbillstatus != 3 && vbillstatus != 4){
            $.modal.alertWarning("请选择已核销或部分核销的应付单");
            return;
        }
        $.modal.open("支付反核销", prefix + "/back_pay/" + payDetailId,600,500);
    }

    function g7UPay() {
        $.modal.openOptions({
            title: "G7U盾统一支付",
            url: prefix + "/g7UPay",
            width: 900,
            height: 600,
            btn: ['关闭'],
            callBack: function (index, layero) {
                //var iframeWin = layero.find('iframe')[0];
                //iframeWin.contentWindow.submitHandler(index, layero);
                layer.close(index)
            }
        });
    }

    function offlinePay() {
        if ($("[name='payWay']").val() != 'yl') {
            $.modal.msgWarning("支付途径请选择银联");
            return
        }
        $.modal.confirm("确定导出吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            var search = $.common.formToJSON("role-form");
            search.status = $.common.join($('#status').selectpicker('val'));
            search.costTypeFreight = $.common.join($('#costTypeFreight').selectpicker('val'));
            search.params = new Map();
            search.params.transLineId = $.common.join($('#transLineId').selectpicker('val'));
            $.post(prefix + "/offlinePayExport", search, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    function onCheckbox(row) {
        var data = $.btTable.bootstrapTable('getData');
        data.forEach((res,i)=>{
            if(!res["0"]){
                if(row.batchNo != null && res.batchNo==row.batchNo){
                    $.btTable.bootstrapTable('check', i);
                }
            }
        })
    }
</script>
</body>
</html>