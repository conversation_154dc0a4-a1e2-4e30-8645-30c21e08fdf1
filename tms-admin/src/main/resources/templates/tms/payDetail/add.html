<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细新增')"/>
</head>

<body>
<div class="form-content">
    <form id="form-payDetail-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5" style="color: red; ">
                                        运单号：</label>
                                    <div class="col-sm-7">
                                        <div class="input-group">
                                        <input name="lotno" id="lotno" class="form-control valid" type="text"
                                               onclick="selectLot()" required maxlength="25" readonly> <span
                                            class="input-group-addon"><i
                                            class="fa fa-search" onclick="selectLot()"></i></span>
                                        </div>
                                        <input name="invoiceVbillno" type="hidden" id="invoiceVbillno">
                                        <input name="lotId" type="hidden" id="lotId">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        承运商：</label>
                                    <div class="col-sm-7">
                                        <input name="carrName" id="carrName" type="text"
                                               class="form-control dis" disabled >
                                        <input name="carrierId" type="hidden" id="carrierId">
                                        <input name="carrCode" type="hidden" id="carrCode">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        司机：</label>
                                    <div class="col-sm-7">
                                        <input name="driverName" id="driverName" class="form-control dis" type="text"
                                               maxlength="30" disabled>
                                        <input name="driverMobile" type="hidden" id="driverMobile">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        结算公司：</label>
                                    <div class="col-sm-7">
                                        <input name="balaCorpName" id="balaCorpName" class="form-control dis" type="text"
                                               maxlength="30" disabled>
                                        <input name="balaCorp" id="balaCorp" class="form-control dis" type="hidden"
                                               maxlength="30" >
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        提货日期：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class=" form-control dis"
                                               name="reqDeliDate" id="reqDeliDate" autocomplete="off" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        到货日期：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control dis"
                                               name="reqArriDate" id="reqArriDate" autocomplete="off" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        油卡卡号：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control "  maxlength="25"
                                               autocomplete="off" id="oilCardNumber" name="oilCardNumber">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        收款人：</label>
                                    <div class="col-sm-7">
                                        <input name="recAccount" id="recAccount" class="form-control " type="text"
                                               maxlength="10"  autocomplete="off">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        收款银行：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control " maxlength="25"
                                               autocomplete="off" id="recBank" name="recBank">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        收款卡号：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control "  maxlength="25"
                                               autocomplete="off" name="recCardNo" id="recCardNo">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">费用类型：</label>
                                    <div class="col-sm-7">
                                        <select name="costTypeOnWay" id="costTypeOnWay" class="form-control valid"
                                                th:with="type=${@dict.getType('cost_type_on_way')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5" style="color: red; ">
                                        费用金额：</label>
                                    <div class="col-sm-7">
                                        <input name="transFeeCount" id="transFeeCount" class="form-control"
                                               type="text"  oninput="$.numberUtil.onlyNumberNegative(this)"  required maxlength="10">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-3">备注：</label>
                                    <div class="col-sm-12">
                                            <textarea name="memo" id="memo" maxlength="250" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--基础信息 end-->
                    </div>
                </div>
            </div>


        </div>

        <div class="panel panel-default">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFour">地址信息</a>

                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseFour">

                    <div class="panel-body">
                        <!-- begin-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabThree" class="custom-tab table">
                                <thead>
                                <tr>

                                    <th style="width: 10%;">提货地省</th>
                                    <th style="width: 10%;">提货地市</th>
                                    <th style="width: 10%;">提货地区</th>
                                    <th style="width: 10%;">到货地省</th>
                                    <th style="width: 10%;">到货地市</th>
                                    <th style="width: 10%;">到货地区</th>

                                </tr>
                                </thead>
                                <tbody >
                                <tr>
                                    <td id="deliProvince"></td>
                                    <td id="deliCity"></td>
                                    <td id="deliArea"></td>
                                    <td id="arriProvince"></td>
                                    <td id="arriCity"></td>
                                    <td id="arriArea"></td>

                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--end-->
                    </div>

                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFive">运力信息</a>

                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseFive">

                    <div class="panel-body">
                        <!-- begin-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabFour" class="custom-tab table">
                                <thead>
                                <tr>

                                    <th style="width: 10%;">车牌号</th>
                                    <th style="width: 10%;">车长</th>
                                    <th style="width: 10%;">车型</th>

                                </tr>
                                </thead>
                                <tbody >
                                <tr>

                                    <td id="carno"></td>
                                    <td id="carLenName"></td>
                                    <td id="carTypeName"></td>

                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--end-->
                    </div>

                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseSix">异常事故</a>

                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseSix">

                    <div class="panel-body">
                        <!-- begin-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabFive" class="custom-tab table">
                                <thead>
                                <tr>

                                    <th style="width: 10%;">异常分类</th>
                                    <th style="width: 10%;">跟踪信息</th>
                                    <th style="width: 10%;">异常发生时间</th>
                                    <th style="width: 10%;">预计到达时间</th>


                                </tr>
                                </thead>
                                <tbody id="exp">
                                <tr>


                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--end-->
                    </div>

                </div>
            </div>
        </div>

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="commit()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">
    var prefix = ctx + "payDetail";
    $(function () {

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');

        var options = {};
        $.table.init(options);

        $("#form-payDetail-add").validate({
            onkeyup: false,
            focusCleanup: true
        });
        //油卡添加校验
        $("#oilCardNumber").rules("add", {
            remote: {
                url: ctx + "basic/fuelCard/checkOilCardNumber",
                type: "post",
                dataType: "json",
                data: {
                    fuelcardNo : function() {
                        return $.common.trim($("#oilCardNumber" ).val());
                    }
                },
                dataFilter: function(data, type) {
                    return data > 0;
                }
            },
            messages: {
                remote: "无效油卡",
            }
        });
    });
    var carrierId = [[${carrierId}]];
    /**
     * 运单选择框
     */
    function selectLot() {

        $.modal.open("选择运单", prefix + "/chooseLot?carrierId="+carrierId);
    }

    /**
     * 表单提交
     */
    function commit(){
        if ($.validate.form()) {
            $('.dis').removeAttr("disabled");
            // 表单提交
            $.operate.saveTab(prefix + "/addPayDetail", $('#form-payDetail-add').serialize());
        }
    }

</script>
</body>

</html>