<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商应付列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">承运商名称：</label>
                            <div class="col-sm-7">
                                <input name="carrName" id="carrName" class="form-control" type="text" placeholder="请输入承运商名称"
                                       maxlength="30"  aria-required="true">
                                <input id="hiddenText" type="text" style="display:none" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group"></div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group"></div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "carrierPay";

    $(function () {
        var options = {
            url: prefix + "/list",
            showToggle: false,
            showColumns: true,
            modalName: "承运商应付",
            rememberSelected: true,
            uniqueId: "carrierId",
            clickToSelect:true,
            columns: [
                {
                    title: '操作',
                    align: 'left',
                    field: 'carrierId',
                    formatter: function (value,row,index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="detail(\'' + value + '\')"><i  class="fa fa-newspaper-o" style="font-size: 15px;" ></i></a>');
                        return actions.join('');
                    }

                },
                {
                    title: '承运商名称',
                    align: 'left',
                    field: 'carrName'

                },

                {
                    title: '承运商编码',
                    align: 'left',
                    field: 'carrCode'
                },

                {
                    title: '总金额',
                    align: 'right',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '已付金额',
                    align: 'right',
                    field: 'gotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '总条数',
                    align: 'left',
                    field: 'size'
                },

                /*{
                   title: '未付金额',
                   align: 'right',
                   field: 'ungotAmount',
                   formatter: function (value, row, index) {
                       if (value === null) {
                           return ;
                       }
                       return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                   }
               }*/
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });


</script>
<script>


    /**
     * 跳转应付明细
     * @param carrierId 承运商ID
     */
    function detail(carrierId) {
        var url = ctx + "payDetail?carrierId="+carrierId;

        $.modal.openTab("承运商应付明细", url);
    }



</script>

</body>
</html>