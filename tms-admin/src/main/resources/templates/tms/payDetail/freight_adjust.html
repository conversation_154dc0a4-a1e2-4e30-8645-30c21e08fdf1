<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('运费调整')"/>
</head>
<body>
<div class="form-content">
    <form id="form-freightAdjust" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFive">付款信息</a>
                    </h4>
                </div>
                <div id="collapseFive" class="panel-collapse collapse in">
                    <input type="hidden" name="payDetailId" th:value="${payDetailId}">
                    <input type="hidden" id="carrierId" th:value="${carrierId}">
                    <div class="panel-body">
                        <!-- 付款信息-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 8%;">付款类型</th>
                                    <th style="width: 10%;">调整前金额</th>
                                    <th style="width: 10%;">调整后金额</th>
                                    <th style="width: 15%;">油卡号</th>
                                    <th style="width: 20%;">备注</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="dict,stat : ${@dict.getType('cost_type_freight')}"
                                    th:id="row+${dict.dictValue}">
                                    <td>
                                        <div th:text="${dict.dictLabel}"></div>
                                        <input type="hidden" th:id="|costTypeFreight_${stat.index}|" th:name="|payDetailList[${stat.index}].costTypeFreight|" th:value="${dict.dictValue}">
                                    </td>
                                    <td>
                                        <div class="col-md-12">
                                            <span style="color: #e99535" th:if="${dict.dictValue} == ${costTypeFreight}">[[${transFeeCount}]]</span>
                                            <span style="color: #e99535" th:if="${dict.dictValue} != ${costTypeFreight}">0</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="col-md-12">
                                            <input th:name="|payDetailList[${stat.index}].transFeeCount|" th:id="|transFeeCount_${stat.index}|" placeholder=""
                                                   class="form-control" type="text" oninput="$.numberUtil.onlyNumber(this);calculateTotal()"
                                                   autocomplete="off">
                                        </div>
                                    </td>
                                    <td style="border-right: 1px solid #e7eaec">
                                        <div class="col-md-12">
                                            <input th:name="|payDetailList[${stat.index}].oilCardNumber|" th:id="|oilCardNumber_${stat.index}|" placeholder=""
                                                   class="form-control" type="text" maxlength="20" autocomplete="off"
                                                   th:disabled="${dict.dictValue == '0' or dict.dictValue == '2' or dict.dictValue == '4'}">
                                            <input th:if="${stat.index!=0}" th:name="|payDetailList[${stat.index}].memo|" th:id="|memo_${stat.index}|" class=" form-control" type="hidden" />
                                        </div>
                                    </td>
                                    <td th:rowspan="${stat.size}" th:if="${stat.index==0&&type==1}">
                                        <textarea th:name="|payDetailList[${stat.index}].memo|" th:id="|memo_${stat.index}|" class=" form-control" maxlength="200" th:rows="${stat.size}+6" placeholder="添加备注"></textarea>
                                    </td>
                                    <td rowspan="3" th:if="${stat.index==0&&type==0}">
                                        <textarea th:name="|payDetailList[${stat.index}].memo|" th:id="|memo_${stat.index}|" class=" form-control" maxlength="200" rows="4" placeholder="添加备注"></textarea>

                                    </td>

                                </tr>
                                <tr>
                                    <td>总金额:</td>
                                    <td style="color:#ff8b1b;font-size:15px">[[${transFeeCount}]]</td>
                                    <td style="color:#ff8b1b;font-size:15px" id="calculateTotal">0</td>
<!--                                    <td style="text-align:left" th:each="dict : ${@dict.getType('cost_type_freight')}" th:if="${dict.dictValue} == ${costTypeFreight}" th:text="|原先付款类型：${dict.dictLabel}|"></td>-->
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--付款信息 end-->
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<!--<div class="row">-->
<!--    <div class="col-sm-offset-5 col-sm-10">-->
<!--        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保-->
<!--            存</button>&nbsp;-->
<!--        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭-->
<!--        </button>-->
<!--    </div>-->
<!--</div>-->

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "payDetail";
    var transFeeCountOld = [[${transFeeCount}]];

    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];//费用类型

    var costTypeFreightValue = [[${costTypeFreight}]];
    console.log('costTypeFreight',costTypeFreight)
    //按钮类型
    var type = [[${type}]];
    console.log(type)

    $(function () {
        $('#collapseFive').collapse('show');


        //油卡 只显示油卡
        if((costTypeFreightValue == 1 || costTypeFreightValue == 3 || costTypeFreightValue == 5) && type == 0){
            $("#row0").hide();
            $("#row2").hide();
            $("#row4").hide();
        }
        //现金 只显示现金
        if((costTypeFreightValue == 0 || costTypeFreightValue == 2 || costTypeFreightValue == 4) && type == 0){
            $("#row1").hide();
            $("#row3").hide();
            $("#row5").hide();
        }
        //油卡添加校验
        for (var i = 0; i < costTypeFreight.length; i++) {
            addOilCardNumberCheck(i);
        }
    });
    $("#form-freightAdjust").validate({});

    function addOilCardNumberCheck(ind) {
        //油卡添加校验
        $("#oilCardNumber_"+ind).rules("add", {
            remote: {
                url: ctx + "basic/fuelCard/checkOilCardNumber",
                type: "post",
                dataType: "json",
                data: {
                    fuelcardNo : function() {
                        return $.common.trim($("#oilCardNumber_" + ind).val());
                    },
                    carrierId : function () {
                        return $("#carrierId").val();
                    }
                },
                dataFilter: function(data, type) {
                    if (data > 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
            },
            messages: {
                remote: "无效油卡",
            }
        });
    }


    /**
     * 计算合计
     */
    function calculateTotal() {
        var transFeeCountTotal = 0;
        $("[id^=transFeeCount_]").not("[id$=-error]").each(function () {
            var val = $(this).val().replace(/\s+/g, "") == "" ? 0 : $(this).val();
            transFeeCountTotal = decimal(parseFloat(transFeeCountTotal) + parseFloat(val), 5);
        });

        if (transFeeCountTotal == transFeeCountOld) {
            $("#calculateTotal").css("color", "#1ab293");

        } else {
            $("#calculateTotal").css("color", "ff8b1b");
        }
        $("#calculateTotal").text(transFeeCountTotal + "/" + transFeeCountOld);
    }

    /**
     * 提交
     */
    function submitHandler() {
        for(var i=0;i<costTypeFreight.length;i++){
            if(type==1){
                if(i!=0){
                    $('#memo_'+i).val($('#memo_0').val())
                }
            }else{
                if(i==2||i==4){
                    $('#memo_'+i).val($('#memo_0').val())
                }
            }
        }
        if ($.validate.form()) {
            //校验是否有相同油卡
            var hash = {};
            var isSameOil = false;
            $("[id^=oilCardNumber_]").not("[id$=-error]").each(function () {
                var v = $(this).val();
                if(v != "" && hash[v]){
                    isSameOil = true;
                    return true;
                }
                hash[v] = true;
            });
            if (isSameOil) {
                $.modal.msgError("油卡号不能相同！");
                return;
            }

            //判断金额是否相等
            var transFeeCountTotal = 0;
            $("[id^=transFeeCount_]").not("[id$=-error]").each(function () {
                var val = $(this).val().replace(/\s+/g, "") == "" ? 0 : $(this).val();
                transFeeCountTotal = decimal(parseFloat(transFeeCountTotal) + parseFloat(val), 5);
            });
            if (transFeeCountTotal != transFeeCountOld) {
                $.modal.msgError("金额不相等，无法提交！");
                return;
            }

            var data = $("#form-freightAdjust").serializeArray();
            //console.log(data)
            //$.operate.saveTab(prefix + "/freightAdjust", data);
            $.operate.saveTab(prefix + "/freightAdjust", data,function (result){
                let isRefresh = [[${isRefresh}]]
                if (isRefresh) {
                    if (result.code == web_status.SUCCESS) {
                        parent.location.reload();
                    }
                }
                $.operate.successCallback(result);
            });
        }
    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }

</script>
</body>

</html>