<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('申请付款')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: jasny-bootstrap-css" />
</head>
<style>
    .fw{
        font-weight: bold;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 120px;
        line-height: 26px;
        text-align: right;
        color: #808080;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 26px;
    }
    .fcff3{
        color: #ff3636;
    }
    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        /*height: 100px !important;
        border: 1px #dadada dashed;*/
        min-height: 80px;
        height: auto;
        overflow: auto;
        margin: 0;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .addbtn{
        /*width: 100px;*/
        text-align: center;
        color: #fff;
        background: #1ab394;
        line-height: 30px;
        border-radius: 5px;
        cursor: pointer;
    }
    .mt10{
        margin-top: 10px;
    }
    label.error {
        top: 25px !important;
        left: 430px;
    }
    /*父页面宽度不达768px，样式不生效修复*/
    .col-sm-4 {
        position: relative;
        width: 33.3333%;
        float: left;
    }
    .col-sm-3 {
        position: relative;
        width: 25%;
        float: left;
    }
    .col-sm-11 {
        position: relative;
        width: 91.66666667%;
        float: left;
    }
    .col-sm-1 {
        position: relative;
        width: 8.33333333%;
        float: left;
    }
    .col-sm-6 {
        position: relative;
        width: 50%;
        float: left;
    }
    .col-sm-7 {
        position: relative;
        width: 58.33333333%;
        float: left;
    }
    .col-sm-1 {
        position: relative;
        width: 8.33333333%;
        float: left;
    }
</style>
<body>
<div class="form-content">
    <form id="form-payDetail-add" class="form-horizontal" novalidate="novalidate">
        <!--应付明细id-->
        <input type="hidden" name="payDetailId" th:value="${payDetailId}">
        <input type="hidden" name="lotId" th:value="${payDetail.lotId}">

        <div class="panel-body">
            <div class="row">
                <div class="col-sm-6">
                    <div>
                        <div class="flex">
                            <label class="flex_left"><span class="fcff3">*</span>要求支付日期：</label>
                            <div class="flex_right">
                                <input type="text" class=" form-control dis"
                                       name="reqPayDate" id="reqPayDate" autocomplete="off" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div>
                        <div class="flex">
                            <label class="flex_left">申请金额：</label>
                            <div class="flex_right" th:text="${batchAmount}"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt10">
                <div class="col-sm-6">
                    <div class="flex">
                        <label class="flex_left">
                            账户类型：</label>
                        <div class="flex_right">
                            <select name="accountType" id="accountType" class="form-control valid"  th:with="type=${@dict.getType('collection_type')}" onchange="accountTypeChange()" >
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
              <!--  <div class="col-sm-6" id="freight">
                    <div class="flex">
                        <label class="flex_left">付款类型：</label>
                        <div class="flex_right">
                            <select name="costTypeFreight" id="costTypeFreight" class="form-control valid" disabled
                                    th:with="type=${@dict.getType('cost_type_freight')}" onchange="typeChange()">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}" th:field="${payDetail.costTypeFreight}"></option>
                            </select>
                        </div>
                    </div>
                </div>-->
            </div>
            <div class="row mt10">
                <div class="col-sm-6">
                    <div class="col-sm-11" style="padding: 0">
                        <div class="flex">
                            <label class="flex_left"><span class="fcff3" style="display: none">*</span>
                                收款人：</label>
                            <div class="flex_right">
                                <select name="carrBankId" id="carrBankId" class="form-control valid" onchange="bankChange()">
                                    <option value="">--请选择--</option>
                                    <option th:each="dict : ${carrBankList}" th:text="${dict.bankAccount}"
                                            th:value="${dict.carrBankId}" th:field="${payDetail.carrBankId}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-1">
                        <div class="fa fa-plus-square" style="color: #1ab394;line-height: 30px;font-size: 18px;" onclick="addReceiver()"></div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div >
                        <div class="flex">
                            <label class="flex_left color">
                                油卡卡号：</label>
                            <div class="flex_right">
                                <input type="text" class="form-control dis"  maxlength="25"
                                       autocomplete="off" id="oilCardNumber" name="oilCardNumber"
                                       th:value="${payDetail.oilCardNumber}" >
                                <input id="historyCardNumber" name="historyCardNumber" type="text"
                                       th:value="${payDetail.oilCardNumber}" style="display:none" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

<!--                        <div class="mt10" id="writeFuelcardDiv" style="display: none">-->
<!--                            <div style="width: 400px">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">-->
<!--                                        核销油卡：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <select name="writeFuelcardId" id="writeFuelcardId" class="form-control valid" >-->
<!--                                            <option value="">&#45;&#45;请选择&#45;&#45;</option>费用类型-->
<!--                                            <option th:each="fuelcard : ${fuelcardList}" th:text="${fuelcard.fuelcardNo}" th:value="${fuelcard.fuelcardId}"></option>-->
<!--                                        </select>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->

            <div class="row mt10">
                <div class="col-sm-6">
                    <div class="flex">
                        <label class="flex_left"><span class="fcff3" style="display: none">*</span>
                            收款银行：</label>
                        <div class="flex_right">
                            <input id="recBank" name="recBank" class="form-control dis" type="text" th:value="${payDetail.recBank}" disabled>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="flex">
                        <label class="flex_left"><span class="fcff3" style="display: none">*</span>
                            收款卡号：</label>
                        <div class="flex_right">
                            <input id="recCardNo" name="recCardNo" class="form-control dis" type="text" th:value="${payDetail.recCardNo}" disabled>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt10" style="display: none">
                <div class="col-sm-6" id="free">
                    <div class="flex">
                        <label class="flex_left">费用类型：</label>
                        <div class="flex_right">
                            <select name="costTypeOnWay" id="costTypeOnWay" class="form-control valid" disabled
                                    th:with="type=${@dict.getType('cost_type_on_way')}" >
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}" th:field="${payDetail.costTypeOnWay}"></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt10" th:if="${taxTxt != null}">
                <div class="col-sm-12">
                    <div class="flex">
                        <label class="flex_left">含税构成：</label>
                        <div class="flex_right">
                            <span id="taxTxtView"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt10" th:if="${taxTxt != null}">
                <div class="col-sm-12">
                    <div class="flex">
                        <label class="flex_left">上传发票：</label>
                        <div class="flex_right">
                            <input id="receipt" class="form-control" name="receipt" type="file" multiple>
                            <input id="tid_receipt" name="tidReceipt" type="hidden">
                        </div>
                    </div>
                </div>
            </div>
            <!--<div class="row mt10" th:each="map:${payDetailList}">
                <div class="col-sm-6"  th:if="${map.taxTxt != null}">
                    <div class="flex">
                        <label class="flex_left">含税构成：</label>
                        <div class="flex_right">
                            <span id="taxTxtView"></span>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div>
                        <div class="flex">
                            <label class="flex_left">上传发票：</label>
                            <div class="flex_right">
                                <input th:id="receipt+${map.payDetail.payDetailId}" class="form-control" th:name="receipt+${map.payDetail.payDetailId}" type="file" multiple>
                                <input th:id="tidReceipt+${map.payDetail.payDetailId}" th:name="tidReceipt+${map.payDetail.payDetailId}" type="hidden">
                            </div>
                        </div>
                    </div>
                </div>
            </div>-->

            <div class="row mt10">
                <div class="col-sm-12">
                    <div class="flex">
                        <label class="flex_left">支付凭证：</label>
                        <div class="flex_right">
                            <input id="image" class="form-control" name="image" type="file" multiple>
                            <input id="tid" name="tid" type="hidden">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt10">
                <div class="col-sm-12">
                    <div class="flex">
                        <label class="flex_left">申请备注：</label>
                        <div class="flex_right">
                                            <textarea name="applyMemo" maxlength="250"
                                                      class="form-control valid" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div th:if="${profit < 0}" class="row mt10">
                <div class="col-sm-12">
                    <div class="flex">
                        <label class="flex_left">亏本金额：</label>
                        <div class="flex_right" th:text="${profit}"></div>
                    </div>
                </div>
            </div>
            <div th:if="${profit < 0}" class="row mt10">
                <div class="col-sm-12">
                    <div class="flex">
                        <label class="flex_left"><span class="fcff3">*</span> 亏本原因：</label>
                        <div class="flex_right">
                            <textarea name="lossReason" th:text="${lossReason}" maxlength="250" class="form-control" rows="3" required></textarea>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <input type="hidden" name="taxTxt">
    </form>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">
    var prefix = ctx + "payDetail";

    var carrBankList = [[${carrBankList}]];
    var specialCarrBankList = [[${specialCarrBankList}]];
    var oilCarrBankList = [[${oilCarrBankList}]];
    var oilPayCarrBankList = [[${oilPayCarrBankList}]];
    var exceptionPayCarrBanks = [[${exceptionPayCarrBanks}]];
    var payDetailList = [[${payDetailList}]];


    $(function () {
        $('#collapseOne').collapse('show');
        var costTypeFreight = [[${payDetail.costTypeFreight}]];
        if (costTypeFreight === '1' || costTypeFreight === '3' || costTypeFreight === '5'){
            $("#oilCardNumber").attr("required","required");
            $(".color").attr("style","color: red");
            $("#carrBankId").attr("disabled","disabled");
        } else {
            $("#oilCardNumber").attr("disabled","disabled");
            $(".fcff3").css("display","inline-block");
            $("#carrBankId").attr("required","required");
            $("#recBank").attr("required","required");
            $("#recCardNo").attr("required","required");
        }

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqPayDate',
                type: 'date',
                trigger: 'click',
                done: function(value, date, endDate){
                    $("#reqPayDate").val(value);
                    //单独校验日期
                    $("#form-payDetail-add").validate().element($("#reqPayDate"));
                }
            });
        });

        $("#form-payDetail-add").validate({
            onkeyup: false,
            focusCleanup: true
        });

        var picParam = {
            maxFileCount:0,
            //publish: "uploadSuccess",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        var tid = "tid";
        var imageId = "image";
        $.file.initAddFiles(imageId, tid, picParam);

        // 图片上传成功后
        $("#image").on('filebatchuploadsuccess', function (event, data) {
            console.log(event, data)
            //表单提交
            if ($('#receipt').length > 0 && $('#receipt').fileinput('getFilesCount') > 0){
                $("#receipt").fileinput('upload');
            } else {
                //表单提交
                var dis = $(":disabled");
                dis.attr("disabled", false);
                let _data = $('#form-payDetail-add').serialize();
                dis.attr("disabled", true);
                $.operate.save(prefix + "/saveApplyBatchCheck", _data);
            }
        });
        $('#image').on('filesuccessremove', function(event, key, index) {//filepreremove（未上传文件删除前）、fileremoved（未上传文件删除后）
            console.log(event, key, index)
            //return false; 阻止删除
            // 可根据index和tid（或配合filebatchuploadsuccess的回调参数data）删除后台数据，根据后台返回剩余附件数，控制是否清空tid，当前采用直接清空所有的方式
            $('#image').fileinput('clear');
            $('#tid').val('');
        });

        $.file.initAddFiles("receipt", "tid_receipt", {
            maxFileCount:0,
            fileType: null
        });
        $("#receipt").on('filebatchuploadsuccess', function (event) {
            var dis = $(":disabled");
            dis.attr("disabled", false);
            let _data = $('#form-payDetail-add').serialize();
            dis.attr("disabled", true);
            $.operate.save(prefix + "/saveApplyBatchCheck", _data);
        });
        $('#receipt').on('filesuccessremove', function(event, key, index) {
            $('#receipt').fileinput('clear');
            $('#tid_receipt').val('');
        });

        //for (var i = 0; i < payDetailList.length; i++) {
            //$.file.initAddFiles("receipt"+payDetailList[i].payDetail.payDetailId, "tidReceipt"+payDetailList[i].payDetail.payDetailId, {
            //    maxFileCount:0,
            //    fileType: null
            //});

           /* if(i < payDetailList.length && i != payDetailList.length -1){
                $("#receipt"+payDetailList[i].payDetail.payDetailId).on('filebatchuploadsuccess', function (event, data) {
                    debugger;
                    $("#receipt"+payDetailList[i+1].payDetail.payDetailId).fileinput('upload');
                });
            }else{
                $("#receipt"+payDetailList[i].payDetail.payDetailId).on('filebatchuploadsuccess', function (event, data) {
                    $("#image").fileinput('upload');
                });
            }
            $('#receipt'+payDetailList[i].payDetail.payDetailId).on('filesuccessremove', function(event, key, index) {
                $('#receipt'+payDetailList[i].payDetail.payDetailId).fileinput('clear');
                $('#tidReceipt'+payDetailList[i].payDetail.payDetailId).val('');
            });
*/
        //}
        translateTaxTxt()
    });

    /**
     * 费用类型修改
     */
    function typeChange() {
        if (costTypeFreight === '1' || costTypeFreight === '3' || costTypeFreight === '5'){
            $("#oilCardNumber").attr("required","required");
            $(".color").attr("style","color: red");
            $("#oilCardNumber").removeAttr("disabled");
        }else{
            $("#oilCardNumber").removeAttr("required");
            $(".color").removeAttr("style ");
            $("#oilCardNumber").attr("disabled","disabled");
        }
    }

    //收款人更换
    function bankChange() {
        $("#recCardNo").val("");
        $("#recBank").val("");

        var str = '<option value="">--请选择--</option>';
        var accountType = $("#accountType").val();

        if(accountType == 0){
            for(var i = 0 ; i < carrBankList.length ; i++){
                if(carrBankList[i].carrBankId == $("#carrBankId").val()){
                    $("#recCardNo").val(carrBankList[i].bankCard);
                    $("#recBank").val(carrBankList[i].bankName);
                }
            }
        }else if(accountType == 1){
            for(var i = 0 ; i < specialCarrBankList.length ; i++){
                if(specialCarrBankList[i].carrBankId == $("#carrBankId").val()){
                    $("#recCardNo").val(specialCarrBankList[i].bankCard);
                    $("#recBank").val(specialCarrBankList[i].bankName);
                }
            }
        }else if(accountType == 2){
            for(var i = 0 ; i < oilCarrBankList.length ; i++){
                if(oilCarrBankList[i].carrBankId == $("#carrBankId").val()){
                    $("#recCardNo").val(oilCarrBankList[i].bankCard);
                    $("#recBank").val(oilCarrBankList[i].bankName);
                }
            }
        }else if(accountType == 3){
            for(var i = 0 ; i < oilPayCarrBankList.length ; i++){
                if(oilPayCarrBankList[i].carrBankId == $("#carrBankId").val()){
                    $("#recCardNo").val(oilPayCarrBankList[i].bankCard);
                    $("#recBank").val(oilPayCarrBankList[i].bankName);
                }
            }
        }else if(accountType == 4){
            for(var i = 0 ; i < exceptionPayCarrBanks.length ; i++){
                if(exceptionPayCarrBanks[i].carrBankId == $("#carrBankId").val()){
                    $("#recCardNo").val(exceptionPayCarrBanks[i].bankCard);
                    $("#recBank").val(exceptionPayCarrBanks[i].bankName);
                }
            }
        }
    }

    //收款类型调整
    function accountTypeChange(){
        $("#recCardNo").val("");
        $("#recBank").val("");

        var str = '<option value="">--请选择--</option>';
        var accountType = $("#accountType").val();
        var isOilDeposit = $("#isOilDeposit").val();
        if(accountType == 0){
            $("#writeFuelcardDiv").css('display', 'none');
            //承运商收款人
            for(var i = 0 ; i < carrBankList.length ; i++){
                str = str +  '<option value="'+carrBankList[i].carrBankId+'">'+carrBankList[i].bankAccount+"</option>";
            }

        }else if(accountType == 1){
            $("#writeFuelcardDiv").css('display', 'none');
            //专项收款人
            for(var i = 0 ; i < specialCarrBankList.length ; i++){
                str = str +  '<option value="'+specialCarrBankList[i].carrBankId+'">'+specialCarrBankList[i].bankAccount+"</option>";
            }
        }else if(accountType == 2){
            $("#writeFuelcardDiv").css('display', 'none');
            //异常收款人
            for(var i = 0 ; i < oilCarrBankList.length ; i++){
                str = str +  '<option value="'+oilCarrBankList[i].carrBankId+'">'+oilCarrBankList[i].bankAccount+"</option>";
            }
        }else if(accountType == 3){
            $("#writeFuelcardDiv").css('display', 'block');
            //异常收款人
            for(var i = 0 ; i < oilPayCarrBankList.length ; i++){
                str = str +  '<option value="'+oilPayCarrBankList[i].carrBankId+'">'+oilPayCarrBankList[i].bankAccount+"</option>";
            }
        }else if(accountType == 4){
            $("#writeFuelcardDiv").css('display', 'block');
            //异常收款人
            for(var i = 0 ; i < exceptionPayCarrBanks.length ; i++){
                str = str +  '<option value="'+exceptionPayCarrBanks[i].carrBankId+'">'+exceptionPayCarrBanks[i].bankAccount+"</option>";
            }
        }
        $("#carrBankId").html(str);
    }

    /**
     * 表单提交
     */
    function submitHandler(index2){
        if ($('[err-tax]').length > 0) {
            $.modal.msgError("请调整含税构成");
            return
        }
        var accountType = $("#accountType").val();
        var isOilDeposit = $("#isOilDeposit").val();
        if(accountType == 3 && isOilDeposit != 1){
            $.modal.alertWarning("该应付非油卡押金，请选择其他账户类型");
            return false;
        }
        $("[name=taxTxt]").val(taxTxt);
        //debugger;
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function(){
                var costTypeFreight = [[${payDetail.costTypeFreight}]];//付款类型
                var oilCardNumber = $("#oilCardNumber").val();//油卡卡号
                var carrBankId = $("#carrBankId").val();//收款人
                //判断油卡
                if (costTypeFreight != null  &&  (costTypeFreight === '1' || costTypeFreight === '3' || costTypeFreight === '5')){
                    if($.common.isEmpty(oilCardNumber)){
                        $.modal.confirm("油卡卡号为空，是否确认提交？", function() {
                            commoit();
                        })
                    }else{
                        commoit();
                    }
                }else{
                    var recBank = $("#recBank").val();
                    var recCardNo = $("#recCardNo").val();

                    if($.common.isEmpty(recBank) || $.common.isEmpty(recCardNo) ){
                        $.modal.alertWarning("收款人和收款银行不能为空，请先完善信息")
                    }
                }
                //判断现金
                if (costTypeFreight == null || costTypeFreight === '0' || costTypeFreight === '2' || costTypeFreight === '4'){
                    if($.common.isEmpty(carrBankId)){
                        $.modal.confirm("收款人为空，是否确认提交？", function() {
                            commoit();
                        })
                    }else{
                        commoit();
                    }
                }
            })
        }
    }

    function commoit() {
        //console.log('image', $('#image').fileinput('getFilesCount'))
        //console.log('receipt', $('#receipt').fileinput('getFilesCount'))
        //解除disabled
        //var dis = $(":disabled");
        //dis.attr("disabled", false);

        //$.modal.loading("正在处理中，请稍后...");
        //debugger;
        //for (var i = 0; i < payDetailList.length; i++) {
        //    if ($('#receipt'+ payDetailList[i].payDetail.payDetailId).length > 0 && $('#receipt'+ payDetailList[i].payDetail.payDetailId).fileinput('getFilesCount') > 0){
        //        $("#receipt"+ payDetailList[i].payDetail.payDetailId).fileinput('upload');
        //    }
        //}
        //jQuery.subscribe(setTimeout("commit()", "1000"));
        commit()
    }

    function commit(){
        if($('#image').fileinput('getFilesCount') == 0 && ($('#receipt').length == 0 || $('#receipt').fileinput('getFilesCount') == 0)){
            var dis = $(":disabled");
            dis.attr("disabled", false);
            let _data = $('#form-payDetail-add').serialize();
            dis.attr("disabled", true)
            $.operate.save(prefix + "/saveApplyBatchCheck", _data);
        } else if ($('#image').fileinput('getFilesCount') > 0){
            // 表单提交
            $.modal.loading("正在处理中，请稍后...");
            $("#image").fileinput('upload');
        } else if ($('#receipt').fileinput('getFilesCount') > 0){
            $.modal.loading("正在处理中，请稍后...");
            $("#receipt").fileinput('upload');
        }
    }

    function addReceiver(){
        var carrierId = $("#carrierId").val();
        var accountType = $("#accountType").val();
        $.modal.open("新增收款人", ctx + "g7/payee/add?collectionType="+0+"&carrId="+carrierId,500,550);
    }

    var billingType = [[${@dict.getType('billing_type')}]];//开票类型
    var taxTxt = [[${taxTxt}]];

    function translateTaxTxt() {
        if (!taxTxt) { return; }
        var array = taxTxt.split(",");
        var temp = [];
        var sum = new BigNumber("0");
        for (let j = 0; j < array.length; j++) {
            var t = array[j].split(":");
            for (let i = 0; i < billingType.length; i++) {
                if (billingType[i].dictValue == t[0]) {
                    temp.push("[", billingType[i].dictLabel, ":", t[1], "]")
                    sum = sum.plus(new BigNumber(t[1]))
                    break;
                }
            }
        }
        temp.push(' <a href="javascript:adjustTax()">调</a>')
        if (!sum.eq(new BigNumber("[(${batchAmount})]"))) {
            temp.splice(0,0,"<span style='color:red' err-tax data-toggle='tooltip' title='合计不等于当前费用, 请手动调整' data-trigger='manual'>")
            temp.push("</span>")
        }
        $("#taxTxtView").html(temp.join(""))
        $('[err-tax]').tooltip('show')
    }

    function adjustTax() {
        var amount = "[(${batchAmount})]";
        var tmp = [`<div style="padding: 8px 20px;">
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-sm-4 control-label" style="padding-top: 5px;text-align: right">当前费用：</label>
							<div class="col-sm-3">
							  	<p class="form-control-static">${amount}</p>
							</div>
							<div class="col-sm-4">
							  	<p class="form-control-static" id="result" style="color:#ff0000"></p>
							</div>
							<div class="col-sm-1">
							    <i class="fa fa-plus-circle" style="padding: 4px 0;color:#007dcc;font-size: 16px" onclick="addTaxRow(this)"></i>
							</div>
						</div>`]
        var arr = taxTxt.split(",");
        for (let i = 0; i < arr.length; i++) {
            var tt = arr[i].split(":");
            tmp.push('<div class="form-group">')
            tmp.push('<label class="col-sm-7">')
            tmp.push('<select billingtype class="form-control">');
            for (let j = 0; j < billingType.length; j++) {
                tmp.push("<option value='",billingType[j].dictValue,"'",tt[0]==billingType[j].dictValue?' selected':'',">",billingType[j].dictLabel,"</option>");
            }
            tmp.push("</select>");
            tmp.push('</label>')
            tmp.push('<div class="col-sm-4">')
            tmp.push('<input class="form-control" tax oninput="$.numberUtil.onlyNumberTwoDecimal(this);check()" value="',tt[1],'" autocomplete="off">')
            tmp.push('</div>')
            tmp.push('<div class="col-sm-1">')
            tmp.push('<i class="fa fa-minus-circle" style="padding: 4px 0;color:#ff0000;font-size: 16px" onclick="$(this).closest(\'.form-group\').remove();check()"></i>')
            tmp.push('</div>')
            tmp.push('</div>')
        }
        tmp.push('</form>')
        tmp.push('* 所有含税额合计必须<span style="color:blue;font-weight: bold;">等于当前费用</span>')
        tmp.push('</div>')
        layer.open({
            type: 1,
            area: ['450px', '403px'],
            fix: false,
            //不固定
            maxmin: false,
            skin: '',
            shade: 0.3,
            shadeClose: false,
            title: '费用含税额拆分',
            content: tmp.join(""),
            btn: ['<i class="fa fa-check"></i> 保存', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(idx, layero){
                var chk = check();
                var objTemp = {};//校验开票类型时候重复的临时对象
                var tmp = []
                if (chk) {
                    var amount_origin = new BigNumber(amount)
                    var flag = true;
                    $("[billingtype]").each(function(){
                        if (objTemp[$(this).val()] != null) {
                            $.modal.msgError("相同开票类型的请合并");
                            flag = false;
                            return;
                        } else {
                            objTemp[$(this).val()] = 1;
                            var n = new BigNumber($(this).closest('.form-group').find('[tax]').val()).toNumber();
                            if (n === 0 && amount_origin.toNumber() != 0) {
                                $.modal.msgError("请输入非0的数字");
                                flag = false;
                                return;
                            } else if (n < 0 && amount_origin.toNumber() > 0) {
                                $.modal.msgError("当前费用大于0，各税额必须都大于0");
                                flag = false;
                                return;
                            } else if (n > 0 && amount_origin.toNumber() < 0) {
                                $.modal.msgError("当前费用小于0，各税额必须都小于0");
                                flag = false;
                                return;
                            }
                            tmp.push($(this).val()+":"+$(this).closest('.form-group').find('[tax]').val())
                        }
                    })
                    if (flag) {
                        taxTxt = tmp.join(",")
                        translateTaxTxt()
                        layer.close(idx)
                    }
                } else {
                    $.modal.msgError("所有含税额合计必须等于当前费用")
                }
            }
        })
        check()
    }

    function addTaxRow(i){
        var tmp = [];
        tmp.push('<div class="form-group">')
        tmp.push('<label class="col-sm-7">')
        tmp.push('<select billingtype class="form-control">');
        for (let j = 0; j < billingType.length; j++) {
            tmp.push("<option value='",billingType[j].dictValue,"'>",billingType[j].dictLabel,"</option>");
        }
        tmp.push("</select>");
        tmp.push('</label>')
        tmp.push('<div class="col-sm-4">')
        tmp.push('<input class="form-control" tax oninput="$.numberUtil.onlyNumberTwoDecimal(this);check()" autocomplete="off">')
        tmp.push('</div>')
        tmp.push('<div class="col-sm-1">')
        tmp.push('<i class="fa fa-minus-circle" style="padding: 4px 0;color:#ff0000;font-size: 16px" onclick="$(this).closest(\'.form-group\').remove();check()"></i>')
        tmp.push('</div>')
        tmp.push('</div>')
        $(i).closest("form").append(tmp.join(''))
    }
    function check() {
        var amount_origin = new BigNumber("[(${batchAmount})]");
        var sum = calc1();
        if (sum.eq(amount_origin)) {
            $("#result").text("")
            return true;
        } else if (sum.gt(amount_origin)) {
            $("#result").text("超" + sum.minus(amount_origin))
            return false;
        } else if (sum.lt(amount_origin)) {
            $("#result").text("低" + amount_origin.minus(sum))
            return false;
        }
    }
    function calc1() {
        var sum = new BigNumber("0")
        $("[tax]").each(function(){
            var amount = new BigNumber(this.value.trim());
            if (!amount.isNaN()) {
                sum = sum.plus(amount);
            }
        })
        return sum;
    }
</script>
</body>

</html>