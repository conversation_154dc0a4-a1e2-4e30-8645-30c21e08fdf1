<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('G7U盾统一支付')"/>
    <style>
        .btn-info.active {
            color: #ffffff;
            background-color: #1a7bb9;
        }
        .btn-default {
            color: #333;
            background-color: #fff;
            border-color: #ccc;
        }
    </style>
</head>

<body class="gray-bg">
<div class="container-div">

    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4" style="line-height: 27px;text-align: right">应付单号：</label>
                            <div class="col-sm-8">
                                <input name="vbillno" id="vbillno" class="form-control" placeholder="应付单号模糊匹配">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4" style="line-height: 27px;text-align: right">结算公司：</label>
                            <div class="col-sm-8">
                                <select id="corp" name="corp" placeholder="应付单号模糊匹配" class="form-control noselect2 selectpicker" data-none-selected-text="??" th:with="type=${@dict.getType('bala_corp')}">
                                    <option value="">- 切换结算公司</option>
                                    <!-- G7结算公司 -->
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:if="${@g7ConfigMap.get(dict.dictValue) != null}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-4">
                        <div class="form-group" style="text-align: right">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary multiple disabled" onclick="payChecked()" data-toggle="tooltip" data-placement="right"
               data-html="true"
               data-delay='{"show": 500, "hide": 1000}'
               title="<div style='white-space: nowrap;text-align:left'>G7支付以运单为单位，同一运单<br>的计划只要一个被选中，该运单<br>的所有计划均会支付</div>">
                <i class="fa fa-paypal"></i> 支付选中
            </a>
            <a class="btn btn-danger multiple disabled" onclick="deleteG7PayData()" data-toggle="tooltip" data-placement="right"
               data-html="true"
               data-delay='{"show": 500, "hide": 1000}'
               title="<div style='white-space: nowrap;text-align:left'>G7支付以运单为单位，同一运单<br>的计划只要一个被选中，该运单<br>的所有计划均会删除</div>">
                <i class="fa fa-trash-o"></i> 删除选中
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped" style="height: calc(100% - 80px) !important;">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<div id="modal-body" style="padding: 10px;display: none;">
    <form id="form-g7pay" class="form-horizontal">
        <div class="form-group">
            <label class="col-sm-3" style="text-align: right;line-height: 24px"><span id="g7corp_name"></span>U盾支付口令:</label>
            <div class="col-sm-7">
                <input id="g7PayLotId" type="hidden">
                <input id="g7PayCorp" type="hidden">
                <input id="g7PayPayee" type="hidden">
                <input id="g7PayAmount" type="hidden">
                <input id="g7PayRecordId" type="hidden">
                <input class="form-control valid" required id="cipher"/>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "payDetail";
    var dict_bala_corp = [[${@dict.getType('bala_corp')}]];
    function initTable() {
        $.table.destroy()
        var options = {
            url: prefix + "/g7-waitting-pay",
            showToggle: false,
            showColumns: true,
            fixedNumber: 0,
            //pagination: false,
            showFooter: false,
            clickToSelect:true,

            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "合计";
                    }
                },
                {
                    title: '应付单号',
                    align: 'left',
                    field: 'VBILLNO'
                },
                {
                    title: '结算公司',
                    align: 'left',
                    field: 'BILLING_CORP',
                    formatter: function (value, row, index) {
                        // <!-- G7结算公司 -->
                        for (let i = 0; i < dict_bala_corp.length; i++) {
                            if (dict_bala_corp[i].dictValue == value) {
                                return dict_bala_corp[i].dictLabel
                            }
                        }
                        return value;
                    }
                },
                {
                    title: '申请人',
                    align: 'left',
                    field: 'REG_USER_NAME'
                },
                {
                    title: '提交时间',
                    align: 'left',
                    field: 'REG_DATE'
                },
                /*{
                    title: '要求支付日期',
                    align: 'left',
                    field: 'reqPayDate',
                },*/
                /*{
                    title: '司机',
                    align: 'left',
                    field: 'custAbbr'
                },*/
                {
                    title: '收款人',
                    align: 'left',
                    field: 'PAYEE'
                },
                {
                    title: '金额',
                    align: 'right',
                    field: 'AMOUNT',
                    formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }

            ]
        };
        $.table.init(options);
    }
    function searchPre() {
        $.table.search();
    }
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }
    $(function () {
        /*$('[name="options"]').on('change', function () {
            initTable($('[name="options"]:checked').val())
        })*/
        initTable()
    });

    /**
     * 全部支付提交
     */
    /*function submitHandler() {
        $("#cipher").val("")
        $("#g7PayLotId").val("")
        $("#g7corp_name").text($('[name="options"]:checked').val() == 'MY' ? '铭源' : '吉华');
        layer.open({
            type: 1,
            title: '输入<'+($('[name="options"]:checked').val() == 'MY' ? '铭源' : '吉华')+'>U盾支付口令完成支付',
            area: ['500px', '200px'],
            btn: ['立即支付', '关闭'],
            zIndex: 3,
            content: $('#modal-body'), //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
            yes: function(index, layero){
                doG7PayAll(function(result){
                    layer.close(index);
                    if (result.code != 0) {
                        // 失败后刷新
                        $.table.refresh();
                    }
                })
            }
        });
    }*/

    function getG7CorpName(corp) {
        var balaCorp = [[${@dict.getType('bala_corp')}]];
        for (let i = 0; i < balaCorp.length; i++) {
            if (balaCorp[i].dictValue == corp) {
                return balaCorp[i].dictLabel
            }
        }
        return '未知';
    }
    function payChecked() {
        var list = $.btTable.bootstrapTable('getSelections');
        var tmp = []
        var corp = list[0].BILLING_CORP;
        for (let i = 0; i < list.length; i++) {
            if (list[i].BILLING_CORP != corp) {
                $.modal.msgError("只能选择同一家结算公司支付")
                return
            }
            tmp.push(list[i].LOT_ID)
        }
        $("#g7PayLotId").val(tmp.join(","))
        $("#cipher").val("")
        $("#g7PayCorp").val(corp)

        $("#g7corp_name").text(getG7CorpName(corp));
        $("#g7PayAmount").val(list[0].AMOUNT)

        //$('#myModal').modal({backdrop: 'static'})
        layer.open({
            type: 1,
            title: '输入<'+(getG7CorpName(corp))+'>U盾支付口令完成支付',
            area: ['500px', '200px'],
            btn: ['立即支付', '关闭'],
            zIndex: 3,
            content: $('#modal-body'), //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
            yes: function(index, layero){
                //按钮【按钮一】的回调
                doG7Pay(function(result){
                    layer.close(index);
                })
            }
        });
    }
    function doG7Pay(callback) {
        if ($("#form-g7pay").validate().form()) {
            $.modal.confirm("确定支付吗？", function(){
                $.operate.saveAndRefreshCurrent(prefix + "/g7-pay", {
                    lotId: $('#g7PayLotId').val(),
                    cipher: $.trim($("#cipher").val()),
                    corp: $("#g7PayCorp").val()
                }, callback);
            })
        }
    }
    /*function doG7PayAll(callback) {
        if ($("#form-g7pay").validate().form()) {
            $.modal.confirm("本操作将支付当前结算公司的所有单据（包括未显示的），确定全部支付吗？", function(){
                $.operate.saveAndRefreshCurrent(prefix + "/g7-pay-all", {
                    cipher: $.trim($("#cipher").val()),
                    corp: $('[name="options"]:checked').val()
                }, callback);
            })
        }
    }*/
    function deleteG7PayData() {
        var tmp = [];
        var list = $.btTable.bootstrapTable('getSelections');
        var corp = list[0].BILLING_CORP;
        for (let i = 0; i < list.length; i++) {
            if (list[i].BILLING_CORP != corp) {
                $.modal.msgError("只能选择同一结算公司进行批量操作")
                return
            }
            tmp.push(list[i].ID)
        }
        $.modal.confirm("确定删除选中的支付计划吗？", function(){
            $.operate.saveAndRefreshCurrent(prefix + "/deleteG7PayData", { corp: corp, ids: tmp.join(',') });
        });
    }
</script>
</body>
</html>