<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse,.f7f7{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }

    .table-striped {
        height: calc(100% - 70px);
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row no-gutter">
                    <div class="col-sm-6" style="padding: 0">
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input name="vbillno" id="vbillno" th:value="${payDetail?.vbillno}" class="form-control"
                                           placeholder="请输入应付单号" maxlength="30">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input name="lotno" id="lotno" class="form-control"
                                           placeholder="请输入运单号" maxlength="30">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input name="carrName" id="carrName" placeholder="请输入承运商名称" class="form-control valid">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input name="driverName" id="driverName" class="form-control" placeholder="请输入司机名称"
                                           maxlength="30">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6" style="padding: 0">
                        <div class="col-md-4 col-sm-4">
                            <select name="status" id="status" class="form-control valid noselect2 selectpicker"
                                    aria-invalid="false" data-none-selected-text="应付单状态" multiple>
                                <option th:each="dict : ${payDetailStatusEnum}" th:text="${dict.context}"
                                        th:value="${dict.value}"></option>
                            </select>
                        </div>
                        <div class="col-md-5 col-sm-5">

                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-6"></label>-->
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                        class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                        class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>

                    </div>

                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="batchPay()" shiro:hasPermission="finance:payDetail:indemnity">
                <i class="fa fa-file-text-o"></i> 异常赔款核销
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>

<script type="text/template" id="batchPayHtml">
    <div class="row" style="margin: 20px 20px;">
        <div class="panel panel-default">
            <div class="panel-collapse collapse in" style="margin-bottom: 7px;">
                <div class="panel-body">
                    <!--基础信息 begin-->
                    <div class="row">
                        <div class="col-md-6 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">总金额(元)：</label>
                                <div class="col-sm-8">
                                    <input  id="totalAmount" class="form-control" name="totalAmount"
                                            th:value="${payDetail.transFeeCount}" disabled>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">付款类型：</label>
                                <div class="col-sm-8">
                                    <input name="payType" class="form-control" value="1" type="hidden">
                                    <input class="form-control" value="直接付款" disabled >
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">付款方式：</label>
                                <div class="col-sm-8">
                                    <select name="payMethod" id="payMethod" class="form-control"
                                            th:with="type=${@dict.getType('pay_method')}" required disabled>
<!--                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"-->
<!--                                                th:value="${dict.dictValue}"></option>-->
                                        <option value="93">异常销账</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">收款日期：</label>
                                <div class="col-sm-8">
                                    <input name="payDate" id="payDate" class="form-control" readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4" style="color: red">转入账户：</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input name="accountName" id="accountName" required class="form-control valid"
                                               type="text" aria-required="true" maxlength="50">
                                        <!--账户id-->
                                        <input name="inAccount" id="inAccount"  class="form-control valid"
                                               type="hidden" aria-required="true">
                                        <div class="input-group-btn">
                                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-md-2 col-sm-2">付款备注：</label>
                                <div class="col-md-10 col-sm-10">
                                    <textarea id="memo" maxlength="250" class="form-control valid" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: bootstrap-suggest-js"/>

<script th:inline="javascript">
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    var prefix = ctx + "payDetail";
    //应付单状态
    var payDetailStatusEnum = [[${payDetailStatusEnum}]];
    //应付单map
    var payDetailStatusMap = [[${payDetailStatusMap}]];
    //复核通过状态
    var reviewPassStatus = [[${reviewPassStatus}]];
    var collection_type = [[${@dict.getType('collection_type')}]];

    var transFeeCount = 0;
    var gotAmountCount = 0;
    var ungotAmountCount = 0;


    $(function () {
        var options = {
            url: prefix + "/indemnity/list",
            showToggle: false,
            showColumns: true,
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 3,
            height: 570,
            firstLoad: false,
            clickToSelect:true,
            showFooter:true,
            columns: [{
                checkbox: true,
            },
                {
                    title: '操作',
                    align: 'left',
                    field: 'payDetailId',
                    formatter: function (value, row, index) {
                        var actions = [];
                        return actions.join('');
                    }
                },
                {
                    title: '结算公司/调度组',
                    align: 'left',
                    field: 'balaCorp',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(balaCorp, value) + '<br />'+getValue(row.params.transLineName);
                    }
                },
                {
                    title: '应付单号/应付单状态',
                    field: 'vbillno',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var result = value;
                        var context = '';
                        payDetailStatusEnum.forEach(function (v) {
                            if (v.value == row.vbillstatus) {
                                if (row.vbillstatus == payDetailStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-default">'+v.context+'</span>';
                                }else if (row.vbillstatus == payDetailStatusMap.AFFIRM) {
                                    //已确认
                                    context = '<span class="label label-warning">'+v.context+'</span>';
                                } else if(row.vbillstatus == payDetailStatusMap.HAS_BEEN_WRITTEN_OFF){
                                    //已核销
                                    context = '<span class="label label-success">' + v.context + '</span>';
                                } else if(row.vbillstatus == payDetailStatusMap.APPLY){
                                    //已申请
                                    context = '<span class="label label-success">' + v.context + '</span>';
                                    if(row.bankBackFlag == 1){
                                        context += '<span class="label label-danger" style="margin-left: 2px;">银退</span>';
                                    }
                                } else if(row.vbillstatus == payDetailStatusMap.REVIEW_PASS){
                                    //复核通过
                                    context = '<span class="label label-primary">' + v.context + '</span>';
                                    if (row.g7Pay == 3) {
                                        context += '<span class="label label-danger" style="margin-left: 2px;" data-toggle="tooltip" data-html="true" title="'+row.g7PayErr+'">g7退</span>'
                                    }
                                }else{
                                    context = v.context;
                                }
                                return false;
                            }
                        });

                        if (row.lotG7End == 2) {
                            context += ' <span class="label label-success" style="padding:1px;">G7</span>'
                        } else if (row.lotG7Syn != null) {
                            context += ' <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true" title="'
                            if (row.lotG7Syn == 0) {
                                context += '等待G7审验'
                            } else if (row.lotG7Syn == 1) {
                                context += row.lotG7Msg
                            } else if (row.lotG7Syn == 2) {
                                if (row.lotG7Start == null || row.lotG7Start == 0) {
                                    context += '等待推送【发车】'
                                } else if (row.lotG7Start == 1) {
                                    context += '【发车】推送失败'
                                } else if (row.lotG7End == null || row.lotG7End == 0) {
                                    context += '等待推送【到达】'
                                } else if (row.lotG7End == 1) {
                                    context += '【到达】推送失败'
                                }
                            } else if (row.lotG7Syn == 7) {
                                context += '运单已作废'
                            }
                            context += '">G7</span>'
                        }
                        var isMine = row.isMine;
                        if(isMine == 1){
                            context += ' <span class="label label-primary" title="琦欣" style="padding:1px 5px;">Q</span>'
                        }
                        if (row.payWay == 'Y') {
                            context += ' <span class="label label-warning" title="预支" style="padding:1px 5px;">Y</span>'
                        }

                        return result + '<br />' + context
                    }
                },
                // {
                //     title: '申请信息',
                //     align: 'left',
                //     field: 'applyUser',
                //     formatter: function status(value, row, index) {
                //         return getValue(value) + '&nbsp;&nbsp;' + getValue(row.applyTime)+'<br />' + getValue(row.applyMemo)
                //     }
                // },
                {
                    title: '金额明细',
                    align: 'left',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        let data=[];
                        if(row.memo){
                            if(row.transFeeCount){
                                data.push(`<span class="label label-primary pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="总金额">总额</span> `+row.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+ '<i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-placement="top" data-container="body" title="'+row.memo+'"></i>')
                            }
                        }else {
                            if(row.transFeeCount){
                                data.push(`<span class="label label-primary pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="总金额">总额</span> `+row.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                            }
                        }
                        if(row.gotAmount){
                            data.push(`<span class="label badge-info pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="已付金额">已付</span> `+row.gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if(row.ungotAmount){
                            data.push(`<span class="label label-coral pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="未付金额">未付</span> `+row.ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        return data.join("<br/>")
                    }

                },
                {
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == 1){
                            return '<span>异常费用</label>'+ '<br />'+$.table.selectDictLabel(costTypeOnWay, row.costTypeOnWay);
                        }
                    }

                },
                {
                    title: '承运商/身份证',
                    align: 'left',
                    field: 'carrName',
                    formatter: function status(value, row, index) {
                        return getValue(value) + '<br />' + getValue(row.legalCard)
                    }
                },
                {
                    title: '司机/电话',
                    align: 'left',
                    field: 'driverName',
                    formatter: function status(value, row, index) {
                        return  getValue(value) + '<br />' + getValue(row.driverMobile)
                    }
                },
                {
                    title: '创建人/时间',
                    align: 'left',
                    field: 'regUserId',
                    formatter: function status(value, row, index) {
                        return  getValue(value) + '<br />' + getValue(row.regDate)
                    }
                },
                {
                    title: '运单号',
                    align: 'left',
                    field: 'lotno'
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(row.deliAddr == null || row.deliAddr == ""){
                            row.deliAddr = '';
                        }
                        if(row.arriAddr == null || row.arriAddr.arriAddr == ""){
                            row.arriAddr = ''
                        }
                        if(row.deliAddr == "" && row.arriAddr == ""){
                            return "";
                        }else{
                            return `<span class="label label-warning pa2">提</span>`+row.deliAddr+`<br/><span class="label label-success pa2">到</span>`+row.arriAddr;
                        }

                    }
                },
                {
                    title: '提货|到货日期',
                    field: 'reqDeliDate',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var res = ''
                        if(row.reqDeliDate == "" || row.reqDeliDate == null || row.reqDeliDate == 'undefined'){
                            row.reqDeliDate == "";
                            res += '-'
                        }else{
                            res += `<span class="label label-warning pa2">提</span>`+value.substring(0,10)

                        }
                        if(row.reqArriDate == "" || row.reqArriDate == null || row.reqArriDate == 'undefined'){
                            row.reqArriDate == "";
                            res += '<br/>-'
                        }else{
                            res +=`<br/><span class="label label-success pa2">到</span>`+row.reqArriDate.substring(0,10);
                        }
                        return res

                    }
                },
                {
                    title: '是否为调整单',
                    align: 'left',
                    field: 'isAdjust',
                    formatter: function (value, row, index) {
                        if (value === 0) {
                            return '否';
                        }
                        return '是';
                    }
                },
                {
                    title: '调整原因',
                    align: 'left',
                    field: 'adjustMemo'
                },

            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        //应付状态为已申请和部分核销
        var arr = [];
        arr.push(payDetailStatusEnum[1].value);
        $('#status').selectpicker('val',arr);

        searchPre();

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#applyDateStart',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#applyDateEnd',
                type: 'date',
                trigger: 'click'
            });
        });

    });
    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }



    /**
     *
     */
    function batchPay(){
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["vbillstatus"] !== 1) {
            $.modal.alertWarning("请选择【已确认】状态的应付单。");
            return;
        }
        if (bootstrapTable[0]["costTypeOnWay"] !== '27') {
            $.modal.alertWarning("请选择【异常赔款】类型的应付单。");
            return;
        }

        let ungotAmount = bootstrapTable[0]["ungotAmount"];
        let payDetailId = bootstrapTable[0]["payDetailId"];

        layer.open({
            type: 1,
            title: '核销',
            closeBtn: 0,
            area: ['80%', '50%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: $('#batchPayHtml').html(),
            btn: ['保存','取消'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {
                $("#totalAmount").val(ungotAmount)

                /**
                 * 关键字提示查询 转入账户
                 */
                $("#accountName").bsSuggest('init', {
                    url: ctx + "finance/account/findAccount?paymentType=0&keyword=",
                    indexId: 0,
                    showBtn: false,
                    allowNoKeyword: false,
                    getDataMethod: "url",
                    keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
                    effectiveFields: ["accountName"],
                    delay: 300,
                    searchingTip: '搜索中...',
                    hideOnSelect: true,
                    maxOptionCount: 10,
                    inputWarnColor: '',
                }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
                    $("#accountName").val(data.accountName);
                    $("#inAccount").val(data.accountId);
                });

                /**
                 * 初始化日期控件
                 */
                layui.use('laydate', function() {
                    var laydate = layui.laydate;
                    laydate.render({
                        elem: '#payDate',
                        type: 'datetime',
                        trigger: 'click'
                    });
                });


            },
            yes: function (index, layero) {
                let inAccount = $("#inAccount").val();
                if (inAccount == '') {
                    $.modal.alertWarning("请填写【转入账户】。");
                    return
                }
                // let recCardNo = $("#recCardNo").val();
                // if (recCardNo == '') {
                //     $.modal.alertWarning("请填写【收款卡号】。");
                //     return
                // }
                // let recBank = $("#recBank").val();
                // if (recBank == '') {
                //     $.modal.alertWarning("请填写【收款银行】。");
                //     return
                // }

                let memo = $("#memo").val();
                let payDate = $("#payDate").val();

                //数据集
                var data = {}
                data['payDetailId'] = payDetailId
                data['inAccount'] = inAccount
                data['payDate'] = payDate
                data['memo'] = memo

                $.ajax({
                    url: ctx + "payDetail/indemnity/writeOff",
                    type: "post",
                    dataType: "json",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(data),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function (data) {
                        if (data.code == 0) {
                            $.modal.msgSuccess(data.msg);
                            layer.close(index);
                            $.table.refresh();
                        }else {
                            $.modal.alertError(data.msg);
                        }
                        $.modal.closeLoading();
                        $.modal.enable();
                    }
                })
            }
        })
    }

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.status = $.common.join($('#status').selectpicker('val'));
        $.table.search('role-form', data);
    }


    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();

        //应付状态为已申请和部分核销
        var arr = [];
        arr.push(payDetailStatusEnum[1].value);
        $('#status').selectpicker('val',arr);
        searchPre();
    }


    function detail(payDetailId) {
        var type = 'payDetal';
        $.modal.openTab("应付明细", ctx + "payDetailCheck/check?payDetailId=" + payDetailId+"&type="+type);
    }




</script>
</body>
</html>