<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .table-striped{
        height: auto;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运单号：</label>
                            <div class="col-sm-8">
                                <input name="lotno" id="lotno" class="form-control"
                                       placeholder="请输入运单号" maxlength="30">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">应付单状态：</label>
                            <div class="col-sm-8">
                                <select name="status" id="status" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="应付单状态" multiple>
                                    <option th:each="dict : ${payDetailStatusEnum}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>


                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">承运商名称：</label>
                            <div class="col-sm-8">
                                <input name="carrName" id="carrName" placeholder="请输入承运商名称" class="form-control valid">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">司机名称：</label>
                            <div class="col-sm-8">
                                <input name="driverName" id="driverName" class="form-control" placeholder="请输入司机名称"
                                       maxlength="30">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">应付单号：</label>
                            <div class="col-sm-8">
                                <input name="vbillno" id="vbillno" class="form-control"
                                       placeholder="请输入应付单号" maxlength="30">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">创建人：</label>
                            <div class="col-sm-8">
                                <input name="regUserName" id="regUserName" class="form-control"
                                       placeholder="请输入创建人" maxlength="25">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2">创建时间：</label>
                            <div class="col-sm-7">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="startDate">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endtDate"  name="endtDate">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">付款类型：</label>
                            <div class="col-sm-8">
                                <select id="costTypeFreight" name="costTypeFreight" class="form-control noselect2 selectpicker" th:with="type=${@dict.getType('cost_type_freight')}"
                                        data-none-selected-text="付款类型" multiple>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">车牌号：</label>
                            <div class="col-sm-8">
                                <input name="carno" id="carno" class="form-control" placeholder="请输入车牌号"
                                       maxlength="30">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger" onclick="remove()" shiro:hasPermission="trace:comfirmPayment:delete">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-primary" onclick="oppositeAffirm()" shiro:hasPermission="trace:confirmPayment:uncomfirm">
                <i class="fa fa-exclamation"></i> 反确认
            </a>
            <a class="btn btn-primary" onclick="affirm()" shiro:hasPermission="trace:confirmPayment:comfirm">
                <i class="fa fa-mail-reply"></i> 确认
            </a>
            <a class="btn btn-primary multiple disabled" onclick="detailTab()" shiro:hasPermission="trace:confirmPayment:detail">
                <i class="fa fa-newspaper-o"></i> 单据明细
            </a>
            <a class="btn btn-primary" onclick="checking()" shiro:hasPermission="trace:confirmPayment:checking">
                <i class="fa fa-file-text-o"></i> 生成对账单
            </a>
            <a class="btn btn-primary" onclick="insertChecking()" shiro:hasPermission="trace:confirmPayment:join">
                <i class="fa fa-file-text-o"></i> 加入对账单
            </a>
            <a class="btn btn-primary single disabled " onclick="adjust()" shiro:hasPermission="trace:confirmPayment:adjust">
                <i class="fa fa-edit"></i> 调整
            </a>
            <a class="btn btn-primary single disabled" onclick="payRecord()" shiro:hasPermission="trace:confirmPayment:record">
                <i class="fa fa-calculator"></i> 付款记录
            </a>
            <a class="btn btn-primary single disabled" onclick="applyPay()" shiro:hasPermission="trace:confirmPayment:apply">
                <i class="fa fa-calculator"></i> 申请付款
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var prefix = ctx + "payDetail";

    var payDetailStatusEnum = [[${payDetailStatusEnum}]];
    //应付单map
    var payDetailStatusMap = [[${payDetailStatusMap}]];


    var transFeeCount = 0;
    var gotAmountCount = 0;
    var ungotAmountCount = 0;


    $(function () {
        //应付单状态默认新建
        $('#status').selectpicker('val',payDetailStatusEnum[0].value);

        var options = {
            url: prefix + "/mainToPayDetailList",
            createUrl: prefix + "/add?carrierId="+$("#carrierId").val(),
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 3,
            height:560,
            clickToSelect:true,
            showFooter:true,
            onRefresh:function(params){
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
            },
            onCheck: function (row,$element) {
                var transFee = row.transFeeCount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数加上本行数值
                transFeeCount = transFeeCount + transFee;
                gotAmountCount = gotAmountCount + gotAmount;
                ungotAmountCount = ungotAmountCount + ungotAmount;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheck: function (row, $element) {
                var transFee = row.transFeeCount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数减去本行数值
                transFeeCount = transFeeCount - transFee;
                gotAmountCount = gotAmountCount - gotAmount;
                ungotAmountCount = ungotAmountCount - ungotAmount;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onCheckAll: function (rowsAfter) {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                //循环累加
                for (var row of rowsAfter) {
                    transFeeCount = transFeeCount + row.transFeeCount;
                    gotAmountCount = gotAmountCount + row.gotAmount;
                    ungotAmountCount = ungotAmountCount + row.ungotAmount;
                }
                //赋值
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheckAll: function () {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'left',
                    field: 'payDetailId',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('trace:comfirmPayment:edit')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="修改" onclick="edit(\'' + value + '\',\'' + row.vbillstatus + '\',\'' + row.isClose + '\')"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>');
                        }
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="结算公司" onclick="balaCorp(\'' + value + '\',\'' + row.payDetailId + '\')"><i  class="fa fa-calculator" style="font-size: 15px;" ></i></a>');
                        if(row.checkStatus == 1){
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="审核记录" onclick="checkInfo(\'' + value + '\',\'' + row.payDetailId + '\')"><i  class="fa fa-commenting-o" style="font-size: 15px;" ></i></a>');
                        }
                        return actions.join('');
                    }
                },
                {
                    title: '应付单号',
                    field: 'vbillno',
                    align: 'left',
                    footerFormatter: function (row) {
                        return "总金额合计：<nobr id='transFeeCountTotal'>￥0</nobr>&nbsp&nbsp" +
                            "已付金额合计：<nobr id='gotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                            "未付金额合计：<nobr id='ungotAmountCountTotal'>￥0</nobr>";
                    },
                    formatter: function (value, row, index) {
                        var result = value;
                        if (row.lotG7End == 2) {
                            result += ' <span class="label label-success" style="padding:1px;">G7</span>'
                        } else if (row.lotG7Syn != null) {
                            result += ' <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true" title="'
                            if (row.lotG7Syn == 0) {
                                result += '等待G7审验'
                            } else if (row.lotG7Syn == 1) {
                                result += row.lotG7Msg
                            } else if (row.lotG7Syn == 2) {
                                if (row.lotG7Start == null || row.lotG7Start == 0) {
                                    result += '等待推送【发车】'
                                } else if (row.lotG7Start == 1) {
                                    result += '【发车】推送失败'
                                } else if (row.lotG7End == null || row.lotG7End == 0) {
                                    result += '等待推送【到达】'
                                } else if (row.lotG7End == 1) {
                                    result += '【到达】推送失败'
                                }
                            } else if (row.lotG7Syn == 7) {
                                result += '运单已作废'
                            }
                            result += '">G7</span>'
                        }
                        return result
                    }
                },
                {
                    title: '应付单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(row,value) {
                        var context = '';
                        payDetailStatusEnum.forEach(function (v) {
                            if (v.value == value.vbillstatus) {
                                if (value.vbillstatus == payDetailStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-primary">'+v.context+'</span>';
                                }else if (value.vbillstatus == payDetailStatusMap.AFFIRM) {
                                    //已确认
                                    context = '<span class="label label-warning">'+v.context+'</span>';
                                } else if(value.vbillstatus == payDetailStatusMap.HAS_BEEN_WRITTEN_OFF){
                                    //已核销
                                    context = '<span class="label label-success">' + v.context + '</span>';
                                } else if(value.vbillstatus == payDetailStatusMap.APPLY){
                                    //已申请
                                    context = '<span class="label label-success">' + v.context + '</span>';
                                }else{
                                    context = '<span class="label label-info">' + v.context + '</span>';;
                                }
                                return false;
                            }
                        });
                        return context;
                    }
                },{
                    title: '提货日期',
                    align: 'left',
                    field: 'reqDeliDate'
                },

                {
                    title: '到货日期',
                    align: 'left',
                    field: 'reqArriDate'
                },
                {
                    title: '提货|到货省市区',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(row.deliAddr == null || row.deliAddr == ""){
                            row.deliAddr = '';
                        }
                        if(row.arriAddr == null || row.arriAddr.arriAddr == ""){
                            row.arriAddr = ''
                        }
                        if(row.deliAddr == "" && row.arriAddr == ""){
                            return "";
                        }else{
                            return row.deliAddr+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriAddr;
                        }

                    }
                },
                // {
                //     title:'提货地址',
                //     align:'left',
                //     field:'deliAddr'
                // },
                // {
                //     title:'到货地址',
                //     align:'left',
                //     field:'arriAddr'
                // },
                {
                    title: '车牌号',
                    align: 'left',
                    field: 'carno'
                },


                {
                    title: '承运商',
                    align: 'left',
                    field: 'carrName'
                },

                {
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '<span>运费</label>';
                            case 1:
                                return '<span>在途费用</label>';
                            case 2:
                                return '<span>调整费用</label>';
                            default:
                                break;
                        }
                    }

                },
                {
                    title: '付款类型',
                    field: 'costTypeOnWay',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if (row.freeType === '0'){
                            return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                        }else if (row.freeType === '2')  {
                            return '调整费'
                        }
                        return $.table.selectDictLabel(costTypeOnWay, value);
                    }

                },


                {
                    title: '司机',
                    align: 'left',
                    field: 'driverName'
                },
                {
                    title: '司机电话',
                    align: 'left',
                    field: 'driverMobile'
                },
                {
                    title: '总金额',
                    align: 'right',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '已付金额',
                    align: 'right',
                    field: 'gotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未付金额',
                    align: 'right',
                    field: 'ungotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '油卡卡号',
                    align: 'left',
                    field: 'oilCardNumber'
                }, {
                    title: '运单号',
                    align: 'left',
                    field: 'lotno'
                },


                {
                    title: '创建人',
                    align: 'left',
                    field: 'regUserId'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate'
                },


                {
                    title: '是否为调整单',
                    align: 'left',
                    field: 'isAdjust',
                    formatter: function (value, row, index) {
                        if (value === 0) {
                            return '否';
                        }
                        return '是';
                    }
                },
                {
                    title: '调整原因',
                    align: 'left',
                    field: 'adjustMemo'
                }
                ,

                {
                    title: '收款人',
                    align: 'left',
                    field: 'recAccount'
                }
                ,
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'recBank'
                },
                {
                    title: '收款卡号',
                    align: 'left',
                    field: 'recCardNo'
                },
                {
                    title: '备注',
                    align: 'left',
                    field: 'memo'
                },
                {
                    title: '是否符合无车承运人',
                    align: 'left',
                    field: 'isNtocc',
                    formatter: function status(value, row, index) {
                        if (value === 0) {
                            return '否';
                        }
                        return '是';
                    }
                }

            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
    });

    /**
     * 详情页
     */
    function detailTab() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0) {
                $.modal.alertWarning("应付单据只能为新建状态下才能确认");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应付单据");
                return;
            }
            if (bootstrapTable[i]["costTypeFreight"] === '1' && bootstrapTable[i]["oilCardNumber"] === null ) {
                $.modal.alertWarning("预付油卡类型的应付单油卡不能为空");
                return;
            }
        }
        var rows = $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.openTab("应付单据详情", prefix + "/detail/" + rows.join() );
    }


    /**
     * 跳转应付修改页面
     * @param id
     */
    function edit(id,vbillstatus,isClose) {
        if (vbillstatus != 0) {
            $.modal.alertWarning("只能修改新建状态的应付单");
            return;
        }
        if (isClose === '1') {
            $.modal.alertWarning("该应付单已关账");
            return;
        }
        var url = prefix + "/edit?payDetailId=" + id;
        $.modal.openTab("应付明细修改", url);
    }
    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;
    /**
     * 结算公司
     */
    function balaCorp(id) {
        var url = prefix + "/balaCorp?payDetailId=" + id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "结算信息",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 调整
     */
    function  adjust() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["isClose"] === 0) {
            $.modal.alertWarning("请选择已关账的应付单据");
            return;
        }
        $.modal.openTab("调整", prefix + "/adjust?payDetailId="+bootstrapTable[0]["payDetailId"]);
    }
    /**
     * 确认应付明细
     */
    function affirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0) {
                $.modal.alertWarning("应付单据只能为新建状态下才能确认");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应付单据");
                return;
            }
            if (bootstrapTable[i]["freeType"] === '0'){
                if(bootstrapTable[i]["costTypeFreight"] != '1' && bootstrapTable[i]["costTypeFreight"] != '3' && bootstrapTable[i]["costTypeFreight"] != '5'){
                    if (bootstrapTable[i]["carrBankId"] == null || bootstrapTable[i]["carrBankId"] == "" || bootstrapTable[i]["carrBankId"] == 'undefined') {
                        $.modal.alertWarning("收款人信息为空无法进行确认");
                        return;
                    }
                }else{
                    if (bootstrapTable[i]["oilCardNumber"] == null || bootstrapTable[i]["oilCardNumber"] == "" || bootstrapTable[i]["oilCardNumber"] == 'undefined') {
                        $.modal.alertWarning("油卡卡号为空无法进行确认");
                        return;
                    }
                }
            }
        }
        var payDetailIds = $.table.selectColumns("payDetailId");
        if (payDetailIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }


        $.modal.confirm("是否确认？", function () {
            $.operate.post(prefix + "/affirm", {"payDetailIds": payDetailIds.join()});
        });
    }

    /**
     * 结算公司
     */
    function checkInfo(id) {
        var url = prefix + "/checkInfo?payDetailId=" + id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核信息",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 跳转反确认页面
     */
    function oppositeAffirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("应付单据只能为已确认状态下才能反确认");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应付单据");
                return;
            }
        }

        var payDetailIds = $.table.selectColumns("payDetailId");
        if (payDetailIds.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.open("反确认", prefix + "/back_confirm/" + payDetailIds, 500, 300);
    }


    /**
     * 生成对账单的方法
     */
    function checking() {

        var rows = $.table.selectColumns("payDetailId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var  isNtocc = bootstrapTable[0]["isNtocc"];

        var carrierId = bootstrapTable[0]["carrierId"];//承运商

        var  lotG7End = bootstrapTable[0]["lotG7End"];

        for (var i = 0; i < bootstrapTable.length; i++) {
           /* if (bootstrapTable[i]["isNtocc"] !== isNtocc ) {
                $.modal.alertWarning("请选择同为符合或不符合无车承运人的应付单");
                return;
            }*/

            if (bootstrapTable[i]["carrierId"] !== carrierId ) {
                $.modal.alertWarning("请选择相同承运商下的应付单");
                return;
            }

            if (bootstrapTable[i]["lotG7End"] !== lotG7End ) {
                $.modal.alertWarning("G7与非G7的应付单无法加入相同对账包");
                return;
            }

            if (bootstrapTable[i]["vbillstatus"] !== 1 && bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("生成对账单的应付单据只能为新建或已确认状态");
                return;
            }
            //1单笔 2月度
            if (bootstrapTable[i]["balaMethod"] != 1 ) {
                $.modal.alertWarning("该承运商为月度付款不能生成对账单");
                return;
            }

        }



        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.openTab("生成对账单", prefix + "/checking?payDetailIds=" + rows.join()+"&lotG7End="+lotG7End);
    }


    /**
     * 加入对账单的方法
     */
    function insertChecking() {

        var rows = $.table.selectColumns("payDetailId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var  isNtocc = bootstrapTable[0]["isNtocc"];

        var carrierId = bootstrapTable[0]["carrierId"];//承运商

        for (var i = 0; i < bootstrapTable.length; i++) {
            /*if (bootstrapTable[i]["isNtocc"] !== isNtocc ) {
                $.modal.alertWarning("请选择同为符合或不符合无车承运人的应付单");
                return;
            }*/
            if (bootstrapTable[i]["carrierId"] !== carrierId ) {
                $.modal.alertWarning("请选择相同承运商下的应付单");
                return;
            }
            if (bootstrapTable[i]["vbillstatus"] !== 1 && bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("加入对账单的应付单据只能为新建或已确认状态");
                return;
            }
            //1单笔 2月度
            if (bootstrapTable[i]["balaMethod"] != 1 ) {
                $.modal.alertWarning("该承运商为月度付款不能加入对账单");
                return;
            }

        }

        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.open("加入对账单", prefix + "/insertChecking?carrierId=" + carrierId + "&payDetailIds=" + rows + "&isNtocc=" + isNtocc);
    }

    /**
     * 批量删除
     */
    function remove() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("只能删除新建状态下的应付单据");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应付单据");
                return;
            }
        }
        var rows = $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var oilCardNumber = $.table.selectColumns("oilCardNumber");
        $.modal.confirm("确认要删除选中的数据吗?", function () {
            var url = prefix + "/remove";
            var data = {"ids": rows.join(),"oilCardNumbers":oilCardNumber.join()};
            $.operate.submit(url, "post", "json", data);
        });
    }

    /**
     * 分批付款
     */
    function batchPay(){
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["vbillstatus"] !== 1 && bootstrapTable[0]["vbillstatus"] !== 3) {
            $.modal.alertWarning("请选择已确认/部分核销的应付单");
            return;
        }

        if (bootstrapTable[0]["transFeeCount"] === bootstrapTable[0]["gotAmount"]) {
            $.modal.alertWarning("该应付单已完成");
            return;
        }
        var url = prefix + "/batchPay?payDetailId="+bootstrapTable[0]["payDetailId"];
        $.modal.open('分批付款',url);
    }

    /**
     * 申请付款
     */
    function applyPay() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["vbillstatus"] !== 1) {
            $.modal.alertWarning("请选择已确认的应付单");
            return;
        }
        var balaMethod = bootstrapTable[0]["balaMethod"];
        if(balaMethod == null){
            $.modal.alertWarning("请在基础承运商维护结算方式");
            return;
        }
        if (bootstrapTable[0]["balaMethod"] == 2) {
            $.modal.alertWarning("该应付单下的承运商未选择单笔付款");
            return;
        }

        // if (bootstrapTable[0]["freeType"] === '0'){
        //     if(bootstrapTable[0]["costTypeFreight"] != '1' && bootstrapTable[0]["costTypeFreight"] != '3' && bootstrapTable[0]["costTypeFreight"] != '5'){
        //         if (bootstrapTable[0]["carrBankId"] == null || bootstrapTable[0]["carrBankId"] == "" || bootstrapTable[0]["carrBankId"] == 'undefined') {
        //             $.modal.alertWarning("收款人信息为空无法进行付款申请");
        //             return;
        //         }
        //     }
        // }

        var url = prefix + "/applyPay?payDetailId="+bootstrapTable[0]["payDetailId"];
        $.modal.open("申请付款", url);

    }

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.status = $.common.join($('#status').selectpicker('val'));
        data.costTypeFreight = $.common.join($('#costTypeFreight').selectpicker('val'));
        $.table.search('role-form', data);
    }


    //付款记录
    function payRecord() {
        var id = $.table.selectColumns('payDetailId');
        var url =  ctx + "payManage" + "/payRecord?payDetailId="+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "付款记录",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }
    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        //应付单状态默认新建
        $('#status').selectpicker('val',payDetailStatusEnum[0].value);
        searchPre();
    }
</script>
</body>
</html>