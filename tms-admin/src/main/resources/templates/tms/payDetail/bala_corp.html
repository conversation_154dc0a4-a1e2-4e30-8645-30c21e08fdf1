<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('成本分摊列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "payDetail";
    var payDetailId = [[${payDetailId}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    $(function () {
        var options = {
            url: prefix + "/balaCorpList?payDetailId="+payDetailId,
            showToggle: false,
            showColumns: true,
            modalName: "成本分摊",
            fixedColumns: true,
            fixedNumber: 0,
            columns: [

                {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceNo'
                },
                {
                    title: '客户名称',
                    align: 'left',
                    field: 'custAbbr'
                },{
                    title: '提货日期',
                    align: 'left',
                    field: 'actDeliDate'
                },

                {
                    title: '到货日期',
                    align: 'left',
                    field: 'actArriDate'
                },
                {
                    title:'提货地址',
                    align:'left',
                    field:'deliDetailAddr'
                },
                {
                    title:'到货地址',
                    align:'left',
                    field:'arriDetailAddr'
                },
                {
                    title: '运单号',
                    align: 'left',
                    field: 'lot'
                }
                ,
                {
                    title: '结算公司',
                    align: 'left',
                    field: 'invoiceNo',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(balaCorp,  value.substring(0, 2));
                    }
                },
                {
                    title: '金额',
                    align: 'right',
                    field:'costShare',
                    formatter: function (value, row, index) {
                        return row.costShare.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }

            ]
        };
        $.table.init(options);
    });



</script>
</body>
</html>