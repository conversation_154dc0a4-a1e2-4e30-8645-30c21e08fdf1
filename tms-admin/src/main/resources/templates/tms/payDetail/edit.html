<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细修改')"/>
</head>

<body>
<div class="form-content">
    <form id="form-payDetail-edit" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <input name="payDetailId" type="hidden" id="payDetailId" th:value="${payDetail.payDetailId}">
                        <input name="lotId" type="hidden" id="lotId" th:value="${payDetail.lotId}">
                        <input name="corDate" type="hidden" th:value="${#dates.format(payDetail.corDate, 'yyyy-MM-dd HH:mm:ss')}"/>

                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        运单号：</label>
                                    <div class="col-sm-7">

                                        <input name="lotno" id="lotno" class="form-control dis" type="text"
                                               disabled th:value="${payDetail.lotno}">

                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        结算公司：</label>
                                    <div class="col-sm-7">
                                        <input class="form-control dis" type="text"
                                               maxlength="30" disabled th:value="${payDetail.balaCorpName}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        承运商：</label>
                                    <div class="col-sm-7">
                                        <input name="carrName" id="carrName" type="text"
                                               class="form-control dis" disabled th:value="${payDetail.carrName}" >
                                        <input name="carrierId" type="hidden" id="carrierId" th:value="${payDetail.carrierId}">
                                        <input name="carrCode" type="hidden" id="carrCode" th:value="${payDetail.carrCode}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        司机：</label>
                                    <div class="col-sm-7">
                                        <input name="driverName" id="driverName" class="form-control dis" type="text"
                                               maxlength="30" disabled th:value="${payDetail.driverName}">
                                        <input name="driverMobile" type="hidden" id="driverMobile" th:value="${payDetail.driverMobile}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        提货日期：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control dis"
                                               name="reqDeliDate" id="reqDeliDate" autocomplete="off" disabled
                                               th:value="${#dates.format(payDetail.reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        到货日期：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control dis"
                                               name="reqArriDate" id="reqArriDate" autocomplete="off" disabled
                                               th:value="${#dates.format(payDetail.reqArriDate, 'yyyy-MM-dd HH:mm:ss')}">
                                    </div>
                                </div>
                            </div>


                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5 color">
                                        油卡卡号：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control dis"  maxlength="25"
                                               autocomplete="off" id="oilCardNumber" name="oilCardNumber"
                                               th:value="${payDetail.oilCardNumber}" >
                                        <input id="historyCardNumber" name="historyCardNumber" type="text"
                                               th:value="${payDetail.oilCardNumber}" style="display:none" />
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        收款人：</label>
                                    <div class="col-sm-7">
                                        <select name="carrBankId" id="carrBankId" class="form-control valid" onchange="bankChange()">
                                            <option value="">--请选择--</option>
                                            <option th:each="dict : ${carrBankList}" th:text="${dict.bankAccount}"
                                                    th:value="${dict.carrBankId}" th:field="${payDetail.carrBankId}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        收款卡号：</label>
                                    <div class="col-sm-7">
                                        <input id="recCardNo" class="form-control dis" type="text" th:value="${payDetail.recCardNo}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        收款银行：</label>
                                    <div class="col-sm-7">
                                        <input id="recBank" class="form-control dis" type="text" th:value="${payDetail.recBank}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        费用金额(元)：</label>
                                    <div class="col-sm-7">
                                        <input name="transFeeCount" id="transFeeCount" class="form-control dis" type="text"  oninput="$.numberUtil.onlyNumberNegative(this)"
                                               required th:value="${payDetail.transFeeCount}" disabled>
                                        <input name="freeType" id="freeType" th:value="${payDetail.freeType}" type="hidden">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6" id="free">
                                <div class="form-group">
                                    <label class="col-sm-5">费用类型：</label>
                                    <div class="col-sm-7">
                                        <select name="costTypeOnWay" id="costTypeOnWay" class="form-control valid"
                                                th:with="type=${@dict.getType('cost_type_on_way')}" >
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}" th:field="${payDetail.costTypeOnWay}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6" id="freight">
                                <div class="form-group">
                                    <label class="col-sm-5">付款类型：</label>
                                    <div class="col-sm-7">
                                        <select name="costTypeFreight" id="costTypeFreight" class="form-control valid"
                                                th:with="type=${@dict.getType('cost_type_freight')}" onchange="typeChange()">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}" th:field="${payDetail.costTypeFreight}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-3">备注：</label>
                                    <div class="col-sm-12">
                                            <textarea name="memo" id="memo" maxlength="500" class="form-control valid"
                                                      rows="3" th:text="${payDetail.memo}" th:value="${payDetail.memo}"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--基础信息 end-->
                    </div>
                </div>
            </div>


        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFour">地址信息</a>

                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseFour">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabThree" class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 10%;">提货地省</th>
                                <th style="width: 10%;">提货地市</th>
                                <th style="width: 10%;">提货地区</th>
                                <th style="width: 10%;">到货地省</th>
                                <th style="width: 10%;">到货地市</th>
                                <th style="width: 10%;">到货地区</th>

                            </tr>
                            </thead>
                            <tbody id="address">
                            <tr>
                                <td>
                                    <div class="input-group"  id="deliProvince" th:text="${entrustLot.deliProvinceName}"></div>
                                </td>
                                <td>
                                    <div class="input-group"  id="deliCity" th:text="${entrustLot.deliCityName}"></div>
                                </td>
                                <td>
                                    <div class="input-group"  id="deliArea" th:text="${entrustLot.deliAreaName}"></div>
                                </td>
                                <td>
                                    <div class="input-group"  id="arriProvince" th:text="${entrustLot.arriProvinceName}"></div>
                                </td>
                                <td>
                                    <div class="input-group"  id="arriCity" th:text="${entrustLot.arriCityName}"></div>
                                </td>
                                <td>
                                    <div class="input-group"  id="arriArea" th:text="${entrustLot.arriAreaName}"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--end-->
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFive">运力信息</a>

                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseFive">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabFour" class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 10%;">车牌号</th>
                                <th style="width: 10%;">车长</th>
                                <th style="width: 10%;">车型</th>
                            </tr>
                            </thead>
                            <tbody  id="car">
                            <tr >
                                <td>
                                    <div class="input-group"   th:text="${carno}"></div>
                                </td>
                                <td>
                                    <div class="input-group"   th:text="${carLenName}"></div>
                                </td>
                                <td>
                                    <div class="input-group"   th:text="${carTypeName}"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--end-->
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseSix">异常事故</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseSix">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabFive" class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 10%;">异常分类</th>
                                <th style="width: 10%;">跟踪信息</th>
                                <th style="width: 10%;">异常发生时间</th>
                                <th style="width: 10%;">预计到达时间</th>
                            </tr>
                            </thead>
                            <tbody id="exp" >
                            <tr th:each="mapS,status:${exp}">
                                <td>
                                    <div class="input-group"   th:text="${mapS.expType}"></div>
                                </td>
                                <td>
                                    <div class="input-group"   th:text="${mapS.note}"></div>
                                </td>

                                <td>
                                    <div class="input-group"   th:text="${#dates.format(mapS.expOccTime, 'yyyy-MM-dd')}"></div>
                                </td>
                                <td>
                                    <div class="input-group"   th:text="${#dates.format(mapS.estArrivalTime, 'yyyy-MM-dd')}"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--end-->
                </div>
            </div>
        </div>

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="commit()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">
    var prefix = ctx + "payDetail";
    var flag = '0';
    var carrBankList = [[${carrBankList}]];
    $(function () {
        var costTypeFreight = [[${payDetail.costTypeFreight}]];
        if (costTypeFreight === '1' || costTypeFreight === '3' || costTypeFreight === '5'){
            $("#oilCardNumber").attr("required","required");
            $(".color").attr("style","color: red");
        } else {
            $("#oilCardNumber").attr("disabled","disabled");
        }

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');

        var options = {};
        $.table.init(options);
        var freeType =  $("#freeType").val();
        // 费用类型为运费时 去除选择在途费用类型框
        if (freeType === '0'){
            var elem=document.getElementById('free');
            elem.parentNode.removeChild(elem);
        }
        //当费用类型为在途费用是，付款类型不可见
        if(freeType === '1'){
            $("#freight").attr("style","display:none");
        }

        $("#form-payDetail-edit").validate({
            onkeyup: false,
            focusCleanup: true
        });

        //油卡添加校验

        $("#oilCardNumber").rules("add", {
            remote: {
                url: ctx + "basic/fuelCard/checkEditCardNumber",
                type: "post",
                async: false,
                dataType: "json",
                data: {
                    fuelcardNo : function() {
                        return $.common.trim($("#oilCardNumber" ).val());
                    },
                    payDetailId : function() {
                        return $.common.trim($("#payDetailId" ).val());
                    },
                    carrierId : function() {
                        return $.common.trim($("#carrierId" ).val());
                    }
                },
                dataFilter: function(data, type) {
                    flag = data;
                    return $.validate.unique(data);
                }
            },
            messages: {
                remote: "无效油卡"
            }
        });
    });

    /**
     * 费用类型修改
     */
    function typeChange() {
        var costTypeFreight = $("#costTypeFreight").val();
        if (costTypeFreight === '1' || costTypeFreight === '3' || costTypeFreight === '5'){
            $("#oilCardNumber").attr("required","required");
            $(".color").attr("style","color: red");
            $("#oilCardNumber").removeAttr("disabled");
        }else{
            $("#oilCardNumber").removeAttr("required");
            $(".color").removeAttr("style ");
            $("#oilCardNumber").attr("disabled","disabled");
        }
    }

    /**
     * 运单选择框
     */
    function selectLot() {

        $.modal.open("选择运单", prefix + "/chooseLot");
    }

    /**
     * 表单提交
     */
    function commit(){
        if ($.validate.form()) {
            if (flag === '0' || $("#oilCardNumber").val() === '') {
                $('.dis').removeAttr("disabled");
                // 表单提交
                $.operate.saveTab(prefix + "/editPayDetail", $('#form-payDetail-edit').serialize());
            }
        }
    }

    //收款人更换
    function bankChange() {
        $("#recCardNo").val("");
        $("#recBank").val("");
        for(var i = 0 ; i < carrBankList.length ; i++){
            if(carrBankList[i].carrBankId == $("#carrBankId").val()){
                $("#recCardNo").val(carrBankList[i].bankCard);
                $("#recBank").val(carrBankList[i].bankName);
            }
        }
    }

</script>
</body>

</html>