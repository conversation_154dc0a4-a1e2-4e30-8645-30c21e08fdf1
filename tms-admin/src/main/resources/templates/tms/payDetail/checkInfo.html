<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('成本分摊列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "payDetail";
    var payDetailId = [[${payDetailId}]];
    var checkStatusList = [[${checkStatusList}]];
    $(function () {
        var options = {
            url: prefix + "/checkInfoList?payDetailId="+payDetailId,
            showToggle: false,
            showColumns: true,
            modalName: "成本分摊",
            fixedColumns: true,
            fixedNumber: 0,
            columns: [

                {
                    title: '审核状态',
                    align: 'left',
                    field: 'checkStatus',
                    formatter: function status(value,row) {
                        var context = '';
                        checkStatusList.forEach(function (v) {
                            if (v.value == value) {
                                context = v.context;
                            }
                        });
                        return context;
                    }

                },
                {
                    title: '审核人',
                    align: 'left',
                    field: 'checkMan'
                },{
                    title: '审核时间',
                    align: 'left',
                    field: 'checkDate'
                },

                {
                    title: '备注',
                    align: 'left',
                    field: 'memo'
                }
            ]
        };
        $.table.init(options);
    });



</script>
</body>
</html>