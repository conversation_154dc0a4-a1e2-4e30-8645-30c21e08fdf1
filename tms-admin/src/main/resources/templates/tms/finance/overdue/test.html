<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('百旺测试')" />
</head>

<body>
<input type="button" value="税控盘信息查询" onclick="skpxxcx()">
<div id="info"></div>

<input type="button" value="注册码信息导入" onclick="zcmdr()">
<div id="info7"></div>


<input type="button" value="购票信息查询" onclick="gpxxcx()">
<div id="info2"></div>

<input type="button" value="页边距设置" onclick="ybjsz()">
<input id="top" value="0">
<input id="left" value="-17">
<div id="info3"></div>

<input type="button" value="发票开具" onclick="fpkj()">
<div id="info5"></div>

<input type="button" value="发票打印" onclick="fpdy()">
<div id="info4"></div>

<input type="button" value="发票查询" onclick="fpcx()">
<div id="info8"></div>

<input type="button" value="批量打印" onclick="pldy()">
<div id="info9"></div>

<div th:include="include :: footer"></div>
<script th:src="@{/js/jquery.json2xml.js}"></script>
<script th:src="@{/js/jquery.xml2json.js}"></script>
<script th:inline="javascript">
	$(function(){
		skpxxcx()
	});
	var info = null;
	function skpxxcx() {
		var business = {};
		business['@comment'] = "税控盘信息查询"
		business['@id'] = "SKPXXCX"
		var body = {}
		business['body'] = body
		body['@yylxdm'] = "1"
		body['input'] = {}
		body['input']['skpkl'] = "88888888"
		var xml_arg = $.json2xml(business, {"rootTagName": "business","indentString": "","formatOutput": false})
		$("#info").text(xml_arg)
		$.ajax({
			url: 'http://127.0.0.1:12366/SKServer/SKDo',
			type: 'post',
			timeout: 2000,
			data: xml_arg,
			success: function (xml_res) {
				//<?xml version="1.0" encoding="gbk"?>
				//<business comment="税控盘信息查询" id="SKPXXCX">
				//<body yylxdm="1">
				//<output>
				//<skpbh>499905116530</skpbh>税控盘编号
				//<nsrsbh>913206007974104798</nsrsbh>纳税人识别号
				//<nsrmc>南通吉华物流有限公司</nsrmc>纳税人名称
				//<swjgdm>***********</swjgdm>税务机关代码
				//<swjgmc>南通市崇川区</swjgmc>税务机关名称
				//<fplxdm>004007</fplxdm>发票类型代码
				//<dqsz>20211227165229</dqsz>当前时钟
				//<qysj>20160219093219</qysj>启用时间
				//<bbh>1000141021</bbh>版本号
				//<kpjh>0</kpjh>开票机号
				//<qylx>00</qylx>企业类型
				//<blxx>0000010000000000</blxx>保留信息
				//<qtkzxx/>其它扩展信息
				//<returncode>00000000</returncode>
				//<returnmsg>成功</returnmsg>
				//</output>
				//</body>
				//</business>
				info = $.xml2json(xml_res).body.output;
				$("#info").append("<br>");
				$("#info").append(JSON.stringify(info))
			},
			complete: function(XMLHttpRequest, textStatus) {
				if (textStatus == 'timeout') {
					$.modal.alertWarning("未检测到百旺SDK服务，未启动服务或尚未安装？");
				} else if (textStatus == "parsererror" || textStatus == "error") {
					$.modal.alertWarning("百旺SDK服务返回" + textStatus + "，请联系管理员！");
				}
			}
		});

	}

	function zcmdr() {
		/*
        <?xml version="1.0" encoding="gbk"?>
        <business comment="注册码信息导入" id="ZCMDR">
            <body yylxdm="1">
                <input>
                    <zcmxx>注册码信息</zcmxx>
                </input>
            </body>
        </business>
        */
		var business = {};
		business['@comment'] = "注册码信息导入"
		business['@id'] = "ZCMDR"
		var body = {}
		business['body'] = body
		body['@yylxdm'] = "1"
		var input = {}
		body['input'] = input
		input['zcmxx'] = 'LuzpyP7JDnWFZrC2YlgrLht7KxZedGjgJIojyRqFZXjrXtxhodDuTW//ImyLm0p9HP1HteFDf8GrcfEORMNNb90GpLJTNGAxNBa9MN1pDoOrYQB2d+PeIwnY8Hlg4y9x4301ec1e3f80149debd4ba9015577b07'
		invoke(business, '#info7')
	}

	function xml2string(xml) {
		return new XMLSerializer().serializeToString(xml).replace(/</g, '&lt;').replace(/>/g, '&gt;')
	}

	function gpxxcx() {
		/*<?xml version="1.0" encoding="gbk"?>
        <business comment="购票信息查询" id="GPXXCX">
            <body yylxdm="1">
            <input>
                <nsrsbh>纳税人识别号</nsrsbh>
                <skpbh>税控盘编号</skpbh>
                <skpkl>税控盘口令</skpkl>
                <keypwd>税务数字证书密码</keypwd>
                <fplxdm>发票类型代码</fplxdm>
            </input>
            </body>
        </business>*/
		var business = {};
		business['@comment'] = "购票信息查询"
		business['@id'] = "GPXXCX"
		var body = {}
		business['body'] = body
		body['@yylxdm'] = "1"
		body['input'] = {}
		body['input']['nsrsbh'] = info.nsrsbh
		body['input']['skpbh'] = info.skpbh
		body['input']['skpkl'] = "88888888"
		body['input']['keypwd'] = "123456"
		body['input']['fplxdm'] = "004"; // 专票004，普票：007
		invoke(business, "#info2")
	}

	var cur_fp = null; // 当前发票
	function fpkj() {
		return
		/*
        <?xml version="1.0" encoding="gbk"?>
        <business comment="发票开具" id="FPKJ">
            <body yylxdm="1">1：国税 2：地税
                <input>
                    <skpbh>税控盘编号</skpbh>
                    <skpkl>税控盘口令</skpkl>
                    <keypwd>数字证书密码</keypwd>
                    <svrIp>网络IP地址</svrIp>tspz=“12”时不能为空
                    <svrPort>端口号</svrPort>tspz=“12”时不能为空
                    <fplxdm>004</fplxdm>004、007
                    <kplx>0</kplx>0：正数发票开具1：负数发票开具（红冲）
                    <tspz>特殊票种标识</tspz>为空则默认为00-不是01-农产品销售02-农产品收购08-成品油12-机动车
                    <xhdwsbh>销货单位识别号</xhdwsbh>与税控盘保持一致[nsrsbh]
                    <xhdwmc>销货单位名称</xhdwmc>以税控盘中存储为准[nsrmc]
                    <xhdwdzdh>销货单位地址电话</xhdwdzdh>
                    <xhdwyhzh>销货单位银行帐号</xhdwyhzh>
                    <ghdwsbh>购货单位识别号</ghdwsbh>
                    <ghdwmc>购货单位名称</ghdwmc>
                    <ghdwdzdh>购货单位地址电话</ghdwdzdh>
                    <ghdwyhzh>购货单位银行帐号</ghdwyhzh>
                    <bmbbbh>编码表版本号</bmbbbh>商品编码的总版本号[可空]
                    <hsslbs>含税税率标识</hsslbs>0-普通征收1-减按计增（仅支持税率0.015）2-差额征收
                    <fyxm count="1">开具非清单发票行数
                        <group xh="1">
                            <fphxz>发票行性质</fphxz>0 正常行1 折扣行2 被折扣行
                            <spmc>商品名称</spmc>
                            <spsm>商品税目</spsm>[可空]
                            <ggxh>规格型号</ggxh>[可空]
                            <dw>单位</dw>[可空]tspz=12时，此处为“辆”
                            <spsl>商品数量</spsl>[可空]小数点后16位tspz=12时，ggxh非空，此处为1；ggxh为空，可≥1
                            <dj>单价</dj>[可空]小数点后16位
                            <je>金额</je>小数点后2位
                            <kcje>扣除金额</kcje>小数点后2位，只有在差额征收时才添加此节点。
                            <sl>税率</sl>小数点后2位
                            <se>税额</se>小数点后2位
                            <hsbz>含税标志</hsbz>0 不含税1 含税
                            <spbm>商品编码</spbm>下载的商品编码类
                            <zxbm>纳税人自行编码</zxbm>纳税人自己增加的
                            <yhzcbs>优惠政策标识</yhzcbs>0是不使用，1是使用
                            <slbs>0税率标识</slbs>空，是正常税率1:是免税2:是不征税3:普通零税率
                            <zzstsgl>增值税特殊管理</zzstsgl>优惠政策的名称
                        </group>
                    </fyxm>
                    <zhsl></zhsl>[可空]正数票可为空,负数票：对应蓝票相同税率时“综合税率填写sl”;对应蓝票不同税率时“综合税率99.01”
                    <hjje>合计金额</hjje>小数点后2位
                    <hjse>合计税额</hjse>小数点后2位
                    <jshj>价税合计</jshj>小数点后2位
                    <bz>备注</bz>[可空]
                    <skr>收款人</skr>[可空]
                    <fhr>复核人</fhr>[可空]
                    <kpr>开票人</kpr>
                    <jmbbh>加密版本号</jmbbh>[可空]
                    <zyspmc>主要商品名称</zyspmc>[可空]
                    <spsm>商品税目</spsm>[可空]
                    <qdbz>清单标志</qdbz>0 无清单1 有清单
                    <ssyf>所属月份</ssyf>YYYYMM[可空]
                    <kpjh>开票机号</kpjh>
                    <tzdbh>通知单编号</tzdbh>16位数字，最后以为校验位[可空]
                    <yfpdm>原发票代码</yfpdm>正数票时为空[可空]
                    <yfphm>原发票号码</yfphm>正数票时为空[可空]
                    <qmcs>签名参数</qmcs>固定“0000004282000000”
                    <jybz>校验标志</jybz>1: 不校验单价数量
                </input>
            </body>
        </business>
        */
		var business = {};
		business['@comment'] = "发票开具"
		business['@id'] = "FPKJ"
		var body = {}
		business['body'] = body
		body['@yylxdm'] = "1"
		var input = {}
		body['input'] = input
		input['skpbh'] = info.skpbh
		input['skpkl'] = "88888888"
		input['keypwd'] = "123456"
		input['fplxdm'] = "004" // "004", "007"
		input['kplx'] = 0 //0：正数发票开具1：负数发票开具（红冲）
		//<tspz>特殊票种标识</tspz>为空则默认为00-不是01-农产品销售02-农产品收购08-成品油12-机动车
		input['xhdwsbh'] = info.nsrsbh
		input['xhdwmc'] = info.nsrmc
		input['xhdwdzdh'] = "南通市港闸区安顺路2号0513-82520002";
		input['xhdwyhzh'] = "江苏银行南通港闸支行89321015201120060216"
		input['ghdwsbh'] = '91320600717452733F'
		input['ghdwmc'] = '南通江天化学股份有限公司'
		input['ghdwdzdh'] = '南通市经济技术开发区中央路16号 0513-83599160'
		input['ghdwyhzh'] = '中国银行南通开发区支行 492358202466'
		//<bmbbbh>编码表版本号</bmbbbh>商品编码的总版本号[可空]
		input['hsslbs'] = 0//0-普通征收1-减按计增（仅支持税率0.015）2-差额征收
		var fyxm = {}
		input['fyxm'] = fyxm
		var group = [{}]
		fyxm['@count'] = group.length
		fyxm['group'] = group
		group[0]['@xh'] = 1
		group[0]['fphxz'] = 0 //0=正常行 1=折扣行 2=被折扣行
		group[0]['spmc'] = "*运输服务*运输费" // 下拉框
		//<spsm>商品税目</spsm>[可空]
		group[0]['ggxh'] = '半缩醛'//[可空]
		group[0]['dw'] = '吨'//单位</dw>[可空]tspz=12时，此处为“辆”
		group[0]['spsl'] = '0.125';//>商品数量</spsl>[可空]小数点后16位tspz=12时，ggxh非空，此处为1；ggxh为空，可≥1
		group[0]['dj'] = 4403.66972477//单价</dj>[可空]小数点后16位
		group[0]['je'] = 550.46//金额</je>小数点后2位
		//<kcje>扣除金额</kcje>小数点后2位，只有在差额征收时才添加此节点。
		group[0]['sl'] = 0.09//税率</sl>小数点后2位
		group[0]['se'] = 49.54//税额</se>小数点后2位
		group[0]['hsbz'] = 1//含税标志</hsbz>0 不含税1 含税
		//group[0]['spbm'] = ? //>商品编码</spbm>下载的商品编码类
		//<zxbm>纳税人自行编码</zxbm>纳税人自己增加的
		group[0]['yhzcbs'] = 0 //优惠政策标识 0是不使用，1是使用
		//<slbs>0税率标识</slbs>空，是正常税率1:是免税2:是不征税3:普通零税率
		//<zzstsgl>增值税特殊管理</zzstsgl>优惠政策的名称

		//<zhsl></zhsl>[可空]正数票可为空,负数票：对应蓝票相同税率时“综合税率填写sl”;对应蓝票不同税率时“综合税率99.01”
		input['hjje'] = 550.46//合计金额 小数点后2位
		input['hjse'] = 49.54 //合计税额 小数点后2位
		input['jshj'] = 600//价税合计 小数点后2位
		input['bz'] = '品名：半缩\n醛'//备注</bz>[可空]
		input['skr'] = '收款人'//[可空]
		input['fhr'] = '复核人'//[可空]
		input['kpr'] = '开票人'
		//<jmbbh>加密版本号</jmbbh>[可空]
		//<zyspmc>主要商品名称</zyspmc>[可空]
		//<spsm>商品税目</spsm>[可空]
		input['qdbz'] = 0//清单标志</qdbz>0 无清单1 有清单
		//<ssyf>所属月份</ssyf>YYYYMM[可空]
		input['kpjh'] = info.kpjh //开票机号</kpjh>
		//<tzdbh>通知单编号</tzdbh>16位数字，最后以为校验位[可空]
		//<yfpdm>原发票代码</yfpdm>正数票时为空[可空]
		//<yfphm>原发票号码</yfphm>正数票时为空[可空]
		input['qmcs'] = '0000004282000000' //签名参数</qmcs>固定“0000004282000000”
		//input['jybz'] = 1 //校验标志</jybz>1: 不校验单价数量
		invoke(business, '#info5', function(output) {
			cur_fp = output;
			cur_fp['fplxdm'] = business.body.input.fplxdm;
		})
	}

	function ybjsz() {
		/*
        <?xml version="1.0" encoding="gbk"?>
        <business comment="页边距设置" id="YBJSZ">
            <body yylxdm="1">
                <input>
                    <fplxdm>发票类型代码</fplxdm>
                    <top>打印上边距</top>单位：毫米
                    <left>打印左边距</left>
                </input>
            </body>
        </business>
        */
		var top = $("#top").val()
		var left = $("#left").val()
		var business = {};
		business['@comment'] = "页边距设置"
		business['@id'] = "YBJSZ"
		var body = {}
		business['body'] = body
		body['@yylxdm'] = "1"
		var input = {}
		body['input'] = input
		input['fplxdm'] = "004"
		input['top'] = top // 0
		input['left'] = left // -17
		invoke(business, '#info3')
	}

	function fpcx() {
		/*
        <?xml version="1.0" encoding="gbk"?>
        <business comment="发票查询" id="FPCX">
            <body yylxdm="1">
                <input>
                    <nsrsbh>纳税人识别号</nsrsbh>
                    <skpbh>税控盘编号</skpbh>
                    <skpkl>税控盘口令</skpkl>
                    <keypwd>税务数字证书密码</keypwd>
                    <fplxdm>发票类型代码</fplxdm>
                    <cxfs>查询方式</csfs>0：按发票号码段来读1：按时间段来读
                    <cxtj>查询条件</cstj>cxfs为0时：10位发票代码+8位起始号码+8位终止号码cxfs 为1是：起始日期（YYYYMMDD）+终止日期（YYYYMMDD）
                    <cxlx>查询类型</cxlx>0所有票， 1未上传
                </input>
            </body>
        </business>
        */
		var business = {};
		business['@comment'] = "发票查询"
		business['@id'] = "FPCX"
		var body = {}
		business['body'] = body
		body['@yylxdm'] = "1"
		var input = {}
		body['input'] = input
		input['nsrsbh'] = info.nsrsbh
		input['skpbh'] = info.skpbh
		input['skpkl'] = "88888888"
		input['keypwd'] = "123456"
		input['fplxdm'] = "004"
		input['cxfs'] = "1"
		input['cxtj'] = "2021122820211228"
		input['cxlx'] = "0"
		invoke(business, '#info8')
	}

	function fpdy() {
		/*
        <?xml version="1.0" encoding="gbk"?>
        <business comment="发票打印" id="FPDY">
            <body yylxdm="1">
                <input>
                    <nsrsbh>纳税人识别号</nsrsbh>
                    <skpbh>税控盘编号</skpbh>
                    <skpkl>税控盘口令</skpkl>
                    <keypwd>税务数字证书密码</keypwd>
                    <fplxdm>发票类型代码</fplxdm>
                    <fpdm>发票代码</fpdm>
                    <fphm>发票号码</fphm>单张打印时为8，批量打印时，起始号码+终止号码=16位
                    <dylx>打印类型</dylx>0：发票打印，1：清单打印	不弹对话框如下	9：发票打印，8：清单打印
                    <dyfs>打印方式</dyfs>0：批量打印 1：单张打印
                    <dyjmc>打印机名称</dyjmc>填写名称：为指定的打印机	不填名称：为电脑默认的打印机
                </input>
            </body>
        </business>
        */
		var business = {};
		business['@comment'] = "发票打印"
		business['@id'] = "FPDY"
		var body = {}
		business['body'] = body
		body['@yylxdm'] = "1"
		var input = {}
		body['input'] = input
		input['nsrsbh'] = info.nsrsbh
		input['skpbh'] = info.skpbh
		input['skpkl'] = "88888888"
		input['keypwd'] = "123456"
		input['fplxdm'] = "004"
		input['fpdm'] = "3200212130"//"发票开具生成的fpdm"
		input['fphm'] = "85467370"//"发票开具生成的fphm" //单张打印时为8，批量打印时，起始号码+终止号码=16位
		input['dylx'] = "9" // 0：发票打印，1：清单打印	不弹对话框如下	9：发票打印，8：清单打印
		input['dyfs'] = "1" // 0：批量打印 1：单张打印
		input['dyjmc'] = "EPSON LQ-730KII" // 填写名称：为指定的打印机	不填名称：为电脑默认的打印机
		invoke(business, '#info4')
	}
	function pldy() {

		var business = {};
		business['@comment'] = "发票打印"
		business['@id'] = "FPDY"
		var body = {}
		business['body'] = body
		body['@yylxdm'] = "1"
		var input = {}
		body['input'] = input
		input['nsrsbh'] = info.nsrsbh
		input['skpbh'] = info.skpbh
		input['skpkl'] = "88888888"
		input['keypwd'] = "123456"
		input['fplxdm'] = "004"
		input['fpdm'] = "3200212130"//"发票开具生成的fpdm"
		input['fphm'] = "8545933485459335"//"发票开具生成的fphm" //单张打印时为8，批量打印时，起始号码+终止号码=16位
		input['dylx'] = "9" // 0：发票打印，1：清单打印	不弹对话框如下	9：发票打印，8：清单打印
		input['dyfs'] = "0" // 0：批量打印 1：单张打印
		invoke(business, '#info9')
	}

	function invoke(business, infoDiv, callback) {
		var xml_arg = $.json2xml(business, {"rootTagName": "business","indentString": "","formatOutput": false})
		$(infoDiv).text(xml_arg)
		$.ajax({
			url: 'http://127.0.0.1:12366/SKServer/SKDo',
			type: 'post',
			data: xml_arg,
			success: function (xml_res) {
				$(infoDiv).append("<br>");
				$(infoDiv).append(xml2string(xml_res))
				var json = $.xml2json(xml_res);
				$(infoDiv).append("<br>");
				$(infoDiv).append(JSON.stringify(json))
				if (callback) {
					callback(json.body.output)
				}
			}
		});
	}
</script>
</body>
</html>