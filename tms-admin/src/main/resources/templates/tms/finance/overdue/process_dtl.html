<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('应收跟进')" />
	<th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
	/*.left-fixed-body-columns{
		height: calc(100% - 120px) !important;
	}*/
	.table-striped {
		height: calc(100% - 20px);
	}
</style>
<body class="gray-bg">
    <div class="container-div">
		<div class="row">
			<div class="btn-group-sm" id="toolbar" role="group">
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
			</div>
		</div>
	</div>

<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">

	var prefix = ctx + "finance/overdue";
	var sumAmount = 0;
	$(function() {
		//监听回车事件 回车搜索
		$(document).keyup(function(e){
			var key = e.which;
			if(key==13){
				$.table.search();
			}
		});

		var options = {
			url: prefix + "/process_dtl_list?customerId=[(${customerId})]",
			fixedColumns: false,
			fixedNumber: 0,
			showToggle: false,
			showFooter: true,
			height: 760,
			onRefresh:function(params){
				clearTotal();
			},
			onCheck: function (row,$element) {
				addTotal(row);
				setTotal();
			},
			onUncheck: function (row, $element) {
				subTotal(row);
				setTotal();
			},
			onCheckAll: function (rowsAfter) {
				clearTotal();
				//循环累加
				for (var row of rowsAfter) {
					addTotal(row);
				}
				//赋值
				setTotal();
			},
			onUncheckAll: function () {
				//总数清0
				clearTotal();
				//赋值
				setTotal();
			},
			onPostBody: function (params) {
				//合并页脚
				merge_footer();
				getAmountCount();
			},
			columns: [
				{
					title: '序号',
					formatter: function(value, row, index) {
						let option = $("#bootstrap-table").bootstrapTable('getOptions');
						let pageSize = option.pageSize;
						let pageNumber = option.pageNumber;
						return pageSize * (pageNumber - 1) + index + 1
					},
					//footerFormatter: function (row) {
					//	return "总金额：<nobr id='sumAmountTotal'>¥0.00</nobr>";
					//}
				},
				{
					title: '开票公司',
					field: 'BILLING_CORP'
				},
				{
					title: '开票抬头',
					field: 'BILLING_PAYABLE'
				},
				{
					title: '票号',
					field: 'CHECK_NO'
				},
				{
					title: '开票时间',
					field: 'BILLING_DATE',
					formatter: function(value,row,index) {
						if (value != null) {
							return value.substring(0, 10)
						}
					}
				},
				{
					title: '开票金额',
					field: 'BILLING_AMOUNT',
					align: "right",
					formatter: function(value,row,index) {
						if (value != null) {
							return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
						}
					}
				},
				{
					title: '已收金额',
					field: 'GOT_AMOUNT',
					align: "right",
					formatter: function(value,row,index) {
						if (value != null) {
							return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
						}
					}
				},
				{
					title: '未收金额',
					field: 'UNGOT_AMOUNT',
					align: "right",
					formatter: function(value,row,index) {
						if (value != null) {
							return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
						}
					}
				},
				{
					title: '逾期天数',
					field: 'OVERDUE_DAYS',
					align: 'right',
					formatter: function(value,row,index) {
						if (value != null) {
							return value < 0 ? 0 : value
						}
					}
				},
				{
					title: '收款期',
					field: 'COLLECTION_DAYS',
					align: 'right'
				},
				{
					title: '备注说明',
					field: 'BZSM'
				}
			]
		};
		$.table.init(options);

		/**
		 * 初始化日期控件
		 */
		layui.use('laydate', function() {
			var laydate = layui.laydate;
			laydate.render({
				elem: '#yearMonth',
				type: 'month',
				trigger: 'click'
			});
		});
	});

	function searchx() {
		var data = {};
		data.salesDept = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
		$.table.search('role-form', data);
	}
	/**
	 * 合并页脚
	 */
	function merge_footer() {
		var footer_tbody = $('.fixed-table-footer table tbody');
		var footer_tr = footer_tbody.find('>tr');
		var footer_td = footer_tr.find('>td');
		var footer_td_1 = footer_td.eq(0);
		//除了第一列其他都隐藏
		for(var i=1;i<footer_td.length;i++) {
			footer_td.eq(i).hide();
		}
		footer_td_1.attr('colspan', 1).show();
	}

	function getAmountCount() {
		/*var data = $.common.formToJSON("role-form");
		data.salesDept = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
		$.post(prefix + "/getCount", data, function(result){
			if (result.code == 0) {
				var data = result.data;
				$("#sumAmountTotal").text(data.SUMAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
			} else {
				$("#sumAmountTotal").text("ERROR");
			}
		});*/
	}



	/**
	 * 将总计金额清零
	 */
	function clearTotal() {
		//开票金额合计
		sumAmount = 0
	}

	/**
	 * 累计总金额
	 */
	function addTotal(row) {
		sumAmount += row.amount;
	}

	/**
	 *
	 */
	function subTotal(row) {
		sumAmount -= row.amount;
	}

	/**
	 *
	 * 给页脚总计赋值
	 */
	function setTotal() {
		//$("#sumAmount").text(sumAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
	}

	function detail(customerId, billingPayable, custBillingId) {

	}
</script>
</body>
</html>