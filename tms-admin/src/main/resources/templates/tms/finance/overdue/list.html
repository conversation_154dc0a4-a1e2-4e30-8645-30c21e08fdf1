<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('逾期统计')" />
	<th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
	/*.left-fixed-body-columns{
		height: calc(100% - 120px) !important;
	}*/
</style>
<body class="gray-bg">
    <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal">

					<div class="row">
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<input type="text" class="form-control"
										   id="yearMonth" name="yearMonth" placeholder="统计年月">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<input name="custAbbr" placeholder="客户简称" class="form-control valid" type="text"
										   aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<select name="salesDept" id="salesDeptId" class="form-control valid selectpicker"
											aria-invalid="false" data-none-selected-text="运营组" multiple>
										<option th:each="mapS,status:${salesDept}"
												th:value="${mapS.deptId}"
												th:text="${mapS.deptName}">
										</option>
									</select>
								</div>
							</div>
						</div>

						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="btn-group-sm" id="toolbar" role="group">
				<a class="btn btn-success" onclick="exportExcel()" shiro:hasPermission="finance:overdue:out">
					<i class="fa fa-download"></i> 导出
				</a>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
			</div>
		</div>
	</div>

<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">

	var prefix = ctx + "finance/overdue";
	var sumAmount = 0;
	$(function() {
		//监听回车事件 回车搜索
		$(document).keyup(function(e){
			var key = e.which;
			if(key==13){
				$.table.search();
			}
		});

		var options = {
			url: prefix + "/list",
			fixedColumns: true,
			fixedNumber: 3,
			showToggle: false,
			showFooter: true,
			height: 560,
			onRefresh:function(params){
				clearTotal();
			},
			onCheck: function (row,$element) {
				addTotal(row);
				setTotal();
			},
			onUncheck: function (row, $element) {
				subTotal(row);
				setTotal();
			},
			onCheckAll: function (rowsAfter) {
				clearTotal();
				//循环累加
				for (var row of rowsAfter) {
					addTotal(row);
				}
				//赋值
				setTotal();
			},
			onUncheckAll: function () {
				//总数清0
				clearTotal();
				//赋值
				setTotal();
			},
			onPostBody: function (params) {
				//合并页脚
				merge_footer();
				getAmountCount();
			},
			columns: [
				{
					title: '序号',
					formatter: function(value, row, index) {
						let option = $("#bootstrap-table").bootstrapTable('getOptions');
						let pageSize = option.pageSize;
						let pageNumber = option.pageNumber;
						return pageSize * (pageNumber - 1) + index + 1
					},
					//footerFormatter: function (row) {
					//	return "总金额：<nobr id='sumAmountTotal'>¥0.00</nobr>";
					//}
				},
				{
					title: '客户',
					field: 'custAbbr'
				},
				{
					title: '发票抬头',
					field: 'billingPayable'
				},
				{
					title: '统计年月<i class="fa fa-question-circle-o" style="color: #00a9ff" title="本月的统计到前一天"></i>',
					field: 'yearMonth',
					formatter: function (value,row,index) {
						return value.substring(0,7)
					}
				},
				{
					title: '月初逾期未回款',
					field: 'lastMonthFinalOverdue',
					formatter: function(value, row, index) {
						if (value != null) {
							return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
						}
					}
				},
				{
					title: '本月应回款(账期到账)',
					field: 'overdueAmount',
					align: 'right',
					formatter: function(value, row, index) {
						if (value != null) {
							return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
						}
					}
				},
				{
					title: '本月应回款<i class="fa fa-question-circle-o" style="color: #00a9ff" title="月初逾期未回款+本月应回款(账期到账)"></i>',
					field: 'shouldReceAmount',
					align: 'right',
					formatter: function(value, row, index) {
						if (value != null) {
							return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
						}
					}
				},
				{
					title: '本月实际回款',
					field: 'receAmount',
					align: 'right',
					formatter: function(value, row, index) {
						if (value != null) {
							return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
						}
					}
				},
				{
					title: '本月回款率<i class="fa fa-question-circle-o" style="color: #00a9ff" title="本月实际回款/本月应回款"></i>',
					field: 'x',
					align: "right",
					formatter: function(value, row, index) {
						if (row.receAmount && row.shouldReceAmount) {
							return (row.receAmount * 100 / row.shouldReceAmount).toFixed(2) + "%"
						}
						return "0.00%"
					}
				},
				{
					title: '月末累积逾期<i class="fa fa-question-circle-o" style="color: #00a9ff" title="本月应回款-本月实际回款"></i>',
					field: 'finalOverdue',
					align: 'right',
					formatter: function(value, row, index) {
						if (value != null) {
							return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
						}
					}
				},
				{
					title: '账期',
					field: 'collectionDays',
					align: "right"
				},
				{
					title: '总开票额',
					field: 'totalBillingAmount',
					align: "right",
					formatter: function(value, row, index) {
						if (value != null) {
							return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
						}
					}
				},
				{
					title: '逾期率<i class="fa fa-question-circle-o" style="color: #00a9ff" title="月末累积逾期/总开票额"></i>',
					field: 'overdueRate',
					align: "right",
					formatter: function(value,row){
						if (row.totalBillingAmount == 0) {
							return "0.00%";
						} else {
							return (row.finalOverdue * 100 / row.totalBillingAmount).toFixed(2) + '%';
						}
					}
				},
				{
					title: '运营组',
					field: 'salesDeptName'
				}
			]
		};
		$.table.init(options);

		/**
		 * 初始化日期控件
		 */
		layui.use('laydate', function() {
			var laydate = layui.laydate;
			laydate.render({
				elem: '#yearMonth',
				type: 'month',
				trigger: 'click'
			});
		});
	});

	function searchx() {
		var data = {};
		data.salesDept = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
		$.table.search('role-form', data);
	}
	/**
	 * 合并页脚
	 */
	function merge_footer() {
		var footer_tbody = $('.fixed-table-footer table tbody');
		var footer_tr = footer_tbody.find('>tr');
		var footer_td = footer_tr.find('>td');
		var footer_td_1 = footer_td.eq(0);
		//除了第一列其他都隐藏
		for(var i=1;i<footer_td.length;i++) {
			footer_td.eq(i).hide();
		}
		footer_td_1.attr('colspan', 1).show();
	}

	function getAmountCount() {
		/*var data = $.common.formToJSON("role-form");
		data.salesDept = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
		$.post(prefix + "/getCount", data, function(result){
			if (result.code == 0) {
				var data = result.data;
				$("#sumAmountTotal").text(data.SUMAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
			} else {
				$("#sumAmountTotal").text("ERROR");
			}
		});*/
	}

	function exportExcel() {
		$.modal.confirm("即将导出，是否继续？", function() {
			/*$.modal.loading("正在导出数据，请稍后...");
			var search = $.common.formToJSON("role-form");
			search['salesDept'] = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
			$.post(prefix + "/export", search, function(result) {
				if (result.code == web_status.SUCCESS) {
					window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
				} else if (result.code == web_status.WARNING) {
					$.modal.alertWarning(result.msg)
				} else {
					$.modal.alertError(result.msg);
				}
				$.modal.closeLoading();
			});*/
			var search = $.common.formToJSON("role-form");
			search['salesDept'] = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
			$("#tempForm").remove();
			$("[name='tempIframe']").remove();
			$("body").append("<form id='tempForm' target='tempIframe' method='post' action='" +prefix + "/export"+ "' style='display: none'></form>");
			$("body").append("<iframe name='tempIframe' style='display: none'></iframe>")
			for (k in search) {
				if (search[k]) {
					$("#tempForm").append("<input name='" + k + "' value='" + search[k] + "'/>")
				}
			}
			$("#tempForm").submit();
		});
	}

	/**
	 * 将总计金额清零
	 */
	function clearTotal() {
		//开票金额合计
		sumAmount = 0
	}

	/**
	 * 累计总金额
	 */
	function addTotal(row) {
		sumAmount += row.amount;
	}

	/**
	 *
	 */
	function subTotal(row) {
		sumAmount -= row.amount;
	}

	/**
	 *
	 * 给页脚总计赋值
	 */
	function setTotal() {
		//$("#sumAmount").text(sumAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
	}

</script>
</body>
</html>