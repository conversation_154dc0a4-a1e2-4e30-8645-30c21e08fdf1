<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方费用-支付')"/>
</head>

<body>
<div class="form-content">
    <form id="form-otherFee-edit" class="form-horizontal" novalidate="novalidate" th:object="${otherFee}">
        <!--id-->
        <input th:value="*{otherFeeId}" name="otherFeeId" type="hidden">
        <!--更新时间-->
        <input th:value="*{#dates.format(corDate, 'yyyy-MM-dd HH:mm:ss')}" name="corDate" type="hidden">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">
                                        <span >发货单号：</span></label>
                                    <div class="col-sm-8">
                                        <input  class="form-control valid" name="lotno" th:value="*{lotno}" disabled type="text">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>状态：</span></label>
                                    <div class="col-sm-8">
                                        <input name="vbillstatus" th:value="*{vbillstatus == 0?'未付款':'已付款'}" class="form-control"  type="text" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >费用类型：</label>
                                    <div class="col-sm-8">
                                        <select class="form-control valid" th:field="*{feeType}" aria-invalid="false" disabled
                                                name="feeType" th:with="type=${@dict.getType('cost_type_on_way')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">金额：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group">
                                            <span class="input-group-addon">¥</span>
                                            <input type="text" oninput="$.numberUtil.onlyNumber(this)" min="0" id="feeAmount"  name="feeAmount"
                                                   maxlength="15" th:field="*{feeAmount}" class="form-control" style="text-align:right;" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">付款方式：</label>
                                    <div class="col-sm-8">
                                        <select class="form-control valid" th:field="*{payMethod}" aria-invalid="false"
                                                name="payMethod" th:with="type=${@dict.getType('pay_method')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款人：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="recAccount" th:value="*{recAccount}" maxlength="25" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款账号：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="recCardNo" th:value="*{recCardNo}" maxlength="25" class="form-control">

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">收款银行：</label>
                                    <div class="col-sm-4">
                                        <input type="text" name="recBank" th:value="*{recBank}" maxlength="50" class="form-control">
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>支付
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script type="text/javascript">
    var prefix = ctx + "finance/otherFee";

    $(function () {
        $('#collapseOne').collapse('show');
        //校验
        $("#form-otherFee-edit").validate({
            focusCleanup: true
        });
    });
    //提交
    function submitHandler() {
       if ($.validate.form()) {
           $.modal.confirm("是否确认付款",function () {
               $.operate.saveTab(prefix + "/edit", $('#form-otherFee-edit').serialize());
           });

        }
    }


</script>
</body>

</html>