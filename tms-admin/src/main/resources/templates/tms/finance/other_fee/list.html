<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方支付')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <!--审核通过状态-->
                    <input type="hidden" name="checkStatus" value="1">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">发货单号：</label>-->
                            <div class="col-sm-12">
                                <input name="lotno" placeholder="请输入发货单号" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">状态：</label>
                            <div class="col-sm-8">
                                <select class="form-control valid"  aria-invalid="false" name="vbillstatus" id="vbillstatus">
                                    <option></option>
                                    <option th:each="dict : ${otherFeeStatusEnum}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">付款方式：</label>
                            <div class="col-sm-8">
                                <select class="form-control valid"  aria-invalid="false"
                                        name="payMethod" th:with="type=${@dict.getType('pay_method')}" required>
                                    <option></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">结算公司：</label>
                            <div class="col-sm-8">
                                <select class="form-control valid"  aria-invalid="false"
                                        name="balaCorpId" th:with="type=${@dict.getType('bala_corp')}" required>
                                    <option></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">收款人：</label>-->
                            <div class="col-sm-12">
                                <input name="recAccount" placeholder="请输入收款人" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">支付人：</label>-->
                            <div class="col-sm-12">
                                <input name="payUser" placeholder="请输入支付人" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-2">付款时间：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="regDate" placeholder="付款开始时间">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endDate"  name="corDate" placeholder="付款结束时间">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">申请人：</label>-->
                            <div class="col-sm-12">
                                <input name="applyUserId" placeholder="请输入申请人" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">客户名称：</label>-->
                            <div class="col-sm-12">
                                <input name="custName" placeholder="请输入客户名称" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
           <!-- <a class="btn btn-primary" onclick="$.operate.addTab()" shiro:hasPermission="finance:account:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="finance:account:remove">
                <i class="fa fa-remove"></i> 删除
            </a>-->
            <!-- <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="basic:etc:import">
                 <i class="fa fa-upload"></i> 导入
             </a>-->
            <a class="btn btn-danger multiple disabled" onclick="revocation()" shiro:hasPermission="finance:otherFee:revocation">
                <i class="fa fa-file single disabled"></i> 退回
            </a>
             <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="finance:otherFee:export">
                 <i class="fa fa-download"></i> 导出
             </a>
        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "finance/otherFee";
    //权限
    var payFlag = [[${@permission.hasPermi('finance:otherFee:pay')}]];
    //付款方式
    var payMethod = [[${@dict.getType('pay_method')}]];
    //费用类型
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    //结算公司
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    //状态
    var otherFeeStatusEnum = [[${otherFeeStatusEnum}]]

    //昨日日期
    var day1 = new Date();
    day1.setTime(day1.getTime()-24*60*60*1000);
    var d1Month = ("0" + (day1.getMonth() + 1)).slice(-2);
    var d1Day = ("0" + (day1.getDate())).slice(-2);
    var yesterday = day1.getFullYear()+"-" + d1Month + "-" + d1Day;
    //今日日期
    var day2 = new Date();
    day2.setTime(day2.getTime());
    var d2Month = ("0" + (day2.getMonth() + 1)).slice(-2);
    var d2Day = ("0" + (day2.getDate())).slice(-2);
    var today = day2.getFullYear()+"-" + d2Month + "-" + d2Day;

    /**
     * 初始化日期控件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#startDate',
            type: 'date',
            trigger: 'click'
        });
    });
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#endDate',
            type: 'date',
            trigger: 'click'
        });
    });


    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        //状态默认为申请
        $("#vbillstatus").val(2);
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            clickToSelect:true,
            showColumns:true,
            height: 560,
            modalName: "第三方费用",
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'center',
                    field: 'otherFeeId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push(`<a class="btn btn-xs ${payFlag}" href="javascript:void(0)" title="支付" onclick="pay('${row.otherFeeId}','${row.vbillstatus}','${row.lockOtherFee}')"><i class="fa fa-dollar" style="font-size: 15px;"></i></a>`);
                        return actions.join('');
                    }

                },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'lotno'
                },
                {
                    title: '客户名称',
                    align: 'left',
                    field: 'custName'
                },
                {
                    title: '状态',
                    align: 'left',
                    field: 'vbillstatus',
                    formatter: function status(value,row) {
                        let vbillstatus = ``
                        if (value == 0) {
                            vbillstatus = '<span class="label label-default">新建</span>';
                        }
                        if (value == 1) {
                            vbillstatus = '<span class="label label-success">已付款</span>';
                        }
                        if (value == 2) {
                            vbillstatus = '<span class="label label-primary">申请</span>';
                        }

                        let lockOtherFee = ``
                        if (row.lockOtherFee && row.lockOtherFee == 1) {
                            lockOtherFee = `<span class="label label-danger ml5" style="padding:2px;" data-toggle="tooltip"
                                    data-container="body" data-placement="top" data-html="true" title="锁定三方"
                                    data-original-title="">锁</span>`
                        }


                        return vbillstatus + lockOtherFee;
                    }
                },
                {
                    title: '审核状态',
                    align: 'left',
                    field: 'checkStatus',
                    formatter: function(value, item, index) {
                        if (item.checkStatus == 0) {
                            return '<span class="label label-default">待审核</span>';
                        }
                        if (item.checkStatus == 1) {
                            return '<span class="label label-primary">审核通过</span>';
                        }
                        if (item.checkStatus == 2) {
                            return '<span class="label label-danger">审核未通过</span>';
                        }
                    }

                },
                {
                    title: '费用类型',
                    align: 'left',
                    field: 'feeType',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(costTypeOnWay, value.feeType);
                    }
                },
                {
                    title: '金额',
                    align: 'right',
                    field: 'feeAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '付款方式',
                    align: 'left',
                    field: 'payMethod',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(payMethod, value.payMethod);
                    }
                },
                {
                    title: '结算公司',
                    align: 'left',
                    field: 'balaCorpId',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(balaCorp, value.balaCorpId);
                    }
                },
                {
                    title: '收款人',
                    align: 'left',
                    field: 'recAccount',
                },
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'recBank',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.recBank);
                    }
                },
                {
                    title: '收款账号',
                    align: 'left',
                    field: 'recCardNo',
                },
                {
                    title: '申请人',
                    align: 'left',
                    field: 'applyUserId',
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'applyDate',
                },
                {
                    title: '支付人',
                    align: 'left',
                    field: 'payUser',
                },
                {
                    title: '支付时间',
                    align: 'left',
                    field: 'payDate',
                },
                {
                    title: '备注',
                    field: 'memo',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.memo);
                    }
                },

            ]
        };

        $.table.init(options);
    });

    function detail(id) {
        var url = prefix + "/detail/"+id;
        $.modal.openTab('账号明细',url);
    }

    function pay(id, vbillstatus, lockOtherFee) {
        if (lockOtherFee == 1) {
            $.modal.alertWarning("该数据已锁定，无法付款。");
            return false;
        }

        if (vbillstatus != 2) {
            $.modal.alertWarning("请选择“申请”状态的数据！");
            return false;
        }
        $.modal.confirm("确定支付吗？", function () {
            $.operate.post(prefix + "/pay", {"otherFeeId": id});
        });
    }

    //退回
    function revocation() {
        var vbillstatus = $.table.selectColumns("vbillstatus");
        for(var i=0 ; i< vbillstatus.length ; i++){
            if(vbillstatus[i] != 2){
                $.modal.alertWarning("只能退回申请状态的账单")
                return false;
            }
        }
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var otherFeeId = $.table.selectColumns("otherFeeId").join();
        var url = prefix+"/back?otherFeeId="+otherFeeId;
        $.modal.open("退回",url,500,300);

    }

</script>

</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>