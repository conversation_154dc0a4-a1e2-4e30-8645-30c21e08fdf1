<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('可关联红冲')"/>
    <style type="text/css">
        .fixed-table-pagination div.pagination .pagination {
            margin: 10px;
        }
    </style>
</head>
<body>
<div class="container-div">
    <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var prefix = ctx + "finance/receBilling";
    var bala_corp = [[${@dict.getType('bala_corp')}]];//开票公司
    var billing_type = [[${@dict.getType('billing_type')}]];//开票类型
    var receSheetRecordId = [[${receSheetRecordId}]];//申请id


    $(function () {
        var options = {
            url: prefix + "/connAbleRedList?receSheetRecordId=" + receSheetRecordId,
            showExport: false,
            showRefresh: false,
            showSearch: false,
            showColumns: false,
            showToggle: false,
            showFooter: false,
            clickToSelect: true,
            //detailView: true,
            //detailFormatter: detailFormatter,
            columns: [
                {
                    radio: true
                },
                {
                    field: 'checkNo',
                    title: '发票号'
                },
                {
                    field: 'billingPayable',
                    title: '发票抬头',
                },

                {
                    field: 'billingAmount',
                    title: '红冲金额(元)',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    field: 'billingCorp',
                    title: '开票公司',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(bala_corp, value);
                    }
                },
                /*{
                    field: 'taxIdentify',
                    title: '纳税识别号',
                },*/
                {
                    field: 'billingType',
                    title: '开票类型',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(billing_type, value);
                    }
                },
                // {
                // 	field : 'applyUser',
                // 	title : '申请人',
                // },
                // {
                // 	field : 'applyDate',
                // 	title : '申请时间',
                // },
                /*{
                    field: 'billingStatus',
                    title: '是否开票',
                    formatter: function status(value, row, index) {
                        return value == 0 ? '否' : '是';
                    }
                },*/
                /*{
                    field: 'billingUser',
                    title: '开票人',
                },*/
                {
                    field: 'billingDate',
                    title: '开票时间',
                },


            ]
        };
        $.table.init(options);
    });

    function submitHandler(a, b) {
        console.log(a, b)
    }
</script>
</body>
</html>