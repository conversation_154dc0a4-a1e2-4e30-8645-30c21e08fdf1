<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('收款-开票列表')" />
	<th:block th:include="include :: bootstrap-select-css" />
	<th:block th:include="include :: bootstrap-editable-css" />
</head>
<style>
	/*.left-fixed-body-columns{
		height: calc(100% - 120px) !important;
	}*/
</style>
<body class="gray-bg">
    <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal">
					<!--过滤 开票数据 标记-->
					<input type="hidden" name="params[type]" th:value="1">
					<div class="row">
						<div class="col-md-7 col-sm-12">
							<div class="col-md-3 col-sm-3">
								<div class="form-group">
									<!--								<label class="col-sm-4">开票公司 ：</label>-->
									<div class="col-sm-12">
										<select name="billingCorp"  class="form-control"  th:with="type=${@dict.getType('bala_corp')}">
											<option value="">&#45;&#45;开票公司&#45;&#45;</option>
											<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
										</select>
									</div>
								</div>
							</div>
							<div class="col-md-3 col-sm-3">
								<div class="form-group">
									<!--								<label class="col-sm-4">运营组：</label>-->
									<div class="col-sm-12">
										<select name="salesDeptName" id="salesDeptName" class="form-control valid"
												aria-invalid="false" required>
											<option value="">&#45;&#45;运营组&#45;&#45;</option>
											<option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
													th:text="${mapS.deptName}"></option>
										</select>
									</div>
								</div>
							</div>
							<div class="col-md-3 col-sm-3">
								<div class="form-group">
									<!--								<label class="col-sm-4">收款申请单据号：</label>-->
									<div class="col-sm-12">
										<input name="vbillno" placeholder="收款申请单据号" class="form-control valid" type="text"
											   aria-required="true">
									</div>
								</div>
							</div>
							<div class="col-md-3 col-sm-3">
								<div class="form-group">
									<!--								<label class="col-sm-4">客户对账单据号：</label>-->
									<div class="col-sm-12">
										<input name="checkVbillno" placeholder="客户对账单据号" class="form-control valid" type="text"
											   aria-required="true">
									</div>
								</div>
							</div>
						</div>
						<div class="col-md-5 col-sm-8">
							<div class="col-md-4 col-sm-4">
								<div class="form-group">
									<!--								<label class="col-sm-4">发票抬头：</label>-->
									<div class="col-sm-12">
										<input name="billingPayable" placeholder="请输入发票抬头" class="form-control valid" type="text"
											   aria-required="true">
									</div>
								</div>
							</div>
							<div class="col-md-4 col-sm-4">
								<div class="form-group">
									<!--								<label class="col-sm-4">客户简称：</label>-->
									<div class="col-sm-12">
										<input name="custAbbr" placeholder="请输入客户简称" class="form-control valid" type="text"
											   aria-required="true">
									</div>
								</div>
							</div>
							<div class="col-md-4 col-sm-4">
								<div class="form-group">
									<!--								<label class="col-sm-4">申请人：</label>-->
									<div class="col-sm-12">
										<input name="applyUser" placeholder="请输入申请人" class="form-control valid" type="text"
											   aria-required="true">
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-md-5 col-sm-5">
							<div class="col-md-4 col-sm-4">
								<div class="form-group">
									<!--								<label class="col-sm-4 checkType">发票类型：</label>-->
									<div class="col-sm-12">
										<select id="billingType" class="form-control selectpicker"
												multiple data-none-selected-text="发票类型"
												th:with="type=${@dict.getType('billing_type')}">
											<option th:each="dict : ${type}" th:text="${dict.dictLabel}"
													th:value="${dict.dictValue}" th:selected="${billingType == dict.dictValue}"></option>
										</select>
									</div>
								</div>
							</div>
							<div class="col-md-4 col-sm-4">
								<div class="form-group">
									<!--								<label class="col-sm-4 checkType">是否已开票：</label>-->
									<div class="col-sm-12">
										<select name="billingStatus" id="billingStatus" class="form-control">
											<option value="">&#45;&#45;是否已开票&#45;&#45;</option>
											<option value="0" th:selected="${billingStatus == '0'}">否</option>
											<option value="1" th:selected="${billingStatus == '1'}">是</option>
										</select>
									</div>
								</div>
							</div>
							<div class="col-md-4 col-sm-4">
								<div class="form-group">
									<!--								<label class="col-sm-4">开票人：</label>-->
									<div class="col-sm-12">
										<input name="billingUser" placeholder="请输入开票人" class="form-control valid" type="text"
											   aria-required="true">
									</div>
								</div>
							</div>
						</div>
						<div class="col-md-7 col-sm-7">
							<div class="col-md-4 col-sm-4">
								<div class="form-group">
									<!--								<label class="col-sm-2">开票日期：</label>-->
									<div class="">
										<input type="text" style="width: 45%; float: left;" class="time-input form-control"
											   name="params[billingDateStart]" placeholder="开票开始日期">
										<span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
										<input type="text" style="width: 45%; float: left;" class="time-input form-control"
											   name="params[billingDateEnd]" placeholder="开票结束日期">
									</div>
								</div>
							</div>
							<div class="col-md-3 col-sm-3">
								<div class="form-group">
									<!--								<label class="col-sm-4">运营组：</label>-->
									<div class="">
										<select id="vbillstatus" class="form-control selectpicker"
												data-none-selected-text="收款申请单据状态" multiple>
											<option value="0">待审核</option>
											<option value="1">审核通过</option>
											<option value="2">审核未通过</option>
											<option value="3">部分收款</option>
											<option value="4">已收款</option>
										</select>
									</div>
								</div>
							</div>
							<div class="col-md-2 col-sm-2">
								<div class="form-group">
									<!--								<label class="col-sm-4">运营组：</label>-->
									<div class="col-sm-12">
										<select id="receStatus" class="form-control selectpicker"
												data-none-selected-text="核销记录" multiple>
											<option value="0">未收款</option>
											<option value="3">部分收款</option>
											<option value="4">已收款</option>
										</select>
									</div>
								</div>
							</div>
							<div class="col-md-3 col-sm-3">
								<div class="form-group">
									<div class="col-sm-12">
										<input name="checkNo" placeholder="发票号" class="form-control valid" type="text"
											   aria-required="true">
									</div>
								</div>
							</div>
						</div>

						<div class="col-md-2 col-md-offset-10 col-sm-3 col-sm-offset-3" style="text-align: right">
							<div class="form-group">
								<a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="btn-group-sm" id="toolbar" role="group">
				<a class="btn btn-warning multiple disabled" onclick="receipt()" shiro:hasPermission="tms:receBilling:receipt">
					<i class="fa fa-dollar"></i> 百旺开票
				</a>
				<a class="btn btn-info" onclick="showsbfp()" shiro:hasPermission="tms:receBilling:receipt">
					<i class="fa fa-cloud-upload"></i> 上报发票
				</a>
				<a class="btn btn-warning single disabled" onclick="dyfp()" shiro:hasPermission="tms:receBilling:receipt">
					<i class="fa fa-print"></i> 打印发票
				</a>
				<a class="btn btn-success multiple disabled" onclick="dyqd()" shiro:hasPermission="tms:receBilling:receipt">
					<i class="fa fa-print"></i> 打印清单
				</a>
				<a class="btn btn-danger single disabled" onclick="cxkj()" shiro:hasPermission="tms:receBilling:rebilling">
					<i class="fa fa-rotate-left"></i> 重新开具
				</a>
				<a class="btn btn-danger multiple disabled" onclick="removeAll()" shiro:hasPermission="tms:receBilling:remove">
					<i class="fa fa-remove"></i> 删除
				</a>
				<a class="btn btn-primary multiple disabled" onclick="batchRece()" shiro:hasPermission="tms:receBilling:receiveMoney">
					<i class="fa fa-calculator"></i> 开票收款
				</a>
				<a class="btn btn-primary single disabled" onclick="receRecord()" shiro:hasAnyPermissions="finance:receRecord:batchRece,fleet:finance:receRecord:batchRece">
					<i class="fa fa-calculator"></i> 收款记录
				</a>
				<a class="btn btn-warning" onclick="exportExcel()" shiro:hasPermission="tms:receBilling:export">
					<i class="fa fa-download"></i> 导出
				</a>
				<a href="/static/FPYun Enigne SDK Setup(2.12.0318.1231).exe" shiro:hasPermission="tms:receBilling:receipt">百旺SDK下载</a>

			</div>
			<div class="col-sm-12 select-table table-striped">
				<table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
			</div>
		</div>
	</div>
	<div id="cancelRecord" style="display: none">
		<div class="form-horizontal" style="padding: 10px">
			<div class="form-group">
				<label class="col-sm-3 control-label">发票抬头</label>
				<div class="col-sm-9">
					<p class="form-control-static" flag="billingPayable"></p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">发票号码</label>
				<div class="col-sm-9">
					<p class="form-control-static" flag="checkNo"></p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">开票金额</label>
				<div class="col-sm-9">
					<p class="form-control-static" flag="billingAmount"></p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">开票人</label>
				<div class="col-sm-9">
					<p class="form-control-static" flag="billingUser"></p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">开票时间</label>
				<div class="col-sm-9">
					<p class="form-control-static" flag="billingDate"></p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">作废人</label>
				<div class="col-sm-9">
					<p class="form-control-static" flag="regUserName"></p>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">作废时间</label>
				<div class="col-sm-9">
					<p class="form-control-static" flag="regDate"></p>
				</div>
			</div>
		</div>
	</div>
<div th:include="include :: footer"></div>
<script th:src="@{/js/jquery.json2xml.js}"></script>
<script th:src="@{/js/jquery.xml2json.js}"></script>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/finance/receBilling/x-ip}"></script>
<th:block th:include="include :: bootstrap-select-js" />
	<th:block th:include="include :: bootstrap-table-editable-js" />
    <script th:inline="javascript">
		parent.window.kpDebug = undefined
		//是否是车队
		var isFleet = [[${isFleet}]];

		// var prefix = isFleet ? ctx + "fleet/finance/receBilling" : ctx + "finance/receBilling";

		var prefix = ctx + "finance/receBilling";
        var bala_corp = [[${@dict.getType('bala_corp')}]];//开票公司
        var billing_type = [[${@dict.getType('billing_type')}]];//开票类型

        //收款单据状态
        var vbillstatus = [[${vbillstatus}]];
        var billingType = [[${billingType}]];

		//开票金额合计
		var billingAmountTotal = 0;
		var billingAmount0Total = 0;
		var billingAmount1Total = 0;
		var billingAmountCashTotal = 0;
		var gotAmountTotal = 0;
		var ungotAmountTotal = 0;

        $(function() {
            //监听回车事件 回车搜索
            $(document).keyup(function(e){
                var key = e.which;
                if(key==13){
                    $.table.search();
                }
            });

			var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
				exportUrl: prefix + "/export",
                modalName: "收款-开票",
				showToggle: false,
				showFooter: true,
				showExport: false,
				fixedColumns: true,
				idField: "receBillingId",
				fixedNumber: 5,
				/*exportTypes:['excel','csv'],
				exportOptions:{
					ignoreColumn: [0, 1],
					fileName:"开票申请"
				},*/
                clickToSelect:true,
                height: 560,
				onRefresh:function(params){
					clearTotal();
				},
				onCheck: function (row,$element) {
					addTotal(row);
					setTotal();
				},
				onUncheck: function (row, $element) {
					subTotal(row);
					setTotal();
				},
				onCheckAll: function (rowsAfter) {
					clearTotal();
					//循环累加
					for (var row of rowsAfter) {
						addTotal(row);
					}
					//赋值
					setTotal();
				},
				onUncheckAll: function () {
					//总数清0
					clearTotal();
					//赋值
					setTotal();
				},
				onPostBody: function () {
                	clearTotal();
					//合并页脚
					merge_footer();
					getAmountCount();
				},
				/*onEditableSave: function (field, row, oldValue, $el) {
                	if (field == 'bzsm') {
                		var orig = $el.html()
						$.ajax({
							url:prefix +'/setBzsm',
							type: 'post',
							data:{receBillingId:row.receBillingId, bzsm:row.bzsm},
							dataType: 'json',
							success: function (result) {
								if (!result.code == 0) {
									$el.html(orig)
								}
							},
							error:function (response) {
								$el.html(orig)
							}
						})
					}
				},*/
                columns: [
				{
					checkbox: true,
					footerFormatter: function (row) {
						return "总开票金额：<nobr id='billingAmountTotal'>¥0.00</nobr>" +
								" 已开票金额：<nobr id='billingAmount1Total'>¥0.00</nobr>" +
								" 未开票金额：<nobr id='billingAmount0Total'>¥0.00</nobr>" +
								" 收款金额：<nobr id='gotAmountTotal'>¥0.00</nobr>" +
								" 未收款金额：<nobr id='ungotAmountTotal'>¥0.00</nobr>" +
								" 现金金额：<nobr id='billingAmountCashTotal'>¥0.00</nobr>" +
								"<br>" +
								"总合计：总开票金额：<nobr id='billingAmountTotalAll'></nobr>" +
								" 已开票金额：<nobr id='billingAmount1TotalAll'></nobr>" +
								" 未开票金额：<nobr id='billingAmount0TotalAll'></nobr>" +
								" 收款金额：<nobr id='gotAmountTotalAll'></nobr>" +
								" 未收款金额：<nobr id='ungotAmountTotalAll'></nobr>" +
								" 现金金额：<nobr id='billingAmountCashTotalAll'>¥0.00</nobr>";
					}
				},
				{
					title: '操作',
					align: 'center',
					field: 'receBillingId',
					formatter: function(value, row, index) {
						var actions = [];
						if (row.billingType != 6) {
							if ([[${@permission.hasPermi('tms:receBilling:edit')}]] != "hidden" && row.billingStatus == 0 && row.vbillstatus != 0 && row.vbillstatus != 2) {
								actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="开票" onclick="edit(\'' + row.receBillingId + '\')"><i class="fa fa-dollar" style="font-size: 15px;"></i></a> ');
								//if (row.qdCount == 0) {
								//	actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="补充清单" onclick="alert()"><i class="fa fa-plug" style="font-size: 15px;"></i></a> ');
								//}
							}
						}
                        if ([[${@permission.hasPermi('tms:receBilling:checkApplReply')}]] != "hidden" && row.billingStatus == 1 && row.vbillstatus != 0 && row.vbillstatus != 2 && row.redReceBillingId == null) {
                            actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="开票作废" onclick="checkApplReply(\'' + row.receBillingId + '\',',row.receStatus,')"><i class="fa fa-reply" style="font-size: 15px;"></i></a> ');
                        }
                        if (row.redReceBillingId != null) {
                        	actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="作废记录" onclick="cancelRecord(\'' + row.receBillingId + '\')"><i class="fa fa-clock-o" style="font-size: 15px;"></i></a> ')
						}
						if ([[${@permission.hasPermi('tms:receBilling:detail')}]] != "hidden") {
							actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="明细" onclick="detail(\'' + row.receBillingId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
						}
						return actions.join('');
					}
				},
				{
					field : 'salesDeptName',
					title : '运营组',
				},
				{
					field : 'vbillno',
					title : '收款申请单据号',
				},
				{
					field : 'custAbbr',
					title : '结算客户简称',
				},

				{
					field : 'checkVbillno',
					title : '对账单号/应收明细号',
					formatter: function(value,row) {
						if(value && value.slice(0,4) == 'YSDZ'){
							var actions = [];
							actions.push('<a style="text-decoration: underline;" class="" href="javascript:;" title="详情" onclick="detailCheckSheet(\'' + row.receSheetRecordId + '\')">'+ value +'</ a>');
							return actions.join('');
						}else{
							return value;
						}
					}
				},
				{
					field: 'vbillstatus',
					title: '收款申请单据状态',
					align: 'left',
					formatter: function status(value,row) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-default">待审核</span>'
                            case 1:
                                return '<span class="label label-primary">审核通过</span>';
                            case 2:
                                return '<span class="label label-warning">审核未通过</span>';
                            case 3:
                                return '<span class="label label-info">部分收款 </label>';
                            case 4:
                                return '<span class="label label-success">已收款</span>';
                            default:
                                break;
                        }
					}
				},
				{
					field: 'receStatus',
					title: '核销记录',
					formatter: function(value, row, index) {
						switch (value) {
							case 0:
								return '<span class="label label-default">未收款</span>'
							case 3:
								return '<span class="label label-info">部分收款</label>';
							case 4:
								return '<span class="label label-success">已收款</span>';
							default:
								break;
						}
					}
				},
				{
					field : 'billingPayable',
					title : '发票抬头',
					formatter: function(value, row, index) {
						return (value ? value : "") + (row.billingType != 6 ? ("("+row.qdCount+")") : "")
					}
				},
				{
					field : 'bzsm',
					title : '备注说明',
					editable:{
						type: 'text',
						title: '追加备注说明',
						validate:  function (v) {
							//判断是否为空
							//if (v === "") {
							//	return "不能为空！";
							//}
							//判断长度
							//if (getStringLen(v) > 50) {
							//	return "长度过长！";
							//}
						},
						url:prefix + '/setBzsm',
						success: function(result, newValue) {
							if (result.code != 0) {
								return result.msg;
							}
						}
					}
				},
				{
					field : 'checkNo',
					title : '发票号',
				},
				{
					field : 'billingAmount',
					title : '开票金额(元)',
					formatter: function (value, row, index) {
						if (value == null) {
							return ;
						}
						var result = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
						if (row.redReceBillingId && row.params.billingAmountRed) {
							result = result + ' (<span style="text-decoration:line-through;color:red">' + row.params.billingAmountRed.toLocaleString('zh', {style: 'currency', currency: 'CNY'}) + "</span>)";
						}
						return result;

					}
				},
				{
					field : 'gotAmount',
					title : '已收金额(元)',
					formatter: function (value, row, index) {
						if (value == null) {
							return '¥0.00';
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				},
				{
					field : 'ungotAmount',
					title : '未收金额(元)',
					formatter: function (value, row, index) {
						if (value == null) {
							return row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				},
				{
					field : 'billingCorp',
					title : '开票公司',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(bala_corp, value);
                    }
				},
				{
					field : 'taxIdentify',
					title : '纳税识别号',
				},
				{
					field : 'billingType',
					title : '开票类型',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(billing_type, value);
                    }
				},
				{
					field : 'bank',
					title : '开户银行',
				},
				{
					field : 'bankAccount',
					title : '开户账号',
				},
				{
					field : 'addressPhone',
					title : '地址及电话',
					formatter: function status(row,value) {
						return $.table.tooltip(value.addressPhone);
					}
				},
				{
					field : 'applyUser',
					title : '申请人',
				},
				{
					field : 'applyDate',
					title : '申请时间',
				},
				{
					field : 'billingStatus',
					title : '是否已开票',
					formatter: function status(value, row, index) {
					    if (value == 0) {
                            return '<span class="label label-default">否</span>';
						} else {
                            return '<span class="label label-primary">是'+(row.redReceBillingId?'(已作废)':'')+'</span>';
						}
					}
				},
				{
					field : 'billingUser',
					title : '开票人',
				},
				{
					field : 'billingDate',
					title : '开票时间',
				},



				{
					field : 'memo',
					title : '开票备注',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.memo);
                    }
				},
				{
					field : 'remark',
					title : '备注',
					formatter: function status(row,value) {
						return $.table.tooltip(value.remark);
					}
				},

		       ]
            };
            $.table.init(options);


			var receStatus = [[${receStatus}]]
			if (receStatus != null && receStatus != '') {
				var a = [];
				var receStatusList = receStatus.split(',');
				for(var i =0 ;i<receStatusList.length;i++){
					a.push(receStatusList[i]);
				}
				$('#receStatus').selectpicker('val',a);
			}
			//
			// var receStatus = [[${receStatus}]]
			// $("#receStatus").val([receStatus]);
			// $("#receStatus").selectpicker('refresh');

			if (vbillstatus != null && vbillstatus != '') {
				var a = [];
				var vbillstatusList = vbillstatus.split(',');
				for(var i =0 ;i<vbillstatusList.length;i++){
					a.push(vbillstatusList[i]);
				}
				$('#vbillstatus').selectpicker('val',a);

			}

			if (billingType != null && billingType != '') {
				var a = [];
				var billingTypeList = billingType.split(',');
				for(var i =0 ;i<billingTypeList.length;i++){
					a.push(billingTypeList[i]);
				}
				$('#billingType').selectpicker('val',a);

			}
			searchx()
			//searchx()
            /**
             * 初始化日期控件
             */
            layui.use('laydate', function() {
                var laydate = layui.laydate;
                laydate.render({
                    elem: '#startDate',
                    type: 'date',
                    trigger: 'click'
                });
            });
            layui.use('laydate', function() {
                var laydate = layui.laydate;
                laydate.render({
                    elem: '#endtDate',
                    type: 'date',
                    trigger: 'click'
                });
            });
        });

		/**
		 * 合并页脚
		 */
		function merge_footer() {
			var footer_tbody = $('.fixed-table-footer table tbody');
			var footer_tr = footer_tbody.find('>tr');
			var footer_td = footer_tr.find('>td');
			var footer_td_1 = footer_td.eq(0);
			//除了第一列其他都隐藏
			for(var i=1;i<footer_td.length;i++) {
				footer_td.eq(i).hide();
			}
			footer_td_1.attr('colspan', 1).show();
		}

		function searchx() {
			var data = {};
			data["params[vbillstatus]"] = $.common.join($('#vbillstatus').selectpicker('val'));//收款申请单据状态
			data["params[receStatus]"] = $.common.join($('#receStatus').selectpicker('val'));
			data["billingType"] = $.common.join($('#billingType').selectpicker('val'));
			$.table.search('role-form', data);
		}

		/**
		 * 获取所有数据金额合计
		 */
		function getAmountCount() {
			var data = $.common.formToJSON("role-form");
			data["params[vbillstatus]"] = $.common.join($('#vbillstatus').selectpicker('val'));//收款申请单据状态
			data["params[receStatus]"] = $.common.join($('#receStatus').selectpicker('val'));
			$.ajax({
				url: ctx + "finance/receBilling/getCount",
				type: "post",
				dataType: "json",
				data: data,
				success: function(result) {
					var data = result.data;
					if (result.code == 0 && data != undefined) {
						$("#billingAmountTotalAll").text(data.BILLINGAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
						$("#billingAmount0TotalAll").text(data.BILLINGAMOUNT0.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
						$("#billingAmount1TotalAll").text(data.BILLINGAMOUNT1.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
						$("#billingAmountCashTotalAll").text(data.BILLINGAMOUNTCASH.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
						$("#gotAmountTotalAll").text(data.GOTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
						$("#ungotAmountTotalAll").text(data.UNGOTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
					}
				}
			});
		}

		/*function receiveMoney(){
			var bootstrapTable = $.btTable.bootstrapTable('getSelections');
			var  billingPayable = bootstrapTable[0]["billingPayable"];
			for (var i = 0; i < bootstrapTable.length; i++) {
				if (bootstrapTable[i]["billingPayable"] !== billingPayable ) {
					$.modal.alertWarning("请选择发票抬头一致的单据");
					return;
				}

				if (bootstrapTable[i]["vbillstatus"] !== 1 ) {
					$.modal.alertWarning("请选择审核通过的单据");
					return;
				}
			}
			var receBillingId = $.table.selectColumns("receBillingId");
			$.modal.open("发票收款核销", prefix + "/receiveMoney?receBillingIds=" + receBillingId.join(),800,600);
		}*/


		/**
		 * 将总计金额清零
		 */
		function clearTotal() {
			//开票金额合计
			billingAmountTotal = 0;
			billingAmount0Total = 0;
			billingAmount1Total = 0;
			billingAmountCashTotal = 0;
			gotAmountTotal = 0;
			ungotAmountTotal = 0;
		}

		/**
		 * 累计总金额
		 */
		function addTotal(row) {
			//开票金额合计
			if (row.billingType == '6') {
				billingAmountCashTotal = billingAmountCashTotal + row.billingAmount;
			} else {
				billingAmountTotal = billingAmountTotal + row.billingAmount;
				if (row.billingStatus == 0) {
					billingAmount0Total = billingAmount0Total + row.billingAmount;
				} else if (row.billingStatus == 1) {
					billingAmount1Total = billingAmount1Total + row.billingAmount;
				}
			}

			gotAmountTotal = gotAmountTotal + (row.gotAmount||0);
			if (!row.redReceBillingId) {
				ungotAmountTotal = ungotAmountTotal + (row.ungotAmount == null ? row.billingAmount : row.ungotAmount);
			}
		}

		/**
		 *
		 */
		function subTotal(row) {
			if (row.billingType == '6') {
				billingAmountCashTotal = billingAmountCashTotal - row.billingAmount;
			} else {
				billingAmountTotal = billingAmountTotal - row.billingAmount;
				if (row.billingStatus == 0) {
					billingAmount0Total = billingAmount0Total - row.billingAmount;
				} else if (row.billingStatus == 1) {
					billingAmount1Total = billingAmount1Total - row.billingAmount;
				}
			}

			gotAmountTotal = gotAmountTotal - (row.gotAmount||0);
			if (!row.redReceBillingId) {
				ungotAmountTotal = ungotAmountTotal - (row.ungotAmount == null ? row.billingAmount : row.ungotAmount);
			}
		}

		/**
		 *
		 * 给页脚总计赋值
		 */
		function setTotal() {
			$("#billingAmountTotal").text(billingAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
			$("#billingAmount0Total").text(billingAmount0Total.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
			$("#billingAmount1Total").text(billingAmount1Total.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
			$("#billingAmountCashTotal").text(billingAmountCashTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
			$("#gotAmountTotal").text(gotAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
			$("#ungotAmountTotal").text(ungotAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
		}

		function edit(id){
            var url = prefix + "/edit/"+id
            $.modal.openTab("收款申请开票", url);
        }

        /**
		 * 开票作废
         * @param id
         */
        function checkApplReply(id,receStatus) {
        	if (receStatus === 3 || receStatus === 4) {
        		$.modal.msgError("已收款的开票信息不能作废");
        		return
			}
            $.modal.confirm("是否确认该条开票记录作废", function() {
                var data = { "receBillingId": id};
                $.operate.submit(prefix + "/checkApplReply", "post", "json", data);
            });
        }

        /**
		 * 明细
         * @param id
         */
        function detail(id){
            var url = prefix + "/detail/"+id
            $.modal.openTab("开票明细", url);
        }

        /**
		 * 删除 删除未开票的 退还申请开票金额
         */
        function removeAll() {
            //获取行
            var rows = $.btTable.bootstrapTable('getSelections');
            for(var i = 0 ;i < rows.length;i++){
                if(rows[i]['billingStatus'] == 1){
                    $.modal.alertWarning("已开票的请选择开票作废");
                    return false;
				}
			}
			//开票id
			var id = $.table.selectColumns('receBillingId').join();
            var data = {ids: id};
            var url = prefix + "/remove";
            $.modal.confirm("是否确认删除选择的开票信息", function() {
                $.operate.submit(url, "post","json",data);
            });

        }

        function detailCheckSheet(receSheetRecordId){
			$.modal.openTab("客户对账单管理", ctx + "receCheckSheet?receSheetRecordId=" + receSheetRecordId);
		}

		/**
		 * 分批收款 相同结算客户，状态：部分收款，审核通过
		 */
		function batchRece(){
			//选中的行
			var rows = $.btTable.bootstrapTable('getSelections');
			var receSheetRecordId = rows[0]["receSheetRecordId"];
			//结算客户id
			var balaCustomerId = rows[0]["balaCustomerId"];
			var billingCorp = rows[0]["billingCorp"];
			for(var i = 0;i<rows.length;i++){
				if (rows[i]["vbillstatus"] !== 1 && rows[i]["vbillstatus"] !== 3) {
					$.modal.alertWarning("请选择审核通过/部分收款的申请单");
					return;
				}
				/*if(receSheetRecordId !== rows[i]["receSheetRecordId"]){
					$.modal.alertWarning("请选择相同申请单下的开票信息");
					return;
				}*/
				if(balaCustomerId !== rows[i]["balaCustomerId"]){
					$.modal.alertWarning("请选择相同结算客户的申请单");
					return;
				}
				if(billingCorp !== rows[i]["billingCorp"]){
					$.modal.alertWarning("请选择相同开票公司的申请单");
					return;
				}
				if (rows[i]["receStatus"] == 4) {
					$.modal.alertWarning("请选择未入账/部分入账的开票数据");
					return;
				}
				if (rows[i]['redReceBillingId']) {
					$.modal.alertWarning("已作废的发票不能收款");
					return;
				}
			}
			var receBillingId = $.table.selectColumns("receBillingId");
			$.modal.open("发票收款核销", prefix + "/receiveMoney?receBillingIds=" + receBillingId.join(),800,600);
		}

		//收款记录
		function receRecord(){
			var id = $.table.selectColumns('receSheetRecordId');
			var url = ctx + "receSheetRecord" + "/receRecord/"+id;
			layer.open({
				type: 2,
				maxmin: true,
				title: "收款记录",
				area: ['800px', '600px'],
				content: url,
				shadeClose: true,
				btn: ['<i class="fa fa-close"></i> 关闭'],
				yes: function (index, layero) {
					layer.close(index);
				}
			});
		}

		function exportExcel() {
			$.modal.confirm("确定导出所有开票申请吗？", function() {
				$.modal.loading("正在导出数据，请稍后...");
				var search = $.common.formToJSON("role-form");
				search["params[vbillstatus]"] = $.common.join($('#vbillstatus').selectpicker('val'));//收款申请单据状态
				search["params[receStatus]"] = $.common.join($('#receStatus').selectpicker('val'));
				// search.params = new Map();
				// search.params.invoice_vbillstatus = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
				// //search.params.receiveVbillstatus = $.common.join($('#receiveVbillstatus').selectpicker('val'));
				// search.salesDept = $.common.join($('#salesDept').selectpicker('val'));

				$.post(prefix + "/export", search, function(result) {
					if (result.code == web_status.SUCCESS) {
						window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
					} else if (result.code == web_status.WARNING) {
						$.modal.alertWarning(result.msg)
					} else {
						$.modal.alertError(result.msg);
					}
					$.modal.closeLoading();
				});
			});
		}

		function cancelRecord(receBillingId) {
			$('#cancelRecord').find("[flag]").text("");
			$.post(prefix + "/cancelRecord", {billingId:receBillingId}, function(result) {
				if (result.code == web_status.SUCCESS) {
					layer.open({
						type: 1,
						title: '作废记录',
						content: $('#cancelRecord'),
						area: ['400px', '390px'],
						btn: ['关闭'],
						yes: function (index, layero) {
							layer.close(index);
						}
					});
					$('#cancelRecord').find("[flag]").each(function(){
						this.innerText = result.data[$(this).attr("flag")]||'';
					});
				} else if (result.code == web_status.WARNING) {
					$.modal.alertWarning(result.msg)
				} else {
					$.modal.alertError(result.msg);
				}
			})
			window.event? window.event.cancelBubble = true : event.stopPropagation();
		}

		//<option value="4">增值税专用发票（9%）</option>
		// <option value="3">增值税专用发票（6%）</option>
		// <option value="2">增值税普票发票（9%）</option>
		// <option value="5">增值税专用发票（3%）</option>
		// <option value="7">增值税专用发票（13%）</option>
		// <option value="6">不开票</option>
		var billingLxdm = {
			"4": "004",
			"3": "004",
			"2": "007",
			"5": "004",
			"7": "004"
		}
		function receipt() {
			var rows = $.btTable.bootstrapTable('getSelections');
			var corp = rows[0].billingCorp;
			var fplxdm = billingLxdm[rows[0].billingType];
			var ids = []
			var errMsg = [];
			for (var i = 0; i < rows.length; i++) {
				var row = rows[i];
				if (row.billingStatus != 0) {
					errMsg.push(row.vbillno, '[', row.billingPayable,']', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 已开票的单据不能再开票<br>')
				} else if (row.vbillstatus == 0) {
					errMsg.push(row.vbillno, '[', row.billingPayable,']', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 待审核单据不能开票<br>')
				} else if (row.vbillstatus == 2) {
					errMsg.push(row.vbillno, '[', row.billingPayable,']', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 审核未通过单据不能开票<br>')
				} else if (row.billingType == '6') {
					errMsg.push(row.vbillno, '[', row.billingPayable,']', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 不需要开票<br>')
				} else if (row.billingCorp != corp) {
					errMsg.push(row.vbillno, '[', row.billingPayable,']', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 开票公司不一致不能一起开票<br>')
				} else if (billingLxdm[row.billingType] != fplxdm) {
					errMsg.push(row.vbillno, '[', row.billingPayable,']', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 普票专票不能混在一起开具<br>')
				} else if (row.qdCount == 0) {
					errMsg.push(row.vbillno, '[', row.billingPayable,']', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 无清单的数据不可在线开票<br>')
				} else if (fpkj_cache.indexOf(row.receBillingId) >= 0) {
					errMsg.push(row.vbillno, '[', row.billingPayable,']', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 可能已开具发票，请刷新数据后重试<br>')
				}
				ids.push(row.receBillingId);
			}
			if (errMsg.length > 0) {
				$.modal.alertError(errMsg.join(''));
				return;
			}
			$.ajax({
				url: prefix + '/receBillingQd',
				data: 'receBillingId=' + ids.join(','),
				type: 'post',
				cache: false,
				async: false,
				success: function(aj) {
					for (var i = 0; i < rows.length; i++) {
						var qdList = []
						rows[i]['qdList'] = qdList
						for (var j = 0; j < aj.data.length; j++) {
							if (rows[i].receBillingId == aj.data[j].receBillingId) {
								qdList.push(aj.data[j]);
								aj.data.splice(j, 1);
								j--;
							}
						}
					}
				}
			})
			for (var i = 0; i < rows.length; i++) {
				if (rows[i].qdList.length == 0) {
					errMsg.push(rows[i].vbillno, '[', rows[i].billingPayable,']', rows[i].billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 无清单<br>')
				}
			}
			if (errMsg.length > 0) {
				$.modal.alertError(errMsg.join(''));
				return;
			}
			var limit = [(${@sysConfigMapper.checkConfigKeyUnique('kp_batch_limit') == null ? 2 : @sysConfigMapper.checkConfigKeyUnique('kp_batch_limit').configValue})];
			if (rows.length > limit) {
				$.modal.alertError("不支持"+limit+"张以上批量开票");
				return;
			}
			global_kp_count = rows.length;
			skpxxcx(corp, function(skp){
				var top = 2;
				var left = -9.5;
				if (corp == 'JH') {
					left = -8
				}
				ybjsz(fplxdm, top, left, function(){
					fpkj_process(skp, fplxdm, rows, 0)
				})
			})
		}

		var global_kp_count = 0; // 当前开票数量
		var global_kp_idx = 0; // 当前开票索引

		function fpkj_process(skp, fplxdm, rows, idx) {
			global_kp_idx = idx;
			if (idx >= rows.length) {
				if (global_kp_count > 1) {
					// 打印、上传
					var fpdm = rows[0].fpdm;
					var fphm = rows[0].fphm + rows[rows.length - 1].fphm; // 第一个发票号码+最后一个发票号码
					$.modal.alertAndCall(rows.length + "张发票已开具，请确认当前打印机内发票号码是" + rows[0].fphm + "至" + rows[rows.length - 1].fphm,function(index){
						layer.close(index)
						fpdy(skp, 9, 0, fplxdm, fpdm, fphm, function(){
							$.table.refresh();
							$.modal.alertSuccess("开票完成");
						});
					})
					fpsc(skp, fplxdm, rows.length);
				} else {
					$.modal.alertSuccess("开票完成");
					$.table.refresh();
				}

				return;
			}
			fpkj(skp, fplxdm, rows[idx], function() {
				fpkj_process(skp, fplxdm, rows, idx + 1)
			});
		}

		function gpxxcx(skp, fplxdm, callback) {
			invoke("购票信息查询", "GPXXCX", {
				nsrsbh: skp.nsrsbh,//纳税人识别号
				skpbh: skp.skpbh,//税控盘编号
				skpkl: skp.skpkl,//税控盘口令
				keypwd: skp.keypwd,//税务数字证书密码
				fplxdm: fplxdm,//发票类型代码
			}, function(output) {
				//<fplxdm>发票类型代码</fplxdm>
				//<fplgbw>发票领购原始报文</fplgbw>
				//<dqfpdm>当前发票代码</dqfpdm>
				//<dqfphm>当前发票号码</dqfphm>
				//<zsyfs>总剩余份数</zsyfs>
				//<fpdxx count="2">
				//	<group xh="1">
				//		<fpdm>发票代码</fpdm>
				//		<qshm>发票起始号码</qshm>
				//		<zzhm>发票终止号码</zzhm>
				//		<fpfs>发票份数</fpfs>
				//		<syfs>剩余份数</syfs>
				//		<lgrq>领购日期</lgrq>
				//		<lgry>领购人员</lgry>
				//	</group>
				var zsyfs = parseInt(output.zsyfs, 10); //总剩余份数
				if (zsyfs < global_kp_count) {
					alert("当前发票数不足，剩余" + zsyfs + "张");
					return;
				}
				var dqfpdm = output.dqfpdm;
				var dqfphm = output.dqfphm;
				if (!dqfphm && !parent.window.kpDebug) {
					$.table.refresh();
					$.modal.alertError("未取到当前发票号码！");
					return
				}
				callback(dqfphm)
			})
		}

		var global_config = {
			"MY": {
				skr: "[(${@sysConfigMapper.checkConfigKeyUnique('my_kp_skr').configValue})]",
				fhr: "[(${@sysConfigMapper.checkConfigKeyUnique('my_kp_fhr').configValue})]"
			},
			"JH": {
				skr: "[(${@sysConfigMapper.checkConfigKeyUnique('jh_kp_skr').configValue})]",
				fhr: "[(${@sysConfigMapper.checkConfigKeyUnique('jh_kp_fhr').configValue})]"
			}
		}

		var zyspmc = [{name:'运输费', bm:'3010102020100000000'}, // *运输服务*
			{name:'国内道路货物运输服务', bm:'3010102020100000000'}, // *运输服务*
			{name:'力资费', bm:'3040408000000000000'}, // *物流辅助服务*
			{name:'仓储费', bm:'3040407990000000000'}, // *物流辅助服务*
			{name:'装卸费', bm:'3040408000000000000'}, // *物流辅助服务*
			{name:'仓储力资费', bm:'3040407030000000000'}, // *物流辅助服务*
			{name:'进仓费', bm:'3040407990000000000'}, // *物流辅助服务*
			{name:'其他加工劳务', bm:'2010500000000000000'},
			{name:'其他现代服务', bm:'3049900000000000000'}
		]

		var fpkj_cache = []
		function fpkj(skp, fplxdm, row, nextcall) {
			// 获取、开具、上报、打印
			// 1、获取
			gpxxcx(skp, fplxdm, function(dqfphm) {
				var doSth = function(){
					record_process('receBillingId=' + row.receBillingId + "&receipt=1", function (success, errMsg) {
						if (!success) {
							$.modal.alertError(row.vbillno +'[' + row.billingPayable + ']' + row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}) + ' 记录开票前状态出错：' + errMsg);
							$.table.refresh()
							return;
						}
						//$.modal.loading("正在开票" + dqfphm + "...")
						// 2、开具
						var qdList = row.qdList;
						console.log(qdList)
						var group = [];
						var hjje = new BigNumber(0);
						var hjse = new BigNumber(0);
						var jshj = new BigNumber(0);
						var temp = {} // 统计各商品名称的合计
						for (var i = 0; i < qdList.length; i++) {
							var item = {}
							item["@xh"] = i + 1;
							item["fphxz"] = 0;//0=正常行 1=折扣行 2=被折扣行
							item["spmc"] = qdList[i]["spmc"];
							item["ggxh"] = qdList[i]["ggxh"] || "";
							item["dw"] = qdList[i]["dw"] || "";
							item["spsl"] = qdList[i]["spsl"];
							item["dj"] = qdList[i]["dj"];
							item["je"] = qdList[i]["je"];
							if (temp[qdList[i]["spmc"]] == null) {
								temp[qdList[i]["spmc"]] = new BigNumber(qdList[i]["je"]);
							} else {
								temp[qdList[i]["spmc"]] = temp[qdList[i]["spmc"]].plus(new BigNumber(qdList[i]["je"]));
							}
							item["sl"] = qdList[i]["sl"];
							item["se"] = qdList[i]["se"];
							item["hsbz"] = 0;//金额的含税标志</hsbz>0 不含税1 含税
							item["spbm"] = qdList[i]["spbm"];
							item["yhzcbs"] = 0;//优惠政策标识 0是不使用，1是使用
							console.log(item)
							group.push(item)
							/*group.push({
                                "@xh": i + 1,
                                fphxz: 0, //0=正常行 1=折扣行 2=被折扣行
                                spmc: qdList[i].spmc, // 下拉框
                                //<spsm>商品税目</spsm>[可空]
                                ggxh: qdList[i].ggxh,//[可空]
                                dw: qdList[i].dw,//单位</dw>[可空]tspz=12时，此处为“辆”
                                spsl: qdList[i].spsl,//>商品数量</spsl>[可空]小数点后16位tspz=12时，ggxh非空，此处为1；ggxh为空，可≥1
                                dj: qdList[i].dj,//单价</dj>[可空]小数点后16位
                                je: qdList[i].je,//金额</je>小数点后2位
                                //<kcje>扣除金额</kcje>小数点后2位，只有在差额征收时才添加此节点。
                                sl: qdList[i].sl,//税率</sl>小数点后2位
                                se: qdList[i].se,//税额</se>小数点后2位
                                hsbz: 1,//含税标志</hsbz>0 不含税1 含税
                                spbm: qdList[i].spbm, //>商品编码</spbm>下载的商品编码类
                                //<zxbm>纳税人自行编码</zxbm>纳税人自己增加的
                                yhzcbs: 0 //优惠政策标识 0是不使用，1是使用
                                //<slbs>0税率标识</slbs>空，是正常税率1:是免税2:是不征税3:普通零税率
                                //<zzstsgl>增值税特殊管理</zzstsgl>优惠政策的名称
                            })*/
							hjje = hjje.plus(new BigNumber(qdList[i].je))
							hjse = hjse.plus(new BigNumber(qdList[i].se))
							jshj = jshj.plus(new BigNumber(qdList[i].hj))
						}
						var input = {
							skpbh: skp.skpbh,
							skpkl: skp.skpkl,
							keypwd: skp.keypwd,
							fplxdm: fplxdm, // "004", "007"
							kplx: 0, //0：正数发票开具1：负数发票开具（红冲）
							xhdwsbh: skp.nsrsbh,
							xhdwmc: skp.nsrmc,
							xhdwdzdh: skp.xhdwdzdh,
							xhdwyhzh: skp.xhdwyhzh,
							ghdwsbh: row.taxIdentify,
							ghdwmc: row.billingPayable,
							ghdwdzdh: row.addressPhone,
							ghdwyhzh: (row.bank || '') + ' ' + (row.bankAccount || ''),
							bmbbbh: '40.0',//编码表版本号</bmbbbh>商品编码的总版本号[可空]
							hsslbs: 0//0-普通征收1-减按计增（仅支持税率0.015）2-差额征收
						}
						if (qdList.length > 8 || row.qdPrint == 1) {
							input['qdxm'] = {
								'@count': group.length,
								group: group
							}
						} else {
							input['fyxm'] = {
								'@count': group.length,
								group: group
							}
						}
						var zyspmc = null;
						console.log(temp)
						for (var mc in temp) {
							if (zyspmc == null || temp[mc].gt(temp[zyspmc])) {
								zyspmc = mc;
							}
						}
						Object.assign(input, {
							//<zhsl></zhsl>[可空]正数票可为空,负数票：对应蓝票相同税率时“综合税率填写sl”;对应蓝票不同税率时“综合税率99.01”
							hjje: hjje.toNumber(), //合计金额 小数点后2位
							hjse: hjse.toNumber(), //合计税额 小数点后2位
							jshj: jshj.toNumber(), //价税合计 小数点后2位
							bz: row.memo || "",//备注</bz>[可空]
							skr: global_config[row.billingCorp].skr,//[可空]
							fhr: global_config[row.billingCorp].fhr,//[可空]
							kpr: "[(${user?.userName})]",
							//<jmbbh>加密版本号</jmbbh>[可空]
							zyspmc: zyspmc,//>主要商品名称</zyspmc>[可空]
							//<spsm>商品税目</spsm>[可空]
							qdbz: (qdList.length > 8 || row.qdPrint == 1) ? 1 : 0,//清单标志 0=无清单 1=有清单
							//<ssyf>所属月份</ssyf>YYYYMM[可空]
							kpjh: skp.kpjh, //开票机号</kpjh>
							//<tzdbh>通知单编号</tzdbh>16位数字，最后以为校验位[可空]
							//<yfpdm>原发票代码</yfpdm>正数票时为空[可空]
							//<yfphm>原发票号码</yfphm>正数票时为空[可空]
							qmcs: "0000004282000000", //签名参数</qmcs>固定“0000004282000000”
							jybz: 1 //校验标志</jybz>1: 不校验单价数量
						})
						invoke("发票开具", "FPKJ", input, function(output) {
							/*<fplxdm>发票类型代码</fplxdm>
                            <fpdm>发票代码</fpdm>
                            <fphm>发票号码</fphm>
                            <kprq>开票日期</kprq>
                            <hjje>合计金额</hjje>
                            <skm>税控码</skm>*/
							row['fpdm'] = output.fpdm;
							row['fphm'] = output.fphm;
							row['fplxdm'] = output.fplxdm;
							if (!parent.window.kpDebug) {
								fpkj_cache.push(row.receBillingId)
							}
							record_process('receBillingId=' + row.receBillingId + "&receipt=2&fphm=" + output.fphm + "&fpdm=" + output.fpdm + "&fplxdm=" + output.fplxdm, function(success, errMsg){
								if (!success) {
									$.modal.alertError(row.vbillno +'[' + row.billingPayable+']' + row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
											+ ' 记录开票成功【' + output.fplxdm + '|' + output.fpdm + '|' + output.fphm + '】状态出错，请截图并告知管理员。原因：' + errMsg);
									$.table.refresh();
									return;
								}
								if (dqfphm != output.fphm) {
									alert("开票号码" + output.fphm + "与当前发票号码" + dqfphm + "不一致，请放入" + output.fphm + "】后点击“确定”")
								}
								if (global_kp_count == 1) { // 单个发票开具后这里直接打印
									$.modal.confirmAndCancel("即将打印，请确认放入的发票号码是【" + output.fphm + "】的【" + (fplxdm == "004" ? "专票" : "普票") + "】后点击“确认”", function () {
										// 3、打印
										fpdy(skp, 9, 1, output.fplxdm, output.fpdm, output.fphm, function (output) {
											record_process('receBillingId=' + row.receBillingId + "&fpdyStatus=1", function (success, errMsg) {
												console.log("打印结果记录", success, errMsg)
											});
											// 4、上报
											fpsc(skp, fplxdm, 1, function (output) {
												record_process('receBillingId=' + row.receBillingId + "&fpscStatus=1", function (success, errMsg) {
													console.log("上报结果记录", success, errMsg)
												})
												nextcall() // 调用下一个开票数据
											});
										})
									}, function () {
										$.table.refresh()
									})
								} else {
									nextcall();// 跳过开票和上传
								}
							})
						})
					})
				}
				if (global_kp_idx == 0) {
					$.modal.confirm("即将开票的号码是" + dqfphm + "，是否确认继续开票？", doSth)
				} else {
					doSth()
				}
			})
		}

		// 税控盘信息查询
		function skpxxcx(corp, _callback) {
			$.get(prefix + '/skp?id=' + corp, function(result) {
				if (result.code == 0) {
					invoke('税控盘信息查询', 'SKPXXCX', {
						'skpkl': result.data.skpkl
					}, function(output){
						//<?xml version="1.0" encoding="gbk"?>
						//<business comment="税控盘信息查询" id="SKPXXCX">
						//<body yylxdm="1">
						//<output>
						//<skpbh>499905116530</skpbh>税控盘编号
						//<nsrsbh>913206007974104798</nsrsbh>纳税人识别号
						//<nsrmc>南通吉华物流有限公司</nsrmc>纳税人名称
						//<swjgdm>***********</swjgdm>税务机关代码
						//<swjgmc>南通市崇川区</swjgmc>税务机关名称
						//<fplxdm>004007</fplxdm>发票类型代码
						//<dqsz>20211227165229</dqsz>当前时钟
						//<qysj>20160219093219</qysj>启用时间
						//<bbh>1000141021</bbh>版本号
						//<kpjh>0</kpjh>开票机号
						//<qylx>00</qylx>企业类型
						//<blxx>0000010000000000</blxx>保留信息
						//<qtkzxx/>其它扩展信息
						//<returncode>00000000</returncode>
						//<returnmsg>成功</returnmsg>
						//</output>
						//</body>
						//</business>
						var skp = output;
						console.log(skp, result.data)
						if (skp.skpbh != result.data.skpbh && !parent.window.kpDebug) {
							$.modal.alertError("税控盘不匹配，请插入“" + $.table.selectDictLabel(bala_corp, corp) + "”的税控盘");
							return
						}
						for (var k in result.data) {
							skp[k] = result.data[k]
						}
						invoke("注册码信息导入", "ZCMDR", {
							zcmxx: skp.zcmxx
						}, function(output){
							_callback(skp)
						})
					}, 2000)
				}
			})
		}

		function fpsc(skp, fplxdm, fpzs, callback) {
			/*
			<business comment="发票上传" id="FPSC">
			<body yylxdm="1">
			<input>
			<nsrsbh>纳税人识别号</nsrsbh>
			<skpbh>税控盘编号</skpbh>
			<skpkl>税控盘口令</skpkl>
			<keypwd>数字证书密码</keypwd>
			<svrIp>发票上传地址</svrIp>
			<svrPort>发票上传端口</svrPort>
			<fplxdm>发票类型</fplxdm>
			<fpzs>发票张数</fpzs>
			<czlx></czlx>
			</input>
			</body>
			</business>
			*/
			invoke("发票上传", "FPSC", {
				nsrsbh: skp.nsrsbh,
				skpbh: skp.skpbh,
				skpkl: skp.skpkl,
				keypwd: skp.keypwd,
				svrIp: "fpsc.jiangsu.chinatax.gov.cn",
				svrPort: "8001",
				fplxdm: fplxdm,
				fpzs: fpzs
			}, function(){
				invoke("发票上传结果", "FPSCJG", {
					nsrsbh: skp.nsrsbh,//纳税人识别号</nsrsbh>
					skpbh: skp.skpbh,//税控盘编号</skpbh>
					skpkl: skp.skpkl,//税控口令</skpkl>
					keypwd: skp.keypwd,//数字证书密码</keypwd>
					svrIp: "fpsc.jiangsu.chinatax.gov.cn",//发票上传结果地址</svrIp>
					svrPort: "8001",//发票上传结果端口</svrPort>
					fplxdm: fplxdm//发票类型</fplxdm>
				}, function (output) {
					if (fpzs != output.fpjgzs) {
						alert("上传数" + fpzs + "与返回数" + output.fpjgzs + "不一致")
					}
					callback && callback()
				})
			});
		}
		/*function once() {
			skpxxcx("MY", function(skp) {
				invoke("发票上传结果", "FPSCJG", {
					nsrsbh: skp.nsrsbh,//纳税人识别号</nsrsbh>
					skpbh: skp.skpbh,//税控盘编号</skpbh>
					skpkl: skp.skpkl,//税控口令</skpkl>
					keypwd: skp.keypwd,//数字证书密码</keypwd>
					svrIp: "fpsc.jiangsu.chinatax.gov.cn",//发票上传结果地址</svrIp>
					svrPort: "8001",//发票上传结果端口</svrPort>
					fplxdm: "004"//发票类型</fplxdm>
				})
			})
		}*/
		/**
		 * @param dylx 0：发票打印，1：清单打印	不弹对话框如下	9：发票打印，8：清单打印
		 * @param dyfs 0：批量打印 1：单张打印
		 */
		function fpdy(skp, dylx, dyfs, fplxdm, fpdm, fphm, callback) {
			/*
			<?xml version="1.0" encoding="gbk"?>
			<business comment="发票打印" id="FPDY">
				<body yylxdm="1">
					<input>
						<nsrsbh>纳税人识别号</nsrsbh>
						<skpbh>税控盘编号</skpbh>
						<skpkl>税控盘口令</skpkl>
						<keypwd>税务数字证书密码</keypwd>
						<fplxdm>发票类型代码</fplxdm>
						<fpdm>发票代码</fpdm>
						<fphm>发票号码</fphm>单张打印时为8，批量打印时，起始号码+终止号码=16位
						<dylx>打印类型</dylx>0：发票打印，1：清单打印	不弹对话框如下	9：发票打印，8：清单打印
						<dyfs>打印方式</dyfs>0：批量打印 1：单张打印
						<dyjmc>打印机名称</dyjmc>填写名称：为指定的打印机	不填名称：为电脑默认的打印机
					</input>
				</body>
			</business>
			*/
			invoke("发票打印", "FPDY", {
				nsrsbh: skp.nsrsbh,
				skpbh: skp.skpbh,
				skpkl: skp.skpkl,
				keypwd: skp.keypwd,
				fplxdm: fplxdm,
				fpdm: fpdm,
				fphm: fphm, // 单张打印时为8，批量打印时，起始号码+终止号码=16位
				dylx: dylx, // 0：发票打印，1：清单打印 不弹对话框如下 9：发票打印，8：清单打印
				dyfs: dyfs, // 0：批量打印，1：单张打印
				//dyjmc: "EPSON LQ-730KII"//填写名称：为指定的打印机 不填名称：为电脑默认的打印机
			}, callback)
		}

		function record_process(param, callback) {
			$.ajax({
				url: prefix + '/process',
				data: param + "&kpDebug=" + !!parent.window.kpDebug,
				type: 'post',
				success: function(ar){
					callback(ar.code === 0, ar.msg)
				},
				error: function(re) {
					callback(false, re.responseText)
				},
				complete:function() {}
			})
		}
		function ybjsz(fplxdm, top, left, callback) {
			/*
            <?xml version="1.0" encoding="gbk"?>
            <business comment="页边距设置" id="YBJSZ">
                <body yylxdm="1">
                    <input>
                        <fplxdm>发票类型代码</fplxdm>
                        <top>打印上边距</top>单位：毫米
                        <left>打印左边距</left>
                    </input>
                </body>
            </business>
            */
			invoke("页边距设置", "YBJSZ", {
				fplxdm: fplxdm,
				top: top,
				left: left
			}, function(){
				console.log(fplxdm, "页边距设置完成")
				callback && callback()
			})
		}
		function xml2string(xml) {
			return new XMLSerializer().serializeToString(xml)//.replace(/</g, '&lt;').replace(/>/g, '&gt;')
		}

		/**
		 *
		 *
		 * @param comment 参照文档的调用参数
		 * @param id 参照文档的调用参数
		 * @param input 参照文档的调用参数
		 * @param success 调用税控盘执行成功后调用，不成功不调用
		 * @param timeout 超时时间，不填默认20秒
		 */
		function invoke(comment, id, input, success, timeout) {
			_invoke(comment, id, input, function(isSuccess, output){
				if (isSuccess) {
					if (success) {
						success(output)
					}
				} else {
					alert(output.returnmsg)
					$.table.refresh();
				}
			}, timeout)
		}
		/**
		 *
		 *
		 * @param comment 参照文档的调用参数
		 * @param id 参照文档的调用参数
		 * @param input 参照文档的调用参数
		 * @param callback 调用税控盘执行后回调
		 * @param timeout 超时时间，不填默认20秒
		 */
		function _invoke(comment, id, input, callback, timeout) {
			var business = {
				'@comment': comment,
				'@id': id,
				'body': {
					'@yylxdm': '1',
					'input': input
				}
			};
			//$.modal.loading("正在" + comment + "...");
			var ii = $.modal.layerLoading("正在" + comment + "...")
			var xml_arg = $.json2xml(business, {"rootTagName": "business", "indentString": "", "formatOutput": false})
			console.log(xml_arg)
			if (parent.window.kpDebug) {
				callback && callback(true, {})
				//$.modal.closeLoadingImmediately()
				layer.close(ii)
				return;
			}
			$.ajax({
				url: 'http://127.0.0.1:12366/SKServer/SKDo',
				type: 'post',
				timeout: timeout||20000,
				data: xml_arg,
				global: false,
				success: function (xml_res) {
					console.log(xml2string(xml_res))
					layer.close(ii)
					var json = $.xml2json(xml_res);
					console.log(comment, id, json.body.output.returncode, json.body.output.returnmsg)
					if (callback) {
						callback(json.body.output.returncode == "00000000" || json.body.output.returncode == "0", json.body.output)
					}
				},
				error: function(XMLHttpRequest, textStatus, errorThrown) {
					layer.close(ii)
					if (textStatus == 'timeout') {
						alert("百旺SDK响应超时，请确认SDK已安装并已启动！")
					} else if (textStatus == "parsererror" || textStatus == "error") {
						alert("百旺SDK服务返回" + textStatus + "，请联系管理员！")
					}
					$.table.refresh();
				},
				complete: function(XMLHttpRequest, textStatus) {
					// 屏蔽ajaxSetup内的complete
				}
			});
		}
		function dyfp() {
			var rows = $.btTable.bootstrapTable('getSelections');
			var errMsg = [];
			var corp = rows[0].billingCorp;
			var fplxdm = billingLxdm[rows[0].billingType];
			for (var i = 0; i < rows.length; i++) {
				var row = rows[i]
				if (!row.fpdm || !row.fphm || !row.fplxdm) {
					errMsg.push(row.vbillno, '[', row.billingPayable,']', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 非在线开具的发票不支持在线打印<br>')
				} else if (row.billingCorp != corp) {
					errMsg.push(row.vbillno, '[', row.billingPayable,']', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 开票公司不一致不能一起打印<br>')
				} else if (billingLxdm[row.billingType] != fplxdm) {
					errMsg.push(row.vbillno, '[', row.billingPayable,']', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 普票专票不能混在一起开具<br>')
				}
			}
			if (errMsg.length > 0) {
				$.modal.alertError(errMsg.join(''));
				return;
			}
			if (rows.length > 1) {
				$.modal.alertError("暂只支持单个发票的打印");
				return;
			}
			var row = rows[0];
			skpxxcx(corp, function(skp){
				var top = 2;
				var left = -9.5;
				if (corp == 'JH') {
					//top = 2
					left = -8;
				}
				ybjsz(fplxdm, top, left, function(){
					$.modal.confirm("请确认放入的发票号码是【" + row.fphm + "】的【" + (fplxdm=="004" ? "专票" : "普票") + "】后点击“确认”", function(){
						fpdy(skp, 9, 1, row.fplxdm, row.fpdm, row.fphm, function(){
							$.modal.msg("打印命令发送完成")
						})
					})
				})
			})
		}
		function dyqd() {
			var rows = $.btTable.bootstrapTable('getSelections');
			var errMsg = [];
			var corp = rows[0].billingCorp;
			var arr = [];
			for (var i = 0; i < rows.length; i++) {
				var row = rows[i]
				if (!row.fpdm || !row.fphm || !row.fplxdm) {
					errMsg.push(row.vbillno, '<', row.billingPayable,'>', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 非在线开具的发票不支持在线打印<br>')
				} else if (row.billingCorp != corp) {
					errMsg.push(row.vbillno, '<', row.billingPayable,'>', row.billingAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}), ' 开票公司不一致不能一起打印<br>')
				} else if (row.qdCount <= 8 && row.qdPrint == 0) {
					continue;
				}
				arr.push(row);
			}
			if (errMsg.length > 0) {
				$.modal.alertError(errMsg.join(''));
				return;
			}
			if (arr.length == 0) {
				$.modal.alertError("未找到有清单的发票！");
				return;
			}
			var msg = "有" + arr.length + "张发票的<span style='color:red;font-weight: bold;'>[清单]</span>即将打印";
			if (arr.length < rows.length) {
				msg += "(另有" + (rows.length - arr.length) + "张无清单)";
			}
			msg += "，继续请点击“确认”";
			skpxxcx(corp, function(skp){
				$.modal.confirm(msg, function(){
					qddy_process(skp, arr, 0)
				})
			})
		}
		function qddy_process(skp, rows, idx) {
			if (idx >= rows.length) {
				$.modal.alertWarning("清单打印命令发送完成，请等待打印机完成打印...");
				return;
			}
			var row = rows[idx];
			var top = -3;
			var left = -9.5;
			ybjsz(row.fplxdm, top, left, function() {
				fpdy(skp, 8, 1, row.fplxdm, row.fpdm, row.fphm, function () {
					qddy_process(skp, rows, idx + 1)
				})
			})
		}
		function showsbfp() {
			layer.open({
				type: 1,
				area: ['250px', '200px'],
				fix: false,
				//不固定
				maxmin: false,
				shade: 0.3,
				title: '选择要上报的公司',
				content: `
					<div style="padding: 10px 40px;">
						<div class="radio">
						  <label>
							<input type="radio" name="sc_corp" value="MY" checked>
							铭源
						  </label>
						</div>
						<div class="radio">
						  <label>
							<input type="radio" name="sc_corp" value="JH">
							吉华
						  </label>
						</div>
					</div>
				`,
				btn: ['<i class="fa fa-check"></i> 上报', '<i class="fa fa-remove"></i> 取消'],
				// 弹层外区域关闭
				shadeClose: true,
				btn1: function(index, layero){
					var corp = $("[name='sc_corp']:checked").val();
					sbfp_process(corp)
					layer.close(index)
				}
			});
		}
		function sbfp_process(corp) {
			skpxxcx(corp, function(skp) {
				sbfp(skp, "004", function(){
					sbfp(skp, "007")
				})
			})
		}
		function sbfp(skp, fplxdm, nextCall) {
			// 未上传发票查询
			invoke("查询未上传发票数量", "FPCXWSC", {
				"nsrsbh": skp.nsrsbh,//纳税人识别号
				"skpbh": skp.skpbh,//税控盘编号
				"skpkl": skp.skpkl,//税控盘口令
				"fplxdm": fplxdm,//发票类型
				"keypwd": skp.keypwd//数字证书密码
			}, function (output) {
				var fplx = fplxdm == "004" ? "专票" : "普票"
				// Yylxdm	应用类型代码	1	是	1：国税 2：地税
				// fplxdm	发票类型代码	3	是
				// wscfpzs	未发票上传张数		是	未上传发票张数
				// wsczffpzs	作废发票未上传张数		是	未上传作废发票张数
				// returncode	返回代码	8	是	00000000成功，其它失败
				// returnmsg	返回信息	256	是
				var fpzs = parseInt(output.wscfpzs, 10) + parseInt(output.wsczffpzs, 10)
				if (fpzs > 0) {
					$.modal.confirmAndCancel("有" + fpzs + "张"+fplx+"未上传" +(parseInt(output.wsczffpzs, 10) > 0 ? '(含作废'+output.wsczffpzs+'张)': '')+ "，是否上传？", function(){
						fpsc(skp, fplxdm, fpzs, function(){
							$.modal.msg(fplx + "上传完成");
							nextCall && nextCall()
						})
					}, function(){
						nextCall && nextCall()
					})
				} else {
					nextCall && nextCall()
				}
			})
		}
		function cxkj() {
			var rows = $.btTable.bootstrapTable('getSelections');
			if (rows[0].billingStatus != 1) {
				$.modal.msgWarning("请先择已开票的数据");
				return;
			}
			let content = ``;
			if (rows[0].fphm) {
				content = `<div style="padding: 10px 40px;">
						<div class="checkbox">
							<label>
							    <input id="zf_flg" type="checkbox" checked> 作废发票【${rows[0].fphm}】(需要税控盘支持)
							</label>
					    </div>
					</div>`;
			} else {
				content = `<div style="padding: 10px 40px;">※ 请手动作废原发票：【${rows[0].checkNo}】</div>`
			}
			layer.open({
				type: 1,
				area: ['400px', '180px'],
				fix: false,
				//不固定
				maxmin: false,
				shade: 0.3,
				title: '重新开具发票',
				content: content,
				btn: ['<i class="fa fa-check"></i> 确定', '<i class="fa fa-remove"></i> 取消'],
				// 弹层外区域关闭
				shadeClose: true,
				btn1: function(index, layero){
					var dosth2 = function(callback) {
						$.ajax({
							url: prefix + '/cxkj',
							data: 'receBillingId=' + rows[0].receBillingId,
							type: 'post',
							success: function (ar) {
								if (ar.code == 0) {
									if (callback) {
										callback()
									} else {
										$.modal.msgSuccess("处理成功")
										$.table.refresh()
										layer.close(index)
									}
								} else {
									$.modal.msgError(ar.msg)
								}
							}
						})
					}
					if ($("#zf_flg").prop("checked")) {
						if(!rows[0].fplxdm || !rows[0].fpdm || !rows[0].fphm) {
							$.modal.alertWarning("非在线开具的发票不支持在线作废");
							return;
						}
						$.modal.confirm("确认重新开具并作废原发票吗？", function(){
							var hjje = new BigNumber(0);
							$.ajax({
								url: prefix + '/receBillingQd',
								data: 'receBillingId=' + rows[0].receBillingId,
								type: 'post',
								cache: false,
								async: false,
								success: function(aj) {
									for (var j = 0; j < aj.data.length; j++) {
										hjje = hjje.plus(new BigNumber(aj.data[j].je))
									}
								}
							})
							skpxxcx(rows[0].billingCorp, function(skp) {
								dosth2(function(){
									_invoke("发票作废", "FPZF", {
										nsrsbh: skp.nsrsbh,//纳税人识别号
										skpbh: skp.skpbh,//税控盘编号
										skpkl: skp.skpkl,//税控盘口令
										keypwd: skp.keypwd,//税务数字证书密码
										fplxdm: rows[0].fplxdm,//发票类型代码
										zflx: 1,//作废类型(0：空白发票作废，1：已开发票作废)
										fpdm: rows[0].fpdm,//发票代码(空白发票作废为空，长度根据发票类型不同)
										fphm: rows[0].fphm,//发票号码(空白发票作废为空)
										hjje: hjje.toNumber(),//合计金额(空白发票作废为空，单位：元（两位小数）)
										zfr: "[(${user?.userName})]"//作废人
										//qmcs: //签名参数
										//zffs: //作废份数(盘Cos升级后才能使用当作废类型：0，空白发票作废时，分数为1-100，当作废类型：1，已开发票作废时：为空)
									}, function(isSuccess, output){
										if (isSuccess) {
											$.modal.msgSuccess("开票状态已重置成功");
										} else {
											$.modal.alertError("开票状态已重置，但作废发票时发生异常：【" +output.returnmsg +"】，需要到开票系统作废原发票");
										}
										$.table.refresh()
										layer.close(index)
									})
								})
							});
						});
					} else {
						$.modal.confirm("确认重新开具发票吗？", function() {
							dosth2()
						})
					}
				}
			});
		}
    </script>
</body>
</html>