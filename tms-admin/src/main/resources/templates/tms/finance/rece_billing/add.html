<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款开票-add')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-receBilling-add" class="form-horizontal" novalidate="novalidate">
        <!--申请id-->
        <input th:value="${receSheetRecord.receSheetRecordId}" name="receSheetRecordId" type="hidden">
        <!--对账id-->
        <input th:value="${receSheetRecord.tReceCheckSheetId}" name="receCheckSheetId" type="hidden">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="deptId" type="hidden" id="treeId">

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkHead" style="color: red" >发票抬头：</label>
                                    <div class="col-sm-8">
                                        <select  name="billingPayable" id="billingPayable" onchange="selectBillingPayable()" class="form-control valid isCheckVal" required>
                                            <option ></option>
                                            <option th:each="dict : ${custBillings}"
                                                    th:text="${dict.billingPayable}"
                                                    th:value="${dict.billingPayable}"
                                                    th:id="${dict.custBillingId}"
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkAmount" style="color: red">开票金额：</label>
                                    <div class="col-sm-8">
                                        <input type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotals()"
                                               id="billingAmount" name="billingAmount" maxlength="15" class="form-control" required>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkDate" style="color: red">开票日期：</label>
                                    <div class="col-sm-8">
                                        <input type="text"  name="billingDate" id="billingDate" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkNo" style="color: red">发票号：</label>
                                    <div class="col-sm-8">
                                        <input type="text"  name="checkNo" id="checkNo" maxlength="25" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkCorp" style="color: red">开票公司：</label>
                                    <div class="col-sm-8">
                                        <select  name="billingCorp" id="billingCorp" class="form-control valid" required th:with="type=${@dict.getType('bala_corp')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkType" style="color: red">发票类型：</label>
                                    <div class="col-sm-8">
                                        <select name="billingType" id="billingType" class="form-control valid" th:with="type=${@dict.getType('billing_type')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkType" style="color: red">开户行：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="bank" id="bank" maxlength="125"
                                               class="form-control" required disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 isCheck" style="color: red">账号：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="bankAccount" id="bankAccount" maxlength="25"
                                               class="form-control isCheckVal" required disabled>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row">

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 isCheck" style="color: red">纳税人识别号：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="taxIdentify" id="taxIdentify" maxlength="25"
                                               class="form-control isCheckVal"  required disabled>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-2" style="color: red">地址及电话：</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="addressPhone" id="addressPhone"
                                               maxlength="105" class="form-control" disabled>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2 checkRemark" style="color: red">发票备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" maxlength="250" class="form-control valid"
                                                      rows="3"  required></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">发票：</label>
                                    <div class="col-sm-4">
                                        <input name="checkAppendix" id="checkAppendix" class="form-control" type="file" multiple>
                                        <input type="hidden" id="checkAppendixId" name="checkAppendixId" >
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()">
            <i class="fa fa-check"></i>保 存
        </button>
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "finance/receBilling";


    $(function () {
        $('#collapseOne').collapse('show');
        /**
         * 表单校验
         */
        $("#form-receBilling-add").validate({
            focusCleanup: true
        });

        /**
         * 初始化图片
         */
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: 'file',
        };
        $.file.initAddFiles("checkAppendix", "checkAppendixId",picParam);


        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#billingDate',
                type: 'datetime',
                trigger: 'click'
            });
        });

    });

    //提交
    function submitHandler() {
        $(":disabled").removeAttr("disabled");
        if ($.validate.form()) {
            $('#checkAppendix').fileinput('upload');
            jQuery.subscribe("cmt", commit);
        }
    }

    function commit(){
        $.operate.saveTab(prefix + "/add", $('#form-receBilling-add').serialize());
    }

    /**
     * 选择发票抬头 带出客户发票信息
     */
    function selectBillingPayable() {
        var id = $('#billingPayable').find("option:selected").attr("id");//开票信息id
        var url = ctx + "receCheckSheet/selectCustBilling";
        var data = {custBillingId:id};
        $.ajax({
            url: url,
            data: data,
            method: 'post',
            success: function (data) {
                //带入开票信息
                $("#billingCorp").val(data.billingCorp);//开票公司
                $("#billingType").val(data.billingType);//发票类型
                $("#bank").val(data.bank);//开户行
                $("#bankAccount").val(data.bankAccount);//账号
                $("#taxIdentify").val(data.taxIdentify);//纳税人识别号
                $("#addressPhone").val(data.addressPhone);//地址及电话
            }
        });
    }





</script>
</body>
</html>