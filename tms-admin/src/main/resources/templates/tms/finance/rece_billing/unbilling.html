<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('未开票明细列表')" />
	<th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
	/*.left-fixed-body-columns{
		height: calc(100% - 120px) !important;
	}*/
</style>
<body class="gray-bg">
    <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal" onsubmit="return false">
					<div class="row">
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<input type="text" class="form-control" id="yearMonthRange" name="params[yearMonthRange]" placeholder="月份">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<input name="params[custAbbr]" placeholder="客户简称" class="form-control" autocomplete="off">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<select name="salesDept" id="salesDeptId" class="form-control valid selectpicker"
											aria-invalid="false" data-none-selected-text="运营组" multiple>
										<option th:each="mapS,status:${salesDept}"
												th:value="${mapS.deptId}"
												th:text="${mapS.deptName}">
										</option>
									</select>
								</div>
							</div>
						</div>

						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="btn-group-sm" id="toolbar" role="group">
				<a class="btn btn-success" onclick="exportExcel()" shiro:hasPermission="finance:unbilling:out">
					<i class="fa fa-download"></i> 导出
				</a>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
			</div>
		</div>
	</div>

<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">

	var prefix = ctx + "finance/unbilling";
	var sumAmount = 0;
	var sumUnApplyAmount = 0;
	$(function() {
		//监听回车事件 回车搜索
		$(document).keyup(function(e){
			var key = e.which;
			if(key==13){
				$.table.search();
			}
		});

		var options = {
			url: prefix + "/list",
			//fixedColumns: true,
			//fixedNumber: 3,
			showToggle: false,
			showFooter: true,
			height: 560,
			onRefresh:function(params){
				clearTotal();
			},
			onCheck: function (row,$element) {
				addTotal(row);
				setTotal();
			},
			onUncheck: function (row, $element) {
				subTotal(row);
				setTotal();
			},
			onCheckAll: function (rowsAfter) {
				clearTotal();
				//循环累加
				for (var row of rowsAfter) {
					addTotal(row);
				}
				//赋值
				setTotal();
			},
			onUncheckAll: function () {
				//总数清0
				clearTotal();
				//赋值
				setTotal();
			},
			onPostBody: function (params) {
				clearTotal();
				//合并页脚
				merge_footer();
				getAmountCount();
			},
			columns: [
				{
					checkbox: true,
					footerFormatter: function (row) {
						return "小计: 未开票金额：<nobr id='sumAmount'>¥0.00</nobr> &nbsp;&nbsp; 未申请金额：<nobr id='sumUnApplyAmount'>¥0.00</nobr><br>"
								+ "总计: 未开票金额：<nobr id='sumAmountTotal'>¥0.00</nobr> &nbsp;&nbsp; 未申请金额：<nobr id='sumUnApplyAmountTotal'>¥0.00</nobr>";
					}
				},
				{
					title: '序号',
					formatter: function(value, row, index) {
						let option = $("#bootstrap-table").bootstrapTable('getOptions');
						let pageSize = option.pageSize;
						let pageNumber = option.pageNumber;
						return pageSize * (pageNumber - 1) + index + 1
					}
				},
				{
					title: '客户简称',
					field: 'custAbbr'
				},
				/*{
					title: '月份',
					field: 'yearMonth'
				},*/
				{
					title: '未开票金额(元) <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-placement="top" data-container="body" title="未对账的应收明细金额和已对账待申请的对账单金额合计"></i>',
					align: 'right',
					field: 'amount',
					formatter: function (value, row, index) {
						if (value == null) {
							return '';
						}
						return '<a href="javascript:viewDtl(\''+row.customerId+'\');">' + value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}) + '</a>';
					}
				},
				{
					title: '未申请金额(元) <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-placement="top" data-container="body" title="应收申请单中未提交开票的对账调整额和力资费金额"></i>',
					align: 'right',
					field: 'unApplyAmount',
					formatter: function (value, row, index) {
						if (value == null) {
							return '';
						}
						return '<a href="javascript:viewDtlUnApply(\''+row.customerId+'\');">' + value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}) + '</a>';
					}
				},
				{
					title: '运营组',
					field: 'salesDeptName'
				}
			]
		};
		$.table.init(options);

		/**
		 * 初始化日期控件
		 */
		layui.use('laydate', function() {
		 	var laydate = layui.laydate;
		// 	laydate.render({
		// 		elem: '#reqDeliDateStart',
		// 		type: 'date',
		// 		trigger: 'click'
		// 	});

		// 	laydate.render({
		// 		elem: '#reqDeliDateEnd',
		// 		type: 'date',
		// 		trigger: 'click'
		// 	});
			laydate.render({
				elem: '#yearMonthRange',
				type: 'month',
				range: true
			});
		});

	});

	function searchx() {
		var data = {};
		data.salesDept = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
		//data['params[vbillstatus]'] = $.common.join($('#vbillstatus').selectpicker('val'));//状态
		$.table.search('role-form', data);
	}

	function viewDtl(customerId) {
		var url = prefix + "/unbilling_dtl?customerId=" + customerId;
		parent.layer.open({
			type: 2,
			title: "未开票明细",
			area: ['1000px', '699px'],
			content: url,
			btn: ['<i class="fa fa-close"></i> 关闭'],
			yes: function (index, layero) {
				parent.layer.close(index);
			}
		});
	}
	function viewDtlUnApply(customerId) {
		var url = prefix + "/unapply_dtl?customerId=" + customerId;
		parent.layer.open({
			type: 2,
			title: "未申请明细",
			area: ['1000px', '699px'],
			content: url,
			btn: ['<i class="fa fa-close"></i> 关闭'],
			yes: function (index, layero) {
				parent.layer.close(index);
			}
		});
	}
	/**
	 * 合并页脚
	 */
	function merge_footer() {
		$('.fixed-table-footer').find("td:first").attr("colspan", $('.fixed-table-footer').find("td").length)
		$('.fixed-table-footer').find("td:first").siblings().remove();
	}

	function getAmountCount() {
		var data = $.common.formToJSON("role-form");
		data.salesDept = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
		//data['params[vbillstatus]'] = $.common.join($('#vbillstatus').selectpicker('val'));//状态
		$.post(prefix + "/getCount", data, function(result){
			if (result.code == 0) {
				var data = result.data;
				$("#sumAmountTotal").text(data.SUMAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
				$("#sumUnApplyAmountTotal").text(data.SUMUNAPPLYAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
			} else {
				$("#sumAmountTotal").text("ERROR");
				$("#sumUnApplyAmountTotal").text("ERROR");
			}
		});
	}

	function exportExcel() {
		$.modal.confirm("即将导出，是否继续？", function() {
			$.modal.loading("正在导出数据，请稍后...");
			var search = $.common.formToJSON("role-form");
			search['salesDept'] = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
			//search['params[vbillstatus]'] = $.common.join($('#vbillstatus').selectpicker('val'));//状态
			$.post(prefix + "/export", search, function(result) {
				if (result.code == web_status.SUCCESS) {
					window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
				} else if (result.code == web_status.WARNING) {
					$.modal.alertWarning(result.msg)
				} else {
					$.modal.alertError(result.msg);
				}
				$.modal.closeLoading();
			});
		});
	}

	/**
	 * 将总计金额清零
	 */
	function clearTotal() {
		//开票金额合计
		sumAmount = 0
		sumUnApplyAmount = 0
	}

	/**
	 * 累计总金额
	 */
	function addTotal(row) {
		sumAmount += row.amount;
		sumUnApplyAmount += row.unApplyAmount
	}

	/**
	 *
	 */
	function subTotal(row) {
		sumAmount -= row.amount;
		sumUnApplyAmount -= row.unApplyAmount
	}

	/**
	 *
	 * 给页脚总计赋值
	 */
	function setTotal() {
		$("#sumAmount").text(sumAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
		$("#sumUnApplyAmount").text(sumUnApplyAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
	}

</script>
</body>
</html>