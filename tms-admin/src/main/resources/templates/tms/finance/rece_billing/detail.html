<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款开票-detail')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-receBilling-edit" class="form-horizontal" novalidate="novalidate" th:object="${receBilling}">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="deptId" type="hidden" id="treeId">

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkHead"  >发票抬头：</label>
                                    <div class="col-sm-8" th:text="*{billingPayable}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkAmount" >开票金额(元)：</label>
                                    <div class="col-sm-8" th:text="*{billingAmount}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkDate" >开票日期：</label>
                                    <div class="col-sm-8" th:text="*{#dates.format(billingDate, 'yyyy-MM-dd HH:mm:ss')}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkNo" >发票号：</label>
                                    <div class="col-sm-8" th:text="*{checkNo}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkCorp" >开票公司：</label>
                                    <div class="col-sm-8"  th:each="dict : ${@dict.getType('bala_corp')}"
                                         th:if="${dict.dictValue} == *{billingCorp}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkType">发票类型：</label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('billing_type')}"
                                         th:if="${dict.dictValue} == *{billingType}" th:text="${dict.dictLabel}">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkType" >开户行：</label>
                                    <div class="col-sm-8" th:text="*{bank}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 isCheck" >账号：</label>
                                    <div class="col-sm-8" th:text="*{bankAccount}">
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row">

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 isCheck" >纳税人识别号：</label>
                                    <div class="col-sm-8" th:text="*{taxIdentify}">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-2" >地址及电话：</label>
                                    <div class="col-sm-10" th:text="*{addressPhone}">
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2 checkRemark" >发票备注：</label>
                                    <div class="col-md-11 col-sm-10" th:text="*{memo}">

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2 checkRemark" >备注：</label>
                                    <div class="col-md-11 col-sm-10" th:text="*{remark}">

                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--<div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">发票：</label>
                                    <div class="col-sm-4">
                                        <input name="checkAppendix" id="checkAppendix" class="form-control" type="file" multiple>
                                        <input type="hidden" id="checkAppendixId" name="checkAppendixId" th:field="*{checkAppendixId}">
                                    </div>
                                </div>
                            </div>
                        </div>-->
                        <div class="row no-gutter">
                            <div class="col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group">
                                    <label class="col-sm-2">发票附件：</label>
                                    <div class="col-sm-10">
                                        <ul>
                                            <li th:each="file:${sysUploadFiles}">
                                                <a target="view_window" th:href="@{${file.filePath}}" th:text="${file.fileName}"></a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" id="accordion2">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion2"
                           href="tabs_panels.html#collapseTwo">发票清单</a>
                        <label style="color:blue">[[*{qdPrint == 1 ? '需要单独打印销货清单' : ''}]]</label>
                    </h4>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <table class="custom-tab tab table table-bordered" th:if="*{qdList.size() > 0}">
                            <thead style="background: #f4f6f7;">
                            <tr>
                                <th style="width: 20%;">货物或应税劳务名称</th>
                                <th style="width: 10%;">规格型号</th>
                                <th>单位</th>
                                <th>数量</th>
                                <th>单价</th>
                                <th>金额</th>
                                <th>税额</th>
                                <th>含税额</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="qd:*{qdList}">
                                <td th:text="${qd.spmc}"></td>
                                <td th:text="${qd.ggxh}"></td>
                                <td th:text="${qd.dw}"></td>
                                <td th:text="${qd.spsl}"></td>
                                <td th:text="${qd.dj}"></td>
                                <td th:text="${qd.je}"></td>
                                <td th:text="${qd.se}"></td>
                                <td th:text="${qd.hj}"></td>
                            </tr>
                            </tbody>
                        </table>
                        <div th:unless="*{qdList.size() > 0}">
                            <div style="line-height: 32px;">无清单</div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "finance/receBilling";
    //图片
    var sysUploadFiles = [[${sysUploadFiles}]];

    $(function () {
        $('#collapseOne').collapse('show');
        /**
         * 表单校验
         */
        $("#form-receBilling-edit").validate({
            focusCleanup: true
        });

        /**
         * 初始化图片
         */
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: 'file',
        };
        $.file.loadEditFiles("checkAppendix", "checkAppendixId",sysUploadFiles,picParam);


        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#billingDate',
                type: 'datetime',
                trigger: 'click'
            });
        });

    });

    //提交
    function submitHandler() {
        $(":disabled").removeAttr("disabled");
        if ($.validate.form()) {
            $('#checkAppendix').fileinput('upload');
            jQuery.subscribe("cmt", commit);
        }
    }

    function commit(){
        $.operate.saveTab(prefix + "/edit", $('#form-receBilling-edit').serialize());
    }

    /**
     * 选择发票抬头 带出客户发票信息
     */
    function selectBillingPayable() {
        var id = $('#billingPayable').find("option:selected").attr("id");//开票信息id
        var url = ctx + "receCheckSheet/selectCustBilling";
        var data = {custBillingId:id};
        $.ajax({
            url: url,
            data: data,
            method: 'post',
            success: function (data) {
                //带入开票信息
                $("#billingCorp").val(data.billingCorp);//开票公司
                $("#billingType").val(data.billingType);//发票类型
                $("#bank").val(data.bank);//开户行
                $("#bankAccount").val(data.bankAccount);//账号
                $("#taxIdentify").val(data.taxIdentify);//纳税人识别号
                $("#addressPhone").val(data.addressPhone);//地址及电话
            }
        });
    }





</script>
</body>
</html>