<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款开票-edit')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-receBilling-edit" class="form-horizontal" novalidate="novalidate" th:object="${receBilling}">
        <!--开票信息id-->
        <input th:value="${receBilling.receBillingId}" name="receBillingId" type="hidden">
        <!--对账id-->
        <input th:value="${receBilling.receCheckSheetId}" name="receCheckSheetId" type="hidden">
        <!--申请id-->
        <input th:value="${receBilling.receSheetRecordId}" name="receSheetRecordId" type="hidden">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="deptId" type="hidden" id="treeId">

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkHead" style="color: red" >发票抬头：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="billingPayable" class="form-control valid" th:value="*{billingPayable}" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkAmount" style="color: red">开票金额(元)：</label>
                                    <div class="col-sm-8">
                                        <input type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotals()" th:value="*{billingAmount}"
                                               id="billingAmount" name="billingAmount" maxlength="15" class="form-control" disabled required>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkDate" style="color: red">开票日期：</label>
                                    <div class="col-sm-8">
                                        <input type="text"  name="billingDate" id="billingDate" class="form-control" readonly
                                               th:value="*{#dates.format(billingDate, 'yyyy-MM-dd HH:mm:ss')}" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkNo" style="color: red">发票号：</label>
                                    <div class="col-sm-8">
                                        <input type="text"  name="checkNo" id="checkNo" th:value="*{checkNo}" maxlength="25" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkCorp" style="color: red">开票公司：</label>
                                    <div class="col-sm-8">
                                        <select  name="billingCorp" id="billingCorp" class="form-control valid" required th:with="type=${@dict.getType('bala_corp')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:field="*{billingCorp}"
                                                    th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkType" style="color: red">发票类型：</label>
                                    <div class="col-sm-8">
                                        <select name="billingType" id="billingType" class="form-control valid" th:with="type=${@dict.getType('billing_type')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" th:field="*{billingType}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 checkType">开户行：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="bank" id="bank" maxlength="125" th:field="*{bank}"
                                               class="form-control"  >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 isCheck">账号：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="bankAccount" id="bankAccount" maxlength="25" th:field="*{bankAccount}"
                                               class="form-control isCheckVal" >
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row">

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 isCheck" style="color: red">纳税人识别号：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="taxIdentify" id="taxIdentify" maxlength="25" th:field="*{taxIdentify}"
                                               class="form-control isCheckVal"  required >
                                    </div>
                                </div>
                            </div>


                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-2" style="color: red">地址及电话：</label>
                                    <div class="col-sm-10">
                                        <input type="text" name="addressPhone" id="addressPhone" th:field="*{addressPhone}"
                                               maxlength="105" class="form-control" >
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">催收时间：</label>
                                    <div class="col-sm-8">
                                        <input type="hidden" name="urgeTime" id="urgeTime">
                                        <input type="hidden" name="urgeBillingId" id="urgeBillingId">
                                        <div class="form-control" style="display: flex;align-items: center">
                                            <a style="flex: 1" href="javascript:connRed();" id="urgeHandler">点击选择红冲数据</a>
                                            <span class="fa fa-times-circle" style="font-size: 15px; color:#ccc;display: none" data-toggle='tooltip' title='清空已关联红冲' id="clearBtn" onclick="clearUrge(this)"></span>
                                            <span class="fa fa-question-circle" style="font-size: 15px; color:red;margin-left: 3px" data-toggle='tooltip' title='催收时间与关联的红冲开票时间一致'></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2 checkRemark">发票备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" class="form-control valid" th:text="*{memo}"
                                                      rows="3"  ></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2 checkRemark">备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="remark" class="form-control valid" th:text="*{remark}"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">发票：</label>
                                    <div class="col-sm-4">
                                        <input name="checkAppendix" id="checkAppendix" class="form-control" type="file" multiple>
                                        <input type="hidden" id="checkAppendixId" name="checkAppendixId" th:field="*{checkAppendixId}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()">
            <i class="fa fa-check"></i>保 存
        </button>
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "finance/receBilling";
    //图片
    var sysUploadFiles = [[${sysUploadFiles}]];

    $(function () {
        $('#collapseOne').collapse('show');
        /**
         * 表单校验
         */
        $("#form-receBilling-edit").validate({
            focusCleanup: true
        });

        /**
         * 初始化图片
         */
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: 'file',
        };
        $.file.loadEditFiles("checkAppendix", "checkAppendixId",sysUploadFiles,picParam);


        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#billingDate',
                type: 'datetime',
                trigger: 'click',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours=now.getHours();
                    this.dateTime.minutes=now.getMinutes();
                    this.dateTime.seconds=now.getSeconds();
                },
            });

        });

        $('[data-toggle="tooltip"]').tooltip()
    });

    //提交
    function submitHandler() {
        $.unsubscribe("cmt");
        $(":disabled").removeAttr("disabled");
        if ($.validate.form()) {
            $('#checkAppendix').fileinput('upload');
            jQuery.subscribe("cmt", commit);
        }
    }

    function commit(){
        $.operate.saveTab(prefix + "/edit", $('#form-receBilling-edit').serialize());
    }

    /**
     * 选择发票抬头 带出客户发票信息
     */
    function selectBillingPayable() {
        var id = $('#billingPayable').find("option:selected").attr("id");//开票信息id
        var url = ctx + "receCheckSheet/selectCustBilling";
        var data = {custBillingId:id};
        $.ajax({
            url: url,
            data: data,
            method: 'post',
            success: function (data) {
                //带入开票信息
                $("#billingCorp").val(data.billingCorp);//开票公司
                $("#billingType").val(data.billingType);//发票类型
                $("#bank").val(data.bank);//开户行
                $("#bankAccount").val(data.bankAccount);//账号
                $("#taxIdentify").val(data.taxIdentify);//纳税人识别号
                $("#addressPhone").val(data.addressPhone);//地址及电话
            }
        });
    }

    function connRed() {
        var receSheetRecordId = [[${receBilling.receSheetRecordId}]];
        $.modal.open('选择红冲数据', prefix + '/connAbleRedList?receSheetRecordId=' + receSheetRecordId, '990', '600', function (index, layero) {
            var rows = layero.find('iframe')[0].contentWindow.$.btTable.bootstrapTable('getSelections');
            if (rows.length === 0) {
                $.modal.alertWarning("请选择一条记录");
                return;
            }
            $('#urgeHandler').text(rows[0].billingDate);
            $('#clearBtn').show();
            $('#urgeTime').val(rows[0].billingDate);
            $('#urgeBillingId').val(rows[0].receBillingId);
            layer.close(index);
        })
    }

    function clearUrge(btn) {
        $('#urgeHandler').text('点击选择红冲数据');
        $('#urgeTime').val('');
        $('#urgeBillingId').val('');
        $(btn).hide()
    }
</script>
</body>
</html>