<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款申请管理-分批收款')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        label.invalid {
            background-color: rgb(237, 85, 101);
            color: #fff;
            line-height: 26px;
            height: 26px;
            font-size: 12px;
            padding: 0 5px;
        }


    </style>
</head>

<body>
<div class="form-content">
    <form id="form-batchRece-add" class="form-horizontal" novalidate="novalidate">
        <!--收款申请ids-->
        <input type="hidden" id="receBillingIds" name="receBillingIds" th:value="${receBillingIds}">
        <input name="receivableType" class="form-control" value="0" type="hidden">
        <input type="hidden" name="receSheetRecordId" th:value="${receSheetRecordId}">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="deptId" type="hidden" id="treeId">

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">开票金额：</label>
                                    <div class="col-sm-8" >
                                        <input type="text"  disabled  maxlength="15"
                                               th:value="${totalAmount}" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">已收金额：</label>
                                    <div class="col-sm-8" >
                                        <input type="text" disabled th:value="${gotAmount}" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收款金额：</label>
                                    <div class="col-sm-8" >
                                        <input type="text" maxlength="15" name="receivableAmount" id="receivableAmount"
                                               oninput="$.numberUtil.onlyNumber(this);changeReceivableAmount()"
                                               th:value="${ungotAmount > (sumSheetRecordUngotAmount - sumSheetRecordTariffUngotIncome) ? (sumSheetRecordUngotAmount - sumSheetRecordTariffUngotIncome) : ungotAmount}" required class="form-control valid" aria-required="true">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="receivableMethod" id="receivableMethod" class="form-control"
                                                onchange="changeReceivableMethod()"
                                                th:with="type=${@dict.getType('receivable_method')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款日期：</label>
                                    <div class="col-sm-8">
                                        <input name="receivableDate" id="receivableDate" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">转入账户：</label>
                                    <div class="col-md-8 col-sm-8">
                                        <div class="input-group">
                                            <input name="accountName" id="accountName" required class="form-control valid"
                                                   type="text" aria-required="true" maxlength="50">
                                            <!--账户id-->
                                            <input name="inAccount" id="inAccount"  class="form-control valid"
                                                   type="hidden" aria-required="true">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row" id="draftDiv" style="display:none">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">票据号：</label>
                                    <div class="col-sm-8" >
<!--                                        <input type="text" id="ticketNumber" name="ticketNumber" placeholder="票据号" class="form-control valid">-->
                                        <div class="input-group">
                                            <input class="form-control" type="text" id="ticketNumber" name="ticketNumber" placeholder="票据号">
                                            <input class="form-control" type="hidden" id="draftId" name="draftId">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu" >
                                                </ul>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">承兑类型：</label>
                                    <div class="col-sm-8">
                                        <select name="billTypeId" id="billTypeId" class="form-control"
                                                th:with="bill=${@dict.getType('draft_bill_type')}">
                                            <option value=""></option>
                                            <option th:each="billDict : ${bill}" th:text="${billDict.dictLabel}"  th:value="${billDict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">承兑金额：</label>
                                    <div class="col-sm-8">
                                        <span id="amountMoneySpan"></span>
                                        <span id="amountMoneyAddSpan">[[${ungotAmount > (sumSheetRecordUngotAmount - sumSheetRecordTariffUngotIncome) ? (sumSheetRecordUngotAmount - sumSheetRecordTariffUngotIncome) : ungotAmount}]]元</span>
                                    </div>
                                </div>
                            </div>


                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">出票日期：</label>
                                    <div class="col-sm-8" >
                                        <input name="issueDate" id="issueDate" placeholder="请输入出票日" class="form-control"
                                               type="text" maxlength="50" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">到期日期：</label>
                                    <div class="col-sm-8" >
                                        <input name="dueDate" id="dueDate" placeholder="请输入到期日" class="form-control"
                                               type="text" maxlength="50" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">付票单位：</label>
                                    <div class="col-sm-8">
                                        <select name="payCompany" id="payCompany" class="form-control" onchange="changePayCompany()">
                                            <option value=""></option>
                                            <option th:each="custBilling : ${custBillings}"
                                                    th:data-bank="${custBilling.bank}"
                                                    th:text="${custBilling.billingPayable}"
                                                    th:value="${custBilling.billingPayable}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付票行：</label>
                                    <div class="col-sm-8">
                                        <input name="issuingBank" id="issuingBank" placeholder="付票行" class="form-control"
                                               type="text" maxlength="50" autocomplete="off">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" maxlength="250" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="color: #006dcc" th:if="${ungotAmount > (sumSheetRecordUngotAmount - sumSheetRecordTariffUngotIncome)}">
            * 当前申请单含有[[${sumSheetRecordTariffUngotIncome}]]元其他力资费未核销，当前最多可核销[[${sumSheetRecordUngotAmount - sumSheetRecordTariffUngotIncome}]]元；<br>
            * “其他力资费”的收款核销请在力资费专用界面进行操作
        </div>
    </form>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">
    var prefix = ctx + "receSheetRecord";

    let balaCustomerId = [[${balaCustomerId}]]
    let billingCorp = [[${billingCorp}]]

    /**
     * 默认收款日期为当前时间
     */
    var time = new Date();
    var day = ("0" + time.getDate()).slice(-2);
    var month = ("0" + (time.getMonth() + 1)).slice(-2);
    var today = time.getFullYear() + "-" + (month) + "-" + (day);
    var h=time.getHours();       //获取当前小时数(0-23)
    var m=time.getMinutes();     //获取当前分钟数(0-59)
    var s=time.getSeconds();     //获取当前秒数
    var today = time.getFullYear() + "-" + (month) + "-" + (day) + " "+h+":" +m+ ":"+s;
    $("#receivableDate").val(today);

    var quota = parseFloat($("#quota").val());//收款额度

    $(function () {
        $('#collapseOne').collapse('show');
        /** 校验 */
        $("#form-batchRece-add").validate({
            focusCleanup: true
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#receivableDate',
                type: 'datetime',
                trigger: 'click'
            });
            laydate.render({
                elem: '#issueDate',
                type: 'datetime',
                trigger: 'click'
            });
            laydate.render({
                elem: '#dueDate',
                type: 'datetime',
                trigger: 'click'
            });
        });

        $('input[name="selectAll"]').on("change",function(){
            if($(this).is(':checked')){
                $('input[name="rmReq"]').each(function(){
                    $(this).attr("tag","selected");
                    $(this).prop("checked",true);
                });
            }else{
                $('input[name="rmReq"]').each(function(){
                    $(this).attr("tag","");
                    $(this).prop("checked",false);
                });
            }
        });

        $('input[name="rmReq"]').on("change",function(){
            if($(this).is(':checked')){
                $(this).attr("tag","selected");
                if($("input[name='rmReq']:checked").length ==  $('input[name="rmReq"]').length){
                    $('input[name="selectAll"]').prop("checked",true);
                }
            }else{
                $(this).attr("tag","");
                $('input[name="selectAll"]').prop("checked",false);
            }
        });

        //除了表头（第一行）以外所有的行添加click事件.
        $("#table").find("tr").slice(1).click(function () {
            // // 切换样式
            // $(this).toggleClass("tr_active");
            // 找到checkbox对象
            var chks = $("input[type='checkbox']",this);
            var tag = chks.attr("tag");
            if(tag=="selected"){
                // 之前已选中，设置为未选中
                chks.attr("tag","");
                chks.prop("checked",false).change();
            }else{
                // 之前未选中，设置为选中
                chks.attr("tag","selected");
                chks.prop("checked",true).change();
            }
        });


        initBsSuggest()
    });

    function changePayCompany() {
        var selectedOption = $('#payCompany').find('option:selected');
        var bankValue = selectedOption.data('bank');
        $('#issuingBank').val(bankValue);
    }

    function changeReceivableMethod() {
        let receivableMethod = $("#receivableMethod").val();

        if (receivableMethod == 10) {
            $('#draftDiv').show()
        }else {
            $('#draftDiv').hide()
        }

        // $("#form-batchRece-add").valid();
    }

    function changeReceivableAmount() {
        let val = $("#receivableAmount").val();
        $("#amountMoneyAddSpan").text(val + "元");
    }

    function initBsSuggest() {
        $(`#ticketNumber`).bsSuggest('init', {
            url: ctx + `finance/draft/listByTicketNumber?balaCustomerId=${balaCustomerId}&billingCorp=${billingCorp}&keyword=`,
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "id",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: [
                "ticketNumber",

                "billType",
                // "issueDate",
                // "dueDate",
                "payCompany",
                // "issuingBank"
                "balaCorp"
            ],
            effectiveFieldsAlias: {
                "ticketNumber": "票号",
                "billType": "承兑类型",
                // "issueDate": "出票日期",
                // "dueDate": "到期日期",
                "payCompany": "付票单位",
                // "issuingBank": "付票行"
                "balaCorp": "结算公司"
            },
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#draftId`).val(data.id);
            $(`#ticketNumber`).val(data.ticketNumber);
            $(`#billTypeId`).val(data.billTypeId);
            $(`#issueDate`).val(data.issueDate);
            $(`#dueDate`).val(data.dueDate);
            $(`#payCompany`).val(data.payCompany);
            $(`#issuingBank`).val(data.issuingBank);

            $("#amountMoneySpan").text(data.amountMoney + "元（原汇票金额） +");
            // $("#form-batchRece-add").valid();
        })


        $('#ticketNumber').on('keyup', function() {
            $('#draftId').val('');

            $("#amountMoneySpan").text("新添汇票：");
        });
        // $('#userName').on('blur', function() {
        //     if ($('#userId').val() === '') {
        //         $('#userName').val('');
        //     }
        // });

    }

    $("#form-batchRece-add").validate({
        errorClass:"invalid",
        rules:{
            receivableAmount: {
                required: true,
                number: true,
                max: [[${ungotAmount}]],
                min: 0.01
            },
            ticketNumber: {
                required: function() {
                    return $("#receivableMethod").val() == "10";
                }
            },
            billTypeId: {
                required: function() {
                    return $("#receivableMethod").val() == "10";
                }
            },
            issueDate: {
                required: function() {
                    return $("#receivableMethod").val() == "10";
                }
            },
            dueDate: {
                required: function() {
                    return $("#receivableMethod").val() == "10";
                }
            },
            payCompany: {
                required: function() {
                    return $("#receivableMethod").val() == "10";
                }
            },

        },
        messages: {
            receivableAmount: {
                max: "收款金额不能超过未收金额[[${ungotAmount}]]",
                min: "收款金额最低不能低于1分钱"
            },
            ticketNumber: {
                required: "当收款方式为承兑时，票据号为必填"
            },
            billTypeId: {
                required: "当收款方式为承兑时，承兑类型为必填"
            },
            issueDate: {
                required: "当收款方式为承兑时，出票日期为必填"
            },
            dueDate: {
                required: "当收款方式为承兑时，到期日期为必填"
            },
            payCompany: {
                required: "当收款方式为承兑时，付票单位为必填"
            },

        },
        focusCleanup: true
    });

    //提交
    function submitHandler(layerIndex, layero) {
        if ($.validate.form()) {
            var data = $('#form-batchRece-add').serializeArray();
            data.push({"name": "params[receBillingIds]", "value": $("#receBillingIds").val()});
            $.modal.confirm("是否确定核销", function() {
                $.operate.saveModal(ctx + "finance/receBilling/doBatchRece", data, function(r){
                    if (r.code == 0) {
                        $.modal.close();
                        parent.$.modal.msgSuccess("收款成功");
                        parent.$.table.refresh();
                    }
                });
            });
        }
    }

    /**
     * 展示开票对应的收款记录
     * @param receBillingId
     */
    function detail(receBillingId) {
        layer.open({
            type: 2,
            area: ['60%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "开票收款记录",
            content: ctx + "receSheetRecord/record_billing/" + receBillingId,
            btn: ['关闭'],
            shadeClose: true,            // 弹层外区域关闭
            btn3: function (index, layero) {
                return true;
            }
        });
    }

    /**
     * 关键字提示查询 转入账户
     */
    $("#accountName").bsSuggest('init', {
        url: ctx + "finance/account/findAccount?paymentType=0&keyword=",
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["accountName"],
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#accountName").val(data.accountName);
        $("#inAccount").val(data.accountId);
    });



</script>
</body>

</html>