<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('未开票明细列表')" />
	<th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
	/*.left-fixed-body-columns{
		height: calc(100% - 120px) !important;
	}*/
	.table-striped {
		height: calc(100% - 80px);
	}
</style>
<body class="gray-bg">
    <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal">
					<input type="hidden" name="customerId" th:value="${customerId}">
					<div class="row">
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<select name="vbillstatus" id="vbillstatus" class="form-control valid selectpicker"
											aria-invalid="false" data-none-selected-text="应收单状态" multiple>
										<option value="0">待审核</option>
										<option value="1">审核通过</option>
										<option value="3">部分收款</option>
										<option value="4">已收款</option>
									</select>
								</div>
							</div>
						</div>

						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="btn-group-sm" id="toolbar" role="group">
				<a class="btn btn-success" onclick="exportExcel()" shiro:hasPermission="finance:unbilling:out">
					<i class="fa fa-download"></i> 导出
				</a>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
			</div>
		</div>
	</div>

<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">

	var prefix = ctx + "finance/unbilling";
	var sumAmount = 0;
	$(function() {
		//监听回车事件 回车搜索
		$(document).keyup(function(e){
			var key = e.which;
			if(key==13){
				$.table.search();
			}
		});
		var options = {
			url: prefix + "/unapplyDtl",
			//fixedColumns: true,
			//fixedNumber: 3,
			showToggle: false,
			//showFooter: true,
			height: 560,
			onRefresh:function(params){
				clearTotal();
			},
			onCheck: function (row,$element) {
				addTotal(row);
				setTotal();
			},
			onUncheck: function (row, $element) {
				subTotal(row);
				setTotal();
			},
			onCheckAll: function (rowsAfter) {
				clearTotal();
				//循环累加
				for (var row of rowsAfter) {
					addTotal(row);
				}
				//赋值
				setTotal();
			},
			onUncheckAll: function () {
				//总数清0
				clearTotal();
				//赋值
				setTotal();
			},
			onPostBody: function (params) {
				//合并页脚
				merge_footer();
				getAmountCount();
			},
			columns: [
				{
					title: '序号',
					formatter: function(value, row, index) {
						let option = $("#bootstrap-table").bootstrapTable('getOptions');
						let pageSize = option.pageSize;
						let pageNumber = option.pageNumber;
						return pageSize * (pageNumber - 1) + index + 1
					},
					//footerFormatter: function (row) {
					//	return "总金额：<nobr id='sumAmountTotal'>¥0.00</nobr>";
					//}
				},
				{
					title: '应收申请单号',
					field: 'vbillno'
				},
				{
					title: '应收申请单状态',
					field: 'vbillstatus',
					formatter: function status(value, row, index) {
						switch (value) {
							case 0:
								return '<span class="label label-primary">待审核</span>'
							case 1:
								return '<span class="label label-warning">审核通过</span>';
							case 3:
								return '<span class="label label-info">部分收款</label>';
							case 4:
								return '<span class="label label-success">已收款</span>';
							default:
								break;
						}
					}
				},
				{
					title: '调整额(元)',
					align: 'right',
					field: 'adjustAmount',
					formatter: function (value, row, index) {
						if (value == null) {
							return '';
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				},
				{
					title: '力资费(元)',
					align: 'right',
					field: 'tariffAmount',
					formatter: function (value, row, index) {
						if (value == null) {
							return '';
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				},
				{
					title: '开票金额(元)',
					align: 'right',
					field: 'checkAmount',
					formatter: function (value, row, index) {
						if (value == null) {
							return '';
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				},
				{
					title: '现金金额(元)',
					align: 'right',
					field: 'cashAmount',
					formatter: function (value, row, index) {
						if (value == null) {
							return '';
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				},
				{
					title: '未申请金额(元)',
					align: 'right',
					field: 'unApplyAmount',
					formatter: function (value, row, index) {
						if (value == null) {
							return '';
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				}
			]
		};
		$.table.init(options);

		/**
		 * 初始化日期控件
		 */
		// layui.use('laydate', function() {
		// 	var laydate = layui.laydate;
		// 	laydate.render({
		// 		elem: '#reqDeliDateStart',
		// 		type: 'date',
		// 		trigger: 'click'
		// 	});
		// });
		// layui.use('laydate', function() {
		// 	var laydate = layui.laydate;
		// 	laydate.render({
		// 		elem: '#reqDeliDateEnd',
		// 		type: 'date',
		// 		trigger: 'click'
		// 	});
		// });
	});

	function searchx() {
		var data = {};
		//data.salesDept = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
		data['params[vbillstatus]'] = $.common.join($('#vbillstatus').selectpicker('val'));//状态
		$.table.search('role-form', data);
	}
	/**
	 * 合并页脚
	 */
	function merge_footer() {
		var footer_tbody = $('.fixed-table-footer table tbody');
		var footer_tr = footer_tbody.find('>tr');
		var footer_td = footer_tr.find('>td');
		var footer_td_1 = footer_td.eq(0);
		//除了第一列其他都隐藏
		for(var i=1;i<footer_td.length;i++) {
			footer_td.eq(i).hide();
		}
		footer_td_1.attr('colspan', 1).show();
	}

	function getAmountCount() {
		/*var data = $.common.formToJSON("role-form");
		data['params[vbillstatus]'] = $.common.join($('#vbillstatus').selectpicker('val'));//状态
		$.post(prefix + "/getCount", data, function(result){
			if (result.code == 0) {
				var data = result.data;
				$("#sumAmountTotal").text(data.SUMAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
			} else {
				$("#sumAmountTotal").text("ERROR");
			}
		});*/
	}

	function exportExcel() {
		$.modal.confirm("即将导出，是否继续？", function() {
			$.modal.loading("正在导出数据，请稍后...");
			var search = $.common.formToJSON("role-form");
			//search['salesDept'] = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
			search['params[vbillstatus]'] = $.common.join($('#vbillstatus').selectpicker('val'));//状态
			$.post(prefix + "/exportUnapply", search, function(result) {
				if (result.code == web_status.SUCCESS) {
					window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
				} else if (result.code == web_status.WARNING) {
					$.modal.alertWarning(result.msg)
				} else {
					$.modal.alertError(result.msg);
				}
				$.modal.closeLoading();
			});
		});
	}

	/**
	 * 将总计金额清零
	 */
	function clearTotal() {
		//开票金额合计
		sumAmount = 0
	}

	/**
	 * 累计总金额
	 */
	function addTotal(row) {
		sumAmount += row.amount;
	}

	/**
	 *
	 */
	function subTotal(row) {
		sumAmount -= row.amount;
	}

	/**
	 *
	 * 给页脚总计赋值
	 */
	function setTotal() {
		//$("#sumAmount").text(sumAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
	}

</script>
</body>
</html>