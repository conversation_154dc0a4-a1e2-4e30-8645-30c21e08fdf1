<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('收款-开票列表')" />
    <style type="text/css">

        .qd{
            background: #fffcd3;
            padding: 3px 6px;
        }
        .qd_title{
            font-weight: bold;
            line-height: 24px;
        }
        .qd>table>thead>tr>th{
            background: #eff3f9 !important;
            line-height: 24px !important;
            height: 24px;
            padding: 0;
        }
        .qd>table>tbody>tr>td{
            background-color: #fff !important;
            line-height: 24px !important;
            height: 24px;
            padding: 0 !important;
        }
    </style>
</head>
<body class="gray-bg">
     <div class="container-div">
		 <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">
        var prefix = ctx + "finance/receBilling";
        var bala_corp = [[${@dict.getType('bala_corp')}]];//开票公司
        var billing_type = [[${@dict.getType('billing_type')}]];//开票类型
		var receSheetRecordId = [[${receSheetRecordId}]];//申请id


        $(function() {
            var options = {
                url: prefix + "/checkDetailList/"+receSheetRecordId,
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
				exportUrl: prefix + "/export",
                modalName: "收款开票",
		        showExport: false,
                showRefresh:false,
				showSearch:false,
                showColumns:false,
                showToggle:false,
                showFooter:false,
                detailView: true,
                detailFormatter: detailFormatter,
                onExpandRow: function(){
                    // 展开后
                },
                onLoadSuccess:function(){
                    // 加载后
                    $('#bootstrap-table').bootstrapTable('expandAllRows')
                },
                columns: [


				{
					field : 'billingPayable',
					title : '发票抬头',
				},

				{
					field : 'billingAmount',
					title : '开票金额(元)',
					formatter: function (value, row, index) {
						if (value === null) {
							return ;
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				},
				// {
				// 	field : 'receBillingId',
				// 	title : '开票信息id',
				// 	visible: false
				// },
				{
					field : 'billingCorp', 
					title : '开票公司',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(bala_corp, value);
                    }
				},
				{
					field : 'taxIdentify', 
					title : '纳税识别号',
				},
				{
					field : 'billingType',
					title : '开票类型',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(billing_type, value);
                    }
				},
				// {
				// 	field : 'applyUser',
				// 	title : '申请人',
				// },
				// {
				// 	field : 'applyDate',
				// 	title : '申请时间',
				// },
				{
					field : 'billingStatus',
					title : '是否开票',
					formatter: function status(value, row, index) {
						return value==0?'否':'是';
					}
				},
				{
					field : 'billingUser',
					title : '开票人',
				},
				{
					field : 'billingDate',
					title : '开票时间',
				},
				{
					field : 'bank', 
					title : '开户银行',
				},
				{
					field : 'bankAccount', 
					title : '开户账号',
				},
				{
					field : 'addressPhone', 
					title : '地址及电话',
                    formatter: function status(value,row) {
                        return $.table.tooltip(row.addressPhone);
                    }
				},

				{
					field : 'memo', 
					title : '开票备注',
                    formatter: function status(value,row) {
                        return $.table.tooltip(row.memo);
                    }
				},
                {
                    field : 'remark',
                    title : '备注',
                    formatter: function status(value,row) {
                        return $.table.tooltip(row.remark);
                    }
                },

		       ]
            };
            $.table.init(options);

        });

        function detailFormatter(index, row) {
            if (row.billingType == '6') {
                return '<div style="color:red;font-weight:bold;text-align:center;background-color: #ddd;line-height: 24px;">无清单</div>'
            }
            var html = [];
            $.ajax({
                url: prefix + '/receBillingQd',
                data: { receBillingId: row.receBillingId },
                async: false,
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        var list = result.data;
                        if (list.length == 0) {
                            html.push('<div style="color:red;font-weight:bold;text-align:center;background-color: #ddd;line-height: 24px;">无清单</div>')
                        } else {
                            html.push('<div class="qd">',
                                '<div class="qd_title">发票清单 <label style="color:blue">', row.qdPrint==1?'需要单独打印销货清单':'','</label></div>',
                                '<table class="custom-tab tab table table-bordered">',
                                '<thead style="background: #f4f6f7;">',
                                '<tr>',
                                '<th style="width: 20%;">货物或应税劳务名称</th>',
                                '<th style="width: 10%;">规格型号</th>',
                                '<th>单位</th>',
                                '<th>数量</th>',
                                '<th>单价</th>',
                                '<th>金额</th>',
                                '<th>税额</th>',
                                '<th>含税额</th>',
                                '</tr>',
                                '</thead>',
                                '<tbody>')
                            for (let i = 0; i < list.length; i++) {
                                html.push('<tr>',
                                    '<td>', list[i].spmc, '</td>',
                                    '<td>', list[i].ggxh, '</td>',
                                    '<td>', list[i].dw, '</td>',
                                    '<td>', list[i].spsl, '</td>',
                                    '<td>', list[i].dj, '</td>',
                                    '<td>', list[i].je, '</td>',
                                    '<td>', list[i].se, '</td>',
                                    '<td>', list[i].hj, '</td>',
                                    '</tr>')
                            }
                            html.push('</tbody>',
                                '</table>',
                                '</div>');
                        }
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                        html.push('<div style="color:red;font-weight:bold;text-align:center;background-color: #ddd;line-height: 24px;">查询失败</div>')
                    } else {
                        $.modal.alertError(result.msg);
                        html.push('<div style="color:red;font-weight:bold;text-align:center;background-color: #ddd;line-height: 24px;">查询出错</div>')
                    }

                }
            })
            return html.join('');
        }
    </script>
</body>
</html>