<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方费用申请')"/>
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 70px);
        padding-top: 0;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row no-gutter">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--待审核状态-->
                <input type="hidden" name="checkStatus" value="0">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">申请单号：</label>
                            <div class="col-sm-8">
                                <input name="vbillno" placeholder="请输入对账单号" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">

                    </div>
                    <div class="col-md-3 col-sm-6">

                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger multiple disabled" onclick="revocation()" shiro:hasPermission="tms:otherFeeSheetRecord:back">
                <i class="fa fa-file single disabled"></i> 退回
            </a>
            <a class="btn btn-primary single  disabled" onclick="openSheet()" shiro:hasPermission="tms:otherFeeSheetRecord:otherFeeCheckSheet">
                <i class="fa fa-check"></i>查看第三方对账单
            </a>
          <!--  <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="finance:otherFee:export">
                <i class="fa fa-download"></i> 导出
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "tms/otherFeeSheetRecord";

    //权限
    var payFlag = [[${@permission.hasPermi('tms:otherFeeSheetRecord:pay')}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        var options = {
            url: prefix + "/list",
            showToggle:false,
            clickToSelect:true,
            showColumns:true,
            height: 560,
            modalName: "第三方费用",
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'center',
                    field: 'otherFeeSheetRecordId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs ' + payFlag + '" href="javascript:void(0)" title="支付" ' +
                            'onclick="pay(\'' + row.otherFeeSheetRecordId + '\',\''+ row.vbillstatus +'\')"><i class="fa fa-dollar" style="font-size: 15px;"></i></a> ');
                        return actions.join('');
                    }

                },
                {
                    title: '申请单号',
                    align: 'left',
                    field: 'vbillno'
                },
                {
                    title: '单据状态',
                    align: 'left',
                    field: 'vbillstatus',
                    formatter: function status(value,row) {

                        let vbillstatus = ''
                        if (value == 0) {
                            vbillstatus = '<span class="label label-default">业务审核</span>';
                        }
                        if (value == 1) {
                            vbillstatus = '<span class="label label-primary">已申请</span>';
                        }
                        if (value == 2) {
                            vbillstatus = '<span class="label label-success">已支付</span>';
                        };
                        if (value == 3) {
                            vbillstatus = '<span class="label label-danger">财务审核不通过</span>';
                        }

                        let lockOtherFee = ``
                        if (row.lockOtherFee && row.lockOtherFee == 1) {
                            lockOtherFee = `<span class="label label-danger ml5" style="padding:2px;" data-toggle="tooltip"
                                    data-container="body" data-placement="top" data-html="true" title="存在锁定的三方"
                                    data-original-title="">锁</span>`
                        }

                        return vbillstatus + lockOtherFee
                    }
                },
                {
                    title: '申请类型',
                    align: 'left',
                    field: 'applyType',
                    formatter: function (value, row, index) {
                        return value == 0 ? '现金':'油卡'
                    }
                },
                {
                    title: '油卡卡号',
                    align: 'left',
                    field: 'oilcard',
                },
                {
                    title: '收款账户',
                    align: 'left',
                    field: 'recAccount',
                },
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'recBank',
                },
                {
                    title: '收款卡号',
                    align: 'left',
                    field: 'recCardNo',
                },
                {
                    title: '申请金额',
                    align: 'right',
                    field: 'applyPayAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },

                {
                    title: '申请人',
                    align: 'left',
                    field: 'applyPayUser',
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'applyPayDate',
                },
                {
                    title: '付款人',
                    align: 'left',
                    field: 'payUser',
                },
                {
                    title: '付款时间',
                    align: 'left',
                    field: 'payDate',
                },

                {
                    title: '备注',
                    field: 'memo',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.memo);
                    }
                },
            ]
        };

        $.table.init(options);
    });


    /**
     * 跳转第三方对账管理
     */
    function openSheet() {
        var vbillno = $.table.selectColumns("checkVbillno");
        $.modal.openTab("第三方对账", ctx + "business/otherFeeCheckSheet?vbillno=" + vbillno);
    }

    /**
     * 支付
     * @param id
     * @param vbillstatus
     * @returns {boolean}
     */
    function pay(id,vbillstatus) {
        if (vbillstatus != 1) {
            $.modal.alertWarning("请选择“申请”状态的数据！")
            return false;
        }
        var data = {};
        data.otherFeeSheetRecordId = id;

        $.modal.confirm("确定支付吗？", function () {
            $.operate.post(prefix + "/payOtherFeeSheetRecord", data);
        });
    }

    /**
     * 退回
     * @returns {boolean}
     */
    function revocation() {
        var vbillstatus = $.table.selectColumns("vbillstatus");
        for(var i=0 ; i< vbillstatus.length ; i++){
            if(vbillstatus[i] != 1){
                $.modal.alertWarning("只能退回申请状态的账单")
                return false;
            }
        }
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var otherFeeSheetRecordId = $.table.selectColumns("otherFeeSheetRecordId").join();
        var url = prefix+"/back?otherFeeSheetRecordId="+otherFeeSheetRecordId;
        $.modal.open("退回",url,500,300);

    }





</script>

</body>
</html>