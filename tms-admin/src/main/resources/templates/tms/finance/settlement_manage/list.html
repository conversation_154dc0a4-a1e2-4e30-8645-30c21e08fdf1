<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('结算管理')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse,.f7f7{
        background-color: #F7F7F7;
    }

    .table-striped {
        height: calc(100% - 100px);
    }
    .picviewer img{
        width: 32px;
        height: 32px;
        object-fit: scale-down;
    }
    .form-group{
        margin-bottom: 0;
    }
    /*.btn{
        height: 26px;
        line-height: 26px;
        padding: 0px 10px;
    }*/
    .tol{
        width: 8em;
        display: inline-block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        vertical-align: middle;
    }
    .flex{
        display: flex;
        align-items: center;
        /* justify-content: space-between; */
    }
    .tooltip-inner{
        /* background: transparent !important; 
        text-align: left !important;
        color:#363636 !important;
        border:1px solid transparent; */
        max-width: 700px !important;
    }

    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }

</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-1 col-sm-1">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="receiptStatus" id="receiptStatus" class="form-control">
                                    <option value="">-- 回单状态  --</option>
                                    <option value="0" selected>已确认</option>
                                    <option value="1">部分确认</option>
                                    <option value="2">已上传</option>
                                    <option value="3">部分上传</option>
                                    <option value="4">未上传</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-1 col-sm-1">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="settlementCheck" id="settlementCheck" class="form-control">
                                    <option value="">-- 审核状态 --</option>
                                    <option value="0" selected>未审核</option>
                                    <option value="1">已审核</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="isPayDetailApply" id="isPayDetailApply" class="form-control">
                                    <option value="">-- 单笔付款复核 --</option>
                                    <option value="1">待复核</option>
                                </select>
                            </div>
                        </div>
                    </div>
    
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input type="text" style="width: 48%; float: left;" class="form-control"
                                       id="reqDeliDateStart" name="params[reqDeliDateStart]" placeholder="要求提货开始日" readonly>
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:4%;">-</span>
                                <input type="text" style="width: 48%; float: left;" class="form-control"
                                       id="reqDeliDateEnd" name="params[reqDeliDateEnd]" placeholder="要求提货结束日" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="vbillno" id="vbillno" placeholder="发货单号或客户单号" class="form-control valid"
                                       type="text">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="custAbbr" id="custAbbr" placeholder="客户简称" class="form-control valid"
                                       type="text" maxlength="25">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="salesDept" placeholder="运营组" id="salesDept" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运营组" multiple>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row no-gutter mt5">
                    <div class="col-md-5 col-sm-5">
                        <div class="form-group">
                            <div class="col-sm-4">
                                <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                            <div class="col-sm-4">
                                <select name="deliCityId" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4">
                                <select name="deliAreaId" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5 col-sm-5">
                        <div class="form-group">
                           
                            <div class="col-sm-2" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-10" style="padding-right: 0;">
                                <div class="col-sm-4">
                                    <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>
    
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="carrName" id="carrName" placeholder="承运商" class="form-control valid">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row no-gutter mt5">
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="carno" id="carno" placeholder="车号" class="form-control valid">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">

                                <select name="docIfBargain" id="docIfBargain" class="form-control">
                                    <option value="">-- 是否单据议价 --</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>

<!--                                <input name="docIfBargain" id="docIfBargain" placeholder="车号" class="form-control valid">-->
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div id="" style="align-items: baseline;">
                            <input type="checkbox" id="isCloseInvoice"
                                   onchange="changeIsCloseInvoice()"
                                   style="transform: scale(1.2);" />

                            <input name="isCloseInvoice" value="0" type="hidden">
                            <label for="isCloseInvoice" style="font-size: 1.1em; vertical-align: middle;user-select:none;">关闭状态</label>

                        </div>

                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group" style="text-align: center">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
            </form> 
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary multiple disabled" onclick="check()" shiro:hasPermission="tms:finance:settlement_manage:check">
                审核确认
            </a>
            <a class="btn btn-danger multiple disabled" onclick="reverse()" shiro:hasPermission="tms:finance:settlement_manage:check">
                反确认
            </a>

            <a class="btn btn-warning" onclick="exportExcel()" shiro:hasPermission="tms:finance:settlement_manage:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>

<script th:inline="javascript">
    var prefix = ctx + "settlement_manage";

    var traceStatus = [[${@dict.getType('trace_status')}]];

    //付款方式
    var payMethod = [[${@dict.getType('pay_method')}]];
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];

    //合计
    var receivableAmount = 0;//总应收
    var payAmount = 0;//总应付
    var otherAmount = 0;//总三方
    var ptf = 0        //平台费
    var yfsf = 0        //应付税金
    var dsfsf = 0        //第三方税金
    var profit = 0        //利润

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre()
            }
        });

        $("#reqDeliDateStart").val(getFrontFormatDate(-1));

        var  columns = []
        columns.push(...[
            {
                title: '',
                align: 'left',
                field: 'entrustCount',
                width: 20,
                formatter: function status(value, row, index) {
                    return '<span class="label label-info pa2">'+value+'</span>';
                }
            },{
                checkbox: true,
                footerFormatter: function (row) {
                    return "总应收:<nobr id='receivableAmount'>￥0</nobr>&nbsp&nbsp"
                        + "总应付:<nobr id='payAmount'>￥0</nobr>&nbsp&nbsp"
                        + "总三方:<nobr id='otherAmount'>￥0</nobr>&nbsp&nbsp"
                        + "平台费:<nobr id='ptf'>￥0</nobr>&nbsp&nbsp"
                        + "应付税金:<nobr id='yfsf'>￥0</nobr>&nbsp&nbsp"
                        + "第三方税金:<nobr id='dsfsf'>￥0</nobr>&nbsp&nbsp"
                        + "利润:<nobr id='profit'>￥0</nobr>&nbsp&nbsp"
                        ;
                }
            },
            {
                title: '客户信息',
                align: 'left',
                field : 'custAbbr',
                width: 10,
                formatter: function status(value, row, index) {
                    let html = $.table.tooltip(value,8);
                    html += "<div class='mt5'>";
                    if(row.settlementCheck==1){
                        html+= "<span class='carve carve-primary'>已审核</span>"
                    }else{
                        html+= "<span class='carve carve-danger'>未审核</span>"
                    }
                    if(row.ifBargain==0){
                        html+= `<span class='carve carve-primary' style="margin-left: 1px" onclick="jumpContract('`+row.customerId+`')">合同价</span>`;
                    }else{
                        let bargainMemo = row.bargainMemo == null ? '' : row.bargainMemo;
                        let ifBargain = row.customerIfBargain == 1 ? '客户议价' : '单据议价'

                        html+= `<span class='carve carve-danger' data-toggle="tooltip" title="${bargainMemo}" style="margin-left: 1px;cursor: pointer;">${ifBargain}</span>`;
                    }
                    html += "</div>"

                    return html;
                }
            },
            {
                title: '发货单号/客户单号',
                align: 'left',
                field : 'vbillno',
                width: 10,
                formatter: function status(value, row, index) {

                    let noHtml = `<span>${value}</span>`
                    if ([[${@permission.hasPermi('tms:invoice:detail_all')}]] != "hidden") {
                        noHtml = `<a onclick="invocieDetail('${row.invoiceId}')">${value}</a>`

                    }

                    let collHtml = '';
                    if(row.collectAmount != null && row.collectAmount != "") {
                        let title = ''
                        if (row.invoiceBalaType === '2') {
                            title = `现金到付（全部司机代收），`
                        }else if (row.invoiceBalaType === '5') {
                            title = `到付（现金给公司），`
                        }else if (row.invoiceBalaType === '6') {
                            title = `到付+回单（部分司机代收），`
                        }

                        collHtml += ' <span class="label label-success" style="padding:1px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+title+'代收金额：'+row.collectAmount+'元">代</span>';
                    }



                    let memo=""
                    if(row.memo){
                        memo = '<span style="padding:1px;vertical-align: middle;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+row.memo+'"> <i class="fa fa-exclamation-circle"/> </span>'
                    }


                    let custOrderno= "";
                    if(row.custOrderno){
                        custOrderno= row.custOrderno;
                    }else{
                        custOrderno="-";
                    }
                    return noHtml + memo + collHtml + "<br/>" + $.table.tooltip(custOrderno, 14);
                }
            },
            // {
            //     title: '要求提到货日',
            //     align: 'left',
            //     field : 'reqDeliDate',
            //     width: 10,
            //     formatter: function status(value, row, index) {
            //         let html=[];
            //         if(row.reqDeliDate){
            //             html.push('<span class="label label-warning pa2">提</span>'+row.reqDeliDate.substring(0, 10));
            //         }else{
            //             html.push('<span class="label label-warning pa2">提</span>-');
            //         }
            //         if(row.reqArriDate){
            //             html.push('<span class="label label-success pa2">到</span>'+row.reqArriDate.substring(0, 10));
            //         }else{
            //             html.push('<span class="label label-success pa2">到</span> -');
            //         }
            //         return html.join("<br/>")
            //     }
            // },
            // {
            //     title: '装卸货地址',
            //     align: 'left',
            //     field : 'arriProName',
            //     width: 10,
            //     formatter: function status(value, row, index) {
            //         let deli="",arri="";
            //         if(row.deliProName){
            //             deli+=row.deliProName;
            //         }
            //         if(row.deliCityName&&row.deliCityName != '市辖区'){
            //             deli+=row.deliCityName;
            //         }
            //         if(row.deliAreaName){
            //             deli+=row.deliAreaName;
            //         }

            //         if(row.arriProName){
            //             arri+=row.arriProName;
            //         }
            //         if(row.arriCityName&&row.arriCityName != '市辖区'){
            //             arri+=row.arriCityName;
            //         }
            //         if(row.arriAreaName){
            //             arri+=row.arriAreaName;
            //         }
            //         return `<span class="label label-warning pa2">提</span>`+$.table.tooltip(deli,6)+`<br/><span class="label label-success pa2">到</span>`+$.table.tooltip(arri,6);
            //     }
            // },
            {
                title: '要求提到货信息',
                align: 'left',
                field : 'deliProName',
                width: 10,
                formatter: function status(value, row, index) {
                    let deli="",arri="";
                    if(row.deliProName){
                        deli+=row.deliProName;
                    }
                    if(row.deliCityName&&row.deliCityName != '市辖区'){
                        deli+=row.deliCityName;
                    }
                    if(row.deliAreaName){
                        deli+=row.deliAreaName;
                    }

                    if(row.arriProName){
                        arri+=row.arriProName;
                    }
                    if(row.arriCityName&&row.arriCityName != '市辖区'){
                        arri+=row.arriCityName;
                    }
                    if(row.arriAreaName){
                        arri+=row.arriAreaName;
                    }
                    let html=[];
                    if(row.reqDeliDate){
                        html.push('<span class="label label-warning pa2">提</span>'+row.reqDeliDate.substring(0, 10)+'<div class="ml5" style="display: inline-block;vertical-align: middle;">'+$.table.tooltip(deli,6)+'</div>');
                    }else{
                        html.push('<span class="label label-warning pa2">提</span>-'+'<div class="ml5" style="display: inline-block;vertical-align: middle;">'+$.table.tooltip(deli,6)+'</div>');
                    }
                    if(row.reqArriDate){
                        html.push('<span class="label label-success pa2">到</span>'+row.reqArriDate.substring(0, 10)+'<div class="ml5" style="display: inline-block;vertical-align: middle;">'+$.table.tooltip(arri,6)+'</div>');
                    }else{
                        html.push('<span class="label label-success pa2">到</span> -'+'<div class="ml5" style="display: inline-block;vertical-align: middle;">'+$.table.tooltip(arri,6)+'</div>');
                    }
                    return html.join("<br/>")
                }
            },
            {
                title: '货品信息',
                align: 'left',
                field : 'goodsName',
                width: 10,
                formatter: function status(value, row, index) {
                    let html = [];
                    if(row.numCountAdjust||row.weightCountAdjust||row.volumeCountAdjust){
                        if(row.numCountAdjust){
                            html.push(row.numCountAdjust+"件")
                        }
                        if(row.weightCountAdjust){
                            html.push(row.weightCountAdjust+"吨")
                        }
                        if(row.volumeCountAdjust){
                            html.push(row.volumeCountAdjust+"m³")
                        }
                    }else{
                        if(row.numCount){
                            html.push(row.numCount+"件")
                        }
                        if(row.weightCount){
                            html.push(row.weightCount+"吨")
                        }
                        if(row.volumeCount){
                            html.push(row.volumeCount+"m³")
                        }
                    }

                    return $.table.tooltip(value,9)+"<br/>"+$.table.tooltip(html.join("/"),9);
                }
            },
            {
                title: '要求车长车型',
                align: 'left',
                field : 'carLenName',
                formatter: function status(value, row, index) {
                    return value+"米"+row.carTypeName;
                }
            },
            {
                title: '回单状态',
                align: 'left',
                field : 'receiptStatus',
                width: 10,
                formatter: function status(value, row, index) {
                    let html=[]
                    switch (value) {
                        case 0:
                            html.push( '<span class="carve carve-success">已确认</span>' );
                            break;
                        case 1:
                            html.push( '<span class="carve carve-coral">部分确认</span>' );
                            break;
                        case 2:
                            html.push( '<span class="carve carve-primary">已上传</span>' );
                            break;
                        case 3:
                            html.push( '<span class="carve carve-warning">部分上传</span>' );
                            break;
                        case 4:
                            html.push( '<span class="carve carve-danger">未上传</span>' );
                            break;
                        default:
                            break;
                    }

                    return html.join("<br/>")
                }
            },
            {
                title: '回单照片',
                align: 'left',
                field : 'receiptUploadFiles',
                formatter: function status(value, row, index) {
                    const maxImages = 2;
                    let count = 0;

                    var html = "<div class='picviewer' style='display: flex;align-items: center;'>"
                    if(value != null && value != '') {
                        html += `<div>`

                        value.forEach(function (element, index) {
                            if (count < maxImages) {
                                html += `<img style="display: inline-block;margin-right: 10px;" src="`+element.filePath+`"/>`
                            }
                            count++;
                        });

                        html += `</div>`

                        if (count > maxImages) {
                            let filePaths = value.map(item => item.filePath);
                            // filePaths = filePaths.slice(maxImages);
                            let pathsString = encodeURIComponent(JSON.stringify(filePaths));

                            let more = value.length - maxImages
                            // 如果超过最大图片数量，拼接“更多”按钮
                            html += `<div style="flex-shrink: 0;"><a onclick="openReceiptUploadFiles('${pathsString}')">更多(${more}张)</a></div>`;
                        }
                    }else {
                        html = '-';
                    }
                    html +="</div>"
                    return html;
                }
            },
            {
                title: '合同/系统价',
                align: 'left',
                field : 'receivableAmountFreight',
                formatter: function status(value, row, index) {
                    let html=[];
                    if(row.ifBargain==0) {
                        if (row.contractAmount) {
                            html.push(`<span>合同价：` + row.contractAmount.toLocaleString('zh', {
                                style: 'currency',
                                currency: 'CNY'
                            }) + `</span>`)
                        } else {
                            html.push("-");
                        }
                        if (row.systemAmount) {
                            if(!row.contractAmount || row.systemAmount < row.contractAmount){
                                html.push(`<span class='text-danger'>系统价：` + row.systemAmount.toLocaleString('zh', {
                                    style: 'currency',
                                    currency: 'CNY'
                                }) + `</span>`)
                            }else if(row.systemAmount > row.contractAmount){
                                html.push(`<span class='text-navy mr10'>系统价：` + row.systemAmount.toLocaleString('zh', {
                                    style: 'currency',
                                    currency: 'CNY'
                                }) + `</span>`)
                            }else{
                                html.push(`<span>系统价：` + row.systemAmount.toLocaleString('zh', {
                                    style: 'currency',
                                    currency: 'CNY'
                                }) + `</span>`)
                            }
                        } else {
                            html.push("-");
                        }
                    }
                    return html.join("<br/>")
                }
            },
            {
                title: '计价方式',
                align: 'left',
                field : 'billingMethod'
            },
            {
                title: '应收费用',
                align: 'left',
                field : 'receivableAmountFreight',
                formatter: function status(value, row, index) {
                    let html=[];
                    let isAdjust="";
                    if(row.isAdjust == 1){
                        isAdjust="<span class='label label-success pa2 ml5'>调</span>"
                    }
                    if(row.receivableAmountFreight){
                        //html.push(`<span class='text-navy mr10' data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`+getDailList(row.receiveDetailList,'0')+`">应收`+row.receivableAmountFreight.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`+isAdjust)
                        html.push(`<span class='text-navy mr10' data-toggle="tooltip" flag="recvDetailList" fee-type="0" invoice-id="${row.invoiceId}" data-delay="150" data-container="body" data-placement="top" data-html="true" title="加载中...">应收`+row.receivableAmountFreight.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`+isAdjust)
                    }
                    if(row.receivableAmountOnTheWay){
                        //html.push(`<span class='text-warning mr10' data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`+getDailList(row.receiveDetailList,'1')+`">在途`+row.receivableAmountOnTheWay.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`)
                        html.push(`<span class='text-warning mr10' data-toggle="tooltip" flag="recvDetailList" fee-type="1" invoice-id="${row.invoiceId}" data-delay="150" data-container="body" data-placement="top" data-html="true" title="加载中...">在途`+row.receivableAmountOnTheWay.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`)
                    }
                    return html.join("<br/>")
                }
            },

        ])

        if ([[${@permission.hasPermi('tms:finance:settlement_manage:list_profit')}]] != "hidden") {
            columns.push(...[
                {
                    title: '应付费用',
                    align: 'left',
                    field : 'payAmountFreight',
                    formatter: function status(value, row, index) {
                        let payAmount=[];
                        if(row.payAmountFreight){
                            //payAmount.push(`<div><span class="label label-info" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`+getDailList(row.payFreightList)+`">应付`+row.payAmountFreight.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span></div>`)
                            payAmount.push(`<div><span class="label label-info" data-toggle="tooltip" flag="payDetailList" fee-type="0" invoice-id="${row.invoiceId}" data-delay="150" data-container="body" data-placement="top" data-html="true" title="加载中...">应付`+row.payAmountFreight.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span></div>`)
                        }
                        if(row.payAmountOnTheWay){
                            //payAmount.push(`<div class="mt5"><span class="label label-info" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`+getDailList(row.payOnTheWayList)+`">在途`+row.payAmountOnTheWay.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span></div>`)
                            payAmount.push(`<div class="mt5"><span class="label label-info" data-toggle="tooltip" flag="payDetailList" fee-type="1" invoice-id="${row.invoiceId}" data-delay="150" data-container="body" data-placement="top" data-html="true" title="加载中...">在途`+row.payAmountOnTheWay.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span></div>`)
                        }
                        // if (row.netInfo.yfsf) {
                        //     payAmount.push(`<div class="mt5"><span class="label label-warning">税金`+row.netInfo.yfsf.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span></div>`)
                        // }

                        let html=`<div class="flex">`;
                        html+= `<div>`+payAmount.join("")+`</div>`;

                        if(row.isPayDetailApply==1){
                            if(row.entrustCount>1){
                                html+=`<img class="ml5" style="width: 22px;height: 22px;object-fit: scale-down;" src="/img/dbfh.png" alt="" onclick="onExpandRow(\``+index+`\`)">`
                            }else{
                                html+=`<img class="ml5" style="width: 22px;height: 22px;object-fit: scale-down;" src="/img/dbfh.png" alt="" onclick="onPreDbfh(\``+row.invoiceId+`\`)">`
                            }

                        }

                        html+=`</div>`
                        return html;
                    }
                },
                {
                    title: '三方费用',
                    align: 'left',
                    field : 'otherAmount',
                    formatter: function status(value, row, index) {
                        let html=[];
                        if(row.otherAmount){
                            //html.push(`<span data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`+getOtherFeeList(row.otherFeeList)+`">`+row.otherAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`)
                            html.push(`<span data-toggle="tooltip" data-container="body" flag="otherFeeList" invoice-id="${row.invoiceId}" data-delay="150" data-placement="top" data-html="true" title="加载中...">`+row.otherAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`)
                        }else {
                            return "-"

                        }
                        return html.join()
                    }
                },
                {
                    title: '平台费',
                    align: 'left',
                    field : 'ptf',
                    formatter: function status(value, row, index) {
                        let html=[];
                        if(row.ptf){
                            //html.push(`<span data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`+getOtherFeeList(row.otherFeeList)+`">`+row.otherAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`)
                            html.push(`<span>`+row.ptf.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`)
                        }else {
                            return "-"

                        }
                        return html.join()
                    }
                },
                {
                    title: '应付税金',
                    align: 'left',
                    field : 'yfsf',
                    formatter: function status(value, row, index) {
                        let total = Number(value);
                        return total.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '三方税金',
                    align: 'left',
                    field : 'dsfsf',
                    formatter: function status(value, row, index) {
                        let total = Number(value);
                        return total.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '利润',
                    align: 'left',
                    field : 'profit',
                    formatter: function status(value, row, index) {
                        let total = Number(value);
                        return total.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '利润率',
                    align: 'left',
                    field : '',
                    formatter: function status(value, row, index) {
                        if(!row.receivableAmount){
                            return "-"
                        }
                        var percentage = (row.profit/row.receivableAmount)*100;

                        if(percentage<0){
                            return "<span class='text-danger'>"+percentage.toFixed(2) +"%</span>";
                        }if(percentage<15){
                            return "<span class='text-warning'>"+percentage.toFixed(2) +"%</span>";
                        }else{
                            return percentage.toFixed(2)+"%"
                        }
                    }
                },
                // {
                //     title: '未税利润率',
                //     align: 'left',
                //     field : 'receivableAmountOnTheWay',
                //     formatter: function status(value, row, index) {
                //         if(row.payAmountFreight =='' && row.payAmountOnTheWay =='' && row.otherAmount ==''){
                //             return "-"
                //         }
                //
                //         let receivableAmount = Number(row.netInfo.ys);
                //         let payAmount = Number(row.netInfo.yf) + Number(row.netInfo.dsf) + Number(row.netInfo.ptf);
                //         let sum = receivableAmount-payAmount;
                //
                //
                //         if(!receivableAmount){
                //             return "<span class='text-danger'>-100%</span>";
                //         }
                //         var percentage = (sum/receivableAmount)*100;
                //
                //         if(percentage<0){
                //             return "<span class='text-danger'>"+percentage.toFixed(2) +"%</span>";
                //         }if(percentage<15){
                //             return "<span class='text-warning'>"+percentage.toFixed(2) +"%</span>";
                //         }else{
                //             return percentage.toFixed(2)+"%"
                //         }
                //     }
                // },
                // {
                //     title: '未税总成本',
                //     align: 'left',
                //     field : 'netInfo.yf',
                //     formatter: function status(value, row, index) {
                //         let total = Number(value) + Number(row.netInfo.dsf) + Number(row.netInfo.ptf);
                //         return total.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                //
                // },
                // {
                //     title: '未税利润',
                //     align: 'left',
                //     field : 'payAmountOnTheWay',
                //     formatter: function status(value, row, index) {
                //         /*if(row.payAmountFreight =='' && row.payAmountOnTheWay =='' && row.otherAmount ==''){
                //             return "-"
                //         }
                //
                //         let receivableAmount = Number(row.receivableAmountFreight)+Number(row.receivableAmountOnTheWay);
                //         let payAmount = Number(row.payAmountFreight)+Number(row.payAmountOnTheWay);
                //         let sum = receivableAmount-payAmount-Number(row.otherAmount);*/
                //
                //         let sum = Number(row.netInfo.ys) - Number(row.netInfo.yf) - Number(row.netInfo.dsf) - Number(row.netInfo.ptf);
                //
                //         if(sum<0){
                //             return "<span class='text-danger'>"+sum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+"</span>";
                //         }else{
                //             return sum.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //         }
                //     }
                // },
            ]);
        }

        columns.push(...[


            {
                title: '运营组',
                align: 'left',
                field : 'salesDeptName'
            },
            /*{
                title: '承运商',
                field: 'carrName'
            },
            {
                title: '车号',
                field: 'carno'
            },*/
            {
                title: '运输方式',
                align: 'left',
                field : 'carLenName',
                formatter: function (value, row, index) {
                    let list=[]
                    if(row.isOversize&&row.isOversize==1){
                        list.push("<span class='label label-warning pa2'>大件运输</span>")
                    }
                    if(row.unloadPlaceNum&&row.unloadPlaceNum>1){
                        list.push("<span class='label label-success pa2'>"+row.unloadPlaceNum+"卸</span>")
                    }

                    return "<div class='flex' style='justify-content: space-between;'><div>"+row.transName+"</div><div>"+list.join("<br/>")+"</div></div>";
                }
            },
            {
                title: '对账状态',
                field: 'receCheckStatus',
                formatter: function (value, row, index) {
                    if (value == 0) {
                        return '未对账'
                    } else if (value == 1) {
                        return '部分对账'
                    } else if (value == 2) {
                        return '已对账'
                    }
                }
            },
            {
                title: '应收对账单',
                align: 'left',
                field : 'receCheckSheetNo',
                formatter: function status(value, row, index) {
                    if (value) {
                        let html = value.split(',');
                        return html.join("<br/>")
                    }
                }
            },]);

        var options = {
            url: prefix + "/list",
            // showSearch:false,
            // showRefresh:false,
            showToggle:false,
            showColumns:false,
            clickToSelect:true,
            modalName: "结算管理",
            height: 560,
            uniqueId: "invoiceId",
            showFooter: true,
            detailView: true,
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onExpandRow : function(index, row, $detail) {
                initEntrustProductTable(index, row, $detail);
            },
            onPostBody: function (rows) {
                $('[flag="payDetailList"],[flag="recvDetailList"],[flag="otherFeeList"]').on('show.bs.tooltip', function () {
                    if ($(this).attr('data-original-title') == "加载中...") {
                        var that = this;
                        let type = $(this).attr('fee-type')
                        let invoiceId = $(this).attr('invoice-id')
                        let flag = $(this).attr("flag");
                        $.ajax({
                            url: prefix + '/commonFeeList?kind='+flag+'&invoiceId=' + invoiceId + "&type=" + type,
                            cache: false,
                            success: function (res) {
                                if (res.code == 0) {
                                    var html = [];
                                    if (flag == 'recvDetailList') {
                                        html.push(getDailList(res.data, type))
                                    } else if (flag == 'payDetailList') {
                                        html.push(getDailList(res.data))
                                    } else if (flag == 'otherFeeList') {
                                        html.push(getOtherFeeList(res.data))
                                    }
                                    $(that).attr('title', html.join('')).tooltip('fixTitle').tooltip('show');
                                } else {
                                    $(that).attr('title', res.msg).tooltip('fixTitle').tooltip('show');
                                }
                            },
                            error: function(res) {
                                $(that).attr('title', res.responseText).tooltip('fixTitle').tooltip('show');
                            }
                        })
                    }
                })
            },
            columns: columns,
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }
        };


        $.table.init(options);

        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
         /**
         * 初始化日期控件
         */
         layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqDeliDateStart',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#reqDeliDateEnd',
                type: 'date',
                trigger: 'click'
            });
        });

        $('[data-toggle="tooltip"]').tooltip();
       
        $(document).on('mouseleave','[data-toggle="tooltip"]', function(){
            $(this).tooltip('hide');
            $(".tooltip.fade.top.in").remove();
        });
    });

    
    initEntrustProductTable = function(index, rows, $detail) {
        var childTable = $detail.html('<div class="table-responsive"><table id="child-table" class="table text-nowrap"></table></div>').find('table');
        $(childTable).bootstrapTable({
            url: prefix + "/entrust/list?invoiceId="+rows.invoiceId,
            method: 'post',
            sidePagination: "server",
            pagination: false,
            pageNumber: 1,
            pageSize: 10,
            pageList: [],
            contentType: "application/x-www-form-urlencoded",
        
            columns: [
                {
                    title: '运单号/运段类型',
                    align: 'left',
                    field : 'lot',
                    width: 200,
                    formatter: function status(value, row, index) {
                        let ltlType=""
                        switch (row.ltlType) {
                            case 0: 
                                ltlType= '<span class="carve carve-warning ml5">提货段</span>';
                                break;
                            case 1: 
                                ltlType= '<span class="carve carve-coral ml5">干线段</span>';
                                break;
                            case 2: 
                                ltlType= '<span class="carve carve-success ml5">送货段</span>';
                                break;
                            default: 
                                break;
                        }
                        let detail='<a href="javascript:void(0)" onclick="detail(\'' + row.entrustId + '\',\''+ row.isFleetData +'\')">'+value+'</a>';
                        

                        return detail+ltlType;
                    }
                },  
                {
                    title: '单据状态',
                    align: 'left',
                    field : 'vbillstatus',
                    width: 100,
                    formatter: function status(value, row, index) {
                        switch (value - 0) {
                            case 0:
                                return '<span class="carve carve-primary">待确认</label>';
                            case 1:
                                return '<span class="carve carve-warning">已确认</label>';
                            case 2:
                                return '<span class="carve carve-info">已提货</label>';
                            case 3:
                                return '<span class="carve carve-success">已到货 </label>';
                            case 4:
                                return '<span class="carve carve-success">已回单</span>';
                            case 5:
                                return '<span class="carve carve-inverse">关闭</label>';
                            case 6:
                                return '<span class="carve carve-warning">分配车队</label>';
                            default:
                                break;
                        }
                    }
                },
                 {
                    title: '装卸货地址',
                    align: 'left',
                    field : 'arriProName',
                    width: 200,
                    formatter: function status(value, row, index) {
                        let deli="",arri="";
                        if(row.deliProName){
                            deli+=row.deliProName;
                        }
                        if(row.deliCityName&&row.deliCityName != '市辖区'){
                            deli+=row.deliCityName;
                        }
                        if(row.deliAreaName){
                            deli+=row.deliAreaName;
                        }

                        if(row.arriProName){
                            arri+=row.arriProName;
                        }
                        if(row.arriCityName&&row.arriCityName != '市辖区'){
                            arri+=row.arriCityName;
                        }
                        if(row.arriAreaName){
                            arri+=row.arriAreaName;
                        }

                        return `<span class="label label-warning pa2">装</span>`+deli+`<br/><span class="label label-success pa2">卸</span>`+arri;
                    }
                },  
                {
                    title: '货量',
                    align: 'left',
                    field : 'goodsName',
                    width: 150,
                    formatter: function status(value, row, index) {
                        let html = [];       
                        if(row.numCount){
                            html.push(row.numCount+"件") 
                        }
                        if(row.weightCount){
                            html.push(row.weightCount+"吨") 
                        }
                        if(row.volumeCount){
                            html.push(row.volumeCount+"m³") 
                        }     
                        return value+"<br/>"+html.join("/");
                    }
                },
                {
                    title: '回单照片',
                    align: 'left',
                    field : 'receiptUploadFiles',
                    width: 200,
                    formatter: function status(value, row, index) {
                        const maxImages = 2;
                        let count = 0;

                        var html = "<div class='picviewer' style='display: flex;align-items: center;'>"
                        if(value != null && value != '') {
                            html += `<div>`

                            value.forEach(function (element, index) {
                                if (count < maxImages) {
                                    html += `<img style="display: inline-block;margin-right: 10px;" src="`+element.filePath+`"/>`
                                }
                                count++;
                            });

                            html += `</div>`

                            if (count > maxImages) {
                                let filePaths = value.map(item => item.filePath);
                                // filePaths = filePaths.slice(maxImages);
                                let pathsString = encodeURIComponent(JSON.stringify(filePaths));

                                let more = value.length - maxImages
                                // 如果超过最大图片数量，拼接“更多”按钮
                                html += `<div style="flex-shrink: 0;"><a onclick="openReceiptUploadFiles('${pathsString}')">更多(${more}张)</a></div>`;
                            }
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                },

                {
                    title: '承运商',
                    align: 'left',
                    field : 'carrName',
                    width: 100
                },  
                {
                    title: '车号',
                    align: 'left',
                    field : 'carno',
                    width: 100,
                    formatter: function status(value, row, index) {
                        let car = ''
                        if (row.carLen) {
                            car = row.carLen+"米"
                        }
                        if (row.carTypeName) {
                            car = car + row.carTypeName
                        }
                        return value + "<br>" + car;
                    }
                }, 
                {
                    title: '费用明细',
                    align: 'left',
                    field : 'numCount',
                    width: 150,
                    formatter: function status(value, row, index) {
                        let payAmount=[];
                        if(row.payAmountFreight){
                            payAmount.push("<span class='text-navy mr10'>运费</span>"+row.payAmountFreight.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if(row.payAmountOnTheWay){
                            payAmount.push("<span class='text-warning mr10'>在途</span>"+row.payAmountOnTheWay.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        // return html.join("<br/>")

                        let html=`<div class="flex">`
                            html+= `<div>`+payAmount.join("<br/>")+`</div>`

                        if(row.isPayDetailApply==1) {
                            html += `<img class="ml5" style="width: 22px;height: 22px;object-fit: scale-down;" src="/img/dbfh.png" alt="" onclick="onDbfh(\`` + row.lotId + `\`)">`
                        }
                            html+=`</div>`
                        return html;
                    }
                },
                {
                    title: '参考公里数',
                    align: 'left',
                    field : 'actualmileage'
                },
            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }
        });
    };


    function changeIsCloseInvoice() {
        var isCloseInvoice = $("#isCloseInvoice").is(':checked');
        $('input[name="isCloseInvoice"]').val(isCloseInvoice ? '1' : '0');

        searchPre()
    }


    function getFrontFormatDate(data) {
        var date = new Date();
        date.setMonth(date.getMonth()+data);
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() + 1 ;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate;


        return currentdate;
    }

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        searchPre();
    }

     /**
     * 搜索的方法
     */
     function searchPre() {
        var data = {};
        data.salesDept = $.common.join($('#salesDept').selectpicker('val'));

        var tmpEndDate = getFrontFormatDate(0);
        if ($("#reqDeliDateEnd").val().trim()!= '') {
            tmpEndDate = $("#reqDeliDateEnd").val().trim();
        }
        var ymd1 = $("#reqDeliDateStart").val().trim().split("-");
        var ymd2 = tmpEndDate.split("-");

         if ($("#reqDeliDateStart").val() == '' && $("#reqDeliDateEnd").val() == '') {
             $.modal.msgWarning("请输入检索日期");
             return;
         }
        if ((ymd2[0] - ymd1[0]) * 12 + (ymd2[1] - ymd1[1]) + (ymd2[2] - ymd1[2]) / 31 > 12) {
            $.modal.msgWarning("要求提货日期跨度不能超过一年");
            return;
        }

        $.table.search('role-form', data);
    }
    /**
     * 重置
     */
    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $("#role-form")[0].reset();
        $("#salesDept").selectpicker('refresh');
        searchPre();
    }
    
    function check() {
        var invoiceIds =  $.table.selectColumns("invoiceId");
        if (invoiceIds.length == 0) {
            $.modal.alertWarning("请选择确认的数据");
            return;
        }

        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["settlementCheck"] == 1) {
                $.modal.alertWarning("发货单状态为未审核才能进行审核确认");
                return;
            }
        }

        $.modal.confirm("确定确认审核该发货单吗？", function () {
            $.operate.post(prefix + "/check", { 'invoiceIds': invoiceIds.join(),'settlementCheck':1 });
        });
    }
    function reverse() {
        var invoiceIds =  $.table.selectColumns("invoiceId");
        if (invoiceIds.length == 0) {
            $.modal.alertWarning("请选择确认的数据");
            return;
        }

        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["settlementCheck"] == 0) {
                $.modal.alertWarning("发货单状态为已审核才能进行反确认");
                return;
            }
        }

        $.modal.confirm("确定反确认审核该发货单吗？", function () {
            $.operate.post(prefix + "/check", { 'invoiceIds': invoiceIds.join(),'settlementCheck':0 });
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        receivableAmount = 0;//总应收
        payAmount = 0;//总应付
        otherAmount = 0;//总三方
        ptf = 0;//平台费
        yfsf = 0//应付税金
        dsfsf = 0//第三方税金
        profit = 0 //利润
    }

    /**
     * 累计金额
     */
    function addTotal(row) {
        receivableAmount += Number(row.receivableAmountFreight)+Number(row.receivableAmountOnTheWay); 
        payAmount +=Number(row.payAmountFreight)+Number(row.payAmountOnTheWay); 
        otherAmount +=Number(row.otherAmount);
        ptf +=Number(row.ptf);
        yfsf +=Number(row.yfsf);
        dsfsf +=Number(row.dsfsf);
        profit +=Number(row.profit);
    }

    function subTotal(row) {
        receivableAmount -= Number(row.receivableAmountFreight)+Number(row.receivableAmountOnTheWay); 
        payAmount -=Number(row.payAmountFreight)+Number(row.payAmountOnTheWay); 
        otherAmount -=Number(row.otherAmount);
        ptf -=Number(row.ptf);
        yfsf -=Number(row.yfsf);
        dsfsf -=Number(row.dsfsf);
        profit -=Number(row.profit);
    }

    /**
     *
     * 给页脚总计赋值
     */
     function setTotal() {
        $("#receivableAmount").text(receivableAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#payAmount").text(payAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#otherAmount").text(otherAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#ptf").text(ptf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#yfsf").text(yfsf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#dsfsf").text(dsfsf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#profit").text(profit.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }
    /**
     * 导出
     */
    function exportExcel() {
        $.modal.confirm("确定导出所有结算数据吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            
            var search = $.common.formToJSON("role-form");
            search.salesDept = $.common.join($('#salesDept').selectpicker('val'));
            search.params = new Map();
            $.post(prefix + "/export", search, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });

    }

  /**
     * 跳转委托单详情页
     */
     function detail(entrustId,isFleetData) {
        var url = ctx + "trustDeed/detail?entrustId="+entrustId + "&isFleetIn=" + isFleetData;
        $.modal.openTab($.table._option.modalName + "详细", url);
    }
    function onPreDbfh(invoiceId) {
         $.ajax({
             url: prefix + "/getInvoiceSingleLotId?invoiceId=" + invoiceId,
             cache: false,
             success: function (res) {
                 if (res.code == 0) {
                     onDbfh(res.data)
                 } else {
                     $.modal.alertError(res.msg);
                 }
             }
         })
    }
    function onDbfh(lotId) {
        var type = 'review';
        $.modal.openTab("应付明细", ctx + "payDetailCheck/checkByLotId?lotId="+lotId+"&type="+type);
    }

    function getDailList(list,freeType) {
        let html=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'>`;
            
        if(list){
            html+=`<table class='custom-tab tab table'> <thead style='background: #f4f6f7;'><tr>
                    <th>费用类型</th>
                    <th>金额</th>
                    <th>状态</th>
                </tr></thead><tbody> `;
                    list.forEach(res=>{
                        
                        if(freeType){
                            if(res.freeType!=freeType){
                                return false;
                            }
                        }

                        let vbillstatus="";
                        if(res.vbillstatus==0){
                            vbillstatus= `<span class='carve carve-inverse'>新建</span>` ;
                        }else if(res.vbillstatus==1){
                            vbillstatus = `<span class='carve carve-primary'>已确认</span>` ;
                        }else if(res.vbillstatus==2){
                            vbillstatus = `<span class='carve carve-success'>已对账</span>` ;
                        }else if(res.vbillstatus==3){
                            vbillstatus = `<span class='carve carve-coral'>部分核销</span>` ;
                        }else if(res.vbillstatus==4){
                            vbillstatus = `<span class='carve carve-warning'>已核销</span>` ;
                        }else if(res.vbillstatus==5){
                            vbillstatus = `<span class='carve carve-danger'>关闭</span>` ;
                        }else if(res.vbillstatus==6){
                            vbillstatus = `<span class='carve carve-primary'>已申请</span>` ;
                        }else if(res.vbillstatus==7){
                            vbillstatus = `<span class='carve carve-warning'>核销中</span>` ;
                        }else if(res.vbillstatus==8){
                            vbillstatus = `<span class='carve carve-yellow'>审核中</span>` ;
                        }
                        
                        let costType="";
                        if(res.freeType=='1'){
                            costType = $.table.selectDictLabel(costTypeOnWay, res.costTypeOnWay);
                        }else if(res.freeType=='0'){
                            costType = $.table.selectDictLabel(costTypeFreight, res.costTypeFreight);
                        }
                        
                        html+=`<tr>
                            <td>`+(costType?costType:'-')+`</td>
                            <td>`+res.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</td>
                            <td>`+vbillstatus+`</td>
                        </tr>`
                    })

                    
            html+=`</tbody></table>`;
            
        }else{
            html+=`暂无数据`; 
        }
        html+=`</div></div></div>`;
        return html
    }
    function getOtherFeeList(list) {
        let html=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'>`;
            
            if(list){
                html+=`<table class='custom-tab tab table'> <thead style='background: #f4f6f7;'><tr>
                        <th>费用类型</th>
                        <th>付款类型/付款方式</th>
                        <th>金额</th>
                    </tr></thead><tbody> `;
                        list.forEach(res=>{
                            
                            let payType=res.payType==1 ?'油卡':'现金';
          
                            
                            html+=`<tr>
                                <td>`+$.table.selectDictLabel(costTypeOnWay, res.feeType)+`</td>
                                <td>`+payType+`/`+$.table.selectDictLabel(payMethod, res.payMethod)+`</td>
                                <td>`+res.feeAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</td>
                            </tr>`
                        })
    
                        
                html+=`</tbody></table>`;
                
            }else{
                html+=`暂无数据`; 
            }
            html+=`</div></div></div>`;
            return html
    }

    function onExpandRow(index) {
        $.btTable.bootstrapTable('expandRow', index)
    }

    function jumpContract(customerId){
        var url = ctx + "client/contract_n?id="+customerId;
        $.modal.openTab("合同信息", url);
    }

    function openReceiptUploadFiles(filePaths) {
        let decodedPaths = JSON.parse(decodeURIComponent(filePaths));

        // 创建一个隐藏的图片容器，用于预览所有图片
        let imageContainer = $('<div style="display: none;"></div>');

        // 将所有图片添加到容器中
        decodedPaths.forEach(path => {
            let img = new Image();
            img.src = path;

            // 将图片添加到隐藏的容器中
            imageContainer.append(img);
        });

        // 使用 viewer-jquery 预览所有图片
        imageContainer.viewer({
            // 可以根据需要设置更多的配置选项
            // 例如：inline: false（默认为 true，表示以内联方式显示预览）
            inline: false,
            viewed() {
                // 设置预览初始缩放级别等
                // 这里可以添加自定义的预览逻辑
            }
        });
        $('body').append(imageContainer);

        // 触发预览，可以通过模拟点击的方式或其他方式来打开预览
        let firstImage = imageContainer.find('img').eq(0);
        if (firstImage.length > 0) {
            firstImage.click(); // 模拟点击第一张图片以触发 viewer-jquery 预览
        } else {
            console.error('No images found in the list.');
        }


        // $('#vvv').viewer({
        //     url: 'data-original',
        //     title: false
        // });
        // layer.open({
        //     type: 2,
        //     area: ['70%', '90%'],
        //     fix: false,
        //     maxmin: true,
        //     shade: 0.3,
        //     title: "附件详情",
        //     content: ctx + "payDetail/receiptFileDetail/" + tid,
        //     btn: ['关闭'],
        //     shadeClose: true,            // 弹层外区域关闭
        //     cancel: function(index) {
        //         return true;
        //     }
        // });
    }


    /**
     * 详情
     */
    function invocieDetail(invoiceId) {
        var url = ctx + "invoice/detail_all/" + invoiceId;
        $.modal.openTab('单据详情',url);
    }


</script>

</body>
</html>