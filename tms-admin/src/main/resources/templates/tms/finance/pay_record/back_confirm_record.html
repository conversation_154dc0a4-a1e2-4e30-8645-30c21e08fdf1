<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('核销汇总记录')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
</head>
<style>
    .row-margin-top {
        margin-top: 10px;
    }
    .picviewer img{
        width: 32px;
        height: 32px;
        object-fit: scale-down;
    }
    .select-table .table td {
        /* 超出部分隐藏 */
        overflow:hidden;
        /* 超出部分显示省略号 */
        text-overflow:ellipsis;
        /*规定段落中的文本不进行换行 */
        white-space:nowrap;
        /* 配合宽度来使用 */
        height:40px;
    }
</style>
<body class="gray-bg">

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="park-form">
                    <input type="hidden" id="oldAccountId" name="oldAccountId">

                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input name="vbillno" id="vbillno" class="form-control valid" aria-invalid="false"  aria-required="true" placeholder="单号"/>
                                </div>
                            </div>
                        </div>


                        <div class="col-sm-2">
                            <!-- <label class="col-sm-6"></label> -->
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">

            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: ztree-js" />
<script th:inline="javascript">
    var feeRegistrationFlag = [[${@permission.hasPermi('tms:finance:feeRegistration:list')}]];
    var recordDetailFlag = [[${@permission.hasPermi('finance:payRecord:recordDetail')}]];
    var payMethod = [[${@dict.getType('pay_method')}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    var prefix = ctx + "finance/payRecord";
    var accountPrefix = ctx + "finance/account"

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#starttime").removeAttr("lay-key");
        $("#endtime").removeAttr("lay-key");
        laydate.render({
            elem: '#endtime'
            , type: 'date'
        });

        laydate.render({
            elem: '#starttime'
            , type: 'date'
        });
        laydate.render({
            elem: '#payStartTime'
            , type: 'date'
        });

        laydate.render({
            elem: '#payEndTime'
            , type: 'date'
        });
    })

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 185 });

        queryStorageCapacityList();
        $.table.hideColumn("id");

        getAllAccount();
    });

    //初始化查询条件传参
    queryParams = function(params) {
        var search = {};
        $.each($("#park-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;


        return search;
    }
    function queryStorageCapacityList() {
        var options = {
            url: prefix + "/backConfirmRecordList",
            showToggle:false,
            showColumns:false,
            showSearch:false,
            showExport:false,
            modalName: "反核销汇总记录",
            fixedColumns: true,
            showFooter:true,
            fixedNumber:0,
            height: "auto",
            clickToSelect: true,
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"核销汇总记录"
            },
            queryParams: queryParams,
            columns: [
                {
                    title: '主键',
                    align: 'left',
                    field : 'id'  //主键加载完成就隐藏  多选删除的时候是按照第一列的值是否相同来判断一共有几项的
                },

                {
                    title: '单号',
                    align: 'left',
                    field : 'vbillno'
                },
                {
                    title: '反核销人',
                    align: 'left',
                    field : 'backPerson'
                },
                {
                    title: '反核销时间',
                    align: 'left',
                    field : 'regDate'
                },
                {
                    title: '反核销原因',
                    align: 'left',
                    field : 'payBackMemo'
                },
                {
                    title: '附件',
                    align: 'left',
                    field : 'sysUploadFiles',
                    formatter: function status(value, row, index) {
                        var html = "<div class='picviewer'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html += `<img src="`+element.filePath+`"/>`
                            });
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                }
            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }
        };
        $.table.init(options);
    }

</script>
</body>
</html>