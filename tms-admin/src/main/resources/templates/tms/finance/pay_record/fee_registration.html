<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('核销-费用登记')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
</head>
<style>
    .row-margin-top {
        margin-top: 10px;
    }
    .select-table .table td {
        /* 超出部分隐藏 */
        overflow:hidden;
        /* 超出部分显示省略号 */
        text-overflow:ellipsis;
        /*规定段落中的文本不进行换行 */
        white-space:nowrap;
        /* 配合宽度来使用 */
        height:40px;
    }
</style>
<body class="gray-bg">

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12">
                <form id="park-form">
                    <input type="hidden" id="oldAccountId" name="oldAccountId" th:value="${oldAccountId}">
                    <input type="hidden" id="payMethod" name="payMethod" th:value="${payMethod}">
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.addTab()" shiro:hasPermission="tms:finance:feeRegistration:add">-->
<!--                    <i class="fa fa-plus"></i> 新增-->
<!--                </a>-->
                <a class="btn btn-success" onclick="add()" shiro:hasPermission="tms:finance:feeRegistration:add">
                    <i class="fa fa-plus"></i> 费用登记
                </a>
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="tms:finance:feeRegistration:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
<!--                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:finance:storage:export">-->
<!--                    <i class="fa fa-download"></i> 导出-->
<!--                </a>-->
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: ztree-js" />
<script th:inline="javascript">
    var removeFlag = [[${@permission.hasPermi('tms:finance:feeRegistration:remove')}]];

    var prefix = ctx + "finance/payRecord";
    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#starttime").removeAttr("lay-key");
        $("#endtime").removeAttr("lay-key");
        laydate.render({
            elem: '#endtime'
            , type: 'date'
        });

        laydate.render({
            elem: '#starttime'
            , type: 'date'
        });
        laydate.render({
            elem: '#payStartTime'
            , type: 'date'
        });

        laydate.render({
            elem: '#payEndTime'
            , type: 'date'
        });
    })

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 185 });

        queryStorageCapacityList();
        $.table.hideColumn("registrationId");
    });

    function add() {
        var oldAccountId = $("#oldAccountId").val();
        var payMethod = $("#payMethod").val();
        var url = prefix + "/add?accountId=" + oldAccountId + "&payMethod=" + payMethod;
        layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "费用登记",
            area: ['500px', '420px'],
            content: url,
            shadeClose: true,
            btn:['确定', '关闭'],
            yes: function (index, layero) {
                $(layero).find("iframe")[0].contentWindow.submitHandler(0);
            },
            cancel: function(index) {
                parent.layer.close(index);
            }
        });
    }


    //初始化查询条件传参
    queryParams = function(params) {
        var search = {};
        $.each($("#park-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }
    function queryStorageCapacityList() {
        var options = {
            url: prefix + "/feeRegistration",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:false,
            sortName: "regDate",
            sortOrder: "desc",
            modalName: "费用登记",
            fixedColumns: true,
            showFooter:true,
            fixedNumber:5,
            showSearch:false,
            showExport:false,
            height: "auto",
            clickToSelect: true,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"费用登记列表"
            },
            queryParams: queryParams,
            columns: [
                {
                    title: '主键',
                    align: 'left',
                    field : 'registrationId'  //主键加载完成就隐藏  多选删除的时候是按照第一列的值是否相同来判断一共有几项的
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:finance:feeRegistration:remove')}]] != "hidden") {
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" title="删除" onclick="$.operate.remove(\'' + row.registrationId + '\')"><i class="fa fa-remove"></i></a> ');
                        }
                        return actions.join('');
                    }
                },
                // {
                //     title: '核销账户id',
                //     align: 'left',
                //     field : 'accountId'
                // },
                {
                    title: '核销账户',
                    align: 'left',
                    field : 'accountName'
                },
                {
                    title: '费用登记金额（元）',
                    align: 'left',
                    field: 'registrationAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY', minimumFractionDigits: 3});
                    }
                },
                {
                    title: '备注',
                    align: 'left',
                    field : 'remark'
                },
                {
                    title: '创建人',
                    align: 'left',
                    field : 'regUserId'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field : 'regDate'
                },
            ]
        };
        $.table.init(options);
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }




</script>
</body>
</html>