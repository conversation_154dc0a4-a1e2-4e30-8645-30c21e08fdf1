<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('费用登记')" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 80px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .disin{
        display: inline-block;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .infotitle{
        line-height: 30px;
        font-weight: bold;
    }
    .titlebg{
        background: #eff3f9;
        padding: 5px 10px;

    }
    a.del-alink {
        display: block;
        margin: 120px auto 0;
    }
    .bggray{
        background: #f7f8fa;
    }
    .dropdown-menu {
        overflow: auto;
        height: 120px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-book-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" id="oldAccountId" name="oldAccountId" th:value="${oldAccountId}">
        <input type="hidden" id="payMethod" name="payMethod" th:value="${payMethod}">
        <!--要求提货日期-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="over">
                            <div class="fr" style="width: calc(100% - 50px)">
                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 账户：</label>
                                                <div class="flex_right">
                                                    <select name="accountId" id="accountId" class="form-control valid" aria-invalid="false"  aria-required="true" required>
                                                        <option value="">-- 请选择账户 --</option>
                                                    </select>
                                                    <!--                                        <input name="invoicing" id="invoicing" placeholder="开票..." class="form-control" type="text" maxlength="50" autocomplete="off">-->
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 金额：</label>
                                                <div class="flex_right">
                                                    <input name="registrationAmount" id="registrationAmount" placeholder="请输入登记金额..." class="form-control" type="text"
                                                           maxlength="50" autocomplete="off" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff"></span> 备注：</label>
                                                <div class="flex_right">
                                                    <textarea id="remark" name="remark" class="form-control" type="text" placeholder="请填写备注"
                                                              maxlength="250"  aria-required="false"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- <div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
       <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>
    </div>
</div> -->

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-typeahead-js"/>

<script th:inline="javascript">

    var prefix = ctx + "finance/payRecord";

    var accountPrefix = ctx + "finance/account"

    $(function () {
        getAllAccount();
    });

    function getAllAccount() {
        var oldAccountId = $("#oldAccountId").val();
        $.ajax({
            type: "POST",
            url: accountPrefix + "/listNoPage",
            async: false,
            success: function(res){
                var options = ""
                $.each(res, function(i, field) {
                    var accountId = res[i].accountId;
                    var accountName = res[i].accountName;
                    if(accountId == oldAccountId) {
                        var option = "<option value=\'" + accountId + "\' selected>" + accountName + "</option>"
                    }else {
                        var option = "<option value=\'" + accountId + "\'>" + accountName + "</option>"
                    }
                    options = options + option
                });
                $("#accountId").append(options)
            }
        });
    }


    //数字 保留两位小数
    function onlyNumberThreeDecimal (obj) {
        //得到第一个字符是否为负号
        var t = obj.value.charAt(0);
        //先把非数字的都替换掉，除了数字和.
        obj.value = obj.value.replace(/[^\d\.]/g, '');
        //必须保证第一个为数字而不是.
        obj.value = obj.value.replace(/^\./g, '');
        //保证只有出现一个.而没有多个.
        obj.value = obj.value.replace(/\.{2,}/g, '.');
        //保证.只出现一次，而不能出现两次以上
        obj.value = obj.value.replace('.', '$#$').replace(/\./g, '').replace(
            '$#$', '.');
        obj.value = obj.value.slice(0, 15);
        // 只能输入两个小数
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d).*$/,'$1$2.$3');
        //如果第一位是负号，则允许添加
        if (t == '-') {
            obj.value = '-' + obj.value;
        }
    }

    function removeRowThree(obj, index) {
        $(obj).parent().parent().remove()
        //删除之后  改变后面元素的index
    }


    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        laydate.render({
            elem: '#billDate'
            , type: 'datetime'

        });
    })

    /**
     * 校验
     */
    $("#form-book-add").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            reqDeliDate:{
                required:true,
            },
            reqArriDate:{
                required:true,
            },
            custName:{
                required:true,
            },
            balaName:{
                required:true,
            },
            appDeliMobile:{
                isPhone:true
            }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            //commit();
            $.operate.saveModalAndRefush(prefix + "/addFeeRegistration", $('#form-book-add').serialize());
            //jQuery.subscribe("cmt", commit);
        }
    }

    function commit() {
        console.log(JSON.stringify($('#form-book-add').serializeArray()))
        console.log("===========")
        console.log($('#form-book-add').serializeArray())
        $.ajax({
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            url: prefix + "/addParkWayBill",
            data: JSON.stringify($('#form-book-add').serializeArray()),//JSON.stringify(data.field),
            dataType: 'json',
            success: function(result) {
                console.log(result)
                // if(result.code == 0) {
                //     layer.msg('注册成功！');
                // } else {
                //     layer.msg(result.msg);
                // }
            }
        });
    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }

</script>
</body>

</html>