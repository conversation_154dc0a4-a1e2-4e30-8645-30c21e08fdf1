<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方支付')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .table-striped .bootstrap-table {
        height: 95% !important;
    }
    .flex{
        display: flex;
    }
    .flex_left{
        width: 80px;
        line-height: 26px;
        color: #808080;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*border-left: 1px #979797 solid;*/
        box-sizing: border-box;
        /*line-height: 26px;*/
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-4 col-sm-4">
                        <div class="flex">
                            <label class="flex_left">付款时间：</label>
                            <div class="flex_right">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="regDate" placeholder="开始时间">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endDate"  name="corDate" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    <!--<div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            &lt;!&ndash;                            <label class="col-sm-4">结算公司：</label>&ndash;&gt;
                            <div class="col-sm-12">
                                <select name="balaCorp" id="balaCorp" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="结算公司" multiple th:with="type=${@dict.getType('bala_corp')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>-->
                    <!--<div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            &lt;!&ndash;                            <label class="col-sm-4">结算方式：</label>&ndash;&gt;
                            <div class="col-sm-12">
                                <select name="settlementMethod" id="payMethod" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="结算方式" multiple th:with="type=${@dict.getType('pay_method')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>-->
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">单据号：</label>-->
                            <div class="col-sm-12">
                                <input name="vbillno" placeholder="请输入单据号" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-3">
                        <div class="form-group">
                            <!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
                <div class="row">

                    <!--<div class="col-md-3 col-sm-3">
                        <div class="form-group">
                            &lt;!&ndash;                            <label class="col-sm-4">收款账户：</label>&ndash;&gt;
                            <div class="col-sm-12">
                                <input name="recAccount" placeholder="请输入收款账户" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-3">
                        <div class="form-group">
                            &lt;!&ndash;                            <label class="col-sm-4">收款卡号：</label>&ndash;&gt;
                            <div class="col-sm-12">
                                <input name="recCardNo" placeholder="请输入收款卡号" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-3">
                        <div class="form-group">
                            &lt;!&ndash;                            <label class="col-sm-4">转出账户：</label>&ndash;&gt;
                            <div class="col-sm-12">
                                <input name="outAccountName" placeholder="请输入转出账户" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>-->

                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group" th:if="${outAccount == 'd72d567363544ea099f6ac6b33a87bda'}">
            <a class="btn btn-danger multiple disabled" onclick="oppositePayOff()" shiro:hasAnyPermissions="finance:record:oppositePayOff">
                <i class="fa fa-exclamation"></i> 反核销
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "finance/payRecord";
    var pay_method = [[${@dict.getType('pay_method')}]];//付款方式
    var bala_corp = [[${@dict.getType('bala_corp')}]];//结算公司
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    //昨日日期
    var day1 = new Date();
    day1.setTime(day1.getTime()-24*60*60*1000);
    var d1Month = ("0" + (day1.getMonth() + 1)).slice(-2);
    var d1Day = ("0" + (day1.getDate())).slice(-2);
    var yesterday = day1.getFullYear()+"-" + d1Month + "-" + d1Day;
    //今日日期
    var day2 = new Date();
    day2.setTime(day2.getTime());
    var d2Month = ("0" + (day2.getMonth() + 1)).slice(-2);
    var d2Day = ("0" + (day2.getDate())).slice(-2);
    var today = day2.getFullYear()+"-" + d2Month + "-" + d2Day;

    var outAccount = [[${outAccount}]];
    var payAmountCount = 0;
    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        //默认日期
        $("#startDate").val(yesterday);
        $("#endDate").val(today);


        var options = {
            url: prefix + "/listByCondition?outAccount="+outAccount,
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            fixedColumns: true,
            fixedNumber: 0,
            modalName: "付款记录",
            clickToSelect:true,
            showFooter:true,
            columns: [{
                checkbox: true,
            },
                /*{
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('finance:payRecord:salesDeptDetail')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="openSalesDeptDetail(\'' + row.vbillno + '\')"><i  class="fa fa-newspaper-o" style="font-size: 15px;" ></i></a>');
                        }
                        return actions.join('');
                    }
                },*/
                {
                    title: '单据号',
                    align: 'left',
                    field: 'vbillno'
                },
                {
                    title: '结算公司',
                    field: 'outAccount',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(bala_corp, value);
                    }
                },
                {
                    title: '付款金额',
                    align: 'right',
                    field: 'payAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '付款人',
                    align: 'left',
                    field: 'payMan',
                },
                {
                    title: '付款时间',
                    align: 'left',
                    field: 'payDate',
                },
                /*{
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == 0){
                            return '<span>运费</label>';
                        }
                        //在途费用类型
                        var costWay = row.costTypeOnWay;
                        // 异常费用
                        var flag2 = costWay == 18 || costWay == 17 || costWay == 14 || costWay == 13 ||
                            costWay == 12 || costWay == 10 || costWay == 9 || costWay == 8 || costWay == 6 ||
                            costWay == 5 || costWay == 3 || costWay == 21;
                        //在途登记费用
                        var flag1 = costWay == 16 || costWay == 15 || costWay == 7 || costWay == 4 ||
                            costWay == 2 || costWay == 1 || costWay == 22 || costWay == 20 ;
                        if(value == 1 && flag1){
                            return '<span>在途费用</label>';
                        }
                        if(value == 1 && flag2){
                            return '<span>异常费用</label>';
                        }
                        if(value == 2){
                            return '<span>调整费用</label>';
                        }
                    }

                },
                {
                    title: '结算方式',
                    align: 'left',
                    field: 'balaType',
                    formatter: function status(value) {
                        if(value == 1 ){
                            return "单笔";
                        }else if(value == 2){
                            return "月结";
                        }
                    }
                },*/
                {
                    title: '是否有票',
                    align: 'left',
                    field: 'ifHasBill',
                    formatter: function(value){
                        if(value == 1){
                            return "是";
                        }
                        return "否";
                    }
                },
                {
                    title: '收款账户',
                    align: 'left',
                    field: 'recAccount',
                },
                {
                    title: '收款卡号',
                    align: 'left',
                    field: 'recCardNo',

                },
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'recBank',
                },
                {
                    title: '结算方式',
                    field: 'payMethod',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(pay_method, value);
                    }
                },
                {
                    title: '转出账户',
                    align: 'left',
                    field: 'outAccountName',
                },
                /*{
                    title: '创建人',
                    align: 'left',
                    field: 'params.regUserName',
                },*/
                {
                    title: '付款备注',
                    align: 'left',
                    field: 'memo',
                },
            ]
        };

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endDate',
                type: 'date',
                trigger: 'click'
            });
        });

        $.table.init(options);
    });

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        //data.settlementMethod = $.common.join($('#payMethod').selectpicker('val'));//结算方式
        //data.balaCorp = $.common.join($('#balaCorp').selectpicker('val'))//结算公司

        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    function oppositePayOff(){
        var payDetailIds = $.table.selectColumns("payDetailId");
        if (payDetailIds.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("反核销会生成红冲付款数据，并将应付状态改为'已确认'，确认要反核销该付款记录吗？", function () {
            $.operate.post(prefix + "/oppositePayOff", {"payDetailIds":payDetailIds.join()});
        });

    }

    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 650;
    /**
     * 查看运营组 分组的资金
     */
    function openSalesDeptDetail(vbillno) {
        var url = prefix + "/salesDeptDetail?vbillno=" + vbillno;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "明细信息",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    //导出
    function exprot(){
        var formId = "role-form";
        var url = prefix + "/exportSalesDept"
        $.modal.confirm("确定导出所有" + $.table._option.modalName + "吗？", function() {
            var currentId = $.common.isEmpty(formId) ? $('form').attr('id') : formId;
            $.modal.loading("正在导出数据，请稍后...");
            $.post(url, $("#" + currentId).serializeArray(), function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });

    }

    //导出
    function exprot2(){
        var formId = "role-form";
        var url = prefix + "/exportCustomerSalesDept"
        $.modal.confirm("确定导出所有" + $.table._option.modalName + "吗？", function() {
            var currentId = $.common.isEmpty(formId) ? $('form').attr('id') : formId;
            $.modal.loading("正在导出数据，请稍后...");
            $.post(url, $("#" + currentId).serializeArray(), function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });

    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.settlementMethod = $.common.join($('#payMethod').selectpicker('val'));
        data.balaCorp = $.common.join($('#balaCorp').selectpicker('val'))//结算公司
        $.ajax({
            url: prefix + "/getPayRecordCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    console.log(data);
                    $("#sumPayAmountCountTotal").text(data.PAYAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

</script>
</body>
</html>