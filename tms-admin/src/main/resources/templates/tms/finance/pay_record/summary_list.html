<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('核销汇总记录')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
</head>
<style>
    .row-margin-top {
        margin-top: 10px;
    }

    .select-table .table td {
        /* 超出部分隐藏 */
        overflow:hidden;
        /* 超出部分显示省略号 */
        text-overflow:ellipsis;
        /*规定段落中的文本不进行换行 */
        white-space:nowrap;
        /* 配合宽度来使用 */
        height:40px;
    }
</style>
<body class="gray-bg">

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="park-form">
                    <input type="hidden" id="oldAccountId" name="oldAccountId">

                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="accountId" id="accountId" class="form-control valid" aria-invalid="false"  aria-required="true" required>
                                        <option value="">-- 请选择账户 --</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <div class="col-sm-8">
                                    <select name="balaCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}">
                                        <option value="">-- 请选择结算公司 --</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <!-- <label class="col-sm-6"></label> -->
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="finance:payRecord:summary:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: ztree-js" />
<script th:inline="javascript">
    var feeRegistrationFlag = [[${@permission.hasPermi('tms:finance:feeRegistration:list')}]];
    var recordDetailFlag = [[${@permission.hasPermi('finance:payRecord:recordDetail')}]];
    var payMethod = [[${@dict.getType('pay_method')}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    var prefix = ctx + "finance/payRecord";
    var accountPrefix = ctx + "finance/account"

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#starttime").removeAttr("lay-key");
        $("#endtime").removeAttr("lay-key");
        laydate.render({
            elem: '#endtime'
            , type: 'date'
        });

        laydate.render({
            elem: '#starttime'
            , type: 'date'
        });
        laydate.render({
            elem: '#payStartTime'
            , type: 'date'
        });

        laydate.render({
            elem: '#payEndTime'
            , type: 'date'
        });
    })

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 185 });

        queryStorageCapacityList();
        $.table.hideColumn("id");

        getAllAccount();
    });

    function getAllAccount() {
        /*var oldAccountId = $("#oldAccountId").val();
        console.log(oldAccountId)*/
        $.ajax({
            type: "POST",
            url: accountPrefix + "/listNoPage",
            async: false,
            success: function(res){
                console.log(res)
                var options = ""
                $.each(res, function(i, field) {
                    var accountId = res[i].accountId;
                    var accountName = res[i].accountName;
                    var option = "<option value=\'" + accountId + "\'>" + accountName + "</option>"
                    /*var accountId = res[i].accountId;
                    var accountName = res[i].accountName;
                    if(accountId == oldAccountId) {
                        var option = "<option value=\'" + accountId + "\' selected>" + accountName + "</option>"
                    }else {
                        var option = "<option value=\'" + accountId + "\'>" + accountName + "</option>"
                    }*/
                    options = options + option
                });
                $("#accountId").append(options)
            }
        });
    }

    /**
     * 收入核销记录
     * @param tariffId
     */
    function feeRegistration(accountId, payMethod) {
        var url = prefix + "/toFeeRegistration?accountId="+accountId + "&payMethod=" + payMethod;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "费用登记",
            area: ['70%', '100%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 明细
     * @param payRecordId
     */
    function recordDetail(accountId) {
        var url = prefix + "/recordDetail/"+accountId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "明细",
            area: ['70%', '100%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    //初始化查询条件传参
    queryParams = function(params) {
        var search = {};
        $.each($("#park-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }
    function queryStorageCapacityList() {
        var options = {
            url: prefix + "/summary",
            //createUrl: prefix + "/add",
            //updateUrl: prefix + "/edit/{id}",
            //removeUrl: prefix + "/remove",
            exportUrl: prefix + "/summary_export",
            //importUrl: prefix + "/importData",
            //importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:false,
            showSearch:false,
            showExport:false,
            sortName: "accountName",
            sortOrder: "desc",
            modalName: "核销汇总记录",
            fixedColumns: true,
            showFooter:true,
            fixedNumber:0,
            height: "auto",
            clickToSelect: true,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"核销汇总记录"
            },
            queryParams: queryParams,
            columns: [
                {
                    title: '主键',
                    align: 'left',
                    field : 'id'  //主键加载完成就隐藏  多选删除的时候是按照第一列的值是否相同来判断一共有几项的
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:finance:feeRegistration:list')}]] != "hidden") {
                            actions.push('<a class="btn btn-warning btn-xs ' + feeRegistrationFlag + '" href="javascript:void(0)" title="费用登记" onclick="feeRegistration(\'' + row.accountId + "\', \'" + row.payMethod  + '\')"><i class="fa fa-sign-in"></i></a> ');
                        }
                        if ([[${@permission.hasPermi('finance:payRecord:recordDetail')}]] != "hidden") {
                            actions.push('<a class="btn btn-warning btn-xs ' + recordDetailFlag + '" href="javascript:void(0)" title="明细" onclick="recordDetail(\'' + row.accountId + '\')"><i class="fa fa-newspaper-o"></i></a> ');
                        }
                        return actions.join('');
                    }
                },
                {
                    title: '付款方式',
                    align: 'left',
                    field : 'payMethod',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(payMethod, value);
                    }
                },
                {
                    title: '账户名称',
                    align: 'left',
                    field : 'accountName'
                },
                {
                    title: '结算公司',
                    align: 'left',
                    field : 'balaCorp',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(balaCorp, value);
                    }
                },
                {
                    title: '总金额（元）',
                    align: 'left',
                    field: 'amount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY', minimumFractionDigits: 3});
                    }
                },
            ]
        };
        $.table.init(options);
    }

</script>
</body>
</html>