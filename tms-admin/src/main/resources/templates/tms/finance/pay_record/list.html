<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('付款记录')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse,.f7f7{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }

    .table-striped {
        height: calc(100% - 70px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-4 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">付款时间：</label>
                            <div class="col-sm-8">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="regDate" placeholder="开始时间">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endDate"  name="corDate" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">结算公司：</label>-->
                            <div class="col-sm-12">
                                <select name="balaCorp" id="balaCorp" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="结算公司" multiple th:with="type=${@dict.getType('bala_corp')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">付款类型：</label>-->
                            <div class="col-sm-12">
                                <select name="payType" id="payType" data-none-selected-text="付款类型" class="form-control selectpicker">
                                    <option></option>
                                    <option th:value="0">对账付款</option>
                                    <option th:value="1">直接付款</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">结算方式：</label>-->
                            <div class="col-sm-12">
                                <select name="settlementMethod" id="payMethod" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="结算方式" multiple th:with="type=${@dict.getTypeAll('pay_method')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">承运商：</label>-->
                            <div class="col-sm-12">
                                <input name="params[carrName]" placeholder="请输入承运商" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>


                </div>
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">单据号：</label>-->
                            <div class="col-sm-12">
                                <input name="vbillno" placeholder="请输入单据号" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">收款账户：</label>-->
                            <div class="col-sm-12">
                                <input name="recAccount" placeholder="请输入收款账户" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">收款卡号：</label>-->
                            <div class="col-sm-12">
                                <input name="recCardNo" placeholder="请输入收款卡号" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">转出账户：</label>-->
                            <div class="col-sm-12">
                                <input name="outAccountName" placeholder="请输入转出账户" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">创建人：</label>-->
                            <div class="col-sm-12">
                                <input name="regUserId" placeholder="请输入创建人" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">

             <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="finance:payRecord:export">
                 <i class="fa fa-download"></i> 导出
             </a>
            <a class="btn btn-warning" onclick="exprot()" shiro:hasPermission="finance:payRecord:export">
                <i class="fa fa-download"></i> 运营组导出
            </a>

            <a class="btn btn-warning" onclick="exprot2()" shiro:hasPermission="finance:payRecord:export">
                <i class="fa fa-download"></i> 按客户运营组导出
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "finance/payRecord";
    var pay_method = [[${@dict.getTypeAll('pay_method')}]];//付款方式
    var bala_corp = [[${@dict.getType('bala_corp')}]];//结算公司
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    //昨日日期
    var day1 = new Date();
    day1.setTime(day1.getTime()-24*60*60*1000);
    var d1Month = ("0" + (day1.getMonth() + 1)).slice(-2);
    var d1Day = ("0" + (day1.getDate())).slice(-2);
    var yesterday = day1.getFullYear()+"-" + d1Month + "-" + d1Day;
    //今日日期
    var day2 = new Date();
    day2.setTime(day2.getTime());
    var d2Month = ("0" + (day2.getMonth() + 1)).slice(-2);
    var d2Day = ("0" + (day2.getDate())).slice(-2);
    var today = day2.getFullYear()+"-" + d2Month + "-" + d2Day;
    var payAmountCount = 0;
    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        //默认日期
        $("#startDate").val(yesterday);
        $("#endDate").val(today);
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            fixedColumns: true,
            fixedNumber: 3,
            modalName: "付款记录",
            clickToSelect:true,
            showFooter:true,
            onPostBody:function () {
                //合并页脚
                merge_footer();
                //查询合计总金额
                getAmountCount();
            },
            onCheck: function (row,$element) {
                var payAmount = row.payAmount;
                //总数减去本行数值
                payAmountCount = payAmountCount + payAmount;

                //$("#payAmountCountTotal").text(payAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheck: function (row, $element) {
                var payAmount = row.payAmount;
                //总数减去本行数值
                payAmountCount = payAmountCount - payAmount;


                $("#payAmountCountTotal").text(payAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onCheckAll: function (rowsAfter) {
                //总数清0
                payAmountCount = 0;
                //循环累加
                for (var row of rowsAfter) {
                    payAmountCount = payAmountCount + row.payAmount;
                }
                //赋值
                $("#payAmountCountTotal").text(payAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheckAll: function () {
                //总数清0
                payAmountCount = 0;
                $("#payAmountCountTotal").text(payAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onRefresh:function(params){
                //总数清0
                payAmountCount = 0;
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "付款金额：<nobr id='payAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "总合计:付款金额：<nobr id='sumPayAmountCountTotal'>￥0</nobr>&nbsp&nbsp";
                }
            },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('finance:payRecord:salesDeptDetail')}]] != "hidden") {
                            if (row.dataType == 1) {
                                actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="openSalesDeptDetail(\'' + row.vbillno + '\')"><i  class="fa fa-newspaper-o" style="font-size: 15px;" ></i></a>');
                            }
                        }
                        return actions.join('');
                    }
                },
                {
                    title: '单据号',
                    align: 'left',
                    field: 'vbillno'
                },
                {
                    title: '结算公司',
                    field: 'outAccount',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(bala_corp, value);
                    }
                },
                {
                    title: '付款金额',
                    align: 'right',
                    field: 'payAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '付款人',
                    align: 'left',
                    field: 'payMan',
                },
                {
                    title: '付款时间',
                    align: 'left',
                    field: 'payDate',
                },
                {
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == 0){
                            return '<span>运费</label>';
                        } else if (value == '3') {
                            return '<span>定金</label>';
                        }
                        //在途费用类型
                        var costWay = row.costTypeOnWay;
                        // 异常费用
                        var flag2 = costWay == 18 || costWay == 17 || costWay == 14 || costWay == 13 ||
                            costWay == 12 || costWay == 10 || costWay == 9 || costWay == 8 || costWay == 6 ||
                            costWay == 5 || costWay == 3 || costWay == 21 || costWay == 27;
                        //在途登记费用
                        var flag1 = costWay == 16 || costWay == 15 || costWay == 7 || costWay == 4 ||
                            costWay == 2 || costWay == 1 || costWay == 22 || costWay == 20 ;
                        if(value == 1 && flag1){
                            return '<span>在途费用</label>';
                        }
                        if(value == 1 && flag2){
                            return '<span>异常费用</label>';
                        }
                        if(value == 2){
                            return '<span>调整费用</label>';
                        }
                    }

                },
                {
                    title: '付款类型',
                    field: 'costTypeOnWay',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if (row.freeType === '0'){
                            return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                        }else if (row.freeType === '2')  {
                            return '调整费'
                        }else if(row.freeType === '1'){
                            return $.table.selectDictLabel(costTypeOnWay, value);
                        }else if(row.freeType === '3'){
                            return '定金';
                        }
                    }

                },
                {
                    title: '承运商创建人',
                    align: 'left',
                    field: 'carrRegUserName',
                },
                {
                    title: '承运商编码/名称/身份证',
                    align: 'left',
                    field: 'carrCode',
                    formatter: function status(value, row, index) {
                        return value + '&nbsp;&nbsp;'+row.carrName+'<br />' + getValue(row.legalCard)
                    }
                },
                // {
                //     title: '承运商',
                //     align: 'left',
                //     field: 'carrName',
                // },
                // {
                //     title: '承运商身份证',
                //     align: 'left',
                //     field: 'legalCard',
                // },
                {
                    title: '结算方式',
                    align: 'left',
                    field: 'balaType',
                    formatter: function status(value) {
                        if(value == 1 ){
                            return "单笔";
                        }else if(value == 2){
                            return "月结";
                        }
                    }
                },

                {
                    title: '是否有票',
                    align: 'left',
                    field: 'ifHasBill',
                    formatter: function(value){
                        if(value == 1){
                            return "是";
                        }
                        return "否";
                    }
                },

                {
                    title: '收款账户/卡号/银行',
                    align: 'left',
                    field: 'recAccount',
                    formatter: function (value, row, index) {
                        return getValue(value) + '<br />'+getValue(row.recCardNo)+ '<br />'+getValue(row.recBank);
                    }
                },
                // {
                //     title: '收款卡号',
                //     align: 'left',
                //     field: 'recCardNo',
                //
                // },
                // {
                //     title: '收款银行',
                //     align: 'left',
                //     field: 'recBank',
                // },
                {
                    title: '结算方式',
                    field: 'payMethod',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(pay_method, value);
                    }
                },
                {
                    title: '转出账户',
                    align: 'left',
                    field: 'outAccountName',
                },
                {
                    title: '创建人',
                    align: 'left',
                    field: 'params.regUserName',
                },
                {
                    title: '付款备注',
                    align: 'left',
                    field: 'memo',
                },
                {
                    title: '对账单号',
                    align: 'left',
                    field: 'params.checkSheetVbillno'
                },
                {
                    title: '对账总金额',
                    align: 'left',
                    field: 'params.checkTotalAmount'
                },
                {
                    title: '费用分摊',
                    field: 'params.sumInfo',
                    formatter: function(value, row, index) {
                        if (value) {//{"JH":0,"MY":0}
                            var tmp = []
                            for (var k in value) {
                                if (tmp.length > 0) {
                                    tmp.push("<br>")
                                }
                                if (k == 'MY') {
                                    tmp.push("铭源:", value[k])
                                } else if (k == 'JH') {
                                    tmp.push("吉华:", value[k])
                                } else if (k == 'DH') {
                                    tmp.push("鼎辉:", value[k])
                                } else if (k == 'DW') {
                                    tmp.push("鼎旺:", value[k])
                                } else {
                                    tmp.push(k, ":", value[k])
                                }
                            }
                            return tmp.join("")
                        }
                        return ""
                    }
                },
                {
                    title: '费用明细',
                    field: 'params.detailInfo',
                    formatter: function(value, row, index) {
                        if (value) {
                            var tmp = ["<div style='max-height: 80px;overflow: auto;'>"];
                            for (let i = 0; i < value.length; i++) {
                                tmp.push("<div>",value[i].VBILLNO,"【")
                                if (value[i].BALA_CORP_ID == 'MY') {
                                    tmp.push("铭源")
                                } else if (value[i].BALA_CORP_ID == 'JH') {
                                    tmp.push("吉华")
                                } else if (value[i].BALA_CORP_ID == 'DH') {
                                    tmp.push("鼎辉")
                                } else if (value[i].BALA_CORP_ID == 'DW') {
                                    tmp.push("鼎旺")
                                } else {
                                    tmp.push(value[i].BALA_CORP_ID)
                                }
                                tmp.push("】客户：",value[i].CUST_ABBR,
                                    "，要求提货：", value[i].REQ_DELI_DATE ? value[i].REQ_DELI_DATE.substring(0,10):'无',
                                    "，",value[i].DELI_ADDR,
                                    " ~ ",value[i].ARRI_ADDR,
                                    "，货品：",value[i].GOODS_NAME,
                                    "，件数重量体积：",value[i].NUM_COUNT,'|',value[i].WEIGHT_COUNT,'|',value[i].VOLUME_COUNT,
                                    "，车长车型：",value[i].CAR_LEN_NAME,value[i].CAR_TYPE_NAME,
                                    "，调度：",value[i].REG_USER_NAME)
                                if (value[i].CAR_NO) {
                                    tmp.push("，车辆：", value[i].CAR_NO)
                                }
                                tmp.push("</div>")
                            }
                            tmp.push("</div>")
                            return tmp.join("")
                        }
                        return ""
                    }
                }
            ]
        };

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endDate',
                type: 'date',
                trigger: 'click'
            });
        });

        $.table.init(options);
    });
    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        data.settlementMethod = $.common.join($('#payMethod').selectpicker('val'));
        data.balaCorp = $.common.join($('#balaCorp').selectpicker('val'))//结算公司

        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 650;
    /**
     * 查看运营组 分组的资金
     */
    function openSalesDeptDetail(vbillno) {
        var url = prefix + "/salesDeptDetail?vbillno=" + vbillno;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "明细信息",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    //导出
    function exprot(){
        var formId = "role-form";
        var url = prefix + "/exportSalesDept"
        $.modal.confirm("确定导出所有" + $.table._option.modalName + "吗？", function() {
            var currentId = $.common.isEmpty(formId) ? $('form').attr('id') : formId;
            $.modal.loading("正在导出数据，请稍后...");
            $.post(url, $("#" + currentId).serializeArray(), function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });

    }

    //导出
    function exprot2(){
        var formId = "role-form";
        var url = prefix + "/exportCustomerSalesDept"
        $.modal.confirm("确定导出所有" + $.table._option.modalName + "吗？", function() {
            var currentId = $.common.isEmpty(formId) ? $('form').attr('id') : formId;
            $.modal.loading("正在导出数据，请稍后...");
            $.post(url, $("#" + currentId).serializeArray(), function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });

    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.settlementMethod = $.common.join($('#payMethod').selectpicker('val'));
        data.balaCorp = $.common.join($('#balaCorp').selectpicker('val'))//结算公司
        $.ajax({
            url: prefix + "/getPayRecordCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    console.log(data);
                    $("#sumPayAmountCountTotal").text(data.PAYAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

</script>

</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>