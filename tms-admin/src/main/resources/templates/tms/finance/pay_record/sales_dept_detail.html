<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('运营组成本分摊')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    //应付 或 申请 单据号
    var vbillno = [[${vbillno}]];

    var prefix = ctx + "finance/payRecord";
    $(function () {
        var options = {
            url: prefix + "/salesDeptDetailList?vbillno="+vbillno,
            showToggle: false,
            showColumns: true,
            modalName: "运营组成本分摊",
            fixedColumns: true,
            fixedNumber: 0,
            showFooter:true,
            onPostBody:function () {
                //查询合计总金额
                getAmountCount();
            },
            columns: [
                {
                    title: '运营组',
                    align: 'left',
                    field: 'salesDeptName'
                },
                {
                    title: '金额',
                    align: 'right',
                    field:'totalAmount',
                    formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    },
                    footerFormatter: function (row) {
                        return "总金额：<nobr id='totalAmount'>￥0</nobr>";
                    }
                }

            ]
        };
        $.table.init(options);
    });

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        $.ajax({
            url: prefix + "/getCount?vbillno="+vbillno,
            type: "post",
            dataType: "json",
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $("#totalAmount").text(data.totalAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }



</script>
</body>
</html>