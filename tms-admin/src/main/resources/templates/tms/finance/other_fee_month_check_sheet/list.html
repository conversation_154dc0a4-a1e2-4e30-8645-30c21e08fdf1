<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('应收月度对账列表')" />
	<th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal">
					<div class="row">
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
<!--								<label class="col-sm-4">关账年份：</label>-->
								<div class="col-sm-12">
									<input name="year" id="year" placeholder="请输入关账年份" class="form-control" type="text"
										   aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
<!--								<label class="col-sm-4">关账月份：</label>-->
								<div class="col-sm-12">
									<input name="month" id="month" placeholder="请输入关账月份" class="form-control" type="text"
										   aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
<!--								<label class="col-sm-4">客户名称：</label>-->
								<div class="col-sm-12">
									<input name="custName" id="custName" placeholder="请输入客户名称" class="form-control" type="text"
										     aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
<!--								<label class="col-sm-4">运营组：</label>-->
								<div class="col-sm-12">
									<select name="salesDeptId" id="salesDeptId" class="form-control valid selectpicker"
											aria-invalid="false" data-none-selected-text="运营组" multiple>
										<option value=""></option>
										<option th:each="mapS,status:${salesDept}"
												th:value="${mapS.deptId}"
												th:text="${mapS.deptName}">
										</option>
									</select>
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
<!--								<label class="col-sm-6"></label>-->
								<a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>
				</form>
			</div>
			
	        <div class="btn-group-sm" id="toolbar" role="group">
				<a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="finance:otherFeeMonthCheckSheet:export">
					<i class="fa fa-download"></i> 导出
				</a>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
			</div>
		</div>
	</div>
    <div th:include="include :: footer"></div>
	 <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var prefix = ctx + "finance/otherFeeMonthCheckSheet";

        $(function() {
            //监听回车事件 回车搜索
            $(document).keyup(function(e){
                var key = e.which;
                if(key==13){
                    $.table.search();
                }
            });
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
				exportUrl: prefix + "/export",
                modalName: "第三方月度对账",
		        showExport: true,
                showToggle:false,
                showColumns:false,
                clickToSelect:true,
                columns: [{
		            checkbox: true
		        },
				{
					field : 'year', 
					title : '关账年份',
				},
				{
					field : 'month',
					title : '关账月份',
				},
				{
					field : 'custCode',
					title : '客户编码',
				},
				{
					field : 'custName',
					title : '客户名称',
				},
				{
					field : 'salesDeptName',
					title : '运营组名称',
				},
				{
					field : 'totalAmount', 
					title : '当月应付',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
				},
				{
					field : 'gotAmount', 
					title : '当月付款',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
				},
				{
					field : 'openingAmount',
					title : '期末金额',
					formatter: function (value, row, index) {
						if (value === null) {
							return ;
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				},
				// {
				// 	field : 'ungotAmount',
				// 	title : '期末金额',
				// 	formatter: function (value, row, index) {
				// 		if(row.openingAmount != null){
				// 			var endAmount = row.openingAmount + row.totalAmount - row.gotAmount;
				// 			return endAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //
				// 		}
                //
				// 	}
				// },
				{
					field : 'regDate', 
					title : '创建时间',
				},
		       ]
            };
            $.table.init(options);
        });

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            laydate.render({
                elem: '#month',
                type: 'month',
                format:"MM"
            });
            laydate.render({
                elem: '#year',
                type: 'year'
            });
        });

        /**
         * 搜索
         */
        function searchPre() {
            var data = {};
            data.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
            $.table.search('role-form', data);
        }

        /**
         * 重置
         */
        function resetPre() {
            $(".selectpicker").selectpicker('deselectAll');
            $("#role-form")[0].reset();
            searchPre();
        }
    </script>
</body>
</html>