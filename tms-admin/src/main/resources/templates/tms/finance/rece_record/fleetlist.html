<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款记录')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="isFleetData" id="isFleetData" th:value="${isFleetData}">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">单据号：</label>
                            <div class="col-sm-8">
                                <input name="vbillno" placeholder="请输入单据号" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">收款类型：</label>
                            <div class="col-sm-8">
                                <select name="receivableType" id="receivableType" class="form-control"  th:with="type=${@dict.getType('receivable_type')}">
                                    <option value="">--请选择--</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">收款方式：</label>
                            <div class="col-sm-8">
                                <select name="receMethod" id="receMethod" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="结算方式" multiple th:with="type=${@dict.getType('receivable_method')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">客户：</label>
                            <div class="col-sm-8">
                                <input name="custName" placeholder="请输入客户名称" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">收款人：</label>
                            <div class="col-sm-8">
                                <input name="receivableMan" placeholder="请输入收款人" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">结算公司：</label>
                            <div class="col-sm-8">
                                <select name="custBalaCorp" id="custBalaCorp" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="结算公司" multiple th:with="type=${@dict.getType('bala_corp')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">对账单据号：</label>
                            <div class="col-sm-8">
                                <input name="checkVbillno" placeholder="请输入对账单据号" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">发票抬头：</label>
                            <div class="col-sm-8">
                                <input name="billingPayable" placeholder="请输入发票抬头" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">是否系统转换单据：</label>
                            <div class="col-sm-8">
                                <select name="params[type]"  class="form-control">
                                    <option ></option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2">收款时间：</label>
                            <div class="col-sm-7">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="regDate" placeholder="开始时间">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endDate"  name="corDate" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">

                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">

             <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasAnyPermissions="finance:receRecord:export,fleet:finance:receRecord:export">
                 <i class="fa fa-download"></i> 导出
             </a>
        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "finance/receRecord";
    var receivable_method = [[${@dict.getType('receivable_method')}]];//收款方式
    var receivable_type = [[${@dict.getType('receivable_type')}]];//收款类型
    var bala_corp = [[${@dict.getType('bala_corp')}]];//结算公司

    //昨日日期
    var day1 = new Date();
    day1.setTime(day1.getTime()-24*60*60*1000);
    var d1Month = ("0" + (day1.getMonth() + 1)).slice(-2);
    var d1Day = ("0" + (day1.getDate())).slice(-2);
    var yesterday = day1.getFullYear()+"-" + d1Month + "-" + d1Day;
    //今日日期
    var day2 = new Date();
    day2.setTime(day2.getTime());
    var d2Month = ("0" + (day2.getMonth() + 1)).slice(-2);
    var d2Day = ("0" + (day2.getDate())).slice(-2);
    var today = day2.getFullYear()+"-" + d2Month + "-" + d2Day;
    var sumReceivableAmount = 0;

    //初始化查询条件传参
    queryParams = function(params) {
        var search = {};
        //var search = $("#role-form").serializeObject();
        $.each($("#role-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
        //var comName =null;
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        //默认日期
        $("#startDate").val(yesterday);
        $("#endDate").val(today);
        $("[name='params[type]']").val('0')
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            queryParams: queryParams,
            showToggle:false,
            showColumns:true,
            clickToSelect:true,
            showFooter: true,
            fixedColumns: true,
            height: '500px',
            fixedNumber:3,
            modalName: "收款记录",
            onPostBody:function () {
                //合并页脚
                merge_footer();
                getAmountCount();
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "收款金额:<nobr id='sumReceivableAmount'>￥0</nobr>"
                        + "<br>"
                        + "<b>总合计：</b> 收款金额:<nobr id='sumReceivableAmountTotal'>￥0</nobr>"
                }
            },
                {
                    title: '操作',
                    align: 'left',
                    field: 'receCheckSheetId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="应收明细" onclick="receive(\'' + row.receSheetRecordId + '\')"><i  class="fa fa fa-list" style="font-size: 15px;" ></i></a>');
                        return actions.join('');
                    }
                },
                {
                    title: '单据号',
                    align: 'left',
                    field: 'vbillno'
                },
                {
                    title: '对账单据号',
                    align: 'left',
                    field: 'checkVbillno'
                },

                {
                    title: '发票抬头',
                    align: 'left',
                    field: 'billingPayable'
                },

                {
                    title: '客户',
                    align: 'left',
                    field: 'custName'
                },
                {
                    title: '结算客户',
                    align: 'left',
                    field: 'balaCustName'
                },
                {
                    title: '运营组',
                    align: 'left',
                    field: 'salesDeptName'
                },
                {
                    title: '收款类型',
                    field: 'receivableType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(receivable_type, value);
                    }
                },
                {
                    title: '收款方式',
                    field: 'receivableMethod',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(receivable_method, value);
                    }
                },
                {
                    title: '结算公司',
                    field: 'custBalaCorp',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(bala_corp, value);
                    }
                },

                {
                    title: '收款人',
                    align: 'left',
                    field: 'receivableMan',
                },
                {
                   title: '收款账户',
                   align: 'left',
                   field: 'account.accountName',

               },
               {
                   title: '收款账号',
                   align: 'left',
                   field: 'account.account',
               },
                {
                    title: '收款金额',
                    align: 'right',
                    field: 'receivableAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '收款时间',
                    align: 'left',
                    field: 'receivableDate',
                },
                {
                    title: '收款备注',
                    align: 'left',
                    field: 'memo',
                },




            ]
        };

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endDate',
                type: 'date',
                trigger: 'click'
            });
        });

        $.table.init(options);
    });
    /**
     * 合并页脚
     */
    function merge_footer() { // 防止左右拖动时总合计跟着移动
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }
    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.receMethod = $.common.join($('#receMethod').selectpicker('val'));//收款方式
        data.custBalaCorp = $.common.join($('#custBalaCorp').selectpicker('val'));//结算公司
        $.ajax({
            url: prefix + "/list/getSum",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    //总
                    $("#sumReceivableAmountTotal").text(data.SUM_RECEIVABLE_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }
    /**
     * 将总计金额清零
     */
    function clearTotal() {
        sumReceivableAmount = 0;//总应收
    }
    /**
     * 累计总金额
     */
    function addTotal(row) {
        sumReceivableAmount = sumReceivableAmount + row.receivableAmount;//总应收
    }
    function subTotal(row) {
        sumReceivableAmount = sumReceivableAmount - row.receivableAmount;//总应收
    }
    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#sumReceivableAmount").text(sumReceivableAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }

    function searchPre() {
        var data = {};
        data.receMethod = $.common.join($('#receMethod').selectpicker('val'));//收款方式
        data.custBalaCorp = $.common.join($('#custBalaCorp').selectpicker('val'));//结算公司
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }
    // 跳转对应的应收明细页面
    function receive(receSheetRecordId) {
        var url = ctx + "receCheckSheet" + "/receive?receSheetRecordId="+receSheetRecordId;
        $.modal.openTab('应收明细',url);
    }
</script>

</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>