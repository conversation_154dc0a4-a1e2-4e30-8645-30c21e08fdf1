<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('开票收款记录')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="panel-group">
        <div class="panel panel-default">
            <div class="panel-collapse collapse in">
                <div class="panel-body">
                    <div id="collapseThree" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <!--订单货品费用明细 begin-->
                            <div class="fixed-table-body" style="margin: 0px -5px;">
                                <table border="0" class="custom-tab table">
                                    <thead>
                                    <tr>
                                        <th style="width: 20%;">单据号</th>
                                        <th style="width: 15%;">收款日期</th>
                                        <th style="width: 15%;">收款类型</th>
                                        <th style="width: 15%;">收款方式</th>
                                        <th style="width: 15%;">收款金额(元)</th>
                                        <th style="width: 20%;">备注</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr th:each="receRecord:${receRecords}">
                                        <td nowrap="nowrap" th:text="${receRecord.vbillno}"></td>
                                        <td nowrap="nowrap" th:text="${#dates.format(receRecord.receivableDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                        <td nowrap="nowrap">
                                            <div class="col-sm-8" th:each="dict : ${@dict.getType('receivable_type')}"
                                                 th:if="${dict.dictValue} == ${receRecord.receivableType}" th:text="${dict.dictLabel}">
                                            </div>
                                        </td>
                                        <td nowrap="nowrap">
                                            <div class="col-sm-8" th:each="dict : ${@dict.getType('receivable_method')}"
                                                 th:if="${dict.dictValue} == ${receRecord.receivableMethod}" th:text="${dict.dictLabel}">
                                            </div>
                                        </td>
                                        <td nowrap="nowrap" th:text="${receRecord.receivableAmount}"></td>
                                        <td nowrap="nowrap" th:text="${memo}"></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">

</script>

</body>
</html>