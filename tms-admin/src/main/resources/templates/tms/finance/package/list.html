<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('打包对账')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">运单号：</label>-->
                            <div class="col-sm-12">
                                <input name="lotno" id="lotno" class="form-control"
                                       placeholder="请输入运单号" maxlength="30">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">应付单状态：</label>-->
                            <div class="col-sm-12">
                                <select name="status" id="status" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="应付单状态" multiple>
                                    <option th:each="dict : ${payDetailStatusEnum}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>


                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">承运商名称：</label>-->
                            <div class="col-sm-12">
                                <input name="carrName" id="carrName" placeholder="请输入承运商名称" class="form-control valid">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">司机名称：</label>-->
                            <div class="col-sm-12">
                                <input name="driverName" id="driverName" class="form-control" placeholder="请输入司机名称"
                                       maxlength="30">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">应付单号：</label>-->
                            <div class="col-sm-12">
                                <input name="vbillno" id="vbillno" class="form-control"
                                       placeholder="请输入应付单号" maxlength="30">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">创建人：</label>-->
                            <div class="col-sm-12">
                                <input name="regUserName" id="regUserName" class="form-control"
                                       placeholder="请输入创建人" maxlength="25">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">发货单号：</label>-->
                            <div class="col-sm-12">
                                <input name="invoiceVbillno"  class="form-control"
                                       placeholder="请输入发货单号" maxlength="25">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">客户简称：</label>-->
                            <div class="col-sm-12">
                                <input name="custAbbr"  class="form-control"
                                       placeholder="请输入客户简称" maxlength="25">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">付款类型：</label>-->
                            <div class="col-sm-12">
                                <select id="costTypeFreight" name="costTypeFreight" class="form-control noselect2 selectpicker" th:with="type=${@dict.getType('cost_type_freight')}"
                                        data-none-selected-text="付款类型" multiple>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">是否符合无车承运人：</label>-->
                            <div class="col-sm-12">
                                <select id="isNtocc" name="isNtocc" class="form-control selectpicker" data-none-selected-text="是否符合无车承运人">
                                    <option value=""></option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">运营组：</label>-->
                            <div class="col-sm-12">
                                <input name="salesDeptName" id="salesDeptName" placeholder="请输入运营组" class="form-control valid">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-3">要求提货日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateStart"  name="reqDeliDateStart" placeholder="要求提货日期开始">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateEnd"  name="reqDeliDateEnd" placeholder="要求提货日期结束">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-3">回单日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="receiptDateStart" placeholder="回单日期开始">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endtDate"  name="receiptDateEnd" placeholder="回单日期结束">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                   <!-- <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">油卡卡号：</label>
                            <div class="col-sm-8">
                                <input name="oilCardNumber" id="oilCardNumber" class="form-control"
                                       placeholder="请输入油卡卡号" >
                            </div>
                        </div>
                    </div>-->


                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="checking()" shiro:hasPermission="tms:package:checking">
                <i class="fa fa-file-text-o"></i> 生成对账单
            </a>
            <a class="btn btn-primary" onclick="insertChecking()" shiro:hasPermission="tms:package:join">
                <i class="fa fa-file-text-o"></i> 加入对账单
            </a>

            <a class="btn btn-primary single disabled" onclick="showChecking()" shiro:hasPermission="tms:package:showCheking">
                <i class="fa fa-file-text-o"></i> 查看对账单
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var balatype = [[${@dict.getType('bala_type')}]];
    var prefix = ctx + "payDetail";

    var payDetailStatusEnum = [[${payDetailStatusEnum}]];


    var transFeeCount = 0;
    var gotAmountCount = 0;
    var ungotAmountCount = 0;

    $(function () {
        var options = {
            url: ctx + "packagePayDetail/list",
            createUrl: prefix + "/add?carrierId="+$("#carrierId").val(),
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 3,
            clickToSelect:true,
            showFooter:true,
            onPostBody:function () {
                //查询合计总金额
                getAmountCount();
            },
            onRefresh:function(params){
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
            },
            onCheck: function (row,$element) {
                var transFee = row.transFeeCount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数加上本行数值
                transFeeCount = transFeeCount + transFee;
                gotAmountCount = gotAmountCount + gotAmount;
                ungotAmountCount = ungotAmountCount + ungotAmount;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheck: function (row, $element) {
                var transFee = row.transFeeCount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数减去本行数值
                transFeeCount = transFeeCount - transFee;
                gotAmountCount = gotAmountCount - gotAmount;
                ungotAmountCount = ungotAmountCount - ungotAmount;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onCheckAll: function (rowsAfter) {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                //循环累加
                for (var row of rowsAfter) {
                    transFeeCount = transFeeCount + row.transFeeCount;
                    gotAmountCount = gotAmountCount + row.gotAmount;
                    ungotAmountCount = ungotAmountCount + row.ungotAmount;
                }
                //赋值
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheckAll: function () {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "总金额合计：<nobr id='transFeeCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "已付金额合计：<nobr id='gotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "未付金额合计：<nobr id='ungotAmountCountTotal'>￥0</nobr><br>"+
                    "总合计:总金额合计：<nobr id='sumTransFeeCountTotal'>￥0</nobr>&nbsp&nbsp" +
                    "已付金额合计：<nobr id='sumGotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                    "未付金额合计：<nobr id='sumUngotAmountCountTotal'>￥0</nobr>";
                }
            },
                {
                    title: '操作',
                    align: 'left',
                    field: 'payDetailId',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="结算公司" onclick="balaCorp(\'' + value + '\',\'' + row.payDetailId + '\')"><i  class="fa fa-calculator" style="font-size: 15px;" ></i></a>');

                        return actions.join('');
                    }
                },
                {
                    title: '应付单号',
                    field: 'vbillno',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var result = value;
                        if (row.lotG7End == 2) {
                            result += ' <span class="label label-success" style="padding:1px;">G7</span>'
                        } else if (row.lotG7Syn != null) {
                            result += ' <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true" title="'
                            if (row.lotG7Syn == 0) {
                                result += '等待G7审验'
                            } else if (row.lotG7Syn == 1) {
                                result += row.lotG7Msg
                            } else if (row.lotG7Syn == 2) {
                                if (row.lotG7Start == null || row.lotG7Start == 0) {
                                    result += '等待推送【发车】'
                                } else if (row.lotG7Start == 1) {
                                    result += '【发车】推送失败'
                                } else if (row.lotG7End == null || row.lotG7End == 0) {
                                    result += '等待推送【到达】'
                                } else if (row.lotG7End == 1) {
                                    result += '【到达】推送失败'
                                }
                            } else if (row.lotG7Syn == 7) {
                                result += '运单已作废'
                            }
                            result += '">G7</span>'
                        }
                        return result
                    }

                },
                {
                    title: '应付单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == 0){
                            return '<span class="label label-primary">新建</span>';
                        }else if(value == 1){
                            return '<span class="label label-warning">已确认</span>';
                        }else if(value == 2){
                            return '<span class="label label-success">已对账</span>';
                        }else if(value == 3){
                            return '<span class="label label-info">部分核销</span>';
                        }else if(value == 4){
                            return '<span class="label label-info">已核销</span>';
                        }else if(value == 5){
                            return '<span class="label label-default">关闭</span>';
                        }else if(value == 6){
                            return '<span class="label label-success">已申请</span>';
                        }else if(value == 7){
                            return '<span class="label label-danger">核销中</span>';
                        }
                    }
                },{
                    title: '要求提货日期',
                    align: 'left',
                    field: 'entrustReqDeliDate'
                },{
                    title: '提货日期',
                    align: 'left',
                    field: 'reqDeliDate',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,10);
                    }
                },

                {
                    title: '到货日期',
                    align: 'left',
                    field: 'reqArriDate',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,10);
                    }
                },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceVbillno',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.invoiceVbillno);
                    }
                },
                {
                    title: '运营组',
                    align: 'left',
                    field: 'salesDeptName'
                },{
                    title: '客户简称',
                    align: 'left',
                    field: 'custAbbr'
                },
                {
                    title: '提货|到货省市区',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(row.deliAddr == null || row.deliAddr == ""){
                            row.deliAddr = '';
                        }
                        if(row.arriAddr == null || row.arriAddr.arriAddr == ""){
                            row.arriAddr = ''
                        }
                        if(row.deliAddr == "" && row.arriAddr == ""){
                            return "";
                        }else{
                            return row.deliAddr+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriAddr;
                        }

                    }
                },
                // {
                //     title:'提货地址',
                //     align:'left',
                //     field:'deliAddr'
                // },
                // {
                //     title:'到货地址',
                //     align:'left',
                //     field:'arriAddr'
                // },
                {
                    title: '车牌号',
                    align: 'left',
                    field: 'carno'
                },

                {
                    title: '承运商',
                    align: 'left',
                    field: 'carrName'
                },

                {
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '<span>运费</label>';
                            case 1:
                                return '<span>在途费用</label>';
                            case 2:
                                return '<span>调整费用</label>';
                            default:
                                break;
                        }
                    }

                },
                {
                    title: '付款类型',
                    field: 'costTypeOnWay',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if (row.freeType === '0'){
                            return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                        }else if (row.freeType === '2')  {
                            return '调整费'
                        }
                        return $.table.selectDictLabel(costTypeOnWay, value);
                    }

                },


                {
                    title: '司机',
                    align: 'left',
                    field: 'driverName'
                },
                {
                    title: '司机电话',
                    align: 'left',
                    field: 'driverMobile'
                },
                {
                    title: '总金额',
                    align: 'right',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '已付金额',
                    align: 'right',
                    field: 'gotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未付金额',
                    align: 'right',
                    field: 'ungotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '油卡比例',
                    align: 'left',
                    field: 'params.oilRatio',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value + '%';
                    }

                }, {
                    title: '运单号',
                    align: 'left',
                    field: 'lotno'
                },


                {
                    title: '创建人',
                    align: 'left',
                    field: 'regUserId'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate'
                },


                {
                    title: '是否为调整单',
                    align: 'left',
                    field: 'isAdjust',
                    formatter: function (value, row, index) {
                        if (value === 0) {
                            return '否';
                        }
                        return '是';
                    }
                },
                {
                    title: '调整原因',
                    align: 'left',
                    field: 'adjustMemo'
                }
                ,

                {
                    title: '收款人',
                    align: 'left',
                    field: 'recAccount'
                }
                ,
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'recBank'
                },
                {
                    title: '收款卡号',
                    align: 'left',
                    field: 'recCardNo'
                },
                /*{
                    title: '申请人',
                    align: 'left',
                    field: 'applyUser'
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'applyTime'
                },*/
                {
                    title: '备注',
                    align: 'left',
                    field: 'memo'
                },
                {
                    title: '结算客户',
                    align: 'left',
                    field: 'balaCorp'
                }
                ,
                {
                    title: '结算方式',
                    align: 'left',
                    field: 'balaMethod',
                    formatter: function status(value, row, index) {
                        if(value == 1){
                            return "单笔付款";
                        }else if(value == 2){
                            return "月度付款"
                        }else{
                            return "";
                        }
                    }
                },
                {
                    title: '是否符合无车承运人',
                    align: 'left',
                    field: 'isNtocc',
                    formatter: function status(value, row, index) {
                        if (value === 0) {
                            return '否';
                        }
                        return '是';
                    }
                }

            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
        });

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqDeliDateStart',
                type: 'date',
                trigger: 'click'
            });
        });

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqDeliDateEnd',
                type: 'date',
                trigger: 'click'
            });
        });
    });

    /**
     * 跳转应付修改页面
     * @param id
     */
    function edit(id,vbillstatus,isClose) {
        if (vbillstatus != 0) {
            $.modal.alertWarning("只能修改新建状态的应付单");
            return;
        }
        if (isClose === '1') {
            $.modal.alertWarning("该应付单已关账");
            return;
        }
        var url = prefix + "/edit?payDetailId=" + id;
        $.modal.openTab("应付明细修改", url);
    }
    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;
    /**
     * 结算公司
     */
    function balaCorp(id) {
        var url = prefix + "/balaCorp?payDetailId=" + id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "结算信息",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 生成对账单的方法
     */
    function checking() {

        var rows = $.table.selectColumns("payDetailId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var isNtocc = bootstrapTable[0]["isNtocc"];

        var carrierId = bootstrapTable[0]["carrierId"];//承运商

        var lotG7End = bootstrapTable[0]["lotG7End"];

        console.log(lotG7End)


        for (var i = 0; i < bootstrapTable.length; i++) {
            /*if (bootstrapTable[i]["isNtocc"] !== isNtocc ) {
                $.modal.alertWarning("请选择同为符合或不符合无车承运人的应付单");
                return;
            }*/

            if (bootstrapTable[i]["carrierId"] !== carrierId ) {
                $.modal.alertWarning("请选择相同承运商下的应付单");
                return;
            }

            if (bootstrapTable[i]["lotG7End"] !== lotG7End ) {
                $.modal.alertWarning("G7与非G7的应付单无法加入相同对账包");
                return;
            }

            if (bootstrapTable[i]["vbillstatus"] !== 1 && bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("生成对账单的应付单据只能为新建或已确认状态");
                return;
            }
            if (bootstrapTable[i]["freeType"] === '0') {
                if (bootstrapTable[i]["costTypeFreight"] == '1' || bootstrapTable[i]["costTypeFreight"] == '3') {
                    $.modal.alertWarning("预/到付油卡类型无法生成对账单");
                    return;
                }
            }
        }

        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        //验证是否存在异常记录
        $.ajax({
            type: "POST",
            url: prefix + "/checkEntrustExp?payDetailIds="+rows.join(),
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.openTab("生成对账单", prefix + "/checking?payDetailIds=" + rows.join()+"&lotG7End="+lotG7End);
                }
            }
        });
    }


    /**
     * 加入对账单的方法
     */
    function insertChecking() {

        var rows = $.table.selectColumns("payDetailId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var  isNtocc = bootstrapTable[0]["isNtocc"];

        var carrierId = bootstrapTable[0]["carrierId"];//承运商

        for (var i = 0; i < bootstrapTable.length; i++) {
            /*if (bootstrapTable[i]["isNtocc"] !== isNtocc ) {
                $.modal.alertWarning("请选择同为符合或不符合无车承运人的应付单");
                return;
            }*/
            if (bootstrapTable[i]["carrierId"] !== carrierId ) {
                $.modal.alertWarning("请选择相同承运商下的应付单");
                return;
            }
            if (bootstrapTable[i]["vbillstatus"] !== 1 && bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("加入对账单的应付单据只能为新建或已确认状态");
                return;
            }
            if (bootstrapTable[i]["freeType"] === '0') {
                if (bootstrapTable[i]["costTypeFreight"] == '1' || bootstrapTable[i]["costTypeFreight"] == '3') {
                    $.modal.alertWarning("预/到付油卡类型无法加入对账单");
                    return;
                }
            }
        }

        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        //验证是否存在异常记录
        $.ajax({
            type: "POST",
            url: prefix + "/checkEntrustExp?payDetailIds="+rows.join(),
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.open("加入对账单", prefix + "/insertChecking?carrierId=" + carrierId + "&payDetailIds=" + rows + "&isNtocc=" + isNtocc);
                }
            }
        });

    }


    /**
     * 分批付款
     */
    function batchPay(){
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["vbillstatus"] !== 1 && bootstrapTable[0]["vbillstatus"] !== 3) {
            $.modal.alertWarning("请选择已确认/部分核销的应付单");
            return;
        }

        if (bootstrapTable[0]["transFeeCount"] === bootstrapTable[0]["gotAmount"]) {
            $.modal.alertWarning("该应付单已完成");
            return;
        }
        var url = prefix + "/batchPay?payDetailId="+bootstrapTable[0]["payDetailId"];
        $.modal.open('分批付款',url);
    }

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.status = $.common.join($('#status').selectpicker('val'));
        data.costTypeFreight = $.common.join($('#costTypeFreight').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.status = $.common.join($('#status').selectpicker('val'));
        data.costTypeFreight = $.common.join($('#costTypeFreight').selectpicker('val'));
        $.ajax({
            url: ctx + "packagePayDetail/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $("#sumTransFeeCountTotal").text(data.TRANSFEECOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumGotAmountCountTotal").text(data.GOTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumUngotAmountCountTotal").text(data.UNGOTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    function showChecking() {
        //运单号
        /*var lot = $.table.selectColumns("lot");
        var url = prefix + "/showChecking/"+lot.join();
        $.modal.openTab("费用确认", url);*/

        var id = $.table.selectColumns('payDetailId');
        $.ajax({
            url: prefix + "/selectChecking/"+id,
            type:'POST',
            dataType:'json',
            success:function(result){
                if(result.code == web_status.SUCCESS){
                    var vbillno = result.data.vbillno;
                    var url = prefix + "/showChecking/"+vbillno+"/1";
                    $.modal.openTab("月结承运商对账", url);
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
    }
</script>
</body>
</html>