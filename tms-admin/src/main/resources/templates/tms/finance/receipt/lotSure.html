<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('发票确认')" />
</head>
<style>
    .fw{
        font-weight: bold;
    }
    .fc66{
        color: #666666;
    }
    .fcfe7{
        color: #fe720e;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .mt10{
        margin-top: 10px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12">
            <div class="row mt10">
                <div class="col-sm-12" style="color: red;font-weight: bold">
                    <div th:if="${type == '3'}">※ 当前发货单下所有已申请付款的单笔三方</div>
                    <div th:if="${type == '1'}">※ 当前运单下所有已申请付款的单笔应付</div>
                </div>
            </div>
            <div class="row mt10">
                <div class="col-sm-12">
                    <div class="fw" style="font-size: 16px" id="tax"></div>
                </div>
            </div>
            <div class="row mt10">
                <div class="col-sm-3">
                    <div class="over">
                        <div class="fl fc66" th:if="${type == '1'}">运单号：</div>
                        <div class="fl fc66" th:if="${type == '2'}">应付对账单号：</div>
                        <div class="fl fc66" th:if="${type == '3'}">发货单号：</div>
                        <div class="fl fc66" th:if="${type == '4'}">三方对账单号：</div>
                        <div class="fl" th:text="${info.NO}"></div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="over">
                        <div class="fl fc66">类型：</div>
                        <div class="fl" th:if="${type == '1'}">运单单笔应付</div>
                        <div class="fl" th:if="${type == '2'}">应付对账单</div>
                        <div class="fl" th:if="${type == '3'}">发货单单笔三方</div>
                        <div class="fl" th:if="${type == '4'}">三方对账单</div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="over">
                        <div th:if="${type == '1' || type == '2'}" class="fl fc66">承运商：</div>
                        <div th:if="${type == '1' || type == '2'}" class="fl" th:text="${info.CARR_NAME}"></div>
                        <div th:if="${type == '3' || type == '4'}" class="fl fc66">客户：</div>
                        <div th:if="${type == '3' || type == '4'}" class="fl" th:text="${info.CUST_ABBR}"></div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="over">
                        <div class="fl fc66">总金额：</div>
                        <div class="fl fcfe7" th:text="${info.TOTAL}"></div>
                    </div>
                </div>
            </div>
            <div class="row mt10">
                <div class="col-sm-12">
                    <div class="picviewer">
						<span th:each="file : ${files}">
							<img th:src="@{${file.filePath}}" style="width: 80px;height: 80px">
        				</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var billingType = [[${@dict.getType('billing_type')}]];
    var tax_txt = [[${info.TAXTXT}]];
    $(function(){
        var tmp = [];
        var arr = tax_txt.split(",");
        for (let i = 0; i < arr.length; i++) {
            var t = arr[i].split(":");
            for (let j = 0; j < billingType.length; j++) {
                if (t[0] == billingType[j].dictValue) {
                    tmp.push(i > 0?" [":"[",billingType[j].dictLabel,":",t[1],"]")
                    break;
                }
            }
        }
        $('#tax').html(tmp.join(""))
    })
</script>
</body>
</html>