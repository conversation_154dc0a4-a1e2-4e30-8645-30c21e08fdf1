<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('客户往来')" />
	<th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
	/*.left-fixed-body-columns{
		height: calc(100% - 120px) !important;
	}*/
</style>
<body class="gray-bg">
    <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal">

					<div class="row">
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<input type="text" class="form-control"
										   id="thirdCust" name="thirdCust" placeholder="客商">
								</div>
							</div>
						</div>

						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<input type="text" class="form-control"
										   id="no" name="no" placeholder="单号">
								</div>
							</div>
						</div>

						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<select name="uploaded" class="form-control valid noselect2 selectpicker"
											aria-invalid="false" data-none-selected-text="发票上传状态">
										<option value=""></option>
										<option value="0">未上传</option>
										<option value="1">已上传</option>
									</select>
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<select name="status" class="form-control valid noselect2 selectpicker"
											aria-invalid="false" data-none-selected-text="确认状态">
										<option value=""></option>
										<option value="0">待确认</option>
										<option value="1">已确认</option>
									</select>
								</div>
							</div>
						</div>

						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i
										class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
										class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-danger disabled multiple" onclick="unConfirm()" shiro:hasPermission="receipt:confirm:undo">
                    <i class="fa fa-window-close"></i> 反确认
                </a>
            </div>
			<div class="col-sm-12 select-table table-striped">
				<table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
			</div>
		</div>
	</div>

<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">

	var prefix = ctx + "receipt";
	var billingType = [[${@dict.getType('billing_type')}]];
	var sumCheckAmount = 0;
	var sumReceAmount = 0;
	var sumRestAmount = 0;
	$(function() {
		//监听回车事件 回车搜索
		$(document).keyup(function(e){
			var key = e.which;
			if(key==13){
				$.table.search();
			}
		});

		var options = {
			url: prefix + "/lot-list",
			//fixedColumns: true,
			//fixedNumber: 3,
			showToggle: false,
			//showFooter: true,
			height: 560,
			uniqueId: 'ID',
			clickToSelect: true,
			onRefresh:function(params){
				clearTotal();
			},
			onCheck: function (row,$element) {
				addTotal(row);
				setTotal();
			},
			onUncheck: function (row, $element) {
				subTotal(row);
				setTotal();
			},
			onCheckAll: function (rowsAfter) {
				clearTotal();
				//循环累加
				for (var row of rowsAfter) {
					addTotal(row);
				}
				//赋值
				setTotal();
			},
			onUncheckAll: function () {
				//总数清0
				clearTotal();
				//赋值
				setTotal();
			},
			onPostBody: function (params) {
				//合并页脚
				merge_footer();
				getAmountCount();
			},
			columns: [
				{
					checkbox: true,
					footerFormatter: function (row) {
						return "小计开票：<nobr id='sumCheckAmount'>¥0.00</nobr>" +
								" 小计收款：<nobr id='sumReceAmount'>¥0.00</nobr>" +
								" 小计余额：<nobr id='sumRestAmount'>¥0.00</nobr><br>"+
								"总开票：<nobr id='sumCheckAmountTotal'>¥0.00</nobr>" +
								" 总收款：<nobr id='sumReceAmountTotal'>¥0.00</nobr>" +
								" 总余额：<nobr id='sumRestAmountTotal'>¥0.00</nobr>";
					}
				},
				{
					title: '序号',
					formatter: function(value, row, index) {
						let option = $("#bootstrap-table").bootstrapTable('getOptions');
						let pageSize = option.pageSize;
						let pageNumber = option.pageNumber;
						return pageSize * (pageNumber - 1) + index + 1
					}
				},
				{
					title: '单号',
					field: 'NO'
				},
				{
					title: '类型',
					field: 'TYPE',
					formatter: function (value, row, index) {
						if (value == '1') {
							return "运单应付明细"
						} else if (value == '2') {
							return "应付对账"
						} else if (value == '3') {
							return '发货单三方明细'
						} else if (value == '4') {
							return '三方对账'
						}
					}
				},
				{
					title: '客商',
					field: 'THIRD_CUST'
				},
				{
					title: '含税构成',
					field: 'TAXTXT',
					formatter: function (value, row, index) {
						var tmp = [];
						var arr = value.split(",");
						for (let i = 0; i < arr.length; i++) {
							var t = arr[i].split(":");
							for (let j = 0; j < billingType.length; j++) {
								if (t[0] == billingType[j].dictValue) {
									tmp.push(i > 0?" [":"[",billingType[j].dictLabel,":",t[1],"]")
									break;
								}
							}
						}
						return tmp.join("")
					}
				},
				{
					title: '发票上传状态',
					field: 'UPLOADED',
					formatter: function (value, row, index) {
						if (value == 1) {
							return '<a href="javascript:receiptList(\''+row.ID+'\',\''+row.TYPE+'\','+row.STATUS+');">已上传</a>'
						} else {
							return '未上传'
						}
					}
				},
				{
					title: '确认状态',
					field: 'STATUS',
					formatter: function (value, row, index) {
						if (value == 0) {
							if (row.UPLOADED == 1) {
								return '<span style="color: #999; font-weight: bold">待确认</span>'
							}
						} else {
							return '<span style="color: green; font-weight: bold">已确认</span>'
						}
					}
				}
			]
		};
		$.table.init(options);

		/**
		 * 初始化日期控件
		 */
		/*layui.use('laydate', function() {
			var laydate = layui.laydate;
			laydate.render({
				elem: '#yearMonth',
				type: 'month',
				trigger: 'click'
			});
		});*/
	});

	function searchx() {
		var data = {};
		//data.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
		$.table.search('role-form', data);
	}
	/**
	 * 合并页脚
	 */
	function merge_footer() {
		var footer_tbody = $('.fixed-table-footer table tbody');
		var footer_tr = footer_tbody.find('>tr');
		var footer_td = footer_tr.find('>td');
		var footer_td_1 = footer_td.eq(0);
		//除了第一列其他都隐藏
		for(var i=1;i<footer_td.length;i++) {
			footer_td.eq(i).hide();
		}
		footer_td_1.attr('colspan', 1).show();
	}

	function getAmountCount() {
		/*var data = $.common.formToJSON("role-form");
		data.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
		$.post(prefix + "/getCount", data, function(result){
			if (result.code == 0) {
				var data = result.data;
				$("#sumCheckAmountTotal").text(data.SUM_CHECK_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
				$("#sumReceAmountTotal").text(data.SUM_RECE_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
				$("#sumRestAmountTotal").text(data.SUM_REST_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
			} else {
				$("#sumCheckAmountTotal").text("ERROR");
			}
		});*/
	}

	function exportExcel() {
		/*$.modal.confirm("即将导出，是否继续？", function() {
			$.modal.loading("正在导出数据，请稍后...");
			var search = $.common.formToJSON("role-form");
			search['salesDeptId'] = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
			$.post(prefix + "/export", search, function(result) {
				if (result.code == web_status.SUCCESS) {
					window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
				} else if (result.code == web_status.WARNING) {
					$.modal.alertWarning(result.msg)
				} else {
					$.modal.alertError(result.msg);
				}
				$.modal.closeLoading();
			});
		});*/
	}

	/**
	 * 将总计金额清零
	 */
	function clearTotal() {
		//开票金额合计
		sumCheckAmount = 0
		sumReceAmount = 0
		sumRestAmount = 0
	}

	/**
	 * 累计总金额
	 */
	function addTotal(row) {
		sumCheckAmount += row.checkAmount;
		sumReceAmount += row.receAmount;
		sumRestAmount += row.restAmount;
	}

	/**
	 *
	 */
	function subTotal(row) {
		sumCheckAmount -= row.checkAmount;
		sumReceAmount -= row.receAmount;
		sumRestAmount -= row.restAmount;
	}

	/**
	 *
	 * 给页脚总计赋值
	 */
	function setTotal() {
		$("#sumCheckAmount").text(sumCheckAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
		$("#sumReceAmount").text(sumReceAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
		$("#sumRestAmount").text(sumRestAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
	}

	function receiptList(id, type, status) {
		var btn = []
		if (status == 0) {
			btn.push('<i class="fa fa-check"></i> 确认')
		}
		btn.push('<i class="fa fa-remove"></i> 取消')
		var idx = layer.open({
			type: 2,
			area: ['450px', '403px'],
			fix: false,
			//不固定
			maxmin: false,
			shade: 0.3,
			shadeClose: false,
			title: '发票确认',
			content: prefix + '/lot-sure?id='+id+'&type='+type,
			btn: btn,
			// 弹层外区域关闭
			shadeClose: true,
			btn1: function(idx, layero) {
				if (status == 0) {
					$.modal.confirm("确认提交吗？", function () {
						$.operate.saveAndRefreshCurrent(prefix + '/receipt_confirm', 'id=' + id + "&type=" + type, function (result) {
							layer.close(idx)
						})
					})
				} else {
					layer.close(idx)
				}
			}
		})
		layer.full(idx);
		/*return;
		$.ajax({
			url: prefix + "/receiptList",
			data: 'id='+id+"&type="+type,
			cache: false,
			success: function (result) {
				if (result.code == 0) {
					var list = result.data;
					var tmp = []
					tmp.push('<ul class="nav nav-tabs" role="tablist">')
					for (let i = 0; i < list.length; i++) {
						tmp.push('<li role="presentation"',i == 0 ? ' class="active"':'','><a href="#pane-',i,'" aria-controls="pane-',i,'" role="tab" data-toggle="tab">',list[i].fileName,'</a></li>')
					}
					tmp.push('</ul>')
					tmp.push('<div class="tab-content" style="height: calc(100% - 41px);overflow:auto;">')
					for (let i = 0; i < list.length; i++) {
						tmp.push('<div role="tabpanel" class="tab-pane',i == 0 ? ' active':'','" id="pane-',i,'" style="height:100%;text-align: center">')
						var tname = list[i].fileName.toLowerCase();
						if (tname.endsWith(".jpg") || tname.endsWith(".png") || tname.endsWith(".bmp") || tname.endsWith(".gif")) {
							tmp.push('<img id="img-',i,'" src="', list[i].filePath, '" style="">')
						} else {
							tmp.push('<iframe frameborder="0" src="', list[i].filePath, '" style="width:100%;height:100%"></iframe>')
						}
						tmp.push('</div>')
					}
					tmp.push('</div>')
					var btn = []
					if (status == 0) {
						btn.push('<i class="fa fa-check"></i> 确认')
					}
					btn.push('<i class="fa fa-remove"></i> 取消')
					var idx = layer.open({
						type: 2,
						area: ['450px', '403px'],
						fix: false,
						//不固定
						maxmin: false,
						shade: 0.3,
						shadeClose: false,
						title: '发票确认',
						//content: tmp.join(""),
						content: prefix + '/lot-sure',
						btn: btn,
						// 弹层外区域关闭
						shadeClose: true,
						btn1: function(idx, layero) {
							if (status == 0) {
								$.modal.confirm("确认提交吗？", function () {
									$.operate.saveAndRefreshCurrent(prefix + '/receipt_confirm', 'id=' + id + "&type=" + type, function (result) {
										layer.close(idx)
									})
								})
							} else {
								layer.close(idx)
							}
						}
					})
					layer.full(idx);
				} else {
					$.modal.msgWarning(result.msg);
				}
			}
		})*/

	}

	function unConfirm() {
		var list = $.btTable.bootstrapTable('getSelections');
		if (list.length == 0) {
			$.modal.msgWarning("未找到数据")
			return
		}
		let rows = []
		for (let i = 0; i < list.length; i++) {
			if (list[i].STATUS != 1) {
				$.modal.msgWarning("请选择已确认的数据")
				return;
			}
			rows.push({id:list[i].ID,type:list[i].TYPE});
		}
		$.modal.confirm(`确定反确认这${rows.length}条数据`, function(){
			$.operate.postJson(prefix + "/unConfirm", rows)
		})

	}
</script>
</body>
</html>