<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付款余额')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">承运商名称：</label>-->
                            <div class="col-sm-12">
                                <input name="carrName" placeholder="请输入承运商名称" class="form-control" type="text"
                                       maxlength="30"  aria-required="true" th:value="${carrName}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-2">要求提货日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control" placeholder="要求提货开始日期"
                                       id="reqDeliDateStart"  name="params[reqDeliDateStart]">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control" placeholder="要求提货结束日期"
                                       id="reqDeliDateEnd"  name="params[reqDeliDateEnd]">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">承运商名称：</label>-->
                            <div class="col-sm-12">
                                <select class="form-control" name="params[showAll]">
                                    <option value="0">过滤未付为零</option>
                                    <option value="1">展示所有数据</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="lotDetail()" >
                <i class="fa fa-jpy"></i> 运单明细
            </a>
             <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="finance:payDetailOver:export">
                 <i class="fa fa-download"></i> 导出
             </a>
        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">

    var prefix = ctx + "finance/payDetailOver";

    var confirmedAmount = 0;
    var unconfirmedAmount = 0;
    var gotAmount = 0;
    var ungotAmount = 0;

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        /**
         * 日期插件
         */
        layui.use('laydate', function(){
            var laydate = layui.laydate;
            var reqDeliDateStart = laydate.render({
                elem: '#reqDeliDateStart', //指定元素
                isInitValue : false,
                trigger: 'click',
                type: 'date'
            });
            var reqDeliDateEnd = laydate.render({
                elem: '#reqDeliDateEnd', //指定元素
                isInitValue : false,
                trigger: 'click',
                type: 'date'
            });
        });

        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            modalName: "应付款余额",
            showFooter:true,
            height: 580,
            clickToSelect:true,
            onPostBody:function () {
                //合并页脚
                merge_footer();
                //查询合计总金额
                getAmountCount();
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "合计：已确认金额<nobr id='confirmedAmountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "未确认金额：<nobr id='unconfirmedAmountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "未付款金额：<nobr id='ungotAmountTotal'>￥0</nobr><br>" +
                        "总合计:已确认金额：<nobr id='sumConfirmedAmount'>￥0</nobr>&nbsp&nbsp" +
                        "未确认金额：<nobr id='sumUnconfirmedAmount'>￥0</nobr>&nbsp&nbsp" +
                        "未付款金额：<nobr id='sumUngotAmount'>￥0</nobr>&nbsp&nbsp";
                }
            },
                {
                    title: '承运商编码',
                    align: 'left',
                    field: 'carrCode'
                },
                {
                    title: '承运商名称',
                    align: 'left',
                    field: 'carrName'
                },
                {
                    title: '已确认未付款',
                    align: 'right',
                    field: 'confirmedAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未确认金额',
                    align: 'right',
                    field: 'unconfirmedAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未付款合计',
                    align: 'right',
                    field: 'ungotAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                /*{
                    title: '已付款金额',
                    align: 'right',
                    field: 'gotAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },*/
            ]
        };
        $.table.init(options);
    });


    /**
     * 将总计金额清零
     */
    function clearTotal() {
        confirmedAmount = 0;
        unconfirmedAmount = 0;
        gotAmount = 0;
        ungotAmount = 0;
    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        confirmedAmount = confirmedAmount + row.confirmedAmount;
        unconfirmedAmount = unconfirmedAmount + row.unconfirmedAmount;
        gotAmount = gotAmount + row.gotAmount;
        ungotAmount = ungotAmount + row.ungotAmount;
    }

    function subTotal(row) {
        confirmedAmount = confirmedAmount - row.confirmedAmount;
        unconfirmedAmount = unconfirmedAmount - row.unconfirmedAmount;
        gotAmount = gotAmount - row.gotAmount;
        ungotAmount = ungotAmount - row.ungotAmount;
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#confirmedAmountTotal").text(confirmedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#unconfirmedAmountTotal").text(unconfirmedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#gotAmountTotal").text(gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#ungotAmountTotal").text(ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $("#sumConfirmedAmount").text(data.confirmedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumUnconfirmedAmount").text(data.unconfirmedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //$("#sumGotAmount").text(data.gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumUngotAmount").text(data.ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    function lotDetail(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (rows.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        var carrCode = $.table.selectColumns("carrCode");
        var url = ctx + "trace/payReconciliation/0?carrCode="+carrCode;
        $.modal.openTab("运单明细", url);
    }




</script>

</body>
</html>