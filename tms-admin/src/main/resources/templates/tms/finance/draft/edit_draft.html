<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('汇票')" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 80px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .disin{
        display: inline-block;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .infotitle{
        line-height: 30px;
        font-weight: bold;
    }
    .titlebg{
        background: #eff3f9;
        padding: 5px 10px;

    }
    a.del-alink {
        display: block;
        margin: 120px auto 0;
    }
    .bggray{
        background: #f7f8fa;
    }
</style>
<body>
<div class="form-content">
    <form id="form-draft-edit" class="form-horizontal" novalidate="novalidate">
        <input id="id" th:value="${draft.id}" name="id" type="hidden">
        <!--要求提货日期-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="titlebg over">
                            <!--                            <div class="fl">-->
                            <!--                                <a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow()" title="新增行">+</a>-->
                            <!--                            </div>-->
                            <div class="fl fw ml10" style="line-height: 26px">汇票</div>
                        </div>
                        <div class="over">
                            <!--                            <div class="fl" style="width: 40px">-->
                            <!--                                <a class="close-link del-alink" style="background: #fd8481;border-radius: 50%" onclick="removeRowThree(this,0)" title="删除选择行">x</a>-->
                            <!--                            </div>-->
                            <div class="fr" style="width: calc(100% - 50px)">
                                <!-- 台账begin  发货地 收货地-->
                                <div class="infotitle">汇票信息</div>
                                <div class="row no-gutter">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span>  客户名称：</label>
                                            <div class="flex_right">
                                                <!-- <div class="input-group"> -->
                                                <input name="custAbbr" id="custAbbr" onclick="selectClient()"
                                                       th:if="${client != null and client != ''}" th:value="${client.custAbbr}"
                                                       type="text" placeholder="请选择客户" class="form-control valid"
                                                       autocomplete="off" required>
                                                <input name="custName" id="custName" th:if="${client != null}" th:value="${client.custName}" type="hidden">
                                                <input name="custCode" id="custCode" th:if="${client != null}" th:value="${client.custCode}" type="hidden">
                                                <input name="customerId" id="customerId" th:if="${client != null}" th:value="${client.customerId}" type="hidden">


                                                <input name="custAbbr" id="custAbbr"  th:if="${client == null}"
                                                       onclick="selectClient()" type="text" placeholder="请选择客户"
                                                       class="form-control valid" autocomplete="off" required>
                                                <input name="custName" id="custName" th:if="${client == null}" type="hidden">
                                                <input name="custCode" id="custCode" th:if="${client == null}" type="hidden">
                                                <input name="customerId" id="customerId" th:if="${client == null}" type="hidden">

                                                <!-- <span class="input-group-addon"><i class="glyphicon glyphicon-chevron-down"></i></span>
                                            </div> -->
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 结算公司：</label>
                                                <div class="flex_right">
                                                    <!--                                        <input name="xx" id="xx" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" class="form-control" required type="text" maxlength="20">-->
                                                    <select name="balaCorp" id="balaCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" required>
                                                        <option value="">---请选择结算公司---</option>
                                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:selected="${draft.balaCorp} == ${dict.dictValue}"
                                                                th:value="${dict.dictValue}"></option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <!--<div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 项目组：</label>
                                                <div class="flex_right">
                                                    <select name="projectTeam" id="projectTeam" class="form-control valid" required>
                                                        <option value="">-&#45;&#45;请选择项目组-&#45;&#45;</option>
                                                        <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}" th:selected="${draft.projectTeam} == ${mapS.deptId}"
                                                                th:text="${mapS.deptName}"></option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>-->

                                    </div>
                                    <div class="col-md-7 col-sm-7">
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 收票日期：</label>
                                                <div class="flex_right">
                                                    <input type="text" class=" form-control" id="receiptDate" name="receiptDate" required
                                                           placeholder="收票日期" lay-key="1" autocomplete="off" readonly th:value="${#dates.format(draft.receiptDate, 'yyyy-MM-dd HH:mm:ss')}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 票据类型：</label>
                                                <div class="flex_right">
<!--                                                    <input name="billType" id="billType" placeholder="请输入票据类型" class="form-control" type="text" maxlength="50" autocomplete="off" th:value="${draft.billType}">-->

                                                    <select name="billTypeId" id="billTypeId" class="form-control"
                                                            th:with="bill=${@dict.getType('draft_bill_type')}">
                                                        <option value=""></option>
                                                        <option th:each="billDict : ${bill}" th:text="${billDict.dictLabel}"
                                                                th:value="${billDict.dictValue}" th:selected="${draft.billTypeId == billDict.dictValue}"></option>
                                                    </select>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 票号：</label>
                                                <div class="flex_right">
                                                    <input name="ticketNumber" id="ticketNumber" placeholder="请输入票号"
                                                           class="form-control" type="text" maxlength="50" autocomplete="off"
                                                           th:value="${draft.ticketNumber}" required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 出票日：</label>
                                                <div class="flex_right">
                                                    <input name="issueDate" id="issueDate" placeholder="请输入出票日"
                                                           class="form-control" type="text" maxlength="50" autocomplete="off"
                                                           th:value="${#dates.format(draft.issueDate, 'yyyy-MM-dd HH:mm:ss')}" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 到期日：</label>
                                                <div class="flex_right">
                                                    <input name="dueDate" id="dueDate" placeholder="请输入到期日"
                                                           class="form-control" type="text" maxlength="50" autocomplete="off"
                                                           th:value="${#dates.format(draft.dueDate, 'yyyy-MM-dd HH:mm:ss')}" required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-7 col-sm-7">
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 付票单位：</label>
                                                <div class="flex_right">
<!--                                                    <input name="payCompany" id="payCompany" placeholder="请输入付票单位" class="form-control" type="text"-->
<!--                                                           maxlength="50" autocomplete="off" th:value="${draft.payCompany}">-->

                                                    <select name="payCompany" id="payCompany" class="form-control" onchange="changePayCompany()" required>
                                                        <option value=""></option>
                                                        <option th:each="custBilling : ${custBillingList}"
                                                                th:data-bank="${custBilling.bank}"
                                                                th:text="${custBilling.billingPayable}"
                                                                th:value="${custBilling.billingPayable}"
                                                                th:selected="${draft.payCompany == custBilling.billingPayable}"></option>

                                                    </select>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"> 出票行：</label>
                                                <div class="flex_right">
                                                    <input name="issuingBank" id="issuingBank" placeholder="请输入出票行"
                                                           class="form-control" type="text" maxlength="50"
                                                           autocomplete="off" th:value="${draft.issuingBank}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 金额：</label>
                                                <input name="amountMoney" id="amountMoney" placeholder="请输入金额" class="form-control" type="text" maxlength="50" autocomplete="off"
                                                       th:value="${draft.amountMoney}" oninput="$.numberUtil.onlyNumberTwoDecimal(this)"
                                                       th:disabled="${draft.ticketStatus != '1' || draft.discountFee != null || draft.endorFee != null}" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 备注 -->
                                <div class="row no-gutter">
                                    <div class="col-sm-12">
                                        <div class="flex">
                                            <label class="flex_left">备注：</label>
                                            <div class="flex_right">
                                            <textarea name="remark" id="remark" maxlength="200" class="form-control valid" rows="2" th:text="${draft.remark}"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-typeahead-js"/>

<script th:inline="javascript">

    var prefix = ctx + "finance/draft";

    /**
     * 基础信息 - 客户名称
     */
    function selectClient() {
        $.modal.open("选择客户", ctx + "client/related?permission=sales",1050,'',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            //客户id
            $("#customerId").val(rows[0]["customerId"]);
            //客户名称
            $("#custName").val(rows[0]["custName"]);
            //客户简称
            $("#custAbbr").val(rows[0]["custAbbr"]);
            //客户编码
            $("#custCode").val(rows[0]["custCode"]);

            getReceBilling(rows[0]["customerId"])

            layer.close(index);
        });
    }

    function getReceBilling(customerId) {
        let data = {};
        data["customerId"] = customerId

        $.ajax({
            url: ctx + "receCheckSheet/getBillByCustomerId",
            type: "post",
            dataType: "json",
            // contentType: "application/json; charset=utf-8",
            data: data,
            beforeSend: function () {
                // $.modal.loading("正在处理中，请稍后...");
                // $.modal.disable();
            },
            success: function (result) {
                $('#payCompany').html(`<option value=""></option>`)
                if (result.code == 0) {
                    let options = '';
                    result.data.forEach(function (custBilling) {
                        options += `<option value="${custBilling.billingPayable}" data-bank="${custBilling.bank}">${custBilling.billingPayable}</option>`;
                    });
                    $('#payCompany').html('<option value=""></option>' + options);
                } else {
                    $.modal.alertError(result.msg);
                }


            }
        });

    }

    function changePayCompany() {
        var selectedOption = $('#payCompany').find('option:selected');
        var bankValue = selectedOption.data('bank');
        $('#issuingBank').val(bankValue);
    }


    /**
     * 计算合计
     */
    function calculateUnitPriceTotals(obj,index) {
        var tonnage = $("#tonnage"+index).val()//获取吨位
        var unitPrice = $("#unitPrice"+index).val()//获取单价

        if(tonnage != null && tonnage != '' && unitPrice != null && unitPrice != '') {
            $("#freight"+index).val(tonnage*unitPrice)
            $("#paidFreight"+index).val(tonnage*unitPrice)
        }
    }

    /**
     * 计算合计
     */
    function calculateByTonnageTotals(obj,index) {
        console.log(index)
        var tonnage = $("#tonnage"+index).val()//获取吨位
        var unitPrice = $("#unitPrice"+index).val()//获取单价

        if(tonnage != null && tonnage != '' && unitPrice != null && unitPrice != '') {
            $("#freight"+index).val(tonnage*unitPrice)
            $("#paidFreight"+index).val(tonnage*unitPrice)
        }
    }

    $(function () {
        // 初始化省市区
        // $.provinces.init("deliProvinceId0","deliCityId0","deliAreaId0",deliProvinceId,deliCityId,deliAreaId);
        // $.provinces.init("arriProvinceId0","arriCityId0","arriAreaId0",arriProvinceId,arriCityId,arriAreaId);

        let length = $("#collapseOne .over").length;
        //console.log(length)
    });

    //实付运价默认等于运费
    $('#freight0').on('input',function(){
        $('#paidFreight0').val($(this).val());
    })
    $('#freight1').on('input',function(){
        $('#paidFreight1').val($(this).val());
    })
    //TODO  自动补全
    // $.get(prefix + "/getParkOnwners", function(data){
    //     console.log(data.ownerName)
    //     $("#typeahead-demo-3").typeahead({
    //         source: data[0].ownerName
    //     });
    // },'json');
    /**
     * 货主
     */
    //$("input[name^='cargoOwnerName']").typeahead({
    $("#cargoOwnerName0").typeahead({
        source: function (query, process) {
            return $.ajax({
                url: prefix + "/getParkOnwners",
                type: 'get',
                data: {parkOwnerName: query},
                success: function (result) {
                    $("#cargoOwnerId0").val("")
                    var resultList = result.map(function (item) {
                        var aItem = {id: item.id, name: item.ownerName};
                        return JSON.stringify(aItem);
                    });
                    return process(resultList);
                    //return process(result);
                },
            });
        },
        /**
         * 在选中数据后的操作，这里的返回值代表了输入框的值
         *
         * @param obj
         * @return 选中后，最终输入框里的值
         */
        updater: function (obj) {
            var item = JSON.parse(obj);
            //$(this).parent().find("input[name^='cargoOwnerId']").val(item.id)
            $("#cargoOwnerId0").val(item.id)
            return item.name;
        },
        /**
         * 使用指定的方式，高亮(指出)匹配的部分
         *
         * @param obj 数据源中返回的单个实例
         * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
         */
        highlighter: function (obj) {
            var item = JSON.parse(obj);
            var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
            return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                return '<strong>' + match + '</strong>'
            });
        },
        items: 'all', //展示全部 不设置默认展示8个
    });
    /**
     * 货物
     */
    $("#parkGoodsName0").typeahead({
        items: 'all', //展示全部 不设置默认展示8个
        source: function (query, process) {
            return $.ajax({
                url: prefix + "/getParkGoods",
                type: 'get',
                data: {parkGoodsName: $("#parkGoodsName0").val()},
                success: function (result) {
                    $("#parkGoodsId0").val("")
                    var resultList = result.map(function (item) {
                        var aItem = {id: item.id, name: item.goodsName};
                        return JSON.stringify(aItem);
                    });
                    return process(resultList);
                    //return process(result);
                },
            });
        },
        /**
         * 在选中数据后的操作，这里的返回值代表了输入框的值
         *
         * @param obj
         * @return 选中后，最终输入框里的值
         */
        updater: function (obj) {
            var item = JSON.parse(obj);
            $("#parkGoodsId0").val(item.id)
            return item.name;
        },
        /**
         * 使用指定的方式，高亮(指出)匹配的部分
         *
         * @param obj 数据源中返回的单个实例
         * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
         */
        highlighter: function (obj) {
            var item = JSON.parse(obj);
            var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
            return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                return '<strong>' + match + '</strong>'
            });
        },
    });
    /**
     * 车型
     */
    $("#parkCarmodelName0").typeahead({
        items: 'all', //展示全部 不设置默认展示8个
        source: function (query, process) {
            return $.ajax({
                url: prefix + "/getCarModels",
                type: 'get',
                data: {parkCarModelNames: $("#parkCarmodelName0").val()},
                success: function (result) {
                    $("#parkCarmodelId0").val("")

                    var resultList = result.map(function (item) {
                        var aItem = {id: item.id, name: item.modelName};
                        return JSON.stringify(aItem);
                    });
                    return process(resultList);
                },
            });
        },
        /**
         * 在选中数据后的操作，这里的返回值代表了输入框的值
         *
         * @param obj
         * @return 选中后，最终输入框里的值
         */
        updater: function (obj) {
            var item = JSON.parse(obj);
            $("#parkCarmodelId0").val(item.id)
            return item.name;
        },
        /**
         * 使用指定的方式，高亮(指出)匹配的部分
         *
         * @param obj 数据源中返回的单个实例
         * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
         */
        highlighter: function (obj) {
            var item = JSON.parse(obj);
            var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
            return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                return '<strong>' + match + '</strong>'
            });
        },
    });
    /**
     * 车号
     */
    $("#carName0").typeahead({
        items: 'all', //展示全部 不设置默认展示8个
        source: function (query, process) {
            return $.ajax({
                url: prefix + "/getCars",
                type: 'get',
                data: {parkCarNames: $("#carName0").val()},
                success: function (result) {
                    $("#carId0").val("")

                    var resultList = result.map(function (item) {
                        var aItem = {id: item.id, name: item.vehicleNumber};
                        return JSON.stringify(aItem);
                    });
                    return process(resultList);
                },
            });
        },
        /**
         * 在选中数据后的操作，这里的返回值代表了输入框的值
         *
         * @param obj
         * @return 选中后，最终输入框里的值
         */
        updater: function (obj) {
            var item = JSON.parse(obj);
            $("#carId0").val(item.id)
            return item.name;
        },
        /**
         * 使用指定的方式，高亮(指出)匹配的部分
         *
         * @param obj 数据源中返回的单个实例
         * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
         */
        highlighter: function (obj) {
            var item = JSON.parse(obj);
            var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
            return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                return '<strong>' + match + '</strong>'
            });
        },
    });
    /**
     * 车主
     */
    $("#vehicleOwnerName0").typeahead({
        items: 'all', //展示全部 不设置默认展示8个
        source: function (query, process) {
            return $.ajax({
                url: prefix + "/getVehicleOwners",
                type: 'get',
                data: {vehicleOwnerNames: $("#vehicleOwnerName0").val()},
                success: function (result) {
                    $("#vehicleOwnerId0").val("")

                    var resultList = result.map(function (item) {
                        var aItem = {id: item.id, name: item.vehicleOwnerName};
                        return JSON.stringify(aItem);
                    });
                    return process(resultList);
                },
            });
        },
        /**
         * 在选中数据后的操作，这里的返回值代表了输入框的值
         *
         * @param obj
         * @return 选中后，最终输入框里的值
         */
        updater: function (obj) {
            var item = JSON.parse(obj);
            $("#vehicleOwnerId0").val(item.id)
            return item.name;
        },
        /**
         * 使用指定的方式，高亮(指出)匹配的部分
         *
         * @param obj 数据源中返回的单个实例
         * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
         */
        highlighter: function (obj) {
            var item = JSON.parse(obj);
            var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
            return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                return '<strong>' + match + '</strong>'
            });
        },
    });

    function removeRowThree(obj, index) {
        $(obj).parent().parent().remove()
        //删除之后  改变后面元素的index
    }

    /**
     * 计算合计
     */
    function calculateTotals() {
        //件数
        var numList = $("[id^=num_]");
        var numCount = 0;
        numList.each(function(){
            if ($(this).val() != "") {
                numCount = parseFloat(decimal(numCount + parseFloat($(this).val()), 5));
            }

        });
        $("#numCount").val(numCount);
        $("#numCount_text").html(numCount);

        //重量
        var weightList = $("[id^=weight_]");
        var weightCount = 0;
        weightList.each(function(){
            if ($(this).val() != "") {
                weightCount = parseFloat(decimal(weightCount + parseFloat($(this).val()), 5));
            }
        });
        $("#weightCount").val(weightCount);
        $("#weightCount_text").html(weightCount);

        //体积
        var volumeList = $("[id^=volume_]");
        var volumeCount = 0;
        volumeList.each(function(){
            if ($(this).val() != "") {
                volumeCount = parseFloat(decimal(volumeCount + parseFloat($(this).val()), 5));
            }
        });
        $("#volumeCount").val(volumeCount);
        $("#volumeCount_text").html(volumeCount);

        //总金额
        var costAmountList = $("[id^=sum_]");
        var costAmount = 0;
        costAmountList.each(function(){
            if ($(this).val() != "") {
                costAmount = parseFloat(decimal(costAmount + parseFloat($(this).val()), 2));
            }
        });
        $("#costAmount").val(costAmount);
        $("#costAmount_text").html(costAmount);
    }

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#orderDate0").removeAttr("lay-key");
        $("#orderDate1").removeAttr("lay-key");
        $("#paymentTime0").removeAttr("lay-key");
        $("#paymentTime1").removeAttr("lay-key");
        laydate.render({
            elem: '#orderDate0'
            , type: 'datetime'
        });

        laydate.render({
            elem: '#paymentTime0'
            , type: 'datetime'
        });
    })

    /**
     * 校验
     */
    $("#form-book-add").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            reqDeliDate:{
                required:true,
            },
            reqArriDate:{
                required:true,
            },
            custName:{
                required:true,
            },
            balaName:{
                required:true,
            },
            appDeliMobile:{
                isPhone:true
            }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        //changePcaNmae();
        console.log(JSON.stringify($('#form-book-add').serializeArray()))
        console.log("===========")
        console.log($('#form-book-add').serializeArray())
        if ($.validate.form()) {
            //commit();
            $.operate.saveTab(prefix + "/editDraft", $('#form-draft-edit').serialize());
            //jQuery.subscribe("cmt", commit);
        }
    }

    function commit() {
        console.log(JSON.stringify($('#form-book-add').serializeArray()))
        console.log("===========")
        console.log($('#form-book-add').serializeArray())
        $.ajax({
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            url: prefix + "/addParkWayBill",
            data: JSON.stringify($('#form-book-add').serializeArray()),//JSON.stringify(data.field),
            dataType: 'json',
            success: function(result) {
                console.log(result)
                // if(result.code == 0) {
                //     layer.msg('注册成功！');
                // } else {
                //     layer.msg(result.msg);
                // }
            }
        });

        //$.operate.saveTab(prefix + "/addParkWayBill", $('#form-invoice-add').serialize());
    }
    // function commit() {
    //     alert("2")
    //     var dis = $(":disabled");
    //     dis.attr("disabled", false);
    //     var data = $("#form-invoice-add").serializeArray();
    //
    //     $.operate.saveTab(prefix + "/addParkWayBill", data,function (result) {
    //         if (result.code != 0) {
    //             dis.attr("disabled", true);
    //         }
    //     });
    // }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }
</script>
</body>

</html>