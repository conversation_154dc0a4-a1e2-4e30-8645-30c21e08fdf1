<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('汇票列表')" />
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
</head>
<style>
    .row-margin-top {
        margin-top: 10px;
    }
    /*.select-table .table td {*/
    /*    !* 超出部分隐藏 *!*/
    /*    overflow:hidden;*/
    /*    !* 超出部分显示省略号 *!*/
    /*    text-overflow:ellipsis;*/
    /*    !*规定段落中的文本不进行换行 *!*/
    /*    white-space:nowrap;*/
    /*    !* 配合宽度来使用 *!*/
    /*    height:40px;*/
    /*}*/
    .sm{
        background: #ffefef;
        padding: 5px 10px;
        border: 1px #ff9999 solid;
        margin-top: 10px;
    }
    .sm_text{
        color: #655959;
        display: inline-block;
        margin-left: 10px;
        line-height: 20px;
    }
    .fcff3{
        color: #ff3636;
    }
    .dropdownpad{
        padding: 1px 2px;
    }
    .navs .dropdown-menu{
        padding: 10px 0;
        left: 20px;
        top: -10px;
    }
</style>
<body class="gray-bg">

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="sm">
                <div class="sm_text">
                    已过期汇票(只统计待处理)：数量<span class="fcff3" th:text="${dueCount}"></span>
                    金额<span class="fcff3" th:text="'￥' + ${dueAmount}"></span>&nbsp;&nbsp;&nbsp;&nbsp;
                    即将过期(只统计待处理)：数量<span class="fcff3" th:text="${toDueCount}"></span>
                    金额<span class="fcff3" th:text="'￥' + ${toDueAmount}"></span> &nbsp;&nbsp;
                    注:到期日期当日不纳入过期
                </div>
                <!-- eg:当前日期2022-02-24  查询已过期,筛选条件 到:2022-02-23  查询条件包含了小于等于,2022-02-24不过期
                                           查询即将过期,筛选条件 从2022-02-24到2022-03-24  查询条件包含大于等于 小于等于,2022-02-24不过期,2022-03-24不过期-->
            </div>
            <div class="col-sm-12 search-collapse">
                <form id="draft-form">
                    <div class="row">
                        <div class="col-md-4 col-sm-8">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-addon">从</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 收票开始时间" style="font-size: 14px" id="receiptStartTime" name="receiptStartTime" >
                                        <span class="input-group-addon">到</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 收票结束时间" style="font-size: 14px" id="receiptEndTime" name="receiptEndTime">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-8">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-addon">从</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 出票开始时间" style="font-size: 14px" id="issueStartTime" name="issueStartTime" >
                                        <span class="input-group-addon">到</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 出票结束时间" style="font-size: 14px" id="issueEndTime" name="issueEndTime">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-8">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-addon">从</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 背书开始时间" style="font-size: 14px" id="endorserStartTime" name="endorserStartTime" >
                                        <span class="input-group-addon">到</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 背书结束时间" style="font-size: 14px" id="endorserEndTime" name="endorserEndTime">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row row-margin-top">
<!--                        <div class="col-md-2 col-sm-4">-->
<!--                            <div class="form-group">-->
<!--                                <div class="col-sm-12">-->
<!--                                    <input type="text" class="form-control" placeholder="请输入项目组" id="projectTeam" name="projectTeam">-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入票号" id="ticketNumber" name="ticketNumber">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入付票单位" id="payCompany" name="payCompany">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入出票行" id="issuingBank" name="issuingBank">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-8">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-addon">从</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 到期开始时间" style="font-size: 14px" id="dueStartTime" name="dueStartTime" >
                                        <span class="input-group-addon">到</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 到期结束时间" style="font-size: 14px" id="dueEndTime" name="dueEndTime">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row row-margin-top">
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="balaCorp" id="balaCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" required>
                                        <option value="">---请选择结算公司---</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="ticketStatus" id="ticketStatus" class="form-control valid" aria-invalid="false"  aria-required="true" required th:with="type=${@dict.getType('draft_status')}">
                                        <option value="">-- 请选择汇票状态 --</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"
                                                th:selected="${dict.dictValue == ticketStatus}"></option>
                                        <!--                                        <option value="1">待处理</option>-->
                                        <!--                                        <option value="2">已托收</option>-->
                                        <!--                                        <option value="3">已贴现</option>-->
                                        <!--                                        <option value="4">已背书</option>-->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="projectTeam" placeholder="运营组" id="projectTeam" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" data-none-selected-text="运营组" multiple>
                                        <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                                th:text="${mapS.deptName}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.addTab()" shiro:hasPermission="tms:finance:draft:add">-->
<!--                    <i class="fa fa-plus"></i> 新增-->
<!--                </a>-->
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="tms:finance:draft:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
<!--                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="tms:finance:draft:import">-->
<!--                    <i class="fa fa-upload"></i> 导入-->
<!--                </a>-->
                <!--<a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="tms:finance:draft:importExist">
                    <i class="fa fa-upload"></i> 导入已有数据
                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:finance:draft:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="resetting()" shiro:hasPermission="tms:finance:draft:resetting">
                    <i class="fa fa-circle-o-notch"></i> 重置
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" class="text-nowrap" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-select-js"/>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: ztree-js" />
<script id="txHtml" type="text/template">
    <div class="form-content">
        <div>
            <table class="table table-bordered mt5">
                <thead>
                <tr>
                    <td>贴现日期</td>
                    <td>贴现金额</td>
                    <td>备注</td>
                    <td>操作人</td>
                    <td>操作时间</td>
                </tr>
                </thead>
                <tbody id="txTbody">
                <tr><td colspan="5" style="text-align: center;">暂无数据</td></tr>
                </tbody>

            </table>
        </div>
    </div>
</script>
<script id="bsHtml" type="text/template">
    <div class="form-content">
        <div>
            <table class="table table-bordered mt5">
                <thead>
                <tr>
                    <td>背书日期</td>
                    <td>背书金额</td>
                    <td>背书单位</td>
                    <td>备注</td>
                    <td>操作人</td>
                    <td>操作时间</td>
                </tr>
                </thead>
                <tbody id="bsTbody">
                <tr><td colspan="6" style="text-align: center;">暂无数据</td></tr>
                </tbody>

            </table>
        </div>
    </div>
</script>
<script th:inline="javascript">
    //字典数据  结算公司
    var datas = [[${@dict.getType('bala_corp')}]];
    var editFlag = [[${@permission.hasPermi('tms:finance:draft:edit')}]];
    var tuoshouFlag = [[${@permission.hasPermi('tms:finance:draft:editCollection')}]];
    var tiexianFlag = [[${@permission.hasPermi('tms:finance:draft:editDiscount')}]];
    var beishuFlag = [[${@permission.hasPermi('tms:finance:draft:editEndorsement')}]];
    var removeFlag = [[${@permission.hasPermi('tms:finance:draft:remove')}]];
    var prefix = ctx + "finance/draft";

    function importExist() {
        var currentId = $.common.isEmpty(formId) ? 'importTpl' : formId;
        layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入' + $.table._option.modalName + '数据',
            content: $('#' + currentId).html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var index = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#file')[0].files[0]);
                formData.append("updateSupport", $("input[name='updateSupport']").is(':checked'));
                $.ajax({
                    url: $.table._option.importUrl,
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code == web_status.WARNING) {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });
    }

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#starttime").removeAttr("lay-key");
        $("#endtime").removeAttr("lay-key");
        laydate.render({
            elem: '#receiptStartTime'
            , type: 'date'
        });
        laydate.render({
            elem: '#receiptEndTime'
            , type: 'date'
        });

        laydate.render({
            elem: '#issueStartTime'
            , type: 'date'
        });
        laydate.render({
            elem: '#issueEndTime'
            , type: 'date'
        });

        laydate.render({
            elem: '#endorserStartTime'
            , type: 'date'
        });
        laydate.render({
            elem: '#endorserEndTime'
            , type: 'date'
        });

        laydate.render({
            elem: '#dueStartTime'
            , type: 'date'
        });
        laydate.render({
            elem: '#dueEndTime'
            , type: 'date'
        });
    })

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 185 });

        queryParkWayBillList();
        $.table.hideColumn("id");
    });

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        //data.params = new Map();
        data.projectTeam = $.common.join($('#projectTeam').selectpicker('val'));
        $.table.search('draft-form', data);
    }

    /**
     * 回单
     */
    function huidan() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要改变选中的" + rows.length + "条数据的回单状态吗?", function() {
            var url = $.table._option.removeUrl;
            var data = { "ids": rows.join() };
            $.operate.submit(prefix + "/huidan", "post", "json", data);
        });
    }

    /**
     * 入账
     */
    function ruzhang() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要改变选中的" + rows.length + "条数据的入账状态吗?", function() {
            var url = $.table._option.removeUrl;
            var data = { "ids": rows.join() };
            $.operate.submit(prefix + "/ruzhang", "post", "json", data);
        });
    }
    /**
     * 重置状态
     */
    function resetting() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要改变选中的" + rows.length + "条数据的状态为待处理吗?", function() {
            var data = { "ids": rows.join() };
            $.operate.submit(prefix + "/resetting", "post", "json", data);
        });
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("park-form");
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result;
                $("#tonnageCount").text(data.TONNAGECOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#freightCount").text(data.FREIGHTCOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#yishouCount").text(data.YISHOUCOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            }
        });
    }

    /* 汇票管理-托收 */
    function collection(id) {
        var url = prefix + '/collection/' + id;
        $.modal.open("托收", url, '800', '500');
    }
    /* 汇票管理-贴现 */
    function discount(id) {
        var url = prefix + '/discount/' + id;
        //$.modal.open("贴现", url, '800', '500');

        layer.open({
            type: 2,
            area: ['800px', '500px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '贴现',
            content: url,
            btn: ['确定', '全部贴现', '关闭'],
            shadeClose: true,
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            btn2: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitAll(index, layero);
                return false;
            },
            btn3: function (index, layero) {
                return true;
            }
        });
    }
    /* 汇票管理-背书 */
    function endorsement(id) {
        var url = prefix + '/endorsement/' + id;
        layer.open({
            type: 2,
            area: ['800px', '500px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '背书',
            content: url,
            btn: ['确定', '全部背书', '关闭'],
            shadeClose: true,
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            btn2: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitAll(index, layero);
                return false;
            },
            btn3: function (index, layero) {
                return true;
            }
        });
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("park-form");
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result;
                $("#totalAmountCount").text(data.TOTALAMOUNTMOENY.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#totalLessAmount").text(data.LESSAMOUNTTOTAL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            }
        });
    }

    //初始化查询条件传参
    queryParams = function(params) {
        var search = {};
        $.each($("#draft-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    var totalAmountMoney = 0;
    var ye = 0;
    function queryParkWayBillList() {
        var options = {
            url: prefix + "/draftByPage",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            // sortName: "receiptDate",
            // sortOrder: "desc",
            modalName: "汇票",
            fixedColumns: true,
            showFooter:true,
            fixedNumber:5,
            showExport: true,
            clickToSelect: true,
            exportTypes:['excel','csv'],
            height: 560,
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"汇票列表"
            },
            queryParams: queryParams,
            onRefresh:function(params){
                //总数清0
                totalAmountMoney = 0;
            },
            onCheck: function (row,$element) {
                var amountMoney = row.amountMoney;
                //总数加上本行数值
                totalAmountMoney = totalAmountMoney + amountMoney;
                ye += row.lessAmount;
                $("#selectTotalAmountCount").text(totalAmountMoney.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ye").text(ye.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheck: function (row, $element) {
                var amountMoney = row.amountMoney;
                //总数减去本行数值
                totalAmountMoney = totalAmountMoney - amountMoney;
                ye-= row.lessAmount;
                $("#selectTotalAmountCount").text(totalAmountMoney.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ye").text(ye.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onCheckAll: function (rowsAfter) {
                //总数清0
                totalAmountMoney = 0;
                ye = 0;
                //循环累加
                for (var row of rowsAfter) {
                    totalAmountMoney = totalAmountMoney + row.amountMoney;
                    ye += row.lessAmount;
                }
                //赋值
                $("#selectTotalAmountCount").text(totalAmountMoney.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ye").text(ye.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheckAll: function () {
                //总数清0
                totalAmountMoney = 0;
                ye = 0;
                $("#selectTotalAmountCount").text(totalAmountMoney.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ye").text(ye.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onPostBody:function () {
                //查询合计总金额
                getAmountCount();
            },
            columns: [
                {
                    checkbox:true,
                    footerFormatter: function (row) {
                        return "金额：<nobr id='selectTotalAmountCount'>￥0</nobr>&nbsp&nbsp"
                            +" 余额：<nobr id='ye'>￥0</nobr>&nbsp&nbsp"
                            +  "<br>合计：总金额：<nobr id='totalAmountCount'>￥0</nobr>&nbsp&nbsp"
                            +  " 总余额：<nobr id='totalLessAmount'>￥0</nobr>&nbsp&nbsp"
                    }
                },
                {
                    title: '主键',
                    align: 'left',
                    field : 'id'  //主键加载完成就隐藏  多选删除的时候是按照第一列的值是否相同来判断一共有几项的
                },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var actions = [];
                        // if ([[${@permission.hasPermi('tms:finance:draft:edit')}]] != "hidden") {
                        //     actions.push('<a class="btn btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        // }
                        // if ([[${@permission.hasPermi('tms:finance:draft:remove')}]] != "hidden") {
                        //     actions.push('<a class="btn btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a> ');
                        // }
                        if ([[${@permission.hasPermi('tms:finance:draft:editCollection')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs ' + tuoshouFlag + '" href="javascript:void(0)" onclick="collection(\'' + row.id + '\')"><i class="fa fa-reply-all"></i>托收</a> ');
                        }
                        if ([[${@permission.hasPermi('tms:finance:draft:editDiscount')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs ' + tiexianFlag + '" href="javascript:void(0)" onclick="discount(\'' + row.id + '\')"><i class="fa fa-tags"></i>贴现</a> ');
                        }
                        if ([[${@permission.hasPermi('tms:finance:draft:editEndorsement')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs ' + beishuFlag + '" href="javascript:void(0)" onclick="endorsement(\'' + row.id + '\')"><i class="fa fa-book"></i>背书</a> ');
                        }
                        //return actions.join('');
                        return '<div class="dropdown navs">' +
                            '<div class="dropdownpad" data-toggle="dropdown">' +
                            '<i class="fa fa-angle-down"></i>' +
                            '</div>' +
                            '<ul class="dropdown-menu">' + actions.join('') +
                            '</ul>' +
                            '</div>'
                    }
                },
                {
                    title: '状态',
                    align: 'left',
                    field : 'ticketStatus',
                    formatter: function(value, item, index) {
                        if (item.ticketStatus == '1') {
                            return '<span class="label label-danger">待处理</span>';
                        }
                        else if (item.ticketStatus == '2') {
                            return '<span class="label label-primary">已托收</span>';
                        }
                        else if (item.ticketStatus == '3') {
                            return '<span class="label label-info">已贴现</span>';
                        }
                        else if (item.ticketStatus == '4') {
                            return '<span class="label label-warning">已背书</span>';

                        }else if (item.ticketStatus == '5') {
                            return '<span class="label label-success">已完成</span>';
                        }
                    }
                },
                {
                    title: '客户简称',
                    align: 'left',
                    field : 'customerId'
                },
                {
                    title: '所属公司',
                    field: 'balaCorp',
                    align: 'right',
                    formatter: function(value, item, index) {
                        return $.table.selectDictLabel(datas, item.balaCorp);
                    }
                },
                {
                    title: '客户运营组',
                    align: 'left',
                    field : 'deptName'
                },
                {
                    title: '收票日期',
                    align: 'left',
                    field : 'receiptDate',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.substring(0,10);
                    }
                },
                {
                    title: '票据类型',
                    align: 'left',
                    field : 'billType'
                },
                {
                    title: '票号',
                    align: 'left',
                    field : 'ticketNumber'
                },
                {
                    title: '出票日',
                    align: 'left',
                    field : 'issueDate',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.substring(0,10);
                    }
                },
                {
                    title: '到期日',
                    align: 'right',
                    field: 'dueDate',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        //获取当前时间 年月日
                        var date = new Date();
                        //年 getFullYear()：四位数字返回年份
                        var year = date.getFullYear();  //getFullYear()代替getYear()
                        //月 getMonth()：0 ~ 11
                        var month = date.getMonth() + 1;
                        //日 getDate()：(1 ~ 31)
                        var day = date.getDate();
                        var time = year + '-' + addZero(month) + '-' + addZero(day)
                        if(compareDate(value, time)) {  //true  value小于当前日期 -- 已过期
                            //过期标红
                            return '<span style="color: red">' + value.substring(0,10) + '</span>'
                        }else {  //value大于等于当前日期 -- 未过期
                            return value.substring(0,10);
                        }
                    }
                },
                {
                    title: '付票单位',
                    align: 'left',
                    field : 'payCompany'
                },
                {
                    title: '出票行',
                    align: 'left',
                    field : 'issuingBank',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '金额(元)',
                    align: 'right',
                    field: 'amountMoney',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '余额(元)',
                    align: 'right',
                    field: 'lessAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },

                {
                    title: '贴现金额(元)',
                    align: 'right',
                    field: 'discountFee',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        let s = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        return `<div><a onclick="openDetail('${row.id}',0)">${s}</a></div>`
                    }
                },
                {
                    title: '背书金额(元)',
                    align: 'right',
                    field: 'endorFee',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        let s = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        return `<div><a onclick="openDetail('${row.id}',1)">${s}</a></div>`
                    }
                },
                {
                    title: '托收日期',
                    align: 'left',
                    field : 'collectionDate'
                },
                {
                    title: '托收备注',
                    align: 'left',
                    field : 'collectionRemark'
                },
                {
                    title: '备注',
                    align: 'left',
                    field : 'remark'
                },
                {
                    title: '力资费(元)',
                    align: 'right',
                    field: 'amount1',
                    formatter: function (value, row, index) {
                        if (value === null || value === 0 ) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '背靠背(元)',
                    align: 'right',
                    field: 'amount2',
                    formatter: function (value, row, index) {
                        if (value === null || value === 0) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
            ]
        };
        $.table.init(options);
    }
    /**
     * 小于10的拼接上0字符串
     */
    function addZero(s) {
        return s < 10 ? ('0' + s) : s;
    }
    /**
     * 前端比较两个时间大小
     */
    function compareDate(date1, date2) {
        //统一格式 yyyy-MM-dd new Date(dateStr).getTime()方式获取毫秒数
        var date_s = new Date(date1.substring(0, 10));
        var date_e = new Date(date2.substring(0, 10));
        if(date_s.getTime() >= date_e.getTime()) {
            return false
        }else {
            return true
        }
    }

    function openDetail(id,type) {
        let title = type === 0 ? '贴现记录' : '背书记录';
        let htmlId = type === 0 ? 'txHtml' : 'bsHtml';
        layer.open({
            type: 1,
            title: title,
            area: ['85%', '75%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: $(`#${htmlId}`).html(),
            btn: ['关闭'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {
                $.ajax({
                    url: ctx + `finance/draft/get_detail?id=${id}&type=${type}`,
                    type: "get",
                    beforeSend: function () {},
                    success: function (data) {
                        if (data.code == 0) {
                            if (type === 0) {
                                let skList = data.data;

                                $("#txTbody").empty();

                                let html = skList ===null || skList.length === 0 ? `<tr><td colspan="5" style="text-align: center;">暂无数据</td></tr>` : ``
                                skList.forEach(item => {
                                    html += `
                                        <tr>
                                            <td>${item.operateTime}</td>
                                            <td>${item.amount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})}</td>
                                            <td>${item.remark == null ? '' : item.remark}</td>
                                            <td>${item.regUserName}</td>
                                            <td>${item.regDate}</td>

                                        </tr>
                                    `;
                                });
                                $("#txTbody").append(html);
                            }else if (type === 1) {
                                let skList = data.data;

                                $("#bsTbody").empty();

                                let html = skList ===null || skList.length === 0 ? `<tr><td colspan="6" style="text-align: center;">暂无数据</td></tr>` : ``
                                skList.forEach(item => {
                                    html += `
                                        <tr>
                                             <td>${item.operateTime}</td>
                                            <td>${item.amount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})}</td>
                                              <td>${item.endorser}</td>
                                            <td>${item.remark == null ? '' : item.remark}</td>
                                            <td>${item.regUserName}</td>
                                            <td>${item.regDate}</td>
                                        </tr>
                                    `;
                                });
                                $("#bsTbody").append(html);

                            }
                        }

                    }
                })


            },
            cancel: function (index) {
                return true;
            }
        })
    }
</script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
<!--                <input type="checkbox" id="updateSupport" name="updateSupport" title="如果登录账户已经存在，更新这条数据。"> 是否更新已经存在的用户数据-->
                <span>汇票数据导入模板下载</span>
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>