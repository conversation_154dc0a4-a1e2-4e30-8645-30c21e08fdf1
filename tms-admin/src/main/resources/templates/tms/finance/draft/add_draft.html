<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增汇票')" />
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 80px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .disin{
        display: inline-block;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .infotitle{
        line-height: 30px;
        font-weight: bold;
    }
    .titlebg{
        background: #eff3f9;
        padding: 5px 10px;

    }
    a.del-alink {
        display: block;
        margin: 120px auto 0;
    }
    .bggray{
        background: #f7f8fa;
    }
</style>
<body>
<div class="form-content">
    <form id="form-draft-add" class="form-horizontal" novalidate="novalidate">
        <input id="historyGuidingPriceParam" type="hidden">
        <input id="historyGuidingPriceDetailParam" type="hidden">
        <!--要求提货日期-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="titlebg over">
<!--                            <div class="fl">-->
<!--                                <a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow()" title="新增行">+</a>-->
<!--                            </div>-->
                            <div class="fl fw ml10" style="line-height: 26px">汇票</div>
                        </div>
                        <div class="over">
<!--                            <div class="fl" style="width: 40px">-->
<!--                                <a class="close-link del-alink" style="background: #fd8481;border-radius: 50%" onclick="removeRowThree(this,0)" title="删除选择行">x</a>-->
<!--                            </div>-->
                            <div class="fr" style="width: calc(100% - 50px)">
                                <!-- 台账begin  发货地 收货地-->
                                <div class="infotitle">汇票信息</div>
                                <div class="row no-gutter">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 客户名称：</label>
                                            <div class="flex_right">
                                                <!-- <div class="input-group"> -->
                                                <input name="custAbbr" id="custAbbr" onclick="selectClient()" type="text"
                                                       placeholder="请选择客户" class="form-control valid"
                                                       autocomplete="off" required>
                                                <input name="custName" id="custName" type="hidden">
                                                <input name="custCode" id="custCode" type="hidden">
                                                <input name="customerId" id="customerId" type="hidden">
                                                <!-- <span class="input-group-addon"><i class="glyphicon glyphicon-chevron-down"></i></span>
                                            </div> -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 结算公司：</label>
                                                <div class="flex_right">
                                                    <!--                                        <input name="xx" id="xx" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" class="form-control" required type="text" maxlength="20">-->
                                                    <select name="balaCorp" id="balaCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" required>
                                                        <option value="">---请选择结算公司---</option>
                                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                                th:value="${dict.dictValue}"></option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <!--<div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 项目组：</label>
                                                <div class="flex_right">
                                                    <select name="projectTeam" id="projectTeam" class="form-control valid" required>
                                                        <option value="">-&#45;&#45;请选择项目组-&#45;&#45;</option>
                                                        <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                                                th:text="${mapS.deptName}"></option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>-->

                                    </div>
                                    <div class="col-md-7 col-sm-7">
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 收票日期：</label>
                                                <div class="flex_right">
                                                    <input type="text" class=" form-control" id="receiptDate" name="receiptDate" required
                                                           placeholder="收票日期" lay-key="1" autocomplete="off" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 票据类型：</label>
                                                <div class="flex_right">
<!--                                                    <input name="billType" id="billType" placeholder="请输入票据类型"-->
<!--                                                           class="form-control" type="text" maxlength="50"-->
<!--                                                           autocomplete="off" required>-->

                                                    <select name="billTypeId" id="billTypeId" class="form-control"
                                                            th:with="bill=${@dict.getType('draft_bill_type')}">
                                                        <option value=""></option>
                                                        <option th:each="billDict : ${bill}" th:text="${billDict.dictLabel}"  th:value="${billDict.dictValue}" ></option>
                                                    </select>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 票号：</label>
                                                <div class="flex_right">
                                                    <input name="ticketNumber" id="ticketNumber" placeholder="请输入票号"
                                                           class="form-control" type="text" maxlength="50" autocomplete="off" required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 出票日：</label>
                                                <div class="flex_right">
                                                    <input name="issueDate" id="issueDate" placeholder="请输入出票日" required readonly
                                                           class="form-control" type="text" maxlength="50" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 到期日：</label>
                                                <div class="flex_right">
                                                    <input name="dueDate" id="dueDate" placeholder="请输入到期日" required readonly
                                                           class="form-control" type="text" maxlength="50" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-7 col-sm-7">
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 付票单位：</label>
                                                <div class="flex_right">
<!--                                                    <input name="payCompany" id="payCompany" placeholder="请输入付票单位" class="form-control" type="text"-->
<!--                                                           maxlength="50" autocomplete="off" required>-->

                                                    <select name="payCompany" id="payCompany" class="form-control" onchange="changePayCompany()" required>
                                                        <option value=""></option>
                                                    </select>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"> 出票行：</label>
                                                <div class="flex_right">
                                                    <input name="issuingBank" id="issuingBank" placeholder="请输入出票行"
                                                           class="form-control" type="text" maxlength="50" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 金额：</label>
                                                <input name="amountMoney" id="amountMoney" placeholder="请输入金额"
                                                       class="form-control" type="text" maxlength="50" autocomplete="off"
                                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this)" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 备注 -->
                                <div class="row no-gutter">
                                    <div class="col-sm-12">
                                        <div class="flex">
                                            <label class="flex_left">备注：</label>
                                            <div class="flex_right">
                                            <textarea name="remark" id="remark" maxlength="200" class="form-control valid"
                                                      rows="2"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-typeahead-js"/>

<script th:inline="javascript">

    var prefix = ctx + "finance/draft";

    $(function () {
        // 初始化省市区  一共两行
        // $.provinces.init("deliProvinceId0","deliCityId0","deliAreaId0");
        // $.provinces.init("arriProvinceId0","arriCityId0","arriAreaId0");
        //
        // $.provinces.init("deliProvinceId1","deliCityId1","deliAreaId1");
        // $.provinces.init("arriProvinceId1","arriCityId1","arriAreaId1");

        let length = $("#collapseOne .over").length;
        //console.log(length)
    });

    /**
     * 基础信息 - 客户名称
     */
    function selectClient() {
        $.modal.open("选择客户", ctx + "client/related?permission=sales",1050,'',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            //客户id
            $("#customerId").val(rows[0]["customerId"]);
            //客户名称
            $("#custName").val(rows[0]["custName"]);
            //客户简称
            $("#custAbbr").val(rows[0]["custAbbr"]);
            //客户编码
            $("#custCode").val(rows[0]["custCode"]);

            getReceBilling(rows[0]["customerId"])

            layer.close(index);
        });
    }

    function getReceBilling(customerId) {
        let data = {};
        data["customerId"] = customerId

        $.ajax({
            url: ctx + "receCheckSheet/getBillByCustomerId",
            type: "post",
            dataType: "json",
            // contentType: "application/json; charset=utf-8",
            data: data,
            beforeSend: function () {
                // $.modal.loading("正在处理中，请稍后...");
                // $.modal.disable();
            },
            success: function (result) {
                $('#payCompany').html(`<option value=""></option>`)
                if (result.code == 0) {
                    let options = '';
                    result.data.forEach(function (custBilling) {
                        options += `<option value="${custBilling.billingPayable}" data-bank="${custBilling.bank}">${custBilling.billingPayable}</option>`;
                    });
                    $('#payCompany').html('<option value=""></option>' + options);
                } else {
                    $.modal.alertError(result.msg);
                }


            }
        });

    }

    function changePayCompany() {
        var selectedOption = $('#payCompany').find('option:selected');
        var bankValue = selectedOption.data('bank');
        $('#issuingBank').val(bankValue);
    }

    //实付运价默认等于运费
    $('#freight0').on('input',function(){
        $('#paidFreight0').val($(this).val());
    })

    // $.get(prefix + "/getParkOnwners", function(data){
    //     console.log(data.ownerName)
    //     $("#typeahead-demo-3").typeahead({
    //         source: data[0].ownerName
    //     });
    // },'json');
    /**
     * 货主
     */
    //$("input[name^='cargoOwnerName']").typeahead({
    $("#cargoOwnerName0").typeahead({
        source: function (query, process) {
            return $.ajax({
                url: prefix + "/getParkOnwners",
                type: 'get',
                data: {parkOwnerName: query},
                success: function (result) {
                    $("#cargoOwnerId0").val("")
                    var resultList = result.map(function (item) {
                        var aItem = {id: item.id, name: item.ownerName};
                        return JSON.stringify(aItem);
                    });
                    return process(resultList);
                    //return process(result);
                },
            });
        },
        /**
         * 在选中数据后的操作，这里的返回值代表了输入框的值
         *
         * @param obj
         * @return 选中后，最终输入框里的值
         */
        updater: function (obj) {
            var item = JSON.parse(obj);
            //$(this).parent().find("input[name^='cargoOwnerId']").val(item.id)
            $("#cargoOwnerId0").val(item.id)
            return item.name;
        },
        /**
         * 使用指定的方式，高亮(指出)匹配的部分
         *
         * @param obj 数据源中返回的单个实例
         * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
         */
        highlighter: function (obj) {
            var item = JSON.parse(obj);
            var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
            return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                return '<strong>' + match + '</strong>'
            });
        },
        items: 'all', //展示全部 不设置默认展示8个
    });
    /**
     * 货物
     */
    $("#parkGoodsName0").typeahead({
        items: 'all', //展示全部 不设置默认展示8个
        source: function (query, process) {
            return $.ajax({
                url: prefix + "/getParkGoods",
                type: 'get',
                data: {parkGoodsName: $("#parkGoodsName0").val()},
                success: function (result) {
                    $("#parkGoodsId0").val("")
                    var resultList = result.map(function (item) {
                        var aItem = {id: item.id, name: item.goodsName};
                        return JSON.stringify(aItem);
                    });
                    return process(resultList);
                    //return process(result);
                },
            });
        },
        /**
         * 在选中数据后的操作，这里的返回值代表了输入框的值
         *
         * @param obj
         * @return 选中后，最终输入框里的值
         */
        updater: function (obj) {
            var item = JSON.parse(obj);
            $("#parkGoodsId0").val(item.id)
            return item.name;
        },
        /**
         * 使用指定的方式，高亮(指出)匹配的部分
         *
         * @param obj 数据源中返回的单个实例
         * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
         */
        highlighter: function (obj) {
            var item = JSON.parse(obj);
            var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
            return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                return '<strong>' + match + '</strong>'
            });
        },
    });
    /**
     * 车型
     */
    $("#parkCarmodelName0").typeahead({
        items: 'all', //展示全部 不设置默认展示8个
        source: function (query, process) {
            return $.ajax({
                url: prefix + "/getCarModels",
                type: 'get',
                data: {parkCarModelNames: $("#parkCarmodelName0").val()},
                success: function (result) {
                    $("#parkCarmodelId0").val("")

                    var resultList = result.map(function (item) {
                        var aItem = {id: item.id, name: item.modelName};
                        return JSON.stringify(aItem);
                    });
                    return process(resultList);
                },
            });
        },
        /**
         * 在选中数据后的操作，这里的返回值代表了输入框的值
         *
         * @param obj
         * @return 选中后，最终输入框里的值
         */
        updater: function (obj) {
            var item = JSON.parse(obj);
            $("#parkCarmodelId0").val(item.id)
            return item.name;
        },
        /**
         * 使用指定的方式，高亮(指出)匹配的部分
         *
         * @param obj 数据源中返回的单个实例
         * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
         */
        highlighter: function (obj) {
            var item = JSON.parse(obj);
            var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
            return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                return '<strong>' + match + '</strong>'
            });
        },
    });
    /**
     * 车号
     */
    $("#carName0").typeahead({
        items: 'all', //展示全部 不设置默认展示8个
        source: function (query, process) {
            return $.ajax({
                url: prefix + "/getCars",
                type: 'get',
                data: {parkCarNames: $("#carName0").val()},
                success: function (result) {
                    $("#carId0").val("")

                    var resultList = result.map(function (item) {
                        var aItem = {id: item.id, name: item.vehicleNumber};
                        return JSON.stringify(aItem);
                    });
                    return process(resultList);
                },
            });
        },
        /**
         * 在选中数据后的操作，这里的返回值代表了输入框的值
         *
         * @param obj
         * @return 选中后，最终输入框里的值
         */
        updater: function (obj) {
            var item = JSON.parse(obj);
            $("#carId0").val(item.id)
            return item.name;
        },
        /**
         * 使用指定的方式，高亮(指出)匹配的部分
         *
         * @param obj 数据源中返回的单个实例
         * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
         */
        highlighter: function (obj) {
            var item = JSON.parse(obj);
            var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
            return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                return '<strong>' + match + '</strong>'
            });
        },
    });
    /**
     * 车主
     */
    $("#vehicleOwnerName0").typeahead({
        items: 'all', //展示全部 不设置默认展示8个
        source: function (query, process) {
            return $.ajax({
                url: prefix + "/getVehicleOwners",
                type: 'get',
                data: {vehicleOwnerNames: $("#vehicleOwnerName0").val()},
                success: function (result) {
                    $("#vehicleOwnerId0").val("")

                    var resultList = result.map(function (item) {
                        var aItem = {id: item.id, name: item.vehicleOwnerName};
                        return JSON.stringify(aItem);
                    });
                    return process(resultList);
                },
            });
        },
        /**
         * 在选中数据后的操作，这里的返回值代表了输入框的值
         *
         * @param obj
         * @return 选中后，最终输入框里的值
         */
        updater: function (obj) {
            var item = JSON.parse(obj);
            $("#vehicleOwnerId0").val(item.id)
            return item.name;
        },
        /**
         * 使用指定的方式，高亮(指出)匹配的部分
         *
         * @param obj 数据源中返回的单个实例
         * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
         */
        highlighter: function (obj) {
            var item = JSON.parse(obj);
            var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
            return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                return '<strong>' + match + '</strong>'
            });
        },
    });


    let wayBillIndex = 0;
    function insertRow() {
        //获取一共有多少行
        //let length = $("#collapseOne .over").length;
        var trTtml = "";
        wayBillIndex += 1;
        if(wayBillIndex % 2 != 0) {
           trTtml = "<div class=\"over mt10 bggray\">\n"
        }else {
            trTtml = "<div class=\"over\">\n"
        }
        trTtml = trTtml +
            "                            <div class=\"fl\" style=\"width: 40px\">\n" +
            "                                <a class=\"close-link del-alink\" style=\"background: #fd8481;border-radius: 50%\" onclick=\"removeRowThree(this,0)\" title=\"删除选择行\">x</a>\n" +
            "                            </div>\n" +
            "                            <div class=\"fr\" style=\"width: calc(100% - 50px)\">\n" +
            "                                <div class=\"infotitle\">货物信息</div>\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-md-5 col-sm-5\">\n" +
            "                                        <div class=\"col-sm-6\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 日期：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <input type=\"text\" class=\" form-control\" id='orderDate" + wayBillIndex  + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].orderDate\" required\n" +
            "                                                           placeholder=\"要求提货日\" autocomplete=\"off\" readonly>\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-6\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 货主：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <input type=\"text\" id='cargoOwnerName"+ wayBillIndex  + "\'"   +"name=\"parkWaybillList[" + wayBillIndex + "].cargoOwnerName\" placeholder=\"请输入货主姓名...\" class=\"form-control\" maxlength=\"50\" autocomplete=\"off\" data-provide=\"typeahead\"/>\n" +
            "                                                    <input id='cargoOwnerId"+ wayBillIndex + "\'" +"name=\"parkWaybillList[" + wayBillIndex + "].cargoOwnerId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-7 col-sm-7\">\n" +
            "                                        <div class=\"col-sm-4\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 车型：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <input id='parkCarmodelName"+ wayBillIndex + "\'" +"name=\"parkWaybillList[" + wayBillIndex + "].parkCarmodelName\" placeholder=\"请填写车型...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                                    <input id='parkCarmodelId"+  wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].parkCarmodelId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +

            "                                        <div class=\"col-sm-4\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 开票：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <select name=\"parkWaybillList[" + wayBillIndex + "].invoicing\" class=\"form-control valid\" aria-invalid=\"false\"  aria-required=\"true\" required>\n" +
            "                                                        <option value=\"1\">否</option>\n" +
            "                                                        <option value=\"2\">是</option>\n" +
            "                                                    </select>\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +




            "                                        <div class=\"col-sm-4\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 货物：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <input id='parkGoodsName" + wayBillIndex + "\'" +"name=\"parkWaybillList[" + wayBillIndex + "].parkGoodsName\" placeholder=\"请填写货物...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                                    <input id='parkGoodsId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].parkGoodsId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                </div>\n" +
            "\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-sm-6\">\n" +
            "                                        <div class=\"col-sm-5\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\">提货方地址：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <select  id='deliProvinceId" + wayBillIndex +  "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliProvinceId\" class=\"form-control valid\" aria-invalid=\"false\">\n" +
            "                                                    </select>\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-2\">\n" +
            "                                            <select id='deliCityId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliCityId\" class=\"form-control valid\" aria-invalid=\"false\"></select>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-2\">\n" +
            "                                            <select id='deliAreaId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliAreaId\" class=\"form-control valid\" aria-invalid=\"false\"></select>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-3\">\n" +
            "                                            <input id='deliDetailAddr" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliDetailAddr\" placeholder=\"请输入详细地址\" class=\"form-control\" type=\"text\"\n" +
            "                                                   maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-sm-6\">\n" +
            "                                        <div class=\"col-sm-5\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\">收货方地址：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <select  id='arriProvinceId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriProvinceId\"  class=\"form-control valid\" aria-invalid=\"false\">\n" +
            "                                                    </select>\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-2\">\n" +
            "                                            <select id='arriCityId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriCityId\" class=\"form-control valid\" aria-invalid=\"false\"></select>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-2\">\n" +
            "                                            <select id='arriAreaId" +  wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriAreaId\" class=\"form-control valid\" aria-invalid=\"false\"></select>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-3\">\n" +
            "                                            <input id='arriDetailAddr" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriDetailAddr\" placeholder=\"请输入详细地址\" class=\"form-control\" type=\"text\"\n" +
            "                                                   maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <input type=\"hidden\" id='deliProvinceName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliProvinceName\">\n" +
            "                                    <input type=\"hidden\" id='deliCityName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliCityName\">\n" +
            "                                    <input type=\"hidden\" id='deliAreaName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliAreaName\">\n" +
            "                                    <input type=\"hidden\" id='arriProvinceName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriProvinceName\">\n" +
            "                                    <input type=\"hidden\" id='arriCityName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriCityName\">\n" +
            "                                    <input type=\"hidden\" id='arriAreaName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriAreaName\">" +
            "                                </div>\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 吨位：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].tonnage\"" + "id=\"tonnage" + wayBillIndex + "\"" + "placeholder=\"请输入吨位...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\"\n" +
            "                                                       onkeyup=calculateByTonnageTotals(this,"+ wayBillIndex + ")" +
            "                                                       oninput=\"$.numberUtil.onlyNumberTwoDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 单价：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].unitPrice\"" + "id=\"unitPrice" + wayBillIndex + "\"" + "placeholder=\"请输入单价...\" class=\"form-control\" type=\"text\"\n" +
            "                                                       onkeyup=calculateUnitPriceTotals(this,"+ wayBillIndex + ")" +
            "                                                       maxlength=\"50\" autocomplete=\"off\" oninput=\"$.numberUtil.onlyNumberTwoDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 运费：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='freight" +  wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].freight\" placeholder=\"请输入运费...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\"\n" +
            "                                                       oninput=\"$.numberUtil.onlyNumberTwoDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +

            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 入账：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <select name=\"parkWaybillList[" + wayBillIndex + "].entry\" class=\"form-control valid\" aria-invalid=\"false\"  aria-required=\"true\" required>\n" +
            "                                                    <option value=\"2\">未收</option>\n" +
            "                                                    <option value=\"1\">已收</option>\n" +
            "                                                </select>\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].carTypeName\" type=\"hidden\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "\n" +
            "                                </div>\n" +
            "                                <div class=\"infotitle\">运输信息</div>\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 车号：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='carName"+ wayBillIndex + "\'" +"name=\"parkWaybillList[" + wayBillIndex + "].carName\" placeholder=\"请输入车号...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].carId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 车主：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='vehicleOwnerName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].vehicleOwnerName\" placeholder=\"请输入车主姓名...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                                <input id='vehicleOwnerId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].vehicleOwnerId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 回单：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <select name=\"parkWaybillList[" + wayBillIndex + "].receipt\" id=\"receipt\" class=\"form-control valid\" aria-invalid=\"false\"  aria-required=\"true\" required>\n" +
            "                                                    <option value=\"1\">否</option>\n" +
            "                                                    <option value=\"2\">是</option>\n" +
            "                                                </select>" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-6\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\" style=\"width: 90px\"><span class=\"fcff\">*</span> 支付时间：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input type=\"text\" class=\" form-control\" id='paymentTime"+ wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].paymentTime\"\n" +
            "                                                       placeholder=\"支付时间\" lay-key=\"2\" autocomplete=\"off\" readonly>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 实付运价：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='paidFreight"+wayBillIndex+ "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].paidFreight\" placeholder=\"请输入实付运价\" class=\"form-control\" type=\"text\"\n" +
            "                                                       maxlength=\"50\" autocomplete=\"off\" oninput=\"$.numberUtil.onlyNumberTwoDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +

            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\">现金：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].cashMoney\" placeholder=\"请输入现金...\" class=\"form-control\" type=\"text\"\n" +
            "                                                       maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\">油卡：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].oilCard\" placeholder=\"请输入油卡...\" class=\"form-control\" type=\"text\"\n" +
            "                                                       maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                </div>\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-sm-12\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\">备注：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                            <textarea name=\"parkWaybillList[" + wayBillIndex + "].remark\" maxlength=\"200\" class=\"form-control valid\"\n" +
            "                                                      rows=\"2\"></textarea>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                </div>\n" +
            "\n" +
            "                            </div>\n" +
            "                        </div>"

        $(".panel-body").append(trTtml);

        //加载省市区
        $.provinces.init("deliProvinceId"+wayBillIndex, "deliCityId"+wayBillIndex, "deliAreaId"+wayBillIndex);
        $.provinces.init("arriProvinceId"+wayBillIndex, "arriCityId"+wayBillIndex, "arriAreaId"+wayBillIndex);
        $('#deliProvinceId'+wayBillIndex).change(function(){
            $("#deliProvinceName"+wayBillIndex).val($(this).find(":selected").text());
        });
        $('#deliCityId'+wayBillIndex).change(function(){
            $("#deliCityName"+wayBillIndex).val($(this).find(":selected").text());
        });
        $('#deliAreaId'+wayBillIndex).change(function(){
            $("#deliAreaName"+wayBillIndex).val($(this).find(":selected").text());
        });
        $('#arriProvinceId'+wayBillIndex).change(function(){
            $("#arriProvinceName"+wayBillIndex).val($(this).find(":selected").text());
        });
        $('#arriCityId'+wayBillIndex).change(function(){
            $("#arriCityName"+wayBillIndex).val($(this).find(":selected").text());
        });
        $('#arriAreaId'+wayBillIndex).change(function(){
            $("#arriAreaName"+wayBillIndex).val($(this).find(":selected").text());
        });
        //运费和实付运价初始同步
        $('#freight'+wayBillIndex).on('input',function(){
            $('#paidFreight'+wayBillIndex).val($(this).val());
        })

        //时间初始化
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            //日期时间选择器
            $("#orderDate"+wayBillIndex).removeAttr("lay-key");
            $("#paymentTime"+wayBillIndex).removeAttr("lay-key");
            laydate.render({
                elem: '#orderDate'+wayBillIndex
                , type: 'datetime'
                , value: new Date()
            });

            laydate.render({
                elem: '#paymentTime'+wayBillIndex
                , type: 'datetime'
            });
        })
        /**
         * 货主
         */
        $("#cargoOwnerName"+wayBillIndex).typeahead({
            source: function (query, process) {
                return $.ajax({
                    url: prefix + "/getParkOnwners",
                    type: 'get',
                    data: {parkOwnerName: query},
                    success: function (result) {
                        $("#cargoOwnerId"+wayBillIndex).val("")
                        var resultList = result.map(function (item) {
                            var aItem = {id: item.id, name: item.ownerName};
                            return JSON.stringify(aItem);
                        });
                        return process(resultList);
                    },
                });
            },
            /**
             * 在选中数据后的操作，这里的返回值代表了输入框的值
             *
             * @param obj
             * @return 选中后，最终输入框里的值
             */
            updater: function (obj) {
                var item = JSON.parse(obj);
                $("#cargoOwnerId"+wayBillIndex).val(item.id)
                return item.name;
            },
            /**
             * 使用指定的方式，高亮(指出)匹配的部分
             *
             * @param obj 数据源中返回的单个实例
             * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
             */
            highlighter: function (obj) {
                var item = JSON.parse(obj);
                var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
                return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                    return '<strong>' + match + '</strong>'
                });
            },
            items: 'all', //展示全部 不设置默认展示8个
        });
        /**
         * 货物
         */
        $("#parkGoodsName"+wayBillIndex).typeahead({
            items: 'all', //展示全部 不设置默认展示8个
            source: function (query, process) {
                return $.ajax({
                    url: prefix + "/getParkGoods",
                    type: 'get',
                    data: {parkGoodsName: query},
                    success: function (result) {
                        $("#parkGoodsId"+wayBillIndex).val("")
                        var resultList = result.map(function (item) {
                            var aItem = {id: item.id, name: item.goodsName};
                            return JSON.stringify(aItem);
                        });
                        return process(resultList);
                        //return process(result);
                    },
                });
            },
            /**
             * 在选中数据后的操作，这里的返回值代表了输入框的值
             *
             * @param obj
             * @return 选中后，最终输入框里的值
             */
            updater: function (obj) {
                var item = JSON.parse(obj);
                $("#parkGoodsId"+wayBillIndex).val(item.id)
                return item.name;
            },
            /**
             * 使用指定的方式，高亮(指出)匹配的部分
             *
             * @param obj 数据源中返回的单个实例
             * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
             */
            highlighter: function (obj) {
                var item = JSON.parse(obj);
                var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
                return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                    return '<strong>' + match + '</strong>'
                });
            },
        });
        /**
         * 车型
         */
        $("#parkCarmodelName"+wayBillIndex).typeahead({
            items: 'all', //展示全部 不设置默认展示8个
            source: function (query, process) {
                return $.ajax({
                    url: prefix + "/getCarModels",
                    type: 'get',
                    data: {parkCarModelNames: $("#parkCarmodelName"+wayBillIndex).val()},
                    success: function (result) {
                        $("#parkCarmodelId"+wayBillIndex).val("")

                        var resultList = result.map(function (item) {
                            var aItem = {id: item.id, name: item.modelName};
                            return JSON.stringify(aItem);
                        });
                        return process(resultList);
                    },
                });
            },
            /**
             * 在选中数据后的操作，这里的返回值代表了输入框的值
             *
             * @param obj
             * @return 选中后，最终输入框里的值
             */
            updater: function (obj) {
                var item = JSON.parse(obj);
                $("#parkCarmodelId"+wayBillIndex).val(item.id)
                return item.name;
            },
            /**
             * 使用指定的方式，高亮(指出)匹配的部分
             *
             * @param obj 数据源中返回的单个实例
             * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
             */
            highlighter: function (obj) {
                var item = JSON.parse(obj);
                var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
                return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                    return '<strong>' + match + '</strong>'
                });
            },
        });
        /**
         * 车号
         */
        $("#carName"+wayBillIndex).typeahead({
            items: 'all', //展示全部 不设置默认展示8个
            source: function (query, process) {
                return $.ajax({
                    url: prefix + "/getCars",
                    type: 'get',
                    data: {parkCarNames: $("#carName"+wayBillIndex).val()},
                    success: function (result) {
                        $("#carId"+wayBillIndex).val("")

                        var resultList = result.map(function (item) {
                            var aItem = {id: item.id, name: item.vehicleNumber};
                            return JSON.stringify(aItem);
                        });
                        return process(resultList);
                    },
                });
            },
            /**
             * 在选中数据后的操作，这里的返回值代表了输入框的值
             *
             * @param obj
             * @return 选中后，最终输入框里的值
             */
            updater: function (obj) {
                var item = JSON.parse(obj);
                $("#carId"+wayBillIndex).val(item.id)
                return item.name;
            },
            /**
             * 使用指定的方式，高亮(指出)匹配的部分
             *
             * @param obj 数据源中返回的单个实例
             * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
             */
            highlighter: function (obj) {
                var item = JSON.parse(obj);
                var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
                return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                    return '<strong>' + match + '</strong>'
                });
            },
        });
        /**
         * 车主
         */
        $("#vehicleOwnerName"+wayBillIndex).typeahead({
            items: 'all', //展示全部 不设置默认展示8个
            source: function (query, process) {
                return $.ajax({
                    url: prefix + "/getVehicleOwners",
                    type: 'get',
                    data: {vehicleOwnerNames: $("#vehicleOwnerName"+wayBillIndex).val()},
                    success: function (result) {
                        $("#vehicleOwnerId"+wayBillIndex).val("")

                        var resultList = result.map(function (item) {
                            var aItem = {id: item.id, name: item.vehicleOwnerName};
                            return JSON.stringify(aItem);
                        });
                        return process(resultList);
                    },
                });
            },
            /**
             * 在选中数据后的操作，这里的返回值代表了输入框的值
             *
             * @param obj
             * @return 选中后，最终输入框里的值
             */
            updater: function (obj) {
                var item = JSON.parse(obj);
                $("#vehicleOwnerId"+wayBillIndex).val(item.id)
                return item.name;
            },
            /**
             * 使用指定的方式，高亮(指出)匹配的部分
             *
             * @param obj 数据源中返回的单个实例
             * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
             */
            highlighter: function (obj) {
                var item = JSON.parse(obj);
                var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
                return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                    return '<strong>' + match + '</strong>'
                });
            },
        });
    }

    function removeRowThree(obj, index) {
        $(obj).parent().parent().remove()
        //删除之后  改变后面元素的index
    }

    /**
     * 计算合计
     */
    function calculateUnitPriceTotals(obj,index) {
        var tonnage = $("#tonnage"+index).val()//获取吨位
        var unitPrice = $("#unitPrice"+index).val()//获取单价

        if(tonnage != null && tonnage != '' && unitPrice != null && unitPrice != '') {
            $("#freight"+index).val(tonnage*unitPrice)
            $("#paidFreight"+index).val(tonnage*unitPrice)
        }
    }

    /**
     * 计算合计
     */
    function calculateByTonnageTotals(obj,index) {
        console.log(index)
        var tonnage = $("#tonnage"+index).val()//获取吨位
        var unitPrice = $("#unitPrice"+index).val()//获取单价

        if(tonnage != null && tonnage != '' && unitPrice != null && unitPrice != '') {
            $("#freight"+index).val(tonnage*unitPrice)
            $("#paidFreight"+index).val(tonnage*unitPrice)
        }
    }

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#orderDate0").removeAttr("lay-key");
        $("#orderDate1").removeAttr("lay-key");
        $("#paymentTime0").removeAttr("lay-key");
        $("#paymentTime1").removeAttr("lay-key");
        laydate.render({
            elem: '#receiptDate'
            , type: 'datetime'
        });

        laydate.render({
            elem: '#issueDate'
            , type: 'datetime'
        });

        laydate.render({
            elem: '#dueDate'
            , type: 'datetime'
        });
    })

    /**
     * 校验
     */
    $("#form-book-add").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            reqDeliDate:{
                required:true,
            },
            reqArriDate:{
                required:true,
            },
            custName:{
                required:true,
            },
            balaName:{
                required:true,
            },
            appDeliMobile:{
                isPhone:true
            }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            //commit();
            $.operate.saveTab(prefix + "/addDraft", $('#form-draft-add').serialize());
            //jQuery.subscribe("cmt", commit);
        }
    }

    function commit() {
        console.log(JSON.stringify($('#form-book-add').serializeArray()))
        console.log("===========")
        console.log($('#form-book-add').serializeArray())
        $.ajax({
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            url: prefix + "/addParkWayBill",
            data: JSON.stringify($('#form-book-add').serializeArray()),//JSON.stringify(data.field),
            dataType: 'json',
            success: function(result) {
                console.log(result)
                // if(result.code == 0) {
                //     layer.msg('注册成功！');
                // } else {
                //     layer.msg(result.msg);
                // }
            }
        });

        //$.operate.saveTab(prefix + "/addParkWayBill", $('#form-invoice-add').serialize());
    }

    //转换省市区名称
    function changePcaNmae(){
        //基本信息转换省市区保存name
        var provinceName = $("#provinceId0").find(":selected").text();
        if(provinceName === '-- 请选择 --'){
            $("#provinceName0").val("");
        }else{
            $("#provinceName0").val(provinceName);
        }
        var cityName = $("#cityId0").find(":selected").text();
        if(cityName === '-- 请选择 --'){
            $("#cityName0").val("");
        }else{
            $("#cityName0").val(cityName);
        }
        var areaName = $("#areaId0").find(":selected").text();
        if(areaName === '-- 请选择 --'){
            $("#areaName0").val("");
        }else{
            $("#areaName0").val(areaName);
        }
        //关注线路省市区名称
        for (var i = 0; i <= wayBillIndex+1; i++) {
            var deliProvinceId = "deliProvinceId" + i;
            var deliCityId = "deliCityId" + i;
            var deliAreaId = "deliAreaId" + i;
            //发货方
            var deliProvinceName = $("#" + deliProvinceId).find(":selected").text();
            if(deliProvinceName === '-- 请选择 --'){
                $("#deliProvinceName" + i).val("");
            }else{
                $("#deliProvinceName" + i).val(deliProvinceName);
            }
            //$("#deliProvinceName" + i).val($("#" + deliProvinceId).find(":selected").text());

            var deliCityName = $("#" + deliCityId).find(":selected").text();
            if(deliCityName === '-- 请选择 --'){
                $("#deliCityName" + i).val("");
            }else{
                $("#deliCityName" + i).val(deliCityName);
            }

            var deliAreaName =  $("#" + deliAreaId).find(":selected").text();
            if(deliAreaName === '-- 请选择 --'){
                $("#deliAreaName" + i).val("");
            }else{
                $("#deliAreaName" + i).val(deliAreaName);
            }
            //收货方
            var arriProvinceId = "arriProvinceId" + i;
            var arriCityId = "arriCityId" + i;
            var arriAreaId = "arriAreaId" + i;

            var arriProvinceName = $("#" + arriProvinceId).find(":selected").text();
            if(arriProvinceName === '-- 请选择 --'){
                $("#arriProvinceName" + i).val("");
            }else{
                $("#arriProvinceName" + i).val(arriProvinceName);
            }

            var arriCityName = $("#" + arriCityId).find(":selected").text();
            if(arriCityName === '-- 请选择 --'){
                $("#arriCityName" + i).val("");
            }else{
                $("#arriCityName" + i).val(arriCityName);
            }

            var arriAreaName =  $("#" + arriAreaId).find(":selected").text();
            if(arriAreaName === '-- 请选择 --'){
                $("#arriAreaName" + i).val("");
            }else{
                $("#arriAreaName" + i).val(arriAreaName);
            }
        }

    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }
</script>
</body>

</html>