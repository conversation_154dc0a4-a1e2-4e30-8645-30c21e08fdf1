<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('汇票贴现')" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 80px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .disin{
        display: inline-block;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .infotitle{
        line-height: 30px;
        font-weight: bold;
    }
    .titlebg{
        background: #eff3f9;
        padding: 5px 10px;

    }
    a.del-alink {
        display: block;
        margin: 120px auto 0;
    }
    .bggray{
        background: #f7f8fa;
    }
</style>
<body>
<!--<div class="form-content">-->
<!--    <form id="form-book-add" class="form-horizontal" novalidate="novalidate">-->
<!--        <input id="id" th:value="${draft.id}" name="parkWaybillList[0].id" type="hidden">-->
<!--        &lt;!&ndash;要求提货日期&ndash;&gt;-->
<!--        <div class="panel-group" id="accordion">-->
<!--            <div class="panel panel-default">-->
<!--                <div id="collapseOne" class="panel-collapse collapse in">-->
<!--                    <div class="panel-body">-->
<!--                        <div class="titlebg over">-->
<!--                            &lt;!&ndash;                            <div class="fl">&ndash;&gt;-->
<!--                            &lt;!&ndash;                                <a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow()" title="新增行">+</a>&ndash;&gt;-->
<!--                            &lt;!&ndash;                            </div>&ndash;&gt;-->
<!--                            <div class="fl fw ml10" style="line-height: 26px">汇票</div>-->
<!--                        </div>-->
<!--                        <div class="over">-->
<!--                            &lt;!&ndash;                            <div class="fl" style="width: 40px">&ndash;&gt;-->
<!--                            &lt;!&ndash;                                <a class="close-link del-alink" style="background: #fd8481;border-radius: 50%" onclick="removeRowThree(this,0)" title="删除选择行">x</a>&ndash;&gt;-->
<!--                            &lt;!&ndash;                            </div>&ndash;&gt;-->
<!--                            <div class="fr" style="width: calc(100% - 50px)">-->
<!--                                &lt;!&ndash; 台账begin  发货地 收货地&ndash;&gt;-->
<!--                                <div class="infotitle">汇票信息</div>-->
<!--                                <div class="row no-gutter">-->
<!--                                    <div class="col-md-5 col-sm-5">-->
<!--                                        <div class="col-sm-6">-->
<!--                                            <div class="flex">-->
<!--                                                <label class="flex_left"><span class="fcff">*</span> 项目组：</label>-->
<!--                                                <div class="flex_right">-->
<!--                                                    <input type="text" placeholder="请输入项目组" class="form-control" name="projectTeam" id="projectTeam" maxlength="50" autocomplete="off" th:value="${draft.projectTeam}"/>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                        <div class="col-sm-6">-->
<!--                                            <div class="flex">-->
<!--                                                <label class="flex_left"><span class="fcff">*</span> 收票日期：</label>-->
<!--                                                <div class="flex_right">-->
<!--                                                    <input type="text" class=" form-control" id="receiptDate" name="receiptDate" required-->
<!--                                                           placeholder="收票日期" lay-key="1" autocomplete="off" readonly th:value="${#dates.format(draft.receiptDate, 'yyyy-MM-dd HH:mm:ss')}">-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                    <div class="col-md-7 col-sm-7">-->
<!--                                        <div class="col-sm-4">-->
<!--                                            <div class="flex">-->
<!--                                                <label class="flex_left"><span class="fcff">*</span> 票据类型：</label>-->
<!--                                                <div class="flex_right">-->
<!--                                                    <input name="billType" id="billType" placeholder="请输入票据类型" class="form-control" type="text" maxlength="50" autocomplete="off" th:value="${draft.billType}">-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                        <div class="col-sm-4">-->
<!--                                            <div class="flex">-->
<!--                                                <label class="flex_left"><span class="fcff">*</span> 票号：</label>-->
<!--                                                <div class="flex_right">-->
<!--                                                    <input name="ticketNumber" id="ticketNumber" placeholder="请输入票号" class="form-control" type="text" maxlength="50" autocomplete="off" th:value="${draft.ticketNumber}">-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                        <div class="col-sm-4">-->
<!--                                            <div class="flex">-->
<!--                                                <label class="flex_left"><span class="fcff">*</span> 出票日：</label>-->
<!--                                                <div class="flex_right">-->
<!--                                                    <input name="issueDate" id="issueDate" placeholder="请输入出票日" class="form-control" type="text" maxlength="50" autocomplete="off" th:value="${#dates.format(draft.issueDate, 'yyyy-MM-dd HH:mm:ss')}">-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                <div class="row no-gutter">-->
<!--                                    <div class="col-md-3  col-sm-4">-->
<!--                                        <div class="flex">-->
<!--                                            <label class="flex_left"><span class="fcff">*</span> 到期日：</label>-->
<!--                                            <div class="flex_right">-->
<!--                                                <input name="dueDate" id="dueDate" placeholder="请输入到期日" class="form-control" type="text" maxlength="50" autocomplete="off" th:value="${#dates.format(draft.dueDate, 'yyyy-MM-dd HH:mm:ss')}">-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                    <div class="col-md-3 col-sm-4">-->
<!--                                        <div class="flex">-->
<!--                                            <label class="flex_left"><span class="fcff">*</span> 付票单位：</label>-->
<!--                                            <div class="flex_right">-->
<!--                                                <input name="payCompany" id="payCompany" placeholder="请输入付票单位" class="form-control" type="text"-->
<!--                                                       maxlength="50" autocomplete="off" th:value="${draft.payCompany}">-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                    <div class="col-md-3 col-sm-4">-->
<!--                                        <div class="flex">-->
<!--                                            <label class="flex_left"><span class="fcff">*</span> 出票行：</label>-->
<!--                                            <div class="flex_right">-->
<!--                                                <input name="issuingBank" id="issuingBank" placeholder="请输入出票行" class="form-control" type="text" maxlength="50" autocomplete="off"-->
<!--                                                       th:value="${draft.issuingBank}">-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                    <div class="col-md-3 col-sm-4">-->
<!--                                        <div class="flex">-->
<!--                                            <label class="flex_left"><span class="fcff">*</span> 金额：</label>-->
<!--                                            <input name="amountMoney" id="amountMoney" placeholder="请输入金额" class="form-control" type="text" maxlength="50" autocomplete="off" th:value="${draft.amountMoney}" oninput="$.numberUtil.onlyNumberTwoDecimal(this)">-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                                &lt;!&ndash; 备注 &ndash;&gt;-->
<!--                                <div class="row no-gutter">-->
<!--                                    <div class="col-sm-12">-->
<!--                                        <div class="flex">-->
<!--                                            <label class="flex_left">备注：</label>-->
<!--                                            <div class="flex_right">-->
<!--                                            <textarea name="remark" id="remark" maxlength="200" class="form-control valid" th:text="${draft.remark}"-->
<!--                                                      rows="2"></textarea>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </form>-->
<!--</div>-->
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-draft-edit">
        <input id="draftId" th:value="${draft.id}" name="draftId" type="hidden">
        <input id="type" th:value="0" name="type" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label ">余额：</label>
            <div class="col-sm-8">
                <span class="form-control" >¥[[${draft.lessAmount}]]</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label "><span style="color:red">*</span>贴现日期：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" readonly="true" name="operateTime" id="operateTime" required/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label "><span style="color:red">*</span>贴现金额：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="amount" id="amount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" required/>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea name="remark" id="remark" maxlength="200" class="form-control valid"
                          rows="2"></textarea>
            </div>
        </div>
    </form>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-typeahead-js"/>

<script th:inline="javascript">

    var prefix = ctx + "finance/draft";

    let draft = [[${draft}]];

    $(function () {

    });

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        laydate.render({
            elem: '#operateTime'
            , type: 'datetime'
        });
    })

    /**
     * 校验
     */
    $("#form-book-add").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            reqDeliDate:{
                required:true,
            },
            reqArriDate:{
                required:true,
            },
            custName:{
                required:true,
            },
            balaName:{
                required:true,
            },
            appDeliMobile:{
                isPhone:true
            }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            //commit();
            $.operate.save(prefix + "/savePayRecord", $('#form-draft-edit').serialize());
            //jQuery.subscribe("cmt", commit);
        }
    }

    function submitAll(){
        let lessAmount = draft.lessAmount;
        $("#amount").val(lessAmount);
        submitHandler();
    }

    function commit() {
        console.log(JSON.stringify($('#form-book-add').serializeArray()))
        console.log("===========")
        console.log($('#form-book-add').serializeArray())
        $.ajax({
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            url: prefix + "/addParkWayBill",
            data: JSON.stringify($('#form-book-add').serializeArray()),//JSON.stringify(data.field),
            dataType: 'json',
            success: function(result) {
                console.log(result)
                // if(result.code == 0) {
                //     layer.msg('注册成功！');
                // } else {
                //     layer.msg(result.msg);
                // }
            }
        });

        //$.operate.saveTab(prefix + "/addParkWayBill", $('#form-invoice-add').serialize());
    }
    // function commit() {
    //     alert("2")
    //     var dis = $(":disabled");
    //     dis.attr("disabled", false);
    //     var data = $("#form-invoice-add").serializeArray();
    //
    //     $.operate.saveTab(prefix + "/addParkWayBill", data,function (result) {
    //         if (result.code != 0) {
    //             dis.attr("disabled", true);
    //         }
    //     });
    // }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }
</script>
</body>

</html>