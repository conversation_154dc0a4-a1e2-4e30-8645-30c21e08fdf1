<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('应收月度对账列表')" />
	<th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
	.table-striped{
		height: calc(100% - 120px);
	}
</style>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal">
					<div class="row">
						<div class="col-md-3 col-sm-4">
							<div class="form-group">
								<label class="col-sm-4">关账年份：</label>
								<div class="col-sm-8">
									<input name="year" id="year" placeholder="请输入关账年份" class="form-control" type="text"
										   aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-md-3 col-sm-4">
							<div class="form-group">
								<label class="col-sm-4">关账月份：</label>
								<div class="col-sm-8">
									<input name="month" id="month" placeholder="请输入关账年份" class="form-control" type="text"
										   aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
<!--								<label class="col-sm-4">客户名称：</label>-->
								<div class="col-sm-12">
									<input name="custName" id="custName" placeholder="请输入客户名称" class="form-control" type="text"
										     aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
<!--								<label class="col-sm-4">运营组：</label>-->
								<div class="col-sm-12">
									<select name="salesDeptId" id="salesDeptId" class="form-control valid selectpicker"
											aria-invalid="false" data-none-selected-text="运营组" multiple>
										<option value="">--运营组--</option>
										<option th:each="mapS,status:${salesDept}"
												th:value="${mapS.deptId}"
												th:text="${mapS.deptName}">
										</option>
									</select>
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
<!--								<label class="col-sm-6"></label>-->
								<a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>

				</form>
			</div>
			
	        <div class="btn-group-sm" id="toolbar" role="group">
				<a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="finance:receMonthCheckSheet:export">
					<i class="fa fa-download"></i> 导出
				</a>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
			</div>
		</div>
	</div>
    <div th:include="include :: footer"></div>
	 <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var prefix = ctx + "finance/receMonthCheckSheet";

        //合计
        var sumTotalAmount = 0;
        var sumGotAmount = 0;
        var sumInvoicedAmount = 0;
        var sumOpeningAmount = 0;

        var myDate = new Date();
        //获取当前年
        var year = myDate.getFullYear();
        $("#year").val(year);
        //获取当前月
        var month = myDate.getMonth();
        if(month == 0){
            year = year -1;
            month = 12;
        }
        $("#month").val(month);


        $(function() {
            //监听回车事件 回车搜索
            $(document).keyup(function(e){
                var key = e.which;
                if(key==13){
                    $.table.search();
                }
            });
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
				exportUrl: prefix + "/export",
                modalName: "应收月度对账",
		        showExport: true,
                showToggle:false,
                showColumns:false,
                clickToSelect:true,
                showFooter:true,
                onPostBody:function () {
                    //合并页脚
                    merge_footer();
                    //查询合计总金额
                    getAmountCount();
                },
                onRefresh:function(params){
                    clearTotal();
                },
                onCheck: function (row,$element) {
                    addTotal(row);
                    setTotal();
                },
                onUncheck: function (row, $element) {
                    subTotal(row);
                    setTotal();
                },
                onCheckAll: function (rowsAfter) {
                    clearTotal();
                    //循环累加
                    for (var row of rowsAfter) {
                        addTotal(row);
                    }
                    //赋值
                    setTotal();
                },
                onUncheckAll: function () {
                    //总数清0
                    clearTotal();
                    //赋值
                    setTotal();
                },
                columns: [{
		            checkbox: true,
                    footerFormatter: function (row) {
                        return "当月应收:<nobr id='sumTotalAmount'>￥0</nobr>&nbsp&nbsp"
                            + "当月收款:<nobr id='sumGotAmount'>￥0</nobr>&nbsp&nbsp"
                            + "当月开票:<nobr id='sumInvoicedAmount'>￥0</nobr>&nbsp&nbsp"
                            + "期末金额:<nobr id='sumOpeningAmount'>￥0</nobr><br>"+
                            "总合计：当月应收：<nobr id='countTotalAmount'>￥0</nobr>&nbsp&nbsp" +
                            "当月收款：<nobr id='countGotAmount'>￥0</nobr>&nbsp&nbsp" +
                            "当月开票：<nobr id='countInvoicedAmount'>￥0</nobr>&nbsp&nbsp" +
                            "期末金额：<nobr id='countOpeningAmount'>￥0</nobr>&nbsp&nbsp";
                    }
		        },
				{
					field : 'year', 
					title : '关账年份',
				},
				{
					field : 'month',
					title : '关账月份',
				},
				{
					field : 'custCode', 
					title : '客户编码',
				},
				{
					field : 'custName', 
					title : '客户名称',
				},
				{
					field : 'salesDeptName',
					title : '运营组名称',
				},
				{
					field : 'totalAmount', 
					title : '当月应收',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
				},
				{
					field : 'gotAmount', 
					title : '当月收款',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
				},
				{
					field : 'invoicedAmount',
					title : '当月开票',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
				},
				{
					field : 'openingAmount',
					title : '期末金额',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
				},
				{
					field : 'regDate', 
					title : '创建时间',
				},
		       ]
            };
            $.table.init(options);
        });

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            laydate.render({
                elem: '#month',
                type: 'month',
                format:"MM"
            });
            laydate.render({
                elem: '#year',
                type: 'year'
            });
        });

        /**
         * 搜索
         */
        function searchPre() {
            var data = {};
            data.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
            $.table.search('role-form', data);
        }

        /**
         * 重置
         */
        function resetPre() {
            $(".selectpicker").selectpicker('deselectAll');
            $("#role-form")[0].reset();
            searchPre();
        }

        /**
         * 将总计金额清零
         */
        function clearTotal() {
            sumTotalAmount = 0;
            sumGotAmount = 0;
            sumInvoicedAmount = 0;
            sumOpeningAmount = 0;
        }

        /**
         * 累计总金额
         */
        function addTotal(row) {
            sumTotalAmount = sumTotalAmount + row.totalAmount;
            sumGotAmount = sumGotAmount + row.gotAmount;
            sumInvoicedAmount = sumInvoicedAmount + row.invoicedAmount;
            sumOpeningAmount = sumOpeningAmount + row.openingAmount;
        }

        function subTotal(row) {
            sumTotalAmount = sumTotalAmount - row.totalAmount;
            sumGotAmount = sumGotAmount - row.gotAmount;
            sumInvoicedAmount = sumInvoicedAmount - row.invoicedAmount;
            sumOpeningAmount = sumOpeningAmount - row.openingAmount;
        }

        /**
         *
         * 给页脚总计赋值
         */
        function setTotal() {
            $("#sumTotalAmount").text(sumTotalAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            $("#sumGotAmount").text(sumGotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            $("#sumInvoicedAmount").text(sumInvoicedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            $("#sumOpeningAmount").text(sumOpeningAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        }

        /**
         * 合并页脚
         */
        function merge_footer() {
            var footer_tbody = $('.fixed-table-footer table tbody');
            var footer_tr = footer_tbody.find('>tr');
            var footer_td = footer_tr.find('>td');
            var footer_td_1 = footer_td.eq(0);
            //除了第一列其他都隐藏
            for(var i=1;i<footer_td.length;i++) {
                footer_td.eq(i).hide();
            }
            footer_td_1.attr('colspan', 1).show();
        }

        /**
         * 获取所有数据金额合计
         */
        function getAmountCount() {
            var data = $.common.formToJSON("role-form");
            $.ajax({
                url: prefix + "/getCount",
                type: "post",
                dataType: "json",
                data: data,
                success: function(result) {
                    if (result.code == 0) {
                        var data = result.data;
                        $("#countTotalAmount").text(data.countTotalAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        $("#countGotAmount").text(data.countGotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        $("#countInvoicedAmount").text(data.countInvoicedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        $("#countOpeningAmount").text(data.countOpeningAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    }
                }
            });
        }
    </script>


</body>
</html>