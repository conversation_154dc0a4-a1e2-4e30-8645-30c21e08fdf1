<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收入核销记录')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
    <th:block th:include="include :: bootstrap-editable-css" />
</head>
<style>
</style>
<body class="gray-bg">

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: ztree-js" />
<th:block th:include="include :: bootstrap-table-editable-js"/>
<script th:inline="javascript">
    //字典数据  结算公司
    var tariffId = [[${tariffId}]];
    var type = [[${type}]];

    var prefix = ctx + "finance/tariff";

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 185 });
        // 初始化省市区
        //$.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        //$.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        queryStorageCapacityList();
        $.table.hideColumn("id");
    });
    // var selectTonnageCount = 0;
    // var selectFreightCount = 0;
    // var selectYishouCount = 0;
    function queryStorageCapacityList() {
        var options = {
            url: prefix + "/incomeRecordList?tariffId="+tariffId+"&type="+type,
            showToggle:false,
            showColumns:false,
            sortName: "regDate",
            sortOrder: "desc",
            modalName: "收入核销记录",
            fixedColumns: true,
            showFooter:true,
            showSearch: false,
            showRefresh: false,
            //fixedNumber:5,
            clickToSelect: true,
            columns: [
                {
                    title: '主键',
                    align: 'left',
                    field : 'id'  //主键加载完成就隐藏  多选删除的时候是按照第一列的值是否相同来判断一共有几项的
                },
                {
                    title: '金额',
                    align: 'left',
                    field : 'amount',
                    formatter: function (value, row, index) {
                        if (value == null) {
                            return '¥0.00';
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '收款账户',
                    align: 'left',
                    field : 'collectionAccount'
                },
                {
                    title: '备注',
                    align: 'left',
                    field : 'remark'
                },
                {
                    title: '收款时间',
                    align: 'left',
                    field : 'regDate'
                },
                {
                    title: '收款人',
                    align: 'left',
                    field : 'regUserName'
                }
            ]
        };
        $.table.init(options);
    }
</script>
</body>
</html>