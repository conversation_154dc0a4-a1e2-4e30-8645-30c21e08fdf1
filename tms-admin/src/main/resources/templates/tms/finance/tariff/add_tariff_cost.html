<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('成本核销')"/>
</head>
<style>
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .ml10{
        margin-left: 10px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-tariff-add" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <!-- 基础信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">成本核销记录</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="mt20" style="padding-bottom: 10px">
                            <table class="table table-bordered">
                                <thead style="background: #F7F8FA">
                                <tr>
                                    <th>承运商实付金额(¥)</th>
                                    <th>类型</th>
                                    <th>现金/油卡金额(¥)</th>
                                    <th>承运商账户</th>
                                    <th>卡号</th>
                                    <th>银行</th>
                                    <th>备注</th>
                                    <th>核销时间</th>
                                    <th>核销人</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="mapS,status:${writeOffList}">
                                    <td>
                                        <div class="input-group" th:text="${mapS.amount}"></div>
                                    </td>
                                    <td >
                                        <div class="input-group" th:if="${mapS.costVerficationType == 1}">现金</div>
                                        <div class="input-group" th:if="${mapS.costVerficationType == 2}">油卡</div>
                                    </td>
                                    <td >
                                        <div class="input-group" th:text="${mapS.crash}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" th:text="${mapS.carrierAccount}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" th:text="${mapS.cardNumber}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" th:text="${mapS.bank}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" th:text="${mapS.remark}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" th:text="${#dates.format(mapS.regDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" th:text="${mapS.regUserName}"></div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
            <!-- 运输跟踪 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">成本核销</a>
                    </h4>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--力资id-->
                        <input type="hidden" name="tariffId" th:value="${tariff.tariffId}">
                        <!-- 类型1:收入 2:成本 -->
                        <input type="hidden" name="type" value="2">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span style="">承运商实付金额：</span></label>
                                    <div class="col-sm-8">
                                        <input name="amount" id="amount"  class="form-control"
                                               type="text" min="0" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" maxlength="15" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span style="">现金/油卡</span></label>
                                    <div class="col-sm-8">
                                        <select name="costVerficationType" id="costVerficationType" class="form-control valid" aria-invalid="false"  aria-required="true" required>
                                            <option value="">-- 请选现金/油卡 --</option>
                                            <option value="1">现金</option>
                                            <option value="2">油卡</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span style="">现金/油卡金额：</span></label>
                                    <div class="col-sm-8">
                                        <input name="crash" id="crash"  class="form-control"
                                               type="text" min="0" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" maxlength="15" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span style="">承运商账户</span></label>
                                    <div class="col-sm-8">
                                        <input name="carrierAccount" id="carrierAccount"  class="form-control"
                                               type="text" maxlength="50" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span style="">卡号</span></label>
                                    <div class="col-sm-8">
                                        <input name="cardNumber" id="cardNumber"  class="form-control"
                                               type="text" maxlength="50" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span style="">银行</span></label>
                                    <div class="col-sm-8">
                                        <input name="bank" id="bank"  class="form-control"
                                               type="text" maxlength="50" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span style="">备注：</span></label>
                                    <div class="col-sm-8">
                                        <input name="remark" id="remark"  class="form-control"
                                               type="textarea"  autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "finance/tariff";

    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-tariff-add").serializeArray();
            $.operate.saveTab(prefix + "/saveCostHexiao", data);
        }
    }
</script>
</body>
</html>