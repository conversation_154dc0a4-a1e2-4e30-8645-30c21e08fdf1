<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('其他力资费')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
    <th:block th:include="include :: bootstrap-editable-css" />
</head>
<style>
    .row-margin-top {
        margin-top: 10px;
    }
    /*.select-table table {*/
    /*    table-layout:fixed;*/
    /*}*/
    .select-table .table td {
        /* 超出部分隐藏 */
        overflow:hidden;
        /* 超出部分显示省略号 */
        text-overflow:ellipsis;
        /*规定段落中的文本不进行换行 */
        white-space:nowrap;
        /* 配合宽度来使用 */
        height:40px;
    }
</style>
<body class="gray-bg">

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="park-form">
                    <input type="hidden" id="parentId" name="parentId">

                    <div class="row">
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">发货单编号：</label>-->
                                <div class="col-sm-12">
                                    <select name="balaCorp" id="balaCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" required>
                                        <option value="">---请选择结算公司---</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户发货单号：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入客户名称"  name="custName">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">车长：</label>-->
                                <div class="col-sm-12">
                                    <select name="revenueStatus" id="revenueStatus" class="form-control valid" aria-invalid="false"  aria-required="true" required>
                                        <option value="">-- 请选择收入核销状态 --</option>
                                        <option value="1">未核销</option>
                                        <option value="2">部分核销</option>
                                        <option value="3">已核销</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">车长：</label>-->
                                <div class="col-sm-12">
                                    <select name="costStatus" id="costStatus" class="form-control valid" aria-invalid="false"  aria-required="true" required>
                                        <option value="">-- 请选择成本核销状态 --</option>
                                        <option value="1">未核销</option>
                                        <option value="2">部分核销</option>
                                        <option value="3">已核销</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-8">
                            <div class="form-group">
                                <!--                            <label class="col-sm-5">要求提货日期：</label>-->
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-addon">从</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 申请开始时间" style="font-size: 14px" id="starttime" name="startTime" >
                                        <span class="input-group-addon">到</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 申请结束时间" style="font-size: 14px" id="endtime" name="endTime">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row row-margin-top">
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户发货单号：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入收款申请单号"  name="vbillno">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-10">

                        </div>
                        <div class="col-sm-2">
                            <label class="col-sm-4"></label>
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-danger multiple disabled" onclick="ensureMore()" shiro:hasPermission="tms:finance:tariff:ensure">
                    <i class="fa fa-check"></i> 确认
                </a>
<!--                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="tms:park_data:waybill:import">-->
<!--                    <i class="fa fa-upload"></i> 导入-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:finance:tariff:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: ztree-js" />
<th:block th:include="include :: bootstrap-table-editable-js"/>
<script th:inline="javascript">
    //字典数据  结算公司
    var datas = [[${@dict.getType('bala_corp')}]];

    var editFlag = [[${@permission.hasPermi('tms:finance:tariff:editCost')}]];

    var saveIncomeFlag = [[${@permission.hasPermi('tms:finance:tariff:toSaveIncome')}]];
    var saveCostFlag = [[${@permission.hasPermi('tms:finance:tariff:toSaveCost')}]];
    var incomeListFlag = [[${@permission.hasPermi('tms:finance:tariff:incomelist')}]];
    var costListFlag = [[${@permission.hasPermi('tms:finance:tariff:costlist')}]];

    var removeFlag = [[${@permission.hasPermi('tms:park_data:waybill:remove')}]];
    var prefix = ctx + "finance/tariff";

    //合计总金额
    var amountTotalSelect = 0;
    var incomeSumTotalSelect = 0;
    var costTotalSelect = 0;
    var costSumTotalSelect = 0;

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#starttime").removeAttr("lay-key");
        $("#endtime").removeAttr("lay-key");
        laydate.render({
            elem: '#endtime'
            , type: 'date'
        });

        laydate.render({
            elem: '#starttime'
            , type: 'date'
        });
        laydate.render({
            elem: '#payStartTime'
            , type: 'date'
        });

        laydate.render({
            elem: '#payEndTime'
            , type: 'date'
        });
    })

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 185 });
        // 初始化省市区
        //$.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        //$.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        queryStorageCapacityList();
        $.table.hideColumn("tariffId");
    });

    //确认多个
    function ensureMore() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要改变选中的" + rows.length + "条数据的状态吗?", function() {
            var url = $.table._option.removeUrl;
            var data = { "ids": rows.join() };
            $.operate.submit(prefix + "/ensureMoreTariffStatus", "post", "json", data);
        });
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    // function getAmountCount() {
    //     var data = $.common.formToJSON("park-form");
    //     $.ajax({
    //         url: prefix + "/getCount",
    //         type: "post",
    //         dataType: "json",
    //         data: data,
    //         success: function(result) {
    //             var data = result;
    //             $("#tonnageCount").text(data.TONNAGECOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    //             $("#freightCount").text(data.FREIGHTCOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    //             $("#yishouCount").text(data.YISHOUCOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    //         }
    //     });
    // }
    /**
     * 跳转收入核销
     */
    function amountHexiao(tariffId) {
        $.modal.openTab("收入核销", prefix + "/addTariffAmount/"+tariffId,450,400);
    }
    /**
     * 跳转成本核销
     */
    function costHexiao(tariffId) {
        $.modal.openTab("成本核销", prefix + "/addTariffCost/"+tariffId,450,450);
    }

    function incomeHistory(tariffId) {
        var url = prefix + "/incomeRecord?tariffId="+tariffId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "收入核销记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    function costHistory(tariffId) {
        var url = prefix + "/costRecord?tariffId="+tariffId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "成本核销记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 收入/成本确认
     */
    function checkCost(tariffId) {
        $.modal.confirm("一旦收入、成本确认, 无法修改!", function() {
            var url = prefix + "/ensureTariffStatus";
            var data = { "tariffId": tariffId,"tariffStatus": "2" };
            $.operate.submit(url, "post", "json", data);
        });
    }

    //初始化查询条件传参
    queryParams = function(params) {
        var search = {};
        $.each($("#park-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    function onlyNumberTwoDecimal (obj) {
       regExp =  /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(obj)
        return regExp
    }

    function queryStorageCapacityList() {
        var options = {
            url: prefix + "/tariffPage",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showFooter:true,
            showColumns:false,
            sortName: "regDate",
            sortOrder: "desc",
            modalName: "力资费",
            //fixedNumber:5,
            showExport: true,
            clickToSelect: true,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"力资费列表"
            },
            queryParams: queryParams,
            onEditableSave: function (field, row, oldValue, $el) {
                if(row.costStatus == 3) {
                    $.modal.alertWarning("力资费用已核销，无法调整！");
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                    return;
                }
                if (field === 'cost') {
                    let data = {tariffId: row.tariffId, cost: row.cost};
                    $.ajax({
                        url: prefix + "/editTariffCost",
                        type: "post",
                        dataType: "json",
                        data: data,
                        success: function (result) {
                            if (result.code === 0) {
                                var data = result.data;
                                //刷新
                                $.btTable.bootstrapTable('refresh', {
                                    silent: true
                                });
                            } else {
                                $.modal.msgError(result.msg);
                                //刷新
                                $.btTable.bootstrapTable('refresh', {
                                    silent: true
                                });
                            }
                        }
                    });
                }
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onPostBody: function () {
                clearTotal();
                //合并页脚
                merge_footer();
                getAmountCount();
            },
            columns: [
                {
                    checkbox:true,
                    footerFormatter: function (row) {
                        return "已选总费用：<nobr id='amountTotalSelect'>¥0.00</nobr>" +
                            " 已选收入核销总金额：<nobr id='incomeSumTotalSelect'>¥0.00</nobr>" +
                            " 已选总成本：<nobr id='costTotalSelect'>¥0.00</nobr>" +
                            " 已选成本核销总金额：<nobr id='costSumTotalSelect'>¥0.00</nobr>" +
                            "<br>" +
                            "总合计：总费用：<nobr id='amountTotal'></nobr>" +
                            " 收入核销总金额：<nobr id='incomeSumTotal'></nobr>" +
                            " 总成本：<nobr id='costTotal'></nobr>" +
                            " 成本核销总金额：<nobr id='costSumTotal'></nobr>";
                    }
                },
                {
                    title: '主键',
                    align: 'left',
                    field : 'tariffId',  //主键加载完成就隐藏  多选删除的时候是按照第一列的值是否相同来判断一共有几项
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        // if ([[${@permission.hasPermi('tms:finance:tariff:ensure')}]] != "hidden") {
                        //     actions.push('<a class="btn btn-danger btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="checkCost(\'' + row.tariffId + '\')"><i class="fa fa-check"></i>确认</a> ');
                        // }
                        if ([[${@permission.hasPermi('tms:finance:tariff:toSaveIncome')}]] != "hidden" && row.tariffStatus != 1) {
                            actions.push('<a class="btn btn-warning btn-xs ' + saveIncomeFlag + '" href="javascript:void(0)" title="收入核销" ' +
                                'onclick="amountHexiao(\'' + row.tariffId + '\')"><i class="fa fa-sign-in"></i></a> ');
                        }
                        if ([[${@permission.hasPermi('tms:finance:tariff:toSaveCost')}]] != "hidden" && row.tariffStatus != 1) {
                            actions.push('<a class="btn btn-warning btn-xs ' + saveCostFlag + '" href="javascript:void(0)" title="成本核销" ' +
                                'onclick="costHexiao(\'' + row.tariffId + '\')"><i class="fa fa-sign-out"></i></a> ');
                        }
                        // if ([[${@permission.hasPermi('tms:finance:tariff:incomelist')}]] != "hidden") {
                        //     actions.push('<a class="btn btn-success btn-xs ' + incomeListFlag + '" href="javascript:void(0)" title="收入核销记录" onclick="incomeHistory(\'' + row.tariffId + '\')"><i class="fa fa-align-left"></i></a> ');
                        // }
                        // if ([[${@permission.hasPermi('tms:finance:tariff:costlist')}]] != "hidden") {
                        //     actions.push('<a class="btn btn-success btn-xs ' + costListFlag + '" href="javascript:void(0)" title="成本核销记录" onclick="costHistory(\'' + row.tariffId + '\')"><i class="fa fa-align-right"></i></a> ');
                        // }
                        // if ([[${@permission.hasPermi('tms:finance:tariff:detail')}]] != "hidden") {
                        //     actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" title="明细" onclick="detail(\'' + row.receSheetRecordId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        // }
                        return actions.join('');
                    }
                },
                {
                    title: '付款单号',
                    align: 'left',
                    field : 'paymentNo'
                },
                {
                    title: '申请单号',
                    align: 'left',
                    field : 'vbillno'
                },
                {
                    title: '发票号码',
                    align: 'left',
                    field : 'invoiceNo',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '结算公司',
                    align: 'left',
                    field : 'balaCorp',
                    formatter: function(value, item, index) {
                        return $.table.selectDictLabel(datas, item.balaCorp);
                    }
                },
                {
                    title: '客户名称',
                    align: 'left',
                    field : 'custName'
                },
                {
                    title: '力资费用状态',
                    align: 'left',
                    field : 'tariffStatus',
                    formatter: function (value, row, index) {
                        if (value === '1') {
                            return '新建';
                        }else if(value === '2') {
                            return '确认';
                        }
                    }
                },
                {
                    title: '费用（元）',
                    align: 'right',
                    field: 'amount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY', minimumFractionDigits: 2});
                    }
                },
                {
                    title: '收入核销总金额(元)',
                    align: 'right',
                    field: 'incomeSum',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY', minimumFractionDigits: 2});
                    }
                },
                {
                    title: '收入核销状态',
                    align: 'left',
                    field : 'revenueStatus',
                    formatter: function (value, row, index) {
                        if (value === '1') {
                            return '未核销';
                        }else if(value === '2') {
                            return '部分核销';
                        }else {
                            return '已核销';
                        }
                    }
                },
                {
                    title: '成本',
                    field: 'cost',
                    align: 'right',
                    editable:{
                        type: 'text',
                        title: '修改成本',
                        validate:  function (v) {
                            if (!onlyNumberTwoDecimal(v)) {
                                return "请输入正确的数字！";
                            }
                        }
                    }
                },
                {
                    title: '成本核销总金额(元)',
                    align: 'right',
                    field: 'costSum',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY', minimumFractionDigits: 2});
                    }
                },
                {
                    title: '成本核销状态',
                    align: 'left',
                    field : 'costStatus',
                    formatter: function (value, row, index) {
                        if (value === '1') {
                            return '未核销';
                        }else if(value === '2') {
                            return '部分核销';
                        }else {
                            return '已核销';
                        }
                    }
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field : 'regDate'
                },
                {
                    title: '申请人',
                    align: 'left',
                    field : 'userName'
                }
            ]
        };
        $.table.init(options);
    }
    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("park-form");
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;
                if (result.code == 0 && data != undefined) {
                    $("#amountTotal").text(data.AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#incomeSumTotal").text(data.INCOMESUM.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#costTotal").text(data.COST.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#costSumTotal").text(data.COSTSUM.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        //开票金额合计
        amountTotalSelect = 0;
        incomeSumTotalSelect = 0;
        costTotalSelect = 0;
        costSumTotalSelect = 0;
    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        amountTotalSelect = amountTotalSelect + (row.amount||0)
        incomeSumTotalSelect = incomeSumTotalSelect + (row.incomeSum||0)
        costTotalSelect = costTotalSelect + (row.cost||0)
        costSumTotalSelect = costSumTotalSelect + (row.costSum||0)
    }

    /**
     *  取消选中减少总金额
     */
    function subTotal(row) {
        amountTotalSelect = amountTotalSelect - (row.amount||0)
        incomeSumTotalSelect = incomeSumTotalSelect - (row.incomeSum||0)
        costTotalSelect = costTotalSelect - (row.cost||0)
        costSumTotalSelect = costSumTotalSelect - (row.costSum||0)
    }

    /**
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#amountTotalSelect").text(amountTotalSelect.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#incomeSumTotalSelect").text(incomeSumTotalSelect.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#costTotalSelect").text(costTotalSelect.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#costSumTotalSelect").text(costSumTotalSelect.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }
</script>
</body>
</html>