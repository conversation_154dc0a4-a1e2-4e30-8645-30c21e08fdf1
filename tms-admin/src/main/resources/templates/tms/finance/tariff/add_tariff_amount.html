<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收入核销')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style>
    label.invalid {
        background-color: rgb(237, 85, 101);
        color: #fff;
        line-height: 26px;
        height: 26px;
        font-size: 12px;
        padding: 0 5px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-tariff-add" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <!-- 基础信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">收入核销记录</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="mt20" style="padding-bottom: 10px">
                            <table class="table table-bordered">
                                <thead style="background: #F7F8FA">
                                <tr>
                                    <th>金额</th>
                                    <th>收款账户</th>
                                    <th>收款方式</th>
                                    <th>备注</th>
                                    <th>收款时间</th>
                                    <th>收款人</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="mapS,status:${writeOffList}">
                                    <td>
                                        <div class="input-group" th:text="${mapS.amount}"></div>
                                    </td>
                                    <td >
                                        <div class="input-group" th:text="${mapS.collectionAccount}"></div>
                                    </td>
                                    <!-- 字典回显 -->
                                    <td >
                                        <div class="input-group" th:text="${@dict.getLabel('receivable_method', mapS.receivableType)}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" th:text="${mapS.remark}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" th:text="${#dates.format(mapS.regDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" th:text="${mapS.regUserName}"></div>
                                    </td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
            <!-- 运输跟踪 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">收入核销</a>
                    </h4>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--力资id-->
                        <input type="hidden" name="tariffId" th:value="${tariff.tariffId}">
                        <!-- 类型1:收入 2:成本 -->
                        <input type="hidden" name="type" value="1">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red"><span style="">金额：</span></label>
                                    <div class="col-sm-8">
                                        <input name="amount" id="amount"  class="form-control" required  type="text" min="0"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);changeReceivableAmount()"
                                               maxlength="15" autocomplete="off" aria-required="true">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收款账户：</label>
                                    <div class="col-md-8 col-sm-8">
                                        <div class="input-group">
                                            <input name="collectionAccount" id="accountName" required class="form-control valid"
                                                   type="text" aria-required="true" maxlength="25">
                                            <!--账户id-->
                                            <input name="inAccountId" id="inAccountId"  class="form-control valid"
                                                   type="hidden" aria-required="true" maxlength="25">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="receivableType" id="receivableType" class="form-control" onchange="changeReceivableMethod()"
                                                th:with="type=${@dict.getType('receivable_method')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row" id="draftDiv" style="display:none">

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">票据号：</label>
                                    <div class="col-sm-8" >
                                        <!--                                        <input type="text" id="ticketNumber" name="ticketNumber" placeholder="票据号" class="form-control valid">-->
                                        <div class="input-group">
                                            <input class="form-control" type="text" id="ticketNumber" name="ticketNumber" placeholder="票据号">
                                            <input class="form-control" type="hidden" id="draftId" name="draftId">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-2" style="color: red">承兑金额：</label>
                                    <div class="col-sm-10">
                                        <span id="amountMoneySpan">0元（原汇票金额）</span> +
                                        <span id="amountMoneyAddSpan">0元</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">承兑类型：</label>
                                    <div class="col-sm-8">
                                        <select name="billTypeId" id="billTypeId" class="form-control"
                                                th:with="bill=${@dict.getType('draft_bill_type')}">
                                            <option value=""></option>
                                            <option th:each="billDict : ${bill}" th:text="${billDict.dictLabel}"  th:value="${billDict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">出票日期：</label>
                                    <div class="col-sm-8" >
                                        <input name="issueDate" id="issueDate" placeholder="请输入出票日" class="form-control"
                                               type="text" maxlength="50" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">到期日期：</label>
                                    <div class="col-sm-8" >
                                        <input name="dueDate" id="dueDate" placeholder="请输入到期日" class="form-control"
                                               type="text" maxlength="50" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">付票单位：</label>
                                    <div class="col-sm-8">
                                        <select name="payCompany" id="payCompany" class="form-control" onchange="changePayCompany()">
                                            <option value=""></option>
                                            <option th:each="custBilling : ${custBillings}"
                                                    th:data-bank="${custBilling.bank}"
                                                    th:text="${custBilling.billingPayable}"
                                                    th:value="${custBilling.billingPayable}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付票行：</label>
                                    <div class="col-sm-8">
                                        <input name="issuingBank" id="issuingBank" placeholder="付票行" class="form-control"
                                               type="text" maxlength="50" autocomplete="off">
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-2"><span style="">备注：</span></label>
                                    <div class="col-sm-10">
                                        <textarea name="remark" id="remark"  class="form-control" rows="2"
                                               autocomplete="off"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">
    var prefix = ctx + "finance/tariff";
    let balaCustomerId = [[${tariff.customerId}]]
    let billingCorp = [[${billingCorp}]]

    $(function () {
        $("#form-tariff-add").validate({
            focusCleanup: true
        });

        initBsSuggest()
    });

    /**
     * 关键字提示查询 转入账户
     */
    $("#accountName").bsSuggest('init', {
        url: ctx + "finance/account/findAccount?paymentType=0&keyword=",
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["accountName"],
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#accountName").val(data.accountName);
        $("#inAccountId").val(data.accountId);
    });


    /**
     * 初始化日期控件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#issueDate',
            type: 'datetime',
            trigger: 'click'
        });
        laydate.render({
            elem: '#dueDate',
            type: 'datetime',
            trigger: 'click'
        });
    });

    function changePayCompany() {
        var selectedOption = $('#payCompany').find('option:selected');
        var bankValue = selectedOption.data('bank');
        $('#issuingBank').val(bankValue);
    }

    function changeReceivableMethod() {
        let receivableType = $("#receivableType").val();

        if (receivableType == 10) {
            $('#draftDiv').show()
        }else {
            $('#draftDiv').hide()
        }

        // $("#form-batchRece-add").valid();
    }

    function changeReceivableAmount() {
        let val = $("#amount").val();
        $("#amountMoneyAddSpan").text(val + "元");
    }

    function initBsSuggest() {
        $(`#ticketNumber`).bsSuggest('init', {
            url: ctx + `finance/draft/listByTicketNumber?balaCustomerId=${balaCustomerId}&billingCorp=${billingCorp}&keyword=`,
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "id",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: [
                "ticketNumber",

                "billType",
                // "issueDate",
                // "dueDate",
                "payCompany",
                // "issuingBank",
                "balaCorp"
            ],
            effectiveFieldsAlias: {
                "ticketNumber": "票号",
                "billType": "承兑类型",
                // "issueDate": "出票日期",
                // "dueDate": "到期日期",
                "payCompany": "付票单位",
                // "issuingBank": "付票行"
                "balaCorp": "结算公司"
            },
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#draftId`).val(data.id);
            $(`#ticketNumber`).val(data.ticketNumber);
            $(`#billTypeId`).val(data.billTypeId);
            $(`#issueDate`).val(data.issueDate);
            $(`#dueDate`).val(data.dueDate);
            $(`#payCompany`).val(data.payCompany);
            $(`#issuingBank`).val(data.issuingBank);

            $("#amountMoneySpan").text(data.amountMoney + "元（原汇票金额）");
            // $("#form-batchRece-add").valid();
        })


        $('#ticketNumber').on('keyup', function() {
            $('#draftId').val('');

            $("#amountMoneySpan").text("0元（原汇票金额）");
        });
        // $('#userName').on('blur', function() {
        //     if ($('#userId').val() === '') {
        //         $('#userName').val('');
        //     }
        // });

    }

    $("#form-tariff-add").validate({
        errorClass:"invalid",
        rules:{
            ticketNumber: {
                required: function() {
                    return $("#receivableType").val() == "10";
                }
            },
            billTypeId: {
                required: function() {
                    return $("#receivableType").val() == "10";
                }
            },
            issueDate: {
                required: function() {
                    return $("#receivableType").val() == "10";
                }
            },
            dueDate: {
                required: function() {
                    return $("#receivableType").val() == "10";
                }
            },
            payCompany: {
                required: function() {
                    return $("#receivableType").val() == "10";
                }
            },
        },
        messages: {
            ticketNumber: {
                required: "当收款方式为承兑时，票据号为必填"
            },
            billTypeId: {
                required: "当收款方式为承兑时，承兑类型为必填"
            },
            issueDate: {
                required: "当收款方式为承兑时，出票日期为必填"
            },
            dueDate: {
                required: "当收款方式为承兑时，到期日期为必填"
            },
            payCompany: {
                required: "当收款方式为承兑时，付票单位为必填"
            },
        },
        focusCleanup: true
    });

    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-tariff-add").serializeArray();
            $.operate.saveTab(prefix + "/saveIncomeHexiao", data);
        }
    }

</script>
</body>
</html>