<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('月度净利润列表')" />
	<th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
	.table-striped {
		height: calc(100% - 20px);
	}
</style>
<body class="gray-bg">
    <div class="container-div">
		<div class="row">
			<!--<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal" onsubmit="return false">
					<div class="row">
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<select name="salesDept" id="salesDeptId" class="form-control valid selectpicker"
											aria-invalid="false" data-none-selected-text="运营组" multiple>
										<option th:each="mapS,status:${salesDept}"
												th:value="${mapS.deptId}"
												th:text="${mapS.deptName}">
										</option>
									</select>
								</div>
							</div>
						</div>

						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>
				</form>
			</div>-->
			<div class="btn-group-sm" id="toolbar" role="group">
				<a class="btn btn-success" onclick="exportExcel()">
					<i class="fa fa-download"></i> 导出
				</a>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
			</div>
		</div>
	</div>

<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">

	var prefix = ctx + "finance/net-profits";
	var sumAmount = 0;
	var sumTaxAmount = 0;
	var g7Rate = [[${@sysConfigServiceImpl.selectConfigByKey("net_profits_g7_rate")}]];
	var oilTax = [[${@sysConfigServiceImpl.selectConfigByKey("net_profits_oil_tax")}]];
	$(function() {
		//监听回车事件 回车搜索
		$(document).keyup(function(e){
			var key = e.which;
			if(key==13){
				$.table.search();
			}
		});

		var options = {
			url: prefix + "/list-yf?customerId=[(${customerId})]&yearMonth=[(${yearMonth})]",
			//fixedColumns: true,
			//fixedNumber: 3,
			showToggle: false,
			showFooter: true,
			height: 560,
			onRefresh:function(params){
				clearTotal();
			},
			onCheck: function (row,$element) {
				addTotal(row);
				setTotal();
			},
			onUncheck: function (row, $element) {
				subTotal(row);
				setTotal();
			},
			onCheckAll: function (rowsAfter) {
				clearTotal();
				//循环累加
				for (var row of rowsAfter) {
					addTotal(row);
				}
				//赋值
				setTotal();
			},
			onUncheckAll: function () {
				//总数清0
				clearTotal();
				//赋值
				setTotal();
			},
			onPostBody: function (params) {
				clearTotal();
				//合并页脚
				//merge_footer();
				//getAmountCount();
				$('[flag="tax_dtl"]').on('show.bs.tooltip', function () {
					if ($(this).attr('data-original-title') == "加载中...") {
						var that = this;
						let type = $(this).attr('biz-type')
						let lot_tax = $(this).attr('lot_tax')
						$.ajax({
							url: prefix + '/yf-tax-dtl?payDetailId=' + $(this).attr('biz-id') + "&type=" + type + "&customerId=[(${customerId})]&yearMonth=[(${yearMonth})]",
							cache: false,
							success: function (res) {
								if (res.code == 0) {
									var html = [];
									if (res.data.length == 0) {
										html.push("未配置含税，运单税率: " + lot_tax)
									} else {
										for (let i = 0; i < res.data.length; i++) {
											if (res.data[i].isHistory == 0) {
												html.push("<div style='text-align: left'>", res.data[i].effectDate.substring(5, 10), " / ",
														res.data[i].dictLabel.replace('增值税', '').replace('专用发票', '专票').replace('普票发票', '普票').replace('（','(').replace('）',')'),
														" / ", res.data[i].amount, "÷", res.data[i].numVal1, "</div>")
											}
										}
									}
									$(that).attr('title', html.join('')).tooltip('fixTitle').tooltip('show');
								} else {
									$(that).attr('title', res.msg).tooltip('fixTitle').tooltip('show');
								}
							},
							error: function(res) {
								$(that).attr('title', res.responseText).tooltip('fixTitle').tooltip('show');
							}
						})
					}
				})
			},
			columns: [
				{
					checkbox: true,
					footerFormatter: function (row) {
						return "小计: 应付分摊金额：<nobr id='sumTaxAmount'>¥0.00</nobr> &nbsp;&nbsp; 去税金额：<nobr id='sumAmount'>¥0.00</nobr><br>"
								//+ "总计: 未开票金额：<nobr id='sumAmountTotal'>¥0.00</nobr> &nbsp;&nbsp; 未申请金额：<nobr id='sumTaxAmountTotal'>¥0.00</nobr>";
					}
				},
				{
					title: '序号',
					formatter: function(value, row, index) {
						let option = $("#bootstrap-table").bootstrapTable('getOptions');
						let pageSize = option.pageSize;
						let pageNumber = option.pageNumber;
						return pageSize * (pageNumber - 1) + index + 1
					}
				},
				{
					title: '应付明细',
					field: 'vbillno'
				},
				{
					title: '发货单',
					field: 'invoiceNo',
				},
				{
					title: '支付方式',
					field: 'costTypeFreight',
					formatter: function (value, row, index) {
						if (value == '1' || value == '3' || value == '5') {
							return '油卡'
						} else {
							if (row.g7Pay == 2) {
								return 'G7'
							} else {
								return '现金'
							}
						}
					}
				},
				{
					title: '应付分摊',
					align: 'right',
					field: 'costShare',
					formatter: function (value, row, index) {
						if (value != null) {
							return value.toLocaleString('zh', {
								maximumFractionDigits: 2,
								minimumFractionDigits: 2
							});
						}
					}
				},
				{
					title: '类型',
					field: 'type',
					formatter: function (value, row, index) {
						if (value == 1) {
							return "一般应付"
						} else if (value == 2) {
							return "调整单"
						} else if (value == 3) {
							return "对账调整"
						} else if (value == 4) {
							return "税率调整"
						}
					}
				},
				{
					title: '去税金额',
					align: 'right',
					field: 'amount',
					formatter: function (value, row, index) {
						if (value != null) {
							let text = value.toLocaleString('zh', {
								maximumFractionDigits: 2,
								minimumFractionDigits: 2
							})
							if (row.costTypeFreight == '1' || row.costTypeFreight == '3' || row.costTypeFreight == '5') {
								text += " <i class='fa fa-question-circle' data-toggle='tooltip' style='font-size: 15px' data-placement='left' data-container='body' title='" + row.costShare + " ÷ " + oilTax + "'></i>";
							} else {
								if (row.g7Pay == 2) {
									text += " <i class='fa fa-question-circle' data-toggle='tooltip' style='font-size: 15px' data-placement='left' data-container='body' title='"+row.costShare+"÷1.09+"+row.costShare+"×"+g7Rate+"'></i>";
								} else {
									text += " <i class='fa fa-question-circle' flag='tax_dtl' data-html='true' biz-id='" + row.payDetailId + "' lot_tax='"+row.lotTax+"' biz-type='" + row.type + "' data-toggle='tooltip' style='font-size: 15px' data-placement='left' data-container='body' title='加载中...'></i>";
								}
							}
							return text
						}
					}
				}
			]
		};
		$.table.init(options);

	});

	function searchx() {
		var data = {};
		$.table.search('role-form', data);
	}

	/**
	 * 合并页脚
	 */
	function merge_footer() {
		$('.fixed-table-footer').find("td:first").attr("colspan", $('.fixed-table-footer').find("td").length)
		$('.fixed-table-footer').find("td:first").siblings().remove();
	}

	// function getAmountCount() {
	// 	var data = $.common.formToJSON("role-form");
	// 	data.salesDept = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
	// 	//data['params[vbillstatus]'] = $.common.join($('#vbillstatus').selectpicker('val'));//状态
	// 	$.post(prefix + "/getCount", data, function(result){
	// 		if (result.code == 0) {
	// 			var data = result.data;
	// 			$("#sumAmountTotal").text(data.SUMAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
	// 			$("#sumTaxAmountTotal").text(data.sumTaxAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
	// 		} else {
	// 			$("#sumAmountTotal").text("ERROR");
	// 			$("#sumTaxAmountTotal").text("ERROR");
	// 		}
	// 	});
	// }

	function exportExcel() {
		$.modal.confirm("即将导出，是否继续？", function() {
			$.modal.loading("正在导出数据，请稍后...");
			var search = $.common.formToJSON("role-form");
			//search['salesDept'] = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
			//search['params[vbillstatus]'] = $.common.join($('#vbillstatus').selectpicker('val'));//状态
			search['customerId'] = [[${customerId}]];
			search['yearMonth'] = [[${yearMonth}]];
			search['custAbbr'] = [[${custAbbr}]];
			$.post(prefix + "/export-yf", search, function(result) {
				if (result.code == web_status.SUCCESS) {
					window.location.href = ctx + "common/download?fileName=" + encodeURI(result.data) + "&delete=" + true;
				} else if (result.code == web_status.WARNING) {
					$.modal.alertWarning(result.msg)
				} else {
					$.modal.alertError(result.msg);
				}
				$.modal.closeLoading();
			});
		});
	}

	/**
	 * 将总计金额清零
	 */
	function clearTotal() {
		//开票金额合计
		sumAmount = 0
		sumTaxAmount = 0
	}

	/**
	 * 累计总金额
	 */
	function addTotal(row) {
		sumAmount += row.amount;
		if (row.type != 4) {
			sumTaxAmount += row.costShare
		}
	}

	/**
	 *
	 */
	function subTotal(row) {
		sumAmount -= row.amount;
		if (row.type != 4) {
			sumTaxAmount -= row.costShare
		}
	}

	/**
	 *
	 * 给页脚总计赋值
	 */
	function setTotal() {
		$("#sumAmount").text(sumAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
		$("#sumTaxAmount").text(sumTaxAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
	}

</script>
</body>
</html>