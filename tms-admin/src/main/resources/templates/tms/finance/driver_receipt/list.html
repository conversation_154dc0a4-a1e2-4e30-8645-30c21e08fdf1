<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('司机代收')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运单号：</label>
                            <div class="col-sm-8">
                                <input name="lotno" id="lotno" class="form-control"
                                       placeholder="请输入运单号" maxlength="30">
                            </div>
                        </div>
                    </div>


                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">承运商：</label>
                            <div class="col-sm-8">
                                <input name="carrName" id="carrName" placeholder="请输入承运商名称" class="form-control valid">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">代收单号：</label>
                            <div class="col-sm-8">
                                <input name="vbillno" id="vbillno" class="form-control"
                                       placeholder="请输入代收单号" maxlength="30">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="driverWriteOff()" shiro:hasPermission="finance:driverReceipt:driverWriteOff">
                <i class="fa fa-newspaper-o"></i> 司机代收核销
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var prefix = ctx + "driverReceipt";
    //应付单状态
    var payDetailStatusEnum = [[${payDetailStatusEnum}]];
    //应付单map
    var payDetailStatusMap = [[${payDetailStatusMap}]];

    $(function () {
        var options = {
            url: prefix + "/list",
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            rememberSelected: false,
            clickToSelect:true,
            height: 560,
            uniqueId: "payDetailId",
            columns: [{
                checkbox: true
            },
                {
                    title: '代收单号',
                    align: 'left',
                    field : 'vbillno'
                },
                {
                    title: '状态',
                    align: 'left',
                    field : 'status',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '<span class="label label-primary">待确认</label>';
                            case 1:
                                return '<span class="label label-warning">已确认</label>';
                            case 2:
                                return '<span class="label label-info">已核销</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '承运商名称',
                    align: 'left',
                    field : 'carrName'
                }
                ,
                {
                    title: '代收金额',
                    align: 'right',
                    field : 'collectionAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }
                ,
                {
                    title: '核销金额',
                    align: 'right',
                    field : 'writeOffAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '实收金额',
                    align: 'right',
                    field : 'actualAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '确认时间',
                    align: 'left',
                    field : 'affirmTime'
                },
                {
                    title: '确认人',
                    align: 'left',
                    field : 'affirmMan'
                }
                ,
                {
                    title: '运单号',
                    align: 'left',
                    field : 'lotno'
                }

            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
    });

    /**
     * 司机代收核销
     */
    function driverWriteOff() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应付单据");
                return;
            }
        }
        var rows = $.table.selectFirstColumns();
        if (rows.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }

        var payDetailId = $.table.selectColumns("payDetailId").join();


        $.modal.openTab("应付单据详情", prefix + "/driverWriteOff/" + payDetailId );
    }

</script>
</body>
</html>