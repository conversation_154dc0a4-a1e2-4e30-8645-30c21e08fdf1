<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('司机代收核销')" />

</head>

<body>
<div class="form-content">
    <form id="form-driverWriteOff" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="payDetailId" th:value="${payDetailId}">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">应收明细</a>

                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">

                        <div class="fixed-table-body">
                            <table class="custom-tab table-hover table" border="0" id="receiveDetailTable">
                                <thead>
                                <tr>
                                    <th style="width: 5%;">选择</th>
                                    <th style="width: 15%;">应收单号</th>
                                    <th style="width: 10%;">应收单据状态</th>
                                    <th style="width: 15%;">发货单号</th>
                                    <th style="width: 10%;">结算方式</th>
                                    <th style="width: 10%;">费用类型</th>
                                    <th style="width: 10%;">总金额</th>
                                    <th style="width: 10%;">已收金额</th>
                                    <th style="width: 10%;">未收金额</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="receiveDetail,stat : ${receiveDetailList}">
                                    <td>
                                        <input th:name="|receiveDetailList[${stat.index}].receiveDetailId|"
                                               th:value="${receiveDetail.receiveDetailId}" th:data-index="${stat.index}"
                                               type="checkbox">
                                    </td>
                                    <td th:text="${receiveDetail.vbillno}"></td>
                                    <td>
                                        <div th:each="receiveDetailStatus:${receiveDetailStatusEnum}"
                                        th:if="${receiveDetail.vbillstatus} == ${receiveDetailStatus.value}"
                                        th:text="${receiveDetailStatus.context}"></div>
                                        <input type="hidden" th:id="rdVbillstatus_ + ${stat.index}" th:value="${receiveDetail.vbillstatus}">
                                    </td>
                                    <td th:text="${receiveDetail.invoiceVbillno}"></td>
                                    <td>
                                        <div th:each="dict : ${@dict.getType('bala_type')}" th:if="${dict.dictValue == receiveDetail.balatype}"
                                             th:text="${dict.dictLabel}" ></div>
                                        <input type="hidden" th:id="balaType_ + ${stat.index}" th:value="${receiveDetail.balatype}">
                                    </td>
                                    <td><div th:each="dict : ${freeTypeEnum}" th:if="${dict.value == receiveDetail.freeType}"
                                             th:text="${dict.context}"></div>
                                    </td>

                                    <td th:text="${receiveDetail.transFeeCount}"></td>
                                    <td th:text="${receiveDetail.gotAmount}"></td>
                                    <td th:text="${receiveDetail.ungotAmount}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">应付明细</a>
                    </h4>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="fixed-table-body">
                            <table class="custom-tab table-hover table" border="0" id="payDetailTable">
                                <thead>
                                <tr>
                                    <th style="width: 5%;">选择</th>
                                    <th style="width: 15%;">应付单号</th>
                                    <th style="width: 10%;">应付单状态</th>
                                    <th style="width: 10%;">费用类型</th>
                                    <th style="width: 10%;">付款类型</th>
                                    <th style="width: 10%;">核销金额</th>
                                    <th style="width: 10%;">总金额</th>
                                    <th style="width: 10%;display: none">油卡比例</th>
                                    <th style="width: 10%;">已付金额</th>
                                    <th style="width: 10%;">未付金额</th>
                                </tr>
                                </thead>
                                <tbody>
                                <!--th:each="segPackGoods,segPackGoodsStat : ${segPackGoodsList}"-->
                                <tr th:each="payDetail,stat : ${payDetailList}">
                                    <td>
                                        <input th:name="|driverWriteOffPayDetailVOList[${stat.index}].payDetailId|"
                                               th:value="${payDetail.payDetailId}" type="checkbox" th:data-index="${stat.index}"
                                               >
                                    </td>
                                    <td th:text="${payDetail.vbillno}"></td>
                                    <td>
                                        <div th:each="payDetailStatus:${payDetailStatusEnum}"
                                             th:if="${payDetail.vbillstatus} == ${payDetailStatus.value}"
                                             th:text="${payDetailStatus.context}"></div>
                                        <input type="hidden" th:id="pdVbillstatus_ + ${stat.index}" th:value="${payDetail.vbillstatus}">
                                    </td>
                                    <td>
                                        <div th:if="${payDetail.freeType == '0'}">运费</div>
                                        <div th:if="${payDetail.freeType == '1'}">在途费用</div>
                                        <div th:if="${payDetail.freeType == '2'}">调整费用</div>

                                    </td>
                                    <td>
                                        <div th:each="dict : ${@dict.getType('cost_type_freight')}"
                                             th:if="${dict.dictValue == payDetail.costTypeFreight and payDetail.freeType == '0'}"
                                             th:text="${dict.dictLabel}"></div>
                                        <div th:each="dict : ${@dict.getType('cost_type_on_way')}"
                                             th:if="${dict.dictValue == payDetail.costTypeOnWay and payDetail.freeType == '1'}"
                                             th:text="${dict.dictLabel}"></div>
                                    </td>

                                    <td>
                                        <input th:name="|driverWriteOffPayDetailVOList[${stat.index}].amount|" type="text" autocomplete="off" aria-autocomplete="none"
                                               oninput="checkAmount(this)" th:data-index="${stat.index}" data-old="0"
                                               th:id="amount_ + ${stat.index}">
                                    </td>
                                    <td th:text="${payDetail.transFeeCount}"></td>
                                    <td style="display: none" th:text="${payDetail.params.oilRatio}"></td>
                                    <td th:text="${payDetail.gotAmount}"></td>
                                    <td th:id="ungotAmount_+${stat.index}" th:text="${payDetail.ungotAmount}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFour">代收信息</a>
                    </h5>
                </div>
                <div id="collapseFour" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-12 col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-1">备注：</label>
                                    <div class="col-sm-11" th:text="${createMemo}">

                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 添加图片预览区域 -->
                        <div class="row">
                            <div class="col-md-12 col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-1">附件：</label>
                                    <div class="col-sm-11">
                                        <div class="picviewer" style="display: flex;align-items: center;flex-wrap: wrap;" th:if="${uploadFileList != null}">
                                            <div th:each="file : ${uploadFileList}" style="margin-right: 10px;margin-bottom: 5px;">
                                                <img th:src="${file.filePath}" style="width: 32px;height: 32px;object-fit: scale-down;cursor: pointer;"  />
                                            </div>
                                        </div>
                                        <div th:if="${uploadFileList == null}">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="panel-group">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseThree">收款信息</a>
                    </h4>
                </div>
                <div id="collapseThree" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">转入账户：</label>
                                    <div class="col-sm-8">
                                        <select name="inAccount" class="form-control" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${account}" th:text="${dict.accountName}" th:value="${dict.accountId}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收款日期：</label>
                                    <div class="col-sm-8">
                                        <input name="receivableDate" id="receivableDate" class="form-control" required readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">实收金额：</label>
                                    <div class="col-sm-8">
                                        <input name="" id="leftoverFeeCount" th:value="${transFeeCount}" class="form-control" required readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" id="memo" maxlength="100" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer" />
<script>
    //司机代收总金额
    var FeeCount = [[${transFeeCount}]];
    //剩余金额
    var leftoverFeeCount = [[${transFeeCount}]];

    var prefix = ctx + "driverReceipt";

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');

        $('input[name="selectAll"]').on("change",function(){
            if($(this).is(':checked')){

            }else{

            }
        });
    });
    /**
     * 日期插件
     */
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        var reqDeliDateStart = laydate.render({
            elem: '#receivableDate', //指定元素
            isInitValue: false,
            trigger: 'click',
            type: 'datetime',
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours=now.getHours();
                this.dateTime.minutes=now.getMinutes();
                this.dateTime.seconds=now.getSeconds();
            },
            done: function(value, date, endDate){
                $("#receivableDate").val(value);
                //单独校验日期
                $("#form-driverWriteOff").validate().element($("#receivableDate"))
            }
        });
    });

    //校验金额
    function checkAmount(obj) {
        //索引id
        var index = $(obj).data("index");
        //根据索引id 获取该条未收金额
        var transFeeCount = parseFloat($("#ungotAmount_" + index).text());
        //上次输入的值
        var old = $(obj).data("old");

        //先把非数字的都替换掉，除了数字和.
        obj.value = obj.value.replace(/[^\d\.]/g, '');
        //必须保证第一个为数字而不是.
        obj.value = obj.value.replace(/^\./g, '');
        //保证只有出现一个.而没有多个.
        obj.value = obj.value.replace(/\.{2,}/g, '.');
        //保证.只出现一次，而不能出现两次以上
        obj.value = obj.value.replace('.', '$#$').replace(/\./g, '').replace(
            '$#$', '.');
        //保留两位小数
        if (obj.value.indexOf(".") != -1) {
            obj.value = obj.value.slice(0, obj.value.indexOf(".")) + obj.value.slice(obj.value.indexOf("."), obj.value.indexOf(".") + 3);
        }
        obj.value = obj.value.slice(0, 20);
        //最大可输入的值，用之前剩余金额加上 该输入框上次输入的值
        var max = decimal(parseFloat(leftoverFeeCount) + parseFloat(old),2);
        //当最大输入值小于未收金额，并且输入的值大于最大输入值，则将输入值改为最大输入值
        if (max < transFeeCount && parseFloat(obj.value) >= max) {
            obj.value = max;
        }
        //当最大输入值大于未收金额，并且输入值大于等于未收金额，则将输入值改为未收金额
        if (max >= transFeeCount && parseFloat(obj.value) >= transFeeCount) {
            obj.value = transFeeCount;
        }
        //将本次输入值存入 data-old 中，用于下次计算最大金额
        $(obj).data("old",obj.value);
        calculation();
    }

    /**
     * 计算最大金额，将所有输入值加起来，用司机代收总金额减去
     */
    function calculation() {
        var count = 0;
        $("[id^=amount_]").each(function () {
            if ($(this).val() != "") {
                count = parseFloat(count) + parseFloat($(this).val());
            }
        });

        leftoverFeeCount = decimal(parseFloat(FeeCount) - count,2);

        $("#leftoverFeeCount").val(leftoverFeeCount);
    }

    $("#form-driverWriteOff").validate({});
    /**
     * 提交
     */
    var receiveDetailAffirm = [[${receiveDetailAffirm}]];//应收已确认状态
    var payDetailAffirm = [[${payDetailAffirm}]];   //应付已确认状态
    function submitHandler() {
        if ($.validate.form()) {
            //应收相关判断
            var receiveDetailCheckedList = $("#receiveDetailTable input[type=checkbox]:checked");
            if (receiveDetailCheckedList.length === 0) {
                $.modal.msgError("请至少勾选一条应收明细！");
                return;
            }

            var rdStatus = false;
            var rdBalaType = false;
            receiveDetailCheckedList.each(function () {
                var index = $(this).data("index");
                //校验单据状态
                var rdVbillstatus = $("#rdVbillstatus_" + index).val();
                if (rdVbillstatus != receiveDetailAffirm) {
                    rdStatus = true;
                }

                //校验结算方式
                // var balaType = $("#balaType_" + index).val();
                // if (balaType !== "2") {
                //     rdBalaType = true;
                // }
            });
            if (rdStatus) {
                $.modal.msgError("请选中“已确认”状态的应收数据！");
                return;
            }
            // if (rdBalaType) {
            //     $.modal.msgError("请选中结算方式为“司机代收”的应收数据！");
            //     return;
            // }


/*            if ([[${isOnlyReceive}]] == 0) {
                var pdStatus = false;
                var pdAmount = false;
                //校验应付
                var payDetailCheckedList = $("#payDetailTable input[type=checkbox]:checked");
                if (payDetailCheckedList.length === 0) {
                    $.modal.msgError("请至少勾选一条应付明细！");
                    return;
                }
                payDetailCheckedList.each(function () {
                    var index = $(this).data("index");
                    //校验单据状态
                    var pdVbillstatus = $("#pdVbillstatus_" + index).val();
                    if (pdVbillstatus != payDetailAffirm) {
                        pdStatus = true;
                    }

                    //校验核销金额是否为空
                    var amount = $("#amount_" + index).val();
                    if (amount == "") {
                        pdAmount = true;
                    }

                });
                if (pdStatus) {
                    $.modal.msgError("请选中“已确认”状态的应付数据！");
                    return;
                }
                if (pdAmount) {
                    $.modal.msgError("请填写核销金额！");
                    return;
                }
            }*/

            $.modal.confirm("确认核销吗？", function () {
                var data = $("#form-driverWriteOff").serializeArray();
                $.operate.saveTab(prefix + "/driverWriteOff", data);
            });
        }
    }
    
    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }
</script>
</body>

</html>