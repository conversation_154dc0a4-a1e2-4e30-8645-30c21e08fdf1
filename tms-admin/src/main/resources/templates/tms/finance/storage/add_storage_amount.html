<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收入核销')"/>
</head>
<style>
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .ml10{
        margin-left: 10px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-client-add" class="form-horizontal" novalidate="novalidate">
        <!--力资id-->
        <input type="hidden" name="storageId" th:value="${storage.id}">
        <!-- 类型1:收入 2:成本 -->
        <input type="hidden" name="writeOffType" value="1">

        <!-- 核销记录 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h5 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseOne">收入核销记录</a>
                </h5>
            </div>
            <div id="collapseOne" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!--基础信息 begin-->
                    <div class="mt20" style="padding-bottom: 10px">
                        <table class="table table-bordered">
                            <thead style="background: #F7F8FA">
                            <tr>
                                <th>金额</th>
                                <th>收款账户</th>
<!--                                <th>收款方式</th>-->
                                <th>备注</th>
                                <th>收款时间</th>
                                <th>收款人</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="mapS,status:${writeOffList}">
                                <td>
                                    <div class="input-group" th:text="${'¥' + mapS.amount}"></div>
                                </td>
                                <td >
                                    <div class="input-group" th:text="${mapS.collectionAccount}"></div>
                                </td>
                                <!-- 字典回显 -->
<!--                                <td >-->
<!--                                    <div class="input-group" th:text="${@dict.getLabel('receivable_method', mapS.receivableType)}"></div>-->
<!--                                </td>-->
                                <td>
                                    <div class="input-group" th:text="${mapS.remark}"></div>
                                </td>
                                <td>
                                    <div class="input-group" th:text="${#dates.format(mapS.regDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                </td>
                                <td>
                                    <div class="input-group" th:text="${mapS.regUserName}"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--基础信息 end-->
                </div>
            </div>
        </div>
        <!--  收入核销 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">收入核销</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span style="">金额：</span></label>
                                <div class="col-sm-8">
                                    <input name="amount" id="amount"  class="form-control"
                                           type="text" min="0" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" maxlength="15" autocomplete="off" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span style="">收款账户</span></label>
                                <div class="col-sm-8">
                                    <input name="collectionAccount" id="collectionAccount"  class="form-control"
                                           type="text" maxlength="50" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span style="">备注：</span></label>
                                <div class="col-sm-8">
                                    <input name="remark" id="remark"  class="form-control"
                                           type="textarea"  autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "finance/storage";

    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-client-add").serializeArray();
            $.operate.saveTab(prefix + "/saveIncomeHexiao", data);
        }
    }
</script>
</body>
</html>