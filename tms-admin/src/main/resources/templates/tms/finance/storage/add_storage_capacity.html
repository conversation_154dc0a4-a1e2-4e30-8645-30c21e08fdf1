<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('仓储')" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 80px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .disin{
        display: inline-block;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .infotitle{
        line-height: 30px;
        font-weight: bold;
    }
    .titlebg{
        background: #eff3f9;
        padding: 5px 10px;

    }
    a.del-alink {
        display: block;
        margin: 120px auto 0;
    }
    .bggray{
        background: #f7f8fa;
    }
    .dropdown-menu {
        overflow: auto;
        height: 120px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-book-add" class="form-horizontal" novalidate="novalidate">
        <input id="historyGuidingPriceParam" type="hidden">
        <input id="historyGuidingPriceDetailParam" type="hidden">
        <!--要求提货日期-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="titlebg over">
                            <!--                            <div class="fl">-->
                            <!--                                <a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow()" title="新增行">+</a>-->
                            <!--                            </div>-->
                            <div class="fl fw ml10" style="line-height: 26px">仓储力资录入</div>
                        </div>
                        <div class="over">
                            <div class="fr" style="width: calc(100% - 50px)">
                                <!-- 台账begin  发货地 收货地-->
                                <div class="infotitle">仓储力资信息</div>
                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="col-sm-6">
                                            <!--                                <div class="col-md-5 col-sm-6">-->
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 开票日期：</label>
                                                <div class="flex_right">
                                                    <input type="text" class=" form-control" id="billDate" name="billDate" required
                                                           placeholder="开票日期" lay-key="1" autocomplete="off" readonly>
                                                </div>
                                            </div>
                                            <!--                                </div>-->
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 发票号：</label>
                                                <div class="flex_right">
                                                    <input type="text" placeholder="请输入发票号..." class="form-control" name="invoiceNo" id="invoiceNo" maxlength="50" autocomplete="off" required/>
                                                    <!--                                        <input name="custOrderno" id="custOrderno" placeholder="货主" class="form-control" type="text" maxlength="50" autocomplete="off">-->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-7 col-sm-7">
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 客户名称：</label>
                                                <div class="flex_right">
                                                    <!--                                                    <input name="customerName" id="customerName" placeholder="请填写客户名称..." class="form-control" type="text" maxlength="50" autocomplete="off">-->
                                                    <input name="custAbbr" id="custAbbr" onclick="selectClient()" type="text"
                                                           placeholder="请选择客户" class="form-control valid"
                                                           aria-required="true" required autocomplete="off" readonly>
                                                    <input name="customerName" id="customerName" type="hidden">
                                                    <input name="custCode" id="custCode" type="hidden">
                                                    <input name="customerId" id="customerId" type="hidden">
                                                    <input name="balaCorpId" id="balaCorpId" type="hidden">
                                                    <input name="balaDept" id="balaDept" type="hidden">
                                                    <input name="salesDept" id="salesDept" type="hidden">
                                                    <input name="psndoc" id="psndoc" type="hidden">
                                                    <input name="billingCorp" id="billingCorp" type="hidden">
                                                    <!--                                                    <span class="input-group-addon"><i class="fa fa-search"></i></span>-->
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 开票金额：</label>
                                                <div class="flex_right">
                                                    <input name="invoicedAmount" id="invoicedAmount" placeholder="请填写开票金额..." class="form-control" type="text" maxlength="50" autocomplete="off" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 仓储/力资：</label>
                                                <div class="flex_right">
                                                    <select name="storageOrLabor" id="storageOrLabor" class="form-control valid" aria-invalid="false"  aria-required="true" required>
                                                        <option value="1">仓储费</option>
                                                        <option value="2">力资费</option>
                                                    </select>
                                                    <!--                                        <input name="invoicing" id="invoicing" placeholder="开票..." class="form-control" type="text" maxlength="50" autocomplete="off">-->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 发货人：</label>
                                                <div class="flex_right">
                                                    <input name="consignor" id="consignor" placeholder="请输入发货人..." class="form-control" type="text" maxlength="50" autocomplete="off" required/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 发货手机:</label>
                                                <div class="flex_right">
                                                    <input name="shipperPhoneNumber" id="shipperPhoneNumber" placeholder="请输入发货人手机号..." class="form-control" type="text"
                                                           maxlength="50" autocomplete="off" oninput="onlyNumberThreeDecimal(this)" required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-7 col-sm-7">
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff"></span> 收款人：</label>
                                                <div class="flex_right">
                                                    <input name="payee" id="payee" placeholder="请输入运费..." class="form-control" type="text" maxlength="50" autocomplete="off"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff"></span> 收款手机：</label>
                                                <div class="flex_right">
                                                    <input name="payeeMobileNumber" id="payeeMobileNumber" placeholder="请输入收款人手机号..." class="form-control" type="text" maxlength="50" autocomplete="off"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff"></span> 开户行：</label>
                                                <div class="flex_right">
                                                    <input name="depositBank" id="depositBank" placeholder="请输入开户行..." class="form-control" type="text" maxlength="50" autocomplete="off"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff"></span> 卡号：</label>
                                                <div class="flex_right">
                                                    <input name="cardNo" id="cardNo" placeholder="请输入卡号..." class="form-control" type="text" maxlength="50" autocomplete="off"/>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span> 应付运费:</label>
                                                <div class="flex_right">
                                                    <input name="freighrPayable" id="freighrPayable" placeholder="请输入应付运费..." class="form-control" type="text"
                                                           maxlength="50" autocomplete="off" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-typeahead-js"/>

<script th:inline="javascript">

    var prefix = ctx + "finance/storage";

    $(function () {
        // 初始化省市区  一共两行
        let length = $("#collapseOne .over").length;
    });

    //实付运价默认等于运费
    $('#freight0').on('input',function(){
        $('#paidFreight0').val($(this).val());
    })

    //数字 保留两位小数
    function onlyNumberThreeDecimal (obj) {
        //得到第一个字符是否为负号
        var t = obj.value.charAt(0);
        //先把非数字的都替换掉，除了数字和.
        obj.value = obj.value.replace(/[^\d\.]/g, '');
        //必须保证第一个为数字而不是.
        obj.value = obj.value.replace(/^\./g, '');
        //保证只有出现一个.而没有多个.
        obj.value = obj.value.replace(/\.{2,}/g, '.');
        //保证.只出现一次，而不能出现两次以上
        obj.value = obj.value.replace('.', '$#$').replace(/\./g, '').replace(
            '$#$', '.');
        obj.value = obj.value.slice(0, 15);
        // 只能输入两个小数
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d).*$/,'$1$2.$3');
        //如果第一位是负号，则允许添加
        if (t == '-') {
            obj.value = '-' + obj.value;
        }
    }

    function removeRowThree(obj, index) {
        $(obj).parent().parent().remove()
        //删除之后  改变后面元素的index
    }

    /**
     * 计算合计
     */
    function calculateUnitPriceTotals(obj,index) {
        var tonnage = $("#tonnage"+index).val()//获取吨位
        var unitPrice = $("#unitPrice"+index).val()//获取单价

        if(tonnage != null && tonnage != '' && unitPrice != null && unitPrice != '') {
            $("#freight"+index).val(tonnage*unitPrice)
            $("#paidFreight"+index).val(tonnage*unitPrice)
        }
    }

    /**
     * 计算合计
     */
    function calculateByTonnageTotals(obj,index) {
        console.log(index)
        var tonnage = $("#tonnage"+index).val()//获取吨位
        var unitPrice = $("#unitPrice"+index).val()//获取单价

        if(tonnage != null && tonnage != '' && unitPrice != null && unitPrice != '') {
            $("#freight"+index).val(tonnage*unitPrice)
            $("#paidFreight"+index).val(tonnage*unitPrice)
        }
    }

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        laydate.render({
            elem: '#billDate'
            , type: 'datetime'

        });
    })

    /**
     * 校验
     */
    $("#form-book-add").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            reqDeliDate:{
                required:true,
            },
            reqArriDate:{
                required:true,
            },
            custName:{
                required:true,
            },
            balaName:{
                required:true,
            },
            appDeliMobile:{
                isPhone:true
            }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            //commit();
            $.operate.saveTab(prefix + "/addStorageCapacity", $('#form-book-add').serialize());
            //jQuery.subscribe("cmt", commit);
        }
    }

    function commit() {
        console.log(JSON.stringify($('#form-book-add').serializeArray()))
        console.log("===========")
        console.log($('#form-book-add').serializeArray())
        $.ajax({
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            url: prefix + "/addParkWayBill",
            data: JSON.stringify($('#form-book-add').serializeArray()),//JSON.stringify(data.field),
            dataType: 'json',
            success: function(result) {
                console.log(result)
                // if(result.code == 0) {
                //     layer.msg('注册成功！');
                // } else {
                //     layer.msg(result.msg);
                // }
            }
        });
    }

    //转换省市区名称
    function changePcaNmae(){
        //基本信息转换省市区保存name
        var provinceName = $("#provinceId0").find(":selected").text();
        if(provinceName === '-- 请选择 --'){
            $("#provinceName0").val("");
        }else{
            $("#provinceName0").val(provinceName);
        }
        var cityName = $("#cityId0").find(":selected").text();
        if(cityName === '-- 请选择 --'){
            $("#cityName0").val("");
        }else{
            $("#cityName0").val(cityName);
        }
        var areaName = $("#areaId0").find(":selected").text();
        if(areaName === '-- 请选择 --'){
            $("#areaName0").val("");
        }else{
            $("#areaName0").val(areaName);
        }
        //关注线路省市区名称
        for (var i = 0; i <= wayBillIndex+1; i++) {
            var deliProvinceId = "deliProvinceId" + i;
            var deliCityId = "deliCityId" + i;
            var deliAreaId = "deliAreaId" + i;
            //发货方
            var deliProvinceName = $("#" + deliProvinceId).find(":selected").text();
            if(deliProvinceName === '-- 请选择 --'){
                $("#deliProvinceName" + i).val("");
            }else{
                $("#deliProvinceName" + i).val(deliProvinceName);
            }
            //$("#deliProvinceName" + i).val($("#" + deliProvinceId).find(":selected").text());

            var deliCityName = $("#" + deliCityId).find(":selected").text();
            if(deliCityName === '-- 请选择 --'){
                $("#deliCityName" + i).val("");
            }else{
                $("#deliCityName" + i).val(deliCityName);
            }

            var deliAreaName =  $("#" + deliAreaId).find(":selected").text();
            if(deliAreaName === '-- 请选择 --'){
                $("#deliAreaName" + i).val("");
            }else{
                $("#deliAreaName" + i).val(deliAreaName);
            }
            //收货方
            var arriProvinceId = "arriProvinceId" + i;
            var arriCityId = "arriCityId" + i;
            var arriAreaId = "arriAreaId" + i;

            var arriProvinceName = $("#" + arriProvinceId).find(":selected").text();
            if(arriProvinceName === '-- 请选择 --'){
                $("#arriProvinceName" + i).val("");
            }else{
                $("#arriProvinceName" + i).val(arriProvinceName);
            }

            var arriCityName = $("#" + arriCityId).find(":selected").text();
            if(arriCityName === '-- 请选择 --'){
                $("#arriCityName" + i).val("");
            }else{
                $("#arriCityName" + i).val(arriCityName);
            }

            var arriAreaName =  $("#" + arriAreaId).find(":selected").text();
            if(arriAreaName === '-- 请选择 --'){
                $("#arriAreaName" + i).val("");
            }else{
                $("#arriAreaName" + i).val(arriAreaName);
            }
        }

    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }

    /**
     * 基础信息 - 客户名称
     */
    function selectClient() {
        $.modal.open("选择客户", ctx + "client/related?permission=sales",1050,'',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //清空结算客户信息
            $("#balaName").val("");
            $("#balaCode").val("");
            $("#balaCustomerId").val("");

            //清空提货地址信息
            $("#deliAddrName").val("");
            $("#deliAddrName_text").html("");

            $("#deliveryId").val("");
            $("#deliProvinceId").val("");
            $("#deliCityId").val("");
            $("#deliAreaId").val("");
            $("#deliMobile").val("");
            $("#deliMobile_text").html("");
            $("#deliContact").val("");
            $("#deliContact_text").html("");

            $("#deliView").val("");
            $("#deliView_text").html("");

            $("#deliProName").val("");
            $("#deliCityName").val("");
            $("#deliAreaName").val("");
            $("#deliAddrCode").val("");
            $("#deliDetailAddr").val("");

            //清空收货地址信息
            $("#arriAddrName").val("");
            $("#arriAddrName_text").html("");

            $("#arrivalId").val("");
            $("#arriProvinceId").val("");
            $("#arriCityId").val("");
            $("#arriAreaId").val("");
            $("#arriMobile").val("");
            $("#arriMobile_text").html("");
            $("#arriContact").val("");
            $("#arriContact_text").html("");

            $("#addiView").val("");
            $("#addiView_text").html("");
            $("#arriProName").val("");
            $("#arriCityName").val("");
            $("#arriAreaName").val("");
            $("#arriAddrCode").val("");
            $("#arriDetailAddr").val("");
            //清空集团
            $("#groupName").val("");
            $("#groupId").val("");

            //清空货品名称与货品类型
            $("[id^=goodsId_]").val("")
            $("[id^=goodsName_]").val("")
            $("[id^=goodsCode_]").val("")
            $("[id^=goodsCharacter_]").val("")
            $("[id^=goodsTypeName_]").val("")

            //客户id
            $("#customerId").val(rows[0]["customerId"]);
            //客户名称
            $("#customerName").val(rows[0]["custName"]);
            //客户简称
            $("#custAbbr").val(rows[0]["custAbbr"]);
            //客户编码
            $("#custCode").val(rows[0]["custCode"]);
            //结算公司
            $("#balaCorpId").val(rows[0]["balaCorp"]);
            //驻场组
            $("#stationDept").val(rows[0]["stationDept"]);
            //驻场组名称
            $("#stationDeptName").val(rows[0]["stationDeptName"]);
            //结算组
            $("#balaDept").val(rows[0]["balaDept"]);
            //运营部
            $("#salesDept").val(rows[0]["salesDept"]);
            //业务员
            $("#psndoc").val(rows[0]["psndoc"]);
            //结算方式
            $("#balaType").val(rows[0]["balaType"]);
            //开票公司
            $("#billingCorp").val(rows[0]["billingCorp"]);
            //app联系人
            $("#appDeliContact").val(rows[0]["appDeliContact"]);
            //app联系方式默认填业务员联系方式
            $("#appDeliMobile").val(rows[0]["appDeliMobile"]);

            //集团
            $.ajax({
                url: ctx + "group/getGroupByCustomerId",
                type: "post",
                dataType: "json",
                data: {customerId: rows[0]["customerId"]},
                success: function (result) {
                    if (result.code == 0 && result.data!=undefined) {
                        if (result.data != null) {
                            $("#groupName").val(result.data.GROUP_NAME);
                            $("#groupId").val(result.data.GROUP_ID);
                        }
                    }
                }
            });
            //选中完需单独校验
            $("#form-book-add").validate().element($("#custAbbr"));
            layer.close(index);
        });
    }
</script>
</body>

</html>