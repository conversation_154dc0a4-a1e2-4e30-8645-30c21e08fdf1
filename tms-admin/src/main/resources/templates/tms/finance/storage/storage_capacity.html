<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('仓储力资列表')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
</head>
<style>
    .row-margin-top {
        margin-top: 10px;
    }
    .select-table .table td {
        /* 超出部分隐藏 */
        overflow:hidden;
        /* 超出部分显示省略号 */
        text-overflow:ellipsis;
        /*规定段落中的文本不进行换行 */
        white-space:nowrap;
        /* 配合宽度来使用 */
        height:40px;
    }
</style>
<body class="gray-bg">

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="park-form">
                    <input type="hidden" id="parentId" name="parentId">

                    <div class="row">
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">发货单编号：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入发票号"  name="invoiceNo">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户发货单号：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入客户名称"  name="customerName">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">发货单编号：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入发货人" name="consignor">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户发货单号：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入收款人"  name="payee">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">车长：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入车型"  name="parkCarmodelName">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">车长：</label>-->
                                <div class="col-sm-12">
                                    <select name="storageOrLabor" id="storageOrLabor" class="form-control valid" aria-invalid="false"  aria-required="true" required>
                                        <option value="">-- 请选择仓储/力资 --</option>
                                        <option value="1">仓储费</option>
                                        <option value="2">力资费</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row row-margin-top">
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">发货单编号：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入开户行"  name="depositBank">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户发货单号：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入卡号"  name="cardNo">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-8">
                            <div class="form-group">
                                <!--                            <label class="col-sm-5">要求提货日期：</label>-->
                                <div class="col-sm-12">
                                    <div class="input-group">
                                        <span class="input-group-addon">从</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 开票开始时间" style="font-size: 14px" id="starttime" name="startTime" >
                                        <span class="input-group-addon">到</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 开票结束时间" style="font-size: 14px" id="endtime" name="endTime">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <label class="col-sm-4"></label>
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>

                    <div class="row row-margin-top">
                        <div class="col-sm-5">

                        </div>
                        <div class="col-sm-5">

                        </div>


                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" shiro:hasPermission="tms:finance:storage:add">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="tms:finance:storage:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
<!--                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="tms:park_data:waybill:import">-->
<!--                    <i class="fa fa-upload"></i> 导入-->
<!--                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:finance:storage:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: ztree-js" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:finance:storage:edit')}]];
    var saveIncomeFlag = [[${@permission.hasPermi('tms:finance:storage:toSaveIncome')}]];
    var saveCostFlag = [[${@permission.hasPermi('tms:finance:storage:toSaveCost')}]];
    var incomeListFlag = [[${@permission.hasPermi('tms:finance:storage:incomelist')}]];
    var costListFlag = [[${@permission.hasPermi('tms:finance:storage:costlist')}]];
    var removeFlag = [[${@permission.hasPermi('tms:finance:storage:remove')}]];

    var resetPwdFlag = [[${@permission.hasPermi('system:user:resetPwd')}]];
    var prefix = ctx + "finance/storage";

    var invoicedAmountSelect = 0;
    var freighrPayableSelect = 0;

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#starttime").removeAttr("lay-key");
        $("#endtime").removeAttr("lay-key");
        laydate.render({
            elem: '#endtime'
            , type: 'date'
        });

        laydate.render({
            elem: '#starttime'
            , type: 'date'
        });
        laydate.render({
            elem: '#payStartTime'
            , type: 'date'
        });

        laydate.render({
            elem: '#payEndTime'
            , type: 'date'
        });
    })

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 185 });

        queryStorageCapacityList();
        $.table.hideColumn("id");
    });

    /**
     * 跳转收入核销
     */
    function amountHexiao(id) {
        $.modal.openTab("收入核销", prefix + "/addStorageAmount/"+id,450,400);
    }
    /**
     * 跳转成本核销
     */
    function costHexiao(id) {
        $.modal.openTab("成本核销", prefix + "/addStorageCost/"+id,450,450);
    }

    /**
     * 收入核销记录
     * @param tariffId
     */
    function incomeHistory(storageId) {
        var url = prefix + "/incomeRecord?storageId="+storageId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "收入核销记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 成本核销记录
     * @param tariffId
     */
    function costHistory(storageId) {
        var url = prefix + "/costRecord?storageId="+storageId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "成本核销记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }


    //初始化查询条件传参
    queryParams = function(params) {
        var search = {};
        $.each($("#park-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }
    function queryStorageCapacityList() {
        var options = {
            url: prefix + "/storagePage",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            sortName: "billDate",
            sortOrder: "desc",
            modalName: "仓储力资",
            fixedColumns: true,
            showFooter:true,
            fixedNumber:5,
            showExport: true,
            clickToSelect: true,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"仓储力资列表"
            },
            queryParams: queryParams,
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onPostBody: function () {
                clearTotal();
                //合并页脚
                merge_footer();
                getAmountCount();
            },
            columns: [
                {
                    checkbox:true,
                    footerFormatter: function (row) {
                        return "已选开票总金额：<nobr id='invoicedAmountSelect'>¥0.00</nobr>" +
                            " 已选应付总运费：<nobr id='freighrPayableSelect'>¥0.00</nobr>" +
                            "<br>" +
                            "总合计：开票总金额：<nobr id='invoicedAmountTotal'></nobr>" +
                            " 应付总运费：<nobr id='freighrPayableTotal'></nobr>";
                    }
                },
                {
                    title: '主键',
                    align: 'left',
                    field : 'id'  //主键加载完成就隐藏  多选删除的时候是按照第一列的值是否相同来判断一共有几项的
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        //未做确认功能  如果核销中的仓储费会造成金额不对等  暂时隐藏修改
                        if ([[${@permission.hasPermi('tms:finance:storage:edit')}]] != "hidden" && row.revenueStatus == '1' && row.costStatus == '1') {
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)"  title="编辑" onclick="$.operate.editTab(\'' + row.id + '\')"><i class="fa fa-edit"></i></a> ');
                        }
                        if ([[${@permission.hasPermi('tms:finance:storage:remove')}]] != "hidden" && row.revenueStatus == '1' && row.costStatus == '1') {
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" title="删除" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i></a> ');
                        }
                        if ([[${@permission.hasPermi('tms:finance:storage:toSaveIncome')}]] != "hidden") {
                            actions.push('<a class="btn btn-warning btn-xs ' + saveIncomeFlag + '" href="javascript:void(0)" title="收入核销" onclick="amountHexiao(\'' + row.id + '\')"><i class="fa fa-sign-in"></i></a> ');
                        }
                        if ([[${@permission.hasPermi('tms:finance:storage:toSaveCost')}]] != "hidden") {
                            actions.push('<a class="btn btn-warning btn-xs ' + saveCostFlag + '" href="javascript:void(0)" title="成本核销" onclick="costHexiao(\'' + row.id + '\')"><i class="fa fa-sign-out"></i></a> ');
                        }
                        // if ([[${@permission.hasPermi('tms:finance:storage:incomelist')}]] != "hidden") {
                        //     actions.push('<a class="btn btn-success btn-xs ' + incomeListFlag + '" href="javascript:void(0)" title="收入核销记录" onclick="incomeHistory(\'' + row.id + '\')"><i class="fa fa-align-left"></i></a> ');
                        // }
                        // if ([[${@permission.hasPermi('tms:finance:storage:costlist')}]] != "hidden") {
                        //     actions.push('<a class="btn btn-success btn-xs ' + costListFlag + '" href="javascript:void(0)" title="成本核销记录" onclick="costHistory(\'' + row.id + '\')"><i class="fa fa-align-right"></i></a> ');
                        // }
                        return actions.join('');
                    }
                },
                {
                    title: '仓储费号',
                    align: 'left',
                    field : 'storageNo'
                },
                {
                    title: '开票日期',
                    align: 'left',
                    field : 'billDate'
                },
                {
                    title: '发票号',
                    align: 'left',
                    field : 'invoiceNo'
                },
                {
                    title: '客户名称',
                    align: 'left',
                    field : 'customerName'
                },
                {
                    title: '开票金额（元）',
                    align: 'right',
                    field: 'invoicedAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY', minimumFractionDigits: 3});
                    }
                },
                {
                    title: '收入核销状态',
                    align: 'left',
                    field : 'revenueStatus',
                    formatter: function (value, row, index) {
                        if (value === '1') {
                            return '未核销';
                        }else if(value === '2') {
                            return '部分核销';
                        }else {
                            return '已核销';
                        }
                    }
                },
                {
                    title: '仓储/力资',
                    align: 'left',
                    field : 'storageOrLabor',
                    formatter: function (value, row, index) {
                        if (value === '1') {
                            return '仓储费';
                        }else {
                            return '力资费';
                        }
                    }
                },
                {
                    title: '发货人',
                    align: 'left',
                    field : 'consignor'
                },
                {
                    title: '发货人手机号',
                    align: 'left',
                    field : 'shipperPhoneNumber'
                },
                {
                    title: '收款人',
                    align: 'left',
                    field : 'payee'
                },
                {
                    title: '收款人手机号',
                    align: 'left',
                    field : 'payeeMobileNumber'
                },
                {
                    title: '开户行',
                    align: 'left',
                    field : 'depositBank'
                },
                {
                    title: '卡号',
                    align: 'left',
                    field : 'cardNo'
                },
                {
                    title: '应付运费',
                    align: 'left',
                    field : 'freighrPayable'
                },
                {
                    title: '成本核销状态',
                    align: 'left',
                    field : 'costStatus',
                    formatter: function (value, row, index) {
                        if (value === '1') {
                            return '未核销';
                        }else if(value === '2') {
                            return '部分核销';
                        }else {
                            return '已核销';
                        }
                    }
                },
            ]
        };
        $.table.init(options);
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("park-form");
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;
                if (result.code == 0 && data != undefined) {
                    $("#invoicedAmountTotal").text(data.INVOICEDAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#freighrPayableTotal").text(data.FREIGHRPAYABLE.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        //开票金额合计
        invoicedAmountSelect = 0;
        freighrPayableSelect = 0;
    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        invoicedAmountSelect = invoicedAmountSelect + (row.invoicedAmount||0)
        freighrPayableSelect = freighrPayableSelect + (row.freighrPayable||0)
    }

    /**
     *  取消选中减少总金额
     */
    function subTotal(row) {
        invoicedAmountSelect = invoicedAmountSelect - (row.invoicedAmount||0)
        freighrPayableSelect = freighrPayableSelect - (row.freighrPayable||0)
    }

    /**
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#invoicedAmountSelect").text(invoicedAmountSelect.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#freighrPayableSelect").text(freighrPayableSelect.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }
</script>
</body>
</html>