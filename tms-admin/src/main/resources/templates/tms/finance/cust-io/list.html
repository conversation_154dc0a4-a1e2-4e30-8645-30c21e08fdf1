<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('客户往来')" />
	<th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
	/*.left-fixed-body-columns{
		height: calc(100% - 120px) !important;
	}*/
</style>
<body class="gray-bg">
    <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal">

					<div class="row">
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<input type="text" class="form-control"
										   id="yearMonth" name="yearMonth" placeholder="统计年月">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<select name="billingCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}">
										<option value="">-- 开票公司 --</option>
										<option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
									</select>
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<input name="custAbbr" placeholder="客户简称" class="form-control valid" type="text"
										   aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<input name="billingPayable" placeholder="发票抬头" class="form-control valid" type="text"
										   aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<div class="col-sm-12">
									<select name="salesDeptId" id="salesDeptId" class="form-control valid selectpicker"
											aria-invalid="false" data-none-selected-text="运营组" multiple>
										<option th:each="mapS,status:${salesDept}"
												th:value="${mapS.deptId}"
												th:text="${mapS.deptName}">
										</option>
									</select>
								</div>
							</div>
						</div>

						<div class="col-md-2 col-sm-4">
							<div class="form-group">
								<a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="btn-group-sm" id="toolbar" role="group">
				<a class="btn btn-success" onclick="exportExcel()">
					<i class="fa fa-download"></i> 导出
				</a>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
			</div>
		</div>
	</div>

<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">

	var prefix = ctx + "finance/cust-io";
	var globalBillingCorps = [[${@dict.getType('bala_corp')}]];
	var sumCheckAmount = 0;
	var sumReceAmount = 0;
	var sumRestAmount = 0;
	$(function() {
		//监听回车事件 回车搜索
		$(document).keyup(function(e){
			var key = e.which;
			if(key==13){
				$.table.search();
			}
		});

		var options = {
			url: prefix + "/list",
			//fixedColumns: true,
			//fixedNumber: 3,
			showToggle: false,
			showFooter: true,
			height: 560,
			onRefresh:function(params){
				clearTotal();
			},
			onCheck: function (row,$element) {
				addTotal(row);
				setTotal();
			},
			onUncheck: function (row, $element) {
				subTotal(row);
				setTotal();
			},
			onCheckAll: function (rowsAfter) {
				clearTotal();
				//循环累加
				for (var row of rowsAfter) {
					addTotal(row);
				}
				//赋值
				setTotal();
			},
			onUncheckAll: function () {
				//总数清0
				clearTotal();
				//赋值
				setTotal();
			},
			onPostBody: function (params) {
				//合并页脚
				merge_footer();
				getAmountCount();
			},
			columns: [
				{
					checkbox: true,
					footerFormatter: function (row) {
						return "小计开票：<nobr id='sumCheckAmount'>¥0.00</nobr>" +
								" 小计收款：<nobr id='sumReceAmount'>¥0.00</nobr>" +
								" 小计余额：<nobr id='sumRestAmount'>¥0.00</nobr><br>"+
								"总开票：<nobr id='sumCheckAmountTotal'>¥0.00</nobr>" +
								" 总收款：<nobr id='sumReceAmountTotal'>¥0.00</nobr>" +
								" 总余额：<nobr id='sumRestAmountTotal'>¥0.00</nobr>";
					}
				},
				{
					title: '序号',
					formatter: function(value, row, index) {
						let option = $("#bootstrap-table").bootstrapTable('getOptions');
						let pageSize = option.pageSize;
						let pageNumber = option.pageNumber;
						return pageSize * (pageNumber - 1) + index + 1
					}
				},
				{
					title: '客户',
					field: 'custAbbr'
				},
				{
					title: '开票公司',
					field: 'billingCorp',
					formatter: function (value, row, index) {
						for (let i = 0; i < globalBillingCorps.length; i++) {
							if (globalBillingCorps[i].dictValue == value) {
								return globalBillingCorps[i].dictLabel;
							}
						}
					}
				},
				{
					title: '开票抬头',
					field: 'billingPayable'
				},
				{
					title: '统计年月 <i class="fa fa-question-circle-o" style="color: #00a9ff" title="本月的统计到当天零点"></i>',
					field: 'yearMonth',
					formatter: function (value,row,index) {
						return value.substring(0,7)
					}
				},
				{
					title: '上月结转余额',
					field: 'upRestAmount',
					align: 'right',
					formatter: function(value, row, index) {
						if (row.upFlag == null || row.upFlag == 1) { // 上期的记录
							if (value == null) {
								return '<a href="javascript:setUpRestAmount(\'' + row.id + '\',\'' + row.custAbbr + '\',\'' + (row.billingCorp || '无') + '\',\'' + (row.billingPayable || '无') + '\',\'' + row.yearMonth + '\',\'\')" title="点击设置">未设置</a>'
							} else {
								return '<a href="javascript:setUpRestAmount(\'' + row.id + '\',\'' + row.custAbbr + '\',\'' + (row.billingCorp || '无') + '\',\'' + (row.billingPayable || '无') + '\',\'' + row.yearMonth + '\',' + value + ')" title="点击修改">' + value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}) + '</a>'
							}
						} else {
							return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
						}
					}
				},
				{
					title: '本月开票金额',
					field: 'checkAmount',
					align: 'right',
					formatter: function(value, row, index) {
						if (value != null) {
							return "<a href='javascript:checkDtl(\""+row.id+"\")'>" + value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}) + "</a>"
						}
					}
				},
				{
					title: '本月收款金额',
					field: 'receAmount',
					align: 'right',
					formatter: function(value, row, index) {
						if (value != null) {
							return "<a href='javascript:receDtl(\""+row.id+"\")'>" + value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}) + "</a>"
						}
					}
				},
				{
					title: '调整额',
					field: 'adjustAmount',
					align: 'right',
					formatter: function(value, row, index) {
						var editable = row.yearMonth.substring(0,7) === $.date.format(new Date(), 'yyyy-MM');
						var tmp = [];
						if (editable) {
							var txt = "<a href='javascript:adjustDtl(\"" + row.id + "\",\"" + row.custAbbr + "\",\""
									+ (row.billingCorp || '无') + "\",\"" + (row.billingPayable || '无')
									+ "\",\"" + row.yearMonth + "\"," + value + ",\""+(row.adjustReason||'')+"\")' title='"+(row.adjustReason||'')+"'>"
									+ (value == null ? '调整' : value).toLocaleString('zh', { style: 'currency', currency: 'CNY' })
									+ "</a>"
							if (row.adjustReason != null) {
								txt += " <i class='fa fa-question-circle-o' style='color:red' title='"+row.adjustReason+"'></i>"
							}
							return txt
						} else {
							if (value != null) {
								return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}) + " <i class='fa fa-question-circle-o' style='color:red' title='"+row.adjustReason+"'></i>"
							}
							return null;
						}
					}
				},
				{
					title: '月末余额 <i class="fa fa-question-circle-o" style="color: #00a9ff" title="上月结转余额 + 本月开票金额 - 本月收款金额 + 调整额"></i>',
					field: 'restAmount',
					align: 'right',
					formatter: function(value, row, index) {
						if (value != null) {
							return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
						}
					}
				},
				{
					title: '运营组',
					field: 'salesDeptName'
				}
			]
		};
		$.table.init(options);

		/**
		 * 初始化日期控件
		 */
		layui.use('laydate', function() {
			var laydate = layui.laydate;
			laydate.render({
				elem: '#yearMonth',
				type: 'month',
				trigger: 'click'
			});
		});
	});

	function searchx() {
		var data = {};
		data.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
		$.table.search('role-form', data);
	}
	/**
	 * 合并页脚
	 */
	function merge_footer() {
		var footer_tbody = $('.fixed-table-footer table tbody');
		var footer_tr = footer_tbody.find('>tr');
		var footer_td = footer_tr.find('>td');
		var footer_td_1 = footer_td.eq(0);
		//除了第一列其他都隐藏
		for(var i=1;i<footer_td.length;i++) {
			footer_td.eq(i).hide();
		}
		footer_td_1.attr('colspan', 1).show();
	}

	function getAmountCount() {
		var data = $.common.formToJSON("role-form");
		data.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
		$.post(prefix + "/getCount", data, function(result){
			if (result.code == 0) {
				var data = result.data;
				$("#sumCheckAmountTotal").text(data.SUM_CHECK_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
				$("#sumReceAmountTotal").text(data.SUM_RECE_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
				$("#sumRestAmountTotal").text(data.SUM_REST_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
			} else {
				$("#sumCheckAmountTotal").text("ERROR");
			}
		});
	}

	function exportExcel() {
		$.modal.confirm("即将导出，是否继续？", function() {
			$.modal.loading("正在导出数据，请稍后...");
			var search = $.common.formToJSON("role-form");
			search['salesDeptId'] = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
			$.post(prefix + "/export", search, function(result) {
				if (result.code == web_status.SUCCESS) {
					window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
				} else if (result.code == web_status.WARNING) {
					$.modal.alertWarning(result.msg)
				} else {
					$.modal.alertError(result.msg);
				}
				$.modal.closeLoading();
			});
		});
	}

	/**
	 * 将总计金额清零
	 */
	function clearTotal() {
		//开票金额合计
		sumCheckAmount = 0
		sumReceAmount = 0
		sumRestAmount = 0
	}

	/**
	 * 累计总金额
	 */
	function addTotal(row) {
		sumCheckAmount += row.checkAmount;
		sumReceAmount += row.receAmount;
		sumRestAmount += row.restAmount;
	}

	/**
	 *
	 */
	function subTotal(row) {
		sumCheckAmount -= row.checkAmount;
		sumReceAmount -= row.receAmount;
		sumRestAmount -= row.restAmount;
	}

	/**
	 *
	 * 给页脚总计赋值
	 */
	function setTotal() {
		$("#sumCheckAmount").text(sumCheckAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
		$("#sumReceAmount").text(sumReceAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
		$("#sumRestAmount").text(sumRestAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
	}

	function setUpRestAmount(id, custAbbr, billingCorp, billingPayable, yearMonth, upRestAmount) {
		layer.open({
			type: 1,
			area: ['450px', '300px'],
			fix: false,
			//不固定
			maxmin: false,
			shade: 0.3,
			title: '设置期初值',
			content: `
				<div style="padding: 8px 20px;">
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-sm-4 control-label">客户：</label>
							<div class="col-sm-8">
							  	<p class="form-control-static">${custAbbr}</p>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">开票公司：</label>
							<div class="col-sm-8">
							  	<p class="form-control-static">${billingCorp}</p>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">开票抬头：</label>
							<div class="col-sm-8">
							  	<p class="form-control-static">${billingPayable}</p>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">统计年月：</label>
							<div class="col-sm-8">
							  	<p class="form-control-static">${yearMonth.substring(0,7)}</p>
							</div>
						</div>
						<div class="form-group">
							<label for="inputPassword" class="col-sm-4 control-label">上月结转余额：</label>
							<div class="col-sm-6">
							    <div class="input-group">
								    <input type="text" class="form-control" id="upRestAmount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" value="${upRestAmount}" autocomplete="off">
								    <div class="input-group-addon">元</div>
								</div>
							</div>
						</div>
					</form>
				</div>
				`,
			btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
			// 弹层外区域关闭
			shadeClose: true,
			btn1: function(index, layero){
				if ($("#upRestAmount").val() == "") {
					$.modal.msgWarning("请输入上月结转余额");
					return;
				}
				$.modal.confirm("确定提交吗？", function () {
					$.operate.saveAndRefreshCurrent(prefix + "/setUpRestAmount", {id:id, upRestAmount: $("#upRestAmount").val()}, function(aj){
						if (aj.code == 0) {
							layer.close(index)
						}
					})
				});
			}
		})
	}

	function checkDtl(id) {
		layer.open({
			type: 2,
			area: ['900px', $(window).height() - 50 + 'px'],
			fix: false,
			title: "开票明细",
			content: prefix + "/checkView?id="+id,
			btn: ['关闭'],
			// 弹层外区域关闭
			shadeClose: true
		});
	}
	function receDtl(id) {
		layer.open({
			type: 2,
			area: ['900px', $(window).height() - 50 + 'px'],
			fix: false,
			title: "收款明细",
			content: prefix + "/receView?id="+id,
			btn: ['关闭'],
			// 弹层外区域关闭
			shadeClose: true
		});
	}
	function adjustDtl(id, custAbbr, billingCorp, billingPayable, yearMonth, adjustAmount, adjustReason) {
		layer.open({
			type: 1,
			area: ['450px'],
			fix: false,
			//不固定
			maxmin: false,
			shade: 0.3,
			title: '调整额',
			content: `
				<div style="padding: 8px 20px;">
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-sm-4 control-label">客户：</label>
							<div class="col-sm-8">
							  	<p class="form-control-static">${custAbbr}</p>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">开票公司：</label>
							<div class="col-sm-8">
							  	<p class="form-control-static">${billingCorp}</p>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">开票抬头：</label>
							<div class="col-sm-8">
							  	<p class="form-control-static">${billingPayable}</p>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">统计年月：</label>
							<div class="col-sm-8">
							  	<p class="form-control-static">${yearMonth.substring(0,7)}</p>
							</div>
						</div>
						<div class="form-group">
							<label for="inputPassword" class="col-sm-4 control-label"><span style="color: red">*</span> 调整额：</label>
							<div class="col-sm-6">
							    <div class="input-group">
								    <input type="text" class="form-control" id="adjustAmount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" value="${adjustAmount == null ? '':adjustAmount}" autocomplete="off">
								    <div class="input-group-addon">元</div>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label for="inputPassword" class="col-sm-4 control-label"><span style="color: red">*</span> 调整原因：</label>
							<div class="col-sm-6">
								<textarea class="form-control" id="adjustReason" rows="4" id="reason" autocomplete="off">${adjustReason||''}</textarea>
							</div>
						</div>
					</form>
				</div>
				`,
			btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
			// 弹层外区域关闭
			shadeClose: true,
			btn1: function(index, layero){
				if ($("#adjustAmount").val() == "") {
					$.modal.msgWarning("请输入调整额");
					return;
				}
				if ($("#adjustReason").val() == "") {
					$.modal.msgWarning("请输入调整原因");
					return;
				}
				$.modal.confirm("确定提交吗？", function () {
					$.operate.saveAndRefreshCurrent(prefix + "/setAdjustAmount", {id:id, adjustAmount: $("#adjustAmount").val(), adjustReason: $("#adjustReason").val()}, function(aj){
						if (aj.code == 0) {
							layer.close(index)
						}
					})
				});
			}
		})
	}
</script>
</body>
</html>