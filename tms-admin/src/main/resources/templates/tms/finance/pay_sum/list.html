<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方支付')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <!--<div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">单据号：</label>
                            <div class="col-sm-8">
                                <input name="vbillno" placeholder="请输入单据号" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">结算方式：</label>
                            <div class="col-sm-8">
                                <select name="settlementMethod" id="payMethod" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="结算方式" multiple th:with="type=${@dict.getType('pay_method')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">收款账户：</label>
                            <div class="col-sm-8">
                                <input name="recAccount" placeholder="请输入收款账户" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">收款卡号：</label>
                            <div class="col-sm-8">
                                <input name="recCardNo" placeholder="请输入收款卡号" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2">付款时间：</label>
                            <div class="col-sm-7">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="regDate" placeholder="开始时间">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endDate"  name="corDate" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>-->
        <!--<div class="btn-group-sm" id="toolbar" role="group">

             <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="finance:payRecord:export">
                 <i class="fa fa-download"></i> 导出
             </a>
        </div>-->

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "finance/paySum";
    var detailFlag = [[${@permission.hasPermi('finance:accountDetail:view')}]];
    var pay_method = [[${@dict.getType('pay_method')}]];//付款方式
    var bala_corp = [[${@dict.getType('bala_corp')}]];//结算公司


    //昨日日期
    var day1 = new Date();
    day1.setTime(day1.getTime()-24*60*60*1000);
    var d1Month = ("0" + (day1.getMonth() + 1)).slice(-2);
    var d1Day = ("0" + (day1.getDate())).slice(-2);
    var yesterday = day1.getFullYear()+"-" + d1Month + "-" + d1Day;
    //今日日期
    var day2 = new Date();
    day2.setTime(day2.getTime());
    var d2Month = ("0" + (day2.getMonth() + 1)).slice(-2);
    var d2Day = ("0" + (day2.getDate())).slice(-2);
    var today = day2.getFullYear()+"-" + d2Month + "-" + d2Day;

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        //默认日期
        $("#startDate").val(yesterday);
        $("#endDate").val(today);
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            clickToSelect:true,
            modalName: "付款记录",
            height: 560,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'center',
                    field: 'otherFeeId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs ' + detailFlag + '" href="javascript:void(0)" title="明细" onclick="detail(\'' + row.outAccount + '\')"><i class="fa fa-list" style="font-size: 15px;"></i></a> ');
                        return actions.join('');
                    }

                },
                {
                    title: '付款账户',
                    align: 'left',
                    field: 'outAccountName'
                },
                {
                    title: '总金额',
                    align: 'right',
                    field: 'payAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '单据数',
                    align: 'left',
                    field: 'countVbillno'
                }
            ]
        };

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endDate',
                type: 'date',
                trigger: 'click'
            });
        });

        $.table.init(options);
    });

    //跳转账户明细
    function detail(outAccount) {
        var url = prefix + "/accountDetail/"+outAccount;
        $.modal.openTab('账户明细',url);
    }


    function searchPre() {
        var data = {};
        data.settlementMethod = $.common.join($('#payMethod').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

</script>

</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>