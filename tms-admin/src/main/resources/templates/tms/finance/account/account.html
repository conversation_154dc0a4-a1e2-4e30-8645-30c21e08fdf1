<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('账号列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                  <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">账户编码：</label>
                            <div class="col-sm-8">
                                <input name="accountCode" placeholder="请输入账户编码" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">账户名称：</label>
                            <div class="col-sm-8">
                                <input name="accountName" placeholder="请输入账户名称" class="form-control" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                      <div class="col-md-3 col-sm-6">
                          <div class="form-group">
                              <label class="col-sm-4">结算公司：</label>
                              <div class="col-sm-8">
                                  <select id="balaCorp" name="balaCorp" class="form-control valid" onchange="changeBalaCorp()" th:with="type=${@dict.getType('bala_corp')}" required>
                                      <option value="">--- 请选择结算公司 ---</option>
                                      <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                  </select>
                              </div>
                          </div>
                      </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset();reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="$.operate.addTab()" shiro:hasPermission="finance:account:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="finance:account:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
           <!-- <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="basic:etc:import">
                <i class="fa fa-upload"></i> 导入
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="basic:etc:export">
                <i class="fa fa-download"></i> 导出
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "finance/account";
    //权限
    var editFlag = [[${@permission.hasPermi('finance:account:edit')}]];
    var detailFlag = [[${@permission.hasPermi('finance:account:detail')}]];

    var balaCorp = [[${@dict.getType('bala_corp')}]];
    var receive_payment_type = [[${@dict.getType('receive_payment_type')}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            showExport: true,
            clickToSelect:true,
            modalName: "账号",
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"账户列表"
            },
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    width: '100px',
                    align: 'center',
                    field: 'accountId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs ' + editFlag + '" href="javascript:void(0)" title="修改" onclick="edit(\'' + row.accountId + '\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                        actions.push('<a class="btn btn-xs ' + detailFlag + '" href="javascript:void(0)" title="明细" onclick="detail(\'' + row.accountId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        return actions.join('');
                    }

                },
                {
                    title: '账户编码',
                    align: 'left',
                    field: 'accountCode'
                },
                {
                    title: '账户名称',
                    align: 'left',
                    field: 'accountName'
                },
                {
                    title: '账号',
                    align: 'left',
                    field: 'account'
                },
                {
                    title: '账号类型',
                    align: 'left',
                    field: 'dept.deptName'
                },
                {
                    title: '收付款类型',
                    align: 'left',
                    field: 'paymentType',
                    formatter: function status(value,row) {
                        return $.table.selectDictLabel(receive_payment_type, value);
                    }
                },

                /*  {
                      title: '账户金额',
                      align: 'right',
                      field: 'accountBala',
                      halign: "center",
                      formatter: function (value, row, index) {
                          if (value === null) {
                              return ;
                          }
                          return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                      }
                  },*/
                {
                    title: '结算公司',
                    align: 'left',
                    field: 'balaCorp',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(balaCorp, value.balaCorp);
                    }
                }

            ]
        };

        $.table.init(options);
    });

    function detail(id) {
        var url = prefix + "/detail/"+id;
        $.modal.openTab('账号明细',url);
    }

    function edit(id) {
        var url = prefix + "/edit/"+id;
        $.modal.openTab('账号修改',url);
    }



    //清楚隐藏id，并刷新表单
    function reset() {
        $("#driverId").val("");
        $("#carId").val("");
        location.reload();
    }


</script>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</body>
</html>