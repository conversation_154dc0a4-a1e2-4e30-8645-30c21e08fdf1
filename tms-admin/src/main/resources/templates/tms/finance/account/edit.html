<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('账户管理-edit')"/>
</head>

<body>
<div class="form-content">
    <form id="form-account-edit" class="form-horizontal" novalidate="novalidate" th:object="${account}">
        <input name="accountId" type="hidden" th:field="*{accountId}"/>
        <!--更新时间-->
        <input name="corDate"  type="hidden" th:value="${#dates.format(account.corDate, 'yyyy-MM-dd HH:mm:ss')}"/>
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">
                                        <span >账户编码：</span></label>
                                    <div class="col-sm-8">
                                        <input  class="form-control valid"  name="accountCode" th:field="*{accountCode}" maxlength="50"  type="text">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red"><span>账户名称：</span></label>
                                    <div class="col-sm-8">
                                        <input name="accountName" class="form-control" th:field="*{accountName}" type="text"
                                               required maxlength="50">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">账号：</label>
                                    <div class="col-sm-8">
                                        <input name="account" class="form-control" type="text" th:field="*{account}"
                                               maxlength="50"  aria-required="true" required>

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">开户行：</label>
                                    <div class="col-sm-8">
                                        <select name="bank" class="form-control valid" th:with="type=${@dict.getType('banks')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:field="*{bank}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">账号类型：</label>
                                    <div class="col-sm-8">
                                        <select name="accountType" class="form-control valid" required>
                                            <option value=""></option>
                                            <option th:each="mapS,status:${@dict.getType('finance_account')}"
                                                    th:field="*{accountType}"
                                                    th:value="${mapS.dictValue}"
                                                    th:text="${mapS.dictLabel}">
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!--<div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">账户金额：</label>
                                    <div class="col-sm-8">
                                        <input type="text" oninput="$.numberUtil.onlyNumber(this);" min="0" name="accountBala" maxlength="15" th:field="*{accountBala}"  class="form-control">
                                    </div>
                                </div>
                            </div>-->
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收付款类型：</label>
                                    <div class="col-sm-8">
                                        <select name="paymentType" class="form-control valid" required th:with="type=${@dict.getType('receive_payment_type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:field="*{paymentType}"
                                                    th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">结算公司：</label>
                                    <div class="col-sm-8">
                                        <select name="balaCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:field="*{balaCorp}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-6">
                                            <textarea name="memo" maxlength="250" th:field="*{memo}" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script type="text/javascript">
    var prefix = ctx + "finance/account";


    $(function () {
        $('#collapseOne').collapse('show');
        //校验
        $("#form-account-edit").validate({
            focusCleanup: true
        });
    });



    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/edit", $('#form-account-edit').serialize());
        }
    }


</script>
</body>

</html>