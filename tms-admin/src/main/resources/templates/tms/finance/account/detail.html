<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('账户管理-detail')"/>
</head>

<body>
<div class="form-content">
    <form id="form-account-edit" class="form-horizontal" novalidate="novalidate" th:object="${account}">
        <input name="accountId" type="hidden" th:field="*{accountId}"/>
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">
                                        <span >账户编码：</span></label>
                                    <div class="col-sm-8" th:text="*{accountCode}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>账户名称：</span></label>
                                    <div class="col-sm-8" th:text="*{accountName}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">账号：</label>
                                    <div class="col-sm-8" th:text="*{account}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">开户行：</label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('banks')}" th:if="${dict.dictValue} == *{bank}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">账号类型：</label>
                                    <div class="col-sm-8" th:text="*{dept.deptName}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收付款类型：</label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('payment_type')}" th:if="${dict.dictValue} == *{paymentType}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">账户金额：</label>
                                    <div class="col-sm-8" th:text="*{accountBala}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">结算公司：</label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('bala_corp')}" th:if="${dict.dictValue} == *{balaCorp}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-6" th:text="*{memo}">
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>

</body>

</html>