<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('账号列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                  <div class="row">

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">账户名称：</label>
                            <div class="col-sm-8">
                                <input name="accountName" placeholder="请输入账户名称" class="form-control" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset();reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
          <!--  <a class="btn btn-primary" onclick="$.operate.addTab()" shiro:hasPermission="finance:account:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="finance:account:remove">
                <i class="fa fa-remove"></i> 删除
            </a>-->
           <!-- <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="basic:etc:import">
                <i class="fa fa-upload"></i> 导入
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="basic:etc:export">
                <i class="fa fa-download"></i> 导出
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "finance/account";
    //权限
    var editFlag = [[${@permission.hasPermi('basic:goodsType:edit')}]];
    var detailFlag = [[${@permission.hasPermi('basic:goodsType:detail')}]];

    var balaCorp = [[${@dict.getType('bala_corp')}]];

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showRefresh: false,
            showSearch: false,
            showColumns: false,
            modalName: "账号",
            columns: [{
                radio: true
            },
                // {
                //     title: '操作',
                //     width: '100px',
                //     align: 'center',
                //     field: 'accountId',
                //     formatter: function(value, row, index) {
                //         var actions = [];
                //         actions.push('<a class="btn btn-xs ' + editFlag + '" href="javascript:void(0)" title="修改" onclick="edit(\'' + row.accountId + '\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                //         actions.push('<a class="btn btn-xs ' + detailFlag + '" href="javascript:void(0)" title="明细" onclick="detail(\'' + row.accountId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                //         return actions.join('');
                //     }
                //
                // },
                {
                    title: '账户编码',
                    align: 'left',
                    field: 'accountCode'
                },
                {
                    title: '账户名称',
                    align: 'left',
                    field: 'accountName'
                },

                {
                    title: '账号类型',
                    align: 'left',
                    field: 'dept.deptName'
                },
                {
                    title: '账户金额',
                    align: 'right',
                    field: 'accountBala',
                    halign: "center",
                    formatter: function (value, row, index) {
                        if(row.accountBala != null){
                            return row.accountBala.toLocaleString( {style: 'currency', currency: 'CNY'});
                        }

                    }
                },


            ]
        };

        $.table.init(options);
    });

    /**
     * 选择司机的提交方法
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.close();
        //选中账户id
        parent.$("#accountId").val($.table.selectColumns('accountId').join());
        //选中账户名称
        parent.$("#accountName").val($.table.selectColumns('accountName').join());
    }








</script>

</body>
</html>