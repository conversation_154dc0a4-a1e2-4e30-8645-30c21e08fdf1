<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('应收月度对账列表')" />
</head>
<style>
	.table-striped{
		height: calc(100% - 80px);
	}
</style>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal">
					<div class="row">
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
<!--								<label class="col-sm-4">关账年份：</label>-->
								<div class="col-sm-12">
									<input name="year" id="year" placeholder="请输入关账年份" class="form-control" type="text"
										   aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
<!--								<label class="col-sm-4">关账月份：</label>-->
								<div class="col-sm-12">
									<input name="month" id="month" placeholder="请输入关账年份" class="form-control" type="text"
										   aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
<!--								<label class="col-sm-4">承运商名称：</label>-->
								<div class="col-sm-12">
									<input name="carrName" id="carrName" placeholder="请输入客户名称" class="form-control" type="text"
										     aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-md-2 col-sm-4">
							<div class="form-group">
<!--								<label class="col-sm-6"></label>-->
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>
				</form>
			</div>
			
	        <div class="btn-group-sm" id="toolbar" role="group">
				<a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="finance:payMonthCheckSheet:export">
					<i class="fa fa-download"></i> 导出
				</a>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
			</div>
		</div>
	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">
        var prefix = ctx + "finance/payMonthCheckSheet";

        $(function() {
            //监听回车事件 回车搜索
            $(document).keyup(function(e){
                var key = e.which;
                if(key==13){
                    $.table.search();
                }
            });
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
				exportUrl: prefix + "/export",
                modalName: "应付月度对账",
		        showExport: true,
                showToggle:false,
                showColumns:false,
                clickToSelect:true,
                columns: [{
		            checkbox: true
		        },
				{
					field : 'year', 
					title : '关账年份',
				},
				{
					field : 'month',
					title : '关账月份',
				},
				{
					field : 'carrCode',
					title : '承运商编码',
				},
				{
					field : 'carrName',
					title : '承运商名称',
				},
				{
					field : 'idCard',
					title : '身份证号码',
				},
				{
					field : 'totalAmount', 
					title : '总应付',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
				},
				{
					field : 'adjustAmount',
					title : '调整金额',
					formatter: function (value, row, index) {
						if (value === null) {
							return ;
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				},
				{
					field : 'allAmount',
					title : '实际应付',
					formatter: function (value, row, index) {
						if (value === null) {
							return ;
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				},
				{
					field : 'gotAmount', 
					title : '当月付款',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
				},
				{
					field : 'ungotAmount',
					title : '当月未付',
					formatter: function (value, row, index) {
						if (value === null) {
							return ;
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				},
				{
					field : 'openingAmount',
					title : '期初金额',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
				},
				{
					field : 'endingAmount',
					title : '期末金额',
					formatter: function (value, row, index) {
						if (value === null) {
							return ;
						}
						return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					}
				},
				// {
				// 	field : 'ungotAmount',
				// 	title : '期末金额',
				// 	formatter: function (value, row, index) {
				// 		if(row.openingAmount != null){
				// 			var endAmount = row.openingAmount + row.totalAmount - row.gotAmount;
				// 			return endAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //
				// 		}
                //
				// 	}
				// },
				{
					field : 'regDate', 
					title : '创建时间',
				},
		       ]
            };
            $.table.init(options);
        });

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            laydate.render({
                elem: '#month',
                type: 'month',
                format:"MM"
            });
            laydate.render({
                elem: '#year',
                type: 'year'
            });
        });
    </script>
</body>
</html>