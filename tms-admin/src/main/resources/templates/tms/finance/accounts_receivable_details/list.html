<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应收账款明细')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .table-striped{
        height: calc(100% - 120px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">客户名称：</label>-->
                            <div class="col-sm-12">
                                <input name="custName" placeholder="请输入客户名称" class="form-control" type="text"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">业务经办：</label>-->
                            <div class="col-sm-12">
                                <select name="salesDeptName" id="salesDept" class="form-control valid"
                                        aria-invalid="false" required>
                                    <option value="">--业务经办--</option>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptName}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-2">要求提货日：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="reqStartDate"  name="reqStartDate" placeholder="要求提货开始日">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="reqEndDate"  name="reqEndDate" placeholder="要求提货结束日">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">未收类型：</label>-->
                            <div class="col-sm-12">
                                <select name="unReceivableType" id="unReceivableType" class="form-control valid"
                                        aria-invalid="false" required>
                                    <option value="">--未收类型--</option>
                                    <option value="1">正值</option>
                                    <option value="2">0</option>
                                    <option value="3">负值</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>



            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">

            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="finance:accountsReceivableDetails:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "finance/accountsReceivableDetails";

    //合计
    var totalReceivableAmount = 0 ;
    var totalConfirmedAmount = 0;
    var totalUnconfirmedAmount = 0;
    var totalApplicationAmount = 0;
    var totalGotAmount = 0;
    var totalUngotAmount = 0;
    var totalInvoicedAmount = 0;
    var totalUnbilledAmount = 0;
    var totalCashApplicationAmount = 0;
    var totalOilApplicationAmount = 0;

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqStartDate',
                type: 'month'
            });
        });

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqEndDate',
                type: 'month'
            });
        })

        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            clickToSelect:true,
            modalName: "应收账款明细",
            height: 580,
            fixedNumber: 4,
            showFooter: true,
            onPostBody:function () {
                //合并页脚
                merge_footer();
                getAmountCount();
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "合计:&nbsp&nbsp"
                        + "总应收:<nobr id='totalReceivableAmount'>￥0</nobr>&nbsp&nbsp"
                        + "已确认金额:<nobr id='totalConfirmedAmount'>￥0</nobr>&nbsp&nbsp"
                        + "未确认金额:<nobr id='totalUnconfirmedAmount'>￥0</nobr>&nbsp&nbsp"
                        + "已收金额:<nobr id='totalGotAmount'>￥0</nobr>&nbsp&nbsp"
                        + "未收金额:<nobr id='totalUngotAmount'>￥0</nobr>&nbsp&nbsp<br>"
                        + "申请金额:<nobr id='totalApplicationAmount'>￥0</nobr>&nbsp&nbsp"
                        + "现金申请金额:<nobr id='totalCashApplicationAmount'>￥0</nobr>&nbsp&nbsp"
                        + "开票申请金额:<nobr id='totalOilApplicationAmount'>￥0</nobr>&nbsp&nbsp"
                        + "已开票金额:<nobr id='totalInvoicedAmount'>￥0</nobr>&nbsp&nbsp<br>"
                    +"总合计:&nbsp&nbsp"
                    + "总应收:<nobr id='totalReceivableAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "已确认金额:<nobr id='totalConfirmedAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "未确认金额:<nobr id='totalUnconfirmedAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "已收金额:<nobr id='totalGotAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "未收金额:<nobr id='totalUngotAmounts'>￥0</nobr>&nbsp&nbsp<br>"
                    + "申请金额:<nobr id='totalApplicationAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "现金申请金额:<nobr id='totalCashApplicationAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "开票申请金额:<nobr id='totalOilApplicationAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "已开票金额:<nobr id='totalInvoicedAmounts'>￥0</nobr>&nbsp&nbsp";

                }
            },
                {
                    title: '操作',
                    align: 'center',
                    field: 'carrierId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('finance:receive:business')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="应收明细"onclick="jumpReceDetail(\'' + row.customerId + '\')"><i class="fa fa-list" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('basic:carrBank:view')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="应收对账"onclick="jumpReceCheckSheet(\'' + row.customerId + '\')"><i class="fa fa-calculator" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('finance:receCheckSheet:view')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="客户对账"onclick="jumpCustCheckSheet(\'' + row.customerId + '\')"><i class="fa fa-file-text-o" style="font-size: 15px;"></i></a>');
                        }
                        return actions.join('');
                    }

                },
                {
                    title: '客户名称',
                    align: 'left',
                    field: 'custName'
                },
                {
                    title: '业务经办',
                    align: 'left',
                    field: 'salesDeptName',
                },
                {
                    title: '总应收(元)',
                    align: 'right',
                    field: 'totalReceivableAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '已确认金额(元)',
                    align: 'right',
                    field: 'totalConfirmedAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未确认金额(元)',
                    align: 'right',
                    field: 'totalUnconfirmedAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '已收金额(元)',
                    align: 'right',
                    field: 'gotAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未收金额(元)',
                    align: 'right',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        var ungotAmpunt = row.totalReceivableAmount-row.gotAmount
                        return ungotAmpunt.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '申请金额(元)',
                    align: 'right',
                    field: 'totalApplicationAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '现金申请金额(元)',
                    align: 'right',
                    field: 'totalCashApplicationAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '开票申请金额(元)',
                    align: 'right',
                    field: 'totalOilApplicationAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '已开票金额(元)',
                    align: 'right',
                    field: 'totalInvoicedAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                // {
                //     title: '未开票金额(元)',
                //     align: 'right',
                //     field: 'totalUnbilledAmount',
                //     halign: "left",
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // }
            ]
        };

        $.table.init(options);
    });

    /**
     * 跳转 业务中心-应收明细
     */
    function jumpReceDetail(customerId){
        var reqEndDate = $("#reqEndDate").val();
        if($.common.isNotEmpty(reqEndDate)){
            reqEndDate = reqEndDate + "-01"
        }
        var reqStartDate = $("#reqStartDate").val();
        if($.common.isNotEmpty(reqStartDate)){
            reqStartDate = reqStartDate + "-01"
        }
        var url = ctx + "receive/business?customerId="+customerId+"&reqEndDate="+reqEndDate+"&reqStartDate="+reqStartDate;
        $.modal.openTab("应收明细",url);
    }

    /**
     * 跳转 业务中心-应收对账
     */
    function jumpReceCheckSheet(customerId){
        var reqEndDate = $("#reqEndDate").val();
        var reqStartDate = $("#reqStartDate").val();
        var url = ctx + "receivableReconciliation?customerId="+customerId+"&reqEndDate="+reqEndDate+"&reqStartDate="+reqStartDate;
        $.modal.openTab("应收对账",url);
    }

    /**
     * 跳转 业务中心-客户对账
     */
    function jumpCustCheckSheet(customerId){
        var url = ctx + "receCheckSheet?customerId="+customerId;
        $.modal.openTab("客户对账",url);
    }

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: prefix + "/getAmountCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;
                if (result.code == 0 && data != undefined) {
                    $("#totalReceivableAmounts").text(data.totalReceivableAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#totalConfirmedAmounts").text(data.totalConfirmedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#totalUnconfirmedAmounts").text(data.totalUnconfirmedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#totalApplicationAmounts").text(data.totalApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#totalGotAmounts").text(data.gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#totalUngotAmounts").text(data.ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#totalInvoicedAmounts").text(data.totalInvoicedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#totalUnbilledAmounts").text(data.totalUnbilledAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#totalCashApplicationAmounts").text(data.totalCashApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#totalOilApplicationAmounts").text(data.totalOilApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        totalReceivableAmount = 0;
        totalConfirmedAmount = 0;
        totalUnconfirmedAmount = 0;
        totalApplicationAmount = 0;
        totalGotAmount = 0;
        totalUngotAmount = 0;
        totalInvoicedAmount = 0;
        totalUnbilledAmount = 0;
        totalCashApplicationAmount = 0;
        totalOilApplicationAmount = 0;

    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        totalReceivableAmount = totalReceivableAmount + row.totalReceivableAmount;
        totalConfirmedAmount = totalConfirmedAmount + row.totalConfirmedAmount;
        totalUnconfirmedAmount = totalUnconfirmedAmount + row.totalUnconfirmedAmount;
        totalApplicationAmount = totalApplicationAmount + row.totalApplicationAmount;
        totalGotAmount = totalGotAmount + row.gotAmount;
        totalUngotAmount = totalReceivableAmount - totalGotAmount;
        totalInvoicedAmount = totalInvoicedAmount + row.totalInvoicedAmount;
        totalUnbilledAmount = totalUnbilledAmount + row.totalUnbilledAmount;
        totalCashApplicationAmount = totalCashApplicationAmount + row.totalCashApplicationAmount;
        totalOilApplicationAmount = totalOilApplicationAmount + row.totalOilApplicationAmount;
    }

    function subTotal(row) {
        totalReceivableAmount = totalReceivableAmount - row.totalReceivableAmount;
        totalConfirmedAmount = totalConfirmedAmount - row.totalConfirmedAmount;
        totalUnconfirmedAmount = totalUnconfirmedAmount - row.totalUnconfirmedAmount;
        totalApplicationAmount = totalApplicationAmount - row.totalApplicationAmount;
        totalGotAmount = totalGotAmount - row.gotAmount;
        totalUngotAmount = totalReceivableAmount - totalGotAmount ;
        totalInvoicedAmount = totalInvoicedAmount - row.totalInvoicedAmount;
        totalUnbilledAmount = totalUnbilledAmount - row.totalUnbilledAmount;
        totalCashApplicationAmount = totalCashApplicationAmount - row.totalCashApplicationAmount;
        totalOilApplicationAmount = totalOilApplicationAmount - row.totalOilApplicationAmount;
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#totalReceivableAmount").text(totalReceivableAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#totalConfirmedAmount").text(totalConfirmedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#totalUnconfirmedAmount").text(totalUnconfirmedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#totalApplicationAmount").text(totalApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#totalGotAmount").text(totalGotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#totalUngotAmount").text(totalUngotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#totalInvoicedAmount").text(totalInvoicedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#totalUnbilledAmount").text(totalUnbilledAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#totalCashApplicationAmount").text(totalCashApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#totalOilApplicationAmount").text(totalOilApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }


</script>

</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>