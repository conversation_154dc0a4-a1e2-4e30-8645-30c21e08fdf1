<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
      xmlns="http://www.w3.org/1999/html">
<head>
    <th:block th:include="include :: header('地址-add')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .tab td {
        position: relative
    }

    label.error {
        top: 10px !important;
    }
    .fixed-table-body2{
        background-color: rgb(239,243,248);
        text-align:left!important;
        box-sizing:border-box
    }
    .iframe-main{
        width: 100%;
        height: 70%;
    }
    #container {
        width: auto;
        height: 500px;

    }
    #panel {
        position: fixed;
        background-color: white;
        max-height: 90%;
        overflow-y: auto;
        top: 10px;
        right: 10px;
        width: 280px;
        display: none;
    }
    #panel .amap-lib-driving {
        border-radius: 4px;
        overflow: hidden;
    }
    .fixed-table-body{
        margin: 0;
        overflow-x: hidden;
    }
</style>

<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-post-add">
        <input type="hidden" name="id" th:value="${interview.id}">
        <div class="form-group">
            <label class="col-sm-3 control-label"><span style="color: red;">*</span>姓名：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="name" id="name" required th:value="${interview.name}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label"><span style="color: red;">*</span>联系方式：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="phone" id="phone" required th:value="${interview.phone}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label ">年龄：</label>
            <div class="col-sm-8">
                <input class="form-control" type="number" name="age" id="age" th:value="${interview.age}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label ">学历：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="education" id="education" th:value="${interview.education}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label ">专业：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="major" id="major" th:value="${interview.major}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label ">户籍：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="domicile" id="domicile" th:value="${interview.domicile}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">过往物流经验：</label>
            <div class="col-sm-8">
                <textarea id="experience" name="experience" class="form-control" rows="6">[[${interview.experience}]]</textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">是否符合要求：</label>
            <div class="col-sm-8">
                <div class="radio-box">
                    <input type="radio" th:value="1"  name="ifMeet" th:checked="${interview.ifMeet == 1}">
                    <label>是</label>
                </div>
                <div class="radio-box">
                    <input type="radio" th:value="0"  name="ifMeet" th:checked="${interview.ifMeet == 0}">
                    <label>否</label>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label ">初试时间：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="firstDate" id="firstDate"  th:value="${#dates.format(interview.firstDate, 'yyyy-MM-dd HH:mm:ss')}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label ">初试面试人员：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="firstMan" id="firstMan" th:value="${interview.firstMan}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">是否通过初试：</label>
            <div class="col-sm-8">
                <div class="radio-box">
                    <input type="radio" th:value="1"  name="ifSuccessFirst" th:checked="${interview.ifSuccessFirst == 1}">
                    <label>是</label>
                </div>
                <div class="radio-box">
                    <input type="radio" th:value="0"  name="ifSuccessFirst" th:checked="${interview.ifSuccessFirst == 0}">
                    <label>否</label>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label ">复试时间：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="secondDate" id="secondDate"  th:value="${#dates.format(interview.secondDate, 'yyyy-MM-dd HH:mm:ss')}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label ">复试面试人员：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="secondMan" id="secondMan" th:value="${interview.secondMan}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">是否通过复试：</label>
            <div class="col-sm-8">
                <div class="radio-box">
                    <input type="radio" th:value="1"  name="ifSuccessSecond" th:checked="${interview.ifSuccessSecond == 0}">
                    <label>是</label>
                </div>
                <div class="radio-box">
                    <input type="radio" th:value="0"  name="ifSuccessSecond" th:checked="${interview.ifSuccessSecond == 0}">
                    <label>否</label>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label ">推荐备用岗位：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" name="spareWork" id="spareWork"  th:value="${interview.spareWork}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea id="remark" name="remark" class="form-control" rows="6">[[${interview.remark}]]</textarea>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<script type="text/javascript">
    var prefix = ctx + "interview";

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-post-add').serialize());
        }
    }

    layui.use('laydate', function () {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#firstDate', //指定元素
            type: 'datetime',
            trigger: 'click',
            max: 3,
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours=now.getHours();
                this.dateTime.minutes=now.getMinutes();
                this.dateTime.seconds=now.getSeconds();
            },
            done: function(value, date, endDate){
                $("#firstDate").val(value);
            }
        });
        laydate.render({
            elem: '#secondDate', //指定元素
            type: 'datetime',
            trigger: 'click',
            max: 3,
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours=now.getHours();
                this.dateTime.minutes=now.getMinutes();
                this.dateTime.seconds=now.getSeconds();
            },
            done: function(value, date, endDate){
                $("#secondDate").val(value);
            }
        });
    });

</script>
</body>
</html>