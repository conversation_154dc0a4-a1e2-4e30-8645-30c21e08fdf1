<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('岗位列表')" />
</head>
<body class="gray-bg">
	<div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="post-form">
					<div class="select-list">
						<ul>

							<li>
								姓名：<input type="text" name="name"/>
							</li>
							<li>
								面试情况：<select name="params[ifSuccess]">
								<option value="">-- 请选择 --</option>
								<option value="0">通过初试</option>
								<option value="1">通过复试</option>
							</select>
							</li>
							<li>
								创建时间：
								<input type="text" placeholder="创建开始时间"
									   id="startDate"  name="params[startDate]">
					-
								<input type="text" placeholder="创建结束时间"
									   id="endDate"  name="params[endDate]">
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
							    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			
			<div class="btn-group-sm" id="toolbar" role="group">
				<a class="btn btn-success" onclick="$.operate.add()">
	                <i class="fa fa-plus"></i> 新增
	            </a>
<!--				<a class="btn btn-primary single disabled" onclick="$.operate.edit()">
		            <i class="fa fa-edit"></i> 修改
		        </a>-->
				<a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
		            <i class="fa fa-remove"></i> 删除
		        </a>
				<a class="btn btn-warning" onclick="$.table.exportExcel()">
		            <i class="fa fa-download"></i> 导出
		        </a>
	        </div>
	        
	        <div class="col-sm-12 select-table table-striped">
			    <table id="bootstrap-table" data-mobile-responsive="true"></table>
			</div>
		</div>
	</div>
	<th:block th:include="include :: footer" />
	<script th:inline="javascript">
		var prefix = ctx + "interview";

		$(function() {
			layui.use('laydate', function() {
				var laydate = layui.laydate;
				laydate.render({
					elem: '#startDate',
					type: 'date',
					trigger: 'click'
				});
				laydate.render({
					elem: '#endDate',
					type: 'date',
					trigger: 'click'
				});
			});


			var options = {
		        url: prefix + "/list",
		        createUrl: prefix + "/add",
		        updateUrl: prefix + "/edit/{id}",
		        removeUrl: prefix + "/remove",
		        exportUrl: prefix + "/export",
		        modalName: "招聘信息",
				uniqueId:"id",
				clickToSelect:true,
		        columns: [{
		            checkbox: true
		        },
		        {
		            field: 'name',
		            title: '姓名'
		        },
				{
					field: 'phone',
					title: '联系方式'
				},
		        {
		            field: 'age',
		            title: '年龄'
		        },
		        {
		            field: 'education',
		            title: '学历'
		        },
		        {
		            field: 'major',
		            title: '专业'
		        },
				{
					field: 'experience',
					title: '过往物流经验',
					formatter: function status(value,row) {
						return $.table.tooltip(value);
					}
				},
				{
					field: 'domicile',
					title: '户籍'
				},
				{
					field: 'ifMeet',
					title: '是否符合要求',
					formatter(value,row,index){
						if(value == '0') return '否'
						else if (value == '1') return '是'
						else return '-';
					}
				},
				{
					field: 'spareWork',
					title: '应聘沟通岗位'
				},
		        {
		            field: 'createTime',
		            title: '创建时间'
		        },
		        {
		            title: '操作',
		            align: 'center',
		            formatter: function(value, row, index) {
		                var actions = [];
		                actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
		                actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
		                return actions.join('');
		            }
		        }]
		    };
		    $.table.init(options);
		});
	</script>
</body>
</html>