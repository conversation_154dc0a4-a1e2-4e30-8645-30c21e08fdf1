<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('调整额费用配置')"/>
</head>
<style>
    .flex{
        display: flex;
    }
    .lh26{
        line-height: 26px;
        width: 8em;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .w60{
        width: 60% !important;
        line-height: 26px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-client-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="id" th:value="${advancePayRecord.id}">

        <div class="flex">
            <div class="lh26">支付方式：</div>
            <div class="w60">
                [[${advancePayRecord.payType == 0 ? '现金' :'' }]]
                [[${advancePayRecord.payType == 1 ? '油卡' :'' }]]
                [[${advancePayRecord.payType == 2 ? '银行承兑' :'' }]]
            </div>
        </div>
        <div class="flex mt10">
            <div class="lh26">金额：</div>
            <div class="w60">
                ¥[[${#numbers.formatDecimal(advancePayRecord.amount,1,2)}]]
            </div>
        </div>
        <div class="flex mt10">
            <div class="lh26">附件：</div>
            <div class="w60">
                  <span class="picviewer" th:each="pic:${sysUploadFiles}">
                    <img style="height:100px;width: 100px" class="th_img" th:src="@{${pic.filePath}}"/>
                </span>

            </div>
        </div>
        <div class="flex mt10">
            <div class="lh26">付款时间：</div>
            <div class="w60">
               <input name="payOffTime" id="payOffTime" class="form-control" autocomplete="off" >
            </div>
        </div>
        <div class="flex mt10">
            <div class="lh26">转出账户：</div>
            <div class="w60 flex">
                <input name="accountName" id="accountName" required class="form-control valid" type="text" aria-required="true">
                <!--账户id-->
                <input name="outAccount" id="outAccount"  class="form-control valid"
                       type="hidden" aria-required="true">
                <div class="input-group-btn">
                    <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right" role="menu">
                    </ul>
                </div>
            </div>
        </div>
        <div class="flex mt10">
            <div class="lh26">收款账户：</div>
            <div class="w60">
                <select name="carrBankId" id="carrBankId" class="form-control" th:required="${advancePayRecord.payType == 0 || advancePayRecord.payType == 2}">
                    <option value=""></option>
                    <option th:each="dict : ${carrBankList}"
                            th:text="${dict.bankAccount}"
                            th:id="${dict.carrBankId}"
                            th:value="${dict.carrBankId}">
                    </option>
                </select>
            </div>
        </div>
        <input id="bankAccount" name="bankAccount" type="hidden">
        <div class="flex mt10">
            <div class="lh26">收款卡号：</div>
            <div class="w60">
                <input name="bankCard" id="bankCard" class="form-control"  type="text" disabled>
            </div>
        </div>
        <div class="flex mt10">
            <div class="lh26">收款银行：</div>
            <div class="w60">
                <input name="bankName" id="bankName" class="form-control"  type="text" disabled>
            </div>
        </div>
        <div class="flex mt10">
            <div class="lh26">油卡卡号：</div>
            <div class="w60">
                <input name="fuelCardNo" id="fuelCardNo" class="form-control"  type="text" autocomplete="false" th:required="${advancePayRecord.payType == 1}">
            </div>
        </div>
        <div class="flex mt10">
            <div class="lh26">备注：</div>
            <div class="w60">
                <textarea name="remark" maxlength="500" class="form-control" rows="3"></textarea>
            </div>
        </div>

    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js" />
<script th:inline="javascript">
    var prefix = ctx + "advancePay";

    var leaveQuota = [[${leaveQuota}]];

    //提交表单
    function submitHandler() {

        if ($.validate.form()) {
            $("#bankCard").attr("disabled", false);
            $("#bankName").attr("disabled", false);
            if($("#amount").val() > leaveQuota){
                $.modal.alertWarning("预借金额不能大于剩余额度！");
                return false;
            }
            var data = $("#form-client-add").serializeArray();
            $.operate.save(prefix + "/saveAdvancePayPayOff", data);
        }
    }

    $(function () {

        /**
         * 关键字提示查询 转入账户
         */
        $("#accountName").bsSuggest('init', {
            url: ctx + "finance/account/findAccount?paymentType=1&keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["accountName"],
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
            $("#accountName").val(data.accountName);
            $("#outAccount").val(data.accountId);
        })

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#payOffTime',
                type: 'datetime',
                trigger: 'click'
            });
        });

        //加载收款信息
        $('#carrBankId').change(function(){
            var id = $(this).find(":selected").attr("value");
            var data = {carrBankId:id};
            var url = ctx + "payManage/selectReceInfo";
            $.ajax({
                url : url,
                method : 'POST',
                data : data,
                success:function (data) {
                    $("#bankAccount").val(data.bankAccount);
                    $("#bankCard").val(data.bankCard);
                    $("#bankName").val(data.bankName);
                }
            })

        });
    });

</script>
</body>
</html>