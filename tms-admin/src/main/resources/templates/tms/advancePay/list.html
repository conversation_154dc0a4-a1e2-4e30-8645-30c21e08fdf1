<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细申请审核')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
<!--                            <label class="col-sm-4">应付单号：</label>-->
                            <div class="col-sm-12">
                                <select name="type" class="form-control">
                                    <option value="">全部</option>
                                    <option value="0" selected>预付</option>
                                    <option value="1">抵扣</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">应付单号：</label>-->
                            <div class="col-sm-12">
                                <select name="statusStr" id="statusStr" class="form-control selectpicker" aria-invalid="false" data-none-selected-text="状态" multiple>
                                    <option value="0" >新建</option>
                                    <option value="1">已核销</option>
                                    <option value="2">已抵扣</option>
                                    <option value="3">部分抵扣</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
<!--                            <label class="col-sm-4">申请人：</label>-->
                            <div class="col-sm-12">
                                <input name="carrName" class="form-control" type="text" placeholder="请输入承运商"
                                       maxlength="30"  aria-required="true" th:value="${carrName}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
<!--                        <label class="col-sm-6"></label>-->
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single  disabled" onclick="payOff()" shiro:hasPermission="tms:advancePay:payOff">
                <i class="fa fa-check"></i>核销
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "advancePay";
    //收款方式
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var collection_type = [[${@dict.getType('collection_type')}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: ctx + "advancePay/list",
            showToggle: false,
            showColumns: true,
            clickToSelect:true,
            height: 560,
            showFooter:true,
            uniqueId: "id",
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'carrName',
                    title: '承运商信息',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return row.carrName+"/"+row.carrierPhone
                    }
                },
                {
                    title: '状态',
                    field: 'status',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value) {
                            case 0:
                                return '<span class="label label-info pa2">新建</label>';
                            case 1:
                                return '<span class="label label-primary pa2">已核销</label>';
                            case 2:
                                return '<span class="label label-dange pa2">已抵扣</label>';
                            case 3:
                                return '<span class="label label-warning pa2">部分抵扣</label>';
                            default:
                                break;
                        }
                    }

                },
                {
                    title: '类型',
                    field: 'type',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '<span class="label label-warning pa2">预借</label>';
                            case 1:
                                return '<span class="label label-danger pa2">抵扣</label>';
                            default:
                                break;
                        }
                    }

                },
                {
                    title: '付款方式',
                    field: 'payType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '<span class="label label-primary pa2">现金</label>';
                            case 1:
                                return '<span class="label label-success pa2">油卡</label>';
                            case 2:
                                return '<span class="label label-info pa2">银行承兑</label>';
                            default:
                                break;
                        }
                    }

                },
                {
                    title: '预付金额',
                    align: 'right',
                    field: 'amount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }
                    }
                },
                {
                    title: '未抵扣金额',
                    align: 'right',
                    field: 'unUseAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }
                    }
                },
                {
                    title: '申请人',
                    align: 'left',
                    field: 'regUserName'
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'regDate'
                },
                {
                    title: '核销人',
                    align: 'left',
                    field: 'payOffMan'
                },
                {
                    title: '核销时间',
                    align: 'left',
                    field: 'payOffTime'
                },
                {
                    title: '收款人/卡号/银行',
                    align: 'left',
                    field: 'bankAccount',
                    formatter: function status(value, row, index) {
                        let html=[],htmlT=[];
                        if(value){
                            html.push( getValue(value) );
                        }
                        if(row.bankCard){
                            html.push( getValue(row.bankCard) );
                        }
                        if(html.length>0){
                            htmlT.push( html.join('/') );
                        }
                        if(row.bankName){
                            htmlT.push( getValue(row.bankName) );
                        }
                        return htmlT.join('<br/>')
                    }
                },
                {
                    title: '油卡卡号',
                    align: 'left',
                    field: 'fuelCardNo'
                },
                {
                    title: '备注',
                    align: 'left',
                    field: 'remark'
                },


            ]
        };

        $.table.init(options);
    });

    //审核
    function payOff() {
        var status = $.table.selectColumns("status").join();
        if(status != 0){
            $.modal.alertWarning("请选择新建的申请进行核销");
            return;
        }
        var id = $.table.selectColumns("id").join();
        var url = prefix + "/payOff?id="+id;
        $.modal.open('承运商预付核销',url,500,700);
    }

    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        $("#statusStr").selectpicker('refresh');
        searchPre();
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        data.statusStr =  $.common.join($('#statusStr').selectpicker('val'));
        $.table.search('role-form', data);
    }

    function getValue(val){
        if(val == null){
            val = "/";
        }
        return val
    }

</script>


</body>
</html>