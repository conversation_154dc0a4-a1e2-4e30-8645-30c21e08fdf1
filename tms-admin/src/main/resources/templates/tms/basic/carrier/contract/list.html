<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('异常跟踪')"/>
</head>
<body>
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="carrierId" th:value="${carrierId}">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运单号：</label>
                            <div class="col-sm-8">
                                <input name="lot" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <input id="hiddenText" type="text" style="display:none" />
                    <div  class="col-md-3 col-md-offset-6 col-sm-6 col-sm-offset-6">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">

        </div>


        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">
    $("#distpicker1").distpicker();
    $("#distpicker").distpicker();

    var prefix = ctx + "basic/carrier";
    var carrierId = [[${carrierId}]];
    var lotStatus = [[${lotStatus}]];
    var transCode = [[${@dict.getType('trans_code')}]];//运输方式
    var carLen = [[${@dict.getType('car_len')}]];//车长
    var carType = [[${@dict.getType('car_type')}]];//车型

    //查看合同
    function contract() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        var url = prefix + "/contractView/"+rows.join();
        $.modal.openTab("查看合同" , url);
    }

    //下载合同
    function downloadForWord(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        var url = prefix+"/contractDownload/"+rows.join();
        $.operate.saveModal(url, $('#role-form').serialize(),function (result) {
            if (result.code == web_status.SUCCESS){
                var hostport=document.location.host;
                var donwloadPath = "http://"+hostport + result.data;
                window.open(donwloadPath);
            }
        });
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        var options = {
            url: prefix + "/contractList",
            showSearch: false,
            showRefresh: false,
            pagination: false,
            showToggle:false,
            showColumns:true,
            clickToSelect:true,
            modalName: "运单合同",
            rememberSelected: true,
            uniqueId: 'entrustLotId',
            columns: [{
                checkbox: true
            },
                {
                    title: '运单号',
                    halign:'left',
                    align: 'left',

                    field : 'lot'
                },
                {
                    title: '运单状态',
                    halign: 'left',
                    align: 'left',

                    field : 'vbillstatus',
                    formatter: function status(value, row, index) {
                        for(var i=0 ; i<lotStatus.length ; i++){
                            if(lotStatus[i].value==value)
                                return lotStatus[i].context;
                        }
                        return value;
                    }
                },
                {
                    title: '提货地址',
                    halign:'left',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.deliProvinceName + row.deliCityName + row.deliAreaName
                    }
                },
                {
                    title: '提货时间',
                    halign:'left',
                    align: 'left',
                    field : 'reqDeliDate',
                },
                {
                    title: '到货地址',
                    halign:'left',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.arriProvinceName + row.arriCityName + row.arriAreaName
                    }
                },
                {
                    title: '到货时间',
                    halign:'left',
                    align: 'left',
                    field : 'reqArriDate',
                },
                {
                    title: '运输方式',
                    halign:'left',
                    align: 'left',
                    field : 'transType',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(transCode, value);
                    }
                },
                {
                    title: '车牌号',
                    halign:'left',
                    align: 'left',
                    field : 'carNo'
                },{
                    title: '司机名称',
                    halign:'left',
                    align: 'left',
                    field : 'driverName'
                },
                {
                    title: '车长',
                    halign:'left',
                    align: 'left',
                    field : 'carLen',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(carLen, value);
                    }
                },
                {
                    title: '车型',
                    halign:'left',
                    align: 'left',
                    field : 'carType',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(carType, value);
                    }
                }, {
                    title: '总件数',
                    halign:'left',
                    align: 'left',
                    field : 'numCount'
                }, {
                    title: '总重量',
                    halign:'left',
                    align: 'left',
                    field : 'weightCount'
                }, {
                    title: '总体积',
                    halign:'left',
                    align: 'left',
                    field : 'volumeCount'
                }, {
                    title: '结算金额',
                    halign:'left',
                    align: 'right',
                    field : 'costAmount'
                },
                {
                    title: '备注',
                    halign:'left',
                    align: 'left',
                    field : 'memo'
                }
            ]
        };

        $.table.init(options);
    });




</script>

</body>

</html>