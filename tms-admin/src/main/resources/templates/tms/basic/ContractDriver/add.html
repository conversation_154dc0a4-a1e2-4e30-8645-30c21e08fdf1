<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('basic-司机合同')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-carrier-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="id" th:value="${contractDriver.id}">
        <!-- 联系方式 start -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">司机合同</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
            	<!--   第 0 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >模板ID：</span></label>
                                <div class="col-sm-8">
                                    <input name="tmplateId"  id="tmplateId" class="form-control" type="text"
                                           maxlength="25" required>
                                </div>
                            </div>
                        </div>
                    </div>
            	<!--   第 1 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >甲方：</span></label>
                                <div class="col-sm-8">
                                    <input name="partyA"  id="partyA" class="form-control" type="text"
                                           maxlength="25" required>
                                </div>
                            </div>
                        </div>
                    </div>
            	<!--   第 2 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >乙方：</span></label>
                                <div class="col-sm-8">
                                    <input name="partyB"  id="partyB" class="form-control" type="text"
                                           maxlength="25" required>
                                </div>
                            </div>
                        </div>
                    </div>
            	<!--   第 3 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >签名URL：</span></label>
                                <div class="col-sm-8">
                                    <input name="signUrl"  id="signUrl" class="form-control" type="text"
                                           maxlength="25" required>
                                </div>
                            </div>
                        </div>
                    </div>
            	<!--   第 4 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >签名时间：</span></label>
                                <div class="col-sm-8">
                                    <input name="signDate"  id="signDate" class="form-control" type="text"
                                           maxlength="25" required>
                                </div>
                            </div>
                        </div>
                    </div>
            	<!--   第 5 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >合同开始时间：</span></label>
                                <div class="col-sm-8">
                                    <input name="startDate"  id="startDate" class="form-control" type="text"
                                           maxlength="25" required>
                                </div>
                            </div>
                        </div>
                    </div>
            	<!--   第 6 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >合同结束时间：</span></label>
                                <div class="col-sm-8">
                                    <input name="endDate"  id="endDate" class="form-control" type="text"
                                           maxlength="25" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
        <!--联系方式end-->
        <!--凭证上传 start-->
        <div class="panel-group" id="accordionTwo">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">凭证上传</a>
                    </h5>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row no-gutter">
                            <div class="col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group">
                                    <label class="col-sm-2">凭证附件：</label>
                                    <div class="col-sm-10">
                                        <input name="evidence" id="evidence" class="form-control" type="file">
                                        <input type="hidden" id="evidenceId" name="evidenceId">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--凭证上传 end-->
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "basic/contractDriver";
    /**
     * 初始化
     */
    $(function () {
        // 表单验证
        $("#form-carrier-add").validate({
            focusCleanup: true
        });
        //展开，折叠
        $('#collapseTwo').collapse('show');
        //图片功能
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("evidence", "evidenceId", picParam);
    });

    

    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            $('#evidence').fileinput('upload');
            jQuery.subscribe("cmt", commit);
        }
    }

    function commit() {
        var data = $("#form-carrier-add").serializeArray();
        $.operate.saveTab(prefix + "/add", data);
    }
</script>
</body>
</html>