<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商协议列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .table-striped {
        height: calc(100% - 134px);
    }
    .left-fixed-body-columns{
        z-index: 10;
    }
    .sm_icon{
        width: 18px;
        height: 18px;
        display: inline-block;
        line-height: 18px;
        text-align: center;
        font-size: 16px;
        color: #ed5565;
    }
    .sm_text{
        display: inline-block;
        margin-left: 10px;
        line-height: 30px;
        color: #ed5565;
    }
    .sm_btn{
        display: inline-block;
        width: 100px;
        height: 30px;
        line-height: 30px;
        margin-left: 20px;
        text-align: center;
        background: #fde6de;
        border: 1px #ff9999 solid;
        color: #ed5565;
        border-radius: 20px;
        cursor: pointer;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row sm" th:if="${expiredPeriod != 0}">
                    <span class="sm_icon">
                        <i class="fa fa-bell-o"></i>
                    </span>
                    <span class="sm_text" id="warn_msg">
                        共<span class="fcff3" th:text="${expiredPeriod}"></span>个协议价即将到期或已到期，请及时更新
                    </span>
                    <span class="sm_btn" onclick="selectExpiredPeriod()">
                        <input type="hidden" id="expiredPeriod" name="expiredPeriod"/>
                        快速筛选
                    </span>
                </div>

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">承运商名称：</label>
                            <div class="col-sm-8">
                                <input name="carrName" placeholder="请输入承运商名称" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <input id="hiddenText" type="text" style="display:none" />

                    <div class="col-md-6 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="tableQuery();"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="addPeriod()" >
                <i class="fa fa-plus"></i> 新增协议价
            </a>
            <!--<a class="btn btn-danger " onclick="clearPeriod()" shiro:hasPermission="basic:carrierProtocol:clear">
                <i class="fa fa-remove"></i> 清空线路
            </a>-->
            <a class="btn btn-danger " onclick="$.operate.removeAll()" shiro:hasPermission="basic:carrierProtocol:remove">
                <i class="fa fa-trash"></i> 删除
            </a>
           <!-- <a class="btn btn-primary" onclick="$.operate.addTab()" shiro:hasPermission="basic:carrierProtocol:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="basic:carrierProtocol:remove">
                <i class="fa fa-remove"></i> 删除
            </a>-->
            <!--<a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="basic:carrierProtocol:import">
                <i class="fa fa-upload"></i> 导入
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="basic:carrierProtocol:export">
                <i class="fa fa-download"></i> 导出
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "basic/carrierProtocol";

    //计价方式List
    var pricingMethodEnumList = [[${pricingMethodEnumList}]];
    //权限
    var editFlag = [[${@permission.hasPermi('basic:carrierProtocol:edit')}]];

    $(function () {
        // 初始化省市区
        //$.provinces.init("startProvinceId","startCityId","startAreaId");
        //$.provinces.init("endProvinceId","endCityId","endAreaId");

        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $("#expiredPeriod").val("");
                $.table.search()
            }
        });
        var options = {
            url: prefix + "/list",
            //createUrl: prefix + "/add",
            //detailUrl: prefix + "/detail",
            //removeUrl: ctx + "basic/CarrierPeriod/removePeriods",
            removeUrl: prefix + "/delinfo",
            //exportUrl: prefix + "/export",
            //importUrl: prefix + "/importData",
            //importTemplateUrl: prefix + "/importTemplate",
            clickToSelect:true,
            showToggle:false,
            showColumns:true,
            modalName: "承运商协议",
            fixedColumns: true,
            height: 560,
            fixedNumber:0,
            uniqueId:"carrierId",
            columns: [
                {
                    checkbox:true
                },
                {
                    title: '操作',
                    align: 'center',
                    field: 'carrierId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        //actions.push('<a class="btn btn-xs ' + editFlag + ' " href="javascript:void(0)" title="添加区间" onclick="edit(\'' + row.carrierProtocolId + '\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                        actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="查看协议价" onclick="editPeriodPrice(\'' + row.carrierId + '\')"><i class="fa fa-navicon" style="font-size: 15px;"></i></a> ');
                        /*[# th:if="${@shiroUtils.getUserId() == 1}"]*/
                        actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="转成新协议价" onclick="convertToNew(\'' + row.carrierId + '\')">转</a> ');
                        actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="查看协议价(新)" onclick="editPeriodPriceX(\'' + row.carrierId + '\')"><i class="fa fa-navicon" style="font-size: 15px;"></i></a> ');
                        /*[/]*/
                        actions.push('<a class="btn btn-xs ' + editFlag + ' " href="javascript:void(0)" title="修改基础数据" onclick="editBasePeriodPrice(\'' + row.carrierId + '\',\''+row.id+'\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a> ');
                        return actions.join('');
                    }
                },
                {
                    title: '承运商名称',
                    align: 'left',
                    field: 'carrName'
                },
                {
                    title: '线路数量',
                    align: 'left',
                    field: 'lineCnt',
                    formatter:function(value,row){
                        return value ;
                    }
                },
                {
                    title: '预警日期',
                    align: 'left',
                    field: 'warningDate',
                },
                {
                    title: '有效期至',
                    align: 'left',
                    field: 'expiredDate',
                },
                {
                    title: '路线匹配模式',
                    align: 'left',
                    field: 'lineMatchType',
                    formatter: function(value,row){
                        if(null == value)return '-';
                        if(1 == value)return '匹配到区';
                        if(2 == value)return '匹配到市';
                    }
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate'
                },

            ]
        };
        $.table.init(options);
    });

    /**
     * 修改
     * @param id
     */
    function edit(id) {
        var url = prefix + "/edit/"+id;
        $.modal.openTab('承运商协议修改',url);
    }

    function clearPeriod() {
        var ids = $.table.selectColumns("carrierId");
        if (ids.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认清空勾选的" + ids.length + "条协议价的数据吗？", function(){
            $.ajax({
                url:"/basic/CarrierPeriod/removePeriods",
                data: {ids:ids.join(',')},
                type: 'post',
                success: function(result){
                    if (result.code == 0) {
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg)
                    }
                }
            })
        })
    }

    /**
     * 添加协议价
     */
    function editPeriodPrice(carrierId){
        let url = prefix + "/editPeriodPrice?carrierId="+carrierId;
        //let data = {};
        $.modal.openTab("编辑协议价",url);
    }

    function editPeriodPriceX(carrierId){
        let url = prefix + "/editPeriodPriceX?carrierId=" + carrierId;
        $.modal.openTab("编辑协议价", url);
    }

    /**
     * 添加协议价
     */
    function editBasePeriodPrice(carrierId){
        //let url = prefix + "/editBasePeriodPrice?carrierId="+carrierId;
        let url = prefix + "/addPeriod?carrierId="+carrierId;
        //let data = {};
        //$.modal.openTab("编辑基础数据",url);

        //let height = $('.container-div').height()
        $.modal.open("编辑基础信息", url, "900", "600");
    }


    /**
     * 添加区间
     * @param id
     */
    function addPeriod() {
        var url = prefix + "/addPeriod?carrierId=";

        /* $.modal.open("添加区间", url,"800","600",function(pageNo,layerObj){
             layerObj.find('iframe')[0].contentWindow.submitHandler(addCallBack,pageNo);
         })*/
        var height = $('.container-div').height()
        $.modal.open("新增协议价",url,"900",height);
    }
    
    function selectExpiredPeriod() {
        $("#expiredPeriod").val(1);
        $.table.search()
    }

    function tableQuery(){

        $("#expiredPeriod").val("");
        $.table.search()
    }

    function convertToNew(carrierId) {
        $.modal.confirm("确定继续？", function(){
            var tt = $.modal.layerLoading("正在转换，请稍候...");
            $.ajax({
                url: prefix + "/convertToNew",
                data: "carrierId=" + carrierId,
                type: 'post',
                success: function (r) {
                    layer.close(tt)
                    $.modal.msgSuccess(r.msg);
                }
            })
        })
    }
</script>

</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>