<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('司机列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="isexception" th:value="${isexception}">
                <div class="row">
                    <div class="col-sm-5">
                        <div class="form-group">
<!--                    <div class="col-md-3 col-sm-6">-->
                            <label class="col-sm-4">司机名称：</label>
                            <div class="col-sm-8">
                                <input id="driverName" name="driverName" placeholder="请输入司机名称" class="form-control valid"
                                       type="text" aria-required="true">
                            </div>
                        </div>
<!--                    </div>-->
                </div>
                <div class="col-sm-2">
                    <label class="col-sm-4"></label>
                    <div class="form-group">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </div>
                </div>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table id="bootstrap-table" data-mobile-responsive="true" style="table-layout: fixed;"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">
    var prefix = ctx + "basic/driver";
    var method = [[${method}]]
    $(function () {
        var options = {
            url: prefix + method,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { radio: true },
                {
                    title: '司机ID',
                    field: 'driverId',
                    visible:false
                },
                {
                    title: '司机编号',
                    width: '160px',
                    align: 'left',
                    field: 'driverCode',
                },
                {
                    title: '司机名称',
                    width: '120px',
                    align: 'left',
                    field: 'driverName'
                },
                {
                    title: '手机号码',
                    width: '120px',
                    align: 'left',
                    field: 'phone'
                }
            ]
        };

        $.table.init(options);
    });

    /** 获取选中行*/
    function getChecked() {
        return $.btTable.bootstrapTable('getSelections');
    }

    /**
     * 选择承运人后的提交方法
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.close();

        // 选中的承运人ID
        parent.$("#driverId").val(rows.join());
        // 选中的司机名称
        parent.$("#driverName").val($.table.selectColumns("driverName"));
        parent.$("#phone").val($.table.selectColumns("phone"));
        parent.$("#driverMobile").val($.table.selectColumns("phone"));
    }


</script>

</body>
</html>