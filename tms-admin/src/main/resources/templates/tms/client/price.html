<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户详情')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: galpop-css" />
</head>
<style>
    .bg_title{
        font-size: 15px;
        font-weight: bold;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 120px;
        line-height: 20px;
        text-align: left;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .fw{
        font-weight: bold;
    }
    .btn-success {
        color: #fff !important;
        background-color: #5cb85c !important;
        border-color: #4cae4c !important;
    }
    .btn-success:hover,
    .btn-success:focus,
    .btn-success:active,
    .btn-success.active,
    .open .dropdown-toggle.btn-success {
        color: #fff !important;
        background-color: #47a447 !important;
        border-color: #398439 !important;
    }
    .bor1{
        border-right: 1px #eee solid;
    }

    .good{
        display: inline-block;
        padding: 10px 10px;
        border: 1px #eee solid;
    }
    .bg_gr{
        background: #f8f8f8 !important;
    }
    .borl0{
        border-left: 0px #eee solid;
    }
</style>
<body>
<div class="form-content">
    <form id="form-user-add" class="form-horizontal" novalidate="novalidate">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseEight">指导价</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseEight">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabEight" class="custom-tab table table-bordered">
                            <thead>
                            <tr>
                                <th style="width: 30%;">线路名称</th>
                                <th style="width: 14%;">回单预警时间</th>
                                <th style="width: 10%;">车长</th>
                                <th style="width: 10%;">车型</th>
                                <th style="width: 10%;">指导价</th>
                                <th style="width: 13%;">开始时间</th>
                                <th style="width: 13%;">结束时间</th>
                            </tr>
                            </thead>
                            <tbody>

                            <tr th:each="mapS,status:${guidePrice}">
                                <td>
                                    <div class="input-group" th:text="${mapS.lineName}"></div>
                                </td>
                                <td>
                                    <div class="input-group">D+2</div>
                                </td>
                                <td>
                                    <div class="input-group" th:each="map,status:${carLen}" th:if="${map.dictValue==''+mapS.carLen}" th:text="${map.dictLabel}"></div>
                                </td>
                                <td>
                                    <div class="input-group" th:each="map,status:${carType}" th:if="${map.dictValue==''+mapS.carType}" th:text="${map.dictLabel}"></div>
                                </td>
                                <td>
                                    <div th:align="right" th:text="${mapS.guidingPrice}"></div>
                                <td>
                                    <div class="input-group" th:text="${#dates.format(mapS.startDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                </td>
                                <td>
                                    <div class="input-group" th:text="${#dates.format(mapS.endDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--end-->
                </div>
            </div>

        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: galpop-js" />
<script th:inline="javascript">
    $(function () {

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');
        $('#collapseSeven').collapse('show');
        // 图片预览插件
        $('.galpop').galpop();
        var provinceId =  [[${client.provinceId}]];
        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            success: function (result) {
                for (var i in result) {
                    if (result[i].PROVINCE_CODE === provinceId){
                        $('#provinceId').text(result[i].PROVINCE_NAME );
                    }
                }
            }
        });
        var cityId = [[${client.cityId}]];
        $.ajax({
            type: "get",
            url: ctx + "province/city?method=1&code=" + provinceId,
            success: function (result) {
                for (var i in result) {

                    if (result[i].CITY_CODE === cityId){

                        $('#cityId').text(result[i].CITY_NAME );
                    }
                }
            }
        });
        var areaId =  [[${client.areaId}]];
        var address =  [[${client.address}]];
        $.ajax({
            type: "get",
            url: ctx + "province/city?method=2&code=" + cityId,
            success: function (result) {
                for (var i in result) {
                    if (result[i].AREA_CODE === areaId){
                        $('#areaId').text(result[i].AREA_NAME );
                        $('#address').text(address);
                    }
                }
            }
        });
    });

</script>


</body>

</html>