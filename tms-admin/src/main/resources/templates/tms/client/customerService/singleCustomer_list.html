<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户-客服绑定列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="col-sm-4">客服姓名：</label>
                            <div class="col-sm-8">
                                <input name="serviceName" id="serviceName" placeholder="请输入客服姓名"
                                       class="form-control valid" type="text" maxlength="50">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="col-sm-4">客服电话：</label>
                            <div class="col-sm-8">
                                <input name="servicePhone" id="servicePhone" placeholder="请输入客服电话"
                                       class="form-control valid" type="text" maxlength="50">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6"></div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="add()" shiro:hasPermission="client:client:accountList">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="client:client:accountList">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true" style="table-layout: fixed;"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "tms/customerService";
    var customerId = [[${customerId}]];
    var customerName = [[${customerName}]];
    //运输方式
    var trans_code = [[${@dict.getType('trans_code')}]];
    $(function () {
        var options = {
            url: prefix + "/list?customerId=" + customerId,
            removeUrl: prefix + "/remove",
            showToggle: false,
            showColumns: false,
            modalName: "客户账号",
            uniqueId: "customerServiceId",
            columns: [
                {checkbox: true},
                {title: '客服姓名', field: 'serviceName', width: '30%', align: 'left'},
                {title: '手机', field: 'servicePhone', align: 'left'},
                {
                    title: '客服类型', field: 'serviceType'
                    ,formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(trans_code, value);
                    }
                }
            ]
        };

        $.table.init(options);
        $(document).keyup(function (e) {
            var key = e.which;
            if (key === 13) {
                //查询方法
                $.table.search();
            }
        });
    });

    // 账号添加页面
    function add() {
        $.modal.openTab('添加客户客服绑定', prefix + "/add?customerId=" + customerId + "&customerName=" + customerName)
    }
</script>

</body>
</html>