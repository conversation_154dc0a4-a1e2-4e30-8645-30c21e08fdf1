<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户绑定：add')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body>
<div class="form-content">
    <form id="form-userCustomer-add" class="form-horizontal" novalidate="novalidate">
        <!--客户id-->
        <input type="hidden" name="customerId" th:value="${customerId}">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">客户名称：[[${customerName}]]</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span style="color: red">用户：</span></label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input class="form-control" type="text" id="userName" name="userName" required>
                                        <input name="userId" id="userId" class="form-control" type="hidden" aria-required="true">
                                        <div class="input-group-btn">
                                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span style="color: red">客服类型：</span></label>
                                <div class="col-sm-8">
                                    <select class="form-control" name="serviceType" th:with="type=${@dict.getType('trans_code')}">
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "tms/customerService";
    $(function () {
        // 表单验证
        $("#form-userCustomer-add").validate({
            focusCleanup: true
        });
        //展开，折叠
        $('#collapseTwo').collapse('show');

        /**
         * 关键字提示查询
         */
        $("#userName").bsSuggest('init', {
            url: ctx + "system/user/findUserInfo?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "userName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["userName","deptName","phonenumber"],
            effectiveFieldsAlias: {"userName":"用户名","deptName":"部门","phonenumber":"手机号码"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $("#userName").val(data.userName);
            $("#userId").val(data.userId);
        })
    });

    /**
     * 系统用户
     */
    function selectUser() {
        $.modal.open("选择用户", ctx + "system/user/selectRadioUser/listSys");
    }

    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function(){
                var data = $("#form-userCustomer-add").serializeArray();
                $.operate.saveTab(prefix + "/add", data);
            })
        }
    }


</script>
</body>
</html>