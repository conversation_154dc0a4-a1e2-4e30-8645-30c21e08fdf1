<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户绑定：add')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body>
<div class="form-content">
    <form id="form-userCustomer-add" class="form-horizontal" novalidate="novalidate">
        <!--客户id-->
        <input type="hidden" name="customerIds" th:value="${customerIds}">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">客户名称：[[${customerName}]]</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row" th:with="type=${@dict.getType('trans_code')}">
                        <div class="col-sm-12" th:each="dict,status : ${type}">
                            <div class="form-group">
                                <label class="col-sm-2 col-md-1 col-lg-1">[[${dict.dictLabel}]]：</span></label>
                                <div class="col-sm-3 col-md-3 col-lg-3">
                                    <div class="input-group">
                                        <input th:name="|userList[${status.index}].transCode|" type="hidden" th:value="${dict.dictValue}">
                                        <input class="form-control" type="text" th:id="|userName${dict.dictValue}|" th:name="|userList[${status.index}].userName|" >
                                        <input th:name="|userList[${status.index}].userId|" th:id="|userId${dict.dictValue}|" class="form-control" type="hidden" aria-required="true">
                                        <div class="input-group-btn">
                                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "tms/customerService";
    var trans_code = [[${@dict.getType('trans_code')}]];
    $(function () {
        // 表单验证
        $("#form-userCustomer-add").validate({
            focusCleanup: true
        });
        //展开，折叠
        $('#collapseTwo').collapse('show');

        for(var i = 0 ; i < trans_code.length ; i++){
            const dictValue = trans_code[i].dictValue;
            $("#userName"+dictValue).bsSuggest('init', {
                url: ctx + "system/user/findUserInfo?keyword=",
                indexId: 0,
                showBtn: false,
                allowNoKeyword: false,
                getDataMethod: "url",
                keyField: "userName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
                effectiveFields: ["userName","deptName","phonenumber"],
                effectiveFieldsAlias: {"userName":"用户名","deptName":"部门","phonenumber":"手机号码"},
                delay: 300,
                searchingTip: '搜索中...',
                hideOnSelect: true,
                maxOptionCount: 10,
                inputWarnColor: '',
            }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
                console.log("#userName"+dictValue)
                $("#userName"+dictValue).val(data.userName);
                $("#userId"+dictValue).val(data.userId);
            })
        }

    });

    /**
     * 系统用户
     */
    function selectUser(target) {
        $.modal.open("选择用户", ctx + "system/user/selectUser/listSys?target=" + (target||''));
    }

    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function(){
                var data = $("#form-userCustomer-add").serializeArray();
                $.operate.saveTab(prefix + "/batchBindService", data);
            })
        }
    }
</script>
</body>
</html>