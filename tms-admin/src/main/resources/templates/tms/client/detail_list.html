<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户信息列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">客户名称：</label>
                            <div class="col-sm-8">
                                <input name="custName" id="custName" placeholder="请输入客户名称"
                                       class="form-control valid" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">业务员：</label>
                            <div class="col-sm-8">
                                <input name="psndocName"  id="psndocName"  placeholder="请输入业务员" class="form-control valid" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">结算组：</label>
                            <div class="col-sm-8">
                                <select name="balaDept" id="balaDept" class="form-control valid"
                                        aria-invalid="false" required>
                                    <option value=""></option>
                                    <option th:each="mapS,status:${balanceDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">联系人：</label>
                            <div class="col-sm-8">
                                <input name="contact" id="contact" class="form-control" type="text"
                                       maxlength="30" autocomplete="off" required th:value="${userName}">

                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">联系人电话：</label>
                            <div class="col-sm-8">

                                <input name="phone" id="phone" class="form-control" type="text"
                                       maxlength="30" autocomplete="off" >

                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运营组：</label>
                            <div class="col-sm-8">
                                <select name="salesDept" id="salesDept" class="form-control valid"
                                        aria-invalid="false" required>
                                    <option value=""></option>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">创建人：</label>
                            <div class="col-sm-8">
                                <input name="corUserId" placeholder="请输入创建人" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">客户级别：</label>
                            <div class="col-sm-8">
                                <select id="customerLevel" name="customerLevel" class="form-control"
                                        th:with="type=${@dict.getType('customer_level')}" aria-invalid="false">
                                    <option value=""></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div >
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">地址：</label>
                                <div class="col-sm-8">
                                    <select  name="provinceId" id="provinceId"  class="form-control valid" aria-invalid="false">

                                    </select>

                                </div>
                            </div>
                        </div>

                        <div class="col-sm-2">
                            <select name="cityId" id="cityId" class="form-control valid" aria-invalid="false"></select>
                        </div>

                        <div class="col-sm-2">
                            <select name="areaId" id="areaId" class="form-control valid" aria-invalid="false"></select>
                        </div>


                        <div class="col-sm-2">
                            <input name="address" id="address" placeholder="请输入详细地址" class="form-control" type="text"
                                   maxlength="350" required="" aria-required="true">
                        </div>

                    </div>


                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2">创建时间：</label>
                            <div class="col-sm-8">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="regDate" placeholder="开始时间">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endtDate"  name="corDate" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="$.operate.addTab()" shiro:hasPermission="client:client:insert">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary multiple disabled" onclick="check()" shiro:hasPermission="client:client:check">
                <i class="fa fa-check"></i>审核
            </a>
            <a class="btn btn-primary"  onclick="$.table.importExcel()" shiro:hasPermission="client:client:import">
                <i class="fa fa-upload"></i> 导入
            </a>
            <a class="btn btn-warning" onclick="exportExcel()"  shiro:hasPermission="client:client:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="client:client:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-primary" onclick="openMargin()" shiro:hasPermission="tms:margin:view">
                <i class="fa fa-dollar"></i> 保证金
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">
    var billingType = [[${@dict.getType('billing_type')}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    var balaType = [[${@dict.getType('bala_type')}]];
    var clientType = [[${@dict.getType('cust_Type')}]];
    var customerLevel = [[${@dict.getType('customer_level')}]];//客户类别

    var prefix = ctx + "client";
    var groupId = [[${groupId}]];


    $(function () {

        var options = {
            url: ctx + "group/detail_List?groupId="+groupId,
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            fixedColumns: true,
            rememberSelected: true,
            fixedNumber:4,
            height: 560,
            modalName: "客户",
            clickToSelect:true,
            uniqueId: "customerId",
            columns: [{
                field: 'state',
                checkbox: true
            },
                {
                    title: '操作',
                    field: 'customerId',
                    formatter: function(value,row,index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('client:client:edit')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="修改" onclick="edit(\'' + value + '\')"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>');
                        }
                        if ([[${@permission.hasPermi('client:client:detail')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="明细"  onclick="detail(\'' + value + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('client:client:contract')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="合同"  onclick="contract(\'' + value + '\')"> <i class="fa fa-folder-o" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('client:client:accountList')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="客户账号" onclick="accountList(\'' + value + '\')"><i class="fa fa-user" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:custAddress:view')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="客户地址" onclick="addressList(\'' + value + '\')"><i class="fa fa-location-arrow" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:custGoods:view')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="客户货品" onclick="goodList(\'' + value + '\')"><i class="fa fa-briefcase" style="font-size: 15px;"></i></a>');
                        }
                        return actions.join('');
                    }

                },
                {
                    title: '客户编码',
                    field: 'custCode',
                    align: 'left'
                },
                {
                    title: '客户名称',
                    field: 'custName',
                    align: 'left'
                },
                {
                    title: '客户简称',
                    field: 'custAbbr',
                    align: 'left'
                },
                {
                    title: '审核状态',
                    align: 'left',
                    field: 'checkStatus',
                    formatter: function(value, item, index) {
                        if (item.checkStatus == 0) {
                            return '待审核';
                        }
                        if (item.checkStatus == 1) {
                            return '<span class="label label-primary">审核通过</span>';
                        }
                        if (item.checkStatus == 2) {
                            return '<span class="label label-danger">审核未通过</span>';
                        }
                    }

                },
                {
                    title: '审核时间',
                    align: 'left',
                    field: 'checkDate'
                },
                {
                    title: '审核人',
                    align: 'left',
                    field: 'checkMan'
                },
                // {
                //     title: '客户类别',
                //     field: 'custType',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //
                //         return $.table.selectDictLabel(clientType, value);
                //     }
                //
                // },
                {
                    title: '客户级别',
                    field: 'customerLevel',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(customerLevel, value);
                    }
                },
                {
                    title: '结算组',
                    field: 'balaDeptName',
                    align: 'left'
                },
                {
                    title: '运营组',
                    field: 'salesDeptName',
                    align: 'left'
                },
                {
                    title: '驻场组',
                    field: 'stationDeptName',
                    align: 'left'
                },
                {
                    title: '结算公司',
                    field: 'balaCorp',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(balaCorp, value);
                    }

                },
                {
                    title: '业务员',
                    field: 'psndocName',
                    align: 'left'
                },
                {
                    title: '业务员联系方式',
                    field: 'psncontact',
                    align: 'left'
                },
                {
                    title: '联系人',
                    field: 'contact',
                    align: 'left'
                },
                {
                    title: '手机',
                    field: 'phone',
                    align: 'left'
                },

                {
                    title: '详细地址',
                    field: 'address',
                    align: 'left'
                },
                /*  {
                      title: '开票公司',
                      field: 'billingCorp',
                      align: 'left',
                      formatter: function status(value, row, index) {
                          return $.table.selectDictLabel(balaCorp, value);
                      }
                  },
                  {
                      title: '纳税识别号',
                      field: 'taxIdentify',
                      align: 'left'
                  },
                  {
                      title: '开票类型',
                      field: 'billingType',
                      align: 'left',
                      formatter: function status(value, row, index) {
                          return $.table.selectDictLabel(billingType, value);
                      }
                  },
                  {
                      title: '发票抬头',
                      field: 'billingPayable',
                      align: 'left'
                  },
                  {
                      title: '开户银行',
                      field: 'bank',
                      align: 'left'
                  },

                  {
                      title: '开户账号',
                      field: 'bankAccount',
                      align: 'left'
                  }
                  ,
                  {
                      title: '注册地址',
                      field: 'registerAddr',
                      align: 'left'
                  }
                  ,
                  {
                      title: '注册资金',
                      field: 'registerCapital',
                      align: 'right'
                  }
                  ,
                  {
                      title: '法人代表',
                      field: 'legalRepresent',
                      align: 'left'
                  }
                  ,
                  {
                      title: '收件人',
                      field: 'addressee',
                      align: 'left'
                  }
                  ,
                  {
                      title: '收件人联系方式',
                      field: 'addresseeContact',
                      align: 'left'
                  }
                  ,
                  {
                      title: '收件人地址',
                      field: 'addresseeAddr',
                      align: 'left'
                  },*/
                {
                    title: '账期',
                    field: 'paymentDays',
                    align: 'left'
                },
                {
                    title: '创建人',
                    align: 'left',
                    field: 'regUserId'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate'
                },
                {
                    title: '修改人',
                    align: 'left',
                    field: 'corUserId'
                }
            ]
        };

        $.table.init(options);
        // 初始化省市区
        $.provinces.init("provinceId","cityId","areaId");
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
        });
    });




    /*运营组选择树*/
    function selectSalesDeptTree() {
        var options = {
            title: '部门选择',
            width: "380",
            url: prefix + "/selectDeptTree/61",
            callBack: salesDeptSubmit
        };
        $.modal.openOptions(options);
    }

    function salesDeptSubmit(index, layero) {
        var body = layer.getChildFrame('body', index);
        $("#salesDept").val(body.find('#treeId').val());
        $("#salesDeptName").val(body.find('#treeName').val());
        layer.close(index);
    }

    /*结算组选择树*/
    function selectBalanceDeptTree() {
        var options = {
            title: '部门选择',
            width: "380",
            url: prefix + "/selectDeptTree/65",
            callBack: balanceDeptSubmit
        };
        $.modal.openOptions(options);
    }

    function balanceDeptSubmit(index, layero) {
        var body = layer.getChildFrame('body', index);
        $("#balaDept").val(body.find('#treeId').val());
        $("#balaDeptName").val(body.find('#treeName').val());
        layer.close(index);
    }
    /**
     * 跳转详情画面
     */
    function detail(clientId) {
        var url = prefix + "/detail?clientId="+clientId;
        $.modal.openTab($.table._option.modalName + "详细", url);
    }

    /**
     * 跳转合同信息画面
     * @param id
     */
    var contract = function (id) {
        var url = prefix + "/contract?id="+id;
        $.modal.openTab("合同信息", url);
    }

    /**
     * 跳转客户信息修改页面
     * @param id
     */
    function edit(id) {
        var url = prefix + "/edit?clientId="+id;
        $.modal.openTab("客户信息修改", url);
    }

    function reset() {
        $("#balaDept").val("");
        $("#salesDept").val("");
        $.form.reset();
    }

    /**
     *  跳转客户账号页面
     * @param id
     */
    function accountList(id) {
        var url = ctx + "custUser?customerId="+id;
        $.modal.openTab('客户账号',url);
    }
    /**
     *  跳转客户地址页面
     * @param id
     */
    function addressList(id) {
        var url = ctx + "custAddress?customerId="+id;
        $.modal.openTab('客户地址',url);
    }

    /**
     *  审核
     */
    function check(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/check";
        //获取状态
        var checkStatusArr = $.btTable.bootstrapTable('getSelections');
        //循环判断是否能够进行审核
        for(var i=0;i<checkStatusArr.length;i++){
            if(checkStatusArr[i]['checkStatus'] == 1){
                $.modal.alertWarning("通过的记录不能再进行审核操作");
                return;
            }
            var flag = false;
            if(checkStatusArr[i]['checkStatus'] == 2){
                layer.confirm("请审核选中的" + rows.length + "条数据",{
                    btn:["待审核"]
                },function (index, layero) {
                    if(!flag){
                        flag = true;
                        layer.close(index);
                        var data = { "ids": rows.join(),"flag":"0" };
                        $.operate.submit(url, "post", "json", data);
                    }
                })
                return ;
            }
        }
        var flag = false;
        layer.confirm("请审核选中的" + rows.length + "条数据",{
            btn:["通过","不通过"]
        },function (index, layero) {
            if(!flag){
                flag = true;
                layer.close(index);
                var data = { "ids": rows.join(),"flag":"1" };
                $.operate.submit(url, "post", "json", data);
            }
        },function (index) {
            if(!flag){
                flag = true;
                layer.close(index);
                var data = { "ids": rows.join(),"flag":"2" };
                $.operate.submit(url, "post", "json", data);
            }
        });
    }

    /**
     *  跳转货品页面
     * @param id
     */
    function goodList(id) {
        var url = ctx + "custGoods?customerId="+id;
        $.modal.openTab('客户货品',url);
    }

    // 导出客户
    function exportExcel (){
        var rows =  $.table.selectFirstColumns();
        var confirm = "确定导出选中的"+rows.length+"条";
        var data = {"customerIds":rows.join()};
        if (rows.length === 0) {
            confirm = "确定导所有的";
            data = {"customerIds":""};
        }
        $.modal.confirm(confirm + $.table._option.modalName + "吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post($.table._option.exportUrl, data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    /**
     * 打开保证金
     */
    function openMargin() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length != 1) {
            $.modal.alertWarning("只能选择一个客户信息");
            return;
        }
        var customerId =  $.table.selectFirstColumns();
        $.modal.openTab("查看保证金", ctx + "tms/margin?customerId="+customerId);
    }
</script>

</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>