<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户合同')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>

</head>
<style type="text/css">
    .cur{
        cursor: pointer;
    }
    .switch{
        width:48px;
        height:24px;
        /* border-radius:16px; */
        overflow: hidden;
        vertical-align:middle;
        position:relative;
        display: inline-block;
        background:#ccc;
        box-shadow: 0 0 1px #1ab394;
     
        border-radius: 2em;
        padding: 2px;
        transition: all .4s ease;
 
    }
    .switch input{
        visibility: hidden;
    }
    .switch span{
        position:absolute;
        top:0;
        left:0;
        /* border-radius: 50%; */
        background:#fff;
        width:50%;
        height:100%;
        transition:all linear 0.2s;

        border-radius: 2em;
    }
    .switch span::before{
        position: absolute;
        top:0;
        left:-100%;
        content:'';
        width:200%;
        height:100%;
        /* border-radius: 30px; */
        background:#1ab394;

        border-radius: 2em;
    }
    .switch span::after{
        content:'';
        position:absolute;
        left:0;
        top:0;
        width:100%;
        height:100%;
        /* border-radius: 50%; */
        background:#fff;
        border-radius: 2em;
    }
    .switch input:checked +span{
        transform:translateX(100%);
    }
    .checkbox{
        padding-top: 0 !important;
    }
    .checkbox input[type="radio"] {
        position: absolute;
        clip: rect(0, 0, 0, 0);
    }

    .checkbox input[type='radio'] + label {
        display: block;
        height: 26px;
        padding: 6px 12px;
        font-size: 13px;
        font-weight: 500;
        line-height: 1;
        border: 1px solid #ccc;
        text-align: center;
        float: left;
        margin-right: 10px;
        cursor: pointer;
        border-radius: 2px;
    }

    .checkbox input[type='radio']:checked + label {
        border: 1px solid #009aff;
        color: #009aff;
        border-radius: 2px;
        font-weight: 500;
    }

    .td td {
        position: relative
    }
    .bg_title{
        font-size: 15px;
        font-weight: bold;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 120px;
        line-height: 20px;
        text-align: left;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .fw{
        font-weight: bold;
    }
    .week{
        border: 1px solid #D9D9D9;
        width: 100px;
        padding: 5px 0;
        text-align: center;
        color: #808080;
        cursor: pointer;
    }
    .act{
        border: 1px solid #1AB394;
        color: #1AB394;
    }
    .model-15{
        margin-top: 3px;
    }
    .input-group-addon {
        padding: 0px 10px;
    }
    .toop{
        width: 30px;
        height: 20px;
        text-align: center;
        background: #1a1a1a;
        border-radius: 50%;
        display: inline-block;
        color: #fff !important;
        line-height: 20px;
        cursor: pointer;
        margin-right: 5px;
    }
    .topRow{
        text-align: left;
        border-bottom: 1px solid #e7eaec;
        margin: 0;
        padding-top: 5px;
    }
    .toopT{
        width: 30px;
        height: 20px;
        text-align: center;
        background: #f8ac59;
        border-radius: 5px;
        display: inline-block;
        color: #fff !important;
        cursor: pointer;
        padding: 2px 4px;
    }
    
    .flex {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
</style>
<body>
<div class="form-content">
    <form id="form-user-add"  class="form-horizontal" novalidate="novalidate"
          enctype="multipart/form-data">

        <!--        <div class="panel-group" id="accordion">-->
        <!--            <div class="panel panel-default">-->
        <!--                <div class="panel-heading">-->
        <!--                    <h5 class="panel-title">-->
        <!--                        <a data-toggle="collapse" data-parent="#accordion"-->
        <!--                           href="tabs_panels.html#collapseOne">基础信息</a>-->
        <!--                    </h5>-->
        <!--                </div>-->
        <!--                <div id="collapseOne" class="panel-collapse collapse in">-->
        <!--                    <div class="panel-body">-->
        <!--                        &lt;!&ndash;基础信息 begin&ndash;&gt;-->
        <input type="hidden" id="custId" name="custId" th:value="${client.customerId}">
        <!--                        <div class="row">-->
        <!--                            <div class="col-md-3 col-sm-6">-->
        <!--                                <div class="form-group">-->
        <!--                                    <label class="col-sm-5">客户编码：</label>-->
        <!--                                    <div class="col-sm-7">-->
        <!--                                        [[${client.custCode}]]-->
        <!--                                    </div>-->
        <!--                                </div>-->
        <!--                            </div>-->
        <!--                            <div class="col-md-3 col-sm-6">-->
        <!--                                <div class="form-group">-->
        <!--                                    <label class="col-sm-5">客户名称：</label>-->
        <!--                                    <div class="col-sm-7">-->
        <!--                                        [[${client.custName}]]-->
        <!--                                    </div>-->
        <!--                                </div>-->
        <!--                            </div>-->

        <!--                            <div class="col-md-3 col-sm-6">-->
        <!--                                <div class="form-group">-->
        <!--                                    <label class="col-sm-5">结算组：</label>-->
        <!--                                    <div class="col-sm-7">-->
        <!--                                        [[${client.balaDeptName}]]-->
        <!--                                    </div>-->
        <!--                                </div>-->
        <!--                            </div>-->
        <!--                            <div class="col-md-3 col-sm-6">-->
        <!--                                <div class="form-group">-->
        <!--                                    <label class="col-sm-5">运营组：</label>-->
        <!--                                    <div class="col-sm-7">-->
        <!--                                        [[${client.salesDeptName}]]-->
        <!--                                    </div>-->
        <!--                                </div>-->
        <!--                            </div>-->
        <!--                        </div>-->
        <!--                        <div class="row">-->
        <!--                            <div class="col-md-3 col-sm-6">-->
        <!--                                <div class="form-group">-->
        <!--                                    <label class="col-sm-5">结算公司：</label>-->
        <!--                                    <div class="col-sm-7">-->
        <!--                                        <span class="form-control-static" th:text="${@dict.getLabel('bala_corp',client.balaCorp)}"></span>-->
        <!--                                    </div>-->
        <!--                                </div>-->
        <!--                            </div>-->


        <!--                            <div class="col-md-3 col-sm-6">-->
        <!--                                <div class="form-group">-->
        <!--                                    <label class="col-sm-5">业务员：</label>-->
        <!--                                    <div class="col-sm-7">-->
        <!--                                        [[${client.psndocName}]]-->
        <!--                                    </div>-->
        <!--                                </div>-->
        <!--                            </div>-->

        <!--                            <div class="col-md-3 col-sm-6">-->
        <!--                                <div class="form-group">-->
        <!--                                    <label class="col-sm-5">业务员联系方式：</label>-->
        <!--                                    <div class="col-sm-7">-->
        <!--                                        [[${client.psncontact}]]-->
        <!--                                    </div>-->
        <!--                                </div>-->
        <!--                            </div>-->
        <!--                        </div>-->
        <!--                        &lt;!&ndash;基础信息 end&ndash;&gt;-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--            </div>-->

        <!--        </div>-->
        <div class="panel panel-default">
            <!--            <div class="panel-heading">-->
            <!--                <h4 class="panel-title">-->
            <!--                    <a data-toggle="collapse" data-parent="#accordion"-->
            <!--                       href="tabs_panels.html#collapseTwo">合同信息</a>-->
            <!--                </h4>-->
            <!--            </div>-->

            <div class="panel-collapse collapse in" id="collapseTwo">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="bg_title">合同信息</div>
                    <div class="fixed-table-body" style="margin: 10px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table td">
                            <thead>
                            <tr>
                                <!--                                <th style="width: 4%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow()" title="新增行">+</a></th>-->
                                <th style="width: 18%;">合同上传</th>
                                <th style="width: 18%;">合同名称</th>
                                <th style="width: 20%;">合同开始时间</th>
                                <th style="width: 20%;">合同到期时间</th>
                                <th style="width: 20%;">合同预警时间</th>
                            </tr>
                            </thead>
                            <tbody>


                            <tr th:each="mapS,status:${contracts}">

                                <!--                                <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" th:onclick="'removeRow(this,'+${status.index}+')'" title="删除选择行"></a></td>-->
                                <th:block th:if='${mapS.tid!=null}'>
                                    <td id="tidState">
                                        <a href="#" th:name="${mapS.filePath}" onclick="downloadFile(this.name)">[[${mapS.fileName}]]</a>
                                        <input id="fileId" type="hidden" th:name="|contractList[${status.index}].tid|"  th:value="${mapS.tid}">
                                        <input  th:name="|contractList[${status.index}].fileName|" type="hidden" th:value="${mapS.fileName}">

                                        <a th:if='${mapS.tid!=null}' class="btn btn-xs btn-success" href="javascript:void(0)" title=""  th:onclick="|onImg(${status.index})|"> <i class="fa fa-refresh" style="font-size: 15px;"></i></a>
                                    </td>
                                </th:block>
                                <th:block th:if='${mapS.tid==null}'>
                                    <td>
                                        <input id="uploadFile" type="file" name="uploadFile" class="form-control" multiple>

                                        <input th:id="fileName" th:name="|contractList[${status.index}].fileName|" type="hidden"
                                               th:value="${mapS.fileName}">
                                        <input type="hidden" id="tid"  th:name="|contractList[${status.index}].tid|">
                                    </td>
                                </th:block>
                                <td>
                                    <input id="count" name="count" type="hidden" th:value="${status.size}">
                                    <input type="hidden" id="contractsSize" th:value="${status.size}">
                                    <input th:id="name_+${status.index}" th:name="|contractList[${status.index}].name|" class="form-control" type="text" required placeholder=""
                                           aria-required="true" th:value="${mapS.name}" maxlength="50">
                                </td>
                                <td>
                                    <input th:id="effectiveDate_+${status.index}" th:name="|contractList[${status.index}].effectiveDate|" class="form-control"
                                           autocomplete="off" required th:value="${#dates.format(mapS.effectiveDate, 'yyyy-MM-dd')}">
                                </td>
                                <td><input th:id="invalidDate_+${status.index}" th:name="|contractList[${status.index}].invalidDate|"  class="form-control"
                                           autocomplete="off" required th:value="${#dates.format(mapS.invalidDate, 'yyyy-MM-dd')}">
                                </td>
                                <td><input th:name="|contractList[${status.index}].warningDate|" th:id="warningDate_+${status.index}"  class="form-control"
                                           autocomplete="off" required th:value="${#dates.format(mapS.warningDate, 'yyyy-MM-dd')}">
                                </td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                    <!--end-->
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <!--            <div class="panel-heading">-->
            <!--                <h4 class="panel-title">-->
            <!--                    <a data-toggle="collapse" data-parent="#accordion" href="tabs_panels.html#collapseFour">合同价</a>-->
            <!--                </h4>-->
            <!--            </div>-->
            <div class="panel-collapse collapse in" id="collapseFour">
                <div class="panel-body">
                    <div class="over">
                        <div class="bg_title fl">合同价</div>

                        <div class="fr flex">
                            <div class="flex fl" style="align-items: center;">
                                <!--                                <div class="checkbox" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="发货单新建时，合同价取值将不精确到货品">-->
                                <!--                                    <input id="accurateGoods" type="radio" name="accurateGoods" value="0" th:checked="${client.accurateGoods==0}">-->
                                <!--                                    <label for="accurateGoods">不精确到货品</label>-->
                                <!--                                </div>-->
                                <!--                                <div class="checkbox" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="发货单新建时，合同价取值将精确到货品">-->
                                <!--                                    <input id="accurateGoods1" type="radio" name="accurateGoods" value="1" th:checked="${client.accurateGoods==1}">-->
                                <!--                                    <label for="accurateGoods1">精确到货品</label>-->
                                <!--                                </div>-->
<!--                                <div class="week accurateGoods" onclick="changeType(1)">-->
<!--                                    精确到货品-->
<!--                                    <input id="accurateGoods" type="hidden" name="accurateGoods" th:value="${client.accurateGoods}">-->
<!--                                </div>-->
                                <!--                                <div class="week accurateArriDetail" style="margin: 0 10px" onclick="changeType(2)">-->
                                <!--                                    精确到详细地址-->
                                <!--                                    <input id="accurateArriDetail" type="hidden" name="accurateArriDetail" th:value="${client.accurateArriDetail}">-->
                                <!--                                </div>-->
                                <div class="flex floatDate" style="margin: 0 5px;">
                                    <section class='model-15'>
                                        <input type="text" placeholder="春节价开始日期" style="width: 47.5%; float: left;" class="time-input form-control" id="floatDateStart" name="floatDateStart"  th:value="${#dates.format(client.floatDateStart, 'yyyy-MM-dd')}">
                                        <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                        <input type="text" placeholder="春节价结束日期" style="width: 47.5%; float: left;" class="time-input form-control" id="floatDateEnd" name="floatDateEnd" th:value="${#dates.format(client.floatDateEnd, 'yyyy-MM-dd')}">
                                    </section>  
                                </div>
                                <div class="flex" style="margin: 0 5px">
                                    <div style="line-height: 32px;">开启春节价：</div>
                                    <section class='model-15'>
                                        <label class='switch cur'>
                                            <input type="checkbox" class="switch" id="checkbox2" />
                                            <span></span>
                                        </label>
                                    </section>
                                    <input type="hidden" id="contractPriceFloat" name="contractPriceFloat" th:value="${client.contractPriceFloat}" />
                                </div>
                                <div class="flex" style="margin: 0 5px">
                                    <div style="line-height: 32px;">精确到货品：</div>
                                    <section class='model-15'>
                                        <label class='switch cur'>
                                            <input type="checkbox" class="switch" id="checkbox1" />
                                            <span></span>
                                        </label>
                                    </section>
                                    <input type="hidden" id="accurateGoods" name="accurateGoods" th:value="${client.accurateGoods}" />
                                </div>
                                <div class="flex" style="margin: 0 5px">
                                    <div style="line-height: 32px;">精确到详细地址：</div>
                                    <section class='model-15'>
                                        <label class='switch cur'>
                                            <input type="checkbox" class="switch" id="checkbox" />
                                            <span></span>
                                        </label>
                                    </section>
                                    <input type="hidden" id="accurateArriDetail" name="accurateArriDetail" th:value="${client.accurateArriDetail}" />
                                </div>
                                <!--                                <input type="hidden" id="lineMatchType" name="carrierPeriodInfo.lineMatchType" >-->
                                <div style="border-left: 1px dashed; width: 10px; height: 24px;"></div>
                            </div>

                            <div class="flex fl" style="align-items: center;">
                                <div class="checkbox" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="发货单新建时，合同价取值将精确到区">
                                    <input id="accurateRegion" type="radio" name="accurateRegion" value="0" th:checked="${client.accurateRegion==0}">
                                    <label for="accurateRegion">精确到区</label>
                                </div>
                                <div class="checkbox" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="发货单新建时，合同价取值将精确到市">
                                    <input id="accurateRegion1" type="radio" name="accurateRegion" value="1" th:checked="${client.accurateRegion==1}">
                                    <label for="accurateRegion1">精确到市</label>
                                </div>
                                <!--                                <div class="checkbox" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="发货单新建时，合同价取值将精确到详细地址">-->
                                <!--                                    <input id="accurateRegion2" type="radio" name="accurateRegion" value="2" th:checked="${client.accurateRegion==2}">-->
                                <!--                                    <label for="accurateRegion2">精确到详细地址</label>-->
                                <!--                                </div>-->
                            </div>

                            <a class="btn btn-success" onclick="importAllData()" >
                                <i class="fa fa-upload"></i> 批量导入
                            </a>

                        </div>
                    </div>
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 10px -5px;">
                        <table border="0" id="infoTabFour" class="custom-tab table td">
                            <thead>
                            <tr>
                                <th style="width: 3%;" rowspan="2"> <a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRowFour()" title="新增行">+</a></th>
                                <th style="width: 25%;">线路名称</th>
                                <!--                                <th >公里</th>-->
                                <th style="width: 5%;">货品</th>
                                <th style="width: 9%;">计费方式</th>
                                <th style="width: 13%;">货品特性</th>
                                <th style="width: 5%;">车长</th>
                                <th style="width: 8%;">车型</th>
                                <th style="width: 6%;">是否有区间</th>
                                <!-- <th style="width: 5%;">毛利率</th> -->
                                <th style="width: 19%;">&nbsp;价格区间</th>
                                <th style="width: 7%;" class="floatDate">春节价格</th>
                            </tr>

                            </thead>
                            <tbody>
                            <tr th:each="mapS,status:${contractpcs}" >
                                <input type="hidden" id="contractpcsSize" th:value="${status.size}">
                                <td>
                                    <a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"  th:onclick="'removeRowFour(this,'+${status.index}+')'" title="删除选择行"></a>
                                    <a class="btn btn-xs" title="复制信息" onclick="getCopy(this)"><i class="fa fa-copy" style="font-size: 15px;"></i></a>
                                </td>
                                <td>
                                    <div class="input-group">
                                        <!--                                        <input th:name="|custContractpcList[${status.index}].lineName|" th:id="lineName_+${status.index}" placeholder="请选择线路" th:onclick="'selectLine('+${status.index}+')'"-->
                                        <!--                                               class="form-control" type="text"  required th:value="${mapS.lineName}">-->
                                        <!--                                        <input th:name="|custContractpcList[${status.index}].transLineId|" th:id="transLineId_+${status.index}" type="hidden" th:value="${mapS.transLineId}">-->
                                        <!--                                        <input th:name="|custContractpcList[${status.index}].lineCode|" th:id="lineCode_+${status.index}" type="hidden" th:value="${mapS.lineCode}">-->
                                        <!--                                        <span class="input-group-addon"><i class="fa fa-search"></i></span>-->
                                        <div class="form-group">
                                            <div class="col-sm-4" style="padding-left: 5px;padding-right: 0px;">
                                                <select th:name="|custContractpcList[${status.index}].deliProvinceId|"
                                                        th:id="deliProvinceId_+${status.index}" th:onchange="'changeSelect(this,'+${status.index}+')'"  class="form-control valid" aria-invalid="false">
                                                </select>
                                            </div>
                                            <div class="col-sm-4" style="padding-left: 5px;padding-right: 0px;">
                                                <select  th:name="|custContractpcList[${status.index}].deliCityId|"
                                                         th:id="deliCityId_+${status.index}" th:onchange="'changeSelect(this,'+${status.index}+')'" class="form-control valid" aria-invalid="false"></select>
                                            </div>
                                            <div class="col-sm-4" style="padding-left: 5px;padding-right: 10px;">
                                                <select  th:name="|custContractpcList[${status.index}].deliAreaId|"
                                                         th:id="deliAreaId_+${status.index}" th:onchange="'changeSelect(this,'+${status.index}+')'" class="form-control valid" aria-invalid="false"></select>
                                            </div>
                                        </div>
                                        <div class="form-group detailed1">
                                            <div class="col-sm-4" style="padding-left: 5px;padding-right: 0px;">
                                                <select th:name="|custContractpcList[${status.index}].arriProvinceId|"
                                                        th:id="arriProvinceId_+${status.index}" th:onchange="'changeSelect(this,'+${status.index}+')'"  class="form-control valid" aria-invalid="false">
                                                </select>
                                            </div>
                                            <div class="col-sm-4" style="padding-left: 5px;padding-right: 0px;">
                                                <select  th:name="|custContractpcList[${status.index}].arriCityId|"
                                                         th:id="arriCityId_+${status.index}" th:onchange="'changeSelect(this,'+${status.index}+')'" class="form-control valid" aria-invalid="false"></select>
                                            </div>
                                            <div class="col-sm-4" style="padding-left: 5px;padding-right: 10px;">
                                                <select  th:name="|custContractpcList[${status.index}].arriAreaId|"
                                                         th:id="arriAreaId_+${status.index}" th:onchange="'changeSelect(this,'+${status.index}+')'" class="form-control valid" aria-invalid="false"></select>
                                            </div>
                                        </div>
                                        <div class="form-group detailed2" style="display: none;">
                                            <div class="col-sm-12" style="padding-left: 5px;">

                                                <input  class="form-control" th:onclick="|selectReceipt(this,${status.index})|" type="text" th:onchange="'changeSelect(this,'+${status.index}+')'" readonly
                                                        th:value="${mapS.arriProName ==null ?'': mapS.arriProName }+${mapS.arriCityName  ==null ?'': mapS.arriCityName}+${mapS.arriAreaName ==null ?'': mapS.arriAreaName}+${mapS.arriDetailAddr ==null ?'': mapS.arriDetailAddr}">

                                                <input th:name="|custContractpcList[${status.index}].arriProvinceId|" type="hidden" th:value="${mapS.arriProvinceId}">
                                                <input th:name="|custContractpcList[${status.index}].arriCityId|" type="hidden" th:value="${mapS.arriCityId}">
                                                <input th:name="|custContractpcList[${status.index}].arriAreaId|" type="hidden" th:value="${mapS.arriAreaId}">

                                                <input th:name="|custContractpcList[${status.index}].arrivalId|" type="hidden" th:value="${mapS.arrivalId}">
                                                <input th:name="|custContractpcList[${status.index}].arriAddrName|" type="hidden" th:value="${mapS.arriAddrName}">
                                                <input th:name="|custContractpcList[${status.index}].arriDetailAddr|" type="hidden" th:value="${mapS.arriDetailAddr}">

                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <input class="form-control" th:onclick="|selectGoods(this,${status.index})|" th:onchange="'changeSelect(this,'+${status.index}+')'"
                                           type="text" readonly th:value="${mapS.goodsName}"/>
                                    <input th:name="|custContractpcList[${status.index}].goodsId|" type="hidden" th:value="${mapS.goodsId}">
                                    <input th:name="|custContractpcList[${status.index}].goodsName|" type="hidden" th:value="${mapS.goodsName}">
                                </td>
                                <!--                                <td><input th:name="|custContractpcList[${status.index}].mileage|" th:id="mileage_+${status.index}"-->
                                <!--                                           class=" form-control" autocomplete="off" aria-required="true"  type="text"  oninput="$.numberUtil.onlyNumber(this)"  min="0.0" maxlength="10" th:value="${mapS.mileage}"></td>-->
                                <td>
                                    <select th:name="|custContractpcList[${status.index}].billingMethod|" th:id="billingMethod_+${status.index}"
                                            class="form-control valid" th:onchange="'billingMethodChange(this,'+${status.index}+')'">
                                        <option th:each="billingMethod : ${billingMethod}" th:text="${billingMethod.context}"
                                                th:value="${billingMethod.value}" th:selected="${billingMethod.value==mapS.billingMethod}"></option>
                                    </select></td>
                                <td><select th:name="|custContractpcList[${status.index}].goodsCharacter|" th:id="goodsCharacter" class="form-control valid"
                                            th:with="type=${@dict.getType('goods_character')}" th:onchange="'changeSelect(this,'+${status.index}+')'">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}" th:selected="${dict.dictValue==''+mapS.goodsCharacter}"></option>
                                </select>
                                </td>
                                <td><select th:name="|custContractpcList[${status.index}].carLen|" th:id="carLen" class="form-control"
                                            th:with="type=${@dict.getType('car_len')}" th:onchange="'changeSelect(this,'+${status.index}+')'">
                                    <option></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}" th:selected="${dict.dictValue==''+mapS.carLen}"></option>
                                </select>
                                </td>
                                <td><select th:name="|custContractpcList[${status.index}].carType|" th:id="carType" class="form-control"
                                            th:with="type=${@dict.getType('car_type')}" th:onchange="'changeSelect(this,'+${status.index}+')'">
                                    <option></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}" th:selected="${dict.dictValue==''+mapS.carType}"></option>
                                </select>
                                </td>
                                <td><select th:name="|custContractpcList[${status.index}].ifSection|" th:id="ifSection_+${status.index}" class="form-control valid"
                                            th:with="type=${@dict.getType('if_section')}" th:onchange="'ifSectionChange(this,'+${status.index}+')'">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}" th:selected="${dict.dictValue==''+mapS.ifSection}"></option>
                                </select>
                                </td>
                                <!-- <td>
                                    <div class="input-group">
                                        <input th:name="|custContractpcList[${status.index}].profit|" th:id="profit_+${status.index}" min="0" max="100"
                                               class="form-control" type="number"  th:value="${mapS.profit}">
                                        <span class="input-group-addon">%</span>
                                    </div>
                                </td> -->
                                <td style="text-align: center;" class="cla">
                                    <input class="form-control price"  th:name="|custContractpcList[${status.index}].guidingPrice|" th:id="price_+${status.index}"
                                           th:value="${mapS.guidingPrice}"  type="text"  oninput="$.numberUtil.onlyNumber(this)" maxlength="10" min="0" required/>

                                        <div class="collapse-link add-alink popup flex" th:id="popup_+${status.index}"
                                            style="font-size: 12px; margin:0px; font-weight:400; width:auto; padding:0px 6px;background: none">
                                            <div style="flex: 1;" th:if="${not #lists.isEmpty(contractpcs[status.index].contractpcSectionList)}">
                                                <div class="row topRow" th:if="${sectionS.startSection!=null ||sectionS.endSection!=null}" th:each="sectionS,sectionSatus:${contractpcs[status.index].contractpcSectionList}">
                                                    <div class="col-sm-12 sectionIndex">  
                                                        <div class="form-group">
                                                            <div class="col-sm-7">
                                                                <span th:text="${sectionS.startSection}"></span>
                                                                <span th:if="${sectionS.startSection!=null}" th:text="${sectionS.startOperator==0?'＜':sectionS.startOperator==1?'≤':''}"></span>
                                                                <span th:if="${sectionS.startSection!=null ||sectionS.endSection!=null}">x</span>
                                                                <span th:if="${sectionS.endSection!=null}" th:text="${sectionS.endOperator==2?'＜':sectionS.endOperator==3?'≤':''}"></span>
                                                                <span th:text="${sectionS.endSection}"></span>
                                                            </div>
                                                            <div class="col-sm-2" th:text="${sectionS.guidingPrice}"></div>
                                                            <div class="col-sm-2 floatDate" th:text="${sectionS.specialPrice}"></div>
                                                            <div class="col-sm-1 flex">
                                                                <span class="toopT" title="区间内统一价" data-toggle="tooltip" data-placement="left" th:style="${sectionS.isFixedPrice==1?'':'display: none;'}">统</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div>
                                                <a href="#" class="collapse-link add-alink show-layer-alink popup" style="background: none"><i class="fa fa-cog" style="font-size: 16px;color: #0ba687;"></i></a>
                                                       
                                                <div class="show-div">
            
                                                    <div class="row show-add" >
                                                        <a class="collapse-link add-alink show-alink" th:name="${status.index}" title="新增" >+</a>
                                                        <div class="col-sm-12"><div>
                                                        </div>
                                                            <div class="row show-add d0" th:each="sectionS,sectionSatus:${contractpcs[status.index].contractpcSectionList}">
            
                                                                <a class="close-link del-alink show-alink"  th:onclick="'showDivDel(this,'+${status.index}+')'" title="删除" >-</a>
                                                                <div class="col-sm-12 sectionIndex" th:id="${sectionSatus.size}">
                                                                    <input th:name="range_+${status.index}"  hidden>
                                                                    
                                                                    <div class="form-group"  >
                                                                        <div class="col-sm-2 ">
                                                                            <input class="form-control" placeholder="区间开始" type="text"  oninput="$.numberUtil.onlyNumber(this)"  min="0.0" step="0.01" maxlength="10"
                                                                                    th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].startSection|"
                                                                                    id="startSection" th:value="${sectionS.startSection}" >
                                                                        </div>
                                                                        <div class="col-sm-2">
                                                                            <select th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].startOperator|"  class="form-control">
                                                                                <option value="0" th:selected="${sectionS.startOperator==0}">＞</option>
                                                                                <option value="1" th:selected="${sectionS.startOperator==1}">≥</option>
                                                                            </select>
                                                                        </div>
                                                                        <div class="col-sm-2">
                                                                            <select th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].endOperator|" class="form-control">
                                                                                <option value="2" th:selected="${sectionS.endOperator==2}">＜</option>
                                                                                <option value="3" th:selected="${sectionS.endOperator==3}">≤</option>
                                                                            </select>
                                                                        </div>
                                                                        <div class="col-sm-2">
                                                                            <input class="form-control"  placeholder="区间结束" type="text"  oninput="$.numberUtil.onlyNumber(this)"  min="0.0" step="0.01" maxlength="10"
                                                                                    th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].endSection|"
                                                                                    id="endSection" th:value="${sectionS.endSection}">
                                                                        </div>
                                                                        
                                                                        <div class="col-sm-2">
                                                                            <input class="form-control" placeholder="价格" type="text"  oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10"
                                                                                    th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].guidingPrice|"
                                                                                    id="guidingPrice" th:value="${sectionS.guidingPrice}">
                                                                            
                                                                        </div>
                                                                        <div class="col-sm-1 floatDate">
                                                                            <input class="form-control price" placeholder="春节价格" th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].specialPrice|"
                                                                                    th:value="${sectionS.specialPrice}"  type="text"  oninput="$.numberUtil.onlyNumber(this)" maxlength="10" min="0" required/>
                                                                        </div>
                                                                        <div class="col-sm-1 flex">
                                                                            <span class="toop" title="区间内统一价" data-toggle="tooltip" data-placement="left" th:style="${sectionS.isFixedPrice==1?'':'display: none;'}">?</span>
                                                                            <select th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].isFixedPrice|" class="form-control" onchange="onToop(this)">
                                                                                <option value="0" th:selected="${sectionS.isFixedPrice==0}">否</option>
                                                                                <option value="1" th:selected="${sectionS.isFixedPrice==1}">是</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
            
                                                            </div>
                                                        </div>
            
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                      

                                    <!-- <a href="#" class="collapse-link add-alink show-layer-alink popup" th:id="popup_+${status.index}"
                                       style="font-size: 12px; margin:0px; font-weight:400; width:auto; padding:0px 6px;background: none"><i class="fa fa-cog" style="font-size: 16px;color: #0ba687"></i></a>
                                    

 
                                    <div class="show-div">

                                        <div class="row show-add" >
                                            <a class="collapse-link add-alink show-alink" th:name="${status.index}" title="新增" >+</a>
                                            <div class="col-sm-12"><div>
                                            </div>
                                                <div class="row show-add d0" th:each="sectionS,sectionSatus:${contractpcs[status.index].contractpcSectionList}">

                                                    <a class="close-link del-alink show-alink"  th:onclick="'showDivDel(this,'+${status.index}+')'" title="删除" >-</a>
                                                    <div class="col-sm-12 sectionIndex" th:id="${sectionSatus.size}">
                                                        <input th:name="range_+${status.index}"  hidden>
                                                       
                                                        <div class="form-group"  >
                                                            <div class="col-sm-2 ">
                                                                <input class="form-control" placeholder="区间开始" type="text"  oninput="$.numberUtil.onlyNumber(this)"  min="0.0" step="0.1" maxlength="10"
                                                                       th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].startSection|"
                                                                       id="startSection" th:value="${sectionS.startSection}" >
                                                            </div>
                                                            <div class="col-sm-2">
                                                                <select th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].startOperator|"  class="form-control">
                                                                    <option value="0" th:selected="${sectionS.startOperator==0}">＞</option>
                                                                    <option value="1" th:selected="${sectionS.startOperator==1}">≥</option>
                                                                </select>
                                                            </div>
                                                            <div class="col-sm-2">
                                                                <select th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].endOperator|" class="form-control">
                                                                    <option value="2" th:selected="${sectionS.endOperator==2}">＜</option>
                                                                    <option value="3" th:selected="${sectionS.endOperator==3}">≤</option>
                                                                </select>
                                                            </div>
                                                            <div class="col-sm-2">
                                                                <input class="form-control"  placeholder="区间结束" type="text"  oninput="$.numberUtil.onlyNumber(this)"  min="0.0" step="0.1" maxlength="10"
                                                                       th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].endSection|"
                                                                       id="endSection" th:value="${sectionS.endSection}">
                                                            </div>
                                                            
                                                            <div class="col-sm-2">
                                                                <input class="form-control" placeholder="价格" type="text"  oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10"
                                                                       th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].guidingPrice|"
                                                                       id="guidingPrice" th:value="${sectionS.guidingPrice}">
                                                                
                                                            </div>
                                                            <div class="col-sm-1 floatDate">
                                                                <input class="form-control price" placeholder="春节价格" th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].specialPrice|"
                                                                       th:value="${sectionS.specialPrice}"  type="text"  oninput="$.numberUtil.onlyNumber(this)" maxlength="10" min="0" required/>
                                                            </div>
                                                            <div class="col-sm-1 flex">
                                                                <span class="toop" title="区间内统一价" data-toggle="tooltip" data-placement="left" th:style="${sectionS.isFixedPrice==1?'':'display: none;'}">?</span>
                                                                <select th:name="|custContractpcList[${status.index}].contractpcSectionList[${sectionSatus.index}].isFixedPrice|" class="form-control" onchange="onToop(this)">
                                                                    <option value="0" th:selected="${sectionS.isFixedPrice==0}">否</option>
                                                                    <option value="1" th:selected="${sectionS.isFixedPrice==1}">是</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>

                                        </div>
                                    </div> -->
                                </td>
                                <td class="floatDate">
                                    <input class="form-control price"  th:name="|custContractpcList[${status.index}].specialPrice|"
                                           th:value="${mapS.specialPrice}"  type="text"  oninput="$.numberUtil.onlyNumber(this)" maxlength="10" min="0" required/>
                                </td>
                            </tr>

                            </tbody>
                        </table>

                    </div>
                    <!--end-->
                </div>
            </div>

        </div>




    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="upload()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger"  onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>

<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <input type="hidden" id="customerId" name="customerId" th:value="${client.customerId}"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a th:href="@{/file/importContractData.xlsx}" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”或“xlsm”格式文件！
            </font>
        </div>
    </form>
</script>

<script th:inline="javascript">
    var prefix = ctx + "client";
    let client = [[${client}]];

    $(function () {
        if(client.accurateArriDetail==1){
            //$('.accurateArriDetail').addClass('act')
            $('#checkbox').attr('checked',true)
        }else{
            $('#checkbox').attr('checked',false)
        }
        if(client.accurateGoods==1){
            $('#checkbox1').attr('checked',true)
        }else{
            $('#checkbox1').attr('checked',false)
        }
        if(client.contractPriceFloat==1){
            // var number = client.floatRate * 100
            $('#checkbox2').attr('checked',true)
            // $('.floatRate').show()
            // $('#changeRate').val(number)
            $('.floatDate').show();
        }else{
            $('#checkbox2').attr('checked',false)
            // $('.floatRate').hide()
            $('.floatDate').hide();
        }

        $('[data-toggle="tooltip"]').tooltip()
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');

        var options = {};
        $.table.init(options);


        let contractpcs = [[${contractpcs}]];

        const sleep = (time) => {
            return new Promise((resolve) => setTimeout(resolve, time))
        }



        const doSomething = async () => {
            for (let i = 0; i < contractpcs.length; i++) {
                await sleep(200)
                const contractpc = contractpcs[i];

                // 初始化省市区
                $.provinces.init("deliProvinceId_" + i, "deliCityId_" + i, "deliAreaId_" + i, contractpc.deliProvinceId
                    , contractpc.deliCityId, contractpc.deliAreaId);
                $.provinces.init("arriProvinceId_" + i, "arriCityId_" + i, "arriAreaId_" + i, contractpc.arriProvinceId
                    , contractpc.arriCityId, contractpc.arriAreaId);

            }
        }

        doSomething()

        $(".week").click(function () {
            $(this).toggleClass("act");
        });


        var size = $("#contractsSize").val();
        layui.use('laydate', function(){

            for (var i = 0; i < size; i++) {
                var laydate = layui.laydate;
                var reqDeliDate = laydate.render({
                    elem: '#effectiveDate_'+i, //指定元素
                    format : 'yyyy-MM-dd',
                    isInitValue : false,
                    max: $("#invalidDate_"+i).val(),
                    done: function(value, date, endDate){
                        reqArriDate.config.min = {
                            year: date.year,
                            month: date.month - 1,//关键
                            date: date.date,
                            hours: 0,
                            minutes: 0,
                            seconds: 0
                        };
                        warning.config.min = {
                            year: date.year,
                            month: date.month - 1,//关键
                            date: date.date,
                            hours: 0,
                            minutes: 0,
                            seconds: 0
                        };
                        //单独校验日期
                        var element= "#effectiveDate_"+(i - 1);
                        $("#form-user-add").validate().element($(element));
                    }
                });
                var reqArriDate = laydate.render({
                    elem: '#invalidDate_'+i, //指定元素
                    format : 'yyyy-MM-dd',
                    isInitValue : false,
                    min: $("#effectiveDate_"+i).val(),
                    done: function(value, date, endDate){
                        reqDeliDate.config.max = {
                            year: date.year,
                            month: date.month - 1,//关键
                            date: date.date,
                            hours: 0,
                            minutes: 0,
                            seconds: 0
                        };
                        warning.config.max = {
                            year: date.year,
                            month: date.month - 1,//关键
                            date: date.date,
                            hours: 0,
                            minutes: 0,
                            seconds: 0
                        };
                        //单独校验日期
                        var element= "#invalidDate_"+(i - 1);
                        $("#form-user-add").validate().element($(element));
                    }
                });

                var warning = laydate.render({
                    elem: '#warningDate_'+i, //指定元素
                    format : 'yyyy-MM-dd',
                    isInitValue : false,
                    trigger: 'click',
                    done: function(value, date, endDate){
                        //单独校验日期
                        var element= "#warningDate_"+(i - 1);
                        $("#form-user-add").validate().element($(element));
                    }
                });
            }
        });



        // $(".uploadFile").fileinput({
        //     publish: "imgDone",
        //     overwriteInitial: false,
        //     dropZoneEnabled: false,
        //     showPreview: false,
        //     uploadAsync: false,
        //     showClose: false,  //关闭按钮
        //     showUpload: false, //是否显示上传按钮
        //     showRemove: false, // 是否显示移除按钮
        //     uploadUrl: ctx + "common/uploadFile",  //上传的地址
        //     /*uploadUrl: prefix + "/upload"*/ //上传的地址
        //     enctype: 'multipart/form-data',
        //     allowedFileExtensions: ['doc', 'docx', 'xls', 'xlsx', 'pdf', 'txt', 'ppt', 'zip'],
        //     removeFromPreviewOnError: true,
        //     minFileSize: 0,
        //     maxFileSize: 10240,
        //     uploadExtraData: {key: 'uploadFile'}
        // });
        //

        //图片功能
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("uploadFile", "tid", picParam);


        // var count = $("#count").val() - 0;
        // for ( var i = 0; i <= count ; i++) {
        //     var control = $("#uploadFile_"+i);
        //     var file = $("#file_"+i);
        //     control.on("change", function () {
        //         var filePath = $(this).val();
        //         var arr = filePath.split('\\');
        //         var fileName = arr[arr.length - 1];
        //         file.val(fileName);
        //     });
        // }
        var contractpcsIndex = $("#contractpcsSize").val() - 0;


        for (var i = 0; i <contractpcsIndex ; i++) {
            // 是否有区间
            var price = "#price_"+i;
            var ifSection = "#ifSection_"+i;
            var popup = "#popup_"+i;
            if ($(ifSection).val() == '1') {
                $(popup).show();
                $(price).hide();
                $(ifSection).parent().next().next().find("input").attr("disabled","disabled");

            }else {
                $(popup).hide();
                $(price).show();
                $(ifSection).parent().next().next().find("input").removeAttr("disabled");

            }

            // 计费方式
            var billingMethod = "#billingMethod_"+i;
            var mileage = "#mileage_"+i;
            if ($(billingMethod).val() == '6') {
                $(mileage).prop("disabled", false);
                $(ifSection).each(function(){
                    $(this).find("option").eq(1).prop("selected",true)
                });
                $(ifSection).prop("disabled", true);
                $(popup).hide();
                $(price).show();
            }else {
                $(mileage).prop("disabled", true);
                $(ifSection).prop("disabled", false);
            }
        }

        let val=  $("input:radio[name='accurateRegion']:checked").val();
        let val1 = $('#accurateArriDetail').val()
        if(val1==1){
            $(".detailed1").css("display","none");
            $(".detailed1 select").attr("disabled","disabled");
            $(".detailed2").css("display","block");
            $(".detailed2 input").removeAttr("disabled");
        }else{
            $(".detailed1").css("display","block");
            $(".detailed1 select").removeAttr("disabled");
            $(".detailed2").css("display","none");
            $(".detailed2 input").attr("disabled","disabled");
        }
        // if(val==2){
        //     $(".detailed1").css("display","none");
        //     $(".detailed1 select").attr("disabled","disabled");
        //     $(".detailed2").css("display","block");
        //     $(".detailed2 input").removeAttr("disabled");
        // }else{
        //     $(".detailed1").css("display","block");
        //     $(".detailed1 select").removeAttr("disabled");
        //     $(".detailed2").css("display","none");
        //     $(".detailed2 input").attr("disabled","disabled");
        // }

        onAccurateRegion();

        $("#checkbox1").change(function(){
            var isT = $(this).is(":checked")
            if(isT){
                $('#accurateGoods').val('1')
            }else{
                $('#accurateGoods').val('0')
            }
        })
        
        $("#checkbox2").change(function(){
            var isT = $(this).is(":checked")
            if(isT){
                $('#contractPriceFloat').val('1')
                $('.floatDate').show()
                // $('.floatRate').show()
            }else{
                $('#contractPriceFloat').val('0')
                // $('.floatRate').hide()
                $('.floatDate').hide()
            }
        })
    });

    function onAccurateRegion() {
        $("#checkbox").change(function(){
            var isT = $(this).is(":checked")
            if(isT){
                $('#accurateArriDetail').val('1')
            }else{
                $('#accurateArriDetail').val('0')
            }
            let val= $('#accurateArriDetail').val();
            if(isT){
                $(".detailed1").css("display","none");
                $(".detailed1 select").attr("disabled","disabled");
                $(".detailed2").css("display","block");
                $(".detailed2 input").removeAttr("disabled");
            }else{
                $(".detailed1").css("display","block");
                $(".detailed1 select").removeAttr("disabled");
                $(".detailed2").css("display","none");
                $(".detailed2 input").attr("disabled","disabled");
            }
        })
    }

    function onToop(obj) {
        let val=$(obj).val();
       
        if(val==1){
            $(obj).prev().css("display","block");
        }else{
            $(obj).prev().css("display","none");
        }
    }

    function changeSelect(obj,lineIndex){

        var val = $(obj).val();
        var options = $(obj).find("option");
        options.each(function (i) {
            if ($(this).val() == val) {
                $(this).attr("selected", true);
            }else{
                $(this).attr("selected", false);
            }
        });
    }

    function changeType(type){
        if(type==1){
            if($('#accurateGoods').val()==0){
                $('#accurateGoods').val('1')
            }else{
                $('#accurateGoods').val('0')
            }
        }
        if(type==2){
            if($('#accurateArriDetail').val()==0){
                $('#accurateArriDetail').val('1')
                $(".detailed1").css("display","none");
                $(".detailed1 select").attr("disabled","disabled");
                $(".detailed2").css("display","block");
                $(".detailed2 input").removeAttr("disabled");
            }else{
                $('#accurateArriDetail').val('0')
                $(".detailed1").css("display","block");
                $(".detailed1 select").removeAttr("disabled");
                $(".detailed2").css("display","none");
                $(".detailed2 input").attr("disabled","disabled");
            }

        }


    }

    function changeFloatRate(){
        var val = $('#changeRate').val()
        if(val!=''){
            val = val/100
        }
        // $('#floatRate').val(val)
    }

    // 根据判断是否 选择了区间
    function ifSectionChange(obj,index) {
        var price = "#price_"+index;
        var popup = "#popup_"+index;
        var val = $(obj).val();
        var options = $(obj).find("option");
        options.each(function (i) {
            if ($(this).val() == val) {
                $(this).attr("selected", true);
                $(obj).parent().next().next().find("input").removeAttr("disabled");
            }else{
                $(this).attr("selected", false);
                $(obj).parent().next().next().find("input").attr("disabled","disabled"); 
            }
        });
        if ($(obj).val() == '0' ){
            $(popup).hide();
            $(price).show();
            return;
        }
        $(popup).show();
        $(price).hide();
    }

    // 计费方式的限制是否有区间
    function billingMethodChange(obj,index) {
        var price = "#price_"+index;
        var popup = "#popup_"+index;
        var ifSection = "#ifSection_"+index;
        var mileage = "#mileage_"+index;
        var val = $(obj).val();
        var options = $(obj).find("option");
        options.each(function (i) {
            if ($(this).val() == val) {
                $(this).attr("selected", true);
            }else{
                $(this).attr("selected", false);
            }
        });
        if ($(obj).val() == '6' ){
            $(mileage).prop("disabled", false);
            $(ifSection).each(function(){
                $(this).find("option").eq(1).prop("selected",true)
            });
            var val1 = $(ifSection).val();
            var options1 = $(ifSection).find("option");
            options1.each(function (i) {
                if ($(this).val() == val1) {
                    $(this).attr("selected", true);
                }else{
                    $(this).attr("selected", false);
                }
            });

            $(ifSection).prop("disabled", true);
            $(popup).hide();
            $(price).show();
            return;
        }else {
            $(mileage).prop("disabled", true);
            $(ifSection).prop("disabled", false);
        }

        if ($(obj).val() == '3'||  $(obj).val() == '4'){

            $(ifSection).each(function(){
                $(this).find("option").eq(1).prop("selected",true)
            });
            var val1 = $(ifSection).val();
            var options1 = $(ifSection).find("option");
            options1.each(function (i) {
                if ($(this).val() == val1) {
                    $(this).attr("selected", true);
                }else{
                    $(this).attr("selected", false);
                }
            });
            $(ifSection).prop("disabled", true);
            $(popup).hide();
            $(price).show();
            return;
        }else {
            $(ifSection).prop("disabled", false);
        }



    }

    /**
     * 线路选择框
     */
    function selectLine(lineIndex) {

        $.modal.open("选择线路", prefix + "/line?lineIndex=" + lineIndex, "","",function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            // 选中线路的ID
            $("#transLineId_" + lineIndex).val(rows[0]["transLineId"]);
            // 选中线路的名称
            $("#lineName_" + lineIndex).val(rows[0]["lineName"]);
            $("#lineCode_" + lineIndex).val(rows[0]["lineCode"]);
            //选中完需单独校验
            $("#form-user-add").validate().element($("#lineName_" + lineIndex));
            layer.close(index);
        });
    }


    var index = $("#count").val() - 0;

    // 将文件上传
    function upload() {
        var data = $('#form-user-add').serializeArray();

        let accurateArriDetail=$("#accurateArriDetail").val();
        let accurateGoods=$("#accurateGoods").val();
        
        let contractPriceFloat=$("#contractPriceFloat").val();
        let floatDateStart=$("#floatDateStart").val();
        let floatDateEnd=$("#floatDateEnd").val();
       
        
        let length=$("#infoTabFour tbody").find("tr").length;
        let val=  $("input:radio[name='accurateRegion']:checked").val();
        let rows = 0;
        let rowsMsg = [];

        if(contractPriceFloat==1){   
            if(floatDateStart==""){
                rowsMsg.push('请选择春节价开始日期');
            }
            if(floatDateEnd==""){
                rowsMsg.push('请选择春节价结束日期');
            }
        }
        
        for(var i=0;i<length;i++){
            if(accurateArriDetail==1){
                let arrivalId=$("input[name='custContractpcList["+i+"].arrivalId']").val();

                if( arrivalId ==''){
                    rowsMsg.push('第'+(i+1)+'行,目的地请精确到详细地址');
                }

            }else if(val==1){
                if($('#deliCityId_'+i).val()==''){
                    rowsMsg.push('第'+(i+1)+'行,始发地请精确到市');
                }
                if($('#arriCityId_'+i).val()==''){
                    rowsMsg.push('第'+(i+1)+'行,目的地请精确到市');
                }

            }else if(val==0){
                if($('#deliAreaId_'+i).val()==''){
                    rowsMsg.push('第'+(i+1)+'行,始发地请精确到区');
                }
                if($('#arriAreaId_'+i).val()==''){
                    rowsMsg.push('第'+(i+1)+'行,目的地请精确到区');
                }
            }
            if(accurateGoods==1){
                let goodsId=$("input[name='custContractpcList["+i+"].goodsId']").val();
                if( goodsId ==''){
                    rowsMsg.push('第'+(i+1)+'行,请选择货品');
                }
            }
            

        }
        if(rowsMsg.length > 0){
            $.modal.alertError(rowsMsg.join("<br>"));
            return;
        }
        if ($.validate.form()) {


            // 将动态增行后的文件对象循环上传
            // var i = 0;
            // for (i = 0; i <= index; i++) {
            //     $("#uploadFile_" + i).fileinput('upload');
            // }
            // 将表单提交
            // commit();

            let fileId = $("#fileId").val();

            if (fileId == null) {
                $("#uploadFile").fileinput('upload');

                var filePath = $("#uploadFile").val();
                var arr = filePath.split('\\');
                var fileName = arr[arr.length - 1];
                $("#fileName").val(fileName);


                jQuery.subscribe("cmt", commit);

            }else {
                commit()
            }




        }
    }

    function commit() {
        $.operate.saveTab(prefix + "/upload", $('#form-user-add').serialize())
    }

    /* 删除指定表格行 */
    function removeRow(obj,index) {
        if ($("#infoTab tbody").find('tr').length > 1) {
            $("#infoTab tbody").find(obj).closest("tr").remove();
        } else {
            $("#name_" + index).val("");
            $("#effectiveDate_" + index).val("");
            $("#invalidDate_" + index).val("");
            $("#warningDate_" + index).val("");
        }
    }

    var goodsCharacterHTML = [[${goodsCharacter}]];
    var gcHTML = '';
    for ( var i = 0; i < goodsCharacterHTML.length; i++) {
        gcHTML += '<option  value='+goodsCharacterHTML[i].dictValue+' >'+goodsCharacterHTML[i].dictLabel+'</option>'
    }

    var vehicleLengthHTML = [[${vehicleLength}]];
    var vlHTML = '<option ></option>';
    for ( var i = 0; i < vehicleLengthHTML.length; i++) {
        vlHTML += '<option  value='+vehicleLengthHTML[i].dictValue+' >'+vehicleLengthHTML[i].dictLabel+'</option>'
    }

    var carTypeHTML =  [[${@dict.getType('car_type')}]];
    var ctHTML = '<option ></option>';
    for ( var i = 0; i < carTypeHTML.length; i++) {
        ctHTML += '<option  value='+carTypeHTML[i].dictValue+' >'+carTypeHTML[i].dictLabel+'</option>'
    }

    var ifSectionHTML = [[${ifSection}]];
    var isHTML = '';
    for ( var i = 0; i < ifSectionHTML.length; i++) {
        isHTML += '<option  value='+ifSectionHTML[i].dictValue+' >'+ifSectionHTML[i].dictLabel+'</option>'
    }

    var billingMethodHTML = [[${billingMethod}]];
    var bmHTML = '';
    for ( var i = 0; i < billingMethodHTML.length; i++) {
        bmHTML += '<option  value='+billingMethodHTML[i].value+' >'+billingMethodHTML[i].context+'</option>'

    }
    var lineIndex = $("#contractpcsSize").val() - 1;
    /* 新增行 */
    function insertRowFour() {

        lineIndex += 1;

        var price = 'price' + lineIndex;
        var popup = 'popup' + lineIndex;
        let vals=$('#checkbox2').is(':checked');
        /* 新增表格行 */
        var trTtml = '<tr>' +
            '      <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"  onclick="removeRowFour(this,'+lineIndex+')" title="删除行"></a><a class="btn btn-xs" title="复制信息" onclick="getCopy(this)"><i class="fa fa-copy" style="font-size: 15px;"></i></a></td>\n' +
            '      <td>' +
            '          <div class="input-group">' +
            // '              <input name="custContractpcList['+ lineIndex +'].lineName" id="lineName_'+ lineIndex +'" placeholder="请选择线路" onclick="selectLine('+lineIndex+')" class="form-control" type="text" required aria-required="true">' +
            // '              <input name="custContractpcList['+ lineIndex +'].transLineId" id="transLineId_'+ lineIndex +'"  type="hidden">' +
            // '              <input name="custContractpcList['+ lineIndex +'].lineCode"  id="lineCode_'+ lineIndex +'" type="hidden">' +
            // '              <span class="input-group-addon"><i class="fa fa-search"></i></span>' +
            '<div class="form-group">' +
            '<div class="col-sm-4" style="padding-left: 5px;padding-right: 0px;">' +
            '<select name="custContractpcList['+lineIndex+'].deliProvinceId" id="deliProvinceId_'+lineIndex+'" class="form-control valid" aria-invalid="false">' +
            '</select>'+
            '</div>'+
            ' <div class="col-sm-4" style="padding-left: 5px;padding-right: 0px;">'+
            ' <select name="custContractpcList['+lineIndex+'].deliCityId" id="deliCityId_'+lineIndex+'" class="form-control valid" aria-invalid="false"></select>'+
            '</div>'+
            '<div class="col-sm-4" style="padding-left: 5px;padding-right: 10px;">'+
            '<select name="custContractpcList['+lineIndex+'].deliAreaId" id="deliAreaId_'+lineIndex+'" class="form-control valid" aria-invalid="false"></select>'+
            '</div>'+
            '</div>'+
            '<div class="form-group detailed1">' +
            '<div class="col-sm-4" style="padding-left: 5px;padding-right: 0px;">' +
            '<select name="custContractpcList['+lineIndex+'].arriProvinceId" id="arriProvinceId_'+lineIndex+'" class="form-control valid" aria-invalid="false">' +
            '</select>'+
            '</div>'+
            ' <div class="col-sm-4" style="padding-left: 5px;padding-right: 0px;">'+
            ' <select name="custContractpcList['+lineIndex+'].arriCityId" id="arriCityId_'+lineIndex+'" class="form-control valid" aria-invalid="false"></select>'+
            '</div>'+
            '<div class="col-sm-4" style="padding-left: 5px;padding-right: 10px;">'+
            '<select name="custContractpcList['+lineIndex+'].arriAreaId" id="arriAreaId_'+lineIndex+'" class="form-control valid" aria-invalid="false"></select>'+
            '</div>'+
            '</div>'+
            ' <div class="form-group detailed2" style="display: none;">'+
            '<div class="col-sm-12" style="padding-left: 5px;">'+
            '<input class="form-control" onclick="selectReceipt(this,'+lineIndex+')" type="text" readonly>'+
            '<input name="custContractpcList['+lineIndex+'].arriProvinceId" type="hidden">'+
            '<input name="custContractpcList['+lineIndex+'].arriCityId" type="hidden">'+
            '<input name="custContractpcList['+lineIndex+'].arriAreaId" type="hidden">'+

            '<input name="custContractpcList['+lineIndex+'].arrivalId" type="hidden">'+
            '<input name="custContractpcList['+lineIndex+'].arriAddrName" type="hidden">'+
            '<input name="custContractpcList['+lineIndex+'].arriDetailAddr" type="hidden">'+
            '</div>'+
            '</div>'+
            '          </div>' +
            '      </td>' +
            '<td>'+
            '<input class="form-control" onclick="selectGoods(this,'+lineIndex+')" type="text" readonly/>'+
            '<input name="custContractpcList['+lineIndex+'].goodsId" type="hidden">'+
            '<input name="custContractpcList['+lineIndex+'].goodsName" type="hidden">'+
            '</td>'+
            // '      <td><input  id="mileage_'+ lineIndex +'" name="custContractpcList['+ lineIndex +'].mileage" class=" form-control" ' +
            // '   type="text"  oninput="$.numberUtil.onlyNumber(this)"  min="0" maxlength="10" autocomplete="off" aria-required="true" ></td>' +
            '      <td><select  name="custContractpcList['+ lineIndex +'].billingMethod" id="billingMethod_'+ lineIndex +'"  class=" form-control" required >' +
            '                  '+bmHTML+'         </select></td>' +
            '      <td>' +
            '           <select  name="custContractpcList['+ lineIndex +'].goodsCharacter" id="goodsCharacter_'+ lineIndex +'" class="form-control valid" aria-invalid="false" required>' +
            '             '+gcHTML+'  ' +
            '                 </select></td>' +
            '      <td><select name="custContractpcList['+ lineIndex +'].carLen"  id="carLen_'+ lineIndex +'"  class="form-control" aria-invalid="false" > ' +
            '                 '+vlHTML+'      </select></td>' +'      ' +
            '<td><select name="custContractpcList['+ lineIndex +'].carType"  id="carType_'+ lineIndex +'"  class="form-control" aria-invalid="false" > ' +
            '                 '+ctHTML+'      </select></td>' +
            '      <td><select  name="custContractpcList['+ lineIndex +'].ifSection" id="ifSection_'+ lineIndex +'"  class="form-control"  required>' +
            '                      '+isHTML+'             </select></td>\n' +
            // '<td><div class="input-group">'+
            // ' <input class="form-control"  min="0" max="100" type="number" name="custContractpcList['+lineIndex+'].profit" id="profit_'+lineIndex+'">'+
            // ' <span class="input-group-addon">%</span></div></td>'+
            '      <td style="text-align: center;">' +
            '<input class="form-control"  name="custContractpcList['+ lineIndex +'].guidingPrice" id='+price+' required type="text"  oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10" />'+
            '          <a href="#" name='+popup+' id='+popup+' class="collapse-link add-alink show-layer-alink" style="font-size: 12px; margin:0px; font-weight:400; width:auto; padding:0px 6px;background: none"><i class="fa fa-cog" style="font-size: 16px;color: #0ba687"></i></a>' +
            '          <!--弹出层div展示内容在这里-->' +
            '          <div class="show-div" >' +
            ' <div class="row show-add" >' +
            '            <a class="collapse-link add-alink show-alink" style="font-size: 22px;"  title="新增" name='+lineIndex+'>+</a>' +
            '            <div class="col-sm-12"><div>' +
            '        </div>' +
            '              <div class="row show-add">' +
            '                  <a class="close-link del-alink show-alink"  onclick="showDivDel(this,'+lineIndex+')" title="删除" >-</a>' +
            '                  <div class="col-sm-12 sectionIndex" id='+lineIndex+' >' +
            '                       <input name="range_'+index+'" hidden>' +
            '                      <div class="form-group">' +
            '                   <div class="col-sm-2">' +
            '                       <input class="form-control" placeholder="区间开始" type="text"  oninput="$.numberUtil.onlyNumber(this)"  min="0.0" step="0.01" maxlength="10" name="custContractpcList['+lineIndex+'].contractpcSectionList['+lineIndex+'].startSection" >' +
            '                   </div>' +
            '                   <div class="col-sm-2">' +
            '                       <select name="custContractpcList['+lineIndex+'].contractpcSectionList['+lineIndex+'].startOperator" class="form-control">' +
            '                       <option value="0">＞</option>' +
            '                       <option value="1">≥</option>' +
            '                       </select>' +
            '                   </div>' +
            '                   <div class="col-sm-2">' +
            '                       <select name="custContractpcList['+lineIndex+'].contractpcSectionList['+lineIndex+'].endOperator"  class="form-control">\n' +
            '                           <option value="2">＜</option>' +
            '                           <option value="3">≤</option>' +
            '                       </select>' +
            '                   </div>' +
            '                   <div class="col-sm-2">' +
            '                       <input class="form-control"  placeholder="区间结束" type="text"  oninput="$.numberUtil.onlyNumber(this)"  min="0.0" step="0.01" maxlength="10"  name="custContractpcList['+lineIndex+'].contractpcSectionList['+lineIndex+'].endSection" >' +
            '                   </div>' +
            '                   <div class="col-sm-2">' +
            '                       <input class="form-control" placeholder="价格" type="text"  oninput="$.numberUtil.onlyNumber(this)"  min="0.0" maxlength="10"  name="custContractpcList['+lineIndex+'].contractpcSectionList['+lineIndex+'].guidingPrice">' +
            '                  </div>' 
            if(vals){
                trTtml +='<div class="col-sm-1 floatDate">'   
            }else{
                trTtml +='<div class="col-sm-1 floatDate" style="display: none;">'
            }
                    trTtml +='    <input class="form-control price" placeholder="春节价格" name="custContractpcList['+lineIndex+'].contractpcSectionList['+lineIndex+'].specialPrice"'
                    +'             type="text"  oninput="$.numberUtil.onlyNumber(this)" maxlength="10" min="0" required/>'
                    +'</div>'
                    +'<div class="col-sm-1 flex">'
                        +'  <span class="toop" title="区间内统一价" data-toggle="tooltip" data-placement="left" style="display: none;">?</span>'                                     
                        +'<select name="custContractpcList['+lineIndex+'].contractpcSectionList['+lineIndex+'].isFixedPrice" class="form-control" onchange="onToop(this)">'
                            +'<option value="0" >否</option>'
                            +'<option value="1" >是</option>'
                            +'</select>'
                            +'</div>'
                +'       </div>'
                +'   </div>'
                +

            '              </div>' +
            '          </div>' +
            '      </td>'
            if(vals){
                trTtml +='<td class="floatDate">'   
            }else{
                trTtml +='<td class="floatDate" style="display: none;">'
            }
            trTtml+=
                '<input class="form-control price"  name="custContractpcList['+lineIndex+'].specialPrice"  type="text"  oninput="$.numberUtil.onlyNumber(this)" maxlength="10" min="0" required/>'+
            '</td>'+
            '</tr>';


        $("#infoTabFour tbody").append(trTtml);

        $.provinces.init("deliProvinceId_"+lineIndex,"deliCityId_"+lineIndex,"deliAreaId_"+lineIndex);
        $.provinces.init("arriProvinceId_"+lineIndex,"arriCityId_"+lineIndex,"arriAreaId_"+lineIndex);

        var pri =  "#"+price;
        var pop =  "#"+popup;
        var ifs =  "#ifSection_"+lineIndex;
        $(pri).hide();
        $(ifs).on("change", function () {
            if ($(ifs).val() == '0' ){
                $(pop).hide();
                $(pri).show();
                return;
            }
            $(pop).show();
            $(pri).hide();
        });

        var bill = '#billingMethod_'+lineIndex;
        var mil = '#mileage_'+lineIndex;
        $(mil).prop("disabled", true);
        // 计费方式
        $(bill).on("change", function () {
            if ($(bill).val() == '6' ){

                $(mil).prop("disabled", false);
                $(ifs).each(function(){
                    $(this).find("option").eq(1).prop("selected",true)
                });
                $(ifs).prop("disabled", true);
                $(pop).hide();
                $(pri).show();
                return;
            }else {
                $(mil).prop("disabled", true);
                $(ifs).prop("disabled", false);
            }

            if ($(bill).val() == '3'||  $(bill).val() == '4'){

                $(ifs).each(function(){
                    $(this).find("option").eq(1).prop("selected",true)
                });
                $(ifs).prop("disabled", true);
                $(pop).hide();
                $(pri).show();
                return;
            }else {
                $(ifs).prop("disabled", false);
            }

        });

        let val=  $("input:radio[name='accurateRegion']:checked").val();
        let val1 = $('#accurateArriDetail').val
        if(val==1){
            $(".detailed1").css("display","none");
            $(".detailed1 select").attr("disabled","disabled");
            $(".detailed2").css("display","block");
            $(".detailed2 input").removeAttr("disabled");
        }else{
            $(".detailed1").css("display","block");
            $(".detailed1 select").removeAttr("disabled");
            $(".detailed2").css("display","none");
            $(".detailed2 input").attr("disabled","disabled");
        }

    }

    /* 删除指定表格行 */
    function removeRowFour(obj,index) {

        if ($("#infoTabFour tbody").find('tr').length > 1) {
            $("#infoTabFour tbody").find(obj).closest("tr").remove();

        }else {
            $("#lineName_"+index).val("");
            $("#transLineId_"+index).val("");
            $("#lineCode_"+index).val("");
        }

    }




    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 100;
    $(function () {
        //弹出层事件
        $(".table").on('click', 'a.show-layer-alink', function () {
            layer.open({
                type: 1,
                title: '添加区间',
                content: $(this).next(".show-div"),
                area: [width + 'px', height + 'px'],
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    layer.close(index);
                }
            });
            return false;
        });
        //弹出层+ -事件
        //新增
        var section_index = $(".show-div>.row>.add-alink").parents(".row").find(".sectionIndex").attr("id") - 0 ;
        $(".table").on('click', '.show-div .row a.add-alink', function insertDiv () {
            var index = $(this).attr("name") - 0;
            section_index += 1;

            let vals=$('#checkbox2').is(':checked');

            var appendHtml =
                '' +
                ' <div class="row show-add d0" >' +
                '                   <div class="col-sm-12"><div>' +
                '               </div>' +
                '<div class="row show-add">'
                +'    <a class="close-link del-alink show-alink" onclick="showDivDel(this,'+index+')" title="删除">-</a>'
                +'   <div class="col-sm-12" >' +
                '<input name="range_'+index+'" hidden>'
                +'       <div class="form-group">'
                +'           <div class="col-sm-2">'
                +'               <input class="form-control" placeholder="区间开始" type="text"  oninput="$.numberUtil.onlyNumber(this)"  min="0.0" step="0.01" name="custContractpcList['+index+'].contractpcSectionList['+section_index+'].startSection" >'
                +'           </div>'
                +'           <div class="col-sm-2">'
                +'               <select name="custContractpcList['+index+'].contractpcSectionList['+section_index+'].startOperator" class="form-control">'
                +'               <option value="0">＞</option>'
                +'               <option value="1">≥</option>'
                +'               </select>'
                +'           </div>'
                +'           <div class="col-sm-2">'
                +'               <select name="custContractpcList['+index+'].contractpcSectionList['+section_index+'].endOperator" class="form-control">'
                +'                   <option value="2">＜</option>'
                +'                   <option value="3">≤</option>'
                +'               </select>'
                +'           </div>'
                +'           <div class="col-sm-2">'
                +'               <input class="form-control"  placeholder="区间结束" type="text"  oninput="$.numberUtil.onlyNumber(this)"  min="0.0" step="0.01" name="custContractpcList['+index+'].contractpcSectionList['+section_index+'].endSection" >'
                +'           </div>'
                // +'           <div class="col-sm-2">'
                // +'                     <div class="input-group">'
                // +'               <input class="form-control" placeholder="毛利率" type="text"  oninput="$.numberUtil.onlyNumber(this)" min="0" max="100"   name="custContractpcList['+index+'].contractpcSectionList['+section_index+'].profit" >'
                // +'           <span class="input-group-addon">%</span>'
                // +'           </div>'
                // +'           </div>'
                +'           <div class="col-sm-2">'
                +'               <input class="form-control" placeholder="价格" type="text"  oninput="$.numberUtil.onlyNumber(this)" min="0.0"   name="custContractpcList['+index+'].contractpcSectionList['+section_index+'].guidingPrice" >'
                +'           </div>'
                if(vals){
                    appendHtml +='<div class="col-sm-1 floatDate">'   
                }else{
                    appendHtml +='<div class="col-sm-1 floatDate" style="display: none;">'
                }
                
                   appendHtml += '    <input class="form-control price" placeholder="春节价格" name="custContractpcList['+index+'].contractpcSectionList['+section_index+'].specialPrice"'
                    +'             type="text"  oninput="$.numberUtil.onlyNumber(this)" maxlength="10" min="0" required/>'
                    +'</div>'
                    +'<div class="col-sm-1">'
                        +'<select name="custContractpcList['+index+'].contractpcSectionList['+section_index+'].isFixedPrice" class="form-control">'
                            +'<option value="1" >是</option>'
                            +'<option value="0" >否</option>'
                            +'</select>'
                            +'</div>'
                +'       </div>'
                +'   </div>'
                +'   <div class="col-sm-3">'
                +'       <div class="form-group show-add">'
                +'       </div>'
                +'   </div>'
                +'  </div>';
            $(this).closest('.show-div').append(appendHtml);

            return false;

        });


        //删除
        /*  $(".table").on('click','.show-div .row a.del-alink',function(){

          });*/
    });

    function showDivDel(obj,index){
        var name = "range_"+index;
        var length = "input[name="+name+"]";
        var nums = $(length).length;
        if (nums == 1){
            alert('仅剩最后一列了！');
            return false;
        }
        $(obj).closest(".row").remove();
        return false;
    }

    function downloadFile(value){

        window.location.href = ctx + "common/downloadFile?fileName=" + value+"&delete=false";
    }

    /**
     * 导入数据
     */
    function importAllData() {
        layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入数据',
            content: $('#importTpl').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx') && !$.common.endWith(file, '.xlsm'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var index = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#file')[0].files[0]);
                formData.append("updateSupport", $("input[name='updateSupport']").is(':checked'));
                let customerId = [[${client.customerId}]]
                formData.append("customerId", customerId);
                $.ajax({
                    url: ctx + "client/importContractData",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code == web_status.WARNING) {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });
    }
    function onImg(index) {
        $("#tidState").html('');
        let contracts=[[${contracts}]]

        var appendHtml =` <input id="uploadFile" type="file" name="uploadFile" class="form-control" multiple>
                        <input th:id="fileName" th:name="|contractList[`+index+`].fileName|" type="hidden"
                            th:value="`+contracts[index].fileName+`">
                        <input type="hidden" id="tid"  th:name="|contractList[`+index+`].tid|">`;
        $("#tidState").append(appendHtml);

        //图片功能
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("uploadFile", "tid", picParam);
    }
    function getCopy(obj) {
        lineIndex += 1;
        let index=$(obj).parents("tr").index();
        var $tr = $("#infoTabFour tbody tr:eq("+index+")").clone();
        let trHtml=$("#infoTabFour tbody tr:eq("+index+")");

        $tr.find("input").each(function () {
            $(this).attr({
                'id': function(_, id) {
                    if(id){
                        if(!id.indexOf("profit_")){
                            return "profit_"+lineIndex;
                        }else if(!id.indexOf("price_")){
                            return "price_"+lineIndex;
                        }
                    }
                },
                'name': function(_, name) {
                    if(name){
                        if(!name.indexOf("range_")){
                            return "range_"+lineIndex;
                        }else{
                            return name.replace(/(\[).*?(\])/, "["+lineIndex+"]");
                        }
                    }
                },
                'onchange': function(_, onchange) {
                    if(onchange){

                        if(!onchange.indexOf("changeSelect(this,")){
                            return "changeSelect(this,"+lineIndex+")";
                        }
                    }
                },
                'onclick': function(_, onclick) {
                    if(onclick){
                        if(!onclick.indexOf("selectReceipt(this,")){
                            return "selectReceipt(this,"+lineIndex+")";
                        }
                    }
                }
            })
        })
        $tr.find("select").each(function () {
            $(this).attr({
                'id': function(_, id) {
                    if(id){
                        if(!id.indexOf("deliProvinceId_")){
                            return "deliProvinceId_"+lineIndex;
                        }else if(!id.indexOf("deliCityId_")){
                            return "deliCityId_"+lineIndex;
                        }else if(!id.indexOf("deliAreaId_")){
                            return "deliAreaId_"+lineIndex;
                        }else if(!id.indexOf("arriProvinceId_")){
                            return "arriProvinceId_"+lineIndex;
                        }else if(!id.indexOf("arriCityId_")){
                            return "arriCityId_"+lineIndex;
                        }else if(!id.indexOf("arriAreaId_")){
                            return "arriAreaId_"+lineIndex;
                        }else if(!id.indexOf("billingMethod_")){
                            return "billingMethod_"+lineIndex;
                        }else if(!id.indexOf("ifSection_")){
                            return "ifSection_"+lineIndex;
                        }
                    }
                },
                'name': function(_, name) {
                    if(name){
                        return name.replace(/(\[).*?(\])/, "["+lineIndex+"]");
                    }
                },
                'onchange': function(_, onchange) {
                    if(onchange){

                        if(!onchange.indexOf("ifSectionChange(this,")){
                            return "ifSectionChange(this,"+lineIndex+")";
                        }else if(!onchange.indexOf("billingMethodChange(this,")){
                            return "billingMethodChange(this,"+lineIndex+")";
                        }else if(!onchange.indexOf("changeSelect(this,")){
                            return "changeSelect(this,"+lineIndex+")";
                        }
                    }
                }
            })
        })
        $tr.find("a").each(function () {
            $(this).attr({
                'onclick': function(_, onclick) {
                    if(onclick){
                        if(!onclick.indexOf("removeRowFour(this,")){
                            return "removeRowFour(this,"+lineIndex+")";
                        }else if(!onclick.indexOf("showDivDel(this,")){
                            return "showDivDel(this,"+lineIndex+")";
                        }
                    }
                },
                'name': function(_, name) {
                    if(name){
                        return lineIndex;
                    }
                }
            })
        })
        $tr.find("div").each(function () {
            $(this).attr({
                'id': function(_, id) {
                    if(id){
                        if(!id.indexOf("popup_")){
                            return "popup_"+lineIndex;
                        }
                    }
                }
            })
        })

        //$tr.insertAfter(trHtml);
        $("#infoTabFour tbody").append($tr);
        
        var deliProvinceId = $("#deliProvinceId_"+index).val()
        var deliCityId = $("#deliCityId_"+index).val()
        var deliAreaId = $("#deliAreaId_"+index).val()
        var arriProvinceId = $("#arriProvinceId_"+index).val()
        var arriCityId = $("#arriCityId_"+index).val()
        var arriAreaId = $("#arriAreaId_"+index).val()

        $.provinces.init("deliProvinceId_"+lineIndex,"deliCityId_"+lineIndex,"deliAreaId_"+lineIndex,deliProvinceId,deliCityId,deliAreaId);
        $.provinces.init("arriProvinceId_"+lineIndex,"arriCityId_"+lineIndex,"arriAreaId_"+lineIndex,arriProvinceId,arriCityId,arriAreaId);
    }

    /**
     * 收货信息
     */
    function selectReceipt(obj,indexNum) {
        var customerId = [[${client.customerId}]];

        $.modal.open("收货信息", ctx + "basic/address/selectAddress?customerId="+customerId+"&addrType=0","",'',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            $()
            $(obj).val(rows[0].provinceName+rows[0].cityName+rows[0].areaName+rows[0].detailAddr);

            $("input[name='custContractpcList["+indexNum+"].arriProvinceId']").val(rows[0].provinceId);
            $("input[name='custContractpcList["+indexNum+"].arriCityId']").val(rows[0].cityId);
            $("input[name='custContractpcList["+indexNum+"].arriAreaId']").val(rows[0].areaId);

            $("input[name='custContractpcList["+indexNum+"].arrivalId']").val(rows[0].addressId);
            $("input[name='custContractpcList["+indexNum+"].arriAddrName']").val(rows[0].addrName);
            $("input[name='custContractpcList["+indexNum+"].arriDetailAddr']").val(rows[0].detailAddr);

            layer.close(index);
        });
    }

    /**
     * 选择货品名称
     *
     */
    function selectGoods(obj,indexNum) {
        var customerId = [[${client.customerId}]];
        $.modal.open("选择货品", ctx + "client/goods?type=0&customerId=" + customerId, "", "", function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            $(obj).val(rows[0].goodsName);
            $("input[name='custContractpcList["+indexNum+"].goodsId']").val(rows[0].goodsId);
            $("input[name='custContractpcList["+indexNum+"].goodsName']").val(rows[0].goodsName);
            layer.close(index);
        });
    }

</script>
</body>

</html>