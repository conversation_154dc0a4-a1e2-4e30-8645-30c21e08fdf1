<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('货主端结算客户选择页')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">客户名称：</label>
                            <div class="col-sm-8">
                                <input name="custName" id="custName" placeholder="请输入客户名称"
                                       class="form-control" type="text">
                                <input id="hiddenText" type="text" style="display:none" />

                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group"></div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
            <input id="relatedIndex" th:value="${relatedIndex}"  type="hidden">
            <input id="customerId" th:value="${customerId}"  type="hidden">
            <input id="permission" th:value="${permission}"  type="hidden">
        </div>
    </div>

</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var clientType = [[${@dict.getType('cust_Type')}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];

    var prefix = ctx + "client";
    var customerId = $("#customerId").val();
    var permission = $("#permission").val();

    $(function () {
        var options = {
            url: prefix + "/listClient?customerId=" + customerId +"&permission=" + permission,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            clickToSelect:true,
            columns: [{
                radio: true
            },
                {
                    title: 'id',
                    field: 'customerId',
                    visible: false
                },
                {
                    title: '客户编码',
                    field: 'custCode',
                    visible:false
                },
                {
                    title: '客户简称',
                    field: 'custAbbr',
                    align: 'left'
                },
                {
                    title: '客户名称',
                    field: 'custName',
                    align: 'left'
                },
                {
                    title: '客户类别',
                    field: 'custType',
                    align: 'left',
                    formatter: function status(value, row, index) {

                        return $.table.selectDictLabel(clientType, value);
                    }

                },
                {
                    title: '结算组',
                    field: 'balaDeptName',
                    visible:false
                },
                {
                    title: '运营部',
                    field: 'salesDeptName',
                    visible:false
                },
                {
                    title: '驻场组',
                    field: 'stationDeptName',
                    visible:false
                },
                {
                    title: '结算公司',
                    field: 'balaCorp',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(balaCorp, value);
                    }

                }
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });

    /** 获取选中行*/
    function getChecked() {
        return $.btTable.bootstrapTable('getSelections');
    }

    /**
     * 选择结算客户后的提交方法
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.close();
        var relatedIndex = $("#relatedIndex").val();

        // 选中的客户ID
        parent.$("#relatedCustId_" + relatedIndex).val(rows.join());
        // 选中的客户名称
        parent.$("#relatedClientName_" + relatedIndex).val($.table.selectColumns("custName").join());


        return;
    }
</script>
</body>
</html>