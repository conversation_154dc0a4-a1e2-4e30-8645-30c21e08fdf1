<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('关联运营部')"/>
</head>
<style>

</style>
<body>
<div class="form-content">
    <form id="form-client-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" th:value="${customerIds}" name="customerIds">
        <div class="panel panel-default">
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color:red">运营部：</label>
                                    <div class="col-sm-8">
                                        <select id="salesId" name="salesId" class="form-control valid" aria-invalid="false" required>
                                            <option value=""> -- 请选择 --</option>
                                            <option th:each="salesGroup : ${salesGroupList}"
                                                    th:text="${salesGroup.salesName}"
                                                    th:value="${salesGroup.id}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "client";

    /**
     * 校验
     */
    $("#form-client-add").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            salesId: {
                required:true,
            },
        }
    });
    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-client-add").serializeArray();
            $.operate.save(prefix + "/relate_sales_group", data);
        }
    }
</script>
</body>
</html>