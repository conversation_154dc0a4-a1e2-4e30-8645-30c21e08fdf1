<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('销售中心客户权限')"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>

</head>
<style>
    .edit-container {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 15px;
    }
    .user-info {
        margin-bottom: -5px;
    }
    .transfer-container {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        margin-top: 5px;
    }
    .fl{
        flex-grow: 1;
        display: flex;
    }
    .co{
        display: flex;
        flex-direction: column;
    }
    .rlBnt{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%; /* 添加这一行 */

    }

    #moveRight, #moveLeft {
        margin: 10px 0;
    }

    #moveRight, #moveLeft {
        padding: 0 10px;
        height: 30px;
        line-height: 30px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form" class="form-horizontal">
                <!--<div class="row">
                    <div class="col-md-3 col-sm-3">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <input name="vbillno" id="vbillno" th:value="${packNo}" placeholder="对账单号" class="form-control" type="text" maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                        </div>
                    </div>
                </div>-->

            </form>
        </div>
        <div class="col-sm-12 select-table">
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-primary" onclick="add()" >
                    <i class="fa fa-plus"></i> 新添
                </a>
            </div>
            <table id="table" class="table table-striped table-responsive table-bordered table-hover" >
            </table>

        </div>
    </div>

</div>

<script id="editHtml" type="text/template">
    <div class="edit-container">
        <div class="user-info">
            <div>
                <div class="layui-row" style="display: flex; align-items: center;">
                    <h5 class="layui-col-md1">
                        <span>用户名:</span>
                    </h5>
                    <div class="layui-col-md3">
                        <div class="input-group">
                            <input class="form-control" type="text" id="userName" name="userName" placeholder="用户名">
                            <input class="form-control" type="hidden" id="userId" name="userId">
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md1">
<!--                        <button type="button" class="layui-btn layui-btn-xs"-->
<!--                                style="height: 25px;margin-left: 5px"-->
<!--                                onclick="saveUserId()">-->
<!--                             保存-->
<!--                        </button>-->

                    </div>
                </div>


            </div>
        </div>
        <div class="transfer-container" id="transfer-container">
            <div class="layui-row fl" style="margin-top: 5px">
                <div class="layui-col-md5 co">
                    <div class="layui-form layui-form-pane" style="margin-bottom: -10px;">
                        <div class="layui-form-item">
                            <div class="layui-input-inline" style="margin-left: 0px">
                                <input type="text" name="leftSearch" id="leftSearch" placeholder="请输入客户简称" autocomplete="off" class="layui-input">
                            </div>
                            <button class="layui-btn" id="leftSearchBtn">搜索</button>
                        </div>
                    </div>

                    <table id="leftTable" lay-filter="leftTable"></table>
                </div>
                <div class="layui-col-md2 rlBnt" style="text-align: center;">
                    <button class="layui-btn" id="moveRight">
                        <i class="layui-icon layui-icon-next"></i>
                    </button>
                    <br><br>
                    <button class="layui-btn" id="moveLeft">
                        <i class="layui-icon layui-icon-prev"></i>
                    </button>
                </div>
                <div class="layui-col-md5 co">
                    <div class="layui-form layui-form-pane" style="margin-bottom: -10px;">
                        <div class="layui-form-item">
                            <div class="layui-input-inline" style="margin-left: 0px">
                                <input type="text" name="leftSearch" id="rightSearch" placeholder="请输入客户简称" autocomplete="off" class="layui-input">
                            </div>
                            <button class="layui-btn" id="rightSearchBtn">搜索</button>
                        </div>
                    </div>

                    <table id="rightTable" lay-filter="rightTable"></table>
                </div>
            </div>


        </div>
    </div>
</script>

<div th:include="include :: footer"></div>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>
<th:block th:include="include :: bootstrap-suggest-js"/>

<script th:inline="javascript">

    $(function () {
        let options = initOptions();
        $.table.init(options);
    });

    //
    function initOptions() {
        return {
            id: "table",
            toolbar: "toolbar",
            formId: "form",
            url: `${ctx}crmAcl/list`,
            showToggle:false,
            showColumns:false,
            showSearch:false,
            modalName: "销售中心客户权限",
            uniqueId: "id",
            clickToSelect: true,

            columns:[
                {
                    checkbox: true,
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 20,
                    switchable:false,
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push(`<a class="btn btn-xs" href="javascript:void(0)" title="修改"
                                            onclick="edit('${row.userId}','${row.userName}')"><i class="fa fa-edit" style="font-size: 15px;"></i></a>`);

                        return actions.join('');
                    }
                },
                {
                    title: '用户名',
                    align: 'left',
                    field : 'userName',
                    formatter: function (value, row, index) {
                        return `${row.userName}`;
                    },
                },
                {
                    title: '客户简称',
                    align: 'left',
                    field : 'custAbbr',
                    formatter: function (value, row, index) {
                        return `${row.custAbbr}`;
                    },
                },
            ]
        }
    }


    function add() {
        addOrEdit()
    }

    function edit(userId,userName) {
        addOrEdit(userId, userName);
    }

    function addOrEdit(uid,uName) {
        //0新添 1修改
        let type = uid === null || uid === undefined ? 0 : 1;

        layer.open({
            type: 1,
            title: type === 0 ? '新添' : '修改',
            area: ['90%', '80%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: $('#editHtml').html(),
            btn: ['关闭'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {
                initBsSuggest()

                let userId = '';
                if (type === 0) {
                    //新添

                }else {
                    //修改
                    $("#userId").val(uid)
                    $("#userName").val(uName)

                    $("#userName").prop("disabled", true);

                    userId = uid
                }

                layui.use(['table', 'jquery'], function(){
                    var table = layui.table;
                    var $ = layui.jquery;

                    // 左表配置
                    var leftTableIns = table.render({
                        elem: '#leftTable'
                        ,url: ctx + "client/list" // 左表数据接口
                        ,method: 'post'  // 使用POST方法
                        ,contentType: 'application/x-www-form-urlencoded'
                        ,request: {
                            pageName: 'pageNum',  // 页码的参数名称，默认：page
                            limitName: 'pageSize' // 每页数据量的参数名，默认：limit
                        }
                        ,parseData: function(res){ //res 即为原始返回的数据
                            return {
                                "code": res.code,
                                "msg": "",
                                "count": res.total,
                                "data": res.rows
                            };
                        }
                        ,page: {
                            layout: ['prev', 'page', 'next', 'count','limit'],
                        }
                        ,height: '#transfer-container-60'
                        ,id: 'leftTable'
                        ,cols: [
                            [
                                {type:'checkbox'}
                                ,{field:'custAbbr', title: '客户简称'}
                                ,{field:'custCode', title: '客户编号'}
                            ]
                        ]
                    });

                    // 右表配置
                    var rightTableIns = table.render({
                        elem: '#rightTable'
                        ,url: ctx + "crmAcl/custList" // 右表数据接口
                        ,method: 'post'  // 使用POST方法
                        ,contentType: 'application/x-www-form-urlencoded'
                        ,request: {
                            pageName: 'pageNum',  // 页码的参数名称，默认：page
                            limitName: 'pageSize' // 每页数据量的参数名，默认：limit
                        }
                        ,parseData: function(res){ //res 即为原始返回的数据
                            return {
                                "code": res.code,
                                "msg": "",
                                "count": res.total,
                                "data": res.rows
                            };
                        }
                        ,where: {
                            userId: userId,
                        }
                        ,page: {
                            layout: ['prev', 'page', 'next', 'count','limit'],
                        }
                        ,height: '#transfer-container-60'
                        ,id: 'rightTable'
                        ,cols: [
                            [
                                {type:'checkbox'}
                                ,{field:'custAbbr', title: '客户简称'}
                                ,{field:'custCode', title: '客户编号'}
                            ]
                        ]

                    });
                    // 左表搜索功能
                    $('#leftSearchBtn').on('click', function(){
                        var searchValue = $('#leftSearch').val();
                        leftTableIns.reload({
                            where: {
                                custAbbr: searchValue,
                                customerId: '',
                                permission: 'sales'
                            },
                        });
                    });
                    // 右表搜索功能
                    $('#rightSearchBtn').on('click', function(){
                        var searchValue = $('#rightSearch').val();
                        rightTableIns.reload({
                            where: {
                                custAbbr: searchValue,
                                userId: userId,
                            },
                        });
                    });


                    // 向右移动
                    $('#moveRight').on('click', function(){
                        var checkStatus = table.checkStatus('leftTable');
                        var data = checkStatus.data;
                        if(data.length === 0){
                            layer.msg('请选择要移动的数据');
                            return;
                        }

                        userId = $("#userId").val();
                        if (userId === '') {
                            layer.msg('请先输入用户');
                            return;
                        }

                        let custIds = data.map(item => item.customerId).join(",");

                        bindCust(userId, custIds, function (){
                            //如果为新添，将用户名置灰
                            $("#userName").prop("disabled", true);

                            //刷新列表
                            $('#rightSearchBtn').click();
                            $('#leftSearchBtn').click();

                        });
                    });

                    // 向左移动
                    $('#moveLeft').on('click', function(){
                        var checkStatus = table.checkStatus('rightTable');
                        var data = checkStatus.data;
                        if(data.length === 0){
                            layer.msg('请选择要移动的数据');
                            return;
                        }
                        let custIds = data.map(item => item.customerId).join(",");

                        unbindCust(userId, custIds,function (){
                            //刷新列表
                            $('#rightSearchBtn').click();
                            $('#leftSearchBtn').click();
                        })
                    });
                });

            },
            btn1: function (index) {
                $.table.refresh()
                layer.close(index);
            },
            cancel: function (index) {
                $.table.refresh()
                return true;
            }
        });

    }

    function bindCust(userId, custIds, callback) {
        let data = {};
        data["userId"] = userId
        data["custIds"] = custIds

        $.ajax({
            url: ctx + "crmAcl/bindCust",
            type: "post",
            dataType: "json",
            // contentType: "application/json; charset=utf-8",
            data: data,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
                $.modal.disable();
            },
            success: function (result) {
                if (result.code == 0) {
                    $.modal.msgSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
                $.modal.enable();

                callback()
            }
        });

    }

    function unbindCust(userId, custIds, callback) {
        let data = {};
        data["userId"] = userId
        data["custIds"] = custIds
        $.ajax({
            url: ctx + "crmAcl/unbindCust",
            type: "post",
            dataType: "json",
            // contentType: "application/json; charset=utf-8",
            data: data,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
                $.modal.disable();
            },
            success: function (result) {
                if (result.code == 0) {
                    $.modal.msgSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
                $.modal.enable();

                callback()
            }
        });
    }

    function initBsSuggest() {
        $(`#userName`).bsSuggest('init', {
            url: ctx + "system/user/findUserInfo?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "userName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["userName","deptName","phonenumber"],
            effectiveFieldsAlias: {"userName":"用户名","deptName":"部门","phonenumber":"手机号码"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#userName`).val(data.userName);
            $(`#userId`).val(data.userId);
        })

        $('#userName').on('keyup', function() {
            $('#userId').val('');
        });
        $('#userName').on('blur', function() {
            if ($('#userId').val() === '') {
                $('#userName').val('');
            }
        });

    }

</script>
</body>
</html>