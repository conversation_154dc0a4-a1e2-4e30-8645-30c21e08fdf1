<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('新增合同价')" />
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .tooltip-inner{
        /* background: transparent !important;
        text-align: left !important;
        color:#363636 !important;
        border:1px solid transparent; */
        max-width: 700px !important;
    }

    .cur{
        cursor: pointer;
    }

    .tc{
        text-align: center;
    }
    .fixed-table-body{
        overflow: inherit;
    }
    .bootstrap-select.form-control{
        position: initial;
    }

    .flex{
        display: flex;
    }
    .flex_left{
        line-height: 26px;
        text-align: right;
        color: #000000 !important;
        margin-bottom: 0;
    }
    .flex_right{
        min-width:0;
        flex:1;
    }


    .box_a {
        position: relative;
    }
    .config {
        position: absolute;
        top: 50%;
        left: 50%;


        opacity: 0;
        transition: opacity 0.3s;
    }

    /* 显示设置,隐藏表格 */
    .box_a:hover .config {
        opacity: 1;
    }

    .box_a:hover .table {
        opacity: 0;
        transition: opacity 0.3s;
    }

    .config {
        opacity: 0;
        z-index: 999;
    }
    .noselect {
        -webkit-touch-callout: none; /* iOS Safari */
        -webkit-user-select: none; /* Chrome/Safari/Opera */
        -khtml-user-select: none; /* Konqueror */
        -moz-user-select: none; /* Firefox */
        -ms-user-select: none; /* Internet Explorer/Edge */
        user-select: none; /* Non-prefixed version, currentlynot supported by any browser */
    }

    .config-section {
        background: #f9f9f9;
        padding: 8px 8px 0px 8px;
        border-radius: 4px;
    }

    /* 分隔线 */
    .divider {
        height: 1px;
        background: #e8e8e8;
        margin: 8px 0;
    }

    .version-container {
        display: flex;
        align-items: center;
        margin-left: 20px;
        padding-left: 10px;
        border-left: 1px #eee solid;
    }

    .version-text {
        font-size: 14px;
        color: #666;
    }

    .version-number {
        font-weight: bold;
        margin: 0 10px;
    }

    .switch-btn {
        padding: 4px 12px;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .switch-btn:hover {
        background-color: #f5f5f5;
    }


</style>
<body class="gray-bg">

<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
                <input type="hidden" id="customerId" name="customerId" th:value="${customerId}">

                <div class="config-section">
                    <div class="row no-gutter" shiro:hasPermission="client:autoDispatch:edit">
                        <div class="col-md-2 col-xs-2">
                            <div class="flex" >
                                <div class="flex_right">
                                    <span  th:text="${client.custAbbr}"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-xs-2">
                            <div class="flex" >
                                <label class="flex_left">开启配置：</label>
                                <div class="flex_right">
                                    <section class='model-15 '>
                                        <label class="checkbox-switch">
                                            <input type="checkbox" id="isAutoDispatch" />
                                            <label for="isAutoDispatch" class="switch-label">
                                                <span class="label-text">是</span>
                                                <span class="label-text">否</span>
                                            </label>
                                        </label>
                                    </section>
                                    <input type="hidden" name="isAutoDispatch" th:value="${client.isAutoDispatch}"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="flex" >
                                <label class="flex_left">送货费：</label>
                                <div class="flex_right">
                                    <div style="display: flex;align-items: baseline;">
                                        <input type="checkbox" id="autoDispatchDeliFee"
                                               onchange="changeAutoDispatchDeliFee()"
                                               style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />
                                        <label for="autoDispatchDeliFee" class="noselect" style="font-size: 1.1em; vertical-align: middle;">可添加</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="flex" >
                                <label class="flex_left">提货费：</label>
                                <div class="flex_right">
                                    <div style="display: flex;align-items: baseline;">
                                        <input type="checkbox" id="autoDispatchPickUpFee"
                                               onchange="changeAutoDispatchPickUpFee()"
                                               style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />
                                        <label for="autoDispatchPickUpFee" class="noselect" style="font-size: 1.1em; vertical-align: middle;">可添加</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <div class="version-container">
                                <span class="version-text">当前版本：</span>
                                <span id="versionNumber" class="version-number">暂无版本</span>
                                <a class="switch-btn" onclick="switchVersion()">切换版本</a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="divider"></div>

                <div class="search-section">
                    <div class="row">
                        <div class="col-md-3 col-sm-3">
                            <select id="billingMethod" class="form-control valid custom-select" aria-invalid="false">
                                <option value="">--计费方式--</option>
                                <option th:each="dict : ${billingMethod}" th:text="${dict.context}"
                                        th:value="${dict.value}">
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input id="carrName" placeholder="承运商" class="form-control valid" type="text"
                                           aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-6">
                        </div>

                    </div>

                    <div class="row">
                        <div class="col-md-4 col-sm-4">
                            <div class="form-group">
                                <div class="col-md-4 col-sm-4">
                                    <select id="deliProvinceId"
                                            class="form-control valid custom-select"
                                            aria-invalid="false"></select>
                                    </select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="deliCityId" class="form-control valid custom-select"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="deliAreaId" class="form-control valid custom-select"
                                            aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 col-sm-1" onclick="changeDiv()">
                            <img th:src="@{/img/change.png}" style="width: 25px;height: 25px;display: block;margin: 0 auto">
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <div class="form-group">
                                <div class="col-md-4 col-sm-4">
                                    <select id="arriProvinceId"
                                            class="form-control valid custom-select"
                                            aria-invalid="false"></select>
                                    </select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="arriCityId" class="form-control valid custom-select"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="arriAreaId" class="form-control valid custom-select"
                                            aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                                <label class="col-sm-6"></label>-->
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="configSearch()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="configReset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>
                </div>

            </form>

        </div>

        <div class="col-sm-12 select-table">
            <div class="btn-group-sm">
                <a class="btn btn-danger multiple btn-rounded btn-sm" onclick="removeAll()" shiro:hasPermission="client:autoDispatch:edit">
                    <i class="fa fa-remove"></i> 删除
                </a>

                <a id="importDiv" class="btn btn-success btn-rounded btn-sm" onclick="importAllData()" shiro:hasPermission="client:autoDispatch:edit">
                    <i class="fa fa-upload"></i> 区间类型批量导入
                </a>

                <a class="btn btn-success btn-rounded btn-sm" onclick="addSubsectionAddr()" shiro:hasPermission="client:autoDispatch:edit">
                    <i class="fa fa-cog"></i> 设定中转点
                </a>
                <a class="btn btn-warning btn-rounded btn-sm" shiro:hasPermission="client:autoDispatch:export" onclick="exportExcel()" ><i class="fa fa-download"></i>&nbsp;导出</a>

                <a class="btn btn-warning btn-rounded btn-sm" shiro:hasPermission="client:autoDispatch:copyConfig" onclick="copyConfig()" ><i class="fa fa-exchange"></i>拷贝配置</a>

                <span  id="subsectionAddrSpan"
                       th:style="${subsectionAddr != null} ? 'padding:3px;cursor: pointer;':'padding:3px;cursor: pointer;display: none;'"
                       class="label label-primary" data-toggle="tooltip" data-container="body" data-placement="right"
                       data-html="true" th:title="|已配置中转点：${subsectionAddr}|">中转点</span>

            </div>


            <div class="row mt5" style="padding: 10px">
                <div class="col-md-12 col-sm-12">
                    <div id="show-div-2">
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab_2" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 7%">
                                        <input type="checkbox" id="selectAllCheckbox" onclick="toggleAllCheckbox(this)" style="margin-right:8px;">
                                        <a class="collapse-link" style="font-size: 22px;color: #1ab394;user-select:none;"
                                           onclick="insertAddrData(2)" title="新增行" shiro:hasPermission="client:autoDispatch:edit">+</a>
                                    </th>
                                    <th style="width: 17%;">提货地址</th>
                                    <th style="width: 15%;">到货地址</th>
                                    <th style="width: 8%;">计价方式/货品特性</th>
                                    <th style="width: 6%;">车长/车型</th>
                                    <th style="width: 6%;">货品</th>
                                    <th style="width: 8%;">承运商</th>
                                    <th style="width: 8%;">票点</th>
                                    <th style="width: 10%;">油卡比例</th>
                                    <th style="width: 15%;">
                                        应付结算标准<i class="fa fa-question-circle ml5 f16 cur" data-toggle="tooltip" data-container="body"
                                                       data-placement="left" data-html="true"
                                                       title="<div style='white-space: nowrap;text-align: left;'>
                                                            区间：区间价格，一口价：应付总价=输入金额；非一口价：应付单价=输入金额<br>
                                                            固(单)：应付单价=输入金额<br>
                                                            固(总)：应付总价=输入金额<br>
                                                            减(%)：应付单价=应收单价 *（1-输入比率）、应付总价=应收总价 *（1-输入比率）<br>
                                                            减(元)：应付单价=应收单价-输入金额<br>
                                                        </div>"></i>
                                    </th>
                                </tr>
                                </thead>
                                <tbody id="tab_body_2">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-5 col-md-5">
                    <nav aria-label="Page navigation">
                        <ul class="pagination" id="pagination">
                            <li>
                                <a href="#" aria-label="Previous" id="prevPage">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li class="active"><a href="#" id="currentPage">1</a></li>
                            <li>
                                <a href="#" aria-label="Next" id="nextPage">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
                <div class="col-sm-7 col-md-7">
                    <div class="dataTables_info" id="dataTable_info" role="status" aria-live="polite" style="display: inline-block;margin: 20px 0;">
                        共 <span id="totalRecords">0</span> 条记录
                    </div>
                    <div class="dataTables_length" id="dataTable_length" style="display: inline-block;margin: 20px 20px;">
                        <label style="display: flex; align-items: center; white-space: nowrap;">
                            <span style="margin-right: 5px;">每页</span>
                            <select name="dataTable_length" id="pageSizeSelect" onchange="changePageSize()" aria-controls="dataTable" class="custom-select custom-select-sm form-control form-control-sm" style="width: auto; display: inline-block; margin: 0 5px;">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span style="margin-left: 5px;">条记录</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a th:href="@{/file/importAutoDispatch.xlsx}" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>

    <div style="padding: 20px">
        <table id="file-table"
               class="table table-striped table-responsive table-bordered table-hover" >
        </table>
    </div>

</script>

<script id="copyConfigHtml" type="text/template">
    <div class="form-content">
        <div class="row">
            <div class="col-md-12 col-sm-12">
                <input name="targetCustId" id="targetCustId" class="form-control" placeholder="目标客户id"
                       type="text">
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 col-sm-12">
                <div class="col-md-4 col-sm-4">
                    <select id="deliPid"
                            class="form-control valid custom-select"
                            aria-invalid="false"></select>
                    </select>
                </div>
                <div class="col-md-4 col-sm-4">
                    <select id="deliCid" class="form-control valid custom-select"
                            aria-invalid="false"></select>
                </div>
                <div class="col-md-4 col-sm-4">
                    <select id="deliAid" class="form-control valid custom-select"
                            aria-invalid="false"></select>
                </div>

            </div>
        </div>
    </div>
</script>
<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-fileinput-js" />
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: xm-select"/>
<script th:inline="javascript">
    let customerId = [[${client.customerId}]]
    var editFlag = [[${@permission.hasPermi('client:autoDispatch:edit')}]];

    let billingMethod = [[${billingMethod}]]
    var billingMethodHtml = '<option value=""></option>';
    for ( var i = 0; i < billingMethod.length; i++) {
        billingMethodHtml += '<option  value='+billingMethod[i].value+' >'+billingMethod[i].context+'</option>'
    }

    var carType = [[${@dict.getType('car_type')}]];//车辆类型
    var carTypeHtml = '';
    for ( var i = 0; i < carType.length; i++) {
        carTypeHtml += '<option  value='+carType[i].dictValue+' >'+carType[i].dictLabel+'</option>'
    }

    var carLen = [[${@dict.getType('car_len')}]];//车长
    var carLenHtml = '';
    for ( var i = 0; i < carLen.length; i++) {
        carLenHtml += '<option  value='+carLen[i].dictValue+' >'+carLen[i].dictLabel+'</option>'
    }

    var billingType = [[${@dict.getType('billing_type')}]];//开票信息
    var billingTypeHtml = '';
    for ( var i = 0; i < billingType.length; i++) {
        billingTypeHtml += '<option  value='+billingType[i].dictValue+' >'+billingType[i].dictLabel+'</option>'
    }

    let addrInd_0 = 0;

    const  editOldHtml = new Map()

    $(function () {
        init("deliProvinceId","deliCityId","deliAreaId");
        init("arriProvinceId","arriCityId","arriAreaId");


        $('[data-toggle="tooltip"]').tooltip({html: true, trigger: 'hover'});

        // let autoDispatchType = [[${client.autoDispatchType}]]
        let isAutoDispatch = [[${client.isAutoDispatch}]]

        if (isAutoDispatch && isAutoDispatch == 1) {
            $("#isAutoDispatch").prop("checked", true);

            // $('input[name="autoDispatchType"]').iCheck('enable')
            // if (autoDispatchType) {
            //     $('input[name="autoDispatchType"][value="' + autoDispatchType + '"]').iCheck('check');
            //     initConfig(autoDispatchType)
            //
            //     if (autoDispatchType == 2) {
            //         $("#importDiv").show()
            //     }
            // } else {
            //     $('input[name="autoDispatchType"][value="0"]').iCheck('check');
            //     initConfig(0)
            // }

            // initConfig(2)


        } else {
            $("#isAutoDispatch").prop("checked", false);
            // $('input[name="autoDispatchType"]').iCheck('uncheck')
            // $('input[name="autoDispatchType"]').iCheck('disable')
        }

        loadVersionNo()

        $("#isAutoDispatch").change(function () {
            var isT = $(this).is(":checked")
            if (isT) {
                $('[name="isAutoDispatch"]').val('1');

             /*   $('input[name="autoDispatchType"]').iCheck('enable')

                let type = autoDispatchType ? autoDispatchType : 0;

                $('input[name="autoDispatchType"][value="' + type + '"]').iCheck('check');*/

                let data = {"customerId": customerId, "isAutoDispatch": 1, "autoDispatchType": 2}
                $.ajax({
                    url: ctx + "autoDispatchConfig/switchConfig",
                    type: "post",
                    dataType: "json",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(data),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (data) {
                        if (data.code == 0) {
                            $.modal.msgSuccess("开启成功。");
                        }
                        // initConfig(2);

                        $.modal.closeLoading();
                    }
                })


            } else {
                $('[name="isAutoDispatch"]').val('0');

                // $('input[name="autoDispatchType"]').iCheck('uncheck')
                // $('input[name="autoDispatchType"]').iCheck('disable')

                // $('[id^="tab_body_"]').empty();
                // $('[id^="show-div-"]').css("display", "none");

                let data = {"customerId": customerId, "isAutoDispatch": 0}
                $.ajax({
                    url: ctx + "autoDispatchConfig/switchConfig",
                    type: "post",
                    dataType: "json",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(data),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (data) {
                        if (data.code == 0) {
                            $.modal.msgSuccess("关闭配置成功。");
                        }
                        $.modal.closeLoading();
                    }
                })

            }
        });


/*        $("input[name='autoDispatchType']").on('ifChecked ifUnchecked', function () {
            var selectedValue = $(this).val(); // 获取选中的值

            //被选中时
            if ($(this).is(':checked')) {
                let data = {"customerId": customerId, "isAutoDispatch": 1, "autoDispatchType": selectedValue}
                $.ajax({
                    url: ctx + "autoDispatchConfig/switchConfig",
                    type: "post",
                    dataType: "json",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(data),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (data) {
                        if (data.code == 0) {
                            $.modal.msgSuccess("配置类型切换成功。");
                        }

                        $("#importDiv").hide();

                        if (selectedValue == 0) {
                            initConfig(0);
                        } else if (selectedValue == 1) {
                            initConfig(1);
                        } else if (selectedValue == 2) {
                            initConfig(2);

                            $("#importDiv").show()
                        }

                        $.modal.closeLoading();
                    }
                })
            } else {
                console.log("取消选中的值为: " + selectedValue);
            }
        });*/

        if ([[${client.autoDispatchDeliFee}]] == 1) {
            $('#autoDispatchDeliFee').prop('checked', true);
        }else {
            $('#autoDispatchDeliFee').prop('checked', false);
        }

        if ([[${client.autoDispatchPickUpFee}]] == 1) {
            $('#autoDispatchPickUpFee').prop('checked', true);
        }else {
            $('#autoDispatchPickUpFee').prop('checked', false);
        }

    });


    function switchVersion() {
        let customerId = [[${client.customerId}]]

        layer.open({
            type: 2,
            title: '切换版本',
            area: ['70%', '80%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: ctx + `autoDispatchVersion/version_list?customerId=${customerId}`,
            btn: ['关闭'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {
                // 将回调函数传递给iframe
                var iframe = document.getElementsByTagName('iframe');
                if (iframe && iframe.length > 0) {
                    iframe[0].contentWindow.parentCallback = loadVersionNo;
                }
            },
            btn1: function (index) {
                loadVersionNo()
                // $.table.refresh()
                layer.close(index);
            },
            cancel: function (index) {
                loadVersionNo()
                // alert(1111)
                // $.table.refresh()
                return true;
            }
        });
    }

    function loadVersionNo() {
        let customerId = [[${client.customerId}]]
        let data = {customerId:customerId}
        $.ajax({
            url: ctx + "autoDispatchVersion/currentVersion",
            type: "get",
            dataType: "json",
            data: data,
            beforeSend: function () {
            },
            success: function (response) {
                if (response.code === 0) {
                    if (response.data) {
                        $("#versionNumber").text(response.data.versionNum);

                        initConfig(2)
                    }else {
                        $("#versionNumber").text("暂无版本");
                    }
                } else {
                    $.modal.alertError(response.msg || "获取版本列表失败");
                }
            },
            error: function() {
                $.modal.alertError("获取版本失败");
            }
        });
    }


    function changeAutoDispatchDeliFee() {
        var autoDispatchDeliFee = $("#autoDispatchDeliFee").is(':checked') ? '1' : '0';

        let data = {"customerId": customerId, "autoDispatchDeliFee": autoDispatchDeliFee}
        $.ajax({
            url: ctx + "autoDispatchConfig/changeAutoDispatchDeliFee",
            type: "post",
            dataType: "json",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify(data),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (data) {
                if (data.code == 0) {
                    $.modal.msgSuccess("修改成功。");
                }else {
                    $.modal.alertError("修改失败，" + data.msg);
                }
                $.modal.closeLoading();
            }
        })
    }

    function changeAutoDispatchPickUpFee() {
        var autoDispatchPickUpFee = $("#autoDispatchPickUpFee").is(':checked') ? '1' : '0';

        let data = {"customerId": customerId, "autoDispatchPickUpFee": autoDispatchPickUpFee}
        $.ajax({
            url: ctx + "autoDispatchConfig/changeAutoDispatchPickUpFee",
            type: "post",
            dataType: "json",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify(data),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (data) {
                if (data.code == 0) {
                    $.modal.msgSuccess("修改成功。");
                }else {
                    $.modal.alertError("修改失败，" + data.msg);
                }
                $.modal.closeLoading();
            }
        })
    }

    /**
     * 提交
     */
    // function submitHandler() {
    //     if ($.validate.form()) {
    //         var data = $("#form-out-quote").serializeArray();
    //         data.push({"name": "autoDispatchConfigList[2].arriAreaIds", "value": "320101,320102,320104"});
    //         $.operate.save(ctx + "autoDispatchConfig/addConfig", data);
    //     }
    // }

    /**
     * 删除
     *
     * @param addrInd       当前数据下标
     * @param type          类型
     * @param sameId        数据库中的sameId
     */
    function rmAddrData(addrInd, type, sameId) {
        if (sameId) {
            layer.confirm('确定删除该条数据吗?', function (index) {
                $.ajax({
                    url: ctx + "autoDispatchConfig/rmConfig",
                    type: "post",
                    dataType: "json",
                    data: {"sameId": sameId, "type": type},
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function (data) {
                        if (data.code == 0) {
                            rmAddrLocalData(addrInd, type);

                            $.modal.msgSuccess(data.msg);
                        }

                        $.modal.closeLoading();
                        $.modal.enable();
                    }
                })
                layer.close(index);
            });
        }else {
            rmAddrLocalData(addrInd, type);
        }
    }

    function rmAddrLocalData(addrInd, type) {
        if (type == 0) {
           /* var totalRows = $('#infoTab_0 tbody tr').length;

            if (totalRows > 1) {
                $(`#addrRow_${addrInd}`).remove();
            } else {
                //
                $(`#addrRow_${addrInd}`).remove();
                insertAddrData(type)
            }*/
        }else if (type == 2){
            var totalRows = $('#infoTab_2 tbody tr').length;

            if (totalRows > 1) {
                $(`#addrRow_${addrInd}`).remove();
            } else {
                $(`#addrRow_${addrInd}`).remove();
                insertAddrData(type)
            }
        }
    }

    var pageNum = 1;
    var pageSize = 10; // 每页显示的行数

    function changePageSize() {
        var selectedValue = $('#pageSizeSelect').val();
        pageSize = parseInt(selectedValue);
        pageNum = 1;

        initConfig(2)
    }
    /**
     * 初始化配置信息
     * @param type  类型
     */
    function initConfig(type) {
        cleanConfig(type);

        let data = {
            "customerId": customerId,
            "type": type,
            "pageNum": pageNum,
            "pageSize": pageSize
        };

        const fields = [
            "deliProvinceId", "deliCityId", "deliAreaId",
            "arriProvinceId", "arriCityId", "arriAreaId",
            "carrName", "billingMethod"
        ];
        fields.forEach(field => {
            const value = $("#" + field).val();
            if (value !== "" && value !==null) {
                data[field] = value;
            }
        });

        // let deliProvinceId = $("deliProvinceId").val();
        // let deliCityId = $("deliCityId").val();
        // let deliAreaId = $("deliAreaId").val();
        // let arriProvinceId = $("arriProvinceId").val();
        // let arriCityId = $("arriCityId").val();
        // let arriAreaId = $("arriAreaId").val();
        //
        // let carrName = $("carrName").val();
        // let billingMethod = $("billingMethod").val();

        $.ajax({
            url: ctx + "autoDispatchConfig/getConfig",
            type: "post",
            dataType: "json",
            data: data,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
                $.modal.disable();
            },
            success: function (data) {
                if (data.code === 0) {
                    //后台的值
                    // let dataList = data.data.autoDispatchConfigList
                    let dataList = data.data.tableDataInfo ? data.data.tableDataInfo.rows : null;

                    if (dataList && dataList.length > 0) {
                        let html = ''
                        if (type == 0) {
                        }else if (type == 2) {
                            dataList.forEach(function (ele, index) {
                                let bilName = ''
                                billingMethod.forEach(res=>{
                                    if(res.value==ele.billingMethod){
                                        bilName=res.context;
                                    }
                                })

                                let goodsCharacter = '';
                                if (ele.goodsCharacter == 0) {
                                    goodsCharacter = '普通品'
                                }else if (ele.goodsCharacter == 1) {
                                    goodsCharacter = '危险品'
                                }

                                let isRoundTrip = ''
                                if (ele.isRoundTrip == 1) {
                                    isRoundTrip = '往返订单'
                                } else if (ele.isRoundTrip == 0) {
                                    isRoundTrip = '单程订单'
                                }

                                let carLenName = '';
                                if (ele.carLenName) {
                                    carLenName = ele.carLenName
                                }

                                let carTypeName = '';
                                if (ele.carTypeName) {
                                    carTypeName = ele.carTypeName
                                }

                                let amountHtml = ''
                                if (ele.deductionType != '4') {
                                    let deductionAmount = ''
                                    if (ele.deductionAmount != null) {
                                        if (ele.deductionType == 0) {
                                            deductionAmount = '应收扣减：' + ele.deductionAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                                        }else if (ele.deductionType == 1) {
                                            let deductionFeeType = ''
                                            if (ele.deductionFeeType == 0) {
                                                deductionFeeType = '(仅运费)'
                                            }else {
                                                deductionFeeType = '(运费+在途)'
                                            }

                                            deductionAmount = '应收扣减：' +ele.deductionAmount + '%' + deductionFeeType;
                                        }else if (ele.deductionType == 2) {
                                            deductionAmount = '固定应付总价：' +ele.deductionAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                                        }else if (ele.deductionType == 3) {
                                            deductionAmount = '固定应付单价：' +ele.deductionAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                                        }
                                    }
                                    amountHtml = `<div>${deductionAmount}</div>`
                                }else {
                                    amountHtml =
                                        `<a class="fa fa-cog config"
                                                style="color: #0092e7;font-size: 20px;"
                                                onclick="openSectionTab(${index},'${ele.sameId}','${ele.id}')" title="配置价格"></a>`
                                }

                                let arriAreaNameHtml = ''
                                if (ele.arriAreaName) {
                                    arriAreaNameHtml = $.table.tooltip(ele.arriAreaName,10);
                                }

                                let arriHtml = ''
                                if (ele.arriType == '0') {
                                    arriHtml = `
                                        <div class="" style="margin-left: -5px;">
                                            <div class="col-sm-3 col-xs-3" style="padding-left: 0px;padding-right: 0px;">
                                                <div>${ele.arriProName}</div>
                                            </div>
                                            <div class="col-sm-3 col-xs-3" style="padding-left: 0px;padding-right: 0px;">
                                                <div>${ele.arriCityName}</div>
                                            </div>
                                            <div class="col-sm-6 col-xs-6" style="padding-left: 0px;padding-right: 10px;">
                                                <div>${arriAreaNameHtml}</div>
                                            </div>
                                        </div>`
                                }else if (ele.arriType == '1') {
                                    arriHtml = `<div>${ele.arriAddrName}</div>`
                                }



                                let bilTypeHtml = ''
                                billingType.forEach(res=>{
                                    if(res.dictValue==ele.billingType){
                                        bilTypeHtml=res.dictLabel;
                                    }
                                })

                                let oilRatioHtml = '';
                                if (ele.oilRatio != null && ele.oilRatio !== '') {
                                    let oilCostTypeHtml = ''
                                    if (ele.oilCostType == '1') {
                                        oilCostTypeHtml = '预付油卡：'
                                    }else if (ele.oilCostType == '3') {
                                        oilCostTypeHtml = '到付油卡：'
                                    }else if (ele.oilCostType == '5') {
                                        oilCostTypeHtml = '回付油卡：'
                                    }
                                    oilRatioHtml = oilCostTypeHtml + ele.oilRatio + (ele.oilType == 1 ? '元' : '%');
                                }



                                let sectionHtml = ''
                                if (ele.autoDispatchSectionList && ele.deductionType == '4') {
                                    for(let section of ele.autoDispatchSectionList) {
                                        let startOperator = "";
                                        if (section.startOperator != null) {
                                            startOperator = section.startOperator == '0' ? '<' : '≤'
                                        }

                                        let endOperator = "";
                                        if (section.endOperator) {
                                            endOperator = section.endOperator == '2' ? '<' : '≤'
                                        }

                                        let price = "";
                                        if (section.price != null) {
                                            price = section.price.toLocaleString('zh', {
                                                style: 'currency',
                                                currency: 'CNY'
                                            });
                                        }

                                        let isFixedPrice = "";
                                        if (section.isFixedPrice != null) {
                                            isFixedPrice = section.isFixedPrice == '0' ? '' : '固';
                                        }

                                        sectionHtml = sectionHtml +
                                        `<tr>
                                            <td>
                                                <div>${section.startSection}</div>
                                            </td>
                                            <td style="width: 10%">
                                                <div class="flex">
                                                   <div>${startOperator}</div>
                                                   <div style="margin: 0 10px;">x</div>
                                                   <div>${endOperator}</div>
                                                </div>

                                            </td>
                                            <td>
                                                <div>${section.endSection == null ? "" : section.endSection}</div>
                                            </td>
                                            <td>
                                                <div style="margin-left: 6px;">${price}</div>
                                            </td>
                                            <td>
                                                <div >${isFixedPrice}</div>
                                            </td>
                                        </tr>`;
                                    }

                                    if (sectionHtml != '') {
                                        sectionHtml = `<table class="table">
                                                ${sectionHtml}
                                        </table>`
                                    }
                                }

                                // sameId



                                html = html +
                                `<tr id="addrRow_${index}" onclick="clickRow(${index})">
                                    <td>
                                        <div style="display: inline-block;">
                                            <input id="checkbox_${index}" data-same-id="${ele.sameId}" type="checkbox">
                                        </div>

                                        <div style="display: inline-block;margin-left:5px">
                                            <a class="fa fa-times-circle ${editFlag}"
                                               style="color: #fd8481;font-size: 20px;"
                                               onclick="rmAddrData(${index},${type},'${ele.sameId}')" title="删除选择行"></a>
                                        </div>
<!--                                        <div style="display: inline-block;margin-left:5px">-->
<!--                                            <a class="fa fa-clone" style="color: #0092e7;font-size: 17px;"-->
<!--                                               onclick="cloneAddrData(${index},${type})" title="复制货品"></a>-->
<!--                                        </div>-->
                                        <div style="display: inline-block;margin-left:5px">
                                            <a class="fa fa-pencil-square-o ${editFlag}" style="font-size: 17px;"
                                               onclick="editAddrData(${index},'${ele.sameId}',${type})" title="修改数据"></a>
                                        </div>

                                        <input id="configId_${index}" value="${ele.id}" type="hidden">
<!--                                        <input id="deliProName_${index}" value="${ele.deliProName}" type="hidden">-->
<!--                                        <input id="deliCityName_${index}" value="${ele.deliCityName}" type="hidden">-->
<!--                                        <input id="deliAreaName_${index}" value="${ele.deliAreaName}" type="hidden">-->
                                        <input id="deliProvinceId_${index}" value="${ele.deliProvinceId}" type="hidden">
                                        <input id="deliCityId_${index}" value="${ele.deliCityId}" type="hidden">
                                        <input id="deliAreaId_${index}" value="${ele.deliAreaId}" type="hidden">
<!--                                        <input id="arriProName_${index}" value="${ele.arriProName}" type="hidden">-->
<!--                                        <input id="arriCityName_${index}" value="${ele.arriCityName}" type="hidden">-->
<!--                                        <input id="arriAreaName_${index}" value="${ele.arriAreaName}" type="hidden">-->
                                        <input id="arriProvinceId_${index}" value="${ele.arriProvinceId}" type="hidden">
                                        <input id="arriCityId_${index}" value="${ele.arriCityId}" type="hidden">
                                        <input id="arriAreaId_${index}" value="${ele.arriAreaId}" type="hidden">
                                        <input id="carrName_${index}" value="${ele.carrName}" type="hidden">
                                        <input id="carrierId_${index}" value="${ele.carrierId}" type="hidden">
                                        <input id="deductionAmount_${index}" value="${ele.deductionAmount}" type="hidden">
                                        <input id="deductionType_${index}" value="${ele.deductionType}" type="hidden">
                                        <input id="deductionFeeType_${index}" value="${ele.deductionFeeType}" type="hidden">
                                        <input id="sameId_${index}" value="${ele.sameId}" type="hidden">
                                        <input id="billingMethod_${index}" value="${ele.billingMethod}" type="hidden">
                                        <input id="goodsCharacter_${index}" value="${ele.goodsCharacter}" type="hidden">
                                        <input id="isRoundTrip_${index}" value="${ele.isRoundTrip}" type="hidden">
                                        <input id="carLen_${index}" value="${ele.carLen}" type="hidden">
                                        <input id="carType_${index}" value="${ele.carType}" type="hidden">
                                        <input id="billingType_${index}" value="${ele.billingType}" type="hidden">
                                        <input id="oilRatio_${index}" value="${ele.oilRatio}" type="hidden">
                                        <input id="arriAddrName_${index}" value="${ele.arriAddrName}" type="hidden">
                                        <input id="arriType_${index}" value="${ele.arriType}" type="hidden">
                                        <input id="oilType_${index}" value="${ele.oilType}" type="hidden">
                                        <input id="oilCostType_${index}" value="${ele.oilCostType}" type="hidden">
                                        <input id="goodsId_${index}" value="${ele.goodsId}" type="hidden">
                                        <input id="goodsName_${index}" value="${ele.goodsName}" type="hidden">

                                    </td>
                                    <td>
                                        <div class="" style="margin-left: -5px;">
                                            <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 0px;">
                                                <div>${ele.deliProName}</div>
                                            </div>
                                            <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 0px;">
                                                <div>${ele.deliCityName}</div>
                                            </div>
                                            <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 10px;">
                                                <div>${ele.deliAreaName}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                       ${arriHtml}
                                    </td>
                                    <td>
                                        <div class="">
                                            <div>${bilName}</div>
                                            <div>${goodsCharacter}</div>
                                            <div>${isRoundTrip}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="" >
                                            <div>${carLenName}</div>
                                            <div>${carTypeName}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="">
                                        ${ele.goodsName == null ? "" : ele.goodsName}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="">
                                            <div>${ele.carrName}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="">
                                            <div>${bilTypeHtml}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="">
                                            <div>${oilRatioHtml}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="" id="sectionTab_${index}">
                                            <div class="box_a">
                                              ${amountHtml}
                                              ${sectionHtml}
                                            </div>
                                        </div>
                                        <div style="display: none" id="section_${index}" data-len="0">
                                            <div class="">
                                                <div class="col-md-12 col-sm-12">
                                                    <table border="0" class="custom-tab table td">
                                                        <thead>
                                                            <tr>
                                                                <th style="width: 5%;" rowspan="2">
                                                                    <a class="collapse-link add-alink show-alink" style="font-size: 22px;"
                                                                        title="新增" onclick="insertSection(${index})">+</a>
                                                                </th>
                                                                <th style="width: 25%;">区间开始</th>
                                                                <th style="width: 25%;">自定义区间</th>
                                                                <th style="width: 25%;">区间结束</th>
                                                                <th style="width: 10%;">价格</th>
                                                                <th style="width: 10%;">是否一口价</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="section_body_${index}">

                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>`;
                            })

                            $("#tab_body_2").prepend(html);

                            let total = data.data.tableDataInfo.total; // 假设后端返回了总记录数
                            let totalPages = Math.ceil(total / pageSize);
                            setupPagination(totalPages,total, 2);
                        }

                        addrInd_0 = dataList.length
                    }else {
                        addrInd_0 = 0
                    }
                }

                $.modal.closeLoading();
                $.modal.enable();
            }
        })
    }

    function setupPagination(totalPages, total, type) {
        $('#pagination').empty();

        // 添加"上一页"按钮
        $('#pagination').append(`
            <li class="paginate_button page-item previous ${pageNum === 1 ? 'disabled' : ''}" id="dataTable_previous">
                <a href="#" aria-controls="dataTable" data-dt-idx="0" tabindex="0" class="page-link">上一页</a>
            </li>
        `);

        // 添加页码按钮
        let startPage, endPage;
        if (totalPages <= 7) {
            // 如果总页数小于等于7，显示所有页码
            startPage = 1;
            endPage = totalPages;
        } else {
            // 如果总页数大于7，使用省略号
            if (pageNum <= 4) {
                startPage = 1;
                endPage = 5;
            } else if (pageNum > totalPages - 4) {
                startPage = totalPages - 4;
                endPage = totalPages;
            } else {
                startPage = pageNum - 2;
                endPage = pageNum + 2;
            }
        }

        if (startPage > 1) {
            $('#pagination').append(`
            <li class="paginate_button page-item">
                <a href="#" aria-controls="dataTable" data-dt-idx="1" tabindex="0" class="page-link">1</a>
            </li>
        `);
            if (startPage > 2) {
                $('#pagination').append(`
                <li class="paginate_button page-item disabled">
                    <a href="#" aria-controls="dataTable" data-dt-idx="ellipsis1" tabindex="0" class="page-link">...</a>
                </li>
            `);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            $('#pagination').append(`
            <li class="paginate_button page-item ${i === pageNum ? 'active' : ''}">
                <a href="#" aria-controls="dataTable" data-dt-idx="${i}" tabindex="0" class="page-link">${i}</a>
            </li>
        `);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                $('#pagination').append(`
                <li class="paginate_button page-item disabled">
                    <a href="#" aria-controls="dataTable" data-dt-idx="ellipsis2" tabindex="0" class="page-link">...</a>
                </li>
            `);
            }
            $('#pagination').append(`
            <li class="paginate_button page-item">
                <a href="#" aria-controls="dataTable" data-dt-idx="${totalPages}" tabindex="0" class="page-link">${totalPages}</a>
            </li>
        `);
        }

        // 添加"下一页"按钮
        $('#pagination').append(`
            <li class="paginate_button page-item next ${pageNum === totalPages ? 'disabled' : ''}" id="dataTable_next">
                <a href="#" aria-controls="dataTable" data-dt-idx="${totalPages + 1}" tabindex="0" class="page-link">下一页</a>
            </li>
        `);


        $('#totalRecords').text(total);

        // 添加事件监听
        $('#dataTable_previous').click((e) => {
            e.preventDefault();
            if (pageNum > 1) {
                pageNum--;
                initConfig(type);
            }
        });

        $('#dataTable_next').click((e) => {
            e.preventDefault();
            if (pageNum < totalPages) {
                pageNum++;
                initConfig(type);
            }
        });

        $('.paginate_button:not(.previous):not(.next):not(.disabled)').click(function (e) {
            e.preventDefault();
            pageNum = parseInt($(this).find('a').text());
            initConfig(type);
        });

    }


    function configSearch() {
      /*  var autoDispatchType = []
        $("input[name='autoDispatchType']").each(function() {
            if ($(this).is(':checked')) {
                autoDispatchType.push($(this).val());
            }
        });

        var autoDispatchTypeStr = autoDispatchType.join(",")*/

        var autoDispatchTypeStr = 2;
        initConfig(autoDispatchTypeStr)
    }

    function configReset() {
        init("deliProvinceId","deliCityId","deliAreaId");
        init("arriProvinceId","arriCityId","arriAreaId");

        const fields = [
            "carrName", "billingMethod"
        ];

        fields.forEach(field => {
            $("#" + field).val("");
        });

    }

    /**
     * 清空配置信息
     * @param type
     */
    function cleanConfig(type) {
        $('[id^="tab_body_"]').empty();
        $('[id^="show-div-"]').css("display","none");

        $('#show-div-' + type).css("display", "block");
    }

    /**
     * 插入行
     * @param type          类型
     * @returns {number}    新插入行的下标addrInd_0
     */
    function insertAddrData(type,data) {
        addrInd_0++

        let addr_ind = addrInd_0

        if (type === 0) {

        }else if (type === 2) {
            let dedHtml = ''

            if (data && data.deductionType != '4') {
                dedHtml =
                    `<input name="deductionAmount"
                               id="deductionAmount_${addr_ind}"
                               class="form-control" placeholder="配置价格" type="text" required
                               value="${data.deductionAmount}"
                               oninput="$.numberUtil.onlyNumber(this)" min="0"/>
                `
            }else {
                dedHtml =
                    `<a class="fa fa-cog"
                            style="color: #0092e7;font-size: 20px;"
                            onclick="openSectionTab(${addr_ind},'${data === undefined ? '' : data.sameId}','${data === undefined ? '' : data.configId}')" title="配置价格"></a>
                `
            }


            let html =
            `<tr id="addrRow_${addr_ind}">
                <td>
                    <div style="display: inline-block;">
                        <a class="fa fa-times-circle ${editFlag}"
                           style="color: #fd8481;font-size: 20px;"
                           onclick="rmAddrData(${addr_ind},${type},'${data === undefined ? '' : data.sameId}')" title="删除选择行"></a>
                    </div>
                    <div style="display: inline-block;margin-left:5px">
<!--                        <a class="fa fa-clone" style="color: #0092e7;font-size: 17px;"-->
<!--                           onclick="cloneAddrData(${addr_ind},${type})" title="复制货品"></a>-->
                    </div>
                    <div style="display: inline-block;margin-left:5px">
                        <a class="fa fa-floppy-o ${editFlag}" style="color: #0092e7;font-size: 17px;"
                           onclick="saveAddrData(${addr_ind},${type},'${data === undefined ? '' : data.sameId}')" title="保存数据"></a>
                    </div>

                </td>
                <td>
                    <div class="" style="margin-left: -5px;">
                        <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 0px;">
                            <select name="deliProvinceId"
                                    id="deliProvinceId_${addr_ind}"
                                    onchange="updateHiddenInput('deliProvinceId','deliProName','${addr_ind}')"
                                    class="form-control valid custom-select" required aria-invalid="false">
                            </select>
                            <input name="deliProName"
                                   id="deliProName_${addr_ind}" type="hidden">
<!--                        <label id="deliProvinceId-error" style="display:none;top:0px" class="error" for="deliProvinceId"></label>-->
                        </div>
                        <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 0px;">
                            <select name="deliCityId"
                                    id="deliCityId_${addr_ind}"
                                    onchange="updateHiddenInput('deliCityId','deliCityName','${addr_ind}')"
                                    class="form-control valid custom-select" required aria-invalid="false">
                            </select>
                            <input name="deliCityName"
                                   id="deliCityName_${addr_ind}" type="hidden">
                        </div>
                        <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 10px;">
                            <select name="deliAreaId"
                                    id="deliAreaId_${addr_ind}"
                                    onchange="updateHiddenInput('deliAreaId','deliAreaName','${addr_ind}')"
                                    class="form-control valid custom-select" required aria-invalid="false">
                            </select>
                            <input name="deliAreaName"
                                   id="deliAreaName_${addr_ind}" type="hidden">
                        </div>
                    </div>
                </td>
                <td>
                    <input id="arriType_${addr_ind}" name="arriType" value="0" type="hidden" >
                    <div id="arriDiv_${addr_ind}" class="" style="margin-left: -5px;">
                        <div class="col-sm-5 col-xs-5" >
                            <select name="arriProvinceId"
                                    id="arriProvinceId_${addr_ind}"
                                    onchange="updateHiddenInput('arriProvinceId','arriProName','${addr_ind}')"
                                    class="form-control valid custom-select" required aria-invalid="false">
                            </select>
                            <input name="arriProName"
                                   id="arriProName_${addr_ind}" type="hidden">
                        </div>
                        <div class="col-sm-5 col-xs-5">
                            <select name="arriCityId"
                                    id="arriCityId_${addr_ind}"
                                    onchange="updateHiddenInput('arriCityId','arriCityName','${addr_ind}')"
                                    class="form-control valid custom-select" required aria-invalid="false"></select>
                             <input name="arriCityName"
                                    id="arriCityName_${addr_ind}" type="hidden">
                        </div>
                        <div class="col-sm-2 col-xs-2">
                            <a class="fa fa-exchange" style="color: #1ab394;font-size: 20px;"
                                onclick="exchangeArri(${addr_ind})" title="切换地址">
                            </a>
                        </div>
                        <div class="col-sm-12 col-xs-12" style="margin-top: 5px">
<!--                            <select name="arriAreaId"-->
<!--                                    id="arriAreaId_${addr_ind}"-->
<!--                                    onchange="updateHiddenInput('arriAreaId','arriAreaName','${addr_ind}')"-->
<!--                                    class="form-control valid custom-select" required aria-invalid="false">-->
<!--                            </select>-->
<!--                            <input name="arriAreaName"-->
<!--                                    id="arriAreaName_${addr_ind}" type="hidden">-->

                            <div id="arriArea_${addr_ind}"></div>
                            <input name="arriAreaId" id="arriAreaId_${addr_ind}" type="hidden">
                        </div>
                    </div>
                    <div id="arriNameDiv_${addr_ind}" style="margin-left: -5px;display: none;">
                        <div class="col-sm-10 col-xs-10" >
                            <input name="arriAddrName"
                               id="arriAddrName_${addr_ind}"
                               class="form-control valid" placeholder="收货地址" readonly
                               type="text" onclick="selectReceipt(${addr_ind})"/>
                        </div>
                        <div class="col-sm-2 col-xs-2" >
                            <a class="fa fa-exchange" style="color: #1ab394;font-size: 20px;"
                                onclick="exchangeArri(${addr_ind})" title="切换地址">
                            </a>

                        </div>

                    </div>
                </td>
                <td>
                    <div class="">
                        <select name="billingMethod"
                                id="billingMethod_${addr_ind}"
                                onchange="changeBillingMethod(${addr_ind},'${data === undefined ? '' : data.sameId}','${data === undefined ? '' : data.configId}')"
                                class="form-control valid custom-select" aria-invalid="false" required>
                            <option value="" disabled selected hidden>计费方式</option>
                            ${billingMethodHtml}
                        </select>
                    </div>
                    <div style="margin-top: 5px">
                        <select name="goodsCharacter"
                                id="goodsCharacter_${addr_ind}"
                                class="form-control valid custom-select" aria-invalid="false">
                            <option value="" disabled selected hidden>货品特性</option>
                            <option value=""></option>
                            <option value="0">普通品</option>
                            <option value="1">危险品</option>
                        </select>
                    </div>
                    <div style="margin-top: 5px">
                        <select name="isRoundTrip"
                                id="isRoundTrip_${addr_ind}"
                                class="form-control valid custom-select" aria-invalid="false">
                            <option value="" disabled selected hidden>是否往返</option>
                            <option value=""></option>
                            <option value="0">单程</option>
                            <option value="1">往返</option>
                        </select>
                    </div>
                </td>
                <td>
                    <div class="" >
                        <select name="carLen"
                                id="carLen_${addr_ind}"
                                class="form-control valid custom-select" aria-invalid="false">
                            <option value="" disabled selected hidden>车长</option>
                            <option value=""></option>
                            ${carLenHtml}
                        </select>
                    </div>
                    <div style="margin-top: 5px">
                        <select name="carType"
                                id="carType_${addr_ind}"
                                class="form-control valid custom-select" aria-invalid="false">
                            <option value="" disabled selected hidden>车型</option>
                            <option value=""></option>
                            ${carTypeHtml}
                        </select>
                    </div>

                </td>
                <td>
                    <div class="">
                        <div class="flex_right">
                            <input id="goodsName_${addr_ind}" name="goodsName" class="form-control" onclick="selectGoods(${addr_ind})" type="text" placeholder="货品" readonly />
                            <input id="goodsId_${addr_ind}" name="goodsId" type="hidden">
                        </div>

                    </div>
                </td>
                <td>
                    <div class="">
                        <input name="carrName"
                               id="carrName_${addr_ind}"
                               class="form-control valid" placeholder="承运商"
                               type="text" required/>
                        <input name="carrierId"
                               id="carrierId_${addr_ind}"
                               type="hidden" required/>
                        <div class="input-group-btn">
                            <ul class="dropdown-menu dropdown-menu-right"" role="menu"></ul>
                        </div>
                    </div>
                </td>
                <td>
                    <select name="billingType" id="billingType_${addr_ind}" class="form-control valid selectSpan">
                        <option value=""></option>
                        ${billingTypeHtml}
                    </select>
                </td>
                <td>
                    <div >
                        <div>
                            <select name="oilCostType" id="oilCostType_${addr_ind}" class="form-control" style="flex: 0.7;">
                                <option value="5">回付油卡</option>
                                <option value="3">到付油卡</option>
                                <option value="1">预付油卡</option>
                            </select>
                        </div>
                        <div style="display: flex;margin-top: 5px">
                            <input name="oilRatio" id="oilRatio_${addr_ind}" style="flex: 1;"
                                oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                type="text" class="form-control valid" autocomplete="off">

                            <select name="oilType" id="oilType_${addr_ind}" class="form-control" style="flex: 0.7;">
                                <option value="0">%</option>
                                <option value="1">元</option>
                            </select>

                        </div>

                    </div>
                </td>
                <td >
                    <div style="display: flex; flex-direction: column;">
                        <div style="display: flex; align-items: center;">
                            <select name="deductionType" id="deductionType_${addr_ind}"
                                    onchange="changeDeductionType(${addr_ind},'${data === undefined ? '' : data.sameId}','${data === undefined ? '' : data.configId}');"
                                    class="form-control" style="flex: 0.5;" ${data === undefined || data.billingMethod !== '3'? '' : 'disabled'}>
                                <option value="4">区间</option>
                                <option value="3">固(单)</option>
                                <option value="2">固(总)</option>
                                <option value="1">减(%)</option>
                                <option value="0">减(元)</option>
                            </select>
                        
                            <div  id="deductionFeeTypeDiv_${addr_ind}" style="${data === undefined || data.deductionType !== '1'? 'display: none;' : ''} flex: 1;">
                                <select name="deductionFeeType" id="deductionFeeType_${addr_ind}" class="form-control">
                                    <option value="0">仅运费</option>
                                    <option value="1">运费+在途</option>
                                </select>
                            </div>
                        </div>
                        <div  id="sectionTab_${addr_ind}">
                            ${dedHtml}
                        </div>

                    </div>

                    <div style="display: none" id="section_${addr_ind}" data-len="0">
                        <div class="">
                            <div class="col-md-12 col-sm-12">
                                <table border="0" class="custom-tab table td">
                                    <thead>
                                        <tr>
                                            <th style="width: 5%;" rowspan="2">
                                                <a class="collapse-link add-alink show-alink" style="font-size: 22px;"
                                                    title="新增" onclick="insertSection(${addr_ind})">+</a>
                                            </th>
                                            <th style="width: 25%;">区间开始</th>
                                            <th style="width: 25%;">自定义区间</th>
                                            <th style="width: 25%;">区间结束</th>
                                            <th style="width: 10%;">价格</th>
                                            <th style="width: 10%;">是否一口价</th>
                                        </tr>
                                    </thead>
                                    <tbody id="section_body_${addr_ind}">
                                        <tr>
                                            <td>
                                                <a class="close-link del-alink show-alink" onclick="delSection(this)" title="删除">-</a>
                                            </td>
                                            <td>
                                                <input name="startSection"
                                                       id="startSection_${addr_ind}_0"
                                                       oninput="$.numberUtil.onlyNumber(this)"
                                                       class="form-control" placeholder="区间开始" type="text"
                                                       min="0.0" step="0.1" maxlength="10">
                                            </td>
                                            <td>
                                                <div class="flex">
                                                    <select name="startOperator"
                                                            id="startOperator_${addr_ind}_0"
                                                            class="form-control">
                                                        <option value="0">＜</option>
                                                        <option value="1">≤</option>
                                                    </select>

                                                   <div style="margin: 0 10px;">x</div>
                                                    <select name="endOperator"
                                                            id="endOperator_${addr_ind}_0"
                                                            class="form-control">
                                                        <option value="2">＜</option>
                                                        <option value="3">≤</option>
                                                    </select>
                                                </div>

                                            </td>
                                            <td>
                                                <input name="endSection"
                                                       id="endSection_${addr_ind}_0"
                                                       oninput="$.numberUtil.onlyNumber(this)"
                                                       class="form-control" placeholder="区间结束" type="text"
                                                       min="0.0" step="0.1" maxlength="10">
                                            </td>
                                            <td>
                                                <input name="price"
                                                       id="price_${addr_ind}_0"
                                                       oninput="$.numberUtil.onlyNumber(this)"
                                                       class="form-control" placeholder="价格" type="text" required
                                                       min="0.0" maxlength="10">
                                            </td>
                                            <td>
                                                <select name="isFixedPrice"
                                                        id="isFixedPrice_${addr_ind}_0"
                                                        class="form-control">
                                                    <option value="0" selected>否</option>
                                                    <option value="1" >是</option>
                                                </select>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>`;

            $("#tab_body_2").prepend(html);

            window.scrollTo(0, 0);

        }

        if (data != undefined) {
            init("deliProvinceId_" + addr_ind, "deliCityId_" + addr_ind, "deliAreaId_" + addr_ind
                , data.deliProvinceId
                , data.deliCityId
                , data.deliAreaId);
            init_1("arriProvinceId_" + addr_ind, "arriCityId_" + addr_ind, "arriArea_" + addr_ind
                , data.arriProvinceId
                , data.arriCityId
                , data.arriAreaId);

            $("#arriAreaId_" + addr_ind).val(data.arriAreaId);

            $("#carrName_" + addr_ind).val(data.carrName);
            $("#carrierId_" + addr_ind).val(data.carrierId);
            $("#deductionAmount_" + addr_ind).val(data.deductionAmount);
            $("#deductionType_" + addr_ind).val(data.deductionType);
            $("#deductionFeeType_" + addr_ind).val(data.deductionFeeType);
            $("#billingMethod_" + addr_ind).val(data.billingMethod);
            $("#goodsCharacter_" + addr_ind).val(data.goodsCharacter);
            $("#isRoundTrip_" + addr_ind).val(data.isRoundTrip);
            $("#carLen_" + addr_ind).val(data.carLen);
            $("#carType_" + addr_ind).val(data.carType);
            $("#billingType_" + addr_ind).val(data.billingType);
            $("#oilRatio_" + addr_ind).val(data.oilRatio);

            $("#arriAddrName_" + addr_ind).val(data.arriAddrName);
            $("#arriType_" + addr_ind).val(data.arriType);
            $("#oilType_" + addr_ind).val(data.oilType);
            $("#oilCostType_" + addr_ind).val(data.oilCostType);

            $("#goodsId_" + addr_ind).val(data.goodsId);
            $("#goodsName_" + addr_ind).val(data.goodsName);

            if (data.arriType == 0) {
                $(`#arriDiv_${addr_ind}`).show()
                $(`#arriNameDiv_${addr_ind}`).hide()
            }else {
                $(`#arriDiv_${addr_ind}`).hide()
                $(`#arriNameDiv_${addr_ind}`).show()
            }

        }else {
            init("deliProvinceId_" + addr_ind, "deliCityId_" + addr_ind, "deliAreaId_" + addr_ind);
            init_1("arriProvinceId_" + addr_ind, "arriCityId_" + addr_ind, "arriArea_" + addr_ind);
        }
        initCarrierBsSuggest(addr_ind);

        return addr_ind;
    }

    function exchangeArri(addr_ind) {
        let arriType = $(`#arriType_${addr_ind}`).val();

        if (arriType == '0') {
            $(`#arriDiv_${addr_ind}`).hide()
            $(`#arriNameDiv_${addr_ind}`).show()

            $(`#arriType_${addr_ind}`).val("1");
        }else {
            $(`#arriDiv_${addr_ind}`).show()
            $(`#arriNameDiv_${addr_ind}`).hide()

            $(`#arriType_${addr_ind}`).val("0");
        }
    }

    /**
     * 选择货品名称
     *
     */
    function selectGoods(ind) {
        layer.open({
            type: 2,
            area: ['80%', '80%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "选择货品",
            content: ctx + "client/goods?type=0&customerId=" + customerId,
            btn: ['确定','清空', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero){
                //获取整行
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                $(`#goodsId_${ind}`).val(rows[0].goodsId);
                $(`#goodsName_${ind}`).val(rows[0].goodsName);
                layer.close(index);
            },
            btn2: function(index, layero){
                $(`#goodsId_${ind}`).val("");
                $(`#goodsName_${ind}`).val("");
                layer.close(index);
            },
            cancel: function(index) {
                return true;
            }
        });
    }

    function selectReceipt(addr_ind) {
        $.modal.open("收货信息", ctx + "basic/address/selectAddress?customerId="+customerId+"&addrType=0","",'',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            $(`#arriAddrName_${addr_ind}`).val(rows[0].addrName)

            layer.close(index);
        });

    }

    /**
     * 插入行 html不含输入框
     * @param type          类型
     * @param addr_ind      新插入行的下标addrInd_0
     * @param ele           回填的数据
     * @returns {string}    拼接好的html
     */
    function insertAddrHtml(type, addr_ind, ele) {
        let index = addr_ind
        let html = ``

        if (type == 0) {
        }else if (type == 2) {
            let bilName = ''
            billingMethod.forEach(res=>{
                if(res.value==ele.billingMethod){
                    bilName=res.context;
                }
            })
            let goodsCharacter = ''
            if (ele.goodsCharacter == 0) {
                goodsCharacter = '普通品'
            }else if (ele.goodsCharacter == 1) {
                goodsCharacter = '危险品'
            }

            let isRoundTrip = ''
            if (ele.isRoundTrip == 0) {
                isRoundTrip = '单程订单'
            }else if (ele.isRoundTrip == 1) {
                isRoundTrip = '往返订单'
            }


            let carLenName = '';
            if (ele.carLenName) {
                carLenName = ele.carLenName
            }

            let carTypeName = '';
            if (ele.carTypeName) {
                carTypeName = ele.carTypeName
            }

            let amountHtml = ''
            if (ele.deductionType != '4') {
                let deductionAmount = ''
                if (ele.deductionAmount != null) {
                    if (ele.deductionType == 0) {
                        deductionAmount = '应收扣减：' + ele.deductionAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }else if (ele.deductionType == 1) {
                        let deductionFeeType = ''
                        if (ele.deductionFeeType == 0) {
                            deductionFeeType = '(仅运费)'
                        }else {
                            deductionFeeType = '(运费+在途)'
                        }

                        deductionAmount = '应收扣减：' +ele.deductionAmount + '%' + deductionFeeType;
                    }else if (ele.deductionType == 2) {
                        deductionAmount = '固定应付总价：' +ele.deductionAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }else if (ele.deductionType == 3) {
                        deductionAmount = '固定应付单价：' +ele.deductionAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }
                amountHtml = `<div>${deductionAmount}</div>`
            }else {
                amountHtml =
                    `<a class="fa fa-cog config"
                                                style="color: #0092e7;font-size: 20px;"
                                                onclick="openSectionTab(${index},'${ele.sameId}','${ele.id}')" title="配置价格"></a>`
            }

            let arriAreaNameHtml = ''
            if (ele.arriAreaName) {
                arriAreaNameHtml = $.table.tooltip(ele.arriAreaName,10);
            }

            let bilTypeHtml = ''
            billingType.forEach(res=>{
                if(res.dictValue==ele.billingType){
                    bilTypeHtml=res.dictLabel;
                }
            })

            let oilRatioHtml = '';
            if (ele.oilRatio != null && ele.oilRatio !== '') {
                let oilCostTypeHtml = ''
                if (ele.oilCostType == '1') {
                    oilCostTypeHtml = '预付油卡：'
                }else if (ele.oilCostType == '3') {
                    oilCostTypeHtml = '到付油卡：'
                }else if (ele.oilCostType == '5') {
                    oilCostTypeHtml = '回付油卡：'
                }

                oilRatioHtml = oilCostTypeHtml + ele.oilRatio + (ele.oilType == '1' ? '元' : '%')
            }

            let sectionHtml = ''
            if (ele.autoDispatchSectionList && ele.deductionType == '4') {
                for(let section of ele.autoDispatchSectionList) {
                    let startOperator = "";
                    if (section.startOperator != null) {
                        startOperator = section.startOperator == '0' ? '<' : '≤'
                    }

                    let endOperator = "";
                    if (section.endOperator) {
                        endOperator = section.endOperator == '2' ? '<' : '≤'
                    }

                    let price = "";
                    if (section.price != null) {
                        price = section.price.toLocaleString('zh', {
                            style: 'currency',
                            currency: 'CNY'
                        });
                    }

                    let isFixedPrice = "";
                    if (section.isFixedPrice != null) {
                        isFixedPrice = section.isFixedPrice == '0' ? '' : '固';
                    }

                    sectionHtml = sectionHtml +
                        `<tr>
                                            <td>
                                                <div>${section.startSection}</div>
                                            </td>
                                            <td style="width: 10%">
                                                <div class="flex">
                                                   <div>${startOperator}</div>
                                                   <div style="margin: 0 10px;">x</div>
                                                   <div>${endOperator}</div>
                                                </div>

                                            </td>
                                            <td>
                                                <div>${section.endSection == null ? "" : section.endSection}</div>
                                            </td>
                                            <td>
                                                <div style="margin-left: 6px;">${price}</div>
                                            </td>
                                            <td>
                                                <div >${isFixedPrice}</div>
                                            </td>
                                        </tr>`;
                }

                if (sectionHtml != '') {
                    sectionHtml = `<table class="table">
                                                ${sectionHtml}
                                        </table>`
                }
            }

            let arriHtml = ''
            if (ele.arriType == '0') {
                arriHtml = `
                        <div class="" style="margin-left: -5px;">
                            <div class="col-sm-3 col-xs-3" style="padding-left: 0px;padding-right: 0px;">
                                <div>${ele.arriProName}</div>
                            </div>
                            <div class="col-sm-3 col-xs-3" style="padding-left: 0px;padding-right: 0px;">
                                <div>${ele.arriCityName}</div>
                            </div>
                            <div class="col-sm-6 col-xs-6" style="padding-left: 0px;padding-right: 10px;">
                                <div>${arriAreaNameHtml}</div>
                            </div>
                        </div>`
            }else if (ele.arriType == '1') {
                arriHtml = `<div>${ele.arriAddrName}</div>`
            }

            html = html +
                `<tr id="addrRow_${index}" onclick="clickRow(${index})">
                    <td>
                        <div style="display: inline-block;">
                            <input id="checkbox_${index}" data-same-id="${ele.sameId}" type="checkbox">
                        </div>

                        <div style="display: inline-block;margin-left:5px">
                            <a class="fa fa-times-circle  ${editFlag}"
                               style="color: #fd8481;font-size: 20px;"
                               onclick="rmAddrData(${index},${type},'${ele.sameId}')" title="删除选择行"></a>
                        </div>
<!--                        <div style="display: inline-block;margin-left:5px">-->
<!--                                            <a class="fa fa-clone" style="color: #0092e7;font-size: 17px;"-->
<!--                                               onclick="cloneAddrData(${index},${type})" title="复制货品"></a>-->
<!--                        </div>-->
                        <div style="display: inline-block;margin-left:5px">
                            <a class="fa fa-pencil-square-o ${editFlag}" style="font-size: 17px;"
                               onclick="editAddrData(${index},'${ele.sameId}',${type})" title="修改数据"></a>
                        </div>

                        <input id="configId_${index}" value="${ele.id}" type="hidden">
                        <input id="deliProvinceId_${index}" value="${ele.deliProvinceId}" type="hidden">
                        <input id="deliCityId_${index}" value="${ele.deliCityId}" type="hidden">
                        <input id="deliAreaId_${index}" value="${ele.deliAreaId}" type="hidden">
                        <input id="arriProvinceId_${index}" value="${ele.arriProvinceId}" type="hidden">
                        <input id="arriCityId_${index}" value="${ele.arriCityId}" type="hidden">
                        <input id="arriAreaId_${index}" value="${ele.arriAreaId}" type="hidden">
                        <input id="carrName_${index}" value="${ele.carrName}" type="hidden">
                        <input id="carrierId_${index}" value="${ele.carrierId}" type="hidden">
                        <input id="deductionAmount_${index}" value="${ele.deductionAmount}" type="hidden">
                        <input id="deductionType_${index}" value="${ele.deductionType}" type="hidden">
                        <input id="deductionFeeType_${index}" value="${ele.deductionFeeType}" type="hidden">
                        <input id="sameId_${index}" value="${ele.sameId}" type="hidden">
                        <input id="billingMethod_${index}" value="${ele.billingMethod}" type="hidden">
                        <input id="goodsCharacter_${index}" value="${ele.goodsCharacter}" type="hidden">
                        <input id="isRoundTrip_${index}" value="${ele.isRoundTrip}" type="hidden">
                        <input id="carLen_${index}" value="${ele.carLen}" type="hidden">
                        <input id="carType_${index}" value="${ele.carType}" type="hidden">
                        <input id="billingType_${index}" value="${ele.billingType}" type="hidden">
                        <input id="oilRatio_${index}" value="${ele.oilRatio}" type="hidden">
                        <input id="arriAddrName_${index}" value="${ele.arriAddrName}" type="hidden">
                        <input id="arriType_${index}" value="${ele.arriType}" type="hidden">
                        <input id="oilType_${index}" value="${ele.oilType}" type="hidden">
                        <input id="oilCostType_${index}" value="${ele.oilCostType}" type="hidden">

                        <input id="goodsId_${index}" value="${ele.goodsId}" type="hidden">
                        <input id="goodsName_${index}" value="${ele.goodsName}" type="hidden">
                    </td>
                    <td>
                        <div class="" style="margin-left: -5px;">
                            <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 0px;">
                                <div>${ele.deliProName}</div>
                            </div>
                            <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 0px;">
                                <div>${ele.deliCityName}</div>
                            </div>
                            <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 10px;">
                                <div>${ele.deliAreaName}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        ${arriHtml}
                    </td>
                    <td>
                        <div class="">
                            <div>${bilName}</div>
                            <div>${goodsCharacter}</div>
                            <div>${isRoundTrip}</div>
                        </div>
                    </td>
                    <td>
                        <div class="" >
                            <div>${carLenName}</div>
                            <div>${carTypeName}</div>
                        </div>
                    </td>
                    <td>
                        <div class="">
                            <div>${ele.goodsName == null ? '' : ele.goodsName}</div>
                        </div>
                    </td>
                    <td>
                        <div class="">
                            <div>${ele.carrName}</div>
                        </div>
                    </td>
                    <td>
                        <div class="">
                            <div>${bilTypeHtml}</div>
                        </div>
                    </td>
                    <td>
                        <div class="">
                            <div>${oilRatioHtml}</div>
                        </div>
                    </td>
                    <td>
                        <div class="" id="sectionTab_${index}">
                            <div class="box_a">
                              ${amountHtml}
                              ${sectionHtml}
                            </div>
                        </div>
                        <div style="display: none" id="section_${index}" data-len="0">
                            <div class="">
                                <div class="col-md-12 col-sm-12">
                                    <table border="0" class="custom-tab table td">
                                        <thead>
                                            <tr>
                                                <th style="width: 5%;" rowspan="2">
                                                    <a class="collapse-link add-alink show-alink" style="font-size: 22px;"
                                                        title="新增" onclick="insertSection(${index})">+</a>
                                                </th>
                                                <th style="width: 25%;">区间开始</th>
                                                <th style="width: 25%;">自定义区间</th>
                                                <th style="width: 25%;">区间结束</th>
                                                <th style="width: 10%;">价格</th>
                                                <th style="width: 10%;">是否一口价</th>
                                            </tr>
                                        </thead>
                                        <tbody id="section_body_${index}">

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>`;

        }
        return html
    }

    /**
     * 保存当条数据
     *
     * @param addr_ind
     * @param type
     */
    function saveAddrData(addr_ind,type,sameId) {
        //数据集
        var data = {}

        //配置数据
        var configData = {};
        var addrRow = $("#addrRow_" + addr_ind);
        addrRow.find("[name]").each(function () {
            var name = $(this).attr("name");
            var value = $(this).val();
            configData[name] = value;
        });

        // let arriAreaName = xmSelect.get('#arriArea_' +addr_ind )[0].getValue("nameStr");
        // configData['arriAreaName'] = arriAreaName

        //价格明细数据
        var sectionData = []
        var sectionRow = $("#section_body_" + addr_ind + " tr");
        sectionRow.each(function(index) {
            var section = {}
            $(this).find("[name]").each(function () {
                var name = $(this).attr("name");
                var value = $(this).val();

                section[name] = value
            })
            sectionData.push(section)
        })
        configData['autoDispatchSectionList'] = sectionData

        sameId ? configData['sameId'] = sameId : ''

        data['autoDispatchConfigList'] = [configData]
        data['customerId'] = $("#customerId").val()
        data['isAutoDispatch'] = $("input[name='isAutoDispatch']").val()

     /*   var autoDispatchType = []
        $("input[name='autoDispatchType']").each(function() {
            if ($(this).is(':checked')) {
                autoDispatchType.push($(this).val());
            }
        });

        var autoDispatchTypeStr = autoDispatchType.join(",")
        data['autoDispatchType'] = autoDispatchTypeStr*/

        let autoDispatchTypeStr = 2
        data['autoDispatchType'] = autoDispatchTypeStr
        if (configData.arriType == null) {
            $.modal.msgError("到货地址请填写完整。")
            return
        }

        if (configData.deliAreaId == null || configData.deliAreaId == '') {
            $.modal.msgError("提货地址请填写完整。");
            // xmSelect.get('#arriArea_' + addr_ind)[0].warning();
            return;
        }
        if (configData.arriType == 0 && (configData.arriAreaId == null || configData.arriAreaId == '')) {
            $.modal.msgError("到货地址请填写完整。")
            return
        }
        if (configData.arriType == 1 && (configData.arriAddrName == null || configData.arriAddrName == '')) {
            $.modal.msgError("到货地址请填写完整。")
            return
        }

        if (configData.carrierId == null || configData.carrierId == '') {
            $.modal.msgError("请选择承运商。");
            return;
        }

        // if (autoDispatchTypeStr == 2 && (configData.billingMethod == null || configData.billingMethod == '')) {
        //     $.modal.msgError("请选择计价方式。")
        //     return
        // }

        if ((autoDispatchTypeStr == 0 || (autoDispatchTypeStr == 2 && configData.deductionType !='4'))
                && (configData.deductionAmount == null || configData.deductionAmount == '')) {
            $.modal.msgError("请填写配置价格。");
            return;
        }
        if (!sameId && autoDispatchTypeStr == 2 && configData.deductionType == '4'
                && (sectionData.length < 1 || (sectionData.length == 1 && sectionData[0].price == ''))) {
            $.modal.msgError("请填写配置价格。")
            return
        }

        if (sameId) {
            //修改
            $.ajax({
                url: ctx + "autoDispatchConfig/editConfig",
                type: "post",
                dataType: "json",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(data),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                        initConfig(type)
                    }else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();
                }
            });

        }else{
            //新添
            $.ajax({
                url: ctx + "autoDispatchConfig/addConfig",
                type: "post",
                dataType: "json",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(data),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);

                        rmAddrLocalData(addr_ind, type);

                        if (result.data) {
                            let html = insertAddrHtml(type,addr_ind,result.data[0]);

                            if (type == 0) {
                                $("#tab_body_0").prepend(html);
                            }else {
                                $("#tab_body_2").prepend(html);
                            }
                        }


                        // initConfig(type)
                    }else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();
                }
            });
        }

    }

    function editAddrData(addr_ind, sameId, type) {

        let oldHtml = $("#addrRow_" + addr_ind).html();

        editOldHtml.set(sameId, oldHtml);

        let data = {
            configId: $("#configId_" + addr_ind).val(),
            deliProvinceId: $("#deliProvinceId_" + addr_ind).val(),
            deliCityId: $("#deliCityId_" + addr_ind).val(),
            deliAreaId: $("#deliAreaId_" + addr_ind).val(),
            arriProvinceId: $("#arriProvinceId_" + addr_ind).val(),
            arriCityId: $("#arriCityId_" + addr_ind).val(),
            arriAreaId: $("#arriAreaId_" + addr_ind).val(),
            carrName: $("#carrName_" + addr_ind).val(),
            carrierId: $("#carrierId_" + addr_ind).val(),
            deductionAmount: $("#deductionAmount_" + addr_ind).val(),
            deductionType: $("#deductionType_" + addr_ind).val(),
            deductionFeeType: $("#deductionFeeType_" + addr_ind).val(),
            sameId: $("#sameId_" + addr_ind).val(),
            billingMethod: $("#billingMethod_" + addr_ind).val(),
            goodsCharacter: $("#goodsCharacter_" + addr_ind).val(),
            isRoundTrip: $("#isRoundTrip_" + addr_ind).val(),
            carLen: $("#carLen_" + addr_ind).val(),
            carType: $("#carType_" + addr_ind).val(),
            billingType: $("#billingType_" + addr_ind).val(),
            oilRatio: $("#oilRatio_" + addr_ind).val(),
            arriAddrName: $("#arriAddrName_" + addr_ind).val(),
            arriType: $("#arriType_" + addr_ind).val(),
            oilType: $("#oilType_" + addr_ind).val(),
            oilCostType: $("#oilCostType_" + addr_ind).val(),
            goodsId: $("#goodsId_" + addr_ind).val(),
            goodsName: $("#goodsName_" + addr_ind).val(),
        }

        Object.entries(data).forEach(([key, val]) => {
            if(val === "null") {
                data[key] = "";
            }
        })


        $("#addrRow_" + addr_ind).remove();
        insertAddrData(type,data);
    }

    /**
     * 复制行
     *
     * @param sourceIdx
     * @param type
     */
/*    function cloneAddrData(sourceIdx,type) {
        let addr_ind = insertAddrData(type);

        $("#deliProName_" + addr_ind).val($("#deliProName_" + sourceIdx).val());
        $("#deliCityName_" + addr_ind).val($("#deliCityName_" + sourceIdx).val());
        $("#deliAreaName_" + addr_ind).val($("#deliAreaName_" + sourceIdx).val());
        $("#arriProName_" + addr_ind).val($("#arriProName_" + sourceIdx).val());
        $("#arriCityName_" + addr_ind).val($("#arriCityName_" + sourceIdx).val());
        $("#arriAreaName_" + addr_ind).val($("#arriAreaName_" + sourceIdx).val());
        $("#carrName_" + addr_ind).val($("#carrName_" + sourceIdx).val());
        $("#carrierId_" + addr_ind).val($("#carrierId_" + sourceIdx).val());
        $("#deductionAmount_" + addr_ind).val($("#deductionAmount_" + sourceIdx).val());
        $("#deductionType_" + addr_ind).val($("#deductionType_" + sourceIdx).val());

        init("deliProvinceId_" + addr_ind, "deliCityId_" + addr_ind, "deliAreaId_" + addr_ind
            , $("#deliProvinceId_" + sourceIdx).val(), $("#deliCityId_" + sourceIdx).val(), $("#deliAreaId_" + sourceIdx).val());

        init("arriProvinceId_" + addr_ind, "arriCityId_" + addr_ind, "arriAreaId_" + addr_ind
            , $("#arriProvinceId_" + sourceIdx).val(), $("#arriCityId_" + sourceIdx).val(), $("#arriAreaId_" + sourceIdx).val());

    }*/

    /**
     * 打开价格详情
     * @param addr_ind
     */
    function openSectionTab(addr_ind,sameId,configId) {
        layer.open({
            type: 1,
            title: '配置价格',
            closeBtn: 0,
            area: ['80%', '80%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: $('#section_' + addr_ind),
            btn: ['保存','取消'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {
                if (sameId) {
                    $.ajax({
                        url: ctx + "autoDispatchConfig/getSection",
                        type: "post",
                        dataType: "json",
                        data: {"sameId": sameId},
                        beforeSend: function () {
                            $.modal.loading("正在处理中，请稍后...");
                            $.modal.disable();
                        },
                        success: function (data) {
                            if (data.code === 0) {
                                // let length = data.data.length;
                                // $('#section_' + addr_ind).data('len', length);

                                let sectionHmtl = '';
                                data.data.forEach(function (section, sectionIndex) {
                                    let startOperator = "";
                                    if (section.startOperator != null) {
                                        startOperator = section.startOperator == '0' ? '<' : '≤'
                                    }

                                    let endOperator = "";
                                    if (section.endOperator) {
                                        endOperator = section.endOperator == '2' ? '<' : '≤'
                                    }

                                    let price = "";
                                    if (section.price != null) {
                                        price = section.price.toLocaleString('zh', {
                                            style: 'currency',
                                            currency: 'CNY'
                                        });
                                    }

                                    let isFixedPrice = "";
                                    if (section.isFixedPrice != null) {
                                        isFixedPrice = section.isFixedPrice == '0' ? '否' : '是';
                                    }

                                    sectionHmtl = sectionHmtl +
                                    `<tr>
                                        <td>
                                            <a class="close-link del-alink show-alink" onclick="delSection(this,'${section.id}')" title="删除">-</a>
                                        </td>
                                        <td>
                                            <div>${section.startSection}</div>
                                        </td>
                                        <td>
                                            <div class="flex">
                                               <div>${startOperator}</div>
                                               <div style="margin: 0 10px;">x</div>
                                               <div>${endOperator}</div>
                                            </div>

                                        </td>
                                        <td>
                                            <div>${section.endSection == null ? "" : section.endSection}</div>
                                        </td>
                                        <td>
                                            <div>${price}</div>
                                        </td>
                                        <td>
                                            <div>${isFixedPrice}</div>
                                        </td>

                                    </tr>`;
                                });

                                $("#section_body_" + addr_ind).empty();
                                $("#section_body_" + addr_ind).append(sectionHmtl)
                            }

                            $.modal.closeLoading();
                            $.modal.enable();
                        }
                    });
                }
            },
            yes: function (index, layero) {
                if (sameId) {
                    //数据集
                    var data = {}
                    //配置数据
                    var configData = {};
                    configData['sameId'] = sameId
                    configData['id'] = configId

                    var sectionData = []
                    var sectionRow = $("#section_body_" + addr_ind + " tr");
                    sectionRow.each(function(index) {
                        var section = {}
                        $(this).find("[name]").each(function () {
                            var name = $(this).attr("name");
                            var value = $(this).val();

                            section[name] = value
                        })
                        if (!jQuery.isEmptyObject(section)) {
                            sectionData.push(section)
                        }
                    })

                    configData['autoDispatchSectionList'] = sectionData
                    data['autoDispatchConfigList'] = [configData]
                    console.log(sectionData)

                    $.ajax({
                        url: ctx + "autoDispatchConfig/addSection",
                        type: "post",
                        dataType: "json",
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(data),
                        beforeSend: function () {
                            $.modal.loading("正在处理中，请稍后...");
                            $.modal.disable();
                        },
                        success: function (data) {
                            if (data.code == 0) {
                                // initConfig(2)

                                $.modal.msgSuccess(data.msg);
                                layer.close(index);

                            }else {
                                $.modal.alertError(data.msg);
                            }
                            $.modal.closeLoading();
                            $.modal.enable();
                        }
                    })
                }
            }
        })
    }


    /**
     * 插入区间
     * @param addr_ind
     */
    function insertSection(addr_ind) {
        // 获取data-len的值
        var section_ind = $('#section_' + addr_ind).data('len');
        section_ind = Number(section_ind) + 1
        // 重新赋值
        $('#section_' + addr_ind).data('len', section_ind);
        let html =
            `
            <tr>
                <td>
                    <a class="close-link del-alink show-alink" onclick="delSection(this)" title="删除">-</a>
                </td>
                <td>
                    <input name="startSection"
                           id="startSection_${addr_ind}_${section_ind}"
                           oninput="$.numberUtil.onlyNumber(this)"
                           class="form-control" placeholder="区间开始" type="text"
                           min="0.0" step="0.1" maxlength="10">
                </td>
                <td>
                    <div class="flex">
                        <select name="startOperator"
                                id="startOperator_${addr_ind}_${section_ind}"
                                class="form-control">
                            <option value="0">＜</option>
                            <option value="1">≤</option>
                        </select>

                       <div style="margin: 0 10px;">x</div>

                        <select name="endOperator"
                                id="endOperator_${addr_ind}_${section_ind}"
                                class="form-control">
                            <option value="2">＜</option>
                            <option value="3">≤</option>
                        </select>
                    </div>

                </td>
                <td>
                    <input name="endSection"
                           id="endSection_${addr_ind}_${section_ind}"
                           oninput="$.numberUtil.onlyNumber(this)"
                           class="form-control" placeholder="区间结束" type="text"
                           min="0.0" step="0.1" maxlength="10">
                </td>
                <td>
                    <input name="price"
                           id="price_${addr_ind}_${section_ind}"
                           oninput="$.numberUtil.onlyNumber(this)"
                           class="form-control" placeholder="价格" type="text" required
                           min="0.0" maxlength="10">
                </td>
                <td>
                    <select name="isFixedPrice"
                            id="isFixedPrice_${addr_ind}_${section_ind}"
                            class="form-control">
                        <option value="0" selected>否</option>
                        <option value="1" >是</option>
                    </select>
                </td>

            </tr>
            `
        $("#section_body_" + addr_ind).append(html)
    }

    /**
     * 删除明细数据
     * @param obj
     * @param sectionId
     */
    function delSection(obj,sectionIds) {
        if (sectionIds) {
            layer.confirm('确定删除该条数据吗?', function (index) {
                $.ajax({
                    url: ctx + "autoDispatchConfig/rmSection",
                    type: "post",
                    dataType: "json",
                    data: {"sectionIds": sectionIds},
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },

                    success: function (data) {
                        if (data.code == 0) {
                            $(obj).parent('td').parent('tr').remove()
                            $.modal.msgSuccess(data.msg);
                        }else {
                            $.modal.alertError(data.msg);
                        }
                        $.modal.closeLoading();
                        $.modal.enable();
                    }
                })
                layer.close(index);
            });
        }else {
            $(obj).parent('td').parent('tr').remove()
        }
    }

    /**
     * 修改计价方式
     *
     * @param addr_ind
     */
    function changeBillingMethod(addr_ind, sameId, configId) {
        let billingMethod = $("#billingMethod_" + addr_ind).val();
        if (billingMethod == '3') {
            $(`#deductionType_${addr_ind}`).val('3')
            $(`#deductionType_${addr_ind}`).prop('disabled', true);
        }else {
            $(`#deductionType_${addr_ind}`).prop('disabled', false);
        }

        changeDeductionType(addr_ind, sameId, configId);
        // let html = ''
        // if (billingMethod == '3') {
        //     html =
        //         `<input name="deductionAmount"
        //                        id="deductionAmount_${addr_ind}"
        //                        class="form-control" placeholder="配置价格" type="text" required
        //                        oninput="$.numberUtil.onlyNumber(this)" min="0"/>
        //         `
        // } else {
        //     html =
        //         `<a class="fa fa-cog"
        //                     style="color: #0092e7;font-size: 20px;"
        //                     onclick="openSectionTab(${addr_ind},'${sameId}','${configId}')" title="配置价格"></a>
        //         `
        // }
        // $("#sectionTab_" + addr_ind).empty();
        // $("#sectionTab_" + addr_ind).append(html)

    }


    function updateHiddenInput(selectedId, hiddenInputId,index) {
        var selectedOptionText = $("#" + selectedId + "_" + index + " option:selected").text();
        $("#" + hiddenInputId + "_" + index).val(selectedOptionText);
    }

    function changeDeductionType(ind, sameId, configId) {
        let val = $(`#deductionType_${ind}`).val();

        let html = ''
        if (val != '4') {
            html =
                `<input name="deductionAmount"
                           id="deductionAmount_${ind}"
                           class="form-control" placeholder="配置价格" type="text" required
                           oninput="$.numberUtil.onlyNumber(this)" min="0"/>
            `
        } else {
            html =
                `<a class="fa fa-cog"
                        style="color: #0092e7;font-size: 20px;"
                        onclick="openSectionTab(${ind},'${sameId}','${configId}')" title="配置价格"></a>
            `
        }
        $("#sectionTab_" + ind).empty();
        $("#sectionTab_" + ind).append(html)

        // 添加处理额外下拉框显示的逻辑
        var extraSelectContainer = $("#deductionFeeTypeDiv_" + ind);

        $("#deductionFeeType_" + ind).val('0');
        if (val === "1") { // 当选择"减(%)"时
            extraSelectContainer.show();
        } else {
            extraSelectContainer.hide();
        }

    }

    /**
     * 导入数据
     */
    function importAllData() {
        layer.open({
            type: 1,
            area: ['70%', '85%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入数据',
            content: $('#importTpl').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            success: function () {
                let options = initOptions();
                $.table.init(options);
            },

            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx') && !$.common.endWith(file, '.xlsm'))){
                    $.modal.msgWarning("请选择后缀为 ‘xls‘或’xlsx’的文件。");
                    return false;
                }
                var index = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#file')[0].files[0]);
                formData.append("customerId", customerId);
                $.ajax({
                    url: ctx + "autoDispatchConfig/import",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            initConfig(2)

                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code == web_status.WARNING) {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });
    }


    function initOptions() {
        return {
            id: "file-table",
            url: `${ctx}autoDispatchConfigFile/list`,
            showToggle: false,
            showColumns: false,
            showSearch: false,
            showRefresh: false,
            modalName: "历史文件",
            height: 560,
            uniqueId: "id",
            clickToSelect: true,
            columns: [
                {
                    title: '文件名称',
                    align: 'left',
                    field: 'fileName',
                },
                {
                    title: '上传人',
                    align: 'center',
                    field: 'regUserName',
                },
                {
                    title: '上传时间',
                    align: 'center',
                    field: 'regDate',
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index){
                        return `<a href="#" onclick="downloadFile('${row.filePath}','${row.fileName}')">下载</a>`
                    }
                },
            ]
        };
    }

    function downloadFile(filePath, fileName) {
        const protocol = document.location.protocol; // 确保使用与当前页面相同的协议
        const a = document.createElement('a');
        a.href = protocol + "//" + document.location.host + filePath; // 动态选择协议
        a.download = fileName;
        a.click();

    }


    function clickRow(ind) {
        // 获取点击事件的目标元素
        const target = event.target;

        // 如果目标元素是复选框，则不执行后续代码
        if (target.type === 'checkbox') {
            return;
        }

        // 获取复选框
        const checkbox = $(`#checkbox_${ind}`);

        // 切换复选框的选中状态
        checkbox.prop('checked', !checkbox.prop('checked'));

        // 更新 delSameId 数组
        updateDelSameId(ind, checkbox);

        // 新增：同步全选框状态
        const all = $("input[type='checkbox'][id^='checkbox_']");
        const checked = all.filter(":checked");
        $("#selectAllCheckbox").prop('checked', all.length === checked.length && all.length > 0);
    }

    var delSameId = [];

    $(document).on('change', 'input[type="checkbox"][id^="checkbox_"]', function() {
        var id = $(this).attr('id');
        var ind = id.split('_')[1]; // 获取复选框的索引
        updateDelSameId(ind, $(this));
        // 同步全选框状态
        const all = $("input[type='checkbox'][id^='checkbox_']");
        const checked = all.filter(":checked");
        $("#selectAllCheckbox").prop('checked', all.length === checked.length && all.length > 0);
    });

    function updateDelSameId(ind, checkbox) {
        var sameId = checkbox.data('same-id');
        var obj = { index: ind, sameId: sameId };

        if (checkbox.prop('checked')) {
            // 添加对象到 delSameId 数组
            delSameId.push(obj);
        } else {
            // 从 delSameId 数组中移除对象
            delSameId = delSameId.filter(item => item.index !== ind);
        }
        console.log(delSameId); // For debugging
    }


    /**
     * 设定中转站地址
     */
    function addSubsectionAddr() {

        $.modal.open("提货信息", ctx + "basic/address/selectAddress?customerId="+customerId+"&addrType=3","","",function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            let data = {}
            data.customerId = customerId;
            data.autoSubsectionAddrId = rows[0]["addressId"]

            $.ajax({
                url: ctx + "autoDispatchConfig/setSubsectionAddr",
                type: "post",
                dataType: "json",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(data),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },

                success: function (data) {
                    if (data.code == 0) {
                        $.modal.msgSuccess(data.msg);

                        $("#subsectionAddrSpan").show()
                        $("#subsectionAddrSpan").prop("title", "已配置中转点：" + rows[0]["addrName"]);
                    }else {
                        $.modal.alertError(data.msg);
                    }
                    $.modal.closeLoading();
                }
            })


            layer.close(index);
        });

    }

    function removeAll() {
        // 直接收集当前选中的checkbox的sameId
        var checkedBoxes = $("input[type='checkbox'][id^='checkbox_']:checked");
        if (checkedBoxes.length === 0) {
            $.modal.msgWarning("请至少选择一条数据");
            return;
        }
        var sameIds = checkedBoxes.map(function() {
            return $(this).data('same-id');
        }).get().join(',');

        var type = 2

        layer.confirm(`确定删除选中的${checkedBoxes.length}条数据吗?`, function (index) {
            $.ajax({
                url: ctx + "autoDispatchConfig/rmConfig",
                type: "post",
                dataType: "json",
                data: {"sameId": sameIds, "type": type},
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function (data) {
                    if (data.code == 0) {
                        $.modal.msgSuccess(data.msg);
                        delSameId = [];
                        initConfig(type); // 刷新数据
                        $("#selectAllCheckbox").prop('checked', false);
                    }else {
                        $.modal.msgError(data.msg);
                    }

                    $.modal.closeLoading();
                    $.modal.enable();
                }
            })
        });
    }


    function initXmSelect(id, dataList) {
        var demo4 = xmSelect.render({
            el: '#' + id,
            language: 'zn',
            size: 'mini',
            model: {
                label: {
                    type: 'block',
                    block: {
                        //最大显示数量, 0:不限制
                        showCount: 3,
                        //是否显示删除图标
                        showIcon: true,
                    }
                }
            },
            toolbar: {
                show: true,
            },
            autoRow: true,
            prop: {
                name: 'AREA_NAME',
                value: 'AREA_CODE',
            },
            on: function(data){
                //arr:  当前多选已选中的数据
                var arr = data.arr;
                let reverse = id.split('_').reverse();
                $("#arriAreaId_" + reverse[0]).val(arr.map(obj => obj.AREA_CODE).join(","))
            },
            data: dataList
        })
    }

    /**
     *
     **/
    function initCarrierBsSuggest(invInd) {
        let ind = invInd
        $("#carrName_" + ind).bsSuggest({
            idField: 'carrierId',                    //每组数据的哪个字段作为 data-id，优先级高于 indexId 设置（推荐）
            keyField: 'carrName',                   //每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            allowNoKeyword: false, //是否允许无关键字时请求数据
            multiWord: false, //以分隔符号分割的多关键字支持separator: ",", //多关键字支持时的分隔符，默认为空格
            getDataMethod: "url", //获取数据的方式，总是从 URL 获取
            effectiveFields: ["carrName", "contact","phone","legalCard", "provinceName", "cityName"],
            effectiveFieldsAlias: {carrName: "承运商名称", contact: "联系人",phone:"电话",legalCard:"身份证", provinceName: "省份", cityName: "城市"},
            showHeader: true,
            hideOnSelect: true,            // 鼠标从列表单击选择了值时，是否隐藏选择列表
            inputWarnColor: 'rgba(255,0,0,.1)', //输入框内容不是下拉列表选择时的警告色
            url: ctx + 'basic/carrier/bsSuggestList', //custAbbr /*优先从url ajax 请求 json 帮助数据，注意最后一个参数为关键字请求参数*/
            fnPreprocessKeyword: function (keyword) {
                //请求数据前，对输入关键字作进一步处理方法。注意，应返回字符串
                return keyword.trim();
            },
            fnAdjustAjaxParam: function (keyword, options) {  //该插件默认是GET请求  https://github.com/lzwme/bootstrap-suggest-plugin/issues?q=post
                //if(!isNull(keyword)) { //走get请求
                console.log("post")
                return {
                    type: 'POST',
                    timeout: 10000,
                    data: {
                        carrName: keyword,
                        pageSize: 20,  //承运商数据较多 默认只查20条
                        pageNum: 1
                    }
                }
                //}
            },
            processData: function (json) {// url 获取数据时，对数据的处理，作为 getData 的回调函数
                var i, len, data = {value: []};
                if (!json || json.rows.length == 0) {
                    return false;
                }
                len = json.rows.length;
                for (i = 0; i < len; i++) {
                    data.value.push({
                        "carrName": json.rows[i].carrName,
                        "contact": json.rows[i].contact,
                        "phone": json.rows[i].phone,
                        "legalCard": json.rows[i].legalCard,
                        "provinceName": json.rows[i].provinceName,
                        "cityName": json.rows[i].cityName,
                        "carrierId": json.rows[i].carrierId
                    });
                }
                return data;
            }
        }).on('onSetSelectValue', function (e, keyword, data) {
            $("#carrierId_" + ind).val(data.carrierId);
            $("#carrName_" + ind).val(data.carrName);
        });
    }

    function init(provinceId, cityId, areaId, province, city, area) {
        $('#'+ provinceId +' option').remove();
        $('#'+ cityId +' option').remove();
        $('#'+ areaId +' option').remove();
        $('#' + provinceId).append("<option value='' disabled selected hidden>省</option>");
        $('#'+ cityId).append("<option value='' disabled selected hidden>市</option>");
        $('#'+ areaId).append("<option value='' disabled selected hidden>区</option>");

        $('#' + provinceId).append("<option value=''></option>")
        $('#' + cityId).append("<option value=''></option>")
        $('#' + areaId).append("<option value=''></option>")

        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            async: false,
            success: function (result) {
                for (var i in result) {
                    $('#' + provinceId).append("<option value='" + result[i].PROVINCE_CODE + "'>" + result[i].PROVINCE_NAME + "</option>")
                }
                $('#'+ provinceId +' option').each(function () {
                    if ($(this).val() == province) {
                        $(this).attr("selected", true)
                    }
                })
            }
        });
        if (province !== undefined) {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + province,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }
                    $('#'+ cityId +' option').each(function () {
                        if ($(this).val() == city) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + city,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                    }
                    $('#'+ areaId +' option').each(function () {
                        if ($(this).val() == area) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
        }

        $('#'+ provinceId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ cityId +' option').remove();
                    $('#'+ areaId +' option').remove();
                    $('#'+ cityId).append("<option value='' disabled selected hidden>市</option>");
                    $('#'+ areaId).append("<option value='' disabled selected hidden>区</option>");

                    $('#' + cityId).append("<option value=''></option>")
                    $('#' + areaId).append("<option value=''></option>")

                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }

                }
            });
        });

        $('#'+ cityId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ areaId +' option').remove();
                    $('#'+ areaId).append("<option value='' disabled selected hidden>区</option>");
                    $('#' + areaId).append("<option value=''></option>")

                    for (var i in result) {
                        $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                    }
                    // $('#'+ areaId).selectpicker('refresh');
                }
            });
        });
    }

    function init_1(provinceId, cityId, areaId, province, city, area) {
        $('#'+ provinceId +' option').remove();
        $('#'+ cityId +' option').remove();
        $('#'+ areaId +' option').remove();
        $('#' + provinceId).append("<option value='' disabled selected hidden>省</option>");
        $('#'+ cityId).append("<option value='' disabled selected hidden>市</option>");
        $('#'+ areaId).append("<option value='' disabled selected hidden>区</option>");

        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            async: false,
            success: function (result) {
                for (var i in result) {
                    $('#' + provinceId).append("<option value='" + result[i].PROVINCE_CODE + "'>" + result[i].PROVINCE_NAME + "</option>")
                }
                $('#'+ provinceId +' option').each(function () {
                    if ($(this).val() == province) {
                        $(this).attr("selected", true)
                    }
                })
            }
        });
        if (province !== undefined) {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + province,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }
                    $('#'+ cityId +' option').each(function () {
                        if ($(this).val() == city) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + city,
                async: false,
                success: function (result) {
                    initXmSelect(areaId, result);
                    xmSelect.get('#' + areaId)[0].setValue(area.split(','))
                }
            });
        }

        $('#'+ provinceId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ cityId +' option').remove();
                    $('#'+ cityId).append("<option value='' disabled selected hidden>市</option>");
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }

                    //清空区
                    xmSelect.get('#' + areaId)[0].update({data: []});
                }
            });
        });

        $('#'+ cityId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + $(this).val(),
                success: function (result) {
                    initXmSelect(areaId, result);
                }
            });
        });
    }

    function exportExcel() {
     /*   var autoDispatchType = []
        $("input[name='autoDispatchType']").each(function() {
            if ($(this).is(':checked')) {
                autoDispatchType.push($(this).val());
            }
        });

        var autoDispatchTypeStr = autoDispatchType.join(",")*/
        var autoDispatchTypeStr = 2

        let data = {"customerId": customerId, "type": autoDispatchTypeStr}

        const fields = [
            "deliProvinceId", "deliCityId", "deliAreaId",
            "arriProvinceId", "arriCityId", "arriAreaId",
            "carrName", "billingMethod"
        ];
        fields.forEach(field => {
            const value = $("#" + field).val();
            if (value !== "" && value !==null) {
                data[field] = value;
            }
        });

        $.modal.confirm("确定导出数据吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(ctx + "autoDispatchConfig/export", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });

    }

    function copyConfig() {
        layer.open({
            type: 1,
            area: ['60%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "拷贝配置",
            content: $("#copyConfigHtml").html(),
            btn: ['确定', '关闭'],
            shadeClose: true,
            success: function (layero, index) {
                init("deliPid","deliCid","deliAid");

            },
            yes: function(index, layero) {
                let data = {
                    sourceCustId: customerId,
                    targetCustId:$("#targetCustId").val(),
                    deliAid:$("#deliAid").val()
                }

                $.ajax({
                    url: ctx + "autoDispatchConfig/copyConfig",
                    type: "post",
                    // dataType: "json",
                    // contentType: "application/json; charset=utf-8",
                    // data: JSON.stringify(data),
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function(result) {
                        if (result.code == 0) {
                            $.modal.msgSuccess(result.msg);
                        }else {
                            $.modal.alertError(result.msg);
                        }
                        $.modal.closeLoading();
                        $.modal.enable();
                    }
                });
            },
            cancel: function(index) {
                return true;
            }
        })
    }

    function toggleAllCheckbox(source) {
        const checked = source.checked;
        $("input[type='checkbox'][id^='checkbox_']").prop('checked', checked).trigger('change');
    }

</script>
</body>

</html>