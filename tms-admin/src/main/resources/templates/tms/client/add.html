<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增客户信息')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<style type="text/css">
    .bg_title{
        font-size: 15px;
        font-weight: bold;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 113px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;
    }
    .fcff{
        color: #ff1f1f;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .tc{
        text-align: center;
    }
    .bor{
        border: 1px #eee solid;
    }
    .over{
        overflow: hidden;

    }
    .fl{
        float: left;
    }
    .ml10{
        margin-left: 10px;
    }
    .mt10{
        margin-top: 10px;
    }
    .table>thead>tr>th{
        background: #eff3f9 !important;
        padding: 0;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }

    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .theme-explorer{
        width: 100%;
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }

</style>
<body>
<div class="form-content">
    <form id="form-client-add" class="form-horizontal" novalidate="novalidate">
        <input name="mobileUserId" id="mobileUserId" th:value="${mobileUserId}" type="hidden">
        <input name="password" id="password" th:value="${password}" type="hidden">
        <input name="salt" id="salt" th:value="${salt}" type="hidden">
        <input name="loginName" id="loginName" th:value="${loginName}" type="hidden">
        <input name="userType" id="userType" th:value="${userType}" type="hidden">
        <input name="userId" id="userId" th:value="${userId}" type="hidden">
        <input name="corDate" id="corDate" th:value="${corDate}" type="hidden">
        <input name="sourceType" id="sourceType" th:value="${sourceType}" type="hidden">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <!--                <div class="panel-heading">-->
                <!--                    <h5 class="panel-title">-->
                <!--                        <a data-toggle="collapse" data-parent="#accordion"-->
                <!--                           href="tabs_panels.html#collapseOne">基础信息</a>-->
                <!--                    </h5>-->
                <!--                </div>-->
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body" >
                        <div class="bg_title">基础信息</div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        客户名称：</font></label>
                                    <div class="flex_right">
                                        <input name="custName" id="custName" class="form-control" type="text"
                                               maxlength="100" required autocomplete="off" th:value="${companyName}"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 客户简称：</label>
                                    <div class="flex_right">
                                        <input name="custAbbr" id="custAbbr" class="form-control" type="text"
                                               autocomplete="off" maxlength="100" required/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 结算公司：</label>
                                    <div class="flex_right">
                                        <select id="operateCorp" name="operateCorp" class="form-control valid" onchange="changeBalaCorp()"
                                                th:with="type=${@dict.getType('bala_corp')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 运营公司：</label>
                                    <div class="flex_right">
                                        <select id="balaCorp" name="balaCorp" class="form-control valid"
                                                th:with="type=${@dict.getType('bala_corp')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left"><span class="fcff">*</span>运营部：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <select id="salesId" name="salesId" onchange="changeSalesId()"-->
<!--                                                class="form-control valid" aria-invalid="false" required>-->
<!--                                            <option value=""> &#45;&#45; 请选择 &#45;&#45;</option>-->
<!--                                            <option th:each="salesGroup : ${salesGroupList}"-->
<!--                                                    th:text="${salesGroup.salesName}"-->
<!--                                                    th:value="${salesGroup.id}"></option>-->
<!--                                        </select>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

                           <!-- <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        运营组：</font></label>
                                    <div class="flex_right">
                                        <select name="salesDept" id="salesDept" class="form-control valid"
                                                aria-invalid="false" required>
&lt;!&ndash;                                            <option value=""></option>&ndash;&gt;
&lt;!&ndash;                                            <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"&ndash;&gt;
&lt;!&ndash;                                                    th:text="${mapS.deptName}"></option>&ndash;&gt;
                                        </select>
                                    </div>
                                </div>
                            </div>-->
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>运营组：</font></label>
                                    <div class="flex_right">
                                        <input name="salesDeptName" id="salesDeptName" onclick="selectSalesDept()"
                                               class="form-control" type="text"
                                               maxlength="25" autocomplete="off" readonly required>
                                        <input name="salesDept" id="salesDept" class="form-control" type="hidden">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>开票类型：</label>
                                    <div class="flex_right">
                                        <select name="billingType" id="billingType" class="form-control valid"
                                                th:with="type=${@dict.getType('billing_type')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:selected="${dict.dictValue == '4'}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">
                                        客户集团：</label>
                                    <div class="flex_right">

                                    <!--    <select name="group.groupId" id="group.groupId" class="form-control valid"
                                                aria-invalid="false">
                                            <option value=""></option>
                                            <option th:each="mapS,status:${groupList}" th:value="${mapS.groupId}"
                                                    th:text="${mapS.groupName}"></option>
                                        </select>-->
                                        <div class="input-group">
                                            <input name="groupName" id="groupName" placeholder="客户集团" class="form-control" type="text"
                                                   maxlength="20" autocomplete="off" aria-required="true">
                                            <input name="group.groupId" id="groupId" placeholder="客户集团" class="form-control" type="hidden"
                                                   maxlength="20" autocomplete="off" aria-required="true">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">客户级别：</label>
                                    <div class="flex_right">
                                        <select name="customerLevel" class="form-control valid"
                                                th:with="type=${@dict.getType('customer_level')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">
                                        客户性质：</label>
                                    <div class="flex_right">
                                        <select name="customerType" id="customerType" class="form-control valid"
                                                aria-invalid="false">
                                            <option th:each="mapS,status:${userClass}" th:value="${mapS.value}"
                                                    th:text="${mapS.context}" th:selected="${userClassId+''==mapS.value+''}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">证件号：</label>
                                    <div class="flex_right">
                                        <input name="businesslicense" id="businesslicense" class="form-control"
                                               maxlength="25" th:value="${businessLicense}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">企业性质：</label>
                                    <div class="flex_right">
                                        <select name="enterpriseNature" class="form-control valid"
                                                th:with="type=${@dict.getType('enterprise_nature')}">
                                            <option value="" selected="selected">-- 请选择 --</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><font>
                                        结算组：</font></label>
                                    <div class="flex_right">

                                        <select name="balaDept" id="balaDept" class="form-control valid"
                                                aria-invalid="false">
                                            <option value=""></option>
                                            <option th:each="mapS,status:${balanceDept}" th:value="${mapS.deptId}"
                                                    th:text="${mapS.deptName}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">驻场组：</label>
                                    <div class="flex_right">
                                        <select name="stationDept" id="stationDept" class="form-control valid"
                                                aria-invalid="false">
                                            <option value=""></option>
                                            <option th:each="mapS,status:${stationDept}" th:value="${mapS.deptId}"
                                                    th:text="${mapS.deptName}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6" style="display: none">
                                <div class="flex">
                                    <label class="flex_left">客户来源：</label>
                                    <div class="flex_right">
                                        <select name="customerSource" class="form-control valid"
                                                th:with="type=${@dict.getType('customer_source')}">
                                            <option value="">-- 请选择 --</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}" th:selected="${dict.dictValue == '1'}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!--                            <div class="col-md-3 col-sm-6">-->
                            <!--                                <div class="flex">-->
                            <!--                                    <label class="flex_left">是否启用合同价计费：</label>-->
                            <!--                                    <div class="flex_right">-->
                            <!--                                        <select name="enableContractPrice" class="form-control valid">-->
                            <!--                                            <option value="1">不启用</option>-->
                            <!--                                            <option value="2">启用</option>-->
                            <!--                                        </select>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                            <!--<div class="col-md-3 col-sm-6" style="display: none">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        客户状态：</font></label>
                                    <div class="flex_right">
                                        <label class="toggle-switch switch-solid">
                                            <input type="checkbox" id="isEnabled">
                                            <span></span>
                                        </label>
                                    </div>
                                </div>
                            </div>-->
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 联系人：</label>
                                    <div class="flex_right">
                                        <div class="">
                                            <input name="contact" id="contact" class="form-control" type="text"
                                                   maxlength="25" autocomplete="off" required th:value="${userName}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        联系人电话：</font></label>
                                    <div class="flex_right">
                                        <div class="over">
                                            <div class="fl" style="width: 80%">
                                                <input name="phone" required id="phone" class="form-control"
                                                       type="text"
                                                       maxlength="25" autocomplete="off" th:value="${phone}">
                                            </div>
                                            <div class="fl ml10">
                                                <a href="#" data-toggle="tooltip" data-placement="top" title="系统货主方登录账号。">
                                                    <i class="fa fa-question-circle" style="color: #ff9113;font-size: 18px"></i>
                                                </a>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> APP联系人：</label>
                                    <div class="flex_right">
                                        <div class="input-group">
                                            <input name="appDeliContact" id="appDeliContact" placeholder="APP联系人" class="form-control" type="text"
                                                   maxlength="20" autocomplete="off" aria-required="true" required>
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> APP联系人手机：</label>
                                    <div class="flex_right">
                                        <div class="over">
                                            <div class="fl" style="width: 80%">
                                                <input name="appDeliMobile" id="appDeliMobile" placeholder="APP联系人" class="form-control" type="text"
                                                       maxlength="11" autocomplete="off" aria-required="true" required>
                                            </div>
                                            <div class="fl ml10">
                                                <a href="#" data-toggle="tooltip" data-placement="top" title="说明：本信息为司机到场需联系的我司人员信息，信息将在app（畅运通-车主版）中显示。">
                                                    <i class="fa fa-question-circle" style="color: #ff9113;font-size: 18px"></i>
                                                </a>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">
                                        联系人职位：</label>
                                    <div class="flex_right">
                                        <input name="contactPost" id="contactPost" class="form-control" type="text"
                                               maxlength="25" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">
                                        邮箱：</label>
                                    <div class="flex_right">
                                        <input name="email" id="email" class="form-control" type="text"
                                               maxlength="25" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">引荐人：</label>
                                    <div class="flex_right">
                                        <div class="input-group">
                                            <input name="referrer" id="referrer" class="form-control" type="text"
                                                   maxlength="25" autocomplete="off">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">标的金额：</label>
                                    <div class="flex_right">
                                        <div class="input-group">
                                            <input name="subjectAmount" id="subjectAmount" placeholder="标的金额"
                                                   class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                   type="text" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="flex">
                                <label class="flex_left">地址：</label>
                                <div class="flex_right">
                                    <div class="col-sm-2">
                                        <select name="provinceId" id="province" class="form-control valid"
                                                aria-invalid="false"></select>
                                        <input name="provinceName" id="provinceName" class="form-control" type="hidden">
                                    </div>
                                    <div class="col-sm-2">
                                        <select name="cityId" id="city" class="form-control valid" aria-invalid="false"></select>
                                        <input name="cityName" id="cityName" class="form-control" type="hidden">
                                    </div>
                                    <div class="col-sm-2">
                                        <select name="areaId" id="area" class="form-control valid" aria-invalid="false"></select>
                                        <input name="areaName" id="areaName" class="form-control" type="hidden">

                                    </div>
                                    <div class="col-sm-6">
                                        <input name="address" id="address" placeholder="请输入详细地址" class="form-control"
                                               type="text" th:value="${address}"
                                               maxlength="50">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="flex">
                                    <label class=" flex_left">备注：</label>
                                    <div class="flex_right">
                                            <textarea name="memo" id="memo" maxlength="100" class="form-control valid"
                                                      rows="3" autocomplete="off"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg_title mt10">系统信息</div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">是否跟踪：</label>
                                    <div class="flex_right">
                                        <select name="isNeedTrace" class="form-control valid" disabled>
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </div>
                                </div>

                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">是否需要回单：</label>
                                    <div class="flex_right">
                                        <select name="isNeedReceipt" class="form-control valid" disabled>
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">设置指导价：</label>
                                    <div class="flex_right">
                                        <select name="crtGuidePrice" id="crtGuidePrice" class="form-control valid" required aria-invalid="false" disabled>
                                            <option value=""></option>
                                            <option value="0">否</option>
                                            <option value="1" selected>是</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">客户类型：</label>
                                     <div class="col-sm-7">
                                         <select id="custType" name="custType" class="form-control valid"
                                                 th:with="type=${@dict.getType('cust_Type')}">
                                             <option value=""></option>
                                             <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                     th:value="${dict.dictValue}"></option>
                                         </select>
                                     </div>
                                 </div>
                             </div>-->

                        </div>

                        <!--                        <div class="row">-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-5">-->
                        <!--                                        业务员：</label>-->
                        <!--                                    <div class="col-sm-7">-->
                        <!--                                        <div class="input-group">-->
                        <!--                                            <input name="psndocName" id="psndocName" class="form-control" type="text"-->
                        <!--                                                   maxlength="25" autocomplete="off" onclick="selectUser()" disabled>-->
                        <!--                                            <input name="psndoc" id="psndoc" class="form-control" type="hidden"-->
                        <!--                                                   maxlength="25" autocomplete="off" >-->
                        <!--                                            <span class="input-group-addon"><i class="fa fa-search"></i></span>-->
                        <!--                                        </div>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->

                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-5">-->
                        <!--                                        业务员联系方式：</label>-->
                        <!--                                    <div class="col-sm-7">-->
                        <!--                                        <input name="psncontact" id="psncontact" class="form-control" type="text"-->
                        <!--                                               maxlength="25" autocomplete="off" disabled>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-5">税率：</label>-->
                        <!--                                    <div class="col-sm-7">-->
                        <!--                                        <input name="tariff" id="tariff" oninput="$.numberUtil.onlyNumberCustom(this,100,0,5,2);"-->
                        <!--                                               type="text" class="form-control" autocomplete="off">-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->

                        <!--                        </div>-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">装卸计价：</label>
                                    <div class="flex_right">
                                        <div class="over">
                                            <div class="fl" style="width: 80%">
                                                <select name="handlingChargesType" id="handlingChargesType" class="form-control valid">
                                                    <option value=""></option>
                                                    <option value="0">按件</option>
                                                    <option value="1">按方</option>
                                                    <option value="2">按吨</option>
                                                </select>
                                            </div>
                                            <div class="fl ml10">
                                                <a href="#" data-toggle="tooltip" data-placement="top" title="三方费用自动计费配置。">
                                                    <i class="fa fa-question-circle" style="color: #ff9113;font-size: 18px"></i>
                                                </a>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">装卸单价：</label>
                                    <div class="flex_right">
                                        <input style="display: inline-block" name="handlingCharges" id="handlingCharges" type="text"
                                               class="form-control valid" oninput="$.numberUtil.onlyNumberCustom(this,9999999,-9999999,10,3);"
                                               autocomplete="off">
                                    </div>
                                </div>
                            </div>

                        </div>

                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
            <!--            <div class="panel panel-default">-->
            <!--                <div class="panel-heading">-->
            <!--                    <h4 class="panel-title">-->
            <!--                        <a data-toggle="collapse" data-parent="#accordion"-->
            <!--                           href="tabs_panels.html#collapseTwo">联系方式</a>-->
            <!--                    </h4>-->
            <!--                </div>-->
            <!--                <div id="collapseTwo" class="panel-collapse collapse in">-->
            <!--                    <div class="panel-body">-->
            <!--                        &lt;!&ndash;联系方式 start&ndash;&gt;-->
            <!--                        <div class="row">-->
            <!--                            <div class="col-md-3 col-sm-6">-->
            <!--                                <div class="form-group">-->
            <!--                                    <label class="col-sm-5"><font color="red">-->
            <!--                                        联系人：</font></label>-->
            <!--                                    <div class="col-sm-7">-->
            <!--                                        <div class="input-group">-->
            <!--                                            <input name="contact" id="contact" class="form-control" type="text"-->
            <!--                                                   maxlength="25" autocomplete="off" required th:value="${userName}">-->
            <!--                                        </div>-->
            <!--                                    </div>-->
            <!--                                </div>-->
            <!--                            </div>-->
            <!--                            <div class="col-md-3 col-sm-6">-->
            <!--                                <div class="form-group">-->
            <!--                                    <label class="col-sm-5">-->
            <!--                                        联系人职位：</label>-->
            <!--                                    <div class="col-sm-7">-->
            <!--                                        <input name="contactPost" id="contactPost" class="form-control" type="text"-->
            <!--                                               maxlength="25" autocomplete="off">-->
            <!--                                    </div>-->
            <!--                                </div>-->
            <!--                            </div>-->
            <!--                            <div class="col-md-3 col-sm-6">-->
            <!--                                <div class="form-group">-->
            <!--                                    <label class="col-sm-5"><font color="red">-->
            <!--                                        联系人电话：</font></label>-->
            <!--                                    <div class="col-sm-7">-->
            <!--                                        <input name="phone" required id="phone" class="form-control"-->
            <!--                                               type="text"-->
            <!--                                               maxlength="25" autocomplete="off" th:value="${phone}">-->
            <!--                                    </div>-->
            <!--                                </div>-->
            <!--                            </div>-->
            <!--                            <div class="col-md-3 col-sm-6">-->
            <!--                                <div class="form-group">-->
            <!--                                    <label class="col-sm-5">-->
            <!--                                        邮箱：</label>-->
            <!--                                    <div class="col-sm-7">-->
            <!--                                        <input name="email" id="email" class="form-control" type="text"-->
            <!--                                               maxlength="25" autocomplete="off">-->
            <!--                                    </div>-->
            <!--                                </div>-->
            <!--                            </div>-->
            <!--                        </div>-->
            <!--                        &lt;!&ndash;联系方式 end&ndash;&gt;-->
            <!--                    </div>-->
            <!--                </div>-->
            <!--            </div>-->
            <!-- <div class="panel panel-default">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a data-toggle="collapse" data-parent="#accordion"
                            href="tabs_panels.html#collapseThree">开票信息</a>
                     </h4>
                 </div>
                 <div id="collapseThree" class="panel-collapse collapse in">
                     <div class="panel-body">
                         &lt;!&ndash;会计属性 start&ndash;&gt;
                         <div class="row">
                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5"><font color="red">
                                         开票公司：</font></label>
                                     <div class="col-sm-7">
                                         <input type="text" id="billingCorp" class="form-control" disabled>
                                         <input type="hidden" id="billingCorpVal" name="billingCorp" class="form-control">
                                             &lt;!&ndash;<select  name="billingCorp" id="billingCorp" class="form-control valid"
                                                     th:with="type=${@dict.getType('bala_corp')}" required disabled>
                                                 <option value=""></option>
                                                 <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                         th:value="${dict.dictValue}"></option>
                                             </select>&ndash;&gt;
                                     </div>
                                 </div>
                             </div>

                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         发票抬头：</label>
                                     <div class="col-sm-7">
                                         <input name="billingPayable" id="billingPayable"  class="form-control"
                                                type="text"
                                                maxlength="25" autocomplete="off">
                                     </div>
                                 </div>
                             </div>
                             &lt;!&ndash;<div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         法人代表：</label>
                                     <div class="col-sm-7">
                                         <input name="legalRepresent" id="legalRepresent" class="form-control"
                                                type="text" th:value="${userName}"
                                                maxlength="25" autocomplete="off">
                                     </div>
                                 </div>
                             </div>&ndash;&gt;
                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         纳税识别号：</label>
                                     <div class="col-sm-7">
                                         <input name="taxIdentify" id="taxIdentify" class="form-control" type="text"
                                                maxlength="25" autocomplete="off">
                                     </div>
                                 </div>
                             </div>
                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         开户银行：</label>
                                     <div class="col-sm-7">
                                         <input name="bank"  id="bank" class="form-control"
                                                type="text"
                                                maxlength="125" autocomplete="off">

                                         &lt;!&ndash;<select name="bankId" id="bankId" class="form-control valid"
                                                 onchange="setBank();"  th:with="type=${@dict.getType('banks')}">
                                             <option value=""></option>
                                             <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                     th:value="${dict.dictValue}"></option>
                                         </select>
                                         <input name="bank" id="bank" type="hidden">&ndash;&gt;
                                     </div>
                                 </div>
                             </div>

                         </div>
                         <div class="row">
                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         开票类型：</label>
                                     <div class="col-sm-7">
                                         <select name="billingType" id="billingType" class="form-control valid"
                                                 th:with="type=${@dict.getType('billing_type')}">
                                             <option value=""></option>
                                             <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                     th:value="${dict.dictValue}"></option>
                                         </select>
                                     </div>
                                 </div>
                             </div>


                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         开户账号：</label>
                                     <div class="col-sm-7">
                                         <input name="bankAccount"  id="bankAccount" class="form-control"
                                                type="text"
                                                maxlength="25" autocomplete="off">
                                     </div>
                                 </div>
                             </div>
                             <div class="col-sm-5">
                                 <div class="form-group">
                                     <label class="col-sm-3">
                                         地址及电话：</label>
                                     <div class="col-sm-9">
                                         <input name="registerAddr" id="registerAddr" class="form-control" type="text"
                                                maxlength="100" autocomplete="off">
                                     </div>
                                 </div>
                             </div>
                             &lt;!&ndash;<div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         结算方式：</label>
                                     <div class="col-sm-7">
                                         <select name="balaType" id="balaType" class="form-control valid"
                                                 th:with="type=${@dict.getType('bala_type')}">
                                             <option value=""></option>
                                             <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                     th:value="${dict.dictValue}"></option>
                                         </select>
                                     </div>
                                 </div>
                             </div>&ndash;&gt;
                         </div>
                         <div class="row">


                            &lt;!&ndash; <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         注册资金：</label>
                                     <div class="col-sm-7">
                                         <input name="registerCapital" id="registerCapital" class="form-control"
                                                type="text" min="0" oninput="$.numberUtil.onlyNumber(this)"
                                                maxlength="10" autocomplete="off">
                                     </div>
                                 </div>
                             </div>&ndash;&gt;

                             &lt;!&ndash;<div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         法人身份证：</label>
                                     <div class="col-sm-7">
                                         <input name="legalCard" id="legalCard" class="form-control" type="text"
                                                maxlength="25" autocomplete="off" th:value="${cardId}">
                                     </div>
                                 </div>
                             </div>&ndash;&gt;
                         </div>
                         <div class="row">
                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         收件人：</label>
                                     <div class="col-sm-7">
                                         <input name="addressee" id="addressee" class="form-control" type="text"
                                                maxlength="25" autocomplete="off">
                                     </div>
                                 </div>
                             </div>
                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         收件人联系方式：</label>
                                     <div class="col-sm-7">
                                         <input name="addresseeContact" id="addresseeContact" class="form-control"
                                                type="text"
                                                maxlength="25" autocomplete="off">
                                     </div>
                                 </div>
                             </div>
                             <div class="col-sm-5">
                                 <div class="form-group">
                                     <label class="col-sm-3">
                                         邮寄地址：</label>
                                     <div class="col-sm-9">
                                         <input name="addresseeAddr" id="addresseeAddr" class="form-control" type="text"
                                                maxlength="100" autocomplete="off">
                                     </div>
                                 </div>
                             </div>

                         </div>
                         &lt;!&ndash;会计属性 end&ndash;&gt;
                     </div>
                 </div>
             </div>
         </div>-->
            <!--开票信息 start-->
            <div class="panel panel-default">
                <!--                <div class="panel-heading">-->
                <!--                    <h4 class="panel-title">-->
                <!--                        <a data-toggle="collapse" data-parent="#accordion"-->
                <!--                           href="tabs_panels.html#collapseThree">开票信息</a>-->
                <!--                    </h4>-->
                <!--                </div>-->
                <div class="panel-collapse collapse in" id="collapseThree">

                    <div class="panel-body">
                        <div class="bg_title">开票信息</div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>合同账期：</label>
                                    <div class="flex_right">
                                        <input name="collectionDays" id="collectionDays" class="form-control"
                                               type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off"
                                               onkeyup="this.value=this.value.replace(/\D/g,'')" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">特殊日期：</label>
                                    <div class="flex_right">
                                        <div class="over">
                                            <div class="fl" style="width: 80%">
                                                <input name="specialDate" id="specialDate" class="form-control"
                                                       type="number" min="1" max="31" maxlength="5" autocomplete="off"
                                                       onkeyup="this.value=this.value.replace(/\D/g,'')">
                                            </div>
                                            <div class="fl ml10">
                                                <a href="#" data-toggle="tooltip" data-placement="top" title="涉及对账收款月份非自然月的时候需要填写，例如某客户25号（包含）之后单据算到下月对账，则特殊日期填25">
                                                    <i class="fa fa-question-circle" style="color: #ff9113;font-size: 18px"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">对账期：</label>
                                    <div class="flex_right">
                                        <input name="paymentDays" id="paymentDays" class="form-control"
                                               type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off"
                                               th:value="${accountPeriod}"
                                               onkeyup="this.value=this.value.replace(/\D/g,'')" required disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">申请开票期：</label>
                                    <div class="flex_right">
                                        <input name="invoiceDays" id="invoiceDays" class="form-control"
                                               type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off"
                                               th:value="${billingPeriod}"
                                               onkeyup="this.value=this.value.replace(/\D/g,'')" required disabled>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabThree" class="custom-tab table" >

                                <thead>
                                <tr>
                                    <th style="width: 3%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRowThree()" title="新增行">+</a></th>
                                    <th style="width: 15%;">开票公司</th>
                                    <th style="width: 15%;">发票抬头</th>
                                    <th style="width: 12%;">纳税识别号</th>
                                    <th style="width: 12%;">开户银行</th>
                                    <th style="width: 8%;">开票类型</th>
                                    <th style="width: 15%;">开户账号</th>
                                    <th style="width: 20%;">地址及电话</th>
                                </tr>

                                </thead>
                                <tbody>

                    <!--            <tr>
                                    <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRowThree(this,0)" title="删除选择行"></a></td>
                                    <td>
                                        <select id="billingCorp0" name="custBillingList[0].billingCorp" class="form-control valid"
                                                th:with="type=${@dict.getType('bala_corp')}" readonly>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </td>
                                    <td>
                                        <input type="text" id="billingPayable0" name="custBillingList[0].billingPayable" maxlength="25"  class="form-control">
                                    </td>
                                    <td>
                                        <input type="text" id="taxIdentify0" name="custBillingList[0].taxIdentify" maxlength="25" class="form-control">
                                    </td>
                                    <td>
                                        <input type="text" id="bank0" name="custBillingList[0].bank" maxlength="125" class="form-control">
                                    </td>
                                    <td>
                                        <select id="billingType0" name="custBillingList[0].billingType" class="form-control valid"
                                                th:with="type=${@dict.getType('billing_type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </td>
                                    <td>
                                        <input type="text" id="bankAccount0" name="custBillingList[0].bankAccount" maxlength="25" class="form-control">
                                    </td>
                                    <td>
                                        <input type="text" id="addressPhone0" name="custBillingList[0].addressPhone" maxlength="125" class="form-control">
                                    </td>
                                </tr>-->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <!--                <div class="panel-heading">-->
                <!--                    <h4 class="panel-title">-->
                <!--                        <a data-toggle="collapse" data-parent="#accordion"-->
                <!--                           href="tabs_panels.html#collapseFour">结算客户</a>-->
                <!--                    </h4>-->
                <!--                </div>-->
                <div class="panel-collapse collapse in" id="collapseFour">

                    <div class="panel-body">
                        <div class="bg_title">结算客户</div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        结算客户：</font></label>
                                    <div class="flex_right">
                                        <div class="input-group">
                                            <input name="relatedClientName_0" id="relatedClientName_0" type="text"
                                                   placeholder="请选择结算客户" class="form-control valid" autocomplete="off"
                                                   onclick="selectRelatedClient(0)" readonly>
                                            <input id='relatedCustId_0' name='custBalaList[0].relatedCustId' type="hidden">
                                            <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        是否默认：</font></label>
                                    <div class="flex_right">
                                        <select name="custBalaList[0].isDefault" id="isDefault_0" class="form-control valid"
                                                aria-invalid="false">
                                            <option value="0">否</option>
                                            <option value="1">是</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        是否失效：</font></label>
                                    <div class="flex_right">
                                        <select name="custBalaList[0].lockedFlag" id="lockedFlag_0" class="form-control valid"
                                                aria-invalid="false">
                                            <option value="0">否</option>
                                            <option value="1">是</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
           <!-- <div class="panel panel-default">
                &lt;!&ndash;                <div class="panel-heading">&ndash;&gt;
                &lt;!&ndash;                    <h4 class="panel-title">&ndash;&gt;
                &lt;!&ndash;                        <a data-toggle="collapse" data-parent="#accordion"&ndash;&gt;
                &lt;!&ndash;                           href="tabs_panels.html#collapseSeven">线路</a>&ndash;&gt;
                &lt;!&ndash;                    </h4>&ndash;&gt;
                &lt;!&ndash;                </div>&ndash;&gt;
                <div class="panel-collapse collapse in" id="collapseSeven">

                    <div class="panel-body">
                        <div class="bg_title">线路</div>
                        &lt;!&ndash; begin&ndash;&gt;
                        <div class="fixed-table-body mt10" style="margin: 0px -5px;">
                            <table border="0" id="infoTabSeven" class="custom-tab table">

                                <thead>
                                <tr>
                                    <th style="width: 3%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRowSeven()" title="新增行">+</a></th>
                                    <th style="width: 20%;">线路名称</th>
                                    <th style="width: 15%;">回单预警时间</th>
                                    <th style="width: 3%;"></th>
                                    <th style="width: 8%;"></th>
                                    <th style="width: 8%;"></th>
                                    <th style="width: 18%;"></th>
                                    <th style="width: 18%;"></th>

                                </tr>

                                </thead>
                                <tbody>

                                <tr>
                                    <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRowSeven(this,0)" title="删除选择行"></a></td>
                                    <td>
                                        <div class="input-group">
                                            <input id="lineName_0" name="lineName_0"  placeholder="请选择线路" onclick="selectLine(0)"
                                                   class="form-control" type="text" readonly>
                                            <input name="custTransLineList[0].transLineId" id="transLineId_0" type="hidden">
                                            <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                        </div>
                                    </td>
                                    <td>
                                        <select name="custTransLineList[0].receiptIntervalDay" id="receiptIntervalDay_0" class="form-control valid"
                                                th:with="type=${@dict.getType('receipt_interval_day')}">
                                            <option value="" selected="selected">&#45;&#45; 请选择 &#45;&#45;</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </td>

                                </tr>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>-->
            <div class="panel panel-default">
                <div class="panel-collapse collapse in">

                    <div class="panel-body">
                        <div class="bg_title">客服</div>
                        <!-- begin-->
                        <div class=" mt10" style="margin: 0px -5px;">
                            <table border="0" class="custom-tab table">
                                <thead>
                                    <tr>
                                        <th style="width: 5%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRowKF()" title="新增行">+</a></th>
                                        <th style="width: 25%;">用户</th>
                                        <th style="width: 70%;">客服类型</th>

                                    </tr>
                                </thead>
                                <tbody  id="infoTabKF">
                                    <tr>
                                        <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRowKF(this,0)" title="删除选择行"></a></td>
                                        <td>
                                            <div class="input-group">
                                                <input class="form-control" type="text" id="userName_0" name="userName_0">
                                                <input name="customerServiceVOList[0].serviceId" id="serviceId_0" class="form-control" type="hidden" aria-required="true">
                                                <div class="input-group-btn">
                                                    <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                        <span class="caret"></span>
                                                    </button>
                                                    <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div id="serviceType_0"></div>
                                            <input name="customerServiceVOList[0].serviceType"
                                                   id="serviceTypeValue_0"
                                                   type="hidden">
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <!--                <div class="panel-heading">-->
                <!--                    <h4 class="panel-title">-->
                <!--                        <a data-toggle="collapse" data-parent="#accordion"-->
                <!--                           href="tabs_panels.html#collapsePic">证件上传</a>-->
                <!--                    </h4>-->
                <!--                </div>-->
                <div id="collapsePic" class="panel-collapse collapse in">
                    <div class="panel-body" id="picType" style="padding: 5px 10px 10px;">
                        <div class="bg_title">证件上传</div>
                        <div class="row mt10" >
                            <div class="col-md-3 col-sm-4" th:each="dict : ${picList}" >
                                <div class="">
                                    <label class="" th:text="${dict.context}+'：'">
                                    </label>
                                </div>
                                <div class="">
                                    <div class="">
                                        <input th:id="'image'+${dict.value}" class="form-control"
                                               th:name="'image'+${dict.value}" type="file" >
                                        <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}"
                                               type="hidden">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div id="collapsePic1" class="panel-collapse collapse in">
                    <div class="panel-body" style="padding: 5px 10px 10px;">
                        <div class="bg_title">注意事项上传</div>
                        <div class="row mt10" >
                            <div class="col-md-12 col-sm-12">
                                <div class="">
                                    <input id="noticeFile" name="noticeFile" class="form-control" type="file" >
                                    <input id="noticeFileId" name="noticeFileId" type="hidden">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger"  onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: datetimepicker-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: xm-select"/>

<script th:inline="javascript">
    var picType = [[${picList}]];

    $(function () {

        /**
         * 结算公司决定开票公司
         */
        $('#operateCorp').change(function(){
            var balaCorpVal = $(this).val();
            var balaCorpText = $(this).find("option:selected").text();
            $('#billingCorp').val(balaCorpText);
            $('#billingCorpVal').val(balaCorpVal);
        });

        var userType = $("#userType").val();
        if (userType != '5') {
            // 表单验证
            $("#form-client-add").validate({
                onkeyup: false,
                rules: {
                    phone: {
                        isPhone: true,
                        remote: {
                            url: ctx + "system/user/checkPhoneUnique",
                            type: "post",
                            dataType: "json",
                            data: {
                                "phonenumber": function () {
                                    return $.common.trim($("#phone").val());
                                },
                                "userType": "1"
                            },
                            dataFilter: function (data, type) {
                                return $.validate.unique(data);
                            }
                        }
                    },
                    custName: {
                        remote: {
                            url: prefix + "/checkCustNameUnique",
                            type: "post",
                            dataType: "json",
                            data: {
                                "custName": function () {
                                    return $.common.trim($("#custName").val());
                                },
                            },
                            dataFilter: function (data, type) {
                                return $.validate.unique(data);
                            }
                        }
                    },
                    custAbbr: {
                        remote: {
                            url: prefix + "/checkCustAbbrUnique",
                            type: "post",
                            dataType: "json",
                            data: {
                                "custAbbr": function () {
                                    return $.common.trim($("#custAbbr").val());
                                },
                            },
                            dataFilter: function (data, type) {
                                return $.validate.unique(data);
                            }
                        }
                    },
                    addresseeContact: {
                        isPhone: true
                    },
                    psncontact:{
                        isPhone: true
                    },
                    legalCard:{
                        isIdentity:true
                    },appDeliMobile:{
                        isPhone:true
                    }
                },
                messages: {
                    "phone":{
                        remote: "手机号码已存在"
                    },
                    "custName":{
                        remote: "客户名称已存在"
                    },
                    "custAbbr":{
                        remote: "客户简称已存在"
                    }
                }
            });
        } else {
            $("#phone").attr("disabled",true);
            // 表单验证
            $("#form-client-add").validate({
                onkeyup: false,
                rules: {
                    phone: {
                        isPhone: true
                    },
                    addresseeContact: {
                        isPhone: true
                    },
                    psncontact:{
                        isPhone: true
                    },appDeliMobile:{
                        isPhone:true
                    }
                }
            });
        }



        var provinceId = [[${provinceId}]];
        var cityId = [[${cityId}]];
        var areaId = [[${areaId}]];
        // 初始化省市区
        $.provinces.init("province","city","area",provinceId,cityId,areaId);
        $('#province').change(function(){
            $("#provinceName").val($(this).find(":selected").text());
        });
        $('#city').change(function(){
            $("#cityName").val($(this).find(":selected").text());
        });
        $('#area').change(function(){
            $("#areaName").val($(this).find(":selected").text());
        });

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');
        $('#collapseSeven').collapse('show');

        var options = {};
        $.table.init(options);


        //获取TID数字
        var custPicList = [[${custPicList}]];
        for(var i=0 ; i<custPicList.length ; i++){
            var picType = custPicList[i]["picType"];
            $("#tid"+picType).val(custPicList[i]["appendixId"]);
        }
        //循环图片路径信息，初始化图片上传区域
        var imagePath = [[${dictMap}]];
        for (var key in imagePath) {
            var publishFlag = "done" + key;
            var param = {
                maxFileCount: 0,
                publish: "publishFlag",  //用于绑定下一步方法
                fileType: null //文件类型

            };
            var tid = "tid" + key;
            var imageId = "image" + key;
            $.file.loadEditFiles(imageId, tid, imagePath[key], param);
        }


        initBsSuggest(0)

        initXmSelect('serviceType_0', [[${@dict.getType('trans_code')}]]);


        //图片功能
        var noticeFileParam = {
            maxFileCount: 0,
            publish: "noticeFileCmt",
            fileType: "file"
        };
        $.file.initAddFiles("noticeFile", "noticeFileId", noticeFileParam);


        initAppDeliContactBsSuggest()
        initReferrerBsSuggest()
        initGroupBsSuggest()
    });

    // 回单预警时间
    var receiptDayHTML = [[${receiptDay}]];
    var rdHTML = '';
    for (var i = 0; i < receiptDayHTML.length; i++) {

        rdHTML += '<option  value=' + receiptDayHTML[i].dictValue + ' >' + receiptDayHTML[i].dictLabel + '</option>'
    }

    // 车长
    var carLenHTML = [[${carLen}]];
    var clHTML = '';
    for (var i = 0; i < carLenHTML.length; i++) {
        clHTML += '<option  value=' + carLenHTML[i].dictValue + ' >' + carLenHTML[i].dictLabel + '</option>'
    }
    // 车型
    var carTypeHTML = [[${carType}]];
    var ctHTML = '';
    for (var i = 0; i < carTypeHTML.length; i++) {
        ctHTML += '<option  value=' + carTypeHTML[i].dictValue + ' >' + carTypeHTML[i].dictLabel + '</option>'
    }



    var prefix = ctx + "client";


    function initAppDeliContactBsSuggest() {
        $(`#appDeliContact`).bsSuggest('init', {
            url: ctx + "system/user/findUserInfo?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "userName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["userName", "deptName", "phonenumber"],
            effectiveFieldsAlias: {"userName": "用户名", "deptName": "部门", "phonenumber": "手机号码"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#appDeliContact`).val(data.userName);
            $(`#appDeliMobile`).val(data.phonenumber);
        })
    }

    function initGroupBsSuggest() {
        $(`#groupName`).bsSuggest('init', {
            url: ctx + "group/findGroup?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "groupName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["groupName"],
            effectiveFieldsAlias: {"groupName": "集团名称"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#groupName`).val(data.groupName);
            $(`#groupId`).val(data.groupId);
        })

        $('#groupName').on('keyup', function() {
            $('#groupId').val('');
        });
        $('#groupName').on('blur', function() {
            if ($('#groupId').val() === '') {
                $('#groupName').val('');
            }
        });

    }

    function initReferrerBsSuggest() {
        $(`#referrer`).bsSuggest('init', {
            url: ctx + "system/user/findUserInfo?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "userName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["userName", "deptName", "phonenumber"],
            effectiveFieldsAlias: {"userName": "用户名", "deptName": "部门", "phonenumber": "手机号码"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#referrer`).val(data.userName);
            // $(`#appDeliMobile`).val(data.phonenumber);
        })
    }

    /*选择业务员*/
    function selectUser(){
        $.modal.open("选择业务员", ctx + "system/user/selectUser/salesUserList",'','',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $("#psndocName").val("");
                $("#psndoc").val("");
                $("#psncontact").val("");
                layer.close(index);
                return;
            }

            //业务员
            $("#psndocName").val(rows[0]["userName"]);
            $("#psndoc").val(rows[0]["userId"]);
            $("#psncontact").val(rows[0]["phonenumber"]);
            $("#form-client-add").validate().element($("#psndocName"));
            layer.close(index);
        });
    }

    //上传完成标志位
    var flag;
    /*提交表单*/
    function submitHandler() {

        if ($.validate.form()) {
            let handlingChargesType = $("#handlingChargesType").val();
            let handlingCharges = $("#handlingCharges").val();
            if (handlingChargesType != '' && handlingCharges == '') {
                $.modal.alertWarning("装卸单价不能为空。");
                return;
            }

            var balaCorpVal = $("#operateCorp").val();//结算公司
            for(var i=0;i<=billingIndex;i++){
                let val = $("#billingCorp"+i).val();
                if (val != '' && val !=null && balaCorpVal != val) {
                    $.modal.alertWarning("请确保开票公司与结算公司一致。");
                    return false;
                }
            }

            // var isValid = true;
            // $('[id^="serviceId_"]').each(function() {
            //     var index = $(this).attr('id').split('_')[1];
            //     var serviceId = $(this).val();
            //     var serviceType = $('#serviceType_' + index).val();
            //     if ((serviceId && !serviceType) || (!serviceId && serviceType)) {
            //         isValid = false;
            //         return false; // break the loop
            //     }
            // });
            // if (!isValid) {
            //     $.modal.alertWarning("客服信息请填写完整。");
            //     return false;
            // }

            var isValidKP = true;
            $('[id^="billingCorp"]').each(function() {
                var index = $(this).attr('id').split('billingCorp')[1];

                var billingCorp = $('#billingCorp' + index).val();
                var billingPayable = $('#billingPayable' + index).val();
                var taxIdentify = $('#taxIdentify' + index).val();
                var bank = $('#bank' + index).val();
                var billingType = $('#billingType' + index).val();
                var bankAccount = $('#bankAccount' + index).val();
                var addressPhone = $('#addressPhone' + index).val();

                if (billingCorp !== '' || billingPayable !== '' || taxIdentify !== '' || bank !== '' || billingType !== ''
                    || bankAccount !== '' || addressPhone !== '') {

                    if (billingCorp === '' || billingPayable === '' || taxIdentify === '' || bank === '' || billingType === ''
                        || bankAccount === '' || addressPhone === '') {
                        isValidKP = false;
                        return false; // break the loop
                    }
                }
            });
            if (!isValidKP) {
                $.modal.alertWarning("开票信息请填写完整。");
                return false;
            }


            //提交表单flag置空
            flag = "";
            if (picType.length == 1) {
                var value = picType[i].value;
                //如果还没有上传图片，flag就是空的，直接上传第一个
                if ($("#image" + value).val() != "") {
                    $("#image" + value).fileinput('upload');
                    flag = "done" + value;
                    jQuery.subscribe(flag, uploadNoticeFile);
                } else {
                    uploadNoticeFile();
                }
            } else {
                //循环字典表图片类型
                for (var i = 0; i < picType.length; i++) {
                    var value = picType[i].value;
                    //如果还没有上传图片，flag就是空的，直接上传第一个
                    if (flag == "" && i >= 0 && i < picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                        } else {
                            continue;
                        }
                    }
                    //所有都为空，直接提交表单;如果只上传最后一个，前面都没有上传，直接上传并提交表单，设置延时等待上传完成，不然来不及回调
                    if (flag == "" && i == picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                            jQuery.subscribe(flag, uploadNoticeFile);
                        } else {
                            uploadNoticeFile();
                        }
                    }
                    //如果前面有上传，且input框不空，执行上传
                    if (flag != "" && i > 0 && i < picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            jQuery.subscribe(flag, uploadPic(value));
                        } else {
                            continue;
                        }
                    }
                    //判断最后一个是否为空，为空直接提交表单，不为空上传完提交表单
                    if (flag != "" && i == picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                            jQuery.subscribe(flag, uploadNoticeFile);
                        } else {
                            jQuery.subscribe(flag, uploadNoticeFile);
                        }
                    }
                }
            }
        }
    }

    function uploadNoticeFile() {
        $("#noticeFile").fileinput('upload');
        jQuery.subscribe("noticeFileCmt", setTimeout("commit()", "1000"));

    }

    function uploadPic(dictValue) {
        $("#image" + dictValue).fileinput('upload');
        flag = "done" + dictValue;
    }

    /**
     * 保存客户的方法
     */
    function commit() {
        $(":disabled").attr("disabled", false);
        var count = 0;
        for (var i = 0;i <= relatedIndex;i++){
            if ($("#isDefault_" + i).val() === '1') {
                count ++;
            }
            if (count > 1) {
                $.modal.alertWarning("只能默认一个结算客户");
                return false;
            }
        }



        var data = $('#form-client-add').serializeArray()
        // var isEnabled = $("input[id='isEnabled']").is(':checked') == true ? 0 : 1;
        // data.push({"name": "isEnabled", "value": isEnabled});
        $.ajax({
            type: "post",
            dataType: "json",
            data:  {phonenumber:$("#phone").val(),userType:"1"},
            url:  ctx + "system/user/checkPhoneUnique",
            success: function(result) {
                if (result == "1") {
                    var title = $("#phone").val()+" 联系电话重复！";
                    $.modal.alertWarning(title);
                    return false;
                }else {
                    // 表单提交
                    $.operate.saveTab(prefix + "/addClient", data);
                }
            }
        });

    }

    /**
     * 结算客户的选择框
     */
    function selectRelatedClient(relatedIndex) {
        let sourceType = $("#sourceType").val();
        $.modal.open("选择结算客户", prefix + `/related?sourceType=${sourceType}&relatedIndex=` + relatedIndex);
    }



    /**
     * 货品选择框
     */
    function selectGoods() {

        $.modal.open("选择货品", prefix + "/getGoods");
    }

    /**
     * 线路选择框
     */
    function selectLine(lineIndex) {

        $.modal.open("选择线路", prefix + "/line?lineIndex=" + lineIndex);
    }

    /**
     * 初始化日期控件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#billingDate',
            type: 'datetime',
            trigger: 'click',
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours=now.getHours();
                this.dateTime.minutes=now.getMinutes();
                this.dateTime.seconds=now.getSeconds();
            }
        });
    });

    /**
     * 设置开户行名称
     */
    function setBank() {
        $("#bank").val($("#bankId option:selected").text());
    }




    var relatedIndex = 0;
    /* 新增表格行 */
    function insertRowTwo() {
        relatedIndex += 1;
        var trTtml = '<tr>'
            + '<td> <a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRowTwo(this,'+relatedIndex+')" title="删除选择行"></a></td>'
            + ' <td> <div class="input-group">' +
            '   <input name='+relatedIndex+' id="relatedClientName_'+relatedIndex+'" placeholder="请选择结算客户" type="text"' +
            '     class="form-control valid" autocomplete="off"' +
            '     onclick="selectRelatedClient(this.name)" readonly>' +
            '    <input name="custBalaList['+ relatedIndex +'].relatedCustId" id="relatedCustId_'+relatedIndex+'"  type="hidden">' +
            '    <span class="input-group-addon"><i class="fa fa-search"></i></span>' +
            '   </div> </td>'
            + ' <td>'
            + '               <select name="custBalaList['+ relatedIndex +'].isDefault" id="isDefault_'+relatedIndex+'"  class="form-control" aria-invalid="false">'
            + '                    <option value="0">否</option>'
            + '                   <option value="1">是</option>'
            + '               </select>'
            + '            </td>'
            + '             <td> <select name="custBalaList['+ relatedIndex +'].lockedFlag" id="lockedFlag_'+relatedIndex+'"  class="form-control" aria-invalid="false">'
            + '                <option value="0">否</option>'
            + '                 <option value="1">是</option>'
            + '              </select></td>'
            + '   </tr>';
        $("#infoTabTwo tbody").append(trTtml);
    }

    /* 删除指定表格行 */
    function removeRowTwo(obj,index) {
        if ($("#infoTabTwo tbody").find('tr').length > 1) {
            $("#infoTabTwo tbody").find(obj).closest("tr").remove();
        } else {
            $("#relatedClientName_" + index).val("");
            $("#relatedCustId_" + index).val("");
        }
    }



    /* 删除指定表格行 */
    function removeRowFour(obj) {
        $("#infoTabFour tbody").find(obj).closest("tr").remove();
    }

    var lineIndex = 0;

    /* 新增表格行 */
    function insertRowSeven() {
        lineIndex += 1;
        var lineName = 'lineName_' + lineIndex;
        var trTtml = ' <tr>\n' +
            '<td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"  onclick="removeRowSeven(this,'+lineIndex+')" title="删除选择行"></a></td>' +
            '     <td>\n' +
            '              <div class="input-group">\n' +
            '              <input name=' + lineIndex + ' id=' + lineName + ' placeholder="请选择线路"  onclick="selectLine(this.name)" class="form-control"  type="text"  readonly >\n' +
            '              <input name="custTransLineList['+ lineIndex +'].transLineId" id="transLineId_'+ lineIndex +'" type="hidden">' +
            '              <span class="input-group-addon"><i class="fa fa-search"></i></span>' +
            '              </div>' +
            '             </td>' +
            '            <td>' +
            '             <select  name="custTransLineList['+ lineIndex +'].receiptIntervalDay" id="receiptIntervalDay_'+ lineIndex +'" class="form-control valid" >' +
            '             <option value="" selected="selected">-- 请选择 --</option>'+
            '               ' + rdHTML + ' ' +
            '             </select>' +
            '             </td>' +
            '</tr>'

        $("#infoTabSeven tbody").append(trTtml);
    }

    /* 删除指定表格行 */
    function removeRowSeven(obj,index) {
        if ($("#infoTabSeven tbody").find('tr').length > 1) {
            $("#infoTabSeven tbody").find(obj).closest("tr").remove();
        } else {
            $("#lineName_"+index).val("");
            $("#transLineId_"+index).val("");
        }
    }

    var kfLineIndex = 0;
    let transCodeList = [[${@dict.getType('trans_code')}]]
    // var transCodeListHtml = '';
    // for ( var i = 0; i < transCodeList.length; i++) {
    //     transCodeListHtml += '<option  value='+transCodeList[i].dictValue+' >'+transCodeList[i].dictLabel+'</option>'
    // }

    /* 新增表格行 */
    function insertRowKF() {
        kfLineIndex += 1;

        let ind = kfLineIndex
        var trTtml = `
                <tr>
                    <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRowKF(this,${kfLineIndex})" title="删除选择行"></a></td>
                    <td>
                        <div class="input-group">
                            <input class="form-control" type="text" id="userName_${kfLineIndex}" name="userName_${kfLineIndex}">
                            <input name="customerServiceVOList[${kfLineIndex}].serviceId" id="serviceId_${kfLineIndex}" class="form-control" type="hidden" aria-required="true">
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                </ul>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div id="serviceType_${kfLineIndex}"></div>
                        <input name="customerServiceVOList[${kfLineIndex}].serviceType" id="serviceTypeValue_${kfLineIndex}" type="hidden">
                    </td>

                </tr>
        `

        $("#infoTabKF").append(trTtml);

        initXmSelect(`serviceType_${kfLineIndex}`, [[${@dict.getType('trans_code')}]]);

        initBsSuggest(ind)
    }

    /* 删除指定表格行 */
    function removeRowKF(obj,index) {
        var $tbody = $('#infoTabKF');
        var $rows = $tbody.children('tr'); // Only select direct child tr elements
        if ($rows.length === 1) {
            // Clear the data in the input fields and select element
            var $inputs = $rows.find('input');
            var $selects = $rows.find('select');
            $inputs.val('');
            $selects.prop('selectedIndex', 0);
        }else {
            $(obj).closest('tr').remove();

        }

        //
        // if ($("#infoTabKF").find('tr').length > 1) {
        //     $("#infoTabKF").find(obj).closest("tr").remove();
        // } else {
        //     $("#lineName_"+index).val("");
        //     $("#transLineId_"+index).val("");
        // }
    }
    function initBsSuggest(ind) {
        $(`#userName_${ind}`).bsSuggest('init', {
            url: ctx + "system/user/findUserInfo?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "userName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["userName","deptName","phonenumber"],
            effectiveFieldsAlias: {"userName":"用户名","deptName":"部门","phonenumber":"手机号码"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#userName_${ind}`).val(data.userName);
            $(`#serviceId_${ind}`).val(data.userId);
        })

    }

    function initXmSelect(id, dataList) {
        var demo4 = xmSelect.render({
            el: '#' + id,
            language: 'zn',
            size: 'mini',
            model: {
                label: {
                    type: 'block',
                    block: {
                        //最大显示数量, 0:不限制
                        showCount: 5,
                        //是否显示删除图标
                        showIcon: true,
                    }
                }
            },
            toolbar: {
                show: true,
            },
            autoRow: true,
            prop: {
                name: 'dictLabel',
                value: 'dictValue',
            },
            on: function (data) {
                //arr:  当前多选已选中的数据
                var arr = data.arr;
                let val = arr.map(obj => obj.dictValue).join(",");

                let reverse = id.split('_').reverse();
                $("#serviceTypeValue_" + reverse[0]).val(val);
            },
            data: dataList
        })
    }


    var billingIndex = 0;
    /**
     * 动态新增开票信息
     */
    var billingType = [[${@dict.getType('billing_type')}]];//开票类型
    var billingTypeHTML = '';
    for (var i = 0; i < billingType.length; i++) {
        billingTypeHTML += '<option  value=' + billingType[i].dictValue + ' >' + billingType[i].dictLabel + '</option>'
    }
    // 开票公司
    var billingType = [[${@dict.getType('bala_corp')}]];
    var biHTML = '';
    for (var i = 0; i < billingType.length; i++) {
        biHTML += '<option  value=' + billingType[i].dictValue + ' >' + billingType[i].dictLabel + '</option>'
    }
    function insertRowThree() {
        billingIndex += 1;
        var trTtml = ' <tr>\n' +
            '<td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"  onclick="removeRowThree(this,'+billingIndex+')" title="删除选择行"></a></td>' +
            '<td>'+
            '<select name="custBillingList['+billingIndex+'].billingCorp" id="billingCorp'+ billingIndex +'" class="form-control valid" readonly>'+
            '<option value=""></option>'+
            ''+biHTML+''+
            '</select>'+
            '</td>'+
            '<td><input type="text" id="billingPayable'+billingIndex+'" name="custBillingList['+billingIndex+'].billingPayable" maxlength="25"  class="form-control"></td>'+
            '<td><input type="text" id="taxIdentify'+billingIndex+'" name="custBillingList['+billingIndex+'].taxIdentify" maxlength="25" class="form-control"></td>'+
            '<td><input type="text" id="bank'+billingIndex+'" name="custBillingList['+billingIndex+'].bank" maxlength="125" class="form-control"></td>'+
            '<td>'+
            '<select name="custBillingList['+billingIndex+'].billingType" id="billingType'+billingIndex+'" class="form-control valid" >'+
            '<option value=""></option>'+
            ''+billingTypeHTML+''+
            '</select>'+
            '</td>'+
            '<td><input type="text" id="bankAccount'+billingIndex+'" name="custBillingList['+billingIndex+'].bankAccount" maxlength="25" class="form-control"></td>'+
            '<td><input type="text" id="addressPhone'+billingIndex+'" name="custBillingList['+billingIndex+'].addressPhone" maxlength="125" class="form-control"></td>'+
            // '<td><input type="text" id="addressee'+billingIndex+'" name="custBillingList['+billingIndex+'].addressee" maxlength="25" class="form-control"></td>'+
            // '<td><input type="text" id="addresseeContact'+billingIndex+'" name="custBillingList['+billingIndex+'].addresseeContact" maxlength="25" class="form-control"></td>'+
            // '<td><input type="text" id="addresseeAddr'+billingIndex+'" name="custBillingList['+billingIndex+'].addresseeAddr" maxlength="125" class="form-control"></td>'+
            '</tr>'

        $("#infoTabThree tbody").append(trTtml);
        changeBalaCorp();

    }
    function removeRowThree(obj,index) {
        if ($("#infoTabThree tbody").find('tr').length > 1) {
            $("#infoTabThree tbody").find(obj).closest("tr").remove();
        } else {
            $("#billingCorp"+index).val("");
            $("#billingCorpVal"+index).val("");
            $("#billingPayable"+index).val("");
            $("#taxIdentify"+index).val("");
            $("#bank"+index).val("");
            $("#billingType"+index).val("");
            $("#bankAccount"+index).val("");
            $("#addressPhone"+index).val("");
            $("#addressee"+index).val("");
            $("#addresseeContact"+index).val("");
            $("#addresseeAddr"+index).val("");
        }
    }

    /**
     * 结算公司决定开票公司
     */
    function changeBalaCorp(){
        var balaCorpVal = $("#operateCorp").val();//结算公司
        for(var i=0;i<=billingIndex;i++){
            $("#billingCorp"+i).val(balaCorpVal);
        }
    }


    function changeSalesId() {
        //2a828327651f4e1cbbc798d8c56ec99c 为车队
        let val = $("#salesId").val();
        if (val === '2a828327651f4e1cbbc798d8c56ec99c') {
            $("#billingType").val("6")
        }else {
            $("#billingType").val("4")
        }

        var selectedTexts = $("#salesId option:selected").map(function() {
            return $(this).text();
        }).get();

        // 使用逗号分隔多个值
        var deptName = selectedTexts.join(",");

        $.ajax({
            url: ctx + "tms/custSalesTarget/get_sales_dept",
            type: "get",
            dataType: "json",
            data: {parentDeptName: deptName},
            success: function (result) {
                if (result.code == 0 && result.data!=undefined) {
                    $("#salesDept").empty();

                    if (result.data != null) {
                        $.each(result.data, function(index, option) {
                            $("#salesDept").append($('<option>', {
                                value: option.deptId,
                                text: option.deptName
                            }));
                        });


                    }
                }
            }
        });
    }


    function selectSalesDept() {
        layer.open({
            type: 2,
            area: ['50%', '80%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "选择运营组",
            content: ctx + "system/dept/opsGroupTree",
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero){
                //获取整行
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                $(`#salesDeptName`).val(rows[0].deptName);
                $(`#salesDept`).val(rows[0].deptId);
                layer.close(index);
            },
            cancel: function(index) {
                return true;
            }
        });
    }
</script>
</body>

</html>