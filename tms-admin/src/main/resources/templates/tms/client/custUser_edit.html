<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户账号信息')"/>
</head>
<body>
<div class="form-content">
    <form id="form-custUser-edit" class="form-horizontal">

        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4">姓名：</label>
                    <div class="col-sm-8">
                        <input name="loginName" id="loginName" placeholder="请输入姓名" class="form-control valid" type="text"
                               required aria-required="true" th:value="${custUser.userName}" maxlength="25">
                        <input name="userId" type="hidden" id="userId" th:value="${custUser.userId}"/>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4">联系手机：</label>
                    <div class="col-sm-8">
                        <input name="phone" id="phone" placeholder="请输入手机号码" class="form-control valid" type="text"
                               required aria-required="true"  th:value="${custUser.phone}" maxlength="11">
                    </div>
                </div>
            </div>

        </div>

    </form>
</div>
<th:block th:include="include :: footer" />
</body>
<script th:inline="javascript">
    var prefix = ctx + "custUser";

    var customerId = [[${custUser.customerId}]];
    var custUserId = [[${custUser.custUserId}]];
    /**
     * 校验
     */
    $("#form-custUser-edit").validate({
        onkeyup: false,
        rules:{
            phone:{
                isPhone:true,
                remote: {
                    url: ctx + "system/user/checkPhoneUnique",
                    type: "post",
                    dataType: "json",
                    data: {
                        "userType": function () {
                            return '1';
                        },
                        "userId": function() {
                            return $("#userId").val();
                        },
                        "phonenumber": function() {
                            return $.common.trim($("#phone").val());
                        }
                    },
                    dataFilter: function (data, type) {
                        return $.validate.unique(data);
                    }
                }
            },
        },
        messages: {
            "phone":{
                remote: "手机号码已经存在"
            }
        },
        focusCleanup: true
    });

    /**
     * 客户的选择框
     */
    function selectClient() {

        parent.$.modal.open("选择客户", ctx + "client/related",'','',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                parent.$.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //客户id
            $("#customerId").val(rows[0]["customerId"]);
            //客户名称
            $("#custName").val(rows[0]["custName"]);

            //选中完需单独校验
            $("#form-custUser-add").validate().element($("#custName"));
            parent.layer.close(index);
        });
    }

    // 提交回调的方法
    function submitHandler() {
        if ($.validate.form()) {
            $.ajax({
                url: prefix + "/editSave",
                data: {"userName":$("#loginName").val(),
                    "phone":$("#phone").val(),
                    "userId":$("#userId").val(),
                    "customerId":customerId,
                "custUserId":custUserId},
                type: "post",
                error: function (request) {
                    $.modal.alertError("系统错误");
                },
                success: function (data) {
                    $.operate.successCallback(data);
                }
            })
        }
    }
</script>
</body>

</html>