<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保证金管理-列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--客户id-->
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">客户简称 ：</label>-->
                            <div class="col-sm-12">
                                <input name="custAbbr" id="custAbbr" placeholder="请输入客户简称"
                                       class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">收付款类型 ：</label>-->
                            <div class="col-sm-12">
                                <select name="marginType" class="form-control" th:with="type=${@dict.getType('margin_type')}">
                                    <option value="">-- 收付款类型 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">状态：</label>
                            <div class="col-sm-8">
                                <select id="marginVbillstatus" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="状态" multiple >
                                    <option th:each="dict : ${marginVbillstatus}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">所属公司：</label>
                            <div class="col-sm-8">
                                <select name="balaCorp" id="balaCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" required>
                                    <option value="">---请选择结算公司---</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>


            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="checkRecord()" shiro:hasPermission="tms:margin:checkRecord">
                <i class="fa fa-calculator"></i> 审核记录(旧)
            </a>
            <a class="btn btn-info single disabled" onclick="process()">
                <i class="fa fa-laptop"></i> 微信审批进度
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
    //字典数据  结算公司
    var datas = [[${@dict.getType('bala_corp')}]];
    var prefix = ctx + "tms/margin";
    //保证金状态
    var marginVbillstatus = [[${marginVbillstatus}]];
    //保证金状态-申请
    var marginVbillstatusApply = [[${marginVbillstatusApply}]];
    //类型
    var marginType = [[${@dict.getType('margin_type')}]];
    var payMethod = [[${@dict.getType('pay_method')}]]

    var selectedMyCount = 0;
    var selectedJhCount = 0;
    var selectedHkCount= 0;
    var selectedMySum = 0;
    var selectedJhSum = 0;
    var selectedHkSum = 0;

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });
        var options = {
            url: prefix + "/list",
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            exportUrl: prefix + "/export",
            fixedNumber: 0,
            showToggle: false,
            showColumns: true,
            modalName: "保证金",
            uniqueId: "marginId",
            height: 560,
            clickToSelect: true,
            showFooter:true,
            onRefresh:function(params){
                //总数清0
                selectedMyCount = 0;
                selectedJhCount = 0;
                selectedHkCount= 0;
                selectedMySum = 0;
                selectedJhSum = 0;
                selectedHkSum = 0;
            },
            onCheck: function (row,$element) {
                var marginAmount = row.marginAmount;
                var balaCorp = row.balaCorp;
                if(balaCorp == 'MY') {
                    selectedMyCount++
                    selectedMySum = selectedMySum + marginAmount
                    $("#selectedMyCount").text(selectedMyCount);
                    $("#selectedMySum").text(selectedMySum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
                else if(balaCorp == 'JH') {
                    selectedJhCount++
                    selectedJhSum = selectedJhSum + marginAmount
                    $("#selectedJhCount").text(selectedJhCount);
                    $("#selectedJhSum").text(selectedJhSum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
                else if(balaCorp == 'HK') {
                    selectedHkCount++
                    selectedHkSum = selectedHkSum + marginAmount
                    $("#selectedHkCount").text(selectedHkCount);
                    $("#selectedHkSum").text(selectedHkSum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            },
            onUncheck: function (row, $element) {
                var marginAmount = row.marginAmount;
                var balaCorp = row.balaCorp;
                if(balaCorp == 'MY') {
                    selectedMyCount--
                    selectedMySum = selectedMySum - marginAmount
                    $("#selectedMyCount").text(selectedMyCount);
                    $("#selectedMySum").text(selectedMySum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
                else if(balaCorp == 'JH') {
                    selectedJhCount--
                    selectedJhSum = selectedJhSum - marginAmount
                    $("#selectedJhCount").text(selectedJhCount);
                    $("#selectedJhSum").text(selectedJhSum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
                else if(balaCorp == 'HK') {
                    selectedHkCount--
                    selectedHkSum = selectedHkSum - marginAmount
                    $("#selectedHkCount").text(selectedHkCount);
                    $("#selectedHkSum").text(selectedHkSum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            },
            onCheckAll: function (rowsAfter) {
                //总数清0
                selectedMyCount = 0;
                selectedJhCount = 0;
                selectedHkCount= 0;
                selectedMySum = 0;
                selectedJhSum = 0;
                selectedHkSum = 0;
                //循环累加
                for (var row of rowsAfter) {
                    var marginAmount = row.marginAmount;
                    var balaCorp = row.balaCorp;
                    if(balaCorp == 'MY') {
                        selectedMyCount++
                        selectedMySum = selectedMySum + marginAmount
                    }
                    else if(balaCorp == 'JH') {
                        selectedJhCount++
                        selectedJhSum = selectedJhSum + marginAmount
                    }
                    else if(balaCorp == 'HK') {
                        selectedHkCount++
                        selectedHkSum = selectedHkSum + marginAmount
                    }
                }
                $("#selectedMyCount").text(selectedMyCount);
                $("#selectedMySum").text(selectedMySum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#selectedJhCount").text(selectedJhCount);
                $("#selectedJhSum").text(selectedJhSum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#selectedHkCount").text(selectedHkCount);
                $("#selectedHkSum").text(selectedHkSum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheckAll: function () {
                //总数清0
                selectedMyCount = 0;
                selectedJhCount = 0;
                selectedHkCount= 0;
                selectedMySum = 0;
                selectedJhSum = 0;
                selectedHkSum = 0;
                //循环累加
                $("#selectedMyCount").text(selectedMyCount);
                $("#selectedMySum").text(selectedMySum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#selectedJhCount").text(selectedJhCount);
                $("#selectedJhSum").text(selectedJhSum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#selectedHkCount").text(selectedHkCount);
                $("#selectedHkSum").text(selectedHkSum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onPostBody:function () {
                //查询合计总金额
                getAmountCount();
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "已选铭源总数量：<nobr id='selectedMyCount'>￥0;</nobr>&nbsp&nbsp"
                        + "已选吉华总数量：<nobr id='selectedJhCount'>￥0;</nobr>&nbsp&nbsp"
                        + "已选皓凯总数量：<nobr id='selectedHkCount'>￥0;</nobr>&nbsp&nbsp"
                        + "已选铭源总金额：<nobr id='selectedMySum'>￥0;</nobr>&nbsp&nbsp"
                        + "已选吉华总金额：<nobr id='selectedJhSum'>￥0;</nobr>&nbsp&nbsp"
                        + "已选皓凯总金额：<nobr id='selectedHkSum'>￥0;</nobr>&nbsp&nbsp<br>"
                        + "铭源总数量：<nobr id='myCount'>￥0;</nobr>&nbsp&nbsp"
                        + "吉华总数量：<nobr id='jhCount'>￥0;</nobr>&nbsp&nbsp"
                        + "皓凯总数量：<nobr id='hkCount'>￥0;</nobr>&nbsp&nbsp"
                        + "铭源总金额：<nobr id='mySum'>￥0;</nobr>&nbsp&nbsp"
                        + "吉华总金额：<nobr id='jhSum'>￥0;</nobr>&nbsp&nbsp"
                        + "皓凯总金额：<nobr id='hkSum'>￥0;</nobr>&nbsp&nbsp"
                    // + "未收金额合计：<nobr id='weishouCount'>￥0;</nobr><br>"
                }
            },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        var title = "付款";
                        if(row.marginType == 1){
                            title = "收款"
                        }
                        if ([[${@permission.hasPermi('tms:margin:finance:detail')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="明细"onclick="detail(\'' + row.marginId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:margin:receive')}]] != "hidden" && row.marginVbillstatus == marginVbillstatusApply) {
                            actions.push('<a class="btn btn-xs" href="javascript:void(0)" title='+title+' onclick="receive(\'' + row.marginId + '\',\'' + row.marginType + '\')"><i class="fa fa-dollar" style="font-size: 15px;"></i></a>');
                        }
                        return actions.join('');
                    },
                },
                {
                    title: '客户简称',
                    field: 'custAbbr',
                    align: 'left',
                },
                {
                    title: '所属公司',
                    field: 'balaCorp',
                    align: 'left',
                    formatter: function(value, item, index) {
                        return $.table.selectDictLabel(datas, item.balaCorp);
                    }
                },
                {
                    title: '状态',
                    field: 'marginVbillstatus',
                    align: 'left',
                    formatter: function status(value,row) {
                        var context = '';
                        marginVbillstatus.forEach(function (v) {
                            if (v.value == row.marginVbillstatus) {
                                context = v.context;
                                return false;
                            }
                        });
                        return context;
                    }
                },
                {
                    title: '类型',
                    field: 'marginType',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(marginType, row.marginType);
                    }
                },
                {
                    title: '保证金金额',
                    field: 'marginAmount',
                    align: 'right',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '申请人',
                    field: 'applyUserName',
                    align: 'left',
                },
                {
                    title: '申请时间',
                    field: 'applyDate',
                    align: 'left',
                },
                {
                    field: 'payMethod',
                    title: '收付款方式',
                    formatter(value,row,index) {
                        return $.table.selectDictLabel(payMethod, value)
                    }
                },
                {
                    title: '账户',
                    field: 'payAccount',
                    align: 'left',
                },
                {
                    title: '支付时间',
                    field: 'payDate',
                    align: 'left',
                },
                {
                    title: '创建人',
                    field: 'regUserName',
                    align: 'left',
                },
                {
                    title: '审批单号',
                    field: 'spNo'
                }
            ]
        };
        $.table.init(options);

        var arr = [];
        arr.push(marginVbillstatus[1].value);//已申请
        arr.push(marginVbillstatus[3].value);//已付款
        arr.push(marginVbillstatus[4].value);//已收款
        $('#marginVbillstatus').selectpicker('val',arr);
        searchPre();
    });

    /**
     * 脚合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        //var data = {};
        data.params = new Map();
        data.params.marginVbillstatus = $.common.join($('#marginVbillstatus').selectpicker('val'));

        //console.log($.common.join($('#marginVbillstatus').selectpicker('val')))
        //console.log(data)

        // var arr = [];
        // arr.push(marginVbillstatus[1].value);//已申请
        // arr.push(marginVbillstatus[3].value);//已付款
        // arr.push(marginVbillstatus[4].value);//已收款
        // $('#marginVbillstatus').selectpicker('val',arr);
        // data.marginVbillstatus = arr
        // console.log(data)
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result;
                for (let i = 0; i < data.length; i++) {
                    if(data[i].balaCorp == 'MY') {
                        $("#myCount").text(data[i].corpCount);
                        $("#mySum").text(data[i].corpSum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    }
                    else if(data[i].balaCorp == 'JH') {
                        $("#jhCount").text(data[i].corpCount);
                        $("#jhSum").text(data[i].corpSum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    }
                    else if(data[i].balaCorp == 'HK') {
                        $("#hkCount").text(data[i].corpCount);
                        $("#hkSum").text(data[i].corpSum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    }
                }
            }
        });
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        data.params = new Map();
        data.params.marginVbillstatus = $.common.join($('#marginVbillstatus').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        var arr = [];
        arr.push(marginVbillstatus[1].value);//已申请
        arr.push(marginVbillstatus[3].value);//已付款
        arr.push(marginVbillstatus[4].value);//已收款
        $('#marginVbillstatus').selectpicker('val',arr);
        searchPre();
    }

    /**
     * 收款-付款
     * @param marginId
     */
    function receive(marginId,marginType) {
        var title = '付款'
        if(marginType == 1){
            title = '收款';
        }
        var type = 'receive';
        $.modal.openTab(title, prefix + "/edit?marginId="+marginId+"&type="+type);
    }

    /**
     * 明细
     * @param marginId
     */
    function detail(marginId) {
        $.modal.openTab("明细", prefix + "/detail?marginId="+marginId);
    }

    /**
     * 审核记录
     */
    function checkRecord() {
        var marginId = $.table.selectColumns("marginId");
        var url = prefix + "/check_record?marginId="+marginId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核记录",
            area: ['800px',   '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    function process() {
        var spNo = $.table.selectColumns("spNo")[0];
        if (!spNo) {
            $.modal.msgWarning("未提交微信审批")
            return
        }
        wecom_process(spNo);
    }
</script>
</body>
</html>