<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保证金-edit')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <style type="text/css">
        .flex {
            display: flex;
            justify-content: space-between;
        }
        .flex_left {
            width: 120px;
            text-align: right;
        }
        .flex_right {
            flex: 1;
        }
        .file-drop-zone {
            height: auto!important;
            margin: 0;
        }
        .theme-explorer .explorer-frame .kv-file-content {
            width: 70px;
            height: 70px;
            padding: 3px;
        }
    </style>
</head>

<body>
<div class="form-content">
    <form id="form-margin-add" class="form-horizontal" novalidate="novalidate">
        <!--保证金id-->
        <input type="hidden" th:value="${margin.marginId}" name="marginId">
        <input type="hidden" name="apply" value="0" />
        <div class="panel-group" id="accordion11" th:if="${bb!=null}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion11"
                           href="tabs_panels.html#collapse1">投标保证金信息</a>
                    </h5>
                </div>
                <div id="collapse1" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row" style="margin-bottom: 5px">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">线索客户：</label>
                                    <div class="flex_right">
                                        <div style="line-height: 20px" th:text="${bb.custName}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">项目名称：</label>
                                    <div class="flex_right">
                                        <div style="line-height: 20px" th:text="${bb.itemName}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">转履约金金额：</label>
                                    <div class="flex_right">
                                        <div th:if="${type == 'receive'}" style="line-height: 20px" th:text="${margin.bbAmount}"></div>
                                        <input th:unless="${type == 'receive'}" class="form-control" name="bbAmount" required th:value="${margin.bbAmount}" th:max="${bbAmount}" min="0.01" oninput="$.numberUtil.onlyNumberTwoDecimal(this);limitMargin(this)">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- TODO 选择客户 -->
        <div class="panel-group" id="accordion0">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion0"
                           href="tabs_panels.html#collapse0">客户选择</a>
                    </h5>
                </div>
                <div id="collapse0" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span
                                            style="color: red; ">选择客户：</span></label>
                                    <div class="col-sm-8">
                                        <input name="custAbbr" id="custAbbr" onclick="selectClient()" type="text" th:disabled="${type == 'receive'}"
                                               placeholder="请选择客户" class="form-control valid"
                                               aria-required="true" required autocomplete="off" readonly th:value="${margin.custAbbr}">
                                        <input name="custName" id="custName" type="hidden" th:value="${margin.custName}">
                                        <input name="custCode" id="custCode" type="hidden">
                                        <input name="customerId" id="customerId" type="hidden" th:value="${margin.customerId}">
                                        <input name="balaCorpId" id="balaCorpId" type="hidden">
                                        <input name="balaDept" id="balaDept" type="hidden">
                                        <input name="salesDept" id="salesDept" type="hidden">
                                        <input name="psndoc" id="psndoc" type="hidden">
                                        <input name="billingCorp" id="billingCorp" type="hidden">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>结算公司：</span></label>
                                    <div class="col-sm-8" id="balaCorpName"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span style="color: red; ">保证金金额：</span></label>
                                    <div class="col-sm-8">
                                        <input th:if="${type == 'receive' && bb != null}"
                                               th:value="${margin.marginAmount - margin.bbAmount} + '+' + ${margin.bbAmount}+'='+${margin.marginAmount}"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);marginAmountChange()" disabled
                                               class="form-control">
                                        <input th:unless="${type == 'receive' && bb != null}" name="marginAmount" id="marginAmount" th:value="${margin.marginAmount}"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);marginAmountChange()" th:disabled="${type == 'receive'}"
                                               class="form-control" required type="text" maxlength="20" th:min="${margin.bbAmount}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收付款类型：</label>
                                    <div class="col-sm-8">
                                        <select name="marginType" class="form-control valid" required th:disabled="${type == 'receive' || bb != null}"
                                                th:with="type2=${@dict.getType('margin_type')}" onchange="marginTypeChange(this)">
                                            <option value=""></option>
                                            <option th:each="dict : ${type2}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"
                                                    th:field="${margin.marginType}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收付款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="payMethod" class="form-control valid" required
                                                th:with="type=${@dict.getType('pay_method')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}"
                                                    th:text="${dict.dictLabel}" th:field="${margin.payMethod}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">类型：</label>
                                    <div class="col-sm-8">
                                        <select name="logisticsType" class="form-control valid" required
                                                th:with="type=${@dict.getType('logistics_type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:field="${margin.logisticsType}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>对应合同号：</span></label>
                                    <div class="col-sm-8">
                                        <input name="contractNo" id="contractNo" th:value="${margin.contractNo}"
                                               class="form-control" type="text" maxlength="50">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">业务期限开始：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="time-input form-control"
                                               th:value="${#dates.format(margin.businessPeriodStart, 'yyyy-MM-dd')}"
                                               id="businessPeriodStart" name="businessPeriodStart"
                                               placeholder="业务期限开始"  readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">业务期限结束：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="time-input form-control" id="businessPeriodEnd"
                                               th:value="${#dates.format(margin.businessPeriodEnd, 'yyyy-MM-dd')}"
                                               name="businessPeriodEnd" placeholder="业务期限结束"  readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${type == 'receive'}">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>审核人：</span></label>
                                    <div class="col-sm-8">
                                        <input  th:value="${margin.checkUserName}" disabled
                                                class="form-control" required type="text" maxlength="20">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>审核时间：</span></label>
                                    <div class="col-sm-8">
                                        <input  th:value="${#dates.format(margin.checkDate, 'yyyy-MM-dd')}" disabled
                                               class="form-control" required type="text" maxlength="20">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6" th:if="${type == 'receive' && bb != null && margin.marginType==0}">
                                <div class="form-group" style="color: #ff9900">
                                    <label class="col-sm-4" style="font-weight: bold">需打款：</label>
                                    <div class="col-sm-8">
                                        <div class="form-control" style="line-height: 21px;font-weight: bold" th:text="${margin.marginAmount - margin.bbAmount}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">账户：</label>
                                    <div class="col-sm-8">
                                        <select name="payAccount" class="form-control" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${account}" th:text="${dict.accountName}" th:value="${dict.accountId}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-6">
                                            <textarea name="memo" maxlength="500" class="form-control valid"
                                                      rows="3" th:text="${margin.memo}"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

            <div class="panel-group" th:styleappend="${margin.marginType==0?'':'display:none'}" id="accordion3">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion3"
                               href="tabs_panels.html#collapse3">付款信息</a>
                        </h5>
                    </div>
                    <div id="collapse3" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">收款人：</label>
                                        <div class="col-sm-8">
                                            <input name="payee" class="form-control" required autocomplete="off" th:value="${margin.payee}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">银行卡：</label>
                                        <div class="col-sm-8">
                                            <input name="payeeCard" class="form-control" required autocomplete="off" th:value="${margin.payeeCard}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">开户行：</label>
                                        <div class="col-sm-8">
                                            <input name="payeeBank" class="form-control" required autocomplete="off" th:value="${margin.payeeBank}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!--证件上传 start-->
            <div class="panel-group" id="accordionTwo">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapsePic">证件上传</a>
                        </h5>
                    </div>
                    <div id="collapsePic" class="panel-collapse collapse in">
                        <div class="panel-body" id="picType">
                            <div class="row" th:each="dict : ${marginPicList}">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-9" th:text="${dict.context}+'：'"></label>
                                    </div>
                                </div>
                                <div class="col-md-9 col-sm-6">
                                    <div class="form-group">
                                        <div class="col-sm-7">
                                            <input th:id="'image'+${dict.value}" class="form-control"
                                                   th:name="'image'+${dict.value}" type="file" multiple>
                                            <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}"
                                                   type="hidden">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--证件上传 end-->

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseThree">合同信息</a>
                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseThree">
                    <div class="panel-body">
                        <!-- begin-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table td">
                                <thead>
                                <tr>
                                    <th style="width: 10%;">合同文件</th>
                                    <th style="width: 10%;">合同名称</th>
                                    <th style="width: 12%;">合同开始时间</th>
                                    <th style="width: 10%;">合同到期时间</th>
                                    <th style="width: 10%;">合同预警时间</th>
                                </tr>
                                </thead>
                                <tbody>


                                <tr th:each="mapS,status:${contracts}">
                                    <th:block th:if='${mapS.tid!=null}'>
                                        <td>
                                            <a href="#" th:name="${mapS.filePath}" onclick="downloadFile(this.name)">[[${mapS.fileName}]]</a>
                                            <input type="hidden" th:name="|contractList[${status.index}].tid|"  th:value="${mapS.tid}">
                                            <input  th:name="|contractList[${status.index}].fileName|" type="hidden" th:value="${mapS.fileName}">
                                        </td>
                                    </th:block>
                                    <th:block th:if='${mapS.tid==null}'>
                                        <td></td>
                                    </th:block>
                                    <td th:text="${mapS.name}"></td>
                                    <td th:text="${#dates.format(mapS.effectiveDate, 'yyyy-MM-dd')}"></td>
                                    <td th:text="${#dates.format(mapS.invalidDate, 'yyyy-MM-dd')}"></td>
                                    <td th:text="${#dates.format(mapS.warningDate, 'yyyy-MM-dd')}"></td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                        <!--end-->
                    </div>
                </div>
            </div>


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()">
            <i class="fa fa-check"></i><span th:if="${type == 'apply'}">保存并申请</span><span th:unless="${type == 'apply'}">保 存</span>
        </button>
        <button type="button" th:if="${type == 'edit'}" class="btn btn-sm btn-warning" onclick="submitHandler(1)"><i class="fa fa-check"></i>
            保存并申请
        </button>
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "tms/margin";
    //保证金附件List
    var marginPicList = [[${marginPicList}]];
    //操作类型
    var type = [[${type}]];
    var bb = [[${bb}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];

    $(function () {
        let marginBalaCorp = [[${margin.balaCorp}]];
        if (marginBalaCorp) {
            $("#balaCorpName").html($.table.selectDictLabel(balaCorp, marginBalaCorp));
        }
        $('#collapseOne').collapse('show');
        $('#collapsePic').collapse('show');
        $('#collapseThree').collapse('show');

        //获取TID并赋值
        var marginPicTidList = [[${marginPicTidList}]];
        for(var i=0 ; i<marginPicTidList.length ; i++){
            var picType = marginPicTidList[i]["picType"];
            $("#tid"+picType).val(marginPicTidList[i]["appendixId"]);
        }
        //循环图片路径信息，初始化图片上传区域
        var imagePath = [[${marginPicUrlList}]];
        for (var key in imagePath) {
            var publishFlag = "cmt_" + key;
            var fileType = null;
            if(key == 0){
                //文件类型
                fileType = 'file';
            }
            var param = {
                maxFileCount: 0,
                publish: publishFlag,  //用于绑定下一步方法
                fileType: fileType, //文件类型
                overwriteInitial: false
            };
            var tid = "tid" + key;
            var imageId = "image" + key;
            $.file.loadEditFiles(imageId, tid, imagePath[key], param);

        }

        marginAmountChange();
    });


    //提交
    function submitHandler(apply) {
        if ($('[name=marginType]').val() == '0') {
            var option = $('[name=payMethod]').find('option:selected').text();
            if (option.indexOf('银行转账') >= 0) {
                if (!$('[name=payee]').val().trim() || !$('[name=payeeCard]').val().trim() || !$('[name=payeeBank]').val().trim()) {
                    $.modal.msgError("请补全收款人、银行卡、开户行");
                    return
                }
            }
        }
        let pass = true;
        if (bb != null) {
            if ($("#image2").fileinput('getFilesCount') == 0) {
                $.modal.msgWarning("请上传“保证金收据”图片")
                pass = false;
            }
        }
        $('[name=apply]').val(apply == 1 ? 1 : 0)
        jQuery.unsubscribe("cmt_0");
        jQuery.unsubscribe("cmt_1");
        jQuery.unsubscribe("cmt_2");
        if ($.validate.form() && pass) {
            $.modal.confirm("确认提交吗", function(){
                $.modal.loading("正在处理中，请稍后...");
                $("#image0").fileinput('upload');
                jQuery.subscribe("cmt_0",cmtFile_1);
                jQuery.subscribe("cmt_1",cmtFile_2);
                jQuery.subscribe("cmt_2",commit);
            })

        }
    }
    function limitMargin(e) {
        $('#marginAmount').attr("min", e.value);
    }
    function cmtFile_1(){
        $("#image1").fileinput('upload');
    }
    function cmtFile_2(){
        $("#image2").fileinput('upload');
    }

    function commit() {
        var url = '';
        if(type == 'edit'){
            url = prefix + "/edit"
        }
        if(type == 'apply'){
            url = prefix + "/apply"
        }
        if(type == 'receive'){
            url = prefix + "/receive"
        }
        $.operate.saveTab(url, $('#form-margin-add').serialize());
    }

    /**
     * 基础信息 - 客户名称
     */
    function selectClient() {
        $.modal.open("选择客户", ctx + "client/related?permission=sales",1050,'',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //清空结算客户信息
            $("#balaName").val("");
            $("#balaCode").val("");
            $("#balaCustomerId").val("");

            //客户id
            $("#customerId").val(rows[0]["customerId"]);
            //客户名称
            $("#custName").val(rows[0]["custName"]);
            //客户简称
            $("#custAbbr").val(rows[0]["custAbbr"]);
            //客户编码
            $("#custCode").val(rows[0]["custCode"]);
            //结算公司
            $("#balaCorpId").val(rows[0]["operateCorp"]);
            $("#balaCorpName").html($.table.selectDictLabel(balaCorp, rows[0]["operateCorp"]));
            //驻场组
            $("#stationDept").val(rows[0]["stationDept"]);
            //驻场组名称
            $("#stationDeptName").val(rows[0]["stationDeptName"]);
            //结算组
            $("#balaDept").val(rows[0]["balaDept"]);
            //运营部
            $("#salesDept").val(rows[0]["salesDept"]);
            //业务员
            $("#psndoc").val(rows[0]["psndoc"]);
            //结算方式
            $("#balaType").val(rows[0]["balaType"]);
            //开票公司
            $("#billingCorp").val(rows[0]["billingCorp"]);
            //app联系人
            $("#appDeliContact").val(rows[0]["appDeliContact"]);
            //app联系方式默认填业务员联系方式
            $("#appDeliMobile").val(rows[0]["appDeliMobile"]);

            //结算公司赋值
            $("#balaCorp").val($("#balaCorpId").val())
            //集团
            $.ajax({
                url: ctx + "group/getGroupByCustomerId",
                type: "post",
                dataType: "json",
                data: {customerId: rows[0]["customerId"]},
                success: function (result) {
                    if (result.code == 0 && result.data!=undefined) {
                        if (result.data != null) {
                            $("#groupName").val(result.data.GROUP_NAME);
                            $("#groupId").val(result.data.GROUP_ID);
                        }
                    }
                }
            });
            //选中完需单独校验
            $("#form-margin-add").validate().element($("#custAbbr"));
            layer.close(index);
        });
    }
    function marginTypeChange(select) {
        if ($(select).val() == '0') {
            $("#accordion3").show()
            $("#accordion3").find(":input").prop("disabled", false)
        } else {
            $("#accordion3").hide()
            $("#accordion3").find(":input").prop("disabled", true)
        }
    }
    function marginAmountChange() {
        let marginAmount = $('[name=marginAmount]').val();
        let bbAmount = $('[name=bbAmount]').length > 0 ? $('[name=bbAmount]').val() : 0;
        let flag = (bbAmount && marginAmount && parseFloat(marginAmount) > parseFloat(bbAmount));

        $('#accordion3').find(".error").removeClass('error');
        $('#payee-error').remove();
        $('#payeeCard-error').remove();
        $('#payeeBank-error').remove();
        $('[name=payee]').prop("required", flag);
        $('[name=payeeCard]').prop("required", flag);
        $('[name=payeeBank]').prop("required", flag);
    }
</script>
</body>

</html>