<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保证金管理-列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--客户id-->
                <input type="hidden" name="customerId" id="customerId" th:value="${customerId}">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">收付款类型 ：</label>
                            <div class="col-sm-8">
                                <select name="marginType" class="form-control" th:with="type=${@dict.getType('margin_type')}">
                                    <option value="">-- 请选择 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>


            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="add()"  shiro:hasPermission="tms:margin:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single disabled" onclick="checkRecord()" shiro:hasPermission="tms:margin:checkRecord">
                <i class="fa fa-calculator"></i> 审核记录
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "tms/margin";
    //客户id
    var customerId = [[${customerId}]];
    //定金状态
    var marginVbillstatus = [[${marginVbillstatus}]];
    //定金状态-新建
    var marginVbillstatusNew = [[${marginVbillstatusNew}]];
    //类型
    var marginType = [[${@dict.getType('margin_type')}]];

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });
        var options = {
            url: prefix + "/list",
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            exportUrl: prefix + "/export",
            fixedNumber: 0,
            showToggle: false,
            showColumns: true,
            modalName: "保证金",
            uniqueId: "marginId",
            height: 560,
            clickToSelect: true,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:margin:detail')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="明细"onclick="detail(\'' + row.marginId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:margin:edit')}]] != "hidden" && row.marginVbillstatus === marginVbillstatusNew) {
                            actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="修改"onclick="edit(\'' + row.marginId + '\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:margin:apply')}]] != "hidden" && row.marginVbillstatus === marginVbillstatusNew) {
                            actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="申请"onclick="apply(\'' + row.marginId + '\')"><i class="fa fa-check-circle-o" style="font-size: 15px;"></i></a>');
                        }
                        return actions.join('');
                    },
                },
                {
                    title: '状态',
                    field: 'marginVbillstatus',
                    align: 'left',
                    formatter: function status(value,row) {
                        var context = '';
                        marginVbillstatus.forEach(function (v) {
                            if (v.value == row.marginVbillstatus) {
                                context = v.context;
                                return false;
                            }
                        });
                        return context;
                    }
                },
                {
                    title: '收付款类型',
                    field: 'marginType',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(marginType, row.marginType);
                    }
                },
                {
                    title: '保证金金额',
                    field: 'marginAmount',
                    align: 'right',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '申请人',
                    field: 'applyUserName',
                    align: 'left',
                },
                {
                    title: '申请时间',
                    field: 'applyDate',
                    align: 'left',
                },
                {
                    title: '创建人',
                    field: 'regUserName',
                    align: 'left',
                },

            ]
        };
        $.table.init(options);
    });

    /**
     * 新增
     *
     */
    function add() {
        $.modal.openTab("新增", prefix + "/add?customerId="+customerId);
    }

    /**
     * 修改
     * @param marginId
     */
    function edit(marginId) {
        var type = 'edit';
        $.modal.openTab("修改", prefix + "/edit?marginId="+marginId+"&type="+type);
    }

    /**
     * 申请
     * @param marginId
     */
    function apply(marginId) {
        $.modal.confirm("确认要申请选中的数据吗?", function () {
            var type = 'apply';
            $.modal.openTab("申请", prefix + "/edit?marginId="+marginId+"&type="+type);
        });
    }

    /**
     * 明细
     * @param marginId
     */
    function detail(marginId) {

        $.modal.openTab("明细", prefix + "/detail?marginId="+marginId);
    }

    /**
     * 审核记录
     */
    function checkRecord() {
        var marginId = $.table.selectColumns("marginId");
        var url = prefix + "/check_record?marginId="+marginId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核记录",
            area: ['800px',   '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

</script>
</body>
</html>