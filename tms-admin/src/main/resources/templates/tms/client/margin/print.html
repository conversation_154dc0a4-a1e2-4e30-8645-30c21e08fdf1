<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <meta charset="UTF-8">
    <title></title>
    <style>
        @page {
            /*size: 210mm 297mm;*/
            /*margin: 1.54cm 1.17cm 1.54cm 1.17cm;*/
            margin: 6mm;
            /*mso-header-margin: 1.5cm;
            mso-footer-margin: 1.75cm;
            mso-paper-source: 0;*/
        }
        html {
            /*width: 210mm;
            height: 297mm;*/
            border: 0px #000 solid;
            margin: 0;
            padding: 0;
        }
        body {
            margin: 0;
            padding: 0;
        }
        * {
            font-family: SimHei;
            font-size: 14px;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
        .right {
            text-align: right;
        }
        .center {
            text-align: center;
        }
        .dtl {
            width: 100%;
            border-left: 1px #000 solid;
            border-top: 1px #000 solid;
            border-collapse: collapse;
            margin-top: 0.5mm;
        }
        .dtl td {
            border-right: 1px #000 solid;
            border-bottom: 1px #000 solid;
            padding: 1px 3px;
            line-height: 19px;
        }
        .tdTitle {
            background-color: #dedede;
            width: 26mm;
            text-align: center;
            -webkit-print-color-adjust: exact; /*控制打印的时候有背景色*/
        }
        .bold {
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="title">[[${balaCorpName}]]保证金申请单</div>
<div class="right">单据编号：<span th:text="${margin.spNo}">SP202201010001</span></div>
<table class="dtl">
    <tr>
        <td class="tdTitle">标题</td>
        <td colspan="3">[[${margin.custAbbr}]][[${margin.marginAmount}]]元的保证金[[${margin.getMarginType() == 0 ? "付" : "收"}]]款申请</td>
    </tr>
    <tr>
        <td class="tdTitle">类别</td>
        <td th:text="${margin.getMarginType() == 0 ? '付款' : '收款'}"></td>
        <td class="tdTitle">[[${margin.getMarginType() == 0 ? '付款' : '收款'}]]方式</td>
        <td th:text="${payMethodName}"></td>
    </tr>
    <tr>
        <td class="tdTitle">客户名称</td><td style="width: 35%" th:text="${margin.custName}"></td>
        <td class="tdTitle">申请日期</td><td th:text="${#dates.format(margin.applyDate, 'yyyy-MM-dd HH:mm')}"></td>
    </tr>
    <tr>
        <td class="tdTitle">申请部门</td><td>[(${applyer?.dept?.deptName})]</td>
        <td class="tdTitle">申请人</td><td th:text="${margin.applyUserName}"></td>
    </tr>
    <tr>
        <td class="tdTitle">事由</td>
        <td colspan="3"><pre th:text="${margin.memo}" style="margin: 0;padding:0;"></pre></td>
    </tr>
    <tr>
        <td class="tdTitle">收款单位</td><td th:text="${margin.payee}"></td>
        <td class="tdTitle">银行帐号</td><td th:text="${margin.payeeCard}"></td>
    </tr>
    <tr>
        <td class="tdTitle">开户行</td><td colspan="3" th:text="${margin.payeeBank}"></td>
    </tr>
    <tr>
        <td class="tdTitle">保证金金额</td><td th:text="${#numbers.formatDecimal(margin.marginAmount,1,2)}"></td>
        <td class="tdTitle">大写金额</td><td th:text="${T(com.ruoyi.common.core.text.Convert).digitUppercase(margin.marginAmount)}"></td>
    </tr>
</table>

<table class="dtl">
    <tr>
        <td class="tdTitle bold" style="width: 33.33%">各部门审批意见</td>
        <td class="tdTitle bold" style="width: 33.33%">总经理审批意见</td>
        <td class="tdTitle bold" style="width: 33.33%">董事长审批意见</td>
    </tr>
    <tr>
        <td style="height: 2.5cm;" valign="top">
            <div th:each="item:${gbm_sp}">
                <span th:text="${item.user}"></span>
                <span th:text="${#dates.format(item.time, 'yyyy-MM-dd HH:mm')}"></span>
                <span th:text="${item.speech}"></span>
            </div>
        </td>
        <td valign="top">
            <span th:text="${zjl_sp?.user}"></span>
            <span th:text="${#dates.format(zjl_sp?.time, 'yyyy-MM-dd HH:mm')}"></span>
            <span th:text="${zjl_sp?.speech}"></span>
        </td>
        <td valign="top"></td>
    </tr>
</table>
</body>
</html>