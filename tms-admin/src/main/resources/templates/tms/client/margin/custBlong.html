<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保证金管理-列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--客户id-->
                <div class="row">
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <select  id="salesId" placeholder="运营部" class="form-control valid noselect2 selectpicker"
                                         onchange="changeSalesId()" aria-invalid="false" data-none-selected-text="运营部" multiple>
                                    <option th:each="salesGroup : ${salesGroupList}" th:value="${salesGroup.id}"
                                            th:text="${salesGroup.salesName}"></option>
                                </select>
                                <input type="hidden" name="salesId" value="">

                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <select  id="salesDept" placeholder="运营组" class="form-control valid noselect2 selectpicker"
                                         onchange="changeSalesDept()" aria-invalid="false" data-none-selected-text="运营组" multiple>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                                <input type="hidden" name="salesDept" value="">

                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">客户简称 ：</label>-->
                            <div class="col-sm-12">
                                <input name="custAbbr" id="custAbbr" placeholder="请输入客户简称"
                                       class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">客户简称 ：</label>-->
                            <div class="col-sm-12">
                                <input name="yearMonth" id="yearMonth" placeholder="年月"
                                       class="form-control" type="text" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">客户简称 ：</label>-->
                            <div class="col-sm-12">
                                <!--<select name="type" id="type"
                                       class="form-control" >
                                    <option value="">&#45;&#45;请选择类型&#45;&#45;</option>
                                    <option value="0">逾期资金成本</option>
                                    <option value="1">保证金成本</option>
                                </select>-->
                                <input type="hidden" name="type" class="form-control" value="1">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>


            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
    //字典数据  结算公司

    var prefix = ctx + "client";

    var selectedMyCount = 0;
    var selectedJhCount = 0;
    var selectedHkCount= 0;
    var selectedMySum = 0;
    var selectedJhSum = 0;
    var selectedHkSum = 0;

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });
        var options = {
            url: ctx + "tms/margin/blongList",
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            exportUrl: ctx + "tms/margin/exportCustBlong",
            fixedNumber: 0,
            showToggle: false,
            showColumns: true,
            modalName: "保证金",
            uniqueId: "marginId",
            height: 560,
            clickToSelect: true,
            showFooter:false,
            columns: [
                {
                    title: '操作',
                    field: 'id',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="修改组别" onclick="editGroup(\'' + value + '\')"><i  class="fa fa-retweet" style="font-size: 15px;" ></i></a>');
                        return actions.join('');
                    },
                },
                {
                    title: '客户简称',
                    field: 'custAbbr',
                    align: 'left',
                },
                {
                    title: '年月',
                    field: 'month',
                    align: 'left',
                },
                {
                    title: '管理部',
                    field: 'mgmtDeptName',
                    align: 'left',
                },
                {
                    title: '运营部',
                    field: 'salesName',
                    align: 'left',
                },
                {
                    title: '运营组',
                    field: 'salesDeptName',
                    align: 'left'
                }
            ]
        };
        $.table.init(options);

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#yearMonth',
                type: 'month',
                range: true
            });
        });

        searchPre();
    });



    /**
     * 搜索
     */
    function searchPre() {
        var data = {};

        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        var arr = [];
        arr.push(marginVbillstatus[1].value);//已申请
        arr.push(marginVbillstatus[3].value);//已付款
        arr.push(marginVbillstatus[4].value);//已收款
        $('#marginVbillstatus').selectpicker('val',arr);
        searchPre();
    }

    function changeSalesDept() {
        $('input[name="salesDept"]').val($.common.join($('#salesDept').selectpicker('val')));

    }
    function changeSalesId() {
        $('input[name="salesId"]').val($.common.join($('#salesId').selectpicker('val')));

        var selectedTexts = $("#salesId option:selected").map(function() {
            return $(this).text();
        }).get();

        // 使用逗号分隔多个值
        var deptName = selectedTexts.join(",");

        $.ajax({
            url: ctx + "tms/custSalesTarget/get_sales_dept",
            type: "get",
            dataType: "json",
            data: {parentDeptName: deptName},
            success: function (result) {
                if (result.code == 0 && result.data!=undefined) {
                    $("#salesDept").empty();
                    $('input[name="salesDept"]').val("")

                    if (result.data != null) {
                        $.each(result.data, function(index, option) {
                            $("#salesDept").append($('<option>', {
                                value: option.deptId,
                                text: option.deptName
                            }));
                        });

                        $("#salesDept").selectpicker("refresh");
                    }
                }
            }
        });

    }

    /**
     * 收款-付款
     * @param marginId
     */
    function receive(marginId,marginType) {
        var title = '付款'
        if(marginType == 1){
            title = '收款';
        }
        var type = 'receive';
        $.modal.openTab(title, prefix + "/edit?marginId="+marginId+"&type="+type);
    }

    /**
     * 明细
     * @param marginId
     */
    function detail(marginId) {
        $.modal.openTab("明细", prefix + "/detail?marginId="+marginId);
    }

    /**
     * 审核记录
     */
    function checkRecord() {
        var marginId = $.table.selectColumns("marginId");
        var url = prefix + "/check_record?marginId="+marginId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核记录",
            area: ['800px',   '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    function process() {
        var spNo = $.table.selectColumns("spNo")[0];
        if (!spNo) {
            $.modal.msgWarning("未提交微信审批")
            return
        }
        wecom_process(spNo);
    }

    function editGroup(id) {

        var url = prefix + "/editGroup2?id="+id;
        $.modal.openTab("客户所属组别修改", url);
    }

    function reset(formId){
        $(".selectpicker").selectpicker('deselectAll');
        $('input[name="salesDept"]').val('');
        $('input[name="salesId"]').val('');
        var currentId = $.common.isEmpty(formId) ? $('form').attr('id') : formId;
        $("#" + currentId)[0].reset();
        $.btTable.bootstrapTable('refresh');
    }

</script>
</body>
</html>