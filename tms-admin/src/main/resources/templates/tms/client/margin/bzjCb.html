<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保证金管理-列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--客户id-->
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">客户简称 ：</label>-->
                            <div class="col-sm-12">
                                <input name="custAbbr" id="custAbbr" placeholder="请输入客户简称"
                                       class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">客户简称 ：</label>-->
                            <div class="col-sm-12">
                                <input name="yearMonth" id="yearMonth" placeholder="年月"
                                       class="form-control" type="text" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">客户简称 ：</label>-->
                            <div class="col-sm-12">
                                <!--<select name="type" id="type"
                                       class="form-control" >
                                    <option value="">&#45;&#45;请选择类型&#45;&#45;</option>
                                    <option value="0">逾期资金成本</option>
                                    <option value="1">保证金成本</option>
                                </select>-->
                                <input type="hidden" name="type" class="form-control" value="1">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>


            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
    //字典数据  结算公司

    var prefix = ctx + "client";

    var selectedMyCount = 0;
    var selectedJhCount = 0;
    var selectedHkCount= 0;
    var selectedMySum = 0;
    var selectedJhSum = 0;
    var selectedHkSum = 0;

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });
        var options = {
            url: ctx + "tms/margin/bzjList",
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            exportUrl: prefix + "/export",
            fixedNumber: 0,
            showToggle: false,
            showColumns: true,
            modalName: "保证金",
            uniqueId: "marginId",
            height: 560,
            clickToSelect: true,
            showFooter:false,
            columns: [
                {
                    title: '操作',
                    field: 'id',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="修改组别" onclick="editGroup(\'' + value + '\')"><i  class="fa fa-retweet" style="font-size: 15px;" ></i></a>');
                        return actions.join('');
                    },
                },
                {
                    title: '客户简称',
                    field: 'custAbbr',
                    align: 'left',
                },
                {
                    title: '运营部',
                    field: 'salesName',
                    align: 'left',
                },
                {
                    title: '运营组',
                    field: 'deptName',
                    align: 'left'
                },
                {
                    title: '年月',
                    field: 'yearMonth',
                    align: 'left',
                },
                {
                    title: '类型',
                    field: 'type',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(value == 0){
                            return '<span class="label label-warning">逾期资金成本</label>'
                        }else if(value == 1){
                            return '<span class="label label-danger">保证金成本</label>'
                        }else{
                            return '-'
                        }
                    }
                },
                {
                    title: '保证金成本',
                    field: 'zjcb',
                    align: 'right',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '当月目标数',
                    field: 'cnt',
                    align: 'right',
                }

            ]
        };
        $.table.init(options);

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#yearMonth',
                type: 'month',
                range: false
            });
        });

        searchPre();
    });



    /**
     * 搜索
     */
    function searchPre() {
        var data = {};

        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        var arr = [];
        arr.push(marginVbillstatus[1].value);//已申请
        arr.push(marginVbillstatus[3].value);//已付款
        arr.push(marginVbillstatus[4].value);//已收款
        $('#marginVbillstatus').selectpicker('val',arr);
        searchPre();
    }

    /**
     * 收款-付款
     * @param marginId
     */
    function receive(marginId,marginType) {
        var title = '付款'
        if(marginType == 1){
            title = '收款';
        }
        var type = 'receive';
        $.modal.openTab(title, prefix + "/edit?marginId="+marginId+"&type="+type);
    }

    /**
     * 明细
     * @param marginId
     */
    function detail(marginId) {
        $.modal.openTab("明细", prefix + "/detail?marginId="+marginId);
    }

    /**
     * 审核记录
     */
    function checkRecord() {
        var marginId = $.table.selectColumns("marginId");
        var url = prefix + "/check_record?marginId="+marginId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核记录",
            area: ['800px',   '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    function process() {
        var spNo = $.table.selectColumns("spNo")[0];
        if (!spNo) {
            $.modal.msgWarning("未提交微信审批")
            return
        }
        wecom_process(spNo);
    }

    function editGroup(id) {

        var url = prefix + "/editGroup1?id="+id;
        $.modal.openTab("保证金成本组别修改", url);
    }
</script>
</body>
</html>