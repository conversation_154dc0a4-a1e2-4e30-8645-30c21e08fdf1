<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('业务中心-保证金管理')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--客户id-->
                <div class="row">
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">

                            <div class="col-sm-12">
                                <input name="custAbbr" class="form-control" autocomplete="off" aria-autocomplete="none" type="text" maxlength="20" placeholder="请输入客户名称">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <select  id="salesId" placeholder="运营部" class="form-control valid noselect2 selectpicker"
                                         onchange="changeSalesId()" aria-invalid="false" data-none-selected-text="运营部" multiple>
                                    <option th:each="salesGroup : ${salesGroupList}" th:value="${salesGroup.deptId}"
                                            th:text="${salesGroup.deptName}"></option>
                                </select>
                                <input type="hidden" name="salesId" value="">

                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <select  id="salesDept" placeholder="运营组" class="form-control valid noselect2 selectpicker"
                                         onchange="changeSalesDept()" aria-invalid="false" data-none-selected-text="运营组" multiple>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                                <input type="hidden" name="salesDept" value="">

                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <select name="balaDept" placeholder="结算组" class="form-control valid noselect2 selectpicker"
                                          aria-invalid="false" data-none-selected-text="结算组" multiple>
                                    <option th:each="mapS,status:${balaDept}" th:value="${mapS.deptId}" th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group flex">
                            <div class="col-sm-12">
                                <select name="kbFlag" class="form-control" onchange="changeSalesDept()">
                                    <option value="">-请选择条件-</option>
                                    <option value="1">三个月内营收小于保证金余额</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <input id="hiddenText" type="text" style="display:none" />

                    <div class="col-md-2 col-sm-6">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <!--<a class="btn btn-primary" onclick="add()"  shiro:hasPermission="tms:margin:business:manage">
                <i class="fa fa-cog"></i> 管理
            </a>-->
            <a class="btn btn-primary" onclick="add()"  shiro:hasPermission="tms:margin:business:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:marginBusiness:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-info" th:onclick="showSpSettings([[${templateId}]], [[${biz}]])" shiro:hasPermission="wecom:sp:setting">
                <i class="fa fa-cog"></i> 配置
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
    var prefix = ctx + "tms/margin";


    //开票金额合计
    var ye = 0;
    var syys = 0;

    function searchx() {
        var data = {};
        data.balaDept = $.common.join($('[name=balaDept]').selectpicker('val'));//结算组
        $.table.search('role-form', data);
    }

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });
        var options = {
            url: prefix + "/business/list",
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            exportUrl: prefix + "/export",
            fixedNumber: 0,
            showToggle: false,
            showColumns: true,
            showFooter:true,
            modalName: "保证金",
            uniqueId: "marginId",
            height: 560,
            clickToSelect: true,
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onPostBody: function () {
                clearTotal();
                //合并页脚
                merge_footer();
                getAmountCount();
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "余额：<nobr id='ye'>¥0.00</nobr>" +
                        " 三个月内营收：<nobr id='syys'>¥0.00</nobr>" +
                        "<br>" +
                        "总合计：余额：<nobr id='yeTotal'></nobr>" +
                        " 三个月内营收：<nobr id='syysTotal'></nobr>" ;
                }
            },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="明细" onclick="detail(\'' + row.customerId + '\','+index+')"><i class="fa fa-list" style="font-size: 15px;"></i></a>');
                        return actions.join('');
                    },
                },
                {title: '部门', field: 'salesGroupName', align: 'left'},
                {title: '运营组', field: 'salesDeptName', align: 'left'},
                {title: '客户名称', field: 'custAbbr', align: 'left'},
                {title: '结算组', field:'balaDeptName'},
                //{title: '客户联系人', field: 'contact', align: 'left',},
                //{title: '客户联系电话', field: 'phone', align: 'left',},
                {
                    title: '余额',
                    field: 'thisMonthLender',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if(row.lastMonthAmount === null){
                            row.lastMonthAmount = 0;
                        }
                        var over = row.lastMonthAmount+row.thisMonthDebit - row.thisMonthLender
                        return over.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {title: '付款中', field: 'paying',align: 'right',formatter:function(value,row,index){
                    return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }},
                {title: '收款中', field: 'recving',align: 'right',formatter:function(value,row,index){
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }},
                {
                    title: '三个月内营收 <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px;color:coral" data-html="true" data-container="body" title="从[(${latest3Month})]至今"></i>',
                    field: 'lastMonthReceiveAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if(row.lastMonthReceiveAmount === null){
                            row.lastMonthReceiveAmount = 0;
                        }
                        return row.lastMonthReceiveAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }
            ]
        };
        $.table.init(options);
    });

    /**
     * 明细
     * @param marginId
     */
    function detail(customerId, index) {
        // $.modal.openTab("明细", prefix + "/business/detail?customerId="+customerId);
        let row = $('#bootstrap-table').bootstrapTable('getData')[index];
        $.modal.openTab(row.custAbbr +"保证金明细", prefix + "/manageMargin?customerId="+customerId);
    }

    function changeSalesDept() {
        $('input[name="salesDept"]').val($.common.join($('#salesDept').selectpicker('val')));

    }
    function changeSalesId() {
        $('input[name="salesId"]').val($.common.join($('#salesId').selectpicker('val')));

        var selectedIds = $("#salesId option:selected").map(function() {
            return $(this).attr("value");
        }).get();

        // 使用逗号分隔多个值
        var deptIds = selectedIds.join(",");

        $.ajax({
            url: ctx + "tms/custSalesTarget/get_sales_dept_id",
            type: "get",
            dataType: "json",
            data: {parentDeptId: deptIds},
            success: function (result) {
                if (result.code == 0 && result.data!=undefined) {
                    $("#salesDept").empty();
                    $('input[name="salesDept"]').val("")

                    if (result.data != null) {
                        $.each(result.data, function(index, option) {
                            $("#salesDept").append($('<option>', {
                                value: option.deptId,
                                text: option.deptName
                            }));
                        });

                        $("#salesDept").selectpicker("refresh");
                    }
                }
            }
        });

    }
    /**
     * 管理
     */
    function add() {
        $.modal.openTab("新增履约保证金", prefix + "/addMargin");
    }

    function reset(formId){
        $(".selectpicker").selectpicker('deselectAll');
        $('input[name="salesDept"]').val('');
        $('input[name="salesId"]').val('');
        var currentId = $.common.isEmpty(formId) ? $('form').attr('id') : formId;
        $("#" + currentId)[0].reset();
        $('.selectpicker').selectpicker('refresh');
        searchx()
    }


    /**
     * 将总计金额清零
     */
    function clearTotal() {
        ye = 0;
        syys = 0;

    }

    function addTotal(row) {
        ye += row.lastMonthAmount;
        syys += row.lastMonthReceiveAmount;
    }


    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#ye").text(ye.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#syys").text(syys.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }

    function subTotal(row) {
        ye -= row.lastMonthAmount;
        syys -= row.lastMonthReceiveAmount;
    }

    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }


    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.balaDept = $.common.join($('[name=balaDept]').selectpicker('val'));//结算组
        $.ajax({
            url: prefix + "/getListCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                $("#yeTotal").text(result.LASTMONTHAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#syysTotal").text(result.LASTMONTHRECEIVEAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            }
        });
    }

</script>
</body>
</html>