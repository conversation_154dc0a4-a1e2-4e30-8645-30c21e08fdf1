<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保证金管理-列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--客户id-->
                <input type="hidden" name="customerId" id="customerId" th:value="${customerId}">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">保证金状态 ：</label>
                            <div class="col-sm-8">
                                <select name="marginVbillstatus" class="form-control">
                                    <option value="">-- 请选择 --</option>
                                    <option th:each="mapS,status:${marginVbillstatus}" th:text="${mapS.context}" th:value="${mapS.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">收付款类型 ：</label>
                            <div class="col-sm-8">
                                <select name="marginType" class="form-control" th:with="type=${@dict.getType('margin_type')}">
                                    <option value="">-- 请选择 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6" th:if="${customerId==null}">
                        <div class="form-group">
                            <label class="col-sm-4">客户简称 ：</label>
                            <div class="col-sm-8">
                                <input name="custAbbr" class="form-control"  type="text" maxlength="20">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </div>

                </div>


            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="add()"  shiro:hasPermission="tms:margin:business:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple" onclick="del()"  shiro:hasPermission="tms:margin:remove">
                <i class="fa fa-times"></i> 删除
            </a>
            <a class="btn btn-primary single disabled" onclick="checkRecord()" shiro:hasPermission="tms:margin:business:checkRecord">
                <i class="fa fa-calculator"></i> 审核记录
            </a>
            <a class="btn btn-info" th:onclick="showSpSettings([[${templateId}]], [[${biz}]])" shiro:hasPermission="wecom:sp:setting">
                <i class="fa fa-cog"></i> 配置
            </a>
            <a class="btn btn-info single disabled" onclick="process()">
                <i class="fa fa-laptop"></i> 审批进度
            </a>
            <a class="btn btn-warning single disabled" onclick="bzjPrint()">
                <i class="fa fa-print"></i> 打印
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
    var prefix = ctx + "tms/margin";
    //客户id
    var customerId = [[${customerId}]];
    //保证金状态
    var marginVbillstatus = [[${marginVbillstatus}]];
    //保证金状态-新建
    var marginVbillstatusNew = [[${marginVbillstatusNew}]];
    //类型
    var marginType = [[${@dict.getType('margin_type')}]];
    var payMethod = [[${@dict.getType('pay_method')}]]

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });
        var options = {
            url: prefix + "/marginListWithShiro",
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            exportUrl: prefix + "/export",
            fixedNumber: 0,
            showToggle: false,
            showColumns: true,
            modalName: "保证金",
            uniqueId: "marginId",
            height: 560,
            clickToSelect: true,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:margin:business:detail')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="明细"onclick="detail(\'' + row.marginId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:margin:business:edit')}]] != "hidden" && (row.marginVbillstatus === 0 || row.marginVbillstatus === 5)) { //新建、驳回状态下才可以修改
                            actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="修改"onclick="edit(\'' + row.marginId + '\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:margin:business:apply')}]] != "hidden" && (row.marginVbillstatus === 0 || row.marginVbillstatus === 5)) { //新建、驳回状态下才可以申请
                            actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="申请"onclick="apply(\'' + row.marginId + '\')"><i class="fa fa-check-circle-o" style="font-size: 15px;"></i></a>');
                        }
                        return actions.join('');
                    },
                },
                {
                    title: '状态',
                    field: 'marginVbillstatus',
                    align: 'left',
                    formatter: function status(value,row) {
                        var context = '';
                        marginVbillstatus.forEach(function (v) {
                            if (v.value == row.marginVbillstatus) {
                                context = v.context;
                                return false;
                            }
                        });
                        return context;
                    }
                },
                {
                    title: '收付款类型',
                    field: 'marginType',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(marginType, row.marginType);
                    }
                },
                {
                    title: '客户名称',
                    field: 'custName',
                    align: 'left',
                    // formatter: function (value, row, index) {
                    //     return $.table.selectDictLabel(marginType, row.marginType);
                    // }
                },
                {
                    title: '保证金金额',
                    field: 'marginAmount',
                    align: 'right',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '转自投标金额',
                    field: 'bbAmount',
                    align: 'right',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    field: 'payMethod',
                    title: '收付款方式',
                    formatter(value,row,index) {
                        return $.table.selectDictLabel(payMethod, value)
                    }
                },
                {
                    title: '账户',
                    field: 'payAccount',
                    align: 'left',
                },
                {
                    title: '申请人',
                    field: 'applyUserName',
                    align: 'left',
                },
                {
                    title: '申请时间',
                    field: 'applyDate',
                    align: 'left',
                },
                {
                    title: '创建人',
                    field: 'regUserName',
                    align: 'left',
                },
                {
                    title: '审批单号',
                    field: 'spNo'
                }
            ]
        };
        $.table.init(options);
    });

    /**
     * 新增
     *
     */
    function add() {
        $.modal.openTab("新增履约保证金", prefix + "/addMargin");
    }

    /**
     * 修改
     * @param marginId
     */
    function edit(marginId) {
        var type = 'edit';
        $.modal.openTab("修改", prefix + "/edit?marginId="+marginId+"&type="+type);
    }

    /**
     * 申请
     * @param marginId
     */
    function apply(marginId) {
        //$.modal.confirm("确认要申请选中的数据吗?", function () {
            var type = 'apply';
            $.modal.openTab("申请", prefix + "/edit?marginId="+marginId+"&type="+type);
        //});
    }

    /**
     * 明细
     * @param marginId
     */
    function detail(marginId) {

        $.modal.openTab("明细", prefix + "/detail?marginId="+marginId);
    }

    /**
     * 审核记录
     */
    function checkRecord() {
        var marginId = $.table.selectColumns("marginId");
        var url = prefix + "/check_record?marginId="+marginId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核记录",
            area: ['800px',   '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    function process() {
        var spNo = $.table.selectColumns("spNo")[0];
        if (!spNo) {
            $.modal.msgWarning("尚未提交审批")
            return
        }
        wecom_process(spNo);
    }
    function bzjPrint() {
        var spNo = $.table.selectColumns("spNo")[0];
        var marginId = $.table.selectColumns("marginId")[0];
        var marginVbillstatus = $.table.selectColumns("marginVbillstatus")[0];
        if (marginVbillstatus == 1 && spNo) {
            let iframe = document.getElementById("print-frame");
            if (!iframe) {
                iframe = document.createElement('IFRAME');
                iframe.id = "print-frame"
                document.body.appendChild(iframe);
                iframe.setAttribute('style', 'display:none;');
            }
            iframe.src = prefix + "/print?marginId="+marginId+"&spNo="+spNo
            iframe.onload = function() { //解决图片显示不了的问题
                iframe.contentWindow.focus();
                iframe.contentWindow.print();
                //document.body.removeChild(iframe);
            };
        } else {
            $.modal.msgWarning("只能打印微信端审批通过的单据")
        }
    }
    function del() {
        var statusArr = $.table.selectColumns("marginVbillstatus");
        for (let i = 0; i < statusArr.length; i++) {
            if (statusArr[i] !== 0 && statusArr[i] !== 5) {
                $.modal.msgWarning("只能删除新建或驳回的单据")
                return
            }
        }
        var idArr = $.table.selectColumns("marginId");
        $.modal.confirm("确认删除该"+idArr.length+"条数据？", function(){
            $.operate.post(prefix + "/remove", "ids="+idArr.join(','))
        })
    }
</script>
</body>
</html>