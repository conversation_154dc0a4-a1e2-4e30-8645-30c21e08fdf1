<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保证金-edit')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-margin-add" class="form-horizontal" novalidate="novalidate">
        <!--保证金id-->
        <input type="hidden" th:value="${margin.marginId}" name="marginId">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row" th:if="${type == 'edit'}">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span style="color: red; ">保证金金额：</span></label>
                                    <div class="col-sm-8">
                                        <input name="marginAmount" id="marginAmount" th:value="${margin.marginAmount}"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                               class="form-control" required type="text" maxlength="20">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收付款类型：</label>
                                    <div class="col-sm-8">
                                        <select name="marginType" class="form-control valid" required
                                                th:with="type=${@dict.getType('margin_type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"
                                                    th:field="${margin.marginType}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收付款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="payMethod" class="form-control valid" required
                                                th:with="type=${@dict.getType('pay_method')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}"
                                                    th:text="${dict.dictLabel}" th:field="${margin.payMethod}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">类型：</label>
                                    <div class="col-sm-8">
                                        <select name="logisticsType" class="form-control valid" required
                                                th:with="type=${@dict.getType('logistics_type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:field="${margin.logisticsType}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${type == 'apply' || type == 'receive'}">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>客户名称：</span></label>
                                    <div class="col-sm-8">
                                        <input  th:value="${margin.custAbbr}" disabled
                                               class="form-control" required type="text" maxlength="20">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>保证金金额：</span></label>
                                    <div class="col-sm-8">
                                        <input name="marginAmount" th:value="${margin.marginAmount}" disabled
                                               class="form-control" required type="text" maxlength="20">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收付款类型：</label>
                                    <div class="col-sm-8">
                                        <select name="marginType" class="form-control valid" disabled
                                                th:with="type=${@dict.getType('margin_type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"
                                                    th:field="${margin.marginType}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收付款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="payMethod" class="form-control valid"  required
                                                th:with="type=${@dict.getType('pay_method')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}"
                                                    th:text="${dict.dictLabel}" th:field="${margin.payMethod}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6" th:if="${type == 'apply' || type == 'receive'}">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">类型：</label>
                                    <div class="col-sm-8">
                                        <select name="logisticsType" class="form-control valid" required
                                                th:with="type=${@dict.getType('logistics_type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}"
                                                    th:text="${dict.dictLabel}"
                                                    th:field="${margin.logisticsType}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>对应合同号：</span></label>
                                    <div class="col-sm-8">
                                        <input name="contractNo" id="contractNo" th:value="${margin.contractNo}"
                                               class="form-control" type="text" maxlength="50">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">业务期限开始：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="time-input form-control"
                                               th:value="${#dates.format(margin.businessPeriodStart, 'yyyy-MM-dd')}"
                                               id="businessPeriodStart" name="businessPeriodStart"
                                               placeholder="业务期限开始"  readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">业务期限结束：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="time-input form-control" id="businessPeriodEnd"
                                               th:value="${#dates.format(margin.businessPeriodEnd, 'yyyy-MM-dd')}"
                                               name="businessPeriodEnd" placeholder="业务期限结束"  readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${type == 'receive'}">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>审核人：</span></label>
                                    <div class="col-sm-8">
                                        <input  th:value="${margin.checkUserName}" disabled
                                                class="form-control" required type="text" maxlength="20">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>审核时间：</span></label>
                                    <div class="col-sm-8">
                                        <input  th:value="${#dates.format(margin.checkDate, 'yyyy-MM-dd')}" disabled
                                               class="form-control" required type="text" maxlength="20">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">账户：</label>
                                    <div class="col-sm-8">
                                        <select name="payAccount" class="form-control" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${account}" th:text="${dict.accountName}" th:value="${dict.accountId}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-6">
                                            <textarea name="memo" maxlength="500" class="form-control valid"
                                                      rows="3" th:text="${margin.memo}"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--证件上传 start-->
            <div class="panel-group" id="accordionTwo">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapsePic">证件上传</a>
                        </h5>
                    </div>
                    <div id="collapsePic" class="panel-collapse collapse in">
                        <div class="panel-body" id="picType">
                            <div class="row" th:each="dict : ${marginPicList}">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-9" th:text="${dict.context}+'：'"></label>
                                    </div>
                                </div>
                                <div class="col-md-9 col-sm-6">
                                    <div class="form-group">
                                        <div class="col-sm-7">
                                            <input th:id="'image'+${dict.value}" class="form-control"
                                                   th:name="'image'+${dict.value}" type="file" multiple>
                                            <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}"
                                                   type="hidden">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--证件上传 end-->

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseThree">合同信息</a>
                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseThree">
                    <div class="panel-body">
                        <!-- begin-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table td">
                                <thead>
                                <tr>
                                    <th style="width: 10%;">合同文件</th>
                                    <th style="width: 10%;">合同名称</th>
                                    <th style="width: 12%;">合同开始时间</th>
                                    <th style="width: 10%;">合同到期时间</th>
                                    <th style="width: 10%;">合同预警时间</th>
                                </tr>
                                </thead>
                                <tbody>


                                <tr th:each="mapS,status:${contracts}">
                                    <th:block th:if='${mapS.tid!=null}'>
                                        <td>
                                            <a href="#" th:name="${mapS.filePath}" onclick="downloadFile(this.name)">[[${mapS.fileName}]]</a>
                                            <input type="hidden" th:name="|contractList[${status.index}].tid|"  th:value="${mapS.tid}">
                                            <input  th:name="|contractList[${status.index}].fileName|" type="hidden" th:value="${mapS.fileName}">
                                        </td>
                                    </th:block>
                                    <th:block th:if='${mapS.tid==null}'>
                                        <td></td>
                                    </th:block>
                                    <td th:text="${mapS.name}"></td>
                                    <td th:text="${#dates.format(mapS.effectiveDate, 'yyyy-MM-dd')}"></td>
                                    <td th:text="${#dates.format(mapS.invalidDate, 'yyyy-MM-dd')}"></td>
                                    <td th:text="${#dates.format(mapS.warningDate, 'yyyy-MM-dd')}"></td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                        <!--end-->
                    </div>
                </div>
            </div>

        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "tms/margin";
    //保证金附件List
    var marginPicList = [[${marginPicList}]];
    //操作类型
    var type = [[${type}]];

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapsePic').collapse('show');
        $('#collapseThree').collapse('show');

        //获取TID并赋值
        var marginPicTidList = [[${marginPicTidList}]];
        for(var i=0 ; i<marginPicTidList.length ; i++){
            var picType = marginPicTidList[i]["picType"];
            $("#tid"+picType).val(marginPicTidList[i]["appendixId"]);
        }
        //循环图片路径信息，初始化图片上传区域
        var imagePath = [[${marginPicUrlList}]];
        for (var key in imagePath) {
            var publishFlag = "cmt_" + key;
            var fileType = null;
            if(key == 0){
                //文件类型
                fileType = 'file';
            }
            var param = {
                maxFileCount: 0,
                publish: publishFlag,  //用于绑定下一步方法
                fileType: fileType, //文件类型
                overwriteInitial: false
            };
            var tid = "tid" + key;
            var imageId = "image" + key;
            $.file.loadEditFiles(imageId, tid, imagePath[key], param);

        }
    });


    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.modal.loading("正在处理中，请稍后...");
            $("#image0").fileinput('upload');
            jQuery.subscribe("cmt_0",cmtFile_1);
            jQuery.subscribe("cmt_1",commit);
        }
    }

    function cmtFile_1(){
        $("#image1").fileinput('upload');
    }

    function commit() {
        var url = '';
        if(type == 'edit'){
            url = prefix + "/edit"
        }
        if(type == 'apply'){
            url = prefix + "/apply"
        }
        if(type == 'receive'){
            url = prefix + "/receive"
        }
        $.operate.saveTab(url, $('#form-margin-add').serialize());
    }

</script>
</body>

</html>