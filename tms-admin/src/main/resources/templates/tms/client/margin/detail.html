<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保证金-detail')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <style type="text/css">
        .flex {
            display: flex;
            justify-content: space-between;
        }
        .flex_left {
            width: 120px;
            text-align: right;
        }
        .flex_right {
            flex: 1;
        }
    </style>
</head>

<body>
<div class="form-content">
    <form id="form-margin-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion11" th:if="${bb!=null}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion11"
                           href="tabs_panels.html#collapse1">投标保证金信息</a>
                    </h5>
                </div>
                <div id="collapse1" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">线索客户：</label>
                                    <div class="flex_right" style="line-height: 20px" th:text="${bb.custName}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">项目名称：</label>
                                    <div class="flex_right" style="line-height: 20px" th:text="${bb.itemName}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left" style="width: 120px">转履约金金额：</label>
                                    <div class="flex_right" style="line-height: 20px" th:text="${margin.bbAmount}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" id="accordion0">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion0"
                           href="tabs_panels.html#collapse0">客户信息</a>
                    </h5>
                </div>
                <div id="collapse0" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">客户名称：</label>
                                    <div class="col-sm-8" th:text="${margin.custAbbr}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>保证金金额：</span></label>
                                    <div class="col-sm-8">
                                        <span th:text="${margin.marginAmount}"></span>
                                        <span th:if="${bb != null && margin.marginType==0}" style="color:red;font-weight: bold">
                                            （需打款：[[${margin.marginAmount - margin.bbAmount}]]）
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收付款类型：</label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('margin_type')}"
                                         th:if="${dict.dictValue} == ${margin.marginType}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收付款方式：</label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('pay_method')}"
                                         th:if="${dict.dictValue} == ${margin.payMethod}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">类型：</label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('logistics_type')}"
                                         th:if="${dict.dictValue} == ${margin.logisticsType}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>对应合同号：</span></label>
                                    <div class="col-sm-8" th:text="${margin.contractNo}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">业务期限开始：</label>
                                    <div class="col-sm-8" th:text="${#dates.format(margin.businessPeriodStart, 'yyyy-MM-dd')}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">业务期限结束：</label>
                                    <div class="col-sm-8" th:text="${#dates.format(margin.businessPeriodEnd, 'yyyy-MM-dd')}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>账户：</span></label>
                                    <div class="col-sm-8" th:text="${margin.payAccount}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>支付时间：</span></label>
                                    <div class="col-md-8" th:text="${#dates.format(margin.payDate, 'yyyy-MM-dd')}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10" th:text="${margin.memo}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" th:styleappend="${margin.marginType==0?'':'display:none'}" id="accordion3">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion3"
                           href="tabs_panels.html#collapse3">付款信息</a>
                    </h5>
                </div>
                <div id="collapse3" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span style="color: red; ">收款人：</span></label>
                                    <div class="col-sm-8" th:text="${margin.payee}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">银行卡：</label>
                                    <div class="col-sm-8" th:text="${margin.payeeCard}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">开户行：</label>
                                    <div class="col-sm-8" th:text="${margin.payeeBank}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" id="accordionTwo">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">证件信息</a>
                    </h5>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-6">相关文件：</label>
                                    <div class="col-sm-6">
                                        <div style="display:inline-block">
                                            <div th:each="pic:${marginPicUrlList}" th:if="${pic.picType== 0}">
                                                <a target="view_window" th:href="@{${pic.filePath}}"
                                                   th:text="${pic.fileName}"></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-6">相关图片：</label>
                                    <div class="col-sm-6">
                                        <div style="display:inline-block">
                                            <div th:each="pic:${marginPicUrlList}" th:if="${pic.picType== 1}">
                                                <img modal="zoomImg" style="width:70px; height:50px"
                                                     th:src="@{${pic.filePath}}"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-6">保证金收据：</label>
                                    <div class="col-sm-6">
                                        <div style="display:inline-block">
                                            <div th:each="pic:${marginPicUrlList}" th:if="${pic.picType== 2}">
                                                <img modal="zoomImg" style="width:70px; height:50px"
                                                     th:src="@{${pic.filePath}}"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseThree">合同信息</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseThree">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table td">
                            <thead>
                            <tr>
                                <th style="width: 10%;">合同文件</th>
                                <th style="width: 10%;">合同名称</th>
                                <th style="width: 12%;">合同开始时间</th>
                                <th style="width: 10%;">合同到期时间</th>
                                <th style="width: 10%;">合同预警时间</th>
                            </tr>
                            </thead>
                            <tbody>


                            <tr th:each="mapS,status:${contracts}">
                                <th:block th:if='${mapS.tid!=null}'>
                                    <td>
                                        <a href="#" th:name="${mapS.filePath}" onclick="downloadFile(this.name)">[[${mapS.fileName}]]</a>
                                        <input type="hidden" th:name="|contractList[${status.index}].tid|"  th:value="${mapS.tid}">
                                        <input  th:name="|contractList[${status.index}].fileName|" type="hidden" th:value="${mapS.fileName}">
                                    </td>
                                </th:block>
                                <th:block th:if='${mapS.tid==null}'>
                                    <td></td>
                                </th:block>
                                <td th:text="${mapS.name}"></td>
                                <td th:text="${#dates.format(mapS.effectiveDate, 'yyyy-MM-dd')}"></td>
                                <td th:text="${#dates.format(mapS.invalidDate, 'yyyy-MM-dd')}"></td>
                                <td th:text="${#dates.format(mapS.warningDate, 'yyyy-MM-dd')}"></td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                    <!--end-->
                </div>
            </div>
        </div>

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
    });
</script>
</body>

</html>