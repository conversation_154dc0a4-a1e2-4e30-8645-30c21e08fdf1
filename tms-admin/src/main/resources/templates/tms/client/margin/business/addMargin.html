<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保证金-add')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <style type="text/css">
        .flex {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .flex_left {
            width: 120px;
            text-align: right;
        }
        .flex_right {
            flex: 1;
        }
        .file-drop-zone {
            height: auto!important;
            margin: 0;
        }
        .theme-explorer .explorer-frame .kv-file-content {
            width: 70px;
            height: 70px;
            padding: 3px;
        }
    </style>
</head>

<body>
<div class="form-content">
    <form id="form-margin-add" class="form-horizontal" novalidate="novalidate">
        <!--客户id-->
<!--        <input type="hidden" th:value="${customerId}" name="customerId">-->
        <!--保证金状态-新建-->
        <input type="hidden" th:value="${marginVbillstatus}" name="marginVbillstatus">
        <input type="hidden" th:value="${bb?.id}" name="bbid">
        <input th:if="${bb != null}" name="apply" type="hidden" value="1">

        <div class="panel-group" id="accordion11" th:if="${bb!=null}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion11"
                           href="tabs_panels.html#collapse1">投标保证金信息</a>
                    </h5>
                </div>
                <div id="collapse1" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">线索客户：</label>
                                    <div class="flex_right">
                                        <div style="line-height: 20px;border: none;" th:text="${bb.custName}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">项目名称：</label>
                                    <div class="flex_right">
                                        <div style="line-height: 20px;border: none;" th:text="${bb.itemName}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left" style="width: 120px">转履约金金额：</label>
                                    <div class="flex_right">
                                        <input class="form-control" name="bbAmount" required th:value="${bbAmount}" th:max="${bbAmount}" min="0.01" oninput="$.numberUtil.onlyNumberTwoDecimal(this);limitMargin(this)">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- TODO 选择客户 -->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">客户选择</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span
                                            style="color: red; ">选择客户：</span></label>
                                    <div class="col-sm-8">
                                        <input name="custAbbr" id="custAbbr" onclick="selectClient()" type="text"
                                               placeholder="请选择客户" class="form-control valid"
                                               aria-required="true" required autocomplete="off" readonly>
                                        <input name="custName" id="custName" type="hidden">
                                        <input name="custCode" id="custCode" type="hidden">
                                        <input name="customerId" id="customerId" type="hidden">
                                        <input name="balaCorpId" id="balaCorpId" type="hidden">
                                        <input name="balaDept" id="balaDept" type="hidden">
                                        <input name="salesDept" id="salesDept" type="hidden">
                                        <input name="psndoc" id="psndoc" type="hidden">
                                        <input name="billingCorp" id="billingCorp" type="hidden">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>结算公司：</span></label>
                                    <div class="col-sm-8" id="balaCorpName"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <div class="panel-group" id="accordionX">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordionX"
                           href="tabs_panels.html#collapseOneX">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOneX" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span
                                            style="color: red; ">保证金金额：</span></label>
                                    <div class="col-sm-8">
                                        <input name="marginAmount" id="marginAmount"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);marginAmountChange(this)" autocomplete="off" aria-autocomplete="none"
                                               th:min="${bbAmount}"
                                               class="form-control" required type="text" maxlength="20">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收付款类型：</label>
                                    <div class="col-sm-8">
                                        <select name="marginType" class="form-control valid" required
                                                th:with="type=${@dict.getType('margin_type')}" onchange="marginTypeChange(this)" th:disabled="${bb!=null}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}" th:selected="${bb!=null && dict.dictValue=='0'}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">收付款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="payMethod" class="form-control valid" required onchange="changePayMethod(this)"
                                                th:with="type=${@dict.getType('pay_method')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">类型：</label>
                                    <div class="col-sm-8">
                                        <select name="logisticsType" class="form-control valid" required
                                                th:with="type=${@dict.getType('logistics_type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>对应合同号：</span></label>
                                    <div class="col-sm-8">
                                        <input name="contractNo" id="contractNo" class="form-control" type="text" maxlength="50">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">业务期限开始：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="time-input form-control" id="businessPeriodStart" name="businessPeriodStart"
                                               placeholder="业务期限开始"  readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">业务期限结束：</label>
                                    <div class="col-sm-8">
                                        <input type="text" class="time-input form-control" id="businessPeriodEnd" name="businessPeriodEnd"
                                               placeholder="业务期限结束"  readonly>
                                    </div>
                                </div>
                            </div>
                            <!--<div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><font color="red">结算公司：</font></label>
                                    <div class="col-sm-8">
                                        <select name="balaCorp" id="balaCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" required disabled>
                                            <option value="">---请选择结算公司---</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>-->
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-6">
                                            <textarea name="memo" maxlength="500" class="form-control valid" rows="3" ></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" style="display: none" id="accordion3">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion3"
                           href="tabs_panels.html#collapse3">付款信息</a>
                    </h5>
                </div>
                <div id="collapse3" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>收款人：</span></label>
                                    <div class="col-sm-8">
                                        <input name="payee" class="form-control" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">银行卡：</label>
                                    <div class="col-sm-8">
                                        <input name="payeeCard" class="form-control" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">开户行：</label>
                                    <div class="col-sm-8">
                                        <input name="payeeBank" class="form-control" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--证件上传 start-->
        <div class="panel-group" id="accordionTwo">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapsePic">附件上传</a>
                    </h5>
                </div>
                <div id="collapsePic" class="panel-collapse collapse in">
                    <div class="panel-body" id="picType">
                        <div class="row" th:each="dict : ${marginPicList}" >
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-9" th:id="'image'+${dict.value}+'Label'" th:text="${dict.context}+'：'"></label>
                                </div>
                            </div>
                            <div class="col-md-9 col-sm-6">
                                <div class="form-group">
                                    <div class="col-sm-7">
                                        <input th:id="'image'+${dict.value}" class="form-control"
                                               th:name="'image'+${dict.value}" type="file" multiple>
                                        <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}" type="hidden">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--证件上传 end-->

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseThree">合同信息</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseThree">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table td">
                            <thead>
                            <tr>
                                <th style="width: 10%;">合同文件</th>
                                <th style="width: 10%;">合同名称</th>
                                <th style="width: 12%;">合同开始时间</th>
                                <th style="width: 10%;">合同到期时间</th>
                                <th style="width: 10%;">合同预警时间</th>
                            </tr>
                            </thead>
                            <tbody>
                                <tr th:each="mapS,status:${contracts}">
                                    <th:block th:if='${mapS.tid!=null}'>
                                        <td>
                                            <a href="#" th:name="${mapS.filePath}" onclick="downloadFile(this.name)">[[${mapS.fileName}]]</a>
                                            <input type="hidden" th:name="|contractList[${status.index}].tid|"  th:value="${mapS.tid}">
                                            <input  th:name="|contractList[${status.index}].fileName|" type="hidden" th:value="${mapS.fileName}">
                                        </td>
                                    </th:block>
                                    <th:block th:if='${mapS.tid==null}'>
                                        <td></td>
                                    </th:block>
                                    <td th:text="${mapS.name}"></td>
                                    <td th:text="${#dates.format(mapS.effectiveDate, 'yyyy-MM-dd')}"></td>
                                    <td th:text="${#dates.format(mapS.invalidDate, 'yyyy-MM-dd')}"></td>
                                    <td th:text="${#dates.format(mapS.warningDate, 'yyyy-MM-dd')}"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--end-->
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button th:if="${bb == null}" type="button" class="btn btn-sm btn-primary" onclick="submitHandler()">
            <i class="fa fa-check"></i> 保 存
        </button>

        <button type="button" class="btn btn-sm btn-warning" onclick="submitHandler(1)">
            <i class="fa fa-check"></i> 保存并申请
        </button>

        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()">
            <i class="fa fa-reply-all"></i> 关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "tms/margin";
    //保证金附件List
    var marginPicList = [[${marginPicList}]];
    var bb = [[${bb}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapsePic').collapse('show');
        $('#collapseThree').collapse('show');

        for (var i = 0; i < marginPicList.length; i++) {
            var dictValue = marginPicList[i].value;
            var publishFlag = "cmt_" + dictValue;
            var fileType = null;
            if(dictValue == 0){
                //文件类型
                fileType = 'file';
            }
            var picParam = {
                maxFileCount: 0,
                publish: publishFlag,  //用于绑定下一步方法
                fileType: fileType//文件类型
            };
            var tid = "tid" + dictValue;
            var imageId = "image" + dictValue;
            $.file.initAddFiles(imageId, tid, picParam);
        }

        $('[name=marginType]').change()

        if (bb!=null){
            $('#image2Label').css('color', "red").prepend('* ');
        }
    });

    let applyParam = null; // 非bb单时，控制是‘保存’或‘保存+申请’

    //提交
    function submitHandler(applyFlag) {
        if ($('[name=marginType]').val() == '0') {
            var option = $('[name=payMethod]').find('option:selected').text();
            if (option.indexOf('银行转账') >= 0) {
                if (!$('[name=payee]').val().trim() || !$('[name=payeeCard]').val().trim() || !$('[name=payeeBank]').val().trim()) {
                    $.modal.msgError("请补全收款人、银行卡、开户行");
                    return
                }
            }
        }
        applyParam = applyFlag || '';
        let pass = true;
        if (bb != null) {
            if ($("#image2").fileinput('getFilesCount') == 0 && $('#tid2').val() == "") {
                $.modal.msgWarning("请上传“保证金收据”图片")
                pass = false;
            }
        }
        jQuery.unsubscribe("cmt_0");
        jQuery.unsubscribe("cmt_1");
        jQuery.unsubscribe("cmt_2");
        if ($.validate.form() && pass) {
            $.modal.confirm("确认提交吗？", function(){
                $.modal.loading("正在处理中，请稍后...");
                $("#image0").fileinput('upload');
                jQuery.subscribe("cmt_0",cmtFile_1);
                jQuery.subscribe("cmt_1", function(){
                    if ($("#image2").fileinput('getFilesCount') > 0) {
                        jQuery.subscribe("cmt_2", commit);
                        cmtFile_2()
                    } else {
                        commit();
                    }
                });
            })
        }
    }

    function limitMargin(e) {
        $('#marginAmount').attr("min", e.value);
    }

    function cmtFile_1(){
        $("#image1").fileinput('upload');
    }
    function cmtFile_2(){
        $("#image2").fileinput('upload');
    }

    function commit() {
        // console.log($('#form-margin-add').serialize())
        // return
        $('#balaCorp').attr("disabled",false);
        let b = $("[name=marginType]").prop("disabled");
        $("[name=marginType]").prop("disabled", false);
        let data = $('#form-margin-add').serialize();
        $('#balaCorp').attr("disabled",true);
        $("[name=marginType]").prop("disabled", b);
        if (bb == null && applyParam) {
            data = data + "&apply=" + applyParam;
        }
        $.operate.saveTab(prefix + "/add", data);
    }

    /**
     * 基础信息 - 客户名称
     */
    function selectClient() {
        $.modal.open("选择客户", ctx + "tms/margin/related",1050,'',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //清空结算客户信息
            $("#balaName").val("");
            $("#balaCode").val("");
            $("#balaCustomerId").val("");

            //客户id
            $("#customerId").val(rows[0]["customerId"]);
            //客户名称
            $("#custName").val(rows[0]["custName"]);
            //客户简称
            $("#custAbbr").val(rows[0]["custAbbr"]);
            //客户编码
            $("#custCode").val(rows[0]["custCode"]);
            //结算公司
            $("#balaCorpId").val(rows[0]["operateCorp"]);
            $("#balaCorpName").html($.table.selectDictLabel(balaCorp, rows[0]["operateCorp"]));
            //驻场组
            $("#stationDept").val(rows[0]["stationDept"]);
            //驻场组名称
            $("#stationDeptName").val(rows[0]["stationDeptName"]);
            //结算组
            $("#balaDept").val(rows[0]["balaDept"]);
            //运营部
            $("#salesDept").val(rows[0]["salesDept"]);
            //业务员
            $("#psndoc").val(rows[0]["psndoc"]);
            //结算方式
            $("#balaType").val(rows[0]["balaType"]);
            //开票公司
            $("#billingCorp").val(rows[0]["billingCorp"]);
            //app联系人
            $("#appDeliContact").val(rows[0]["appDeliContact"]);
            //app联系方式默认填业务员联系方式
            $("#appDeliMobile").val(rows[0]["appDeliMobile"]);


            //结算公司赋值
            $("#balaCorp").val($("#balaCorpId").val())
            //集团
            $.ajax({
                url: ctx + "group/getGroupByCustomerId",
                type: "post",
                dataType: "json",
                data: {customerId: rows[0]["customerId"]},
                success: function (result) {
                    if (result.code == 0 && result.data!=undefined) {
                        if (result.data != null) {
                            $("#groupName").val(result.data.GROUP_NAME);
                            $("#groupId").val(result.data.GROUP_ID);
                        }
                    }
                }
            });
            //选中完需单独校验
            $("#form-margin-add").validate().element($("#custAbbr"));
            layer.close(index);
        });
    }
    function marginTypeChange(select) {
        if ($(select).val() == '0') {
            $("#accordion3").show()
            $("#accordion3").find(":input").prop("disabled", false)
        } else {
            $("#accordion3").hide()
            $("#accordion3").find(":input").prop("disabled", true)
        }
    }
    function marginAmountChange(e) {
        let bbAmount = $('[name=bbAmount]').length > 0 ? $('[name=bbAmount]').val() : 0;
        let flag = (bbAmount && e.value && parseFloat(e.value) > parseFloat(bbAmount));

        $('#accordion3').find(".error").removeClass('error');
        $('#payee-error').remove();
        $('#payeeCard-error').remove();
        $('#payeeBank-error').remove();
        $('[name=payee]').prop("required", flag);
        $('[name=payeeCard]').prop("required", flag);
        $('[name=payeeBank]').prop("required", flag);
    }
    function changePayMethod(select) {
        if ($(select).val() == '7') {
            if (bb != null) {
                if (!$('[name=payee]').val().trim()) {
                    $('[name=payee]').val(bb.skdw)
                }
                if (!$('[name=payeeCard]').val().trim()) {
                    $('[name=payeeCard]').val(bb.yhzh)
                }
            }
        }
    }
</script>
</body>

</html>