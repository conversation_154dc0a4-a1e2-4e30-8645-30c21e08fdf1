<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('集团信息')"/>
</head>
<body>
<div class="form-content">
    <form id="form-group-edit" class="form-horizontal">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4">集团名称：</label>
                    <div class="col-sm-8">
                        <input name="groupName" id="groupName" th:value="${group.groupName}" class="form-control valid"
                               required  maxlength="100" type="text">
                        <input name="groupId" id="groupId" th:value="${group.groupId}"  type="hidden">
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
</body>
<script th:inline="javascript">

    var prefix = ctx + "group";
    $(function () {
        // 表单验证
        $("#form-group-edit").validate({
            rules: {
                groupName: {
                    required: true
                }
            }
        });
    });

    // 提交回调的方法
    function submitHandler() {

        if ($.validate.form()) {
            $.ajax({
                url: prefix + "/editGroup",
                type: "post",
                data: $('#form-group-edit').serialize(),
                error: function (request) {
                    $.modal.alertError("系统错误");
                },
                success: function (data) {
                    $.operate.successCallback(data);
                }
            })
        }
    }
</script>
</body>

</html>