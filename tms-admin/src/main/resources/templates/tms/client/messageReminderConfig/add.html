<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户应收消息提醒配置：add')"/>
</head>
<body>
<div class="form-content">
    <form id="form-messageReminderConfig-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="customerId" th:value="${customerId}">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">客户名称：<span th:text="${custAbbr}"></span></a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-sm-2 col-md-1 col-lg-1"><span style="color: red">用户：</span></label>
                                <div class="col-sm-6 col-md-9 col-lg-9">
                                    <div class="input-group">
                                        <input class="form-control" type="text" onclick="selectUser()"
                                               placeholder="用户" id="userName" name="userName" readonly>
                                        <input name="userId" id="userId" class="form-control" type="hidden"
                                               aria-required="true">
                                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4" style="color: red">消息类型：</label>
                                <div class="col-sm-8">
                                    <select name="messageType" class="form-control valid" required
                                            th:with="type=${@dict.getType('message_type')}">
                                        <option value=""></option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "client/messageReminderConfig";

    $(function () {
        // 表单验证
        $("#form-userCustomer-add").validate({
            focusCleanup: true
        });
        //展开，折叠
        $('#collapseTwo').collapse('show');
    });

    /**
     * 系统用户
     */
    function selectUser() {
        $.modal.open("选择用户", ctx + "system/user/selectUser/listSys");
    }

    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-messageReminderConfig-add").serializeArray();
            $.operate.saveTab(prefix + "/add", data);
        }
    }
</script>
</body>
</html>