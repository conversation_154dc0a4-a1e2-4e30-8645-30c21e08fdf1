<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户应收消息提醒配置：edit')"/>
</head>
<body>
<div class="form-content">
    <form id="form-messageReminderConfig-edit" class="form-horizontal" novalidate="novalidate">
        <!--消息提醒配置主键-->
        <input type="hidden" th:field="${messageReminderConfig.messageReminderConfigId}">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">基础信息</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4" style="color: red">客户：</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input class="form-control" type="text" onclick="selectCustomer()"
                                               placeholder="选择客户" id="custAbbr" th:value="${messageReminderConfig.params.custName}" readonly required>
                                        <input name="customerId" id="customerId" th:value="${messageReminderConfig.customerId}"
                                               class="form-control" type="hidden"
                                               maxlength="30" aria-required="true">
                                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4" style="color: red">消息类型：</label>
                                <div class="col-sm-8">
                                    <select name="messageType" class="form-control valid" required
                                            th:with="type=${@dict.getType('message_type')}">
                                        <option value=""></option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:field = "${messageReminderConfig.messageType}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "client/messageReminderConfig";

    $(function () {
        // 表单验证
        $("#form-userCustomer-add").validate({
            focusCleanup: true
        });
        //展开，折叠
        $('#collapseTwo').collapse('show');
    });

    /**
     * 客户
     */
    function selectCustomer() {
        $.modal.open("选择客户", ctx + "client/related");
    }

    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-messageReminderConfig-edit").serializeArray();
            $.operate.saveTab(prefix + "/edit", data);
        }
    }
</script>
</body>
</html>