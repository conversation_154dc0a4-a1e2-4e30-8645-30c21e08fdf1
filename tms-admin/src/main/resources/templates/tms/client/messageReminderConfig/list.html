<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保证金管理-列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4"><span>用户名称：</span></label>
                            <div class="col-sm-8">
                                <input name="params[userName]" class="form-control" required type="text" maxlength="20">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">消息类型 ：</label>
                            <div class="col-sm-8">
                                <select name="messageType" class="form-control" th:with="type=${@dict.getType('message_type')}">
                                    <option value="">-- 请选择 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>


            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="addTab()" shiro:hasPermission="tms:messageReminderConfig:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="tms:messageReminderConfig:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "client/messageReminderConfig";

    //消息类型
    var message_type = [[${@dict.getType('message_type')}]];
    //客户id
    var customerId = [[${customerId}]];
    //客户简称
    var custAbbr = [[${custAbbr}]];

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
        var options = {
            url: prefix + "/list?customerId="+customerId,
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            exportUrl: prefix + "/export",
            createUrl: prefix + "/add",
            fixedNumber: 0,
            showToggle: false,
            showColumns: true,
            modalName: "应收消息配置",
            uniqueId: "messageReminderConfigId",
            height: 560,
            clickToSelect: true,
            columns: [{
                checkbox: true
            },
                {
                    title: '用户名称',
                    field: 'params.userName',
                    align: 'left',
                },
                {
                    title: '电话号码',
                    field: 'params.phonenumber',
                    align: 'left',
                },
                {
                    title: '消息类型',
                    field: 'messageType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(message_type, value);
                    }
                },
            ]
        };
        $.table.init(options);
    });

    /**
     * 新增
     */
    function addTab() {
        $.modal.openTab("新增", prefix + "/add?customerId="+customerId+"&custAbbr="+custAbbr);
    }

    /**
     * 修改
     * @param messageReminderConfigId
     */
    function edit(messageReminderConfigId) {
        $.modal.openTab("修改", prefix + "/edit?messageReminderConfigId="+messageReminderConfigId);
    }


</script>
</body>
</html>