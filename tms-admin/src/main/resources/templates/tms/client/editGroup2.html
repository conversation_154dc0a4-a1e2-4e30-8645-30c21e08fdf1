<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('修改客户信息')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<style type="text/css">
    .td td {
        position: relative
    }
    .bg_title{
        font-size: 15px;
        font-weight: bold;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 113px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;
    }
    .fcff{
        color: #ff1f1f;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .tc{
        text-align: center;
    }
    .bor{
        border: 1px #eee solid;
    }
    .over{
        overflow: hidden;

    }
    .fl{
        float: left;
    }
    .ml10{
        margin-left: 10px;
    }
    .table>thead>tr>th{
        background: #eff3f9 !important;
        padding: 0;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }

    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .theme-explorer{
        width: 100%;
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }

    .tdStyle {
       border-left: 1px solid #e7eaec;
        border-right: 1px solid #e7eaec;

    }

</style>
<body>
<div class="form-content">
    <form id="form-client-edit" class="form-horizontal" novalidate="novalidate">

        <input name="id" type="hidden" id="id" th:value="${custFeeMonthBlong.id}"/>

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body" style="padding: 5px 10px 10px;">
                        <div class="bg_title">基础信息</div>
                        <div class="row">
                            <div class="col-md-12 col-sm-12">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        客户名称：</font></label>
                                    <div class="flex_right">
                                        [[${custFeeMonthBlong.custAbbr}]]

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        月份：</font></label>
                                    <div class="flex_right">
                                        [[${custFeeMonthBlong.month}]]

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        运营组：</font></label>
                                    <div class="flex_right">
                                        <input name="salesDeptName" id="salesDeptName" onclick="selectSalesDept()"
                                               class="form-control" type="text" th:value="${custFeeMonthBlong.salesDeptName}"
                                               maxlength="25" autocomplete="off" readonly required>
                                        <input name="salesDept" id="salesDept" th:value="${custFeeMonthBlong.salesDept}" class="form-control" type="hidden">

                                    </div>
                                </div>
                            </div>



                        </div>




                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
        </div>



        <div class="panel panel-default">
            <div class="panel-collapse collapse in" id="collapseThree">
                <div class="panel-body" >
                    <div class="bg_title">目标数据</div>
                    <div class="row" style="padding-left:10px;padding-right:10px;padding-bottom:6px">

                        <div class="col-sm-12 select-table table-striped" >
                            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">

    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: xm-select"/>

<script th:inline="javascript">

    var customerServiceList = [[${customerServiceList}]];



    /*选择业务员*/
    function selectUser() {
        $.modal.open("选择业务员", ctx + "system/user/selectUser/salesUserList", '', '', function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $("#psndocName").val("");
                $("#psndoc").val("");
                $("#psncontact").val("");
                layer.close(index);
                return;
            }

            //业务员
            $("#psndocName").val(rows[0]["userName"]);
            $("#psndoc").val(rows[0]["userId"]);
            $("#psncontact").val(rows[0]["phonenumber"]);
            $("#form-client-edit").validate().element($("#psndocName"));
            layer.close(index);
        });
    }

    $(function () {
        var customerId1 = [[${custFeeMonthBlong.customerId}]];

        var options = {
            url: ctx + "client/custSalesTargetList?customerId="+customerId1,
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            exportUrl: prefix + "/export",
            fixedNumber: 0,
            showToggle: false,
            showColumns: false,
            modalName: "保证金",
            uniqueId: "marginId",
            height: 560,
            clickToSelect: true,
            showFooter:false,
            showSearch:false,
            showToggle:false,
            showRefresh:false,
            columns: [

                {
                    title: '运营部',
                    field: 'salesName',
                    align: 'left',
                },
                {
                    title: '运营组',
                    field: 'salesDeptName',
                    align: 'left',
                },
                {
                    title: '营收目标(元)',
                    field: 'reportTarget',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value == null) {
                            return row.targetAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '调整时间',
                    field: 'corDate',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return value;
                        }else{
                            return row.regDate;
                        }
                    }
                },


                {
                    title: '利润率',
                    field: 'profitRateStr',
                    align: 'right',
                }
                ,    {
                    title: '月份',
                    field: 'yearMonth',
                    align: 'left',
                }

            ]
        };
        $.table.init(options);

        /**
         * 结算公司决定开票公司
         */
        $('#balaCorp').change(function () {
            var balaCorpVal = $(this).val();
            var balaCorpText = $(this).find("option:selected").text();
            $('#billingCorp').val(balaCorpText);
            $('#billingCorpVal').val(balaCorpVal);
        });

        $("#form-client-edit").validate({
            onkeyup: false,
            rules: {
                addresseeContact: {
                    isPhone: true
                },
                psncontact: {
                    isPhone: true
                },
                legalCard: {
                    isIdentity: true
                }, appDeliMobile: {
                    isPhone: true
                },
                custName: {
                    remote: {
                        url: prefix + "/checkCustNameUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "custName": function () {
                                return $.common.trim($("#custName").val());
                            },
                            "customerId": function () {
                                return $.common.trim($("#customerId").val());
                            }
                        },
                        dataFilter: function (data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
                custAbbr: {
                    remote: {
                        url: prefix + "/checkCustAbbrUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "custAbbr": function () {
                                return $.common.trim($("#custAbbr").val());
                            },
                            "customerId": function () {
                                return $.common.trim($("#customerId").val());
                            }
                        },
                        dataFilter: function (data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },

            },
            messages: {
                "phone": {
                    remote: "手机号码已经存在"
                },
                "custName": {
                    remote: "客户名称已存在"
                },
                "custAbbr": {
                    remote: "客户简称已存在"
                }
            }
        });


        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');
        $('#collapseSeven').collapse('show');

        let customerId = [[${client.customerId}]];

        var options = {
            url: ctx + "client/clientNotReceiveList",
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 0,
            clickToSelect: true,
            showFooter: false,
            columns: [{
                checkbox: true
            },
                {
                    title: '运营部',
                    field: 'salesName',
                    align: 'left',

                },
                {
                    title: '运营组',
                    field: 'salesDeptName',
                    align: 'left',

                },

                {
                    title: '单量',
                    field: 'invoiceCnt',
                    align: 'right',
                    formatter: function (value, row, index) {
                        return `<a onclick="jumpRecord('` + row.salesDept + `','` + row.salesId + `')">` + value + `</a>`
                    }
                },
                {
                    title: '未收款金额(元)',
                    field: 'ungotAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                }


            ]
        };

        $.table.init(options);


        for (var i = 0; i < customerServiceList.length; i++) {
            let serviceType = customerServiceList[i].serviceType;

            initBsSuggest(i)

            initXmSelect('serviceType_' + i, [[${@dict.getType('trans_code')}]]);
            xmSelect.get('#serviceType_' + i)[0].setValue(serviceType.split(','))
            $("#serviceTypeValue_" + i).val(serviceType);


        }


    });

    var picType = [[${picList}]];


    var prefix = ctx + "client";


    /*提交表单*/
    function submitHandler() {
        if ($.validate.form()) {
            commit();
        }
    }

    function uploadPic(dictValue) {
        $("#image" + dictValue).fileinput('upload');
        flag = "done" + dictValue;
    }

    /**
     * 保存客户的方法
     */
    function commit() {
        var data = $('#form-client-edit').serializeArray()
        $.operate.saveTab(prefix + "/saveEditGroup2", data);
    }

    /**
     * 结算客户的选择框
     */
    function selectRelatedClient(relatedIndex) {
        $.modal.open("选择结算客户", prefix + "/related?relatedIndex=" + relatedIndex);
    }

    /**
     * 设置开户行名称
     */
    function setBank() {
        $("#bank").val($("#bankId option:selected").text());
    }

    /**
     * 货品选择框
     */
    function selectGoods() {

        $.modal.open("选择货品", prefix + "/getGoods");
    }


    function submit(index, layero) {
        $('#goodsName').removeAttr("disabled");
        var tree = layero.find("iframe")[0].contentWindow.$._tree;
        if ($.tree.notAllowParents(tree)) {
            var body = layer.getChildFrame('body', index);
            $("#treeId").val(body.find('#treeId').val());
            $("#treeName").val(body.find('#treeName').val());
            $('#goodsTypeId').val(body.find('#treeId').val());
            layer.close(index);
        }
    }


    /* 删除指定表格行 */
    function removeRowFour(obj) {
        $("#infoTabFour tbody").find(obj).closest("tr").remove();
    }

    var lineIndex = $("#clientLineSize").val() - 0;


    function getSplitAmount() {
        let salesDept = $("#salesDept").val();
        let salesId = $("#salesId").val();

    }

    function updateGroup() {
        let salesDeptOld = $.table.selectColumns("salesDept");
        let salesIdOld = $.table.selectColumns("salesId");

        if (salesDeptOld.length != 1) {
            $.modal.alertWarning("请勾选一条单据");
            return;
        }

        let salesDept = $("#salesDept").val();
        let salesId = $("#salesId").val();
        let customerId = $("#customerId").val();

        let salesDeptStr = $("#salesDept option:selected").text();
        let salesIdStr = $("#salesId option:selected").text();

        let data = {
            "customerId": customerId,
            "salesDeptOld": salesDeptOld.join(),
            "salesIdOld": salesIdOld.join(),
            "salesDept": salesDept,
            "salesId": salesId
        }
        console.log(data)
        $.modal.confirm("确认要调整选中单据运营组为" + salesDeptStr + "，运营部为" + salesIdStr + "吗？", function () {
            $.operate.saveModal(prefix + "/updateInvoiceGroup", data, function () {
                $.table.refresh();
            });
        });
    }

    function jumpRecord(salesDept, salesId) {
        let customerId = $("#customerId").val();
        if (salesId == 'null') {
            salesId = '';
        }
        $.modal.open("明细", prefix + "/jumpInvoiceRecord?salesDept=" + salesDept + "&salesId=" + salesId + "&customerId=" + customerId, 800, 700);
    }

    var kfLineIndex = customerServiceList.length;
    let transCodeList = [[${@dict.getType('trans_code')}]]
    // var transCodeListHtml = '';
    // for (var i = 0; i < transCodeList.length; i++) {
    //     transCodeListHtml += '<option  value=' + transCodeList[i].dictValue + ' >' + transCodeList[i].dictLabel + '</option>'
    // }

    /* 新增表格行 */
    function insertRowKF() {
        kfLineIndex += 1;

        let ind = kfLineIndex
        var trTtml = `
                <tr>
                    <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRowKF(this,${kfLineIndex})" title="删除选择行"></a></td>
                    <td>
                        <div class="input-group">
                            <input class="form-control" type="text" id="userName_${kfLineIndex}" name="userName_${kfLineIndex}">
                            <input name="customerServiceVOList[${kfLineIndex}].serviceId" id="serviceId_${kfLineIndex}" class="form-control" type="hidden" aria-required="true">
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                </ul>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div id="serviceType_${kfLineIndex}"></div>
                        <input name="customerServiceVOList[${kfLineIndex}].serviceType" id="serviceTypeValue_${kfLineIndex}" type="hidden">
                    </td>

                </tr>
        `


        $("#infoTabKF").append(trTtml);
        initXmSelect(`serviceType_${kfLineIndex}`, [[${@dict.getType('trans_code')}]]);

        initBsSuggest(ind)
    }


    /* 删除指定表格行 */
    function removeRowKF(obj, index) {
        var $tbody = $('#infoTabKF');
        var $rows = $tbody.children('tr'); // Only select direct child tr elements
        // if ($rows.length === 1) {
        //     // Clear the data in the input fields and select element
        //     var $inputs = $rows.find('input');
        //     var $selects = $rows.find('select');
        //     $inputs.val('');
        //     $selects.prop('selectedIndex', 0);
        // } else {
            $(obj).closest('tr').remove();

        // }
    }

    function initBsSuggest(ind) {
        $(`#userName_${ind}`).bsSuggest('init', {
            url: ctx + "system/user/findUserInfo?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "userName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["userName", "deptName", "phonenumber"],
            effectiveFieldsAlias: {"userName": "用户名", "deptName": "部门", "phonenumber": "手机号码"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#userName_${ind}`).val(data.userName);
            $(`#serviceId_${ind}`).val(data.userId);
        })

    }

    function initXmSelect(id, dataList) {
        var demo4 = xmSelect.render({
            el: '#' + id,
            language: 'zn',
            size: 'mini',
            model: {
                label: {
                    type: 'block',
                    block: {
                        //最大显示数量, 0:不限制
                        showCount: 5,
                        //是否显示删除图标
                        showIcon: true,
                    }
                }
            },
            toolbar: {
                show: true,
            },
            autoRow: true,
            prop: {
                name: 'dictLabel',
                value: 'dictValue',
            },
            on: function (data) {
                //arr:  当前多选已选中的数据
                var arr = data.arr;
                let reverse = id.split('_').reverse();
                $("#serviceTypeValue_" + reverse[0]).val(arr.map(obj => obj.dictValue).join(","));
            },
            data: dataList
        })
    }

    function selectSalesDept() {
        layer.open({
            type: 2,
            area: ['50%', '80%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "选择运营组",
            content: ctx + "system/dept/opsGroupTree",
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero){
                //获取整行
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                $(`#salesDeptName`).val(rows[0].deptName);
                $(`#salesDept`).val(rows[0].deptId);
                layer.close(index);
            },
            cancel: function(index) {
                return true;
            }
        });
    }
</script>
</body>

</html>