<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('合同价版本')" />
</head>

<style>
    .form-content {
        padding: 15px;
    }
    .layui-table-tool {
        background-color: #ffffff;
    }
    .search-box {
        margin-bottom: 15px;
    }
    .layui-table-page {
        text-align: right;
    }
    .layui-btn-sm {
        padding: 0 10px;
        height: 28px;
        line-height: 28px;
    }

    /* 表格内容自动换行 */
    .layui-table-cell {
        height: auto;
        overflow: visible;
        text-overflow: inherit;
        white-space: normal;
        word-break: break-all;
        padding: 5px 10px;
    }

</style>

<body>
<div class="form-content">
    <!-- 按钮区 -->
    <div style="margin-bottom: 10px">
        <a class="layui-btn layui-btn-primary layui-btn-sm" onclick="addVersion()">创建新版本</a>
    </div>

    <!-- 表格区域 -->
    <table id="versionTable" lay-filter="versionTable"></table>

    <!-- 添加有效期编辑模板 -->
    <script type="text/html" id="validityBar">
        {{# if(d.validDate) { }}
        <div class="validity-display-{{d.id}}">
            <span>{{d.validDate}}</span>
            <a class="layui-btn layui-btn-xs" onclick="editValidity('{{d.id}}', '{{d.validDate}}')">修改</a>
        </div>
        <div class="validity-edit-{{d.id}}" style="display:none">
            <input type="text" class="layui-input validity-input-{{d.id}}" style="width:120px;display:inline-block">
            <a class="layui-btn layui-btn-xs" onclick="saveValidity('{{d.id}}')">保存</a>
        </div>
        {{# } else { }}
        <div class="validity-display-{{d.id}}">
            <span>暂无</span>
            <a class="layui-btn layui-btn-xs" onclick="editValidity('{{d.id}}', '')">添加</a>
        </div>
        <div class="validity-edit-{{d.id}}" style="display:none">
            <input type="text" class="layui-input validity-input-{{d.id}}" style="width:120px;display:inline-block">
            <a class="layui-btn layui-btn-xs" onclick="saveValidity('{{d.id}}')">保存</a>
        </div>
        {{# } }}
    </script>

    <!-- 操作列模板 -->
    <script type="text/html" id="operationBar">
        {{# if(d.isLatest === 1) { }}
        <span style="color: #16baaa;">当前版本</span>
        {{# } else { }}
        <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="switch">切换到此版本</a>
        {{# } }}
    </script>
</div>

<th:block th:include="include :: footer" />
<script th:inline="javascript">
    let customerId = [[${customerId}]]

    layui.use(['table', 'form', 'laydate'], function(){
        var table = layui.table,
            form = layui.form,
            laydate = layui.laydate,
            $ = layui.$;

        // 初始化日期选择器
        laydate.render({
            elem: '#createTime',
            type: 'datetime'
        });

        // 初始化表格
        table.render({
            elem: '#versionTable',
            url: ctx + 'contractpcVersion/version_list',  // 替换为实际的数据接口
            method: 'post',
            where: {
                customerId: customerId,
            },
            page: true,
            limits: [10, 15, 20, 25, 30],
            limit: 10,
            height: 'full-70',
            // cellMinWidth: 120,
            cols: [
                [
                {field: 'versionNum', title: '版本号', width: '15%'},
                {field: 'note', title: '备注', width: '15%'},
                {field: 'regUserName', title: '创建人', width: '25%',templet:function (d) {
                        return `<div>${d.regUserName}</div><div>${d.regDate}</div>`
                    }},
                {field: 'validity', title: '有效期', width: '25%', templet: '#validityBar'},

                {fixed: 'right', title: '操作', toolbar: '#operationBar', width: '20%', align: 'center'}
            ]
            ],
            response: {
                statusCode: 0
            },
            parseData: function(res){
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.total,
                    "data": res.rows
                };
            }
        });

        // 监听搜索表单提交
        form.on('submit(searchForm)', function(data){
            table.reload('versionTable', {
                where: data.field,
                page: {
                    curr: 1
                }
            });
            return false;
        });

        // 监听表格工具条
        table.on('tool(versionTable)', function(obj){
            var data = obj.data;
            if(obj.event === 'switch'){
                switchVersion(data);
            } else if(obj.event === 'view'){

            }
        });

        // 切换版本
        function switchVersion(data) {
            layer.confirm('确认要切换到版本 ' + data.versionNum + ' 吗？', {
                icon: 3,
                title: '提示'
            }, function(index){
                $.ajax({
                    url: ctx + 'contractpcVersion/switchVersion',
                    type: 'POST',
                    data: {
                        versionId: data.id,
                        customerId: customerId,
                    },
                    beforeSend: function(){
                        $.modal.loading("正在切换版本，请稍候...");
                    },
                    success: function(res){
                        if(res.code === 0){
                            $.modal.alertSuccess("版本切换成功");
                            // 刷新父页面表格
                            parent.$.table.refresh();
                            // 关闭当前窗口
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        } else {
                            $.modal.alertError(res.msg || "版本切换失败");
                        }
                    },
                    error: function(){
                        $.modal.alertError("版本切换失败");
                    },
                    complete: function(){
                        $.modal.closeLoading();
                    }
                });
                layer.close(index);
            });
        }
    });

    function addVersion() {
        layer.open({
            type: 2,
            title: '创建版本',
            area: ['70%', '80%'],
            content: ctx + `contractpcVersion/add?customerId=${customerId}`,
            btn: ['保存并设为最新版本', '关闭'],
            yes: function(index, layero) {
                // 调用iframe中的保存方法
                var iframeWindow = window[layero.find('iframe')[0]['name']];
                iframeWindow.submitForm(function(success) {
                    if (success) {
                        layui.table.reload('versionTable');  // 刷新表格
                        layer.close(index);
                    }
                });
            },
            btn2: function (index) {
                layer.close(index);
            }
        });
    }

    // 有效期相关函数
    function editValidity(id, currentValue) {
        $('.validity-display-' + id).hide();
        $('.validity-edit-' + id).show();

        // 初始化日期选择器
        layui.laydate.render({
            elem: '.validity-input-' + id,
            type: 'datetime',
            value: currentValue
        });
    }

    function saveValidity(id) {
        var value = $('.validity-input-' + id).val();

        $.ajax({
            url: ctx + 'contractpcVersion/updateValidity',
            type: 'POST',
            data: {
                versionId: id,
                validity: value
            },
            success: function(res) {
                if(res.code === 0) {
                    layer.msg('保存成功');
                    layui.table.reload('versionTable');
                } else {
                    layer.msg(res.msg || '保存失败');
                }
            },
            error: function() {
                layer.msg('保存失败');
            }
        });
    }

</script>
</body>
</html>