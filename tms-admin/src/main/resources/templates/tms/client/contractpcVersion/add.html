<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('新添合同价版本')" />
</head>
<style>
    .form-content {
        padding: 20px;
        max-width: 800px;
        margin: 0 auto;
    }
    .layui-form-item {
        margin-bottom: 15px;
    }
    .layui-input {
        width: 100%;
    }
</style>

<body>
<div class="form-content">
    <form class="layui-form" lay-filter="versionForm">
        <!-- 版本号 -->
        <div class="layui-form-item">
            <label class="layui-form-label">版本号</label>
            <div class="layui-input-block">
                <input type="text" th:value="${versionNo}" class="layui-input" readonly>
            </div>
        </div>

        <!-- 备注 -->
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="note" placeholder="请输入备注信息" class="layui-textarea"></textarea>
            </div>
        </div>

        <!-- 隐藏字段 -->
        <input type="hidden" name="customerId" th:value="${customerId}">
    </form>
</div>

<th:block th:include="include :: footer" />
<script th:inline="javascript">
    let customerId = [[${customerId}]]

    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        form.render();
    });

    // 供父窗口调用的保存方法
    function submitForm(callback) {
        var formData = layui.form.val('versionForm');

        $.ajax({
            url: ctx + 'contractpcVersion/add',
            type: 'POST',
            data: formData,
            beforeSend: function(){
                $.modal.loading("正在保存，请稍候...");
            },
            success: function(res){
                if(res.code === 0){
                    $.modal.msgSuccess("保存成功");
                    if (typeof callback === 'function') {
                        callback(true);
                    }
                } else {
                    $.modal.msgError(res.msg || "保存失败");
                    if (typeof callback === 'function') {
                        callback(false);
                    }
                }
            },
            error: function(){
                $.modal.msgError("保存失败");
                if (typeof callback === 'function') {
                    callback(false);
                }
            },
            complete: function(){
                $.modal.closeLoading();
            }
        });
    }
</script>
</body>
</html>