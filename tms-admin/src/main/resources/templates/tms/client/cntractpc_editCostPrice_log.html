<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('成本价修改记录')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/css/bootstrap-editable.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>
</head>
<style>
    .custom-cell-background-red {
        background-color: rgb(255 0 0 / 43%) !important; /* 自定义的背景颜色 */
    }
    .custom-cell-background-red a {
        color: #FFFFFF;
    }

    .custom-cell-background-green {
        background-color: #8DBF8B !important; /* 自定义的背景颜色 */
    }
    .custom-cell-background-green a {
        color: #FFFFFF;
    }

    /* 表格滚动条 */
    .bootstrap-table {
        overflow-x: auto;
    }
    .bootstrap-table .fixed-table-container,
    .bootstrap-table .fixed-table-body {
        overflow: visible !important; /* 强制覆盖内部滚动 */
    }

    /* 彻底修复表头文字重叠问题 */
    .bootstrap-table .fixed-table-container .table thead th {
        position: relative;
        text-shadow: none !important;
        font-weight: 500;
        color: #333;
        background-color: #f5f7fa;
    }

    .bootstrap-table .fixed-table-container .table thead th .th-inner {
        padding: 8px;
        line-height: 1.5;
        vertical-align: middle;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
    }

    /* 防止表头内容重叠 */
    .bootstrap-table .fixed-table-container .table thead th .sortable {
        background-image: none;
        position: relative;
    }

    /* 确保表头只有一层文字 */
    .bootstrap-table .fixed-table-container .table thead th:before,
    .bootstrap-table .fixed-table-container .table thead th:after {
        content: none !important;
    }

    /* 添加工具栏与表格之间的间距 */
    #toolbar {
        margin-bottom: 10px;
    }

    /* 美化新添按钮 */
    #toolbar .btn-info {
        margin-right: 5px;
        padding: 5px 15px;
        transition: all 0.3s;
    }

    #toolbar .btn-info:hover {
        background-color: #17a2b8;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    .lf{
        margin-left: 10px;
        background-color: #ffffff;
        border-radius: 3px;
        cursor:pointer;
    }

    .label-success{
        color: #1ab394;
        background-color: transparent;
        border: 1px solid #1ab394;
    }
    .label-primary{
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }
    .label-warning{
        color: #f8ac59;
        background-color: transparent;
        border: 1px solid #f8ac59;
    }

    .customer-stats {
        font-size: 16px;
    }

    .customer-stats span {
        margin-left: 10px;
        font-weight: 550;
    }

    .customer-stats span:nth-child(2) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
        color: #FF6C00;
    }
    .customer-stats span:nth-child(3) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
    }

    .customer-stats span:nth-child(4) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
        color: #0d62bb;
    }

    .table-light {
        background-color: #f8f9fa; /* 设置背景色为浅灰色 */
    }

    #buttons-toolbar-container {
        clear: both;
        display: block;
        width: 100%;
    }

    #toolbar {
        margin-bottom: 10px;
        display: inline-block;
    }

    .select-table {
        clear: both;
    }

    /* 确保表格在工具栏下方正确显示 */
    .bootstrap-table {
        clear: both;
        margin-top: 10px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div >
            <form id="log-form" class="form-horizontal">
                <input type="hidden" id="contractpcId" name="contractpcId" th:value="${custContractpcId}">
            </form>
        </div>

        <div class="col-sm-12 select-table">
<!--            <div id="buttons-toolbar-container" style="margin-bottom: 10px;">-->
<!--                <span id="buttons-toolbar"></span>-->
<!--            </div>-->

            <table id="bootstrap-table"
                   data-buttons-toolbar="#buttons-toolbar"
                   class="table table-striped table-responsive table-bordered table-hover" >
            </table>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/tableExport.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/js/bootstrap-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/editable/bootstrap-table-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/bootstrap-table-export.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>
<script th:src="@{/ajax/libs/layui/layui.js}"></script>

<script th:inline="javascript">
    var prefix = ctx + "client/cust_cntractpc";
    var contractpcId = $("#contractpcId").val();
    var contractpcSectionId = $("#contractpcSectionId").val();
    var billing_type = [[${@dict.getType('billing_type')}]];//开票类型

    $(function () {
        let options = initOptions();
        $.table.init(options);
    });

    function initOptions() {
        return {
            url: ctx + "client/cust_cntractpc/editCostPrice/log",
            queryParams: function(params) {
                var search = $.table.queryParams(params);
                search.contractpcId = contractpcId;
                search.contractpcSectionId = contractpcSectionId;
                return search;
            },
            uniqueId: "id",
            showToggle:false,
            showColumns:false,
            showSearch:false,
            pagination:false,
            // showRefresh:false,
            modalName: "成本价修改记录",
            height: 460,
            clickToSelect: true,
            // stickyHeader: true,  // 启用固定表头功能
            // stickyHeaderOffsetY: 0,  // 可选，调整固定表头距离顶部的距离
            columns: [
                {
                    title: '序号',
                    field: '',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    title: '修改时间',
                    valign: 'middle',
                    align: 'center',
                    field: 'regDate',
                    // formatter: function(value, row, index) {
                    //     return $.common.dateFormat(value, "yyyy-MM-dd HH:mm:ss");
                    // }
                },
                {
                    title: '修改人',
                    align: 'center',
                    valign: 'middle',
                    field: 'regUserName',
                },
                {
                    title: '是否区间',
                    align: 'center',
                    field: 'ifSection',
                    formatter: function(value, row, index) {
                        return value === '0' ? '否' : row.sectionRangeText;
                    }
                },
                {
                    title: '原成本价',
                    align: 'right',
                    field: 'costPriceOld',
                    formatter: function(value, row, index) {
                        if (value === null || value === undefined) {
                            return '-';
                        }
                        return value.toFixed(2);
                    }
                },
                {
                    title: '新成本价',
                    align: 'right',
                    field: 'costPrice',
                    formatter: function(value, row, index) {
                        if (value === null || value === undefined) {
                            return '-';
                        }
                        return value.toFixed(2);
                    }
                },
                {
                    title: '原成本票点',
                    align: 'center',
                    valign: 'middle',
                    field: 'costBillingTypeOld',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(billing_type, value);
                    }
                },
                {
                    title: '新本票点',
                    align: 'center',
                    valign: 'middle',
                    field: 'costBillingType',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(billing_type, value);
                    }
                },
                {
                    title: '变动金额',
                    align: 'right',
                    field: '',
                    formatter: function(value, row, index) {
                        if (row.costPrice === null || row.costPriceOld === null) {
                            return '-';
                        }
                        var diff = row.costPrice - row.costPriceOld;
                        var diffStr = diff.toFixed(2);
                        if (diff > 0) {
                            return '<span style="color:red">+' + diffStr + '</span>';
                        } else if (diff < 0) {
                            return '<span style="color:green">' + diffStr + '</span>';
                        } else {
                            return '0.00';
                        }
                    }
                }
            ]
        };
    }
</script>

</body>
</html>