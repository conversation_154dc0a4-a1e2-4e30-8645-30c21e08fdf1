<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户详情')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: galpop-css" />
</head>
<style>
    .bg_title{
        font-size: 15px;
        font-weight: bold;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 120px;
        line-height: 20px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .fw{
        font-weight: bold;
    }
    .f16{
        font-size: 16px;
    }
    .btn-success {
        color: #fff !important;
        background-color: #5cb85c !important;
        border-color: #4cae4c !important;
    }
    .btn-success:hover,
    .btn-success:focus,
    .btn-success:active,
    .btn-success.active,
    .open .dropdown-toggle.btn-success {
        color: #fff !important;
        background-color: #47a447 !important;
        border-color: #398439 !important;
    }
    .bor1{
        border-right: 1px #eee solid;
    }

    .good{
        display: inline-block;
        padding: 10px 10px;
        border: 1px #eee solid;
    }
    .bg_gr{
        background: #f8f8f8 !important;
    }
    .borl0{
        border-left: 0px #eee solid;
    }
    .company{
        height: 30px;
        line-height: 30px;
        border-radius: 10px;
        border: 1px #eee solid;
        width: 80px;
        text-align: center;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 10px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-user-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
<!--                <div class="panel-heading">-->
<!--                    <h5 class="panel-title">-->
<!--                        <a data-toggle="collapse" data-parent="#accordion"-->
<!--                           href="tabs_panels.html#collapseOne">基础信息</a>-->
<!--                    </h5>-->
<!--                </div>-->
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="bg_title">基础信息</div>
                        <div class="mt10">
                            <div class="over">
                                <div class="fl mt10">
                                    <div class="">[[${client.custCode}]]</div>
                                    <div class="fw">[[${client.custName}]]/[[${client.custAbbr}]]</div>
                                </div>
                                <div class="fl  over ml20" style="border-left: 1px #eee solid;padding: 0 15px 10px 15px;line-height: 30px">
                                    <div class="fl f16 fw" th:if="${client.operateCorp!= null}">
                                        <span class="form-control-static" th:text="${@dict.getLabel('bala_corp',client.operateCorp)}"></span>
                                    </div>
                                    <div class="fl company ml10">结算公司</div>
                                </div>

                                <div class="fl  over ml20" style="border-left: 1px #eee solid;padding: 0 15px 10px 15px;line-height: 30px">
                                    <div class="fl f16 fw" th:if="${client.balaCorp!= null}">
                                        <span class="form-control-static" th:text="${@dict.getLabel('bala_corp',client.balaCorp)}"></span>
                                    </div>
                                    <div class="fl company ml10">运营公司</div>
                                </div>

                            </div>
                        </div>
                        <div style="position: absolute;top: 20px;right: 30px;width: 80px;height: 65px" th:if="${client.isEnabled == 0}">
                            <img th:src="@{/img/qy.png}" style="width: 80px;height: 65px">
                        </div>
                        <div style="position: absolute;top: 20px;right: 30px;width: 80px;height: 65px" th:if="${client.isEnabled == 1}">
                            <img th:src="@{/img/bqy.png}" style="width: 80px;height: 65px">
                        </div>
                        <!--基础信息 begin-->
                        <input name="deptId" type="hidden" id="treeId">
                        <div class="row mt20">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">管理部/运营部：</label>
                                    <div class="flex_right">
                                        [[${mgmtDeptName}]]/[[${opsDeptName}]]
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">运营组：</label>
                                    <div class="flex_right">
                                        [[${salesDept.deptName}]]
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">结算组：</label>
                                    <div class="flex_right">
                                        [[${balaDept.deptName}]]
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">驻场组：</label>
                                    <div class="flex_right">
                                        [[${stationDept.deptName}]]
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">客户集团：</label>
                                    <div class="flex_right">
                                        [[${group.groupName}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">客户级别：</label>
                                    <div class="flex_right" th:if="${client.customerLevel != null}" th:text="${@dict.getLabel('customer_level',client.customerLevel)}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">客户性质：</label>
                                    <div class="flex_right">
                                        <span th:if="${client.customerType == 1}">企业用户</span>
                                        <span th:if="${client.customerType == 2}">个人用户</span>
                                    </div>
                                </div>
                            </div>


                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">证件号：</label>
                                    <div class="flex_right">
                                        [[${client.businesslicense}]]
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">客户状态：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <label class="toggle-switch switch-solid">-->
<!--                                            <input type="checkbox" disabled id="isEnabled" th:checked="${client.isEnabled == 0}">-->
<!--                                            <span></span>-->
<!--                                        </label>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">企业性质：</label>
                                    <div class="flex_right">
                                        <span th:text="${@dict.getLabel('enterprise_nature',client.enterpriseNature)}"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">客户来源：</label>
                                    <div class="flex_right">
                                        <span th:if="${client.customerSource == 1}">系统创建</span>
                                        <span th:if="${client.customerSource == 2}">外包业务</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">启用合同价计费：</label>
                                    <div class="flex_right">
                                        <span th:if="${client.enableContractPrice == 1}">不启用</span>
                                        <span th:if="${client.enableContractPrice == 2}">启用</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">APP姓名/电话：</label>
                                    <div class="flex_right">
                                        [[${client.appDeliContact}]]/[[${client.appDeliMobile}]]
                                    </div>
                                </div>
                            </div>
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">APP联系人手机：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        [[${client.appDeliMobile}]]-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">引荐人：</label>
                                    <div class="flex_right">
                                        [[${client.referrer}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">标的金额：</label>
                                    <div class="flex_right">
                                        [[${client.subjectAmount}]]
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">地址：</label>
                                    <div class="flex_right">
                                        [[${client.provinceName}]]/[[${client.cityName}]]/[[${client.areaName}]]/[[${client.address}]]
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">开票类型：</label>
                                    <div class="flex_right" th:each="dict : ${@dict.getType('billing_type')}" th:if="${dict.dictValue == client.billingType}">
                                        [[${dict.dictLabel}]]
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">联系人：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <div class="input-group">-->
<!--                                            [[${client.contact}]]/[[${client.phone}]]-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">联系人职位：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        [[${client.contactPost}]]-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">联系人邮箱：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        [[${client.email}]]-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">备注：</label>
                                    <div class="flex_right">
                                        [[${client.memo}]]
                                    </div>
                                </div>
                            </div>


<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">业务员姓名/电话：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        [[${client.psndocName}]]/[[${client.psncontact}]]-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">客户编码：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        [[${client.custCode}]]-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">客户名称：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        [[${client.custName}]]-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">客户简称：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        [[${client.custAbbr}]]-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->



                        </div>
<!--                        <div class="row">-->





<!--&lt;!&ndash;                            <div class="col-md-3 col-sm-6">&ndash;&gt;-->
<!--&lt;!&ndash;                                <div class="flex">&ndash;&gt;-->
<!--&lt;!&ndash;                                    <label class="flex_left">业务员联系方式：</label>&ndash;&gt;-->
<!--&lt;!&ndash;                                    <div class="flex_right">&ndash;&gt;-->
<!--&lt;!&ndash;                                        [[${client.psncontact}]]&ndash;&gt;-->
<!--&lt;!&ndash;                                    </div>&ndash;&gt;-->
<!--&lt;!&ndash;                                </div>&ndash;&gt;-->
<!--&lt;!&ndash;                            </div>&ndash;&gt;-->

<!--                        </div>-->
                        <div class="row">




                            <!--<div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">客户类型：</label>
                                    <div class="col-sm-7" th:if="${client.custType!= null}">
                                        <span class="form-control-static" th:text="${@dict.getLabel('cust_Type',client.custType)}"></span>
                                    </div>
                                </div>
                            </div>-->


<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">税率：</label>-->
<!--                                    <div class="flex_right" th:text="${client.tariff}">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                        </div>


<!--                        <div class="row">-->
<!--                            <div class="col-md-3">-->
<!--                                <div class="form-group" th:each="pic:${custPicPath}"-->
<!--                                     th:if="${pic.filePath!=null}">-->


<!--                                    <label  class="col-sm-5" th:each="picEnum:${picEnum}" th:if="${pic.picType==picEnum.value}"-->
<!--                                    th:text="${picEnum.context}+ ："></label>-->

<!--                                <div class="col-sm-7" >-->
<!--                                    <div style="display:inline-block" >-->
<!--                                        <img style="width:70px; height:50px" modal="zoomImg" th:src="@{${pic.filePath}}" alt="图片加载失败"/>-->
<!--                                    </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                        </div>-->

                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
<!--            <div class="panel-heading">-->
<!--                <h4 class="panel-title">-->
<!--                    <a data-toggle="collapse" data-parent="#accordion"-->
<!--                       href="tabs_panels.html#collapseTwo">联系方式</a>-->
<!--                </h4>-->
<!--            </div>-->
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="bg_title">系统配置信息</div>
                    <!--联系方式 start-->
                    <div class="row mt10">
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left" style="width: 140px">是否启用合同计价费：</label>
                                <div class="flex_right">
                                    <span th:if="${client.enableContractPrice == 1}">否</span>
                                    <span th:if="${client.enableContractPrice == 2}">是</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left" style="width: 140px">是否设置一般指导价：</label>
                                <div class="flex_right">
                                    <span th:if="${client.crtGuidePrice == 0}">否</span>
                                    <span th:if="${client.crtGuidePrice == 1}">是</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left" style="width: 140px">是否设置特殊指导价：</label>
                                <div class="flex_right">
                                    <span th:if="${client.isSpecialReferencePrice == 0}">否</span>
                                    <span th:if="${client.isSpecialReferencePrice == 1}">是</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left" style="width: 140px">是否需要回单：</label>
                                <div class="flex_right">
                                    <span th:if="${client.isNeedReceipt == 0}">否</span>
                                    <span th:if="${client.isNeedReceipt == 1}">是</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left" style="width: 140px">是否跟踪：</label>
                                <div class="flex_right">
                                    <span th:if="${client.isNeedTrace == 0}">否</span>
                                    <span th:if="${client.isNeedTrace == 1}">是</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left" style="width: 140px">装卸计价：</label>
                                <div class="flex_right">
                                    <span th:if="${client.handlingChargesType == '0'}">按件</span>
                                    <span th:if="${client.handlingChargesType == '1'}">按方</span>
                                    <span th:if="${client.handlingChargesType == '2'}">按吨</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left" style="width: 140px">装卸单价：</label>
                                <div class="flex_right">
                                    [[${client.handlingCharges}]]
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left" style="width: 140px">调整配置额：</label>
                                <div class="flex_right">
                                    [[${client.adjustment}]]
                                </div>
                            </div>
                        </div>
                    </div>
<!--                    <div class="row">-->
<!--                        <div class="col-md-3 col-sm-6">-->
<!--                            <div class="flex">-->
<!--                                <label class="flex_left" style="width: 160px">是否锁定第三方费用配置：</label>-->
<!--                                <div class="flex_right">-->
<!--                                    <span th:if="${client.isLockOtherFee == 0}">否</span>-->
<!--                                    <span th:if="${client.isLockOtherFee == 1}">是</span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                    <!--联系方式 end-->
                </div>
            </div>
        </div>
        <!--开票信息 start-->
        <div class="panel panel-default">
<!--            <div class="panel-heading">-->
<!--                <h4 class="panel-title">-->
<!--                    <a data-toggle="collapse" data-parent="#accordion"-->
<!--                       href="tabs_panels.html#collapseThree">开票信息</a>-->
<!--                </h4>-->
<!--            </div>-->
            <div class="panel-collapse collapse in" id="collapseThree">
                <div class="panel-body">
                    <div class="over">
                        <div class="bg_title fl">开票信息</div>

                    </div>
                    <div class="row mt10">
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">对账期：</label>
                                <div class="flex_right">
                                    [[${client.paymentDays}]]
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">申请开票期：</label>
                                <div class="flex_right">
                                    [[${client.invoiceDays}]]
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">合同账期：</label>
                                <div class="flex_right">
                                    [[${client.collectionDays}]]
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">特殊日期：</label>
                                <div class="flex_right">
                                    [[${client.specialDate}]]
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="fixed-table-body" style="margin: 10px -5px;">
                        <table id="infoTabThree" class="table table-bordered" >

                            <thead>
                            <tr>
                                <th style="width: 15%;">开票公司</th>
                                <th style="width: 15%;">发票抬头</th>
                                <th style="width: 15%;">纳税识别号</th>
                                <th style="width: 15%;">开户银行</th>
                                <th style="width: 10%;">开票类型</th>
                                <th style="width: 15%;">开户账号</th>
                                <th style="width: 15%;">地址及电话</th>

                            </tr>

                            </thead>
                            <tbody>

                            <tr th:each="custBillingList,stat: ${custBillings}">
                                <input type="hidden" id="custBillingSize" th:value="${stat.size}">

                                <td style="text-align: left" th:text="${@dict.getLabel('bala_corp',custBillingList.billingCorp)}"></td>
                                <td style="text-align: left" th:text="${custBillingList.billingPayable}"></td>
                                <td style="text-align: left" th:text="${custBillingList.taxIdentify}"></td>
                                <td style="text-align: left" th:text="${custBillingList.bank}"></td>
                                <td style="text-align: left" th:if="${custBillingList.billingType} == null"></td>
                                <td style="text-align: left" th:if="${custBillingList.billingType} != null "
                                    th:text="${@dict.getLabel('billing_type',custBillingList.billingType)}">
                                </td>
                                <td style="text-align: left" th:text="${custBillingList.bankAccount}"></td>
                                <td style="text-align: left" th:text="${custBillingList.addressPhone}"></td>
<!--                                <td style="text-align: left" >-->
<!--                                    <div th:text="${custBillingList.addressee}"></div>-->
<!--                                    <div th:text="${custBillingList.addresseeContact}"></div>-->
<!--                                </td>-->
<!--&lt;!&ndash;                                <td style="text-align: left" th:text="${custBillingList.addresseeContact}"></td>&ndash;&gt;-->
<!--                                <td style="text-align: left" th:text="${custBillingList.addresseeAddr}"></td>-->

                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>

        <div class="panel panel-default">
<!--            <div class="panel-heading">-->
<!--                <h4 class="panel-title">-->
<!--                    <a data-toggle="collapse" data-parent="#accordion"-->
<!--                       href="tabs_panels.html#collapseSix">货品信息</a>-->
<!--                </h4>-->
<!--            </div>-->
            <div id="collapseSix" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="bg_title">货品信息</div>
                    <div class="mt20">
<!--                        <div class="over">-->
<!--                            <div class="bg_gr fl good" >货品类型</div>-->
<!--                            <div class="good fl borl0" th:each="mapS,status:${clientGoods}">-->
<!--                                <div class="" th:text="${mapS.goodsTypeName}"></div>-->
<!--                            </div>-->

<!--                        </div>-->
                        <table class="custom-tab table table-bordered"  >
                            <tbody>
                            <tr>
                                <td style="background: #f8f8f8 !important;">
                                    <div class="" >货品类型</div>
                                </td>
                                <td th:each="mapS,status:${clientGoods}">
                                    <div class="" th:text="${mapS.goodsTypeName}"></div>
                                </td>
                            </tr>
                            <tr>
                                <td style="background: #f8f8f8 !important;">
                                    <div class="" >货品名称</div>
                                </td>
                                <td th:each="mapS,status:${clientGoods}">
                                    <div class="" th:text="${mapS.goodsName}"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
<!--                    <div class="fixed-table-body" style="margin: 0px -5px;">-->
<!--                        <table border="0" id="infoTabFour" class="custom-tab table"  >-->
<!--                            <thead>-->
<!--                            <tr>-->
<!--                                <th style="width: 15%;">货品类型</th>-->
<!--                                <th style="width: 15%;">货品名称</th>-->

<!--                            </tr>-->
<!--                            </thead>-->
<!--                            <tbody>-->
<!--                            <tr th:each="mapS,status:${clientGoods}">-->
<!--                                <td>-->
<!--                                    <div class="input-group" th:text="${mapS.goodsTypeName}"></div>-->
<!--                                </td>-->
<!--                                <td>-->
<!--                                    <div class="input-group" th:text="${mapS.goodsName}"></div>-->
<!--                                </td>-->
<!--                            </tr>-->
<!--                            </tbody>-->
<!--                        </table>-->
<!--                    </div>-->

                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div id="collapseFive" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="bg_title">客服/业务员信息</div>
                    <div class="mt20">
                        <table class="custom-tab table table-bordered"  >
                            <tbody>
                            <tr>
                                <td style="background: #f8f8f8 !important;">
                                    <div class="" >运输方式</div>
                                </td>
                                <td style="background: #f8f8f8 !important;">
                                    <div class="" >客服姓名</div>
                                </td>
                                <td style="background: #f8f8f8 !important;">
                                    <div class="" >电话</div>
                                </td>
                            </tr>
                            <tr th:each="mapS,status:${customerServiceList}">
                                <td th:text="${@dict.getLabel('trans_code',mapS.serviceType)}"></td>
                                <td>[[${mapS.serviceName}]]</td>
                                <td>[[${mapS.servicePhone}]]</td>
                            </tr>
                            </tbody>
                        </table>
                        <br>
                        <table class="custom-tab table table-bordered"  >
                            <tbody>
                            <tr>
                                <td style="background: #f8f8f8 !important;">
                                    <div class="" >业务员姓名</div>
                                </td>
                                <td style="background: #f8f8f8 !important;">
                                    <div class="" >电话</div>
                                </td>
                            </tr>
                            <tr th:each="mapS,status:${customerClerkList}">
                                <td>[[${mapS.clerkName}]]</td>
                                <td>[[${mapS.clerkPhone}]]</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--                    <div class="fixed-table-body" style="margin: 0px -5px;">-->
                    <!--                        <table border="0" id="infoTabFour" class="custom-tab table"  >-->
                    <!--                            <thead>-->
                    <!--                            <tr>-->
                    <!--                                <th style="width: 15%;">货品类型</th>-->
                    <!--                                <th style="width: 15%;">货品名称</th>-->

                    <!--                            </tr>-->
                    <!--                            </thead>-->
                    <!--                            <tbody>-->
                    <!--                            <tr th:each="mapS,status:${clientGoods}">-->
                    <!--                                <td>-->
                    <!--                                    <div class="input-group" th:text="${mapS.goodsTypeName}"></div>-->
                    <!--                                </td>-->
                    <!--                                <td>-->
                    <!--                                    <div class="input-group" th:text="${mapS.goodsName}"></div>-->
                    <!--                                </td>-->
                    <!--                            </tr>-->
                    <!--                            </tbody>-->
                    <!--                        </table>-->
                    <!--                    </div>-->

                </div>
            </div>
        </div>
<!--        <div class="panel panel-default">-->
<!--            <div class="panel-heading">-->
<!--                <h4 class="panel-title">-->
<!--                    <a data-toggle="collapse" data-parent="#accordion"-->
<!--                       href="tabs_panels.html#collapseFour">结算客户</a>-->
<!--                </h4>-->
<!--            </div>-->
<!--            <div  class="panel-collapse collapse in" id="collapseFour">-->
<!--                <div class="panel-body">-->
<!--                    &lt;!&ndash; begin&ndash;&gt;-->
<!--                    <div class="fixed-table-body" style="margin: 0px -5px;">-->
<!--                        <table border="0" id="infoTabTwo" class="custom-tab table">-->
<!--                            <thead>-->
<!--                            <tr>-->
<!--                                <th style="width: 15%;">关联结算客户</th>-->
<!--                                <th style="width: 12%;">是否默认</th>-->
<!--                                <th style="width: 10%;">是否失效</th>-->

<!--                            </tr>-->
<!--                            </thead>-->
<!--                            <tbody>-->
<!--                            <tr th:each="mapS,status:${relatedClient}" >-->
<!--                                <td>-->
<!--                                    <div class="input-group" th:text="${mapS.custName}"></div>-->
<!--                                </td>-->
<!--                                <th:block th:if='${mapS.isDefault==null}'>-->
<!--                                    <td></td>-->
<!--                                </th:block>-->
<!--                                <th:block th:if='${mapS.isDefault=="1"}'>-->
<!--                                    <td> <div class="input-group" >是</div></td>-->
<!--                                </th:block>-->
<!--                                <th:block th:if='${mapS.isDefault=="0"}'>-->
<!--                                    <td> <div class="input-group" >否</div></td>-->
<!--                                </th:block>-->
<!--                                <th:block th:if='${mapS.lockedFlag==null}'>-->
<!--                                    <td></td>-->
<!--                                </th:block>-->
<!--                                <th:block th:if='${mapS.lockedFlag=="1"}'>-->
<!--                                    <td> <div class="input-group" >是</div></td>-->
<!--                                </th:block>-->
<!--                                <th:block th:if='${mapS.lockedFlag=="0"}'>-->
<!--                                    <td> <div class="input-group" >否</div></td>-->
<!--                                </th:block>-->
<!--                            </tr>-->
<!--                            </tbody>-->
<!--                        </table>-->
<!--                    </div>-->
<!--                    &lt;!&ndash;end&ndash;&gt;-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->

<!--        <div class="panel panel-default">-->
<!--            <div class="panel-heading">-->
<!--                <h4 class="panel-title">-->
<!--                    <a data-toggle="collapse" data-parent="#accordion"-->
<!--                       href="tabs_panels.html#collapseSeven">线路</a>-->
<!--                </h4>-->
<!--            </div>-->
<!--            <div class="panel-collapse collapse in" id="collapseSeven">-->
<!--                <div class="panel-body">-->
<!--                    &lt;!&ndash; begin&ndash;&gt;-->
<!--                    <div class="fixed-table-body" style="margin: 0px -5px;">-->
<!--                        <table border="0" id="infoTabSeven" class="custom-tab table">-->

<!--                            <thead>-->
<!--                            <tr>-->
<!--                                <th style="width: 15%;">线路名称</th>-->
<!--                                <th style="width: 15%;">回单预警时间</th>-->

<!--                            </tr>-->
<!--                            </thead>-->
<!--                            <tbody>-->
<!--                            <tr th:each="mapS,status:${clientLine}">-->
<!--                                <td>-->
<!--                                    <div class="input-group" th:text="${mapS.lineName}"></div>-->
<!--                                </td>-->
<!--                                <th:block th:if='${mapS.receiptIntervalDay==null}'>-->
<!--                                    <td></td>-->
<!--                                </th:block>-->
<!--                                <th:block th:if='${mapS.receiptIntervalDay!=null}'>-->
<!--                                    <td><div class="input-group" th:text="${@dict.getLabel('receipt_interval_day',mapS.receiptIntervalDay)}"></div></td>-->

<!--                                </th:block>-->
<!--                            </tr>-->
<!--                            </tbody>-->
<!--                        </table>-->
<!--                    </div>-->

<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="panel panel-default">-->
<!--            <div class="panel-heading">-->
<!--                <h4 class="panel-title">-->
<!--                    <a data-toggle="collapse" data-parent="#accordion"-->
<!--                       href="tabs_panels.html#collapseEight">指导价</a>-->
<!--                </h4>-->
<!--            </div>-->
<!--            <div class="panel-collapse collapse in" id="collapseEight">-->
<!--                <div class="panel-body">-->
<!--                    &lt;!&ndash; begin&ndash;&gt;-->
<!--                    <div class="fixed-table-body" style="margin: 0px -5px;">-->
<!--                        <table border="0" id="infoTabEight" class="custom-tab table table-bordered">-->
<!--                            <thead>-->
<!--                            <tr>-->
<!--                                <th style="width: 30%;">线路名称</th>-->
<!--                                <th style="width: 14%;">回单预警时间</th>-->
<!--                                <th style="width: 10%;">车长</th>-->
<!--                                <th style="width: 10%;">车型</th>-->
<!--                                <th style="width: 10%;">指导价</th>-->
<!--                                <th style="width: 13%;">开始时间</th>-->
<!--                                <th style="width: 13%;">结束时间</th>-->
<!--                            </tr>-->
<!--                            </thead>-->
<!--                            <tbody>-->

<!--                            <tr th:each="mapS,status:${guidePrice}">-->
<!--                                <td>-->
<!--                                    <div class="input-group" th:text="${mapS.lineName}"></div>-->
<!--                                </td>-->
<!--                                <td>-->
<!--                                    <div class="input-group">D+2</div>-->
<!--                                </td>-->
<!--                                <td>-->
<!--                                    <div class="input-group" th:each="map,status:${carLen}" th:if="${map.dictValue==''+mapS.carLen}" th:text="${map.dictLabel}"></div>-->
<!--                                </td>-->
<!--                                <td>-->
<!--                                    <div class="input-group" th:each="map,status:${carType}" th:if="${map.dictValue==''+mapS.carType}" th:text="${map.dictLabel}"></div>-->
<!--                                </td>-->
<!--                                <td>-->
<!--                                    <div th:align="right" th:text="${mapS.guidingPrice}"></div>-->
<!--                                <td>-->
<!--                                    <div class="input-group" th:text="${#dates.format(mapS.startDate, 'yyyy-MM-dd HH:mm:ss')}"></div>-->
<!--                                </td>-->
<!--                                <td>-->
<!--                                    <div class="input-group" th:text="${#dates.format(mapS.endDate, 'yyyy-MM-dd HH:mm:ss')}"></div>-->
<!--                                </td>-->
<!--                            </tr>-->
<!--                            </tbody>-->
<!--                        </table>-->
<!--                    </div>-->
<!--                    &lt;!&ndash;end&ndash;&gt;-->
<!--                </div>-->
<!--            </div>-->

<!--        </div>-->
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: galpop-js" />
<script th:inline="javascript">
    $(function () {

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');
        $('#collapseSeven').collapse('show');
        // 图片预览插件
        $('.galpop').galpop();
        var provinceId =  [[${client.provinceId}]];
        var aa =  [[${client}]];
        console.log(aa)
        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            success: function (result) {
                for (var i in result) {
                    if (result[i].PROVINCE_CODE === provinceId){
                        $('#provinceId').text(result[i].PROVINCE_NAME );
                    }
                }
            }
        });
        var cityId = [[${client.cityId}]];
        $.ajax({
            type: "get",
            url: ctx + "province/city?method=1&code=" + provinceId,
            success: function (result) {
                for (var i in result) {

                    if (result[i].CITY_CODE === cityId){

                        $('#cityId').text(result[i].CITY_NAME );
                    }
                }
            }
        });
        var areaId =  [[${client.areaId}]];
        var address =  [[${client.address}]];
        $.ajax({
            type: "get",
            url: ctx + "province/city?method=2&code=" + cityId,
            success: function (result) {
                for (var i in result) {
                    if (result[i].AREA_CODE === areaId){
                        $('#areaId').text(result[i].AREA_NAME );
                        $('#address').text(address);
                    }
                }
            }
        });
    });



</script>


</body>

</html>