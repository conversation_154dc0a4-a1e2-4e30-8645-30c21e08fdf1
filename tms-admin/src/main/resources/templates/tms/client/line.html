<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('线路选择页')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">线路名称：</label>
                            <div class="col-sm-8">
                                <input name="lineName" placeholder="请输入线路名称" class="form-control" type="text"
                                       aria-required="true">
                                <input id="hiddenText" type="text" style="display:none" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">起始地：</label>
                                <div class="col-sm-8">
                                    <select name="startProvinceId" id="startProvinceId" class="form-control valid"
                                            aria-invalid="false">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <select name="startCityId" id="startCityId" class="form-control valid"
                                    aria-invalid="false"></select>
                        </div>
                        <div class="col-sm-2">
                            <select name="startAreaId" id="startAreaId" class="form-control valid"
                                    aria-invalid="false"></select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">目的地：</label>
                                <div class="col-sm-8">
                                    <select name="endProvinceId" id="endProvinceId" class="form-control valid"
                                            aria-invalid="false">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <select name="endCityId" id="endCityId" class="form-control valid"
                                    aria-invalid="false"></select>
                        </div>
                        <div class="col-sm-2">
                            <select name="endAreaId" id="endAreaId" class="form-control valid"
                                    aria-invalid="false"></select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-9 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
            <input id="lineIndex" th:value="${lineIndex}" name="lineIndex" type="hidden">
        </div>
    </div>

</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">

    var prefix = ctx + "basic/line";


    $(function () {
        // 初始化省市区
        $.provinces.init("startProvinceId","startCityId","startAreaId");
        $.provinces.init("endProvinceId","endCityId","endAreaId");


        var options = {
            url: prefix + "/transLineList",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            clickToSelect:true,
            columns: [{
                radio: true
            },

                {
                    title: '线路ID',
                    field: 'transLineId',
                    visible: false
                },
                {
                    title: '线路名称',
                    align: 'left',
                    field: 'lineName'
                },
                {
                    field: 'startDetailAddr',
                    title: '起始地',
                    align: 'left',
                    formatter: function status(row,value) {
                        if(value.startProvinceName == null){
                            value.startProvinceName = "";
                        }
                        if(value.startCityName == null){
                            value.startCityName = "";
                        }
                        if(value.startAreaName == null){
                            value.startAreaName = "";
                        }

                        return value.startProvinceName + value.startCityName+value.startAreaName;
                    }
                },
                {
                    field: 'endDetailAddr',
                    title: '目的地',
                    align: 'left',
                    formatter: function status(row,value) {
                        if(value.endProvinceName == null){
                            value.endProvinceName = "";
                        }
                        if(value.endCityName == null){
                            value.endCityName = "";
                        }
                        if(value.endAreaName == null){
                            value.endAreaName = "";
                        }

                        return value.endProvinceName + value.endCityName+value.endAreaName;
                    }

                }
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });
    /** 获取选中行*/
    function getChecked() {
        return $.btTable.bootstrapTable('getSelections');
    }
    /**
     * 选择线路后的提交方法
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.close();

        var lineIndex = $("#lineIndex").val();

        // 选中线路的ID
        parent.$("#transLineId_" + lineIndex).val(rows.join());
        // 选中线路的名称
        parent.$("#lineName_" + lineIndex).val($.table.selectColumns("lineName").join());
        parent.$("#lineCode_" + lineIndex).val($.table.selectColumns("lineCode").join());


        return false;

    }
</script>
</body>
</html>