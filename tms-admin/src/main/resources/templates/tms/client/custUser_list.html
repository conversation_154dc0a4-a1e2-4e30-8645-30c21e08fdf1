<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户集团列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-sm-3">
                        <div class="form-group">
                           <label class="col-sm-4">客户姓名：</label>
                            <div class="col-sm-8">
                                <input name="userName" id="userName" placeholder="请输入客户姓名" class="form-control valid" type="text"
                                       maxlength="50">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6"></div>
                    <div class="col-sm-2">
                        <label class="col-sm-4"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="add()" shiro:hasPermission="client:client:accountList">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="client:client:accountList">
            <i class="fa fa-remove"></i> 删除
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table id="bootstrap-table" data-mobile-responsive="true" style="table-layout: fixed;"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "custUser";
    var customerId = [[${custUser.customerId}]];
    $(function () {
        var options = {
            url: prefix + "/list?customerId="+customerId,
            removeUrl: prefix + "/remove",
            showToggle:false,
            showColumns:false,
            modalName: "客户账号",
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    field: 'custUserId',
                    width: '5%',
                    formatter: function(value,row,index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('client:client:accountList')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="修改" onclick="edit(\'' + value + '\')"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>');
                        }
                        return actions.join('');
                    }
                },
                {
                    title: '客户姓名',
                    field: 'userName',
                    width:'10%',
                    align: 'left'
                },
                {
                    title: '手机',
                    field: 'phone',
                    align: 'left'
                }
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });
    // 账号添加页面
    function add() {
        $.modal.open('添加账号', prefix + "/add?customerId="+customerId,"500","300")
    }
    // 账号修改页面
    function edit(custUserId) {
        $.modal.open('修改账号', prefix + "/edit?custUserId="+custUserId+"&customerId="+customerId
            ,"500","300")
    }

</script>

</body>
</html>