<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户集团列表')"/>
</head>
<style>
    .table-striped {
        height: calc(100% - 74px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">集团名称：</label>-->
                            <div class="col-sm-12">
                                <input name="groupName" id="groupName" placeholder="请输入集团名称" class="form-control valid" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">创建人：</label>-->
                            <div class="col-sm-12">
                                <input name="corUserId" placeholder="请输入创建人" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-2">创建时间：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="regDate" placeholder="创建开始时间">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endtDate"  name="corDate" placeholder="创建结束时间">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="add()" shiro:hasPermission="client:group:insert">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="client:group:remove">
            <i class="fa fa-remove"></i> 删除
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table id="bootstrap-table" data-mobile-responsive="true" style="table-layout: fixed;"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "group";

    $(function () {
        var options = {
            url: prefix + "/list",
            removeUrl: prefix + "/remove",
            showToggle:false,
            showColumns:false,
            clickToSelect:true,
            height: 560,
            modalName: "客户集团",
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    field: 'groupId',
                    formatter: function(value,row,index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('client:group:edit')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="修改" onclick="edit(\'' + value + '\')"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>');
                        }
                        if ([[${@permission.hasPermi('client:group:detailList')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="detail(\'' + value + '\')"><i  class="fa fa-list" style="font-size: 15px;" ></i></a>');
                        }
                        return actions.join('');
                    }
                },
                {
                    title: '集团编码',
                    field: 'groupCode',
                    align: 'left'
                },
                {
                    title: '集团名称',
                    field: 'groupName',
                    align: 'left'
                },
                {
                    title: '创建人',
                    align: 'left',
                    field: 'regUserId'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'corDate'
                },
                {
                    title: '修改人',
                    align: 'left',
                    field: 'corUserId'
                }
            ]
        };
        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
        });


        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });
    // 集团添加页面
    function add() {
        $.modal.open('添加集团', prefix + "/add","500","300")
    }
    // 集团修改页面
    function edit(groupId) {
        $.modal.open('修改集团', prefix + "/edit?groupId="+groupId,"500","300")
    }

    // 集团明细页面
    function detail(groupId) {
        $.modal.openTab('客户信息', prefix + "/detailList?groupId="+groupId)
    }

</script>

</body>
</html>