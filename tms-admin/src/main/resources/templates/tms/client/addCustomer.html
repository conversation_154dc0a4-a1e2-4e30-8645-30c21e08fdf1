<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户信息')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .bg_title{
        font-size: 15px;
        font-weight: bold;
    }
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;

    }
    .flex_left{
        width: 100px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;
    }
    /*.row .form-group label[class*='col-'] {*/
    /*    text-align: right;*/
    /*    line-height: 30px;*/
    /*    margin-bottom: 0;*/
    /*}*/
    .addbtn{
        width: 120px;
        text-align: center;
        color: #fff;
        background: #1ab394;
        line-height: 30px;
        border-radius: 5px;
        cursor: pointer;
    }
    .addGoods{
        color: #1ab394;
        line-height: 30px;
        cursor: pointer;
    }
    .wap_content{
        padding: 10px 10px;
        border: 1px #eee solid;
        border-radius: 3px;

    }
    .line20{
        line-height: 20px;
    }
    .fcff6{
        color: #ff6c00;
    }
    .hisbtn{
        line-height: 25px;
        border-radius: 20px;
        width: 140px;
        text-align: center;
        color: #1ab394;
        border: 1px #1ab394 solid;
        cursor: pointer;
    }
    .eye{
        width: 20px;
        height: 20px;
        background: #1ab394 url("../../img/eyes.png") no-repeat center;
        background-size: 20px 20px;
        border-radius: 50%;
        /*display: inline-block;*/
    }
    .disin{
        display: inline-block;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .table>tbody>tr{
        border-bottom: 1px solid #e7eaec!important;
    }
    .table>tbody>tr>td {
        border-bottom: 0px solid #e7eaec!important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-client-add" class="form-horizontal" novalidate="novalidate">
        <input name="mobileUserId" id="mobileUserId" th:value="${mobileUserId}" type="hidden">
        <input name="password" id="password" th:value="${password}" type="hidden">
        <input name="salt" id="salt" th:value="${salt}" type="hidden">
        <input name="loginName" id="loginName" th:value="${loginName}" type="hidden">
        <input name="userType" id="userType" th:value="${userType}" type="hidden">
        <input name="userId" id="userId" th:value="${userId}" type="hidden">
        <input name="corDate" id="corDate" th:value="${corDate}" type="hidden">
        <input name="sourceType" id="sourceType" th:value="${sourceType}" type="hidden">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
<!--                <div class="panel-heading">-->
<!--                    <h5 class="panel-title">-->
<!--                        <a data-toggle="collapse" data-parent="#accordion"-->
<!--                           href="tabs_panels.html#collapseOne">基础信息</a>-->
<!--                    </h5>-->
<!--                </div>-->
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="bg_title">基础信息</div>
                        <div class="row">


<!--                            <input name="group.groupId" id="group.groupId" type="hidden">-->
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 客户名称：</label>
                                    <div class="flex_right">
                                        <input name="custName" id="custName" class="form-control" type="text"
                                               maxlength="100" required autocomplete="off"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left" ><span class="fcff">*</span> 客户简称：</label>
                                    <div class="flex_right">
                                        <input name="custAbbr" id="custAbbr" class="form-control" type="text"
                                               autocomplete="off" maxlength="100" required/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 结算公司：</label>
                                    <div class="flex_right">
                                        <select id="balaCorp" name="balaCorp" class="form-control valid" onchange="changeBalaCorp()"
                                                th:with="type=${@dict.getType('bala_corp')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">

                            <input name="group.groupId" type="hidden" value="">
<!--                            <div class="col-md-4 col-sm-4">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">-->
<!--                                        客户集团：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <select name="group.groupId" id="group.groupId" class="form-control valid"-->
<!--                                                aria-invalid="false">-->
<!--                                            <option value=""></option>-->
<!--                                            <option th:each="mapS,status:${groupList}" th:value="${mapS.groupId}"-->
<!--                                                    th:text="${mapS.groupName}"></option>-->
<!--                                        </select>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-md-4 col-sm-4">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">客户级别：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <select name="customerLevel" class="form-control valid"-->
<!--                                                th:with="type=${@dict.getType('customer_level')}">-->
<!--                                            <option value=""></option>-->
<!--                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"-->
<!--                                                    th:value="${dict.dictValue}"></option>-->
<!--                                        </select>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->



                            <!--                            <div class="col-md-3 col-sm-6">-->
                            <!--                                <div class="form-group">-->
                            <!--                                    <label class="col-sm-5">-->
                            <!--                                        业务员：</label>-->
                            <!--                                    <div class="col-sm-7">-->
                            <!--                                        <div class="input-group">-->
                            <!--                                        <input name="psndocName" id="psndocName" class="form-control" type="text"-->
                            <!--                                               maxlength="25" autocomplete="off" onclick="selectUser()" disabled>-->
                            <!--                                        <input name="psndoc" id="psndoc" class="form-control" type="hidden"-->
                            <!--                                               maxlength="25" autocomplete="off" >-->
                            <!--                                            <span class="input-group-addon"><i class="fa fa-search"></i></span>-->
                            <!--                                        </div>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->

                            <!--                            <div class="col-md-3 col-sm-6">-->
                            <!--                                <div class="form-group">-->
                            <!--                                    <label class="col-sm-5">-->
                            <!--                                        业务员联系方式：</label>-->
                            <!--                                    <div class="col-sm-7">-->
                            <!--                                        <input name="psncontact" id="psncontact" class="form-control" type="text"-->
                            <!--                                               maxlength="25" autocomplete="off" disabled>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                        </div>
                        <div class="row">
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>运营部：</label>
                                    <div class="flex_right">
                                        <select id="salesId" name="salesId" onchange="changeSalesId()"
                                                class="form-control valid" aria-invalid="false" required>
                                            <option value=""> -- 请选择 --</option>
                                            <option th:each="salesGroup : ${salesGroupList}"
                                                    th:text="${salesGroup.salesName}"
                                                    th:value="${salesGroup.id}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 运营组：</label>
                                    <div class="flex_right">
                                        <select name="salesDept" id="salesDept" class="form-control valid"
                                                aria-invalid="false" required>
<!--                                            <option value=""></option>-->
<!--                                            <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"-->
<!--                                                    th:text="${mapS.deptName}"></option>-->
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>开票类型：</label>
                                    <div class="flex_right">
                                        <select name="billingType" id="billingType" class="form-control valid"
                                                th:with="type=${@dict.getType('billing_type')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:selected="${dict.dictValue == '4'}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!--                            <div class="col-md-4 col-sm-4">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">-->
<!--                                        客户性质：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <select name="customerType" id="customerType" class="form-control valid"-->
<!--                                                aria-invalid="false">-->
<!--                                            <option th:each="mapS,status:${userClass}" th:value="${mapS.value}"-->
<!--                                                    th:text="${mapS.context}" th:selected="${userClassId+''==mapS.value+''}"></option>-->
<!--                                        </select>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left">证件号：</label>
                                    <div class="flex_right">
                                        <input name="businesslicense" id="businesslicense" class="form-control"
                                               maxlength="25">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left">企业性质：</label>
                                    <div class="flex_right">
                                        <select name="enterpriseNature" class="form-control valid"
                                                th:with="type=${@dict.getType('enterprise_nature')}">
                                            <option value="" selected="selected">-- 请选择 --</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                           <!-- <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">客户类型：</label>
                                    <div class="col-sm-7">
                                        <select id="custType" name="custType" class="form-control valid"
                                                th:with="type=${@dict.getType('cust_Type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>-->

                        </div>
                        <div class="row">
<!--                            <div class="col-md-4 col-sm-4">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">结算组：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <select id="balaDept" name="balaDept" class="form-control valid" aria-invalid="false">-->
<!--                                            <option value=""></option>-->
<!--                                            <option th:each="mapS,status:${balanceDept}" th:value="${mapS.deptId}" th:text="${mapS.deptName}"></option>-->
<!--                                        </select>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left">驻场组：</label>
                                    <div class="flex_right">
                                        <select name="stationDept" id="stationDept" class="form-control valid"
                                                aria-invalid="false">
                                            <option value=""></option>
                                            <option th:each="mapS,status:${stationDept}" th:value="${mapS.deptId}"
                                                    th:text="${mapS.deptName}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 联系人：</label>
                                    <div class="flex_right">
                                        <div class="input-group">
                                            <input name="contact" id="contact" class="form-control" type="text"
                                                   maxlength="25" autocomplete="off" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 联系人电话：</label>
                                    <div class="flex_right">
                                        <div class="flex_right">
                                            <div class="over">
                                                <div class="fl" style="width: 80%">
                                                    <input name="phone" required id="phone" class="form-control"
                                                           type="text"
                                                           maxlength="25" autocomplete="off" th:value="${phone}">
                                                </div>
                                                <div class="fl ml10">
                                                    <a href="#" data-toggle="tooltip" data-placement="top" title="系统货主方登录账号。">
                                                        <i class="fa fa-question-circle" style="color: #ff9113;font-size: 18px"></i>
                                                    </a>
                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">

                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> APP联系人：</label>
                                    <div class="flex_right">
                                        <input name="appDeliContact" id="appDeliContact" placeholder="APP联系人" class="form-control" type="text"
                                               maxlength="20" autocomplete="off" aria-required="true" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 联系人手机：</label>
                                    <div class="flex_right">
                                        <div class="over">
                                            <div class="fl" style="width: 80%">
                                                <input name="appDeliMobile" id="appDeliMobile" placeholder="APP联系人" class="form-control" type="text"
                                                       maxlength="11" autocomplete="off" aria-required="true" required>
                                            </div>
                                            <div class="fl ml10">
                                                <a href="#" data-toggle="tooltip" data-placement="top" title="说明：本信息为司机到场需联系的我司人员信息，信息将在app（畅运通-车主版）中显示。">
                                                    <i class="fa fa-question-circle" style="color: #ff9113;font-size: 18px"></i>
                                                </a>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
<!--                        <div class="row">-->
<!--                            <div class="col-md-4 col-sm-4">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">-->
<!--                                        联系人职位：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <input name="contactPost" id="contactPost" class="form-control" type="text"-->
<!--                                               maxlength="25" autocomplete="off">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                            <div class="col-md-4 col-sm-4">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">-->
<!--                                        邮箱：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <input name="email" id="email" class="form-control" type="text"-->
<!--                                               maxlength="25" autocomplete="off">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="row ">-->
<!--                            <div class="col-md-12 col-sm-12">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">地址：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <div class="form-group" style="margin-left: 0px;">-->
<!--                                            <div class="col-sm-2">-->
<!--                                                <select name="provinceId" id="province" class="form-control valid"-->
<!--                                                        aria-invalid="false"></select>-->
<!--                                                <input name="provinceName" id="provinceName" class="form-control" type="hidden">-->
<!--                                            </div>-->
<!--                                            <div class="col-sm-2">-->
<!--                                                <select name="cityId" id="city" class="form-control valid" aria-invalid="false"></select>-->
<!--                                                <input name="cityName" id="cityName" class="form-control" type="hidden">-->
<!--                                            </div>-->
<!--                                            <div class="col-sm-2">-->
<!--                                                <select name="areaId" id="area" class="form-control valid" aria-invalid="false"></select>-->
<!--                                                <input name="areaName" id="areaName" class="form-control" type="hidden">-->

<!--                                            </div>-->
<!--                                            <div class="col-sm-6">-->
<!--                                                <input name="address" id="address" placeholder="请输入详细地址" class="form-control"-->
<!--                                                       type="text" maxlength="50">-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->


                        <!--                        <div class="row">-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-5">税率：</label>-->
                        <!--                                    <div class="col-sm-7">-->
                        <!--                                        <input name="tariff" id="tariff" oninput="$.numberUtil.onlyNumberCustom(this,100,0,5,2);"-->
                        <!--                                               type="text" class="form-control" autocomplete="off">-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                        </div>-->
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="flex">
                                    <label class=" flex_left">备注：</label>
                                    <div class="flex_right">
                                            <textarea name="memo" id="memo" maxlength="100" class="form-control valid"
                                                      rows="3" autocomplete="off"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg_title mt10">系统信息</div>
                        <div class="row">
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left">是否跟踪：</label>
                                    <div class="flex_right">
                                        <select name="isNeedTrace" class="form-control valid" disabled>
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left">是否需要回单：</label>
                                    <div class="flex_right">
                                        <select name="isNeedReceipt" class="form-control valid" disabled>
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 设置指导价：</label>
                                    <div class="flex_right">
                                        <select name="crtGuidePrice" id="crtGuidePrice" class="form-control valid" required aria-invalid="false" disabled>
                                            <option value=""></option>
                                            <option value="0">否</option>
                                            <option value="1" selected>是</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left">装卸计价：</label>
                                    <div class="flex_right">
                                        <div class="over">
                                            <div class="fl" style="width: 80%">
                                                <select name="handlingChargesType" id="handlingChargesType" class="form-control valid">
                                                    <option value=""></option>
                                                    <option value="0">按件</option>
                                                    <option value="1">按方</option>
                                                    <option value="2">按吨</option>
                                                </select>
                                            </div>
                                            <div class="fl ml10">
                                                <a href="#" data-toggle="tooltip" data-placement="top" title="三方费用自动计费配置。">
                                                    <i class="fa fa-question-circle" style="color: #ff9113;font-size: 18px"></i>
                                                </a>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4">
                                <div class="flex">
                                    <label class="flex_left">装卸单价：</label>
                                    <div class="flex_right">
                                        <input style="display: inline-block" name="handlingCharges" id="handlingCharges" type="text"
                                               class="form-control valid" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                               autocomplete="off">
                                    </div>
                                </div>
                            </div>

                        </div>


                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
            <!-- <div class="panel panel-default">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a data-toggle="collapse" data-parent="#accordion"
                            href="tabs_panels.html#collapseThree">开票信息</a>
                     </h4>
                 </div>
                 <div id="collapseThree" class="panel-collapse collapse in">
                     <div class="panel-body">
                         &lt;!&ndash;会计属性 start&ndash;&gt;
                         <div class="row">
                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5"><font color="red">
                                         开票公司：</font></label>
                                     <div class="col-sm-7">
                                         <input type="text" id="billingCorp" class="form-control" disabled>
                                         <input type="hidden" id="billingCorpVal" name="billingCorp" class="form-control">
                                             &lt;!&ndash;<select  name="billingCorp" id="billingCorp" class="form-control valid"
                                                     th:with="type=${@dict.getType('bala_corp')}" required disabled>
                                                 <option value=""></option>
                                                 <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                         th:value="${dict.dictValue}"></option>
                                             </select>&ndash;&gt;
                                     </div>
                                 </div>
                             </div>

                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         发票抬头：</label>
                                     <div class="col-sm-7">
                                         <input name="billingPayable" id="billingPayable"  class="form-control"
                                                type="text"
                                                maxlength="25" autocomplete="off">
                                     </div>
                                 </div>
                             </div>
                             &lt;!&ndash;<div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         法人代表：</label>
                                     <div class="col-sm-7">
                                         <input name="legalRepresent" id="legalRepresent" class="form-control"
                                                type="text" th:value="${userName}"
                                                maxlength="25" autocomplete="off">
                                     </div>
                                 </div>
                             </div>&ndash;&gt;
                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         纳税识别号：</label>
                                     <div class="col-sm-7">
                                         <input name="taxIdentify" id="taxIdentify" class="form-control" type="text"
                                                maxlength="25" autocomplete="off">
                                     </div>
                                 </div>
                             </div>
                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         开户银行：</label>
                                     <div class="col-sm-7">
                                         <input name="bank"  id="bank" class="form-control"
                                                type="text"
                                                maxlength="125" autocomplete="off">

                                         &lt;!&ndash;<select name="bankId" id="bankId" class="form-control valid"
                                                 onchange="setBank();"  th:with="type=${@dict.getType('banks')}">
                                             <option value=""></option>
                                             <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                     th:value="${dict.dictValue}"></option>
                                         </select>
                                         <input name="bank" id="bank" type="hidden">&ndash;&gt;
                                     </div>
                                 </div>
                             </div>

                         </div>
                         <div class="row">
                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         开票类型：</label>
                                     <div class="col-sm-7">
                                         <select name="billingType" id="billingType" class="form-control valid"
                                                 th:with="type=${@dict.getType('billing_type')}">
                                             <option value=""></option>
                                             <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                     th:value="${dict.dictValue}"></option>
                                         </select>
                                     </div>
                                 </div>
                             </div>


                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         开户账号：</label>
                                     <div class="col-sm-7">
                                         <input name="bankAccount"  id="bankAccount" class="form-control"
                                                type="text"
                                                maxlength="25" autocomplete="off">
                                     </div>
                                 </div>
                             </div>
                             <div class="col-sm-5">
                                 <div class="form-group">
                                     <label class="col-sm-3">
                                         地址及电话：</label>
                                     <div class="col-sm-9">
                                         <input name="registerAddr" id="registerAddr" class="form-control" type="text"
                                                maxlength="100" autocomplete="off">
                                     </div>
                                 </div>
                             </div>
                             &lt;!&ndash;<div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         结算方式：</label>
                                     <div class="col-sm-7">
                                         <select name="balaType" id="balaType" class="form-control valid"
                                                 th:with="type=${@dict.getType('bala_type')}">
                                             <option value=""></option>
                                             <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                     th:value="${dict.dictValue}"></option>
                                         </select>
                                     </div>
                                 </div>
                             </div>&ndash;&gt;
                         </div>
                         <div class="row">


                            &lt;!&ndash; <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         注册资金：</label>
                                     <div class="col-sm-7">
                                         <input name="registerCapital" id="registerCapital" class="form-control"
                                                type="text" min="0" oninput="$.numberUtil.onlyNumber(this)"
                                                maxlength="10" autocomplete="off">
                                     </div>
                                 </div>
                             </div>&ndash;&gt;

                             &lt;!&ndash;<div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         法人身份证：</label>
                                     <div class="col-sm-7">
                                         <input name="legalCard" id="legalCard" class="form-control" type="text"
                                                maxlength="25" autocomplete="off" th:value="${cardId}">
                                     </div>
                                 </div>
                             </div>&ndash;&gt;
                         </div>
                         <div class="row">
                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         收件人：</label>
                                     <div class="col-sm-7">
                                         <input name="addressee" id="addressee" class="form-control" type="text"
                                                maxlength="25" autocomplete="off">
                                     </div>
                                 </div>
                             </div>
                             <div class="col-md-3 col-sm-6">
                                 <div class="form-group">
                                     <label class="col-sm-5">
                                         收件人联系方式：</label>
                                     <div class="col-sm-7">
                                         <input name="addresseeContact" id="addresseeContact" class="form-control"
                                                type="text"
                                                maxlength="25" autocomplete="off">
                                     </div>
                                 </div>
                             </div>
                             <div class="col-sm-5">
                                 <div class="form-group">
                                     <label class="col-sm-3">
                                         邮寄地址：</label>
                                     <div class="col-sm-9">
                                         <input name="addresseeAddr" id="addresseeAddr" class="form-control" type="text"
                                                maxlength="100" autocomplete="off">
                                     </div>
                                 </div>
                             </div>

                         </div>
                         &lt;!&ndash;会计属性 end&ndash;&gt;
                     </div>
                 </div>
             </div>
         </div>-->



            <!--结算客户 start-->
            <input name="custBalaList[0].isDefault" id="isDefault_0" type="hidden">

            <!--结算客户 start-->
            <input name="custTransLineList[0].transLineId" id="transLineId_0" type="hidden">

            <!--开票信息 start-->
            <div class="panel panel-default">
<!--                <div class="panel-heading">-->
<!--                    <h4 class="panel-title">-->
<!--                        <a data-toggle="collapse" data-parent="#accordion"-->
<!--                           href="tabs_panels.html#collapseThree">开票信息</a>-->
<!--                    </h4>-->
<!--                </div>-->
                <div class="panel-collapse collapse in" id="collapseThree">
                    <div class="panel-body">
                        <div class="bg_title">开票信息</div>
                        <div class="row">
                            <div class="col-md-3 col-sm-3">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>合同账期：</label>
                                    <div class="flex_right">
                                        <input name="collectionDays" id="collectionDays" class="form-control"
                                               type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off"
                                               onkeyup="this.value=this.value.replace(/\D/g,'')" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class="flex">
                                    <label class="flex_left" style="width: 80px">特殊日期：</label>
                                    <div class="flex_right">
                                        <div class="over">
                                            <div class="fl" style="width: 80%">
                                                <input name="specialDate" id="specialDate" class="form-control"
                                                       type="number" min="1" max="31" maxlength="5" autocomplete="off"
                                                       onkeyup="this.value=this.value.replace(/\D/g,'')">
                                            </div>
                                            <div class="fl ml10">
                                                <a href="#" data-toggle="tooltip" data-placement="top" title="涉及对账收款月份非自然月的时候需要填写，例如某客户25号（包含）之后单据算到下月对账，则特殊日期填25">
                                                    <i class="fa fa-question-circle" style="color: #ff9113;font-size: 18px"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>对账期：</label>
                                    <div class="flex_right">
                                        <input name="paymentDays" id="paymentDays" class="form-control"
                                               type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off"
                                               th:value="${accountPeriod}"
                                               onkeyup="this.value=this.value.replace(/\D/g,'')" required disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>申请开票期：</label>
                                    <div class="flex_right">
                                        <input name="invoiceDays" id="invoiceDays" class="form-control"
                                               type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off"
                                               th:value="${billingPeriod}"
                                               onkeyup="this.value=this.value.replace(/\D/g,'')" required disabled>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabThree" class="custom-tab table" >

                                <thead>
                                <tr>
                                    <th style="width: 3%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRowThree()" title="新增行">+</a></th>
                                    <th>开票公司</th>
                                    <th>发票抬头</th>
                                    <th>纳税识别号</th>
                                    <th>开户银行</th>
                                    <th>开票类型</th>
                                    <th>开户账号</th>
                                    <th style="width: 15%;">地址及电话</th>
                                    <!--                                <th style="width: 8%;">收件人</th>-->
                                    <!--                                <th style="width: 10%;">收件人联系方式</th>-->
                                    <!--                                <th style="width: 18%;">邮寄地址</th>-->
                                </tr>

                                </thead>
                                <tbody>

                                <tr>
                                    <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRowThree(this,0)" title="删除选择行"></a></td>
                                    <td>
                                        <select id="billingCorp0" name="custBillingList[0].billingCorp" class="form-control valid"
                                                th:with="type=${@dict.getType('bala_corp')}" >
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </td>
                                    <td>
                                        <input type="text" id="billingPayable0" name="custBillingList[0].billingPayable" maxlength="25"  class="form-control">
                                    </td>
                                    <td>
                                        <input type="text" id="taxIdentify0" name="custBillingList[0].taxIdentify" maxlength="25" class="form-control">
                                    </td>
                                    <td>
                                        <input type="text" id="bank0" name="custBillingList[0].bank" maxlength="125" class="form-control">
                                    </td>
                                    <td>
                                        <select id="billingType0" name="custBillingList[0].billingType" class="form-control valid"
                                                th:with="type=${@dict.getType('billing_type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </td>
                                    <td>
                                        <input type="text" id="bankAccount0" name="custBillingList[0].bankAccount" maxlength="25" class="form-control">
                                    </td>
                                    <td>
                                        <input type="text" id="addressPhone0" name="custBillingList[0].addressPhone" maxlength="125" class="form-control">
                                    </td>
                                    <!--                                <td>-->
                                    <!--                                    <input type="text" id="addressee0" name="custBillingList[0].addressee" maxlength="25" class="form-control">-->
                                    <!--                                </td>-->
                                    <!--                                <td>-->
                                    <!--                                    <input type="text" id="addresseeContact0" name="custBillingList[0].addresseeContact" maxlength="25" class="form-control">-->
                                    <!--                                </td>-->
                                    <!--                                <td>-->
                                    <!--                                    <input type="text" id="addresseeAddr0" name="custBillingList[0].addresseeAddr" maxlength="125" class="form-control">-->
                                    <!--                                </td>-->

                                </tr>
<!--                                <tr>-->
<!--                                    <td colspan="6">-->
<!--                                        <input type="text" placeholder="请输入详细地址及电话" id="addressPhone0" name="custBillingList[0].addressPhone" maxlength="125" class="form-control">-->
<!--                                    </td>-->
<!--                                </tr>-->
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
            <div class="panel panel-default">
<!--                <div class="panel-heading">-->
<!--                    <h4 class="panel-title">-->
<!--                        <a data-toggle="collapse" data-parent="#accordion"-->
<!--                           href="tabs_panels.html#collapseSix">证件上传</a>-->
<!--                    </h4>-->
<!--                </div>-->
                <div id="collapseSix" class="panel-collapse collapse in">
                    <div class="panel-body" style="padding: 15px 10px">
                        <div class="bg_title">证件上传</div>
                        <div class="row no-gutter">
                            <div class="col-sm-6 col-md-6" th:each="dict : ${picList}" >
                                <div class="flex">
                                    <div class="flex_left" >
                                        <label class="" th:text="${dict.context}+'：'">
                                        </label>
                                    </div>
                                    <div class="flex_right">
                                        <input th:id="'image'+${dict.value}" class="form-control"
                                               th:name="'image'+${dict.value}" type="file" >
                                        <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}"
                                               type="hidden">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--                        <div class="row no-gutter mt10">-->
                        <!--                            -->
                        <!--                        </div>-->
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: datetimepicker-js"/>
<script th:inline="javascript">
    var picType = [[${picList}]];

    $(function () {

        /**
         * 结算公司决定开票公司
         */
        $('#balaCorp').change(function(){
            var balaCorpVal = $(this).val();
            var balaCorpText = $(this).find("option:selected").text();
            $('#billingCorp').val(balaCorpText);
            $('#billingCorpVal').val(balaCorpVal);
        });

        var userType = $("#userType").val();
        if (userType != '5') {
            // 表单验证
            $("#form-client-add").validate({
                onkeyup: false,
                rules: {
                    phone: {
                        isPhone: true,
                        remote: {
                            url: ctx + "system/user/checkPhoneUnique",
                            type: "post",
                            dataType: "json",
                            data: {
                                "phonenumber": function () {
                                    return $.common.trim($("#phone").val());
                                },
                                "userType": "1"
                            },
                            dataFilter: function (data, type) {
                                return $.validate.unique(data);
                            }
                        }
                    },
                    custName: {
                        remote: {
                            url: prefix + "/checkCustNameUnique",
                            type: "post",
                            dataType: "json",
                            data: {
                                "custName": function () {
                                    return $.common.trim($("#custName").val());
                                },
                            },
                            dataFilter: function (data, type) {
                                return $.validate.unique(data);
                            }
                        }
                    },
                    custAbbr: {
                        remote: {
                            url: prefix + "/checkCustAbbrUnique",
                            type: "post",
                            dataType: "json",
                            data: {
                                "custAbbr": function () {
                                    return $.common.trim($("#custAbbr").val());
                                },
                            },
                            dataFilter: function (data, type) {
                                return $.validate.unique(data);
                            }
                        }
                    },
                    addresseeContact: {
                        isPhone: true
                    },
                    psncontact:{
                        isPhone: true
                    },
                    legalCard:{
                        isIdentity:true
                    },appDeliMobile:{
                        isPhone:true
                    }
                },
                messages: {
                    "phone":{
                        remote: "手机号码已存在"
                    },
                    "custName":{
                        remote: "客户名称已存在"
                    },
                    "custAbbr":{
                        remote: "客户简称已存在"
                    }
                }
            });
        } else {
            $("#phone").attr("disabled",true);
            // 表单验证
            $("#form-client-add").validate({
                onkeyup: false,
                rules: {
                    phone: {
                        isPhone: true
                    },
                    addresseeContact: {
                        isPhone: true
                    },
                    psncontact:{
                        isPhone: true
                    },appDeliMobile:{
                        isPhone:true
                    }
                }
            });
        }



        var provinceId = [[${provinceId}]];
        var cityId = [[${cityId}]];
        var areaId = [[${areaId}]];
        // 初始化省市区
        $.provinces.init("province","city","area",provinceId,cityId,areaId);
        $('#province').change(function(){
            $("#provinceName").val($(this).find(":selected").text());
        });
        $('#city').change(function(){
            $("#cityName").val($(this).find(":selected").text());
        });
        $('#area').change(function(){
            $("#areaName").val($(this).find(":selected").text());
        });

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');
        $('#collapseSeven').collapse('show');

        var options = {};
        $.table.init(options);


        //获取TID数字
        var custPicList = [[${custPicList}]];
        for(var i=0 ; i<custPicList.length ; i++){
            var picType = custPicList[i]["picType"];
            $("#tid"+picType).val(custPicList[i]["appendixId"]);
        }
        //循环图片路径信息，初始化图片上传区域
        var imagePath = [[${dictMap}]];
        for (var key in imagePath) {
            var publishFlag = "done" + key;
            var param = {
                maxFileCount: 0,
                publish: "publishFlag",  //用于绑定下一步方法
                fileType: null //文件类型

            };
            var tid = "tid" + key;
            var imageId = "image" + key;
            $.file.loadEditFiles(imageId, tid, imagePath[key], param);
        }


    });

    // 回单预警时间
    var receiptDayHTML = [[${receiptDay}]];
    var rdHTML = '';
    for (var i = 0; i < receiptDayHTML.length; i++) {

        rdHTML += '<option  value=' + receiptDayHTML[i].dictValue + ' >' + receiptDayHTML[i].dictLabel + '</option>'
    }

    // 车长
    var carLenHTML = [[${carLen}]];
    var clHTML = '';
    for (var i = 0; i < carLenHTML.length; i++) {
        clHTML += '<option  value=' + carLenHTML[i].dictValue + ' >' + carLenHTML[i].dictLabel + '</option>'
    }
    // 车型
    var carTypeHTML = [[${carType}]];
    var ctHTML = '';
    for (var i = 0; i < carTypeHTML.length; i++) {
        ctHTML += '<option  value=' + carTypeHTML[i].dictValue + ' >' + carTypeHTML[i].dictLabel + '</option>'
    }



    var prefix = ctx + "client";



    /*选择业务员*/
    function selectUser(){
        $.modal.open("选择业务员", ctx + "system/user/selectUser/salesUserList",'','',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $("#psndocName").val("");
                $("#psndoc").val("");
                $("#psncontact").val("");
                layer.close(index);
                return;
            }

            //业务员
            $("#psndocName").val(rows[0]["userName"]);
            $("#psndoc").val(rows[0]["userId"]);
            $("#psncontact").val(rows[0]["phonenumber"]);
            $("#form-client-add").validate().element($("#psndocName"));
            layer.close(index);
        });
    }

    //上传完成标志位
    var flag;
    /*提交表单*/
    function submitHandler() {
        if ($.validate.form()) {
            var balaCorpVal = $("#balaCorp").val();//结算公司
            for(var i=0;i<=billingIndex;i++){
                let val = $("#billingCorp"+i).val();
                if (val != '' && val !=null && balaCorpVal != val) {
                    $.modal.alertWarning("请确保开票公司与结算公司一致。");
                    return false;
                }
            }

            //提交表单flag置空
            flag = "";
            if (picType.length == 1) {
                var value = picType[i].value;
                //如果还没有上传图片，flag就是空的，直接上传第一个
                if ($("#image" + value).val() != "") {
                    $("#image" + value).fileinput('upload');
                    flag = "done" + value;
                    jQuery.subscribe(flag, setTimeout("commit()", "1000"));
                } else {
                    commit();
                }
            } else {
                //循环字典表图片类型
                for (var i = 0; i < picType.length; i++) {
                    var value = picType[i].value;
                    //如果还没有上传图片，flag就是空的，直接上传第一个
                    if (flag == "" && i >= 0 && i < picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                        } else {
                            continue;
                        }
                    }
                    //所有都为空，直接提交表单;如果只上传最后一个，前面都没有上传，直接上传并提交表单，设置延时等待上传完成，不然来不及回调
                    if (flag == "" && i == picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                            jQuery.subscribe(flag, setTimeout("commit()", "1000"));
                        } else {
                            commit();
                        }
                    }
                    //如果前面有上传，且input框不空，执行上传
                    if (flag != "" && i > 0 && i < picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            jQuery.subscribe(flag, uploadPic(value));
                        } else {
                            continue;
                        }
                    }
                    //判断最后一个是否为空，为空直接提交表单，不为空上传完提交表单
                    if (flag != "" && i == picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                            jQuery.subscribe(flag, setTimeout("commit()", "1000"));
                        } else {
                            jQuery.subscribe(flag, setTimeout("commit()", "1000"));
                        }
                    }
                }
            }
        }
    }

    function uploadPic(dictValue) {
        $("#image" + dictValue).fileinput('upload');
        flag = "done" + dictValue;
    }

    /**
     * 保存客户的方法
     */
    function commit() {
        $(":disabled").attr("disabled", false);
        var count = 0;
        for (var i = 0;i <= relatedIndex;i++){
            if ($("#isDefault_" + i).val() === '1') {
                count ++;
            }
            if (count > 1) {
                $.modal.alertWarning("只能默认一个结算客户");
                return false;
            }
        }

        $.ajax({
            type: "post",
            dataType: "json",
            data:  {phonenumber:$("#phone").val(),userType:"1"},
            url:  ctx + "system/user/checkPhoneUnique",
            success: function(result) {
                if (result == "1") {
                    var title = $("#phone").val()+" 联系电话重复！";
                    $.modal.alertWarning(title);
                    return false;
                }else {
                    // 表单提交
                    $.operate.saveModalAndRefush(prefix + "/addClient", $('#form-client-add').serialize());
                }
            }
        });

    }

    /**
     * 结算客户的选择框
     */
    function selectRelatedClient(relatedIndex) {

        $.modal.open("选择结算客户", prefix + "/related?relatedIndex=" + relatedIndex);
    }



    /**
     * 货品选择框
     */
    function selectGoods() {

        $.modal.open("选择货品", prefix + "/getGoods");
    }

    /**
     * 线路选择框
     */
    function selectLine(lineIndex) {

        $.modal.open("选择线路", prefix + "/line?lineIndex=" + lineIndex);
    }

    /**
     * 初始化日期控件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#billingDate',
            type: 'datetime',
            trigger: 'click',
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours=now.getHours();
                this.dateTime.minutes=now.getMinutes();
                this.dateTime.seconds=now.getSeconds();
            }
        });
    });

    /**
     * 设置开户行名称
     */
    function setBank() {
        $("#bank").val($("#bankId option:selected").text());
    }




    var relatedIndex = 0;
    /* 新增表格行 */
    function insertRowTwo() {
        relatedIndex += 1;
        var trTtml = '<tr>'
            + '<td> <a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRowTwo(this,'+relatedIndex+')" title="删除选择行"></a></td>'
            + ' <td> <div class="input-group">' +
            '   <input name='+relatedIndex+' id="relatedClientName_'+relatedIndex+'" placeholder="请选择结算客户" type="text"' +
            '     class="form-control valid" autocomplete="off"' +
            '     onclick="selectRelatedClient(this.name)" readonly>' +
            '    <input name="custBalaList['+ relatedIndex +'].relatedCustId" id="relatedCustId_'+relatedIndex+'"  type="hidden">' +
            '    <span class="input-group-addon"><i class="fa fa-search"></i></span>' +
            '   </div> </td>'
            + ' <td>'
            + '               <select name="custBalaList['+ relatedIndex +'].isDefault" id="isDefault_'+relatedIndex+'"  class="form-control" aria-invalid="false">'
            + '                    <option value="0">否</option>'
            + '                   <option value="1">是</option>'
            + '               </select>'
            + '            </td>'
            + '             <td> <select name="custBalaList['+ relatedIndex +'].lockedFlag" id="lockedFlag_'+relatedIndex+'"  class="form-control" aria-invalid="false">'
            + '                <option value="0">否</option>'
            + '                 <option value="1">是</option>'
            + '              </select></td>'
            + '   </tr>';
        $("#infoTabTwo tbody").append(trTtml);
    }

    /* 删除指定表格行 */
    function removeRowTwo(obj,index) {
        if ($("#infoTabTwo tbody").find('tr').length > 1) {
            $("#infoTabTwo tbody").find(obj).closest("tr").remove();
        } else {
            $("#relatedClientName_" + index).val("");
            $("#relatedCustId_" + index).val("");
        }
    }



    /* 删除指定表格行 */
    function removeRowFour(obj) {
        $("#infoTabFour tbody").find(obj).closest("tr").remove();
    }

    var lineIndex = 0;

    /* 新增表格行 */
    function insertRowSeven() {
        lineIndex += 1;
        var lineName = 'lineName_' + lineIndex;
        var trTtml = ' <tr>\n' +
            '<td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"  onclick="removeRowSeven(this,'+lineIndex+')" title="删除选择行"></a></td>' +
            '     <td>\n' +
            '              <div class="input-group">\n' +
            '              <input name=' + lineIndex + ' id=' + lineName + ' placeholder="请选择线路"  onclick="selectLine(this.name)" class="form-control"  type="text"  readonly >\n' +
            '              <input name="custTransLineList['+ lineIndex +'].transLineId" id="transLineId_'+ lineIndex +'" type="hidden">' +
            '              <span class="input-group-addon"><i class="fa fa-search"></i></span>' +
            '              </div>' +
            '             </td>' +
            '            <td>' +
            '             <select  name="custTransLineList['+ lineIndex +'].receiptIntervalDay" id="receiptIntervalDay_'+ lineIndex +'" class="form-control valid" >' +
            '             <option value="" selected="selected">-- 请选择 --</option>'+
            '               ' + rdHTML + ' ' +
            '             </select>' +
            '             </td>' +
            '</tr>'

        $("#infoTabSeven tbody").append(trTtml);
    }

    /* 删除指定表格行 */
    function removeRowSeven(obj,index) {
        if ($("#infoTabSeven tbody").find('tr').length > 1) {
            $("#infoTabSeven tbody").find(obj).closest("tr").remove();
        } else {
            $("#lineName_"+index).val("");
            $("#transLineId_"+index).val("");
        }
    }

    var billingIndex = 0;
    /**
     * 动态新增开票信息
     */
    var billingType = [[${@dict.getType('billing_type')}]];//开票类型
    var billingTypeHTML = '';
    for (var i = 0; i < billingType.length; i++) {
        billingTypeHTML += '<option  value=' + billingType[i].dictValue + ' >' + billingType[i].dictLabel + '</option>'
    }
    // 开票公司
    var billingType = [[${@dict.getType('bala_corp')}]];
    var biHTML = '';
    for (var i = 0; i < billingType.length; i++) {
        biHTML += '<option  value=' + billingType[i].dictValue + ' >' + billingType[i].dictLabel + '</option>'
    }
    function insertRowThree() {
        billingIndex += 1;
        var trTtml = ' <tr>\n' +
            '<td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"  onclick="removeRowThree(this,'+billingIndex+')" title="删除选择行"></a></td>' +
            '<td>'+
            '<select name="custBillingList['+billingIndex+'].billingCorp" id="billingCorp'+ billingIndex +'" class="form-control valid" >'+
            '<option value=""></option>'+
            ''+biHTML+''+
            '</select>'+
            '</td>'+
            '<td><input type="text" id="billingPayable'+billingIndex+'" name="custBillingList['+billingIndex+'].billingPayable" maxlength="25"  class="form-control"></td>'+
            '<td><input type="text" id="taxIdentify'+billingIndex+'" name="custBillingList['+billingIndex+'].taxIdentify" maxlength="25" class="form-control"></td>'+
            '<td><input type="text" id="bank'+billingIndex+'" name="custBillingList['+billingIndex+'].bank" maxlength="125" class="form-control"></td>'+
            '<td>'+
            '<select name="custBillingList['+billingIndex+'].billingType" id="billingType'+billingIndex+'" class="form-control valid" >'+
            '<option value=""></option>'+
            ''+billingTypeHTML+''+
            '</select>'+
            '</td>'+
            '<td><input type="text" id="bankAccount'+billingIndex+'" name="custBillingList['+billingIndex+'].bankAccount" maxlength="25" class="form-control"></td>'+
            '<td><input type="text" id="addressPhone'+billingIndex+'" name="custBillingList['+billingIndex+'].addressPhone" maxlength="125" class="form-control"></td>'+
            // '<td><input type="text" id="addressee'+billingIndex+'" name="custBillingList['+billingIndex+'].addressee" maxlength="25" class="form-control"></td>'+
            // '<td><input type="text" id="addresseeContact'+billingIndex+'" name="custBillingList['+billingIndex+'].addresseeContact" maxlength="25" class="form-control"></td>'+
            // '<td><input type="text" id="addresseeAddr'+billingIndex+'" name="custBillingList['+billingIndex+'].addresseeAddr" maxlength="125" class="form-control"></td>'+
            '</tr>'

        $("#infoTabThree tbody").append(trTtml);
        changeBalaCorp();

    }
    function removeRowThree(obj,index) {
        if ($("#infoTabThree tbody").find('tr').length > 1) {
            $("#infoTabThree tbody").find(obj).closest("tr").remove();
        } else {
            $("#billingCorp"+index).val("");
            $("#billingCorpVal"+index).val("");
            $("#billingPayable"+index).val("");
            $("#taxIdentify"+index).val("");
            $("#bank"+index).val("");
            $("#billingType"+index).val("");
            $("#bankAccount"+index).val("");
            $("#addressPhone"+index).val("");
            $("#addressee"+index).val("");
            $("#addresseeContact"+index).val("");
            $("#addresseeAddr"+index).val("");
        }
    }

    /**
     * 结算公司决定开票公司
     */
    function changeBalaCorp(){
        var balaCorpVal = $("#balaCorp").val();//结算公司
        for(var i=0;i<=billingIndex;i++){
            $("#billingCorp"+i).val(balaCorpVal);
        }
    }

    function changeSalesId() {
        //2a828327651f4e1cbbc798d8c56ec99c 为车队
        let val = $("#salesId").val();
        if (val === '2a828327651f4e1cbbc798d8c56ec99c') {
            $("#billingType").val("6")
        }else {
            $("#billingType").val("4")
        }

        var selectedTexts = $("#salesId option:selected").map(function() {
            return $(this).text();
        }).get();

        // 使用逗号分隔多个值
        var deptName = selectedTexts.join(",");

        $.ajax({
            url: ctx + "tms/custSalesTarget/get_sales_dept",
            type: "get",
            dataType: "json",
            data: {parentDeptName: deptName},
            success: function (result) {
                if (result.code == 0 && result.data!=undefined) {
                    $("#salesDept").empty();

                    if (result.data != null) {
                        $.each(result.data, function(index, option) {
                            $("#salesDept").append($('<option>', {
                                value: option.deptId,
                                text: option.deptName
                            }));
                        });


                    }
                }
            }
        });

    }
</script>
</body>

</html>