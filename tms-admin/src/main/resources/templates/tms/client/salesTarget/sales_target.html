<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('运营部列表')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
</head>
<style>
    .select-table .table td {
        /* 超出部分隐藏 */
        overflow:hidden;
        /* 超出部分显示省略号 */
        text-overflow:ellipsis;
        /*规定段落中的文本不进行换行 */
        white-space:nowrap;
        /* 配合宽度来使用 */
        height:40px;
    }
</style>
<body class="gray-bg">
<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="park-form">
                    <input type="hidden" id="parentId" name="parentId">
                    <div class="row">
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select id="salesId" name="salesId" class="form-control valid" aria-invalid="false" required>
                                        <option value=""> -- 请选择运营部 --</option>
                                        <option th:each="salesGroup : ${salesGroupList}" th:text="${salesGroup.salesName}" th:value="${salesGroup.id}"></option>
                                    </select>
                                    <input type="hidden" id="salesName" name="salesName" value="">
                                </div>
                            </div>
                        </div>


                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">运营组：</label>-->
                                <div class="col-sm-12">
                                    <select name="userId" id="userId" class="form-control valid" aria-invalid="false" required>
                                        <option value="">--运营组--</option>
                                        <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}" th:text="${mapS.deptName}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2 col-sm-">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请选择年月" id="yearMonth" name="yearMonth" required>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="add()" shiro:hasPermission="tms:client:sales_target:add">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <!--<a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="tms:park_data:waybill:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>-->
                <!--<a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="tms:park_data:waybill:import">
                    <i class="fa fa-upload"></i> 导入
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:park_data:waybill:export">
                    <i class="fa fa-download"></i> 导出
                </a>-->
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: ztree-js" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:client:sales_target:edit')}]];
    var removeFlag = [[${@permission.hasPermi('tms:client:sales_target:remove')}]];
    var prefix = ctx + "client/sales_target";

    layui.use('laydate', function() {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#yearMonth',
            type: 'month',
            trigger: 'click',
            format: 'yyyyMM' //可任意组合
        });
    });

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 185 });

        queryParkWayBillList();
        $.table.hideColumn("id");
    });

    function reset() {
        $.form.reset();
    }

    function add() {
        $.modal.openTab("新增", prefix + "/add", 450, 400);
    }

    function edit(id) {
        $.modal.openTab("修改", prefix + "/edit/"+id, 450, 400);
    }


    //初始化查询条件传参
    queryParams = function(params) {
        var search = {};
        $.each($("#park-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    function queryParkWayBillList() {
        var options = {
            url: prefix + "/salesTargetByPage",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            sortName: "yearMonth",
            sortOrder: "desc",
            modalName: "列表",
            fixedColumns: true,
            showFooter:true,
            fixedNumber:5,
            showExport: true,
            clickToSelect: true,
            exportTypes:['excel','csv'],
            exportOptions: {
                ignoreColumn: [0,1],
                fileName:"列表"
            },
            queryParams: queryParams,
            columns: [
                /*{
                    checkbox:true
                },*/
                {
                    title: '主键',
                    align: 'left',
                    field : 'id'  //主键加载完成就隐藏  多选删除的时候是按照第一列的值是否相同来判断一共有几项的
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-close"></i>删除</a> ');
                        return actions.join('');
                    }
                },
                {
                    title: '运营部名称',
                    align: 'left',
                    field : 'salesName'
                },
                {
                    title: '运营组',
                    align: 'left',
                    field : 'userName'
                },
                {
                    title: '年月',
                    align: 'left',
                    field : 'yearMonth'
                },
                {
                    title: '月目标(¥)',
                    align: 'left',
                    field : 'monthTarget'
                },
                {
                    title: '日目标(¥)',
                    align: 'left',
                    field : 'dayTarget'
                }
            ]
        };
        $.table.init(options);
    }
</script>
</body>
</html>