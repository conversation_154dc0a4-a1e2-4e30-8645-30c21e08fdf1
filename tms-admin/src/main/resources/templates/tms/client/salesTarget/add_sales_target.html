<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增')"/>
</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 113px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;
    }
    .fcff{
        color: #ff1f1f;
    }
</style>
<body>
<div class="form-content">
    <form id="form-client-add" class="form-horizontal" novalidate="novalidate">
        <div class="panel panel-default">
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                        <label class="flex_left"><span class="fcff">*</span> <font>运营部：</font></label>
                                        <div class="flex_right">
                                            <select id="salesId" name="salesId" class="form-control valid" aria-invalid="false" required>
                                                <option value=""> -- 请选择运营部 --</option>
                                                <option th:each="salesGroup : ${salesGroupList}" th:text="${salesGroup.salesName}" th:value="${salesGroup.id}"></option>
                                            </select>
                                            <input type="hidden" id="salesName" name="salesName" value="">
                                        </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>运营组：</font></label>
                                    <div class="flex_right">
                                        <select name="userId" id="userId" class="form-control selectpicker" aria-invalid="false" data-none-selected-text="运营组" required>
                                            <option value="">-- 请选择运营组 --</option>
                                            <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"  th:text="${mapS.deptName}"></option>
                                        </select>

                                        <!--<input type="text" class="form-control" placeholder="请输入用户名"  id="userName" name="userName" onclick="selectUser()" required>-->
                                        <input type="hidden" class="form-control" id="userName" name="userName">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>年月：</font></label>
                                    <div class="flex_right">
                                        <input type="text" class="form-control" placeholder="请选择年月" id="yearMonth" name="yearMonth" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>月目标(¥)：</font></label>
                                    <div class="flex_right">
                                        <input type="text" class="form-control" placeholder="请输入月目标" id="monthTarget" name="monthTarget" required onchange="dayInput(this.value)">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--<div class="row">
                            <div class="col-md-2 col-sm-4">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <input type="text" class="form-control" placeholder="请输入日目标" id="dayTarget" name="dayTarget" required readonly="readonly" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>-->
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>


<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "client/sales_target";

    $(function() {
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#yearMonth',
                type: 'month',
                trigger: 'click',
                format: 'yyyyMM' //可任意组合
            });
        });
    })

    /**
     * 校验
     */
    $("#form-client-add").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            salesName: {
                required:true,
            },
        }
    });
    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            $("#salesName").val($("#salesId").find("option:selected").text())
            $("#userName").val($("#userId").find("option:selected").text())

            console.log($("#user"))
            var data = $("#form-client-add").serializeArray();
            $.operate.saveTab(prefix + "/addSalesTarget", data);
        }
    }

    /**
     * 系统用户
     */
    function selectUser() {
        $.modal.open("选择用户", ctx + "system/user/selectRadioUser/listSys");
    }

    function dayInput(value) {
        var yearMonth = $("#yearMonth").val()
        var array = yearMonth.split("-")
        var day = getDays(array[0], array[1])
        console.log(value)
        console.log(yearMonth)


    }

    function getDays(year, month) {
        let days = [31,28,31,30,31,30,31,31,30,31,30,31]
        if ( (year % 4 ===0) && (year % 100 !==0 || year % 400 ===0) ) {
            days[1] = 29
        }
        return days[month]
    }


    //说明：javascript的除法结果会有误差，在两个浮点数相除的时候会比较明显。这个函数返回较为精确的除法结果。
    //调用：accDiv(arg1,arg2)
    //返回值：arg1除以arg2的精确结果
    function accDiv(arg1,arg2){
        var t1=0,t2=0,r1,r2;
        try{t1=arg1.toString().split(".")[1].length}catch(e){}
        try{t2=arg2.toString().split(".")[1].length}catch(e){}
        with(Math){
            r1=Number(arg1.toString().replace(".",""))
            r2=Number(arg2.toString().replace(".",""))
            return (r1/r2)*pow(10,t2-t1);
        }
    }
</script>
</body>
</html>