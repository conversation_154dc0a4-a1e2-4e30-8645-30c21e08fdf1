<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('货品选择页')"/>
</head>
<style>
    .table-striped {
        height: calc(100% - 78px);
    }
    .bootstrap-table .fixed-table-container {
        height: calc(100% - 68px) !important;
    }
    .fixed-table-pagination .pagination-detail{
        margin: 0;
    }
    .fixed-table-pagination{
        margin-top: 10px;
    }
    label{
        height: 26px;
        line-height: 26px;
    }
    .col-sm-4{
        margin: 0;
    }
    .form-group{
        margin: 0;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <input type="hidden" id="customerId" th:value="${customerId}">
            <input type="hidden" id="type" th:value="${type}">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-sm-5">
                        <div class="form-group">
                            <label class="col-sm-4">货品名称：</label>
                            <div class="col-sm-8">
                                <input name="goodsName" placeholder="请输入货品名称" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-5">
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-4">
                                    <span>货品类型：</span></label>
                                <div class="col-sm-8">
                                    <input class="form-control valid" placeholder="请输入货品类型" name="goodsTypeName"
                                           type="text">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-2">
                        <label class="col-sm-4"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <div th:if="${customerId != null and customerId != ''} ">
                <a class="btn btn-primary btn-sm" onclick="addGoods()">&nbsp;+ 新增货品</a>
            </div>

            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
            <input id="goodsIndex" th:value="${goodsIndex}" name="goodsIndex" type="hidden">
        </div>
    </div>

</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">

    var prefix = ctx + "basic/goods";
    var customerId = $("#customerId").val();
    var type = $("#type").val();

    $(function () {
        var options = {
            url: prefix + "/goodsList?checkStatus=1&type=" + type + "&customerId=" + customerId ,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            clickToSelect:true,
            columns: [{
                radio: true
            },

                {
                    title: '货品ID',
                    visible: false,
                    field: 'goodsId'
                },
                {
                    title: '货品名称',
                    align: 'left',
                    field: 'goodsName'

                },
                {
                    title: '货品类型',
                    align: 'left',
                    field: 'goodsTypeName'

                }
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });

    /** 获取选中行*/
    function getChecked() {
        return $.btTable.bootstrapTable('getSelections');
    }

    function addGoods() {
        if (customerId == null || customerId == '') {
            $.modal.alertWarning("请先选择客户！");
            return
        }
        $.modal.open("添加货品", ctx + "client/goods/add?customerId="+customerId, 550, "");
    }

    /**
     * 选择货品后的提交方法
     */
    // function submitHandler() {
    //     var rows = $.table.selectFirstColumns();
    //     if (rows.length == 0) {
    //         $.modal.alertWarning("请至少选择一条记录");
    //         return;
    //     }
    //     $.modal.close();
    //
    //     var goodsIndex = $("#goodsIndex").val();
    //     // 选中的ID
    //     parent.$("#goodsId_" + goodsIndex).val(rows.join());
    //     // 选中的名称
    //     parent.$("#goodsName_" + goodsIndex).val($.table.selectColumns("goodsName").join());
    //     // 货品类型名称
    //     parent.$("#goodsTypeName_" + goodsIndex).val($.table.selectColumns("goodsTypeName").join());
    //     return false;
    //
    // }
</script>
</body>
</html>