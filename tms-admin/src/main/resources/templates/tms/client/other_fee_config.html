<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方费用配置')"/>
</head>

<body>
<div class="form-content">
    <form id="form-client-add" class="form-horizontal" novalidate="novalidate">
        <!--客户id-->
        <input type="hidden" name="customerId" th:value="${client.customerId}">

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">基本信息</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">

                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span style="color: red">是否锁定第三方费用配置：</span></label>
                                <div class="col-sm-8">
                                    <select id="isLockOtherFee" name="isLockOtherFee" class="form-control valid"
                                            th:with="type=${@dict.getType('is_deposit')}" required>
                                        <option value=""></option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}" th:field = "${client.isLockOtherFee}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>


    </form>
</div>



<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "client";

    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-client-add").serializeArray();
            $.operate.save(prefix + "/saveOtherFeeConfig", data);
        }
    }
</script>
</body>
</html>