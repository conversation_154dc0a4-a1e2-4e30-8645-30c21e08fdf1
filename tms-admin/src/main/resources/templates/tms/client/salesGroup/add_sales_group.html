<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('调整账期')"/>
</head>
<style>

</style>
<body>
<div class="form-content">
    <form id="form-client-add" class="form-horizontal" novalidate="novalidate">
        <div class="panel panel-default">
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div>
                        <div class="row">
                            <div class="col-md-2 col-sm-4">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <input type="text" class="form-control" placeholder="请输入运营部名称"  name="salesName" required>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-2 col-sm-4">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <input type="text" class="form-control" placeholder="请输入运营部负责人"  name="salesTeamLeader" required>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-2 col-sm-4">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <input type="text" class="form-control" placeholder="请输入企业微信小蜜对应key"  name="enterpriseWechatKey" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-4">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <input type="text" class="form-control" placeholder="排序"  name="sort" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "client/sales_group";
    /**
     * 校验
     */
    $("#form-client-add").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            salesName: {
                required:true,
            },
        }
    });
    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-client-add").serializeArray();
            $.operate.save(prefix + "/addSalesGroup", data);
        }
    }
</script>
</body>
</html>