<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('新增合同价')" />
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .cur{
        cursor: pointer;
    }

    /* .show-div{
        display: block;
    } */
    .tc{
        text-align: center;
    }
    .fixed-table-body{
        overflow: inherit;
    }
    /*.bootstrap-select.form-control{*/
    /*    position: initial;*/
    /*}*/

    .flex{
        display: flex;
        align-items:center;
        justify-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 26px;
        text-align: right;
        color: #000000 !important;
        margin-bottom: 0;
    }
    .flex_right{
        line-height: 26px;
        min-width:0;
        flex:1;
    }

    .price-readonly {
        background-color: #f0f0f0 !important;
        color: #888888 !important;
        cursor: not-allowed !important;
        pointer-events: none !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
        <div class="panel-body">
            <input type="hidden" id="customerId" name="customerId">

            <div class="row">
                <div class="col-md-6 col-xs-6">
                    <div class="flex" style="align-items: flex-start; justify-content: space-between;">
                        <label class="flex_left">计费方式：</label>
                        <div class="flex_right">
                            <select name="billingMethod" id="billingMethod" class="form-control valid custom-select"
                                    onchange="changeBillingMethod()"
                                    aria-invalid="false" data-none-selected-text="计费方式" required>
                                <option value="" disabled selected hidden>请选择计费方式</option>
                                <option th:each="dict : ${billingMethod}" th:text="${dict.context}"
                                        th:value="${dict.value}">
                                </option>
                            </select>
                            <label style="display:none;top:0px" id="billingMethod-error" class="error" for="billingMethod"></label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt10" id="addr">
                <div class="col-md-12 col-xs-12">
                    <div class="flex">
                        <label class="flex_left"> 线路名称：</label>
                        <div class="flex_right">
                            请选择装卸货省市区
                        </div>
                    </div>
                </div>

                <div class="col-md-12 col-xs-12">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">
                            <a class="fa fa-exchange" style="color: #1ab394;font-size: 15px;margin-right: 15px;"
                               onclick="exchangeDeli()" title="切换地址">
                            </a>
                            <input id="deliType" value="0" type="hidden">
                            <span class="label label-warning pa2">提</span>
                        </label>
                        <div class="flex_right">
                            <div id="deliDiv" class="form-group" style="margin-left: -5px;">
                                <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 0px;">
                                    <select name="deliProvinceId" id="deliProvinceId" required
                                            class="form-control valid custom-select" aria-invalid="false">
                                    </select>
                                    <label style="display:none;top:0px" id="deliProvinceId-error" class="error" for="deliProvinceId"></label>

                                </div>
                                <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 0px;">
                                    <select name="deliCityId" id="deliCityId" class="form-control valid custom-select" required
                                            aria-invalid="false"></select>

                                    <label style="display:none;top:0px" id="deliCityId-error" class="error" for="deliCityId"></label>

                                </div>
                                <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 10px;">
                                    <select name="deliAreaId" id="deliAreaId" class="form-control selectpicker valid" required
                                            aria-invalid="false" multiple placeholder="请选择" data-none-selected-text="请选择">
                                    </select>

                                    <label style="display:none;top:0px" id="deliAreaId-error" class="error" for="deliAreaId"></label>
                                </div>
                            </div>
                            <div id="deliNameDiv" class="form-group" style="margin-left: -5px;display: none;">
                                <div class="col-sm-12 col-xs-12" style="padding-left: 5px;padding-right: 10px;">
                                    <input id="selectAddrDeliText" class="form-control" onclick="selectAddrDeli()" type="text" placeholder="点击选择详细地址"/>
                                    <input id="deliAddrName" name="deliAddrName" class="form-control" type="hidden"/>
                                    <input id="deliId" name="deliId" class="form-control" type="hidden"/>
                                    <input id="deliDetailAddr" name="deliDetailAddr" class="form-control" type="hidden"/>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <div class="col-md-12 col-xs-12">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">
                            <a class="fa fa-exchange" style="color: #1ab394;font-size: 15px;margin-right: 15px;"
                               onclick="exchangeArri()" title="切换地址">
                            </a>
                            <input id="arriType" value="0" type="hidden">
                            <span class="label label-success pa2">到</span>
                        </label>
                        <div class="flex_right">
                            <div id="arriDiv" class="form-group" style="margin-left: -5px;">
                                <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 0px;">
                                    <select name="arriProvinceId" id="arriProvinceId" required
                                            class="form-control valid custom-select" aria-invalid="false">
                                    </select>
                                    <label style="display:none;top:0px" id="arriProvinceId-error" class="error" for="arriProvinceId"></label>

                                </div>
                                <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 0px;">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid custom-select" required
                                            aria-invalid="false"></select>
                                    <label style="display:none;top:0px" id="arriCityId-error" class="error" for="arriCityId"></label>

                                </div>
                                <div class="col-sm-4 col-xs-4" style="padding-left: 5px;padding-right: 10px;">
                                    <select name="arriAreaId" id="arriAreaId" class="form-control selectpicker valid" required
                                            aria-invalid="false" multiple placeholder="请选择" data-none-selected-text="请选择">
                                    </select>

                                    <label style="display:none;top:0px" id="arriAreaId-error" class="error" for="arriAreaId"></label>
                                </div>
                            </div>

                            <div id="arriNameDiv" class="form-group" style="margin-left: -5px;display: none;">
                                <div class="col-sm-12 col-xs-12" style="padding-left: 5px;padding-right: 10px;">
                                    <input id="selectAddrArriText" class="form-control" onclick="selectAddrArri()" type="text" placeholder="点击选择详细地址"/>
                                    <input id="arriAddrName" name="arriAddrName" class="form-control" type="hidden"/>
                                    <input id="arrivalId" name="arrivalId" class="form-control" type="hidden"/>
                                    <input id="arriDetailAddr" name="arriDetailAddr" class="form-control" type="hidden"/>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

            </div>

            <div class="row mt10">
                <div class="col-md-6 col-xs-6">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">货品特性：</label>
                        <div class="flex_right">
                            <select name="goodsCharacter" id="goodsCharacter" class="form-control custom-select"
                                    required data-none-selected-text="货品特性" aria-invalid="false">
                                <option value="" disabled selected hidden>请选择货品特性</option>
                                <option th:each="dict : ${goodsCharacter}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}">
                                </option>
                            </select>
                            <label style="display:none;top:0px" id="goodsCharacter-error" class="error" for="goodsCharacter"></label>

                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xs-6">
                    <div class="flex">
                        <label class="flex_left">货品：</label>
                        <div class="flex_right">
                            <input class="form-control" onclick="selectGoods(this)" type="text" readonly />
                            <input name="goodsId" type="hidden">
                            <input name="goodsName" type="hidden">
                        </div>
                    </div>
                </div>

            </div>

            <div class="row mt10">
                <div class="col-md-6 col-xs-6">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">车长：</label>
                        <div class="flex_right">
                            <select name="carLen" id="carLen" class="form-control selectpicker valid" aria-invalid="false"
                                    multiple data-none-selected-text="车长">
                                <option th:each="dict : ${vehicleLength}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}">
                                </option>
                            </select>
<!--                            <label style="display:none;top:0px" id="carLen-error" class="error" for="carLen"></label>-->

                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xs-6">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">车型：</label>
                        <div class="flex_right">
                            <select name="carType" id="carType" class="form-control selectpicker valid" aria-invalid="false"
                                    multiple data-none-selected-text="车型" th:with="type=${@dict.getType('car_type')}">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
<!--                            <label style="display:none;top:0px" id="carType-error" class="error" for="carType"></label>-->

                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt10">
                <div class="col-md-6 col-xs-6">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">是否大件：</label>
                        <div class="flex_right">
                            <select name="isOversize" id="isOversize" class="form-control custom-select" aria-invalid="false">
                                <option value="2" selected>不区分</option>
                                <option value="0">非大件</option>
                                <option value="1">大件</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-xs-6">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">运输方式：</label>
                        <div class="flex_right">
                            <select name="isFtl" id="isFtl" class="form-control custom-select" aria-invalid="false">
                                <option value=""></option>
                                <option value="0">零担</option>
                                <option value="1">整车</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt10">
                <div class="col-md-6 col-xs-6">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">是否往返订单：</label>
                        <div class="flex_right">
                            <select name="isRoundTrip" id="isRoundTrip" class="form-control custom-select" aria-invalid="false">
                                <option value="2" selected>不区分</option>
                                <option value="0">单程</option>
                                <option value="1">往返</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row mt10">
                <div class="col-md-3 col-xs-3">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">是否有区间：</label>
                        <div class="flex_right">
                            <section class='model-15'>
                                <label class="checkbox-switch">
                                    <input type="checkbox" id="checkbox" />
                                    <label for="checkbox" class="switch-label">
                                        <span class="label-text">是</span>
                                        <span class="label-text">否</span>
                                    </label>
                                </label>
                            </section>
                            <input type="hidden" id="ifSection" name="ifSection"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-xs-3">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">是否含税：</label>
                        <div class="flex_right">
                            <section class='model-15 '>
                                <label class="checkbox-switch">
                                    <input type="checkbox" id="isIncludeTax" checked/>
                                    <label for="isIncludeTax" class="switch-label">
                                        <span class="label-text">是</span>
                                        <span class="label-text">否</span>
                                    </label>
                                </label>
                            </section>

                            <input type="hidden" name="isIncludeTax" value="1" />
                        </div>
                    </div>
                </div>


            </div>
            <div class="floatDate">
                <div class="row">
                    <div class="col-md-3 col-xs-3">
                        <div class="flex" style="align-items: center;">
                            <label class="flex_left" style="width: 80px; margin-bottom: 0;">合同价：</label>
                            <div class="flex_right" style="flex: 1;">
                                <input class="form-control" placeholder="合同价" name="guidingPrice" id="guidingPrice"
                                       type="text" required oninput="$.numberUtil.onlyNumber(this)" min="0"
                                       maxlength="10" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-xs-3">
                        <div class="flex" style="align-items: flex-start;">
                            <label class="flex_left" style="width: 80px; margin-bottom: 0;">保底价：</label>
                            <div class="flex_right" style="flex: 1;">
                                <input class="form-control" placeholder="保底价" name="reservePrice" id="reservePrice"
                                       type="text" oninput="$.numberUtil.onlyNumber(this)" min="0"
                                       maxlength="10" />
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-xs-3 ">
                        <div class="flex" style="align-items: flex-start;">
                            <label class="flex_left" style="width: 80px; margin-bottom: 0;">送货费：</label>
                            <div class="flex_right" style="flex: 1;">
                                <input class="form-control" placeholder="送货费"
                                       name="deliveryFee" id="deliveryFee" type="text"
                                       oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row" style="margin-top: 15px;">
                    <div class="col-md-3 col-xs-3">
                        <div class="flex" style="align-items: center;">
                            <label class="flex_left" style="width: 80px; margin-bottom: 0;">成本价：</label>
                            <div class="flex_right" style="flex: 1;">
                                <input class="form-control" placeholder="成本价"
                                       name="costPrice" id="costPrice" type="text"
                                       oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-xs-3">
                        <div class="flex" style="align-items: center;">
                            <label class="flex_left" style="width: 80px; margin-bottom: 0;">成本票点：</label>
                            <div class="flex_right" style="flex: 1;">
                                <select name="costBillingType" id="costBillingType" class="form-control valid" th:with="type=${@dict.getType('billing_type')}"
                                        aria-invalid="false" aria-required="true">
                                    <option value=""></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}" th:attr="data-rate=${dict.numVal1}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="show-div" id="show-div">
                <table border="0" id="infoTabFour" class="custom-tab table td">
                    <thead>
                    <tr>
                        <th style="width: 5%;" rowspan="2"> <a class="collapse-link add-alink show-alink" style="font-size: 22px;" title="新增" onclick="insertDiv(this,0)">+</a> </th>
                        <th style="width: 10%;">区间开始</th>
                        <th style="width: 20%;">自定义区间</th>
                        <th style="width: 10%;">区间结束</th>
                        <th style="width: 10%;">合同价</th>
                        <th style="width: 10%;">成本价</th>
                        <th style="width: 15%;">成本票点</th>
                        <th style="width: 10%;">价格类型</th>
                        <th style="width: 10%;">送货费</th>

                    </tr>

                    </thead>
                    <tbody id="sectionTbody">
                    </tbody>
                </table>
            </div>

            <div class="show-div" id="show-div-1">
                <table border="0" class="custom-tab table td">
                    <thead>
                    <tr>
                        <th style="width: 15%;" colspan="2">
                            <a class="collapse-link add-alink show-alink"
                               style="font-size: 22px;float:left;"
                               title="新增" onclick="insertDiv(this,1)">+</a>
                            <span style="display:inline-block;text-align:center">公里(a <= x < b)</span>
                        </th>
                        <th style="width: 10%;">区间开始</th>
                        <th style="width: 15%;">自定义区间</th>
                        <th style="width: 10%;">区间结束</th>
                        <th style="width: 10%;">合同价</th>
                        <th style="width: 10%;">成本价</th>
                        <th style="width: 10%;">成本票点</th>
                        <th style="width: 10%;">价格类型</th>
                        <th style="width: 10%;">送货费</th>

                    </tr>

                    </thead>
                    <tbody id="sectionTbody-1">
                    </tbody>
                </table>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-fileinput-js" />
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "client/cust_cntractpc";

    var billing_type = [[${@dict.getType('billing_type')}]];

    $(function () {
        $("#customerId").val(window.parent.document.getElementById("customerId").value);

        init("deliProvinceId", "deliCityId", "deliAreaId");
        init("arriProvinceId", "arriCityId", "arriAreaId");

        $("#checkbox").change(function(){
            var isT = $(this).is(":checked")
            let billingMethod = $("#billingMethod").val();

            if(isT){
                $('#ifSection').val('1');
                $('.floatDate').css("display","none")

                if (billingMethod === '8' || billingMethod === '9') {
                    $('#show-div-1').css("display","block")
                }else {
                    $('#show-div').css("display","block")
                }
            }else{
                $('#ifSection').val('0')
                $('.floatDate').css("display","revert")

                $('#show-div-1').css("display","none")
                $('#show-div').css("display","none")

                $('#sectionTbody').empty();
                $('#sectionTbody-1').empty();

            }
        })

        $('#isIncludeTax').on('change', function() {
            if ($(this).is(':checked')) {
                $(this).val('1');
                $('[name="isIncludeTax"]').val('1');
            } else {
                $(this).val('0');
                $('[name="isIncludeTax"]').val('0');
            }
        });

    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-out-quote").serializeArray();
            $.operate.saveAndRefreshCurrent(prefix + "/edit_batch", data);
        }
    }


    function exchangeDeli() {
        let deliType = $("#deliType").val()

        $("#selectAddrDeliText").val("")
        $("#deliProvinceId").val("")
        $("#deliCityId").val("")
        $("#deliAreaId").val("")
        $("#deliAddrName").val("")
        $("#deliId").val("")
        $("#deliDetailAddr").val("")

        if (deliType == '0') {
            $(`#deliDiv`).hide()
            $(`#deliNameDiv`).show()

            $(`#deliType`).val("1");
        }else {
            $(`#deliDiv`).show()
            $(`#deliNameDiv`).hide()

            $(`#deliType`).val("0");
        }
    }

    function selectAddrDeli() {
        var customerId = window.parent.document.getElementById("customerId").value;

        parent.layer.open({
            type: 2,
            area: ['85%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "地址",
            content: ctx + "basic/address/selectAddress?customerId=" + customerId + "&addrType=0",
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero) {
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                let row = rows[0]

                $("#selectAddrDeliText").val(row.addrName + "（" + row.provinceName + row.cityName + row.areaName + row.detailAddr + "）");
                init("deliProvinceId", "deliCityId", "deliAreaId", row.provinceId, row.cityId, row.areaId);

                $("#deliAddrName").val(row.addrName)
                $("#deliId").val(row.addressId)
                $("#deliDetailAddr").val(row.detailAddr)

                parent.layer.close(index);
            },
            cancel: function (index) {
                return true;
            }
        });
    }

    function exchangeArri() {
        let arriType = $("#arriType").val()

        $("#selectAddrArriText").val("")
        $("#arriProvinceId").val("")
        $("#arriCityId").val("")
        $("#arriAreaId").val("")
        $("#arriAddrName").val("")
        $("#arrivalId").val("")
        $("#arriDetailAddr").val("")

        if (arriType == '0') {
            $(`#arriDiv`).hide()
            $(`#arriNameDiv`).show()

            $(`#arriType`).val("1");
        }else {
            $(`#arriDiv`).show()
            $(`#arriNameDiv`).hide()

            $(`#arriType`).val("0");
        }
    }

    function selectAddrArri() {
        var customerId = window.parent.document.getElementById("customerId").value;

        parent.layer.open({
            type: 2,
            area: ['85%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "地址",
            content: ctx + "basic/address/selectAddress?customerId=" + customerId + "&addrType=1",
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero) {
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                let row = rows[0]

                $("#selectAddrArriText").val(row.addrName + "（" + row.provinceName + row.cityName + row.areaName + row.detailAddr + "）");
                init("arriProvinceId", "arriCityId", "arriAreaId", row.provinceId, row.cityId, row.areaId);

                $("#arriAddrName").val(row.addrName)
                $("#arrivalId").val(row.addressId)
                $("#arriDetailAddr").val(row.detailAddr)

                parent.layer.close(index);
            },
            cancel: function (index) {
                return true;
            }
        });
    }



    /**
     * 选择货品名称
     *
     */
    function selectGoods(obj) {
        var customerId = window.parent.document.getElementById("customerId").value;

        layer.open({
            type: 2,
            area: ['80%', '80%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "货品选择",
            content: ctx + "client/goods?type=0&customerId=" + customerId,
            btn: ['确定','清空', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero){
                //获取整行
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                $(obj).val(rows[0].goodsName);
                $("input[name='goodsId']").val(rows[0].goodsId);
                $("input[name='goodsName']").val(rows[0].goodsName);
                layer.close(index);
            },
            btn2: function(index, layero){
                $(obj).val('');
                $("input[name='goodsId']").val('');
                $("input[name='goodsName']").val('');
                layer.close(index);
            },
            cancel: function(index) {
                return true;
            }
        });
    }

    /**
     *
     */
    function changeBillingMethod() {
        // let billingMethod = $("#billingMethod").val();
        //
        // if (billingMethod == 7) {
        //     $("#addr").hide()
        // }else {
        //     $("#addr").show()
        // }

        let billingMethod = $("#billingMethod").val();

        if (billingMethod == 7 || billingMethod == 8 || billingMethod == 9) {
            $('#deliProvinceId').removeAttr('required');
            $('#deliCityId').removeAttr('required');
            $('#deliAreaId').removeAttr('required');
            $('#arriProvinceId').removeAttr('required');
            $('#arriCityId').removeAttr('required');
            $('#arriAreaId').removeAttr('required');
        }else {
            $('#deliProvinceId').attr('required', 'required');
            $('#deliCityId').attr('required', 'required');
            $('#deliAreaId').attr('required', 'required');
            $('#arriProvinceId').attr('required', 'required');
            $('#arriCityId').attr('required', 'required');
            $('#arriAreaId').attr('required', 'required');
        }

        var isT = $("#checkbox").is(":checked")

        if (isT) {
            $('#show-div-1').css("display","none")
            $('#show-div').css("display","none")
            $('#sectionTbody').empty();
            $('#sectionTbody-1').empty();

            if (billingMethod === '8' || billingMethod === '9') {
                $('#show-div-1').css("display","block")
            }else {
                $('#show-div').css("display","block")
            }
        }
    }

    function showDivDel(kilIndex, sectionIndex, type) {
        if (type === 1) {
            var $td = $(`#kilTd_${kilIndex}`);
            // 获取原始data-ind值
            var origDataInd = $td.data('ind');
            // 减1
            var newDataInd = origDataInd - 1;
            // 设置新的rowspan和data-ind
            $td.attr('rowspan', newDataInd);
            $td.data('ind', newDataInd);

            $(`[id^=kilTr_${kilIndex}_${sectionIndex}]`).remove();

        }else {
            $(`[id^=kilTr_${kilIndex}_]`).remove();
        }
    }

    //新增
    var section_index = 0
    var kil_index = 0

    function insertDiv(obj,type) {
        section_index += 1;
        let sectionIndex = section_index

        kil_index += 1;
        let kilIndex = kil_index

        var appendHtml = `<tr id="kilTr_${kilIndex}_${sectionIndex}">`;

        if (type === 1) {

            appendHtml = appendHtml
                    + `
                            <td id="kilTd_${kilIndex}" rowspan="1" data-ind="1">
                                <div style="display: flex; align-items: center;">
                                    <span>
                                        <a class="close-link del-alink show-alink"
                                           onclick="showDivDel(${kilIndex})" title="删除">-</a>
                                    </span>

                                    <span style="display:flex; align-items:center;margin-left: 5px">
                                        <input name="contractpcSectionList[${sectionIndex}].startKil"
                                               id="startKil_${kilIndex}_${sectionIndex}"
                                               onchange="changeKil(this,0,${kilIndex})"
                                               style="margin-right: 5px;" class="form-control" placeholder="始" type="text">
                                        <span style="margin: 0 5px;">-</span>
                                        <input name="contractpcSectionList[${sectionIndex}].endKil"
                                               id="endKil_${kilIndex}_${sectionIndex}"
                                               onchange="changeKil(this,1,${kilIndex})"
                                               class="form-control" placeholder="终" type="text">
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <a class="collapse-link add-alink show-alink " style="font-size: 22px;" title="新增" onclick="insertDiv_1('${kilIndex}','${sectionIndex}')">+</a>
                                </div>
                                <input name="range" hidden>
                            </td>
                          `
        }else {
            appendHtml = appendHtml
                + `
                        <td>
                            <div style="display: flex; align-items: center;">
                                <a class="close-link del-alink show-alink" onclick="showDivDel(${kilIndex})" title="删除">-</a>
                            </div>
                            <input name="range" hidden>
                        </td>

                `

        }

        var billingTypeHtml = '';
        for ( var i = 0; i < billing_type.length; i++) {
            billingTypeHtml += `
                <option value="${billing_type[i].dictValue}" data-rate="${billing_type[i].numVal1}">
                    ${billing_type[i].dictLabel}
                </option>
            `
        }

        appendHtml= appendHtml
                        + `
                                <td>
                                    <input class="form-control" placeholder="区间开始" type="text"
                                                oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                                name="contractpcSectionList[${sectionIndex}].startSection">
                                </td>
                                <td>
                                    <div class="flex">
                                        <select name="contractpcSectionList[${sectionIndex}].startOperator" class="form-control">
                                            <option value="0">＜</option>
                                            <option value="1">≤</option>
                                        </select>

                                        <div style="margin: 0 10px;">x</div>

                                        <select name="contractpcSectionList[${sectionIndex}].endOperator" class="form-control">
                                            <option value="2">＜</option>
                                            <option value="3">≤</option>
                                        </select>
                                    </div>

                                </td>
                                <td>
                                    <input class="form-control" placeholder="区间结束" type="text"
                                                oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                                name="contractpcSectionList[${sectionIndex}].endSection">
                                </td>
                                <td>
                                    <input class="form-control" placeholder="合同价" type="text" required
                                    oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                    name="contractpcSectionList[${sectionIndex}].guidingPrice">
                                </td>
                                <td>
                                    <input class="form-control" placeholder="成本价" type="text"
                                    oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                    name="contractpcSectionList[${sectionIndex}].costPrice">
                                </td>
                                <td>
                                    <select name="contractpcSectionList[${sectionIndex}].costBillingType"
                                            class="form-control valid" aria-invalid="false" aria-required="true">
                                        <option value=""></option>
                                            ${billingTypeHtml}
                                    </select>
                                </td>
                                <td>
                                    <select name="contractpcSectionList[${sectionIndex}].isFixedPrice"
                                            onchange="handlePriceChange(${sectionIndex})"
                                            class="form-control">
                                        <option value="0" selected>单价</option>
                                        <option value="1" >一口价</option>
                                        <option value="2" >起步价</option>
                                    </select>
                                </td>
                                <td>
                                    <input class="form-control" placeholder="送货费" type="text"
                                    oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                    name="contractpcSectionList[${sectionIndex}].deliveryFee">
                                </td>

                            </tr>`;

        if (type === 1) {
            $("#sectionTbody-1").append(appendHtml);
        }else {
            $("#sectionTbody").append(appendHtml);
        }
    }


    function insertDiv_1(kilIndex,oldSectionIndex) {
        section_index += 1;
        let sectionIndex = section_index

        let startKil = $(`#startKil_${kilIndex}_${oldSectionIndex}`).val();
        let endKilKil = $(`#endKil_${kilIndex}_${oldSectionIndex}`).val();

        var billingTypeHtml = '';
        for ( var i = 0; i < billing_type.length; i++) {
            billingTypeHtml += `
                <option value="${billing_type[i].dictValue}" data-rate="${billing_type[i].numVal1}">
                    ${billing_type[i].dictLabel}
                </option>
            `
        }

        var  appendHtml=`<tr id="kilTr_${kilIndex}_${sectionIndex}">
                                <td>
                                    <input name="contractpcSectionList[${sectionIndex}].startKil"
                                           id="startKil_${kilIndex}_${sectionIndex}"
                                           value="${startKil}"
                                           style="margin-right: 5px;" class="form-control" placeholder="始" type="hidden">
                                    <input name="contractpcSectionList[${sectionIndex}].endKil"
                                           id="endKil_${kilIndex}_${sectionIndex}"
                                           value="${endKilKil}"
                                           class="form-control" placeholder="终" type="hidden">
                                    <div style="display: flex; align-items: center;">
                                        <a class="close-link del-alink show-alink" onclick="showDivDel(${kilIndex},${sectionIndex},1)" title="删除">-</a>
                                    </div>
                                </td>
                                <td>
                                    <input class="form-control" placeholder="区间开始" type="text"
                                                oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                                name="contractpcSectionList[${sectionIndex}].startSection">
                                </td>
                                <td>
                                    <div class="flex">
                                        <select name="contractpcSectionList[${sectionIndex}].startOperator" class="form-control">
                                            <option value="0">＜</option>
                                            <option value="1">≤</option>
                                        </select>

                                        <div style="margin: 0 10px;">x</div>

                                        <select name="contractpcSectionList[${sectionIndex}].endOperator" class="form-control">
                                            <option value="2">＜</option>
                                            <option value="3">≤</option>
                                        </select>
                                    </div>

                                </td>
                                <td>
                                    <input class="form-control" placeholder="区间结束" type="text"
                                                oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                                name="contractpcSectionList[${sectionIndex}].endSection">
                                </td>
                                <td>
                                    <input class="form-control" placeholder="合同价" type="text" required
                                    oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                    name="contractpcSectionList[${sectionIndex}].guidingPrice">
                                </td>
                                <td>
                                    <input class="form-control" placeholder="成本价" type="text"
                                    oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                    name="contractpcSectionList[${sectionIndex}].costPrice">
                                </td>
                                <td>
                                    <select name="contractpcSectionList[${sectionIndex}].costBillingType"
                                            class="form-control valid" aria-invalid="false" aria-required="true">
                                            <option value=""></option>
                                            ${billingTypeHtml}
                                    </select>
                                </td>

                                <td>
                                    <select name="contractpcSectionList[${sectionIndex}].isFixedPrice"
                                            onchange="handlePriceChange(${sectionIndex})"
                                            class="form-control">
                                        <option value="0" selected>单价</option>
                                        <option value="1" >一口价</option>
                                        <option value="2" >起步价</option>
                                    </select>
                                </td>
                                <td>
                                    <input class="form-control" placeholder="送货费" type="text"
                                    oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                    name="contractpcSectionList[${sectionIndex}].deliveryFee">
                                </td>

                            </tr>`

        var $td = $(`#kilTd_${kilIndex}`);
        // 获取原始data-ind值
        var origDataInd = $td.data('ind');
        // 减1
        var newDataInd = origDataInd + 1;
        // 设置新的rowspan和data-ind
        $td.attr('rowspan', newDataInd);
        $td.data('ind', newDataInd);

        $(`#kilTr_${kilIndex}_${oldSectionIndex}`).after(appendHtml);
        return false;
    }

    function changeKil(obj, type, kilIndex) {
        let val = $(obj).val();
        if (type === 0) {
            $(`[id^=startKil_${kilIndex}_]`).val(val);
        } else if (type === 1) {
            $(`[id^=endKil_${kilIndex}_]`).val(val);
        }
    }
    function init(provinceId, cityId, areaId, province, city, area) {
        $('#'+ provinceId +' option').remove();
        $('#'+ cityId +' option').remove();
        $('#'+ areaId +' option').remove();
        $('#' + provinceId).append("<option value='' selected disabled hidden>-- 请选择 --</option>");
        $('#'+ cityId).append("<option value='' selected disabled hidden>-- 请选择 --</option>");
        // $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");

        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            async: false,
            success: function (result) {
                for (var i in result) {
                    $('#' + provinceId).append("<option value='" + result[i].PROVINCE_CODE + "'>" + result[i].PROVINCE_NAME + "</option>")
                }
                $('#'+ provinceId +' option').each(function () {
                    if ($(this).val() == province) {
                        $(this).attr("selected", true)
                    }
                })
            }
        });
        if (province !== undefined) {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + province,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }
                    $('#'+ cityId +' option').each(function () {
                        if ($(this).val() == city) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + city,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                    }
                    $('#'+ areaId +' option').each(function () {
                        if ($(this).val() == area) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
        }

        $('#'+ provinceId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ cityId +' option').remove();
                    $('#'+ areaId +' option').remove();
                    $('#'+ cityId).append("<option value='' selected disabled hidden>-- 请选择 --</option>");
                    // $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }

                }
            });
        });

        $('#'+ cityId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ areaId +' option').remove();
                    // $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");
                    for (var i in result) {
                        $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                    }
                    $('#'+ areaId).selectpicker('refresh');
                }
            });
        });
    }


    function handlePriceChange(index) {
        var $startSectionInput = $('input[name="contractpcSectionList[' + index + '].startSection"]');
        var $startOperatorSelect = $('select[name="contractpcSectionList[' + index + '].startOperator"]');
        var $endOperatorSelect = $('select[name="contractpcSectionList[' + index + '].endOperator"]');
        var $isFixedPriceSelect = $('select[name="contractpcSectionList[' + index + '].isFixedPrice"]');

        if ($isFixedPriceSelect.val() == "2") {
            // 选中"起步价"时
            $startSectionInput
                .prop('readonly', true)
                .val('0')
                .addClass('price-readonly');

            $startOperatorSelect
                .prop('readonly', true)
                .val('1')
                .addClass('price-readonly');

            $endOperatorSelect
                .prop('readonly', true)
                .val('3')
                .addClass('price-readonly');
        } else {
            // 其他选项时
            $startSectionInput
                .prop('readonly', false)
                .removeClass('price-readonly');

            $startOperatorSelect
                .prop('readonly', false)
                .removeClass('price-readonly');

            $endOperatorSelect
                .prop('readonly', false)
                .removeClass('price-readonly');
        }
    }


</script>
</body>

</html>