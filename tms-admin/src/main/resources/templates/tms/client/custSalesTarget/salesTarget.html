<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/css/bootstrap-editable.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.css}" rel="stylesheet"/>

</head>
<style>
    .custom-cell-background-red {
        background-color: rgb(255 0 0 / 43%) !important; /* 自定义的背景颜色 */
    }
    .custom-cell-background-red a {
        color: #FFFFFF;
    }

    .custom-cell-background-green {
        background-color: #8DBF8B !important; /* 自定义的背景颜色 */
    }
    .custom-cell-background-green a {
        color: #FFFFFF;
    }

    .lf{
        margin-left: 10px;
        background-color: #ffffff;
        border-radius: 3px;
        cursor:pointer;
    }

    .label-success{
        color: #1ab394;
        background-color: transparent;
        border: 1px solid #1ab394;
    }
    .label-primary{
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }
    .label-warning{
        color: #f8ac59;
        background-color: transparent;
        border: 1px solid #f8ac59;
    }

    .customer-stats {
        font-size: 16px;
    }

    .customer-stats span {
        margin-left: 10px;
        font-weight: 550;
    }

    .customer-stats span:nth-child(2) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
        color: #FF6C00;
    }
    .customer-stats span:nth-child(3) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
    }

    .customer-stats span:nth-child(4) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
        color: #0d62bb;
    }

    .table-light {
        background-color: #f8f9fa; /* 设置背景色为浅灰色 */
    }

    .progress-wrapper {
        position: relative;
        width: 100%;
        height: 20px;
        background-color: #f5f5f5;
        border-radius: 4px;
        overflow: hidden;
    }

    .progress {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .progress-bar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        background-color: #4caf50;
        transition: width 0.3s ease;
    }

    .progress-text {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        line-height: 20px;
        padding: 0 10px;
        font-size: 12px;
    }

    .tagged-div {
        position: relative; /* 设置为相对定位，使内嵌元素定位相对于此元素 */
    }

    .tag {
        position: absolute; /* 设置为绝对定位，使其相对于包含它的 .tagged-div 定位 */
        top: -14px; /* 距离顶部为0 */
        left: -5px; /* 距离左侧为0 */
        background-color: #d2e7f9; /* 标记的背景颜色 */
        padding: 1px 3px; /* 内边距 */
        border-radius: 6px; /* 边框圆角 */
        font-size: x-small; /* 字体大小 */
    }

    .sticky-header-container {
        pointer-events: none;
    }
     /* 添加样式禁用没有权限的用户点击 */
     .no-permission {
        pointer-events: none !important;
        cursor: default !important;
        color: #666 !important;
    }

</style>
<body class="gray-bg">
<div class="col-sm-12 search-collapse">
    <form id="role-form" class="form-horizontal">
        <div class="row no-gutter">
            <div class="col-md-2 col-sm-2">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <input name="custAbbr" id="custAbbr" placeholder="客户简称" class="form-control" type="text" maxlength="20" autocomplete="off">                    </div>
                </div>
            </div>
            <div class="col-md-1 col-sm-1">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <select  id="mgmtDeptId" placeholder="管理部" class="form-control valid noselect2 selectpicker"
                                onchange="changeMgmtDept()" aria-invalid="false" data-none-selected-text="管理部" multiple>
                            <option th:each="dept : ${mgmtDept}" th:value="${dept.deptId}"
                                    th:text="${dept.deptName}"></option>
                        </select>
                        <input type="hidden" name="mgmtDeptId" value="">

                    </div>
                </div>
            </div>
            <div class="col-md-1 col-sm-1">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <select  id="salesId" placeholder="运营部" class="form-control valid noselect2 selectpicker"
                                onchange="changeSalesId()" aria-invalid="false" data-none-selected-text="运营部" multiple>
                        </select>
                        <input type="hidden" name="salesId" value="">

                    </div>
                </div>
            </div>
            <div class="col-md-1 col-sm-1">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <select  id="salesDept" placeholder="运营组" class="form-control valid noselect2 selectpicker"
                                onchange="changeSalesDept()" aria-invalid="false" data-none-selected-text="运营组" multiple>
<!--                            <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"-->
<!--                                    th:text="${mapS.deptName}"></option>-->
                        </select>
                        <input type="hidden" name="salesDept" value="">

                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-2">
                <div class="form-group">
                    <div class="col-sm-12">
                        <input id="month" type="text" class=" form-control"
                               placeholder="起始月份 - 结束月份" autocomplete="off">
                        <input name="month1" id="month1" type="hidden" th:value="${month1}">
                        <input name="month2" id="month2" type="hidden" th:value="${month2}">
                        <input name="month3" id="month3" type="hidden" th:value="${month3}">
                    </div>
                </div>
            </div>

            <div class="col-md-3 col-sm-3">
                <!--                        <label class="col-sm-4"></label>-->
                <div class="form-group" style="text-align: left;">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    <a  class="btn btn-info btn-rounded btn-sm" onclick="targetImport()" shiro:hasPermission="tms:salesTarget:import"><i class="fa fa-download"></i>&nbsp;目标导入</a>
                    <span id="buttons-toolbar"></span>
                </div>

            </div>

        </div>
        <div class="row no-gutter">

        </div>
    </form>
</div>

<div class="col-sm-12 select-table  ">
    <div class="row no-gutter">
<!--        <div class="col-md-2 col-sm-2">-->
<!--            <div class="customer-stats">-->
<!--                &lt;!&ndash;        <span>客户总数：</span><span>1111</span> <span>未设定目标：</span><span>1111</span>&ndash;&gt;-->
<!--                <span>完成情况：</span><span id="cntSpan1"></span><span>/</span><span id="cntSpan2"></span>-->
<!--            </div>-->
<!--        </div>-->
        <div class="col-md-12 col-sm-12">
            <div>
                <table class="table table-bordered ">
                    <thead class="table-light">
                    <tr>
                        <th id="month1Total" style="text-align: center; " colspan="4">[[${month1}]]总营收</th>
                        <th id="month2Total" style="text-align: center; " colspan="4">[[${month2}]]总营收</th>
                        <th id="month3Total" style="text-align: center; " colspan="4">[[${month3}]]总营收</th>
                        <th style="text-align: center;" colspan="2">下月目标额</th>
                    </tr>
                    <tr>
<!--                        <th>总营收</th>-->
                        <th style="text-align: center;" colspan="2">营收目标/完成率</th>
                        <th style="text-align: center;" colspan="2">利润目标额/完成率</th>
<!--                        <th>总营收</th>-->
                        <th style="text-align: center;" colspan="2">营收目标/完成率</th>
                        <th style="text-align: center;" colspan="2">利润目标额/完成率</th>
<!--                        <th>总营收</th>-->
                        <th style="text-align: center;" colspan="2">营收目标/完成率</th>
                        <th style="text-align: center;" colspan="2">利润目标额/完成率</th>

                        <th style="text-align: center;" colspan="1">营收目标</th>
                        <th style="text-align: center;" colspan="1">利润目标额</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td id="targetAmount1Total" style="text-align: right;">-</td>
                        <td id="targetAmount1Rate" style="text-align: right;">-</td>
                        <td id="targetNetProfit1Total" style="text-align: right;">-</td>
                        <td id="targetNetProfit1Rate" style="text-align: right;">-</td>

                        <td id="targetAmount2Total" style="text-align: right;">-</td>
                        <td id="targetAmount2Rate" style="text-align: right;">-</td>
                        <td id="targetNetProfit2Total" style="text-align: right;">-</td>
                        <td id="targetNetProfit2Rate" style="text-align: right;">-</td>

                        <td id="targetAmount3Total" style="text-align: right;">-</td>
                        <td id="targetAmount3Rate" style="text-align: right;">-</td>
                        <td id="targetNetProfit3Total" style="text-align: right;">-</td>
                        <td id="targetNetProfit3Rate" style="text-align: right;">-</td>

                        <td id="targetAmountTotal" style="text-align: right;">-</td>
                        <td id="targetNetProfitTotal" style="text-align: right;">-</td>
                    </tr>
                    </tbody>
                </table>

            </div>

        </div>
    </div>
    <table id="bootstrap-table"
           data-buttons-toolbar="#buttons-toolbar"
           class="table table-striped table-responsive table-bordered table-hover" >
    </table>
</div>

<div class="container-div">
    <div class="row">

    </div>
</div>

<script id="profitHtml" type="text/template">
    <div class="form-content">
        <div>
            <div style="margin-bottom: 10px;">
                <div class="row">
                    <!-- 将利润总览拉长为一整行 -->
                    <div class="col-md-12">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <h3 class="panel-title">利润总览</h3>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-3" style="border-right: 1px solid #eee; display: flex; flex-direction: column; justify-content: center; align-items: center; min-height: 80px;">
                                        <div style="margin-bottom: 5px;">
                                            <p id="khmc" style="font-size: 20px; font-weight: 700; color: #1c84c6; margin: 0; line-height: 1.2;">-</p>
                                        </div>
                                        <div>
                                            <p id="lrrq" style="font-size: 16px; color: #676a6c; margin: 0;">-</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <h4>利润目标</h4>
                                        <h2 id="lrmb" style="color: #1c84c6; font-weight: bold;">-</h2>
                                    </div>
                                    <div class="col-md-3">
                                        <h4>利润总额</h4>
                                        <h2 id="lrze" style="color: #1ab394; font-weight: bold;">-</h2>
                                    </div>
                                    <div class="col-md-3">
                                        <h4>利润率</h4>
                                        <h2 id="lrl" style="color: #ed5565; font-weight: bold;">-</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 第一类费用：收支类 -->
            <table class="table table-bordered table-hover">
                <thead>
                <tr class="active">
                    <th style="width: 16%;">应收</th>
                    <th style="width: 16%;">应付成本</th>
                    <th style="width: 16%;">应付税金</th>
                    <th style="width: 16%;">三方费用</th>
                    <th style="width: 16%;">三方税金</th>
                    <th style="width: 16%;">平台费</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td id="hsys">-</td>
                    <td id="yfcb">-</td>
                    <td id="yfsf">-</td>
                    <td id="sffy">-</td>
                    <td id="sfsf">-</td>
                    <td id="ptf">-</td>
                </tr>
                </tbody>
            </table>

            <!-- 第二类费用：费用一 -->
            <div style="margin-top: 10px;">
                <h4 style="font-weight: bold; color: #1c84c6; border-bottom: 1px solid #eee; padding-bottom: 5px;">费用一</h4>
                <table class="table table-bordered table-hover">
                    <thead>
                    <tr class="active">
                        <th style="width: 20%;">逾期资金成本</th>
                        <th style="width: 20%;">费用报销</th>
                        <th style="width: 20%;">费用领用</th>
                        <th style="width: 20%;">货损赔付</th>
                        <th style="width: 20%;">保证金</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td id="yqzjcb">-</td>
                        <td id="fybx">-</td>
                        <td id="fyly">-</td>
                        <td id="hspf">-</td>
                        <td id="bzj">-</td>
                    </tr>
                    </tbody>
                </table>
            </div>

            <!-- 第三类费用：费用二 -->
            <div style="margin-top: 10px;">
                <h4 style="font-weight: bold; color: #1c84c6; border-bottom: 1px solid #eee; padding-bottom: 5px;">费用二</h4>
                <table class="table table-bordered table-hover">
                    <thead>
                    <tr class="active">
                        <th style="width: 20%;">承兑贴息</th>
                        <th style="width: 20%;">人员工资</th>
                        <th style="width: 20%;">社保费用</th>
                        <th style="width: 20%;">公司管理费</th>
                        <th style="width: 20%;">资金成本</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td id="cdtx">-</td>
                        <td id="gz">-</td>
                        <td id="sb">-</td>
                        <td id="gsglf">-</td>
                        <td id="beforezjcb">-</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div>
            <div style="margin-top: 15px;font-size: 20px">保证金：</div>
            <table class="table table-bordered mt5">
                <thead>
                    <tr>
<!--                        <td>客户简称</td>-->
                        <td>保证金类型</td>
                        <td>保证金</td>
                        <td>申请人</td>
                        <td>申请时间</td>
                        <td>付款时间</td>
                        <td>成本天数</td>
                        <td>年息</td>
                        <td>保证金成本</td>
                    </tr>
                </thead>
                <tbody id="bzjTbody">
                    <tr><td colspan="9" style="text-align: center;">暂无数据</td></tr>
                </tbody>

            </table>
        </div>

<!--        <div>
            <div style="margin-top: 15px;font-size: 20px">费用报销：</div>
            <table class="table table-bordered mt5">
                <thead>
                    <tr>
                        <td>客户简称</td>
                        <td>标题</td>
                        <td>报销事由</td>
                        <td>申请人</td>
                        <td>报销时间</td>
                        <td>金额</td>
                    </tr>
                </thead>
                <tbody id="fybxTbody">
                    <tr><td colspan="6" style="text-align: center;">暂无数据</td></tr>
                </tbody>

            </table>
        </div>-->

        <div>
            <div style="margin-top: 15px;font-size: 20px">货损赔付：</div>
            <table class="table table-bordered mt5">
                <thead>
                    <tr>
<!--                        <td>客户简称</td>-->
                        <td>发货单号</td>
                        <td>异常说明</td>
                        <td>发生时间</td>
                        <td>损失金额</td>
                    </tr>
                </thead>
                <tbody id="hspfTbody">
                    <tr><td colspan="5" style="text-align: center;">暂无数据</td></tr>
                </tbody>

            </table>
        </div>
    </div>
</script>

<th:block th:include="include :: footer"/>
<!--<th:block th:include="include :: bootstrap-table-editable-js"/>-->
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/tableExport.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/js/bootstrap-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/editable/bootstrap-table-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/bootstrap-table-export.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>


<script th:inline="javascript">
    var prefix = ctx + "tms/custSalesTarget";
    var hasEditPermission = [[${@permission.hasAnyPermissions('tms:sales_target:edit_target')}]];

    $(function () {
        let options = initOptions([[${month1}]],[[${month2}]],[[${month3}]]);
        $.table.init(options);

        getCount([[${month1}]],[[${month2}]],[[${month3}]])
    });

    function initOptions(month1,month2,month3) {
        let month1Str = formatMonthString(month1)
        let month2Str = formatMonthString(month2)
        let month3Str = formatMonthString(month3)

        let nextMonth = formatMonthString([[${nextMonth}]])
        return {
            url: prefix + "/avg_stats",
            showToggle:false,
            showColumns:false,
            showSearch:false,
            pagination:false,
            showRefresh:false,
            modalName: "目标额",
            showExport: true,
            exportTypes:['excel'],
            exportOptions:{
                // fileName:""
            },
            height: 560,
            stickyHeader: true,  // 启用固定表头功能
            stickyHeaderOffsetY: 0,  // 可选，调整固定表头距离顶部的距离

            onEditableSave: function (field, row, oldValue, $el){
                // 再次检查权限
                if (!hasEditPermission) {
                    $.modal.msgError("您没有权限执行此操作");
                    // 刷新表格，恢复原值
                    $.btTable.bootstrapTable('refresh');
                    return;
                }

                let data = {};
                if(field=="targetAmount"){
                    data = {customerId: row.customerId, price: row.targetAmount,priceType:0}
                }else{
                    data = {customerId: row.customerId, price: row.profitRate,priceType:1}
                }

                $.ajax({
                    url: ctx + "tms/custSalesTarget/edit_target",
                    type: "post",
                    dataType: "json",
                    data: data,
                    success: function (result) {
                        if (result.code === 0) {
                            var data = result.data;
                            //刷新
                            $.btTable.bootstrapTable('refresh', {
                                silent: true
                            });
                            getCount(month1,month2,month3)
                        } else {
                            $.modal.msgError(result.msg);
                            //刷新
                            $.btTable.bootstrapTable('refresh', {
                                silent: true
                            });
                            getCount(month1,month2,month3)
                        }
                    }
                });
            },
            columns: [
                [
                    {
                        title: '运营部',
                        valign: 'middle',
                        align: 'center',
                        field : 'salesName',
                        rowspan:2
                    },
                    {
                        title: '运营组',
                        align: 'center',
                        valign: 'middle',
                        field: 'salesDeptName',
                        rowspan:2
                    },
                    {
                        title: '客户简称',
                        align: 'center',
                        valign: 'middle',
                        field : 'custAbbr',
                        rowspan:2
                    },

                    {
                        title: month1Str,
                        align: 'center',
                        colspan:2
                    },
                    {
                        title: month2Str,
                        align: 'center',
                        colspan:2
                    },
                    {
                        title: month3Str,
                        align: 'center',
                        colspan:2
                    },
                    {
                        title: nextMonth + `<a class="btn btn-xs " href="javascript:void(0)" title="下载下月数据" onclick="exportData()"><i class="fa fa-download" style="font-size: 15px;"></i></a>` ,
                        align: 'center',
                        colspan:2
                    },
                ],
                [
                    {
                        title: '营收额 / 目标（万）',
                        align: 'right',
                        field: 'sum1',
                        formatter: function (value, row, index) {
                            return formatSalesTarget(value, row.targetAmount1);
                        }
                    },
                    {
                        title: '利润完成率',
                        align: 'right',
                        field: 'profitSum1',
                        formatter: function (value, row, index) {
                            return formatProfitRate(value, row.targetNetProfit1, row.sum1, row.custProfit1, row.custAbbr, month1);
                        }
                    },
                    {
                        title: '营收额 / 目标（万）',
                        align: 'right',
                        field: 'sum2',
                        formatter: function (value, row, index) {
                            return formatSalesTarget(value, row.targetAmount2);
                        }

                    },
                    {
                        title: '利润完成率',
                        align: 'right',
                        field: 'profitSum2',
                        formatter: function (value, row, index) {
                            return formatProfitRate(value, row.targetNetProfit2, row.sum2, row.custProfit2, row.custAbbr, month2);
                        }
                    },
                    {
                        title: '营收额 / 目标（万）',
                        align: 'right',
                        field: 'sum3',
                        formatter: function (value, row, index) {
                            return formatSalesTarget(value, row.targetAmount3);
                        }

                    },
                    {
                        title: '利润完成率',
                        align: 'right',
                        field: 'profitSum3',
                        formatter: function (value, row, index) {
                            return formatProfitRate(value, row.targetNetProfit3, row.sum3, row.custProfit3, row.custAbbr, month3);

                        }
                    },
                    {
                        title: '营收目标（万）',
                        field: 'targetAmount',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            if (value) {
                                return value
                            }else {
                                return ''
                            }
                        },
                        editable:{
                            type: 'text',
                            title: '修改营收目标额',
                            emptytext: '—',
                            validate:  function (v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            }
                        }

                    },
                    {
                        title: '利润率（%）',
                        field: 'profitRate',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            if (value) {
                                return value
                            }else {
                                return ''
                            }
                        },
                        editable: {
                            type: 'text',
                            title: '修改利润率',
                            emptytext: '—',
                            validate: function (v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            }
                        }
                
                    },
                ]
            ],
            onLoadSuccess: function(data) {
                let fieldList = ["salesName"];
                mergeCells(data.rows, 1,  $('#bootstrap-table'), fieldList);

                // 如果没有权限，添加CSS样式禁用点击
                if (!hasEditPermission) {
                    // 移除所有可编辑元素的点击事件和样式
                    $("a[data-name='targetAmount'], a[data-name='profitRate']").each(function() {
                        $(this).off('click').removeClass('editable-click').css({
                            'cursor': 'default',
                            'pointer-events': 'none'
                        }).attr('href', 'javascript:void(0)');

                        // 移除editable-empty类，防止鼠标悬停效果
                        $(this).removeClass('editable-empty');

                        // 添加自定义类，用于CSS样式控制
                        $(this).addClass('no-permission');

                        // 移除data-*属性，彻底禁用editable功能
                        $(this).removeAttr('data-pk').removeAttr('data-value');
                    });
                }

            }
        };
    }


    /**
     *
     * @param value         营收额
     * @param targetAmount  目标额
     * @returns {string}
     */
    function formatSalesTarget(value, targetAmount) {
        let sum;
        if (value != null) {
            sum = Number((value/10000).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'})
        } else {
            sum = '-'
        }

        let targetAmountFormatted;
        if (targetAmount != null) {
            targetAmountFormatted = Number((targetAmount / 10000).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'})
        } else {
            targetAmountFormatted = '-'
        }

        let rateHtml = '';
        if (sum != '-' && targetAmountFormatted != '-') {
            let rate
            if (targetAmount != 0) {
                rate = value / targetAmount * 100;
                rate = parseFloat(rate).toFixed(2);
            }else{
                if (value != 0) {
                    rate = 100;
                }else {
                    rate = 0;
                }
            }

            rateHtml =
                `<div class="progress-wrapper">
                    <div class="progress">
                        <div class="progress-bar" style="width: ${rate}%;"></div>
                        <div class="progress-text">${rate}%</div>
                    </div>
                </div>`;
        }

        return `<div>${sum} / ${targetAmountFormatted}</div><div>${rateHtml}</div>`
    }


    /**
     *
     * @param value             净利润 合计
     * @param targetNetProfit   净利润 目标
     * @param sum               总营收 合计价格
     * @param custProfit
     * @param custAbbr
     * @param month
     * @returns {string}
     */
    function formatProfitRate(value, targetNetProfit, sum, custProfit, custAbbr, month) {
        let profitSum;
        if (value != null) {
            profitSum = Number((value / 10000).toFixed(2)).toLocaleString('zh', {
                style: 'currency',
                currency: 'CNY'
            });
        } else {
            profitSum = '-';
        }

        let targetNetProfitFormatted;
        if (targetNetProfit != null) {
            targetNetProfitFormatted = Number((targetNetProfit / 10000).toFixed(2)).toLocaleString('zh', {
                style: 'currency',
                currency: 'CNY'
            });
        } else {
            targetNetProfitFormatted = '-';
        }

        let rateHtml = '';
        if (profitSum != '-' && targetNetProfitFormatted != '-' && targetNetProfit != 0) {
            let rate = value / targetNetProfit * 100;
            rate = parseFloat(rate).toFixed(2);

            let cl = '';
            if (rate < 0) {
                cl = 'color: #ed5565;';
            }

            rateHtml =
                `<div class="progress-wrapper">
                    <div class="progress">
                        <div class="progress-bar" style="width: ${rate}%;"></div>
                        <div class="progress-text" style="${cl}">${rate}%</div>
                    </div>
                </div>`;
        }

        /*    let profitRateHtml = `<div style="flex: 2;"></div>`;
    if (row.profitRate3) {
        profitRateHtml =
            `
             <div style="flex: 2; padding-top: 4px;;background: #93C4F8;font-size: x-small;border-radius: 6px;white-space: nowrap;">
                    标${row.profitRate3}%
             </div>
            `
    }

    let realRateHtml = `<div style="flex: 2;margin-left: 2px;"></div>`
    if (value != '' && value!=null && row.sum3) {
        let rate = value / row.sum3 * 100;
        rate = parseFloat(rate).toFixed(2);

        let bg = '#93C4F8'
        if (rate < 0) {
            bg = '#ed5565'
        }

        realRateHtml =
            `<div style="flex: 2; padding-top: 4px;;background: ${bg};font-size: x-small;border-radius: 6px;white-space: nowrap;margin-left: 2px;">
                    实${rate}%
            </div>`;

    }

    let custProfit3 = encodeURI(JSON.stringify(row.custProfit3));
    return `<div><a onclick="openDetail('${custProfit3}')">${profitSum}</a> / ${targetNetProfit}</div>
            <div style="">
                <div>${rateHtml}</div>
                <div style="display: flex;padding-top: 2px;">
                    ${profitRateHtml}
                    ${realRateHtml}
                 </div>
            </div>`;*/

        let custProfitEncoded = encodeURI(JSON.stringify(custProfit));

        return `<div style="cursor:pointer;"
                     onclick="openDetail('${custProfitEncoded}','${sum}','${targetNetProfit}','${custAbbr}','${month}')">
                    ${rateHtml}
                </div>`;
    }

    /**
     * 合并单元格
     *
     */
    function mergeCells(data, colspan, target, sameFiled) {
        sameFiled.forEach(res=>{
            for (var i = 0; i < data.length; i++) {
                data[i][res+'_rows'] = 1;
                for (let j = i + 1; j < data.length; j++) {
                    if (res == 'deliName') {
                        if (data[i][res] == data[j][res]) {
                            data[i][res+'_rows'] = (data[i][res+'_rows'] || 1) + 1;
                            data[i]['f'] = i;
                            data[j]['f'] = i;
                        } else {
                            break;
                        }
                    } else {
                        if (data[i][res] == data[j][res] && data[i]['f'] == data[j]['f']) {
                            data[i][res+'_rows'] = (data[i][res+'_rows'] || 1) + 1;
                        } else {
                            break;
                        }
                    }
                }
                i = i+ data[i][res+'_rows'] - 1;
            }
            for (var i = 0; i < data.length; i++) {
                if (data[i][res+ "_rows"] > 1) {
                    target.bootstrapTable('mergeCells', {index: i, field: res, colspan: 1, rowspan: data[i][res+ "_rows"]});
                }
            }
        })
    }

    /**
     * 搜索的方法
     */
    function searchPre() {
        let options = initOptions($("#month1").val(),$("#month2").val(),$("#month3").val());

        $.table.destroy()
        $.table.init(options);


        getCount($("#month1").val(),$("#month2").val(),$("#month3").val())
        // var data = {};
        // $.table.search('role-form', data);
    }

    function targetImport(){
        layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入数据',
            content: $('#importTpl').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var index = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#file')[0].files[0]);
                $.ajax({
                    url: prefix + "/targetImport",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        console.log(result);
                        console.log(result.msg);
                        console.log(result.code);
                        if (result.code == web_status.SUCCESS) {
                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code == web_status.WARNING) {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });
    }

    function getCount(month1, month2, month3) {
        // $("#month1Total").text(formatMonthString(month1)+'总营收')
        // $("#month2Total").text(formatMonthString(month2)+'总营收')
        // $("#month3Total").text(formatMonthString(month3)+'总营收')

        var search = $.common.formToJSON('role-form');

        $.ajax({
            url: ctx + "tms/custSalesTarget/avg_stats/count",
            type: "post",
            dataType: "json",
            data: search,
            success: function (result) {
                if (result.code === 0) {
                    var data = result.data;


                    let month1TotalHtml = `${formatMonthString(month1)}总营收：
                                                <span style="color: #0d62bb;font-size: 1.2em;">
                                                    ${Number((data.sum1Total/10000).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'})}万
                                                </span>`
                    $("#month1Total").html(month1TotalHtml)

                    let month2TotalHtml = `${formatMonthString(month2)}总营收：
                                                <span style="color: #0d62bb;font-size: 1.2em;">
                                                    ${Number((data.sum2Total/10000).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'})}万
                                                </span>`
                    $("#month2Total").html(month2TotalHtml)

                    let month3TotalHtml = `${formatMonthString(month3)}总营收：
                                                <span style="color: #0d62bb;font-size: 1.2em;">
                                                    ${Number((data.sum3Total/10000).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'})}万
                                                </span>`
                    $("#month3Total").html(month3TotalHtml)

                    // $("#cntSpan1").text(data.setCount)
                    // $("#cntSpan2").text(data.customerCount)


                    if (data.targetAmountTotal) {
                        $("#targetAmountTotal").text(Number((data.targetAmountTotal).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'}) + '万');
                    }else {
                        $("#targetAmountTotal").text('-');
                    }

                    if (data.targetNetProfitTotal) {
                        $("#targetNetProfitTotal").text(Number((data.targetNetProfitTotal/10000).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'})+ '万');
                    }else {
                        $("#targetNetProfitTotal").text('-');
                    }

                    if (data.targetNetProfit1Total) {
                        $("#targetNetProfit1Total").text(Number((data.targetNetProfit1Total/10000).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'})+ '万');
                    }else {
                        $("#targetNetProfit1Total").text('-');
                    }
                    if (data.targetNetProfit2Total) {
                        $("#targetNetProfit2Total").text(Number((data.targetNetProfit2Total/10000).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'})+ '万');
                    }else {
                        $("#targetNetProfit2Total").text('-');
                    }
                    if (data.targetNetProfit3Total) {
                        $("#targetNetProfit3Total").text(Number((data.targetNetProfit3Total/10000).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'})+ '万');
                    }else {
                        $("#targetNetProfit3Total").text('-');
                    }

                    if (data.targetAmount1Total) {
                        $("#targetAmount1Total").text(Number((data.targetAmount1Total/10000).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'})+ '万');
                    }else {
                        $("#targetAmount1Total").text('-');
                    }
                    if (data.targetAmount2Total) {
                        $("#targetAmount2Total").text(Number((data.targetAmount2Total/10000).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'})+ '万')
                    }else {
                        $("#targetAmount2Total").text('-')
                    }
                    if (data.targetAmount3Total) {
                        $("#targetAmount3Total").text(Number((data.targetAmount3Total/10000).toFixed(2)).toLocaleString('zh', {style: 'currency', currency: 'CNY'})+ '万')
                    }else {
                        $("#targetAmount3Total").text('-')
                    }

                    if (data.sum1Total && data.targetAmount1Total) {
                        let rate1 = parseFloat(data.sum1Total / data.targetAmount1Total * 100).toFixed(2);
                        $("#targetAmount1Rate").text(rate1+'%');
                    }else {
                        $("#targetAmount1Rate").text('-');
                    }
                    if (data.sum2Total && data.targetAmount2Total) {
                        let rate2 = parseFloat(data.sum2Total / data.targetAmount2Total * 100).toFixed(2);
                        $("#targetAmount2Rate").text(rate2+'%')
                    }else {
                        $("#targetAmount2Rate").text('-')
                    }
                    if (data.sum3Total && data.targetAmount3Total) {
                        let rate3 = parseFloat(data.sum3Total / data.targetAmount3Total * 100).toFixed(2);
                        $("#targetAmount3Rate").text(rate3+'%')
                    }else {
                        $("#targetAmount3Rate").text('-')
                    }

                    if (data.profitSum1Total && data.targetNetProfit1Total) {
                        let rate1 = parseFloat(data.profitSum1Total / data.targetNetProfit1Total * 100).toFixed(2);
                        $("#targetNetProfit1Rate").text(rate1+'%');
                    }else {
                        $("#targetNetProfit1Rate").text('-');
                    }
                    if (data.profitSum2Total && data.targetNetProfit2Total) {
                        let rate2 = parseFloat(data.profitSum2Total / data.targetNetProfit2Total * 100).toFixed(2);
                        $("#targetNetProfit2Rate").text(rate2+'%')
                    }else {
                        $("#targetNetProfit2Rate").text('-')
                    }
                    if (data.profitSum3Total && data.targetNetProfit3Total) {
                        let rate3 = parseFloat(data.profitSum3Total / data.targetNetProfit3Total * 100).toFixed(2);
                        $("#targetNetProfit3Rate").text(rate3+'%')
                    }else {
                        $("#targetNetProfit3Rate").text('-')
                    }


                } else {
                    $("#month1Total").text(formatMonthString(month1)+'总营收')
                    $("#month2Total").text(formatMonthString(month2)+'总营收')
                    $("#month3Total").text(formatMonthString(month3)+'总营收')

                    // $("#cntSpan1").text('0')
                    // $("#cntSpan2").text('0')

                    $("#targetAmountTotal").text('-');
                    $("#targetNetProfitTotal").text('-');

                    $("#targetAmount1Total").text('-')
                    $("#targetAmount2Total").text('-')
                    $("#targetAmount3Total").text('-')

                    $("#targetNetProfit1Total").text('-');
                    $("#targetNetProfit2Total").text('-');
                    $("#targetNetProfit3Total").text('-');

                    $("#targetAmount1Rate").text('-');
                    $("#targetAmount2Rate").text('-')
                    $("#targetAmount3Rate").text('-')
                }
            }
        });

    }


    function changeMgmtDept() {
        $('input[name="mgmtDeptId"]').val($.common.join($('#mgmtDeptId').selectpicker('val')));

        $.ajax({
            url: ctx + "system/dept/opsDeptByMgmtDeptId",
            type: "post",
            dataType: "json",
            data: {mgmtDeptId: $('input[name="mgmtDeptId"]').val()},
            success: function (result) {
                $("#salesId").empty();
                $('input[name="salesId"]').val("")

                if (result.code == 0 && result.data != undefined) {
                    if (result.data != null) {
                        $.each(result.data, function (index, option) {
                            $("#salesId").append($('<option>', {
                                value: option.deptId,
                                text: option.deptName
                            }));
                        });
                    }
                }

                $("#salesId").selectpicker("refresh");
            }
        });
    }
    function changeSalesId() {
        $('input[name="salesId"]').val($.common.join($('#salesId').selectpicker('val')));

        $.ajax({
            url: ctx + "system/dept/opsGroupByOpsDeptId",
            type: "post",
            dataType: "json",
            data: {opsDeptId: $('input[name="salesId"]').val()},
            success: function (result) {
                $("#salesDept").empty();
                $('input[name="salesDept"]').val("")

                if (result.code == 0 && result.data!=undefined) {

                    if (result.data != null) {
                        $.each(result.data, function(index, option) {
                            $("#salesDept").append($('<option>', {
                                value: option.deptId,
                                text: option.deptName
                            }));
                        });

                    }
                }
                $("#salesDept").selectpicker("refresh");

            }
        });
    }
    function changeSalesDept() {
        $('input[name="salesDept"]').val($.common.join($('#salesDept').selectpicker('val')));

    }


    let flg = 1
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        //要求提货日期
        let myDate = laydate.render({
            elem: "#month",
            type: "month",
            id: "month",
            range: true,
            rangeLinked: true,
            value: [[${month1}]] + " - " + [[${month3}]],
            change: function(value, date, endDate){
                let sDate = new Date(date.year + '-' + date.month);
                let eDate = new Date(endDate.year + '-' + endDate.month);

                // 获取开始日期的年份和月份
                let startYear = sDate.getFullYear();
                let startMonth = sDate.getMonth();

                // 获取结束日期的年份和月份
                let endYear = eDate.getFullYear();
                let endMonth = eDate.getMonth();

                // 计算日期差距的月份数
                let monthsDiff = (endYear - startYear) * 12 + (endMonth - startMonth) + 1;

                // 判断是否跨度三个月
                if (monthsDiff !== 3) {
                    flg = 0;
                    $(".laydate-btns-confirm").addClass("laydate-disabled");
                    // myDate.hint('日期跨度必须为三个月',1000);

                    laydate.hint('month', {
                        content: '日期跨度必须为三个月',
                        ms: 1000
                    });
                } else {
                    if (flg==0){$(".laydate-btns-confirm").removeClass("laydate-disabled");}
                    flg = 1;
                }
            },
            done: function(value, date, endDate){
                if (date.year) {
                    $("#month1").val(date.year + '-' + (date.month < 10 ? '0' + date.month : date.month));
                    $("#month2").val(date.year + '-' + ((date.month + 1) < 10 ? '0' + (date.month + 1) : (date.month + 1)));
                    $("#month3").val(date.year + '-' + ((date.month + 2) < 10 ? '0' + (date.month + 2) : (date.month + 2)))
                }else {
                    $("#month1").val("")
                    $("#month2").val("")
                    $("#month3").val("")
                }
            },
            shortcuts: [
                {
                    text: "去年同期",
                    value: function(){
                        return [subtractOneYear($("#month1").val()), subtractOneYear($("#month3").val())];
                    }()
                },
                {
                    text: "近三月",
                    value: function(){
                        let month1 =  [[${month1}]]
                        let month3 =  [[${month3}]]
                        return [month1, month3];
                    }()
                },
            ]
        });
    });


    function openEntrust(deliArea, arriArea, reqCarLen, reqCarType, amount, month, transType) {
        let url = ctx + "tms/reference_price/avg_stats/lot_detail?deliArea=" + deliArea + "&arriArea=" + arriArea + "&reqCarLen="
            + reqCarLen + "&reqCarType=" + reqCarType + "&month=" + month + "&transType=" + transType;

        if (amount) {
            url = url + "&amount=" + amount
        }
        var scrollTop = $(window).scrollTop();
        layer.open({
            type: 2,
            area: ['95%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "历史运单",
            content: [url,'no'],
            btn: ['关闭'],
            // scrollbar: true,
            cancel: function (index) {
                return true;
            },
            success: function () {
                $(window).scrollTop(scrollTop);
            },
            // end: function () {
            //     enableScroll();
            // }
        });
    }

    //验证字符串是否是数字
    function checkNumber(val) {
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val)){
            return true;
        }
        else{
            return false;
        }
    }

    function formatMonthString(dateString) {
        var date = new Date(dateString);
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        return year + '年' + month + '月';
    }
    function subtractOneYear(dateString) {
        var date = new Date(dateString);
        date.setFullYear(date.getFullYear() - 1);
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        if (month < 10) {
            month = '0' + month;
        }
        return year + '-' + month;
    }


    function openDetail(obj, sum, targetNetProfit, custAbbr, month) {
        let parse = JSON.parse(decodeURI(obj));
        if (parse == null) {
            return
        }

        layer.open({
            type: 1,
            title: '利润构成（元）',
            area: ['85%', '85%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: $('#profitHtml').html(),
            btn: ['关闭'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {
                $("#khmc").text(custAbbr)
                $("#lrrq").text(month)

                $("#hsys").text(parse.hsys.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#yfcb").text(parse.yfcb.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#yfsf").text(parse.yfsf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#sffy").text(parse.sffy.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#sfsf").text(parse.sfsf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#yqzjcb").text(parse.yqzjcb.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#fybx").text(parse.fybx.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#fyly").text(parse.fyly.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#hspf").text(parse.hspf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#bzj").text(parse.bzj.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#ptf").text(parse.ptf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))

                $("#cdtx").text(parse.cdtx.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#gz").text(parse.gz.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#sb").text(parse.sb.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#gsglf").text(parse.gsglf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#beforezjcb").text(parse.beforezjcb.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))

                $("#lrze").text(parse.lr.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                if (targetNetProfit != null && targetNetProfit != '') {
                    $("#lrmb").text(Number(targetNetProfit).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
                if (sum != null && sum != '') {
                    $("#zys").text(Number(sum).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
                if (parse.lr != '' && parse.lr!=null && sum) {
                    if (Number(sum) !== 0) {
                        let rate = parse.lr / sum * 100;
                        rate = parseFloat(rate).toFixed(2);
                        $("#lrl").text(rate + '%')
                    } else {
                        $("#lrl").text('0%');
                    }
                }


                let customerId = parse.customerId;
                let yearMonth = parse.yearMonth;
                $.ajax({
                    url: ctx + `tms/custSalesTarget/get_cust_profit?customerId=${customerId}&yearMonth=${yearMonth}`,
                    type: "get",
                    beforeSend: function () {

                    },
                    success: function (data) {
                        if (data.code == 0) {

                            let bzjVOList = data.data.bzjVOList;
                            $("#bzjTbody").empty();

                            let html = bzjVOList ===null || bzjVOList.length === 0 ? `<tr><td colspan="9" style="text-align: center;">暂无数据</td></tr>` : ``
                            bzjVOList.forEach(item => {
                                const roundedMarginCost = Math.round(item.marginCost * 100) / 100;
                                html += `
                                        <tr>
                                            <td>${item.marginType === 0 ? '借' : '贷'}</td>
                                            <td>${item.marginAmount}</td>
                                            <td>${item.applyUserName}</td>
                                            <td>${item.applyDate}</td>
                                            <td>${item.payDate}</td>
                                            <td>${item.costDays}</td>
                                            <td>${item.annualInterest}</td>
                                            <td>${roundedMarginCost}</td>
                                        </tr>
                                    `;
                            });
                            $("#bzjTbody").append(html);

                            // let fybxVOList = data.data.fybxVOList;
                            // $("#fybxTbody").empty();
                            //
                            // let fybxHtml = fybxVOList ===null || fybxVOList.length === 0 ? `<tr><td colspan="6" style="text-align: center;">暂无数据</td></tr>` : ``
                            // fybxVOList.forEach(item => {
                            //     const roundedMarginCost = Math.round(item.sumFyje * 100) / 100;
                            //
                            //     fybxHtml += `
                            //             <tr>
                            //                 <td>${item.custAbbr}</td>
                            //                 <td>${item.bt}</td>
                            //                 <td>${item.bxsy}</td>
                            //                 <td>${item.applyer}</td>
                            //                 <td>${item.writeOffTime}</td>
                            //                 <td>${roundedMarginCost}</td>
                            //             </tr>
                            //         `;
                            // });
                            // $("#fybxTbody").append(fybxHtml);


                            let hspfVOList = data.data.hspfVOList;
                            $("#hspfTbody").empty();

                            let hspfHtml = hspfVOList ===null || hspfVOList.length === 0 ? `<tr><td colspan="5" style="text-align: center;">暂无数据</td></tr>` : ``
                            hspfVOList.forEach(item => {
                                const roundedMarginCost = Math.round(item.lossAmount * 100) / 100;

                                hspfHtml += `
                                        <tr>
                                            <td>${item.invoiceVbillno}</td>
                                            <td>${item.handleNote}</td>
                                            <td>${item.regDate}</td>
                                            <td>${roundedMarginCost}</td>
                                        </tr>
                                    `;
                            });
                            $("#hspfTbody").append(hspfHtml);



                        }

                    }
                })


            },
            cancel: function (index) {
                return true;
            }
        });
    }

    function exportData() {
        $.modal.confirm("确定导出吗？", function () {
            $.modal.loading("正在导出数据，请稍后...");
            var search = $.common.formToJSON('role-form');
            $.post(prefix + "/export", search, function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });

    }
</script>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a th:href="@{/file/salesTargetDemo.xlsx}" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</body>
</html>