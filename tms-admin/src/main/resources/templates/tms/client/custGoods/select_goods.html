<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('货品选择页')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-sm-5">
                        <div class="form-group">
                            <label class="col-sm-4">货品名称：</label>
                            <div class="col-sm-8">
                                <input name="goodsName" placeholder="请输入货品名称" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-5">
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-4">
                                    <span>货品类型：</span></label>
                                <div class="col-sm-8">
                                    <input class="form-control valid" placeholder="请输入货品类型" name="goodsTypeName"
                                           type="text">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-2">
                        <label class="col-sm-4"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
            <input id="customerId" th:value="${customerId}" name="customerId" type="hidden">
        </div>
    </div>

</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">

    var prefix = ctx + "custGoods";

    $(function () {
        var options = {
            url: prefix + "/goodsList?checkStatus=1&customerId="+$("#customerId").val(),
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            clickToSelect:true,
            columns: [{
                checkbox: true
            },

                {
                    title: '货品ID',
                    visible: false,
                    field: 'goodsId'
                },
                {
                    title: '货品名称',
                    align: 'left',
                    field: 'goodsName'

                },
                {
                    title: '货品类型',
                    align: 'left',
                    field: 'goodsTypeName'

                }
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });

    /** 获取选中行*/
    function getChecked() {
        return $.btTable.bootstrapTable('getSelections');
    }

</script>
</body>
</html>