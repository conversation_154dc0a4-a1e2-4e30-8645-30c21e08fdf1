<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户货品列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">货品名称：</label>
                            <div class="col-sm-8">
                                <input name="goodsName" id="goodsName"
                                       class="form-control valid" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">货品编码：</label>
                            <div class="col-sm-8">
                                <input name="goodsCode"  id="goodsCode"
                                        class="form-control valid" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">货品类型：</label>
                            <div class="col-sm-8">
                                <input name="goodsTypeName"  id="goodsTypeName"
                                       class="form-control valid" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6"></div>
                </div>
                <div class="row">
                    <div>
                        <div class="col-md-3 col-sm-6"></div>
                        <div class="col-sm-2">
                        </div>
                        <div class="col-sm-2">
                        </div>
                        <div class="col-sm-2">
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="add()" shiro:hasPermission="tms:custGoods:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-danger multiple disabled" onclick="removeAll()"
               shiro:hasPermission="tms:custGoods:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
          <!--  <a class="btn btn-primary" onclick="importExcel()" shiro:hasPermission="basic:custAddress:import">
                <i class="fa fa-upload"></i> 导入
            </a>
            <a class="btn btn-warning" onclick="exportExcel()" shiro:hasPermission="tms:custAddress:export">
                <i class="fa fa-download"></i> 导出
            </a>-->
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">

    var prefix = ctx + "custGoods";
    var customerId = [[${custGoods.customerId}]];
    $(function () {
        var options = {
            url: prefix + "/list?customerId=" + customerId,
            createUrl: prefix + "/add",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/import",
            importTemplateUrl: prefix + "/importTemplate",
            modalName: "客户货品",
            showToggle:false,
            showColumns:true,
            columns: [
                {checkbox: true},
                {field: 'custAbbr', title: '客户名称'},
                {field: 'goodsCode', title: '货品编码'},
                {field: 'goodsName', title: '货品名称'},
                {field: 'goodsTypeName', title: '货品类型名称'},
                {field: 'length', title: '长'},
                {field: 'width', title: '宽'},
                {field: 'height', title: '高'},
                {field: 'transNote', title: '运输要求'}
            ]
        };
        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });



    /**
     * 批量删除
     */
    function removeAll() {
        //获取所有被选中的记录
        var rows = $("#bootstrap-table").bootstrapTable('getSelections');
        if (rows.length === 0) {
            $.modal.alertError("请先选择要删除的记录！");
            return;
        }
        var url = prefix + "/remove";
        $.modal.confirm("确认要删除选中的" + rows.length + "条数据吗?", function () {
            var ids = '';
            for (var i = 0; i < rows.length; i++) {
                ids += rows[i]['custGoodsId'] + ",";
            }
            ids = ids.substring(0, ids.length - 1);
            var data = {ids: ids};
            $.operate.submit(url, "post", "json", data);
        });
    }

    // 导出客户
    function exportExcel() {
        var rows = $.table.selectFirstColumns();
        var confirm = "确定导出选中的" + rows.length + "条";
        var data = {"customerIds": rows.join()};
        if (rows.length === 0) {
            confirm = "确定导所有的";
            data = {"customerId": customerId};
        }
        $.modal.confirm(confirm + $.table._option.modalName + "吗？", function () {
            $.modal.loading("正在导出数据，请稍后...");
            $.post($.table._option.exportUrl, data, function (result) {
                if (result.code === web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code === web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    /**
     * 货品的选择框
     */
    function add() {

        $.modal.open("选择货品", prefix + "/add?customerId=" + customerId, "", "", function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            $.modal.confirm("确认要添加选中的" + rows.length + "条数据吗?", function () {
                var goodsIds = '';
                for (var i = 0; i < rows.length; i++) {
                    goodsIds += rows[i]['goodsId'] + ",";
                }
                goodsIds = goodsIds.substring(0, goodsIds.length - 1);
                var data = {goodsIds: goodsIds, customerId: customerId};
                $.operate.submit(prefix + "/add", "post", "json", data);
            });
            layer.close(index);
            return false;
        });
    }

    // 导入数据
    function importExcel(formId) {
        var currentId = $.common.isEmpty(formId) ? 'importTpl' : formId;
        layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入' + $.table._option.modalName + '数据',
            content: $('#' + currentId).html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file === '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var index = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("customerId", customerId);
                formData.append("file", $('#file')[0].files[0]);
                formData.append("updateSupport", $("input[name='updateSupport']").is(':checked'));
                $.ajax({
                    url: $.table._option.importUrl,
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code === web_status.SUCCESS) {
                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code === web_status.WARNING) {
                            $.modal.closeAll();
                            $.modal.alertWarning(result.msg)
                            $.table.refresh();
                        } else {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });
    }
</script>
</body>

<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i
                        class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>