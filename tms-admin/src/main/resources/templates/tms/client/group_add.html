<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('集团信息')"/>
</head>
<body>
<div class="form-content">
    <form id="form-group-add" class="form-horizontal">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4">集团名称：</label>
                    <div class="col-sm-8">
                        <input name="groupName" id="groupName" placeholder="请输入集团名称" class="form-control valid" type="text"
                               required aria-required="true" maxlength="100">
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
</body>
<script th:inline="javascript">
    var prefix = ctx + "group";
    $(function () {
        // 表单验证
        $("#form-group-add").validate({
            rules: {
                groupName: {
                    required: true
                }
            }
        });
    });

    // 提交回调的方法
    function submitHandler() {

        if ($.validate.form())  {
            $.ajax({
                url: prefix + "/addGroup",
                type: "post",
                data: $('#form-group-add').serialize(),
                error : function(request) {
                    $.modal.alertError("系统错误");
                },
                success : function(data) {
                    $.operate.successCallback(data);
                }
            })
        }
    }
</script>
</body>

</html>