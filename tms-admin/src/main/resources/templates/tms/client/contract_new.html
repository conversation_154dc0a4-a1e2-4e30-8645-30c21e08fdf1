<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户合同')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style type="text/css">
    .table-striped{
        padding-top: 0;
    }
    .cur{
        cursor: pointer;
    }
    .switch{
        width:48px;
        height:24px;
        /* border-radius:16px; */
        overflow: hidden;
        vertical-align:middle;
        position:relative;
        display: inline-block;
        background:#ccc;
        box-shadow: 0 0 1px #1ab394;
     
        border-radius: 2em;
        padding: 2px;
        transition: all .4s ease;
 
    }
    .switch input{
        visibility: hidden;
    }
    .switch span{
        position:absolute;
        top:0;
        left:0;
        /* border-radius: 50%; */
        background:#fff;
        width:50%;
        height:100%;
        transition:all linear 0.2s;

        border-radius: 2em;
    }
    .switch span::before{
        position: absolute;
        top:0;
        left:-100%;
        content:'';
        width:200%;
        height:100%;
        /* border-radius: 30px; */
        background:#1ab394;

        border-radius: 2em;
    }
    .switch span::after{
        content:'';
        position:absolute;
        left:0;
        top:0;
        width:100%;
        height:100%;
        /* border-radius: 50%; */
        background:#fff;
        border-radius: 2em;
    }
    .switch input:checked +span{
        transform:translateX(100%);
    }
    .checkbox{
        padding-top: 0 !important;
    }
    .checkbox input[type="radio"] {
        position: absolute;
        clip: rect(0, 0, 0, 0);
    }

    .checkbox input[type='radio'] + label {
        display: block;
        height: 26px;
        padding: 6px 12px;
        font-size: 13px;
        font-weight: 500;
        line-height: 1;
        border: 1px solid #ccc;
        text-align: center;
        float: left;
        margin-right: 10px;
        cursor: pointer;
        border-radius: 2px;
    }

    .checkbox input[type='radio']:checked + label {
        border: 1px solid #009aff;
        color: #009aff;
        border-radius: 2px;
        font-weight: 500;
    }

    .td td {
        position: relative
    }
    .bg_title{
        font-size: 15px;
        font-weight: bold;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 90px;
        line-height: 20px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .fw{
        font-weight: bold;
    }
    .week{
        border: 1px solid #D9D9D9;
        width: 100px;
        padding: 5px 0;
        text-align: center;
        color: #808080;
        cursor: pointer;
    }
    .act{
        border: 1px solid #1AB394;
        color: #1AB394;
    }
    .model-15{
        margin-top: 3px;
    }
    .input-group-addon {
        padding: 0px 10px;
    }
    .toop{
        width: 30px;
        height: 20px;
        text-align: center;
        background: #1a1a1a;
        border-radius: 50%;
        display: inline-block;
        color: #fff !important;
        line-height: 20px;
        cursor: pointer;
        margin-right: 5px;
    }
    .topRow{
        text-align: left;
        border-bottom: 1px solid #e7eaec;
        margin: 0;
        padding-top: 5px;
    }
    .toopT{
        width: 30px;
        height: 20px;
        text-align: center;
        background: #f8ac59;
        border-radius: 5px;
        display: inline-block;
        color: #fff !important;
        cursor: pointer;
        padding: 2px 4px;
    }
    
    .flex {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .no-gutter{
        margin: 10px 0 0;
    }
    .btn-sm{
        padding: 3px 10px;
    }
    .form-check{
        display: inline-block;
        vertical-align: middle;
    }

    .form-check input{
        display:none;
    }
    .form-check label{
        float:left;
        border:1px solid  #337ab7;
        border-radius:50%;
        width: 18px;
        line-height: 16px;
        text-align:center;
        font-weight:lighter;
        cursor:pointer;
    }
    /* 设置radio-style中的当input被选中时的紧接在其后的label的样式 */
    .form-check input:checked+label{
        color:#fff;
        background:#337ab7;
    }
    .company{
        height: 30px;
        line-height: 30px;
        border-radius: 10px;
        border: 1px #eee solid;
        width: 80px;
        text-align: center;
    }
    .hlh30{
        height: 30px;
        line-height: 30px;
    }

    .version-container {
        display: flex;
        align-items: center;
        margin-left: 20px;
        padding-left: 10px;
        border-left: 1px #eee solid;
    }

    .version-text {
        font-size: 14px;
        color: #666;
    }

    .version-number {
        font-weight: bold;
        margin: 0 10px;
    }

    .switch-btn {
        padding: 4px 12px;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .switch-btn:hover {
        background-color: #f5f5f5;
    }


    /*版本弹框*/
    .version-list-container {
        padding: 15px;
    }

    .version-table {
        width: 100%;
        border-collapse: collapse;
        text-align: center;
    }

    .version-table th,
    .version-table td {
        padding: 10px;
        border: 1px solid #e8e8e8;
    }

    .version-table th {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    .version-table tbody tr:hover {
        background-color: #f5f5f5;
    }

    .btn-switch {
        padding: 4px 12px;
        background-color: #1890ff;
        color: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 8px;
    }

    .btn-switch:hover {
        background-color: #40a9ff;
    }

    .btn-view {
        padding: 4px 12px;
        background-color: #1ab394;
        color: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .btn-view:hover {
        background-color: #178872;
    }

</style>
<body>
<div class="form-content">


    <div class="over flex" style="justify-content: flex-start;">

        <div class="mr10 hlh30">
            <div class="fw">[[${client.custAbbr}]]</div>
        </div>
        <div class="over hlh30" style="border-left: 1px #eee solid;padding-left: 10px;">
            <div class="fl f16 fw" th:if="${client.balaCorp!= null}">
                <span class="form-control-static" th:text="${@dict.getLabel('bala_corp',client.balaCorp)}"></span>
            </div>
            <div class="fl company ml0">结算公司</div>
        </div>

        <!-- 新增版本号显示和切换按钮 -->
        <div class="version-container">
            <span class="version-text">当前版本：</span>
            <span id="versionNumber" class="version-number">[[${versionNum}]]</span>
            <button class="switch-btn" onclick="switchVersion()">切换版本</button>
        </div>

    </div>
  

    <ul class="nav nav-tabs mt10" role="tablist">
        <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab">合同价</a></li>
        <li role="presentation"><a href="#profile" aria-controls="profile" role="tab" data-toggle="tab">合同信息</a></li>
    </ul>

    <div class="tab-content">
        <div role="tabpanel" class="tab-pane active" id="home">

            <form id="role-form" class="form-horizontal">
                <input type="hidden" id="id" name="id" th:value="${client.customerId}">
                <input type="hidden" id="customerId" name="customerId" th:value="${client.customerId}">
                   
                                       
                <div class="row no-gutter">
                    <!-- <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="addrName" id="addrName" placeholder="输入省、市、区搜索" class="form-control" type="text"
                                       maxlength="30" autocomplete="off">
                            </div>
                        </div>
                    </div> -->


                    <!-- <div class="col-md-1 col-sm-1">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="carType" placeholder="车型" id="carType" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="车型" multiple th:with="type=${@dict.getType('car_type')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div> -->
                    <div class="col-md-5 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <div class="col-sm-4">
                                    <select name="deliProvinceId" id="deliProvinceId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliCityId" id="deliCityId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliAreaId" id="deliAreaId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="col-md-7 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-1" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-11">
                                <div class="col-sm-4">
                                    <select name="arriProvinceId" id="arriProvinceId"
                                            class="form-control valid"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid"></select>
                                </div>

                                <div class="col-sm-4">
                                    <select name="arriAreaId" id="arriAreaId" class="form-control valid"></select>
                                </div>
                            </div>


                        </div>
                    </div>


                </div>

                <div class="row no-gutter">

                    <div class="col-md-2 col-sm-1">
                        <div class="form-group">
                            <select name="carLen" placeholder="车长" id="carLen" class="form-control valid noselect2 selectpicker"
                                    aria-invalid="false" data-none-selected-text="车长" multiple th:with="type=${@dict.getType('car_len')}">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1 col-sm-1">
                        <select name="billingMethod" id="billingMethod" class="form-control valid"aria-invalid="false">
                            <option value="">--计费方式--</option>
                            <option th:each="dict : ${billingMethod}" th:text="${dict.context}"
                                    th:value="${dict.value}">
                            </option>
                        </select>
                    </div>
                    <div class="col-md-1 col-sm-1">
                        <select name="priceReview" id="priceReview" class="form-control valid"aria-invalid="false">
                            <option value="">--是否审核--</option>
                            <option value="0">未审核</option>
                            <option value="1">已审核</option>
                            <option value="2">已驳回</option>
                        </select>
                    </div>

                    <div class="col-md-8 col-sm-4">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>

                            <a class="btn btn-primary btn-rounded btn-sm"
                               onclick="addTab()" shiro:hasPermission="client:contract:add">
                                <i class="fa fa-plus"></i>&nbsp;新增
                            </a>

                            <a class="btn btn-success btn-rounded btn-sm"
                               onclick="importAllData()" shiro:hasPermission="client:contract:add">
                                <i class="fa fa-upload"></i> 批量导入
                            </a>
                            <a class="btn btn-success btn-rounded btn-sm"
                               onclick="importAllDataCB()" shiro:hasPermission="client:contract:addCB">
                                <i class="fa fa-upload"></i> 成本导入（合同价模板）
                            </a>
                            <a class="btn btn-success btn-rounded btn-sm"
                               onclick="batchEdit()" shiro:hasPermission="client:contract:edit">
                                <i class="fa fa-pencil-square-o"></i> 批量修改
                            </a>
                            <a class="btn btn-danger btn-rounded btn-sm"
                               onclick="batchDel()" shiro:hasPermission="client:contract:remove">
                                <i class="fa fa-remove"></i> 批量删除
                            </a>
                            <a class="btn btn-success btn-rounded btn-sm"
                               onclick="review()" shiro:hasPermission="client:contract:review">
                                <i class="fa fa-check-square-o"></i> 批量审核
                            </a>
<!--                            <a class="btn btn-success btn-rounded btn-sm"-->
<!--                               onclick="unReview()" shiro:hasPermission="client:contract:review">-->
<!--                                <i class="fa fa-minus-square-o"></i> 撤回审核-->
<!--                            </a>-->
                            <a class="btn btn-warning btn-rounded btn-sm" shiro:hasPermission="client:contract:export"
                               onclick="showExportDialog()" ><i class="fa fa-download"></i>&nbsp;导出</a>

                            <a class="btn btn-danger btn-rounded btn-sm" shiro:hasPermission="client:contract:export2" onclick="exportExcel2()"
                               data-toggle="tooltip" data-html="true" title="<div>一口价的量单价计算：单成本直接算作量单价<br>起步价的量单价计算：(单成本-起步成本)/(单量-起步量)</div>">
                                <i class="fa fa-download"></i>&nbsp;成本导出</a>

                            <a class="btn btn-info btn-rounded btn-sm" shiro:hasPermission="client:contract:export2" href="javascript:;" onclick="impt()">
                                <i class="fa fa-upload"></i>&nbsp;成本导入</a>

                            <a class="btn btn-warning btn-rounded btn-sm"
                               shiro:hasPermission="client:contract:copyConfig" onclick="copyConfig()" >
                                <i class="fa fa-exchange"></i>拷贝配置</a>
                        </div>
                    </div>
                </div>
            </form>
            <!-- <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-primary" onclick="addTab()" shiro:hasPermission="client:client:contract">
                    新增
                </a>
            </div> -->
            <div class="col-sm-12 select-table table-striped" >
                <table class="text-nowrap table-bordered" id="bootstrap-table" data-advanced-search="true"></table>
            </div>
        </div>
        <div role="tabpanel" class="tab-pane" id="profile">
            <form id="form-user-add"  class="form-horizontal" novalidate="novalidate" enctype="multipart/form-data">
                <input type="hidden" id="custId" name="custId" th:value="${client.customerId}">
                <div id="collapseTwo">
                    <div class="panel-body" style="padding: 5px 0px 0px;">
                        <!-- begin-->
                        <div class="fixed-table-body">
                            <table border="0" id="infoTab" class="custom-tab table td">
                                <thead>
                                <tr>
                                    <th style="width: 18%;">合同</th>
                                    <th style="width: 18%;">合同名称</th>
                                    <th style="width: 18%;">合同编号</th>
                                    <th style="width: 18%;">合同生效日期</th>
                                    <th style="width: 18%;">合同终止时间</th>
                                    <th style="width: 18%;">合同延期时间</th>
                                    <!--<th style="width: 10%;">操作</th>-->
                                </tr>
                                </thead>
                                <tbody>
        
        
                                <tr th:each="mapS,status:${contractBusinessList}">
                                    <th:block th:if='${not #lists.isEmpty(mapS.sysUploadFileList)}'>
                                        <td >
                                            <div th:each="file,status1:${mapS.sysUploadFileList}">
                                                <a href = "javascript:void(0)" th:onclick="openContract([[${file.filePath}]],[[${file.fileName}]])">[[${file.fileName}]]</a><br>
                                            </div>

                                            <!--th:onclick="|javascript : openContract('${file.filePath}','${file.fileName}')|"-->
                                            <!--value.forEach(function (element, index) {
                                            let filePath = element.filePath
                                            let fileName = element.fileName
                                            html = html + "<a href='#' onclick=\"openContract('" + filePath + "','" + fileName  +"')\">" + element.fileName + "</a><br>"
                                            });-->
                                        </td>
                                    </th:block>
                                    <th:block th:if='${#lists.isEmpty(mapS.sysUploadFileList)}'><td>暂无附件</td></th:block>
                                    <td th:text="${mapS.contractName}"></td>
                                    <td th:text="${mapS.contractNumber}"></td>
                                    <td th:text="${#dates.format(mapS.effectiveDate, 'yyyy-MM-dd')}"></td>
                                    <td th:text="${#dates.format(mapS.endDate, 'yyyy-MM-dd')}"></td>
                                    <td th:text="${#dates.format(mapS.extensionDate, 'yyyy-MM-dd')}"></td>
                                    <!--<th:block th:if='${mapS.tid==null}'>
                                        <td>
                                            <input id="uploadFile" type="file" name="uploadFile" class="form-control" multiple>
        
                                            <input th:id="fileName" th:name="|contractList[${status.index}].fileName|" type="hidden"
                                                th:value="${mapS.fileName}">
                                            <input type="hidden" id="tid"  th:name="|contractList[${status.index}].tid|">
                                        </td>
                                    </th:block>
                                    <td>
                                        <input id="count" name="count" type="hidden" th:value="${status.size}">
                                        <input type="hidden" id="contractsSize" th:value="${status.size}">
                                        <input th:id="name_+${status.index}" th:name="|contractList[${status.index}].name|" class="form-control" type="text" required placeholder=""
                                            aria-required="true" th:value="${mapS.name}" maxlength="50" >
                                    </td>
                                    <td>
                                        <input th:id="effectiveDate_+${status.index}" th:name="|contractList[${status.index}].effectiveDate|" class="form-control"
                                            autocomplete="off" required th:value="${#dates.format(mapS.effectiveDate, 'yyyy-MM-dd')}" >
                                    </td>
                                    <td><input th:id="invalidDate_+${status.index}" th:name="|contractList[${status.index}].invalidDate|"  class="form-control"
                                            autocomplete="off" required th:value="${#dates.format(mapS.invalidDate, 'yyyy-MM-dd')}" >
                                    </td>
                                    <td><input th:name="|contractList[${status.index}].warningDate|" th:id="warningDate_+${status.index}"  class="form-control"
                                            autocomplete="off" required th:value="${#dates.format(mapS.warningDate, 'yyyy-MM-dd')}" >
                                    </td>
                                    <td>
                                        
                                        <a th:class="${mapS.tid!=null} ? 'btn btn-xs btn-success' : 'btn btn-xs btn-success hide'" href="javascript:void(0)" th:onclick="|onImg(${status.index},this)|"> 
                                             修改
                                        </a>
                                        
                                        <a th:class="${mapS.tid==null} ? 'btn btn-xs btn-primary' : 'btn btn-xs btn-primary hide'" href="javascript:void(0)" title="" onclick="upload()"> 
                                            保存
                                        </a>
                                        <a class="btn btn-xs btn-danger hide" href="javascript:void(0)" title="" th:onclick="|abolish(${status.index},this)|"> 
                                            取消
                                         </a>
                                    </td>-->
                                </tr>
        
                                </tbody>
                            </table>
                        </div>
                        <!--end-->
                    </div>
                </div>
            </form>
        </div>
    </div>
    
</div>

<!-- <div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="upload()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger"  onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div> -->


<th:block th:include="include :: footer"/>

<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-select-js" />

<!-- 添加导出弹框模板 -->
<script id="exportTpl" type="text/template">
    <div style="padding: 20px">
        <div style="margin-bottom: 15px; padding-left: 20px">
            确认导出合同价数据吗？
        </div>
        <div class="checkbox check-box" style="margin: 0; padding-left: 20px">
            <label style="font-weight: normal">
                <input type="checkbox" id="mergeExport" name="mergeExport" checked>
                <span>合并导出</span>
                <span style="color: #737373; margin-left: 8px">
                    <i class="fa fa-info-circle"></i> 勾选后将合并到货区
                </span>
            </label>
        </div>
    </div>
</script>

<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <input type="hidden" id="customerId" name="customerId" th:value="${client.customerId}"/>
            <div class="mt10 pt5">
                <span>
                    导入模板 ：
                </span>
                <span>
                    <a th:href="@{/file/importContractData.xlsx}" class="btn btn-success btn-xs"><i class="fa fa-file-excel-o"></i> 下载导入模板（旧）</a>
                </span>
                <span>
                    <a th:href="@{/file/importContractData_new.xlsx}" class="btn btn-primary btn-xs"><i class="fa fa-file-excel-o"></i> 下载导入模板（新）</a>
                </span>
<!--                <span style="margin-left: 15px">-->
<!--                    <a href="#" onclick="downTool()" class="btn btn-primary btn-xs"><i class="fa fa-file-excel-o"></i> 下载省市区转换工具</a>-->
<!--                </span>-->
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>

    <div style="padding: 20px">
        <table id="file-table"
               class="table table-striped table-responsive table-bordered table-hover" >
        </table>
    </div>


</script>

<script id="importTplCB" type="text/template">
    <form id="importFormCB" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="fileCB" name="file"/>
            <div class="mt10 pt5">
                <span>
                导入模板 ：<a th:href="@{/file/importContractData.xlsx}" class="btn btn-info btn-xs"><i class="fa fa-file-excel-o"></i> 下载导入模板</a>
                </span>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>

<script id="switchVersionHtml" type="text/template">
    <div class="version-list-container">
        <table class="version-table">
            <thead>
            <tr>
                <th width="20%">版本号</th>
                <th width="25%">创建时间</th>
                <th width="25%">创建人</th>
                <th width="30%">操作</th>
            </tr>
            </thead>
            <tbody id="versionListBody">
            <!-- 数据将通过 JavaScript 动态插入 -->
            </tbody>
        </table>
    </div>

</script>

<script th:src="@{/js/xlsx.full.min.js}"></script>
<script th:inline="javascript">
    var prefix = ctx + "client/cust_cntractpc";

    var billingMethod=[[${billingMethod}]];
    var goodsCharacter = [[${@dict.getType('goods_character')}]];
    var carLen = [[${@dict.getType('car_len')}]];
    var carType = [[${@dict.getType('car_type')}]];
    var ifSection = [[${@dict.getType('if_section')}]];
    var data = [[${contractBusinessList}]]
    var dictBillingType = [[${@dict.getType('billing_type')}]];

    $(function () {
        console.log(data)
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });

        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("uploadFile", "tid", picParam);

        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        let options = conOpt();
        $.table.init(options);

        $('#bootstrap-table').on('all.bs.table', function () {
            var data = $('#bootstrap-table').bootstrapTable('getData', true);
            let fieldList = ["deliName","arriName"];
            mergeCells(data, 1,  $('#bootstrap-table'), fieldList);
        });

        // $("input[name='arriCityNameInitials']").change(function (obj) {
        //     searchPre()
        // });

        var size = $("#contractsSize").val();
        layui.use('laydate', function(){

            for (var i = 0; i < size; i++) {
                var laydate = layui.laydate;
                var reqDeliDate = laydate.render({
                    elem: '#effectiveDate_'+i, //指定元素
                    format : 'yyyy-MM-dd',
                    isInitValue : false,
                    max: $("#invalidDate_"+i).val(),
                    done: function(value, date, endDate){
                        reqArriDate.config.min = {
                            year: date.year,
                            month: date.month - 1,//关键
                            date: date.date,
                            hours: 0,
                            minutes: 0,
                            seconds: 0
                        };
                        warning.config.min = {
                            year: date.year,
                            month: date.month - 1,//关键
                            date: date.date,
                            hours: 0,
                            minutes: 0,
                            seconds: 0
                        };
                        //单独校验日期
                        var element= "#effectiveDate_"+(i - 1);
                        $("#form-user-add").validate().element($(element));
                    }
                });
                var reqArriDate = laydate.render({
                    elem: '#invalidDate_'+i, //指定元素
                    format : 'yyyy-MM-dd',
                    isInitValue : false,
                    min: $("#effectiveDate_"+i).val(),
                    done: function(value, date, endDate){
                        reqDeliDate.config.max = {
                            year: date.year,
                            month: date.month - 1,//关键
                            date: date.date,
                            hours: 0,
                            minutes: 0,
                            seconds: 0
                        };
                        warning.config.max = {
                            year: date.year,
                            month: date.month - 1,//关键
                            date: date.date,
                            hours: 0,
                            minutes: 0,
                            seconds: 0
                        };
                        //单独校验日期
                        var element= "#invalidDate_"+(i - 1);
                        $("#form-user-add").validate().element($(element));
                    }
                });

                var warning = laydate.render({
                    elem: '#warningDate_'+i, //指定元素
                    format : 'yyyy-MM-dd',
                    isInitValue : false,
                    trigger: 'click',
                    done: function(value, date, endDate){
                        //单独校验日期
                        var element= "#warningDate_"+(i - 1);
                        $("#form-user-add").validate().element($(element));
                    }
                });
            }
        });
        
        var contracts=[[${contracts}]]
        if(contracts){
            contracts.forEach(res=>{
                if(res.tid){
                    $("#form-user-add input").attr('disabled','disabled')
                }
            })
        }
        
    })

    function conOpt() {
        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            showToggle:false,
            showColumns:false,
            // fixedColumns: true,
            rememberSelected: false,
            // fixedNumber:4,
            height: 560,
            modalName: "合同价列表",
            clickToSelect:true,
            uniqueId: "customerId",
            showRefresh:false,
            showSearch:false,
            columns:[
                // {
                //     title: '提货省',
                //     align: 'left',
                //     field : 'deliProName',
                // },
                // {
                //     title: '提货市',
                //     align: 'left',
                //     field : 'deliCityName',
                // },
                // {
                //     title: '提货区',
                //     align: 'left',
                //     field : 'deliAreaName',
                // },
                {
                    title: '提货地址',
                    align: 'left',
                    field : 'deliName',
                    // formatter: function(value, row, index){
                    //     return value.replace(new RegExp("市辖区"),"");
                    // }
                },
                {
                    title: '到货地址',
                    align: 'left',
                    field : 'arriName',
                    // formatter: function(value, row, index){
                    //     return value.replace(new RegExp("市辖区"),"");
                    // }
                },

                // {
                //     title: '到货省',
                //     align: 'left',
                //     field : 'arriProName',
                // },
                // {
                //     title: '到货市',
                //     align: 'left',
                //     field : 'arriCityName',
                // },
                // {
                //     title: '到货区',
                //     align: 'left',
                //     field : 'arriAreaName',
                // },
                // {
                //     title: '货品',
                //     align: 'left',
                //     field : 'goodsName',
                // },
                {
                    title: '计费方式',
                    align: 'left',
                    field : 'billingMethod',
                    formatter: function(value, row, index){
                        let htmlText="";
                        billingMethod.forEach(res=>{
                            if(res.value==value){
                                htmlText=res.context;
                            }
                        })
                        return htmlText;
                    }
                },
                {
                    title: '货品特性',
                    align: 'left',
                    field : 'goodsCharacter',
                    formatter: function(value, row, index){
                        return $.table.selectDictLabel(goodsCharacter, value);
                    }
                },
                {
                    title: '货品名称',
                    align: 'left',
                    field : 'goodsName',
                    formatter: function(value, row, index){
                        return value
                    }
                },
                {
                    title: '车长车型',
                    align: 'left',
                    field : 'carLen',
                    formatter: function(value, row, index){
                        let htmlText=[];
                        if(row.carLen){
                            htmlText.push($.table.selectDictLabel(carLen, row.carLen))
                        }
                        if(row.carType){
                            htmlText.push($.table.selectDictLabel(carType, row.carType))
                        }
                        return htmlText.join('米');
                    }
                },
                {
                    title: '是否大件',
                    align: 'left',
                    field : 'isOversize',
                    formatter: function(value, row, index){
                        let html = ''
                        if (value == 0) {
                            html = '非大件'
                        }else if (value == 1) {
                            html = '大件'
                        }else if (value == 2) {
                            html = '不区分'
                        }
                        return html
                    }
                },
                {
                    title: '运输方式',
                    align: 'left',
                    field : 'isFtl',
                    formatter: function(value, row, index){
                        let html = ''
                        if (value == 0) {
                            html = '零担'
                        }else if (value == 1) {
                            html = '整车'
                        }
                        return html
                    }
                },
                {
                    title: '是否有区间',
                    align: 'left',
                    field : 'ifSection',
                    formatter: function(value, row, index){
                        return $.table.selectDictLabel(ifSection, value);
                    }
                },
                {
                    title: '是否往返',
                    align: 'left',
                    field : 'isRoundTrip',
                    formatter: function(value, row, index){
                        let html = ''
                        if (value == 0) {
                            html = '单程'
                        }else if (value == 1) {
                            html = '往返'
                        }else if (value == 2) {
                            html = '不区分'
                        }
                        return html
                    }
                },
                {
                    title: '价格(合同价/成本)',
                    align: 'left',
                    field : 'contractpcSectionList',
                    formatter: function(value, row, index){
                        if(row.ifSection==1){

                            let sectionHtml = ''
                            for(let section of value) {
                                let startOperator = "";
                                if (section.startOperator != null) {
                                    startOperator = section.startOperator == '0' ? '<' : '≤'
                                }

                                let endOperator = "";
                                if (section.endOperator) {
                                    endOperator = section.endOperator == '2' ? '<' : '≤'
                                }

                                let price = "¥0";
                                if (section.guidingPrice != null) {
                                    price = section.guidingPrice.toLocaleString('zh', {
                                        style: 'currency',
                                        currency: 'CNY'
                                    });
                                }
                                if (section.costPrice != null) {
                                    price = price + "/" + section.costPrice.toLocaleString('zh', {
                                        style: 'currency',
                                        currency: 'CNY'
                                    });

                                    if (section.costBillingType != null && section.costBillingType != '') {
                                        price = price + `(${dictBillingType.find(itm => itm.dictValue == section.costBillingType)?.dictLabel})`
                                    }

                                }


                                let isFixedPrice = "";
                                if (section.isFixedPrice != null) {
                                    if (section.isFixedPrice == '0') {
                                        isFixedPrice = '单';
                                    } else if (section.isFixedPrice == '1') {
                                        isFixedPrice = '固';
                                    }else if (section.isFixedPrice == '2') {
                                        isFixedPrice = '起'
                                    }
                                }

                                sectionHtml = sectionHtml +
                                    `<tr>
                                            <td>
                                                <div>${section.startSection}</div>
                                            </td>
                                            <td style="width: 10%">
                                                <div class="flex">
                                                   <div>${startOperator}</div>
                                                   <div style="margin: 0 10px;">x</div>
                                                   <div>${endOperator}</div>
                                                </div>

                                            </td>
                                            <td>
                                                <div>${section.endSection == null ? "" : section.endSection}</div>
                                            </td>
                                            <td>
                                                <div style="margin-left: 6px;">${price}</div>
                                            </td>
                                            <td>
                                                <div >${isFixedPrice}</div>
                                            </td>
                                        </tr>`;

                            }

                            if (sectionHtml != '') {
                                sectionHtml = `<table class="table">
                                                ${sectionHtml}
                                        </table>`
                            }

                            return sectionHtml
                            // let htmlText=[];
                            // value.forEach(res=>{
                            //     let text='';
                            //     if(res.startSection ||res.endSection){
                            //         if(res.startSection){
                            //             text+=res.startSection;
                            //         }
                            //
                            //         if(res.startSection){
                            //             text += res.startOperator==0?'＜':res.startOperator==1?'≤':'';
                            //         }
                            //         if(res.startSection ||res.endSection){
                            //             text += 'x';
                            //         }
                            //         if(res.endSection){
                            //             text += res.endOperator==2?'＜':res.endOperator==3?'≤':'';
                            //         }
                            //         if(res.endSection){
                            //             text+=res.endSection;
                            //         }
                            //
                            //         if(res.guidingPrice){
                            //             text+= ' '+res.guidingPrice
                            //         }
                            //
                            //         if(res.specialPrice){
                            //             text+= ' '+res.specialPrice
                            //         }
                            //
                            //         htmlText.push(text);
                            //     }
                            // })
                            // return htmlText.join("<br/>")
                        }else{
                            let html = '<div>'

                            if (row.guidingPrice != null) {
                                let price = row.guidingPrice.toLocaleString('zh', {
                                    style: 'currency',
                                    currency: 'CNY'
                                });

                                html = html + `<div>合：${price}</div>`
                            }

                            if (row.reservePrice != null) {
                                let price = row.reservePrice.toLocaleString('zh', {
                                    style: 'currency',
                                    currency: 'CNY'
                                });

                                html = html + `<div>保：${price}</div>`
                            }

                            if (row.costPrice != null) {
                                let price = row.costPrice.toLocaleString('zh', {
                                    style: 'currency',
                                    currency: 'CNY'
                                });

                                if (row.costBillingType != null && row.costBillingType != '') {
                                    price = price + `(${dictBillingType.find(itm => itm.dictValue == row.costBillingType)?.dictLabel})`
                                }

                                html = html + `<div>成：${price}</div>`
                            }

                            if (row.deliveryFee != null) {
                                let price = row.deliveryFee.toLocaleString('zh', {
                                    style: 'currency',
                                    currency: 'CNY'
                                });

                                html = html + `<div>送：${price}</div>`
                            }

                            html = html + `</div>`

                            return html
                        }

                    }
                },
                {
                    title: '操作',
                    field: 'customerId',
                    formatter: function(value,row,index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('client:contract:remove')}]] != "hidden") {
                            actions.push('<a class="btn btn-danger btn-xs" href="#" onclick="remove(\''+row.custContractpcId+'\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        if ([[${@permission.hasPermi('client:contract:edit')}]] != "hidden") {
                            actions.push('<a class="btn btn-success btn-xs" href="#" onclick="edit(\''+row.custContractpcId+'\',\''+row.customerId+'\')"><i class="fa fa-cog"></i>修改</a>');
                        }
                        if ([[${@permission.hasPermi('client:contract:editCostPrice')}]] != "hidden") {
                            actions.push('<a class="btn btn-success btn-xs" href="#" onclick="editCostPrice(\''+row.custContractpcId+'\',\''+row.customerId+'\')"><i class="fa fa-cog"></i>修改成本价</a>');
                        }

                        if ([[${@permission.hasPermi('client:contract:review')}]] != "hidden") {
                            if (row.priceReview == 0) {
                                actions.push(`<a class="btn btn-success btn-xs" href="#" onclick="singleReview('${row.custContractpcId}')"><i class="fa fa-check-square-o"></i>通过</a>`);

                                actions.push(`<a class="btn btn-success btn-xs" href="#" onclick="dismissReview('${row.custContractpcId}')"><i class="fa fa-minus-square-o"></i>驳回</a>`);

                            }else if (row.priceReview == 1) {
                                // actions.push(`<a class="btn btn-success btn-xs" href="#" onclick="singleUnReview('${row.custContractpcId}')"><i class="fa fa-minus-square-o"></i>撤回审核</a>`);
                                actions.push(`<span class="ml5" style="color: #1ab394;">已审核</span>`);

                            }else if (row.priceReview == 2) {
                                actions.push(`<span class="ml5" style="color: #f8ac59;">已驳回</span>`);
                            }
                        }else{
                            if (row.priceReview == 0) {
                                // actions.push(`<span class="ml5">未审核</span>`);
                            }else if (row.priceReview == 1) {
                                actions.push(`<span class="ml5" style="color: #1ab394;">已审核</span>`);
                            }else if (row.priceReview == 2) {
                                actions.push(`<span class="ml5" style="color: #f8ac59;">已驳回</span>`);
                            }

                        }


                        return actions.join(' ');
                    }
                }
            ],
            onLoadSuccess: function(data) {
                let fieldList = ["deliName","arriName"];
                mergeCells(data.rows, 1,  $('#bootstrap-table'), fieldList);
            }
        }

        return options;
    }

    //pdf预览
    function openContract(filePath, fileName) {
        window.open( "http://" + document.location.host + filePath, fileName);
    }

    /**
     * 合并单元格
     * 
     */
    function mergeCells(data, colspan, target, sameFiled) {
        /*sameFiled.forEach(res=>{
            var sortMap = {};
            for (var i = 0; i < data.length; i++) {
                for (var prop in data[i]) {
                    if (prop == res) {
                        var key = data[i][prop]

                        if (sortMap.hasOwnProperty(key)) {
                            sortMap[key] = sortMap[key] * 1 + 1;
                        } else {
                            sortMap[key] = 1;
                        }
                        break;
                    }
                }
            }
            var index = 0;
           
            for (var prop in sortMap) {
                var count = sortMap[prop] * 1;
                target.bootstrapTable('mergeCells', { index: index, field: res, colspan: colspan, rowspan: count });
                console.log({ index: index, field: res, colspan: colspan, rowspan: count })
                //target.bootstrapTable('mergeCells', { index: index, field: [res], colspan: colspan, rowspan: count });
                index += count;
            }
        })*/
        sameFiled.forEach(res=>{
            for (var i = 0; i < data.length; i++) {
                data[i][res+'_rows'] = 1;
                for (let j = i + 1; j < data.length; j++) {
                    if (res == 'deliName') {
                        if (data[i][res] == data[j][res]) {
                            data[i][res+'_rows'] = (data[i][res+'_rows'] || 1) + 1;
                            data[i]['f'] = i;
                            data[j]['f'] = i;
                        } else {
                            break;
                        }
                    } else {
                        if (data[i][res] == data[j][res] && data[i]['f'] == data[j]['f']) {
                            data[i][res+'_rows'] = (data[i][res+'_rows'] || 1) + 1;
                        } else {
                            break;
                        }
                    }
                }
                i = i+ data[i][res+'_rows'] - 1;
            }
            for (var i = 0; i < data.length; i++) {
                if (data[i][res+ "_rows"] > 1) {
                    target.bootstrapTable('mergeCells', {index: i, field: res, colspan: 1, rowspan: data[i][res+ "_rows"]});
                }
            }
        })

    }

    
    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        data.carLen = $.common.join($('#carLen').selectpicker('val'));
        // data.carType = $.common.join($('#carType').selectpicker('val'));
        $.table.search('role-form', data);
    }
     /**
     * 重置
     */
     function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        $("#role-form")[0].reset();
        $("#carLen").selectpicker('refresh');
        $("#carType").selectpicker('refresh');
        searchPre();
    }

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        searchPre();
    }
    
    function addTab() {
        layer.open({
            type: 2,
            area: ['99%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "新增合同价",
            content: ctx + "client/cust_cntractpc/add",
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                // var iframeWin = layero.find('iframe')[0];
                // iframeWin.contentWindow.submitHandler(index, layero);

                var iframeWindow = window[layero.find('iframe')[0]['name']];
                iframeWindow.submitHandler(function(success) {
                    if (success) {
                        loadVersionNo()

                        layer.close(index);
                    }
                });

            },
            cancel: function (index) {
                return true;
            }
        });
    }

    function remove(custContractpcId) {
        $.modal.confirm("确定删除该条合同价列表信息吗?", function() {
            var data = { "id": custContractpcId };
            $.operate.submit(prefix + "/remove", "post", "json", data);
        });
    }

    function review() {
        layer.open({
            title: '批量审核',
            content: '确定要审核该客户的合同价吗？',
            btn: ['通过', '驳回', '取消'],
            yes: function(index, layero) {
                // 通过
                let formToJSON = $.common.formToJSON("role-form");
                formToJSON.checkStatus = 1;
                formToJSON.carLen = $.common.join($('#carLen').selectpicker('val'));
                $.operate.saveAndRefreshCurrent(prefix + "/review", formToJSON);
                layer.close(index);
            },
            btn2: function(index, layero) {
                // 驳回
                let formToJSON = $.common.formToJSON("role-form");
                formToJSON.checkStatus = 2;
                formToJSON.carLen = $.common.join($('#carLen').selectpicker('val'));
                $.operate.saveAndRefreshCurrent(prefix + "/review", formToJSON);
                layer.close(index);
                // return false 可以阻止自动关闭
            }
        });
    }

    function singleReview(id) {
        $.modal.confirm("确定审核通过该条合同价吗?", function() {
            var data = { "custContractpcId": id , "checkStatus": 1};
            // $.operate.submit(prefix + "/review", "post", "json", data, window.location.reload());
            $.operate.saveAndRefreshCurrent(prefix + "/single_review", data);
        });
    }

    function unReview() {
        $.modal.confirm("确定撤回审核该客户的合同价吗?", function() {
            let formToJSON = $.common.formToJSON("role-form");
            formToJSON.checkStatus = 0
            formToJSON.carLen = $.common.join($('#carLen').selectpicker('val'));
            // $.operate.submit(prefix + "/review", "post", "json", data, window.location.reload());
            $.operate.saveAndRefreshCurrent(prefix + "/review", formToJSON);
        });
    }

    function singleUnReview(id) {
        $.modal.confirm("确定撤回审核该条合同价吗?", function() {
            var data = { "custContractpcId": id , "checkStatus": 0};
            $.operate.saveAndRefreshCurrent(prefix + "/single_review", data);
        });
    }

    function dismissReview(id) {
        $.modal.confirm("确定驳回该条合同价吗?", function() {
            var data = { "custContractpcId": id , "checkStatus": 2};
            $.operate.saveAndRefreshCurrent(prefix + "/single_review", data);
        });
    }

    function edit(custContractpcId,customerId) {
        layer.open({
            type: 2,
            area: ['99%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "修改合同价",
            content: prefix + "/edit?custContractpcId="+custContractpcId+"&customerId="+customerId,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });
    }

    function editCostPrice(custContractpcId,customerId) {
        layer.open({
            type: 2,
            area: ['99%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "修改成本价",
            content: ctx + "client/cust_cntractpc/editCostPrice?custContractpcId="+custContractpcId+"&customerId="+customerId,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });

    }


    function batchEdit() {
        let customerId = [[${client.customerId}]]

        layer.open({
            type: 2,
            area: ['99%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "批量修改合同价",
            content: prefix + "/edit_batch?customerId="+customerId,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });
    }

    function batchDel() {
        $.modal.confirm("确定清空该客户当前检索条件下的所有合同价吗?", function() {
            // let customerId = [[${client.customerId}]]
            // var data = { "customerId": customerId };

            let formToJSON = $.common.formToJSON("role-form");
            formToJSON.checkStatus = 0
            formToJSON.carLen = $.common.join($('#carLen').selectpicker('val'));

            $.operate.submit(prefix + "/del_batch", "post", "json", formToJSON);
        });

    }

    function downloadFile(value){
        window.location.href = ctx + "common/downloadFile?fileName=" + value+"&delete=false";
    }
    
    function onImg(index,obj) {
        $("#tidState").html('');
        let contracts=[[${contracts}]]

        var appendHtml =`<input id="uploadFile" type="file" name="uploadFile" class="form-control" multiple>
                        <input id="fileName" name="contractList[`+index+`].fileName" type="hidden"
                            value="`+contracts[index].fileName+`">
                        <input type="hidden" id="tid"  name="contractList[`+index+`].tid">`;
        $("#tidState").append(appendHtml);

        //图片功能
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("uploadFile", "tid", picParam);

        $(obj).addClass("hide");
        $(obj).next().removeClass("hide");
        $(obj).next().next().removeClass("hide");
        $("#form-user-add input").removeAttr('disabled');
    }
    function abolish(index,obj){
        $("#tidState").html('');
        let contracts=[[${contracts}]]
        var appendHtml =` <a href="#" name="`+contracts[index].filePath+`" onclick="downloadFile(this.name)">`+contracts[index].fileName+`</a>
                        <input id="fileId" type="hidden" name="contractList[`+index+`].tid"  value="`+contracts[index].tid+`">
                        <input  name="contractList[`+index+`].fileName" type="hidden" value="`+contracts[index].fileName+`">`

        $("#tidState").append(appendHtml);

        $(obj).addClass("hide");
        $(obj).prev().addClass("hide");
        $(obj).prev().prev().removeClass("hide");

        $("#form-user-add input").attr('disabled','disabled');
    
    }
    
    // 将文件上传
    function upload() {
       
        if ($.validate.form()) {

            let fileId = $("#fileId").val();

            if (fileId == null) {
                $("#uploadFile").fileinput('upload');

                var filePath = $("#uploadFile").val();
                var arr = filePath.split('\\');
                var fileName = arr[arr.length - 1];
                $("#fileName").val(fileName);

                jQuery.subscribe("cmt", commit);

            }else {
                commit()
            }

        }
    }

    function commit() {
        $.operate.saveTab(ctx + "client/upload", $('#form-user-add').serialize())
    }

    /**
     * 导入数据
     */
     function importAllData() {
        layer.open({
            type: 1,
            area: ['70%', '85%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入数据',
            content: $('#importTpl').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            success: function () {
                let options = initOptions();
                $.table.init(options);
            },
            end: function(){
                let options = conOpt();
                $.table.init(options);
            },

            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx') && !$.common.endWith(file, '.xlsm'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var i = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#file')[0].files[0]);
                formData.append("updateSupport", $("input[name='updateSupport']").is(':checked'));
                let customerId = [[${client.customerId}]]
                formData.append("customerId", customerId);
                $.ajax({
                    url: ctx + "client/importContractData",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            layer.close(index);
                            $.modal.msgSuccess(result.msg);
                            location.reload()
                        } else if (result.code == web_status.WARNING) {
                            layer.close(i);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(i);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });
    }

    /**
     * 导入 成本
     */
     function importAllDataCB() {
        layer.open({
            type: 1,
            area: ['70%', '85%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入数据',
            content: $('#importTplCB').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            success: function () {
            },
            btn1: function(index, layero){
                var file = layero.find('#fileCB').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx') && !$.common.endWith(file, '.xlsm'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var i = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#fileCB')[0].files[0]);
                formData.append("updateSupport", $("input[name='updateSupport']").is(':checked'));
                let customerId = [[${client.customerId}]]
                formData.append("customerId", customerId);
                $.ajax({
                    url: ctx + "client/importContractDataCB",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            layer.close(index);
                            $.modal.msgSuccess(result.msg);
                            location.reload()
                        } else if (result.code == web_status.WARNING) {
                            layer.close(i);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(i);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });
    }


    function initOptions() {
        let customerId = [[${client.customerId}]]

        return {
            id: "file-table",
            url: `${ctx}contractpcFile/list`,
            showToggle: false,
            showColumns: false,
            showSearch: false,
            showRefresh: false,
            modalName: "历史文件",
            height: 560,
            uniqueId: "id",
            clickToSelect: true,
            columns: [
                {
                    title: '文件名称',
                    align: 'left',
                    field: 'fileName',
                },
                {
                    title: '上传人',
                    align: 'center',
                    field: 'regUserName',
                },
                {
                    title: '上传时间',
                    align: 'center',
                    field: 'regDate',
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index){
                        return `<a href="#" onclick="downloadFile('${row.filePath}','${row.fileName}')">下载</a>`
                    }
                },
            ]
        };
    }

    function downloadFile(filePath, fileName) {
        const protocol = document.location.protocol; // 确保使用与当前页面相同的协议
        const a = document.createElement('a');
        a.href = protocol + "//" + document.location.host + filePath; // 动态选择协议
        a.download = fileName;
        a.click();

    }

    function switchVersion() {
        let customerId = [[${client.customerId}]]

        layer.open({
            type: 2,
            title: '切换版本',
            area: ['70%', '80%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: ctx + `contractpcVersion/version_list?customerId=${customerId}`,
            btn: ['关闭'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {

            },
            btn1: function (index) {
                loadVersionNo()
                // $.table.refresh()
                layer.close(index);
            },
            cancel: function (index) {
                loadVersionNo()
                // alert(1111)
                // $.table.refresh()
                return true;
            }
        });
    }

    function loadVersionNo() {
        let customerId = [[${client.customerId}]]
        let data = {customerId:customerId}
        $.ajax({
            url: ctx + "contractpcVersion/currentVersion",
            type: "get",
            dataType: "json",
            data: data,
            beforeSend: function () {
            },
            success: function (response) {
                if (response.code === 0) {
                    if (response.data) {
                        $("#versionNumber").text(response.data.versionNum);
                        $.table.refresh()
                    }else {

                    }
                } else {
                    $.modal.alertError(response.msg || "获取版本列表失败");
                }
            },
            error: function() {
                $.modal.alertError("获取版本失败");
            }
        });
    }

    function showExportDialog() {
        layer.open({
            type: 1,
            title: '导出',
            area: ['400px', '200px'],
            content: $('#exportTpl').html(),
            btn: ['确定', '取消'],
            yes: function(index, layero) {
                var mergeExport = layero.find('#mergeExport').prop('checked');
                exportData(mergeExport);
                layer.close(index);
            }
        });
    }

    function exportData(mergeExport) {
        $.modal.loading("正在导出数据，请稍后...");

        // 获取当前表单数据
        var formData = $("#role-form").serializeArray();
        // 添加合并导出参数
        formData.push({
            name: "isMergedArriArea",
            value: mergeExport ? 1 : 0,
        });

        $.post($.table._option.exportUrl, formData, function(result) {
            if (result.code == web_status.SUCCESS) {
                window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
            } else if (result.code == web_status.WARNING) {
                $.modal.alertWarning(result.msg)
            } else {
                $.modal.alertError(result.msg);
            }
            $.modal.closeLoading();
        });
    }


    function exportExcel2() {
        layer.open({
            type: 1,
            title: '选择对应发货单提货日期区间',
            content: `<div style="padding: 10px 20px 10px 10px">
    <div class="flex">
        <span class="flex_left"><span style="color: red">*</span> 日期区间：</span>
        <span class="flex_right"><input class="form-control" laydate placeholder="要求提货日期范围"></span>
    </div>
    <div class="checkbox" style="padding-left: 15px">
        <label><input type="checkbox" checked id="kickOutMultiple">剔除多装多卸</label>
        <label style="margin-left: 15px"><input type="checkbox" checked id="kickOutChngAddr">剔除改送</label>
        <!--<label style="margin-left: 15px"><input type="checkbox" checked id="taxDeduct" onchange="$('#taxDeductTag').toggle()">税金抵扣</label>-->
    </div>
    <div class="flex" style="margin-top: 20px">
        <span class="flex_left">※成本组成：</span>
        <span class="flex_right" style="line-height: 20px">运费 + 提货费(在途) + 送货费(在途)<!--span id="taxDeductTag"> - 税金抵扣</span--></span>
    </div>
</div>`,
            area: ['400px', '250px'],
            skin: '',
            btn: ['确定', '取消'],
            success: function(layero, index) {
                var laydate = layui.laydate;
                laydate.render({
                    elem: layero.find('[laydate]'),
                    type: 'date',
                    range: true,
                    done: function (value, date, endDate) {

                    }
                });
            },
            yes: function (index, layero) {
                var dateRange = layero.find('[laydate]').val();
                if (!dateRange) {
                    $.modal.msgWarning("请选择发货单要求提货日期区间");
                    return
                }
                var data = $("#role-form").serialize();
                dateRange = dateRange.split(' - ');
                data = data + "&startDate=" + dateRange[0] + "&endDate=" + dateRange[1];
                data = data + "&kickOutMultiple=" + ($('#kickOutMultiple').prop('checked') ? 1 : 0);
                data = data + "&kickOutChngAddr=" + ($('#kickOutChngAddr').prop('checked') ? 1 : 0);
                //data = data + "&taxDeduct=" + ($('#taxDeduct').prop('checked') ? 1 : 0);
                $.modal.confirm("即将导出数据，是否继续？", function() {
                    var tt = $.modal.layerLoading("正在导出数据，请稍后...");
                    $.post(prefix + "/export-cost", data, function(result) {
                        layer.close(tt);
                        if (result.code == web_status.SUCCESS) {
                            window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                        } else if (result.code == web_status.WARNING) {
                            $.modal.alertWarning(result.msg)
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    });
                });
            }
        })

    }

    var billingType = [[${@dict.getType('billing_type')}]];

    function impt() {
        // 将billingType转成{label:value}结构
        let billingTypeDict = {};
        for (let i = 0; i < billingType.length; i++) {
            billingTypeDict[billingType[i].dictLabel] = billingType[i].dictValue;
        }

        var rows = [];
        layer.open({
            type: 1,
            area: ['500px', '340px'],
            fix: false,
            //不固定
            maxmin: true,
            skin: '',
            shade: 0.3,
            title: '导入成本价',
            content: $('#import').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            success: function (layero, index) {
                let changeHandle = function () {
                    $("#templateMsg").html("")
                    var file = $('#ipt_file').val();
                    if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                        $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                        return false;
                    }
                    $("#fileMsg").html(file.substring(file.lastIndexOf("\\") + 1));
                    var tt = $.modal.layerLoading("加载中，请稍候...");
                    var files = $('#ipt_file')[0].files;
                    var fileReader = new FileReader();
                    fileReader.onload = function(ev) {
                        try {
                            var data = ev.target.result
                            var workbook = XLSX.read(data, {
                                type: 'binary'
                            }) // 以二进制流方式读取得到整份excel表格对象
                        } catch (e) {
                            console.log('%O',e)
                            layer.close(tt)
                            $('#templateMsg').text('文件类型不正确:' + e.message);
                            return;
                        }
                        var result = [];
                        try {
                            var sheetNames = workbook.SheetNames; // 工作表名称集合
                            var sht = workbook.Sheets[sheetNames[0]]; // 这里我们只读取第一张sheet
                            //console.log(sht)
                            if (sht['AE2'].w != '成本指标') {
                                $.modal.msgWarning("未在第一个sheet的AE2单元格找到‘成本指标’文字");
                                return;
                            }
                            if (sht['AF2'].w != '成本开票') {
                                $.modal.msgWarning("未在第一个sheet的AF2单元格找到‘成本开票’文字");
                                return;
                            }
                            rows = [];
                            result = XLSX.utils.sheet_to_json(sht, {header:[
                                    // 提货省，提货市，提货区，收货省，收货市，收货区，提货点，到货点，计费方式
                                    'deliProName','deliCityName','deliAreaName','arriProName','arriCityName','arriAreaName','deliAddrName','arriAddrName','billingMethod',
                                    // 货品特性，货品名称，车长，车型，是否大件，运输方式，是否往返，计价忽略公里，合同价
                                    'goodsCharacter','goodsName','carLen','carType','isOversize','isFtl','isRoundTrip','isSkipMileage','guidingPrice',
                                    // 公里数区间，货量区间，价格，价格类型
                                    'kilText','sectionText','guidingPrice2','isFixedPrice',
                                    // 发货频次，平均货量，最高单成本，最高量单价，最低单成本，最低量单价，平均单成本，平均量单价
                                    null,null,null,null,null,null,null,null,
                                    // 成本指标，成本开票
                                    'costPrice','costBillingTypeName'
                                ], raw:false, range:2});
                            // 处理数据：
                            // 1、处理成二层结构，没有计价方式、货品特性、是否大件，视为区间数据，归为上一条数据的子节点
                            // 2、字典字段文字转code（计费方式、货品特性、车长、车型、是否大件、运输方式、是否往返、计价忽略公里）
                            // 3、区间拆成两个字段（公里数区间、货量区间）
                            for (let i = 0; i < result.length; i++) {
                                if (result[i].costBillingTypeName) {
                                    result[i].costBillingType = billingTypeDict[result[i].costBillingTypeName];
                                    if (!result[i].costBillingType) {
                                        $.modal.msgError("系统未包含第"+result[i]['__rowNum__']+"行‘"+result[i].costBillingTypeName+"’票点，请核实")
                                        return
                                    }
                                }
                                delete result[i].costBillingTypeName;
                                //console.log(result[i].billingMethod, result[i].goodsCharacter, result[i].isOversize)
                                var isSection = !result[i].billingMethod && !result[i].goodsCharacter && !result[i].isOversize; // 对应第1点
                                let section = null;
                                if (isSection) {
                                    var lastRow = rows[rows.length - 1];
                                    if (!lastRow) {
                                        $.modal.alertError("第一行数据不能是区间");
                                        return;
                                    }
                                    section = result[i];
                                    section.guidingPrice = section.guidingPrice2;
                                    delete result[i]['guidingPrice2'];
                                    //console.log(section)
                                    lastRow.contractpcSectionList.push(section)
                                } else {
                                    rows.push(result[i]);
                                    result[i].contractpcSectionList = [];
                                    section = {kilText:result[i].kilText, sectionText:result[i].sectionText, guidingPrice:result[i].guidingPrice2, isFixedPrice:result[i].isFixedPrice,
                                        costPrice:result[i].costPrice, costBillingType: result[i].costBillingType}
                                    delete result[i].kilText; // 删除属于区间的数据
                                    delete result[i].sectionText; // 删除属于区间的数据
                                    delete result[i].guidingPrice2; // 删除属于区间的数据
                                    delete result[i].isFixedPrice; // 删除属于区间的数据
                                    delete result[i].costPrice; // 删除属于区间的数据
                                    delete result[i].costBillingType; // 删除属于区间的数据
                                    //console.log(section)
                                    result[i].contractpcSectionList.push(section)
                                    //console.log(result[i])
                                    // 值是''的字符数据均删除字段，=>null
                                    let washField = ['deliProName','deliCityName','deliAreaName','arriProName','arriCityName','arriAreaName','deliAddrName','arriAddrName','billingMethod',
                                        'goodsName','carLen','carType'];
                                    for (let j = 0; j < washField.length; j++) {
                                        if (!result[i][washField[j]]) {
                                            delete result[i][washField[j]];
                                        }
                                    }
                                    if (result[i].billingMethod) {
                                        let find = false;
                                        for (const item of billingMethod) {
                                            if (item.context == result[i].billingMethod) {
                                                result[i].billingMethod = item.value;
                                                find = true;
                                                break;
                                            }
                                        }
                                        if (!find) {
                                            $.modal.alertError("未识别的计价方式：" + result[i].billingMethod);
                                            return;
                                        }
                                    }
                                    if (result[i].goodsCharacter) {
                                        let find = false;
                                        for (const item of goodsCharacter) {
                                            if (item.dictLabel == result[i].goodsCharacter) {
                                                result[i].goodsCharacter = item.dictValue
                                                find = true;
                                                break;
                                            }
                                        }
                                        if (!find) {
                                            $.modal.alertError("未识别的货品特性：" + result[i].goodsCharacter);
                                            return;
                                        }
                                    }
                                    if (result[i].carLen) {
                                        let find = false;
                                        for (const item of carLen) {
                                            if (item.dictLabel == result[i].carLen) {
                                                result[i].carLen = item.dictValue;
                                                find = true;
                                                break;
                                            }
                                        }
                                        if (!find) {
                                            $.modal.alertError("未识别的车型：" + result[i].carLen);
                                            return;
                                        }
                                    }
                                    if (result[i].carType) {
                                        let find = false;
                                        for (const item of carType) {
                                            if (item.dictLabel == result[i].carType) {
                                                result[i].carType = item.dictValue;
                                                find = true;
                                                break;
                                            }
                                        }
                                        if (!find) {
                                            $.modal.alertError("未识别的车型：" + result[i].carType);
                                            return;
                                        }
                                    }
                                    if (result[i].isOversize == '不是') {//0=不是,1=是,2=不区分
                                        result[i].isOversize = 0
                                    } else if (result[i].isOversize == '是') {
                                        result[i].isOversize = 1
                                    } else if (result[i].isOversize == '不区分') {
                                        result[i].isOversize = 2
                                    } else if (result[i].isOversize) {
                                        $.modal.alertError("未识别的是否大件：" + result[i].isOversize);
                                        return;
                                    }
                                    if (result[i].isFtl == '零担') {//0=零担,1=整车
                                        result[i].isFtl = 0;
                                    } else if (result[i].isFtl == '整车') {
                                        result[i].isFtl = 1;
                                    } else if (result[i].isFtl) {
                                        $.modal.alertError("未识别的运输方式：" + result[i].isFtl);
                                        return;
                                    }
                                    if (result[i].isRoundTrip == '否') {//0=否,1=是,2=通用
                                        result[i].isRoundTrip = 0
                                    } else if (result[i].isRoundTrip == '是') {
                                        result[i].isRoundTrip = 1
                                    } else if (result[i].isRoundTrip == '通用') {
                                        result[i].isRoundTrip = 2
                                    } else if (result[i].isRoundTrip) {
                                        $.modal.alertError("未识别的是否往返：" + result[i].isRoundTrip);
                                        return;
                                    }
                                    if (result[i].isSkipMileage == '否') {//0=否,1=是
                                        result[i].isSkipMileage = 0
                                    } else if (result[i].isSkipMileage == '是') {
                                        result[i].isSkipMileage = 1
                                    } else if (result[i].isSkipMileage) {
                                        $.modal.alertError("未识别的计价忽略公里：" + result[i].isSkipMileage);
                                        return;
                                    }
                                    //console.log(result[i].billingMethod,result[i].goodsCharacter,result[i].carLen,result[i].carType,result[i].isOversize,result[i].isFtl,result[i].isRoundTrip,result[i].isSkipMileage)
                                    //console.log(result[i])
                                    result[i]['rowNum'] = result[i]['__rowNum__'];
                                    //console.log(JSON.stringify(result[i]))
                                }
                                section['rowNum'] = result[i]['__rowNum__'];
                                if (section.kilText) {
                                    // 0<=x<∞
                                    let arr = section.kilText.split('x');
                                    arr[0] = arr[0].replace('<', '')
                                    arr[0] = arr[0].replace('=', '')
                                    arr[1] = arr[1].replace('<', '')
                                    arr[1] = arr[1].replace('=', '')
                                    if (arr[1] == '∞') {
                                        arr[1] = null;
                                    }
                                    section.startKil = arr[0];
                                    section.endKil = arr[1];
                                }
                                if (section.sectionText) {
                                    // 0<=x<∞
                                    let arr = section.sectionText.split('x');
                                    arr[0] = arr[0].replace('<', '')
                                    arr[0] = arr[0].replace('=', '')
                                    arr[1] = arr[1].replace('<', '')
                                    arr[1] = arr[1].replace('=', '')
                                    if (arr[1] == '∞') {
                                        arr[1] = null;
                                    }
                                    section.startSection = arr[0];
                                    section.endSection = arr[1];
                                }
                                if (section.isFixedPrice == '单价') { // 0=单价,1=一口价,2=起步价
                                    section.isFixedPrice = 0;
                                } else if (section.isFixedPrice == '一口价') {
                                    section.isFixedPrice = 1;
                                } else if (section.isFixedPrice == '起步价') {
                                    section.isFixedPrice = 2;
                                } else if (section.isFixedPrice) {
                                    $.modal.msgWarning("价格类型未识别：" + section.isFixedPrice);
                                    return;
                                }
                                //console.log(section)
                            }

                        } finally {
                            console.log(rows)
                            $("#templateMsg").html("检测到" + result.length + "行数据")
                            layer.close(tt)
                        }


                        //在控制台打印出来表格中的数据
                        /*if (globalInvoiceList == null || globalInvoiceList.length == 0) {
                            $.modal.alertError("未在第一个Sheet中找到数据")
                            layer.close(tt)
                            return;
                        }*/
                    };
                    // 以二进制方式打开文件
                    fileReader.readAsBinaryString(files[0]);
                    $('#ipt_file').val("")
                    /*$('#ipt_file').remove();
                    $("#ipt_file_div").append('<input type="file"' +
                        ' accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"' +
                        ' id="ipt_file">')
                    $('#ipt_file').change(changeHandle)*/
                }
                layero.find(":input[type=file]").change(changeHandle)
            },
            btn1: function(index, layero){
                if (rows == null || rows.length == 0) {
                    $.modal.alertError("没有可导入的数据")
                    return;
                }
                $.modal.confirm("确认覆盖现有的成本数据吗？", function(){
                    var tt = $.modal.layerLoading("导入中，请稍候...");
                    $.ajax({
                        url: prefix + "/import-cost?customerId=[(${client.customerId})]",
                        contentType: "application/json;charset=UTF-8",
                        data: JSON.stringify(rows),
                        type: 'POST',
                        dataType: 'json',
                        success: function (result) {
                            layer.close(tt)
                            if (result.code != 0) {
                                $.modal.alertError(result.msg);
                            } else {
                                let unMatchRowNums = result.data.unMatchRowNums;
                                let manyMatchRowNums = result.data.manyMatchRowNums;
                                let success = result.data.success;
                                if (success > 0) {
                                    $.table.refresh();
                                }
                                if (unMatchRowNums.length == 0 && manyMatchRowNums.length == 0) {
                                    layer.close(index);
                                    $.modal.msgSuccess("更新成功数：" + success);
                                } else {
                                    let msg = ['<div style="max-width: 600px">']
                                    if (unMatchRowNums.length > 0) {
                                        msg.push("未匹配到路线的行号：");
                                        for (let i = 0; i < unMatchRowNums.length; i++) {
                                            msg.push(i > 0 ? ',' : '', unMatchRowNums[i] + 1);
                                        }
                                        msg.push("<br>")
                                    }
                                    if (manyMatchRowNums.length > 0) {
                                        msg.push("匹配到多条路线的行号：");
                                        for (let i = 0; i < manyMatchRowNums.length; i++) {
                                            msg.push(i > 0 ? ',' : '', manyMatchRowNums[i] + 1);
                                        }
                                        msg.push("<br>")
                                    }
                                    msg.push(success + "条数据已更新")
                                    msg.push("</div>");
                                    $.modal.alertWarning(msg.join(""));
                                }
                            }
                        }
                    })
                })
            }
        });
    }



    function copyConfig() {
        let customerId = [[${client.customerId}]]

        layer.open({
            type: 1,
            area: ['60%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "拷贝配置",
            content: $("#copyConfigHtml").html(),
            btn: ['确定', '关闭'],
            shadeClose: true,
            success: function (layero, index) {

            },
            yes: function(index, layero) {
                let data = {
                    sourceCustId: customerId,
                    targetCustId:$("#targetCustId").val(),
                }

                $.ajax({
                    url: ctx + "client/cust_cntractpc/copyConfig",
                    type: "post",
                    // dataType: "json",
                    // contentType: "application/json; charset=utf-8",
                    // data: JSON.stringify(data),
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function(result) {
                        if (result.code == 0) {
                            $.modal.msgSuccess(result.msg);
                        }else {
                            $.modal.alertError(result.msg);
                        }
                        $.modal.closeLoading();
                        $.modal.enable();
                    }
                });
            },
            cancel: function(index) {
                return true;
            }
        })
    }


</script>
<script id="import" type="text/template">
    <div style="padding: 10px 20px;">
        <div style="font-weight: bold;color: #5daf34">说明：只更新<span style="color: red;">非空非零</span>的<span style="color: #49A7FF">成本指标、成本开票</span>列，其它数据为参考数据，请勿修改；导出成本之后与导入成本之前请勿修改系统上的路线和区间数据，否则找不到更新目标。</div>
        <div class="mt10 pt5">
            适用模板：‘成本导出’的Excel文件
        </div>
        <div id="ipt_file_div" style="display: none">
            <input type="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" id="ipt_file"/>
        </div>
        <a href="javascript:$('#ipt_file').click()"class="mt10 btn btn-success btn-sm"><i class="fa fa-file-excel-o"></i> 选择要导入的文件</a>
        <span id="fileMsg" class="mt10" style="display: inline-block;line-height: 26px;vertical-align: middle"></span>
        <div class="mt10" style="color:red;">
            提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
        <div id="templateMsg" class="mt10" style="font-weight: bold;color:blue;"></div>
    </div>
</script>


<script id="copyConfigHtml" type="text/template">
    <div class="form-content">
        <div class="row">
            <div class="col-md-12 col-sm-12">
                <input name="targetCustId" id="targetCustId" class="form-control" placeholder="目标客户id"
                       type="text">
            </div>
        </div>

    </div>
</script>
</body>

</html>