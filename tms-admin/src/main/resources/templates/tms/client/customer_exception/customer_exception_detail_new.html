<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('异常跟踪详细')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .cur{
        cursor: pointer;
    }
    .checkbox{
        padding-top: 0 !important;
    }
    .checkbox input[type="radio"] {
        position: absolute;
        clip: rect(0, 0, 0, 0);
    }
 
    .checkbox input[type='radio'] + label {
        display: block;
        height: 26px;
        padding: 6px 12px;
        font-size: 13px;
        font-weight: 500;
        line-height: 1;
        border: 1px solid #ccc;
        text-align: center;
        float: left;
        margin-right: 10px;
        cursor: pointer;
        border-radius: 2px;
    }
 
    .checkbox input[type='radio']:checked + label {
        border: 1px solid #009aff;
        color: #009aff;
        border-radius: 2px;
        font-weight: 500;
    }
    .th_img{
        width: 100px;
        height: 100px;
        object-fit: scale-down;
    }
    .fcff{
        color: #ff1f1f;
    }
    .switch{
        width:40px;
        height:24px;
        border-radius:16px;
        overflow: hidden;
        vertical-align:middle;
        position:relative;
        display: inline-block;
        background:#ccc;
        box-shadow: 0 0 1px #1ab394;
    }
    .switch input{
        visibility: hidden;
    }
    .switch span{
        position:absolute;
        top:0;
        left:0;
        border-radius: 50%;
        background:#fff;
        width:50%;
        height:100%;
        transition:all linear 0.2s;
    }
    .switch span::before{
        position: absolute;
        top:0;
        left:-100%;
        content:'';
        width:200%;
        height:100%;
        border-radius: 30px;
        background:#1ab394;
    }
    .switch span::after{
        content:'';
        position:absolute;
        left:0;
        top:0;
        width:100%;
        height:100%;
        border-radius: 50%;
        background:#fff;
    }
    .switch input:checked +span{
        transform:translateX(100%);
    }
    .f16 {
        font-size: 16px;
    }

    .flex {
        display: flex;
        algin-items: center;
        just-content: space-between;
    }

    .flex_left {
        /* width: 7em; */
        line-height: 24px;
        text-align: right;
    }

    .flex_right {
        /* min-width: 12em; */
        flex: 1;
        line-height: 24px;
    }

    .over {
        overflow: hidden;
    }

    .fl {
        float: left;
    }

    .fr {
        float: right;
    }

    .mt10 {
        margin-top: 10px;
    }

    .mt20 {
        margin-top: 20px;
    }

    .ml20 {
        margin-left: 20px;
    }

    .fw {
        font-weight: bold;
    }

    .ydbox {
        padding: 0 10px;
        border: 1px #eee solid;
        height: 80px;
    }

    .ydcontent {
        background: #7f7f7f;
        border-radius: 10px;
        margin-bottom: 10px;
    }

    .ydcontent_title {
        padding: 5px 10px;
        color: #fff;
    }

    .ydcontent_box {
        padding: 10px 10px;
        border-radius: 10px;
        min-height: 60px;
        box-sizing: border-box;
        background: #fff;
        /*border: 1px #7f7f7f solid;*/
        box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.08), 0px 4px 10px 4px rgba(0, 0, 0, 0.08)
    }

    .fc80 {
        color: #808080;
    }

    .addbtn {
        background: #1ab394;
        color: #fff;
        cursor: pointer;
        width: 120px;
        text-align: center;
        line-height: 30px;
    }

    /*以dhlist开头的class*/
    [class^=dhlist] {
        border: 1px #eee solid;
        width: 100%;
        padding: 3px 0;
    }

    [class^=dhlist] span {
        display: inline-block;
        background: #f3f3f3;
        line-height: 20px;
        padding: 0 5px;
        margin-left: 5px;
        margin-bottom: 5px;
    }

    .table-title {
        text-align: center;
        font-weight: bold;
        line-height: 24px;
        background: #f7f8fa
    }

    .tablebox {
        border: 1px #eee solid;
    }

    .cys {
        padding: 20px 0;
        border-bottom: 1px #9f9f9f dashed;
    }

    .fc33 {
        color: #f33131;
    }

    .fc1a {
        color: #1ab394;
    }

    .bor33 {
        border: 1px #f33131 solid;
    }

    .bor1a {
        border: 1px #1ab394 solid;
    }

    .insurance {
        background: #f8f9fc;
        width: 100%;
    }

    .insurancebox {
        padding: 20px 20px;
    }

    .table_text {
        width: 100%;
        text-align: center;
    }

    .boree {
        border: 1px #eee solid;
        box-sizing: border-box;
        line-height: 20px;
    }

    .tabel_total {
        background: #fffcd3;
        padding: 10px 20px;

    }

    .mt10 {
        margin-top: 10px;
    }

    .mt20 {
        margin-top: 20px;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .uploads .table-bordered td{
        border: 0 !important;;
    }

    .boxs{
        box-shadow: 0px 2px 5px 0px rgb(0 0 0 / 8%), 0px 4px 10px 4px rgb(0 0 0 / 8%);
        border-radius: 10px;
    }
    .boxw{
        padding: 5px 0;
    }
    .boxw .flex_left{
        line-height: 26px;
    }
    .fc1ab{
        color: #1ab394;
    }
    .mar10{
        margin-right: 10px;
        margin-top: 10px;
    }
    .del_btn{
        background: #ed5565;
        color: #fff;
        width: 100px;
        line-height: 30px;
        text-align: center;
        height: 30px;
        border-radius: 3px;
    }
    .table>thead>tr>th {
        font-weight: normal;
    }
    .selloutBj{
        position: relative;
        overflow: hidden;
    }
    .sellout {
        background-color: #1c84c6;
        color: #fff !important;
        width: 30%;
        height: 30px;
        line-height: 30px;
        text-align: center;
        position: absolute;
        right: -8%;
        top: 16%;
        transform: rotate(38deg);
        font-size: 14px;
    }
    .selloutT {
        border: 1px solid #333;
        color: #333 !important;
        width: 50%;
        height: 30px;
        line-height: 30px;
        text-align: center;
        position: absolute;
        right: -20%;
        top: 16%;
        transform: rotate(38deg);
        font-size: 14px;
    }
    body{
        background-color: #e7eaec;
    }
    .form-content{
        background-color: transparent;
        padding-bottom: 60px;
    }
    .white-bg {
        padding: 5px 15px;
        border-radius: 5px;
    }
    .toBut{
        vertical-align: middle;
    }
    .f18{
        font-size: 18px;
    }
    .btnT{
        border-radius: 50px;
        padding: 3px 10px;
    }
    .flex{
        align-items: center;
    }
    .taTX{
        min-width: 5em;
    }
    .taTX>div:first-child{
        height: 24px;
        line-height: 24px;
    }
    .naTx{
        margin-top: 5px;
        padding: 5px 0;
        border-top: 1px solid #e7eaec;
        border-bottom: 1px solid #e7eaec;
        text-align: center;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .form-horizontal .radio-inline{
        padding-top: 0;
    }
    .f14{
        font-size: 15px;
    }
    .bTit{
        background-color: #FFE0E0;
        padding: 0 5px;
        border: 1px dashed #ED2929;
        color: #530F0F;
        font-size: 13px;
    }
    .tabr{
        border: 1px dashed;
        padding: 5px 15px;
        border-radius: 5px;
        display: inline-block;
    }
    .label-dangerT{
        color: #ed5565;
        border: 1px solid #ed5565;
        background-color: #fff;
    }
</style>
<body>
<div class="form-content">
    <form id="form-adnormal-add" class="form-horizontal" novalidate="novalidate">
        
        <div class="white-bg">
            <div class="flex" style="justify-content: space-between;">
                <div style="width: 50%;">
                    <div class="flex">
                        <!-- <img th:src="@{/img/hu.png}" style="width: 60px;height: 60px;"/> -->
                        <div>
                            <div class="toBut">
                                <span class="toBut f18 fw" th:text="${entrustExp.exceptionTitle}"></span>
                                <span class="ml5 toBut">
                                    <span class="label label-defaultT" th:if="${entrustExp.caseStatus == '1'}">新建</label>
                                    <span class="label label-dangerT" th:if="${entrustExp.caseStatus == '2'}">处理中</label>
                                    <span class="label label-primaryT" th:if="${entrustExp.caseStatus == '3'}">已结案</label>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
            <div class="row">
                <div class="col-md-10 col-sm-6" th:if="${entrustExp.preliminaryLoss != null}">
                    <div class="flex boxw">
                        <div class="flex_left">初步损失：</div>
                        <div class="flex_right" th:text="${entrustExp.preliminaryLoss}"></div>
                    </div>
                </div>
                <div class="col-md-12 col-sm-12" th:if="${entrustExp.handleNote != null}">
                    <div class="flex boxw">
                        <div class="flex_left">简要说明：</div>
                        <div class="flex_right" th:text="${entrustExp.handleNote}"></div>
                    </div>
                </div>

                <div class="col-md-12 col-sm-12">
                    <div class="flex boxw" style="align-items: flex-start;">
                        <div class="flex_left">附件照片:</div>
                        <div class="flex_right picviewer">
                            <div style="display:inline-block" th:each="mapS,status:${files}">
                                <img class="th_img" src="" th:src="${mapS.filePath}"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="white-bg mt10" th:if="${(not #lists.isEmpty(exceptionTrackList))}">
            <div class=" f14 fw"> 跟踪记录 </div>
            
            <div class="mt10">
                <table class="table table-bordered" id="tracetab">
                    <thead style="background:#f7f8fa">
                    <tr>
                        <!--                                    <th style="width: 8%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRowTrace()" title="新增行">+</a></th>-->
                        <th style="width: 30%">跟踪日期</th>
                        <th style="width: 45%">内容</th>
                        <th>需协助内容</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr name="rowTrace" th:each="mapS,status:${exceptionTrackList}">
                        <td>
                            <div th:text="${#dates.format(mapS.trackingDate, 'yyyy-MM-dd HH:mm')}"></div>
                        </td>
                        <td>
                            <div th:text="${mapS.trackingContent}"></div>
                        </td>
                        <td>
                            <div th:text="${mapS.assistanceContent}"></div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="white-bg mt10">
            <div class=" f14 fw"> 整改信息 </div>
            <div class="row" th:unless="${ exceptionCorrection == null }">
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">责任人:</div>
                        <div class="flex_right" th:text="${exceptionCorrection.responsiblePerson}"></div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">责任小组:</div>
                        <div class="flex_right" th:text="${exceptionCorrection.responsibilityTeam}"></div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">现场:</div>
                        <div class="flex_right" th:text="${exceptionCorrection.onSite}"></div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left">部门负责人:</div>
                        <div class="flex_right" th:text="${exceptionCorrection.manager}"></div>
                    </div>
                </div>
            </div>
            <div class="row" th:unless="${ exceptionCorrection == null }">
                <div class="col-md-3 col-sm-6">
                    <div class="flex boxw">
                        <div class="flex_left"></div>
                        <div class="flex_right">
                            <div class="tabr cur select" onclick="onExamine(this)">
                                <span class="text-success">考核人员</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="display: none;" th:unless="${ exceptionCorrection == null }">
                <div class="row">
                    <div class="col-md-12 col-sm-12">
                        <div class="flex boxw" style="align-items: flex-start;">
                            <div class="flex_left">考核人员:</div>
                            <div class="flex_right">
                                <div class="entrusts">
                                    <div th:class="${status.index ==0 ?'row no-gutter':'row no-gutter mt10'}" th:each="dict,status: ${exceptionCorrection.appraisersList}">
                                        <div class="col-md-6 col-sm-6">
                                            <span th:text="${dict.name}"></span> - 
                                            <span th:text="${dict.amount}"></span>
                                            <span th:text="${dict.unit==1?'金额':'分'}"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 col-sm-12">
                        <div class="flex boxw" style="align-items: flex-start;">
                            <div class="flex_left">备注:</div>
                            <div class="flex_right" th:text="${exceptionCorrection.remark}"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" th:unless="${ exceptionCorrection == null }">
                <div class="col-md-12 col-sm-12">
                    <span class="f18 fw">根本原因分析:</span>
                </div>
                <div class="col-md-12 col-sm-12 mt10">
                    <div>
                        <textarea name="rootCauseAnalysis" maxlength="200" class="form-control" rows="5" th:text="${ exceptionCorrection==null?'':exceptionCorrection.rootCauseAnalysis }" disabled></textarea>
                    </div>
                    <div class="picviewer mt10">
                        <div style="display:inline-block" th:each="mapS,status:${analysisFiles}">
                            <img style="width: 100px;" src="" th:src="${mapS.filePath}"/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" th:unless="${ exceptionCorrection == null }">
                <div class="col-md-12 col-sm-12">
                    <span class="f18 fw">改善对策:</span>
                </div>
                <div class="col-md-12 col-sm-12 mt10">
                    <div>
                        <textarea name="improvementMeasures" maxlength="200" class="form-control" rows="5" th:text="${ exceptionCorrection==null?'':exceptionCorrection.improvementMeasures }" disabled></textarea>
                    </div>
                    <div class="picviewer mt10">
                        <div style="display:inline-block" th:each="mapS,status:${measuresFiles}">
                            <img style="width: 100px;" src="" th:src="${mapS.filePath}"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-sm-offset-5 col-sm-10 white-bg">
                
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
                </button>
            </div>
        </div>
    </form>
</div>
</form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
     var prefix = ctx + "trace";
    $(function () {
        /*全部设为只读*/
        $("input").attr('readonly', true)
        $("select").css('disabled',true)
        $("textarea").attr('readonly', true)

        //损失合计展示
        var  losssum = 0;
        $("#losstab").children("tbody").children("tr").each(function () {
            var num = $(this).children('td').eq(2).text().trim();
            if(num != null && num != '') {
                losssum = accAdd(losssum,parseFloat(num))
            }
        })
        $(".loss_total").find("span").eq(1).text(losssum)

        //收入合计展示
        var  incomesum = 0;
        $("#incometab").children("tbody").children("tr").each(function () {
            var num = $(this).children('td').eq(2).text().trim();
            if(num != null && num != '') {
                incomesum = accAdd(incomesum,parseFloat(num))
            }
        })

        $(".real_total").find("span").eq(1).text(accSub(losssum,incomesum))
        $(".income_total").find("span").eq(1).text(incomesum)


        // let isInsuranceNum=[[${entrustExp.isInsurance}]];
        // if(isInsuranceNum==0){
        //     $("#reportNoDiv").css('display', 'none');
        // }else{
        //     $("#reportNoDiv").css('display', 'block');
        // }

        let isCompensationRequiredNum=[[${entrustExp.isCompensationRequired}]];
        
        if(isCompensationRequiredNum==0||isCompensationRequiredNum==null){
            $("#isCompensationRequiredDiv").css('display', 'none');
        }else{
            $("#isCompensationRequiredDiv").css('display', 'block');
        }

        // $('input[type=radio][name=isInsurance]').change(function() {
        //     let isInsurance=$('input:radio[name="isInsurance"]:checked').val();
        //     if(isInsurance==0){
        //         $("#reportNoDiv").css('display', 'none');
        //     }else{
        //         $("#reportNoDiv").css('display', 'block');
        //     }
        // });

        $('input[type=radio][name=isCompensationRequired]').change(function() {
            let isCompensationRequired=$('input:radio[name="isCompensationRequired"]:checked').val();
            if(isCompensationRequired==0){
                $("#isCompensationRequiredDiv").css('display', 'none');
            }else{
                $("#isCompensationRequiredDiv").css('display', 'block');
            }
        });

        invoiceIds("invoiceIds","#thdzpk",1);
        invoiceIds("transFeeCount1","#thdzje",2);
        invoiceIds("lotIdTransFeeList","#ydzpk",1)
        invoiceIds("transFeeCount2","#ydzje",2);

        $('.picviewer').viewer({
            url: 'data-original',
            title: false,
            navbar:false,
        });
    });

    /**
     ** 加法函数，用来得到精确的加法结果
     ** 说明：javascript的加法结果会有误差，在两个浮点数相加的时候会比较明显。这个函数返回较为精确的加法结果。
     ** 调用：accAdd(arg1,arg2)
     ** 返回值：arg1加上arg2的精确结果
     **/
    function accAdd(arg1, arg2) {
        var r1, r2, m, c;
        try {
            r1 = arg1.toString().split(".")[1].length;
        }
        catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        }
        catch (e) {
            r2 = 0;
        }
        c = Math.abs(r1 - r2);
        m = Math.pow(10, Math.max(r1, r2));
        if (c > 0) {
            var cm = Math.pow(10, c);
            if (r1 > r2) {
                arg1 = Number(arg1.toString().replace(".", ""));
                arg2 = Number(arg2.toString().replace(".", "")) * cm;
            } else {
                arg1 = Number(arg1.toString().replace(".", "")) * cm;
                arg2 = Number(arg2.toString().replace(".", ""));
            }
        } else {
            arg1 = Number(arg1.toString().replace(".", ""));
            arg2 = Number(arg2.toString().replace(".", ""));
        }
        return (arg1 + arg2) / m;
    }
    /**
     ** 减法函数，用来得到精确的减法结果
     ** 说明：javascript的减法结果会有误差，在两个浮点数相减的时候会比较明显。这个函数返回较为精确的减法结果。
     ** 调用：accSub(arg1,arg2)
     ** 返回值：arg1加上arg2的精确结果
     **/
    function accSub(arg1, arg2) {
        var r1, r2, m, n;
        try {
            r1 = arg1.toString().split(".")[1].length;
        }
        catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        }
        catch (e) {
            r2 = 0;
        }
        m = Math.pow(10, Math.max(r1, r2)); //last modify by deeka //动态控制精度长度
        n = (r1 >= r2) ? r1 : r2;
        return ((arg1 * m - arg2 * m) / m).toFixed(n);
    }

    function invoiceIds(obj,tobj,status) {
        let invoiceIds=0;
        $("input[name='"+obj+"']").each(function() {
            if(status==1){
                let num= Math.abs($(this).prev().html());
                invoiceIds+= Number( num )
            }else{
                let num= Math.abs($(this).val());
                invoiceIds+= Number( num )
            }  
        })
        $(tobj).html( invoiceIds.toFixed(2) )
    }
    function remain() {
        let expId=[[${entrustExpId}]];
        var url = prefix + "/residualValueTrackingDetail/"+expId;
        $.modal.open("残值记录", url,'1000');
    }
</script>
</body>

</html>