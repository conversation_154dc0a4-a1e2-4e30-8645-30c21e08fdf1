<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('异常统计')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse,.f7f7{
        background-color: #F7F7F7;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" th:value="${entrustId}">
                <div class="row">

                    <div class="col-md-3 col-sm-3">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input type="text" placeholder="提报开始时间" style="width: 47.5%; float: left;" class="time-input form-control" id="accidentTimeStart" name="params[accidentTimeStart]" >
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" placeholder="提报结束时间" style="width: 47.5%; float: left;" class="time-input form-control" id="accidentTimeEnd" name="params[expOccTimeEnd]" >
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input type="text" placeholder="发货单号" style="width: 100%; float: left;" class="form-control" id="invoiceVbillno" name="invoiceVbillno" >
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input type="text" placeholder="客户简称" style="width: 100%; float: left;" class="form-control" id="custName" name="custName" >
                            </div>
                        </div>
                    </div>

                    <!--<div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input type="text" placeholder="报案号" style="width: 100%; float: left;" class="form-control" id="reportNo" name="reportNo" >
                            </div>
                        </div>
                    </div>-->
                </div>

                <div class="row">
                    <div class="col-md-3 col-sm-3">
                        <div class="form-group">
                            <div class="col-sm-6">
                                <select id="salesId" name="salesId" class="form-control valid" aria-invalid="false" required>
                                    <option value=""> -- 请选择运营部 --</option>
                                    <option th:each="salesGroup : ${salesGroupList}"
                                            th:text="${salesGroup.salesName}"
                                            th:value="${salesGroup.id}"></option>
                                </select>
                            </div>
                            <div class="col-sm-6">
                                <select id="caseStatus" name="caseStatus" class="form-control valid noselect2 selectpicker" data-none-selected-text="异常状态"
                                        aria-invalid="false" aria-required="true" multiple>
                                    <!--                                    <option th:each="dict : ${entrustExpStatusEnum}" th:text="${dict.context}" th:value="${dict.value}"></option>-->
                                    <option value="1">新建</option>
                                    <option value="2">处理中</option>
                                    <option value="3">已结案</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="expType" id="expType" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="异常类型" multiple>
                                    <option th:each="dict : ${@dict.getType('exception_type')}" th:text="${dict.dictLabel}" th:value="${dict.dictCode}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="isInsurance" class="form-control valid" aria-invalid="false">
                                    <option value="">--是否报保险--</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="abnormalLink" class="form-control valid" aria-invalid="false">
                                    <option value="">--是否有残值--</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-4">
                                <select name="deliProvinceId" id="deliProvinceId" class="form-control valid"
                                    aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4">
                                <select name="deliCityId" id="deliCityId" class="form-control valid"
                                        aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <select name="deliAreaId" id="deliAreaId" class="form-control valid"
                                        aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-1" onclick="changeDiv()">
                        <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                    </div>

                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <div class="col-sm-4">
                                    <select name="arriProvinceId" id="arriProvinceId"
                                            class="form-control valid"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid"></select>
                                </div>

                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <select name="arriAreaId" id="arriAreaId" class="form-control valid"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group" style="text-align: center;">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <!--<a class="btn btn-warning" onclick="handleException()" shiro:hasPermission="tms:trace:exceptionTotal:approve">
                &lt;!&ndash;                <i class="fa fa-exclamation"></i> 异常处理&ndash;&gt;
                <i class="fa fa-exclamation"></i> 提交审核
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:exception:export">
                <i class="fa fa-download"></i> 导出
            </a>

            <a class="btn btn-danger multiple disabled" onclick="remove()" shiro:hasPermission="tms:trace:abnormal:delete">
                <i class="fa fa-remove"></i> 删除
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"  class="text-nowrap" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">

    var prefix = ctx + "trace/exceptionTotal";

    var customerId = [[${customerId}]]

    var traceprefix = ctx + "trace";
    var expType = [[${@dict.getType('exp_type')}]];//异常状态
    var abnormal_link = [[${@dict.getType('abnormal_link')}]];//异常状态
    var deptDispatherList = [[${deptDispatherList}]];

    var viewFlag = [[${@permission.hasPermi('trace:exceptionTotal:view')}]];
    var editFlag = [[${@permission.hasPermi('tms:trace:abnormaledit')}]];
    var zhenggaiSaveFlag = [[${@permission.hasPermi('tms:trace:abnormaledit')}]];
    var zhenggaiDetailFlag = [[${@permission.hasPermi('tms:trace:exceptionCorrection:detail')}]];

    // function searchPre() {
    //     var data = {};
    //     data.expType = $.common.join($('#expType').selectpicker('val'));
    //     data.abnormalLink = $.common.join($('#abnormalLink').selectpicker('val'));
    //     data.abnormalStatus = $.common.join($('#abnormalStatus').selectpicker('val'));
    //     $.table.search('role-form', data);
    // }

    function searchPre() {
        var data = {};
        data.caseStatus =  $.common.join($('#caseStatus').selectpicker('val'));
        data.expType =  $.common.join($('#expType').selectpicker('val'));
        data.accidentTimeStart = $('#accidentTimeStart').val() //$.common.join($('#accidentTimeStart').selectpicker('val'));
        data.accidentTimeEnd = $('#accidentTimeEnd').val() //$.common.join($('#accidentTimeEnd').selectpicker('val'));
        $.table.search('role-form', data);
    }

    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 删除
     */
    function remove(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        //判断案件状态
        /*var caseStatus = $.table.selectColumns('caseStatus').join();
        if (caseStatus != '1') {
            $.modal.alertWarning("新建状态的异常才能删除！");
            return false;
        }*/
        $.modal.confirm("确认要删除选中的" + rows.length + "条数据吗?", function() {
            var url = $.table._option.removeUrl;
            var data = { "ids": rows.join() };
            $.operate.submit(url, "post", "json", data);
        });
    }

    function exportTraceExcel() {
        /*var options = {
            title: '在途筛选',
            width: "600",
            height: "420",
            url: prefix + "/toExport",
            btn: ['确定导出', '关闭'],
            //callBack: doSubmit
        };*/

        $.modal.open("异常导出", prefix + "/toExport",'800', '490');
        var ele = document.getElementsByClassName("layui-layer-btn0")[0];
        ele.innerText = "确定导出";

    }

    $(function () {
        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        var options = {
            url: prefix + "/list?customerId=" + customerId,
            removeUrl: ctx + "entrustexceptionmore" + "/removeEntrustExp",
            exportUrl : prefix + "/export",
            showSearch: true,
            showRefresh: true,
            pagination: true,
            showToggle:false,
            showColumns:true,
            showFooter:true,
            modalName: "异常统计",
            rememberSelected: true,
            clickToSelect: true,
            height: 560,
            uniqueId: 'entrustExpId',
            exportTypes:['excel','csv'],
            showExport: true,
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"异常统计"
            },
            columns: [{
                checkbox:true,
                // field : 'caseStatus',
                // formatter:function(value){
                //     if(value == null || value == 1){
                //         return { disabled : false};
                //     }else{
                //         return { disabled : true};
                //     }
                //
                // }
            },{
                title: '操作',
                align: 'center',
                formatter: function(value, row, index) {
                    var actions = [];
                    if ([[${@permission.hasPermi('exception:customer:view')}]] != "hidden") {
                        actions.push('<a class="btn  btn-xs' + viewFlag + '" href="javascript:void(0)"  title="明细" onclick="detail(\'' + row.entrustExpId + '\')">详情</a>');
                    }
                   
                    return actions.join('');
                }
            },
                {
                    title: '案件状态',
                    align: 'left',
                    field : 'caseStatus',
                    formatter: function status(value, row, index) {
                        if(value == 1)
                            return '<span class="label label-default">新建</label>';
                        else if(value == 2)
                            return '<span class="label label-info">处理中</label>';
                        else if(value == 3)
                            return '<span class="label label-info">已结案</label>';
                    }
                },  
               
                {
                    title: '提报时间',
                    align: 'left',
                    field : 'accidentTime',
                    formatter: function status(value, row, index) {
                        return value.slice(0, 10)
                    }
                },
                // {
                //     title: '承运商',
                //     align: 'left',
                //     field : 'carrierName'
                // },
                {
                    title: '客户名称',
                    align: 'left',
                    field : 'custName'
                },
                {
                    title: '运营部名称',
                    align: 'left',
                    field : 'salesName'
                },
                /*{
                    title: '出险时间',
                    align: 'left',
                    field : 'accidentTime'
                },*/
                {
                    title: '异常跟踪标题',
                    align: 'left',
                    field : 'exceptionTitle'
                },
                {
                    title: '简要说明',
                    align: 'left',
                    width: '12%',
                    field : 'handleNote',
                    formatter: function note(value, row, index) {
                        return $.table.tooltip(value)
                        var html = ''
                        html = html + "<span data-toggle='tooltip' data-container='body' data-placement='left' data-html='true' title='"+ value + "'>" + value + "</span>"
                        return html
                    }
                },    
                /*{
                    title: '预估损失（元）',
                    align: 'right',
                    field: 'lossAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '实际收入（元）',
                    align: 'right',
                    field: 'realAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },*/
                {
                    title: '保险',
                    align: 'left',
                    field : 'isInsurance',
                    formatter: function (value, row, index) {
                        if (value == 1) {
                            return "<span class='text-danger'>是</span>";
                        }
                        if (value == 0) {
                            return "否";
                        }
                    }
                },
                {
                    title: '残值',
                    align: 'left',
                    field : 'abnormalLink',
                    formatter: function (value, row, index) {
                        if (value == 1) {
                            return "<span class='text-danger'>是</span>";
                        }
                        if (value == 0) {
                            return "否";
                        }
                    }
                },
                {
                    title: '发货单号',
                    align: 'left',
                    field : 'invoiceVbillno'
                },
                {
                    title: '报案号',
                    align: 'left',
                    field: 'reportNo'
                },
                {
                    title: '创建人',
                    align: 'left',
                    field : 'regUserId'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field : 'regDate'
                }
            ]
        };

        $.table.init(options);
    });

    //异常处理
    function handleException(){
        var rows = $.table.selectColumns('entrustExpId');
        if(rows.length != 1){
            $.modal.alertWarning("请选择一条数据");
            return false;
        }

        $.modal.confirm("确认提交审核？",function () {
            var url =  ctx + "trace/exceptionTotal/submitApprove";
            let data = {};
            data.entrustExpId = rows.join(",");
            $.operate.post(url,data);
        });
    }

    //数据恢复
    function dataRecover(){
        //判断异常状态
        var url =  ctx + "entrustexceptionmore/dataRecover";
        let data = {};
        $.operate.post(url,data);
    }

    /**
     * 跳转详情页
     */
    function detail(entrustExpId) {
        var url = prefix + "/customer_exception_detail?entrustExpId="+entrustExpId;
        $.modal.openTab($.table._option.modalName + "详细", url);
    }

    /**
     * 修改异常
     */
    function update(entrustExpId){
        var url = traceprefix + "/editAbnormal/"+entrustExpId;
        //判断异常状态
        // var abnormalStatus = $.table.selectColumns('abnormalStatus');
        // if(abnormalStatus != driversignStatusValue){
        //     $.modal.alertWarning("该异常在审核中或处理结束，无法进行修改！");
        //     return false;
        // }
        $.modal.openTab("修改异常跟踪", url);
    }
    
    function integrate(entrustExpId) {
        var url = ctx + "entrustexceptionmore/toExceptionCorrection/"+entrustExpId;
        $.modal.openTab("整改", url);
    }
    function detailIntegrate(entrustExpId) {
        var url = ctx + "entrustexceptionmore/toexceptionCorrectionDetail/"+entrustExpId;
        $.modal.openTab("整改详情", url);
    }


</script>

</body>

</html>