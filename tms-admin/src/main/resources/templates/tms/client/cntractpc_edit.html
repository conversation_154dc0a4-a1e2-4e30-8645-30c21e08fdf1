<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('新增合同价')" />
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .cur{
        cursor: pointer;
    }
    /* .show-div{
        display: block;
    } */
    .tc{
        text-align: center;
    }
    .fixed-table-body{
        overflow: inherit;
    }
    .bootstrap-select.form-control{
        position: initial;
    }

    .flex{
        display: flex;
        align-items:center;
        justify-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 26px;
        text-align: right;
        color: #000000 !important;
        margin-bottom: 0;
    }
    .flex_right{
        line-height: 26px;
        min-width:0;
        flex:1;
    }
    .price-readonly {
        background-color: #f0f0f0 !important;
        color: #888888 !important;
        cursor: not-allowed !important;
        pointer-events: none !important;
    }

</style>
<body>
    <div class="form-content">
        <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
            <div class="panel-body">
                <input type="hidden" id="customerId" name="customerId" th:value="${custContractpcVO.customerId}">
                <input type="hidden" id="custContractpcId" name="custContractpcId" th:value="${custContractpcVO.custContractpcId}">

                <div class="row">

                </div>
                <div class="row" id="addr" th:if="${custContractpcVO.billingMethod != 7}">
                    <div class="col-md-12 col-xs-12">
                        <div class="flex">
                            <label class="flex_left"> 线路名称：</label>
                            <div class="flex_right">
                                <div class="flex" style="justify-content: flex-start;">
                                    <div class="flex mr10">
                                        <!-- <span class="label label-warning pa2">提</span> -->
                                        [[${custContractpcVO.deliName}]] 
                                    </div>
                                    <i class="fa fa-long-arrow-right mr10" style="color: #1ab394;"></i>
                                    <div class="flex">
                                        <!-- <span class="label label-success pa2">到</span> -->
                                        [[${custContractpcVO.arriName}]] 
                                    </div>
                                </div>
                            </div>   
                        </div>
                    </div>

                    <!-- <div class="col-md-12 col-xs-12">
                        <div class="flex" style="align-items: flex-start;">
                            <label class="flex_left"> <span class="label label-warning pa2">提</span> </label>
                            <div class="flex_right">
                                [[${custContractpcVO.deliName}]]
                            </div>   
                        </div>
                    </div>

                    <div class="col-md-12 col-xs-12">
                        <div class="flex" style="align-items: flex-start;">
                            <label class="flex_left"> <span class="label label-success pa2">到</span> </label>
                            <div class="flex_right">
                                [[${custContractpcVO.arriName}]]
                            </div>   
                        </div>
                    </div> -->
                </div>
                

                <div class="row mt10">
                    <div class="col-md-5 col-xs-5">
                        <div class="flex">
                            <label class="flex_left">计费方式：</label>
                            <div class="flex_right">
                                <select name="billingMethod" id="billingMethod"
                                        th:class="${custContractpcVO.billingMethod == 7 ? 'form-control custom-select price-readonly' : 'form-control custom-select'}"
                                        onchange="changeBillingMethod()" required>
                                    <option value="" disabled selected hidden>请选择计费方式</option>
                                    <option th:each="dict : ${billingMethod}" th:text="${dict.context}" th:value="${dict.value}"
                                        th:selected="${custContractpcVO.billingMethod}==${dict.value}" th:disabled="${custContractpcVO.billingMethod != 7 and dict.value == 7}">
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 col-xs-4">
                        <div class="flex">
                            <div class="flex_right">
                                <div id="" style="align-items: baseline;">
                                    <input type="checkbox" id="isRound"
                                           onchange="changeIsRound()"
                                           style="transform: scale(1.2);" />

                                    <input name="isRound" value="" type="hidden">
                                    <label for="isRound" style="font-size: 1.1em; vertical-align: middle;user-select:none;">总价四舍五入(保留到个位)</label>

                                </div>
                            </div>
                        </div>
                    </div>


                </div>

                <div class="row mt10">
                    <div class="col-md-6 col-xs-6">
                        <div class="flex">
                            <label class="flex_left">货品特性：</label>
                            <div class="flex_right">
                                <select name="goodsCharacter" id="goodsCharacter" class="form-control valid custom-select" required aria-invalid="false">
                                    <option value="" disabled selected hidden>请选择货品特性</option>
                                    <option th:each="dict : ${goodsCharacter}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                                            th:selected="${custContractpcVO.goodsCharacter}==${dict.dictValue}">
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-xs-6">
                        <div class="flex">
                            <label class="flex_left">货品：</label>
                            <div class="flex_right">
                                <input class="form-control" onclick="selectGoods(this)" type="text"
                                       th:value="${custContractpcVO.goodsName}" readonly />
                                <input name="goodsId" type="hidden" th:value="${custContractpcVO.goodsId}">
                                <input name="goodsName" type="hidden" th:value="${custContractpcVO.goodsName}">
                            </div>
                        </div>
                    </div>

                </div>

                <div class="row mt10">
                    <div class="col-md-6 col-xs-6">
                        <div class="flex">
                            <label class="flex_left">车长：</label>
                            <div class="flex_right">
                                <select name="carLen" id="carLen" class="form-control" aria-invalid="false">
                                    <option value=""></option>
                                    <option th:each="dict : ${vehicleLength}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                                        th:selected="${custContractpcVO.carLen}==${dict.dictValue}">
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-xs-6">
                        <div class="flex">
                            <label class="flex_left">车型：</label>
                            <div class="flex_right"> 
                                <select name="carType" id="carType" class="form-control" aria-invalid="false" th:with="type=${@dict.getType('car_type')}">
                                    <option value=""></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                                        th:selected="${custContractpcVO.carType}==${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt10">
                    <div class="col-md-6 col-xs-6">
                        <div class="flex" style="align-items: flex-start;">
                            <label class="flex_left">是否大件：</label>
                            <div class="flex_right">
                                <select name="isOversize" id="isOversize" class="form-control custom-select" aria-invalid="false">
                                    <option value="2" th:selected="${custContractpcVO.isOversize == 2}">不区分</option>
                                    <option value="0" th:selected="${custContractpcVO.isOversize == 0}">非大件</option>
                                    <option value="1" th:selected="${custContractpcVO.isOversize == 1}">大件</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-xs-6">
                        <div class="flex" style="align-items: flex-start;">
                            <label class="flex_left">运输方式：</label>
                            <div class="flex_right">
                                <select name="isFtl" id="isFtl" class="form-control custom-select" aria-invalid="false">
                                    <option value=""></option>
                                    <option value="0" th:selected="${custContractpcVO.isFtl == 0}">零担</option>
                                    <option value="1" th:selected="${custContractpcVO.isFtl == 1}">整车</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt10">
                    <div class="col-md-6 col-xs-6">
                        <div class="flex" style="align-items: flex-start;">
                            <label class="flex_left">是否往返订单：</label>
                            <div class="flex_right">
                                <select name="isRoundTrip" id="isRoundTrip" class="form-control custom-select" aria-invalid="false">
                                    <option value="2" th:selected="${custContractpcVO.isRoundTrip == 2}">不区分</option>
                                    <option value="0" th:selected="${custContractpcVO.isRoundTrip == 0}">单程</option>
                                    <option value="1" th:selected="${custContractpcVO.isRoundTrip == 1}">往返</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="row mt10">
                    <div class="col-md-3 col-xs-3">
                        <div class="flex" style="align-items: flex-start;">
                            <label class="flex_left">是否有区间：</label>
                            <div class="flex_right">
                                <section class='model-15'>
                                    <label class="checkbox-switch">
                                        <input type="checkbox" id="checkbox" />
                                        <label for="checkbox" class="switch-label">
                                            <span class="label-text">是</span>
                                            <span class="label-text">否</span>
                                        </label>
                                    </label>
                                </section>
                                <input type="hidden" id="ifSection" name="ifSection" th:field="*{custContractpcVO.ifSection}"/>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-xs-3">
                        <div class="flex" style="align-items: flex-start;">
                            <label class="flex_left">是否含税：</label>
                            <div class="flex_right">
                                <section class='model-15 '>
                                    <label class="checkbox-switch">
                                        <input type="checkbox" id="isIncludeTax" th:checked="${custContractpcVO.isIncludeTax == 1}"/>
                                        <label for="isIncludeTax" class="switch-label">
                                            <span class="label-text">是</span>
                                            <span class="label-text">否</span>
                                        </label>
                                    </label>
                                </section>
                                <input type="hidden" name="isIncludeTax" th:value="${custContractpcVO.isIncludeTax}" />
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-xs-3">
                        <div class="flex">
                            <div class="flex_right">
                                <div id="isKilRoundDiv" style="display: none;align-items: baseline;">
                                    <input type="checkbox" id="isKilRound"
                                           onchange="changeIsKilRound()"
                                           style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />

                                    <input name="isKilRound" value="" type="hidden">
                                    <label for="isKilRound" style="font-size: 1.1em; vertical-align: middle;user-select:none;">单价*公里数四舍五入</label>

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-xs-3">
                        <div class="flex">
                            <div class="flex_right">
                                <div id="isSkipMileageDiv" style="display: none;align-items: baseline;">
                                    <input type="checkbox" id="isSkipMileage"
                                           onchange="changeIsSkipMileage()"
                                           style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />

                                    <input name="isSkipMileage" value="" type="hidden">
                                    <label for="isSkipMileage" style="font-size: 1.1em; vertical-align: middle;user-select:none;">计算价格忽略公里数</label>

                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="floatDate">
                    <div class="row">
                        <div class="col-md-3 col-xs-3">
                            <div class="flex"  style="align-items: center;">
                                <label class="flex_left" style="width: 80px; margin-bottom: 0;">合同价：</label>
                                <div class="flex_right" style="flex: 1;">
                                    <input class="form-control"  name="guidingPrice" id="guidingPrice" required type="text" th:value="${custContractpcVO.guidingPrice}"
                                           oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-xs-3">
                            <div class="flex" style="align-items: center;">
                                <label class="flex_left" style="width: 80px; margin-bottom: 0;">保底价：</label>
                                <div class="flex_right" style="flex: 1;">
                                    <input class="form-control"  name="reservePrice" id="reservePrice" type="text"
                                           th:value="${custContractpcVO.reservePrice}"
                                           oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10" />
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-xs-3">
                            <div class="flex" style="align-items: center;">
                                <label class="flex_left" style="width: 80px; margin-bottom: 0;">送货费：</label>
                                <div class="flex_right" style="flex: 1;">
                                    <input class="form-control"  name="deliveryFee" id="deliveryFee" type="text"
                                           th:value="${custContractpcVO.deliveryFee}"
                                           oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10" />
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row" style="margin-top: 15px;" shiro:hasPermission="client:contract:setCostPrice">
                        <div class="col-md-3 col-xs-3">
                            <div class="flex" style="align-items: center;">
                                <label class="flex_left" style="width: 80px; margin-bottom: 0;">成本价：</label>
                                <div class="flex_right">
                                    <input class="form-control"  name="costPrice" id="costPrice" type="text" th:value="${custContractpcVO.costPrice}"
                                           oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10" />
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-xs-3">
                            <div class="flex" style="align-items: center;">
                                <label class="flex_left" style="width: 80px; margin-bottom: 0;">成本票点：</label>
                                <div class="flex_right" style="flex: 1;">
                                    <select name="costBillingType" id="costBillingType" class="form-control valid" th:with="type=${@dict.getType('billing_type')}"
                                            aria-invalid="false" aria-required="true">
                                        <option value=""></option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}" th:attr="data-rate=${dict.numVal1}"
                                                th:selected="${custContractpcVO.costBillingType == dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="show-div" id="show-div">
                    <table border="0" id="infoTabFour" class="custom-tab table td">
                        <thead>
                            <tr>
                                <th style="width: 5%;" rowspan="2"> <a class="collapse-link add-alink show-alink" style="font-size: 22px;" title="新增" onclick="insertDiv(this,0)">+</a> </th>

                                <th style="width: 10%;">区间开始</th>
                                <th style="width: 20%;">自定义区间</th>
                                <th style="width: 10%;">区间结束</th>
                                <th style="width: 10%;">合同价</th>
                                <th style="width: 10%;" shiro:hasPermission="client:contract:setCostPrice">成本价</th>
                                <th style="width: 15%;" shiro:hasPermission="client:contract:setCostPrice">成本票点</th>
                                <th style="width: 10%;">价格类型</th>
                                <th style="width: 10%;">送货费</th>

                            </tr>

                        </thead>
                        <tbody id="sectionTbody">
                            <tr th:each="payDetail,status:${custContractpcVO.contractpcSectionList}"
                                th:id="|kilTr_${status.index}_${status.index}|">
                                <input type="hidden" th:name="|contractpcSectionList[${status.index}].contractpcSectionId|"
                                       th:value="${payDetail.contractpcSectionId}">

                                <td>
                                    <a class="close-link del-alink show-alink" th:onclick="|showDivDel(${status.index})|" title="删除">-</a>
                                    <input name="range_0" hidden>
                                </td>
                                <td>
                                    <input class="form-control" placeholder="区间开始" type="text"
                                           oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                           th:name="|contractpcSectionList[${status.index}].startSection|"
                                           th:value="${payDetail.startSection}"
                                           th:readonly="${payDetail.isFixedPrice == 2}"
                                           th:classappend="${payDetail.isFixedPrice == 2 ? 'price-readonly' : ''}">
                                </td>
                                <td>
                                    <div class="flex">
                                        <select th:name="|contractpcSectionList[${status.index}].startOperator|"
                                                th:readonly="${payDetail.isFixedPrice == 2}"
                                                th:classappend="${payDetail.isFixedPrice == 2 ? 'price-readonly' : ''}"
                                                class="form-control">
                                            <option value="0" th:selected="${payDetail.startOperator==0}">＜</option>
                                            <option value="1" th:selected="${payDetail.startOperator==1}">≤</option>
                                        </select>
                                        <div style="margin: 0 10px;">x</div>
                                        <select th:name="|contractpcSectionList[${status.index}].endOperator|"
                                                th:readonly="${payDetail.isFixedPrice == 2}"
                                                th:classappend="${payDetail.isFixedPrice == 2 ? 'price-readonly' : ''}"
                                                class="form-control">
                                            <option value="2" th:selected="${payDetail.endOperator==2}">＜</option>
                                            <option value="3" th:selected="${payDetail.endOperator==3}">≤</option>
                                        </select>
                                    </div>
                                   
                                </td>

                                <td>
                                    <input class="form-control" placeholder="区间结束" type="text" oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                        th:name="|contractpcSectionList[${status.index}].endSection|" th:value="${payDetail.endSection}">
                                </td>
                                <td>
                                    <input class="form-control" placeholder="合同价" type="text" oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                        th:name="|contractpcSectionList[${status.index}].guidingPrice|" th:value="${payDetail.guidingPrice}">
                                </td>
                                <td shiro:hasPermission="client:contract:setCostPrice">
                                    <input class="form-control" placeholder="成本价" type="text" oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                        th:name="|contractpcSectionList[${status.index}].costPrice|" th:value="${payDetail.costPrice}">
                                </td>
                                <td shiro:hasPermission="client:contract:setCostPrice">
                                    <select th:name="|contractpcSectionList[${status.index}].costBillingType|"
                                            class="form-control valid" th:with="type=${@dict.getType('billing_type')}"
                                            aria-invalid="false" aria-required="true">
                                        <option value=""></option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}" th:attr="data-rate=${dict.numVal1}"
                                                th:selected="${payDetail.costBillingType == dict.dictValue}"></option>
                                    </select>
                                </td>

                                <td>
                                    <select  th:name="|contractpcSectionList[${status.index}].isFixedPrice|"
                                             th:onchange="|handlePriceChange(${status.index})|" class="form-control">
                                        <option value="0" th:selected="${payDetail.isFixedPrice==0}">单价</option>
                                        <option value="1" th:selected="${payDetail.isFixedPrice==1}">一口价</option>
                                        <option value="2" th:selected="${payDetail.isFixedPrice==2}">起步价</option>
                                    </select>
                                </td>
                                <td>
                                    <input class="form-control" placeholder="送货费" type="text"
                                           oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                           th:name="|contractpcSectionList[${status.index}].deliveryFee|"
                                           th:value="${payDetail.deliveryFee}">
                                </td>

                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="show-div" id="show-div-1">
                    <table border="0" class="custom-tab table td">
                        <thead>
                            <tr>
                                <th style="width: 15%;" colspan="2">
                                    <a class="collapse-link add-alink show-alink"
                                       style="font-size: 22px;float:left;" title="新增"
                                       onclick="insertDiv(this,1)">+</a>
                                    <span style="display:inline-block;text-align:center">公里(a <= x < b)</span>

                                </th>
                                <th style="width: 10%;">区间开始</th>
                                <th style="width: 15%;">自定义区间</th>
                                <th style="width: 10%;">区间结束</th>
                                <th style="width: 10%;">合同价</th>
                                <th style="width: 10%;" shiro:hasPermission="client:contract:setCostPrice">成本价</th>
                                <th style="width: 10%;" shiro:hasPermission="client:contract:setCostPrice">成本票点</th>
                                <th style="width: 10%;">价格类型</th>
                                <th style="width: 10%;">送货费</th>

                            </tr>

                        </thead>
                        <tbody id="sectionTbody-1">

                            <div th:each="entry , iterStat: ${groupedSections}">
                                <tr th:each="section, stat : ${entry.value}"
                                    th:with="globIndex=${section.params.index}"
                                    th:id="|kilTr_${iterStat.index}_${globIndex}|">
                                    <input type="hidden" th:name="|contractpcSectionList[${globIndex}].contractpcSectionId|"
                                           th:value="${section.contractpcSectionId}">

                                    <td th:if="${stat.index == 0}"
                                        th:id="|kilTd_${iterStat.index}|"
                                        th:attr="rowspan=${entry.value.size()},data-ind=${entry.value.size()}">
                                        <div style="display: flex; align-items: center;">
                                            <span>
                                                <a class="close-link del-alink show-alink"
                                                   th:onclick="|showDivDel(${iterStat.index})|" title="删除">-</a>
                                            </span>
                                            <span style="display:flex; align-items:center;margin-left: 5px">
                                            <input th:name="|contractpcSectionList[${globIndex}].startKil|"
                                                   th:id="|startKil_${iterStat.index}_${globIndex}|"
                                                   th:onchange="|changeKil(this,0,${iterStat.index})|"
                                                   th:value="${#strings.substringBefore(entry.key, '-')}"
                                                   style="margin-right: 5px;" class="form-control" placeholder="始" type="text">
                                            <span style="margin: 0 5px;">-</span>
                                            <input th:name="|contractpcSectionList[${globIndex}].endKil|"
                                                   th:id="|endKil_${iterStat.index}_${globIndex}|"
                                                   th:onchange="|changeKil(this,1,${iterStat.index})|"
                                                   th:value="${#strings.substringAfter(entry.key, '-')}"
                                                   class="form-control" placeholder="终" type="text">
                                        </span>
                                        </div>

                                    </td>
                                    <td th:if="${stat.index == 0}">
                                        <div style="display: flex; align-items: center;">
                                            <a class="collapse-link add-alink show-alink " style="font-size: 22px;"
                                               title="新增" th:attr="data-index=${iterStat.index}, data-globIndex=${globIndex}"
                                               th:onclick="|insertDiv_1(this.getAttribute('data-index'),this.getAttribute('data-globIndex'))|">+</a>
                                        </div>
                                        <input name="range" hidden>
                                    </td>
                                    <td th:if="${stat.index > 0}">
                                        <input th:name="|contractpcSectionList[${globIndex}].startKil|"
                                               th:id="|startKil_${iterStat.index}_${globIndex}|"
                                               th:value="${#strings.substringBefore(entry.key, '-')}"
                                               style="margin-right: 5px;" class="form-control" placeholder="始" type="hidden">
                                        <input th:name="|contractpcSectionList[${globIndex}].endKil|"
                                               th:id="|endKil_${iterStat.index}_${globIndex}|"
                                               th:value="${#strings.substringAfter(entry.key, '-')}"
                                               class="form-control" placeholder="终" type="hidden">
                                        <div style="display: flex; align-items: center;">
                                            <a class="close-link del-alink show-alink"
                                               th:attr="data-index=${iterStat.index}, data-globIndex=${globIndex}"
                                               th:onclick="|showDivDel(this.getAttribute('data-index'),this.getAttribute('data-globIndex'),1)|"
                                               title="删除">-</a>
                                        </div>
                                    </td>

                                    <td>
                                        <input class="form-control" placeholder="区间开始" type="text"
                                               oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                               th:name="|contractpcSectionList[${globIndex}].startSection|"
                                               th:value="${section.startSection}"
                                               th:readonly="${section.isFixedPrice == 2}"
                                               th:classappend="${section.isFixedPrice == 2 ? 'price-readonly' : ''}">
                                    </td>
                                    <td>
                                        <div class="flex">
                                            <select th:name="|contractpcSectionList[${globIndex}].startOperator|"
                                                    th:readonly="${section.isFixedPrice == 2}"
                                                    th:classappend="${section.isFixedPrice == 2 ? 'price-readonly' : ''}"
                                                    class="form-control">
                                                <option value="0" th:selected="${section.startOperator==0}">＜</option>
                                                <option value="1" th:selected="${section.startOperator==1}">≤</option>
                                            </select>

                                            <div style="margin: 0 10px;">x</div>

                                            <select th:name="|contractpcSectionList[${globIndex}].endOperator|"
                                                    th:readonly="${section.isFixedPrice == 2}"
                                                    th:classappend="${section.isFixedPrice == 2 ? 'price-readonly' : ''}"
                                                    class="form-control">
                                                <option value="2" th:selected="${section.endOperator==2}">＜</option>
                                                <option value="3" th:selected="${section.endOperator==3}">≤</option>
                                            </select>
                                        </div>

                                    </td>
                                    <td>
                                        <input class="form-control" placeholder="区间结束" type="text"
                                               oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                               th:name="|contractpcSectionList[${globIndex}].endSection|"
                                               th:value="${section.endSection}">
                                    </td>
                                    <td>
                                        <input class="form-control" placeholder="合同价" type="text" required
                                               oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                               th:name="|contractpcSectionList[${globIndex}].guidingPrice|"
                                               th:value="${section.guidingPrice}">
                                    </td>
                                    <td shiro:hasPermission="client:contract:setCostPrice">
                                        <input class="form-control" placeholder="成本价" type="text"
                                               oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                               th:name="|contractpcSectionList[${globIndex}].costPrice|"
                                               th:value="${section.costPrice}">
                                    </td>
                                    <td shiro:hasPermission="client:contract:setCostPrice">
                                        <select th:name="|contractpcSectionList[${globIndex}].costBillingType|"
                                                class="form-control valid" th:with="type=${@dict.getType('billing_type')}"
                                                aria-invalid="false" aria-required="true">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}" th:attr="data-rate=${dict.numVal1}"
                                                    th:selected="${section.costBillingType == dict.dictValue}"></option>
                                        </select>
                                    </td>

                                    <td>
                                        <select th:name="|contractpcSectionList[${globIndex}].isFixedPrice|"
                                                th:attr="onchange=|handlePriceChange(${globIndex})|"
                                                class="form-control">
                                            <option value="0" th:selected="${section.isFixedPrice==0}">单价</option>
                                            <option value="1" th:selected="${section.isFixedPrice==1}">一口价</option>
                                            <option value="2" th:selected="${section.isFixedPrice==2}">起步价</option>
                                        </select>
                                    </td>
                                    <td>
                                        <input class="form-control" placeholder="送货费" type="text"
                                               oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                               th:name="|contractpcSectionList[${globIndex}].deliveryFee|"
                                               th:value="${section.deliveryFee}">
                                    </td>

                                </tr>
                            </div>
                        </tbody>
                    </table>
                </div>



            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-fileinput-js" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var prefix = ctx + "client/cust_cntractpc";

        var custContractpcVO=[[${custContractpcVO}]]
        var groupedSectionsLength=[[${groupedSectionsLength}]]
        var billing_type = [[${@dict.getType('billing_type')}]];


        let setCostPricePermi = [[${@permission.hasAnyPermi('client:contract:setCostPrice')}]] != "hidden";

        $(function () {
            if(custContractpcVO.ifSection==1){
                $("#checkbox").prop("checked", true);
                $('.floatDate').css("display","none")

                if (custContractpcVO.billingMethod == '8' || custContractpcVO.billingMethod == '9') {
                    $('#show-div-1').css("display","block")
                    $('#sectionTbody').empty();
                }else {
                    $('#show-div').css("display","block");
                    $('#sectionTbody-1').empty();
                }
            }else{
                $("#checkbox").prop("checked", false);
                $('.floatDate').css("display","revert")

                $('#show-div').css("display","none")
                $('#show-div-1').css("display","none")

                $('#sectionTbody').empty();
                $('#sectionTbody-1').empty();

            }

            $("#checkbox").change(function(){
                var isT = $(this).is(":checked")
                let billingMethod = $("#billingMethod").val();

                if(isT){
                    $('#ifSection').val('1')
                    $('.floatDate').css("display","none")


                    if (billingMethod === '8' || billingMethod === '9') {
                        $('#show-div-1').css("display", "block");
                    } else {
                        $('#show-div').css("display", "block");
                    }
                }else{
                    $('#ifSection').val('0')
                    $('.floatDate').css("display","revert")

                    $('#show-div-1').css("display","none")
                    $('#show-div').css("display","none")

                    $('#sectionTbody').empty();
                    $('#sectionTbody-1').empty();
                }
            })

            $('#isIncludeTax').on('change', function() {
                if ($(this).is(':checked')) {
                    $(this).val('1');
                    $('[name="isIncludeTax"]').val('1');

                    changeIsRoundValue(0)
                } else {
                    $(this).val('0');
                    $('[name="isIncludeTax"]').val('0');

                    changeIsRoundValue(1)
                }
            });


            showIsKilRoundDiv()
            if ([[${custContractpcVO.isKilRound}]] == 0) {
                $('#isKilRound').prop('checked', false);
            }else {
                $('#isKilRound').prop('checked', true);
            }
            changeIsKilRound()

            showIsSkipMileageDiv()
            if ([[${custContractpcVO.isSkipMileage}]] == 0) {
                $('#isSkipMileage').prop('checked', false);
            }else {
                $('#isSkipMileage').prop('checked', true);
            }
            changeIsSkipMileage()


            if ([[${custContractpcVO.isRound}]] == 0) {
                $('#isRound').prop('checked', false);
            }else {
                $('#isRound').prop('checked', true);
            }

            changeIsRound()
        });

        /**
         * 提交
         */
        function submitHandler() {
            if ($.validate.form()) {
                var data = $("#form-out-quote").serializeArray();
                $.operate.save(prefix + "/edit", data);
            }
        }

        function changeIsRound() {
            var isChecked = $("#isRound").is(':checked');
            $('input[name="isRound"]').val(isChecked ? '1' : '0');
        }

        function changeIsRoundValue(type) {
            if (type === 1) {
                // 取消选中并禁用复选框
                $('#isRound').prop('checked', false).prop('disabled', true);
                // 移除 onchange 事件处理程序
                $('#isRound').removeAttr('onchange');
                // 阻止用户点击
                $('#isRound').on('click', function(e) {
                    e.preventDefault();
                });
                // 添加样式以指示禁用状态
                $('#isRound').css('pointer-events', 'none');
                $('#isRound').parent().css('opacity', '0.5');

                $('input[name="isRound"]').val('0');
            } else if (type === 0) {
                // 启用复选框
                $('#isRound').prop('disabled', false);
                // 移除阻止用户点击的样式和事件处理程序
                $('#isRound').off('click');
                $('#isRound').css('pointer-events', 'auto');
                $('#isRound').parent().css('opacity', '1');
                // 恢复 onchange 事件处理程序
                $('#isRound').attr('onchange', 'changeIsRound()');
            }

        }


        function changeIsKilRound() {
            var isChecked = $("#isKilRound").is(':checked');
            $('input[name="isKilRound"]').val(isChecked ? '1' : '0');

        }

        function showIsKilRoundDiv() {
            let billingMethod = $("#billingMethod").val();
            if (billingMethod == 8 || billingMethod == 9) {
                $('#isKilRoundDiv').css('display', 'flex');
            }else {
                $("#isKilRoundDiv").hide()
            }
        }

        function changeIsSkipMileage() {
            var isSkipMileage = $("#isSkipMileage").is(':checked');
            $('input[name="isSkipMileage"]').val(isSkipMileage ? '1' : '0');

        }

        function showIsSkipMileageDiv() {
            let billingMethod = $("#billingMethod").val();
            if (billingMethod == 8 || billingMethod == 9) {
                $('#isSkipMileageDiv').css('display', 'flex');
            }else {
                $("#isSkipMileageDiv").hide()
            }
        }


        /**
         *
         */
        function changeBillingMethod() {
            // let billingMethod = $("#billingMethod").val();
            //
            // if (billingMethod == 7) {
            //     $("#addr").hide()
            // }else {
            //     $("#addr").show()
            // }

            let billingMethod = $("#billingMethod").val();
            if (billingMethod == 7 || billingMethod == 8 || billingMethod == 9) {
                $('#deliProvinceId').removeAttr('required');
                $('#deliCityId').removeAttr('required');
                $('#deliAreaId').removeAttr('required');
                $('#arriProvinceId').removeAttr('required');
                $('#arriCityId').removeAttr('required');
                $('#arriAreaId').removeAttr('required');

            } else {
                $('#deliProvinceId').attr('required', 'required');
                $('#deliCityId').attr('required', 'required');
                $('#deliAreaId').attr('required', 'required');
                $('#arriProvinceId').attr('required', 'required');
                $('#arriCityId').attr('required', 'required');
                $('#arriAreaId').attr('required', 'required');
            }

            var isT = $("#checkbox").is(":checked")

            if (isT) {
                $('#show-div-1').css("display","none")
                $('#show-div').css("display","none")
                $('#sectionTbody').empty();
                $('#sectionTbody-1').empty();

                if (billingMethod === '8' || billingMethod === '9') {
                    $('#show-div-1').css("display","block")
                }else {
                    $('#show-div').css("display","block")
                }
            }

            showIsKilRoundDiv()
            showIsSkipMileageDiv()
        }

        function showDivDel(kilIndex, sectionIndex, type) {
            if (type === 1) {
                var $td = $(`#kilTd_${kilIndex}`);
                // 获取原始data-ind值
                var origDataInd = $td.data('ind');
                // 减1
                var newDataInd = origDataInd - 1;
                // 设置新的rowspan和data-ind
                $td.attr('rowspan', newDataInd);
                $td.data('ind', newDataInd);

                $(`[id^=kilTr_${kilIndex}_${sectionIndex}]`).remove();

            }else {
                $(`[id^=kilTr_${kilIndex}_]`).remove();
            }


            // // 当前按钮的tr
            // var $tr = $(obj).closest('tr');
            // // 获取所有的tr
            // var $trs = $tr.parent().find('tr');
            // // 如果只剩一条了,提示不能删除
            // if($trs.length === 1){
            //     return;
            // }
            // // 否则删除当前行
            // $tr.remove();
        }

        //新增
        var section_index = 0
        if(custContractpcVO.contractpcSectionList&&custContractpcVO.contractpcSectionList.length){
            section_index = custContractpcVO.contractpcSectionList.length;
        }

        var kil_index = 0
        if (groupedSectionsLength) {
            kil_index = groupedSectionsLength;
        }

        function insertDiv(obj,type) {
            section_index += 1;
            let sectionIndex = section_index

            kil_index += 1;
            let kilIndex = kil_index

            var appendHtml = `<tr id="kilTr_${kilIndex}_${sectionIndex}">`;

            if (type === 1) {

                appendHtml = appendHtml
                    + `
                            <td id="kilTd_${kilIndex}" rowspan="1" data-ind="1">
                                <div style="display: flex; align-items: center;">
                                    <span>
                                        <a class="close-link del-alink show-alink"
                                           onclick="showDivDel(${kilIndex})" title="删除">-</a>
                                    </span>

                                    <span style="display:flex; align-items:center;margin-left: 5px">
                                        <input name="contractpcSectionList[${sectionIndex}].startKil"
                                               id="startKil_${kilIndex}_${sectionIndex}"
                                               onchange="changeKil(this,0,${kilIndex})"
                                               style="margin-right: 5px;" class="form-control" placeholder="始" type="text">
                                        <span style="margin: 0 5px;">-</span>
                                        <input name="contractpcSectionList[${sectionIndex}].endKil"
                                               id="endKil_${kilIndex}_${sectionIndex}"
                                               onchange="changeKil(this,1,${kilIndex})"
                                               class="form-control" placeholder="终" type="text">
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <a class="collapse-link add-alink show-alink " style="font-size: 22px;" title="新增" onclick="insertDiv_1('${kilIndex}','${sectionIndex}')">+</a>
                                </div>
                                <input name="range" hidden>
                            </td>
                          `
            }else {
                appendHtml = appendHtml
                    + `
                        <td>
                            <div >
                                <a class="close-link del-alink show-alink" onclick="showDivDel(${kilIndex})" title="删除">-</a>
                            </div>
                            <input name="range" hidden>
                        </td>

                `

            }

            let costPriceHtml = setCostPricePermi ?
                                `<td>
                                    <input class="form-control" placeholder="成本价" type="text"
                                    oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                    name="contractpcSectionList[${sectionIndex}].costPrice">
                                </td>` :``

            let costBillingTypeHtml = '';
            if (setCostPricePermi) {
                let billingTypeOption = '';
                for ( var i = 0; i < billing_type.length; i++) {
                    billingTypeOption += `
                        <option value="${billing_type[i].dictValue}" data-rate="${billing_type[i].numVal1}">
                            ${billing_type[i].dictLabel}
                        </option>
                    `
                }

                costBillingTypeHtml = `<td>
                                            <select name="contractpcSectionList[${sectionIndex}].costBillingType"
                                                    class="form-control valid" aria-invalid="false" aria-required="true">
                                                <option value=""></option>
                                                ${billingTypeOption}
                                            </select>
                                        </td>`;
            }



            appendHtml= appendHtml
                + `
                                <td>
                                    <input class="form-control" placeholder="区间开始" type="text"
                                                oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                                name="contractpcSectionList[${sectionIndex}].startSection">
                                </td>
                                <td>
                                    <div class="flex">
                                        <select name="contractpcSectionList[${sectionIndex}].startOperator" class="form-control">
                                            <option value="0">＜</option>
                                            <option value="1">≤</option>
                                        </select>

                                        <div style="margin: 0 10px;">x</div>

                                        <select name="contractpcSectionList[${sectionIndex}].endOperator" class="form-control">
                                            <option value="2">＜</option>
                                            <option value="3">≤</option>
                                        </select>
                                    </div>

                                </td>
                                <td>
                                    <input class="form-control" placeholder="区间结束" type="text"
                                                oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                                name="contractpcSectionList[${sectionIndex}].endSection">
                                </td>
                                <td>
                                    <input class="form-control" placeholder="合同价" type="text" required
                                    oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                    name="contractpcSectionList[${sectionIndex}].guidingPrice">
                                </td>
                                ${costPriceHtml}
                                ${costBillingTypeHtml}
                                <td>
                                    <select name="contractpcSectionList[${sectionIndex}].isFixedPrice"
                                            onchange="handlePriceChange(${sectionIndex})" class="form-control">
                                        <option value="0" selected>单价</option>
                                        <option value="1" >一口价</option>
                                        <option value="2" >起步价</option>
                                    </select>
                                </td>
                                <td>
                                    <input class="form-control" placeholder="送货费" type="text"
                                    oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                    name="contractpcSectionList[${sectionIndex}].deliveryFee">
                                </td>

                            </tr>`;

            if (type === 1) {
                $("#sectionTbody-1").append(appendHtml);
            }else {
                $("#sectionTbody").append(appendHtml);
            }
        }

        function insertDiv_1(kilIndex,oldSectionIndex) {
            section_index += 1;
            let sectionIndex = section_index

            let startKil = $(`#startKil_${kilIndex}_${oldSectionIndex}`).val();
            let endKilKil = $(`#endKil_${kilIndex}_${oldSectionIndex}`).val();

            let costPriceHtml = setCostPricePermi ?
                                `<td>
                                    <input class="form-control" placeholder="成本价" type="text"
                                    oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                    name="contractpcSectionList[${sectionIndex}].costPrice">
                                </td>` : ``

            let costBillingTypeHtml = '';
            if (setCostPricePermi) {
                let billingTypeOption = '';
                for ( var i = 0; i < billing_type.length; i++) {
                    billingTypeOption += `
                        <option value="${billing_type[i].dictValue}" data-rate="${billing_type[i].numVal1}">
                            ${billing_type[i].dictLabel}
                        </option>
                    `
                }

                costBillingTypeHtml = `<td>
                                            <select name="contractpcSectionList[${sectionIndex}].costBillingType"
                                                    class="form-control valid" aria-invalid="false" aria-required="true">
                                                <option value=""></option>
                                                ${billingTypeOption}
                                            </select>
                                        </td>`;
            }

            var  appendHtml=`<tr id="kilTr_${kilIndex}_${sectionIndex}">
                                <td>
                                    <input name="contractpcSectionList[${sectionIndex}].startKil"
                                           id="startKil_${kilIndex}_${sectionIndex}"
                                           value="${startKil}"
                                           style="margin-right: 5px;" class="form-control" placeholder="始" type="hidden">
                                    <input name="contractpcSectionList[${sectionIndex}].endKil"
                                           id="endKil_${kilIndex}_${sectionIndex}"
                                           value="${endKilKil}"
                                           class="form-control" placeholder="终" type="hidden">
                                    <div style="display: flex; align-items: center;">
                                        <a class="close-link del-alink show-alink" onclick="showDivDel(${kilIndex},${sectionIndex},1)" title="删除">-</a>
                                    </div>
                                </td>
                                <td>
                                    <input class="form-control" placeholder="区间开始" type="text"
                                                oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                                name="contractpcSectionList[${sectionIndex}].startSection">
                                </td>
                                <td>
                                    <div class="flex">
                                        <select name="contractpcSectionList[${sectionIndex}].startOperator" class="form-control">
                                            <option value="0">＜</option>
                                            <option value="1">≤</option>
                                        </select>

                                        <div style="margin: 0 10px;">x</div>

                                        <select name="contractpcSectionList[${sectionIndex}].endOperator" class="form-control">
                                            <option value="2">＜</option>
                                            <option value="3">≤</option>
                                        </select>
                                    </div>

                                </td>
                                <td>
                                    <input class="form-control" placeholder="区间结束" type="text"
                                                oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                                name="contractpcSectionList[${sectionIndex}].endSection">
                                </td>
                                <td>
                                    <input class="form-control" placeholder="合同价" type="text" required
                                    oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                    name="contractpcSectionList[${sectionIndex}].guidingPrice">
                                </td>
                                ${costPriceHtml}
                                ${costBillingTypeHtml}
                                <td>
                                    <select name="contractpcSectionList[${sectionIndex}].isFixedPrice"
                                            onchange="handlePriceChange(${sectionIndex})" class="form-control">
                                        <option value="0" selected>单价</option>
                                        <option value="1" >一口价</option>
                                        <option value="2" >起步价</option>
                                    </select>
                                </td>
                                <td>
                                    <input class="form-control" placeholder="送货费" type="text"
                                    oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                    name="contractpcSectionList[${sectionIndex}].deliveryFee">
                                </td>

                            </tr>`

            var $td = $(`#kilTd_${kilIndex}`);
            // 获取原始data-ind值
            var origDataInd = $td.data('ind');
            // 减1
            var newDataInd = origDataInd + 1;
            // 设置新的rowspan和data-ind
            $td.attr('rowspan', newDataInd);
            $td.data('ind', newDataInd);

            $(`#kilTr_${kilIndex}_${oldSectionIndex}`).after(appendHtml);
            return false;
        }

        function changeKil(obj, type, kilIndex) {
            let val = $(obj).val();
            if (type === 0) {
                $(`[id^=startKil_${kilIndex}_]`).val(val);
            } else if (type === 1) {
                $(`[id^=endKil_${kilIndex}_]`).val(val);
            }
        }

        /**
         * 选择货品名称
         *
         */
        function selectGoods(obj) {
            var customerId = window.parent.document.getElementById("customerId").value;

            layer.open({
                type: 2,
                area: ['80%', '80%'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "选择货品",
                content: ctx + "client/goods?type=0&customerId=" + customerId,
                btn: ['确定','清空', '关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                yes: function (index, layero){
                    //获取整行
                    var rows = layero.find('iframe')[0].contentWindow.getChecked();
                    if (rows.length === 0) {
                        $.modal.alertWarning("请至少选择一条记录");
                        return;
                    }
                    $(obj).val(rows[0].goodsName);
                    $("input[name='goodsId']").val(rows[0].goodsId);
                    $("input[name='goodsName']").val(rows[0].goodsName);
                    layer.close(index);
                },
                btn2: function(index, layero){
                    $(obj).val('');
                    $("input[name='goodsId']").val('');
                    $("input[name='goodsName']").val('');
                    layer.close(index);
                },
                cancel: function(index) {
                    return true;
                }
            });


            // $.modal.open("选择货品", ctx + "client/goods?type=0&customerId=" + customerId, "600", "500", function (index, layero) {
            //     //获取整行
            //     var rows = layero.find('iframe')[0].contentWindow.getChecked();
            //     if (rows.length === 0) {
            //         $.modal.alertWarning("请至少选择一条记录");
            //         return;
            //     }
            //     $(obj).val(rows[0].goodsName);
            //     $("input[name='goodsId']").val(rows[0].goodsId);
            //     $("input[name='goodsName']").val(rows[0].goodsName);
            //     layer.close(index);
            // });
        }


        function handlePriceChange(index) {
            var $startSectionInput = $('input[name="contractpcSectionList[' + index + '].startSection"]');
            var $startOperatorSelect = $('select[name="contractpcSectionList[' + index + '].startOperator"]');
            var $endOperatorSelect = $('select[name="contractpcSectionList[' + index + '].endOperator"]');
            var $isFixedPriceSelect = $('select[name="contractpcSectionList[' + index + '].isFixedPrice"]');

            if ($isFixedPriceSelect.val() == "2") {
                // 选中"起步价"时
                $startSectionInput
                    .prop('readonly', true)
                    .val('0')
                    .addClass('price-readonly');

                $startOperatorSelect
                    .prop('readonly', true)
                    .val('1')
                    .addClass('price-readonly');

                $endOperatorSelect
                    .prop('readonly', true)
                    .val('3')
                    .addClass('price-readonly');
            } else {
                // 其他选项时
                $startSectionInput
                    .prop('readonly', false)
                    .removeClass('price-readonly');

                $startOperatorSelect
                    .prop('readonly', false)
                    .removeClass('price-readonly');

                $endOperatorSelect
                    .prop('readonly', false)
                    .removeClass('price-readonly');
            }
        }


    </script>
</body>

</html>