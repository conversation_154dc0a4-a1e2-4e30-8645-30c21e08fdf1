<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/css/bootstrap-editable.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>

</head>
<style>
    /* 页面整体布局 */
    body.gray-bg {
        height: 100vh;
        overflow: hidden;
    }

    /* 搜索区域固定 */
    .search-container {
        flex-shrink: 0;
        background: #fff;
        padding: 5px;
    }

    /* 表格容器 */
    .table-container {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    /* 工具栏固定 */
    .toolbar-container {
        flex-shrink: 0;
        padding: 10px 15px;
        background: #fff;
        border-bottom: 1px solid #e5e5e5;
    }

    /* 表格滚动区域 */
    .table-scroll-area {
        flex: 1;
        overflow: auto;
        position: relative;
    }
    .table-scroll-area .bootstrap-table {
        height: 100%;
    }
    .table-scroll-area .bootstrap-table .fixed-table-container {
        height: 100%;
    }

    .table-scroll-area .bootstrap-table .fixed-table-body {
        overflow-y: auto;
        max-height: calc(100vh - 200px); /* 根据搜索栏和工具栏高度调整 */
        /* 确保表体紧贴表头 */
        margin-top: 0 !important;
        border-top: none !important;
    }

    /* 彻底修复表头缝隙问题 */
    .table-scroll-area .bootstrap-table .fixed-table-header {
        position: sticky;
        top: 0;
        z-index: 100;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-bottom: 1px solid #ddd;
        /* 确保表头完全覆盖 */
        margin-bottom: -1px;
    }

    /* 表头容器样式 */
    .table-scroll-area .bootstrap-table .fixed-table-header-columns {
        background: #fff !important;
        border-bottom: 1px solid #ddd !important;
    }

    /* 确保表头在滚动时保持固定且无缝隙 - 简化版本避免列错位 */
    .table-scroll-area .table thead th {
        position: sticky;
        top: 0px;
        z-index: 999 !important;
        background-color: #eff3f8 !important;
        border-top: none !important;
        border-bottom: 1px solid #ddd !important;
        /* 简化的阴影，不影响布局 */
        box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
        /* 防止表头抖动和缝隙 */
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
        /* 确保表头完全覆盖 */
        margin-bottom: -1px;
        /* 确保主表头完全不透明 */
        opacity: 1 !important;
        /* 关键修复：使用outline创建无缝覆盖，不影响布局 */
        outline: 1px solid #eff3f8;
        outline-offset: -1px;
    }

    /* 修复Bootstrap Table特有的表头结构 */
    .table-scroll-area .bootstrap-table .fixed-table-header table {
        margin-bottom: 0 !important;
        border-bottom: 1px solid #ddd !important;
    }

    /* 确保表体与表头无缝连接 */
    .table-scroll-area .bootstrap-table .fixed-table-body table {
        margin-top: 0 !important;
        border-top: none !important;
    }
    .custom-cell-background-red {
        background-color: rgb(255 0 0 / 43%) !important; /* 自定义的背景颜色 */
    }
    .custom-cell-background-red a {
        color: #FFFFFF;
    }

    .custom-cell-background-green {
        background-color: #8DBF8B !important; /* 自定义的背景颜色 */
    }
    .custom-cell-background-green a {
        color: #FFFFFF;
    }

    /* 添加工具栏与表格之间的间距 */
    #toolbar {
        margin-bottom: 10px;
    }

    /* 美化新添按钮 */
    #toolbar .btn-info {
        margin-right: 5px;
        padding: 5px 15px;
        transition: all 0.3s;
    }

    #toolbar .btn-info:hover {
        background-color: #17a2b8;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    .lf{
        margin-left: 10px;
        background-color: #ffffff;
        border-radius: 3px;
        cursor:pointer;
    }

    .label-success{
        color: #1ab394;
        background-color: transparent;
        border: 1px solid #1ab394;
    }
    .label-primary{
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }
    .label-warning{
        color: #f8ac59;
        background-color: transparent;
        border: 1px solid #f8ac59;
    }

    .customer-stats {
        font-size: 16px;
    }

    .customer-stats span {
        margin-left: 10px;
        font-weight: 550;
    }

    .customer-stats span:nth-child(2) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
        color: #FF6C00;
    }
    .customer-stats span:nth-child(3) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
    }

    .customer-stats span:nth-child(4) {
        margin-left: 0px;
        font-size: 16px;
        font-weight: 450;
        color: #0d62bb;
    }

    .table-light {
        background-color: #f8f9fa; /* 设置背景色为浅灰色 */
    }

    .progress-wrapper {
        position: relative;
        width: 100%;
        height: 20px;
        background-color: #f5f5f5;
        border-radius: 4px;
        overflow: hidden;
    }

    .progress {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .progress-bar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        background-color: #4caf50;
        transition: width 0.3s ease;
    }

    .progress-text {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        line-height: 20px;
        padding: 0 10px;
        font-size: 12px;
    }

    .tagged-div {
        position: relative; /* 设置为相对定位，使内嵌元素定位相对于此元素 */
    }

    .tag {
        position: absolute; /* 设置为绝对定位，使其相对于包含它的 .tagged-div 定位 */
        top: -14px; /* 距离顶部为0 */
        left: -5px; /* 距离左侧为0 */
        background-color: #d2e7f9; /* 标记的背景颜色 */
        padding: 1px 3px; /* 内边距 */
        border-radius: 6px; /* 边框圆角 */
        font-size: x-small; /* 字体大小 */
    }

    #buttons-toolbar-container {
        clear: both;
        display: block;
        width: 100%;
    }

    #toolbar {
        margin-bottom: 10px;
        display: inline-block;
    }

    .select-table {
        clear: both;
    }

    /* 问号图标样式 */
    .help-icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-color: #1c84c6;
        color: white;
        border-radius: 50%;
        text-align: center;
        line-height: 16px;
        font-size: 12px;
        margin-left: 4px;
        cursor: help;
    }
    
    /* 弹出提示框样式 */
    .popover {
        max-width: 400px;
    }
    
    /* 确保表格在工具栏下方正确显示 */
    .bootstrap-table {
        clear: both;
        margin-top: 10px;
    }

    /* 表格页脚样式 */
    .bootstrap-table .table tfoot {
        font-weight: bold;
        background-color: #f5f7fa;
    }

    .bootstrap-table .table tfoot td {
        padding: 8px;
        border-top: 2px solid #ddd;
    }

    /* 确保表格在工具栏下方正确显示 */
    .bootstrap-table {
        clear: both;
        margin-top: 10px;
    }

    /*111*/
    /* 添加子表格容器样式 */
    .detail-view-container {
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f4f8 100%);
        border-left: 4px solid #1c84c6;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* 子表格整体样式 */
    .detail-table {
        background-color: #fafbfc;
        border: 1px solid #e1e8ed;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
    }

    /* 子表格表头样式 - 修复层级问题 */
    .detail-table thead th {
        background: linear-gradient(to bottom, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        border-bottom: 2px solid #5a67d8;
        position: relative; /* 不使用sticky，避免与主表头冲突 */
        z-index: 50; /* 确保子表头层级低于主表头 */
    }

    /* 子表格行间隔色 */
    .detail-table tbody tr:nth-child(even) {
        background-color: #f7f9fc;
    }

    .detail-table tbody tr:nth-child(odd) {
        background-color: #ffffff;
    }

    /* 子表格行悬停效果 */
    .detail-table tbody tr:hover {
        background-color: #e8f4fd;
        transform: translateX(2px);
        transition: all 0.2s ease;
    }

    /* 主表格样式增强 - 确保主表头优先级 */
    #bootstrap-table .table > thead > tr > th {
        background: linear-gradient(to bottom, #2c3e50 0%, #34495e 100%);
        color: white;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        border-bottom: 3px solid #1abc9c;
        position: sticky;
        top: 0;
        z-index: 102; /* 确保主表头层级最高 */
    }

    /* 主表格行悬停 */
    #bootstrap-table .table > tbody > tr:hover > td {
        background-color: #f0f8ff;
        transition: background-color 0.2s ease;
    }

    /* 展开按钮样式 */
    .detail-icon {
        color: #1c84c6;
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .detail-icon.expanded {
        transform: rotate(90deg);
        color: #28a745;
    }

    .rating-period-icon {
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-bottom: 2px;
        background-color: #f39c12; /* 橙色背景，区别于问号的蓝色 */
        color: white;
        border-radius: 50%;
        text-align: center;
        line-height: 12px;
        font-size: 10px;
        margin-left: 3px;
        cursor: help;
        font-weight: bold;
        white-space: nowrap; /* 防止换行 */
        vertical-align: middle; /* 垂直居中对齐 */
    }

    /* 修复滚动时的额外样式 */
    .table-scroll-area {
        /* 确保滚动容器有明确的边界 */
        border-top: 1px solid transparent;
        /* 消除可能的间距 */
        padding: 0;
        margin: 0;
    }

    /* 防止表格内容在表头下方显示 */
    .table-scroll-area .bootstrap-table .fixed-table-body {
        position: relative;
        z-index: 1;
    }

    /* 确保详情视图不会覆盖主表头 */
    .detail-view-container {
        position: relative;
        z-index: 10; /* 低于主表头的z-index */
    }

    /* 子表格容器的层级控制 */
    .detail-table {
        position: relative;
        z-index: 11;
    }

    /* 额外的缝隙修复 */
    .table-scroll-area .bootstrap-table {
        border-collapse: collapse;
        border-spacing: 0;
    }

    /* 确保表格行之间无间隙 */
    .table-scroll-area .table {
        border-collapse: collapse !important;
        border-spacing: 0 !important;
        margin: 0 !important;
    }

    /* 修复可能的表格容器间距 */
    .table-scroll-area .bootstrap-table .fixed-table-container {
        padding: 0 !important;
        margin: 0 !important;
        border: none !important;
    }

    /* 强制修复表头与表体之间的缝隙 - 最终解决方案 */
    .table-scroll-area .bootstrap-table .fixed-table-header + .fixed-table-body {
        margin-top: -1px !important;
        border-top: none !important;
    }

    /* 确保表头下方没有任何空白 */
    .table-scroll-area .bootstrap-table .fixed-table-header::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 1px;
        background: #ddd;
        z-index: 102;
    }

    /* 防止表格行在表头上方显示 */
    .table-scroll-area .bootstrap-table .fixed-table-body tbody tr:first-child td {
        border-top: none !important;
    }
</style>
<body class="gray-bg" style="display: flex; flex-direction: column;">
<div class="search-container">
    <div class="col-sm-12 search-collapse">
    <form id="role-form" class="form-horizontal" onkeydown="if(event.keyCode==13){searchPre();return false;}">
        <div class="row no-gutter">
            <div class="col-md-2 col-sm-2">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <input name="groupName" id="groupName" placeholder="集团名称" class="form-control" type="text" maxlength="20" autocomplete="off">                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-2">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <select  id="mgmtDeptName" name="mgmtDeptName"  class="form-control valid noselect2 selectpicker"
                                 aria-invalid="false" data-none-selected-text="管理部">
                            <option value=""></option>
                            <option th:each="dept : ${mgmtDept}" th:value="${dept.deptName}"
                                    th:text="${dept.deptName}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-2">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <select name="custLevel" class="form-control valid noselect2 selectpicker"
                                data-none-selected-text="评级" th:with="type=${@dict.getType('customer_level')}">
                            <option value=""></option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-3 col-sm-3">
                <div class="form-group" style="text-align: left;">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    <a class="btn btn-success btn-rounded btn-sm" onclick="custLevelExport()"><i class="fa fa-download"></i>&nbsp;导出数据</a>

                </div>
            </div>

            <span style="float: right;" id="buttons-toolbar"></span>

        </div>
    </form>
</div>
</div>

<div class="table-container">
    <!-- 工具栏 -->
    <div class="toolbar-container">
        <div class="col-sm-12">
            <div class="btn-group-sm" id="toolbar" role="group">
                <!--        <a class="btn btn-primary" shiro:hasAnyPermissions="tms:insuranceRecord:list"-->
                <!--           onclick="add()">-->
                <!--            <i class="fa fa-plus"></i> 新添-->
                <!--        </a>-->

                <!--        <a class="btn btn-danger multiple disabled" shiro:hasAnyPermissions="tms:insuranceRecord:list"-->
                <!--           onclick="deleteRecord()">-->
                <!--            <i class="fa fa-remove"></i> 删除-->
                <!--        </a>-->

            </div>

          <!--  <div id="buttons-toolbar-container" style="margin-bottom: 10px;">
                &lt;!&ndash;        <span id="buttons-toolbar"></span>&ndash;&gt;
            </div>-->
        </div>
    </div>

    <!-- 表格滚动区域 -->
    <div class="table-scroll-area">
        <div class="col-sm-12">
            <table id="bootstrap-table"
                   data-buttons-toolbar="#buttons-toolbar"
                   class="table   table-bordered table-hover" >
            </table>
        </div>
    </div>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/tableExport.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/js/bootstrap-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/editable/bootstrap-table-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/bootstrap-table-export.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>


<script th:inline="javascript">
    var customerLevel = [[${@dict.getType('customer_level')}]];//1A 2B 3C 4D 5S

    $(function () {
        let options = initOptions();
        $.table.init(options);

        $('#bootstrap-table').on('load-success.bs.table', function(e, data) {
            initializePopovers();

            // 动态设置评级时间问号的内容
            if (data && data.rows && data.rows.length > 0) {
                var first = data.rows[0];
                var start = first.ratingStartTime ? first.ratingStartTime.split(' ')[0] : '';
                var end = first.ratingEndTime ? first.ratingEndTime.split(' ')[0] : '';
                var content = (start && end) ? ('评级数据源取' + start + '到' + end) : '暂无评级周期数据';
                $('#levelTimeHelp').attr('data-content', content);
            } else {
                $('#levelTimeHelp').attr('data-content', '暂无评级周期数据');
            }
        });

    });

    // Reusable popover initialization function
    function initializePopovers() {
        $('[data-toggle="popover"]').popover({
            html: true,
            container: 'body',
            template: '<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>',
            content: function() {
                return $(this).attr('data-content').replace(/\n/g, '<br>');
            }
        });
    }

    function initOptions() {
        return {
            url: ctx + "custAutoLevel/list",
            uniqueId: "id",
            showToggle:false,
            showColumns:false,
            showSearch:false,
            // pagination:true,
            pageSize: 200,
            pageList: [100, 200, 300, 500],
            // showRefresh:false,
            modalName: "客户分级",
            // height: 560,
            clickToSelect: true,
            showFooter: true,  // 启用表格页脚，用于显示汇总
            detailView: true,  // 启用详情视图
            detailFormatter: detailFormatter, // 设置详情格式化函数
            detailFilter: function(index, row) {
                // 只有当客户数量大于1时才显示展开按钮
                return row.custCnt > 1;
            },
            columns: [
                [
                    {
                        title: '客户集团',
                        field: 'groupName',
                        formatter: function(value, row, index) {
                            if (row.custCnt === 1) {
                                return `<span>${row.custAbbr}</span>`;
                            }else {
                                return `<span>${value}</span>`;
                            }
                        }
                    },
                    {
                        title: '管理部',
                        field: 'mgmtDeptName',
                    },
                    {
                        title: '现有评级',
                        // valign: 'middle',
                        // align: 'center',
                        field : 'custLevelCurrent',
                        formatter: function(value, row, index) {
                            // if (!value) return "";
                            if (!value) value=''
                            // 拆分字符串并映射为中文等级
                            var levels = value.split(',')
                                .map(function(code) {
                                    let levelText = '';
                                    let label = $.table.selectDictLabel(customerLevel, code);
                                    label = label.replace(/<[^>]*>/g, '').trim();
                                    switch (label) {
                                        case "S":
                                            levelText = "S级-VIP客户";
                                            break;
                                        case "A":
                                            levelText = "A级-优质客户";
                                            break;
                                        case "B":
                                            levelText = "B级-常规客户";
                                            break;
                                        case "C":
                                            levelText = "C级-需谨慎客户";
                                            break;
                                        case "D":
                                            levelText = "D级-淘汰客户";
                                            break;
                                        default:
                                            levelText = label;
                                            break;
                                    }

                                    return levelText;
                                })
                                .filter(Boolean);

                            var displayText = levels.join('，');
                            
                            // 创建一个包含文本和编辑按钮的容器，使用相对定位
                            var html = '<div class="level-cell" style="position:relative;min-height: 30px;" data-row-id="' + row.id + '" data-original-value="' + value + '">' +
                                       '<span class="level-text">' + displayText + '</span>' +
                                       '<a href="javascript:void(0)" class="edit-btn" style="position:absolute; right:5px; top:50%; transform:translateY(-50%); display:none;">' +
                                       '<i class="fa fa-edit"></i></a>' +
                                       '</div>';
                            return html;
                        },
                        events: {
                            'mouseover .level-cell': function (e, value, row, index) {
                                if (!$(e.currentTarget).hasClass('editing')) {
                                    $(e.currentTarget).find('.edit-btn').show();
                                }
                            },
                            'mouseout .level-cell': function (e, value, row, index) {
                                // 如果不在编辑状态，则隐藏编辑按钮
                                if (!$(e.currentTarget).hasClass('editing')) {
                                    $(e.currentTarget).find('.edit-btn').hide();
                                }
                            },
                            'click .edit-btn': function (e, value, row, index) {
                                e.stopPropagation();
                                var $cell = $(e.currentTarget).closest('.level-cell');
                                var originalValue = $cell.data('original-value');
                                var rowId = $cell.data('row-id');
                                var groupId = row.groupId; // 获取groupId
                                
                                // 标记为编辑状态
                                $cell.addClass('editing');
                                
                                // 隐藏文本和编辑按钮
                                $cell.find('.level-text').hide();
                                $cell.find('.edit-btn').hide();
                                
                                // 创建编辑区域容器，使用flex布局确保元素水平排列
                                var $editContainer = $('<div class="edit-container" style="display:flex; align-items:center; justify-content:center;"></div>');
                                $cell.append($editContainer);
                                
                                // 创建下拉框，设置宽度防止占满整列
                                var $select = $('<select class="form-control level-select" style="width:70%; flex:1; margin-right:5px;">');
                                
                                // 添加选项
                                customerLevel.forEach(function(option) {
                                    // var selected = originalValue.split(',').includes(option.dictValue) ? 'selected' : '';
                                    $select.append('<option value="' + option.dictValue + '">' + option.dictLabel + '</option>');
                                });
                                
                                // 添加确认按钮，设置为不换行
                                var $confirmBtn = $('<button class="btn btn-xs btn-primary confirm-btn" style="white-space:nowrap; flex-shrink:0;">确认</button>');
                                
                                // 将下拉框和确认按钮添加到编辑容器中
                                $editContainer.append($select).append($confirmBtn);

                                // 确认按钮点击事件
                                $confirmBtn.on('click', function() {
                                    var newValue = $select.val();

                                    // 发送AJAX请求更新数据
                                    $.ajax({
                                        url: ctx + "custAutoLevel/updateLevel",
                                        type: "POST",
                                        data: {
                                            groupId: groupId,
                                            custLevel: newValue
                                        },
                                        success: function(result) {
                                            if (result.code == web_status.SUCCESS) {
                                                $.modal.msgSuccess("修改成功");

                                                // 直接更新当前行数据，而不是刷新整个表格
                                                updateRowData(index, newValue, $cell);

                                            } else {
                                                $.modal.alertError(result.msg);
                                            }
                                        },
                                        error: function() {
                                            $.modal.alertError("系统错误");
                                        },
                                        complete: function() {
                                            // 移除编辑状态
                                            $cell.removeClass('editing');
                                            // 移除编辑容器
                                            $editContainer.remove();
                                            // 显示文本
                                            $cell.find('.level-text').show();
                                        }
                                    });
                                });
                                
                                // 点击其他地方关闭编辑状态
                                $(document).on('click.levelEdit', function(e) {
                                    if (!$(e.target).closest('.level-cell').length) {
                                        // 移除编辑状态
                                        $cell.removeClass('editing');
                                        // 移除编辑容器
                                        $editContainer.remove();
                                        // 显示文本
                                        $cell.find('.level-text').show();
                                        // 解绑事件
                                        $(document).off('click.levelEdit');
                                    }
                                });
                            }
                        }
                    },
                    {
                        title: '评级 <span class="help-icon" data-toggle="popover" data-placement="bottom" data-trigger="hover" ' +
                            'data-content="S级：满足四项<br/>A级：任意三项达标<br/>B级：任意二项达标<br/>C级：任意一项达标<br/>D级：都不满足<br/>注：体量<0.5W,评级最高为C级 ' +
                            '   体量【0.5W-1W】，评级最高为B级   体量>1W,正常评级">?</span>',
                        valign: 'middle',
                        align: 'center',
                        field : 'custLevel',
                        formatter: function(value, row, index) {
                            var levelText = "";

                            var label = $.table.selectDictLabel(customerLevel, value); // 返回 "A"~"D"~"S"
                            label = label.replace(/<[^>]*>/g, '').trim(); // 去掉 HTML 标签，保留 "A"~"S"

                            switch (label) {
                                case "S":
                                    levelText = "S级-VIP客户";
                                    break;
                                case "A":
                                    levelText = "A级-优质客户";
                                    break;
                                case "B":
                                    levelText = "B级-常规客户";
                                    break;
                                case "C":
                                    levelText = "C级-需谨慎客户";
                                    break;
                                case "D":
                                    levelText = "D级-淘汰客户";
                                    break;
                                default:
                                    levelText = label;
                                    break;
                            }

                            return levelText;
                        },
                        cellStyle: function(value, row, index, field) {
                            if (value != row.custLevelCurrent) {
                                return { css: { "background-color": "#f8d7da" } }; // 浅红色
                            }
                            return {};
                        }

                    },
                    // {
                    //     title: '评级时间',
                    //     align: 'center',
                    //     valign: 'middle',
                    //     field: 'levelTime',
                    // },
                    {
                        title: '评级时间 <span id="levelTimeHelp" class="help-icon" data-toggle="popover" data-placement="top" data-trigger="hover" data-content="">?</span>',
                        align: 'center',
                        valign: 'middle',
                        field: 'levelTime',
                        formatter: function(value, row, index) {
                            if (!value) return "";
                            var dateOnly = value.split(' ')[0];
                            return '<span style="white-space: nowrap;">' + dateOnly + '</span>';
                        }
                    },
                    {
                        title: '月均营收',
                        align: 'center',
                        valign: 'middle',
                        field : 'avgMonthRevenue',
                        formatter: function(value, row, index) {
                            if (value == null || isNaN(value)) return "";
                            return (value / 10000).toFixed(1) + ' 万元';
                        }
                    },
                    {
                        title: '体量 <span class="help-icon" data-toggle="popover" data-placement="top" data-trigger="hover" data-content="月均>50万，判定为体量大">?</span>',
                        align: 'center',
                        valign: 'middle',
                        field : 'volumeLevel',
                        formatter: function(value, row, index) {
                            return value == 1 ? "大" : "小";
                        },
                        cellStyle: function(value, row, index, field) {
                            if (value == 1) {
                                return { css: { "background-color": "#fff3b0" } }; // 浅黄色
                            }
                            return {};
                        }

                    },
                    // {
                    //     title: '利润率',
                    //     align: 'center',
                    //     valign: 'middle',
                    //     field : 'avgProfitRate',
                    //     formatter: function(value, row, index) {
                    //         if (value != null) {
                    //             return (value * 100).toFixed(2) + "%";
                    //         }
                    //         return "";
                    //     }
                    // },
                    {
                        title: '利润 <span class="help-icon" data-toggle="popover" data-placement="top" data-trigger="hover" data-content="净利润月均>10%，判定为利润高">?</span>',
                        align: 'center',
                        valign: 'middle',
                        field : 'profitLevel',
                        formatter: function(value, row, index) {
                            return value == 1 ? "高" : "低";
                        },
                        cellStyle: function(value, row, index, field) {
                            if (value == 1) {
                                return { css: { "background-color": "#fff3b0" } }; // 浅黄色
                            }
                            return {};
                        }

                    },
                    {
                        title: '付款情况 <span class="help-icon" data-toggle="popover" data-placement="top" data-trigger="hover" data-content="回款率>90%,判定为回款及时">?</span>',
                        align: 'center',
                        valign: 'middle',
                        field : 'paymentStatus',
                        formatter: function(value, row, index) {
                            return value == 1 ? "及时" : "拖拉";
                        },
                        cellStyle: function(value, row, index, field) {
                            if (value == 1) {
                                return { css: { "background-color": "#fff3b0" } }; // 浅黄色
                            }
                            return {};
                        }

                    },
                    {
                        title: '付款方式 <span class="help-icon" data-toggle="popover" data-placement="top" data-trigger="hover" data-content="现金金额占比70%以上（包含70%）判定为现金">?</span>',
                        align: 'center',
                        valign: 'middle',
                        field : 'paymentMethod',
                        formatter: function(value, row, index) {
                            return value == 1 ? "现金" : "承兑";
                        },
                        cellStyle: function(value, row, index, field) {
                            if (value == 1) {
                                return { css: { "background-color": "#fff3b0" } }; // 浅黄色
                            }
                            return {};
                        }

                    },

                ]
            ],
        };
    }

    //更新单行数据
    function updateRowData(rowIndex, newValue, $cell) {
        // 1. 只更新内存中的数据源，不触发表格重新渲染
        var tableData = $('#bootstrap-table').bootstrapTable('getData');
        if (tableData[rowIndex]) {
            tableData[rowIndex].custLevelCurrent = newValue;

            // 2. 格式化新的显示文本
            var newDisplayText = formatSingleCustLevel(newValue);

            // 3. 直接更新DOM元素的显示内容
            $cell.find('.level-text').text(newDisplayText);
            $cell.data('original-value', newValue);

            // 4. 直接更新评级列的背景色（通过DOM操作）
            updateRatingColumnStyleDirect(rowIndex, tableData[rowIndex]);

            // 5. 如果当前行有展开的详情视图，也需要同步更新
            updateDetailViewIfExpanded(rowIndex, newValue);
        }
    }

    //格式化单个客户等级显示
    function formatSingleCustLevel(value) {
        if (!value) return "";

        // 拆分字符串并映射为中文等级
        var levels = value.split(',')
            .map(function(code) {
                let levelText = '';
                let label = $.table.selectDictLabel(customerLevel, code);
                label = label.replace(/<[^>]*>/g, '').trim();
                switch (label) {
                    case "S":
                        levelText = "S级-VIP客户";
                        break;
                    case "A":
                        levelText = "A级-优质客户";
                        break;
                    case "B":
                        levelText = "B级-常规客户";
                        break;
                    case "C":
                        levelText = "C级-需谨慎客户";
                        break;
                    case "D":
                        levelText = "D级-淘汰客户";
                        break;
                    default:
                        levelText = label;
                        break;
                }
                return levelText;
            })
            .filter(Boolean);

        return levels.join('，');
    }

    // 直接通过DOM更新评级列的样式
    function updateRatingColumnStyleDirect(rowIndex, rowData) {
        var $table = $('#bootstrap-table');

        // 找到当前行
        var $currentRow = $table.find('tbody tr[data-index="' + rowIndex + '"]');

        if ($currentRow.length > 0) {
            // 根据你的表格结构，找到评级列（第5列，索引4）
            // 注意：需要根据实际列顺序调整索引
            var $ratingCell = $currentRow.find('td').eq(4); // 假设评级列是第5列

            // 检查现有评级是否与计算评级相同
            if (rowData.custLevel != rowData.custLevelCurrent) {
                $ratingCell.css('background-color', '#f8d7da'); // 浅红色
            } else {
                $ratingCell.css('background-color', ''); // 移除背景色
            }
        }
    }

    // 新增函数：如果详情视图已展开，同步更新详情视图中的数据
    function updateDetailViewIfExpanded(rowIndex, newValue) {
        var $table = $('#bootstrap-table');
        var $currentRow = $table.find('tbody tr[data-index="' + rowIndex + '"]');
        var $detailRow = $currentRow.next('tr.detail-view');

        // 如果详情视图已展开
        if ($detailRow.length > 0 && $detailRow.is(':visible')) {
            var $detailTable = $detailRow.find('.detail-table tbody tr');

            // 更新详情表格中每个客户的现有评级列（第4列）
            $detailTable.each(function() {
                var $custRow = $(this);
                var $custLevelCell = $custRow.find('td').eq(3); // 现有评级列

                // 格式化并更新显示文本
                var newDisplayText = formatSingleCustLevel(newValue);
                $custLevelCell.html(newDisplayText);
            });
        }
    }

    function custLevelExport() {
        layer.open({
            type: 1,
            title: '导出数据',
            area: ['35%', '35%'],
            btn: ['确定', '取消'],
            content: '<div style="padding:30px 20px 10px 20px; text-align:center;white-space:normal;">' +
                '<div style="font-size:32px;color:#3c8dbc;margin-bottom:10px;"><i class="fa fa-file-excel-o"></i></div>' +
                '<div style="font-size:16px;font-weight:bold;margin-bottom:8px;">导出客户评级数据</div>' +
                '<div style="font-size:14px;color:#555;margin-bottom:8px;">' +
                '将导出 <span style="color:#337ab7;font-weight:bold;">明细+汇总</span> 数据，Excel文件包含 <span style="color:#337ab7;font-weight:bold;">两个sheet</span>：<br>' +
                '<span style="color:#5cb85c;">• Sheet1：客户评级明细</span><br>' +
                '<span style="color:#5cb85c;">• Sheet2：客户评级汇总</span>' +
                '</div>' +
                '</div>',
            yes: function(index, layero){
                var search = $.common.formToJSON('role-form');
                $.modal.loading("正在导出数据，请稍后...");
                $.post(ctx + "custAutoLevel/export", search, function (result) {
                    if (result.code == web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                });
                layer.close(index);
            }
        });
    }


    function searchPre() {
        $.table.search('role-form');
        $('#bootstrap-table').on('load-success.bs.table', function() {
            initializePopovers();
        });
    }
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }
    
    // 详情视图格式化函数
    function detailFormatter(index, row) {
        var html = '<div class="detail-view-container" style="position:relative; padding: 15px 20px 15px 25px; margin: 5px 0;">';
        html += '<div class="loading-message" style="padding: 10px; color: #666; font-style: italic;">正在加载集团客户数据...</div>';
        html += '<div class="customer-detail-table" style="display:none;">';
        html += '</div>';
        html += '</div>';

        setTimeout(function() {
            loadCustomerDetails(row.groupId, index);
        }, 100);

        return html;
    }
    
    // 加载客户详情数据
    function loadCustomerDetails(groupId, index) {
        var $container = $('#bootstrap-table').find('tr[data-index="' + index + '"]').next().find('.detail-view-container');
        var $loadingMsg = $container.find('.loading-message');
        var $detailTable = $container.find('.customer-detail-table');
        
        // 获取主表格的列配置
        var mainTable = $('#bootstrap-table');
        var mainTableOptions = mainTable.bootstrapTable('getOptions');
        
        // 发送AJAX请求获取集团下的客户数据
        $.ajax({
            url: ctx + "custAutoLevel/custList",
            type: "POST",
            data: { groupId: groupId },
            success: function(result) {
                if (result.code == web_status.SUCCESS) {
                    // 隐藏加载信息
                    $loadingMsg.hide();
                    
                    // 显示详情表格
                    $detailTable.show();
                    
                    // 创建表格HTML结构
                    // var tableHtml = '<table class="detail-table table table-striped table-bordered" style="margin-bottom:0;">';
                    // tableHtml += '<tbody>';
                   
                    // 创建表格HTML结构
                    var tableHtml = '<table class="detail-table table table-striped table-bordered" style="margin-bottom:0;">';
                    // 添加表头
                    tableHtml += '<thead>';
                    tableHtml += '<tr>';
                    tableHtml += '<th style="width: 30px">序号</th>';
                    
                    // 获取主表格的列标题
                    var mainColumns = mainTableOptions.columns[0];
                    for (var i = 0; i < mainColumns.length; i++) {
                        // 跳过checkbox列和操作列
                        if (mainColumns[i].checkbox) continue;
                        if (mainColumns[i].title === '操作') continue;
                        
                        // 添加列标题，移除问号图标
                        var title = mainColumns[i].title;
                        // 移除问号图标及其相关属性
                        title = title.replace(/<span class="help-icon"[^>]*>.*?<\/span>/g, '');
                        if (title==='客户集团') title='客户简称'
                        
                        tableHtml += '<th style="text-align: ' + (mainColumns[i].align || 'left') + ';">' + title + '</th>';
                    }
                    tableHtml += '</tr>';
                    tableHtml += '</thead>';
                    
                    tableHtml += '<tbody>';
                        
                    // 如果没有数据，显示提示信息
                    if (!result.rows || result.rows.length === 0) {
                        tableHtml += '<tr><td colspan="' + mainTableOptions.columns[0].length + 1 + '" style="text-align:center;">该集团下暂无客户数据</td></tr>';
                    } else {
                        // 遍历客户数据
                        $.each(result.rows, function(i, customer) {
                            tableHtml += '<tr class="detail-row">';
                            
                            // 第一列留空，与展开按钮对齐
                            tableHtml += `<td style="width: 30px">${i + 1}</td>`;
                            
                            // 客户名称列
                            tableHtml += '<td>' + (customer.custAbbr || '') + '</td>';
                            
                            // 管理部列
                            tableHtml += '<td>' + (customer.mgmtDeptName || '') + '</td>';
                            
                            // 现有评级列
                            var custLevelText = formatCustLevel(customer.custLevelCurrent);
                            tableHtml += '<td style="text-align:center;">' + custLevelText + '</td>';
                            
                            // 评级列
                            var levelText = formatCustLevel(customer.custLevel);
                            var levelStyle = '';
                            if (customer.custLevel != customer.custLevelCurrent) {
                                // levelStyle = 'background-color:#f8d7da;';
                            }
                            tableHtml += '<td style="text-align:center;' + levelStyle + '">' + levelText + '</td>';
                            
                            // 评级时间列
                            // tableHtml += '<td style="text-align:center;">' + (customer.levelTime || '') + '</td>';
                            var levelTimeText = '';
                            if (customer.levelTime) {
                                levelTimeText = '<span style="white-space: nowrap;">' + customer.levelTime.split(' ')[0]; // 去掉时分秒
                                if (customer.ratingStartTime && customer.ratingEndTime) {
                                    var startDate = customer.ratingStartTime.split(' ')[0];
                                    var endDate = customer.ratingEndTime.split(' ')[0];
                                    levelTimeText += ' <span class="help-icon rating-period-icon" data-toggle="popover" data-placement="top" data-trigger="hover" data-content="评级数据源取' + startDate + '到' + endDate + '">!</span>';
                                }
                                levelTimeText += '</span>';
                            }
                            tableHtml += '<td style="text-align:center;">' + levelTimeText + '</td>';

                            // 月均营收列
                            var revenueText = '';
                            if (customer.avgMonthRevenue != null && !isNaN(customer.avgMonthRevenue)) {
                                revenueText = (customer.avgMonthRevenue / 10000).toFixed(1) + ' 万元';
                            }
                            tableHtml += '<td style="text-align:center;">' + revenueText + '</td>';
                            
                            // 体量列
                            var volumeStyle = '';
                            if (customer.volumeLevel == 1) {
                                volumeStyle = 'background-color:#fff3b0;';
                            }
                            tableHtml += '<td style="text-align:center;' + volumeStyle + '">' + (customer.volumeLevel == 1 ? "大" : "小") + '</td>';
                            
                            // 利润率列
                            // var profitRateText = '';
                            // if (customer.avgProfitRate != null) {
                            //     profitRateText = (customer.avgProfitRate * 100).toFixed(2) + "%";
                            // }
                            // tableHtml += '<td style="text-align:center;">' + profitRateText + '</td>';
                            
                            // 利润列
                            var profitStyle = '';
                            if (customer.profitLevel == 1) {
                                profitStyle = 'background-color:#fff3b0;';
                            }
                            tableHtml += '<td style="text-align:center;' + profitStyle + '">' + (customer.profitLevel == 1 ? "高" : "低") + '</td>';
                            
                            // 付款情况列
                            var paymentStatusStyle = '';
                            if (customer.paymentStatus == 1) {
                                paymentStatusStyle = 'background-color:#fff3b0;';
                            }
                            tableHtml += '<td style="text-align:center;' + paymentStatusStyle + '">' + (customer.paymentStatus == 1 ? "及时" : "拖拉") + '</td>';
                            
                            // 付款方式列
                            var paymentMethodStyle = '';
                            if (customer.paymentMethod == 1) {
                                paymentMethodStyle = 'background-color:#fff3b0;';
                            }
                            tableHtml += '<td style="text-align:center;' + paymentMethodStyle + '">' + (customer.paymentMethod == 1 ? "现金" : "承兑") + '</td>';
                            
                            tableHtml += '</tr>';
                        });
                    }
                    
                    tableHtml += '</tbody>';
                    tableHtml += '</table>';
                    
                    // 将表格添加到容器中
                    $detailTable.html(tableHtml);

                    initializePopovers();

                    // 调整子表格的列宽与主表格一致
                    adjustDetailTableColumnWidths(index);
                } else {
                    $loadingMsg.html('加载失败：' + (result.msg || '系统错误'));
                }
            },
            error: function() {
                $loadingMsg.html('加载失败：系统错误');
            }
        });
    }
    
    // 格式化客户等级显示
    function formatCustLevel(value) {
        if (value == null || value === "") return "";

        // 如果是数字，转为字符串
        value = String(value);
        
        // 拆分字符串并映射为中文等级
        var levels = value.split(',')
            .map(function(code) {
                let levelText = '';
                let label = $.table.selectDictLabel(customerLevel, code);
                label = label.replace(/<[^>]*>/g, '').trim();
                switch (label) {
                    case "S":
                        levelText = "S级-VIP客户";
                        break;
                    case "A":
                        levelText = "A级-优质客户";
                        break;
                    case "B":
                        levelText = "B级-常规客户";
                        break;
                    case "C":
                        levelText = "C级-需谨慎客户";
                        break;
                    case "D":
                        levelText = "D级-淘汰客户";
                        break;
                    default:
                        levelText = label;
                        break;
                }
                return levelText;
            })
            .filter(Boolean);

        return levels.join('，');
    }
    
    // 调整子表格的列宽与主表格一致
    function adjustDetailTableColumnWidths(index) {
        // 延迟执行以确保 DOM 已完全渲染
        // setTimeout(function() {
            var $mainTable = $('#bootstrap-table');
            var $detailTable = $mainTable.find('tr[data-index="' + index + '"]').next().find('.detail-table');
            
            // 获取主表格的所有列（排除复选框列）
            var $mainColumns = $mainTable.find('thead th').filter(function() {
                return !$(this).find('input[type="checkbox"]').length;
            });
            
            // 获取子表格的所有列（跳过序号列）
            var $detailColumns = $detailTable.find('thead th');
            var $detailDataColumns = $detailTable.find('tbody tr:first-child td');
            
            // 确保有足够的列进行匹配
            var mainColCount = $mainColumns.length;
            var detailColCount = $detailColumns.length;
            
            // 从序号列之后开始匹配（索引1开始）
            for (var i = 1; i < detailColCount; i++) {

                // 确保索引有效
                if (i < mainColCount) {
                    var width = $($mainColumns[i]).outerWidth();
                    $($detailColumns[i]).css('width', width + 'px');
                    
                    // 同时设置数据行的单元格宽度
                    if ($detailDataColumns.length > i) {
                        $($detailDataColumns[i]).css('width', width + 'px');
                    }
                }
            }
        // }, 200); // 延迟200毫秒执行
    }
</script>

</body>
</html>
