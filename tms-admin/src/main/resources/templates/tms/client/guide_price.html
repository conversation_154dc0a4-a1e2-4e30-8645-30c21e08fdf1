<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价列表')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
</head>
<style>
</style>
<body class="gray-bg">
<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="park-form">
                    <input type="hidden" th:value="${clientId}" name="customerId"></input>
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <div class="col-md-4 col-sm-4">
                                    <select id="startProvinceId" name="startProvinceId"
                                            class="form-control valid"
                                            aria-invalid="false"></select>
                                    </select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="startCityId" name="startCityId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="startAreaId" name="startAreaId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-1" onclick="changeDiv()">
                            <img th:src="@{/img/change.png}" style="width: 40px;height: 40px;display: block;margin: 0 auto">
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <div class="col-md-4 col-sm-4">
                                    <select id="endProvinceId" name="endProvinceId"
                                            class="form-control valid"
                                            aria-invalid="false"></select>
                                    </select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="endCityId" name="endCityId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="endAreaId" name="endAreaId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                        class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                        class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>
<!--                    <div class="row">-->
<!--                        <div class="col-md-2 col-sm-4">-->
<!--                            <div class="form-group">-->
<!--                                &lt;!&ndash;                            <label class="col-sm-4">发货单编号：</label>&ndash;&gt;-->
<!--                                <div class="col-sm-12">-->
<!--                                    <input type="text" class="form-control" placeholder="请输入消息接收人名称"  name="receiveUser.userName">-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->

<!--                        <div class="col-md-2 col-sm-4">-->
<!--                            <div class="form-group">-->
<!--                                &lt;!&ndash;                            <label class="col-sm-4">发货单状态：</label>&ndash;&gt;-->
<!--                                <div class="col-sm-12">-->
<!--                                    <select name="wxType" id="wxType" class="form-control valid" aria-invalid="false"  aria-required="true" required>-->
<!--                                        <option value="">&#45;&#45; 请选择消息类型 &#45;&#45;</option>-->
<!--                                        <option value="1">公众号</option>-->
<!--                                        <option value="2">小程序</option>-->
<!--                                    </select>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="col-md-4 col-sm-8">-->
<!--                            <div class="form-group">-->
<!--                                &lt;!&ndash;                            <label class="col-sm-5">要求提货日期：</label>&ndash;&gt;-->
<!--                                <div class="col-sm-12">-->
<!--                                    <div class="input-group">-->
<!--                                        <span class="input-group-addon">从</span>-->
<!--                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 消息开始时间" style="font-size: 14px" id="starttime" name="startTime" >-->
<!--                                        <span class="input-group-addon">到</span>-->
<!--                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 消息结束时间" style="font-size: 14px" id="endtime" name="endTime">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="col-sm-2">-->
<!--                            <label class="col-sm-4"></label>-->
<!--                            <div class="form-group">-->
<!--                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>-->
<!--                                <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="tms:park_data:waybill:remove">-->
<!--                    <i class="fa fa-remove"></i> 删除-->
<!--                </a>-->
<!--                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="tms:park_data:waybill:import">-->
<!--                    <i class="fa fa-upload"></i> 导入-->
<!--                </a>-->
<!--                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:park_data:waybill:export">-->
<!--                    <i class="fa fa-download"></i> 导出-->
<!--                </a>-->
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: ztree-js" />
<script th:inline="javascript">
    var removeFlag = [[${@permission.hasPermi('tms:park_data:waybill:remove')}]];
    var carLenDatas = [[${@dict.getType('car_len')}]];
    var carTypeDatas = [[${@dict.getType('car_type')}]];


    var prefix = ctx + "client";

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#starttime").removeAttr("lay-key");
        $("#endtime").removeAttr("lay-key");
        laydate.render({
            elem: '#endtime'
            , type: 'date'
        });

        laydate.render({
            elem: '#starttime'
            , type: 'date'
        });
    })

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 185 });

        queryParkWayBillList();
        $.table.hideColumn("id");
    });

    $.provinces.init("startProvinceId", "startCityId", "startAreaId");
    $.provinces.init("endProvinceId", "endCityId", "endAreaId");

    //初始化查询条件传参
    // queryParams = function(params) {
    //     var search = {};
    //     $.each($("#park-form").serializeArray(), function(i, field) {
    //         search[field.name] = field.value;
    //     });
    //     search.pageSize = params.limit;
    //     search.pageNum = params.offset / params.limit + 1;
    //     search.searchValue = params.search;
    //     search.orderByColumn = params.sort;
    //     search.isAsc = params.order;
    //     console.log(search)
    //     return search;
    // }

    function queryParkWayBillList() {
        var options = {
            url: prefix + "/guidePriceList",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            //sortName: "regDate",
            //sortOrder: "desc",
            modalName: "指导价",
            fixedColumns: true,
            showFooter:true,
            fixedNumber:5,
            showExport: true,
            clickToSelect: true,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"指导价列表"
            },
            //queryParams: queryParams,
            columns: [
                // {
                //     checkbox:true,
                // },
                {
                    title: '主键',
                    align: 'left',
                    field : 'id'  //主键加载完成就隐藏  多选删除的时候是按照第一列的值是否相同来判断一共有几项的
                },
                // {
                //     title: '操作',
                //     align: 'center',
                //     formatter: function(value, row, index) {
                //         var actions = [];
                //         actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a> ');
                //         return actions.join('');
                //     }
                // },
                {
                    title: '线路名称',
                    align: 'left',
                    field : 'lineName',
                    formatter: function(value, row, index) {
                        var arr = value.split('-')
                        return arr[0] +'<i class="fa fa-arrow-circle-right" style="font-size: 16px;color: #1ab394"></i>' + arr[1]
                    }
                },
                {
                    title: '回单预警时间',
                    align: 'left',
                    field : '',
                    formatter: function(value, row, index) {
                        return 'D+2';
                    }
                },
                {
                    title: '车长',
                    align: 'left',
                    field : 'carLen',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(carLenDatas, value);
                    }
                },
                {
                    title: '车型',
                    align: 'left',
                    field : 'carType',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(carTypeDatas, value);
                    }
                },
                {
                    title: '线路价格',
                    align: 'left',
                    field : 'guidingPrice'
                },
                {
                    title: '开始时间',
                    align: 'left',
                    field : 'startDate'
                },
                {
                    title: '结束时间',
                    align: 'left',
                    field : 'endDate'
                }
            ]
        };
        $.table.init(options);
        function changeDiv(){
            var startProvinceId= $('#startProvinceId').val()
            var startCityId= $('#startCityId').val()
            var startAreaId= $('#startAreaId').val()
            var endProvinceId= $('#endProvinceId').val()
            var endCityId= $('#endCityId').val()
            var endAreaId= $('#endAreaId').val()
            $.provinces.init("startProvinceId", "startCityId", "startAreaId", endProvinceId, endCityId, endAreaId);
            $.provinces.init("endProvinceId", "endCityId", "endAreaId", startProvinceId, startCityId, startAreaId);
            //searchPre()
            $.table.search()
        }
    }
</script>
</body>
</html>