<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户收发货地址列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">地址名称：</label>
                            <div class="col-sm-8">
                                <input name="addrName" id="addrName"
                                       class="form-control valid" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">地址编码：</label>
                            <div class="col-sm-8">
                                <input name="addrCode"  id="addrCode"
                                        class="form-control valid" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">联系人：</label>
                            <div class="col-sm-8">
                                <input name="contact"  id="contact"
                                       class="form-control valid" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">联系人电话：</label>
                            <div class="col-sm-7">
                                <div class="input-group">
                                    <input name="mobile" id="mobile" class="form-control" type="text"
                                            autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">地址类别 ：</label>
                                <div class="col-sm-8">
                                    <select id="addrType" name="addrType" class="form-control valid"
                                            aria-invalid="false">
                                        <option value="">请选择</option>
                                        <option th:each="item : ${addressTypeList}" th:text="${item.context}"
                                                th:value="${item.value}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                        </div>
                        <div class="col-sm-2">
                        </div>
                        <div class="col-sm-2">
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
            </form>
        </div>
        
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:custAddress:edit')}]];
    var removeFlag = [[${@permission.hasPermi('tms:custAddress:remove')}]];
    var addressTypeList = [[${addressTypeList}]];

    var prefix = ctx + "custAddress";
    var customerId = [[${custAddress.customerId}]];
    $(function () {
        var options = {
            url: prefix + "/list?customerId=" + customerId,
            modalName: "客户收发货地址",
            showToggle:false,
            showColumns:true,
            showExport:true,
            columns: [
                {field: 'customerName', title: '客户名称'},
                {
                    field: 'isDefault', title: '地址名称',
                    formatter: function (value, row, index) {
                        var context = '';
                        if (value === '1') {
                            context = '<span class="label label-primary"> 默认 </span>';
                        }
                        return context + row.addrName;
                    }
                },
                {field: 'addrCode', title: '地址编码'},
                {field: 'addressWholeName', title: '详细地址'},
                {
                    field: 'addrType', title: '地址类型',
                    formatter: function status(row, value) {
                        var context = '';
                        addressTypeList.forEach(function (v) {
                            if (v.value === value.addrType) {
                                context = '<span class="label label-primary">' + v.context + '</span>';
                                return false;
                            }
                        });
                        return context;
                    }
                },
                {field: 'contact', title: '联系人'},
                {field: 'contactPost', title: '联系人职位'},
                {field: 'mobile', title: '联系人手机'},
                {
                    //visible: row.addrType == '0' || row.addrType == '1' ? true : false,
                    field: "isDefault",
                    title: '是否默认',
                    align: 'center',
                    formatter: function (value, row, index) {
                        if(row.addrType == '0' || row.addrType == '1') {
                            console.log("11")
                            return statusTools(row);
                        }
                    }
                }
            ]
        };
        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });

    /* 地址状态显示 */
    function statusTools(row) {
        if (row.isDefault == '1') {  //0:否  1:是
            return '<i class=\"fa fa-toggle-on text-info fa-2x\" onclick="abandonDefault(\'' + row.custAddressId + '\', \'' + row.customerId + '\')"></i>';
        } else {
            //设为默认
            return '<i class=\"fa fa-toggle-off text-info fa-2x\" onclick="enableDefault(\'' + row.custAddressId + '\', \'' + row.customerId + '\')"></i> ';
        }
    }

    /* 地址管理-取消默认 */
    function abandonDefault(custAddressId,customerId) {
        $.modal.confirm("确认要取消该客户的默认地址吗?取消后该客户将没有默认地址!", function() {
            $.operate.post(prefix + "/setDefault", { "custAddressId": custAddressId, "customerId": customerId, "isDefault": '0' });
        })
    }

    /* 地址管理-设为默认 */
    function enableDefault(custAddressId,customerId) {
        $.modal.confirm("确认要将改地址设为默认地址吗？", function() {
            $.operate.post(prefix + "/setDefault", { "custAddressId": custAddressId, "customerId": customerId, "isDefault": 1 });
        })
    }
</script>
</body>
</html>