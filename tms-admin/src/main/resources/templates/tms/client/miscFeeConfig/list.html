<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/css/bootstrap-editable.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.css}" rel="stylesheet"/>

</head>
<style>
    /* 表格滚动条 */
    .bootstrap-table {
        overflow-x: auto;
        clear: both;
        margin-top: 10px;
    }
    .bootstrap-table .fixed-table-container,
    .bootstrap-table .fixed-table-body {
        overflow: visible !important; /* 强制覆盖内部滚动 */
    }

    /* 表头样式 */
    .bootstrap-table .fixed-table-container .table thead th {
        position: relative;
        text-shadow: none !important;
        font-weight: 500;
        color: #333;
        background-color: #f5f7fa;
    }

    .bootstrap-table .fixed-table-container .table thead th .th-inner {
        padding: 8px;
        line-height: 1.5;
        vertical-align: middle;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
    }

    /* 防止表头内容重叠 */
    .bootstrap-table .fixed-table-container .table thead th .sortable {
        background-image: none;
        position: relative;
    }

    /* 确保表头只有一层文字 */
    .bootstrap-table .fixed-table-container .table thead th:before,
    .bootstrap-table .fixed-table-container .table thead th:after {
        content: none !important;
    }

    /* 工具栏样式 */
    #toolbar {
        margin-bottom: 10px;
        display: inline-block;
    }

    #buttons-toolbar-container {
        clear: both;
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }

    /* 表格页脚样式 */
    .bootstrap-table .table tfoot {
        font-weight: bold;
        background-color: #f5f7fa;
    }

    .bootstrap-table .table tfoot td {
        padding: 8px;
        border-top: 2px solid #ddd;
    }

    /* 固定表头相关 */
    .sticky-header-container {
        pointer-events: none;
    }

    .select-table {
        clear: both;
    }
</style>
<body class="gray-bg">
<div class="col-sm-12 search-collapse">
    <form id="role-form" class="form-horizontal" onkeydown="if(event.keyCode==13){searchPre();return false;}">
        <input type="hidden" id="customerId" name="customerId" th:value="${customerId}">
        <div class="row no-gutter">
            <div class="col-md-2 col-sm-2">
<!--                <div class="form-group flex">-->
<!--                    <div class="col-sm-12">-->
<!--                        <input name="groupName" id="groupName" placeholder="集团名称" class="form-control" type="text" maxlength="20" autocomplete="off">                   -->
<!--                    </div>-->
<!--                </div>-->
            </div>

            <div class="col-md-3 col-sm-3">
                <div class="form-group" style="text-align: left;">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>

                </div>
            </div>
        </div>
    </form>
</div>

<div class="col-sm-12 select-table  ">
    <div class="btn-group-sm" id="toolbar" role="group">
<!--        <a class="btn btn-primary" shiro:hasAnyPermissions="tms:miscFeeConfig:list" onclick="importAllData()">-->
<!--            <i class="fa fa-upload"></i> 批量导入-->
<!--        </a>-->
        <a class="btn btn-primary btn-rounded btn-sm" onclick="addMiscFeeConfig()" shiro:hasPermission="tms:miscFeeConfig:list">
            <i class="fa fa-plus"></i>&nbsp;新增
        </a>

        <a class="btn btn-danger multiple disabled" shiro:hasAnyPermissions="tms:miscFeeConfig:list"
           onclick="deleteMiscFeeConfig()">
            <i class="fa fa-remove"></i> 删除
        </a>

    </div>

    <div id="buttons-toolbar-container" style="margin-bottom: 10px;">
        <span id="buttons-toolbar"></span>
    </div>

    <table id="bootstrap-table"
           data-buttons-toolbar="#buttons-toolbar"
           class="table table-striped table-responsive table-bordered table-hover" >
    </table>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/tableExport.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/js/bootstrap-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/editable/bootstrap-table-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/bootstrap-table-export.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>


<script th:inline="javascript">
    var cost_type_on_way = [[${@dict.getType('cost_type_on_way')}]];
    var customerId = [[${customerId}]]
    //开票类型
    var billingType = [[${@dict.getType('billing_type')}]];

    $(function () {
        let options = initOptions();
        $.table.init(options);
    });

    function initOptions() {
        return {
            url: ctx + "miscFeeConfig/list",
            uniqueId: "id",
            showToggle:false,
            showColumns:false,
            showSearch:false,
            // pagination:false,
            // showRefresh:false,
            modalName: "客户分级",
            height: 560,
            clickToSelect: true,
            // showFooter: true,  // 启用表格页脚，用于显示汇总
            stickyHeader: true,  // 启用固定表头功能
            stickyHeaderOffsetY: 0,  // 可选，调整固定表头距离顶部的距离
            // detailView: true,  // 启用详情视图
            // detailFormatter: detailFormatter, // 设置详情格式化函数
            columns: [
                [
                                  {
                                      checkbox: true,
                                      align: 'center',
                                      valign: 'middle',
                                  },
                                  {
                                      title: '操作',
                                      align: 'center',
                                      valign: 'middle',
                                      switchable:false,
                                      formatter: function(value, row, index) {
                                          // var actions = [];
                                          // if ([[${@permission.hasPermi('tms:insurancePolicy:list')}]] != "hidden") {
                                          //     actions.push(`<a class="btn btn-xs" href="javascript:void(0)" onclick="edit('${row.id}')" title="修改">
                                          //                         <i class="fa fa-edit" style="font-size: 15px;"></i></a>`);
                                          // }
                                          // return actions.join('');
                                      }
                                  },
                    {
                        title: '到货地址名称',
                        // valign: 'middle',
                        // align: 'center',
                        field : 'arriAddrName',

                    },
                    {
                        title: '费用类型',
                        // align: 'center',
                        // valign: 'middle',
                        field: 'costType',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabel(cost_type_on_way, row.costType);
                        }
                    },
                    {
                        title: '计价方式',
                        field: 'billingMethod',
                        formatter: function(value, row, index) {
                            let html = ''
                            if (value === '1') {
                                html = '重量（元/吨）'
                            }else if (value === '2') {
                                html = '体积（元/立方米）'
                            }else if (value === '5') {
                                html = '件（元/件）'
                            }
                            return html;
                        }
                    },

                    {
                        title: '应收金额',
                        field: 'miscYsAmount',
                        formatter: function(value, row, index) {
                            if (value === null) {
                                return ;
                            }
                            let amountHtml = value.toLocaleString('zh', { style: 'currency', currency: 'CNY' });
                            let billingTypeHtml = $.table.selectDictLabel(billingType, row.ysBillingType);

                            return amountHtml + '<br>' + billingTypeHtml;
                        },
                    },
                    {
                        title: '应付金额',
                        // valign: 'middle',
                        // align: 'center',
                        field : 'miscYfAmount',
                        formatter: function(value, row, index) {
                            if (value === null) {
                                return ;
                            }

                            let typeHtml = '';
                            if (row.miscType === 0) {
                                typeHtml = '应付在途';
                            }else if (row.miscType === 1) {
                                typeHtml = '三方费用';
                            }
                            let amountHtml = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});

                            let billingTypeHtml = $.table.selectDictLabel(billingType, row.yfBillingType);

                            return typeHtml + ' ' + amountHtml + '<br>' + billingTypeHtml;
                        },
                    },
                ]
            ],
        };
    }


    function addMiscFeeConfig() {
        layer.open({
            type: 2,
            area: ['70%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "新增配置",
            content: ctx + "miscFeeConfig/add?customerId=" + customerId,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWindow = window[layero.find('iframe')[0]['name']];
                iframeWindow.submitHandler(function(success) {
                    if (success) {
                        layer.close(index);
                    }
                });
            },
            cancel: function (index) {
                return true;
            }
        });
    }

    function searchPre() {
        $.table.search('role-form');
        $('#bootstrap-table').on('load-success.bs.table', function() {
            initializePopovers();
        });
    }
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

    function deleteMiscFeeConfig() {
        var idArr = $.table.selectColumns("id");
        if (idArr.length == 0) {
            $.modal.msgWarning("请至少选择一条数据");
            return;
        }

        $.modal.confirm("确认要删除选中的" + idArr.length + "条数据吗?", function() {
            var url = ctx + "miscFeeConfig/remove";
            var data = { "ids": idArr.join(",") };
            $.operate.submit(url, "post", "json", data);
        });
    }

</script>

</body>
</html>