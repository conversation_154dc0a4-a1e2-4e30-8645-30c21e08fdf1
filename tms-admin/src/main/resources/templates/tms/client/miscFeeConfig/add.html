<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('新增其他费配置')" />
</head>
<style>
    /* 表单样式优化 */
    .form-horizontal .form-group {
        margin-bottom: 20px;
        position: relative;
        display: flex;
        align-items: flex-start;
    }

    .form-horizontal .control-label {
        font-weight: 500;
        color: #333;
        line-height: 34px;
        margin-bottom: 0;
        text-align: right;
        width: 120px;
        padding-right: 15px;
        flex-shrink: 0;
    }

    .form-control-wrapper {
        flex: 1;
        min-width: 0;
    }

    .form-control {
        height: 34px;
        line-height: 1.42857143;
        font-size: 13px;
        width: 100%;
        padding: 6px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
    }

    .form-control:focus {
        border-color: #1ab394;
        box-shadow: 0 0 0 0.2rem rgba(26, 179, 148, 0.25);
        outline: none;
    }

    /* 输入组样式 */
    .input-group {
        display: flex;
        width: 95%;
        position: relative;
    }

    .input-group .form-control {
        flex: 1;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-right: 0;
    }

    .input-group-addon {
        background-color: #f8f8f9;
        border: 1px solid #ddd;
        border-left: 0;
        border-radius: 0 4px 4px 0;
        color: #676a6c;
        padding: 6px 12px;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.42857143;
        text-align: center;
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
    }

    .input-group-btn {
        display: flex;
        align-items: stretch;
    }

    .input-group-btn .btn {
        border-radius: 0 4px 4px 0;
        border-left: 0;
        height: 34px;
        padding: 6px 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
        vertical-align: top;
    }

    .btn-primary {
        background-color: #1ab394;
        border-color: #1ab394;
        color: white;
    }

    .btn-primary:hover {
        background-color: #18a689;
        border-color: #18a689;
    }

    /* 下拉选择框样式 */
    select.form-control {
        height: 34px;
        line-height: 1.42857143;
        cursor: pointer;
    }

    /* 数字输入框样式 */
    input[type="number"].form-control {
        height: 34px;
    }

    /* 金额区域样式优化 */
    .amount-section {
        display: flex;
        gap: 20px;
        width: 100%;
    }

    .amount-box {
        flex: 1;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        background-color: #fafafa;
    }

    .amount-box-title {
        font-weight: 500;
        color: #333;
        margin-bottom: 12px;
        font-size: 14px;
    }

    .amount-box .form-group {
        margin-bottom: 12px;
        display: block;
    }

    .amount-box .form-group:last-child {
        margin-bottom: 0;
    }

    .amount-box .form-control {
        width: 100%;
    }

    .amount-box .input-group {
        display: flex;
        width: 100%;
    }

    /* 应付金额行样式 */
    .payable-row {
        display: flex;
        gap: 10px;
        align-items: flex-start;
    }

    .payable-row select.form-control {
        flex: 1;
        min-width: 120px;
    }

    .payable-row .input-group {
        flex: 2;
        min-width: 150px;
    }

    /* 表单验证错误样式 */
    .form-group.has-error .form-control {
        border-color: #ed5565;
    }

    .form-group.has-error .control-label {
        color: #ed5565;
    }

    .form-group.has-error .input-group-addon {
        border-color: #ed5565;
        color: #ed5565;
    }

    .form-group.has-error .input-group-btn .btn {
        border-color: #ed5565;
    }

    /* 错误信息样式 */
    label.error {
        color: #ed5565;
        font-size: 12px;
        margin-top: 5px;
        display: block;
        width: 100%;
    }

    /* 为有错误信息的表单组预留空间 */
    .form-group.has-error {
        margin-bottom: 35px;
    }

    /* 必填标识 */
    .is-required::before {
        content: '*';
        color: #ed5565;
        margin-right: 4px;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .amount-section {
            flex-direction: column;
            gap: 15px;
        }

        .form-horizontal .control-label {
            width: 100px;
            padding-right: 10px;
        }
    }
</style>

<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-misc-fee-add" novalidate="novalidate">
        <input type="hidden" id="customerId" name="customerId">

        <div class="form-group">
            <label class="control-label is-required">到货地址：</label>
            <div class="form-control-wrapper">
                <div class="input-group">
                    <input type="text" name="arriAddrName" id="arriAddrName" class="form-control" onclick="selectAddrArri()" readonly placeholder="请选择到货地址" required>
                    <input type="hidden" name="arrivalId" id="arrivalId">
                    <input type="hidden" name="arriDetailAddr" id="arriDetailAddr">
                    <input type="hidden" id="selectAddrArriText">
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-primary" onclick="selectAddrArri()" title="选择地址">
                            <i class="fa fa-search"></i>
                        </button>
                    </span>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="control-label is-required">费用类型：</label>
            <div class="form-control-wrapper">
                <select name="costType" class="form-control" th:with="type=${@dict.getType('cost_type_on_way')}" required>
                    <option value="">请选择费用类型</option>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <label class="control-label is-required">计价方式：</label>
            <div class="form-control-wrapper">
                <select name="billingMethod" class="form-control" required>
                    <option value="1">重量（元/吨）</option>
                    <option value="2">体积（元/立方米）</option>
                    <option value="5">件（元/件）</option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <div class="form-control-wrapper">
                <div class="amount-section">
                    <!-- 应收金额 -->
                    <div class="amount-box">
                        <div class="amount-box-title">应收金额</div>
                        <div class="form-group">
                            <div class="input-group">
                                <input type="number" name="miscYsAmount" class="form-control" placeholder="请输入应收金额" step="0.01" min="0">
                                <span class="input-group-addon">元</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <select name="ysBillingType" class="form-control" th:with="type=${@dict.getType('billing_type')}">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                    <!-- 应付金额 -->
                    <div class="amount-box">
                        <div class="amount-box-title">应付金额</div>
                        <div class="form-group">
                            <div class="payable-row">
                                <select name="miscType" class="form-control" required>
                                    <option value="0">应付在途</option>
                                    <option value="1">三方费用</option>
                                </select>
                                <div class="input-group">
                                    <input type="number" name="miscYfAmount" class="form-control" placeholder="请输入应付金额" step="0.01" min="0">
                                    <span class="input-group-addon">元</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <select name="yfBillingType" class="form-control" th:with="type=${@dict.getType('billing_type')}">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />

<script th:inline="javascript">
    $(function () {
        // 表单验证
        $("#form-misc-fee-add").validate({
            rules: {
                costType: {
                    required: true
                },
                arriAddrName: {
                    required: true
                },
                billingMethod: {
                    required: true
                },
                miscYsAmount: {
                    // required: true,
                    min: 0,
                    number: true
                },
                miscType: {
                    required: true
                },
                miscYfAmount: {
                    // required: true,
                    min: 0,
                    number: true
                }
            },
            messages: {
                costType: {
                    required: "请选择费用类型"
                },
                arriAddrName: {
                    required: "请选择到货地址"
                },
                billingMethod: {
                    required: "请选择计价方式"
                },
                miscYsAmount: {
                    // required: "请输入应收金额",
                    min: "应收金额不能小于0",
                    number: "请输入有效的数字"
                },
                miscType: {
                    required: "请选择类型"
                },
                miscYfAmount: {
                    // required: "请输入应付金额",
                    min: "应付金额不能小于0",
                    number: "请输入有效的数字"
                }
            },
            focusCleanup: true,
            errorPlacement: function(error, element) {
                // 将错误信息放置在form-control-wrapper内部
                var wrapper = element.closest('.form-control-wrapper');
                if (wrapper.length) {
                    wrapper.append(error);
                } else {
                    error.insertAfter(element);
                }
            },
            highlight: function(element) {
                $(element).closest('.form-group').addClass('has-error');
            },
            unhighlight: function(element) {
                $(element).closest('.form-group').removeClass('has-error');
            }
        });
    });

    /**
     * 提交表单
     */
    function submitHandler() {
        if ($.validate.form()) {
            // 再拼接 customerId
            var customerId = $("#customerId").val()
                || window.parent.document.getElementById("customerId")?.value
                || '';
            $("#customerId").val(customerId);

            $.operate.save(ctx + "miscFeeConfig/add", $('#form-misc-fee-add').serialize());
        }
    }

    /**
     * 选择到货地址
     */
    function selectAddrArri() {
        var customerId = $("#customerId").val() || window.parent.document.getElementById("customerId")?.value;

        if (!customerId) {
            $.modal.alertWarning("请先选择客户");
            return;
        }

        parent.layer.open({
            type: 2,
            area: ['85%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "选择到货地址",
            content: ctx + "basic/address/selectAddress?customerId=" + customerId + "&addrType=1",
            btn: ['确定', '关闭'],
            shadeClose: true,
            yes: function (index, layero) {
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }

                let row = rows[0];
                let fullAddress = row.addrName + "（" + row.provinceName + row.cityName + row.areaName + row.detailAddr + "）";

                $("#selectAddrArriText").val(fullAddress);
                $("#arriAddrName").val(row.addrName);
                $("#arrivalId").val(row.addressId);
                $("#arriDetailAddr").val(row.detailAddr);

                // 清除验证错误状态
                $("#arriAddrName").closest('.form-group').removeClass('has-error');
                $("#arriAddrName").closest('.form-control-wrapper').find('label.error').remove();

                parent.layer.close(index);
            },
            cancel: function (index) {
                return true;
            }
        });
    }
</script>
</body>

</html>