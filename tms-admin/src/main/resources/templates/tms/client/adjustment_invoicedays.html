<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('调整申请开票期')"/>
</head>

<body>
<div class="form-content">
    <form id="form-client-add" class="form-horizontal" novalidate="novalidate">
        <!--客户id-->
        <input type="hidden" name="customerId" th:value="${client.customerId}">

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">基本信息</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">

                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span style="">申请开票期：</span></label>
                                <div class="col-sm-8">
                                    <input name="invoiceDays" id="invoiceDays" th:value="${client.invoiceDays}" class="form-control"
                                           type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>


    </form>
</div>



<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "client";

    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-client-add").serializeArray();
            $.operate.save(prefix + "/saveAdjustInvoiceDays", data);
        }
    }
</script>
</body>
</html>