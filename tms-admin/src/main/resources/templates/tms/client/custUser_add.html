<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户账号信息')"/>
</head>
<body>
<div class="form-content">
    <form id="form-custUser-add" class="form-horizontal">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4">姓名：</label>
                    <div class="col-sm-8">
                        <input name="loginName" id="loginName" placeholder="请输入姓名" class="form-control" type="text"
                               required  maxlength="25">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4">手机号：</label>
                    <div class="col-sm-8">
                        <input name="phonenumber" id="phonenumber" placeholder="请输入手机号" class="form-control" type="text"
                               required aria-required="true" maxlength="11">
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
</body>
<script th:inline="javascript">
    var prefix = ctx + "custUser";

    var customerId = [[${custUser.customerId}]];
    /**
     * 校验
     */
    $("#form-custUser-add").validate({
        onkeyup: false,
        rules:{
            phonenumber:{
                isPhone:true,
                remote: {
                    url: ctx + "system/user/checkPhoneUnique",
                    type: "post",
                    dataType: "json",
                    data: {
                        name: function () {

                            return $.common.trim($("#phonenumber").val());
                        },
                        "userType": "1"
                    },
                    dataFilter: function (data, type) {
                        return $.validate.unique(data);
                    }
                }
            },
        },
        messages: {

            "phonenumber":{
                remote: "手机号码已经存在"
            }
        },
        focusCleanup: true
    });



    // 提交回调的方法
    function submitHandler() {
        if ($.validate.form()) {
            $.ajax({
                url: prefix + "/add",
                data: {"userName":$("#loginName").val(),
                    "phone":$("#phonenumber").val(),
                "customerId":customerId},
                type: "post",
                error: function (request) {
                    $.modal.alertError("系统错误");
                },
                success: function (data) {
                    $.operate.successCallback(data);
                }
            })
        }
    }
</script>
</body>

</html>