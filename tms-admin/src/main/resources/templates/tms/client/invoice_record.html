<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('修改客户信息')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style type="text/css">
    .td td {
        position: relative
    }
    .bg_title{
        font-size: 15px;
        font-weight: bold;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 113px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;
    }
    .fcff{
        color: #ff1f1f;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .tc{
        text-align: center;
    }
    .bor{
        border: 1px #eee solid;
    }
    .over{
        overflow: hidden;

    }
    .fl{
        float: left;
    }
    .ml10{
        margin-left: 10px;
    }
    .table>thead>tr>th{
        background: #eff3f9 !important;
        padding: 0;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }

    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .theme-explorer{
        width: 100%;
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }

    .tdStyle {
       border-left: 1px solid #e7eaec;
        border-right: 1px solid #e7eaec;

    }

</style>
<body>
<div class="form-content">
    <form id="form-client-edit" class="form-horizontal" novalidate="novalidate">

        <input name="salesDept" type="hidden" id="salesDept" th:value="${salesDept}"/>
        <input name="salesId" type="hidden" id="salesId" th:value="${salesId}"/>
        <input name="customerId" type="hidden" id="customerId" th:value="${customerId}"/>
        <input name="mgmtDeptId" type="hidden" id="mgmtDeptId" th:value="${mgmtDeptId}"/>



            <div class="row" id="collapseTwo">
                <div class="panel-body" >
                    <div class="col-sm-12 select-table table-striped">
                        <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
                    </div>
                </div>
            </div>


    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">



    $(function () {


        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');
        $('#collapseSeven').collapse('show');


        var options = {
            url: ctx + "client/clientNotReceiveInvoiceList",
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 0,
            clickToSelect:true,
            showFooter:false,
            columns: [{
                checkbox: true
            },
                {
                    title: '发货单号',
                    field: 'invoiceVbillno',
                    align: 'left',

                },
                {
                    title: '要求提货日期',
                    field: 'reqDeliDate',
                    align: 'left',

                },
                {
                    title: '运营组',
                    field: 'salesDeptName',
                    align: 'left',

                },
                {
                    title: '运营部',
                    field: 'salesName',
                    align: 'left',

                },
                {
                    title: '管理部',
                    field: 'mgmtDeptName',
                    align: 'left',

                },

                {
                    title: '未收款金额',
                    field: 'ungotAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                }


            ]
        };

        $.table.init(options);



    });


</script>
</body>

</html>