<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('货品信息-add')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-goods-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" id="customerId" name="customerId" th:value="${customerId}">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span
                                            style="color: red; ">货品名称：</span></label>
                                    <div class="col-sm-8">
                                        <!--<input name="goodsName" id="goodsName" class="form-control" required type="text"
                                               maxlength="25">-->
                                        <div class="input-group">
                                            <input name="goodsName" id="goodsName" required class="form-control valid"
                                                   type="text" aria-required="true" maxlength="25">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="form-group">
                                    <label class="col-sm-4">
                                        <span style="color: red; ">货品类型：</span></label>
                                    <div class="col-sm-8">
                                        <div class="input-group">
                                            <input  class="form-control valid" onclick="selectGoodTypeTree()" required
                                                    id="treeName"  name="goodsTypeName" readonly maxlength="100"  type="text">
                                            <input type="hidden" name="goodsType" id="treeId">
                                            <span class="input-group-addon"><i class="fa fa-search" ></i></span>
                                        </div>

                                    </div>
                                </div>
                            </div>
                           
                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">货品特性：</label>
                                    <div class="col-sm-8">
                                        <select name="goodsCharacter" id="goodsCharacter" class="form-control valid" th:with="type=${@dict.getType('goods_character')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">货品分类：</label>
                                    <div class="col-sm-8">
                                        <select name="goodsClass" id="goodsClass" class="form-control" th:with="type=${@dict.getType('good_type')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}"  th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="form-group">
                                    <label class="col-sm-4">英文名称：</label>
                                    <div class="col-sm-8">
                                        <input name="goodsNameEn" id="goodsNameEn" class="form-control" type="text"
                                               maxlength="25"  aria-required="true" th:align="right">

                                    </div>
                                </div>
                            </div>



                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="form-group">
                                    <label class="col-sm-4">成本价：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group">
                                            <span class="input-group-addon">¥</span>
                                            <input type="text" oninput="$.numberUtil.onlyNumber(this);" min="0" name="costPrice" id="costPrice" maxlength="12" class="form-control"  style="text-align:right;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="form-group">
                                    <label class="col-sm-4">销售价：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group">
                                            <span class="input-group-addon">¥</span>
                                            <input type="text" oninput="$.numberUtil.onlyNumber(this);" min="0" name="sellingPrice" maxlength="12" id="sellingPrice" class="form-control"  style="text-align:right;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="form-group">
                                    <label class="col-sm-4">最小包装：</label>
                                    <div class="col-sm-8">
                                        <select name="minPack" id="minPack" class="form-control" th:with="type=${@dict.getType('package_type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}"  th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="form-group">
                                    <label class="col-sm-4">运输要求：</label>
                                    <div class="col-sm-8">
                                        <input type="text" name="transNote" id="transNote" maxlength="100" class="form-control">
                                    </div>
                                </div>
                            </div>

                            <!--<div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">最低温度：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group">
                                            <input type="text" oninput="$.numberUtil.onlyNumber(this);" name="lowTemp" id="lowTemp"  maxlength="12"  class="form-control">
                                            <span class="input-group-addon">℃</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">最高温度：</label>
                                    <div class="col-sm-8">
                                        <div class="input-group">
                                            <input type="text" oninput="$.numberUtil.onlyNumber(this);" name="hightTemp" id="hightTemp" maxlength="12"  class="form-control">
                                            <span class="input-group-addon">℃</span>
                                        </div>
                                    </div>
                                </div>
                            </div>-->
                            
                            <div class="col-md-3 col-sm-6 col-xs-6">
                                <div class="form-group">
                                    <label class="col-sm-4">操作要求：</label>
                                    <div class="col-sm-8">
                                        <input name="operNote" id="operNote" class="form-control" maxlength="100" type="text">
                                    </div>
                                </div>
                            </div>
                        
                            <!--<div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">是否失效：</label>
                                    <div class="col-sm-8">
                                        <select name="lockedFlag" class="form-control" th:with="type=${@dict.getType('locked_flag')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>-->
                        </div>
                        <div class="row ">
                            <div class="col-sm-12 col-md-3 ">
                                <div class="form-group">
                                    <label class="col-sm-2">货品图片：</label>
                                    <div class="col-sm-10">
                                        <input name="goodsAppendix" id="goodsAppendix" class="form-control" type="file" multiple>
                                        <input type="hidden" id="goodsAppendixId" name="goodsAppendixId">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-6">
                                            <textarea name="memo" id="memo" maxlength="100" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!--<div class="row">-->
<!--    <div class="col-sm-offset-5 col-sm-10">-->
<!--        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保-->
<!--            存-->
<!--        </button>&nbsp;-->
<!--        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭-->
<!--        </button>-->
<!--    </div>-->
<!--</div>-->

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script type="text/javascript">
    var prefix = ctx + "basic/goods";

    $(function () {
        $('#collapseOne').collapse('show');

        //图片功能
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("goodsAppendix", "goodsAppendixId", picParam);

        //校验
        $("#form-goods-add").validate({
            onkeyup: false,
            rules:{
                // goodsName:{
                //     remote: {
                //         url: prefix + "/checkGoodsNameUnique",
                //         type: "post",
                //         dataType: "json",
                //         data: {
                //             "goodsName" : function() {
                //                 return $.common.trim($("#goodsName").val());
                //             }
                //         },
                //         dataFilter: function(data, type) {
                //             return $.validate.unique(data);
                //         }
                //     }
                // },
                orderNum:{
                    digits:true
                },

            },
            messages: {
                // "goodsName": {
                //     remote: "货品名称已经存在"
                // }
            },
            focusCleanup: true
        });

        /**
         * 货品名称联想
         */
        $("#goodsName").bsSuggest('init', {
            url: ctx + "basic/goods/queryNames?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "goodsName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["goodsName"],
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
            $("#treeName").val(data.goodsTypeName)
            $("#treeId").val(data.goodsType)
            $("#goodsCharacter").val(data.goodsCharacter)
            $("#goodsClass").val(data.goodsClass)
            $("#goodsNameEn").val(data.goodsNameEn)
            $("#costPrice").val(data.costPrice)
            $("#sellingPrice").val(data.sellingPrice)
            $("#minPack").val(data.minPack)
            $("#transNote").val(data.transNote)
            $("#operNote").val(data.operNote)
            $("#memo").val(data.memo)
            //$("#accountName").val(data.accountName);
            //$("#inAccount").val(data.accountId);
            //alert(JSON.stringify(data))
        });
    });

    /*货品信息-新增-选择父部门树*/
    function selectGoodTypeTree() {
        var treeId = $("#treeId").val();
        var goodsTypeId = $.common.isEmpty(treeId) ? "100" : $("#treeId").val();
        var url = ctx + "basic/goodsType/selectGoodsTypeTree/" + goodsTypeId;
        var options = {
            title: '选择货品类型',
            width: "380",
            url: url,
            callBack: doSubmit
        };
        $.modal.openOptions(options);
    }

    function doSubmit(index, layero){
        var tree = layero.find("iframe")[0].contentWindow.$._tree;
        if ($.tree.notAllowParents(tree)) {
            var body = layer.getChildFrame('body', index);
            $("#treeId").val(body.find('#treeId').val());
            $("#treeName").val(body.find('#treeName').val());
            layer.close(index);
            $.validate.form()
        }
    }

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $('#goodsAppendix').fileinput('upload');
            jQuery.subscribe("cmt", commit);
        }
    }

    function commit() {
        $.operate.saveModalAndRefush(ctx + "client/goods/add", $('#form-goods-add').serialize());
    }

</script>
</body>

</html>