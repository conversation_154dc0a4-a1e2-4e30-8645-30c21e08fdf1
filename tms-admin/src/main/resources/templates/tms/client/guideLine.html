<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('线路选择页')"/>
</head>
<body class="gray-bg">

<div class="ui-layout-center">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" >
                    <div class="select-list">
                        <ul>
                            <li>
                                线路名称：<input type="text" name="lineName" />
                                <input id="hiddenText" type="text" style="display:none" />
                                <input id="guideLineIndex" th:value="${guideLineIndex}" name="guideLineIndex" type="hidden">
                            </li>

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>

            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>

</div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">

    var prefix = ctx + "basic/line";


    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
        var options = {
            url: prefix + "/transLineList",
            clickToSelect:true,
            columns: [{
                radio: true
            },

                {
                    title: '线路ID',
                    field: 'transLineId',
                    visible: false
                },
                {
                    title: '线路名称',
                    align: 'left',
                    field: 'lineName'
                },
                {
                    title: '起始地',
                    field: 'startDetailAddr',
                    align: 'left',
                    formatter: function status(row,value) {
                        if(value.startProvinceName == null){
                            value.startProvinceName = "";
                        }
                        if(value.startCityName == null){
                            value.startCityName = "";
                        }

                        return value.startProvinceName + value.startCityName;
                    }
                },
                {
                    title: '目的地',
                    field: 'endDetailAddr',
                    align: 'left',
                    formatter: function status(row,value) {
                        if(value.endProvinceName == null){
                            value.endProvinceName = "";
                        }
                        if(value.endCityName == null){
                            value.endCityName = "";
                        }

                        return value.endProvinceName + value.endCityName;
                    }
                }
            ]
        };

        $.table.init(options);

    });
    /** 获取选中行*/
    function getChecked() {
        return $.btTable.bootstrapTable('getSelections');
    }
    /**
     * 选择线路后的提交方法
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.close();

        var guideLineIndex = $("#guideLineIndex").val();

        // 选中线路的ID
        parent.$("#lineId_" + guideLineIndex).val(rows.join());
        // 选中线路的名称
        parent.$("#guideLineName_" + guideLineIndex).val($.table.selectColumns("lineName").join());


    }
</script>
</body>
</html>