<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('修改客户信息')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<style type="text/css">
    .td td {
        position: relative
    }
    .bg_title{
        font-size: 15px;
        font-weight: bold;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 113px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;
    }
    .fcff{
        color: #ff1f1f;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .tc{
        text-align: center;
    }
    .bor{
        border: 1px #eee solid;
    }
    .over{
        overflow: hidden;

    }
    .fl{
        float: left;
    }
    .ml10{
        margin-left: 10px;
    }
    .table>thead>tr>th{
        background: #eff3f9 !important;
        padding: 0;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }

    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
        /*height: 40px;*/
        /*line-height: 40px;*/
    }
    .theme-explorer{
        width: 100%;
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
</style>
<body>
<div class="form-content">
    <form id="form-client-edit" class="form-horizontal" novalidate="novalidate">

        <input name="customerId" type="hidden" id="customerId" th:value="${client.customerId}"/>
        <input name="corDate" type="hidden" id="corDate" th:value="${#dates.format(client.corDate, 'yyyy-MM-dd HH:mm:ss')}"/>

        <input name="userId" type="hidden" id="userId" th:value="${user.userId}"/>
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
<!--                <div class="panel-heading">-->
<!--                    <h5 class="panel-title">-->
<!--                        <a data-toggle="collapse" data-parent="#accordion"-->
<!--                           href="tabs_panels.html#collapseOne">基础信息</a>-->
<!--                    </h5>-->
<!--                </div>-->
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body" style="padding: 5px 10px 10px;">
                        <div class="bg_title">基础信息</div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        客户名称：</font></label>
                                    <div class="flex_right">

                                        <input name="custName" id="custName" class="form-control" type="text"
                                               maxlength="100" required autocomplete="off" th:value="${client.custName}"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 客户简称：</label>
                                    <div class="flex_right">
                                        <input name="custAbbr" id="custAbbr" class="form-control" type="text"
                                               autocomplete="off"  required   maxlength="100" aria-required="true"
                                               th:value="${client.custAbbr}"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        结算公司：</font></label>
                                    <div class="flex_right">
                                        <select id="operateCorp" name="operateCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" required onchange="changeBalaCorp()">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="${client.operateCorp}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        运营公司：</font></label>
                                    <div class="flex_right">
                                        <select id="balaCorp" name="balaCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" required >
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="${client.balaCorp}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                           <!-- <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        运营组：</font></label>
                                    <div class="flex_right">
                                        <select name="salesDept" id="salesDept" class="form-control valid"
                                                aria-invalid="false" required>
                                            <option value=""></option>
                                            <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                                    th:text="${mapS.deptName}" th:selected="${client.salesDept+''==mapS.deptId+''}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>-->
                        </div>
                        <div class="row">
                            <!--<div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 运营部：</label>
                                    <div class="flex_right">
                                        <select id="salesId" name="salesId" onchange="changeSalesId()"
                                                class="form-control valid" aria-invalid="false" required>
                                            <option value=""> &#45;&#45; 请选择 &#45;&#45;</option>
                                            <option th:each="salesGroup : ${salesGroupList}"
                                                    th:selected="${client.salesId == salesGroup.id}"
                                                    th:text="${salesGroup.salesName}"
                                                    th:value="${salesGroup.id}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> <font>
                                        运营组：</font></label>
                                    <div class="flex_right">
                                        <select name="salesDept" id="salesDept" class="form-control valid"
                                                aria-invalid="false" required>
                                            <option value=""></option>
                                            <option th:each="mapS,status:${salesDept}"
                                                    th:selected="${client.salesDept == mapS.deptId + ''}"
                                                    th:value="${mapS.deptId}"
                                                    th:text="${mapS.deptName}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>-->

                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">企业性质：</label>
                                    <div class="flex_right">
                                        <select name="enterpriseNature" class="form-control valid"
                                                th:with="type=${@dict.getType('enterprise_nature')}">
                                            <option value="" selected="selected">-- 请选择 --</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}" th:selected="${dict.dictValue+'' == client.enterpriseNature+''}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">
                                        客户集团：</label>
                                    <div class="flex_right">
                                       <!-- <select name="group.groupId" id="group.groupId"  class="form-control valid" aria-invalid="false">
                                            <option></option>
                                            <option th:each="mapS,status:${groupList}" th:value="${mapS.groupId}"
                                                    th:text="${mapS.groupName}" th:selected="${mapS.groupId==group.groupId}"></option>
                                        </select>-->
                                        <input id="group.groupCustId" name="group.groupCustId" type="hidden" th:value="${group.groupCustId}">

                                        <div class="input-group">
                                            <input name="groupName" id="groupName" th:value="${group.groupName}" placeholder="客户集团" class="form-control" type="text"
                                                   maxlength="20" autocomplete="off" aria-required="true">
                                            <input name="group.groupId" id="groupId" th:value="${group.groupId}" class="form-control" type="hidden"
                                                   maxlength="20" autocomplete="off" aria-required="true">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">客户级别：</label>
                                    <div class="flex_right">
                                        <select name="customerLevel" class="form-control valid"
                                                th:with="type=${@dict.getType('customer_level')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:field="${client.customerLevel}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">
                                        客户性质：</label>
                                    <div class="flex_right">
                                        <select name="customerType" id="customerType" class="form-control valid"
                                                aria-invalid="false">
                                            <option th:each="mapS,status:${userClass}" th:value="${mapS.value}"
                                                    th:text="${mapS.context}" th:selected="${client.customerType+''==mapS.value+''}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!--<div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">客户类型：</label>
                                    <div class="col-sm-7">
                                        <select id="custType" name="custType" class="form-control valid" th:with="type=${@dict.getType('cust_Type')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}" th:field="${client.custType}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>-->
                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span>开票类型：</label>
                                    <div class="flex_right">
                                        <select name="billingType" id="billingType" class="form-control valid" th:with="type=${@dict.getType('billing_type')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="${client.billingType}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">证件号：</label>
                                    <div class="flex_right">
                                        <input name="businesslicense" id="businesslicense" class="form-control"
                                               maxlength="25"  th:value="${client.businesslicense}" >
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><font>结算组：</font></label>
                                    <div class="flex_right">
                                        <select name="balaDept" id="balaDept" class="form-control valid"
                                                aria-invalid="false">
                                            <option value=""></option>
                                            <option th:each="mapS,status:${balanceDept}" th:value="${mapS.deptId}"
                                                    th:text="${mapS.deptName}" th:selected="${client.balaDept+''==mapS.deptId+''}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">驻场组：</label>
                                    <div class="flex_right">
                                        <select name="stationDept" id="stationDept" class="form-control valid"
                                                aria-invalid="false">
                                            <option value=""></option>
                                            <option th:each="mapS,status:${stationDept}" th:value="${mapS.deptId}"
                                                    th:text="${mapS.deptName}" th:selected="${client.stationDept+''==mapS.deptId+''}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                           <!-- <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">运营部：</label>
                                    <div class="flex_right">
                                        <select id="salesId" name="salesId" class="form-control valid" aria-invalid="false">
                                            <option value=""> &#45;&#45; 请选择 &#45;&#45;</option>
                                            <option th:each="salesGroup : ${salesGroupList}" th:text="${salesGroup.salesName}" th:selected="${client.salesId == salesGroup.id}" th:value="${salesGroup.id}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>-->
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">客户来源：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <select name="customerSource" class="form-control valid"-->
<!--                                                th:with="type=${@dict.getType('customer_source')}">-->
<!--                                            <option value="">&#45;&#45; 请选择 &#45;&#45;</option>-->
<!--                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"-->
<!--                                                    th:value="${dict.dictValue}" th:selected="${dict.dictValue+'' == client.customerSource+''}"></option>-->
<!--                                        </select>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">是否启用合同价计费：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <select name="enableContractPrice" class="form-control valid">-->
<!--                                            <option value="1" th:selected="${client.enableContractPrice == 1}">不启用</option>-->
<!--                                            <option value="2" th:selected="${client.enableContractPrice == 2}">启用</option>-->
<!--                                        </select>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left"><span class="fcff">*</span> <font>-->
<!--                                        客户状态：</font></label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <label class="toggle-switch switch-solid">-->
<!--                                            <input type="checkbox" id="isEnabled" th:checked="${client.isEnabled == 0}">-->
<!--                                            <span></span>-->
<!--                                        </label>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                        </div>
                        <div class="row">
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left"><span class="fcff">*</span> <font>-->
<!--                                        联系人：</font></label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <div class="input-group">-->
<!--                                            <input name="contact" id="contact" placeholder="" class="form-control" type="text"-->
<!--                                                   maxlength="25" autocomplete="off" disabled th:value="${client.contact}">-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left"><span class="fcff">*</span> <font>-->
<!--                                        联系人电话：</font></label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <div class="over">-->
<!--                                            <div class="fl" style="width: 80%">-->
<!--                                                <input name="phone" id="phone" class="form-control" type="text"-->
<!--                                                       maxlength="25" autocomplete="off" disabled th:value="${client.phone}">-->
<!--                                            </div>-->
<!--                                            <div class="fl ml10">-->
<!--                                                <a href="#" data-toggle="tooltip" data-placement="top" title="系统货主方登录账号。">-->
<!--                                                    <i class="fa fa-question-circle" style="color: #ff9113;font-size: 18px"></i>-->
<!--                                                </a>-->
<!--                                            </div>-->
<!--                                        </div>-->

<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
 <!--                           <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> APP联系人：</label>
                                    <div class="flex_right">
                                        <input name="appDeliContact" id="appDeliContact" th:value="${client.appDeliContact}" placeholder="APP联系人" class="form-control" type="text"
                                               maxlength="20" autocomplete="off" aria-required="true" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> APP联系人手机：</label>
                                    <div class="flex_right">

                                        <div class="over">
                                            <div class="fl" style="width: 80%">
                                                <input name="appDeliMobile" id="appDeliMobile" th:value="${client.appDeliMobile}" placeholder="APP联系人" class="form-control" type="text"
                                                       maxlength="11" autocomplete="off" aria-required="true" required>
                                            </div>
                                            <div class="fl ml10">
                                                <a href="#" data-toggle="tooltip" data-placement="top" title="说明：本信息为司机到场需联系的我司人员信息，信息将在app（畅运通-车主版）中显示。">
                                                    <i class="fa fa-question-circle" style="color: #ff9113;font-size: 18px"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>-->
                        </div>
                        <div class="row">
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">-->
<!--                                        联系人职位：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <input name="contactPost" id="contactPost" class="form-control" type="text"-->
<!--                                               maxlength="25" autocomplete="off" aria-required="true" th:value="${client.contactPost}">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">-->
<!--                                        邮箱：</label>-->
<!--                                    <div class="flex_right">-->
<!--                                        <input name="email" id="email" class="form-control" type="text"-->
<!--                                               maxlength="25" autocomplete="off" aria-required="true" th:value="${client.email}">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">引荐人：</label>
                                    <div class="flex_right">
                                        <div class="input-group">
                                            <input name="referrer" id="referrer" th:value="${client.referrer}" class="form-control" type="text"
                                                   maxlength="25" autocomplete="off">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">标的金额：</label>
                                    <div class="flex_right">
                                        <div class="input-group">
                                            <input name="subjectAmount" id="subjectAmount" th:value="${client.subjectAmount}" placeholder="标的金额"
                                                   class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                   type="text" autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row " >
                                <div class="flex">
                                    <label class="flex_left">地址：</label>
                                    <div class="flex_right">
                                        <div class="col-sm-2">
                                            <select name="provinceId" id="provinceId"  class="form-control valid" aria-invalid="false"></select>
                                            <input name="provinceName" id="provinceName" class="form-control" type="hidden" th:value="${client.provinceName}">

                                        </div>
                                        <div class="col-sm-2">
                                            <select name="cityId" id="cityId"  class="form-control valid" ></select>
                                            <input name="cityName" id="cityName" class="form-control" type="hidden" th:value="${client.cityName}">

                                        </div>
                                        <div class="col-sm-2">
                                            <select name="areaId" id="areaId"  class="form-control valid" aria-invalid="false"></select>
                                            <input name="areaName" id="areaName" class="form-control" type="hidden" th:value="${client.areaName}">

                                        </div>
                                        <div class="col-sm-6">
                                            <input name="address" id="address" placeholder="请输入详细地址" class="form-control" type="text"
                                                   maxlength="50"  aria-required="true" th:value="${client.address}">
                                        </div>
                                    </div>
                                </div>

                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="flex">
                                    <label class=" flex_left">备注：</label>
                                    <div class="flex_right">
                                            <textarea name="memo" id="memo" maxlength="100" class="form-control valid"
                                                      rows="3" autocomplete="off" th:text="${client.memo}"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg_title mt10">系统信息</div>
                        <!--                        <div class="row">-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-5" >-->
                        <!--                                        业务员：</label>-->
                        <!--                                    <div class="col-sm-7">-->
                        <!--                                        <div class="input-group">-->
                        <!--                                            <input name="psndocName" id="psndocName" class="form-control" type="text"-->
                        <!--                                                   maxlength="25" autocomplete="off" onclick="selectUser()"  disabled-->
                        <!--                                                   th:value="${client.psndocName}">-->
                        <!--                                            <input name="psndoc" id="psndoc" class="form-control" type="hidden"-->
                        <!--                                                   maxlength="25" autocomplete="off" th:value="${client.psndoc}">-->
                        <!--                                            <span class="input-group-addon"><i class="fa fa-search"></i></span>-->
                        <!--                                        </div>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->

                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-5">-->
                        <!--                                        业务员联系方式：</label>-->
                        <!--                                    <div class="col-sm-7">-->
                        <!--                                        <input name="psncontact"  id="psncontact" class="form-control" type="text"-->
                        <!--                                               maxlength="25" autocomplete="off"  th:value="${client.psncontact}" disabled>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-5">税率：</label>-->
                        <!--                                    <div class="col-sm-7">-->
                        <!--                                        <input name="tariff" id="tariff" th:value="${client.tariff}" oninput="$.numberUtil.onlyNumberCustom(this,100,0,5,2);"-->
                        <!--                                               type="text" class="form-control" autocomplete="off">-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->

                        <!--                        </div>-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">是否跟踪：</label>
                                    <div class="flex_right">
                                        <select name="isNeedTrace" class="form-control " disabled>
                                            <option value="1" th:selected="${client.isNeedTrace == 1}">是</option>
                                            <option value="0" th:selected="${client.isNeedTrace == 0}">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 设置指导价：</label>
                                    <div class="flex_right">
                                        <select name="crtGuidePrice" id="crtGuidePrice" class="form-control valid" required aria-invalid="false" disabled>
                                            <option value=""></option>
                                            <option value="0" th:selected="${client.crtGuidePrice == 0}">否</option>
                                            <option value="1" th:selected="${client.crtGuidePrice == 1}">是</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">是否需要回单：</label>
                                    <div class="flex_right">
                                        <select name="isNeedReceipt" class="form-control valid" disabled>
                                            <option value="1" th:selected="${client.isNeedReceipt == 1}">是</option>
                                            <option value="0" th:selected="${client.isNeedReceipt == 0}">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">装卸计价：</label>
                                    <div class="flex_right">
                                        <div class="over">
                                            <div class="fl" style="width: 80%">
                                                <select name="handlingChargesType" id="handlingChargesType" class="form-control valid">
                                                    <option value=""></option>
                                                    <option value="0" th:selected="${client.handlingChargesType == '0'}">按件</option>
                                                    <option value="1" th:selected="${client.handlingChargesType == '1'}">按方</option>
                                                    <option value="2" th:selected="${client.handlingChargesType == '2'}">按吨</option>
                                                </select>
                                            </div>
                                            <div class="fl ml10">
                                                <a href="#" data-toggle="tooltip" data-placement="top" title="三方费用自动计费配置。">
                                                    <i class="fa fa-question-circle" style="color: #ff9113;font-size: 18px"></i>
                                                </a>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">装卸单价：</label>
                                    <div class="flex_right">
                                        <input style="display: inline-block" name="handlingCharges" id="handlingCharges" type="text"
                                               class="form-control valid"  oninput="$.numberUtil.onlyNumberCustom(this,9999999,-9999999,10,3);"
                                               th:value="${client.handlingCharges}" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
        </div>

        <!--        <div class="panel panel-default">-->
        <!--                <div class="panel-heading">-->
        <!--                    <h4 class="panel-title">-->
        <!--                        <a data-toggle="collapse" data-parent="#accordion"-->
        <!--                           href="tabs_panels.html#collapseTwo">联系方式</a>-->
        <!--                    </h4>-->
        <!--                </div>-->
        <!--                <div id="collapseTwo" class="panel-collapse collapse in">-->
        <!--                    <div class="panel-body">-->
        <!--                        &lt;!&ndash;联系方式 start&ndash;&gt;-->
        <!--                        <div class="row">-->
        <!--                            <div class="col-md-3 col-sm-6">-->
        <!--                                <div class="form-group">-->
        <!--                                    <label class="col-sm-5"><font color="red">-->
        <!--                                        联系人：</font></label>-->
        <!--                                    <div class="col-sm-7">-->
        <!--                                        <div class="input-group">-->
        <!--                                            <input name="contact" id="contact" placeholder="" class="form-control" type="text"-->
        <!--                                                   maxlength="25" autocomplete="off" disabled th:value="${client.contact}">-->
        <!--                                        </div>-->
        <!--                                    </div>-->
        <!--                                </div>-->
        <!--                            </div>-->
        <!--                            <div class="col-md-3 col-sm-6">-->
        <!--                                <div class="form-group">-->
        <!--                                    <label class="col-sm-5">-->
        <!--                                            联系人职位：</label>-->
        <!--                                    <div class="col-sm-7">-->
        <!--                                        <input name="contactPost" id="contactPost" class="form-control" type="text"-->
        <!--                                               maxlength="25" autocomplete="off" aria-required="true" th:value="${client.contactPost}">-->
        <!--                                    </div>-->
        <!--                                </div>-->
        <!--                            </div>-->
        <!--                            <div class="col-md-3 col-sm-6">-->
        <!--                                <div class="form-group">-->
        <!--                                    <label class="col-sm-5"><font color="red">-->
        <!--                                        联系人电话：</font></label>-->
        <!--                                    <div class="col-sm-7">-->
        <!--                                        <input name="phone" id="phone" class="form-control" type="text"-->
        <!--                                               maxlength="25" autocomplete="off" disabled th:value="${client.phone}">-->
        <!--                                    </div>-->
        <!--                                </div>-->
        <!--                            </div>-->
        <!--                            <div class="col-md-3 col-sm-6">-->
        <!--                                <div class="form-group">-->
        <!--                                    <label class="col-sm-5">-->
        <!--                                            邮箱：</label>-->
        <!--                                    <div class="col-sm-7">-->
        <!--                                        <input name="email" id="email" class="form-control" type="text"-->
        <!--                                               maxlength="25" autocomplete="off" aria-required="true" th:value="${client.email}">-->
        <!--                                    </div>-->
        <!--                                </div>-->
        <!--                            </div>-->
        <!--                        </div>-->
        <!--                        &lt;!&ndash;联系方式 end&ndash;&gt;-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--            </div>-->
        <!-- <div class="panel panel-default">
             <div class="panel-heading">
                 <h4 class="panel-title">
                     <a data-toggle="collapse" data-parent="#accordion"
                        href="tabs_panels.html#collapseThree">开票信息</a>
                 </h4>
             </div>
             <div id="collapseThree" class="panel-collapse collapse in">
                 <div class="panel-body">
                     &lt;!&ndash;会计属性 start&ndash;&gt;
                     <div class="row">
                         <div class="col-md-3 col-sm-6">
                             <div class="form-group">
                                 <label class="col-sm-5"><font color="red">
                                     开票公司：</font></label>
                                 <div class="col-sm-7">
                                     <input type="text" id="billingCorp" class="form-control" disabled
                                            th:if="${client.balaCorp!= null}"
                                            th:value="${@dict.getLabel('bala_corp',client.balaCorp)}">
                                     <input type="hidden" id="billingCorpVal" name="billingCorp" th:value="${client.balaCorp}" class="form-control">

                                         &lt;!&ndash;<select  name="billingCorp" id="billingCorp" class="form-control valid"
                                                  th:with="type=${@dict.getType('bala_corp')}" required>
                                             <option value=""></option>
                                             <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                     th:value="${dict.dictValue}" th:field="${client.billingCorp}"></option>
                                         </select>&ndash;&gt;
                                 </div>
                                 </div>
                             </div>
                         <div class="col-md-3 col-sm-6">
                             <div class="form-group">
                                 <label class="col-sm-5">
                                     发票抬头：</label>
                                 <div class="col-sm-7">
                                     <input name="billingPayable" id="billingPayable"  class="form-control" type="text"
                                            maxlength="25"  autocomplete="off"  th:value="${client.billingPayable}">
                                 </div>
                             </div>
                         </div>
                         &lt;!&ndash;<div class="col-md-3 col-sm-6">
                             <div class="form-group">
                                 <label class="col-sm-5">
                                     法人代表：</label>
                                 <div class="col-sm-7">
                                     <input name="legalRepresent"  id="legalRepresent" class="form-control" type="text"
                                            maxlength="25"  autocomplete="off" aria-required="true" th:value="${client.legalRepresent}">
                                 </div>
                             </div>
                         </div>&ndash;&gt;
                         <div class="col-md-3 col-sm-6">
                             <div class="form-group">
                                 <label class="col-sm-5">
                                         纳税识别号：</label>
                                 <div class="col-sm-7">
                                     <input name="taxIdentify" id="taxIdentify" class="form-control" type="text"
                                            maxlength="25" autocomplete="off" aria-required="true" th:value="${client.taxIdentify}">
                                 </div>
                             </div>
                         </div>
                         <div class="col-md-3 col-sm-6">
                             <div class="form-group">
                                 <label class="col-sm-5">
                                     开户银行：</label>
                                 <div class="col-sm-7">
                                     <input name="bank"  id="bank" class="form-control"
                                            type="text" th:value="${client.bank}"
                                            maxlength="125" autocomplete="off">
                                     &lt;!&ndash; <select name="bankId" id="bankId" class="form-control valid"
                                              onchange="setBank();"   th:with="type=${@dict.getType('banks')}">
                                          <option value=""></option>
                                          <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                  th:value="${dict.dictValue}" th:field="${client.bankId}"></option>
                                      </select>
                                      <input name="bank" id="bank" type="hidden" th:value="${client.bank}">&ndash;&gt;
                                 </div>
                             </div>
                         </div>

                     </div>
                     <div class="row">
                         <div class="col-md-3 col-sm-6">
                             <div class="form-group">
                                 <label class="col-sm-5">
                                         开票类型：</label>
                                 <div class="col-sm-7">
                                     <select name="billingType" id="billingType" class="form-control valid" th:with="type=${@dict.getType('billing_type')}">
                                         <option value=""></option>
                                         <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="${client.billingType}"></option>
                                     </select>
                                 </div>
                             </div>
                         </div>


                         <div class="col-md-3 col-sm-6">
                             <div class="form-group">
                                 <label class="col-sm-5">
                                     开户账号：</label>
                                 <div class="col-sm-7">
                                     <input name="bankAccount"  id="bankAccount" class="form-control" type="text"
                                            maxlength="25" autocomplete="off"  th:value="${client.bankAccount}">
                                 </div>
                             </div>
                         </div>
                         <div class="col-sm-5">
                             <div class="form-group">
                                 <label class="col-sm-3">
                                     地址及电话：</label>
                                 <div class="col-sm-9">
                                     <input name="registerAddr" id="registerAddr" class="form-control" type="text"
                                            maxlength="100" autocomplete="off" aria-required="true" th:value="${client.registerAddr}">
                                 </div>
                             </div>
                         </div>
                        &lt;!&ndash; <div class="col-md-3 col-sm-6">
                             <div class="form-group">
                                 <label class="col-sm-5">
                                         结算方式：</label>
                                 <div class="col-sm-7">
                                     <select name="balaType" id="balaType" class="form-control valid" th:with="type=${@dict.getType('bala_type')}">
                                         <option value=""></option>
                                         <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="${client.balaType}"></option>
                                     </select>
                                 </div>
                             </div>
                         </div>&ndash;&gt;
                     </div>
                     <div class="row">

                         &lt;!&ndash;<div class="col-md-3 col-sm-6">
                             <div class="form-group">
                                 <label class="col-sm-5">
                                         注册资金：</label>
                                 <div class="col-sm-7">
                                     <input name="registerCapital" id="registerCapital"  class="form-control"
                                            type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="10" autocomplete="off"
                                            aria-required="true" th:value="${client.registerCapital}">
                                 </div>
                             </div>
                         </div>

                         <div class="col-md-3 col-sm-6">
                             <div class="form-group">
                                 <label class="col-sm-5">
                                     法人身份证：</label>
                                 <div class="col-sm-7">
                                     <input name="legalCard" id="legalCard" class="form-control" type="text"
                                            maxlength="25" autocomplete="off" th:value="${client.legalCard}">
                                 </div>
                             </div>
                         </div>&ndash;&gt;

                     </div>
                     <div class="row">
                         <div class="col-md-3 col-sm-6">
                             <div class="form-group">
                                 <label class="col-sm-5">
                                     收件人：</label>
                                 <div class="col-sm-7">
                                     <input name="addressee" id="addressee" class="form-control" type="text"
                                            maxlength="25" autocomplete="off" aria-required="true" th:value="${client.addressee}">
                                 </div>
                             </div>
                         </div>
                         <div class="col-md-3 col-sm-6">
                             <div class="form-group">
                                 <label class="col-sm-5">
                                     收件人联系方式：</label>
                                 <div class="col-sm-7">
                                     <input name="addresseeContact" id="addresseeContact"  class="form-control" type="text"
                                            maxlength="25" autocomplete="off" aria-required="true" th:value="${client.addresseeContact}">
                                 </div>
                             </div>
                         </div>
                         <div class="col-sm-5">
                             <div class="form-group">
                                 <label class="col-sm-3">
                                     邮寄地址：</label>
                                 <div class="col-sm-9">
                                     <input name="addresseeAddr" id="addresseeAddr" class="form-control" type="text"
                                            maxlength="100" autocomplete="off" aria-required="true" th:value="${client.addresseeAddr}">
                                 </div>
                             </div>
                         </div>
                     </div>
                     &lt;!&ndash;会计属性 end&ndash;&gt;
                 </div>
             </div>
         </div>
     </div>
-->
        <!--开票信息 start-->
        <div class="panel panel-default">
<!--            <div class="panel-heading">-->
<!--                <h4 class="panel-title">-->
<!--                    <a data-toggle="collapse" data-parent="#accordion"-->
<!--                       href="tabs_panels.html#collapseThree">开票信息</a>-->
<!--                </h4>-->
<!--            </div>-->
            <div class="panel-collapse collapse in" id="collapseThree">
                <div class="panel-body">
                    <div class="bg_title">开票信息</div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left"><span class="fcff">*</span> 合同账期：</label>
                                <div class="flex_right">
                                    <input name="collectionDays" id="collectionDays" class="form-control"
                                           type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off"
                                           th:value="${client.collectionDays}"
                                           onkeyup="this.value=this.value.replace(/\D/g,'')" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">特殊日期：</label>
                                <div class="flex_right">
                                    <div class="over">
                                        <div class="fl" style="width: 80%">
                                            <input name="specialDate" id="specialDate" class="form-control"
                                                   type="number" min="1" max="31" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off"
                                                   th:value="${client.specialDate}"
                                                   onkeyup="this.value=this.value.replace(/\D/g,'')" disabled>
                                        </div>
                                        <div class="fl ml10">
                                            <a href="#" data-toggle="tooltip" data-placement="top" title="涉及对账收款月份非自然月的时候需要填写，例如某客户25号（包含）之后单据算到下月对账，则特殊日期填25">
                                                <i class="fa fa-question-circle" style="color: #7f7f7f;font-size: 18px"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">对账期：</label>
                                <div class="flex_right">
                                    <input name="paymentDays" id="paymentDays" class="form-control"
                                           type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off"
                                           th:value="${client.paymentDays}"
                                           onkeyup="this.value=this.value.replace(/\D/g,'')" required disabled>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">申请开票期：</label>
                                <div class="flex_right">
                                    <input name="invoiceDays" id="invoiceDays" class="form-control"
                                           type="text" min="0" oninput="$.numberUtil.onlyNumber(this)" maxlength="5" autocomplete="off"
                                           th:value="${client.invoiceDays}"
                                           onkeyup="this.value=this.value.replace(/\D/g,'')" required disabled>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabThree" class="custom-tab table" >

                            <thead>
                            <tr>
                                <th style="width: 3%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRowThree()" title="新增行">+</a></th>
                                <th style="width: 15%;">开票公司</th>
                                <th style="width: 15%;">发票抬头</th>
                                <th style="width: 12%;">纳税识别号</th>
                                <th style="width: 12%;">开户银行</th>
                                <th style="width: 8%;">开票类型</th>
                                <th style="width: 15%;">开户账号</th>
                                <th style="width: 20%;">地址及电话</th>
                                <!--                                <th style="width: 8%;">收件人</th>-->
                                <!--                                <th style="width: 10%;">收件人联系方式</th>-->
                                <!--                                <th style="width: 18%;">邮寄地址</th>-->
                            </tr>

                            </thead>
                            <tbody>

                            <tr th:each="custBillingList,stat: ${custBillings}">
                                <input type="hidden" id="custBillingSize" th:value="${stat.size}">
                                <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" th:onclick="'removeRowThree(this,'+${stat.index}+')'" title="删除选择行"></a></td>
                                <td>
                                    <select th:id="|billingCorp${stat.index}|"
                                            th:name="|custBillingList[${stat.index}].billingCorp|"
                                            class="form-control valid"
                                            th:with="type=${@dict.getType('bala_corp')}">
                                        <option value=""></option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}" th:selected="${custBillingList.billingCorp} == ${dict.dictValue}"></option>
                                    </select >
                                    <!--  <input type="text" class="form-control"
                                             th:id="|billingCorp${stat.index}|"
                                             th:value="${@dict.getLabel('bala_corp',custBillingList.billingCorp)}">
                                      <input type="hidden" class="form-control"
                                             th:id="|billingCorpVal${stat.index}|"
                                             th:name="|custBillingList[${stat.index}].billingCorp|"
                                             th:value="${custBillingList.billingCorp}">-->
                                </td>
                                <td>
                                    <input type="text" maxlength="25"  class="form-control"
                                           th:id="|billingPayable${stat.index}|"
                                           th:name="|custBillingList[${stat.index}].billingPayable|"
                                           th:value="${custBillingList.billingPayable}">
                                </td>
                                <td>
                                    <input type="text" maxlength="25" class="form-control"
                                           th:id="|taxIdentify${stat.index}|"
                                           th:name="|custBillingList[${stat.index}].taxIdentify|"
                                           th:value="${custBillingList.taxIdentify}">
                                </td>
                                <td>
                                    <input type="text" maxlength="125" class="form-control"
                                           th:id="|bank${stat.index}|"
                                           th:name="|custBillingList[${stat.index}].bank|"
                                           th:value="${custBillingList.bank}">
                                </td>
                                <td>
                                    <select th:id="|billingType${stat.index}|"
                                            th:name="|custBillingList[${stat.index}].billingType|"
                                            class="form-control valid"
                                            th:with="type=${@dict.getType('billing_type')}">
                                        <option value=""></option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}" th:selected="${custBillingList.billingType} == ${dict.dictValue}"></option>
                                    </select >
                                </td>
                                <td>
                                    <input type="text" maxlength="25" class="form-control"
                                           th:id="|bankAccount${stat.index}|"
                                           th:name="|custBillingList[${stat.index}].bankAccount|"
                                           th:value="${custBillingList.bankAccount}">
                                </td>
                                <td>
                                    <input type="text" maxlength="125" class="form-control"
                                           th:id="|addressPhone${stat.index}|"
                                           th:name="|custBillingList[${stat.index}].addressPhone|"
                                           th:value="${custBillingList.addressPhone}">
                                </td>
                                <!--                                <td>-->
                                <!--                                    <input type="text" maxlength="25" class="form-control"-->
                                <!--                                           th:id="|addressee${stat.index}|"-->
                                <!--                                           th:name="|custBillingList[${stat.index}].addressee|"-->
                                <!--                                           th:value="${custBillingList.addressee}">-->
                                <!--                                </td>-->
                                <!--                                <td>-->
                                <!--                                    <input type="text" maxlength="25" class="form-control"-->
                                <!--                                           th:id="|addresseeContact${stat.index}|"-->
                                <!--                                           th:name="|custBillingList[${stat.index}].addresseeContact|"-->
                                <!--                                           th:value="${custBillingList.addresseeContact}">-->
                                <!--                                </td>-->
                                <!--                                <td>-->
                                <!--                                    <input type="text" maxlength="125" class="form-control"-->
                                <!--                                           th:id="|addresseeAddr${stat.index}|"-->
                                <!--                                           th:name="|custBillingList[${stat.index}].addresseeAddr|"-->
                                <!--                                           th:value="${custBillingList.addresseeAddr}">-->
                                <!--                                </td>-->

                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>

        <div class="panel panel-default">
<!--            <div class="panel-heading">-->
<!--                <h4 class="panel-title">-->
<!--                    <a data-toggle="collapse" data-parent="#accordion"-->
<!--                       href="tabs_panels.html#collapseFour">结算客户</a>-->
<!--                </h4>-->
<!--            </div>-->
            <div class="panel-collapse collapse in" id="collapseFour">
                <div class="panel-body">
                    <div class="bg_title">结算客户</div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">结算客户：</label>
                                <div class="flex_right">
                                    <div class="input-group">
                                        <input name="relatedClientName_0" id="relatedClientName_0" type="text"
                                               placeholder="请选择结算客户" class="form-control valid" autocomplete="off"
                                               onclick="selectRelatedClient(0)" readonly
                                               th:value="${relatedClient.get(0).custName}">
                                        <input id='relatedCustId_0' name='custBalaList[0].relatedCustId' th:value="${relatedClient.get(0).relatedCustId}" type="hidden">
                                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">是否默认：</label>
                                <div class="flex_right">
                                    <select name="custBalaList[0].isDefault" id="isDefault_0" class="form-control valid"
                                            aria-invalid="false">
                                        <option value="0" th:selected="${relatedClient.get(0).isDefault == '0'}">否</option>
                                        <option value="1" th:selected="${relatedClient.get(0).isDefault == '1'}">是</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left">是否失效：</label>
                                <div class="flex_right">
                                    <select name="custBalaList[0].lockedFlag" id="lockedFlag_0" class="form-control valid"
                                            aria-invalid="false">
                                        <option value="0" th:selected="${relatedClient.get(0).lockedFlag == '0'}">否</option>
                                        <option value="1" th:selected="${relatedClient.get(0).lockedFlag == '1'}">是</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

       <!-- <div class="panel panel-default">
&lt;!&ndash;            <div class="panel-heading">&ndash;&gt;
&lt;!&ndash;                <h4 class="panel-title">&ndash;&gt;
&lt;!&ndash;                    <a data-toggle="collapse" data-parent="#accordion"&ndash;&gt;
&lt;!&ndash;                       href="tabs_panels.html#collapseSeven">线路</a>&ndash;&gt;
&lt;!&ndash;                </h4>&ndash;&gt;
&lt;!&ndash;            </div>&ndash;&gt;
            <div class="panel-collapse collapse in" id="collapseSeven">
                <div class="panel-body">
                    <div class="bg_title">线路</div>
                    &lt;!&ndash; begin&ndash;&gt;
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabSeven" class="custom-tab table td">

                            <thead>
                            <tr>
                                <th style="width: 3%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRowSeven()" title="新增行">+</a></th>
                                <th style="width: 15%;">线路名称</th>
                                <th style="width: 15%;">回单预警时间</th>
                                <th style="width: 8%;"></th>
                                <th style="width: 8%;"></th>
                                <th style="width: 8%;"></th>
                                <th style="width: 18%;"></th>
                                <th style="width: 18%;"></th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="mapS,status:${clientLine}">
                                <input type="hidden" id="clientLineSize" th:value="${status.size}">
                                <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"  th:onclick="'removeRowSeven(this,'+${status.index}+')'" title="删除选择行"></a></td>
                                <td>
                                    <div class="input-group">
                                        <input th:name="${status.index}" th:id="lineName_+${status.index}"  th:value="${mapS.lineName}"
                                               placeholder="请选择线路"  onclick="selectLine(this.name)" class="form-control"  type="text"
                                               readonly   >
                                        <input th:name="|custTransLineList[${status.index}].transLineId|" th:id="transLineId_+${status.index}" th:value="${mapS.transLineId}" type="hidden">
                                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                    </div>
                                </td>
                                <td>
                                    <select th:name="|custTransLineList[${status.index}].receiptIntervalDay|" th:id="receiptIntervalDay_+${status.index}" class="form-control valid" th:with="type=${@dict.getType('receipt_interval_day')}">
                                        <option value="" selected="selected">&#45;&#45; 请选择 &#45;&#45;</option>
                                        <option th:each="map,status:${receiptDay}" th:value="${map.dictValue}"
                                                th:text="${map.dictLabel}" th:selected="${map.dictValue==''+mapS.receiptIntervalDay}"></option>
                                    </select>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>-->

        <!--<div class="panel panel-default">
            <div class="panel-collapse collapse in">

                <div class="panel-body">
                    <div class="bg_title">客服</div>
                    &lt;!&ndash; begin&ndash;&gt;
                    <div class=" mt10" style="margin: 0px -5px;">
                        <table border="0" class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 5%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRowKF()" title="新增行">+</a></th>
                                <th style="width: 25%;">用户</th>
                                <th style="width: 20%;">客服类型</th>
                                <th style="width: 50%;"></th>

                            </tr>
                            </thead>
                            <tbody  id="infoTabKF">
                            <tr th:each="customerService,status: ${customerServiceList}">
                                <td>
                                    <a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"
                                       th:onclick="|removeRowKF(this,${status.index})|" title="删除选择行"></a></td>
                                <td>
                                    <div class="input-group">
                                        <input th:id="|userName_${status.index}|"
                                               th:name="|userName_${status.index}|"
                                               th:value="${customerService.serviceName}"
                                               class="form-control" type="text">
                                        <input th:name="|customerServiceList[${status.index}].serviceId|"
                                               th:id="|serviceId_${status.index}|"
                                               th:value="${customerService.serviceId}"
                                               class="form-control" type="hidden" aria-required="true">
                                        <div class="input-group-btn">
                                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <select th:name="|customerServiceList[${status.index}].serviceType|"
                                            th:id="|serviceType_${status.index}|"
                                            th:with="type=${@dict.getType('trans_code')}"
                                            class="form-control"  >
                                        <option value=""></option>
                                        <option th:each="dict : ${type}"
                                                th:selected="${dict.dictValue == customerService.serviceType + ''}"
                                                th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>-->


        <div class="panel panel-default">
<!--            <div class="panel-heading">-->
<!--                <h4 class="panel-title">-->
<!--                    <a data-toggle="collapse" data-parent="#accordion"-->
<!--                       href="tabs_panels.html#collapsePic">证件上传</a>-->
<!--                </h4>-->
<!--            </div>-->
            <div id="collapsePic" class="panel-collapse collapse in">
                <div class="panel-body" style="padding: 5px 10px 10px;">
                    <div class="bg_title">证件上传</div>
                    <div class="row" >
                        <div class="col-md-3 col-sm-6" th:each="dict : ${picList}" >
                            <div class="">
                                <label class="" th:text="${dict.context}+'：'">
                                </label>
                            </div>
                            <div class="">
                                <div class="">
                                    <input th:id="'image'+${dict.value}" class="form-control"
                                           th:name="'image'+${dict.value}" type="file" >
                                    <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}"
                                           type="hidden">
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div id="collapsePic1" class="panel-collapse collapse in">
                <div class="panel-body" style="padding: 5px 10px 10px;">
                    <div class="bg_title">注意事项上传</div>
                    <div class="row" >
                        <div class="col-md-12 col-sm-12">
                            <div class="">
                                <input id="noticeFile" name="noticeFile" class="form-control" type="file" >
                                <input id="noticeFileId" name="noticeFileId" type="hidden">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js" />

<script th:inline="javascript">
    // 回单预警时间
    var receiptDayHTML = [[${receiptDay}]];

    //注意事项附件id
    var noticeFiles = [[${noticeFiles}]];

    var customerServiceList = [[${customerServiceList}]];

    var rdHTML = '';
    for ( var i = 0; i < receiptDayHTML.length; i++) {

        rdHTML += '<option  value='+receiptDayHTML[i].dictValue+' >'+receiptDayHTML[i].dictLabel+'</option>'
    }

    // 车长
    var carLenHTML = [[${carLen}]];
    var clHTML = '';
    for ( var i = 0; i < carLenHTML.length; i++) {
        clHTML += '<option  value='+carLenHTML[i].dictValue+' >'+carLenHTML[i].dictLabel+'</option>'
    }
    // 车型
    var carTypeHTML = [[${carType}]];
    var ctHTML = '';
    for ( var i = 0; i < carTypeHTML.length; i++) {
        ctHTML += '<option  value='+carTypeHTML[i].dictValue+' >'+carTypeHTML[i].dictLabel+'</option>'
    }

    /*选择业务员*/
    function selectUser(){
        $.modal.open("选择业务员", ctx + "system/user/selectUser/salesUserList",'','',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $("#psndocName").val("");
                $("#psndoc").val("");
                $("#psncontact").val("");
                layer.close(index);
                return;
            }

            //业务员
            $("#psndocName").val(rows[0]["userName"]);
            $("#psndoc").val(rows[0]["userId"]);
            $("#psncontact").val(rows[0]["phonenumber"]);
            $("#form-client-edit").validate().element($("#psndocName"));
            layer.close(index);
        });
    }

    $(function () {
        //
      /*  for ( var i = 0; i < customerServiceList.length; i++) {
            initBsSuggest(i)
        }
*/
        /**
         * 结算公司决定开票公司
         */
        $('#operateCorp').change(function(){
            var balaCorpVal = $(this).val();
            var balaCorpText = $(this).find("option:selected").text();
            $('#billingCorp').val(balaCorpText);
            $('#billingCorpVal').val(balaCorpVal);
        });

        $("#form-client-edit").validate({
            onkeyup: false,
            rules: {
                addresseeContact: {
                    isPhone: true
                },
                psncontact:{
                    isPhone: true
                },
                legalCard:{
                    isIdentity:true
                },appDeliMobile:{
                    isPhone:true
                },
                custName: {
                    remote: {
                        url: prefix + "/checkCustNameUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "custName": function () {
                                return $.common.trim($("#custName").val());
                            },
                            "customerId": function () {
                                return $.common.trim($("#customerId").val());
                            }
                        },
                        dataFilter: function (data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
                custAbbr: {
                    remote: {
                        url: prefix + "/checkCustAbbrUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "custAbbr": function () {
                                return $.common.trim($("#custAbbr").val());
                            },
                            "customerId": function () {
                                return $.common.trim($("#customerId").val());
                            }
                        },
                        dataFilter: function (data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },

            },
            messages: {
                "phone":{
                    remote: "手机号码已经存在"
                },
                "custName":{
                    remote: "客户名称已存在"
                },
                "custAbbr":{
                    remote: "客户简称已存在"
                }
            }
        });

        var provinceId =  [[${client.provinceId}]]
        var cityId = [[${client.cityId}]]
        var areaId =  [[${client.areaId}]]
        // 初始化省市区
        $.provinces.init("provinceId","cityId","areaId",provinceId,cityId,areaId);
        $('#provinceId').change(function(){
            $("#provinceName").val($(this).find(":selected").text());
        });
        $('#cityId').change(function(){
            $("#cityName").val($(this).find(":selected").text());
        });
        $('#areaId').change(function(){
            $("#areaName").val($(this).find(":selected").text());
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#billingDate',
                type: 'datetime',
                trigger: 'click',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours=now.getHours();
                    this.dateTime.minutes=now.getMinutes();
                    this.dateTime.seconds=now.getSeconds();
                }
            });
        });


        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');
        $('#collapseSeven').collapse('show');

        var options = {};
        $.table.init(options);

        //获取TID数字
        var custPicList = [[${custPicList}]];
        for(var i=0 ; i<custPicList.length ; i++){
            var picType = custPicList[i]["picType"];
            $("#tid"+picType).val(custPicList[i]["appendixId"]);
        }
        //循环图片路径信息，初始化图片上传区域
        var imagePath = [[${dictMap}]];
        for (var key in imagePath) {
            var publishFlag = "done" + key;
            var param = {
                maxFileCount: 1,
                publish: "publishFlag",  //用于绑定下一步方法
                fileType: null, //文件类型
                overwriteInitial: false

            };
            var tid = "tid" + key;
            var imageId = "image" + key;
            $.file.loadEditFiles(imageId, tid, imagePath[key], param);

        }

        var noticeFileParam = {
            maxFileCount: 0,
            publish: "noticeFileCmt",
            fileType: "file"
        };
        $.file.loadEditFiles("noticeFile", "noticeFileId", noticeFiles, noticeFileParam);

        initReferrerBsSuggest()
        initGroupBsSuggest()
    });

    var picType = [[${picList}]];



    var prefix = ctx + "client";

    function initReferrerBsSuggest() {
        $(`#referrer`).bsSuggest('init', {
            url: ctx + "system/user/findUserInfo?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "userName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["userName", "deptName", "phonenumber"],
            effectiveFieldsAlias: {"userName": "用户名", "deptName": "部门", "phonenumber": "手机号码"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#referrer`).val(data.userName);
            // $(`#appDeliMobile`).val(data.phonenumber);
        })
    }

    function initGroupBsSuggest() {
        $(`#groupName`).bsSuggest('init', {
            url: ctx + "group/findGroup?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "groupName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["groupName"],
            effectiveFieldsAlias: {"groupName": "集团名称"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#groupName`).val(data.groupName);
            $(`#groupId`).val(data.groupId);
        })

        $('#groupName').on('keyup', function() {
            $('#groupId').val('');
        });
        $('#groupName').on('blur', function() {
            if ($('#groupId').val() === '') {
                $('#groupName').val('');
            }
        });

    }

    /*提交表单*/
    function submitHandler() {
        if ($.validate.form()) {

            var balaCorpVal = $("#operateCorp").val();//结算公司
            for(var i=0;i<=billingIndex;i++){
                let val = $("#billingCorp"+i).val();
                if (val != '' && val !=null && balaCorpVal != val) {
                    $.modal.alertWarning("请确保开票公司与结算公司一致。");
                    return false;
                }
            }

            var isValid = true;
            $('[id^="serviceId_"]').each(function() {
                var index = $(this).attr('id').split('_')[1];
                var serviceId = $(this).val();
                var serviceType = $('#serviceType_' + index).val();
                if ((serviceId && !serviceType) || (!serviceId && serviceType)) {
                    isValid = false;
                    return false; // break the loop
                }
            });
            if (!isValid) {
                $.modal.alertWarning("客服信息请填写完整。");
                return false;
            }

            var isValidKP = true;
            $('[id^="billingCorp"]').each(function() {
                var index = $(this).attr('id').split('billingCorp')[1];

                var billingCorp = $('#billingCorp' + index).val();
                var billingPayable = $('#billingPayable' + index).val();
                var taxIdentify = $('#taxIdentify' + index).val();
                var bank = $('#bank' + index).val();
                var billingType = $('#billingType' + index).val();
                var bankAccount = $('#bankAccount' + index).val();
                var addressPhone = $('#addressPhone' + index).val();

                if (billingCorp !== '' || billingPayable !== '' || taxIdentify !== '' || bank !== '' || billingType !== ''
                    || bankAccount !== '' || addressPhone !== '') {

                    if (billingCorp === '' || billingPayable === '' || taxIdentify === '' || bank === '' || billingType === ''
                        || bankAccount === '' || addressPhone === '') {
                        isValidKP = false;
                        return false; // break the loop
                    }
                }
            });
            if (!isValidKP) {
                $.modal.alertWarning("开票信息请填写完整。");
                return false;
            }




            //提交表单flag置空
            flag = "";
            if (picType.length == 1) {
                var value = picType[i].value;
                //如果还没有上传图片，flag就是空的，直接上传第一个
                if ($("#image" + value).val() != "") {
                    $("#image" + value).fileinput('upload');
                    flag = "done" + value;
                    jQuery.subscribe(flag, uploadNoticeFile);
                } else {
                    uploadNoticeFile();
                }
            } else {
                //循环字典表图片类型
                for (var i = 0; i < picType.length; i++) {
                    var value = picType[i].value;
                    //如果还没有上传图片，flag就是空的，直接上传第一个
                    if (flag == "" && i >= 0 && i < picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                        } else {
                            continue;
                        }
                    }
                    //所有都为空，直接提交表单;如果只上传最后一个，前面都没有上传，直接上传并提交表单，设置延时等待上传完成，不然来不及回调
                    if (flag == "" && i == picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                            jQuery.subscribe(flag, uploadNoticeFile);
                        } else {
                            uploadNoticeFile();
                        }
                    }
                    //如果前面有上传，且input框不空，执行上传
                    if (flag != "" && i > 0 && i < picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            jQuery.subscribe(flag, uploadPic(value));
                        } else {
                            continue;
                        }
                    }
                    //判断最后一个是否为空，为空直接提交表单，不为空上传完提交表单
                    if (flag != "" && i == picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                            jQuery.subscribe(flag, uploadNoticeFile);
                        } else {
                            jQuery.subscribe(flag, uploadNoticeFile);
                        }
                    }
                }
            }
        }
    }

    function uploadNoticeFile() {
        $("#noticeFile").fileinput('upload');
        jQuery.subscribe("noticeFileCmt", setTimeout("commit()", "1000"));

    }


    function uploadPic(dictValue) {
        $("#image" + dictValue).fileinput('upload');
        flag = "done" + dictValue;
    }

    /**
     * 保存客户的方法
     */
    function commit() {
        $(":disabled").attr("disabled", false);
        var count = 0;
        for (var i = 0;i <= relatedIndex;i++){
            if ($("#isDefault_" + i).val() === '1') {
                count ++;
            }
            if (count > 1) {
                $.modal.alertWarning("只能默认一个结算客户");
                return false;
            }
        }
        /** 测试用途
         //1.serialize()
        var data1 = $('#form-client-edit').serialize()
        console.log(data1)  //customerId=xxx&cust=xxx
        console.log(typeof data1)//string
        //2.serializeArray()
        var data = $('#form-client-edit').serializeArray()
        console.log(data)//[{name:"customerId",value:"1323212"},{name:"cust",value:"铭源"}]
        console.log(typeof data)//object
        //3.JavaScript对象转json字符串
        var data2 = JSON.stringify(data1)
        console.log(data2)
        console.log(typeof(data2))

        var isEnabled = $("input[id='isEnabled']").is(':checked') == true ? 0 : 1;
        data.push({"name": "isEnabled", "value": isEnabled});

        //重新组装表单序列化Url为Json串
        var jsonData = {}
        var serializeStr = $("#form-client-edit").serialize();
        var array = serializeStr.split("&");
        console.log(array)
        $(array).each(function (i) {
            jsonData[array[i].split("=")[0]] = array[i].split("=")[1];
        })
        console.log(jsonData)
        var data3 = JSON.stringify(jsonData)

        saveTab(prefix + "/editClient", data3)
        **/
        var data = $('#form-client-edit').serializeArray()
        // var isEnabled = $("input[id='isEnabled']").is(':checked') == true ? 0 : 1;
        // data.push({"name": "isEnabled", "value": isEnabled});
        $.operate.saveTab(prefix + "/editClient", data)
    }
    /**
     * 测试用途
    function saveTab(url, data, callback) {
        var config = {
            url: url,
            type: "post",
            dataType: "json",
            contentType: "application/json",
            data: data,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function(result) {
                if (typeof callback == "function") {
                    callback(result);
                }
                $.operate.successTabCallback(result);
            }
        };
        $.ajax(config)
    }
    **/

    /**
     * 结算客户的选择框
     */
    function selectRelatedClient(relatedIndex) {

        $.modal.open("选择结算客户", prefix + "/related?relatedIndex="+relatedIndex);
    }

    /**
     * 设置开户行名称
     */
    function setBank() {
        $("#bank").val($("#bankId option:selected").text());
    }

    /**
     * 货品选择框
     */
    function selectGoods() {

        $.modal.open("选择货品", prefix + "/getGoods");
    }

    /**
     * 线路选择框
     */
    function selectLine(lineIndex) {

        $.modal.open("选择线路", prefix + "/line?lineIndex="+lineIndex, "","",function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            // 选中线路的ID
            $("#transLineId_" + lineIndex).val(rows[0]["transLineId"]);
            // 选中线路的名称
            $("#lineName_" + lineIndex).val(rows[0]["lineName"]);
            $("#lineCode_" + lineIndex).val(rows[0]["lineCode"]);
            //选中完需单独校验
            $("#form-client-edit").validate().element($("#lineName_" + lineIndex));
            layer.close(index);
        });
    }

    function submit(index, layero){
        $('#goodsName').removeAttr("disabled");
        var tree = layero.find("iframe")[0].contentWindow.$._tree;
        if ($.tree.notAllowParents(tree)) {
            var body = layer.getChildFrame('body', index);
            $("#treeId").val(body.find('#treeId').val());
            $("#treeName").val(body.find('#treeName').val());
            $('#goodsTypeId').val(body.find('#treeId').val());
            layer.close(index);
        }
    }



    var relatedIndex =  $("#relatedClientSize").val() - 0;
    /* 新增表格行 */
    function insertRowTwo() {
        relatedIndex += 1;
        var trTtml = '<tr>'
            + '<td> <a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRowTwo(this,'+relatedIndex+')" title="删除选择行"></a></td>'
            + ' <td> <div class="input-group">' +
            '   <input name='+relatedIndex+' id="relatedClientName_'+relatedIndex+'" placeholder="请选择结算客户" type="text"' +
            '     class="form-control valid" autocomplete="off"' +
            '     onclick="selectRelatedClient(this.name)" readonly>' +
            '    <input name="custBalaList['+ relatedIndex +'].relatedCustId" id="relatedCustId_'+relatedIndex+'"  type="hidden">' +
            '    <span class="input-group-addon"><i class="fa fa-search"></i></span>' +
            '   </div> </td>'
            + ' <td>'
            + '               <select name="custBalaList['+ relatedIndex +'].isDefault" id="isDefault_'+relatedIndex+'"  class="form-control" aria-invalid="false">'+
            '                    <option value="0">否</option>'+
            '                   <option value="1">是</option>'+
            '</select>'
            + '            </td>'
            + ' <td> <select name="custBalaList['+ relatedIndex +'].lockedFlag" id="lockedFlag_'+relatedIndex+'"  class="form-control" aria-invalid="false">'+
            '                    <option value="0">否</option>'+
            '                   <option value="1">是</option>'+
            '</select>'
            + '   </tr>';
        $("#infoTabTwo tbody").append(trTtml);
    }

    /* 删除指定表格行 */
    function removeRowTwo(obj,index) {
        if ($("#infoTabTwo tbody").find('tr').length > 1) {
            $("#infoTabTwo tbody").find(obj).closest("tr").remove();
        } else {
            $("#relatedClientName_" + index).val("");
            $("#relatedCustId_" + index).val("");
        }
    }


    /* 删除指定表格行 */
    function removeRowFour(obj) {
        $("#infoTabFour tbody").find(obj).closest("tr").remove();
    }

    var lineIndex = $("#clientLineSize").val() - 0;

    /* 新增表格行 */
    function insertRowSeven() {
        lineIndex += 1;
        var lineName = 'lineName_' + lineIndex;
        var trTtml = ' <tr>' +
            '<td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"  onclick="removeRowSeven(this,'+lineIndex+')" title="删除选择行"></a></td>' +
            '     <td>' +
            '              <div class="input-group">\n' +
            '              <input name=' + lineIndex + ' id=' + lineName + ' placeholder="请选择线路"  onclick="selectLine(this.name)" class="form-control"  type="text"  readonly >\n' +
            '              <input name="custTransLineList['+ lineIndex +'].transLineId" id="transLineId_'+ lineIndex +'" type="hidden">' +
            '              <span class="input-group-addon"><i class="fa fa-search"></i></span>' +
            '              </div>' +
            '             </td>' +
            '            <td>' +
            '             <select  name="custTransLineList['+ lineIndex +'].receiptIntervalDay" id="receiptIntervalDay_'+ lineIndex +'" class="form-control valid" >' +
            '             <option value="" selected="selected">-- 请选择 --</option>'+
            '               ' + rdHTML + ' ' +
            '             </select>' +
            '             </td>' +
            '</tr>'

        $("#infoTabSeven tbody").append(trTtml);
    }

    /* 删除指定表格行 */
    function removeRowSeven(obj,index) {
        if ($("#infoTabSeven tbody").find('tr').length > 1) {
            $("#infoTabSeven tbody").find(obj).closest("tr").remove();
        } else {
            $("#lineName_"+index).val("");
            $("#transLineId_"+index).val("");
        }
    }

    var billingIndex = $("#custBillingSize").val()-0;
    if(isNaN(billingIndex)){
        billingIndex = 0;
    }



/*
    var kfLineIndex = customerServiceList.length;
    let transCodeList = [[${@dict.getType('trans_code')}]]
    var transCodeListHtml = '';
    for ( var i = 0; i < transCodeList.length; i++) {
        transCodeListHtml += '<option  value='+transCodeList[i].dictValue+' >'+transCodeList[i].dictLabel+'</option>'
    }
    /!* 新增表格行 *!/
    function insertRowKF() {
        kfLineIndex += 1;

        let ind = kfLineIndex
        var trTtml = `
                <tr>
                    <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRowKF(this,${kfLineIndex})" title="删除选择行"></a></td>
                    <td>
                        <div class="input-group">
                            <input class="form-control" type="text" id="userName_${kfLineIndex}" name="userName_${kfLineIndex}">
                            <input name="customerServiceList[${kfLineIndex}].serviceId" id="serviceId_${kfLineIndex}" class="form-control" type="hidden" aria-required="true">
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                </ul>
                            </div>
                        </div>
                    </td>
                    <td>
                        <select class="form-control"
                                id="serviceType_${kfLineIndex}"
                                name="customerServiceList[${kfLineIndex}].serviceType">
                            <option value=""></option>
                            ${transCodeListHtml}
                        </select>
                    </td>

                </tr>
        `

        $("#infoTabKF").append(trTtml);

        initBsSuggest(ind)
    }

    /!* 删除指定表格行 *!/
    function removeRowKF(obj,index) {
        var $tbody = $('#infoTabKF');
        var $rows = $tbody.children('tr'); // Only select direct child tr elements
        if ($rows.length === 1) {
            // Clear the data in the input fields and select element
            var $inputs = $rows.find('input');
            var $selects = $rows.find('select');
            $inputs.val('');
            $selects.prop('selectedIndex', 0);
        }else {
            $(obj).closest('tr').remove();

        }
    }
    function initBsSuggest(ind) {
        $(`#userName_${ind}`).bsSuggest('init', {
            url: ctx + "system/user/findUserInfo?keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "userName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["userName","deptName","phonenumber"],
            effectiveFieldsAlias: {"userName":"用户名","deptName":"部门","phonenumber":"手机号码"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#userName_${ind}`).val(data.userName);
            $(`#serviceId_${ind}`).val(data.userId);
        })

    }
*/


    /**
     * 动态新增开票信息
     */
    var billingType = [[${@dict.getType('billing_type')}]];//开票类型
    var billingTypeHTML = '';
    for (var i = 0; i < billingType.length; i++) {
        billingTypeHTML += '<option  value=' + billingType[i].dictValue + ' >' + billingType[i].dictLabel + '</option>'
    }
    // 开票公司
    var billingType = [[${@dict.getType('bala_corp')}]];
    var biHTML = '';
    for (var i = 0; i < billingType.length; i++) {
        biHTML += '<option  value=' + billingType[i].dictValue + ' >' + billingType[i].dictLabel + '</option>'
    }
    function insertRowThree() {
        billingIndex += 1;
        var trTtml = ' <tr>\n' +
            '<td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"  onclick="removeRowThree(this,'+billingIndex+')" title="删除选择行"></a></td>' +
            '<td>'+
            '<select name="custBillingList['+billingIndex+'].billingCorp" id="billingCorp'+ billingIndex +'" class="form-control valid" >'+
            '<option value=""></option>'+
            ''+biHTML+''+
            /* '</select>'+
             '<td>'+
             '<input type="text" id="billingCorp'+ billingIndex +'"  class="form-control" >'+
             '<input type="hidden" id="billingCorpVal'+ billingIndex +'" name="custBillingList['+billingIndex+'].billingCorp" class="form-control">'+
             '</td>'+*/
            '<td><input type="text" id="billingPayable'+billingIndex+'" name="custBillingList['+billingIndex+'].billingPayable" maxlength="25"  class="form-control"></td>'+
            '<td><input type="text" id="taxIdentify'+billingIndex+'" name="custBillingList['+billingIndex+'].taxIdentify" maxlength="25" class="form-control"></td>'+
            '<td><input type="text" id="bank'+billingIndex+'" name="custBillingList['+billingIndex+'].bank" maxlength="125" class="form-control"></td>'+
            '<td>'+
            '<select name="custBillingList['+billingIndex+'].billingType" id="billingType'+billingIndex+'" class="form-control valid" >'+
            '<option value=""></option>'+
            ''+billingTypeHTML+''+
            '</select>'+
            '</td>'+
            '<td><input type="text" id="bankAccount'+billingIndex+'" name="custBillingList['+billingIndex+'].bankAccount" maxlength="25" class="form-control"></td>'+
            '<td><input type="text" id="addressPhone'+billingIndex+'" name="custBillingList['+billingIndex+'].addressPhone" maxlength="125" class="form-control"></td>'+
            // '<td><input type="text" id="addressee'+billingIndex+'" name="custBillingList['+billingIndex+'].addressee" maxlength="25" class="form-control"></td>'+
            // '<td><input type="text" id="addresseeContact'+billingIndex+'" name="custBillingList['+billingIndex+'].addresseeContact" maxlength="25" class="form-control"></td>'+
            // '<td><input type="text" id="addresseeAddr'+billingIndex+'" name="custBillingList['+billingIndex+'].addresseeAddr" maxlength="125" class="form-control"></td>'+
            '</tr>'

        $("#infoTabThree tbody").append(trTtml);
        changeBalaCorp();

    }
    function removeRowThree(obj,index) {
        if ($("#infoTabThree tbody").find('tr').length > 1) {
            $("#infoTabThree tbody").find(obj).closest("tr").remove();
        } else {
            $("#billingCorp"+index).val("");
            $("#billingCorpVal"+index).val("");
            $("#billingPayable"+index).val("");
            $("#taxIdentify"+index).val("");
            $("#bank"+index).val("");
            $("#billingType"+index).val("");
            $("#bankAccount"+index).val("");
            $("#addressPhone"+index).val("");
            $("#addressee"+index).val("");
            $("#addresseeContact"+index).val("");
            $("#addresseeAddr"+index).val("");
        }
    }

    /**
     * 结算公司决定开票公司
     */
    function changeBalaCorp(){
        var balaCorpVal = $("#operateCorp").val();//结算公司
        for(var i=0;i<=billingIndex;i++){
            $("#billingCorp"+i).val(balaCorpVal);
        }
    }

    function changeSalesId() {
        //2a828327651f4e1cbbc798d8c56ec99c 为车队
        let val = $("#salesId").val();
        if (val === '2a828327651f4e1cbbc798d8c56ec99c') {
            $("#billingType").val("6")
        }else {
            $("#billingType").val("4")
        }

        var selectedTexts = $("#salesId option:selected").map(function() {
            return $(this).text();
        }).get();

        // 使用逗号分隔多个值
        var deptName = selectedTexts.join(",");

        $.ajax({
            url: ctx + "tms/custSalesTarget/get_sales_dept",
            type: "get",
            dataType: "json",
            data: {parentDeptName: deptName},
            success: function (result) {
                if (result.code == 0 && result.data!=undefined) {
                    $("#salesDept").empty();

                    if (result.data != null) {
                        $.each(result.data, function(index, option) {
                            $("#salesDept").append($('<option>', {
                                value: option.deptId,
                                text: option.deptName
                            }));
                        });


                    }
                }
            }
        });

    }
</script>
</body>

</html>