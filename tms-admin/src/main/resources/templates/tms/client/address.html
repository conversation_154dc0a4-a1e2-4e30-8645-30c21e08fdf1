<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('地址选择页')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal">
					<div class="row">
						<div class="col-sm-6">
							<div class="form-group">
								<label class="col-sm-4">地址名称：</label>
								<div class="col-sm-8">
									<input name="addrName" placeholder="请输入地址名称" class="form-control valid" type="text"
										  >
								</div>
							</div>
						</div>
						<div class="col-sm-2">
							<div class="form-group"></div>
						</div>

						<div class="col-sm-4">
							<label class="col-sm-4"></label>
							<div class="form-group">
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>

				</form>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
				<input id="addrIndex" th:value="${addrIndex}" name="addrIndex" type="hidden">
			</div>
		</div>

	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">
		var prefix = ctx + "basic/address";

		$(function () {
			var options = {
				url: prefix + "/addressList",
				showSearch: false,
				showRefresh: false,
				showToggle: false,
				showColumns: false,
				columns: [{
					radio: true
				},
					{
						title: 'ID',
						field: 'addressId',
						visible: false
					},
					{
						title: '地址名称',
						field: 'addrName',
					},

					{
						title: '详细地址',
						align: 'left',
						width: '300px',
						formatter: function (value, row, index) {
							var address = row.provinceName + row.cityName + row.areaName + row.detailAddr;
							return $.table.tooltip(address);
						}
					},

					{
						title: '联系人',
						align: 'left',
						field: 'contact'
					},
					{
						title: '联系电话',
						align: 'left',
						field: 'mobile'
					}
				]
			};

			$.table.init(options);
		});

		/** 获取选中行*/
		function getChecked() {
			return $.btTable.bootstrapTable('getSelections');
		}
		/**
		 * 选择结算地址后的提交方法
		 */
		function submitHandler() {
			var rows = $.table.selectFirstColumns();
			if (rows.length == 0) {
				$.modal.alertWarning("请至少选择一条记录");
				return;
			}
			$.modal.close();

			var addrIndex = $("#addrIndex").val();


			// 选中的地址ID
			parent.$("#addressId_"+addrIndex).val(rows.join());
			// 选中的地址名称
			parent.$("#addrName_"+addrIndex).val($.table.selectColumns("addrName").join());

			parent.$("#corpId_"+addrIndex).val($.table.selectColumns("corpId").join());


			return false;
		}
	</script>
</body>
</html>