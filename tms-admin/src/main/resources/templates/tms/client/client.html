<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户信息列表')"/>
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 100px);
        padding-top: 0;
    }
    .left-fixed-body-columns{
        z-index: 10;
    }
    .menus{
        position: fixed;
        top: auto;
        left: auto;
    }
    /*.sm{*/
    /*    background: #fff5f5;*/
    /*    padding: 3px 10px;*/
    /*    margin-top: 5px;*/
    /*    position: relative;*/
    /*}*/
    .sm_icon{
        width: 18px;
        height: 18px;
        display: inline-block;
        line-height: 18px;
        text-align: center;
        font-size: 16px;
        color: #ed5565;
    }
    .sm_text{
        display: inline-block;
        margin-left: 10px;
        line-height: 30px;
        color: #ed5565;
    }
    .sm_btn{
        display: inline-block;
        width: 100px;
        height: 30px;
        line-height: 30px;
        margin-left: 20px;
        text-align: center;
        background: #fde6de;
        border: 1px #ff9999 solid;
        color: #ed5565;
        border-radius: 20px;
        cursor: pointer;
    }
    .btn-default {
        color: #333 !important;
        background-color: #fff !important;
        border-color: #ccc !important;
    }
    .btn-success {
        color: #fff !important;
        background-color: #5cb85c !important;
        border-color: #4cae4c !important;
    }
    .btn-success:hover,
    .btn-success:focus,
    .btn-success:active,
    .btn-success.active,
    .open .dropdown-toggle.btn-success {
        color: #fff !important;
        background-color: #47a447 !important;
        border-color: #398439 !important;
    }
    .colg{
        color: #23af95;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .closed{
        display: inline-block;
        position: absolute;
        right: 10px;
    }
    .hide{
        display: none;
    }
    .pa2{
        padding: 2px 4px;
        /* font-weight: 100; */
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
        /* border-radius: 50%; */
    }
    .enabled-danger{
        width: 10px;
        height: 10px;
        display: inline-block;
        border-radius: 50%;
        vertical-align: middle;
        background-color: #ed5565;
        box-shadow: 0px 0px 0px 2px rgba(237, 85, 101, .5);
        margin-right: 5px;
    }
    .enabled-primary{
        width: 10px;
        height: 10px;
        display: inline-block;
        border-radius: 50%;
        vertical-align: middle;
        background-color: #1ab394;
        box-shadow: 0px 0px 0px 2px rgba(26, 179, 148, .3);
        margin-right: 5px;
    }

    .label-inverse{
        background-color: rgba(38, 38, 38, .4);
    }

    .cur{
        cursor: pointer;
    }

    .tc{
        text-align: center;
    }
    /*.bootstrap-select.form-control{*/
    /*    position: initial;*/
    /*}*/

    .flex{
        display: flex;
        align-items:center;
        justify-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 26px;
        text-align: right;
        color: #000000 !important;
        margin-bottom: 0;
    }
    .flex_right{
        line-height: 26px;
        min-width:0;
        flex:1;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <form id="role-form" class="form-horizontal">

            <div class="col-sm-12 search-collapse" style="padding-top: 0">
                <div class="row no-gutter sm">
                    <span class="sm_icon">
                        <i class="fa fa-bell-o"></i>
                    </span>
                        <span class="sm_text" id="warn_msg">
                        共有
                        <span class="fcff3" id="temporaryCount"></span>个客户合同即将到期或已到期，请及时更新
                    </span>
                        <span class="sm_btn" onclick="selectTemporary()">
                        <input type="hidden" id="temporary" name="temporary"/>
                        快速筛选
                    </span>
<!--                    <button type="button" class="btn btn-sm btn-primary closed">关闭-->
<!--                    </button>&nbsp;-->
                    <span class="closed">
                        <i class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"></i>
                    </span>
                </div>
                    <div class="row no-gutter" style="margin-top: 5px">
<!--                        <div class="col-md-5 col-sm-5">-->
<!--                            -->
<!--                            <div class="col-md-3 col-sm-3">-->
<!--                                <div class="form-group">-->
<!--                                    &lt;!&ndash;                           <label class="col-sm-4">业务员：</label>&ndash;&gt;-->
<!--                                    <div class="col-sm-12">-->
<!--                                        <input name="psndocName"  id="psndocName"  placeholder="业务员名称" class="form-control valid" type="text">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                        </div>-->
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="operateCorp" id="operateCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" required>
                                        <option value="">---请选择结算公司---</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 col-sm-2">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="mgmtDeptId" id="mgmtDeptId" class="form-control valid"
                                            aria-invalid="false" onchange="changeMgmtDeptId()" required>
                                        <option value="">--管理部--</option>
                                        <option th:each="mapS,status:${mgmtDept}" th:value="${mapS.deptId}"
                                                th:text="${mapS.deptName}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 col-sm-2">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="opsDeptId" id="opsDeptId" class="form-control valid"
                                            aria-invalid="false" onchange="changeOpsDeptId()" required>
                                        <option value="">--运营部--</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-1 col-sm-2">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="salesDept" id="salesDept" class="form-control valid"
                                            aria-invalid="false" required>
                                        <option value="">--运营组--</option>
<!--                                        <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"-->
<!--                                                th:text="${mapS.deptName}"></option>-->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                           <label class="col-sm-4">客户名称：</label>-->
                                <input name="custName" id="custName" placeholder="客户名称/客户简称/集团名称"
                                       class="form-control valid" type="text">
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">联系人：</label>-->
                                <div class="col-sm-12">
                                    <input name="contact" id="contact" placeholder="联系人姓名/电话" class="form-control" type="text"
                                           maxlength="30" autocomplete="off" required th:value="${userName}">

                                </div>
                            </div>
                        </div>
    <!--                    <div class="col-md-2 col-sm-4">-->
    <!--                        <div class="form-group">-->
    <!--&lt;!&ndash;                            <label class="col-sm-4">客户简称：</label>&ndash;&gt;-->
    <!--                            <div class="col-sm-12">-->
    <!--                                <input name="custAbbr" id="custAbbr" placeholder="请输入客户简称"-->
    <!--                                       class="form-control valid" type="text">-->
    <!--                            </div>-->
    <!--                        </div>-->
    <!--                    </div>-->
                        <div class="col-md-1 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">审核状态：</label>-->
                                <div class="col-sm-12">
                                    <select id="checkStatus" name="checkStatus" class="form-control" aria-invalid="false">
                                        <option value="">--审核状态--</option>
                                        <option value="0" th:selected="${checkStatus == '0'}">待审核</option>
                                        <option value="1" th:selected="${checkStatus == '1'}">审核通过</option>
                                        <option value="2" th:selected="${checkStatus == '2'}">审核未通过</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户级别：</label>-->
                                <div class="col-sm-12">
                                    <select id="customerLevel" name="customerLevel" class="form-control"
                                            th:with="type=${@dict.getType('customer_level')}" aria-invalid="false">
                                        <option value="">--客户级别--</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>

                        </div>

<!--                        <div class="col-md-4 col-sm-4">-->

<!--                            -->
<!--                            <div class="col-md-8 col-sm-8">-->
<!--                                <div class="form-group">-->
<!--                                    &lt;!&ndash;                            <label class="col-sm-4">创建人：</label>&ndash;&gt;-->
<!--                                    <div class="col-sm-12">-->
<!--                                        <input name="address" id="address" placeholder="详细地址" class="form-control" type="text"-->
<!--                                               maxlength="350" required="" aria-required="true">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->

    <!--                    <div class="col-md-2 col-sm-4">-->
    <!--                        <div class="form-group">-->
    <!--                            &lt;!&ndash;                            <label class="col-sm-4">联系人电话：</label>&ndash;&gt;-->
    <!--                            <div class="col-sm-12">-->

    <!--                                <input name="phone" placeholder="联系人电话" id="phone" class="form-control" type="text"-->
    <!--                                       maxlength="30" autocomplete="off" >-->

    <!--                            </div>-->
    <!--                        </div>-->
    <!--                    </div>-->




<!--                        <div class="col-md-3 col-sm-3">-->

<!--                            <div class="col-md-6 col-sm-6">-->
<!--                                <div class="form-group">-->
<!--                                    &lt;!&ndash;                            <label class="col-sm-4">结算组：</label>&ndash;&gt;-->
<!--                                    <div class="">-->
<!--                                        <select name="balaDept" id="balaDept" class="form-control valid"-->
<!--                                                aria-invalid="false" required>-->
<!--                                            <option value="">&#45;&#45;结算组&#45;&#45;</option>-->
<!--                                            <option th:each="mapS,status:${balanceDept}" th:value="${mapS.deptId}"-->
<!--                                                    th:text="${mapS.deptName}"></option>-->
<!--                                        </select>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                        </div>-->

                    </div>

                    <div class="row no-gutter">
<!--                        <div class="col-md-2 col-sm-4">-->
<!--                            <div class="form-group">-->
<!--                                &lt;!&ndash;                            <label class="col-sm-4">客户级别：</label>&ndash;&gt;-->
<!--                                <div class="col-sm-12">-->
<!--                                    <select id="customerSource" name="customerSource" class="form-control"-->
<!--                                            th:with="type=${@dict.getType('customer_source')}" aria-invalid="false">-->
<!--                                        <option value="">&#45;&#45;请选择客户来源&#45;&#45;</option>-->
<!--                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"-->
<!--                                                th:value="${dict.dictValue}" th:selected="${dict.dictValue == '1'}"></option>-->
<!--                                    </select>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">创建人：</label>-->
                                <div class="col-sm-12">
                                    <input name="corUserId" placeholder="创建人" class="form-control valid" type="text"
                                           aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-2">创建时间：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" style="width: 45%; float: left;" class="form-control"
                                           id="startDate"  name="regDate" placeholder="创建开始时间">
                                    <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                    <input type="text" style="width: 45%; float: left;" class="form-control"
                                           id="endtDate"  name="corDate" placeholder="创建结束时间">
                                </div>
                            </div>

                        </div>
<!--                        <div class="col-md-4 col-sm-6">-->
<!--                            <div class="form-group">-->
<!--                                &lt;!&ndash;                                <label class="col-sm-4">地址：</label>&ndash;&gt;-->
<!--                                <div class="col-sm-4">-->
<!--                                    <select  name="provinceId" id="provinceId"  class="form-control valid" aria-invalid="false">-->

<!--                                    </select>-->

<!--                                </div>-->
<!--                                <div class="col-sm-4">-->
<!--                                    <select name="cityId" id="cityId" class="form-control valid" aria-invalid="false"></select>-->
<!--                                </div>-->

<!--                                <div class="col-sm-4">-->
<!--                                    <select name="areaId" id="areaId" class="form-control valid" aria-invalid="false"></select>-->
<!--                                </div>-->



<!--                            </div>-->
<!--                        </div>-->

                        <div class="col-md-1 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户级别：</label>-->
                                <div class="col-sm-12">
                                    <select id="isEnabled" name="isEnabled" class="form-control" aria-invalid="false">
                                        <option value="">--是否停用--</option>
                                        <option value="0">启用</option>
                                        <option value="1">停用</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户级别：</label>-->
                                <div class="col-sm-12">
                                    <select id="ifBargain" name="ifBargain" class="form-control" aria-invalid="false">
                                        <option value="">--是否议价--</option>
                                        <option value="0">否</option>
                                        <option value="1">是</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户级别：</label>-->
                                <div class="col-sm-12">
                                    <select id="isExistContract" name="isExistContract" class="form-control" aria-invalid="false">
                                        <option value="">--是否存在合同--</option>
                                        <option value="0">存在</option>
                                        <option value="1">不存在</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                                <label class="col-sm-6"></label>-->
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>

            </div>
        </form>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="addCust()" shiro:hasPermission="client:client:insert">
<!--                <i class="fa fa-plus"></i> -->
                新增
            </a>
            <a class="btn btn-default multiple disabled" onclick="check()" shiro:hasPermission="client:client:check">
<!--                <i class="fa fa-check"></i>-->
                审核
            </a>
            <a class="btn btn-default" onclick="addMessageReminderConfig()" shiro:hasPermission="tms:messageReminderConfig:add">
                <!--                <i class="fa fa-bell"></i> -->
                消息提醒配置
            </a>
            <a class="btn btn-default" onclick="batchBindService()" shiro:hasPermission="tms:customerService:batchBind">
<!--                <i class="fa fa-dollar"></i> -->
                批量绑定客服
            </a>
            <a class="btn btn-default" onclick="batchBindClerk()" shiro:hasPermission="tms:customerClerk:batchBind">
<!--                <i class="fa fa-dollar"></i> -->
                批量绑定业务员
            </a>
            <a class="btn btn-success"  onclick="importExcel()" shiro:hasPermission="client:client:import">
                <i class="fa fa-upload"></i> 导入
            </a>
            <!-- 数据修复 -->
<!--            <a class="btn btn-primary"  onclick="importDate()" shiro:hasPermission="client:client:importDate">-->
<!--                <i class="fa fa-upload"></i> 导入账期-->
<!--            </a>-->
            <a class="btn btn-success" onclick="exportExcel()"  shiro:hasPermission="client:client:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="client:client:remove">
<!--                <i class="fa fa-remove"></i> -->
                删除
            </a>
<!--            <a class="btn btn-primary" onclick="openMargin()" shiro:hasPermission="tms:margin:view">-->
<!--                <i class="fa fa-dollar"></i> 保证金-->
<!--            </a>-->

            <a class="btn btn-primary" onclick="removeLeave()" shiro:hasPermission="system:user:clean_employees">
                <i class="fa fa-remove"></i> 一键清除离职人员
            </a>
            <!-- TODO -->
          <!--<a class="btn btn-primary" onclick="relateSalesGroup()" shiro:hasPermission="client:client:relateSalesGroup">
                <i class="fa fa-handshake-o"></i> 关联运营部
            </a>-->
            <a class="btn btn-primary single disabled" onclick="autoDispatch()" shiro:hasPermission="client:autoDispatch:list">
                <i class="fa fa-truck"></i> 自动调度配置
            </a>
            <a class="btn btn-primary single disabled" onclick="miscFeeConfig()" shiro:hasPermission="tms:miscFeeConfig:view">
                <i class="fa fa-truck"></i> 其他费配置
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>

<script th:inline="javascript">
    var billingType = [[${@dict.getType('billing_type')}]];
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    var balaType = [[${@dict.getType('bala_type')}]];
    var clientType = [[${@dict.getType('cust_Type')}]];
    var customerLevel = [[${@dict.getType('customer_level')}]];//客户类别
    var customerSource = [[${@dict.getType('customer_source')}]];//客户来源
    var enterpriseNature = [[${@dict.getType('enterprise_nature')}]];//企业性质

    var editFlag = [[${@permission.hasPermi('client:client:edit')}]];//操作栏修改权限以及客户启停状态修改权限

    var prefix = ctx + "client";
    var userPrefix = ctx + "system/user"

    var sourceType = [[${sourceType}]];

    $(function () {
        var url = `${prefix}/list`
        var exportUrl = `${prefix}/export?sourceType=0`
        if (sourceType === '1') {
            url = `${prefix}/list/fleet`
            exportUrl = `${prefix}/export?sourceType=1`
        }else if (sourceType === '2') {
            url = `${prefix}/list/park`
            exportUrl = `${prefix}/export?sourceType=2`
        }

        var options = {
            url: url,
            // createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            removeUrl: prefix + "/remove",
            exportUrl: exportUrl,
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber:4,
            height: 560,
            modalName: "客户",
            clickToSelect:true,
            uniqueId: "customerId",
            onPostBody: function () {

                getAmountCount();
            },
            columns: [{
                field: 'state',
                checkbox: true
            },
                {
                    title: '操作',
                    field: 'customerId',
                    formatter: function(value,row,index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('client:client:edit')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="修改" onclick="edit(\'' + value + '\')"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>');
                        }
                        if ([[${@permission.hasPermi('client:client:editGroup')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="修改组别" onclick="editGroup(\'' + value + '\')"><i  class="fa fa-retweet" style="font-size: 15px;" ></i></a>');
                        }
                        // if ([[${@permission.hasPermi('client:client:detail')}]] != "hidden") {
                        //     actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="明细"  onclick="detail(\'' + value + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        // }
                        if ([[${@permission.hasPermi('client:client:contract')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="合同"  onclick="contract(\'' + value + '\')"> <i class="fa fa-folder-o" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('client:client:accountList')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="客户账号" onclick="accountList(\'' + value + '\')"><i class="fa fa-user" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:custAddress:view')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="客户地址" onclick="addressList(\'' + value + '\')"><i class="fa fa-location-arrow" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:custGoods:view')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="客户货品" onclick="goodList(\'' + value + '\')"><i class="fa fa-briefcase" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:customerService:singleCustomer')}]] !== "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="客服" '
                                + 'onclick="serviceList(\'' + value +'\',\'' + row.custName + '\')">'
                                + '<i class="fa fa-heart" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:customerClerk:singleCustomer')}]] !== "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="业务员" '
                                + 'onclick="clerkList(\'' + value +'\',\'' + row.custName + '\')">'
                                + '<i class="fa fa-phone" style="font-size: 15px;"></i></a>');
                        }
                        // if ([[${@permission.hasPermi('tms:customerClerk:singleCustomer')}]] !== "hidden") {
                        //     actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="第三方配置" '
                        //         + 'onclick="otherFeeConfig(\'' + value +'\')">'
                        //         + '<i class="fa fa-key" style="font-size: 15px;"></i></a>');
                        // }
                        // if ([[${@permission.hasPermi('client:client:adjustmentConfig')}]] !== "hidden") {
                        //     actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="调整额配置" '
                        //         + 'onclick="adjustmentConfig(\'' + value +'\')">'
                        //         + '<i class="fa fa-yen" style="font-size: 15px;"></i></a>');
                        // }
                        if ([[${@permission.hasPermi('tms:messageReminderConfig:view')}]] !== "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="消息提醒" '
                                + 'onclick="openMessageReminderConfig(\'' + value +'\',\'' + row.custAbbr +'\')">'
                                + '<i class="fa fa-bell" style="font-size: 15px;"></i></a>');
                        }
                        /**
                        if ([[${@permission.hasPermi('client:client:adjustPaymentDays')}]] !== "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="调整对账期" '
                                + 'onclick="adjustPaymentDays(\'' + value +'\')">'
                                + '<i class="fa fa-credit-card" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('client:client:adjustInvoiceDays')}]] !== "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="调整申请开票期" '
                                + 'onclick="adjustInvoicetDays(\'' + value +'\')">'
                                + '<i class="fa fa-credit-card-alt" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('client:client:adjustCollectionDays')}]] !== "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="调整合同账期" '
                                + 'onclick="adjustCollectionDays(\'' + value +'\')">'
                                + '<i class="fa fa-cc-paypal" style="font-size: 15px;"></i></a>');
                        }**/
                        if ([[${@permission.hasPermi('client:client:adjustBillDate')}]] !== "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="基本配置调整" '
                                + 'onclick="adjustSpecialDate(\'' + value +'\')">'
                                + '<i class="fa fa-list-alt" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('client:client:adjustBillDate')}]] !== "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="线路指导价" '
                                + 'onclick="linePrice(\'' + value +'\')">'
                                + '<i class="fa fa-line-chart" style="font-size: 15px;"></i></a>');
                        }

                        if ([[${@permission.hasPermi('client:custLine:list')}]] !== "hidden") {
                            actions.push(`<a class="btn btn-xs  " href="javascript:void(0)" title="客户线路"
                                 onclick="openCustLine('${value}')">
                                <i class="fa fa-map-signs" style="font-size: 15px;"></i></a>`);
                        }

                        //return actions.join('');
                        return '<a class="" href="javascript:void(0)" onclick="detail(\'' + value + '\')" style="color: #1ab394">详情&nbsp;&nbsp;</a>'+
                            // '<a class="btn btn-xs " href="javascript:void(0)" title="合同"  onclick="contract(\'' + value + '\')"> <i class="fa fa-folder-o" style="font-size: 15px;"></i></a>' +'&nbsp;&nbsp;'+
                            '<div class="btn-group">' +
                            '<div class="dropdownpad" data-toggle="dropdown">' +
                            '<i class="fa fa-angle-down colg"></i>' +
                            '</div>' +
                            '<ul class="dropdown-menu menus">' + actions.join('') +
                            '</ul>' +
                            '</div>';
                    }
                },
                {
                    title: '客户编码/客户简称',
                    field: 'custCode',
                    align: 'left',
                    formatter: function(value, row, index){
                        let htmlText;
                        htmlText=`<div>`+value+`</div>` + `<div>`+row.custAbbr+` <span class='label label-warning pa2' style="border-radius: 50%;">`+$.table.selectDictLabel(customerLevel, row.customerLevel)+`</span></div>`;

                        return htmlText
                    }
                },
                {
                    title: '状态',
                    align: 'center',
                    field: 'checkStatus',
                    formatter: function(value, item, index) {
                        let htmlText = [];

                        if (item.checkStatus == 0) {
                            htmlText.push( '<div class="mb5"><span class="carve carve-warning pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+getValue(item.checkMan)+"<br/>" +getValue(item.checkDate)+'">待审核</span></div>');
                        }else if (item.checkStatus == 1) {
                            htmlText.push( '<div class="mb5"><span class="carve carve-primary pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+getValue(item.checkMan)+"<br/>" +getValue(item.checkDate)+'">审核通过</span></div>');
                        } else if (item.checkStatus == 2) {
                            htmlText.push( '<div class="mb5"><span class="carve carve-danger pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+getValue(item.checkMan)+"<br/>" +getValue(item.checkDate)+'">审核未通过</span></div>');
                        }

                        if (item.isEnabled == 1) {
                            htmlText.push('<span class="enabled-danger " data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+item.disabledTime+'"> </span> 禁用');
                        } else {
                            htmlText.push('<span class="enabled-primary"> </span> 启用');
                        }

                        
                        return htmlText.join("")
                    }
                },
                // {
                //     visible: editFlag == 'hidden' ? false : true,
                //     title: '启用状态',
                //     align: 'center',
                //     formatter: function (value, row, index) {
                //         if (row.isEnabled == 1) {
                //             return '<span class="label label-danger isEnabled"> </span> 禁用';
                //         } else {
                //             return '<span class="label label-primary isEnabled"> </span> 启用';
                //         }
                //         // return statusTools(row);
                //     }
                // },
                {
                    title: '运营公司/运营组',
                    field: 'balaCorp',
                    align: 'left',
                    formatter: function(value, row, index){
                        let htmlText;
                        htmlText = $.table.selectDictLabel(balaCorp, value) + "/" + getValue(row.mgmtDeptName) + "<br/>" + `${getValue(row.opsDeptName)}/${getValue(row.salesDeptName)}`;
                        return htmlText
                    }

                },
                // {
                //     title: '联系人/手机',
                //     field: 'contact',
                //     align: 'left',
                //     formatter: function(value, row, index){
                //         let htmlText;
                //         htmlText= getValue(value)+"<br/>" +getValue(row.phone)
                //         return htmlText
                //     }
                //
                // },
                {
                    title: 'APP联系人/手机',
                    field: 'appDeliContact',
                    align: 'left',
                    formatter: function(value, row, index){
                        let htmlText;
                        htmlText= getValue(value)+"<br/>" +getValue(row.appDeliMobile)
                        return htmlText
                    }
                },

                {
                    title: '对账/申请开票期',
                    field: 'paymentDays',
                    align: 'left',
                    formatter: function(value, row, index){
                        let htmlText=[];
                        if(row.paymentDays){
                            htmlText.push(getValue(row.paymentDays)+`天`);
                        }
                        if(row.invoiceDays){
                            htmlText.push(getValue(row.invoiceDays)+`天`);
                        }

                        return htmlText.join("/")
                    }
                },
                {
                    title: '合同账期',
                    field: 'collectionDays',
                    align: 'left',
                    formatter: function(value, row, index){
                        // let htmlText=[];
                        // if(row.collectionDays){
                        //     htmlText.push(`<div><span class="carve label-inverse pa2 mr5">合同账期</span>`+getValue(row.collectionDays)+`天</div>`);
                        // }
                        // if(row.specialDate){
                        //     htmlText.push(`<div class="mt5"><span class="carve label-inverse pa2 mr5">特殊日期</span>`+getValue(row.specialDate)+`日</div>`);
                        // }
                        if(value){
                            return getValue(value)+`天`
                        }

                        
                    }
                },
                {
                    title: '特殊日期',
                    field: 'specialDate',
                    align: 'left',
                    formatter: function(value, row, index){
                        if(value){
                            return getValue(value)+`日`
                        }
                    }
                },

                {
                    title: '合同到期日',
                    field: 'invalidDate',
                    align: 'left',
                    formatter: function(value, row, index){
                        if(value){
                            if (value == '长期有效') {
                                return '长期有效';
                            } else if(value == '有合同无有效期'){
                                return `<span class="text-danger">有合同无有效期</span>`;
                            }else {
                                let now = new Date();
                                let invalidDate=new Date(Date.parse(value));

                                if (invalidDate < now) {
                                    return `<span class="text-danger">` + value.substr(0,10) +`</span>`;
                                } else {
                                    return value.substr(0,10);
                                }
                            }
                        }else{
                            return `<span class="text-danger">无合同</span>`
                        }
                    }
                },
                // {
                //     title: '合同账期',
                //     field: 'collectionDays',
                //     align: 'left'
                // },
                // {
                //     title: '特殊日期',
                //     field: 'specialDate',
                //     align: 'left'
                // },
                
               

                {
                    title: '异常数量',
                    field: 'customerExpCount',
                    align: 'center',
                    formatter: function(value, row, index) {
                        let htmlText=[];
                        if(value){
                            htmlText.push('<a href="javascript:;" onclick="openCustomerException(\''+row.customerId+'\')"> ' + value + "</a></div>")
                        }
                        return htmlText.join("")
                    }
                },
                {
                    title: '需要指导价/回单',
                    field: 'crtGuidePrice',
                    align: 'left',
                    formatter: function(value, row, index) {
                        let htmlText=[];
                        if(row.crtGuidePrice==1){
                            htmlText.push(`是`)
                        }else{
                            htmlText.push(`否`)
                        }
                        if(row.isNeedReceipt==1){
                            htmlText.push(`是`)
                        }else{
                            htmlText.push(`否`)
                        }
                        return htmlText.join("/")
                    }
                },
                {
                    title: '调整限额',
                    field: 'adjustment',
                    align: 'left',
                    formatter: function(value, row, index) {
                        if (value != null){
                            return value + '元'
                        } 
                    }
                },

                {
                    title: '是否议价',
                    field: 'ifBargain',
                    align: 'left',
                    formatter: function(value, row, index) {
                        if(value==1){
                            return '是';
                        }else{
                            return '否';
                        }
                    }
                },
                {
                    title: '允许无合同下单',
                    field: 'contractNeedType',
                    align: 'left',
                    formatter: function(value, row, index) {
                        if(value==1){
                            return '是';
                        }else{
                            return '否';
                        }
                    }
                },

                // {
                //     title: '证件号',
                //     field: 'businesslicense',
                //     align: 'left',
                // },
                // {
                //     title: '客户级别/证件号',
                //     field: 'customerLevel',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         let htmlText;

                //         htmlText= $.table.selectDictLabel(customerLevel, row.customerLevel)+"<br/>" +getValue(row.businesslicense)

                //         return htmlText

                //     }
                // },
                // {
                //     title: '结算组/结算公司',
                //     field: 'balaDeptName',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         let htmlText;
                //         htmlText= getValue(value)+"<br/>" + $.table.selectDictLabel(balaCorp, row.balaCorp)
                //         return htmlText
                //
                //     }
                // },

                // {
                //     title: '运营组/驻场组',
                //     field: 'salesDeptName',
                //     align: 'left',
                //     formatter: function(value, row, index){
                //         let htmlText;
                //         htmlText= getValue(value)+"<br/>" +getValue(row.stationDeptName)
                //         return htmlText
                //     }
                // },
                // {
                //     title: '运营部',
                //     align: 'left',
                //     field: 'salesName'
                // },
                {
                    title: '结算组',
                    field: 'balaDeptName',
                    align: 'left',
                    formatter: function(value, row, index){
                        let htmlText;
                        htmlText= `${getValue(value)}`
                        return htmlText
                    }

                },


                // {
                //     title: 'APP联系人手机',
                //     field: 'appDeliMobile',
                //     align: 'left'
                // },

                // {
                //     title: '手机',
                //     field: 'phone',
                //     align: 'left'
                // },
                //
                // {
                //     title: '详细地址',
                //     field: 'address',
                //     align: 'left'
                // },
                {
                    title: '来源/性质',
                    field: 'customerSource',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let a =  $.table.selectDictLabel(customerSource, value);
                        let b = $.table.selectDictLabel(enterpriseNature, row.enterpriseNature);
                        let htmlText;
                        htmlText= getValue(a)+"<br/>" +getValue(b)

                        return htmlText
                    }
                },
                // {
                //     title: '企业性质',
                //     field: 'enterpriseNature',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         return $.table.selectDictLabel(enterpriseNature, value);
                //     }
                // },

                {
                    title: '创建人/创建时间',
                    align: 'left',
                    field: 'regUserId',
                    formatter: function(value, row, index){
                        let htmlText;
                        htmlText= getValue(value)+"<br/>" +getValue(row.regDate)
                        return htmlText
                    }
                },
                // {
                //     title: '创建时间',
                //     align: 'left',
                //     field: 'regDate'
                // },
                {
                    title: '修改人/修改时间',
                    align: 'left',
                    field: 'corUserId',
                    formatter: function(value, row, index){
                        let htmlText;
                        htmlText= getValue(value)+"<br/>" +getValue(row.corDate)
                        return htmlText
                    }
                },
                {
                    title: '单据运营组',
                    align: 'left',
                    field: 'invoiceSalesDeptName'
                },
                {
                    field: 'noticeFileUrl',
                    title: '注意事项附件',
                    formatter: function(value, row, index) {
                        var html = ""
                        if(value != null && value != '') {
                            html = `<span><a href='#' onclick="downloadNoticeFile('${row.noticeFileUrl}','${row.noticeFileName}')">${row.noticeFileName}</a></span>`
                        }
                        return html;
                    }
                },

                // {
                //     title: '修改时间',
                //     align: 'left',
                //     field: 'corDate'
                // },

            ]
        };

        $.table.init(options);
        // 初始化省市区
        $.provinces.init("provinceId","cityId","areaId");
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });

        $(".closed").click(function () {
            $('.sm').addClass("hide"); //当前点击的添加样式，兄弟节点删除样式
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
        });

    });


    function downloadNoticeFile(filePath, fileName) {
        const protocol = document.location.protocol; // 确保使用与当前页面相同的协议
        const a = document.createElement('a');
        a.href = protocol + "//" + document.location.host + filePath; // 动态选择协议
        a.download = fileName;
        a.click();
    }

    function addCust() {
        $.modal.openTab("添加客户", ctx + `client/add?sourceType=${sourceType}`);

    }

    function importExcel() {
        var currentId =  'importTpl'
        layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入数据',
            content: $('#' + currentId).html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var index = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#file')[0].files[0]);
                formData.append("updateSupport", $("input[name='updateSupport']").is(':checked'));
                formData.append("sourceType", sourceType);
                $.ajax({
                    url: prefix + "/importData",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code == web_status.WARNING) {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });
    }


    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }
    function selectTemporary() {
        $("#temporary").val(1)
        $.table.search()
    }

    //TODO
    function openCustomerException(customerId){
        var url = prefix + "/to_customer_exception/"+customerId;
        $.modal.openTab("异常" , url);
    }

    /* 客户启停状态显示 */
    function statusTools(row) {
        if (row.isEnabled == 1) {
            return '<i class=\"fa fa-toggle-off text-info fa-2x\" onclick="enable(\'' + row.customerId + '\')"></i> ';
        } else {
            return '<i class=\"fa fa-toggle-on text-info fa-2x\" onclick="disable(\'' + row.customerId + '\')"></i> ';
        }
    }

    /* 客户管理-停用 */
    function disable(customerId) {
        $.modal.confirm("确认要停用客户吗？", function() {
            $.operate.post(prefix + "/changeStatus", { "customerId": customerId, "isEnabled": 1 });
        })
    }

    /* 客户管理启用 */
    function enable(customerId) {
        $.modal.confirm("确认要启用客户吗？", function() {
            $.operate.post(prefix + "/changeStatus", { "customerId": customerId, "isEnabled": 0 });
        })
    }

    /*运营组选择树*/
    function selectSalesDeptTree() {
        var options = {
            title: '部门选择',
            width: "380",
            url: prefix + "/selectDeptTree/61",
            callBack: salesDeptSubmit
        };
        $.modal.openOptions(options);
    }

    function salesDeptSubmit(index, layero) {
        var body = layer.getChildFrame('body', index);
        $("#salesDept").val(body.find('#treeId').val());
        $("#salesDeptName").val(body.find('#treeName').val());
        layer.close(index);
    }

    /*结算组选择树*/
    function selectBalanceDeptTree() {
        var options = {
            title: '部门选择',
            width: "380",
            url: prefix + "/selectDeptTree/65",
            callBack: balanceDeptSubmit
        };
        $.modal.openOptions(options);
    }

    function balanceDeptSubmit(index, layero) {
        var body = layer.getChildFrame('body', index);
        $("#balaDept").val(body.find('#treeId').val());
        $("#balaDeptName").val(body.find('#treeName').val());
        layer.close(index);
    }
    /**
     * 跳转详情画面
     */
    function detail(clientId) {
        var url = prefix + "/detail?clientId="+clientId;
        $.modal.openTab($.table._option.modalName + "详细", url);
    }

    /**
     * 跳转合同信息画面
     * @param id
     */
    var contract = function (id) {
        var url = prefix + "/contract_n?id="+id;
        $.modal.openTab("合同信息", url);
    }

    /**
     * 跳转客户信息修改页面
     * @param id
     */
    function edit(id) {
        var url = prefix + "/edit?clientId="+id;
        $.modal.openTab("客户信息修改", url);
    }

    function editGroup(id) {
        var url = prefix + "/editGroup?clientId="+id;
        $.modal.openTab("客户组别修改", url);
    }

    function reset() {
        $("#balaDept").val("");
        $("#salesDept").val("");
        $("#temporary").val('')
        $.form.reset();
    }

    /**
     *  跳转客户账号页面
     * @param id
     */
    function accountList(id) {
        var url = ctx + "custUser?customerId="+id;
        $.modal.openTab('客户账号',url);
    }
    /**
     *  跳转客户地址页面
     * @param id
     */
    function addressList(id) {
        var url = ctx + "custAddress?customerId="+id;
        $.modal.openTab('客户地址',url);
    }

    /**
     *  审核
     */
    function check(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/check";
        //获取状态
        var checkStatusArr = $.btTable.bootstrapTable('getSelections');
        //循环判断是否能够进行审核
        for(var i=0;i<checkStatusArr.length;i++){
            if(checkStatusArr[i]['checkStatus'] == 1){
                $.modal.alertWarning("通过的记录不能再进行审核操作");
                return;
            }
            var flag = false;
            if(checkStatusArr[i]['checkStatus'] == 2){
                layer.confirm("请审核选中的" + rows.length + "条数据",{
                    btn:["待审核"]
                },function (index, layero) {
                    if(!flag){
                        flag = true;
                        layer.close(index);
                        var data = { "ids": rows.join(),"flag":"0" };
                        $.operate.submit(url, "post", "json", data);
                    }
                })
                return ;
            }
        }
        var flag = false;
        layer.confirm("请审核选中的" + rows.length + "条数据",{
            btn:["通过","不通过"]
        },function (index, layero) {
            if(!flag){
                flag = true;
                layer.close(index);
                var data = { "ids": rows.join(),"flag":"1" };
                $.operate.submit(url, "post", "json", data);
            }
        },function (index) {
            if(!flag){
                flag = true;
                layer.close(index);
                var data = { "ids": rows.join(),"flag":"2" };
                $.operate.submit(url, "post", "json", data);
            }
        });
    }

    /**
     *  跳转货品页面
     * @param id
     */
    function goodList(id) {
        var url = ctx + "custGoods?customerId="+id;
        $.modal.openTab('客户货品',url);
    }

    /**
     *  跳转客服列表
     * @param id
     * @param customerName
     */
    function serviceList(id, customerName) {
        var url = ctx + "tms/customerService/singleCustomer?customerId=" + id + "&customerName=" + customerName;
        $.modal.openTab('[' + customerName + ']绑定客服列表', url);
    }

    /**
     *  跳转客户业务员绑定
     * @param id
     * @param customerName
     */
    function clerkList(id, customerName) {
        var url = ctx + "tms/customerClerk/singleCustomer?customerId=" + id + "&customerName=" + customerName;
        $.modal.openTab('[' + customerName + ']绑定业务员列表', url);
    }

    // 导出客户
    function exportExcel (){
        var confirm = "确定导出客户数据";
        var data = {};
        var data = $.common.formToJSON("role-form");
        //data.params = new Map();
        //data.projectTeam = $.common.join($('#projectTeam').selectpicker('val'));
        $.table.search('role-form', data);
        $.modal.confirm(confirm + $.table._option.modalName + "吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post($.table._option.exportUrl, data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    /**
     * 导入账期
     */
    function importDate() {
        // 导入数据
            var currentId = 'importDate';
            layer.open({
                type: 1,
                area: ['400px', '230px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: '导入' + $.table._option.modalName + '数据',
                content: $('#' + currentId).html(),
                btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
                // 弹层外区域关闭
                shadeClose: true,
                btn1: function(index, layero){
                    var file = layero.find('#datefile').val();
                    if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                        $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                        return false;
                    }
                    var index = layer.load(2, {shade: false});
                    $.modal.disable();
                    var formData = new FormData();
                    formData.append("file", $('#datefile')[0].files[0]);
                    formData.append("updateSupport", $("input[name='updateSupport']").is(':checked'));
                    $.ajax({
                        url: prefix + "/importCustomerDate",
                        data: formData,
                        cache: false,
                        contentType: false,
                        processData: false,
                        type: 'POST',
                        success: function (result) {
                            if (result.code == web_status.SUCCESS) {
                                $.modal.closeAll();
                                $.modal.alertSuccess(result.msg);
                                $.table.refresh();
                            } else if (result.code == web_status.WARNING) {
                                layer.close(index);
                                $.modal.enable();
                                $.modal.alertWarning(result.msg)
                            } else {
                                layer.close(index);
                                $.modal.enable();
                                $.modal.alertError(result.msg);
                            }
                        }
                    });
                }
            });
    }

    /**
     * 第三方费用配置
     */
    // function otherFeeConfig(id) {
    //     $.modal.open("第三方配置", prefix + "/otherFeeConfig?customerId="+id,400,300);
    // }

    /**
     * 调整额配置
     */
    /*function adjustmentConfig(id){
        $.modal.open("调整额配置", prefix + "/adjustmentConfig?customerId="+id,400,300);
    }*/

    /**
     * 调整对账期
     */
    /*function adjustPaymentDays(id){
        $.modal.open("调整对账期", prefix + "/adjustPaymentDays?customerId="+id,400,300);
    }*/

    /**
     * 调整申请开票期
     */
    /*function adjustInvoicetDays(id){
        $.modal.open("调整申请开票期", prefix + "/adjustInvoiceDays?customerId="+id,400,300);
    }*/

    /**
     * 调整合同账期
     */
    /*function adjustCollectionDays(id){
        $.modal.open("调整合同账期", prefix + "/adjustCollectionDays?customerId="+id,400,300);
    }*/

    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: prefix + "/selectTemporaryCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;
                if (result.code == 0 && data != undefined) {
                   console.log(data);
                   $("#temporaryCount").html(data)
                }
            }
        });
    }

    /**
     * 基本配置调整
     */
    function adjustSpecialDate(id){
        $.modal.open("基本配置调整", prefix + "/adjustSpecialDate?customerId="+id,450,720);
    }
    /**
     * 线路指导价
     */
    function linePrice(id){
        var url = prefix + "/toGuidePriceList/"+id;
        $.modal.openTab( "线路指导价", url);
    }

    function openCustLine(id) {
        var url = `${ctx}tms/custLine?clientId=${id}`;
        $.modal.openTab("客户线路", url);
    }

    /**
     * 打开保证金
     */
    function openMargin() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length != 1) {
            $.modal.alertWarning("只能选择一个客户信息");
            return;
        }
        var customerId =  $.table.selectFirstColumns();
        $.modal.openTab("查看保证金", ctx + "tms/margin?customerId="+customerId);
    }

    /**
     * 新增消息提醒配置
     */
    function addMessageReminderConfig() {
        var rows =  $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请选择客户");
            return;
        }
        var custAbbr = $.table.selectColumns("custAbbr").join();
        var customerId = $.table.selectColumns("customerId").join();
        $.modal.openTab("消息提醒配置", ctx + "client/messageReminderConfig/add?custAbbr="+custAbbr+"&customerId="+customerId);
    }

    /**
     * 查看消息提醒配置
     * @param id
     */
    function openMessageReminderConfig(id,custAbbr) {
        $.modal.openTab("["+custAbbr+"]消息提醒", ctx + "client/messageReminderConfig?customerId="+id+"&custAbbr="+custAbbr);
    }

    /**
     * 批量绑定客服
     */
    function batchBindService() {
        var rows =  $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请选择客户");
            return;
        }
        $.modal.openTab('添加客户客服绑定', ctx + "tms/customerService/batchBindService?customerIds=" + rows.join()
            + "&customerName=" + $.table.selectColumns("custAbbr").join())
    }

    /**
     * 批量绑定业务员
     */
    function batchBindClerk() {
        var rows =  $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请选择客户");
            return;
        }
        $.modal.openTab('添加业务员绑定', ctx + "tms/customerClerk/batchBindClerk?customerIds=" + rows.join()
            + "&customerName=" + $.table.selectColumns("custAbbr").join())
    }

    /**
     * 一键见清除离职用户
     */
    function removeLeave() {
        $.modal.confirm("确认一键删除停用用户吗？", function() {
            $.operate.post(prefix + "/clean_employees", {});
        })
    }

    /**
     * 关联运营部
     */
    function relateSalesGroup() {
        var rows =  $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请选择客户");
            return;
        }
        $.modal.open("关联运营部", prefix + "/to_relate_sales_group/"+ rows.join(), 450, 400);
    }

    function exception(params) {
        console.log(params)
    }

    function autoDispatch() {
        var rows =  $.table.selectFirstColumns();
        // layer.open({
        //     type: 2,
        //     area: ['95%', '90%'],
        //     fix: false,
        //     maxmin: true,
        //     shade: 0.3,
        //     title: "自动调度配置",
        //     content: ctx + "autoDispatchConfig/auto_dispatch?customerId=" + rows.join(),
        //     btn: ['关闭'],
        //     shadeClose: true,            // 弹层外区域关闭
        //     // yes: function (index, layero) {
        //     //     var iframeWin = layero.find('iframe')[0];
        //     //     iframeWin.contentWindow.submitHandler(index, layero);
        //     // },
        //     cancel: function (index) {
        //         return true;
        //     }
        // });

        $.modal.openTab("自动调度配置", ctx + "autoDispatchConfig/auto_dispatch?customerId=" + rows.join());
    }
    function miscFeeConfig() {
        var rows =  $.table.selectFirstColumns();
        $.modal.openTab("其他费配置", ctx + "miscFeeConfig?customerId=" + rows.join());
    }

    function changeMgmtDeptId() {
        var mgmtDept = $("#mgmtDeptId").val();
        $.ajax({
            url: ctx + "system/dept/opsDeptByMgmtDeptId",
            type: "post",
            dataType: "json",
            data: {mgmtDeptId: mgmtDept},
            success: function (result) {
                if (result.code == 0 && result.data != undefined) {
                    $("#opsDeptId").empty();
                    $("#opsDeptId").append($('<option>', {
                        value: '',
                        text: '--运营部--'
                    }));

                    if (result.data != null) {
                        $.each(result.data, function (index, option) {
                            $("#opsDeptId").append($('<option>', {
                                value: option.deptId,
                                text: option.deptName
                            }));
                        });
                    }
                }else {
                    $("#opsDeptId").empty();
                    $("#opsDeptId").append($('<option>', {
                        value: '',
                        text: '--运营部--'
                    }));

                }
            }
        });
    }

    function changeOpsDeptId() {
        var opsDept = $("#opsDeptId").val();
        $.ajax({
            url: ctx + "system/dept/opsGroupByOpsDeptId",
            type: "post",
            dataType: "json",
            data: {opsDeptId: opsDept},
            success: function (result) {
                if (result.code == 0 && result.data != undefined) {
                    $("#salesDept").empty();
                    $("#salesDept").append($('<option>', {
                        value: '',
                        text: '--运营组--'
                    }));

                    if (result.data != null) {
                        $.each(result.data, function (index, option) {
                            $("#salesDept").append($('<option>', {
                                value: option.deptId,
                                text: option.deptName
                            }));
                        });
                    }
                }else {
                    $("#salesDept").empty();
                    $("#salesDept").append($('<option>', {
                        value: '',
                        text: '--运营组--'
                    }));
                }
            }
        });

    }

    function changeSalesId() {
        //2a828327651f4e1cbbc798d8c56ec99c 为车队
        // let val = $("#salesId").val();
        // if (val === '2a828327651f4e1cbbc798d8c56ec99c') {
        //     $("#billingType").val("6")
        // }else {
        //     $("#billingType").val("4")
        // }

        var selectedTexts = $("#salesId option:selected").map(function() {
            return $(this).text();
        }).get();

        // 使用逗号分隔多个值
        var deptName = selectedTexts.join(",");

        $.ajax({
            url: ctx + "tms/custSalesTarget/get_sales_dept",
            type: "get",
            dataType: "json",
            data: {parentDeptName: deptName},
            success: function (result) {
                if (result.code == 0 && result.data!=undefined) {
                    $("#salesDept").empty();

                    if (result.data != null) {
                        $.each(result.data, function(index, option) {
                            $("#salesDept").append($('<option>', {
                                value: option.deptId,
                                text: option.deptName
                            }));
                        });


                    }
                }
            }
        });

    }

</script>

</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <!--导入模板 ：
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>-->

                <input type="checkbox" id="updateSupport" name="updateSupport" title="如果客户已经存在，更新这条数据。"> 是否更新已经存在的客户数据
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>

<script id="importDate" type="text/template">
    <form id="importDateForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="datefile" name="file"/>
<!--            <div class="mt10 pt5">-->
<!--                导入模板 ：-->
<!--                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>-->
<!--            </div>-->
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>

</html>
