<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('新增合同价')" />
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .cur{
        cursor: pointer;
    }
    /* .show-div{
        display: block;
    } */
    .tc{
        text-align: center;
    }
    .fixed-table-body{
        overflow: inherit;
    }
    .bootstrap-select.form-control{
        position: initial;
    }

    .flex{
        display: flex;
        align-items:center;
        justify-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 26px;
        text-align: right;
        color: #000000 !important;
        margin-bottom: 0;
    }
    .flex_right{
        line-height: 26px;
        min-width:0;
        flex:1;
    }
    .price-readonly {
        background-color: #f0f0f0 !important;
        color: #888888 !important;
        cursor: not-allowed !important;
        pointer-events: none !important;
    }

</style>
<body>
<div class="form-content">
    <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
        <div class="panel-body">
            <input type="hidden" id="customerId" name="customerId" th:value="${custContractpcVO.customerId}">
            <input type="hidden" id="custContractpcId" name="custContractpcId" th:value="${custContractpcVO.custContractpcId}">

            <div class="row">

            </div>
            <div class="row" id="addr" th:if="${custContractpcVO.billingMethod != 7}">
                <div class="col-md-12 col-xs-12">
                    <div class="flex">
                        <label class="flex_left"> 线路名称：</label>
                        <div class="flex_right">
                            <div class="flex" style="justify-content: flex-start;">
                                <div class="flex mr10">
                                    [[${custContractpcVO.deliName}]]
                                </div>
                                <i class="fa fa-long-arrow-right mr10" style="color: #1ab394;"></i>
                                <div class="flex">
                                    [[${custContractpcVO.arriName}]]
                                </div>
                                <div class="flex ml10" style="margin-left: auto;">
                                    <button type="button" class="btn btn-primary btn-xs" onclick="showCostPriceHistory()">
                                        <i class="fa fa-history"></i> 成本价历史
                                    </button>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row mt10">
                <div class="col-md-5 col-xs-5">
                    <div class="flex">
                        <label class="flex_left">计费方式：</label>
                        <div class="flex_right">
                            <input id="billingMethod" th:value="${custContractpcVO.billingMethod}" type="hidden">
                            <span th:each="dict : ${billingMethod}" th:if="${custContractpcVO.billingMethod}==${dict.value}" th:text="${dict.context}"></span>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 col-xs-4">
                    <div class="flex">
                        <div class="flex_right">
                            <div id="" style="align-items: baseline;">
                                <input type="checkbox" id="isRound" onclick="return false;"
                                       style="transform: scale(1.2); opacity: 1;" />
                                <input name="isRound" value="" type="hidden">
                                <label for="isRound" style="font-size: 1.1em; vertical-align: middle;user-select:none;">总价四舍五入(保留到个位)</label>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <div class="row mt10">
                <div class="col-md-6 col-xs-6">
                    <div class="flex">
                        <label class="flex_left">货品特性：</label>
                        <div class="flex_right">
                            <span th:each="dict : ${goodsCharacter}" th:if="${custContractpcVO.goodsCharacter}==${dict.dictValue}" th:text="${dict.dictLabel}"></span>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xs-6">
                    <div class="flex">
                        <label class="flex_left">货品：</label>
                        <div class="flex_right">
                            <span th:text="${custContractpcVO.goodsName}"></span>
                        </div>
                    </div>
                </div>

            </div>

            <div class="row mt10">
                <div class="col-md-6 col-xs-6">
                    <div class="flex">
                        <label class="flex_left">车长：</label>
                        <div class="flex_right">
                            <span th:each="dict : ${vehicleLength}" th:if="${custContractpcVO.carLen}==${dict.dictValue}" th:text="${dict.dictLabel}"></span>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xs-6">
                    <div class="flex">
                        <label class="flex_left">车型：</label>
                        <div class="flex_right">
                            <span th:each="dict : ${@dict.getType('car_type')}" th:if="${custContractpcVO.carType}==${dict.dictValue}" th:text="${dict.dictLabel}"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt10">
                <div class="col-md-6 col-xs-6">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">是否大件：</label>
                        <div class="flex_right">
                            <span th:if="${custContractpcVO.isOversize == 2}">不区分</span>
                            <span th:if="${custContractpcVO.isOversize == 0}">非大件</span>
                            <span th:if="${custContractpcVO.isOversize == 1}">大件</span>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-xs-6">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">运输方式：</label>
                        <div class="flex_right">
                            <span th:if="${custContractpcVO.isFtl == 0}">零担</span>
                            <span th:if="${custContractpcVO.isFtl == 1}">整车</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt10">
                <div class="col-md-6 col-xs-6">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">是否往返订单：</label>
                        <div class="flex_right">
                            <span th:if="${custContractpcVO.isRoundTrip == 2}">不区分</span>
                            <span th:if="${custContractpcVO.isRoundTrip == 0}">单程</span>
                            <span th:if="${custContractpcVO.isRoundTrip == 1}">往返</span>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row mt10">
                <div class="col-md-3 col-xs-3">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">是否有区间：</label>
                        <div class="flex_right">
                            <span th:if="${custContractpcVO.ifSection == '1'}">有区间</span>
                            <span th:if="${custContractpcVO.ifSection == '0'}">无区间</span>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 col-xs-3">
                    <div class="flex" style="align-items: flex-start;">
                        <label class="flex_left">是否含税：</label>
                        <div class="flex_right">
                            <span th:if="${custContractpcVO.isIncludeTax == 1}">含税</span>
                            <span th:if="${custContractpcVO.isIncludeTax == 0}">不含税</span>

                        </div>
                    </div>
                </div>

                <div class="col-md-3 col-xs-3">
                    <div class="flex">
                        <div class="flex_right">
                            <div id="isKilRoundDiv" style="display: none;align-items: baseline;">
                                <input type="checkbox" id="isKilRound" onclick="return false;"
                                       style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />
                                <label for="isKilRound" style="font-size: 1.1em; vertical-align: middle;user-select:none;">单价*公里数四舍五入</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 col-xs-3">
                    <div class="flex">
                        <div class="flex_right">
                            <div id="isSkipMileageDiv" style="display: none;align-items: baseline;">
                                <input type="checkbox" id="isSkipMileage" onclick="return false;"
                                       style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />
                                <label for="isSkipMileage" style="font-size: 1.1em; vertical-align: middle;user-select:none;">计算价格忽略公里数</label>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="floatDate">
                <div class="row mt10">
                    <div class="col-md-3 col-xs-3">
                        <div class="flex"  style="align-items: center;">
                            <label class="flex_left" style="width: 80px; margin-bottom: 0;">合同价：</label>
                            <div class="flex_right">
                                <input class="form-control"  name="guidingPrice" id="guidingPrice" disabled type="text"
                                       th:value="${custContractpcVO.guidingPrice}"
                                       oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-xs-3">
                        <div class="flex"  style="align-items: center;">
                            <label class="flex_left" style="width: 80px; margin-bottom: 0;">保底价：</label>
                            <div class="flex_right">
                                <input class="form-control"  name="reservePrice" id="reservePrice" type="text" disabled
                                       th:value="${custContractpcVO.reservePrice}"
                                       oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-xs-3">
                        <div class="flex"  style="align-items: center;">
                            <label class="flex_left" style="width: 80px; margin-bottom: 0;">送货费：</label>
                            <div class="flex_right">
                                <input class="form-control"  name="deliveryFee" id="deliveryFee" type="text" disabled
                                       th:value="${custContractpcVO.deliveryFee}"
                                       oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row" style="margin-top: 15px;">
                    <div class="col-md-3 col-xs-3">
                        <div class="flex" style="align-items: center;">
                            <label class="flex_left" style="width: 80px; margin-bottom: 0;">成本价：</label>
                            <div class="flex_right">
                                <input class="form-control"  name="costPrice" id="costPrice" type="text"
                                       th:value="${custContractpcVO.costPrice}"
                                       oninput="$.numberUtil.onlyNumber(this)" min="0" maxlength="10" />
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-xs-3">
                        <div class="flex" style="align-items: center;">
                            <label class="flex_left" style="width: 80px; margin-bottom: 0;">成本票点：</label>
                            <div class="flex_right" style="flex: 1;">
                                <select name="costBillingType" id="costBillingType" class="form-control valid"
                                        th:with="type=${@dict.getType('billing_type')}"
                                        aria-invalid="false" aria-required="true">
                                    <option value=""></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}" th:attr="data-rate=${dict.numVal1}"
                                            th:selected="${custContractpcVO.costBillingType == dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            </div>


            <div class="show-div" id="show-div">
                <table border="0" id="infoTabFour" class="custom-tab table td">
                    <thead>
                    <tr>
<!--                        <th style="width: 5%;" rowspan="2"> <a class="collapse-link add-alink show-alink" style="font-size: 22px;" title="新增" onclick="insertDiv(this,0)">+</a> </th>-->

                        <th style="width: 10%;">区间开始</th>
                        <th style="width: 20%;">自定义区间</th>
                        <th style="width: 10%;">区间结束</th>
                        <th style="width: 10%;">合同价</th>
                        <th style="width: 10%;">成本价</th>
                        <th style="width: 15%;">成本票点</th>
                        <th style="width: 10%;">价格类型</th>
                        <th style="width: 10%;">送货费</th>

                    </tr>

                    </thead>
                    <tbody id="sectionTbody">
                    <tr th:each="payDetail,status:${custContractpcVO.contractpcSectionList}"
                        th:id="|kilTr_${status.index}_${status.index}|">
                        <input type="hidden" th:name="|contractpcSectionList[${status.index}].contractpcSectionId|"
                               th:value="${payDetail.contractpcSectionId}">
                        <td>
                            <input class="form-control" placeholder="区间开始" type="text"
                                   oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                   th:name="|contractpcSectionList[${status.index}].startSection|"
                                   th:value="${payDetail.startSection}"
                                   disabled>
                        </td>
                        <td>
                            <div class="flex">
                                <select th:name="|contractpcSectionList[${status.index}].startOperator|"
                                        disabled class="form-control">
                                    <option value="0" th:selected="${payDetail.startOperator==0}">＜</option>
                                    <option value="1" th:selected="${payDetail.startOperator==1}">≤</option>
                                </select>
                                <div style="margin: 0 10px;">x</div>
                                <select th:name="|contractpcSectionList[${status.index}].endOperator|"
                                        disabled class="form-control">
                                    <option value="2" th:selected="${payDetail.endOperator==2}">＜</option>
                                    <option value="3" th:selected="${payDetail.endOperator==3}">≤</option>
                                </select>
                            </div>

                        </td>

                        <td>
                            <input class="form-control" placeholder="区间结束" type="text" disabled
                                   th:name="|contractpcSectionList[${status.index}].endSection|" th:value="${payDetail.endSection}">
                        </td>
                        <td>
                            <input class="form-control" placeholder="合同价" type="text" disabled
                                   th:name="|contractpcSectionList[${status.index}].guidingPrice|" th:value="${payDetail.guidingPrice}">
                        </td>
                        <td>
                            <input class="form-control" placeholder="成本价" type="text" oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                   th:name="|contractpcSectionList[${status.index}].costPrice|" th:value="${payDetail.costPrice}">
                        </td>
                        <td>
                            <select th:name="|contractpcSectionList[${status.index}].costBillingType|"
                                    class="form-control valid" th:with="type=${@dict.getType('billing_type')}"
                                    aria-invalid="false" aria-required="true">
                                <option value=""></option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}" th:attr="data-rate=${dict.numVal1}"
                                        th:selected="${payDetail.costBillingType == dict.dictValue}"></option>
                            </select>
                        </td>

                        <td>
                            <select  th:name="|contractpcSectionList[${status.index}].isFixedPrice|"
                                     disabled class="form-control">
                                <option value="0" th:selected="${payDetail.isFixedPrice==0}">单价</option>
                                <option value="1" th:selected="${payDetail.isFixedPrice==1}">一口价</option>
                                <option value="2" th:selected="${payDetail.isFixedPrice==2}">起步价</option>
                            </select>
                        </td>
                        <td>
                            <input class="form-control" placeholder="送货费" type="text" disabled
                                   th:name="|contractpcSectionList[${status.index}].deliveryFee|"
                                   th:value="${payDetail.deliveryFee}">
                        </td>

                    </tr>
                    </tbody>
                </table>
            </div>

            <div class="show-div" id="show-div-1">
                <table border="0" class="custom-tab table td">
                    <thead>
                    <tr>
                        <th style="width: 15%;" colspan="2">
                            <span style="display:inline-block;text-align:center">公里(a <= x < b)</span>

                        </th>
                        <th style="width: 10%;">区间开始</th>
                        <th style="width: 15%;">自定义区间</th>
                        <th style="width: 10%;">区间结束</th>
                        <th style="width: 10%;">合同价</th>
                        <th style="width: 10%;">成本价</th>
                        <th style="width: 10%;">成本票点</th>
                        <th style="width: 10%;">价格类型</th>
                        <th style="width: 10%;">送货费</th>

                    </tr>

                    </thead>
                    <tbody id="sectionTbody-1">
                    <div th:each="entry , iterStat: ${groupedSections}">
                        <tr th:each="section, stat : ${entry.value}"
                            th:with="globIndex=${section.params.index}"
                            th:id="|kilTr_${iterStat.index}_${globIndex}|">
                            <input type="hidden" th:name="|contractpcSectionList[${globIndex}].contractpcSectionId|"
                                   th:value="${section.contractpcSectionId}">
                            <td th:if="${stat.index == 0}"
                                th:id="|kilTd_${iterStat.index}|"
                                th:attr="rowspan=${entry.value.size()},data-ind=${entry.value.size()}">
                                <div style="display: flex; align-items: center;">
                                    <span style="display:flex; align-items:center;margin-left: 5px">
                                            <input th:name="|contractpcSectionList[${globIndex}].startKil|"
                                                   th:id="|startKil_${iterStat.index}_${globIndex}|"
                                                   th:value="${#strings.substringBefore(entry.key, '-')}"
                                                   disabled style="margin-right: 5px;" class="form-control" placeholder="始" type="text">
                                            <span style="margin: 0 5px;">-</span>
                                            <input th:name="|contractpcSectionList[${globIndex}].endKil|"
                                                   th:id="|endKil_${iterStat.index}_${globIndex}|"
                                                   th:value="${#strings.substringAfter(entry.key, '-')}"
                                                   disabled class="form-control" placeholder="终" type="text">
                                        </span>
                                </div>

                            </td>
                            <td th:if="${stat.index == 0}">
                                <input name="range" hidden>
                            </td>
                            <td th:if="${stat.index > 0}">
                                <input th:name="|contractpcSectionList[${globIndex}].startKil|"
                                       th:id="|startKil_${iterStat.index}_${globIndex}|"
                                       th:value="${#strings.substringBefore(entry.key, '-')}"
                                       disabled style="margin-right: 5px;" class="form-control" placeholder="始" type="hidden">
                                <input th:name="|contractpcSectionList[${globIndex}].endKil|"
                                       th:id="|endKil_${iterStat.index}_${globIndex}|"
                                       th:value="${#strings.substringAfter(entry.key, '-')}"
                                       disabled class="form-control" placeholder="终" type="hidden">
                            </td>

                            <td>
                                <input class="form-control" placeholder="区间开始" type="text"
                                       oninput="$.numberUtil.onlyNumber(this)" min="0.0" step="0.01" maxlength="10"
                                       th:name="|contractpcSectionList[${globIndex}].startSection|"
                                       th:value="${section.startSection}" disabled>
                            </td>
                            <td>
                                <div class="flex">
                                    <select th:name="|contractpcSectionList[${globIndex}].startOperator|"
                                            disabled class="form-control">
                                        <option value="0" th:selected="${section.startOperator==0}">＜</option>
                                        <option value="1" th:selected="${section.startOperator==1}">≤</option>
                                    </select>

                                    <div style="margin: 0 10px;">x</div>

                                    <select th:name="|contractpcSectionList[${globIndex}].endOperator|"
                                            disabled class="form-control">
                                        <option value="2" th:selected="${section.endOperator==2}">＜</option>
                                        <option value="3" th:selected="${section.endOperator==3}">≤</option>
                                    </select>
                                </div>

                            </td>
                            <td>
                                <input class="form-control" placeholder="区间结束" type="text" disabled
                                       th:name="|contractpcSectionList[${globIndex}].endSection|"
                                       th:value="${section.endSection}">
                            </td>
                            <td>
                                <input class="form-control" placeholder="合同价" type="text" disabled
                                       th:name="|contractpcSectionList[${globIndex}].guidingPrice|"
                                       th:value="${section.guidingPrice}">
                            </td>
                            <td>
                                <input class="form-control" placeholder="成本价" type="text"
                                       oninput="$.numberUtil.onlyNumber(this)" min="0.0" maxlength="10"
                                       th:name="|contractpcSectionList[${globIndex}].costPrice|"
                                       th:value="${section.costPrice}">
                            </td>
                            <td>
                                <select th:name="|contractpcSectionList[${globIndex}].costBillingType|"
                                        class="form-control valid" th:with="type=${@dict.getType('billing_type')}"
                                        aria-invalid="false" aria-required="true">
                                    <option value=""></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}" th:attr="data-rate=${dict.numVal1}"
                                            th:selected="${section.costBillingType == dict.dictValue}"></option>
                                </select>
                            </td>

                            <td>
                                <select th:name="|contractpcSectionList[${globIndex}].isFixedPrice|"
                                        disabled class="form-control">
                                    <option value="0" th:selected="${section.isFixedPrice==0}">单价</option>
                                    <option value="1" th:selected="${section.isFixedPrice==1}">一口价</option>
                                    <option value="2" th:selected="${section.isFixedPrice==2}">起步价</option>
                                </select>
                            </td>
                            <td>
                                <input class="form-control" placeholder="送货费" type="text" disabled
                                       th:name="|contractpcSectionList[${globIndex}].deliveryFee|"
                                       th:value="${section.deliveryFee}">
                            </td>

                        </tr>
                    </div>
                    </tbody>
                </table>
            </div>



        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-fileinput-js" />
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "client/cust_cntractpc";

    var custContractpcVO=[[${custContractpcVO}]]
    var groupedSectionsLength=[[${groupedSectionsLength}]]

    $(function () {
        if(custContractpcVO.ifSection==1){
            $("#checkbox").prop("checked", true);
            $('.floatDate').css("display","none")

            if (custContractpcVO.billingMethod == '8' || custContractpcVO.billingMethod == '9') {
                $('#show-div-1').css("display","block")
                $('#sectionTbody').empty();
            }else {
                $('#show-div').css("display","block");
                $('#sectionTbody-1').empty();
            }
        }else{
            $("#checkbox").prop("checked", false);
            $('.floatDate').css("display","revert")

            $('#show-div').css("display","none")
            $('#show-div-1').css("display","none")

            $('#sectionTbody').empty();
            $('#sectionTbody-1').empty();

        }

        $("#checkbox").change(function(){
            return false
        })

        $('#isIncludeTax').on('change', function() {
            if ($(this).is(':checked')) {
                $(this).val('1');
                $('[name="isIncludeTax"]').val('1');

                changeIsRoundValue(0)
            } else {
                $(this).val('0');
                $('[name="isIncludeTax"]').val('0');

                changeIsRoundValue(1)
            }
        });


        showIsKilRoundDiv()
        if ([[${custContractpcVO.isKilRound}]] == 0) {
            $('#isKilRound').prop('checked', false);
        }else {
            $('#isKilRound').prop('checked', true);
        }

        showIsSkipMileageDiv()
        if ([[${custContractpcVO.isSkipMileage}]] == 0) {
            $('#isSkipMileage').prop('checked', false);
        }else {
            $('#isSkipMileage').prop('checked', true);
        }


        if ([[${custContractpcVO.isRound}]] == 0) {
            $('#isRound').prop('checked', false);
        }else {
            $('#isRound').prop('checked', true);
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-out-quote").serializeArray();
            $.operate.save(ctx + "client/cust_cntractpc/editCostPrice", data);
        }
    }


    function changeIsRoundValue(type) {
        if (type === 1) {
            // 取消选中并禁用复选框
            $('#isRound').prop('checked', false).prop('disabled', true);
            // 移除 onchange 事件处理程序
            $('#isRound').removeAttr('onchange');
            // 阻止用户点击
            $('#isRound').on('click', function(e) {
                e.preventDefault();
            });
            // 添加样式以指示禁用状态
            $('#isRound').css('pointer-events', 'none');
            $('#isRound').parent().css('opacity', '0.5');

            $('input[name="isRound"]').val('0');
        } else if (type === 0) {
            // 启用复选框
            $('#isRound').prop('disabled', false);
            // 移除阻止用户点击的样式和事件处理程序
            $('#isRound').off('click');
            $('#isRound').css('pointer-events', 'auto');
            $('#isRound').parent().css('opacity', '1');
            // 恢复 onchange 事件处理程序
            $('#isRound').attr('onchange', 'changeIsRound()');
        }

    }


    function showIsKilRoundDiv() {
        let billingMethod = $("#billingMethod").val();
        if (billingMethod == 8 || billingMethod == 9) {
            $('#isKilRoundDiv').css('display', 'flex');
        }else {
            $("#isKilRoundDiv").hide()
        }
    }



    function showIsSkipMileageDiv() {
        let billingMethod = $("#billingMethod").val();
        if (billingMethod == 8 || billingMethod == 9) {
            $('#isSkipMileageDiv').css('display', 'flex');
        }else {
            $("#isSkipMileageDiv").hide()
        }
    }

    function showCostPriceHistory() {
        let custContractpcId = $("#custContractpcId").val();
        parent.layer.open({
            type: 2,
            area: ['75%', '80%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "成本价调整记录",
            content: ctx + "client/cust_cntractpc/editCostPrice/log?custContractpcId=" +custContractpcId,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });

    }



</script>
</body>

</html>