<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('审核详情')" />
</head>
<style>
    * {
        font-size: 16px;
        color: #1a1a1a !important;
        border-color: #333333 !important;
    }

    .f18 {
        font-size: 18px;
    }

    .f20 {
        font-size: 18px;
    }

    .f24 {
        font-size: 24px;
    }

    .ml20 {
        margin-left: 20px;
    }

    .panel-body {
        /* padding: 5px 10px 10px; */
        padding: 0;
    }

    /* .panel-body .over>div {
        padding: 5px 10px 10px;
        border-left: 1px solid;
    } */

    .fc66 {
        color: #666;
    }

    .fw {
        font-weight: bold;
    }

    .f16 {
        font-size: 16px;
    }

    .over {
        overflow: hidden;
    }

    .fl {
        float: left;
    }

    .fr {
        float: right;
    }

    .flex {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .flexT {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .flexT .flex_right {
        flex: inherit;
    }

    .flex_left {
        width: 100px;
        line-height: 24px;
        text-align: right;
        margin: 0;
    }

    .flex_right {
        min-width: 0;
        flex: 1;
        line-height: 24px;
    }

    .box_warp {
        background: #eff8ff;
        /* padding: 5px 5px 0; */
        border: 1px #eee solid;
    }

    .row {
        margin: 0;
    }

    .box_warp .row>div {
        padding: 5px 5px 0;
        border-left: 1px solid;
    }

    .fcff5 {
        /* color: #ff5f00 !important; */
        color: red !important;
    }

    .f1ab394 {
        /* color: #ff5f00 !important; */
        color: #1ab394 !important;
    }

    .tr_hover {
        background: #edf6ff;
        color: #5eadff !important;
    }

    .table>thead>tr>th {
        font-weight: normal;
        color: #808080;
    }

    .fc80 {
        color: #808080;
    }

    .label-not {
        background-color: rgba(0, 0, 0, 0) !important;
    }

    .cur {
        cursor: pointer
    }

    .panel-body {
        background-color: #ffffff;
        border-radius: 5px;
    }

    .tooltipBody {
        position: relative;
    }

    /* .tooltipBody:before{ 
        content: " ";
        border-top: 9px solid #cdcdcd;
        border-right: 9px solid transparent;
        border-left: 9px solid transparent;
        transform: rotate(45deg);
        position: absolute;
        bottom: -5px;
        left: 138px
    } 
    .tooltipBody:after{ 
        content: " ";
        border-top: 9px solid #fff;
        border-right: 9px solid transparent;
        border-left: 9px solid transparent;
        transform: rotate(45deg);
        position: absolute; 
        bottom: -4px;
        left: 138px
    }  */


    .ontooltip {
        display: none;

        position: absolute;
        margin-top: -240px;
        z-index: 1;
        border: 1px solid #cdcdcd;
        border-radius: 5px;
    }

    .oncur:hover+.ontooltip {
        display: block;
    }


    .disflex {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap
    }

    .line {
        width: 1px;
        min-height: 20px;
        max-height: 60px;
        border-left: 2px dashed #cdcdcd;
        margin-left: 18px;
    }

    .table>thead>tr>th,
    .table>tbody>tr>td,
    .table-bordered td {
        border-color: #333333 !important;
        /* word-break: break-all; */
    }

    .padt20 {
        padding-top: 20px;
    }

    /* .table-bordered th,
    .panel-group .panel-heading+.panel-collapse>.panel-body {
        border: none !important;
    }

    .table-bordered td:first-child {
        border-left: none !important;
    }

    .table-borderedT {
        border: none !important;
    } */

    .table>tbody+tbody {
        border-width: 1px;
    }

    .wrapper {
        padding: 0;
    }

    .boNo {
        border: 0;
    }

    .bj {
        width: 100%;
        height: 86px;
        /* background: linear-gradient(180deg, rgba(26, 179, 148, 0) 0%, rgba(26, 179, 148, 0.2) 100%); */
        position: relative;
        overflow: hidden;
    }

    .sellout {
        background-color: rgba(208, 35, 35, .12);
        color: #D02323 !important;
        width: 25%;
        height: 50px;
        line-height: 50px;
        text-align: center;
        margin-top: 2px;
        position: absolute;
        left: -8%;
        transform: rotate(-35deg);
        font-size: 22px;
    }

    .selloutT {
        background-color: rgba(0, 108, 221, .12);
        color: #006CDD !important;
        width: 25%;
        height: 50px;
        line-height: 50px;
        text-align: center;
        margin-top: 2px;
        position: absolute;
        left: -8%;
        transform: rotate(-35deg);
        font-size: 22px;
    }

    .tc {
        text-align: center;
    }

    .btnT {
        border-radius: 50PX;
        padding: 3px 6px;
    }

    .ff4 {
        background-color: rgba(255, 79, 0, .1);
        border-color: rgba(255, 79, 0, .4) !important;
        color: #FF4F00 !important;
    }

    .ff4f {
        color: #FF4F00 !important;
    }

    .ab3 {
        background-color: rgba(26, 179, 148, .1);
        border-color: rgba(26, 179, 148, .4) !important;
        color: #1AB394 !important;
    }

    .c-main {
        position: relative;
    }

    .c-main:before {
        content: ' ';
        width: 20px;
        height: 2px;
        background-color: #000000;
        position: absolute;
        top: 24%;
        right: -6%;
    }

    .c-mainT {
        position: relative;
    }

    .c-mainT:before {
        content: ' ';
        width: 20px;
        height: 8px;
        border-top: 2px solid #000000;
        border-bottom: 2px solid #000000;
        /* background-color: #000000; */
        position: absolute;
        top: 20%;
        right: -6%;
    }


    .sp {
        background-color: #314659;
        color: #ffffff !important;
        display: inline-block;
        padding: 0px 4px;
        font-size: 12px;
        border-radius: 50%;
    }

    .dml {
        background-color: #FFFCD3;
        padding: 0 10px;
        box-sizing: border-box;
        border: 1px solid;
        display: inline-block;
        position: relative;
        top: 10px;
    }

    .zr {
        background-color: #FFFCD3;
        padding: 0 10px;
        box-sizing: border-box;
        border: 1px solid;
        text-align: right;
        display: inline-block;
    }

    .bodyTable {
        border-top: 1px solid;
        padding-bottom: 10px;
    }

    .dxImg {
        position: absolute;
        top: -180%;
    }

    .pad10 {
        padding: 10px 15px;
    }

    .flexS {
        display: flex;
        /* align-items: center; */
        justify-content: space-between;
    }

    .h100 {
        height: 60px;
        /* display: flex;
        align-items: center; */
    }

    #computeDml {
        position: relative;
        top: -6px;
    }
    #computeLr {
        position: relative;
        top: -6px;
    }
    

    .pl2 {
        text-align: center;
    }
    .tablebj{
        background-color: #eff3f8 !important;
        /* background: linear-gradient(180deg, #FFFFFF 0%, #BBE2FB 100%); */
    }
    .tooltip-inner{
        color: #ffffff !important;
    }

    .toop{
        width: 20px;
        height: 20px;
        text-align: center;
        background: #1a1a1a;
        border-radius: 50%;
        display: inline-block;
        color: #fff !important;
        line-height: 20px;
        cursor: pointer;
    }
    .picviewer img{
        height: 32px;
        width: 32px;
        object-fit: scale-down;
    }
</style>

<body>
    <form id="form-check" class="form-horizontal" novalidate="novalidate" style="position: relative;">
        <div class="wrapper wrapper-content animated">
            <div class="form-content">
                <div class="">
                    <div class="panel-group">
                        <div class="panel panel-default ">
                            <div class="panel-heading bj">
                                <div class='sellout' th:if="${isSplitInvoice=='1'}">拆单拼货</div>
                                <div class='selloutT' th:if="${isSplitInvoice=='0'}">整装</div>

                                <div class="container-fluid">
                                    <div class="row">
                                        <div class="col-md-2"></div>
                                        <div class="col-md-2 pl2">
                                            <span class="btn btnT btn-default ff4">总应收(元)</span>
                                        </div>

                                        <div class="col-md-2 pl2">
                                            <span class="btn btnT btn-default ab3">总成本(元)</span>
                                        </div>

                                        <div class="col-md-3 pl2">
                                            <span class="btn btnT btn-default ff4" style="margin-right: 30%;">总毛利(元)</span>
                                        </div>
                                        <div class="col-md-1"> </div>
                                        <div class="col-md-2 pl2">
                                            <span class="btn btnT btn-default ff4">不含税利润/利润率</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="container-fluid">
                                    <div class="row" style="margin-top: 10px;">
                                        <div class="col-md-2 h100"></div>
                                        <div class="col-md-2 h100 tc c-main">
                                            <p class="fw" style="color: #FF4F00 !important;font-size: 24px;"
                                                id="receiveNum"></p>
                                        </div>

                                        <div class="col-md-2 h100 tc c-mainT">
                                            <p class="fw" style="color: #1AB394 !important;font-size: 24px;"
                                                id="allocationNum"></p>
                                        </div>

                                        <div class="col-md-4 h100" style="padding-left: 20px;">
                                            <div style="color: #FF4F00 !important;font-size: 24px;">
                                                <span class="fw" style="color: #FF4F00 !important;font-size: 24px;"
                                                    id="profitNum"></span> /
                                                <span class="fw" style="color: #FF4F00 !important;font-size: 24px;"
                                                    id="computeNum"></span>

                                                <span id="computeDml">
                                                    <img src="/img/dml.jpg" title="低毛利"
                                                        style="width: 36px;height: 36px;vertical-align: middle;" />
                                                </span>
                                            </div>
                                        </div>

                                        <div class="col-md-2 h100 tc">
                                            <div style="color: #FF4F00 !important;font-size: 32px;justify-content: space-evenly;" class="flex">
                                                <span class="fw" style="color: #FF4F00 !important;font-size: 24px;"
                                                   id="netProfits"></span> /
                                                <span class="fw" style="color: #FF4F00 !important;font-size: 24px;"
                                                id="netProfitNum"></span>

                                                <span id="computeLr">
                                                    <img src="/img/dlr.png" title="低利润"
                                                        style="width: 36px;height: 36px;vertical-align: middle;" />
                                                </span>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                            </div>

                            <div class="panel-body" th:each="invoiceVO,stat:${entrustAndPayDetailVO.invoiceVOList}">
                                <div class="bodyTable">
                                    <div class="container-fluid">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <p>
                                                    <span class="sp">[[${stat.index+1}]]</span>
                                                    <span style="font-size: 18px;" class="fw"> 发货单
                                                        [[${invoiceVO.invoice.vbillno}]]</span>
                                                        <span class="label label-inverse" style="color: #FFFFFF !important;" th:if="${invoiceVO.invoice.vbillstatus == '8'}">关闭</span>
                                                    <span
                                                        th:if="${invoiceVO.invoice.memo}">[[${invoiceVO.invoice.memo}]]</span>
                                                    <span th:if="${invoiceVO.invoice.ifBilling=='0'}"><img
                                                            src="/img/bkp.png"
                                                            style="width: 100px;vertical-align: middle;" /></span>
                                                    <!-- <span th:else><img src="/img/kp.png"
                                                                style="width: 100px;vertical-align: middle;" /></span> -->
                                                </p>

                                                <!-- <p th:if="${invoiceVO.invoice.memo}">备注：[[${invoiceVO.invoice.memo}]]
                                                </p> -->
                                            </div>
                                            <!-- <div class="col-md-4" style="text-align: right;">
                                                <div class="dml">
                                                    <div style="display: inline-block;"
                                                        th:if="${invoiceVO.receiveTotal} != 0">
                                                        <div
                                                            th:if="${invoiceVO.profit}/${invoiceVO.receiveTotal}<=0.15">
                                                            <img src="/img/dml.jpg" title="低毛利"
                                                                style="width: 50px;height: 50px;vertical-align: middle;" />
                                                        </div>
                                                    </div>


                                                    <div style="display: inline-block;vertical-align: middle">
                                                        <p style="color: #333333 !important;font-size: 16px;">毛利(元)/毛利率
                                                        </p>
                                                        <p class="fw" style="color: #FF4F00 !important;font-size: 20px;">
                                                            <span class="fw" style="color: #FF4F00 !important;font-size: 20px;">[[${invoiceVO.profit}]]</span>/

                                                            <span class="fw" style="color: #FF4F00 !important;font-size: 20px;"
                                                                th:text="${invoiceVO.receiveTotal==0}?'100%':''"></span>
                                                                <span class="fw" style="color: #FF4F00 !important;font-size: 20px;" th:id="'id_'+${stat.index}"
                                                                th:if="invoiceVO.receiveTotal!=0"></span>
                                                        </p>
                                                    </div>

                                                </div>
                                            </div> -->

                                        </div>
                                    </div>

                                    <div class="container-fluid mt10">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="fixed-table-body table-responsive table_hover">
                                                    <table class="custom-tab tab table table-bordered table-borderedT">
                                                        <thead class="tablebj">
                                                            <tr>
                                                                <th style="width: 10%;">要求提货日期</th>
                                                                <th style="width: 10%;">客户简称</th>
                                                                <th style="width: 14%;">提货方<span class="ff4f">➨</span>到货方</th>
                                                                <th style="width: 10%;">车型/车长</th>
                                                                <th style="width: 16%;">货量</th>
                                                                <th style="width: 10%;">计价方式</th>
                                                            <!--    <th style="width: 8%;">回单照片</th>-->
                                                                <th style="width: 6%;">运费</th>
                                                                <th style="width: 6%;">在途</th>
                                                                <th style="width: 7%;">总应收</th>
                                                                <th style="width: 11%;">总应收(不含税)</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <div>
                                                                <tr>
                                                                        <td class="f20 fw"
                                                                            th:text="${#dates.format(invoiceVO.invoice.reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}">
                                                                        </td>
                                                                        <td class="f20 fw"
                                                                            th:text="${invoiceVO.invoice.custAbbr}">
                                                                        </td>

                                                                        <td class="f20 fw">
                                                                            [[${invoiceVO.invoice.deliAddrName}]]
                                                                            <!-- [[${invoiceVO.invoice.deliProName}]][[${invoiceVO.invoice.deliCityName}]][[${invoiceVO.invoice.deliAreaName}]] -->
                                                                            <span class="ff4f">➨</span>
                                                                            <!-- [[${invoiceVO.invoice.arriProName}]][[${invoiceVO.invoice.arriCityName}]][[${invoiceVO.invoice.arriAreaName}]] -->
                                                                            [[${invoiceVO.invoice.arriAddrName}]]
                                                                        </td>
                                                                        <td>
                                                                            [[${invoiceVO.invoice.carLenName}]]米[[${invoiceVO.invoice.carTypeName}]]
                                                                        </td>
                                                                        <td>
                                                                            [[${invoiceVO.goodsName}]]
                                                                            <span
                                                                                th:if="${invoiceVO.invoice.numCount}!=0">
                                                                                [[${invoiceVO.invoice.numCount}+'件|']]</span>
                                                                            <span
                                                                                th:if="${invoiceVO.invoice.weightCount}!=0">
                                                                                [[${invoiceVO.invoice.weightCount}+'吨|']]</span>
                                                                            <span
                                                                                th:if="${invoiceVO.invoice.volumeCount}!=0">
                                                                                [[${invoiceVO.invoice.volumeCount}+'m³']]</span>
                                                                            <!-- [[${invoiceVO.invoice.numCount}+'件|'+${invoiceVO.invoice.weightCount}+'吨|'+${invoiceVO.invoice.volumeCount}+'m³']] -->
                                                                        </td>
                                                                        <!-- <td th:rowspan="${receiveStat.size}">
                                                                    [[${invoiceVO.goodsName}]]</td>
                                                                <td th:rowspan="${receiveStat.size}"
                                                                    th:text="${invoiceVO.invoice.numCount}+'件|'+${invoiceVO.invoice.weightCount}+'吨|'+${invoiceVO.invoice.volumeCount}+'m³'">
                                                                </td> -->
                                                                    <td>
                                                                        [[${invoiceVO.billingMethodStr}]]
                                                                    </td>
                                                                       <!-- <td>
                                                                            <div class="picviewer">
                                                                                <div  style="display:inline-block"
                                                                                    th:if="${not #lists.isEmpty(invoiceVO.receiptPic)}"
                                                                                    th:each="pic:${invoiceVO.receiptPic}">
                                                                                    <img style="height:32px"
                                                                                        th:src="@{${pic.filePath}}" />
                                                                                </div>
                                                                            </div>
                                                                        </td>-->

                                                                    <td class="fw ff4f f20 ter">
                                                                        [[${invoiceVO.receiveDetailVOTotal.transFeeFreight}]]
                                                                    </td>
                                                                    <td>
                                                                        <!-- [[${invoiceVO.receiveDetailVOTotal.transFeeOnWay}]] -->
                                                                        <div th:each="item:${invoiceVO.receiveDetailVOList}" th:if="${item.freeType == '1'}">
                                                                            <span th:each="costTypeOnWay:${@dict.getType('cost_type_on_way')}"
                                                                                th:if="${item.costTypeOnWay==costTypeOnWay.dictValue}" th:text="${costTypeOnWay.dictLabel}"></span>
                                                                            <span class="fw ff4f f20 ter" th:text="${item.transFeeCount}"></span>
                                                                            <i class="fa fa-question-circle" data-toggle="tooltip"
                                                                               style="font-size: 15px" data-html="true" data-container="body"
                                                                               th:title="${item.memo}"></i>
                                                                        </div>
                                                                    </td>
                                                                    <td class="fw ff4f f20 ter">
                                                                        [[${invoiceVO.receiveDetailVOTotal.transFeeCount}]]
                                                                    </td>
                                                                    <td class="fw ff4f f20 ter">
                                                                        [[${ invoiceVO.netProfitsDetail.ys != null ? #numbers.formatDecimal(invoiceVO.netProfitsDetail.ys,1,2) : 0}]]
                                                                    </td>
                                                                </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            <!-- <div class="col-md-1" style="text-align: right;">
                                                <div class="zr">
                                                    <p style="color: #333333 !important;font-size: 15px;">应收总金额(元)</p>
                                                    <p class="fw" style="color: #FF4F00 !important;font-size: 20px;">[[${invoiceVO.receiveTotal}]]</p>
                                                </div>
                                            </div> -->

                                        </div>
                                    </div>

                                    <div class="container-fluid mt10">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="fixed-table-body table-responsive table_hover">
                                                    <table class="custom-tab tab table table-bordered">
                                                        <thead class="tablebj">
                                                            <tr>
                                                                <th style="width: 14%;">运单号</th>
                                                                <th style="width: 15%;">提货地址<span class="f1ab394">➨</span>到货地址</th>
                                                                <th style="width: 11%;">承运商(联系方式)</th>
                                                                <th style="width: 6%;">车牌号</th>
                                                                <!-- <th>应付单号</th>
                                                                <th>应付单状态</th> -->
                                                                <th style="width: 13%;">货量（计价方式）</th>
                                                                <!-- <th style="width: 7%;"></th> -->
                                                                <th style="width: 7%;">回单照片</th>
                                                                <th style="width: 7%;">费用类型</th>
                                                                <th style="width: 5%;">应付</th>
                                                                <th style="width: 4%;">三方</th>
                                                                <th style="width: 7%;">总应付</th>
                                                                <th style="width: 12%;">总应付(不含税)</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody th:id="'allocationList'+${stat.index}">
                                                            
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            <!-- <div class="col-md-4">
                                                <div class="fixed-table-body table-responsive table_hover">
                                                    <table class="custom-tab tab table table-bordered">
                                                        <thead style="background: #F7F8FA">
                                                            <tr>
                                                                <th>费用单号</th>
                                                                <th>件数/体积(m³)/重量(吨)</th>
                                                                <th>费用类型</th>
                                                                <th>三方费用</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr th:each="otherFee :${invoiceVO.otherFeeList}">
                                                                <td th:text="${otherFee.vbillno}"></td>
                                                                <td></td>
                                                                <td th:each="dict:${@dict.getType('cost_type_on_way')}"
                                                                    th:if="${dict.dictValue} == ${otherFee.feeType}"
                                                                    th:text="${dict.dictLabel}">
                                                                </td>
                                                                <td th:text="${otherFee.feeAmount}"></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div> -->
                                            <!-- <div class="col-md-1" style="text-align: right;">
                                                <div class="zr">
                                                    <p style="color: #333333 !important;font-size: 15px;">应付总金额(元)</p>
                                                    <p class="fw" style="color: #1AB394 !important;font-size: 20px;">
                                                        [[${invoiceVO.allocationTotal+invoiceVO.otherFeeTotal}]]</p>
                                                </div>
                                            </div> -->

                                        </div>
                                    </div>
                                </div>


                            </div>

                        </div>
                    </div>

                    <div class="panel-group">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h5 class="panel-title">
                                    <a data-toggle="collapse" data-parent="#accordionThree"
                                        href="tabs_panels.html#collapseOne">审核信息</a>
                                </h5>
                            </div>

                            <div class="panel-body">
                                <div style="padding-bottom: 10px;">
                                    <div class="container-fluid mt10">
                                        <div class="row">

                                            <!-- <div class="col-md-4" style="padding-right: 0;">
                                                <div class="fixed-table-body table-responsive table_hover">
                                                    <table class="custom-tab tab table table-bordered table-borderedT">
                                                        <thead style="background: #f4f6f7;">
                                                            <tr>
                                                                <th style="width: 50%;">车主</th>
                                                                <th style="width: 50%;">车辆</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td rowspan="2" class="fw" th:text="|${entrustAndPayDetailVO.carrierName}|"></td>
                                                                <td rowspan="2" class="fw" th:utext="|${entrustAndPayDetailVO.carNo==null?'':entrustAndPayDetailVO.carNo}/${entrustAndPayDetailVO.carLenName==null?'':entrustAndPayDetailVO.carLenName}米/${entrustAndPayDetailVO.carTypeName==null?'':entrustAndPayDetailVO.carTypeName}|"></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div> -->

                                            <div class="col-md-12" style="padding-left: 0;">
                                                <div class="fixed-table-body table-responsive table_hover">
                                                    <table class="custom-tab tab table table-bordered table-borderedS">

                                                        <tbody>
                                                            <!-- <tr>
                                                                <td style="writing-mode: vertical-lr;text-align: center;width: 2%;"
                                                                    class="tablebj" rowspan="9">
                                                                    审核信息
                                                                </td>
                                                            </tr> -->
                                                            <tr class="tablebj">
                                                                <td style="width: 14%;">承运商</td>
                                                                <td style="width: 10%;">车辆</td>
                                                                <td style="width: 9%;">应付单号</td>
                                                                <td style="width: 2%;">状态</td>
                                                                <td style="width: 8%;">费用类型</td>
                                                                <td style="width: 6%;">油卡</td>
                                                                <td style="width: 7%;">账户名称</td>
                                                                <td style="width: 7%;">银行卡</td>
                                                                <td style="width: 6%;">金额</td>
                                                                <td style="width: 6%;">总金额</td>

                                                            </tr>
                                                            <tr th:each="payDetail,payDetailStat:${entrustAndPayDetailVO.payDetailList}"
                                                                th:classappend="${payDetail.vbillno == payDetailVbillno} ? 'tr_hover' : '' ">

                                                                <input type="hidden" th:id="payDetailSize"
                                                                    th:value="${payDetailStat.size}">

                                                                <td th:if="${payDetailStat.index==0}"
                                                                    th:rowspan="${payDetailStat.size}" class="fw">
                                                                    <span th:text="|${entrustAndPayDetailVO.carrierName+'/'+entrustAndPayDetailVO.carrier.phone}|"></span>
                                                                    
                                                                    <span class="label label-warning" th:if="${entrustAndPayDetailVO.carrier.carrType == '2' && entrustAndPayDetailVO.carrier.ifHasBill == 1}">合约</span>

                                                                    <span class="label label-coral" th:if="${entrustAndPayDetailVO.carrier.carrType == '2' && entrustAndPayDetailVO.carrier.ifHasBill == 0 && entrustAndPayDetailVO.bankAccount != entrustAndPayDetailVO.driverName}">代收</span>
                                                                    <span th:each="pic:${entrustAndPayDetailVO.sysUploadFiles}" class="picviewer">
                                                                        <img style="height:48px"
                                                                             th:src="@{${pic.filePath}}" />
                                                                    </span>
                                                                </td>
                                                                <td th:if="${payDetailStat.index==0}"
                                                                    th:rowspan="${payDetailStat.size}" class="fw"
                                                                    th:text="|${entrustAndPayDetailVO.carTypeName==null?'':entrustAndPayDetailVO.carTypeName}/
                                                                ${entrustAndPayDetailVO.carLenName==null?'':entrustAndPayDetailVO.carLenName}米/
                                                                ${entrustAndPayDetailVO.carNo==null?'':entrustAndPayDetailVO.carNo}/
                                                                ${entrustAndPayDetailVO.driverName==null?'':entrustAndPayDetailVO.driverName}|">

                                                                </td>

                                                                <td style="border-left: none !important;"
                                                                    th:id="|payDetailVbillno${payDetailStat.index}|">
                                                                    <span> [[ ${payDetail.vbillno} ]] </span>
                                                                    <span class="label label-warning cur oncur"
                                                                        th:if="${payDetail.isAdjust} == 1">调</span>

                                                                    <div class="ontooltip"
                                                                        th:if="${payDetail.isAdjust} == 1">
                                                                        <div class="panel-body tooltipBody" th:if="${payDetail.adjustRecord!=null}">
                                                                            <div class="padt5">

                                                                                <div class="over disflex">
                                                                                    <div class="fl">
                                                                                        <div class="round th">
                                                                                            <img th:src="@{/img/pep.png}"
                                                                                                style="width: 30px;height: 30px">
                                                                                        </div>
                                                                                        <div class="line"></div>
                                                                                    </div>
                                                                                    <div class="fl">
                                                                                        <div class="fl"
                                                                                            style="padding: 5px 10px;box-sizing: border-box;">
                                                                                            <div style="display: inline-block"
                                                                                                class="fc00a">
                                                                                                [[${payDetail.adjustRecord.regUserName}]]
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="fl ml20">
                                                                                            <div
                                                                                                style="padding: 5px 10px;box-sizing: border-box;width: 10em">
                                                                                                [[${payDetail.adjustRecord.memo}]]
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="fl"
                                                                                        style="width: 8em;padding: 5px 10px;box-sizing: border-box;">
                                                                                        <div class="fc80">
                                                                                            [[${#dates.format(payDetail.adjustRecord.regDate,
                                                                                            'yyyy-MM-dd
                                                                                            HH:mm:ss')}]]
                                                                                        </div>
                                                                                    </div>

                                                                                </div>

                                                                                <div class="over disflex">
                                                                                    <div class="fl">
                                                                                        <div class="round th">
                                                                                            <img th:src="@{/img/pep.png}"
                                                                                                style="width: 30px;height: 30px">
                                                                                        </div>
                                                                                        <div class="line"></div>
                                                                                    </div>
                                                                                    <div class="fl">
                                                                                        <div class="fl"
                                                                                            style="padding: 5px 10px;box-sizing: border-box;">
                                                                                            <div style="display: inline-block"
                                                                                                class="fc00a">
                                                                                                [[${payDetail.adjustRecord.firstCheckUserName}]]
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="fl ml20">
                                                                                            <div
                                                                                                style="padding: 5px 10px;box-sizing: border-box;width: 10em">
                                                                                                [[${payDetail.adjustRecord.firstCheckMemo}]]
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="fl"
                                                                                        style="width: 8em;padding: 5px 10px;box-sizing: border-box;">
                                                                                        <div class="fc80">
                                                                                            [[${#dates.format(payDetail.adjustRecord.firstCheckDate,
                                                                                            'yyyy-MM-dd
                                                                                            HH:mm:ss')}]]
                                                                                        </div>
                                                                                    </div>

                                                                                </div>

                                                                                <div class="over disflex">
                                                                                    <div class="fl">
                                                                                        <div class="round th">
                                                                                            <img th:src="@{/img/pep.png}"
                                                                                                style="width: 30px;height: 30px">
                                                                                        </div>
                                                                                        <div class="line"></div>
                                                                                    </div>
                                                                                    <div class="fl">
                                                                                        <div class="fl"
                                                                                            style="padding: 5px 10px;box-sizing: border-box;">
                                                                                            <div style="display: inline-block"
                                                                                                class="fc00a">
                                                                                                [[${payDetail.adjustRecord.secondCheckUserName}]]
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="fl ml20">
                                                                                            <div
                                                                                                style="padding: 5px 10px;box-sizing: border-box;width: 10em">
                                                                                                [[${payDetail.adjustRecord.secondCheckMemo}]]
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="fl"
                                                                                        style="width: 8em;padding: 5px 10px;box-sizing: border-box;">
                                                                                        <div class="fc80">
                                                                                            [[${#dates.format(payDetail.adjustRecord.secondCheckDate,
                                                                                            'yyyy-MM-dd
                                                                                            HH:mm:ss')}]]
                                                                                        </div>
                                                                                    </div>

                                                                                </div>

                                                                                <div class="over disflex">
                                                                                    <div class="fl">
                                                                                        <div class="round th">
                                                                                            <img th:src="@{/img/pep.png}"
                                                                                                style="width: 30px;height: 30px">
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="fl">
                                                                                        <div class="fl"
                                                                                            style="padding: 5px 10px;box-sizing: border-box;">
                                                                                            <div style="display: inline-block"
                                                                                                class="fc00a">
                                                                                                [[${payDetail.adjustRecord.thirdCheckUserName}]]
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="fl ml20">
                                                                                            <div
                                                                                                style="padding: 5px 10px;box-sizing: border-box;width: 10em;">
                                                                                                [[${payDetail.adjustRecord.thirdCheckMemo}]]
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="fl"
                                                                                        style="width: 8em;padding: 5px 10px;box-sizing: border-box;">
                                                                                        <div class="fc80">
                                                                                            [[${#dates.format(payDetail.adjustRecord.thirdCheckDate,
                                                                                            'yyyy-MM-dd
                                                                                            HH:mm:ss')}]]
                                                                                        </div>
                                                                                    </div>

                                                                                </div>


                                                                            </div>

                                                                        </div>
                                                                    </div>

                                                                </td>
                                                                <td
                                                                    th:id="|payDetailVbillstatus${payDetailStat.index}|">
                                                                    <span class="label label-default"
                                                                        th:if="${payDetail.vbillstatus} == 0">新建</span>
                                                                    <span class="label label-warning"
                                                                        th:if="${payDetail.vbillstatus} == 1">已确认</span>
                                                                    <span class="label label-coral"
                                                                        th:if="${payDetail.vbillstatus} == 2">已对账</span>
                                                                    <span class="label label-info"
                                                                        th:if="${payDetail.vbillstatus} == 3">部分核销</span>
                                                                    <span class="label label-success"
                                                                        th:if="${payDetail.vbillstatus} == 4">已核销</span>
                                                                    <span class="label label-inverse"
                                                                        th:if="${payDetail.vbillstatus} == 5">关闭</span>
                                                                    <span class="label label-success"
                                                                        th:if="${payDetail.vbillstatus} == 6">已申请</span>
                                                                    <span class="label label-info"
                                                                        th:if="${payDetail.vbillstatus} == 7">核销中</span>
                                                                    <span class="label label-default"
                                                                        th:if="${payDetail.vbillstatus} == 8">审核中</span>
                                                                    <span class="label label-default"
                                                                        th:if="${payDetail.vbillstatus} == 9">复核通过</span>
                                                                </td>
                                                                
                                                                <td th:each="costTypeFreight:${@dict.getType('cost_type_freight')}"
                                                                    th:id="|freeType${payDetailStat.index}|"
                                                                    th:if="${payDetail.freeType == '0' and costTypeFreight.dictValue==payDetail.costTypeFreight}"
                                                                    >
                                                                    <span th:text="${costTypeFreight.dictLabel}"></span>

                                                                    <span class="toop" th:if="${payDetail.memo!=null&&payDetail.memo!=''}" th:title="${payDetail.memo}"
                                                                        data-toggle="tooltip" data-placement="right">?</span>
                                                                </td>
                                                                <td th:each="costTypeOnWay:${@dict.getType('cost_type_on_way')}"
                                                                    th:id="|freeType${payDetailStat.index}|"
                                                                    th:if="${payDetail.freeType == '1' and costTypeOnWay.dictValue==payDetail.costTypeOnWay}"
                                                                    >
                                                                    <span th:text="${costTypeOnWay.dictLabel}"></span>
                                                                    <span class="toop" th:if="${payDetail.memo!=null&&payDetail.memo!=''}" th:title="${payDetail.memo}"
                                                                        data-toggle="tooltip" data-placement="right">?</span>
                                                                </td>

                                                                <td th:text="${payDetail.oilCardNumber==null?'':payDetail.oilCardNumber}"
                                                                    th:id="|oilCardNumber${payDetailStat.index}|">
                                                                </td>
                                                                <td th:text="${payDetail.recAccount==null?'':payDetail.recAccount}"
                                                                th:id="|recAccount${payDetailStat.index}|"></td>

                                                                <td th:text="${payDetail.recCardNo==null?'':payDetail.recCardNo}"
                                                                    th:id="|recCardNo${payDetailStat.index}|"></td>
                                                              

                                                                <td class="f1ab394 fw f20 ter"
                                                                    th:text="${payDetail.transFeeCount+payDetail.taxAmount }"
                                                                    th:id="|transFeeCount${payDetailStat.index}|">
                                                                    xxx
                                                                </td>
                                                                <td class="f1ab394 fw f20 ter"
                                                                    th:if="${payDetailStat.index==0}"
                                                                    th:rowspan="${payDetailStat.size}"
                                                                    th:id="|transFeeCount_${payDetailStat.index}|">
                                                                </td>
                                                            </tr>

                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                            </div>

                            <!-- <div class="panel-body" th:each="businessCheck :${businessCheckList}">
                                <div class="pad10">
                                    <div class="over">
                                        <div class="fl">
                                            <div class="round th">
                                                <img th:src="@{/img/pep.png}" style="width: 40px;height: 40px">
                                            </div>
                                            <div class="line"></div>
                                        </div>
                                        <div class="fr over ml20" style="width: calc(100% - 71px)">
                                            <div class="fl" style="width: 140px">
                                                <div class="">
                                                    <div style="display: inline-block" class="fc00a"
                                                        th:text="${businessCheck.checkMan}"></div>
                                                    <div style="display: inline-block" class="fc00a">
                                                        <span class="label label-default"
                                                            th:if="${businessCheck.checkStatus == 0}">待审核</span>
                                                        <span class="label label-primary"
                                                            th:if="${businessCheck.checkStatus == 1}">审核通过</span>
                                                        <span class="label label-warning"
                                                            th:if="${businessCheck.checkStatus == 2}">审核不通过</span>
                                                    </div>
                                                </div>
                                                <div class="mt10 fc80"
                                                    th:text="${#dates.format(businessCheck.checkDate, 'yyyy-MM-dd HH:mm:ss')}">
                                                </div>
                                            </div>
                                            <div class="fl ml20" style="width: 200px">
                                                <div style="padding: 10px 10px;box-sizing: border-box;width: 100%"
                                                    th:text="${businessCheck.memo}"></div>
                                            </div>

                                            <div class="fl ml20" style="width: calc(50% - 140px)">
                                                <div class="flex">
                                                    <label class="flex_left">支付凭证：</label>
                                                    <div class="flex_right">
                                                        <span th:each="pic:${sysUploadFiles}" class="picviewer">
                                                            <img style="height:48px"
                                                                th:src="@{${pic.filePath}}" />
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            
                                        </div>
                                    </div>
                                </div>
                            </div> -->

                            <input name="payDetailId" type="hidden" th:value="${payDetailId}">

                            <div class="panel-body" th:each="payDetail :${payDetail}">
                                <div class="pad10">
                                    <div class="over">
                                        <div class="fl">
                                            <div class="round th">
                                                <img th:src="@{/img/pep.png}" style="width: 40px;height: 40px">
                                            </div>
                                        </div>
                                        <div class="fr over ml20" style="width: calc(100% - 71px)">
                                            <div class="fl" style="width: 140px">
                                                <div class="">
                                                    <div style="display: inline-block" class="fc00a"
                                                        th:text="${payDetail.checkUserName}"></div>
                                                    <div style="display: inline-block" class="fc00a">
                                                        <span class="label label-default"
                                                        th:if="${payDetail.checkStatus == null}">暂无复核</span>
                                                        <span class="label label-default"
                                                            th:if="${payDetail.checkStatus == 0}">待审核</span>
                                                        <span class="label label-primary"
                                                            th:if="${payDetail.checkStatus == 1}">复核通过</span>
                                                        <span class="label label-warning"
                                                            th:if="${payDetail.checkStatus == 2}">复核不通过</span>
                                                        <span class="label label-warning"
                                                            th:if="${payDetail.checkStatus == 3}">反复核</span>
                                                    </div>
                                                </div>
                                                <div class="mt10 fc80"
                                                    th:text="${#dates.format(payDetail.checkDate, 'yyyy-MM-dd HH:mm:ss')}">
                                                </div>
                                            </div>
                                            <div class="fl ml20" style="width: 200px">
                                                <div style="padding: 10px 10px;box-sizing: border-box;width: 100%"
                                                    th:text="${payDetail.checkMemo}"></div>
                                            </div>

                                            <div class="fl ml20" style="width: calc(50% - 140px)">
                                                <div class="flex">
                                                    <label class="flex_left">申请备注：</label>
                                                    <div class="flex_right" th:text="${payDetail.applyMemo}"></div>
                                                </div>
                                            </div>

                                            <div class="fl ml20" style="width: calc(50% - 140px)">
                                                <div class="flex">
                                                    <label class="flex_left">支付凭证：</label>
                                                    <div class="flex_right">
                                                        <span th:each="pic:${sysUploadFiles}" class="picviewer">
                                                            <img style="height:48px"
                                                                th:src="@{${pic.filePath}}" />
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                       
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>

                    <div class="panel-group">
                        <div class="panel panel-default">
                            <div class="panel-body">
                                <div>
                                    <div class="container-fluid">

                                        <div class="row">
                                            <div class="col-md-12" style="padding-left: 0;">
                                                <div class="fixed-table-body table-responsive table_hover">
                                                    <table style="border: 0 !important;"
                                                        class="custom-tab tab table table-bordered table-borderedS">
                                                        <tbody>
                                                            <tr>
                                                                <td colspan="2" style="border: 0 !important;">
                                                                    <textarea id="memo" name="memo" class="form-control"
                                                                        type="text" style="border: 0;"
                                                                        placeholder="低毛利、负毛利、退回请填写备注" maxlength="250"
                                                                        required="" rows="2"
                                                                        aria-required="true"></textarea>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-offset-5 col-sm-10">
                            <button type="button" class="btn btn-sm btn-primary"
                                th:if="${type == 'check' || type == 'review'}" onclick="submitApprove()"><i
                                    class="fa fa-check"></i>通 过
                            </button>&nbsp;
                            <button type="button" class="btn btn-sm btn-danger"
                                th:if="${type == 'check' || type == 'review'}" onclick="submitBack()"><i
                                    class="fa fa-reply-all"></i>不通过
                            </button>
                            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i
                                    class="fa fa-close"></i>关 闭
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        </div>
    </form>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        /**
         * 校验
         */
        $("#form-invoice-unconfirm").validate({

        });

        //应付明细
        var payDetailVbillno = [[${ payDetailVbillno }]];

        var payDetailSize = $("#payDetailSize").val() - 0;
        //初始化
        $(function () {
            console.log( [[${businessCheckList}]] )
            let invoiceVT = [[${ entrustAndPayDetailVO.invoiceVOList }]]
              //总应收(元)
            let receiveNum = 0
              //总成本(元)
            let allocationNum = 0
              //总毛利(元)
            let profitNum = 0;
           
            //总净利润
            let netProfits=0;
           //总不含税应收费用
           let NoPayableNUm = 0;

            invoiceVT.forEach((res, index) => {
                receiveNum += res.receiveDetailVOTotal.transFeeCount
                allocationNum += res.allocationTotal+res.otherFeeTotal
                //净利润
                if(res.netProfits){
                    netProfits += res.netProfits;
                }else{
                    netProfits += 0;
                }
                //不含税应收费用
                if(res.netProfitsDetail.ys){
                    NoPayableNUm += res.netProfitsDetail.ys;
                }else{
                    NoPayableNUm += 0;
                }
                //不含税应付费用
                let NoPayable =0;
                if(res.netProfitsDetail.ptf){
                    NoPayable += res.netProfitsDetail.ptf;
                }else{
                    NoPayable += 0;
                }
                if(res.netProfitsDetail.dsf){
                    NoPayable += res.netProfitsDetail.dsf;
                }else{
                    NoPayable += 0;
                }
                if(res.netProfitsDetail.yf){
                    NoPayable += res.netProfitsDetail.yf;
                }else{
                    NoPayable += 0;
                }
                NoPayable = NoPayable.toFixed(2);

                //三方费用(元)
                let feeAmount = 0;

                if (res.otherFeeList.length>0) {
                    res.otherFeeList.forEach(ret => {
                        feeAmount += ret.feeAmount
                    })
                    feeAmount = feeAmount.toFixed(2)
                }
                // $("#otherid"+index).html(feeAmount)
                
                let lot=''
                let sum=1
                let allocationVList=[]
                let lengths=null
                res.allocationVOList.forEach((rec,i)=>{
                    if(!lot){
                        rec['sum']=sum
                        lot=rec.lot
                        allocationVList.push(rec)
                        lengths=allocationVList.length-1
                    }else{
                        if(lot==rec.lot){
                            sum+=1
                            rec['sum']=0
                            allocationVList[lengths]['sum']=sum
                            lot=rec.lot
                            allocationVList.push(rec)
                        }else{
                            sum=1
                            rec['sum']=sum 
                            lot=rec.lot
                            allocationVList.push(rec)
                            lengths=allocationVList.length-1
                        }
                    }
                    
                })
                
                let allocationList=$('#allocationList'+ index)
                appendHtml(allocationVList,feeAmount,allocationList,index,NoPayable)
            })
            profitNum= (receiveNum - allocationNum).toFixed(2)

            $("#profitNum").html(profitNum)
            $("#receiveNum").html(receiveNum.toFixed(2))
            $("#allocationNum").html(allocationNum.toFixed(2))
            $("#netProfits").html(netProfits.toFixed(2));

            let netProfitNum=0;
            if(NoPayableNUm==0){
                if(netProfits!=0){
                    netProfitNum = -100;
                }
            }else{
                if(netProfits!=0){
                    netProfitNum = ((netProfits/NoPayableNUm)*100).toFixed(2);
                }  
            }
    
            if (netProfitNum < 15) {
                $("#computeLr").css('display', 'inline-block')
            } else {
                $("#computeLr").css('display', 'none')
            }
            $("#netProfitNum").html(netProfitNum+'%');

            //总毛利率
            let computesT = 0;
            if(receiveNum==0){
                if(profitNum==0){
                    computesT = 0
                }else{
                    computesT = -100
                }  
            }else{
                if(profitNum==0){
                    computesT = 0
                }else{
                    computesT = (profitNum / receiveNum) * 100
                    computesT = computesT.toFixed(2)
                }  
            }
            

            if (computesT < 15) {
                $("#computeDml").css('display', 'inline-block')
            } else {
                $("#computeDml").css('display', 'none')
            }

            $("#computeNum").html(computesT + '%')


            // let feeAmount = 0;
            // if (invoiceVT[0].otherFeeList) {
            //     invoiceVT[0].otherFeeList.forEach(res => {
            //         feeAmount += res.feeAmount
            //     })
            // }
            // $("#otherid").html(feeAmount)

            let payDetailList = [[${ entrustAndPayDetailVO.payDetailList }]]

            let payDetailNum = 0
            payDetailList.forEach(payDetail => {
                payDetailNum += payDetail.transFeeCount
                payDetailNum += payDetail.taxAmount
            })
            $("#transFeeCount_0").html(payDetailNum.toFixed(2))


            for (var i = 0; i < payDetailSize; i++) {
                var vbillno = $("#payDetailVbillno" + i).text().trim();
                if (vbillno == payDetailVbillno) {
                    $("#payDetailVbillno" + i).css("color", "");
                    $("#freeType" + i).css("color", "");
                    $("#oilCardNumber" + i).css("color", "");
                    $("#recCardNo" + i).css("color", "");
                    $("#recAccount" + i).css("color", "");
                    $("#gotAmount" + i).css("color", "#ff5f00");
                    $("#ungotAmount" + i).css("color", "");
                    $("#transFeeCount" + i).css("color", "#ff5f00");
                }
            }

            // $('[data-toggle="tooltip"]').tooltip()
        });

        //类型 check 审核；review 复核
        var type = [[${ type }]];

        var url = ctx + "payDetailCheck/check";
        if (type == 'review') {
            url = ctx + "payDetail/saveReview";
        }

        /**
         * 审核通过
         */
        function submitApprove() {
            var data = $("#form-check").serializeArray();
            data.push({ "name": "checkStatus", "value": 1 });
            $.operate.saveTab(url, data);
        }
        /**
         * 审核不通过
         */
        function submitBack() {
            var data = $("#form-check").serializeArray();
            data.push({ "name": "checkStatus", "value": 2 });
            if ($("#memo").val() === '') {
                $.modal.msgWarning("请填写退回备注！");
                return;
            }
            $.operate.saveTab(url, data);
        }

        function openDetailTab(tid) {
            layer.open({
                type: 2,
                area: ['70%', '90%'],
                fix: false,
                maxmin: true,
                shade: 0.3,
                title: "图片详情",
                content: ctx + "payDetail/receiptFileDetail/" + tid,
                btn: ['关闭'],
                shadeClose: true,            // 弹层外区域关闭
                cancel: function (index) {
                    return true;
                }
            });
        }
        function appendHtml(list,sum,allocationList,index,NoPayable){
            
            let html=``
            let listSum=list.length
            let invoiceVT = [[${entrustAndPayDetailVO.invoiceVOList}]]
            let costTypeFreight=[[${@dict.getType('cost_type_freight')}]]
            let costTypeOnWay=[[${@dict.getType('cost_type_on_way')}]]
            

            list.forEach((res,i)=>{
                if(i==0){
                    let picviewer=``
                    if ( res.receiptPic ) {
                        res.receiptPic.forEach(ret=>{
                            picviewer+=`<div style="display:inline-block">
                                        <img style="height:32px" src="`+ret.filePath+`" />
                                    </div>`
                        })
                    }
                   
                    let goodsName=res.goodsName+(res.totalNum!=0?'<span>'+ res.totalNum+'件|</span>':'')+
                                    (res.totalWeight!=0?'<span>'+ res.totalWeight+'吨|</span>':'')+
                                    (res.totalVolume!=0?'<span>'+ res.totalVolume+'m³|</span>':'')
                    let freeType= ''
                    if(res.freeType=='0'){
                        costTypeFreight.forEach(dict=>{
                            if( dict.dictValue == res.costTypeFreight){
                                freeType=dict.dictLabel
                            }
                        })
                    }else if(res.freeType=='1'){
                        costTypeOnWay.forEach(dict=>{
                            if( dict.dictValue == res.costTypeOnWay){
                                freeType=dict.dictLabel
                            }
                        })
                    }

                    html +=`<tr>
                                <td rowspan="`+res.sum+`"> `+res.lot+` </td>
                                <td rowspan="`+res.sum+`"> `+res.deliDetailAddr+`<span class="f1ab394">➨</span> `+res.arriDetailAddr+` </td>
                                <td rowspan="`+res.sum+`">`+ res.carrName+`(`+res.carrierPhone+`)`+` </td> 
                                <td rowspan="`+res.sum+`">`+ res.carNo+` </td>
                                <td rowspan="`+res.sum+`">`+goodsName+ `<span>`+ (res.pricingMethod==0?'按吨':res.pricingMethod==1?'按方':res.pricingMethod==2?'按件':res.pricingMethod==3?'按票':'')  +`</span></td>
                                <td rowspan="`+res.sum+`"><div class="picviewer">`+picviewer+`</div></td>
                                <td>`+freeType+`</td>
                                <td class="f1ab394 fw f20 ter">`+res.costShare+`</td>
                                <td rowspan="`+listSum+`" class="fw f1ab394 f20 ter">`+sum+`</td>
                                <td rowspan="`+listSum+`" class="fw f1ab394 f20 ter">`+(invoiceVT[index].allocationTotal+invoiceVT[index].otherFeeTotal).toFixed(2)+`</td>
                                <td rowspan="`+listSum+`" class="f1ab394 fw f20 ter">`+NoPayable+`</td>
                            </tr>`
                } else if(res.sum==0){
                    let freeType= ''
                    if(res.freeType=='0'){
                        costTypeFreight.forEach(dict=>{
                            if( dict.dictValue == res.costTypeFreight){
                                freeType=dict.dictLabel
                            }
                        })
                    }else if(res.freeType=='1'){
                        costTypeOnWay.forEach(dict=>{
                            if( dict.dictValue == res.costTypeOnWay){
                                freeType=dict.dictLabel
                            }
                        })
                    }
                    html +=`<tr>
                                <td>`+freeType+`</td>
                                <td class="f1ab394 fw f20 ter">`+res.costShare+`</td>
                            </tr>`
                }else{
                    let picviewer=``
                    if ( res.receiptPic ) {
                        res.receiptPic.forEach(ret=>{
                            picviewer+=`<div style="display:inline-block">
                                        <img style="height:32px" src="`+ret.filePath+`" />
                                    </div>`
                        })
                    }
                    let goodsName=res.goodsName+(res.totalNum!=0?'<span>'+ res.totalNum+'件|</span>':'')+
                                    (res.totalWeight!=0?'<span>'+ res.totalWeight+'吨|</span>':'')+
                                    (res.totalVolume!=0?'<span>'+ res.totalVolume+'m³|</span>':'')
                    let freeType= ''
                    if(res.freeType=='0'){
                        costTypeFreight.forEach(dict=>{
                            if( dict.dictValue == res.costTypeFreight){
                                freeType=dict.dictLabel
                            }
                        })
                    }else if(res.freeType=='1'){
                        costTypeOnWay.forEach(dict=>{
                            if( dict.dictValue == res.costTypeOnWay){
                                freeType=dict.dictLabel
                            }
                        })
                    }
                    html +=`<tr>
                                <td rowspan="`+res.sum+`"> `+res.lot+` </td>
                                <td rowspan="`+res.sum+`"> `+res.deliDetailAddr+`<span class="f1ab394">➨</span> `+res.arriDetailAddr+` </td>
                                <td rowspan="`+res.sum+`">`+ res.carrName+`(`+res.carrierPhone+`)`+` </td> 
                                <td rowspan="`+res.sum+`">`+ res.carNo+` </td>
                                <td rowspan="`+res.sum+`">`+goodsName+ `<span>`+ (res.pricingMethod==0?'按吨':res.pricingMethod==1?'按方':res.pricingMethod==2?'按件':res.pricingMethod==3?'按票':'')  +`</span></td>
                                <td rowspan="`+res.sum+`"><div class="picviewer">`+picviewer+`</div></td>
                                <td>`+freeType+`</td>
                                <td class="f1ab394 fw f20 ter">`+res.costShare+`</td>
                            </tr>`
                }
                
            })
            
            $(allocationList).append(html);

            $('.picviewer').viewer({
                url: 'data-original',
                title: false,
                navbar:false,
            });
        }
    </script>
</body>

</html>