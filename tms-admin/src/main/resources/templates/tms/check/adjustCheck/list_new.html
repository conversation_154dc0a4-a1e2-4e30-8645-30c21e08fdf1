<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付对账')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
    .tooltip-inner{
        /* background: transparent !important;
        text-align: left !important;
        color:#363636 !important;
        border:1px solid transparent; */
        max-width: 400px !important;
    }
    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .vertical-timeline-icon{
        width: 10px;
        height: 10px;
        background-color: #18a689;
        border-color: #18a689;
        top: 20px;
    }
    .vertical-timeline-content{
        margin-left: 16px;
    }
    .leftIcon{
        position: relative;
        margin: 0;
    }
    .leftIcon::after{
        content:" ";
        position: absolute;
        width: 1px;
        height: 100%;
        background-color: #18a689;
        top: 20px;
        left: 4px;
    }
    .leftIcon:last-child::after{
        height: 50%;
    }
    .leftIcon:first-child{
        font-size: 14px;
        color: #1ab394;
    }
    .flex{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 0 -5px;
    }
    .flex>span{
        margin:0 5px;
    }
    .pa1{
        white-space: pre-wrap;
        width: 4em;
        display: inline-block;
        line-height: 14px;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .mtdiv{
        display: inline-block;
        vertical-align: middle;
        margin-left: 5px;
    }
    .mtdiv div{
        text-align: center;
    }
    .mtdiv div:nth-child(2){
        margin-top: 4px;
    }
    .whpr{
        white-space: pre-wrap;
        width: 8em;
        display: inline-block;
    }
    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .vertical-timeline-icon{
        width: 10px;
        height: 10px;
        background-color: #18a689;
        border-color: #18a689;
        top: 20px;
    }
    .vertical-timeline-content{
        margin-left: 16px;
    }
    .leftIcon{
        position: relative;
        margin: 0;
    }
    .leftIcon::after{
        content:" ";
        position: absolute;
        width: 1px;
        height: 100%;
        background-color: #18a689;
        top: 20px;
        left: 4px;
    }
    .leftIcon:last-child::after{
        height: 50%;
    }
    .leftIcon:first-child{
        font-size: 14px;
        color: #1ab394;
    }
    .text-coral{
        color: coral;
    }
    .cpx{
        display: inline-block;
        width: 35px;
        height: 35px;
        background: url("/img/cpx.png") no-repeat 100%/100%;
        vertical-align: middle;
    }
    .ssx{
        display: inline-block;
        width: 45px;
        height: 40px;
        background: url("/img/ssx.png") no-repeat 100%/100%;
        vertical-align: middle;
        position: relative;
        top: 6px;
    }

    .xjx{
        display: inline-block;
        width: 50px;
        height: 40px;
        background: url("/img/xjx.png") no-repeat 100%/100%;
        vertical-align: middle;
        position: relative;
        top: 6px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row no-gutter">
                    <div class="col-md-1 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="custAbbr" class="form-control" type="text" placeholder="客户简称"
                                       maxlength="30" required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input id="invoiceVbillno" name="invoiceVbillno" class="form-control" type="text" placeholder="请输入发货单号"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="adjustCheckStatusArr" id="adjustCheckStatusArr" class="form-control valid noselect2 selectpicker" onchange="searchPre()"
                                        aria-invalid="false" data-none-selected-text="审核状态" multiple>
                                    <option th:each="dict : ${adjustCheckStatus}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="startDate" id="startDate" placeholder="申请开始日">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="endtDate" id="endtDate" placeholder="申请结束日">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary multiple disabled" onclick="invoiceCheck()" shiro:hasPermission="tms:adjustCheck:invoiceCheckNew">
                <i class="fa fa-check"></i> 业务审核
            </a>
            <a class="btn btn-primary multiple disabled" onclick="leaderCheck()" shiro:hasPermission="tms:adjustCheck:leaderCheckNew">
                <i class="fa fa-check"></i> 总经办审核
            </a>
            <a class="btn btn-primary multiple disabled" onclick="financeCheck()" shiro:hasPermission="tms:adjustCheck:financeCheckNew">
                <i class="fa fa-check"></i> 财务确认
            </a>

            <a class="btn btn-info" th:onclick="showSpSettings([[${templateId}]], [[${biz}]])" shiro:hasPermission="wecom:sp:setting">
                <i class="fa fa-cog"></i> 配置
            </a>

            <a class="btn btn-info single disabled" onclick="process()">
                <i class="fa fa-laptop"></i> 审批进度
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
    //运单状态
    var entrustLotStatus = [[${entrustLotStatus}]];
    var prefix = ctx + "adjustCheck";

    //调账状态
    var adjustCheckStatusArr = [[${adjustCheckStatusArr}]];
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var oilRate = [[${@sysConfigServiceImpl.selectConfigByKey("oil_tax_rate")}]];
    var cashRate = [[${@sysConfigServiceImpl.selectConfigByKey("cash_tax_rate")}]];

    $(function () {
        var options = {
            url: prefix + "/newAdjustList",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/adjustImport",
            showToggle: false,
            showColumns: true,
            modalName: "应付对账",
            uniqueId: "adjustRecordId",
            fixedColumns: true,
            height: 580,
            rememberSelected: false,
            fixedNumber: 0,
            clickToSelect:true,
            showFooter:false,
            columns: [{
                checkbox: true
            },
                {
                    title: '审核状态',
                    field: 'adjustCheckStatus',
                    align: 'left',
                    formatter: function status(value,row) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-primary" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="'+getCheckList(row)+'">待业务审核</span><br/>'+row.regDate;
                            case 1:
                                return '<span class="label label-warning" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="'+getCheckList(row)+'">待总经办审核</span><br/>'+row.regDate;
                            case 2:
                                return '<span class="label label-info" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="'+getCheckList(row)+'">待财务确认</span><br/>'+row.regDate;
                            case 3:
                                return '<span class="label label-success" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="'+getCheckList(row)+'">审核通过</span><br/>'+row.regDate;
                            case 4:
                                return '<span class="label label-inverse" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="'+getCheckList(row)+'">审核不通过</span><br/>'+row.regDate;
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '申请人/申请原因',
                    align: 'left',
                    field: 'regUserName',
                    formatter: function status(value,row) {
                        return value + '<br />' + $.table.tooltip(row.memo);
                    }
                },
                {
                    title: '发货单号/客户名称',
                    align: 'left',
                    field : 'invoiceVbillno',
                    formatter: function(value, row, index) {
                       return $.table.tooltip(value)+"<br/>"+$.table.tooltip(row.custAbbr);
                    }
                },
                {
                    title: '要求提/到货地址',
                    align: 'left',
                    field : 'deliAddress',
                    formatter: function(value, row, index) {
                        return `<span class="label label-warning pa2">提</span>`+row.deliAddress+`<br/><span class="label label-success pa2">到</span>`+row.arriAddress;
                    }
                },
                {
                    title: '货量/车型',
                    align: 'left',
                    field : 'carLenType',
                    formatter: function(value, row, index) {
                        let html=row.goodsName;
                        if(row.numWeightVolume!="0件0吨0方"&&row.numWeightVolume!="件吨方"){
                            html+=" "+row.numWeightVolume
                        }
                        return $.table.tooltip(html)+"<br/>"+ $.table.tooltip(value);
                    }
                },
                {
                    title: '费用',
                    align: 'left',
                    field : 'receiveAmount',
                    formatter: function(value, row, index) {
                        let html=[]
                        if(row.receiveAmount){
                            html.push(`<span class="label label-coral pa2">应收</span>`+row.receiveAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if(row.payAmount){
                            html.push(`<span class="label badge-info pa2">应付</span>`+row.payAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if(row.otherAmount){
                            html.push(`<span class="label label-primary pa2">三方</span>`+row.otherAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        return html.join("<br/>")
                    }
                },
                {
                    title: '税金',
                    align: 'left',
                    field : 'yfTax',
                    formatter: function(value, row, index) {
                        let html=[]
                        /*if(row.netInfo.ys){
                            html.push(`<span class="label label-coral pa2">未税应收</span>`+row.netInfo.ys.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if(row.netInfo.yf){
                            html.push(`<span class="label badge-info pa2">未税应付</span>`+row.netInfo.yf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if(row.netInfo.dsf){
                            html.push(`<span class="label label-primary pa2">未税三方</span>`+row.netInfo.dsf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if(row.netInfo.ptf){
                            html.push(`<span class="label label-success pa2">未税平台费</span>`+row.netInfo.ptf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }*/
                        if (row.yfTax) {
                            html.push(`<span class="label badge-info pa2">应付税金</span>`+row.yfTax.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if (row.sfTax) {
                            html.push(`<span class="label label-primary pa2">三方税金</span>`+row.sfTax.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if (row.ptf) {
                            html.push(`<span class="label label-success pa2">平台费</span>`+row.ptf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        return html.join("<br/>")
                    }
                },
                {
                    title: '调整量',
                    align: 'left',
                    field: 'receiveDetailVOS',
                    formatter: function (value, row, index) {
                        let html=[];
                        if(row.receiveDetailVOS.length>0){
                            row.receiveDetailVOS.forEach(item=>{
                                if(item.transFeeCount != 0){
                                    let text=[];
                                    if(item.freeType==0){
                                        text.push("<span class='text-coral'>应收运费</span>");
                                    }else{
                                        text.push("<span class='text-coral'>在途应收</span>");
                                    }
                                    if(item.transFeeCount>0){
                                        html.push(text.join('/')+'&nbsp;&nbsp;'+item.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                                    }else{
                                        html.push(text.join('/')+'&nbsp;&nbsp;'+"<span class='text-danger'>"+item.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+"</span>");
                                    }
                                }
                            })
                        }
                        if(row.payDetailList.length>0){
                            row.payDetailList.forEach(item=>{
                                if(item.transFeeCount != 0) {
                                    let text = [];
                                    if (item.freeType == 0) {
                                        text.push("<span class='text-info'>应付运费</span>");
                                        if (item.costTypeFreight) {
                                            text.push($.table.selectDictLabel(costTypeFreight, item.costTypeFreight));
                                        }
                                    } else {
                                        text.push("<span class='text-info'>在途应付</span>");
                                        if (item.costTypeOnWay) {
                                            text.push($.table.selectDictLabel(costTypeOnWay, item.costTypeOnWay));
                                        }
                                    }

                                    if (item.transFeeCount > 0) {
                                        html.push(text.join('/') + '&nbsp;&nbsp;' + "<span class='text-danger'>" + item.transFeeCount.toLocaleString('zh', {
                                            style: 'currency',
                                            currency: 'CNY'
                                        }) + "</span>");
                                    } else {
                                        html.push(text.join('/') + '&nbsp;&nbsp;' + item.transFeeCount.toLocaleString('zh', { style: 'currency', currency: 'CNY' }));
                                    }
                                }
                            })
                        }
                        if(row.otherFeeList.length>0){
                            row.otherFeeList.forEach(item=>{
                                let text=[];
                                text.push("<span class='text-info'>三方应付</span>")
                                if(item.feeType){
                                    text.push($.table.selectDictLabel(costTypeOnWay, item.feeType));
                                }
                                if(item.feeAmount>0){
                                    html.push(text.join('/')+'&nbsp;&nbsp;'+"<span class='text-danger'>"+item.feeAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+"</span>");
                                }else{ 
                                    html.push(text.join('/')+'&nbsp;&nbsp;'+item.feeAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                                }
                            })
                        }
                        
                        if(row.numAdjust){
                            html.push(`<span class="label label-warning pa2">发货单件数</span>`+row.numAdjust+`件`)
                        }
                        if(row.weightAdjust){
                            html.push(`<span class="label label-warning pa2">发货单重量</span>`+row.weightAdjust+`吨`)
                        }
                        if(row.volumeAdjust){
                            html.push(`<span class="label label-warning pa2">发货单体积</span>`+row.volumeAdjust+`方`)
                        }
                        if(row.numAdjustLot){
                            html.push(`<span class="label label-warning pa2">运单件数</span>`+row.numAdjustLot+`件`)
                        }
                        if(row.weightAdjustLot){
                            html.push(`<span class="label label-warning pa2">运单重量</span>`+row.weightAdjustLot+`吨`)
                        }
                        if(row.volumeAdjustLot){
                            html.push(`<span class="label label-warning pa2">运单体积</span>`+row.volumeAdjustLot+`方`)
                        }
                        return html.join('<br/>')
                    }

                },
               /* {
                    title: '预估毛利',
                    align: 'left',
                    field : 'payAmount',
                    formatter: function(value, row, index) {
                        let oldNum= Number(row.receiveAmount)-Number(row.payAmount)-Number(row.otherAmount);
                        if(row.adjustCheckStatus==3){
                            if(row.receiveDetailVOS.length>0){
                                row.receiveDetailVOS.forEach(item=>{
                                    oldNum -=Number(item.transFeeCount);
                                })
                            }
                            if(row.payDetailList.length>0){
                                row.payDetailList.forEach(item=>{
                                    oldNum +=Number(item.transFeeCount);
                                })
                            }
                            if(row.otherFeeList.length>0){
                                row.otherFeeList.forEach(item=>{
                                    oldNum +=Number(item.feeAmount);
                                })
                            }
                        } 
                        let num= Number(row.receiveAmount)-Number(row.payAmount)-Number(row.otherAmount);
                        if(row.adjustCheckStatus!=3){
                            if(row.receiveDetailVOS.length>0){
                                row.receiveDetailVOS.forEach(item=>{
                                    num +=Number(item.transFeeCount);
                                })
                            }
                            if(row.payDetailList.length>0){
                                row.payDetailList.forEach(item=>{
                                    num -=Number(item.transFeeCount);
                                })
                            }
                            if(row.otherFeeList.length>0){
                                row.otherFeeList.forEach(item=>{
                                    num -=Number(item.feeAmount);
                                })
                            }
                        }
                        
                        let differ= Number(num)-Number(oldNum);

                        let html="";
                        html+=num.toLocaleString('zh', {style: 'currency', currency: 'CNY'});

                        if(differ==0){
                            html+="<span class='cpx ml5'></span>"
                        }else if(differ>0){
                            html+="<span class='ml5 ssx' data-toggle='tooltip' data-container='body' data-placement='top' data-html='true' title='+"+differ.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+"'></span>"
                        }else{
                            html+="<span class='ml5 xjx' data-toggle='tooltip' data-container='body' data-placement='top' data-html='true' title='"+differ.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+"'></span>"
                        }
                        // if(oldNum>0){
                        //     html.push(`<span class="label label-warning pa2">前</span>`+oldNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        // }else{
                        //     html.push(`<span class="label label-warning pa2">前</span><span class="text-danger">`+oldNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`);
                        // } 

                        // if(differ>0){
                        //     html.push("↑"+differ.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        // }else{
                        //     html.push("<span class='text-danger'>↓"+differ.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+"</span>");
                        // }
                        
                        // if(num>0){
                        //     html.push(`<span class="label label-success pa2">后</span>`+num.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        // }else{
                        //     html.push(`<span class="label label-success pa2">后</span><span class="text-danger">`+num.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`);
                        // } 
                        return html;
                    }
                },*/
                {
                    title: '预估利润',
                    align: 'left',
                    field : 'payAmount',
                    formatter: function(value, row, index) {
                        //let oldNum= Number(row.netInfo.ys)-Number(row.netInfo.yf)-Number(row.netInfo.dsf)-Number(row.netInfo.ptf);
                        let oldNum = (row.receiveAmount||0)-(row.payAmount||0)-(row.otherAmount||0)-(row.ptf||0);
                        if(row.adjustCheckStatus==3){ // 审核通过
                            if(row.receiveDetailVOS.length>0){
                                row.receiveDetailVOS.forEach(item=>{
                                    oldNum += item.transFeeCount * item.platRate; // 应收调整时的平台费(成本)
                                    oldNum -= Number(item.transFeeCount);
                                })
                            }
                            if(row.payDetailList.length>0){
                                row.payDetailList.forEach(item=>{
                                    oldNum +=Number(item.transFeeCount);
                                    if (item.costTypeFreight == '1' || item.costTypeFreight == '3' || item.costTypeFreight == '5') {
                                        oldNum += item.transFeeCount * oilRate;
                                    } else {
                                        if (item.lotBillingType == '6') {
                                            oldNum += item.transFeeCount * cashRate;
                                        }
                                    }
                                })
                            }
                            if(row.otherFeeList.length>0){
                                row.otherFeeList.forEach(item=>{
                                    oldNum +=Number(item.feeAmount);
                                    oldNum += item.feeAmount * cashRate;
                                })
                            }
                        }
                        //let num= Number(row.netInfo.ys)-Number(row.netInfo.yf)-Number(row.netInfo.dsf)-Number(row.netInfo.ptf);
                        let num = (row.receiveAmount||0)-(row.payAmount||0)-(row.otherAmount||0)-(row.ptf||0);
                        if(row.adjustCheckStatus!=3){
                            if(row.receiveDetailVOS.length>0){
                                row.receiveDetailVOS.forEach(item=>{
                                    num -= item.transFeeCount * item.platRate; // 应收调整时的平台费(成本)
                                    num +=Number(item.transFeeCount);
                                })
                            }
                            if(row.payDetailList.length>0){
                                row.payDetailList.forEach(item=>{
                                    num -=Number(item.transFeeCount);
                                    if (item.costTypeFreight == '1' || item.costTypeFreight == '3' || item.costTypeFreight == '5') {
                                        num -= item.transFeeCount * oilRate;
                                    } else {
                                        if (item.lotBillingType == '6') {
                                            num -= item.transFeeCount * cashRate;
                                        }
                                    }
                                })
                            }
                            if(row.otherFeeList.length>0){
                                row.otherFeeList.forEach(item=>{
                                    num -=Number(item.feeAmount);
                                    num -= item.feeAmount * cashRate;
                                })
                            }
                        }

                        let differ= Number(num)-Number(oldNum);

                        let html="";
                        html+=num.toLocaleString('zh', {style: 'currency', currency: 'CNY'});

                        if(differ==0){
                            html+="<span class='cpx ml5'></span>"
                        }else if(differ>0){
                            html+="<span class='ml5 ssx' data-toggle='tooltip' data-container='body' data-placement='top' data-html='true' title='+"+differ.toFixed(2)+"'></span>"
                        }else{
                            html+="<span class='ml5 xjx' data-toggle='tooltip' data-container='body' data-placement='top' data-html='true' title='"+differ.toFixed(2)+"'></span>"
                        }
                        // if(oldNum>0){
                        //     html.push(`<span class="label label-warning pa2">前</span>`+oldNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        // }else{
                        //     html.push(`<span class="label label-warning pa2">前</span><span class="text-danger">`+oldNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`);
                        // }

                        // if(differ>0){
                        //     html.push("↑"+differ.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        // }else{
                        //     html.push("<span class='text-danger'>↓"+differ.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+"</span>");
                        // }

                        // if(num>0){
                        //     html.push(`<span class="label label-success pa2">后</span>`+num.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        // }else{
                        //     html.push(`<span class="label label-success pa2">后</span><span class="text-danger">`+num.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`);
                        // }
                        return html;
                    }
                },
                {   title: '附件',
                    field: 'sysUploadFiles',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var html = "<div class='picviewer'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html += `<img style="height:32px" src="`+element.filePath+`"/>`
                            });
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                },
                {
                    title: '承运商',
                    align: 'left',
                    field : 'carrName'
                },
                {
                    title: '审批单号',
                    field: 'spNo'
                }
            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }
        };

        $("#adjustCheckStatusArr").val(adjustCheckStatusArr);

        $.table.init(options);

        // var arr = [];
        // arr.push(segmentStatusList[0].value);
        // arr.push(segmentStatusList[1].value);
        // arr.push(segmentStatusList[6].value);
        // arr.push(segmentStatusList[7].value);
        //$('#adjustCheckStatusArr').selectpicker('val',arr);
        // $("#adjustCheckStatusArr").find('option').eq(0).attr("selected",true);
        // $("#adjustCheckStatusArr").find('option').eq(1).attr("selected",true);
        // $("#adjustCheckStatusArr").find('option').eq(2).attr("selected",true);

        // searchPre();

        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
    });

    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }


    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.adjustCheckStatusArr = $.common.join($('#adjustCheckStatusArr').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        $("#adjustCheckStatusArr").find('option').eq(0).attr("selected",true);
        $("#adjustCheckStatusArr").find('option').eq(1).attr("selected",true);
        $("#adjustCheckStatusArr").find('option').eq(2).attr("selected",true);
        searchPre();
    }

    /**
     * 审核
     */
    function invoiceCheck() {
        var adjustRecordIds = $.table.selectColumns('adjustRecordId').join();
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if ( bootstrapTable[i]["adjustCheckStatus"] != 0) {
                $.modal.alertWarning("当前审核状态不需要进行业务审核！");
                return false;
            }
        }
        layer.open({
            type: 2,
            area: ['800px', '500px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "业务审核",
            content:  prefix + "/invoiceCheck?adjustRecordIds=" + adjustRecordIds,
            btn: ['通过', '不通过','关闭'],
            shadeClose: true,
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitApprove(index, layero);
            },
            btn2: function(index, layero){
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitBack(index, layero);
                return false;
            },
            btn3: function (index, layero) {
                return true;
            }
        });
    }

    function leaderCheck() {
        var adjustRecordIds = $.table.selectColumns('adjustRecordId').join();
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if ( bootstrapTable[i]["adjustCheckStatus"] != 1) {
                $.modal.alertWarning("当前审核状态不需要进行总经办审核！");
                return false;
            }

        }
        layer.open({
            type: 2,
            area: ['800px', '500px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "总经办审核",
            content:  prefix + "/leaderCheck?adjustRecordIds=" + adjustRecordIds,
            btn: ['通过', '不通过','关闭'],
            shadeClose: true,
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitApprove(index, layero);
            },
            btn2: function(index, layero){
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitBack(index, layero);
                return false;
            },
            btn3: function (index, layero) {
                return true;
            }
        });
    }

    function financeCheck() {
        var adjustRecordIds = $.table.selectColumns('adjustRecordId').join();
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if ( bootstrapTable[i]["adjustCheckStatus"] != 2) {
                $.modal.alertWarning("当前审核状态不需要进行财务确认！");
                return false;
            }

        }
        layer.open({
            type: 2,
            area: ['800px', '500px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "财务确认",
            content:  prefix + "/financeCheck?adjustRecordIds=" + adjustRecordIds,
            btn: ['通过', '不通过','关闭'],
            shadeClose: true,
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitApprove(index, layero);
            },
            btn2: function(index, layero){
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitBack(index, layero);
                return false;
            },
            btn3: function (index, layero) {
                return true;
            }
        });
    }

    function jumpAdjustRecordId(adjustRecordId,adjustCheckStatus,adjustRecordType) {
        var jumpUrl = prefix;
        if(adjustRecordType == '0'){
            jumpUrl = ctx + "payDetailAdjustCheck";
        }else if(adjustRecordType == '1'){
            jumpUrl = ctx + "otherFeeAdjustCheck";
        }else if(adjustRecordType == '2'){
            jumpUrl = ctx + "receiveDetailAdjustCheck";
        }
        var url = jumpUrl + "/listDetail?adjustRecordId="+adjustRecordId+"&adjustCheckStatus="+adjustCheckStatus;
        $.modal.openTab("运单明细" , url);
    }

    function picUpload(adjustRecordId,adjustCheckStatus){
        var url =  ctx + "receiveDetailAdjustCheck/picUpload?adjustRecordId="+adjustRecordId;
        $.modal.open('附件上传',url,800,600);
    }

    function getCheckList(item) {
        let html="";
        html+=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'><div class='vertical-container light-timeline'>`;
            if(item.thirdCheckUserName){
                html+=`<div class='vertical-timeline-block leftIcon'>
                    <div class='vertical-timeline-icon'>
                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                    </div>
                    <div class='vertical-timeline-content'>
                        <div class=''>
                            <div class='fw f18'> 三级审核人: `+item.thirdCheckUserName+`</div>
                            <div class='fw f18'>
                                备注: `+(item.thirdCheckMemo== null?'暂无数据':item.thirdCheckMemo)+`
                            </div>
                            <div style='white-space: nowrap;'>`+item.thirdCheckDate+`</div>
                        </div>
                        <div class='mt10' style='white-space: initial;'></div>
                    </div>

                </div>`;
            }
            if(item.secondCheckUserName){
                html+=`<div class='vertical-timeline-block leftIcon'>
                    <div class='vertical-timeline-icon'>
                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                    </div>
                    <div class='vertical-timeline-content'>
                        <div class=''>
                            <div class='fw f18'> 二级审核人: `+item.secondCheckUserName+`</div>
                            <div class='fw f18'>
                                备注: `+(item.secondCheckMemo== null?'暂无数据':item.secondCheckMemo)+`
                            </div>
                            <div style='white-space: nowrap;'>`+item.secondCheckDate+`</div>
                        </div>
                        <div class='mt10' style='white-space: initial;'></div>
                    </div>

                </div>`;
            }
            if(item.firstCheckUserName){
                html+=`<div class='vertical-timeline-block leftIcon'>
                    <div class='vertical-timeline-icon'>
                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                    </div>
                    <div class='vertical-timeline-content'>
                        <div class=''>
                            <div class='fw f18'> 一级审核人: `+item.firstCheckUserName+`</div>
                            <div class='fw f18'>
                                备注: `+(item.firstCheckMemo== null?'暂无数据':item.firstCheckMemo)+`
                            </div>
                            <div style='white-space: nowrap;'>`+item.firstCheckDate+`</div>
                        </div>
                        <div class='mt10' style='white-space: initial;'></div>
                    </div>

                </div>`;
            }
            
            
           
          
        let title="",selectDictLabel=""
        if(item.budgetType==2||item.budgetType==4){
            title= "在途应收";
            selectDictLabel=$.table.selectDictLabel(payOrCollect, item.budgetType)+"/"+$.table.selectDictLabel(costTypeOnWay, item.costType)
        }else if(item.budgetType==1||item.budgetType==5||item.budgetType==null){
            if(item.isLotFee==0){
                title= "在途应付";
                selectDictLabel=$.table.selectDictLabel(payOrCollect, item.budgetType)+"/"+$.table.selectDictLabel(costTypeOnWay, item.costType)
            }else{
                title= "三方应付";
                selectDictLabel=$.table.selectDictLabel(costTypeOnWay, item.feeType)
            }
        }

        html+=`<div class='vertical-timeline-block leftIcon'>
                <div class='vertical-timeline-icon'>
                    <img src='/img/pep.png' style='width: 100%;height: 100%'>
                </div>
                <div class='vertical-timeline-content'>
                    <div class=''>
                        <div class='fw f18 mt10' style='white-space: nowrap;'>`+item.regUserName+`创建了`+title+`费用</div>
                        <div class='fw f18' style='white-space: nowrap;'>`+selectDictLabel+`</div>
                        <div style='white-space: nowrap;'>`+item.regDate+`</div>
                    </div>
                    <div class='mt10' style='white-space: initial;'></div>
                </div>
            </div>`;
            
        html+=`</div></div></div></div>`;
        return html
    }

    function process() {
        var spNo = $.table.selectColumns("spNo")[0];
        if (!spNo) {
            $.modal.msgWarning("未提交企业微信审批")
            return
        }
        wecom_process(spNo);
    }
</script>
</body>
</html>