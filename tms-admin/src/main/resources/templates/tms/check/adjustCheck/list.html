<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付对账')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
    .tooltip-inner{
        /* background: transparent !important;
        text-align: left !important;
        color:#363636 !important;
        border:1px solid transparent; */
        max-width: 400px !important;
    }
    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .vertical-timeline-icon{
        width: 10px;
        height: 10px;
        background-color: #18a689;
        border-color: #18a689;
        top: 20px;
    }
    .vertical-timeline-content{
        margin-left: 16px;
    }
    .leftIcon{
        position: relative;
        margin: 0;
    }
    .leftIcon::after{
        content:" ";
        position: absolute;
        width: 1px;
        height: 100%;
        background-color: #18a689;
        top: 20px;
        left: 4px;
    }
    .leftIcon:last-child::after{
        height: 50%;
    }
    .leftIcon:first-child{
        font-size: 14px;
        color: #1ab394;
    }
    .flex{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 0 -5px;
    }
    .flex>span{
        margin:0 5px;
    }
    .pa1{
        white-space: pre-wrap;
        width: 4em;
        display: inline-block;
        line-height: 14px;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .mtdiv{
        display: inline-block;
        vertical-align: middle;
        margin-left: 5px;
    }
    .mtdiv div{
        text-align: center;
    }
    .mtdiv div:nth-child(2){
        margin-top: 4px;
    }
    .whpr{
        white-space: pre-wrap;
        width: 8em;
        display: inline-block;
    }
    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .vertical-timeline-icon{
        width: 10px;
        height: 10px;
        background-color: #18a689;
        border-color: #18a689;
        top: 20px;
    }
    .vertical-timeline-content{
        margin-left: 16px;
    }
    .leftIcon{
        position: relative;
        margin: 0;
    }
    .leftIcon::after{
        content:" ";
        position: absolute;
        width: 1px;
        height: 100%;
        background-color: #18a689;
        top: 20px;
        left: 4px;
    }
    .leftIcon:last-child::after{
        height: 50%;
    }
    .leftIcon:first-child{
        font-size: 14px;
        color: #1ab394;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row no-gutter">
                    <div class="col-md-1 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="adjustType" id="adjustType" class="form-control valid" onchange="searchPre()"
                                        aria-invalid="false" data-none-selected-text="调整类型">
                                    <option value="0">应付</option>
                                    <option value="1">应收+三方</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="custAbbr" class="form-control" type="text" placeholder="客户简称"
                                       maxlength="30" required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input id="invoiceVbillno" name="invoiceVbillno" class="form-control" type="text" placeholder="请输入发货单号"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="adjustCheckStatusArr" id="adjustCheckStatusArr" class="form-control valid noselect2 selectpicker" onchange="searchPre()"
                                        aria-invalid="false" data-none-selected-text="审核状态" multiple>
                                    <option th:each="dict : ${adjustCheckStatus}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="salesDeptPayDetail" id="salesDeptPayDetail" class="form-control valid noselect2 selectpicker" onchange="searchPre()"
                                        aria-invalid="false" data-none-selected-text="运营组" multiple>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="startDate" id="startDate" placeholder="申请开始日">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="endtDate" id="endtDate" placeholder="申请结束日">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="invoiceCheck()" shiro:hasPermission="tms:adjustCheck:invoiceCheck">
                <i class="fa fa-check"></i> 业务审核
            </a>
            <a class="btn btn-primary single disabled" onclick="leaderCheck()" shiro:hasPermission="tms:adjustCheck:leaderCheck">
                <i class="fa fa-check"></i> 总经办审核
            </a>
            <a class="btn btn-primary single disabled" onclick="financeCheck()" shiro:hasPermission="tms:adjustCheck:financeCheck">
                <i class="fa fa-check"></i> 财务确认
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    //运单状态
    var entrustLotStatus = [[${entrustLotStatus}]];
    var prefix = ctx + "adjustCheck";

    //调账状态
    var adjustCheckStatusArr = [[${adjustCheckStatusArr}]];
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];

    $(function () {
        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/adjustImport",
            showToggle: false,
            showColumns: true,
            modalName: "应付对账",
            uniqueId: "adjustRecordId",
            fixedColumns: true,
            height: 580,
            rememberSelected: false,
            fixedNumber: 0,
            clickToSelect:true,
            showFooter:false,
            firstLoad: false,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'center',
                    field: 'adjustRecordId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if(row.tid != '' && row.tid != null){
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="附件" onclick="picUpload(\'' + row.adjustRecordId + '\',\''+row.adjustCheckStatus+'\')"><i class="fa fa-file-image-o" style="font-size: 15px;"></i></a>');
                        }
                        actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="明细"onclick="jumpAdjustRecordId(\'' + row.adjustRecordId + '\',\''+row.adjustCheckStatus+'\',\''+row.adjustRecordType+'\')"><i class="fa fa-list" style="font-size: 15px;"></i></a>');
                        return actions.join('');
                    }
                },
                {
                    title: '审核状态',
                    field: 'adjustCheckStatus',
                    align: 'left',
                    formatter: function status(value,row) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-primary" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="'+getCheckList(row)+'">待业务审核</span><br/>'+row.regDate;
                            case 1:
                                return '<span class="label label-warning" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="'+getCheckList(row)+'">待总经办审核</span><br/>'+row.regDate;
                            case 2:
                                return '<span class="label label-info" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="'+getCheckList(row)+'">待财务确认</span><br/>'+row.regDate;
                            case 3:
                                return '<span class="label label-success" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="'+getCheckList(row)+'">审核通过</span><br/>'+row.regDate;
                            case 4:
                                return '<span class="label label-inverse" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="'+getCheckList(row)+'">审核不通过</span><br/>'+row.regDate;
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '申请人/申请原因',
                    align: 'left',
                    field: 'regUserName',
                    formatter: function status(value,row) {
                        return value + '<br />' + $.table.tooltip(row.memo);
                    }
                },
                {
                    title: '调账类型',
                    align: 'left',
                    field: 'adjustRecordType',
                    formatter: function status(value,row) {
                        if(row.adjustRecordType == 2){
                            return `<span class="label label-coral" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="应收调账">应收</span>`;
                        }else if(row.adjustRecordType==0){
                            return`<span class="label badge-info" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="应付调账">应付</span>`;
                        }else if(row.adjustRecordType==1){
                            return `<span class="label label-primary" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="第三方调账">三方</span>`;
                        }
                    }
                },
                {
                    title: '金额调整',
                    align: 'left',
                    field: 'sumTransFeeCount',
                    formatter: function (value, row, index) {
                        let html=[];
                        if(row.adjustRecordType == 2){
                            row.receiveVOS.forEach(item=>{
                                if(item.freightTotal||item.freightTotalAfter){
                                    let freightTotal = item.freightTotal;
                                    let freightTotalAfter = item.freightTotalAfter;
                                    let percentageNum= freightTotalAfter-freightTotal;
                                    if(percentageNum){
                                        let data=`<div class="flex">`;
                                        data+=`<span class="text-info">运费</span>`;

                                        if (freightTotal > freightTotalAfter) {
                                            data+=`<span class="text-info">↓`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                        }else{
                                            if(percentageNum){
                                                data+=`<span class="text-danger">↑`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                            }else{
                                                data+=`<span class="text-danger">&nbsp;&nbsp;</span>`;
                                            }
                                        }
                                        data+=`</div>`;
                                        html.push(data);
                                    }
                                }
                                if(item.onWayTotal||item.onWayTotalAfter){
                                    let onWayTotal = item.onWayTotal;
                                    let onWayTotalAfter = item.onWayTotalAfter;
                                    let percentageNum= onWayTotalAfter-onWayTotal;
                                    if(percentageNum){
                                        let data=`<div class="flex">`;
                                        data+=`<span class="text-info">在途</span>`;
                                        if (onWayTotal > onWayTotalAfter) {
                                            data+=`<span class="text-info">↓`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                        }else {
                                            data+=`<span class="text-danger">↑`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                        }
                                        data+=`</div>`;
                                        html.push(data);
                                    }
                                }
                            })
                        }else if(row.adjustRecordType==0){
                            row.payReconciliationVOS.forEach(item=>{
                                if(item.receiptAmountFreight||item.receiptAmountFreightAfter){
                                    let receiptAmountFreight = item.receiptAmountFreight;
                                    let receiptAmountFreightAfter = item.receiptAmountFreightAfter;
                                    let percentageNum= receiptAmountFreightAfter-receiptAmountFreight;
                                    if(percentageNum){
                                        let data=`<div class="flex">`;
                                        data+=`<span class="text-info">运费</span>`;


                                    
                                        if (receiptAmountFreight > receiptAmountFreightAfter) {
                                            data+=`<span class="text-info">↓`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                        
                                        }else{
                                            if(percentageNum){
                                                data+=`<span class="text-danger">↑`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                            }else{
                                                data+=`<span class="text-danger">&nbsp;&nbsp;</span>`;
                                            }
                                        }
                                        data+=`</div>`;
                                        html.push(data);
                                    }
                                }

                                if(item.receiptAmountOnWay||item.receiptAmountOnWayAfter){
                                    let receiptAmountOnWay = item.receiptAmountOnWay;
                                    let receiptAmountOnWayAfter = item.receiptAmountOnWayAfter;
                                    let percentageNum= receiptAmountOnWayAfter-receiptAmountOnWay;
                                    if(percentageNum){
                                        let data=`<div class="flex">`;
                                        data+=`<span class="text-info">在途</span>`;
                                        if(percentageNum){
                                            if (receiptAmountOnWay > receiptAmountOnWayAfter) {
                                                data+=`<span class="text-info">↓`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                            }else {
                                                data+=`<span class="text-danger">↑`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                            }
                                        }
                                        data+=`</div>`;
                                        html.push(data);
                                    }
                                }

                                if(item.oilCardAmount||item.oilCardAmountAfter){
                                    let oilCardAmount = item.oilCardAmount;
                                    let oilCardAmountAfter = item.oilCardAmountAfter;
                                    let percentageNum= oilCardAmountAfter-oilCardAmount;
                                    if(percentageNum){
                                        let data=`<div class="flex">`;
                                        data+=`<span class="text-info">油卡</span>`;
                                        if (oilCardAmount > oilCardAmountAfter) {
                                            data+=`<span class="text-info">↓`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                        }else{
                                            data+=`<span class="text-danger">↑`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                        }
                                        data+=`</div>`;
                                        html.push(data);
                                    }
                                }
                            })
                        }else if(row.adjustRecordType==1){
                            row.custOtherFeeVOList.forEach(item=>{
                                if(item.loadingFee||item.adjustLoadingFee){
                                    let loadingFee = item.loadingFee;
                                    let adjustLoadingFee = item.adjustLoadingFee;
                                    let percentageNum= adjustLoadingFee-loadingFee;
                                    if(percentageNum){
                                        let data=`<div class="flex">`;
                                        data+=`<span class="text-info">装卸费(现金)</span>`;


                                        
                                        if (loadingFee > adjustLoadingFee) {
                                            data+=`<span class="text-info">↓`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                           
                                        }else{
                                            data+=`<span class="text-danger">↑`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                            
                                        }
                                        data+=`</div>`;
                                        html.push(data);
                                    }
                                }

                                if(item.storageFee||item.adjustStorageFee){
                                    let storageFee = item.storageFee;
                                    let adjustStorageFee = item.adjustStorageFee;
                                    let percentageNum= adjustStorageFee-storageFee;
                                    if(percentageNum){
                                        let data=`<div class="flex">`;
                                        data+=`<span class="text-info">仓储费(现金)</span>`;


                                        
                                        if (storageFee > adjustStorageFee) {
                                            data+=`<span class="text-info">↓`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                           
                                        }else{
                                            data+=`<span class="text-danger">↑`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                            
                                        }
                                        data+=`</div>`;
                                        html.push(data);
                                    }
                                }

                                if(item.otherFee||item.adjustOtherFee){
                                    let otherFee = item.otherFee;
                                    let adjustOtherFee = item.adjustOtherFee;
                                    let percentageNum= adjustOtherFee-otherFee;
                                    if(percentageNum){
                                        let data=`<div class="flex">`;
                                        data+=`<span class="text-info">其他(现金)</span>`;


                                       
                                        if (otherFee > adjustOtherFee) {
                                            data+=`<span class="text-info">↓`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                           
                                        }else{
                                            data+=`<span class="text-danger">↑`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                           
                                        }
                                        data+=`</div>`;
                                        html.push(data);
                                    }
                                }

                                if(item.oilLoadingFee||item.adjustOilLoadingFee){
                                    let oilLoadingFee = item.oilLoadingFee;
                                    let adjustOilLoadingFee = item.adjustOilLoadingFee;
                                    let percentageNum= adjustOilLoadingFee-oilLoadingFee;
                                    if(percentageNum){
                                        let data=`<div class="flex">`;
                                        data+=`<span class="text-info">装卸费(油卡)</span>`;

                                        if (oilLoadingFee > adjustOilLoadingFee) {
                                            data+=`<span class="text-info">↓`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                           
                                        }else{
                                            data+=`<span class="text-danger">↑`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                           
                                        }
                                        data+=`</div>`;
                                        html.push(data);
                                    }
                                }

                                if(item.oilStorageFee||item.adjustOilStorageFee){
                                    let oilStorageFee = item.oilStorageFee;
                                    let adjustOilStorageFee = item.adjustOilStorageFee;
                                    let percentageNum= adjustOilStorageFee-oilStorageFee;
                                    if(percentageNum){
                                        let data=`<div class="flex">`;
                                        data+=`<span class="text-info">仓储费(油卡)</span>`;


                                        
                                        if (oilStorageFee > adjustOilStorageFee) {
                                            data+=`<span class="text-info">↓`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                            
                                        }else{
                                            data+=`<span class="text-danger">↑`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                            
                                        }
                                        data+=`</div>`;
                                        html.push(data);
                                    }
                                }

                                if(item.oilOtherFee||item.adjustOilOtherFee){
                                    let oilOtherFee = item.oilOtherFee;
                                    let adjustOilOtherFee = item.adjustOilOtherFee;
                                    let percentageNum= adjustOilOtherFee-oilOtherFee;
                                    if(percentageNum){
                                        let data=`<div class="flex">`;
                                        data+=`<span class="text-info">其他(油卡)</span>`;


                                        if (oilOtherFee > adjustOilOtherFee) {
                                            data+=`<span class="text-info">↓`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                           
                                        }else{
                                            data+=`<span class="text-danger">↑`+percentageNum.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                                            
                                        }
                                        data+=`</div>`;
                                        html.push(data);
                                    }
                                }
                            })
                        }

                        return html.join('')
                    }

                },
                {
                    title: '货量调整',
                    align: 'left',
                    field: 'sumTransFeeCountAfter',
                    formatter: function (value, row, index) {
                        let html=[];
                        let list="";
                        if(row.adjustRecordType == 2){
                            list=row.receiveVOS;
                        }else if(row.adjustRecordType==0){
                            list=row.payReconciliationVOS
                        }else if(row.adjustRecordType==1){
                            list=row.custOtherFeeVOList;
                        }
                        list.forEach(item=>{
                            if(item.numCount&&item.numCountAdjust){
                                if(item.numCount!=item.numCountAdjust){
                                    let num=item.numCountAdjust-item.numCount;
                                    if(item.numCount>item.numCountAdjust){
                                        html.push(`<div class="flex"><span>件数</span><span class="text-info">↓`+num+`</span></div>`);
                                    }else{
                                        html.push(`<div class="flex"><span>件数</span><span class="text-danger">↑`+num+`</span></div>`);
                                    }
                                }
                            }
                            if(item.weightCount&&item.weightCountAdjust){
                                if(item.weightCount!=item.weightCountAdjust){
                                    let num=item.weightCountAdjust-item.weightCount;
                                    if(item.weightCount>item.weightCountAdjust){
                                        html.push(`<div class="flex"><span>重量</span><span class="text-info">↓`+num+`</span></div>`);
                                    }else{
                                        html.push(`<div class="flex"><span>重量</span><span class="text-danger">↑`+num+`</span></div>`);
                                    }
                                }
                            }
                            if(item.volumeCount&&item.volumeCountAdjust){
                                if(item.volumeCount!=item.volumeCountAdjust){
                                    let num=item.volumeCountAdjust-item.volumeCount;
                                    if(item.volumeCount>item.volumeCountAdjust){
                                        html.push(`<div class="flex"><span>体积</span><span class="text-info">↓`+num+`</span></div>`);
                                    }else{
                                        html.push(`<div class="flex"><span>体积</span><span class="text-danger">↑`+num+`</span></div>`);
                                    }
                                }
                            }
                        })
                        return html.join('')
                    }
                },
                {
                    title: '发货单号/客户名称',
                    align: 'left',
                    field : 'invoiceVbillno',
                    formatter: function(value, row, index) {
                       return value+"<br/>"+row. custAbbr
                    }
                },
                {
                    title: '要求提/到货地址',
                    align: 'left',
                    field : 'deliAddress',
                    formatter: function(value, row, index) {
                        return `<span class="label label-warning pa2">提</span>`+$.table.tooltip(row.deliAddress,9)+`<br/><span class="label label-success pa2">到</span>`+$.table.tooltip(row.arriAddress,9);
                    }
                },
                {
                    title: '要求车长车型',
                    align: 'left',
                    field : 'carLenType'
                },
                {
                    title: '费用',
                    align: 'left',
                    field : 'receiveAmount',
                    formatter: function(value, row, index) {
                        let html=[]
                        if(row.receiveAmount){
                            html.push(`<span class="label label-coral pa2">应收</span>`+row.receiveAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if(row.payAmount){
                            html.push(`<span class="label badge-info pa2">应付</span>`+row.payAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        if(row.otherAmount){
                            html.push(`<span class="label label-primary pa2">三方</span>`+row.otherAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        return html.join("<br/>")
                    }
                },
                {
                    title: '承运商',
                    align: 'left',
                    field : 'carrName'
                },
                {   title: '附件',
                    field: 'sysUploadFiles',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var html = ""
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html +=  $.table.imageView(element.filePath)
                            });
                        }else {
                            html = '-';
                        }
                        return html;
                    }
                },
                {
                    title: '运营组',
                    align: 'left',
                    field: 'saleDeptName'
                }

            ]
        };

        $("#adjustCheckStatusArr").val(adjustCheckStatusArr);

        $.table.init(options);

        // var arr = [];
        // arr.push(segmentStatusList[0].value);
        // arr.push(segmentStatusList[1].value);
        // arr.push(segmentStatusList[6].value);
        // arr.push(segmentStatusList[7].value);
        //$('#adjustCheckStatusArr').selectpicker('val',arr);
        $("#adjustCheckStatusArr").find('option').eq(0).attr("selected",true);
        $("#adjustCheckStatusArr").find('option').eq(1).attr("selected",true);
        $("#adjustCheckStatusArr").find('option').eq(2).attr("selected",true);

        searchPre();

        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
    });

    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }


    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.adjustCheckStatusArr = $.common.join($('#adjustCheckStatusArr').selectpicker('val'));
        data.salesDeptPayDetail = $.common.join($('#salesDeptPayDetail').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        $("#adjustCheckStatusArr").find('option').eq(0).attr("selected",true);
        $("#adjustCheckStatusArr").find('option').eq(1).attr("selected",true);
        $("#adjustCheckStatusArr").find('option').eq(2).attr("selected",true);
        searchPre();
    }

    /**
     * 审核
     */
    function invoiceCheck() {
        var adjustRecordId = $.table.selectColumns('adjustRecordId').join();
        var adjustRecordType = $.table.selectColumns('adjustRecordType').join();
        var adjustCheckStatus = $.table.selectColumns('adjustCheckStatus').join();
        if(adjustCheckStatus != 0){
            $.modal.alertWarning("当前审核状态不需要进行业务审核！");
            return false;
        }
        var jumpUrl = prefix;
        if(adjustRecordType == 0){
            jumpUrl = ctx + "payDetailAdjustCheck";
        }else if(adjustRecordType == 1){
            jumpUrl = ctx + "otherFeeAdjustCheck";
        }else if(adjustRecordType == 2){
            jumpUrl = ctx + "receiveDetailAdjustCheck";
        }
        layer.open({
            type: 2,
            area: ['800px', '500px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "业务审核",
            content:  jumpUrl + "/invoiceCheck?adjustRecordId=" + adjustRecordId,
            btn: ['通过', '不通过','关闭'],
            shadeClose: true,
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitApprove(index, layero);
            },
            btn2: function(index, layero){
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitBack(index, layero);
                return false;
            },
            btn3: function (index, layero) {
                return true;
            }
        });
    }

    function leaderCheck() {
        var adjustRecordId = $.table.selectColumns('adjustRecordId').join();
        var adjustRecordType = $.table.selectColumns('adjustRecordType').join();
        var adjustCheckStatus = $.table.selectColumns('adjustCheckStatus').join();
        if(adjustCheckStatus != 1){
            $.modal.alertWarning("当前审核状态不需要进行总经办审核！");
            return false;
        }
        var jumpUrl = prefix;
        if(adjustRecordType == 0){
            jumpUrl = ctx + "payDetailAdjustCheck";
        }else if(adjustRecordType == 1){
            jumpUrl = ctx + "otherFeeAdjustCheck";
        }else if(adjustRecordType == 2){
            jumpUrl = ctx + "receiveDetailAdjustCheck";
        }
        layer.open({
            type: 2,
            area: ['800px', '500px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "总经办审核",
            content:  jumpUrl + "/leaderCheck?adjustRecordId=" + adjustRecordId,
            btn: ['通过', '不通过','关闭'],
            shadeClose: true,
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitApprove(index, layero);
            },
            btn2: function(index, layero){
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitBack(index, layero);
                return false;
            },
            btn3: function (index, layero) {
                return true;
            }
        });
    }

    function financeCheck() {
        var adjustRecordId = $.table.selectColumns('adjustRecordId').join();
        var adjustRecordType = $.table.selectColumns('adjustRecordType').join();
        var adjustCheckStatus = $.table.selectColumns('adjustCheckStatus').join();
        if(adjustCheckStatus != 2){
            $.modal.alertWarning("当前审核状态不需要进行财务确认！");
            return false;
        }
        var jumpUrl = prefix;
        if(adjustRecordType == 0){
            jumpUrl = ctx + "payDetailAdjustCheck";
        }else if(adjustRecordType == 1){
            jumpUrl = ctx + "otherFeeAdjustCheck";
        }else if(adjustRecordType == 2){
            jumpUrl = ctx + "receiveDetailAdjustCheck";
        }
        layer.open({
            type: 2,
            area: ['800px', '500px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "财务确认",
            content:  jumpUrl + "/financeCheck?adjustRecordId=" + adjustRecordId,
            btn: ['通过', '不通过','关闭'],
            shadeClose: true,
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitApprove(index, layero);
            },
            btn2: function(index, layero){
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitBack(index, layero);
                return false;
            },
            btn3: function (index, layero) {
                return true;
            }
        });
    }

    function jumpAdjustRecordId(adjustRecordId,adjustCheckStatus,adjustRecordType) {
        var jumpUrl = prefix;
        if(adjustRecordType == '0'){
            jumpUrl = ctx + "payDetailAdjustCheck";
        }else if(adjustRecordType == '1'){
            jumpUrl = ctx + "otherFeeAdjustCheck";
        }else if(adjustRecordType == '2'){
            jumpUrl = ctx + "receiveDetailAdjustCheck";
        }
        var url = jumpUrl + "/listDetail?adjustRecordId="+adjustRecordId+"&adjustCheckStatus="+adjustCheckStatus;
        $.modal.openTab("运单明细" , url);
    }

    function picUpload(adjustRecordId,adjustCheckStatus){
        var url =  ctx + "receiveDetailAdjustCheck/picUpload?adjustRecordId="+adjustRecordId;
        $.modal.open('附件上传',url,800,600);
    }

    function getCheckList(item) {
        let html="";
        html+=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'><div class='vertical-container light-timeline'>`;
            if(item.thirdCheckUserName){
                html+=`<div class='vertical-timeline-block leftIcon'>
                    <div class='vertical-timeline-icon'>
                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                    </div>
                    <div class='vertical-timeline-content'>
                        <div class=''>
                            <div class='fw f18'> 三级审核人: `+item.thirdCheckUserName+`</div>
                            <div class='fw f18'>
                                备注: `+(item.thirdCheckMemo== null?'暂无数据':item.thirdCheckMemo)+`
                            </div>
                            <div style='white-space: nowrap;'>`+item.thirdCheckDate+`</div>
                        </div>
                        <div class='mt10' style='white-space: initial;'></div>
                    </div>

                </div>`;
            }
            if(item.secondCheckUserName){
                html+=`<div class='vertical-timeline-block leftIcon'>
                    <div class='vertical-timeline-icon'>
                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                    </div>
                    <div class='vertical-timeline-content'>
                        <div class=''>
                            <div class='fw f18'> 二级审核人: `+item.secondCheckUserName+`</div>
                            <div class='fw f18'>
                                备注: `+(item.secondCheckMemo== null?'暂无数据':item.secondCheckMemo)+`
                            </div>
                            <div style='white-space: nowrap;'>`+item.secondCheckDate+`</div>
                        </div>
                        <div class='mt10' style='white-space: initial;'></div>
                    </div>

                </div>`;
            }
            if(item.firstCheckUserName){
                html+=`<div class='vertical-timeline-block leftIcon'>
                    <div class='vertical-timeline-icon'>
                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                    </div>
                    <div class='vertical-timeline-content'>
                        <div class=''>
                            <div class='fw f18'> 一级审核人: `+item.firstCheckUserName+`</div>
                            <div class='fw f18'>
                                备注: `+(item.firstCheckMemo== null?'暂无数据':item.firstCheckMemo)+`
                            </div>
                            <div style='white-space: nowrap;'>`+item.firstCheckDate+`</div>
                        </div>
                        <div class='mt10' style='white-space: initial;'></div>
                    </div>

                </div>`;
            }
            
            
           
          
        let title="",selectDictLabel=""
        if(item.budgetType==2||item.budgetType==4){
            title= "在途应收";
            selectDictLabel=$.table.selectDictLabel(payOrCollect, item.budgetType)+"/"+$.table.selectDictLabel(costTypeOnWay, item.costType)
        }else if(item.budgetType==1||item.budgetType==5||item.budgetType==null){
            if(item.isLotFee==0){
                title= "在途应付";
                selectDictLabel=$.table.selectDictLabel(payOrCollect, item.budgetType)+"/"+$.table.selectDictLabel(costTypeOnWay, item.costType)
            }else{
                title= "三方应付";
                selectDictLabel=$.table.selectDictLabel(costTypeOnWay, item.feeType)
            }
        }

        html+=`<div class='vertical-timeline-block leftIcon'>
                <div class='vertical-timeline-icon'>
                    <img src='/img/pep.png' style='width: 100%;height: 100%'>
                </div>
                <div class='vertical-timeline-content'>
                    <div class=''>
                        <div class='fw f18 mt10' style='white-space: nowrap;'>`+item.regUserName+`创建了`+title+`费用</div>
                        <div class='fw f18' style='white-space: nowrap;'>`+selectDictLabel+`</div>
                        <div style='white-space: nowrap;'>`+item.regDate+`</div>
                    </div>
                    <div class='mt10' style='white-space: initial;'></div>
                </div>
            </div>`;
            
        html+=`</div></div></div></div>`;
        return html
    }

</script>
</body>
</html>