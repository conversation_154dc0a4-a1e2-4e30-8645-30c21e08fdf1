<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('demo')"/>
</head>

<style type="text/css">
    .custom-tab td{
        text-align: left;
    }
</style>

<body>
<div class="form-content">
    <form id="form-allocation-edit" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group">
            <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapseTwo">成本信息</a>
                        </h4>
                    </div>
                    <div class="panel-collapse collapse in" id="collapseTwo">
                        <div class="panel-body">

                            <div class="fixed-table-body" style="margin: 0px -5px;">
                                <table border="0" class="custom-tab table">
                                    <thead>
                                    <tr>
                                        <th style="width: 10%;">发货单号</th>
                                        <th style="width: 10%;">客户编号</th>
                                        <th style="width: 12%;">客户名称</th>
                                        <th style="width: 10%;">运单号</th>
                                        <th style="width: 10%;">委托单号</th>
                                        <th style="width: 10%;">费用类型</th>
                                        <th style="width: 10%;">费用明细</th>
                                        <th style="width: 10%;">金额(¥)</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr th:each="allocation,stat: ${list}">
                                        <input type="hidden" th:name="'costAllocationId_'+${stat.index}"
                                               th:value="${allocation.costAllocationId}">
                                        <td th:text="${allocation.invoiceNo}"></td>
                                        <td th:text="${allocation.custCode}"></td>
                                        <td th:text="${allocation.custAbbr}"></td>
                                        <td th:text="${allocation.lot}"></td>
                                        <td th:text="${allocation.entrustNo}"></td>
                                        <td th:text="${allocation.freeTypeName}"></td>
                                        <td th:if="${allocation.freeType=='1'&&allocation.costTypeOnWay!=null}" th:text="${@dict.getLabel('cost_type_on_way',allocation.costTypeOnWay)}"></td>
                                        <td th:if="${allocation.freeType=='0'&&allocation.costTypeFreight!=null}" th:text="${@dict.getLabel('cost_type_freight',allocation.costTypeFreight)}"></td>
                                        <td th:if="${allocation.freeType=='1'&&allocation.costTypeOnWay==null}"></td>
                                        <td th:if="${allocation.freeType=='0'&&allocation.costTypeFreight==null}"></td>
                                        <td th:if="${allocation.freeType!='0'&&allocation.freeType!='1'}"></td>

                                        <td><input th:id="'costShare_'+${stat.index}"
                                                   th:name="'costShare_'+${stat.index}" class="form-control costShare"
                                                   type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculate()" required maxlength="20"
                                                   th:value="${allocation.costShare}" aria-required="true">
                                        </td>
                                    </tr>
                                    <input type="hidden" id="payDetailId" name="payDetailId" th:value="${payDetailId}">
                                    <input type="hidden" id="freeType" name="freeType" th:value="${freeType}">
                                    <input type="hidden" id="totalCount" th:value="${total}">
                                    <input type="hidden" id="total" name="total" th:value="${total}">
                                    <input type="hidden" id="listSize" name="listSize" th:value="${listSize}">
                                    </tbody>
                                </table>
                                <div style="color:red">实际总金额：<span th:text="${total}"></span> &nbsp;&nbsp;&nbsp;<span>提示：</span><span id="alertBox"></span></div>

                            </div>
                        </div>
                    </div>
                </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseThree">发货单信息</a>

                    </h4>
                </div>
                <div id="collapseThree" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--订单货品费用明细 begin-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabThree1" class="custom-tab table" >
                                <thead>
                                <tr>
                                    <th style="width: 10%;text-align:center">发货单号</th>
                                    <th style="width:  10%;text-align:center">要求提货日期</th>
                                    <th style="width: 20%;text-align:center">起始地</th>
                                    <th style="width:  20%;text-align:center">目的地</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="invoice:${invoiceList}">
                                    <td th:text="${invoice.vbillno}"></td>
                                    <td th:text="${#dates.format(invoice.reqDeliDate, 'yyyy-MM-dd')}"></td>
                                    <td th:text="${invoice.deliProName+invoice.deliCityName+invoice.deliAreaName+invoice.deliDetailAddr}"></td>
                                    <td th:text="${invoice.arriProName+invoice.arriCityName+invoice.arriAreaName+invoice.arriDetailAddr}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">发货单货品明细</a>

                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--订单货品费用明细 begin-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabThree" class="custom-tab table" >
                                <thead>
                                <tr>
                                    <th style="width: 10%;text-align:center">发货单号</th>
                                    <th style="width: 10%;text-align:center">货品编码</th>
                                    <th style="width:  10%;text-align:center">货品名称</th>
                                    <th style="width: 10%;text-align:center">件数</th>
                                    <th style="width:  10%;text-align:center">重量(吨)</th>
                                    <th style="width:  10%;text-align:center">体积(m3)</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="goods:${invPackGoodsList}">
                                    <td th:text="${goods.invoiceId}"></td>
                                    <td th:text="${goods.goodsCode}"></td>
                                    <td th:text="${goods.goodsName}"></td>
                                    <td th:text="${goods.num}"></td>
                                    <td th:text="${goods.weight}"></td>
                                    <td th:text="${goods.volume}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button id="save" type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i
                class="fa fa-check"></i>保
            存
        </button>
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script>
    var prefix = ctx + "trace/allocation";

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');

        var options = {};
        $.table.init(options);
        $('#save').attr("disabled", false);
    });

    //获取后台传过来的总金额
    var totalCount = $('#totalCount').val();

    //计算总金额
    function calculate() {
        var total = 0;
        if (total != totalCount) {
            $('#save').attr("disabled", true);
        }
        var list = document.getElementsByClassName("costShare");
        for (var i = 0; i < list.length; i++) {
            if (list[i].value == "") {
                list[i].value = 0;
            }
            total += Number(list[i].value);
        }

        //输入框总金额小于实际总金额，提示清空
        if (total != totalCount) {
            $('#alertBox').text("当前总金额为"+total+"，与实际总金额不相等，请继续分摊金额");
        }
        //输入框总金额大于实际总金额，提示信息
        /*  if (total > totalCount) {
              $('#alertBox').text("总金额超出，请继续分摊金额");
          }*/
        //输入框总金额等于分摊总金额，保存按钮可用
        if (total == totalCount) {
            $('#alertBox').text("");
            $('#save').attr("disabled", false);
        }
        //将输入框总金额放入Input框，传到后台校验是否与当下的总金额一致
        $('#total').val(total);
    }

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/edit", $('#form-allocation-edit').serialize());
        }
    }
</script>
</body>

</html>