<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('成本分摊-detail')"/>
</head>
<style type="text/css">
    .custom-tab td {
        text-align: left;
    }
</style>
<body>
<div class="form-content">
    <form id="form-user-add" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">运单号：</label>
                                    <div class="col-sm-7" th:text="${allocation.lot}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">分摊金额(¥)：</label>
                                    <div class="col-sm-7"
                                         th:text="${#numbers.formatDecimal(allocation.costShare,0,'COMMA',2,'POINT')}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">车长（米）：</label>
                                    <div class="col-sm-7" th:if="${allocation.carLen!=null}"
                                         th:text="${@dict.getLabel('car_len',allocation.carLen)}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">车型：</label>
                                    <div class="col-sm-7" th:if="${allocation.carType!=null}"
                                         th:text="${@dict.getLabel('car_type',allocation.carType)}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFour">委托单信息</a>
                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseFour">
                    <div class="panel-body">

                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 15%;">委托单号</th>
                                    <th style="width: 15%;">总件数（件）</th>
                                    <th style="width: 15%;">总重量（吨）</th>
                                    <th style="width: 15%;">总体积（m3）</th>
                                    <th style="width: 20%;">发货地址</th>
                                    <th style="width: 20%;">收货地址</th>
                              <!--      <th style="width: 10%;">实际提货时间</th>
                                    <th style="width: 10%;">实际到货时间</th>-->
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="entrust:${entrustList}">
                                <td th:text="${entrust.vbillno }"></td>
                                <td th:text="${entrust.numCount}"></td>
                                <td th:text="${entrust.weightCount}"></td>
                                <td th:text="${entrust.volumeCount}"></td>
                                <td th:text="${entrust.deliDetailAddr }"></td>
                                <td th:text="${entrust.arriDetailAddr }"></td>
                             <!--   <td th:text="${#dates.format(entrust.actDeliDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                <td th:text="${#dates.format(entrust.actArriDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                               --> </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">货品明细</a>
                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseTwo">
                    <div class="panel-body">

                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 10%;">货品名称</th>
                                    <th style="width: 12%;">货品编码</th>
                                    <th style="width: 10%;">重量（吨）</th>
                                    <th style="width: 10%;">件数（件）</th>
                                    <th style="width: 10%;">体积（m3）</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="good : ${goodsList}">
                                    <td th:text="${good.goodsName }"></td>
                                    <td th:text="${good.goodsCode }"></td>
                                    <td th:text="${good.weight }"></td>
                                    <td th:text="${good.num }"></td>
                                    <td th:text="${good.volume }"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseThree">成本信息</a>
                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseThree">
                    <div class="panel-body">
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 10%;">发货单号</th>
                                    <th style="width: 10%;">客户编号</th>
                                    <th style="width: 12%;">客户名称</th>
                                    <th style="width: 10%;">运单号</th>
                                    <th style="width: 10%;">委托单号</th>
                                    <th style="width: 10%;">费用类型</th>
                                    <th style="width: 10%;">费用明细</th>
                                    <th style="width: 10%;">金额(¥)</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="allocation,stat: ${list}">
                                    <input type="hidden" th:name="'costAllocationId_'+${stat.index}"
                                           th:value="${allocation.costAllocationId}">
                                    <td th:text="${allocation.invoiceNo}"></td>
                                    <td th:text="${allocation.custCode}"></td>
                                    <td th:text="${allocation.custAbbr}"></td>
                                    <td th:text="${allocation.lot}"></td>
                                    <td th:text="${allocation.entrustNo}"></td>
                                    <td th:text="${allocation.freeTypeName}"></td>
                                    <td th:if="${allocation.freeType=='1'&&allocation.costTypeOnWay!=null}"
                                        th:text="${@dict.getLabel('cost_type_on_way',allocation.costTypeOnWay)}"></td>
                                    <td th:if="${allocation.freeType=='0'&&allocation.costTypeFreight!=null}"
                                        th:text="${@dict.getLabel('cost_type_freight',allocation.costTypeFreight)}"></td>
                                    <td th:if="${allocation.freeType=='1'&&allocation.costTypeOnWay==null}"></td>
                                    <td th:if="${allocation.freeType=='0'&&allocation.costTypeFreight==null}"></td>
                                    <td th:if="${allocation.freeType!='0'&&allocation.freeType!='1'}"></td>
                                    <td th:text="${allocation.costShare}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <!--<button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;-->
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script>
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');

        var options = {};
        $.table.init(options);
    });


</script>
</body>

</html>