<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('成本分摊列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">客户名称：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control"
                                       placeholder="请输入客户名称" name="custAbbr">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运单号：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" placeholder="请输入运单号"
                                       name="lot" th:value="${entrustLotNo}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">对账单状态：</label>
                            <div class="col-sm-8">
                                <select name="vbillstatus" id="vbillstatus" class="form-control">
                                    <option></option>
                                    <option value="0" selected>新建</option>
                                    <option value="1">已确认</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <label class="col-sm-4"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" id="search" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="trace:allocation:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">
    $("#distpicker").distpicker();
    $("#distpicker1").distpicker();
    var prefix = ctx + "trace/allocation";
    var height = document.documentElement.clientHeight - 200;
    var width = document.documentElement.clientWidth - 320;
    var feeTypeList = [[${feeTypeList}]];
    var cost_type_on_way = [[${@dict.getType('cost_type_on_way')}]];
    var cost_type_freight = [[${@dict.getType('cost_type_freight')}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            removeUrl: prefix + "/remove",
            showToggle: false,
            showColumns: true,
            modalName: "成本分摊",
            fixedColumns: true,
            fixedNumber: 0,
            clickToSelect: true,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    width: '120px',
                    field: 'costAllocationId',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)" title="修改" onclick="edit(\'' + row.payDetailId + '\',\''+row.invoiceId+'\')"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>');
                        actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="明细"  onclick="detail(\'' + row.payDetailId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        return actions.join('');
                    }
                },
          /*      {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceNo'
                },*/
                {
                    title: '运单号',
                    align: 'left',
                    field: 'lot'
                },
                {
                    title: '费用类型',
                    align: 'left',
                    field: 'freeType',
                    formatter: function status(row, value) {
                        var context = '';
                        feeTypeList.forEach(function (v) {
                            if (v.value == value.freeType) {
                                context = '<span class="label label-primary">' + v.context + '</span>';
                                return false;
                            }
                        });
                        return context;
                    }
                },
                {
                    title: '状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value, row) {
                        if (value == 0) {
                            return'<span class="label label-primary">新建</span>';
                        }else if (value == 1) {
                            return'<span class="label label-primary">已确认</span>';
                        }
                    }
                },
                {
                    title: '费用类型明细',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (row.freeType == '1') {
                            if (row.costTypeOnWay != null) {
                                return $.table.selectDictLabel(cost_type_on_way, row.costTypeOnWay);
                            }
                        } else {
                            if (row.costTypeFreight != null) {
                                return $.table.selectDictLabel(cost_type_freight, row.costTypeFreight);
                            }
                        }
                    }
                },
                {
                    title: '金额(元)',
                    halign: "center",
                    align: 'right',
                    field:'costShare',
                    formatter: function (value, row, index) {
                        return row.costShare.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '承运商名称',
                    align: 'left',
                    field: "carrName"
                },
                {
                    title: '承运商电话',
                    align: 'left',
                    field: "phone"
                },{
                    title: '调度人',
                    align: 'left',
                    field: "dispatchName"
                },{
                    title: '调度时间',
                    align: 'left',
                    field: "dispatchDate"
                },
                {
                    title: '车牌号',
                    align: 'left',
                    field: "carNo"
                },
                {
                    title: '地址',
                    align: 'left',
                    field: "deliDetailAddr",
                    formatter: function status(value, row, index) {
                        var deliDetailAddr = row.deliDetailAddr == null ? "暂无" : row.deliDetailAddr;
                        var arriDetailAddr = row.arriDetailAddr == null ? "暂无" : row.arriDetailAddr;
                        return deliDetailAddr
                            + '<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>' + arriDetailAddr;
                    }
                }
            ]
        };
        $.table.init(options);
    });

    //详情
    function detail(payDetailId) {
        var url = prefix + "/detail/" + payDetailId;
        $.modal.openTab("成本分摊详情", url);
    }

    /**
     * 成本分摊
     */
    function edit(payDetailId,invoiceId) {
        //验证发货单是否超过五天
        $.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    var url = prefix + "/edit?payDetailId=" + payDetailId;
                    $.modal.openTab("成本分摊修改", url);
                }
            }
        });



    }


</script>
</body>
</html>