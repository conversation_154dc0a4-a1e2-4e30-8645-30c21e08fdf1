<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应收明细新增页面')"/>
</head>

<body>
<div class="form-content">
    <form id="form-receive-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="invoiceId" type="hidden" id="invoiceId">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5" style="color: red; ">
                                        发货单号：</label>
                                    <div class="col-sm-7">
                                        <div class="input-group">
                                            <input name="invoiceVbillno" id="invoiceVbillno" class="form-control valid" type="text"
                                                    onclick="selectInvoice()" required maxlength="25"> <span
                                                class="input-group-addon"><i
                                                class="fa fa-search" onclick="selectInvoice()"></i></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        结算客户：</label>
                                    <div class="col-sm-7">
                                        <input name="balaName" id="balaName" class="form-control dis" type="text"
                                               disabled>
                                        <input name="balatype" id="balatype" class="form-control" type="hidden" >
                                        <input name="balaCode" id="balaCode" class="form-control" type="hidden">
                                        <input name="balaCustomer" id="balaCustomer" class="form-control" type="hidden">
                                        <input name="balaDept" id="balaDept" class="form-control" type="hidden">
                                        <input name="psndoc" id="psndoc" class="form-control" type="hidden">
                                        <input name="salesDept" id="salesDept" class="form-control" type="hidden">
                                        <input name="custOrderno" id="custOrderno" class="form-control" type="hidden">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        结算公司：</label>
                                    <div class="col-sm-7">
                                        <input name="balaCorpName" id="balaCorpName" class="form-control dis" type="text"
                                               maxlength="30" disabled>
                                        <input name="balaCorp" id="balaCorp" class="form-control dis" type="hidden"
                                               maxlength="30" >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        客户名称：</label>
                                    <div class="col-sm-7">
                                        <input name="custName" id="custName" class="form-control dis" type="text"
                                               disabled>
                                        <input name="custCode" id="custCode" class="form-control" type="hidden">
                                        <input name="customerId" id="customerId" class="form-control" type="hidden">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">计费件数：</label>
                                    <div class="col-sm-7">
                                        <input name="numCount" id="numCount" class="form-control dis" disabled/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        计费重量：</label>
                                    <div class="col-sm-7">
                                        <input name="feeWeightCount" id="feeWeightCount" class="form-control dis" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        计费体积：</label>
                                    <div class="col-sm-7">
                                        <input name="volumeCount" id="volumeCount" class="form-control dis"
                                               disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">要求提货日期：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control dis"
                                               name="reqDeliDate" id="reqDeliDate" autocomplete="off" disabled>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">要求到货日期：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control dis"
                                               name="reqArriDate" id="reqArriDate" autocomplete="off" disabled>
                                    </div>
                                </div>
                            </div>


                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">费用类型：</label>
                                    <div class="col-sm-7">
                                        <select name="costTypeOnWay" id="costTypeOnWay" class="form-control valid"
                                                th:with="type=${@dict.getType('cost_type_on_way')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5" style="color: red; ">
                                        费用金额：</label>
                                    <div class="col-sm-7">
                                        <input name="transFeeCount" id="transFeeCount" class="form-control"
                                               type="text"  oninput="$.numberUtil.onlyNumberTwoDecimal(this)"  required maxlength="10">
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-3">备注：</label>
                                    <div class="col-sm-12">
                                            <textarea name="memo" id="memo" maxlength="250" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--基础信息 end-->
                    </div>
                </div>
            </div>


        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFive">货品明细</a>
                </h4>
            </div>
            <div id="collapseFive" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 15%;">货品类型</th>
                                <th style="width: 12%;">货品名称</th>
                                <th style="width: 8%;">件数</th>
                                <th style="width: 8%;">重量</th>
                                <th style="width: 8%;">体积</th>
                                <th style="width: 8%;">金额</th>
                            </tr>
                            </thead>
                            <tbody id="goods">
                            <tr ></tr>
                            </tbody>
                        </table>
                    </div>
                    <!--订单货品费用明细 end-->
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">发货单提货信息</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">APP联系人：</label>
                                <div class="col-sm-8">
                                    <div class="input-group" id="appDeliContact"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">APP联系人手机：</label>
                                <div class="col-sm-8">
                                    <div class="input-group" id="appDeliMobile"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">提货方：</label>
                                <div class="col-sm-8">
                                    <div class="input-group" id="deliAddrName">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">联系电话：</label>
                                <div class="col-sm-8" id="deliMobile">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">联系人：</label>
                                <div class="col-sm-8" id="deliContact">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">

                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-1">地址：</label>
                                <div class="col-sm-11" id="deliAddr">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseThree">发货单收货信息</a>
                </h4>
            </div>
            <div id="collapseThree" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">收货方：</label>
                                <div class="col-sm-8">
                                    <div class="input-group" id="arriAddrName">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">联系电话：</label>
                                <div class="col-sm-8" id="arriMobile">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">联系人：</label>
                                <div class="col-sm-8" id="arriContact">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">

                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-1">地址：</label>
                                <div class="col-sm-11" id="arriAddr">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="commit()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">

    var prefix = ctx + "receive";


    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');

        var options = {};
        $.table.init(options);

        var flag = [[${flag}]];
        if(flag == '1'){

            var invoice = [[${invoice}]];

            // 选中的发货单号
            $("#invoiceVbillno").val(invoice.vbillno);
            // 发货单Id
            $("#invoiceId").val(invoice.invoiceId);
            // 结算公司
            var datas = [[${@dict.getType('bala_corp')}]];
            for ( var i = 0; i < datas.length; i++) {
                if (datas[i].dictValue === invoice.balaCorpId) {
                    $("#balaCorpName").val(datas[i].dictLabel);
                    $("#balaCorp").val(datas[i].dictValue);
                }
            }
            // 选中的结算组
            $("#balaDept").val(invoice.balaDept);
            // 业务员
            $("#psndoc").val(invoice.psndoc);
            // 运营部
            $("#salesDept").val(invoice.salesDept);
            // 选中的结算客户名
            $("#balaName").val(invoice.balaName);
            $("#balaCode").val(invoice.balaCode);
            $("#balaCustomer").val(invoice.balaCustomerId);
            // 选中的结算方式
            $("#balatype").val(invoice.balaType);
            // 选中的计费件数
            $("#numCount").val(invoice.numCount);
            // 选中的计费重量
            $("#feeWeightCount").val(invoice.weightCount);
            // 选中的计费体积
            $("#volumeCount").val(invoice.volumeCount);
            // 选中的运费
            $("#arriPayAmount").val(invoice.arriPayAmount);
            // 选中的总金额
            $("#totalCount").val(invoice.costAmount);
            // 选中的要求提货日期
            $("#reqDeliDate").val(invoice.reqDeliDate);
            // 选中的发货单状态
            $("#vbillstatus").val(invoice.vbillstatus);
            // 选中的客户名称
            $("#custName").val(invoice.custAbbr);
            $("#custCode").val(invoice.custCode);
            $("#customerId").val(invoice.customerId);
            // 选中的要求到货日期
            $("#reqArriDate").val(invoice.reqArriDate);
            // 客户订单号
            $("#custOrderno").val(invoice.custOrderno);
            // 地址信息
            $.ajax({
                type: "get",
                url:  ctx +"receive/selectInvoiceById?invoiceId="+invoice.invoiceId,
                success: function(result) {
                    $("#appDeliContact").html(result.invoice.appDeliContact);
                    $("#appDeliMobile").html(result.invoice.appDeliMobile);
                    $("#deliAddrName").html(result.invoice.deliAddrName);
                    $("#deliMobile").html(result.invoice.deliMobile);
                    $("#deliContact").html(result.invoice.deliContact);
                    $("#deliAddr").html(result.invoice.deliProName+result.invoice.deliCityName+result.invoice.deliAreaName+result.invoice.deliDetailAddr);
                    $("#arriAddrName").html(result.invoice.arriAddrName);
                    $("#arriMobile").html(result.invoice.arriMobile);
                    $("#arriContact").html(result.invoice.arriContact);
                    $("#arriAddr").html(result.invoice.arriProName+result.invoice.arriCityName+result.invoice.arriAreaName+result.invoice.arriDetailAddr);

                    // 清空历史
                    $("#goods").html("");
                    var html = '';
                    for (var i = 0; i < result.invoice.invPackGoodsList.length; i++) {
                        html += '<tr>' +
                            '<td ><div class="input-group">'+result.invoice.invPackGoodsList[i].goodsTypeName+'</div></td>' +
                            '<td ><div class="input-group">'+result.invoice.invPackGoodsList[i].goodsName+'</div></td>' +
                            '<td ><div class="input-group">'+result.invoice.invPackGoodsList[i].num+'</div></td>' +
                            '<td ><div class="input-group">'+result.invoice.invPackGoodsList[i].weight+'</div></td>' +
                            '<td ><div class="input-group">'+result.invoice.invPackGoodsList[i].volume+'</div></td>' +
                            '<td  >' +result.invoice.invPackGoodsList[i].sum+'</td>' +
                            '</tr>'
                    }
                    $("#goods").append(html);
                }
            });
        }


    });

    var customerId = [[${customerId}]];
    /**
     * 发货单的选择框
     */
    function selectInvoice() {

        $.modal.open("选择发货单", prefix + "/chooseInvoice?customerId="+customerId);
    }

    /**
     * 表单提交
     */
    function  commit(){
        if ($.validate.form()) {
            $('.dis').removeAttr("disabled");

            // 表单提交
            $.operate.saveTab(prefix + "/addReceive", $('#form-receive-add').serialize());
        }
    }





</script>
</body>

</html>