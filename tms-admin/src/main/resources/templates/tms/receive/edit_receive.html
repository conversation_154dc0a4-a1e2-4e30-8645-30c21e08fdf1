<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('费用调整')"/>
</head>

<body>
<div class="form-content">
    <form id="form" class="form-horizontal" novalidate="novalidate">
        <div class="panel-body">
            <!--基础信息 begin-->
            <input name="invoiceId" id="invoiceId" type="hidden" th:value="${invoiceId}">
            <input name="status" id="status" type="hidden" value="-1">
            <div class="row">
                <div class="col-md-4 col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-2">调整后总金额：</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" id="amount" name="amount"
                                   oninput="$.numberUtil.onlyNumberTwoDecimal(this)" placeholder="金额" autocomplete="off">
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-12">
                    <div class="form-group">
                        <label class="col-md-1 col-sm-2">类型：</label>
                        <div class="col-md-11 col-sm-10">
                            <select name="type" id="type" class="form-control valid" aria-invalid="false">
                                <option value="0">运费</option>
                                <option value="1">在途</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-12">
                    <div class="form-group">
                        <label class="col-md-4" th:text="'运费总金额： ￥'+${costAmount}"></label>
                    </div>
                </div>
                <div class="col-md-4 col-sm-12">
                    <div class="form-group">
                        <label class="col-md-4" th:text="'在途费总金额： ￥'+${onWayAmount}"></label>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "tms/segment";

    //运费合计
    var costAmount = [[${costAmount}]];
    //在途费用合计
    var onWayAmount = [[${onWayAmount}]];

    /**
     * 校验
     */
    $("#form").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            amount:{
                required:true,
            },
            type:{
                required:true,
            }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form").serializeArray();
            $.operate.save(ctx + "receive/edit_receive", data);
        }
    }

</script>
</body>
</html>