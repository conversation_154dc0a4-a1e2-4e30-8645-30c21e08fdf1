<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应收汇总列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: bootstrap-editable-css" />

</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 70px);
        padding-top: 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-2 col-sm-4" style="padding-right: 0;">
                        <div class="form-group">
                            <!--                            <label class="col-sm-2">调整时间：</label>-->
                            <div class="">
                                <input type="text" placeholder="调整开始时间" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="params[startDate]">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" placeholder="调整结束时间" style="width: 45%; float: left;" class="form-control"
                                       id="endtDate"  name="params[endtDate]">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-8" style="padding: 0">
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">运营组：</label>-->
                                <div class="col-sm-12">
                                    <select name="salesDeptName" id="salesDeptName" class="form-control selectpicker"
                                            aria-invalid="false" data-none-selected-text="运营组" required>
                                        <option value=""></option>
                                        <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                                th:text="${mapS.deptName}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户简称：</label>-->
                                <div class="col-sm-12">
                                    <input name="custAbbr" id="custAbbr" placeholder="请输入客户简称"
                                           class="form-control valid" type="text" maxlength="25">
                                    <input name="customerId" id="customerId" type="hidden" th:value="${customerId}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">发货单号：</label>-->
                                <div class="col-sm-12">
                                    <input name="vbillno" id="vbillno" class="form-control" type="text"
                                           placeholder="请输入发货单号"
                                           maxlength="25" required="" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">发货单状态：</label>-->
                                <div class="col-sm-12">
                                    <select  id="invoice_vbillstatus" class="form-control selectpicker"
                                             aria-invalid="false" data-none-selected-text="发货单状态" multiple th:with="type=${invoiceStatusList}">
                                        <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-8" style="padding: 0">
                        <div class="col-md-5 col-sm-5">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">调整人：</label>-->
                                <div class="col-sm-12">
                                    <input name="regUserName" id="regUserName" class="form-control"
                                           placeholder="请输入调整人" maxlength="25">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-7 col-sm-7">
                            <div class="form-group">
                                <!--                            <label class="col-sm-2">要求提货日：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" placeholder="要求提货开始日" style="width: 45%; float: left;" class="form-control"
                                           id="reqDeliDateStart"  name="params[reqDeliDateStart]">
                                    <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                    <input type="text" placeholder="要求提货截止日" style="width: 45%; float: left;" class="form-control"
                                           id="reqDeliDateEnd"  name="params[reqDeliDateEnd]">
                                </div>
                            </div>
                        </div>
                    </div>

                   <!-- <div class="col-md-2 col-sm-4">
                        <div class="form-group">
&lt;!&ndash;                            <label class="col-sm-4">客户订单号：</label>&ndash;&gt;
                            <div class="col-sm-12">
                                <input name="custOrderno"  class="form-control"
                                       placeholder="请输入客户订单号" maxlength="50">
                            </div>
                        </div>
                    </div>-->

                </div>
                <div class="row no-gutter">
                    <div class="col-md-5 col-sm-5">
                        <div class="form-group">
<!--                            <label class="col-sm-4">提货方地址：</label>-->
                            <div class="col-sm-9">
                                <div class="col-sm-4">
                                    <select  name="params[deliProvinceId]" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="params[deliCityId]" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="params[deliAreaId]" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <input name="params[deliDetailAddr]" id="deliDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                       maxlength="50" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5 col-sm-5">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">收货方地址：</label>-->
                            <div class="col-sm-2" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 40px;height: 40px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-7">
                                <div class="col-sm-4">
                                    <select  name="params[arriProvinceId]" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="params[arriCityId]" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="params[arriAreaId]" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <input name="params[arriDetailAddr]" id="arriDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                       maxlength="50" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>

            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:receive:receiveAdjustExport">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>


    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: bootstrap-table-editable-js" />

<script th:inline="javascript">
    var vbillstatus = [[${invoiceStatusList}]]; //发货单状态
    //发货单状态map
    var invoiceStatusMap = [[${invoiceStatusMap}]];

    var balaCorp = [[${@dict.getType('bala_corp')}]];
    var balatype = [[${@dict.getType('bala_type')}]];
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var prefix = ctx + "custReceive";

    // 初始化省市区
    $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
    $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

    //客户id
    var customerId = $("#customerId").val();

    //合计
    var receiptAmountTotal = 0;//总金额
    var receiptAmountAdjustTotal = 0;//总调整金额

    $(function () {
        $("#startDate").val(getFrontFormatDate())

        var options = {
            url: prefix + "/adjustList",
            createUrl: prefix + "/add?customerId="+$("#customerId").val(),
            exportUrl: prefix + "/export",
            importUrl: prefix + "/adjustImport",
            showToggle: false,
            showColumns: true,
            modalName: "应收明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 6,
            clickToSelect: true,
            showFooter:true,
            uniqueId: "uniqueId",
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1,7],
                exportHiddenColumns: [8,9],
                fileName:"客户应收明细"
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onEditableSave: function (field, row, oldValue, $el) {
                var isClose = row.isClose;
                if(isClose == 1){
                    $.modal.alertWarning("该月份已关账，无法进行操作！");
                    return false;
                }else{
                    var data;
                    if (field === 'freightTotal') {
                        data = {invoiceId: row.invoiceId, amount: row.freightTotal,status: row.receiveVbillstatus, type: 0};
                        editReceive(data);
                    } else if(field === 'onWayTotal'){
                        data = {invoiceId: row.invoiceId, amount: row.onWayTotal,status: row.receiveVbillstatus, type: 1};
                        editReceive(data);
                    }else if (field === 'custOrderno') {
                        data = {invoiceId: row.invoiceId,orderNo:row.custOrderno}
                        editCustOrderno(data);
                    }
                }

            },
            onPostBody: function () {
                getAmountCount();
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "合计:&nbsp&nbsp"
                        + "总金额:<nobr id='receiptAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        + "调整后总金额:<nobr id='receiptAmountAdjustTotal'>￥0</nobr>&nbsp&nbsp<br>" +
                        "<b>总合计：</b>  总金额：<nobr id='receiptAmountTotalAll'>￥0</nobr>&nbsp&nbsp" +
                        "调整后总金额：<nobr id='receiptAmountAdjustTotalAll'>￥0</nobr>&nbsp&nbsp" ;
                }
            }, {
                title: '操作',
                align: 'left',
                field: 'invoiceId',
                formatter: function (value,row,index) {
                    var actions = [];
                    if ([[${@permission.hasPermi('finance:receive:receiveDetail')}]] != "hidden") {
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="detail(\'' + value + '\',\''+ row.receiveVbillstatus +'\')"><i  class="fa fa-newspaper-o" style="font-size: 15px;" ></i></a>');
                    }
                    return actions.join('');
                }

            },

              /*  {title: '应收单据状态',field: 'receiveVbillstatus',align: 'left',

                    formatter: function status(value, row, index) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-primary">新建</span>'
                            case 1:
                                return '<span class="label label-warning">已确认</span>';
                            case 2:
                                return '<span class="label label-coral">已对账</span>';
                            case 3:
                                return '<span class="label label-info">部分核销 </label>';
                            case 4:
                                return '<span class="label label-success">已核销</span>';
                            case 5:
                                return '<span class="label label-inverse">关闭 </label>';
                            default:
                                break;
                        }
                    }
                },*/
                // {title: '发货单号',align: 'left',field: 'vbillno'},

                {title: '发货单号/发货单状态', align: 'left', field: 'invoiceStatus', switchable: false,
                    formatter: function status(row,value) {
                        var context = '';

                        vbillstatus.forEach(function (v) {
                            if (v.value == value.invoiceStatus) {

                                if (value.invoiceStatus == invoiceStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-primary">'+v.context+'</span>';
                                }else if (value.invoiceStatus == invoiceStatusMap.AFFIRM) {
                                    //已确认
                                    context = '<span class="label label-warning">'+v.context+'</span>';
                                }else if (value.invoiceStatus == invoiceStatusMap.PORTION_PICK_UP
                                    || value.invoiceStatus == invoiceStatusMap.PICK_UP) {
                                    //部分提货 与 已提货
                                    context = '<span class="label label-info">'+v.context+'</span>';
                                }else if (value.invoiceStatus == invoiceStatusMap.PORTION_ARRIVALS
                                    || value.invoiceStatus == invoiceStatusMap.ARRIVALS) {
                                    //部分到货  已到货
                                    context = '<span class="label label-success">'+v.context+'</span>';
                                }else if (value.invoiceStatus == invoiceStatusMap.PORTION_RETURNS
                                    || value.invoiceStatus == invoiceStatusMap.RETURNS) {
                                    //部分回单  已回单
                                    context = '<span class="label label-primary">' + v.context + '</span>';
                                } else {
                                    //关闭
                                    context = '<span class="label label-inverse">' + v.context + '</span>';
                                }

                                return false;
                            }
                        });
                        return value.vbillno + '<br />'+context;
                    }
                },
                {title: '客户简称', align: 'left', field: 'custAbbr'},

                {title: '要求提货日', align: 'left', field: 'reqDeliDate',
                        formatter: function status(value, row, index) {
                            if(value == "" || value == null || value == 'undefined'){
                                return "";
                            }
                            return value.substring(0,10);
                        }
                },
                {title: '提货|到货省市区', align: 'left', field: 'deliAddr',
                    formatter: function status(value, row, index) {
                        if(row.deliAddr == null || row.deliAddr == ""){
                            row.deliAddr = '';
                        }
                        if(row.arriAddr == null || row.arriAddr.arriAddr == ""){
                            row.arriAddr = ''
                        }
                        if(row.deliAddr == "" && row.arriAddr == ""){
                            return "";
                        }else{
                            return `<span class="label label-warning pa2">提</span>`+row.deliAddr+`<br/><span class="label label-success pa2">到</span>`+row.arriAddr;
                        }
                    }},
                {title: '提货省市区', align: 'left', field: 'deliAddr',visible: false},
                {title: '到货省市区', align: 'left', field: 'arriAddr',visible: false},
                {title: '调整明细', align: 'left', field: 'freightTotal',
                    formatter: function (value, row, index) {
                        let data=[];
                        if(value !== row.freightTotalAfter&&value){
                            data.push(`<span class="label label-info pa2">运费</span>`+value + '<i class="fa fa-arrow-right" style="font-size:16px;color: #1ab394"></i>'+row.freightTotalAfter)
                        }
                        if(row.onWayTotal !== row.onWayTotalAfter&&row.onWayTotal){
                            data.push(`<span class="label label-coral pa2">在途</span>`+row.onWayTotal + '<i class="fa fa-arrow-right" style="font-size:16px;color: #1ab394"></i>'+row.onWayTotalAfter)
                        }
                        if(row.transFeeCount !== row.transFeeCountAfter&&row.transFeeCount){
                            data.push(`<span class="label label-info pa2">总金额</span>`+row.transFeeCount + '<i class="fa fa-arrow-right" style="font-size:16px;color: #1ab394"></i>'+row.transFeeCountAfter)
                        }
                        if(data.length==0){
                            return '-'
                        }else{
                            return data.join("<br/>")
                        }

                    }},
                // {title: '调整前|调整后运费(元)', align: 'left', field: 'freightTotal',
                //     formatter: function (value, row, index) {
                //         if(value > row.freightTotalAfter){
                //             return value+'|<label style="color:#fc2727">'+row.freightTotalAfter+'</label>';
                //         }else if(value < row.freightTotalAfter){
                //             return value+'|<label style="color:#068e48">'+row.freightTotalAfter+'</label>';
                //         }else{
                //             return value+'|'+row.freightTotalAfter;
                //         }
                //     }},
                // {title: '调整前|调整后在途(元)', align: 'left', field: 'onWayTotal',
                //     formatter: function (value, row, index) {
                //         if(value > row.onWayTotalAfter){
                //             return value+'|<label style="color:#fc2727">'+row.onWayTotalAfter+'</label>';
                //         }else if(value < row.onWayTotalAfter){
                //             return value+'|<label style="color:#068e48">'+row.onWayTotalAfter+'</label>';
                //         }else{
                //             return value+'|'+row.onWayTotalAfter;
                //         }
                //     }},

                // {title: '调整前|调整后总金额(元)', align: 'left', field: 'transFeeCount',
                //     formatter: function (value, row, index) {
                //         if(value > row.transFeeCountAfter){
                //             return value+'|<label style="color:#fc2727">'+row.transFeeCountAfter+'</label>';
                //         }else if(value < row.transFeeCountAfter){
                //             return value+'|<label style="color:#068e48">'+row.transFeeCountAfter+'</label>';
                //         }else{
                //             return value+'|'+row.transFeeCountAfter;
                //         }
                //     }},
                // {title: '调整后金额(元)', align: 'left', field: 'adjustAmount'},
                // {title: '已收金额', align: 'left', field: 'gotAmount'},
                // {title: '未收金额', align: 'left', field: 'ungotAmount'},
                /*{title: '总成本(元)', align: 'right', field: 'costCount'},
                {title: '运费总成本(元)', align: 'right', field: 'costCountFreight'},
                {title: '在途总成本(元)', align: 'right', field: 'costCountOnWay'},
                {title: '第三方应付(元)', align: 'right', field: 'otherFeeCount'},
                {title: '管理费(元)', align: 'right', field: 'managerFee'},
                {title: '毛利(元)', align: 'right', field: 'grossProfit'},*/
                {title: '货量明细', align: 'left', field: 'numCount',
                    formatter: function (value, row, index) {
                        let data=[];
                        if(value != row.numCountAdjust&&value){
                            data.push(`<span class="label label-coral pa2">总件数</span>`+value + '<i class="fa fa-arrow-right" style="font-size:16px;color: #1ab394"></i>'+row.numCountAdjust)
                        }
                        if(row.weightCount !== row.weightCountAdjust&&row.weightCount){
                            data.push(`<span class="label label-coral pa2">总重量</span>`+row.weightCount + '<i class="fa fa-arrow-right" style="font-size:16px;color: #1ab394"></i>'+row.weightCountAdjust)
                        }
                        if(row.volumeCount !== row.volumeCountAdjust&&row.volumeCount){
                            data.push(`<span class="label label-info pa2">总体积</span>`+row.volumeCount + '<i class="fa fa-arrow-right" style="font-size:16px;color: #1ab394"></i>'+row.volumeCountAdjust)
                        }
                        if(data.length==0){
                            return '-'
                        }else{
                            return data.join("<br/>")
                        }
                    }},
                // {title: '调整前|调整后总件数', align: 'left', field: 'numCount',
                //     formatter: function (value, row, index) {
                //         if(value > row.numCountAdjust){
                //             return value+'|<label style="color:#fc2727">'+row.numCountAdjust+'</label>';
                //         }else if(value < row.numCountAdjust){
                //             return value+'|<label style="color:#068e48">'+row.numCountAdjust+'</label>';
                //         }else{
                //             return value+'|'+row.numCountAdjust;
                //         }
                //     }},
                // {title: '调整前|调整后总重量(吨)', align: 'left', field: 'weightCount',
                //     formatter: function (value, row, index) {
                //         if(value > row.weightCountAdjust){
                //             return value+'|<label style="color:#fc2727">'+row.weightCountAdjust+'</label>';
                //         }else if(value < row.weightCountAdjust){
                //             return value+'|<label style="color:#068e48">'+row.weightCountAdjust+'</label>';
                //         }else{
                //             return value+'|'+row.weightCountAdjust;
                //         }
                //     }},
                // {title: '调整前|调整后总体积(m³)', align: 'left', field: 'volumeCount',
                //     formatter: function (value, row, index) {
                //         if(value > row.volumeCountAdjust){
                //             return value+'|<label style="color:#fc2727">'+row.volumeCountAdjust+'</label>';
                //         }else if(value < row.volumeCountAdjust){
                //             return value+'|<label style="color:#068e48">'+row.volumeCountAdjust+'</label>';
                //         }else{
                //             return value+'|'+row.volumeCountAdjust;
                //         }
                //     }},
/*                {title: '调整后运费(元)', align: 'left', field: 'freightTotalAfter'},
                {title: '调整后在途(元)', align: 'left', field: 'onWayTotalAfter'},
                {title: '调整后总金额(元)', align: 'left', field: 'transFeeCountAfter'},
                {title: '调整后总件数', align: 'left', field: 'numCountAdjust'},
                {title: '调整后总重量(吨)', align: 'left', field: 'weightCountAdjust'},
                {title: '调整后总体积(m³)', align: 'left', field: 'volumeCountAdjust'},*/
                {title: '车长车型', align: 'left', field: 'carLenAndType'},
                {
                    title: '结算客户/运营公司/结算方式',
                    align: 'left',
                    field: 'balaCorpId',
                    formatter: function status(value, row, index) {
                        return getValue(row.balaName)+'/'+$.table.selectDictLabel(balaCorp, value) +'<br />' + $.table.selectDictLabel(balatype, row.balaType);;
                    }
                },
                // {title: '结算方式', align: 'left', field: 'balaType',
                //     formatter: function status(value, row, index) {
                //                     return $.table.selectDictLabel(balatype, value);
                //                 }
                // },
                // {title: '结算公司',align: 'left', field: 'balaCorpId',
                //     formatter : function status(value, row, index) {
                //                     return $.table.selectDictLabel(balaCorp, value);
                //                 }
                // },
                {title: '调整人/时间', align: 'left', field: 'regUserName',
                    formatter : function status(value, row, index) {
                        return getValue(row.regUserName) + '<br />' + getValue(row.regDate)
                    }
                },
                // {title: '调整人', align: 'left', field: 'regUserName' },
                {title: '运营组',align: 'left',field: 'salesDeptName'},

            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#reqDeliDateStart',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#reqDeliDateEnd',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;

        });
        layui.use('laydate', function() {
            var reqDeliDateStart = layui.laydate;

        });
        layui.use('laydate', function() {
            var reqDeliDateEnd = layui.laydate;

        });


    });

    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }
    //获取字符长度
    function getStringLen(str) {
        var len = 0;
        for (var i = 0; i < str.length; i++) {
            str.charCodeAt(i) > 255 ? len += 2 : len += 1;
        }
        return len;
    }

    /**
     * 修改运费或在途费用
     */
    function editReceive(data) {
        $.ajax({
            url: prefix + "/edit_receive",
            type: "post",
            dataType: "json",
            data: data,
            success: function (result) {
                if (result.code === 0) {
                    var data = result.data;
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                } else {
                    $.modal.msgError(result.msg);
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                }
            }
        });
    }

    /**
     * 修改客户发货单号
     */
    function editCustOrderno(data) {
        $.ajax({
            url: ctx + "receivableReconciliation/edit_order_no",
            type: "post",
            dataType: "json",
            data: data,
            success: function (result) {
                if (result.code === 0) {
                    var data = result.data;
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                } else {
                    $.modal.msgError(result.msg);
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                }
            }
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
         receiptAmountTotal = 0;//总金额
         receiptAmountAdjustTotal = 0;//总调整金额
    }
    /**
     * 累计总金额
     */
    function addTotal(row) {
        receiptAmountTotal = receiptAmountTotal + row.transFeeCount;//总金额
        receiptAmountAdjustTotal = receiptAmountAdjustTotal + row.transFeeCountAfter;//总调整金额
    }

    function subTotal(row) {
        receiptAmountTotal = receiptAmountTotal - row.transFeeCount;//总金额
        receiptAmountAdjustTotal = receiptAmountAdjustTotal - row.transFeeCountAfter;//总调整金额
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#receiptAmountTotal").text(receiptAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#receiptAmountAdjustTotal").text(receiptAmountAdjustTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.params = new Map();
        data.invoiceStatus = $.common.join($('#invoice_vbillstatus').selectpicker('val'));

        $.ajax({
            url: prefix + "/getAdjustCount",
            type: "post",
            dataType: "json",
            async: false,
            data: data,
            success: function(result) {
                var data = result.data;
                console.log(data);
                if (result.code == 0 && data != undefined) {
                    // 总金额
                    $("#receiptAmountTotalAll").text(data.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    // 运费类型金额
                    $("#receiptAmountAdjustTotalAll").text(data.transFeeCountAfter.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

                }
            }
        });
    }

    /**
     * 生成对账单的方法
     */
    function checking() {
        var rows =  $.table.selectColumns("invoiceId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var hasApplyLen = 0;
        //结算客户id
        var balaCustomer = bootstrapTable[0]["balaCustomer"];
        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["receiveVbillstatus"] !== 1) {
                $.modal.alertWarning("生成对账单的应收单据只能为已确认状态");
                return;
            }
            if (bootstrapTable[i]["balaCustomer"] !== balaCustomer) {
                $.modal.alertWarning("所选结算客户不相同");
                return;
            }

            //判断是否单独申请
            if (bootstrapTable[i]["hasApply"] === 1) {
                hasApplyLen = hasApplyLen + 1;
            }

        }
        //当都为单独申请时 无法生成
        if (hasApplyLen === bootstrapTable.length) {
            $.modal.alertWarning("没有符合的应收数据，无法生成对账单！");
            return;
        }

        if (rows.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        //判断get请求长度
        var mb = myBrowser();
        if ("IE" == mb) {
            if(rows.join().length>2000){
                $.modal.alertWarning("勾选数量超出限制，IE最大请求长度为2000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }
        if ("Chrome" == mb) {
            if(rows.join().length>8000){
                $.modal.alertWarning("勾选数量超出限制，Chrome最大请求长度为8000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }
        $.modal.openTab("生成对账单", prefix + "/checking?invoiceId=" + rows.join() + "&receiveDetsailIds=");
    }

    /**
     * 跳转反确认页面
     */
    function oppositeAffirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["receiveVbillstatus"] !== 1) {
            $.modal.alertWarning("应收单据只能为已确认状态下才能反确认");
            return;
        }
        if (bootstrapTable[0]["hasApply"] === 1) {
            $.modal.alertWarning("存在单独申请的应收，无法反确认！");
            return;
        }
        if (bootstrapTable[0]["isClose"] === 1 ) {
            $.modal.alertWarning("请选择未在关账日期内的应收单据");
            return;
        }

        var invoiceId =  $.table.selectColumns("invoiceId");
        if (invoiceId.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.open("反确认", prefix + "/back_confirm/" + invoiceId,500,300);
    }

    /**
     * 确认应收明细
     */
    function affirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["receiveVbillstatus"] !== 0) {
                $.modal.alertWarning("应收单据只能为新建状态下才能确认");
                return;
            }
            // if (bootstrapTable[i]["isClose"] === 1 ) {
            //     $.modal.alertWarning("请选择未在关账日期内的应收单据");
            //     return;
            // }
        }

        var invoiceId =  $.table.selectColumns("invoiceId");
        if (invoiceId.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.confirm("是否确认？", function () {
            $.operate.post(prefix + "/affirm", {"invoiceIds": invoiceId.join()});
        });
    }

    //新增
    function add() {
        var rows =  $.table.selectColumns("invoiceId");
        if(rows.length == 0 ){
            var url  = prefix + "/add?customerId="+$("#customerId").val();
            $.modal.openTab("添加应收明细", url);
        }else if(rows.length ==1){
            // 选中的行
            var bootstrapTable = $.btTable.bootstrapTable('getSelections');
            if (bootstrapTable[0]["receiveVbillstatus"] !== 0) {
                $.modal.alertWarning("请选择新建状态的数据！");
                return;
            }

            var url  = prefix + "/add?customerId="+$("#customerId").val()+"&invoiceId="+rows.join();
            $.modal.openTab("添加应收明细", url);
        }else {
            $.modal.alertWarning("最多选择一条应收记录");
        }
    }


    /**
     * 加入对账单的方法
     */
    function insertChecking() {
            var rows =  $.table.selectColumns("invoiceId");

            var hasApplyLen = 0;
            // 选中的行
            var bootstrapTable = $.btTable.bootstrapTable('getSelections');
            for (var i = 0; i < bootstrapTable.length;i++ ) {
                if (bootstrapTable[i]["receiveVbillstatus"] !== 1) {
                    $.modal.alertWarning("加入对账单的应收单据只能为已确认");
                    return;
                }
                //判断是否单独申请
                if (bootstrapTable[i]["hasApply"] === 1) {
                    hasApplyLen = hasApplyLen + 1;
                }
            }

            //当都为单独申请时 无法生成
            if (hasApplyLen === bootstrapTable.length) {
                $.modal.alertWarning("没有符合的应收数据，无法生成对账单！");
                return;
            }

            if (rows.length === 0 ) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            var customerId =  bootstrapTable[0]["customerId"];
            var getStr = "/insertChecking?customerId="+customerId+"&invoiceIds="+rows+"&receiveDetsailIds="

            //判断get请求长度
            var mb = myBrowser();
            if ("IE" == mb) {
                if(getStr>2000){
                    $.modal.alertWarning("勾选数量超出限制，IE最大请求长度为2000，当前请求长度为:"+rows.join().length);
                    return false;
                }
            }
            if ("Chrome" == mb) {
                if(getStr>8000){
                    $.modal.alertWarning("勾选数量超出限制，Chrome最大请求长度为8000，当前请求长度为:"+rows.join().length);
                    return false;
                }
            }

            $.modal.open("加入对账单", prefix + getStr);
        }

    /**
     * 分批收款
     */
    function batchRece(){
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        if (bootstrapTable[0]["vbillstatus"] !== 1 && bootstrapTable[0]["vbillstatus"] !== 3) {
            $.modal.alertWarning("请选择已确认/部分核销的应收单");
            return;
        }
        if (bootstrapTable[0]["transFeeCount"] === bootstrapTable[0]["gotAmount"]) {
            $.modal.alertWarning("该应收单已完成");
            return;
        }
        var url = prefix + "/batchRece?receiveDetailId="+bootstrapTable[0]["receiveDetailId"];
        $.modal.open('分批收款',url);
    }


    /**
     * 详情
     */
    function detail(invoiceId, receiveVbillstatus) {
        var url = ctx + "receive/receive_detail?invoiceId=" + invoiceId + "&vbillstatus=" + receiveVbillstatus;
        $.modal.openTab('应收详情',url);
    }

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.params = new Map();
        data.invoiceStatus = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 复核
     */
    function reexamine() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        if (bootstrapTable[0]["receiveVbillstatus"] !== 0) {
            $.modal.alertWarning("应收单据只能为新建状态下才能复核确认");
            return;
        }
        if (bootstrapTable[0]["isClose"] === 1 ) {
            $.modal.alertWarning("请选择未在关账日期内的应收单据");
            return;
        }

        var invoiceId =  $.table.selectColumns("invoiceId");
        if (invoiceId.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/reexamine?invoiceId="+invoiceId;
        $.modal.openTab("复核", url);
    }

    /**
     * 重置
     */
    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        var deliDetailAddr= $('#deliDetailAddr').val()
        var arriDetailAddr= $('#arriDetailAddr').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        $('#deliDetailAddr').val(arriDetailAddr)
        $('#arriDetailAddr').val(deliDetailAddr)
        searchPre();
    }

    //验证字符串是否是数字
    function checkNumber(val) {
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val)){
            return true;
        }
        else{
            return false;
        }
    }

    function myBrowser(){
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isOpera = userAgent.indexOf("Opera") > -1;
        if (isOpera) {
            return "Opera"
        }; //判断是否Opera浏览器
        if (userAgent.indexOf("Firefox") > -1) {
            return "FF";
        } //判断是否Firefox浏览器
        if (userAgent.indexOf("Chrome") > -1){
            return "Chrome";
        }
        if (userAgent.indexOf("Safari") > -1) {
            return "Safari";
        } //判断是否Safari浏览器
        if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
            return "IE";
        }; //判断是否IE浏览器
    }

    function adjustExport(){
        var data = $("#role-form").serializeArray();
        var invoiceStatusList = {};
        invoiceStatusList.name = "invoiceStatus"
        invoiceStatusList.value = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
        data.push(invoiceStatusList);
        console.log(data);
        $.modal.confirm("确定导出所有" +
            $.table._option.modalName + "吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(prefix + "/exportAdjust", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    function getFrontFormatDate() {
        var date = new Date();
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() ;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate;
        return currentdate;
    }

</script>

</body>
</html>