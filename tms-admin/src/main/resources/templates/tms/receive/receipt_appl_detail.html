<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款申请-明细')"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    #memo{
        outline: none;
        border:none
    }
</style>
<body>
<div class="form-content">
    <form id="form-receiptAppl-add" class="form-horizontal" novalidate="novalidate">
        <!--应收明细id-->
        <input name="receiveDetailId" type="hidden" th:value="${receiveDetailVO?.receiveDetailId}">
        <!--客户id-->
        <input name="customerId" type="hidden" th:value="${receiveDetailVO?.customerId}">
        <!--申请金额-->
        <input name="receivableAmount" type="hidden" th:value="${receSheetRecord?.receivableAmount}">
        <!--结算客户id-->
        <input name="balaCustomerId" type="hidden" th:value="${receiveDetailVO?.balaCustomer}">
        <!--对账申请id-->
        <input name="receSheetRecordId" type="hidden" th:value="${receSheetRecord?.receSheetRecordId}">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">申请信息</a>
                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="deptId" type="hidden" id="treeId">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">申请金额：</label>
                                    <div class="col-sm-8" th:text="${receSheetRecord.receivableAmount}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">申请日期：</label>
                                    <div class="col-sm-8" th:text="${#dates.format(receSheetRecord.receivableDate, 'yyyy-MM-dd HH:mm:ss')}">
                                        <input name="receivableDate" id="receivableDate" class="form-control" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款方式：</label>
                                    <div class="col-sm-8"
                                         th:each="dict : ${@dict.getType('receivable_method')}"
                                         th:if="${dict.dictValue} == ${receSheetRecord.receivableMethod}"
                                         th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >转入账户：</label>
                                    <div class="col-sm-8"
                                         th:each="dict : ${account}"
                                         th:if="${dict.accountId} == ${receSheetRecord.inAccount}"
                                         th:text="${dict.accountName}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">是否开票：</label>
                                    <div class="col-sm-8" th:text="${receSheetRecord.isCheck == 0? '是':'否'}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">开票余额：</label>
                                    <div class="col-sm-8" th:text="${receiveDetailVO.transFeeCount - receBillingAmountSum}">
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            </div>
        </div>


        <div class="panel panel-default" id="isShow" th:if="${receSheetRecord.isCheck == 0}">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseThree">开票信息</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseThree">
                <div class="panel-body">
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabThree" class="custom-tab table" >

                            <thead>
                            <tr>
                                <th style="width: 3%;"><a class="collapse-link add-alink" onclick="insertRowThree()" title="新增行">+</a></th>
                                <th style="width: 10%;" class="isCheck">发票抬头</th>
                                <th style="width: 8%;" class="isCheck">开票公司</th>
                                <th style="width: 8%;" class="isCheck">开票金额(元)</th>
                                <th style="width: 10%;" class="isCheck">纳税识别号</th>
                                <th style="width: 12%;" class="isCheck">开户银行</th>
                                <th style="width: 12%;">开票类型</th>
                                <th style="width: 10%;" class="isCheck">开户账号</th>
                                <th style="width: 10%;" class="isCheck">地址及电话</th>
                                <th style="width: 18%;">开票备注</th>
                            </tr>

                            </thead>
                            <tbody>

                            <tr th:each="receBilling:${receBillingList}">
                                <td></td>
                                <td th:text="${receBilling.billingPayable}" style="text-align: left"></td>
                                <td  th:each="dict : ${@dict.getType('bala_corp')}"
                                     th:if="${dict.dictValue} == ${receBilling.billingCorp}"
                                     th:text="${dict.dictLabel}" style="text-align: left">

                                </td>
                                <td th:text="${receBilling.billingAmount}" style="text-align: right">
                                </td>
                                <td th:text="${receBilling.taxIdentify}" style="text-align: left">
                                </td>
                                <td th:text="${receBilling.bank}" style="text-align: left">
                                </td>
                                <td th:each="dict : ${@dict.getType('billing_type')}"
                                    th:if="${dict.dictValue} == ${receBilling.billingType}"
                                    th:text="${dict.dictLabel}" style="text-align: left">
                                </td>
                                <td th:text="${receBilling.bankAccount}" style="text-align: left">
                                </td>
                                <td th:text="${receBilling.addressPhone}" style="text-align: left">
                                </td>
                                <td >
                                    <textarea type="text" id="memo" readonly="" th:text="${receBilling.memo}" maxlength="500" class="form-control valid">
                                    </textarea>

                                </td>


                            </tr>
                            <tr>
                                <td><a class="close-link del-alink" onclick="removeRowThree(this,0)" title="删除选择行">-</a></td>
                                <td>
                                    <select  name="receBillingList[0].billingPayable" id="billingPayable0" onchange="selectBillingPayable(0)" class="form-control valid isCheckVal" required>
                                        <option ></option>
                                        <option th:each="dict : ${custBillings}"
                                                th:text="${dict.billingPayable}"
                                                th:value="${dict.billingPayable}"
                                                th:id="${dict.custBillingId}"
                                        </option>
                                    </select>
                                </td>
                                <td>
                                    <select id="billingCorp0" name="receBillingList[0].billingCorp" class="form-control valid isCheckVal" required
                                            th:with="type=${@dict.getType('bala_corp')}">
                                        <option value=""></option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </td>
                                <td>
                                    <input type="text" id="billingAmount0" name="receBillingList[0].billingAmount"
                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"  maxlength="25" class="form-control" required>
                                </td>
                                <td>
                                    <input type="text" id="taxIdentify0" name="receBillingList[0].taxIdentify" maxlength="25" class="form-control isCheckVal" required disabled>
                                </td>
                                <td>
                                    <input type="text" id="bank0" name="receBillingList[0].bank" maxlength="125" class="form-control isCheckVal" required disabled>
                                </td>
                                <td>
                                    <select id="billingType0" name="receBillingList[0].billingType" class="form-control valid" disabled
                                            th:with="type=${@dict.getType('billing_type')}">
                                        <option value=""></option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </td>
                                <td>
                                    <input type="text" id="bankAccount0" name="receBillingList[0].bankAccount" maxlength="25" class="form-control isCheckVal" required disabled>
                                </td>
                                <td>
                                    <input type="text" id="addressPhone0" name="receBillingList[0].addressPhone" maxlength="125" class="form-control isCheckVal" required disabled>
                                </td>
                                <td>
                                    <textarea type="text" id="memo0" name="receBillingList[0].memo" maxlength="500" class="form-control valid">
                                    </textarea>
                                </td>


                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>

    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" th:if="${receSheetRecord.isCheck == 0}" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger"  onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "receCheckSheet";
    $(function () {

        /** 校验 */
        $("#form-receiptAppl-add").validate({
            focusCleanup: true
        });

        //开票默认是 禁用不开票
        $("#billingType0 option[value = '6']").attr("disabled",true);
        //开票类型默认为 增值税专用发票(9%)
        $("#billingType0 option[value = '4']").attr("selected",true);

        $('#collapseOne').collapse('show');
        $('#collapseThree').collapse('show');
    });

    var billingIndex = 0;

    /**
     * 选择发票抬头 带出客户发票信息
     */
    function selectBillingPayable(billingIndex) {
        var id = $('#billingPayable'+billingIndex).find("option:selected").attr("id");//开票信息id
        var url = prefix + "/selectCustBilling";
        var data = {custBillingId:id};
        $.ajax({
            url: url,
            data: data,
            method: 'post',
            success: function (data) {
                //带入开票信息
                $("#billingCorp"+billingIndex).val(data.billingCorp);//开票公司
                $("#billingType"+billingIndex).val(data.billingType);//发票类型
                $("#bank"+billingIndex).val(data.bank);//开户行
                $("#bankAccount"+billingIndex).val(data.bankAccount);//账号
                $("#taxIdentify"+billingIndex).val(data.taxIdentify);//纳税人识别号
                $("#addressPhone"+billingIndex).val(data.addressPhone);//地址及电话
            }
        });
    }
    /**
     * 动态新增开票信息
     */
    var billingType = [[${@dict.getType('billing_type')}]];//开票类型
    var billingTypeHTML = '';
    for (var i = 0; i < billingType.length; i++) {
        billingTypeHTML += '<option  value=' + billingType[i].dictValue + ' >' + billingType[i].dictLabel + '</option>'
    }
    var billingCorp = [[${@dict.getType('bala_corp')}]];//开票公司
    var billingCorpHTML = '';
    for (var i = 0; i < billingCorp.length; i++) {
        billingCorpHTML += '<option  value=' + billingCorp[i].dictValue + ' >' + billingCorp[i].dictLabel + '</option>'
    }

    var billingPayable = [[${custBillings}]];//发票抬头
    var billingPayableHTML = '';
    for (var i = 0; i < billingPayable.length; i++) {
        billingPayableHTML += '<option  value=' + billingPayable[i].billingPayable + ' id='+ billingPayable[i].custBillingId+'>' + billingPayable[i].billingPayable + '</option>'
    }

    function insertRowThree() {
        billingIndex += 1;
        var trTtml = ' <tr>\n' +
            '<td><a class="close-link del-alink"  onclick="removeRowThree(this,'+billingIndex+')" title="删除选择行">-</a></td>' +
            '<td>' +
            '<select name="receBillingList['+billingIndex+'].billingPayable" onchange="selectBillingPayable('+billingIndex+')" id="billingPayable'+billingIndex+'" required class="form-control valid" >'+
            '<option value=""></option>'+
            ' '+billingPayableHTML+' '+
            '</select>'+
            '</td>'+
            '<td>'+
            '<select name="receBillingList['+billingIndex+'].billingCorp" id="billingCorp'+billingIndex+'" required class="form-control valid" >'+
            '<option value=""></option>'+
            ''+billingCorpHTML+''+
            '</select>'+
            '</td>'+
            '<td><input type="text" id="billingAmount'+billingIndex+'" name="receBillingList['+billingIndex+'].billingAmount" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" required maxlength="25" class="form-control"> </td>'+
            '<td><input type="text" id="taxIdentify'+billingIndex+'" name="receBillingList['+billingIndex+'].taxIdentify" maxlength="25" required class="form-control isCheckVal" disabled></td>'+
            '<td><input type="text" id="bank'+billingIndex+'" name="receBillingList['+billingIndex+'].bank" maxlength="125" required class="form-control isCheckVal" disabled></td>'+
            '<td>'+
            '<select name="receBillingList['+billingIndex+'].billingType" id="billingType'+billingIndex+'" class="form-control valid" disabled>'+
            '<option value=""></option>'+
            ''+billingTypeHTML+''+
            '</select>'+
            '</td>'+
            '<td><input type="text" id="bankAccount'+billingIndex+'" name="receBillingList['+billingIndex+'].bankAccount" maxlength="25" required class="form-control isCheckVal" disabled></td>'+
            '<td><input type="text" id="addressPhone'+billingIndex+'" name="receBillingList['+billingIndex+'].addressPhone" maxlength="125" required class="form-control isCheckVal" disabled></td>'+
            '<td><textarea type="text" id="memo'+billingIndex+'" name="receBillingList['+billingIndex+'].memo" maxlength="500" class="form-control valid"></textarea></td>'+
            '</tr>'

        $("#infoTabThree tbody").append(trTtml);

        //默认增值税专用发票(9%)
        $("#billingType"+billingIndex).find("option[value='4']").attr("selected",true);

    }

    function removeRowThree(obj,index) {
        if ($("#infoTabThree tbody").find('tr').length > 1) {
            $("#infoTabThree tbody").find(obj).closest("tr").remove();
        } else {
            $("#billingCorp"+index).val("");
            $("#billingPayable"+index).val("");
            $("#taxIdentify"+index).val("");
            $("#bank"+index).val("");
            $("#billingType"+index).val("");
            $("#bankAccount"+index).val("");
            $("#addressPhone"+index).val("");
            $("#addressee"+index).val("");
            $("#addresseeContact"+index).val("");
            $("#addresseeAddr"+index).val("");
            $("#memo"+index).val("");
            $("#billingAmount"+index).val("");
        }
    }

    //提交
    function submitHandler() {
        //取消禁用
        $(":disabled").removeAttr("disabled");
        $('.isCheckVal').attr("readonly",true);
        $('.receivableAmount').attr("readonly",true);//申请金额readonly

        for(var i = 0;i<=billingIndex;i++){
            //开票金额
            var billingAmount = $("#billingAmount"+i).val();
            //循环判断开票金额不能为0
            if(billingAmount == 0){
                $.modal.alertError("开票金额不能为0");
                return ;
            }

            //普通发票
            var billingType = $("#billingType"+i).val();
            //普通发票，开户行和开户账号非必填
            if(billingType === '2'){
                $("#bank"+i).attr("required",false);
                $("#bankAccount"+i).attr("required",false);
            }
        }

        if ($.validate.form()) {
            $.operate.saveTab(ctx + "receive/billingAppl", $('#form-receiptAppl-add').serialize());
        }
    }

</script>
</body>
</html>