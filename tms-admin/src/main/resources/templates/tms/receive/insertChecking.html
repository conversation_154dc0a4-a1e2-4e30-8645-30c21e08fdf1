<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('demo')"/>
</head>

<body>
<div class="form-content">
    <form id="form-user-add" class="form-horizontal" novalidate="novalidate">

        <div  class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFive">应收明细</a>
                </h4>
            </div>
            <div id="collapseFive" class="panel-collapse collapse in">
                <div class="panel-body">

                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabThree" class="custom-tab table" >
                            <thead>
                            <tr>
                                <th style="width: 20%;text-align:center">应收单据号</th>
                                <th style="width: 20%;text-align:center">发货单号</th>
                                <th style="width: 15%;text-align:center">客户名称</th>
                                <th style="width: 15%;text-align:center">结算客户</th>
                                <th style="width: 10%;text-align:center">总金额</th>
                                <th style="width: 10%;text-align:center">已收金额</th>
                                <th style="width: 10%;text-align:center">未收金额</th>

                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="mapS,status:${receiveDetailList}">
                                <td th:text="${mapS.vbillno}"></td>
                                <td th:text="${mapS.invoiceVbillno}"></td>
                                <td th:text="${mapS.custName}"></td>
                                <td th:text="${mapS.balaName}"></td>
                                <td th:text="${mapS.transFeeCount}"></td>
                                <td th:text="${mapS.gotAmount}"></td>
                                <td th:text="${mapS.ungotAmount}"></td>

                            </tr>

                            </tbody>
                        </table>
                    </div>

                </div>
            </div>

        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>

<script>
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');

    });

    

</script>
</body>

</html>