<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应收列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 70px);
        padding-top: 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--客户id-->
                <input type="hidden" name="customerId" th:value="${customerId}">
                <!--要求提货年月-->
                <!--<input type="hidden" name="reqStartDate" th:value="${reqStartDate}">-->
                <!--<input type="hidden" name="reqEndDate" th:value="${reqEndDate}">-->
                <div class="row">
                    <div class="col-md-7 col-sm-12" style="padding: 0">
                        <div class="col-md-2 col-sm-2" style="padding: 0">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">应收单状态：</label>-->
                                <div class="col-sm-12">
                                    <select name="status" id="status" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" data-none-selected-text="应收单状态" multiple>
                                        <option th:each="dict : ${receiveDetailStatusEnum}" th:text="${dict.context}"
                                                th:value="${dict.value}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2" style="padding-right: 0">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">发货单号：</label>-->
                                <div class="col-sm-12">
                                    <input name="invoiceVbillno" id="invoiceVbillno" class="form-control" type="text"
                                           placeholder="请输入发货单号"
                                           maxlength="25" required="" aria-required="true" th:value="${invoiceVbillno}">
                                </div>
                            </div>
                        </div>
                        <div style="padding-right: 0" class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">应收单号：</label>-->
                                <div class="col-sm-12">
                                    <input name="vbillno" id="vbillno" placeholder="请输入应收单号" class="form-control valid"
                                           type="text"  maxlength="25">
                                </div>
                            </div>
                        </div>
                        <div style="padding-right: 0" class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">运营组：</label>-->
                                <div class="col-sm-12">
                                    <select name="salesDept" id="salesDept" class="form-control valid selectpicker"
                                            aria-invalid="false" data-none-selected-text="运营组" required>
                                        <option value=""></option>
                                        <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                                th:text="${mapS.deptName}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div style="padding-right: 0" class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户名称：</label>-->
                                <div class="col-sm-12">
                                    <input name="custName" id="custName" class="form-control" type="text"
                                           placeholder="请输入客户名称"
                                           maxlength="25" required="" aria-required="true">
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">发货单状态：</label>-->
                                <div class="col-sm-12">
                                    <select  id="invoice_vbillstatus" class="form-control selectpicker"
                                             aria-invalid="false" data-none-selected-text="发货单状态" multiple th:with="type=${invoiceStatusList}">
                                        <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="" style="display: none">

                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">结算客户：</label>-->
                                <div class="col-sm-12">
                                    <input name="balaName" id="balaName" placeholder="请输入结算客户"
                                           class="form-control valid" type="text" maxlength="25">
                                    <!--<input name="customerId" id="customerId" type="hidden" th:value="${customerId}">-->
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">创建人：</label>-->
                                <div class="col-sm-12">
                                    <input name="regUserName" id="regUserName" class="form-control"
                                           placeholder="请输入创建人" maxlength="25">
                                </div>
                            </div>
                        </div>

                        <div class="col-md-5 col-sm-5">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">创建时间：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" placeholder="创建开始时间" style="width: 45%; float: left;" class="form-control"
                                           id="startDate"  name="startDate">
                                    <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                    <input type="text" placeholder="创建截止时间" style="width: 45%; float: left;" class="form-control"
                                           id="endtDate"  name="endtDate">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-5 col-sm-9" style="padding: 0">
                        <div class="col-md-4 col-sm-4" style="display: none">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">结算方式：</label>-->
                                <div class="col-sm-12">
                                    <select name="balatype" id="balatype" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" data-none-selected-text="结算方式" multiple
                                            th:with="type=${@dict.getType('bala_type')}">
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div style="padding: 0" class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">是否申请开票：</label>-->
                                <div class="col-sm-12">
                                    <select name="isCheck" class="form-control" th:with="type=${@dict.getType('if_billing')}">
                                        <option value="">--是否申请开票--</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" ></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <!-- <div style="padding-right: 0" class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="isCheckSuccess" class="form-control" th:with="type=${@dict.getType('is_check_success')}">
                                        <option value="">--是否开票--</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" ></option>
                                    </select>
                                </div>
                            </div>
                        </div> -->
                        <div style="padding-right: 0" class="col-md-6 col-sm-6">
                            <div class="form-group">
                                <!--                                <label class="col-sm-3" style="padding: 0">要求提货日：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" style="width: 45%; float: left;" class="form-control"
                                           id="reqDeliDateStart"  name="params[reqDeliDateStart]" placeholder="要求提货开始日">
                                    <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                    <input type="text" style="width: 45%; float: left;" class="form-control"
                                           id="reqDeliDateEnd"  name="params[reqDeliDateEnd]" placeholder="要求提货结束日">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row no-gutter">
                    <div class="col-sm-5">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">提货方地址：</label>-->
                            <div class="col-sm-12">
                                <div class="col-sm-4">
                                    <select  name="params[deliProvinceId]" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="params[deliCityId]" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="params[deliAreaId]" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>

                            </div>
                            <!--                            <div class="col-sm-4">-->
                            <!--                                <input name="params[deliDetailAddr]" id="deliDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"-->
                            <!--                                       maxlength="50" autocomplete="off">-->
                            <!--                            </div>-->
                        </div>
                    </div>

                    <div class="col-sm-5">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">收货方地址：</label>-->
                            <div class="col-sm-2" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-10">
                                <div class="col-sm-4">
                                    <select  name="params[arriProvinceId]" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="params[arriCityId]" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="params[arriAreaId]" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>

                            </div>
                            <!--                            <div class="col-sm-3">-->
                            <!--                                <input name="params[arriDetailAddr]" id="arriDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"-->
                            <!--                                       maxlength="50" autocomplete="off">-->
                            <!--                            </div>-->
                        </div>
                    </div>

                    <div class="col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-info" onclick="$.table.exportExcel()" shiro:hasPermission="finance:receive:businessExport">
                <i class="fa fa-upload"></i> 导出
            </a>
            <a class="btn btn-primary single disabled" onclick="receRecord()" shiro:hasPermission="finance:receive:receRecord">
                <i class="fa fa-calculator"></i> 收款记录
            </a>
            <a class="btn btn-primary single disabled" onclick="splitReceiveDetail()" shiro:hasPermission="finance:receive:split">
                <i class="fa fa-calculator"></i> 运费拆分
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var vbillstatus = [[${invoiceStatusList}]]; //发货单状态
    //发货单状态map
    var invoiceStatusMap = [[${invoiceStatusMap}]];
    //是否开票
    var if_billing = [[${@dict.getType('if_billing')}]];
    //是否开票成功
    var is_check_success = [[${@dict.getType('is_check_success')}]];

    var balaCorp = [[${@dict.getType('bala_corp')}]];
    var balatype = [[${@dict.getType('bala_type')}]];
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var prefix = ctx + "receive";

    //财务管理-应收对账 传入的要求提货日
    var reqDeliDateStartParam = [[${reqDeliDateStart}]];
    var reqDeliDateEndParam = [[${reqDeliDateEnd}]];

    var myDate=new Date();
    var reqDeliDateEnd = myDate.getFullYear() + "-" + (("0" + (myDate.getMonth() + 1)).slice(-2)) + "-" + ("0" + myDate.getDate()).slice(-2);
    if($.common.isNotEmpty(reqDeliDateEndParam)){
        $("#reqDeliDateEnd").val(reqDeliDateEndParam);
    }else{
        //$("#reqDeliDateEnd").val(reqDeliDateEnd);
    }

    myDate.setMonth(myDate.getMonth()-1);
    var reqDeliDateStart = myDate.getFullYear() + "-" + (("0" + (myDate.getMonth() + 1)).slice(-2)) + "-" + ("0" + myDate.getDate()).slice(-2);
    if($.common.isNotEmpty(reqDeliDateStartParam)){
        $("#reqDeliDateStart").val(reqDeliDateStartParam);
    }else{
        $("#reqDeliDateStart").val(reqDeliDateStart);
    }


    // 初始化省市区
    $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
    $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

    var transFeeCount = 0;
    var gotAmountCount = 0;
    var ungotAmountCount = 0;
    var adjustAmountCount = 0;
    var managerFeeCount = 0;

    $(function () {
        var options = {
            url: prefix + "/business",
            exportUrl: prefix + "/business/export",
            showExport: false,
            exportTypes:['excel','csv'],
            exportOptions:{
                fileName:"应收明细"
            },
            showToggle: false,
            showColumns: true,
            modalName: "应收明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 3,
            height: 565,
            uniqueId: "receiveDetailId",
            clickToSelect: true,
            showFooter:true,
            onPostBody:function () {
                //合并页脚
                merge_footer();
                getAmountCount();
            },
            onRefresh:function(params){
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                adjustAmountCount = 0;
                managerFeeCount = 0;
            },
            onCheck: function (row,$element) {
                var transFee = row.transFeeCount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                var adjustAmount = row.adjustAmount === null ? 0 : row.adjustAmount;
                var managerFee = row.managerFee === null ? 0 : row.managerFee;
                //总数加上本行数值
                transFeeCount = transFeeCount + transFee;
                gotAmountCount = gotAmountCount + gotAmount;
                ungotAmountCount = ungotAmountCount + ungotAmount;
                adjustAmountCount = adjustAmountCount + adjustAmount;
                managerFeeCount = managerFeeCount + managerFee;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#adjustAmountCountTotal").text(adjustAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#managerFeeCountTotal").text(managerFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheck: function (row, $element) {
                var transFee = row.transFeeCount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                var adjustAmount = row.adjustAmount === null ? 0 : row.adjustAmount;
                var managerFee = row.managerFee === null ? 0 : row.managerFee;
                //总数减去本行数值
                transFeeCount = transFeeCount - transFee;
                gotAmountCount = gotAmountCount - gotAmount;
                ungotAmountCount = ungotAmountCount - ungotAmount;
                adjustAmountCount = adjustAmountCount - adjustAmount;
                managerFeeCount = managerFeeCount - managerFee;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#adjustAmountCountTotal").text(adjustAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#managerFeeCountTotal").text(managerFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onCheckAll: function (rowsAfter) {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                adjustAmountCount = 0;
                managerFeeCount = 0;
                //循环累加
                for (var row of rowsAfter) {
                    transFeeCount = transFeeCount + row.transFeeCount;
                    gotAmountCount = gotAmountCount + row.gotAmount;
                    ungotAmountCount = ungotAmountCount + row.ungotAmount;
                    adjustAmountCount = adjustAmountCount + row.adjustAmount;
                    managerFeeCount = managerFeeCount + row.managerFee;
                }
                //赋值
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#adjustAmountCountTotal").text(adjustAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#managerFeeCountTotal").text(managerFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheckAll: function () {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                adjustAmountCount = 0;
                managerFeeCount = 0;
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#adjustAmountCountTotal").text(adjustAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#managerFeeCountTotal").text(managerFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "总金额：<nobr id='transFeeCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "调整金额：<nobr id='adjustAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "已收金额：<nobr id='gotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "未收金额：<nobr id='ungotAmountCountTotal'>￥0</nobr><br>" +
                        /*      "管理费：<nobr id='managerFeeCountTotal'>￥0</nobr><br>" +*/
                        "总合计:总金额：<nobr id='transFeeCountTotalAll'>￥0</nobr>&nbsp&nbsp" +
                        "调整后金额：<nobr id='adjustAmountCountTotalAll'>￥0</nobr>&nbsp&nbsp" +
                        "已收金额：<nobr id='gotAmountCountTotalAll'>￥0</nobr>&nbsp&nbsp" +
                        "未收金额：<nobr id='ungotAmountCountTotalAll'>￥0</nobr>&nbsp&nbsp" ;
                    /*       "管理费：<nobr id='managerFeeCountTotalAll'>￥0</nobr>&nbsp&nbsp"*/
                }
            },
                {title: '应收单号/应收单据状态',field: 'vbillstatus',align: 'left',
                    formatter: function status(value, row, index) {
                        switch (value) {
                            case 0:
                                return row.vbillno+'<br />'+'<span class="label label-primary">新建</span>'
                            case 1:
                                return row.vbillno+'<br />'+'<span class="label label-warning">已确认</span>';
                            case 2:
                                return row.vbillno+'<br />'+'<span class="label label-coral">已对账</span>';
                            case 3:
                                return row.vbillno+'<br />'+'<span class="label label-info">部分核销 </label>';
                            case 4:
                                return row.vbillno+'<br />'+'<span class="label label-success">已核销</span>';
                            case 5:
                                return row.vbillno+'<br />'+'<span class="label label-inverse">关闭 </label>';
                            default:
                                break;
                        }
                    }
                },
                {title: '发货单号/发货单状态',field: 'invoiceStatus',align: 'left',switchable: false,
                    formatter: function status(row,value) {
                        var context = '';

                        vbillstatus.forEach(function (v) {
                            if (v.value == value.invoiceStatus) {

                                if (value.invoiceStatus == invoiceStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-primary">'+v.context+'</span>';
                                }else if (value.invoiceStatus == invoiceStatusMap.AFFIRM) {
                                    //已确认
                                    context = '<span class="label label-warning">'+v.context+'</span>';
                                }else if (value.invoiceStatus == invoiceStatusMap.PORTION_PICK_UP
                                    || value.invoiceStatus == invoiceStatusMap.PICK_UP) {
                                    //部分提货 与 已提货
                                    context = '<span class="label label-info">'+v.context+'</span>';
                                }else if (value.invoiceStatus == invoiceStatusMap.PORTION_ARRIVALS
                                    || value.invoiceStatus == invoiceStatusMap.ARRIVALS) {
                                    //部分到货  已到货
                                    context = '<span class="label label-success">'+v.context+'</span>';
                                }else if (value.invoiceStatus == invoiceStatusMap.PORTION_RETURNS
                                    || value.invoiceStatus == invoiceStatusMap.RETURNS) {
                                    //部分回单  已回单
                                    context = '<span class="label label-primary">' + v.context + '</span>';
                                } else {
                                    //关闭
                                    context = '<span class="label label-inverse">' + v.context + '</span>';
                                }

                                return false;
                            }
                        });
                        return value.invoiceVbillno+'<br />'+context;
                    }
                },
                {
                    title: '客户名称/订单号',
                    align: 'left',
                    field: 'custName',
                    formatter: function status(value, row, index) {
                        return getValue(value)+'<br />'+getValue(row.custOrderno)
                    }
                },
                // {title: '客户订单号',align: 'left',field: 'custOrderno'},
                // {title: '提货|到货省市区',field: 'deliProName',align: 'left',
                //     formatter: function status(value, row, index) {
                //         return row.deliAddr+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriAddr;
                //     }
                // },
                {
                    title: '要求提|到货日',
                    align: 'left',
                    field: 'reqDeliDate',
                    formatter: function status(value, row, index) {
                        return getValue(row.reqDeliDate) + '<br />'+getValue(row.reqArriDate);
                    }
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(row.deliAddr == null || row.deliAddr == ""){
                            row.deliAddr = '';
                        }
                        if(row.arriAddr == null || row.arriAddr.arriAddr == ""){
                            row.arriAddr = ''
                        }
                        if(row.deliAddr == "" && row.arriAddr == ""){
                            return "";
                        }else{
                            return `<span class="label label-warning pa2">提</span>`+row.deliAddr+`<br/><span class="label label-success pa2">到</span>`+row.arriAddr;
                        }

                    }
                },
                {
                    title: '货量',
                    align: 'left',
                    field: 'numCount',
                    formatter: function status(value, row, index) {
                        let data=[];
                        if(value){
                            data.push(getValue(value) + '件');
                        }
                        if(row.feeWeightCount){
                            data.push(getValue(row.feeWeightCount) + '吨');
                        }
                        if(row.volumeCount){
                            data.push(getValue(row.volumeCount) + 'm3');
                        }
                        return data.join('/')
                    }
                },
                {
                    title: '金额明细',
                    align: 'left',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        // if (value === null) {
                        //     return ;
                        // }
                        // return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        var a = ''
                        var val = row.freeType - 0
                        if(val==0){
                            a = '运费'
                        }else if(val==1){
                            a = '在途费用'
                        }else if(val==2){
                            a = '调整费用'
                        }

                        let data=[];
                        if(row.memo){
                            if(row.actualTotalReceive){
                                data.push(`<span class="label label-primary pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="总金额">`+a+`</span> `+row.actualTotalReceive.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+ '<i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px" data-placement="top" data-container="body" title="'+row.memo+'"></i>')
                            }
                        }else {
                            if(row.actualTotalReceive){
                                data.push(`<span class="label label-primary pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="总金额">`+a+`</span> `+row.actualTotalReceive.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                            }
                        }
                        // if(row.actualTotalReceive){
                        //     data.push(`<span class="label badge-info pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="实际总应收">实际</span> `+row.actualTotalReceive.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        // }
                        // if(row.adjustAmount){
                        //     data.push(`<span class="label label-coral pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="调整后金额">调整后</span> `+row.adjustAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        // }
                        // if(row.gotAmount){
                        //     data.push(`<span class="label badge-info pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="已付金额">已付</span> `+row.gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        // }
                        if(row.ungotAmount){
                            data.push(`<span class="label label-coral pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="未付金额">未付</span> `+row.ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        return data.join("<br/>")
                    }
                },

                // {title: '总金额(元)',align: 'right',field: 'transFeeCount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                // {title: '实际总应收(元)',align: 'right',field: 'actualTotalReceive',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                // {title: '调整后金额(元)',align: 'right',field: 'adjustAmount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                //
                // },
                //
                // {title: '已收金额(元)',align: 'right',field: 'gotAmount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                // {title: '未收金额(元)',align: 'right',field: 'ungotAmount',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                /* {title: '管理费(元)',align: 'right',field: 'managerFee',
                     formatter: function (value, row, index) {
                         if (value === null) {
                             return ;
                         }
                         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                     }

                 },*/
               
                // {field: 'isCheckSuccess',title: '是否开票',align: 'left',
                //     formatter: function status(value, row, index) {
                //         return $.table.selectDictLabel(is_check_success, value);
                //     }
                // },
                {title: '运营组',align: 'left',field: 'salesDeptName'},
                // {title: '费用类型',field: 'freeType',align: 'left',
                //     formatter: function status(value, row, index) {
                //         switch(value - 0) {
                //             case 0:
                //                 return '<span>运费</label>';
                //             case 1:
                //                 return '<span>在途费用</label>';
                //             case 2:
                //                 return '<span>调整费用</label>';
                //             default:
                //                 break;
                //         }
                //     }
                //
                // },
                {title: '收款类型',field: 'costTypeOnWay',align: 'left',
                    formatter: function status(value, row, index) {

                        if (row.freeType === '0'){
                            return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                        }else if (row.freeType === '2')  {
                            return '调整费'
                        }
                        return $.table.selectDictLabel(costTypeOnWay, value);
                    }

                },
                // {
                //     title: '收款类型',
                //     field: 'freeType',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         if (row.freeType === '0'){
                //             return  $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                //         }else if (row.freeType === '2')  {
                //             return '调整费'
                //         }else{
                //             return $.table.selectDictLabel(costTypeOnWay, row.costTypeOnWay);
                //         }
                //     }
                //
                // },
                
                // {title: '计费重量(吨)',align: 'left',field: 'feeWeightCount'},
                // {title: '计费体积(m3)',align: 'left',field: 'volumeCount'},
                // {title: '要求提货日',field: 'reqDeliDate',align: 'left'},
                // {title: '要求到货日',field: 'reqArriDate',align: 'left'},
                
                /*{title: '管理费(元)',align: 'right',field: 'managerFee',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },*/
                // {title: '结算客户',align: 'left',field: 'balaName'},
                {
                    title: '结算客户/运营公司/结算方式',
                    field: 'balaCorp',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return getValue(row.balaName)+'/'+$.table.selectDictLabel(balaCorp, value) +'<br />' + $.table.selectDictLabel(balatype, row.balatype);;
                    }
                },
                // {
                //     title: '账期',
                //     align: 'left',
                //     field: 'paymentDays'
                // },
                
                {
                    title: '是否为调整单/原因',
                    align: 'left',
                    field: 'isAdjust',
                    formatter: function (value, row, index) {
                        if (value === 0) {
                            return '否' + '<br />'+getValue(row.adjustMemo);
                        }
                        return '是'+ '<br />'+getValue(row.adjustMemo);
                    }
                },
                // {
                //     title: '调整原因',
                //     align: 'left',
                //     field: 'adjustMemo',
                // },
                {title: '对账单号',align: 'left',field: 'sheetVbillno'},
                {field: 'isCheck',title: '是否申请开票',align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(if_billing, value);
                    }

                },
                // {title: '创建时间',align: 'left',field: 'regDate'},

                // {title: '备注',align: 'left',field: 'memo',
                //     formatter: function(value, row, index) {
                //         return $.table.tooltip(value);
                //     }
                // },
                // {title: '结算方式',align: 'left',field: 'balatype',
                //     formatter: function status(value, row, index) {
                //         return $.table.selectDictLabel(balatype, value);
                //     }
                // },

                {
                    title: '创建信息',
                    align: 'left',
                    field: 'regUserName',
                    formatter: function status(value, row, index) {
                        return getValue(value) + '<br />'+getValue(row.regDate);
                    }
                },
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#reqDeliDateStart',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#reqDeliDateEnd',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;

        });
        layui.use('laydate', function() {
            var reqDeliDateStart = layui.laydate;

        });
        layui.use('laydate', function() {
            var reqDeliDateEnd = layui.laydate;

        });
    });

    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }

    function receRecord(){
        var id = $.table.selectColumns('receiveDetailId');
        var url =  ctx + "receSheetRecord" + "/receRecord?receiveDetailId="+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "收款记录",
            area: ['80%', '80%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    function splitReceiveDetail() {
        var receiveDetailId = $.table.selectColumns('receiveDetailId').join();
        var transFeeCount = $.table.selectColumns('transFeeCount').join();

        var freeTypeList = $.table.selectColumns('freeType');
        for (var i = 0; i < freeTypeList.length; i++) {
            if (freeTypeList[i] != 0) {
                $.modal.alertWarning("请选择运费类型的应收明细！");
                return;
            }
        }

        $.modal.open("运费拆分",prefix + "/split_receive_detail?receiveDetailId=" + receiveDetailId+"&transFeeCount="+transFeeCount, '500', '300');
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: ctx + "receive/business/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;

                if (result.code == 0 && data != undefined) {
                    //总金额
                    $("#transFeeCountTotalAll").text(data.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //调整金额
                    $("#adjustAmountCountTotalAll").text(data.adjustAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //已收金额
                    $("#gotAmountCountTotalAll").text(data.gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //未收金额
                    $("#ungotAmountCountTotalAll").text(data.ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    //管理费
                    //$("#managerFeeCountTotalAll").text(data.managerFee.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.status = $.common.join($('#status').selectpicker('val'));
        data.balatype = $.common.join($('#balatype').selectpicker('val'));
        data.params = new Map();
        data.params.invoice_vbillstatus = $.common.join($('#invoice_vbillstatus').selectpicker('val'));
        $.table.search('role-form', data);
    }


    /**
     * 重置
     */
    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        var deliDetailAddr= $('#deliDetailAddr').val()
        var arriDetailAddr= $('#arriDetailAddr').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        $('#deliDetailAddr').val(arriDetailAddr)
        $('#arriDetailAddr').val(deliDetailAddr)
        searchPre();
    }
</script>
</body>
</html>