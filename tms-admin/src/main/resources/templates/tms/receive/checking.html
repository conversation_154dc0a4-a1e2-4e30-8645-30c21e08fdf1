<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('生成应收对账')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
        color: #808080;
    }
    .flex_left{
        width: 100px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .tc{
        text-align: center;
        width: 100%;
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .theBtn {
        line-height: 26px;
        border: none;
        min-width: 60px;
        padding: 0 12px;
    }
    .delFlag {
        text-decoration: line-through;
        background-color: #f0e9e3;
    }
</style>
<body>
<div class="form-content">
    <form id="form-receCheck-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body" style="padding: 5px 10px 10px;">
                        <!--基础信息 begin-->
                        <input type="hidden" class="form-control" name="receiveDetailId" id="receiveDetailId"
                               readonly th:value="${receiveDetailIds}">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">客户名称：</label>
                                    <div class="flex_right">
                                        <input type="text" class="form-control" name="custName" id="custName"
                                               disabled th:value="${receiveDetail.custName}">
                                        <input name="customerId" type="hidden" id="customerId"
                                               th:value="${receiveDetail.customerId}">
                                        <input name="custCode" type="hidden" id="custCode"
                                               th:value="${receiveDetail.custCode}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">结算客户：</label>
                                    <div class="flex_right">
                                        <input type="text" class="form-control" id="balaName" name="balaName"
                                               th:value="${receiveDetail.balaName}" disabled>
                                        <input name="balaCustomer" type="hidden" id="balaCustomer"
                                               th:value="${receiveDetail.balaCustomer}">
                                        <input name="balaCode" type="hidden" id="balaCode"
                                               th:value="${receiveDetail.balaCode}">
                                    </div>
                                </div>
                            </div>
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="form-group">-->
<!--                                    <label class="col-sm-5">总金额：</label>-->
<!--                                    <div class="col-sm-7">-->
<!--                                        <input type="text" class="form-control"  id="totalAmount" name="totalAmount"-->
<!--                                               th:value="${transFeeCount}"  disabled>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">结算公司：</label>
                                    <div class="flex_right">
                                        <input  class="form-control"  id="balaCorpName" name="balaCorpName"
                                                  disabled>
                                        <input  class="form-control"  id="balaCorp" name="balaCorp"
                                               th:value="${receiveDetail.balaCorp}"  type="hidden">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left" ><span class="fcff">* </span>对账年份：</label>
                                    <div class="flex_right">
                                        <input type="text" class="form-control" name="year" id="year" required
                                               autocomplete="off" readonly>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="form-group">-->
<!--                                    <label class="col-sm-5">未收金额：</label>-->
<!--                                    <div class="col-sm-7">-->
<!--                                        <input  class="form-control" id="ungotAmount" name="ungotAmount"-->
<!--                                                disabled th:value="${ungotAmount}">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <!--<div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">开票公司：</label>
                                    <div class="col-sm-7">
                                        <input  class="form-control" id="checkCorpName" name="checkCorpName" disabled >
                                        <input  class="form-control" id="checkCorp" name="checkCorp" readonly type="hidden">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">发票抬头：</label>
                                    <div class="col-sm-7">
                                        <input type="text"  class="form-control" id="checkHead" name="checkHead" disabled>

                                        <input  type="hidden"  class="form-control" id="checkType" name="checkType"
                                               readonly>
                                    </div>
                                </div>
                            </div>-->
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">* </span>对账月份：</label>
                                    <div class="flex_right">
                                        <input type="text" class="form-control" name="month" id="month" required
                                               autocomplete="off" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">特殊标记：</label>
                                    <div class="flex_right">
                                        <input type="text"  class="form-control" id="receCheckSheetName" name="receCheckSheetName" maxlength="50">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">

                            <!--<div class="col-md-3 col-sm-6">-->
                                <!--<div class="form-group">-->
                                    <!--<label class="col-sm-5">是否手动核销：</label>-->
                                    <!--<div class="col-sm-7">-->
                                        <!--<select name="handVerification" id="handVerification"  class="form-control valid"-->
                                                <!--th:with="type=${@dict.getType('hand_verification')}">-->
                                            <!--<option th:each="dict : ${type}" th:text="${dict.dictLabel}"-->
                                                    <!--th:value="${dict.dictValue}"></option>-->
                                        <!--</select>-->
                                    <!--</div>-->
                                <!--</div>-->
                            <!--</div>-->
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="flex">
                                    <label class="flex_left">备注：</label>
                                    <div class="flex_right">
                                            <textarea name="memo" id="memo" maxlength="250" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--基础信息 end-->
                    </div>
                </div>
            </div>

            <div  class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFive">应收明细</a>
                        <span style="font-style: italic;color: red;">※未准备申请应收的请勿加入对账包</span>
                    </h4>
                </div>
                <div id="collapseFive" class="panel-collapse collapse in">
                    <div class="panel-body">

                        <div style="display: flex;justify-content: space-between;margin-bottom: 5px">
                            <div></div>
                            <div style="display: flex;">
                                <div style="width: 180px;">
                                    <select id="feeType" class="form-control selectpicker" data-none-selected-text="费用类型">
                                        <option value=""></option>
                                        <option value="0">运费</option>
                                        <option value="1">在途</option>
                                    </select>
                                </div>
                                <div style="margin-left: 5px">
                                    <button type="button" class="theBtn" onclick="findLocal()" style="background-color: #1ab394;color:#fff;"><i class="fa fa-filter"></i>&nbsp;过滤显示</button>
                                    <button type="button" class="theBtn" onclick="showAll()" style="background-color: #f8ac59;color:#fff"><i class="fa fa-refresh"></i>&nbsp;取消过滤</button>
                                </div>
                            </div>
                        </div>
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabThree" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 30px;"></th>
                                    <th style="width: 140px;text-align:center">应收单据号</th>
                                    <th style="width: 70px;text-align:center">费用类型</th>
                                    <th style="width: 140px;text-align:center">发货单号</th>
<!--                                    <th style="width: 15%;text-align:center">客户名称</th>-->
<!--                                    <th style="width: 15%;text-align:center">结算客户</th>-->
                                    <th style="width: 120px;text-align:center">要求提货日期</th>
                                    <th style="text-align:center">要求提货|到货地址</th>
                                    <th style="text-align:center">货品</th>
                                    <th style="width: 160px;text-align:center">总件数|总重量|总体积</th>
                                    <th style="width: 100px;text-align:center">总金额</th>
                                    <th style="width: 100px;text-align:center">已收金额</th>
                                    <th style="width: 100px;text-align:center">未收金额</th>

                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="mapS,status:${receiveDetailList}" th:detail-id="${mapS.receiveDetailId}" th:fylx="${mapS.freeType}">
                                    <td>
                                        <a class="close-link del-alink" style="background: #fd8481;border-radius: 50%" th:onclick="removeRow(this,'[(${mapS.receiveDetailId})]',[[${mapS.transFeeCount}]],[[${mapS.gotAmount}]])" title="删除选择行">x</a>
                                    </td>
                                    <td>
                                        <div class="input-group tc" th:text="${mapS.vbillno}"></div>
                                    </td>
                                    <td th:text="${mapS.freeType == '0' ? '运费' : '在途'}"></td>
                                    <td>
                                        <div class="input-group tc" th:text="${mapS.invoiceVbillno}"></div>
                                    </td>
                                    <td th:text="${#dates.format(mapS.reqDeliDate,'yyyy-MM-dd HH:mm')}"></td>
                                    <td>[[${mapS.deliAddr}]] | [[${mapS.arriAddr}]]</td>
                                    <td th:text="${mapS.goodsName}"></td>
                                    <td>[[${mapS.numCount}]] | [[${mapS.feeWeightCount}]] | [[${mapS.volumeCount}]]</td>
<!--                                    <td>-->
<!--                                        <div class="input-group" th:text="${mapS.custName}"></div>-->
<!--                                    </td>-->
<!--                                    <td>-->
<!--                                        <div class="input-group" th:text="${mapS.balaName}"></div>-->
<!--                                    </td>-->
                                    <td th:align="right" th:text="'¥'+${#numbers.formatDecimal(mapS.transFeeCount,1,'COMMA',2,'POINT')}"></td>
                                    <td th:align="right" th:text="'¥'+${#numbers.formatDecimal(mapS.gotAmount,1,'COMMA',2,'POINT')}"></td>
                                    <td th:align="right" th:text="'¥'+${#numbers.formatDecimal(mapS.ungotAmount,1,'COMMA',2,'POINT')}"></td>

                                </tr>

                                </tbody>
                                <tfoot>
                                <tr style="background: #FFFCD3;text-align: center">
                                    <td colspan="8" style="text-align: left">合计
                                        <span style="color: #9149ff" id="tt-msg"></span>
                                    </td>
                                    <td class="fw" id="td_totalAmount" th:text="'¥'+${#numbers.formatDecimal(transFeeCount,1,'COMMA',2,'POINT')}"></td>
                                    <td class="fw" id="td_gotAmount" th:text="'¥'+${#numbers.formatDecimal(gotAmount,1,'COMMA',2,'POINT')}"></td>
                                    <td class="fw" id="td_ungotAmount" th:text="'¥'+${#numbers.formatDecimal(ungotAmount,1,'COMMA',2,'POINT')}"></td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>

                    </div>
                </div>

            </div>


        </div>
        <input type="hidden" class="form-control" id="totalAmount" name="totalAmount" th:value="${transFeeCount}">
        <input type="hidden" class="form-control" id="ungotAmount" name="ungotAmount" th:value="${ungotAmount}">
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="commit()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">
    var totalAmount = new BigNumber([[${transFeeCount}]]);
    var gotAmount = new BigNumber([[${gotAmount}]]);
    var ungotAmount = new BigNumber([[${ungotAmount}]]);
    var yearMonth = [[${yearMonth}]];
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');

        // 结算公司
        var datas = [[${@dict.getType('bala_corp')}]];
        for ( var i = 0; i < datas.length; i++) {
            if (datas[i].dictValue === $("#balaCorp").val()) {
              $("#balaCorpName").val(datas[i].dictLabel);
            }
        }

        if (yearMonth) {
            var arr = yearMonth.split("-");
            $("#month").val(arr[1]).prop("disabled", true).prop("readOnly",false);
            $("#year").val(arr[0]).prop("disabled", true).prop("readOnly",false);
        } else {
            layui.use('laydate', function(){
                var laydate = layui.laydate;
                laydate.render({
                    elem: '#month',
                    type: 'month',
                    format:"MM"
                });
                laydate.render({
                    elem: '#year',
                    type: 'year'
                });
            });
        }

        var dict = [[${@dict.getType('bala_corp')}]];
        $.ajax({
            type: "get",
            url:  ctx +"receive/selectClient?customerId="+$("#customerId").val(),
            success: function(result) {
                $("#checkType").val(result.billingType);
                $("#checkHead").val(result.billingPayable);
                for (var i = 0; i < dict.length; i++) {
                    if (dict[i].dictValue == result.billingCorp);
                    $("#checkCorpName").val(dict[i].dictLabel);
                }
                $("#checkCorp").val(result.billingCorp);

            }
        });

        showTtMsg()

    });

    /**
     * 提交的方法
     */
    function commit() {
        // 此时统计所有deatilId
        let ids = [];
        $('tr[detail-id]').each(function(){
            if (!$(this).hasClass('delFlag')) {
                ids.push($(this).attr('detail-id'));
            }
        });
        $("#receiveDetailId").val(ids.join(','));
        if ($("#receiveDetailId").val() == "") {
            $.modal.msgError("生成对账单至少需要一条应收明细")
            return;
        }
        if ($.validate.form()){
            $(":disabled").attr("disabled", false).addClass("tmp");
            var datax = $('#form-receCheck-add').serialize();
            $(".tmp").attr("disabled", true).removeClass("tmp")
            $.ajax({
                type: "post",
                dataType: "json",
                data:  datax,
                url:  ctx +"receive/checkReceiveCheckSheetByCustomerId",
                success: function(result) {
                    // 禁用按钮
                    $.modal.disable();
                    if (result === 0) {
                        $.modal.confirm("确认生成对账单吗？", function(){
                            $.operate.saveTab(ctx + "receive/addReceCheck", datax);
                        })
                    } else {
                        var title = $("#year").val()+"年"+$("#month").val()+"月对账单已存在，是否需要创建新的对账单?";
                        $.modal.confirm(title, function() {
                            $.operate.saveTab(ctx + "receive/addReceCheck", datax);
                        });
                    }
                }
            });
        }
    }
    function removeRow(el, payDetailId, cur_total_amount, cur_got_amount) {
        //var ids = $("#receiveDetailId").val().split(",");
        let $tr = $(el).closest("tr");
        if ($tr.hasClass("delFlag")) {
            $tr.removeClass("delFlag");
            totalAmount = totalAmount.plus(new BigNumber(cur_total_amount));
            gotAmount = gotAmount.plus(new BigNumber(cur_got_amount))
            ungotAmount = totalAmount.plus(gotAmount);
        } else {
            $tr.addClass("delFlag");
            totalAmount = totalAmount.minus(new BigNumber(cur_total_amount));
            gotAmount = gotAmount.minus(new BigNumber(cur_got_amount))
            ungotAmount = totalAmount.minus(gotAmount);
        }
        $("#td_totalAmount").text(totalAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
        $("#totalAmount").val(totalAmount.toNumber())
        $("#td_gotAmount").text(gotAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
        $("#td_ungotAmount").text(ungotAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
        $("#ungotAmount").val(ungotAmount.toNumber());
        /*for (var i = 0; i < ids.length; i++) {
            if (ids[i] == payDetailId) {
                ids.splice(i, 1);
                $(el).parents("tr:first").remove();
                $("#receiveDetailId").val(ids.join(','))
                totalAmount = totalAmount.minus(new BigNumber(cur_total_amount));
                gotAmount = gotAmount.minus(new BigNumber(cur_got_amount))
                ungotAmount = totalAmount.minus(gotAmount);
                $("#td_totalAmount").text(totalAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#totalAmount").val(totalAmount.toNumber())
                $("#td_gotAmount").text(gotAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#td_ungotAmount").text(ungotAmount.toNumber().toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                $("#ungotAmount").val(ungotAmount.toNumber());
                break;
            }
        }*/
        showTtMsg();
    }
    function findLocal() {
        var feeType = $('#feeType').val();
        var tt = $.modal.layerLoading("正在过滤，请稍候...");
        $('tr[fylx]').each(function(){
            if (!feeType || $(this).attr('fylx') == feeType) {
                $(this).show()
            } else {
                $(this).hide();
            }
        })
        layer.close(tt);
    }
    function showAll() {
        $('#feeType').val('');
        $('#feeType').selectpicker('refresh')
        findLocal();
    }
    function showTtMsg() {
        $('#tt-msg').text("共"+$('[detail-id]').length+"条应收明细");
        if ($('.delFlag').length > 0) {
            $('#tt-msg').append('，已剔除' + $('.delFlag').length + "条");
        }
    }
</script>
</body>

</html>