<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应收明细修改页面')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-receive-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="receiveDetailId" type="hidden" id="receiveDetailId"
                               th:value="${receiveDetail.receiveDetailId}">
                        <input name="invoiceId" type="hidden" id="invoiceId" th:value="${receiveDetail.invoiceId}">
                        <input name="corDate" type="hidden" th:value="${#dates.format(receiveDetail.corDate, 'yyyy-MM-dd HH:mm:ss')}"/>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        发货单号：</label>
                                    <div class="col-sm-7">

                                            <input name="invoiceVbillno" id="invoiceVbillno" class="form-control dis"
                                                   type="text" disabled
                                                   th:value="${receiveDetail.invoiceVbillno}">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        结算客户：</label>
                                    <div class="col-sm-7">
                                        <div class="input-group">
                                            <input name="balaName" id="balaName" onclick="selectClearingClient()" type="text"
                                                   class="form-control dis"  th:value="${receiveDetail.balaName}" readonly>
                                            <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                        </div>

                                        <input name="balatype" id="balatype" class="form-control" type="hidden"
                                               th:value="${receiveDetail.balatype}">
                                        <input name="balaCode" id="balaCode" class="form-control" type="hidden"
                                               th:value="${receiveDetail.balaCode}">
                                        <input name="balaCustomer" id="balaCustomer" class="form-control"
                                               type="hidden" th:value="${receiveDetail.balaCustomer}">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        结算公司：</label>
                                    <div class="col-sm-7">
                                        <input class="form-control " type="text"
                                               maxlength="30" disabled th:value="${receiveDetail.balaCorpName}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        客户名称：</label>
                                    <div class="col-sm-7">
                                        <input name="custName" id="custName" class="form-control dis" type="text"
                                               disabled th:value="${receiveDetail.custName}">
                                        <input name="custCode" id="custCode" class="form-control" type="hidden"
                                               th:value="${receiveDetail.custCode}">
                                        <input name="customerId" id="customerId" class="form-control" type="hidden"
                                               th:value="${receiveDetail.customerId}">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">计费件数：</label>
                                    <div class="col-sm-7">
                                        <input name="numCount" id="numCount" class="form-control dis" disabled
                                               th:value="${receiveDetail.numCount}"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        计费重量：</label>
                                    <div class="col-sm-7">
                                        <input name="feeWeightCount" id="feeWeightCount" class="form-control dis" disabled
                                               th:value="${receiveDetail.feeWeightCount}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        计费体积：</label>
                                    <div class="col-sm-7">
                                        <input name="volumeCount" id="volumeCount" class="form-control dis"
                                               disabled th:value="${receiveDetail.volumeCount}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">要求提货日期：</label>
                                    <div class="col-sm-7">
                                        <input class="form-control dis"
                                               name="reqDeliDate" id="reqDeliDate" disabled
                                               th:value="${#dates.format(receiveDetail.reqDeliDate, 'yyyy-MM-dd')}">
                                    </div>
                                </div>
                            </div>


                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">要求到货日期：</label>
                                    <div class="col-sm-7">
                                        <input class="form-control dis"
                                               name="reqArriDate" id="reqArriDate" disabled
                                               th:value="${#dates.format(receiveDetail.reqArriDate, 'yyyy-MM-dd')}" >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">客户订单号：</label>
                                    <div class="col-sm-7">
                                        <input class="form-control"
                                               name="custOrderno" id="custOrderno"
                                               th:value="${receiveDetail.custOrderno}" >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6" id="free">
                                <div class="form-group">
                                    <label class="col-sm-5">费用类型：</label>
                                    <div class="col-sm-7">
                                        <select name="costTypeOnWay" id="costTypeOnWay" class="form-control valid"
                                                th:with="type=${@dict.getType('cost_type_on_way')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}" th:field="${receiveDetail.costTypeOnWay}"></option>
                                        </select>

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        费用金额：</label>
                                    <div class="col-sm-7">
                                        <input class="form-control" disabled type="text" th:value="${receiveDetail.transFeeCount}">
                                        <input name="freeType" id="freeType" th:value="${receiveDetail.freeType}" type="hidden">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-3">备注：</label>
                                    <div class="col-sm-12">
                                            <textarea name="memo" id="memo" maxlength="250" class="form-control valid"
                                                      rows="3" th:value="${receiveDetail.memo}"
                                                      th:text="${receiveDetail.memo}"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--基础信息 end-->
                    </div>
                </div>
            </div>


        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFive">货品明细</a>
                </h4>
            </div>
            <div id="collapseFive" class="panel-collapse collapse in">
                <div class="panel-body">

                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 15%;">货品类型</th>
                                <th style="width: 12%;">货品名称</th>
                                <th style="width: 8%;">件数</th>
                                <th style="width: 8%;">重量</th>
                                <th style="width: 8%;">体积</th>
                                <th style="width: 8%;">金额</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="invPackGoods,invPackGoodsStat : ${invoice.invPackGoodsList}">
                                <td style="word-break: keep-all;white-space:nowrap;">
                                    <div class="input-group" th:text="${invPackGoods.goodsTypeName}"></div>
                                </td>
                                <td style="word-break: keep-all;white-space:nowrap;">
                                    <div class="input-group" th:text="${invPackGoods.goodsName}"></div>
                                </td>
                                <td>
                                    <div class="input-group" th:text="${invPackGoods.num}"></div>
                                </td>
                                <td>
                                    <div class="input-group" th:text="${invPackGoods.weight}"></div>
                                </td>
                                <td>
                                    <div class="input-group" th:text="${invPackGoods.volume}"></div>
                                </td>


                                <td th:align="right"  th:text="￥+${#numbers.formatDecimal(invPackGoods.sum,1,'COMMA',2,'POINT')}">
                                </td>

                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--订单货品费用明细 end-->
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">发货单提货信息</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">APP联系人：</label>
                                <div class="col-sm-8">
                                    <div class="input-group" th:text="${invoice.appDeliContact}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">APP联系人手机：</label>
                                <div class="col-sm-8">
                                    <div class="input-group" th:text="${invoice.appDeliMobile}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">提货方：</label>
                                <div class="col-sm-8">
                                    <div class="input-group" th:text="${invoice.deliAddrName}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">联系电话：</label>
                                <div class="col-sm-8" th:text="${invoice.deliMobile}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">联系人：</label>
                                <div class="col-sm-8" th:text="${invoice.deliContact}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">

                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-1">地址：</label>
                                <div class="col-sm-11" th:text="${invoice.deliProName+invoice.deliCityName+invoice.deliAreaName+invoice.deliDetailAddr}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseThree">发货单收货信息</a>
                </h4>
            </div>
            <div id="collapseThree" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!--订单收货信息 end-->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">收货方：</label>
                                <div class="col-sm-8">
                                    <div class="input-group" th:text="${invoice.arriAddrName}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">联系电话：</label>
                                <div class="col-sm-8" th:text="${invoice.arriMobile}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">联系人：</label>
                                <div class="col-sm-8" th:text="${invoice.arriContact}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">

                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-1">地址：</label>
                                <div class="col-sm-11" th:text="${invoice.arriProName+invoice.arriCityName+invoice.arriAreaName+invoice.arriDetailAddr}">
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--订单收货信息 end-->
                </div>
            </div>
        </div>

    </form>
</div>

<div class="row">
    <!--<div class="col-sm-offset-5 col-sm-10">-->
        <!--<button type="button" class="btn btn-sm btn-primary" onclick="commit()"><i class="fa fa-check"></i>保-->
            <!--存-->
        <!--</button>&nbsp;-->
        <!--<button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭-->
        <!--</button>-->
    <!--</div>-->
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "receive";
    $(function () {

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');

        var options = {};
        $.table.init(options);

        var freeType =  $("#freeType").val();
        // 费用类型为运费时 去除选择在途费用类型框
        if (freeType === '0'){
            var elem=document.getElementById('free');
            elem.parentNode.removeChild(elem);
        }
    });

    function selectClearingClient() {
        // 如果 客户名称未选中，则随意传入一个不存在的id:'false',让搜索出来的列表为空
        var customerId = $("#customerId").val() === '' ? 'false' : $("#customerId").val();
        $.modal.open("选择客户", ctx + "client/related?customerId=" + customerId,'','',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            //结算客户名称
            $("#balaName").val(rows[0]["custName"]);
            //结算客户编码
            $("#balaCode").val(rows[0]["custCode"]);
            //结算客户id
            $("#balaCustomer").val(rows[0]["customerId"]);

            $("#custType").val(rows[0]["custType"]);

            layer.close(index);
        });
    }

    /**
     * 发货单的选择框invoiceId
     */
    function selectInvoice() {

        $.modal.open("选择发货单", prefix + "/chooseInvoice");
    }

    /**
     * 表单提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            $('.dis').removeAttr("disabled");

            // 表单提交
            $.operate.saveTab(prefix + "/editReceive", $('#form-receive-add').serialize());
        }
    }




</script>
</body>

</html>