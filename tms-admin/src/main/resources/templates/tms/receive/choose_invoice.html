<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('发货单选择页')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-sm-5">
                        <div class="form-group">
                            <label class="col-sm-4">发货单号：</label>
                            <div class="col-sm-8">
                                <input name="vbillno" id="vbillno" placeholder="请输入发货单号" class="form-control valid" type="text"
                                       aria-required="true">
                                <input id="hiddenText" type="text" style="display:none" />

                            </div>
                        </div>
                    </div>
                    <div class="col-sm-5">
                        <div class="form-group">

                        </div>
                    </div>

                    <div class="col-sm-2">
                        <label class="col-sm-4"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>

</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var carLen = [[${@dict.getType('car_len')}]];
    var carType = [[${@dict.getType('car_type')}]];
    var transCode = [[${@dict.getType('trans_code')}]];
    var prefix = ctx + "invoice";
    var customerId = [[${customerId}]];

    $(function () {
        var options = {
            url: prefix + "/invoiceList?customerId="+customerId,
            showToggle: false,
            showColumns: false,
            modalName: "发货单",
            uniqueId: "invoiceId",
            clickToSelect:true,
            columns: [
                {
                    radio: true
                },
                {
                    title: '发货单号',
                    field: 'vbillno',
                    align: 'left'
                },
                {
                    title: '发货单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '<span>新建</label>';
                            case 1:
                                return '<span>已确认</label>';
                            case 2:
                                return '<span>部分提货</label>';
                            case 3:
                                return '<span>已提货</label>';
                            case 4:
                                return '<span>部分到货</label>';
                            case 5:
                                return '<span>已到货 </label>';
                            case 6:
                                return '<span>部分回单</label>';
                            case 7:
                                return '<span>已回单</label>';
                            case 8:
                                return '<span>关闭</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '客户名称',
                    field: 'custName',
                    align: 'left',
                },
                {
                    title: '要求提货日期',
                    field: 'reqDeliDate',
                    align: 'left',
                    visible: false
                },
                {
                    title: '要求到货日期',
                    field: 'reqArriDate',
                    align: 'left',
                    visible: false
                },
                {
                    title: '所属线路',
                    field: 'transLineName',
                    align: 'left',
                    visible: false
                },
                {
                    title: '车长',
                    field: 'carLen',
                    align: 'left',
                    visible: false,
                    formatter: function status(row, value) {
                        return $.table.selectDictLabel(carLen, value.carLen);
                    }
                },
                {
                    title: '车型',
                    field: 'carType',
                    align: 'left',
                    visible: false,
                    formatter: function status(row, value) {
                        return $.table.selectDictLabel(carType, value.carType);
                    }
                },
                {
                    title: '运输方式',
                    field: 'transCode',
                    align: 'left',
                    visible: false,
                    formatter: function status(row, value) {
                        return $.table.selectDictLabel(transCode, value.transCode);
                    }
                },
                {
                    title: '运费',
                    field: 'arriPayAmount',
                    align: 'left',
                    visible: false
                },
                {
                    title: '提货方',
                    field: 'deliName',
                    align: 'left',
                    visible: false
                },
                {
                    title: '提货省份',
                    field: 'deliProName',
                    align: 'left',
                    visible: false,
                },
                {
                    title: '提货城市',
                    field: 'deliCityName',
                    align: 'left',
                    visible: false
                },
                {
                    title: '收货方',
                    field: 'arriName',
                    align: 'left',
                    visible: false
                },
                {
                    title: '收货省份',
                    field: 'arriProName',
                    align: 'left',
                    visible: false
                },
                {
                    title: '收货城市',
                    field: 'arriCityName',
                    align: 'left',
                    visible: false

                },
                {
                    title: '总件数',
                    field: 'numCount',
                    align: 'left',
                    visible: false
                },
                {
                    title: '总重量',
                    field: 'weightCount',
                    align: 'left',
                    visible: false
                },
                {
                    title: '总体积',
                    field: 'volumeCount',
                    align: 'left',
                    visible: false
                },
                {
                    title: '总金额',
                    field: 'costAmount',
                    align: 'right',
                    visible: false
                }

            ]
        };
        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });

    /**
     * 选择发货单后的提交方法
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.close();
        // 选中的发货单号
        parent.$("#invoiceVbillno").val($.table.selectColumns("vbillno").join());
        // 发货单Id
        parent.$("#invoiceId").val($.table.selectColumns("invoiceId").join());
        // 结算公司
        var datas = [[${@dict.getType('bala_corp')}]];
        for ( var i = 0; i < datas.length; i++) {
            if (datas[i].dictValue === $.table.selectColumns("balaCorpId").join()) {
                parent.$("#balaCorpName").val(datas[i].dictLabel);
                parent.$("#balaCorp").val(datas[i].dictValue);
            }
        }
        // 选中的结算组
        parent.$("#balaDept").val($.table.selectColumns("balaDept").join());
        // 业务员
        parent.$("#psndoc").val($.table.selectColumns("psndoc").join());
        // 运营部
        parent.$("#salesDept").val($.table.selectColumns("salesDept").join());
        // 选中的结算客户名
        parent.$("#balaName").val($.table.selectColumns("balaName").join());
        parent.$("#balaCode").val($.table.selectColumns("balaCode").join());
        parent.$("#balaCustomer").val($.table.selectColumns("balaCustomerId").join());
        // 选中的结算方式
        parent.$("#balatype").val($.table.selectColumns("balaType").join());
        // 选中的计费件数
        parent.$("#numCount").val($.table.selectColumns("numCount").join());
        // 选中的计费重量
        parent.$("#feeWeightCount").val($.table.selectColumns("weightCount").join());
        // 选中的计费体积
        parent.$("#volumeCount").val($.table.selectColumns("volumeCount").join());
        // 选中的运费
        parent.$("#arriPayAmount").val($.table.selectColumns("arriPayAmount").join());
        // 选中的总金额
        parent.$("#totalCount").val($.table.selectColumns("costAmount").join());
        // 选中的要求提货日期
        parent.$("#reqDeliDate").val($.table.selectColumns("reqDeliDate").join());
        // 选中的发货单状态
        parent.$("#vbillstatus").val($.table.selectColumns("vbillstatus").join());
        // 选中的客户名称
        parent.$("#custName").val($.table.selectColumns("custName").join());
        parent.$("#custCode").val($.table.selectColumns("custCode").join());
        parent.$("#customerId").val($.table.selectColumns("customerId").join());
        // 选中的要求到货日期
        parent.$("#reqArriDate").val($.table.selectColumns("reqArriDate").join());
        // 客户订单号
        parent.$("#custOrderno").val($.table.selectColumns("custOrderno").join());
        // 地址信息
        $.ajax({
            type: "get",
            url:  ctx +"receive/selectInvoiceById?invoiceId="+$.table.selectColumns("invoiceId").join(),
            success: function(result) {
                parent.$("#appDeliContact").html(result.invoice.appDeliContact);
                parent.$("#appDeliMobile").html(result.invoice.appDeliMobile);
                parent.$("#deliAddrName").html(result.invoice.deliAddrName);
                parent.$("#deliMobile").html(result.invoice.deliMobile);
                parent.$("#deliContact").html(result.invoice.deliContact);
                parent.$("#deliAddr").html(result.invoice.deliProName+result.invoice.deliCityName+result.invoice.deliAreaName+result.invoice.deliDetailAddr);
                parent.$("#arriAddrName").html(result.invoice.arriAddrName);
                parent.$("#arriMobile").html(result.invoice.arriMobile);
                parent.$("#arriContact").html(result.invoice.arriContact);
                parent.$("#arriAddr").html(result.invoice.arriProName+result.invoice.arriCityName+result.invoice.arriAreaName+result.invoice.arriDetailAddr);

                // 清空历史
                parent.$("#goods").html("");
                var html = '';
                for (var i = 0; i < result.invoice.invPackGoodsList.length; i++) {
                    html += '<tr>' +
                        '<td ><div class="input-group">'+result.invoice.invPackGoodsList[i].goodsTypeName+'</div></td>' +
                        '<td ><div class="input-group">'+result.invoice.invPackGoodsList[i].goodsName+'</div></td>' +
                        '<td ><div class="input-group">'+result.invoice.invPackGoodsList[i].num+'</div></td>' +
                        '<td ><div class="input-group">'+result.invoice.invPackGoodsList[i].weight+'</div></td>' +
                        '<td ><div class="input-group">'+result.invoice.invPackGoodsList[i].volume+'</div></td>' +
                        '<td  >' +result.invoice.invPackGoodsList[i].sum+'</td>' +
                    '</tr>'
                }
                parent.$("#goods").append(html);
            }
        });

    }
</script>
</body>
</html>