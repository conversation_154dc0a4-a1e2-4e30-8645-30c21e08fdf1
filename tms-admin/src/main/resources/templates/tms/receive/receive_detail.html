<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收款申请管理')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="invoiceId" id="invoiceId" th:value="${invoiceId}">
                <input type="hidden" name="vbillstatus" th:value="${vbillstatus}">
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger " onclick="remove()" shiro:hasAnyPermissions="finance:receive:delete,finance:fleet:receive:delete">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-primary single disabled" onclick="receRecord()" shiro:hasAnyPermissions="finance:receive:receRecord,finance:fleet:receive:receRecord">
                <i class="fa fa-calculator"></i> 收款记录
            </a>
<!--            <a class="btn btn-primary single disabled"  onclick="receiptAppl()" shiro:hasAnyPermissions="finance:receive:receiptAppl,finance:fleet:receive:receiptAppl">-->
<!--                <i class="fa fa-dollar"></i> 收款/开票申请-->
<!--            </a>-->
            <a class="btn btn-primary multiple disabled" onclick="affirm()" shiro:hasAnyPermissions="finance:receive:affirm,finance:fleet:receive:affirm">
                <i class="fa fa-mail-reply"></i> 确认
            </a>
            <a class="btn btn-danger single disabled"onclick="oppositeAffirm()" shiro:hasAnyPermissions="finance:receive:oppositeAffirm,finance:fleet:receive:oppositeAffirm">
                <i class="fa fa-exclamation"></i> 反确认
            </a>
<!--            <a class="btn btn-primary"  onclick="editReceive()" shiro:hasPermission="finance:fleet:receive:adjust">-->
<!--                <i class="fa fa-exclamation"></i> 调整费用-->
<!--            </a>-->


            <a class="btn btn-primary multiple disabled " onclick="checking()" shiro:hasPermission="finance:receive:checking">
                <i class="fa fa-file-text-o"></i> 生成对账单
            </a>
            <a class="btn btn-primary multiple disabled " onclick="insertChecking()" shiro:hasPermission="finance:receive:insertChecking">
                <i class="fa fa-file-text-o"></i> 加入对账单
            </a>

            <a class="btn btn-danger " onclick="cancelDriverCollection()" shiro:hasAnyPermissions="finance:receive:revokeDriverCollection,finance:fleet:receive:revokeDriverCollection">
                <i class="fa fa-times"></i> 取消司机代收
            </a>

<!--            <div class="dropdown" style="display: inline-block;"-->
<!--                 shiro:hasAnyPermissions="finance:receive:revokeDriverCollection,finance:fleet:receive:revokeDriverCollection">-->
<!--                <button class="btn btn-danger dropdown-toggle" style="padding: 4px 12px;" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">-->
<!--                    撤销司机代收-->
<!--                    <span class="caret"></span>-->
<!--                </button>-->
<!--                <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">-->
<!--                    <li>-->
<!--                        <a class="btn multiple disabled " onclick="revokeDriverCollection()" shiro:hasAnyPermissions="finance:receive:revokeDriverCollection,finance:fleet:receive:revokeDriverCollection">-->
<!--                            <i class="fa fa-repeat"></i> 撤销司机代收-->
<!--                        </a>-->
<!--                    </li>-->
<!--                    <li>-->
<!--                        <a class="btn  " onclick="cancelDriverCollection()" shiro:hasAnyPermissions="finance:receive:revokeDriverCollection,finance:fleet:receive:revokeDriverCollection">-->
<!--                            <i class="fa fa-times"></i> 取消司机代收-->
<!--                        </a>-->
<!--                    </li>-->
<!--                </ul>-->
<!--            </div>-->

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    //是否是车队
    var isFleet = [[${isFleet}]];
    //
    var isFleetAssign = [[${isFleetAssign}]];

    var prefix = ctx + "receive/receive_detail";
    //运费费用类型
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    //在途费用类型
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];

    var receiveDetailStatus = [[${receiveDetailStatus}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail",
            showToggle: false,
            showColumns: false,
            modalName: "应收汇总",
            clickToSelect:true,
            height: 560,
            showFooter:true,
            uniqueId: "receiveDetailId",
            columns: [{
                checkbox: true
            },
                {title: '操作', align: 'left',
                    field: 'invoiceId',
                    formatter: function (value,row,index) {
                        var actions = [];
                        // if ([[${@permission.hasPermi('finance:receive:edit')}]] != "hidden" && !isFleet) {
                        //     actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="修改" onclick="edit(\'' + row.receiveDetailId + '\',\'' + row.vbillstatus + '\',\'' + row.isClose + '\')"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>');
                        // }
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="运费拆分" onclick="splitReceiveDetail(\'' + row.receiveDetailId + '\',\'' + row.transFeeCount + '\',\'' + row.freeType + '\')"><i  class="fa fa-calculator" style="font-size: 15px;" ></i></a>');
                        
                        return actions.join('');
                    }
                },
                {field: 'vbillno',align: 'left',title: '应收单号',
                    formatter: function (value, row, index) {
                        var result = value;

                        if(row.isCollect == 1) {
                            result += ' <span class="label label-success" style="padding:1px;">代</span>'
                        }
                        return result;

                    }},
                {field: 'vbillstatus',title: '单据状态',align: 'left',
                    formatter: function status(value,row) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-primary">新建</span>'
                            case 1:
                                return '<span class="label label-warning">已确认</span>';
                            case 2:
                                return '<span class="label label-coral">已对账</span>';
                            case 3:
                                return '<span class="label label-info">部分核销 </label>';
                            case 4:
                                return '<span class="label label-success">已核销</span>';
                            case 5:
                                return '<span class="label label-inverse">关闭 </label>';
                            default:
                                break;
                        }
                    }
                },
                {field: 'freeType',title: '费用类型',align: 'left',
                    formatter: function status(value,row) {
                                    switch(value - 0) {
                                        case 0:
                                            return '<span>运费</label>';
                                        case 1:
                                            return '<span>在途费用</label>';
                                        case 2:
                                            return '<span>调整费用</label>';
                                        default:
                                            break;
                                    }
                             }
                },
                {field: 'costTypeOnWay',title: '费用类型明细',align: 'left',
                    formatter: function status(value,row) {
                                    switch(row.freeType - 0) {
                                        case 0:
                                            return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                                        case 1:
                                            return $.table.selectDictLabel(costTypeOnWay, value);
                                        case 2:
                                            return '<span>调整费用</label>';
                                        default:
                                            break;
                                    }
                                }
                },
                {field: 'transFeeCount',title: '金额',align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {field: 'applCheckAmount',title: '申请开票金额',align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {field: 'checkAmount',title: '已开票金额',align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '申请附件',
                    align: 'left',
                    field : 'entrustCostFileId',
                    formatter: function status(value, row, index) {
                        var html = ""
                        if(row.params != null) {
                            if (row.params.fileList != undefined && row.params.fileList != null) {
                                row.params.fileList.forEach(function (element, index) {
                                    let filePath = element.filePath
                                    let fileName = element.fileName
                                    // let downloadHtml = `<span style="cursor: pointer;margin-right: 15px;" title="点击下载文件" onclick="downloadContract('${filePath}','${fileName}')"><i class="fa fa-download"></i></span>`

                                    html = html + "<span><a href='#' onclick=\"downloadContract('" + filePath + "','" + fileName  +"')\">" + element.fileName + "</a></span><br>"
                                });
                            }
                        }
                        return html;
                    }
                },
                {field: 'memo',title: '备注',align: 'left'},
                {field: 'isAdjust',title: '是否为调整单',align: 'left',
                    formatter: function (value, row, index) {
                                    if (value === 0) {
                                        return "否";
                                    } else {
                                        return "是";
                                    }
                                }
                },
                {field: 'adjustMemo',title: '调整原因',align: 'left'},
                {field: 'regDate',title: '创建时间',align: 'left'},
                {field: 'regUserName',title: '创建人',align: 'left'},
                {field: 'isApply',title: '是否单笔申请',align: 'left',
                    formatter: function status(value,row) {
                        if (value === 1) {
                            return '<span>已申请</label>';
                        } else {
                            return '<span>未申请</label>';
                        }
                    }
                },
                {field: 'replyMemo',title: '撤销审核备注',align: 'left'}
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });


    function downloadContract(filePath, fileName) {
        const protocol = document.location.protocol; // 确保使用与当前页面相同的协议
        const a = document.createElement('a');
        a.href = protocol + "//" + document.location.host + filePath; // 动态选择协议
        a.download = fileName;
        a.click();
    }
    /**
     * 批量删除
     */
    function remove() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("只能删除新建状态下的应收单据");
                return;
            }
            if (bootstrapTable[i]["isClose"] === 1 ) {
                $.modal.alertWarning("请选择未在关账日期内的应收单据");
                return;
            }
        }
        var rows = $.table.selectColumns($.table._option.uniqueId);
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要删除选中的数据吗?", function() {
            var url = ctx + "receive/remove";
            var data = { "ids": rows.join() };
            $.operate.submit(url, "post", "json", data);
        });
    }

    /**
     * 撤销司机代收
     */
    function revokeDriverCollection() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0 && bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("只能撤回“新建”或者“已确认”状态下的应收单据");
                return;
            }
            if (bootstrapTable[i]["isCollect"] !== 1) {
                $.modal.alertWarning("请选择司机代收的应收单据");
                return;

            }
        }
        var rows = $.table.selectColumns($.table._option.uniqueId);
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要撤销选中的数据吗?", function() {
            var url = ctx + "receive/revokeDriverCollection";
            var data = { "receiveDetailId": rows.join() };
            $.operate.submit(url, "post", "json", data);
        });
    }

    /**
     * 取消司机代收
     */
    function cancelDriverCollection() {
        var invoiceId = $("#invoiceId").val();

        $.modal.confirm("确认要取消司机代收吗?", function() {
            var url = ctx + "receive/cancelDriverCollection";
            var data = { "invoiceId": invoiceId };
            $.operate.submit(url, "post", "json", data);
        });
    }


    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;
    function receRecord(){
        var id = $.table.selectColumns('receiveDetailId');
        var url =  ctx + "receSheetRecord" + "/receRecord?receiveDetailId="+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "收款记录",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }


    /**
     * 跳转应收修改页面
     * @param id
     */
    function edit(id,vbillstatus,isClose) {
        if (vbillstatus != 0) {
            $.modal.alertWarning("只能修改新建状态的应收单");
            return;
        }
        if (isClose === '1') {
            $.modal.alertWarning("该应收单已关账");
            return;
        }
        var url = ctx + "receive/edit?receiveDetailId="+id;
        $.modal.open("应收明细修改", url, width, height);
    }

    /**
     * 收款申请
     */
    function receiptAppl(){
        //应收状态
        var vbillstatus = $.table.selectColumns('vbillstatus').join();
        //应收id
        var id = $.table.selectColumns('receiveDetailId');
        if (vbillstatus != 1) {
            $.modal.alertError("请选择已确认的数据");
            return;
        }
        var url = ctx + "receive/receiptAppl/"+id;
        $.modal.openTab('收款/开票申请',url);
    }

    /**
     *
     * 生成对账单的方法
     */
    function checking() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        var salesDept = bootstrapTable[0].salesDept;
        var vbillno = bootstrapTable[0].vbillno;
        var isCollect = 0
        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("请选择已确认的数据！");
                return;
            }

            if (bootstrapTable[i]["isApply"] === 1) {
                $.modal.alertWarning("请选择未单独申请的数据！");
                return;
            }

            if (bootstrapTable[i].salesDept !== salesDept) {
                $.modal.alertWarning(bootstrapTable[i].vbillno + "与" + vbillno + "运营组不一致，请分开打包！");
                return;
            }

            if (bootstrapTable[i].isCollect == 1) {
                isCollect = 1;
            }

        }
        //应收明细id
        var receiveDetsailIds = $.table.selectColumns('receiveDetailId').join();

        if (isCollect == 1) {
            var lock = false;
            layer.confirm("存在司机代收，生成对账单时，司机代收的应收数据将自动被过滤。", {
                // area:area,
                icon: 0,
                title: "是否确定生成对账单",
                btn: ['确定', '取消']
            }, function (index) {
                if (!lock) {
                    lock = true
                    layer.close(index);
                    $.modal.openTab("生成对账单", ctx + "receive/checking?invoiceId=&receiveDetsailIds=" + receiveDetsailIds);
                }
            });
        }else {
            $.modal.openTab("生成对账单", ctx + "receive/checking?invoiceId=&receiveDetsailIds=" + receiveDetsailIds);
        }

    }

    /**
     * 加入对账单的方法
     */
    function insertChecking() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        var salesDept = bootstrapTable[0].salesDept;
        var vbillno = bootstrapTable[0].vbillno;
        var isCollect = 0

        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("请选择已确认的数据！");
                return;
            }

            if (bootstrapTable[i]["isApply"] === 1) {
                $.modal.alertWarning("请选择未单独申请的数据！");
                return;
            }
            if (bootstrapTable[i].salesDept !== salesDept) {
                $.modal.alertWarning(bootstrapTable[i].vbillno + "与" + vbillno + "运营组不一致，请分开加入对账包！");
                return;
            }
            if (bootstrapTable[i].isCollect == 1) {
                isCollect = 1;
            }

        }
        //承运商id
        var customerId =  bootstrapTable[0]["customerId"];
        //应收明细id
        var receiveDetsailIds = $.table.selectColumns('receiveDetailId').join();

        var fleet = isFleet ? 1 : 0;


        if (isCollect == 1) {
            var lock = false;
            layer.confirm("存在司机代收，加入对账单时，司机代收的应收数据将自动被过滤。", {
                // area:area,
                icon: 0,
                title: "是否确定加入对账单",
                btn: ['确定', '取消']
            }, function (index) {
                if (!lock) {
                    lock = true
                    layer.close(index);
                    $.modal.open("加入对账单", ctx + "receive/insertChecking?customerId="
                        + customerId + "&invoiceIds=&receiveDetsailIds=" + receiveDetsailIds + "&salesDept="+salesDept + "&isFleet=" + fleet);
                }
            });

        }else {
            $.modal.open("加入对账单", ctx + "receive/insertChecking?customerId="
                + customerId + "&invoiceIds=&receiveDetsailIds=" + receiveDetsailIds + "&salesDept="+salesDept+ "&isFleet=" + fleet);

        }

    }

    /**
     * 确认应收明细
     */
    function affirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');


        for (var i = 0; i < bootstrapTable.length;i++ ) {
            if (bootstrapTable[i]["vbillstatus"] !== 0) {
                $.modal.alertWarning("应收单据只能为新建状态下才能确认");
                return;
            }
            // if (bootstrapTable[i]["isClose"] === 1 ) {
            //     $.modal.alertWarning("请选择未在关账日期内的应收单据");
            //     return;
            // }
        }

        var receiveDetailIds =  $.table.selectColumns("receiveDetailId");
        if (receiveDetailIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.confirm("是否确认？", function () {
            $.operate.post(ctx + "receive/affirm_by_receive", {"receiveIds": receiveDetailIds.join()});
        });
    }

    /**
     * 跳转反确认页面
     */
    function oppositeAffirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["vbillstatus"] !== 1) {
            $.modal.alertWarning("应收单据只能为已确认状态下才能反确认");
            return;
        }
        // if (bootstrapTable[0]["hasApply"] === 1) {
        //     $.modal.alertWarning("存在单独申请的应收，无法反确认！");
        //     return;
        // }
        // if (bootstrapTable[0]["isClose"] === 1 ) {
        //     $.modal.alertWarning("请选择未在关账日期内的应收单据");
        //     return;
        // }

        var receiveDetailIds =  $.table.selectColumns("receiveDetailId");
        if (receiveDetailIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.open("反确认", ctx + "receive/back_confirm_by_receive/" + receiveDetailIds ,500,300);
    }

    /*
     * 调整费用
     */
    function editReceive() {
        var invoiceId = $("#invoiceId").val();

        $.modal.open("调整费用", ctx + "receive/edit_receive/" + invoiceId,400,290);

        //验证发货单是否超过五天
        /*$.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.open("调整费用", ctx + "receive/edit_receive/" + invoiceId,400,290);
                }
            }
        });*/
    }
    function splitReceiveDetail(receiveDetailId,transFeeCount,freeType) {
       
        if (freeType != 0) {
            $.modal.alertWarning("请选择运费类型的应收明细！");
            return;
        }

        $.modal.open("运费拆分",ctx + "receive/split_receive_detail?receiveDetailId=" + receiveDetailId+"&transFeeCount="+transFeeCount, '500', '300');
    }

</script>


</body>
</html>