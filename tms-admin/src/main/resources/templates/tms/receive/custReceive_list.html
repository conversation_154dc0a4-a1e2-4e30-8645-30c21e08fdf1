<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户应收列表')"/>
    <style>
        .sm{
            background: #ffefef;
            padding: 5px 10px;
            border: 1px #ff9999 solid;
            margin-top: 10px;
        }
        .sm_icon{
            width: 18px;
            height: 18px;
            background: #faad14;
            color: #fff;
            border-radius: 50%;
            display: inline-block;
            line-height: 18px;
            text-align: center;
            font-size: 12px;
        }
        .sm_text{
            color: #655959;
            display: inline-block;
            margin-left: 10px;
            line-height: 20px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="sm">
            <span class="sm_icon">!</span>
            <span class="sm_text" id="warn_msg">
                加载中...
            </span>
        </div>
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal" onsubmit="return false;">
                <div class="row no-gutter">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-5">客户名称：</label>-->
                            <div class="col-sm-12">
                                <input name="custName" id="custName" class="form-control" type="text" placeholder="客户名称"
                                       maxlength="30" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-4">运营组：</label>-->
                            <div class="col-sm-12">
                                <select name="salesDept" id="salesDept" class="form-control"
                                        aria-invalid="false" required data-none-selected-text="运营组">
                                    <option value="">--运营组--</option>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="params[spStatus]" class="form-control" aria-invalid="false" required>
                                    <option value="">--应对账逾期状态--</option>
                                    <option value="1">应对账</option>
                                    <option value="2">逾期未对账</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasAnyPermissions="finance:receCheckSheet:export,finance:fleet:receCheckSheet:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    //是否是车队
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/custReceive" : ctx + "custReceive";

    //初始化 统计数量
    var numCountTotal = 0;
    var feeWeightCountTotal = '0吨';
    var volumeCountTotal = '0m³'
    var transFeeCountTotal = '￥0';
    var countInvoiceTotal = 0;


    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/listExport",
            showToggle: false,
            showColumns: true,
            modalName: "客户应收",
            rememberSelected: false,
            uniqueId: "customerId",
            showFooter:true,
            clickToSelect: true,
            onLoadSuccess: getAmountCount,//所有数据被加载时触发
            height: 580,
            columns: [
                {
                    title: '操作',
                    align: 'left',
                    field: 'customerId',
                    formatter: function (value,row,index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="detail(\'' + value + '\',\''+row.salesDept+'\')"><i class="fa fa-newspaper-o" style="font-size: 15px;" ></i></a>');
                        return actions.join('');
                    },
                    footerFormatter: function (row) {
                        return "总合计:&nbsp&nbsp"
                            + "件数:<nobr id='numCountTotal'>"+numCountTotal+"</nobr>&nbsp&nbsp"
                            + "重量:<nobr id='feeWeightCountTotal'>"+feeWeightCountTotal+"</nobr>&nbsp&nbsp"
                            + "体积:<nobr id='volumeCountTotal'>"+volumeCountTotal+"</nobr>&nbsp&nbsp"
                            + "总金额:<nobr id='transFeeCountTotal'>"+transFeeCountTotal+"</nobr>&nbsp&nbsp"
                            +"发货单数量：<nobr id='countInvoiceTotal'>"+countInvoiceTotal+"</nobr>&nbsp&nbsp";
                    }
                },
                {
                    title: '客户名称',
                    align: 'left',
                    field: 'custName'

                },

                {
                    title: '客户编码',
                    align: 'left',
                    field: 'custCode'
                },
                {
                    title: '件数|重量(吨)|体积(m³)',
                    field: 'numCount',
                    formatter: function (value, row, index) {
                       return row.numCount+"件"+'<span style="font-weight: 900">|</span>'+row.feeWeightCount+"吨"+'<span style="font-weight: 900">|</span>'+row.volumeCount+"m³"
                    }
                },
                {
                    title: '对账期',
                    align: 'left',
                    field: 'paymentDays'
                },
                {
                    title: '提货日期',
                    align: 'left',
                    field: 'reqDeliDate'
                },
                {
                    title: '运营组',
                    align: 'left',
                    field: 'salesDeptName'
                },
                {
                    title: '总金额(元)',
                    align: 'right',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '发货单数量',
                    align: 'left',
                    field: 'countInvoice',
                },
            ]
        };
        $.table.init(options);

        loadWarn()
    });


    /**
     *
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: prefix + "/getAmountCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;
                if (result.code == 0) {

                    /*
                     * 有两种情况：
                     *      1.先加载初始化0，再查询统计
                     *      2.先查询统计赋值，在加载0
                     *  解决方法：
                     *      查出统计的值，存入初始化变量中
                     */
                    if(data.numCount){
                        numCountTotal = data.numCount;
                    }
                    
                    feeWeightCountTotal = data.feeWeightCount.toLocaleString()+'吨'
                    volumeCountTotal = data.volumeCount.toLocaleString()+'m³'
                    transFeeCountTotal = data.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
                    countInvoiceTotal = data.countInvoice

                    if(data.numCount){
                        $("#numCountTotal").text(data.numCount);
                    }
                    
                    $("#feeWeightCountTotal").text(data.feeWeightCount.toLocaleString()+'吨');
                    $("#volumeCountTotal").text(data.volumeCount.toLocaleString()+'m³');
                    $("#transFeeCountTotal").text(data.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#countInvoiceTotal").text(data.countInvoice);
                }
            }
        });
    }


    /**
     * 跳转应收明细
     * @param customerId 客户Id
     */
    function detail(customerId, salesDept) {
        var url = isFleet ? ctx + "fleet/receive?customerId="+customerId : ctx + "receive?salesDept="+salesDept+"&customerId="+customerId;
        $.modal.openTab("客户应收明细", url);
    }

    function loadWarn() {
        $.ajax({
            url: prefix + '/loadWarn',
            dataType: 'json',
            success: function(aj) {
                if (aj.code == 0) {
                    var d = aj.data;
                    //"YSK_COUNT":377,"YQWSK_COUNT":310,"YSQ_COUNT":266,"YQWSQ_COUNT":192,"YSQ_AMOUNT":55855709.15,"YQWSK_AMOUNT":97145256.17,"YQWSQ_AMOUNT":33443278.34,"YSK_AMOUNT":200035605.61}}
                    var html = []
                    html.push("应对账：", d.YDZ_COUNT, '家(', d.YDZ_AMOUNT, "元)；");
                    html.push("逾期未对账：", d.YQWDZ_COUNT, '家(', d.YQWDZ_AMOUNT, "元)；");
                    $("#warn_msg").html(html.join(''))
                } else {
                    $("#warn_msg").html(aj.msg)
                }
            }
        })
    }
    function exportExcel(receCheckSheetId){
        $.modal.confirm("确定导出该条未对账明细单据吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            var url = prefix + "/importData";
            var data = {receCheckSheetId:receCheckSheetId};
            $.operate.post(url,data,function(result){
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
            // $.post($.table._option.exportUrl, receCheckSheetId, function(result) {
            //     if (result.code == web_status.SUCCESS) {
            //         window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
            //     } else if (result.code == web_status.WARNING) {
            //         $.modal.alertWarning(result.msg)
            //     } else {
            //         $.modal.alertError(result.msg);
            //     }
            //     $.modal.closeLoading();
            // });
        });

    }

</script>

</body>
</html>