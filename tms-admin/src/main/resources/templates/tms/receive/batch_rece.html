<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('分批收款')"/>
</head>

<body>
<div class="form-content">
    <form id="form-batchRece-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="vbillno" th:value="${receiveDetail.vbillno}">
        <input type="hidden" name="receiveDetailId" th:value="${receiveDetail.receiveDetailId}">
        <input name="corDate" type="hidden" th:value="${#dates.format(receiveDetail.corDate, 'yyyy-MM-dd HH:mm:ss')}"/>

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总金额：</label>
                                    <div class="col-sm-8">
                                    <input  name="totalAmount"   class="form-control"  disabled
                                           th:value="${receiveDetail.transFeeCount}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">可申请额度：</label>
                                    <div class="col-sm-8">
                                    <input  name="quota" id="quota" class="form-control"  disabled
                                           th:value="${receiveDetail.ungotAmount}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款类型：</label>
                                    <div class="col-sm-8">
                                        <input name="receivableType" class="form-control" value="1" type="hidden">
                                        <input class="form-control" value="直接收款" disabled >
                                       <!-- <select name="receivableMethod" class="form-control" th:with="type=${@dict.getType('receivable_type')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                        </select>-->
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">本次收款金额：</label>
                                    <div class="col-sm-8">
                                        <input name="receivableAmount" id="receivableAmount"
                                               min="0" required type="text"  oninput="$.numberUtil.onlyNumber(this)" th:value="${receiveDetail.ungotAmount}"
                                               class="form-control" maxlength="10">
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款方式：</label>
                                    <div class="col-sm-8">
                                        <select name="receivableMethod" class="form-control" th:with="type=${@dict.getType('receivable_method')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款日期：</label>
                                    <div class="col-sm-8">
                                        <input name="receivableDate" id="receivableDate" class="time-input form-control">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">转入账户：</label>
                                    <div class="col-sm-8">
                                        <select name="inAccount" class="form-control" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${account}" th:text="${dict.accountName}" th:value="${dict.accountId}" ></option>
                                        </select>
                                        <!-- <div class="input-group">
                                             <input onclick="selectAccount()" id="accountName" type="text" class="form-control" required>
                                             <input name="inAccount"  id="accountId" type="hidden" class="form-control">
                                             <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                         </div>-->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" maxlength="200" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<script>
    var prefix = ctx + "receive";
    //默认当日日期
    var time = new Date();
    var day = ("0" + time.getDate()).slice(-2);
    var month = ("0" + (time.getMonth() + 1)).slice(-2);
    var today = time.getFullYear() + "-" + (month) + "-" + (day);
    $("#receivableDate").val(today);
    //可申请额度
    var quota = parseFloat($("#quota").val());

    $(function () {
        $('#collapseOne').collapse('show');
        /** 校验 */
        $("#form-batchRece-add").validate({
            focusCleanup: true
        });

    });
    //提交
    function submitHandler() {
        //本次收款金额
        var receivableAmount = parseFloat($("#receivableAmount").val());
        if(receivableAmount>quota){
            $.modal.alertError("收款金额必须<="+quota);
            return ;
        }
        if ($.validate.form()) {
            $.operate.save(prefix + "/saveBatchRece", $('#form-batchRece-add').serialize());
        }
    }

    //选择账户
    function selectAccount(){
        var url = "/finance/account/selectAccount";
        $.modal.open("选择账户", url);
    }



</script>
</body>

</html>