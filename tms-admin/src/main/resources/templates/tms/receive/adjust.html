<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应收调整页面')"/>
</head>

<body>
<div class="form-content">
    <form id="form-receive-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <input name="invoiceId" type="hidden" id="invoiceId" th:value="${receiveDetail.invoiceId}">
                        <input name="receiveDetailId" type="hidden" id="receiveDetailId" th:value="${receiveDetail.receiveDetailId}">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        发货单号：</label>
                                    <div class="col-sm-7">
                                            <input name="invoiceVbillno" id="invoiceVbillno" class="form-control dis"
                                                   type="text" disabled
                                                   th:value="${receiveDetail.invoiceVbillno}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        应收单据号：</label>
                                    <div class="col-sm-7">
                                        <input name="vbillno" id="vbillno" class="form-control dis"
                                               type="text" disabled
                                               th:value="${receiveDetail.vbillno}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        结算客户：</label>
                                    <div class="col-sm-7">
                                        <input name="balaName" id="balaName" class="form-control dis" type="text"
                                               disabled th:value="${receiveDetail.balaName}">
                                        <input name="balatype" id="balatype" class="form-control" type="hidden"
                                               th:value="${receiveDetail.balatype}">
                                        <input name="balaCode" id="balaCode" class="form-control" type="hidden"
                                               th:value="${receiveDetail.balaCode}">
                                        <input name="balaCustomer" id="balaCustomer" class="form-control"
                                               type="hidden" th:value="${receiveDetail.balaCustomer}">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        结算公司：</label>
                                    <div class="col-sm-7">
                                        <input class="form-control " type="text"
                                               maxlength="30" disabled th:value="${receiveDetail.balaCorpName}">
                                        <input name="balaCorp" id="balaCorp" class="form-control dis" type="hidden"
                                               maxlength="30" th:value="${receiveDetail.balaCorp}">
                                    </div>
                                </div>
                            </div>


                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        客户名称：</label>
                                    <div class="col-sm-7">
                                        <input name="custName" id="custName" class="form-control dis" type="text"
                                               disabled th:value="${receiveDetail.custName}">
                                        <input name="custCode" id="custCode" class="form-control" type="hidden"
                                               th:value="${receiveDetail.custCode}">
                                        <input name="customerId" id="customerId" class="form-control" type="hidden"
                                               th:value="${receiveDetail.customerId}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">计费件数：</label>
                                    <div class="col-sm-7">
                                        <input name="numCount" id="numCount" class="form-control dis" disabled
                                               th:value="${receiveDetail.numCount}"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        计费重量：</label>
                                    <div class="col-sm-7">
                                        <input name="feeWeightCount" id="feeWeightCount" class="form-control dis" disabled
                                               th:value="${receiveDetail.feeWeightCount}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        计费体积：</label>
                                    <div class="col-sm-7">
                                        <input name="volumeCount" id="volumeCount" class="form-control dis"
                                               disabled th:value="${receiveDetail.volumeCount}">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">要求提货日期：</label>
                                    <div class="col-sm-7">
                                        <input class="form-control dis"
                                               name="reqDeliDate" id="reqDeliDate" disabled
                                               th:value="${#dates.format(receiveDetail.reqDeliDate, 'yyyy-MM-dd')}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">要求到货日期：</label>
                                    <div class="col-sm-7">
                                        <input class="form-control dis"
                                               name="reqArriDate" id="reqArriDate" disabled
                                               th:value="${#dates.format(receiveDetail.reqArriDate, 'yyyy-MM-dd')}" >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        调整金额：</label>
                                    <div class="col-sm-7">
                                        <input name="transFeeCount" id="transFeeCount" class="form-control"
                                               type="text"  oninput="$.numberUtil.onlyNumber(this)" min="0" required maxlength="10">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-3">调整原因：</label>
                                    <div class="col-sm-12">
                                            <textarea name="adjustMemo" id="adjustMemo" maxlength="250" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFour">地址信息</a>

                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseFour">

                    <div class="panel-body">
                        <!-- begin-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabThree" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 10%;">提货地址名称</th>
                                    <th style="width: 10%;">提货省</th>
                                    <th style="width: 10%;">提货市</th>
                                    <th style="width: 10%;">提货区</th>
                                    <th style="width: 10%;">收货地址名称</th>
                                    <th style="width: 10%;">收货省</th>
                                    <th style="width: 10%;">收货市</th>
                                    <th style="width: 10%;">收货区</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td id="deliAddrName" th:text="${invoice.deliAddrName}"></td>
                                    <td id="deliProName"  th:text="${invoice.deliProName}"></td>
                                    <td id="deliCityName" th:text="${invoice.deliCityName}"></td>
                                    <td id="deliAreaName" th:text="${invoice.deliAreaName}"></td>
                                    <td id="arriAddrName" th:text="${invoice.arriAddrName}"></td>
                                    <td id="arriProName"  th:text="${invoice.arriProName}"></td>
                                    <td id="arriCityName" th:text="${invoice.arriCityName}"></td>
                                    <td id="arriAreaName" th:text="${invoice.arriAreaName}"></td>

                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--end-->
                    </div>

                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">调整记录</a>

                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseTwo">

                    <div class="panel-body">
                        <!-- begin-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabThree1" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 10%;">应收单号</th>
                                    <th style="width: 10%;">单据状态</th>
                                    <th style="width: 10%;">金额（元）</th>
                                    <th style="width: 10%;">申请开票金额（元）</th>
                                    <th style="width: 10%;">已开票金额（元）</th>
                                    <th style="width: 10%;">调整原因</th>
                                    <th style="width: 10%;">创建时间</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="adjust:${adjustList}">
                                    <td th:text="${adjust.vbillno}"></td>
                                    <td th:switch="${adjust.vbillstatus}">
                                        <span th:case="0">新建</span>
                                        <span th:case="1">已确认</span>
                                        <span th:case="2">已对账</span>
                                        <span th:case="3">部分核销</span>
                                        <span th:case="4">已核销</span>
                                        <span th:case="5">关闭</span>
                                        <span th:case="*">-</span>
                                    </td>
                                    <td th:text="${adjust.transFeeCount}" style="text-align:right" ></td>
                                    <td th:text="${adjust.applCheckAmount}" style="text-align:right"></td>
                                    <td th:text="${adjust.checkAmount}" style="text-align:right"></td>
                                    <td th:text="${adjust.adjustMemo}"></td>
                                    <td th:text="${#dates.format(adjust.regDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--end-->
                    </div>

                </div>
            </div>

        </div>

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="commit()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "receive";
    $(function () {

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');

        var options = {};
        $.table.init(options);

    });




    /**
     * 表单提交
     */
    function commit() {
        if ($.validate.form()) {
            $('.dis').removeAttr("disabled");

            // 表单提交
            $.operate.saveTab(prefix + "/insertAdjust", $('#form-receive-add').serialize());
        }
    }




</script>
</body>

</html>