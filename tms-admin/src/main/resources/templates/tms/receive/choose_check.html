<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('对账单选择页')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal">
					<!--对账单加入对账单 新建状态-->
					<input th:value="0" name="params[vbillstatus]" type="hidden">
					<div class="row">

						<div class="col-md-4 col-sm-4">
							<div class="form-group">
								<!-- <label class="col-sm-4">年份：</label> -->
								<div class="col-sm-12">

									<input name="year" id="year" placeholder="请输入年份" class="form-control valid" type="text"
										   readonly aria-required="true">

								</div>
							</div>
						</div>
						<div class="col-md-4 col-sm-4">
							<div class="form-group">
								<!-- <label class="col-sm-4">月份：</label> -->
								<div class="col-sm-12">

									<input name="month" id="month" placeholder="请输入月份" class="form-control valid" type="text"
										   readonly aria-required="true">

								</div>
							</div>
						</div>

						<div class="col-md-4 col-sm-4">
							<div class="form-group">
								<!-- <label class="col-sm-4">月份：</label> -->
								<div class="col-sm-12">
									<div class="form-group">
										<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
										<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- <div class="row">

						<div class="col-md-3 col-sm-6">
							<div class="form-group">

							</div>
						</div>
						<div class="col-md-3 col-sm-6">
							<div class="form-group">

							</div>
						</div>
						<div class="col-md-3 col-sm-6">
							<div class="form-group">

							</div>
						</div>
						<div class="col-md-3 col-sm-6">
							<label class="col-sm-7"></label>
							<div class="form-group">
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>

					</div> -->
				</form>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table id="bootstrap-table" data-mobile-responsive="true"></table>
			</div>
		</div>

	</div>
    <div th:include="include :: footer"></div>
	 <script th:inline="javascript">
		 //是否是车队
		 var isFleet = [[${isFleet}]];
		 var prefix = isFleet == 1 ? ctx + "fleet/receCheckSheet" : ctx + "receCheckSheet";

		 var billingType = [[${@dict.getType('billing_type')}]];
		 var customerId = [[${customerId}]];
		 var invoiceIds = [[${invoiceIds}]];
		 var receiveDetsailIds = [[${receiveDetsailIds}]];
		 var salesDept = [[${salesDept}]];

		 $(function () {
			 var options = {
				 url: prefix + "/list?customerId="+customerId,
				 showToggle:false,
				 showColumns:false,
				 showRefresh:false,
				 showSearch:false,
				 modalName: "客户对账",
				 clickToSelect:true,
				 columns: [{
					 radio: true
				 },
					 {
						 title: '应收对账单Id',
						 field: 'receCheckSheetId',
						 align: 'left',
						 visible: false
					 },
					 {
						 title: '应收对账单号',
						 field: 'vbillno',
						 align: 'left'
					 },
					 {
						 title: '对账名称',
						 field: 'receCheckSheetName',
						 align: 'left'
					 },
					 {
						 title: '对账状态',
						 field: 'vbillstatus',
						 align: 'left',
						 formatter: function status(value,row,index) {
							 switch(value) {
								 case 0:
									 return '<span>新建</label>';
								 case 1:
									 return '<span>已确认</label>';
								 case 2:
									 return '<span>部分核销</label>';
								 case 3:
									 return '<span>已核销</label>';
								 default:
									 break;
							 }
						 }
					 },
					 {
						 title: '对账年月',
						 field: 'totalAmount',
						 align: 'right',
						 formatter: function (value, row, index) {
							 return row.year +'年'+row.month +'月'
						 }

					 },
					//  {
					// 	 title: '对账年份',
					// 	 field: 'year',
					// 	 align: 'left'
					//  },
					//  {
					// 	 title: '对账月份',
					// 	 field: 'month',
					// 	 align: 'left'
					//  },
					//  {
					// 	 title: '客户名称',
					// 	 field: 'custName',
					// 	 align: 'left'
					//  },
					 {
						 title: '结算客户',
						 field: 'balaName',
						 align: 'left',
						 visible: false
					 },
					 {
						 title: '总金额',
						 field: 'totalAmount',
						 align: 'right',
						 formatter: function (value, row, index) {
							 if (value === null) {
								 return ;
							 }
							 return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
						 }

					 },
					//  {
					// 	 title: '已收金额',
					// 	 field: 'gotAmount',
					// 	 align: 'right',
					// 	 formatter: function (value, row, index) {
					// 		 if (value === null) {
					// 			 return ;
					// 		 }
					// 		 return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					// 	 }
					//  },
					//  {
					// 	 title: '未收金额',
					// 	 field: 'ungotAmount',
					// 	 align: 'right',
					// 	 formatter: function (value, row, index) {
					// 		 if (value === null) {
					// 			 return ;
					// 		 }
					// 		 return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
					// 	 }
					//  },

					 {
						 title: '发票抬头',
						 field: 'checkHead',
						 align: 'left',
						 visible: false
					 },
					 {
						 title: '发票类型',
						 field: 'checkType',
						 align: 'left',
						 visible: false,
						 formatter: function status(value,row,index) {

							 return $.table.selectDictLabel(billingType, value);
						 }
					 }
				 ]
			 };

			 $.table.init(options);
			 $(document).keyup(function(e){
				 var key = e.which;
				 if(key==13){
					 //查询方法
					 $.table.search();
				 }
			 });
			 layui.use('laydate', function(){
				 var laydate = layui.laydate;
				 laydate.render({
					 elem: '#month',
					 type: 'month',
					 format:"MM"
				 });
				 laydate.render({
					 elem: '#year',
					 type: 'year'
				 });
			 })
		 });

		 /**
		  * 选择一条应收对账单据后的回调方法
		  */
		 function submitHandler() {
			 var rows = $.table.selectFirstColumns();

			 if (rows.length === 0) {
				 $.modal.alertWarning("请至少选择一条记录");
				 return;
			 }
			 var vbillstatus = $.table.selectColumns("vbillstatus");
			 if (vbillstatus != 0) {
				 $.modal.alertWarning("请选择新建状态的对账单");
				 return;
			 }

			 /*if (salesDept  != $.table.selectColumns("salesDept")[0]) {
				 $.modal.alertWarning("与该对账单运营组不一致，不能加入该对账单！");
				 return
			 }*/

			 var url = ctx + "receive/saveChecking?receCheckSheetId=" + rows.join()
					 + "&invoiceIds=" + invoiceIds + "&receiveDetsailIds=" + receiveDetsailIds;
             $.operate.post(url,null,function(result){
                 if (result.code == web_status.SUCCESS) {
                     $.modal.close();
                     parent.$.table.refresh();
                 }
			 });

		 }
	 </script>

</body>
</html>