<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('运费拆分')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .title{
        font-size: 16px;
    }
    .flex{
        display: flex;
    }
</style>
<body>
<div class="form-content">
    <form id="form-receive-add" class="form-horizontal" novalidate="novalidate">
        <input name="receiveDetailId" type="hidden" id="receiveDetailId" th:value="${receiveDetail.receiveDetailId}">
        <div class="title flex">
            <div style="width: 5em;">说明：</div>
            <div>
                <div>输入金额应大于0，并且小于总金额（<span class="text-danger">[[${receiveDetail.transFeeCount}]]</span>）。</div>
                <div class="mt5">
                    拆分完成后将生成两条相同类型的应收，对应的金额分别为（输入金额）与（总金额-输入金额）。
                </div>
            </div>
           
        </div>
        <div class="title flex" style="margin-top: 20px;">
            <div style="width: 3em;">金额：</div>
            <div>
                <input type="text" class="form-control" name="transFeeCount" id="transFeeCount" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" required>
            </div>
           
        </div>
        <!-- <div class="row">
            <div class="col-md-12 col-sm-12" style="margin-top: 20px;">
                <div class="form-group">

                    <label class="col-xs-2 title"> 金额：</label>
                    <div class="col-xs-5">
                        <input type="text" class="form-control" name="transFeeCount" id="transFeeCount" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" required>
                    </div>
                </div>
            </div>
        </div> -->
    </form>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "receive";
    $(function(){

    })
    //提交
    function submitHandler() {
        let transFeeCountT=[[${receiveDetail.transFeeCount}]]
        if ($.validate.form()) {
            let transFeeCount=$("#transFeeCount").val();
            if(transFeeCount<=0){
                $.modal.alertWarning("填写的金额不允许小于0");
                return;
            } 
            if(transFeeCount > transFeeCountT ){
                $.modal.alertWarning("填写的金额不允许超过运费");
                return;
            }

            $.operate.save(prefix + "/split_receive_detail", $('#form-receive-add').serialize());
        }
       
    }

</script>
</body>
</html>