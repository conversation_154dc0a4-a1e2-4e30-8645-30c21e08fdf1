<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付对账')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .col-sm-6,.col-sm-4,.col-sm-8,.col-sm-3,.col-sm-12{
        padding-left: 6px;
        padding-right: 5px;
    }
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 106px);
        padding-top: 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--<input type="hidden" name="isFleetData" id="isFleetData" th:value="${isFleetData}">-->
                <input name="carrBalaType" type="hidden" th:value="${balaType}">
                <div class="row">
                    <div class="col-sm-5">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">运单状态：</label>-->
                                <div class="col-sm-12">
                                    <select name="lotVbillstatus" id="lotVbillstatus" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" data-none-selected-text="运单状态" multiple>
                                        <option th:each="dict : ${entrustLotStatus}" th:text="${dict.context}"
                                                th:value="${dict.value}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="payableWriteOffStatusList" id="payableWriteOffStatusList"
                                            class="form-control valid noselect2 selectpicker"
                                            data-none-selected-text="应付是否核销" multiple>
                                        <option  value="0">未核销</option>
                                        <option  value="1">部分核销</option>
                                        <option  value="2">已核销</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">承运商名称：</label>-->
                                <div class="col-sm-12">
                                    <input name="custAbbr" id="custAbbr" placeholder="请输入客户简称" class="form-control valid" >
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="col-sm-3">
                        <div class="">
                            <!--                            <label class="col-sm-2">要求提货日期：</label>-->
                            <div class="">
                                <input type="text" style="width: 48%; float: left;" placeholder="要求提货开始日期" class="time-input form-control"
                                       id="reqDeliDateStart"  name="params[reqDeliDateStart]">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:4%;">-</span>
                                <input type="text" style="width: 48%; float: left;" placeholder="要求提货结束日期" class="time-input form-control"
                                       id="reqDeliDateEnd"  name="params[reqDeliDateEnd]">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">承运商名称：</label>-->
                                <div class="col-sm-12">
                                    <input name="carrierName" id="carrierName" placeholder="请输入承运商名称" class="form-control valid" >
                                    <input name="carrCode" id="carrCode" type="hidden" th:value="${carrCode}">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">调度组：</label>-->
                                <div class="col-sm-12">
                                    <select name="transLineId" id="transLineId" class="form-control valid selectpicker" aria-invalid="false"
                                            th:with="type=${dispatcherDeptList}"  data-none-selected-text="调度组" aria-required="true" >
                                        <option></option>
                                        <option th:each="dict : ${type}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>
                                    </select>
                                    <input id="transLineName" name="transLineName" type="hidden">
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">承运商类别 ：</label>-->
                                <div class="col-sm-12">
                                    <select name="carrType" class="form-control" required
                                            th:with="type=${carrierType}">
                                        <option value="">--承运商类别--</option>
                                        <option th:each="dict : ${type}" th:text="${dict.context}"
                                                th:value="${dict.value}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row no-gutter">
                    <!--<div class="col-sm-2" style="padding-right: 0;padding-left: 12px">
                      <select  name="carrBalaType" class="form-control valid" aria-invalid="false">
                           <option value="">&#45;&#45;结算方式&#45;&#45;</option>
                           <option th:value="1">单笔付款</option>
                           <option th:value="2">月度付款</option>
                       </select>
                    </div>-->
                    <div class="col-sm-1">
                        <select  name="isAgencyCollect" id=""  class="form-control valid" aria-invalid="false">
                            <option value="">--允许代收--</option>
                            <option  value="1">是</option>
                            <option  value="2">否</option>
                        </select>
                    </div>
                    <div class="col-sm-1">
                        <input name="carNo" class="form-control valid" placeholder="车牌号">
                    </div>
                    <div class="col-sm-10" style="padding: 0">
                        <div class="col-sm-6">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <!--                                <label class="col-sm-6">线路查询：</label>-->
                                    <div class="col-sm-4">
                                        <select  name="deliProvince" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                        </select>
                                    </div>
                                    <div class="col-sm-4">
                                        <select name="deliCity" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                                    </div>
                                    <div class="col-sm-4">
                                        <select name="deliArea" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="col-sm-6">
                            <div class="col-sm-1" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 30px;height: 30px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-4">
                                <select  name="arriProvince" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                            <div class="col-sm-4">
                                <select name="arriCity" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-3">
                                <select name="arriArea" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">

                        <div class="col-sm-2">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="ifAllConfirm" class="form-control">
                                        <option value="">--影像回单确认--</option>
                                        <option value="0">未确认</option>
                                        <option value="1">部分确认</option>
                                        <option value="2">已确认</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="transType" class="form-control">
                                        <option value="">--运输方式--</option>
                                        <option th:each="dict : ${@dict.getType('trans_code')}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="isOversize" class="form-control">
                                        <option value="">--大件/三超--</option>
                                        <option value="0">否</option>
                                        <option value="1">是</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="ifAllReceiptUpload" class="form-control">
                                        <option value="">--回单照片状态--</option>
                                        <option value="0">未上传</option>
                                        <option value="1">部分上传</option>
                                        <option value="2">已上传</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="carLen" id="carLen" class="form-control" th:with="type=${@dict.getType('car_len')}">
                                        <option value="">--请选择车长--</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">承运商名称：</label>-->
                                <div class="col-sm-12">
                                    <input name="goodsName" id="goodsName" placeholder="请输入货品名称" class="form-control valid" >
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">运单号：</label>-->
                            <div class="col-sm-12">
                                <input name="lot" id="lot" class="form-control"
                                       placeholder="请输入运单号，多个单号用逗号(,)分隔（多个单号时只支持精确查找）"
                                       onblur="this.value=this.value.replace(/，/g, ',').replace(/ /g, '').replace(/	/g, '')"
                                       th:value="${lot}" >
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-2" style="text-align: center">

                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>

                    </div>
                </div>


            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary  " onclick="contract()" shiro:hasAnyPermissions="carrier:contract:view,carrier:contract:view-month,fleet:carrier:contract:view">
                <i class="fa fa-newspaper-o"></i> 查看合同
            </a>
            <a class="btn btn-primary  " onclick="downloadForWord()" shiro:hasAnyPermissions="carrier:contract:view,carrier:contract:view-month,fleet:carrier:contract:view">
                <i class="fa fa-download"></i> 下载合同
            </a>
            <a class="btn btn-primary  " onclick="detailTab()" shiro:hasAnyPermissions="trace:payReconciliation:detailTab,trace:payReconciliation:detailTab-month,fleet:trace:payReconciliation:detailTab">
                <i class="fa fa-newspaper-o"></i> 费用明细
            </a>
            <a class="btn btn-warning" onclick="exportExcel()" shiro:hasAnyPermissions="trace:payReconciliation:export,trace:payReconciliation:export-month,fleet:trace:payReconciliation:export">
                <i class="fa fa-download"></i> 导出
            </a>
           <!-- <a class="btn btn-info"  onclick="$.table.importExcel()" shiro:hasAnyPermissions="trace:payReconciliation:adjustImport,trace:payReconciliation:adjustImport-month,fleet:trace:payReconciliation:adjustImport">
                <i class="fa fa-upload"></i> 调整单导入
            </a>
            <a class="btn btn-warning"  onclick="adjustExport()" shiro:hasAnyPermissions="trace:payReconciliation:adjustExport,trace:payReconciliation:adjustExport-month,fleet:trace:payReconciliation:adjustExport">
                <i class="fa fa-download"></i> 调整单导出
            </a>-->
            <!--<a class="btn btn-primary"  onclick="adjustRecord()" shiro:hasAnyPermissions="trace:payReconciliation:adjustRecord,trace:payReconciliation:adjustRecord-month,fleet:trace:payReconciliation:adjustRecord">
                <i class="fa fa-newspaper-o"></i> 调整单记录
            </a>-->
          <!--  <a class="btn btn-primary single disabled"  onclick="invoiceRecord()" shiro:hasAnyPermissions="trace:payReconciliation:invoiceRecord,trace:payReconciliation:invoiceRecord-month,fleet:trace:payReconciliation:invoiceRecord">
                <i class="fa fa-newspaper-o"></i> 发货单记录
            </a>-->
            <!--<a class="btn btn-info"  onclick="collectionChange()" shiro:hasAnyPermissions="trace:payReconciliation:collectionChange,trace:payReconciliation:collectionChange-month,fleet:trace:payReconciliation:collectionChange">
                <i class="fa fa-user-plus"></i> 允许代收
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a href="file/PayDetailAdjustModel.xlsx" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    //运单状态
    var entrustLotStatus = [[${entrustLotStatus}]];
    var transCode = [[${@dict.getType('trans_code')}]];//运输方式
    var prefix = ctx + "trace/payReconciliation";
    //承运商类别
    var carrierType = [[${carrierType}]];
    //合计
    var receiptAmountFreightTotal = 0;//运费总应收
    var receiptAmountOnWayTotal = 0;//在途总应收
    var oilCardAmountTotal = 0;//油卡
    var sumTransFeeCountTotal = 0;//总金额
    var sumUngotAmountTotal = 0;//未付金额
    var sumGotAmountTotal = 0;//已付金额
    var taxAmountCount = 0;

    //初始化查询条件传参
    queryParams = function(params) {

        var arr = [];
        arr.push(0);
        arr.push(1);
        $('#payableWriteOffStatusList').selectpicker('val',arr);

        var search = {};
        $.each($("#role-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    $(function () {
        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/adjustImport",
        /*    queryParams: queryParams,*/
            showToggle: false,
            showColumns: true,
            modalName: "应付对账",
            fixedColumns: true,
            height: 580,
            rememberSelected: false,
            fixedNumber: 4,
            clickToSelect:true,
            showFooter:true,
            uniqueId:"entrustLotId",
            onPostBody:function () {
                //合并页脚
                merge_footer();
                getAmountCount();
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "合计:&nbsp&nbsp"
                        + "运费:<nobr id='receiptAmountFreightTotal'>￥0</nobr>&nbsp&nbsp"
                        + "在途:<nobr id='receiptAmountOnWayTotal'>￥0</nobr>&nbsp&nbsp"
                        + "油卡:<nobr id='oilCardAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        + "总金额:<nobr id='sumTransFeeCountTotal'>￥0</nobr>&nbsp&nbsp"
                        + "调整额：<nobr id='taxAmountTotal'>￥0</nobr>"
                        + "已付金额:<nobr id='sumGotAmountTotal'>￥0</nobr>&nbsp&nbsp"
                        + "未付金额:<nobr id='sumUngotAmountTotal'>￥0</nobr>&nbsp&nbsp<br>"
                    +"总合计:&nbsp&nbsp"
                    + "运费:<nobr id='receiptAmountFreightTotals'>￥0</nobr>&nbsp&nbsp"
                    + "在途:<nobr id='receiptAmountOnWayTotals'>￥0</nobr>&nbsp&nbsp"
                    + "油卡:<nobr id='oilCardAmountTotals'>￥0</nobr>&nbsp&nbsp"
                    + "总金额:<nobr id='sumTransFeeCountTotals'>￥0</nobr>&nbsp&nbsp"
                    + "调整额：<nobr id='sumTaxAmountTotal'>￥0</nobr>"
                    + "已付金额:<nobr id='sumGotAmountTotals'>￥0</nobr>&nbsp&nbsp"
                    + "未付金额:<nobr id='sumUngotAmountTotals'>￥0</nobr>&nbsp&nbsp<br>"
                }
            }, {
                title: '操作',
                align: 'left',
                formatter: function (value, row, index) {
                    var actions = [];
                    
                    //if ([[${@permission.hasAnyPermi('tms:trace:detail,tms:fleet:trace:detail')}]] != "hidden") { 
                        actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="明细"  onclick="detail(\'' + row.entrustLotId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                    //}
                    return actions.join('');
                }
            },


                // {
                //     title: '运单号/运单状态',
                //     field: 'lot',
                //     align: 'left',
                //     formatter: function (value, row, index) {
                //         let vbillstatus = ''
                //         switch (row.vbillstatus) {
                //             case 0:
                //                 vbillstatus = '<span class="label label-default">待运'+'</span>'
                //             case 1:
                //                 vbillstatus = '<span class="label label-info">部分提货'+'</span>';
                //             case 2:
                //                 vbillstatus = '<span class="label label-success">已提货'+'</span>';
                //             case 3:
                //                 vbillstatus = '<span class="label label-info">部分到货'+'</label>';
                //             case 4:
                //                 vbillstatus = '<span class="label label-success">已到货'+'</span>';
                //             case 6:
                //                 vbillstatus = '<span class="label label-success">分配车队'+'</span>';
                //             default:
                //                 break;
                //         }
                //         return value + '<br />' + vbillstatus
                //     }
                // },
                {
                    title: '运单号/运单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value,row) {

                        switch (value) {
                            case 0:
                                return row.lot + '<br />'+'<span class="label label-default">待运'+'</span>'
                            case 1:
                                return row.lot + '<br />'+'<span class="label label-info">部分提货'+'</span>';
                            case 2:
                                return row.lot + '<br />'+'<span class="label label-success">已提货'+'</span>';
                            case 3:
                                return row.lot + '<br />'+'<span class="label label-info">部分到货'+'</label>';
                            case 4:
                                return row.lot + '<br />'+'<span class="label label-success">已到货'+'</span>';
                            case 6:
                                return row.lot + '<br />'+'<span class="label label-success">分配车队'+'</span>';
                            case 7:
                                return row.lot + '<br />'+'<span class="label label-default">关闭'+'</span>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '承运商',
                    align: 'left',
                    field: 'carrierName',
                    formatter:function status(value, row) {
                        return $.table.tooltip(value)+"<br/>"+row.carrierPhone;
                    }
                },
                {
                    title: '税点类别',
                    align:'left',
                    field:'billingTypeLabel',
                    formatter:function status(value, row) {
                        if(row.ifHasBill==1){
                            return $.table.tooltip(value)+"<br/>"+$.table.tooltip(row.balaType)+"(有票)";
                        }else{
                            return $.table.tooltip(value)+"<br/>"+$.table.tooltip(row.balaType)+"(无票)";
                        }
                    }
                },
                {
                    title: '客户简称',
                    align: 'left',
                    field: 'custAbbr',
                    formatter:function status(value, row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '要求提/到货日期',
                    align: 'left',
                    field: 'reqDeliDate',
                    formatter: function status(value, row, index) {
                        return value.substring(0,10)+ '<br/>' + row.reqArriDate.substring(0,10);
                    },
                },
                {
                    title: '提货|到货省市区',
                    align: 'left',
                    field: 'deliDetailAddress',
                    switchable: false,
                    formatter: function status(value, row, index) {
                        return row.multipleShippingAddressList.sort(sortNum).map(item=>{
                            if(item.addressType == 0){
                                return '<span class="label label-warning pa2">装</span>'+$.table.tooltip(item.provinceName+item.cityName+item.areaName+item.detailAddr);
                            }
                            if(item.addressType == 1){
                                return '<span class="label label-success pa2">卸</span>'+$.table.tooltip(item.provinceName+item.cityName+item.areaName+item.detailAddr);
                            }
                        }).join("<br/>");
                    },
                },
                {
                    title:'货品/货量',
                    align:'left',
                    field:'numCount',
                    formatter: function status(value, row, index) {
                        let html=[]
                        if(value !=0 && value != null){
                            html.push(value+"件")
                        }
                        if(row.weightCount !=0 && row.weightCount != null){
                            html.push(row.weightCount+"吨")
                        }
                        if(row.volumeCount !=0 && row.volumeCount != null){
                            html.push(row.volumeCount+"m³")
                        }
                        return row.goodsName+"<br/>"+html.join('/');
                    },
                },
                {
                    title: '车辆信息',
                    align: 'left',
                    field: 'carLenName',
                    formatter: function status(row,value) {
                        if(value.carNo == null || value.carNo == ''){
                            value.carNo = '';
                        }
                        if(value.carLenName == null || value.carLenName == ''){
                            value.carLenName = '';
                        }
                        if(value.carTypeName == null || value.carTypeName == ''){
                            value.carTypeName = '';
                        }
                       
                        return value.carNo+"<br/>"+value.carLenName+value.carTypeName;
                    }
                },
                {
                    title: '费用类别(元)',
                    align: 'left',
                    field: 'receiptAmountFreight',
                    formatter: function (value, row, index) {
                        let html=[];
                        var now = new Date();
                        let reqDeliDate=new Date(Date.parse(row.reqDeliDate))
                            reqDeliDate=reqDeliDate.setDate(reqDeliDate.getDate()+5)
                            reqDeliDate=new Date(reqDeliDate)

                        if (value != null&&value != 0) {
                            if (reqDeliDate > now ) {
                                html.push(`<span class="label label-coral pa2">运费</span>`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                            } else{
                                if ([[${@permission.hasPermi('trace:payReconciliation:adjustSingle')}]] != "hidden" || [[${@permission.hasPermi('trace:payReconciliation:adjustSingleMonth')}]] != "hidden") {
                                    html.push(`<span class="label label-coral pa2">运费</span>`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`<i class="fa fa-edit text-success ml5" onclick="adjust(\`` + row.entrustLotId + `\`)"></i>`);
                                }else{
                                    html.push(`<span class="label label-coral pa2">运费</span>`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                                }
                            }
                        }else{
                            if (reqDeliDate > now ) {
                                html.push(`<span class="label label-coral pa2">运费</span>0`);
                            } else{
                                if ([[${@permission.hasPermi('trace:payReconciliation:adjustSingle')}]] != "hidden" || [[${@permission.hasPermi('trace:payReconciliation:adjustSingleMonth')}]] != "hidden") {
                                    html.push(`<span class="label label-coral pa2">运费</span>0<i class="fa fa-edit text-success ml5" onclick="adjust(\`` + row.entrustLotId + `\`)"></i>`);
                                }else{
                                    html.push(`<span class="label label-coral pa2">运费</span>0`);
                                }

                            }
                        }
                        if (row.receiptAmountOnWay != null&&row.receiptAmountOnWay !=0 ) {
                            html.push(`<span class="label badge-info pa2">在途</span>`+row.receiptAmountOnWay.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        }
                        // if (row.sumTaxAmount != null&&row.sumTaxAmount!= 0) {
                        //     html.push(`<span class="label label-primary pa2">调整</span>`+row.sumTaxAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        // }
                        return html.join("<br/>")
                    }

                },
                {
                    title: '总金额/税金(元)',
                    align: 'left',
                    field: 'sumTaxAmount',
                    formatter: function (value, row, index) {
                        let html=[]
                        var oilRatio = row.oilRatio;
                        if (value != null&&value != 0) {
                            var ret = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            if( null != oilRatio && 0 != oilRatio){
                                html.push(`<span class="label label-coral pa2">油卡</span>`+oilRatio + "%" )
                            }
                        }
                        let freightFeeRateTooltip="";
                        if(row.freightFeeRate){
                            freightFeeRateTooltip=`<i class="fa fa-question-circle ml5" data-toggle="tooltip" style="font-size: 15px;" data-html="true" title="运费加票点：`+row.freightFeeRate+`%"></i>`
                        }
                        let sumTaxAmount="";
                        if(row.sumTaxAmount){
                            sumTaxAmount=`<i class="fa fa-exclamation-circle ml5" data-toggle="tooltip" style="font-size: 15px;" data-html="true" title="调整额：`+row.sumTaxAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`"></i>`
                        }
                        if (row.sumTransFeeCount != null&&row.sumTransFeeCount != 0) {
                            let num=Number(row.sumTransFeeCount)+Number(row.sumTaxAmount);
                            html.push(`<span class="label label-primary pa2">总额</span>`+num.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+sumTaxAmount+freightFeeRateTooltip)
                        }
                        if (row.payDetailNet != null) {
                            html.push(`<span class="label label-success pa2">税金</span>`+row.payDetailNet.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        }

                        return html.join("<br/>")
                    }

                },
                {
                    title: '已付/未付金额(元)',
                    align: 'left',
                    field: 'sumGotAmount',
                    formatter: function (value, row, index) {
                        let html=[]
                        if (value != null&&value != 0) {
                            html.push(`<span class="label label-primary pa2">已付</span>`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        }
                        if (row.sumUngotAmount  != null&&row.sumUngotAmount != 0) {
                            html.push('<span class="label label-danger pa2">未付</span>'+ row.sumUngotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        }
                        return html.join("<br/>");
                    }
                },
                

                {
                    title: '司机',
                    align: 'left',
                    field: 'driverName'
                },
                {
                    title: '收款信息',
                    align: 'left',
                    field: 'bankCard',
                    formatter:function status(value, row) {
                        let html=''
                        html+=$.table.tooltip(row.bankAccount);
                        if(row.bankAccount!=row.driverName&&row.bankAccount!=null&&row.driverName!=null){
                            html+= '<span class="label label-info" style="margin-left: 5px;">代收</span>'
                        }
                        html+= '<br/>'+ $.table.tooltip(value)
                        return html;
                    }
                },
                {
                    title: '回单信息',
                    align: 'left',
                    field: 'ifAllConfirm',
                    formatter: function status(value,row) {
                        let ifAllConfirm = ''
                        if(value == 0){
                            ifAllConfirm = '未确认'
                        }else if(value == 1){
                            ifAllConfirm = '部分确认'
                        }else if(value == 2){
                            ifAllConfirm = '已确认'
                        }else{
                             ifAllConfirm ='';
                        }

                        let html=""
                        if(row.ifAllReceipt == 0){
                            html= '未正本'
                        }else if(row.ifAllReceipt == 1){
                            html= '部分回单'
                        }else if(row.ifAllReceipt == 2){
                            html= '已回单'
                        }else{
                            html= '';
                        }
                        if(row.receiptTime){
                            html+='<br/>'+row.receiptTime

                        }
                        return ifAllConfirm + '<br />' + html

                    }
                }
                ,
                // {
                //     title: '正本回单',
                //     align: 'left',
                //     field: 'ifAllReceipt',
                //     formatter: function status(value,row) {
                //         let html=""
                //         if(value == 0){
                //             html= '未回单'
                //         }else if(value == 1){
                //             html= '部分回单'
                //         }else if(value == 2){
                //             html= '已回单'
                //         }else{
                //             html= '';
                //         }
                //         if(row.receiptTime){
                //             html+='<br/>'+row.receiptTime
                //         }
                //
                //         return html;
                //     }
                // },
                // {
                //     title: '核销状态',
                //     field: 'payableWriteOffStatus',
                //     align: 'left',
                //     formatter: function status(value, row) {
                //         let status ='';
                //         switch (row.payableWriteOffStatus){
                //             case '0':
                //                 status = '未核销';
                //                 break;
                //             case '1':
                //                 status = '部分核销';
                //                 break;
                //             case '2':
                //                 status = '已核销';
                //                 break;
                //         }
                //         return status;
                //     }
                // },
                {
                    title: '核销状态/账龄',
                    align: 'left',
                    field: 'paymentDays' ,
                    formatter: function status(value, row) {
                        let status ='';
                        switch (row.payableWriteOffStatus){
                            case '0':
                                status = '未核销';
                                break;
                            case '1':
                                status = '部分核销';
                                break;
                            case '2':
                                status = '已核销';
                                break;
                        }

                        if(row.payableWriteOffStatus == '0' || row.payableWriteOffStatus == '1'){
                            return status + '<br />' + value;
                        }else{
                            return status + '<br />' + '-';
                        }
                    }
                },
                
                {
                    title:'调度信息',
                    align:'left',
                    field:'regUserName',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value)+'<br/>'+$.table.tooltip(row.lotRegDate)
                    }
                },
                {
                    title:'调度组',
                    align:'left',
                    field:'transDeptName'
                },
                {
                    title:'运输方式',
                    align:'left',
                    field:'transType',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(transCode, value);
                    }
                },
                {
                    title: '允许代收',
                    field: 'isAgencyCollect',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if (row.isAgencyCollect === 1) {
                            return '是'
                        }else {
                            return '否'
                        }
                    }
                },
                {
                    field: 'sysUploadFile',
                    title: '代收附件',
                    formatter: function(value, row, index) {
                        var html = ""
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html +=  $.table.imageView(element.filePath)
                            });
                        }
                        return html;
                    }
                },
                {
                    title: '承运商类别',
                    align: 'left',
                    field: 'carrType',
                    formatter: function status(row,value) {
                        var context = '';
                        carrierType.forEach(function (v) {
                            if (v.value == value.carrType) {
                                context = v.context;
                                return false;
                            }
                        });
                        return context;
                    }
                },
               
            ]
        };
        $.table.init(options);

        var arr = [];
        arr.push(0);
        arr.push(1);
        $('#payableWriteOffStatusList').selectpicker('val',arr);
        searchPre();


        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });


        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
    });



    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.params = new Map();

        data.lotVbillstatus = $.common.join($('#lotVbillstatus').selectpicker('val'));
        data.params.payableWriteOffStatusList = $.common.join($('#payableWriteOffStatusList').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.lotVbillstatus = $.common.join($('#lotVbillstatus').selectpicker('val'));
        data.payableWriteOffStatusList = $.common.join($('#payableWriteOffStatusList').selectpicker('val'));
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $("#receiptAmountFreightTotals").text(data.RECEIPTAMOUNTFREIGHT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#receiptAmountOnWayTotals").text(data.RECEIPTAMOUNTONWAY.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#oilCardAmountTotals").text(data.OILCARDAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumTransFeeCountTotals").text(data.SUMTRANSFEECOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumGotAmountTotals").text(data.SUMGOTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumUngotAmountTotals").text(data.SUMUNGOTAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumTaxAmountTotal").text(data.SUMTAXAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }


    /**
     * 重置
     */
    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();

        var arr = [];
        arr.push(0);
        arr.push(1);
        $('#payableWriteOffStatusList').selectpicker('val',arr);

        searchPre();
    }
    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        searchPre();
    }
    /**
     * 费用明细
     */
    function detailTab(){
        var entrustLotId = $.table.selectColumns('entrustLotId');//运单id
        if (entrustLotId.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }

        var url = prefix + "/detailTab/"+entrustLotId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "费用明细",
            area: ['1100px', '700px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }


    /**
     * 将总计金额清零
     */
    function clearTotal() {
        receiptAmountFreightTotal = 0;//运费
        receiptAmountOnWayTotal = 0;//在途
        oilCardAmountTotal = 0;//油卡
        sumTransFeeCountTotal = 0;//总金额
        sumUngotAmountTotal = 0;//未付金额
        sumGotAmountTotal = 0;//已付金额
        taxAmountCount = 0;

    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        receiptAmountFreightTotal = receiptAmountFreightTotal + row.receiptAmountFreight;//运费
        receiptAmountOnWayTotal = receiptAmountOnWayTotal + row.receiptAmountOnWay;//在途总应收
        oilCardAmountTotal = oilCardAmountTotal + row.oilCardAmount;//油卡
        sumTransFeeCountTotal = sumTransFeeCountTotal + row.sumTransFeeCount;//总金额
        sumUngotAmountTotal = sumUngotAmountTotal + row.sumUngotAmount;//未付金额
        sumGotAmountTotal = sumGotAmountTotal + row.sumGotAmount;//已付金额
        taxAmountCount = taxAmountCount + row.sumTaxAmount;//已付金额

    }

    function subTotal(row) {
        receiptAmountFreightTotal = receiptAmountFreightTotal - row.receiptAmountFreight;//运费
        receiptAmountOnWayTotal = receiptAmountOnWayTotal - row.receiptAmountOnWay;//在途总应收
        oilCardAmountTotal = oilCardAmountTotal - row.oilCardAmount;//油卡
        sumTransFeeCountTotal = sumTransFeeCountTotal - row.sumTransFeeCount;//总金额
        sumUngotAmountTotal = sumUngotAmountTotal - row.sumUngotAmount;//未付金额
        sumGotAmountTotal = sumGotAmountTotal - row.sumGotAmount;//已付金额
        taxAmountCount = taxAmountCount - row.sumTaxAmount;//已付金额

    }

    function sortNum(a,b) {
        return a.addressType-b.addressType;
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#receiptAmountFreightTotal").text(receiptAmountFreightTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#receiptAmountOnWayTotal").text(receiptAmountOnWayTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#oilCardAmountTotal").text(oilCardAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#sumTransFeeCountTotal").text(sumTransFeeCountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#sumUngotAmountTotal").text(sumUngotAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#sumGotAmountTotal").text(sumGotAmountTotal.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#taxAmountTotal").text(taxAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

    }

    //查看合同
    function contract() {
        var entrustLotId = $.table.selectColumns('entrustLotId');
        if (entrustLotId.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        var url = ctx + "basic/carrier/contractView/"+entrustLotId
        $.modal.openTab("查看合同" , url);
    }

    //下载合同
    function downloadForWord(){
        var entrustLotId = $.table.selectColumns('entrustLotId');  //运单id
        if (entrustLotId.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        var url = ctx + "basic/carrier/contractDownload/"+entrustLotId
        $.operate.saveModal(url, $('#role-form').serialize(),function (result) {
            if (result.code == web_status.SUCCESS){
                var hostport=document.location.host;
                var donwloadPath = "http://"+hostport + result.data;
                window.open(donwloadPath);
            }
        });
    }

    function adjustExport(){
        var data = $("#role-form").serializeArray();
        var lotVbillstatusList = {};
        lotVbillstatusList.name = "lotVbillstatus"
        lotVbillstatusList.value = $.common.join($('#lotVbillstatus').selectpicker('val'));
        data.push(lotVbillstatusList);
        var payableWriteOffStatusList = {name:'params[payableWriteOffStatusList]',value:$.common.join($('#payableWriteOffStatusList').selectpicker('val'))}
        data.push(payableWriteOffStatusList);
        $.modal.confirm("确定导出所有" +
            $.table._option.modalName + "吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(prefix + "/exportAdjust", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }
    
    function adjustRecord(){
        var url = prefix + "/adjustRecord";
        $.modal.openTab('调整单记录',url);
    }

    function invoiceRecord(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (rows.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        var lot = $.table.selectColumns("lot");
        var url = ctx + "trustDeed?lot="+lot;
        $.modal.openTab("委托单", url);
    }

    function collectionChange(){
        var entrustLotIds = $.table.selectColumns("entrustLotId");
        layer.confirm("是否修改选中" + entrustLotIds.length + "条数据为：允许代收？",{
            btn:["确定","取消"]
        },function (index, layero) {
            layer.close(index);
            var data = { "ids": entrustLotIds.join()};
            var url = ctx + "trustDeed/collectionChange";
            $.operate.submit(url, "post", "json", data);
        });
    }

    function detail(entrustLotId){
        // var url = prefix + "/detail?entrustLotId="+entrustLotId;
        // $.modal.openTab("运单详情", url);
        if(entrustLotId){
            $.modal.openTab('运单详情', ctx + "tms/segment/dispatch_detail_V1/" + entrustLotId);
        }else{
            $.modal.alertWarning("请重新选择一条记录");
        }
        
    }

    function exportExcel(){
        var data = $("#role-form").serializeArray();
        var lotVbillstatusList = {};
        lotVbillstatusList.name = "lotVbillstatus"
        lotVbillstatusList.value = $.common.join($('#lotVbillstatus').selectpicker('val'));
        data.push(lotVbillstatusList);
        var payableWriteOffStatusList = {name:'params[payableWriteOffStatusList]',value:$.common.join($('#payableWriteOffStatusList').selectpicker('val'))}
        data.push(payableWriteOffStatusList);

        $.modal.confirm("确定导出所有" + $.table._option.modalName + "吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(prefix + "/export", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }
    function adjust(entrustLotId) {
        var url = ctx + "trace/addPayDetailAdjust?invoiceLotId="+entrustLotId+"&adjustType=1";
        $.modal.open("调账", url);
    }


</script>
</body>
</html>