<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('在途跟踪列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .cur{
        cursor: pointer;
    }
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse,.f7f7{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }

    .table-striped {
        height: calc(100% - 150px);
    }
    .disabled {
        pointer-events: none;
        filter: alpha(opacity=50); /*IE滤镜，透明度50%*/
        -moz-opacity: 0.5; /*Firefox私有，透明度50%*/
        opacity: 0.5; /*其他，透明度50%*/
    }
    /* .table-striped{
        height: auto;
    } */
    .fcff3{
        color: #ff3636 !important;
    }
    .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-xs-1, .col-xs-10, .col-xs-11, .col-xs-12, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9 {
        padding-left: 5px;
        padding-right: 7px;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .status1{
        width: 80px;
        background: #fff;
        border: 1px #eee solid;
        text-align: center;
        cursor: pointer;
        line-height: 20px;
        vertical-align: middle;
    }
    .act{
        background: #1ab492;
        color: #fff;
        border: 1px #1ab492 solid;
    }
    .dropdownpad{
        padding: 1px 2px;
    }
    .navs .dropdown-menu{
        padding: 10px 0;
        left: 20px;
        top: -10px;
    }
    .left-fixed-table-columns, .left-fixed-body-columns{
        z-index: 3;
    }
    .lHie{
        margin: 0;
        display: flex;
        align-items: center;
        width: 300px
    }
    .lHie .col-md-12 div>div{
        max-width: calc(100% - 60px);
    }
    .lHie .col-md-12 div>div>span {
        display: block;
        width: 100%;
        text-align: left;
        overflow:hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow:ellipsis;

    }
    .lHie .col-md-12 div span{
        vertical-align: middle;
        margin-right: 5px;
    }
    .sblogo{
        display: inline-block;
        margin: 0 5px 0 0 ;
        width: 18px;
        height: 18px;
        vertical-align: middle;
        border-radius: 4px;
    }
    .bootstrap-table .table:not(.table-condensed), .bootstrap-table .table:not(.table-condensed)>tbody>tr>td, .bootstrap-table .table:not(.table-condensed)>tbody>tr>th, .bootstrap-table .table:not(.table-condensed)>tfoot>tr>td, .bootstrap-table .table:not(.table-condensed)>tfoot>tr>th, .bootstrap-table .table:not(.table-condensed)>thead>tr>td{
        padding: 4px 8px;
    }
    .mt5{
        margin-top: 4px;
        position: relative;
    }
    /* .mt5 span{
        display: inline-block;
        margin-right: 5px;
    } */

    .label-primaryT{
        color: #1ab394;
        background-color: transparent;
        border: 1px solid #1ab394;
    }
    .label-successT{
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }
    .label-errorT{
        color: #1c84c6;
        background-color: yellow;
        border: 1px solid #1c84c6;
    }
    .label-defaultT{
        color: #5e5e5e;
        background-color: transparent;
        border: 1px solid #5e5e5e;
    }
    .label-warningT{
        color: #f8ac59;
        background-color: transparent;
        border: 1px solid #f8ac59;
    }
    .label-infoT{
        color: #23c6c8;
        background-color: transparent;
        border: 1px solid #23c6c8;
    }
    .label-dangerT{
        color: #ed5565;
        background-color: transparent;
        border: 1px solid #ed5565;
    }
    .label-inverseT{
        color: #262626;
        background-color: transparent;
        border: 1px solid #262626;
    }
    .tah{
        text-align: right;
        overflow:hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow:ellipsis;
    }
    .mtdiv{
        display: inline-block;
        vertical-align: middle;
        margin-left: 5px;
    }
    .mtdiv div{
        text-align: center;
        margin-top: 2px;
    }
    /*.mtdiv div:nth-child(2){*/
    /*    margin-top: 4px;*/
    /*}*/
    .whpr{
        white-space: pre-wrap;
        width: 8em;
        display: inline-block;
    }
    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .vertical-timeline-icon{
        width: 10px;
        height: 10px;
        background-color: #18a689;
        border-color: #18a689;
        top: 20px;
    }
    .vertical-timeline-content{
        margin-left: 16px;
    }
    .leftIcon{
        position: relative;
        margin: 0;
    }
    .leftIcon::after{
        content:" ";
        position: absolute;
        width: 1px;
        height: 100%;
        background-color: #18a689;
        top: 20px;
        left: 4px;
    }
    .leftIcon:last-child::after{
        height: 50%;
    }
    .leftIcon:first-child{
        font-size: 14px;
        font-weight: bold;
    }
    .tooltip-inner{
        max-width: 400px !important;
    }
    .fw{
        font-weight: bold;
    }
    .demo-class .layui-layer-btn{
        border-top: 1px solid #eee;
    }
    .demo-class .layui-layer-btn::before{
        content:"*请认真核对货量、费用、回单影像信息,确保无误后再提交！";
        color: #ff1f1f;
        line-height: 3;
    }
    .demo-class .layui-layer-btn .layui-layer-btn0{

        border-color: #18a689;
        background-color: #18a689;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .note{
        color: #808080;
    }
    .atime{
        color: #808080;
    }
    .leftIcon:first-child .note{
        background: rgba(239,77,64,0.05);
        border-radius: 4px;
        border: 1px solid rgba(239,77,64,0.08);
        padding: 3px 3px;
        color: #F94242;
    }
    .leftIcon:first-child .atime{
        color: #F94242;
    }

    .fcf9{
        color: #F94242;
    }
    .f16{
        font-size: 16px;
        vertical-align: middle;
    }
    .fa-phone{
        color:#009AFE
    }
    .fa-phone:hover{
        color: #0058B1;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="mt10 f7f7">
            <div class="over">
                <div class="col-md-6 col-sm-6">
                    <div class="fl">委托单状态：</div>
                    <div class="fl over">
                        <!--                <div class="fl status1 act" th:each="dict : ${entrustStatusEnum}" th:text="${dict.context}" th:value="${dict.value}"></div>-->
                        <div th:class="${vbillstatus != null ? 'fl status1':'fl status1 act'}" flag onclick="tabto(this,null)">全部</div>
                        <div th:class="${vbillstatus != null && vbillstatus == dict.value  ? 'fl status1 act':'fl status1 '}"
                             th:each="dict : ${entrustStatusEnum}" th:text="${dict.context}" th:onclick="tabto(this,[(${dict.value})])"></div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-2" >
                    <!--<div class="form-group">
                        <div class="col-sm-12">
                            <select name="isFreightLine" id="isFreightLine" class="form-control valid" aria-invalid="false">
                                <option value="">&#45;&#45; 是否是专线 &#45;&#45;</option>
                                <option value="1">是</option>
                                <option value="0">否</option>
                            </select>
                        </div>
                    </div>-->
                </div>
                <div class="col-md-2 col-sm-2">
                    <div class="form-group">
                        <div class="col-sm-12">
                            <input name="serviceName" class="form-control" type="text" placeholder="客服名称"  maxlength="30">
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-2">
                    <select name="params[combinedState]" id="combinedState" class="form-control valid" aria-invalid="false">
                        <option value="">-- 快捷搜索 --</option>
                        <option value="0" th:selected="${combinedState == '0'}">回单上传已超时</option>
                        <option value="1" th:selected="${combinedState == '1'}">回单上传即将超时</option>
                        <option value="2" th:selected="${combinedState == '2'}">正本回单已超时</option>
                        <option value="3" th:selected="${combinedState == '3'}">正本回单即将超时</option>
                    </select>

                </div>
            </div>
        </div>

        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <!-- 合并发货单 委托单 车次 -->
                    <!--<div class="col-md-3 col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4">委托单状态：</label>
                            <div class="col-sm-8">
                                <select name="vbillstatus" id="vbillstatus" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="委托单状态" multiple>
                                    <option th:each="dict : ${entrustStatusEnum}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>-->
                    <input type="hidden" name="vbillstatus" id="vbillstatus" th:value="${vbillstatus}">
                    <div class="col-md-7 col-sm-12">
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                           <label class="col-sm-4">单号：</label>-->
                                <div class="col-sm-12">
                                    <input name="invoiceVbillno" class="form-control" type="text" placeholder="发货单号/委托单/运单号"
                                           maxlength="30" required="" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input name="carrName" placeholder="承运商/手机/车牌/司机" class="form-control valid" type="text"
                                           required="" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input name="custAbbr" class="form-control" type="text" placeholder="客户简称/客户单号"
                                           maxlength="30" required="" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <!--                            <label class="col-sm-4">车队车牌：</label>-->
                                    <div class="col-sm-12">
                                        <input name="fleetCarNo" placeholder="车队车牌" class="form-control valid" type="text"
                                               required="" aria-required="true">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <!--                            <label class="col-sm-4">回单人：</label>-->
                                    <div class="col-sm-12">
                                        <input name="receiptMan" placeholder="回单人" class="form-control valid" type="text"
                                               required="" aria-required="true">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5 col-sm-12">
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">货品名称：</label>-->
                                <div class="col-sm-12">
                                    <input name="goodsName" placeholder="货品名称" class="form-control valid" type="text"
                                           required="" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-5 col-sm-5">
                            <div class="form-group">
                                <!--                            <label class="col-sm-3">要求提货日期：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="pickStartDate" placeholder="要求提货开始日" th:value="${pickStartDate}">
                                    <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                    <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="pickEndDate" placeholder="要求提货结束日" th:value="${pickEndDate}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-5 col-sm-5">
                            <div class="form-group">
                                <!--                            <label class="col-sm-3">要求到货日期：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="reqArriDateStart" placeholder="要求到货开始日">
                                    <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                    <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="reqArriDateEnd" placeholder="要求到货结束日" th:value="${reqArriDateEnd}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-12">
                        <div class="col-md-4 col-sm-4" style="padding-left: 0">
                            <div class="form-group">
                                <!--                            <label class="col-sm-3">回单日期：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="receiptStartDate" placeholder="回单开始日">
                                    <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                    <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="receiptEndDate" placeholder="回单结束日">
                                </div>
                            </div>
                        </div>
                        <!--                            <div class="col-md-4 col-sm-4" style="padding-left: 0">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    &lt;!&ndash;                            <label class="col-sm-3">回单日期：</label>&ndash;&gt;-->
                        <!--                                    <div class="col-sm-12">-->
                        <!--                                        <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="actDeliDateStart" placeholder="实际提货开始日">-->
                        <!--                                        <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>-->
                        <!--                                        <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="actDeliDateEnd" placeholder="实际提货结束日">-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <div class="col-md-8 col-sm-8" style="padding: 0">
                            <!--                                <div class="col-md-3 col-sm-3" >-->
                            <!--                                    <div class="form-group">-->
                            <!--                                        &lt;!&ndash;                            <label class="col-sm-4">当天需要跟踪：</label>&ndash;&gt;-->
                            <!--                                        <div class="col-sm-12">-->
                            <!--                                            <select name="params[combinedState]" class="form-control valid" aria-invalid="false">-->
                            <!--                                                <option value="">&#45;&#45; 即将超时 &#45;&#45;</option>-->
                            <!--                                                <option value="0" th:selected="${combinedState == '0'}">回单上传已超时</option>-->
                            <!--                                                <option value="1" th:selected="${combinedState == '1'}">回单上传即将超时</option>-->
                            <!--                                                <option value="2" th:selected="${combinedState == '2'}">正本回单已超时</option>-->
                            <!--                                                <option value="3" th:selected="${combinedState == '3'}">正本回单即将超时</option>-->
                            <!--                                            </select>-->
                            <!--                                        </div>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <div class="col-md-4 col-sm-4" >
                                <div class="form-group">
                                    <!--                            <label class="col-sm-4">当天需要跟踪：</label>-->
                                    <div class="col-sm-12">
                                        <select name="needTraceToday" class="form-control valid" aria-invalid="false">
                                            <option value="">-- 当天跟踪 --</option>
                                            <option value="1" th:selected="${needTraceToday == '1'}">是</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4" >
                                <div class="form-group">
                                    <!--                            <label class="col-sm-4">是否上传回单：</label>-->
                                    <div class="col-sm-12">
                                        <select name="receiptStatus" class="form-control valid" aria-invalid="false">
                                            <option value="">-- 上传回单 --</option>
                                            <option value="1" th:selected="${receiptStatus == '1'}">是</option>
                                            <option value="0" th:selected="${receiptStatus == '0'}">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-4" >
                                <div class="form-group">
                                    <!--                            <label class="col-sm-4">是否回单影像确认：</label>-->
                                    <div class="col-sm-12">
                                        <select name="receiptConfirmFlag" class="form-control valid" aria-invalid="false">
                                            <option value="">-- 影像确认 --</option>
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-12">
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">是否正本回单：</label>-->
                                <div class="col-sm-12">
                                    <select name="ifReceipt" class="form-control valid" aria-invalid="false">
                                        <option value="">-- 正本回单 --</option>
                                        <option value="1" th:selected="${ifReceipt == '1'}">是</option>
                                        <option value="0" th:selected="${ifReceipt == '0'}">否</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="salesDept" placeholder="运营组" id="salesDept" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" data-none-selected-text="运营组" multiple>
                                        <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                                th:text="${mapS.deptName}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">结算方式：</label>-->
                                <div class="col-sm-12">
                                    <select name="carrBalaType" class="form-control">
                                        <option value="">&#45;&#45;结算方式&#45;&#45;</option>
                                        <option th:value="1">单笔付款</option>
                                        <option th:value="2">月度付款</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">是否分配车队：</label>-->
                                <div class="col-sm-12">
                                    <select name="isFleetAssign" class="form-control valid" aria-invalid="false">
                                        <option value="">-- 分配车队 --</option>
                                        <option value="1">是</option>
                                        <option value="0">否</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">运输方式：</label>-->
                                <div class="col-sm-12">
                                    <select name="transCode" id="transCode" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" data-none-selected-text="运输方式" multiple th:with="type=${@dict.getType('trans_code')}">
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 合并承运商  司机  车牌号  承运商手机-->

                <div class="row">
                    <div class="col-md-5 col-sm-5">
                        <div class="form-group">
                            <!--                                <label class="col-sm-4">提货地址：</label>-->
                            <div class="col-sm-8">
                                <div class="col-sm-4">
                                    <select name="deliProvinceId" id="deliProvinceId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliCityId" id="deliCityId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliAreaId" id="deliAreaId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <input name="deliDetailAddr" id="deliDetailAddr" placeholder="请输入详细地址" class="form-control"
                                       type="text"
                                       maxlength="30">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-5 col-sm-5">
                        <div class="form-group">
                            <!--                                <label class="col-sm-4">收货地址：</label>-->
                            <div class="col-sm-2" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-7">
                                <div class="col-sm-4">
                                    <select name="arriProvinceId" id="arriProvinceId"
                                            class="form-control valid"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid"></select>
                                </div>

                                <div class="col-sm-4">
                                    <select name="arriAreaId" id="arriAreaId" class="form-control valid"></select>
                                </div>
                            </div>

                            <div class="col-sm-3">
                                <input name="arriDetailAddr" id="arriDetailAddr" placeholder="请输入详细地址" class="form-control"
                                       type="text" maxlength="30">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <!--                        <label class="col-sm-4"></label>-->
                        <div class="form-group" style="text-align: center;">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>




                </div>
                <div>
                </div>
            </form>
        </div>
        <!--        //1.什么都不选的情况下 承运商对账(8) 可以操作-->
        <!--        //2.不是分配车队的单选 可以操作所有-->
        <!--        //3.不是分配车队的多选可以操作2 3 4 6 8-->
        <!--        //4.分配车队的单选 可以操作6 7-->
        <!--        //5.分配车队的多选 可以操作6-->
        <div class="btn-group-sm" id="toolbar" role="group">
            <!-- <a class="btn btn-danger single disabled" onclick="revocation()" shiro:hasPermission="tms:trace:revocation">
                 <i class="fa fa-file"></i> 反确认
             </a>-->
            <!--            0-->


            <!--            1-->
            <!--            <a class="btn btn-danger single disabled" onclick="oppositeAffirm()" shiro:hasPermission="tms:fleet:trustDeed:oppositeAffirm">-->
            <!--                <i class="fa fa-reply"></i> 反确认-->
            <!--            </a>-->
            <!--            &lt;!&ndash;            2&ndash;&gt;-->
            <!--            <a class="btn btn-danger single disabled" onclick="removeAll()" shiro:hasPermission="tms:fleet:trustDeed:removeAll">-->
            <!--                <i class="fa fa-trash"></i> 删除-->
            <!--            </a>-->
            <!--            3-->
            <a class="btn btn-primary multiple disabled" onclick="pick()" shiro:hasAnyPermissions="tms:trace:pick,tms:fleet:trace:pick">
                <i class="fa fa-angle-left"></i> 提货作业
            </a>

            <!-- <a class="btn btn-primary single disabled" onclick="tacking()"  shiro:hasAnyPermissions="tms:trace:tacking,tms:fleet:trace:tacking">
                <i class="fa fa-plane"></i> 跟踪
            </a> -->
            <!--            6-->
            <a class="btn btn-primary multiple disabled" onclick="arrive()" shiro:hasAnyPermissions="tms:trace:arrive,tms:fleet:trace:arrive">
                <i class="fa fa-angle-right"></i> 到货作业
            </a>

            <a class="btn btn-primary multiple disabled" onclick="receiptConfirm()" shiro:hasAnyPermissions="tms:trace:receipt:confirm,tms:fleet:trace:receipt:confirm">
                <i class="fa fa-file"></i> 回单影像确认
            </a>
            <a class="btn btn-primary multiple disabled" onclick="receipt()" shiro:hasAnyPermissions="tms:trace:receipt,tms:fleet:trace:receipt">
                <i class="fa fa-file"></i> 正本回单
            </a>


            <!--            4-->

            <!--            <a class="btn btn-primary single disabled " onclick="carLocation()" shiro:hasAnyPermissions="tms:trace:location,tms:fleet:trace:location">-->
            <!--                <i class="fa fa-file" ></i> 轨迹查看-->
            <!--            </a>-->
            <!--            5-->

            <!--            7-->

            <!--            8-->

            <!-- <a class="btn btn-danger single disabled" onclick="pickRevocation()" shiro:hasPermission="tms:trace:pickOppositeAffirm">
                 <i class="fa fa-file single disabled"></i> 业务反确认
             </a>-->
            <!--            9-->
            <a class="btn btn-primary single disabled" onclick="register()" shiro:hasAnyPermissions="tms:trace:register,tms:fleet:trace:register">
                <i class="fa fa-jpy"></i> 费用登记
            </a>
            <!--            10-->
            <a class="btn btn-warning single disabled" onclick="confirmPayment()" shiro:hasAnyPermissions="tms:trace:confirmPayment,tms:fleet:trace:confirmPayment">
                <i class="fa fa-jpy"></i> 费用确认
            </a>
            <a class="btn btn-warning single disabled" onclick="cargo_new()"  shiro:hasAnyPermissions="tms:trustDeed:cargo,tms:fleet:trustDeed:cargo,tms:trustDeed:cargoNew">
                <i class="fa fa-pencil"></i> 货量更新
            </a>
            <a class="btn btn-danger single disabled" onclick="abnormal()" shiro:hasAnyPermissions="tms:trace:abnormal:view,tms:fleet:trace:abnormal">
                <i class="fa fa-plane"></i> 异常跟踪
            </a>
            <!--            11-->
           <!-- <a class="btn btn-primary" onclick="payCheckSheet()" shiro:hasAnyPermissions="tms:trace:payCheckSheet,tms:fleet:trace:payCheckSheet">
                <i class="fa fa-jpy"></i> 承运商对账
            </a>
            <a class="btn btn-primary single disabled" onclick="inviteDriver()" shiro:hasAnyPermissions="tms:trace:invite_driver">
                <i class="fa fa-file"></i> 邀请实名和授权
            </a>-->
            <div class="dropdown single disabled" style="display: inline-block;">
                <button class="btn btn-primary dropdown-toggle" style="padding: 4px 12px;" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                    管理员操作
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">

                    <!--            1-->
                    <!--                <a class="btn btn-danger single disabled" onclick="oppositeAffirm()" shiro:hasPermission="tms:fleet:trustDeed:oppositeAffirm">-->
                    <!--                    <i class="fa fa-reply"></i> 反确认-->
                    <!--                </a>-->
                    <!--                <a class="btn btn-danger single disabled" onclick="removeAll()" shiro:hasPermission="tms:fleet:trustDeed:removeAll">-->
                    <!--                    <i class="fa fa-trash"></i> 删除-->
                    <!--                </a>-->

                    <li>
                        <a class="single disabled" onclick="affirm()" shiro:hasAnyPermissions="tms:fleet:trustDeed:affirm,tms:trustDeed:affirm" >
                            <i class="fa fa-check-circle-o"></i> 确认
                        </a>
                    </li>
                    <!--                    <li>-->
                    <!--                        <a class="single disabled" onclick="oppositeAffirm()" shiro:hasAnyPermissions="tms:fleet:trustDeed:oppositeAffirm,tms:trustDeed:oppositeAffirm">-->
                    <!--                            <i class="fa fa-reply"></i> 反确认-->
                    <!--                        </a>-->
                    <!--                    </li>-->
                    <li>
                        <a class="single disabled" onclick="pickRevocation()" shiro:hasPermission="tms:trace:pickOppositeAffirm">
                            <i class="fa fa-file single disabled"></i> 反确认
                        </a>
                    </li>
                    <li>
                        <a class="single disabled fcff3" onclick="removeAll()" shiro:hasAnyPermissions="tms:fleet:trustDeed:removeAll,tms:trustDeed:removeAll">
                            <i class="fa fa-trash"></i> 删除
                        </a>
                    </li>
                    <li>
                        <a class="single disabled" onclick="entrustClose()" shiro:hasPermission="tms:trustDeed:entrustClose">
                            <i class="fa fa-remove"></i> 关闭
                        </a>
                    </li>
                    <li>
                        <a class="single disabled" onclick="repealClose()" shiro:hasPermission="tms:trustDeed:repealClose">
                            <i class="fa fa-reply"></i> 撤销关闭
                        </a>
                    </li>
                    <li>
                        <th:block th:if="${isFleetData == 0}"> <!-- 不是车队的显示 -->
                            <a class="single disabled" onclick="editReqDeliDate()" shiro:hasAnyPermissions="tms:trace:editReqDeliDate,tms:fleet:trace:editReqDeliDate">
                                <i class="fa fa-pencil"></i> 调整要求提货日期
                            </a>
                        </th:block>
                    </li>
                    <li>
                        <th:block th:if="${isFleetData == 0}"> <!-- 不是车队的显示 -->
                            <a class="single disabled" onclick="editReqArriDate()" shiro:hasAnyPermissions="tms:trace:editReqArriDate,tms:fleet:trace:editReqArriDate">
                                <i class="fa fa-pencil"></i> 调整要求到货日期
                            </a>
                        </th:block>
                    </li>
                    <li>
                        <a class="single disabled" onclick="editTransCode()" shiro:hasAnyPermissions="tms:trace:editReqArriDate,tms:fleet:trace:editReqArriDate">
                            <i class="fa fa-pencil"></i> 调整运输方式
                        </a>
                    </li>
                    <li>
                        <a class="single disabled" onclick="editCarrierAdmin()" shiro:hasPermission="tms:trace:editCarrierAdmin">
                            <i class="fa fa-file single disabled"></i> 修改承运商
                        </a>
                    </li>
                    <li>
                        <a class="single disabled" onclick="pickAll()" shiro:hasPermission="tms:trace:pickAll">
                            <i class="fa fa-file"></i> 一键提货
                        </a>
                    </li>
                    <li>
                        <a class="single disabled" onclick="arriveAll()" shiro:hasPermission="tms:trace:arriveAll">
                            <i class="fa fa-file"></i> 一键到货
                        </a>
                    </li>


                </ul>
            </div>


            <div class="dropdown" style="display: inline-block;"
                 shiro:hasAnyPermissions="tms:trace:export,tms:trace:export,tms:trace:sunshineBackRecord">
                <button class="btn btn-info dropdown-toggle" style="padding: 4px 12px;" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                    数据操作
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                    <li>
                        <a class="btn " onclick="exportTraceExcel()" shiro:hasAnyPermissions="tms:trace:export">
                            <i class="fa fa-download"></i> 跟踪表导出
                        </a>
                    </li>
                    <li>
                        <a class="btn " onclick="exportTraceSelectExcel()" shiro:hasAnyPermissions="tms:trace:export">
                            <i class="fa fa-download"></i> 跟踪表勾选导出
                        </a>
                    </li>
                    <li>
                        <a class="btn" shiro:hasAnyPermissions="tms:trace:sunshineBackRecord" onclick="sunshineBackRecord()" >
                            <i class="fa fa-download"></i> 阳光电源操作记录
                        </a>
                    </li>
                    <li>
                        <a class="btn" shiro:hasAnyPermissions="tms:trace:zllgBackRecord" onclick="zllgBackRecord()" >
                            <i class="fa fa-download"></i> 中粮面业操作记录
                        </a>
                    </li>
                    <li>
                        <a class="btn" shiro:hasAnyPermissions="tms:trace:zjhyRecord" onclick="zjhyRecord()" >
                            <i class="fa fa-download"></i> 浙江恒逸抓单记录
                        </a>
                    </li>
                    <li>
                        <a class="btn" shiro:hasAnyPermissions="tms:trace:xchdRecord" onclick="xchdRecord()" >
                            <i class="fa fa-download"></i> 中化推送回单记录
                        </a>
                    </li>
                </ul>
            </div>
           <!-- <a class="btn btn-warning single disabled" onclick="cargo_new()"  shiro:hasAnyPermissions="tms:trustDeed:cargoNew">
                <i class="fa fa-pencil"></i> 货量更新(新)
            </a>-->
            <!--            12-->

            <!--            13-->
            <!--            <th:block th:if="${isFleetData == 0}"> &lt;!&ndash; 不是车队的显示 &ndash;&gt;-->
            <!--<a class="btn btn-primary single disabled" onclick="editCarrier()" shiro:hasAnyPermissions="tms:trustDeed:editCarrier,tms:fleet:trustDeed:editCarrier">
                <i class="fa fa-pencil"></i> 修改承运商
            </a>-->
            <!--            </th:block>-->
            <!--            14-->
            <!--            <th:block th:if="${isFleetData == 0}"> &lt;!&ndash; 不是车队的显示 &ndash;&gt;-->
            <!--                <a class="btn btn-primary single disabled" onclick="editReqDeliDate()" shiro:hasAnyPermissions="tms:trace:editReqDeliDate,tms:fleet:trace:editReqDeliDate">-->
            <!--                    <i class="fa fa-pencil"></i> 调整要求提货日期-->
            <!--                </a>-->
            <!--            </th:block>-->
            <!--            &lt;!&ndash;            15&ndash;&gt;-->
            <!--            <th:block th:if="${isFleetData == 0}"> &lt;!&ndash; 不是车队的显示 &ndash;&gt;-->
            <!--                <a class="btn btn-primary single disabled" onclick="editReqArriDate()" shiro:hasAnyPermissions="tms:trace:editReqArriDate,tms:fleet:trace:editReqArriDate">-->
            <!--                    <i class="fa fa-pencil"></i> 调整要求到货日期-->
            <!--                </a>-->
            <!--            </th:block>-->
            <!--            <a class="btn btn-primary">
                            <i class="fa fa-file"></i> 撤销回单
                        </a>-->
            <!--<a class="btn btn-primary" shiro:hasPermission="tms:trace:export">
                <i class="fa fa-file"></i> 导出
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"  class="text-nowrap" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<script id="scanCodeHtml" type="text/template">
    <div class="form-content">
        <div class="row" >
            <div class="col-md-12"style="text-align:center;">
                <span  style="font-size:14px;">将二维码 <span style="color: #18a689;">“截图”</span>发送给车主或司机，用 <i class="fa fa-weixin" style="font-size:18px;color:#09BB07;"></i> <span style="color: #09BB07;">微信</span>扫码打开</span>
            </div>

            <div class="col-md-12" style="text-align:center;">
                <img src="" id="scanCodeImg" style='width: 200px;height: 200px;object-fit: contain;'>
            </div>

            <div class="col-md-12 fw" style="text-align:center;font-size:18px" >
                车主： <span id="carrInfo"></span>
            </div>

            <div class="col-md-12 fw" style="text-align:center;font-size:18px" id="driverInfo">
                司机： <span></span>
            </div>

            <div class="col-md-12 fw" style="text-align:center;font-size:18px" id="carrierInfoT"></div>

            <div class="col-md-12 mt5" style="text-align:center;font-size:14px;">
                发货单号: <span id="invoiceVbillnoInfo"></span>
            </div>
        </div>
    </div>
</script>
<script th:inline="javascript">
    var payOrCollect = [[${@dict.getType('pay_or_collect')}]];
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];

    var CloseAccountList = [[${CloseAccountList}]];//关账记录
    var entrustStatusEnum = [[${entrustStatusEnum}]];//委托单状态

    //委托单待确认状态
    var toAffirmstatus = [[${toAffirmstatus}]];
    //委托单已确认状态
    var affirmStatus = [[${affirmStatus}]];

    //获取是否车队数据
    var isFleetData = [[${isFleetData}]];
    //获取是否分配给车队数据
    var isFleetAssign = [[${isFleetAssign}]]
    //运输方式
    var datas = [[${@dict.getType('trans_code')}]];
    if(isFleetData == 0) {  //0不是  1是
        //alert("不是车队数据")  跳转TraceController
        var prefix = ctx + "trace";
    }else {
        //alert("车队数据") 跳转FleetTraceController
        var prefix = ctx + "fleettrace";
    }
    /**
     * 跳转委托单详情页
     */
    function detail(entrustId) {
        var url = ctx + "trustDeed/detail?entrustId="+entrustId + "&isFleetIn=" + isFleetData;
        $.modal.openTab($.table._option.modalName + "详细", url);
    }

    /**
     * 跳转车队具体调度  entrustId(委托单id)
     */
    function diaodudetail(entrustId) {
        var url = ctx + "trustDeed" + "/diaodudetail?entrustId="+entrustId;
        $.modal.openTab($.table._option.modalName + "详细", url);
    }

    function exp(invoiceVbillno){
        var url = prefix + "/exceptionTotal?invoiceVbillno="+invoiceVbillno;
        $.modal.openTab("异常统计", url);
    }


    // $.fn.serializeObject = function () {
    //     var o = {};
    //     var a = this.serializeArray();
    //     $.each(a, function () {
    //         if (o[this.name] !== undefined) {
    //             if (!o[this.name].push) {
    //                 o[this.name] = [o[this.name]];
    //             }
    //             o[this.name].push(this.value || '');
    //         } else {
    //             o[this.name] = this.value || '';
    //         }
    //     });
    //     return o;
    // };

    //初始化查询条件传参
    queryParams = function(params) {

        //var arr = [];
        //arr.push(entrustStatusEnum[1].value);
        //arr.push(entrustStatusEnum[2].value);
        //arr.push(entrustStatusEnum[5].value);
        //$('#vbillstatus').selectpicker('val',arr);

        var search = {};
        //var search = $("#role-form").serializeObject();
        $.each($("#role-form").serializeArray(), function(i, field) {
            //if(field.name == 'vbillstatus') {
            //将委托单状态数组改为string
            //    search[field.name] = $.common.join($('#vbillstatus').selectpicker('val'));
            //}else {
            search[field.name] = field.value;
            //}
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;

        let combinedState = $("#combinedState").val();
        if (combinedState != null) {
            search.params = new Map();
            search.params.combinedState = combinedState
        }

        return search;
        //var comName =null;
    }
    //当前年月日
    function getNowFormatDate() {
        var date = new Date();
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate;
        return currentdate;
    }
    //当前时间推前一个月
    function getFrontFormatDate() {
        var date = new Date();
        date.setDate(date.getDate()-30);
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() + 1 ;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate;
        /*var days = new Date(year, month, 0);
        let year = date.getFullYear();
        //获取当前日期中月的天数
        days = days.getDate();
        var year2 = date.getFullYear();
        var month2 = parseInt(month) - 1;
        if (month2 == 0) {
            year2 = parseInt(year2) - 1;
            month2 = 12;
        }
        var day2 = day;
        var days2 = new Date(year2, month2, 0);
        days2 = days2.getDate();
        if (day2 > days2) {
            day2 = days2;
        }
        if (month2 < 10) {
            month2 = '0' + month2;
        }
        var t2 = year2 + '-' + month2 + '-' + day2;*/


        return currentdate;
    }

    $(function () {
        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        //初始化要求提货开始日期  要求提货结束日期
        //获取当前时间 yyyy-MM-dd  getFrontFormatDate()   getNowFormatDate()
        let pickStartDate = [[${pickStartDate}]]
        if (pickStartDate ==null) {
            $("input[name='pickStartDate']").val(getFrontFormatDate());
        }
        //$("input[name='pickEndDate']").val(getNowFormatDate())

        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        var options = {
            url: prefix + "/list",
            detailUrl: prefix + "/detail",
            exportUrl: prefix + "/export",
            queryParams: queryParams,
            showToggle:false,
            showColumns:true,
            modalName: "在途跟踪",
            fixedColumns: true,
            fixedNumber:4,
            uniqueId: 'entrustId',
            //showExport: true,
            clickToSelect: true,
            //exportTypes:['excel','csv'],
            height: 560,
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"在途跟踪"
            },
            columns: [
                // {
                //     checkbox: true,
                // },
                {
                    valign : 'middle',
                    checkbox: true,
                    formatter:function(value,row,index){
                        if(row.isNewInvoice == '1'){
                            return {
                                disabled : true,
                            }
                        }else{
                            return {
                                disabled : false,
                            }
                        }
                        return value;
                    }
                },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(row.isNewInvoice != '1'){
                            var actions = [];
                            if ([[${@permission.hasAnyPermi('tms:trace:bookingSend')}]] != "hidden" && row.vbillstatus == '3' && row.ltlType == 1 && row.ifBookingArrival != '1') {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="预约送货"  onclick="bookingSend(\''+row.entrustId+'\')"><i class="fa fa-truck" style="font-size: 15px;"></i></a>');
                            }
                            if ([[${@permission.hasAnyPermi('tms:trace:install')}]] != "hidden" && row.vbillstatus == '3' && (row.ltlType == 2 || row.ltlType == 1)) {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="安装"  onclick="install(\''+row.entrustId+'\')"><i class="fa fa-wrench" style="font-size: 15px;"></i></a>');
                            }
                            if(row.haveExp == "1"){
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="待处理异常"  onclick="exp(\'' + row.invoiceVbillno + '\')"><i class="fa fa-exclamation-triangle" style="font-size: 15px;"></i></a>');
                            }

                            if ([[${@permission.hasAnyPermi('tms:trace:collection:view,tms:fleet:trace:detail')}]] != "hidden") {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="到付代收"  onclick="collection(\'' + row.lotId + '\',\''+row.entrustId+'\')"><i class="fa fa-cny" style="font-size: 15px;"></i></a>');
                            }
                            if ([[${@permission.hasAnyPermi('tms:trustDeed:deposit:view')}]] != "hidden") {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="定金"  onclick="deposit(\'' + row.entrustId + '\')"><i class="fa fa-dollar" style="font-size: 15px;"></i></a>');
                            }
                            // if ([[${@permission.hasPermi('tms:trace:detail')}]] != "hidden") {
                            //     actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="查看具体调度"  onclick="diaodudetail(\'' + row.entrustId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                            // }
                            if(row.singleLock == "2" &&  [[${@permission.hasPermi('tms:trace:lockLot')}]] != "hidden") {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="锁定应付"  onclick="lockPaydetail(this,'+ '\'' + row.lotId + "\', \'" + "1" + '\')"><i class="fa fa-unlock" style="font-size: 15px;"></i></a>');
                            }else if(row.singleLock == "1" &&  [[${@permission.hasPermi('tms:trace:unlockLot')}]] != "hidden" ) {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="解锁应付"  onclick="lockPaydetail(this,'+ '\'' + row.lotId  + "\', \'" + "2" + '\')"><i class="fa fa-lock" style="font-size: 15px;"></i></a>');
                            }
                            if ([[${@permission.hasAnyPermi('tms:trace:pushUrgent')}]] != "hidden") {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="推送紧急单"  onclick="urgent(\'' + row.entrustId + '\')"><i class="fa fa-exclamation-triangle" style="font-size: 15px;"></i></a>');
                            }
                            if (row.vbillstatus == 0 || row.vbillstatus == 1) {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="地址改送"  onclick="changeAddress(\'' + row.entrustId + '\')"><i class="fa fa-paper-plane" style="font-size: 15px;"></i></a>');
                            }
                            // return actions.join('');
                            var onDis=[]

                            if ([[${@permission.hasAnyPermi('tms:trace:detail,tms:fleet:trace:detail')}]] != "hidden") {
                                onDis.push('<a class="btn btn-xs " href="javascript:void(0)" title="明细"  onclick="detail(\'' + row.entrustId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                            }
                            let data=JSON.stringify({
                                isFleetData:row.isFleetData,
                                isFleetAssign:row.isFleetAssign,
                                entrustId:row.entrustId,
                                isClose:row.isClose,
                                vbillstatus:row.vbillstatus
                            }).replace(/"/g, '&quot;');
                            if(row.existPrepayOilCard==0){
                                onDis.push('<a class="btn btn-xs " href="javascript:void(0)" title="跟踪" onclick="tacking(\''+ data +'\')">' +
                                    '<img src="/img/gz1.png" class="sblogo" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+ontooltipName(JSON.stringify(row.carLocusList))+'"/></a>');
                            }else if(row.existPrepayOilCard==1){
                                onDis.push('<a class="btn btn-xs " href="javascript:void(0)" title="跟踪" onclick="tacking(\''+ data +'\')">' +
                                    '<img src="/img/gz3.png" class="sblogo" style="width: 20px;height: 24px" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+ontooltipName(JSON.stringify(row.carLocusList))+'"/></a>');
                            }else if(row.existPrepayOilCard=2){
                                onDis.push('<a class="btn btn-xs " href="javascript:void(0)" title="跟踪" onclick="tacking(\''+ data +'\')">' +
                                    '<img src="/img/gz2.png" class="sblogo" style="width: 20px;height: 24px" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+ontooltipName(JSON.stringify(row.carLocusList))+'"/></a>');
                            }



                          /*  if ((row.pickStatus == 1 || row.arriveStatus == 1)
                                && (row.vbillstatus==2 || row.vbillstatus==3)
                                && [[${@permission.hasAnyPermi('tms:trace:pick_push')}]] != "hidden") {
                                onDis.push('<a class="btn btn-xs " href="javascript:void(0)" title="提到货推送" onclick="pickPush(\'' + row.pickUpEntrustWorkId + '\')"><img src="/img/ts.png" class="sblogo"/></a>');
                            }*/

                            let dataObj='\''+ row.lotId +'\',\''+ row.driverName +'\',\''+ row.driverMobile +'\',\''+ row.carno +'\',\''+ row.carrName +'\',\''+ row.phone +'\',\''+ row.invoiceVbillno +'\',\''+ row.carrName +'\',\''+ row.driverCardId+'\',\''+row.carrCardId +'\''
                            actions.push('<div style="display: inline-block;"><img src="/img/ewm.png" class="sblogo" title="二维码"  onclick="scanCode('+dataObj+')"/></div>')

                            if (row.vbillstatus == '1' || row.vbillstatus == '2') {
                                actions.push('<a href="javascript:tqQrCode(\'' + row.entrustId + '\');"><i class="fa fa-qrcode sblogo" data-toggle="tooltip" data-html="true" style="color:green;font-size: 15px;vertical-align: text-top;" data-container="body" title="小黑卡二维码"></i></a>')
                            }
                            if (row.mailId) {
                                actions.push('<a href="javascript:mailTrace(\'' + row.mailId + '\');"><i class="fa fa-envelope-open sblogo" data-toggle="tooltip" data-html="true" style="color:green;font-size: 15px;" data-container="body" title="正本快递追踪"></i></a>')
                            }

                            onDis.push('<div class="dropdown navs" style="display: inline-block"><div class="dropdownpad" data-toggle="dropdown"><i class="fa fa-angle-down"></i></div><ul class="dropdown-menu">' + actions.join('') +'</ul></div>')

                            onDis.push(`<div class="mtdiv">`)
                           /* if (row.pickStatus == 1) {
                                onDis.push(`<div><img src="/img/th.png" class="sblogo" data-toggle="tooltip" data-container="body" data-placement="left" data-html="true" title="`+row.nfpPickUpErrMsg+`"/></div>`)
                            }
                            if (row.arriveStatus == 1) {
                                onDis.push(`<div><img src="/img/dh.png" class="sblogo" data-toggle="tooltip" data-container="body" data-placement="left" data-html="true" title="`+row.nfpArrivalErrMsg+`"/></div>`)
                            }*/
                            if (row.segType != '2') {
                                onDis.push(`<div><span class="label label-primary" style="padding:2px;">拆</span></div>`)
                            }
                            if(row.singleLock == '1'){
                                onDis.push('<div><span class="label label-warning" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="在途锁：' + row.lockMemo + '">锁</span></div>')
                            }
                            //异常锁
                            for(let i = 0 ; i < row.entrustExpVos.length; i++){
                                let item = row.entrustExpVos[i];
                                if(item.lockPay == 1){
                                    onDis.push('<div><span class="label label-danger" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="异常应付锁：' + item.handleNote + '">锁</span></div>')
                                }
                                if(item.lockPay == 2){
                                    onDis.push('<div><span class="label label-danger" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="异常承运商锁：' + item.handleNote + '">锁</span></div>')
                                }
                                if(item.lockOtherFee == 1){
                                    onDis.push('<div><span class="label label-danger" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="异常三方锁：' + item.handleNote + '">锁</span></div>')
                                }
                            }
                            if(row.importantFlag == 1){
                                onDis.push('<div><span class="label label-danger" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="重要跟踪标记">重</span></div>')
                            }
                            if(row.ltlType == 0){
                                onDis.push('<div><span class="label label-warning" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="提货段">提</span></div>')
                            }else if (row.ltlType == 1) {
                                onDis.push('<div><span class="label label-warning" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="干线段">干</span></div>')
                            }else if (row.ltlType == 2) {
                                onDis.push('<div><span class="label label-warning" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="送货段">送</span></div>')
                            }

                            if(row.ifBookingArrival == '1'){
                                onDis.push('<div><span class="label label-info" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="送达时间：'+row.arrivalDate+'">达</span></div>')
                            }else if(row.ifBookingSend == '1') {
                                onDis.push('<div><span class="label label-success" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="预约时间：'+row.bookingDate+'">预</span></div>')
                            }else if(row.ifBookingSend == '2') {
                                onDis.push('<div><span class="label label-danger" style="padding:2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="预约时间：'+row.bookingDate+'">预</span></div>')
                            }

                                onDis.push(`</div>`);

                            return onDis.join('')
                        }
                    }
                },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceVbillno',
                    formatter: function (value, row, index) {
                        var result = $.table.tooltip(value,14);

                        if(row.collectAmount != null && row.collectAmount != "") {
                            let title = ''
                            if (row.invoiceBalaType === '2') {
                                title = `现金到付（全部司机代收），`
                            }else if (row.invoiceBalaType === '5') {
                                title = `到付（现金给公司），`
                            }else if (row.invoiceBalaType === '6') {
                                title = `到付+回单（部分司机代收），`
                            }

                            result += ' <span class="label label-success" style="padding:1px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+title+'代收金额：'+row.collectAmount+'元">代</span>';
                        }

                        /*if (row.syn == 2||row.syn == 20){
                            result+='<img src="/img/sb.png" class="sblogo"/>'
                        }else if (row.syn != null){
                            result+='<img src="/img/hsb.png" class="sblogo" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'
                            if (row.syn == 0) {
                                result += '待审验'
                            } else if (row.syn == 1||row.syn == 30) {
                                result += row.synMsg
                            } else if (row.syn == 10) {
                                result += '审验中'
                            }
                            result += '"/>'
                        }*/
                        let auditStatusTxt = ['待提审','审核中','审核通过','审核失败'];
                        if (row.lotG7End == 2) {
                            let et = '';
                            if (row.g7LotQst) {
                                et = row.g7LotQst + "；"
                            }
                            if (row.g7CarExt && row.g7Corp) {
                                let g7CarExt = JSON.parse(row.g7CarExt);
                                if (g7CarExt.ysz) {
                                    let auditStatus = g7CarExt.ysz[row.g7Corp.toLowerCase()];
                                    if (auditStatus != 2 && auditStatus != null) {
                                        let _st_t = auditStatusTxt[auditStatus]
                                        et = et + "车辆道路运输证【" + _st_t + "】；";
                                    }
                                }
                            }
                            if (row.g7DriverExt && row.g7Corp) {
                                let g7DriverExt = JSON.parse(row.g7DriverExt);
                                if (g7DriverExt.zgz) {
                                    let auditStatus = g7DriverExt.zgz[row.g7Corp.toLowerCase()];
                                    if (auditStatus != 2 && auditStatus != null) {
                                        let _st_t = auditStatusTxt[auditStatus]
                                        et = et + "司机从业资格证【" + _st_t + "】；";
                                    }
                                }
                            }
                            result += ' <span class="label '+ (et?'label-errorT':'label-successT') +'" style="padding:1px;vertical-align: middle;"';
                            if (et) {
                                result += ' data-toggle="tooltip" data-container="body" data-placement="right" data-html="true" title="' + et + '"';
                            }
                            result += '>G7</span>'
                        } else if (row.lotG7Syn != null) {
                            result += ' <span class="label label-dangerT" style="padding:1px;vertical-align: middle;" data-toggle="tooltip" data-placement="right" data-html="true" title="'
                            if (row.g7LotQst) {
                                result = result + row.g7LotQst + "；"
                            }
                            if (row.g7CarExt && row.g7Corp) {
                                let g7CarExt = JSON.parse(row.g7CarExt);
                                if (g7CarExt.ysz) {
                                    let auditStatus = g7CarExt.ysz[row.g7Corp.toLowerCase()];
                                    if (auditStatus != 2 && auditStatus != null) {
                                        let _st_t = auditStatusTxt[auditStatus]
                                        result = result + "车辆道路运输证【" + _st_t + "】；";
                                    }
                                }
                            }
                            if (row.g7DriverExt && row.g7Corp) {
                                let g7DriverExt = JSON.parse(row.g7DriverExt);
                                if (g7DriverExt.zgz) {
                                    let auditStatus = g7DriverExt.zgz[row.g7Corp.toLowerCase()];
                                    if (auditStatus != 2 && auditStatus != null) {
                                        let _st_t = auditStatusTxt[auditStatus]
                                        result = result + "司机从业资格证【" + _st_t + "】；";
                                    }
                                }
                            }
                            if (row.lotG7Syn == 0) {
                                result += '等待G7审验'
                            } else if (row.lotG7Syn == 1) {
                                result += row.lotG7Msg
                            } else if (row.lotG7Syn == 2) {
                                if (row.lotG7Start == null || row.lotG7Start == 0) {
                                    result += '等待推送【发车】'
                                } else if (row.lotG7Start == 1) {
                                    result += '【发车】推送失败'
                                } else if (row.lotG7End == null || row.lotG7End == 0) {
                                    result += '等待推送【到达】'
                                } else if (row.lotG7End == 1) {
                                    result += '【到达】推送失败'
                                }
                            } else if (row.lotG7Syn == 7) {
                                result += '运单已作废'
                            }
                            result += '">G7</span>'
                        }
                        if(row.memo){
                            result += ' <span style="padding:1px;vertical-align: middle;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+row.memo+'"> <i class="fa fa-exclamation-circle"/> </span>'
                        }


                        result += '<br/><div class="mt5">'


                        if(row.ifReceipt == '1') {
                            result+= '<span class="label label-primaryT">已正本回单</span>'
                        }else {
                            if(row.receiptConfirmFlag == '1') {
                                result+= '<span class="label label-warningT">已回单确认</span>'
                            }else {
                                if(row.receiptStatus == '1') {
                                    result+= '<span class="label label-successT">已上传回单</span>'
                                }else {
                                    result+= '<span class="label label-dangerT">未上传回单</span>'
                                }
                            }
                        }
                        if (row.theDayTrack == 1) {
                            result+= '<span class="label label-dangerT">今日未跟踪</span>';
                        }
                        result += '</div>'




                        return result;
                    }
                },
                // {
                //     title: '备注',
                //     align: 'left',
                //     field: 'memo',
                //     formatter: function status(row,value) {
                //         return $.table.tooltip(value.memo);
                //     }
                // },

                {
                    title: '客户信息',
                    field: 'custOrderno',
                    align: 'left',
                    cellStyle: formatTableUnit,
                    formatter: function(value, row, index){
                        let htmlText;
                        if(value){
                            htmlText= $.table.tooltip(value,14);
                        }else{
                            htmlText='-'
                        }

                        var arr = row.custAbbr.split("-")
                        if(arr[1] != null && arr[1] != '') {
                            htmlText+='<br/>'+ row.custAbbr
                        }else {
                            htmlText+= '<br/>'+ arr[0]
                        }

                        if(row.arriMobile){
                            htmlText+= '<i class="fa fa-phone ml5 f16 cur" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+row.arriContact+'/'+row.arriMobile+'" onclick="getPhone(\'' + row.arriMobile + '\')"></i>'
                        }

                        return htmlText
                    }
                },
                // {
                //     title: '客户简称',
                //     field: 'custAbbr',
                //     align: 'left',
                //     formatter: function (value, row, index) {
                //         var arr = value.split("-")
                //         if(arr[1] != null && arr[1] != '') {
                //             return value
                //         }else {
                //             return arr[0]
                //         }
                //     }
                // },

                // {
                //     //title: '是否上传回单',
                //     title: '回单状态',
                //     align: 'left',
                //     field: 'receiptStatus',
                //     formatter: function status(value, row, index) {
                //         //未上传（红色），已上传（蓝色），回单确认(黄色)，正本回单（绿色）
                //         if(row.ifReceipt == '1') {
                //             return '<span class="label label-primary">已正本回单</label>'
                //         }else {
                //             if(row.receiptConfirmFlag == '1') {
                //                 return '<span class="label label-warning">已回单确认</label>'
                //             }else {
                //                 if(value == '1') {
                //                     return '<span class="label label-success">已上传回单</label>'
                //                 }else {
                //                     return '<span class="label label-danger">未上传回单</label>'
                //                 }
                //             }
                //         }
                //         //return value == '1'? '<span class="label label-primary">是</label>':'<span class="label label-danger">否</label>';
                //     }
                // },
                {
                    title: '状态',
                    align: 'left',
                    field: 'vbillstatus',
                    formatter: function status(value, row, index) {
                        var context = '';
                        if (row.fleetSegmentStatus == 0) {
                            context = '待调度';
                        }else if (row.fleetSegmentStatus == 1) {
                            context = '部分调度';
                        }else if (row.fleetSegmentStatus == 2) {
                            context = '已调度';
                        }

                        let payableWriteOffStatus="";
                        if(row.carrBalaType == 1){
                            if(row.spStatus == 3){
                                payableWriteOffStatus= '<span class="label label-danger ml5 pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="已驳回">驳</span>';
                            }else if(row.spStatus == 1){
                                payableWriteOffStatus= '<span class="label label-warning ml5 pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="审批中" style="cursor: pointer" onclick="wecom_process(\''+row.lotSpNo+'\')">审</span>';
                            }else if(row.payableWriteOffStatus==0){
                                payableWriteOffStatus= '<span class="label label-danger ml5 pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="未付款">未</span>';
                            }else if(row.payableWriteOffStatus==1){
                                payableWriteOffStatus= '<span class="label label-warning ml5 pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="部分付款">部</span>';
                            }else if(row.payableWriteOffStatus==2){
                                payableWriteOffStatus= '<span class="label label-success ml5 pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="已付款">已</span>';
                            }
                        }
                        let dpstStatus = null;
                        if (row.depositAmount) {
                            dpstStatus = row.depositVbillstatus
                            if (dpstStatus === 0) { // 新建
                                dpstStatus = '<span class="label label-danger pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="定金未付款">未</span>'
                            } else if (dpstStatus === 3) {
                                dpstStatus = '<span class="label label-success pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="定金已部分付款">部</span>';
                            } else if (dpstStatus === 4) {
                                dpstStatus = '<span class="label label-success pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="定金已付款">已</span>';
                            } else if (dpstStatus === 6) {
                                dpstStatus = '<span class="label label-warning pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="定金已申请付款">申</span>';
                            } else if (dpstStatus === 7) {
                                dpstStatus = '<span class="label label-warning pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="定金核销中">付</span>';
                            }
                        }
                        if (dpstStatus == null) {
                            dpstStatus = '';
                        }
                        switch(value - 0) {
                            case 0:
                                return '<span class="label label-primaryT">待确认</span>'+payableWriteOffStatus+dpstStatus;
                            case 1:
                                return '<span class="label label-warningT">已确认</span>'+payableWriteOffStatus+dpstStatus;
                            case 2:
                                return '<span class="label label-infoT">已提货</span>'+payableWriteOffStatus+dpstStatus;
                            case 3:
                                return '<span class="label label-successT">已到货 </span>'+payableWriteOffStatus+dpstStatus;
                            case 5:
                                return '<span class="label label-inverseT">关闭 </span>'+payableWriteOffStatus+dpstStatus;
                            case 6:
                                return '<span class="label label-warningT whpr">分配车队（'+ context +'）</span>'+payableWriteOffStatus+dpstStatus;
                            default:
                                break;
                        }
                    }
                },


                {
                    title: '要求提/到货信息',
                    field: 'reqDeliDate',
                    align: 'left',
                    switchable: false,
                    formatter: formatterInformation
                },
                {
                    title: '货量/要求车长车型',
                    field: 'multipleShippingAddressList',
                    align: 'left',
                    switchable: false,
                    formatter:  function status(value, row, index) {
                        var goodsName = "";

                        if(row.params.goodsName){
                            goodsName = row.params.goodsName
                        }else if(row.multipleShippingAddressList){
                            $.each(row.multipleShippingAddressList, function(i, elem) {
                                goodsName = elem.shippingGoodsList.map(x => x.goodsName).join(",")
                            })
                        }


                        let dataName='';

                        let num = row.numCountAdjust != 0 && $.common.isNotEmpty(row.numCountAdjust) ? row.numCountAdjust : row.numCount
                        let weight = row.weightCountAdjust != 0 && $.common.isNotEmpty(row.weightCountAdjust) ? row.weightCountAdjust : row.weightCount
                        let volume  = row.volumeCountAdjust != 0 && $.common.isNotEmpty(row.volumeCountAdjust) ? row.volumeCountAdjust : row.volumeCount

                        if(num){
                            dataName+=(num+'件|')
                        }
                        if(weight){
                            dataName+=(weight+'吨|')
                        }
                        if(volume){
                            dataName+=(volume+'m³')
                        }
                        let reqCarLenName = row.reqCarLenName;
                        let reqCarTypeName = row.reqCarTypeName;
                        return $.table.tooltip(goodsName+"&nbsp;"+dataName,20)+`<br/>`+ $.table.tooltip(reqCarLenName + "米" + reqCarTypeName,8);
                    }
                },
                // {
                //     title: '要求车长车型',
                //     align: 'left',
                //     field: 'reqCarLenName',
                //     formatter: function status(value, row, index) {
                //         let reqCarLenName = row.reqCarLenName;
                //         let reqCarTypeName = row.reqCarTypeName;
                //         // return row.reqCarLenName?($.table.tooltip(row.reqCarLenName)+"米"):$.table.tooltip(row.reqCarLenName)+ $.table.tooltip(row.reqCarTypeName)
                //
                //         return reqCarLenName + "米" + reqCarTypeName;
                //     }
                // },
                {
                    title: '金额明细',
                    field: 'costAmount',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let costAmount = [];
                        if(row.costAmount) {
                            costAmount.push('<span class="label label-primary pa2">总应付</span>' + value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        }
                        if (row.costTypeOnWayFee) {
                            let html = '<span class="label label-primary pa2">在途应付</span>'+row.costTypeOnWayFee.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
                            costAmount.push(html)
                        }
                        if (row.receOnWayFee) {
                            let html = '<span class="label badge-info pa2">在途应收</span>'+row.receOnWayFee.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
                            costAmount.push(html)
                        }
                        if (row.otherFee) {
                            let html = '<span class="label label-primary pa2">三方</span>'+row.otherFee.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
                            costAmount.push(html)
                        }

                        if(row.entrustCostList&&row.entrustCostList.length>0){
                            row.entrustCostList.forEach(item=>{
                                if(item.budgetType==1||item.budgetType==5||item.budgetType==null){
                                    let data=""
                                    let html=""
                                    if(item.isLotFee==0){
                                        html= item.regUserName+`创建了在途费用<br/>`+$.table.selectDictLabel(payOrCollect, item.budgetType)+"/"+$.table.selectDictLabel(costTypeOnWay, item.costType);
                                        if(item.cost){
                                            data += '<span class="label label-warning pa2">在途</span>'+item.cost.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
                                        }
                                    }else{
                                        html= item.regUserName+`创建了第三方费用<br/>`+$.table.selectDictLabel(costTypeOnWay, row.feeType);
                                        if(item.cost){
                                            data += '<span class="label label-warning pa2">在途</span>'+$.table.tooltip(item.cost)
                                        }
                                    }

                                    if(item.cost){
                                        if(item.checkStatus==0){
                                            data += ` <span class="label label-primary ml5" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">待审核</span>`;
                                        }else if(item.checkStatus==1){
                                            data += ` <span class="label label-info ml5" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">审核中</span>`;
                                        }else if(item.checkStatus==2){
                                            data += ` <i class="fa fa-check ml5" style="color: #1ab394;font-size: 18px" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`"></i>`;
                                        }else if(item.checkStatus==3){
                                            data += ` <i class="fa fa-close ml5" style="color: red;font-size: 18px" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`"></i>`;
                                        }
                                    }
                                    if(data){
                                        costAmount.push(data)
                                    }
                                }
                            })
                        }
                        if(row.entrustCostList&&row.entrustCostList.length>0){
                            row.entrustCostList.forEach(item=>{
                                if(item.budgetType==5||item.budgetType==null){
                                    let data=""
                                    let html=""
                                    if(item.isLotFee==0){
                                        html= item.regUserName+`创建了在途费用<br/>`+$.table.selectDictLabel(payOrCollect, item.budgetType)+"/"+$.table.selectDictLabel(costTypeOnWay, item.costType);

                                        if(item.feeAmount){
                                            data += '<span class="label label-coral pa2">三方</span>'+$.table.tooltip(item.feeAmount);
                                        }
                                    }else{
                                        html= item.regUserName+`创建了第三方费用<br/>`+$.table.selectDictLabel(costTypeOnWay, row.feeType);
                                        if(item.feeAmount){
                                            data += '<span class="label label-coral pa2">三方</span>'+item.feeAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                                        }
                                    }

                                    if(item.feeAmount){
                                        if(item.checkStatus==0){
                                            data += ` <span class="label label-primary ml5" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">待审核</span>`;
                                        }else if(item.checkStatus==1){
                                            data += ` <span class="label label-info ml5" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">审核中</span>`;
                                        }else if(item.checkStatus==2){
                                            data += ` <i class="fa fa-check ml5" style="color: #1ab394;font-size: 18px" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`"></i>`;
                                        }else if(item.checkStatus==3){
                                            data += ` <i class="fa fa-close ml5" style="color: red;font-size: 18px" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`"></i>`;
                                        }
                                    }
                                    if(data){
                                        costAmount.push(data)
                                    }
                                }
                            })
                        }
                        if(row.entrustCostList&&row.entrustCostList.length>0){
                            row.entrustCostList.forEach(item=>{
                                if(item.budgetType==2||item.budgetType==4){
                                    let data=""
                                    if(item.cost){
                                        data += '<span class="label badge-info pa2">在途应收</span>'+item.cost.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                                    }
                                    let html= item.regUserName+`创建了在途费用<br/>`+$.table.selectDictLabel(payOrCollect, item.budgetType)+"/"+$.table.selectDictLabel(costTypeOnWay, item.costType);

                                    if(item.checkStatus==0){
                                        data += ` <span class="label label-primary ml5" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">待审核</span>`;
                                    }else if(item.checkStatus==1){
                                        data += ` <span class="label label-info ml5" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">审核中</span>`;
                                    }else if(item.checkStatus==2){
                                        data += ` <i class="fa fa-check ml5" style="color: #1ab394;font-size: 18px" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`"></i>`;
                                    }else if(item.checkStatus==3){
                                        data += ` <i class="fa fa-close ml5" style="color: red;font-size: 18px" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`"></i>`;
                                    }
                                    if(data){
                                        costAmount.push(data)
                                    }
                                }
                            })
                        }

                        if(row.receiveAdjustRecord){
                            let html = '<span class="label label-warning pa2">应收调整单</span>'+row.receiveAdjustRecord.adjustAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
                            html += ` <span class="label label-info ml5" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+ontooltipName1(row.receiveAdjustRecord)+`">审核中</span>`;
                            costAmount.push(html)
                        }

                        if(row.payDetailAdjustRecord){
                            let html = '<span class="label label-warning pa2">应付调整单</span>'+row.payDetailAdjustRecord.adjustAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
                            html += ` <span class="label label-info ml5" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+ontooltipName1(row.payDetailAdjustRecord)+`">审核中</span>`;
                            costAmount.push(html)
                        }

                        if(row.otherFeeAdjustRecord){
                            let html = '<span class="label label-warning pa2">三方调整单</span>'+row.otherFeeAdjustRecord.adjustAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
                            html += ` <span class="label label-info ml5" data-toggle="tooltip" data-container="body" data-placement="auto" data-html="true" title="`+ontooltipName1(row.otherFeeAdjustRecord)+`">审核中</span>`;
                            costAmount.push(html)
                        }

                        return costAmount.join("<br/>");
                    }
                },
                // {
                //     title: '总金额',
                //     field: 'costAmount',
                //     align: 'left',
                //     formatter: function (value, row, index) {
                //         if (value === null) {
                //             return ;
                //         }
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                // {
                //     title: '在途/三方',
                //     field: 'entrustCostList',
                //     align: 'left',
                //     formatter: function (value, row, index) {
                //         let text=[]
                //         if(value&&value.length>0){
                //             value.forEach(item=>{
                //                 if(item.budgetType==1||item.budgetType==5||item.budgetType==null){
                //                     let data="<div class='mt5'>"
                //                     let html=""
                //                     if(item.isLotFee==0){
                //                         html= item.regUserName+`创建了在途费用<br/>`+$.table.selectDictLabel(payOrCollect, item.budgetType)+"/"+$.table.selectDictLabel(costTypeOnWay, item.costType);
                //                         data += item.cost.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+"/"+$.table.tooltip(item.feeAmount);
                //                     }else{
                //                         html= item.regUserName+`创建了第三方费用<br/>`+$.table.selectDictLabel(costTypeOnWay, row.feeType);
                //                         data += $.table.tooltip(item.cost)+"/"+item.feeAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //                     }
                //
                //                     if(item.checkStatus==0){
                //                         data += ` <span class="label label-primary ml5" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">待审核</span></div>`;
                //                     }else if(item.checkStatus==1){
                //                         data += ` <span class="label label-info ml5" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">审核中</span></div>`;
                //                     }else if(item.checkStatus==2){
                //                         data += ` <span class="label label-success ml5" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">审核通过</span></div>`;
                //                     }else if(item.checkStatus==3){
                //                         data += ` <span class="label label-danger ml5" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">审核不通过</span></div>`;
                //                     }
                //                     text.push(data)
                //                 }
                //             })
                //         }
                //         if(text.length>0){
                //             return text.join("");
                //         }else{
                //             return $.table.tooltip(text);
                //         }
                //     }
                // },
                // {
                //     title: '在途应收',
                //     field: 'entrustCostList',
                //     align: 'left',
                //     formatter: function (value, row, index) {
                //         let text=[]
                //         if(value&&value.length>0){
                //             value.forEach(item=>{
                //                 if(item.budgetType==2||item.budgetType==4){
                //                     let data="<div class='mt5'>"
                //                     data += item.cost.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //                     let html= item.regUserName+`创建了在途费用<br/>`+$.table.selectDictLabel(payOrCollect, item.budgetType)+"/"+$.table.selectDictLabel(costTypeOnWay, item.costType);
                //
                //                     if(item.checkStatus==0){
                //                         data += ` <span class="label label-primary ml5" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">待审核</span></div>`;
                //                     }else if(item.checkStatus==1){
                //                         data += ` <span class="label label-info ml5" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">审核中</span></div>`;
                //                     }else if(item.checkStatus==2){
                //                         data += ` <span class="label label-success ml5" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">审核通过</span></div>`;
                //                     }else if(item.checkStatus==3){
                //                         data += ` <span class="label label-danger ml5" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="`+getCheckList(item.spNo,item.entrustCostId,item.regDate,html)+`">审核不通过</span></div>`;
                //                     }
                //                     text.push(data)
                //                 }
                //             })
                //         }
                //         if(text.length>0){
                //             return text.join("");
                //         }else{
                //             return $.table.tooltip(text);
                //         }
                //     }
                // },
                // {
                //     title: '状态',
                //     align: 'left',
                //     field: 'vbillstatus',
                //     formatter: function status(value, row, index) {
                //         var context = '';
                //         if (row.fleetSegmentStatus == 0) {
                //             context = '待调度';
                //         }else if (row.fleetSegmentStatus == 1) {
                //             context = '部分调度';
                //         }else if (row.fleetSegmentStatus == 2) {
                //             context = '已调度';
                //         }
                //         switch(value - 0) {
                //             case 0:
                //                 return '<span class="label label-warningT">待审核</label>';
                //             case 1:
                //                 return '<span class="label label-primaryT">已通过</label>';
                //             case 2:
                //                 return '<span class="label label-dangerT">不通过</label>';
                //             default:
                //                 break;
                //         }
                //     }
                // },
                {
                    title: '承运商信息',
                    field: 'carrName',
                    align: 'left',
                    width: 100,
                    switchable: false,
                    formatter: function (value, row, index) {
                        let htmlText='';
                        if(row.carrName){
                            htmlText+= $.table.tooltip(row.carrName,6);
                        }

                        if(row.carrBalaType == 1 ){
                            htmlText+= '<span class="label label-success" style="padding:1px;margin-left: 5px;">单</span>'
                        }else if(row.carrBalaType == 2){
                            htmlText+= '<span class="label label-warning" style="padding:1px;margin-left: 5px;">月</span>'
                        }
                        if (row.familiar == 1) {
                            htmlText+= '<span class="label label-warning" style="padding:1px;margin-left: 2px;">熟</span>'
                        }
                        if (row.carrLockPay == 1) {
                            htmlText+='<span class="label label-danger" style="padding:1px;margin-left: 2px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="承运商锁定付款：' + row.carrLockPayReason + '">锁</span>';

                        }

                        htmlText+= '<br/>'
                        if(row.phone){
                            htmlText+= row.phone+'<i class="fa fa-phone ml5 f16 cur" aria-hidden="true" onclick="getPhone(\'' + row.phone + '\')"></i>'
                        }

                        return htmlText;
                    }
                },
                // {
                //     title: '结算方式',
                //     align: 'left',
                //     field: 'carrBalaType',
                //     formatter: function status(value) {
                //         if(value == 1 ){
                //             return "单笔付款";
                //         }else if(value == 2){
                //             return "月度付款";
                //         }
                //     }
                // },
                {
                    title: '车牌',
                    field: 'carno',
                    align: 'left',
                    formatter: function(value, row, index){
                        let htmlText='';

                        let carLen = row.carLen ? row.carLen + "米" : '';
                        let carTypeName = row.carTypeName ? row.carTypeName : ''

                        htmlText+='<div>'+$.table.tooltip(row.carno) + '<br/>' + $.table.tooltip(carLen + carTypeName,8)

                        if(row.carCheckStatus==2){
                            htmlText+='<br/><span style="color:#ed5565">（不合规）</span></div>'
                        }else{
                            htmlText+='</div>'
                        }
                        return htmlText
                    }
                },
                {
                    title: '司机信息1',
                    align: 'left',
                    field: 'driverName',
                    formatter: function status(row,value){
                        let htmlText='';
                        htmlText+='<div>'+$.table.tooltip(value.driverName)
                        if(value.driverCheckStatus==2){
                            htmlText+='<span style="color:#ed5565">（不合规）</span>'
                        }
                        let phone='';
                        if(value.driverMobile){
                            phone='<i class="fa fa-phone ml5 f16 cur" aria-hidden="true" onclick="getPhone(\'' + value.driverMobile + '\')"></i>'
                        }
                        htmlText+='<span> '+$.table.tooltip(value.driverMobile)+phone+'</span>';
                        if(value.driverCardId){
                            htmlText+='<br/>'+value.driverCardId+'</div>'
                        }
                        return htmlText
                    }
                },

                {
                    title: '司机信息2',
                    align: 'left',
                    field: 'driverName2',
                    formatter: function status(row,value){
                        let htmlText='';
                        htmlText+='<div>'+$.table.tooltip(value.driverName2)

                        let phone='';
                        if(value.driverMobile2){
                            phone='<i class="fa fa-phone ml5 f16 cur" aria-hidden="true" onclick="getPhone(\'' + value.driverMobile2 + '\')"></i>'
                        }
                        htmlText+='<span> '+$.table.tooltip(value.driverMobile2)+phone+'</span>';
                        if(value.driverCardId2){
                            htmlText+='<br/>'+value.driverCardId2+'</div>'
                        }
                        return htmlText
                    }
                },
                {
                    title: '车队信息',
                    field: 'params.fleetInfo',
                    formatter: function status(value,row) {
                        return value;
                    }
                },


                {
                    title: '跟踪次数',
                    align: 'left',
                    field: 'isManualEntry',
                    formatter: function status(value, row, index) {
                        if(value){
                            return value == 0 ?  0:value+'次';
                        }     
                        return $.table.tooltip(value);   
                    }
                },
                // {
                //     title: '承运商',
                //     field: 'carrName',
                //     align: 'left'
                // },





                // {
                //     title: '承运商联系人',
                //     align: 'left',
                //     field: 'contact'
                // },
                // {
                //     title: '承运商手机',
                //     field: 'phone',
                //     align: 'left'
                // },
                // {
                //     title: '司机',
                //     align: 'left',
                //     field: 'driverName',
                //     formatter: function status(row,value) {
                //         return $.table.tooltip(value.driverName);
                //     }
                // },
                // {
                //     title: '司机手机',
                //     align: 'left',
                //     field: 'driverMobile',
                //     formatter: function status(row,value) {
                //         return $.table.tooltip(value.driverMobile);
                //     }
                // },
                {
                    title: '调度信息',
                    field: 'regUserName',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.regUserName) +'<br/>'+ $.table.tooltip(value.regDate)
                    }
                },
                {
                    title: '实际提/到货信息',
                    align: 'center',
                    field: 'actDeliDate',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.actDeliDate)+'<br/>'+$.table.tooltip(value.actArriDate);
                    }
                },
                // {
                //     title: '实际到货时间',
                //     align: 'center',
                //     field: 'actArriDate'
                // },
                {
                    title: '提货操作信息',
                    align: 'center',
                    field: 'actArriDate',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.pickUpOperateUserName)+
                            ($.table.tooltip(value.pickUpOperateUserName)!='-'?($.table.tooltip(value.driverName)==$.table.tooltip(value.pickUpOperateUserName)?'<span class="label label-success" style="padding:1px;margin-left: 5px;vertical-align: middle;">司</span>':''):'')+'<br/>'
                            +$.table.tooltip(value.pickUpOperateTime);
                    }
                },
                {
                    title: '到货操作信息',
                    align: 'center',
                    field: 'regDate',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.arrivalOperateUserName)+
                            ($.table.tooltip(value.arrivalOperateUserName)!='-'?($.table.tooltip(value.driverName)==$.table.tooltip(value.arrivalOperateUserName)?'<span class="label label-success" style="padding:1px;margin-left: 5px;vertical-align: middle;">司</span>':''):'')+'<br/>'
                            +$.table.tooltip(value.arrivalOperateTime);
                    }
                },

                {
                    title: '回单确认信息',
                    field: 'receiptConfirmUser',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.receiptConfirmUser)+'<br/>'+$.table.tooltip(value.receiptConfirmTime)
                    }
                },


                // {
                //     title: '运段号',
                //     align: 'left',
                //     field: 'segmentVbillno'
                // },


                // {
                //     title: '打款账户',
                //     align: 'left',
                //     field: 'paymentAccount',
                //     formatter: function status(value) {
                //         if(value == 0 ){
                //             return "司机";
                //         }else if(value == 1){
                //             return "承运商";
                //         }
                //     }
                // },


                // {
                //     title: '调度时间',
                //     field: 'regDate',
                //     align: 'left'
                // },

                // {
                //     title: '回单确认时间',
                //     field: 'receiptConfirmTime',
                //     align: 'left'
                // },
                {
                    title: '多装多卸',
                    field: 'isMultiple',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if (row.isMultiple === 1) {
                            return '是'
                        }else {
                            return '否'
                        }

                    }
                },

                {
                    title: '运输方式',
                    field: 'transCode',
                    align: 'left',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(datas, value);
                    }
                },
                // {
                //     title: '发货单创建人',
                //     field: 'invoiceRegUser',
                //     align: 'left'
                // },
                // {
                //     title: '车队信息/是否分配车队',
                //     field: 'params.fleetInfo',
                //     formatter: function status(value,row) {
                //         let htmlText=$.table.tooltip(value)
                //          if(row.isFleetAssign == 0) {
                //             htmlText+= '<br/><span class="label label-yellow">' + '否' + '</label>';
                //         }else if(row.isFleetAssign == 1) {
                //             htmlText+= '<br/><span class="label label-yellow">' + '是' + '</label>';
                //         }else {
                //             htmlText+= '<br/><span class="label label-yellow">' + '否' + '</label>';
                //         }
                //         return htmlText;
                //     }
                // },
                // {
                //     title: '是否分配车队',
                //     align: 'left',
                //     field: 'isFleetAssign',
                //     formatter: function status(value, row, index) {  //0自己新建 1:拆段后分配给车队
                //         if(value == 0) {
                //             return '<span class="label label-yellow">' + '否' + '</label>';
                //         }else if(value == 1) {
                //             return '<span class="label label-yellow">' + '是' + '</label>';
                //         }else {
                //             return '<span class="label label-yellow">' + '否' + '</label>';
                //         }
                //     }
                // },

                {
                    title: '预计到场装货时间',
                    align: 'left',
                    field: 'estimatedArrivalTime',
                },
                {
                    title: '单号',
                    align: 'left',
                    field: 'vbillno',
                    formatter: function status(value,row) {
                        return $.table.tooltip(row.vbillno)+'<br/>'+$.table.tooltip(row.lot);
                    }
                },
                // {
                //     title: '车次号',
                //     align: 'left',
                //     field: 'lot'
                // },
                {
                    title: '运营组',
                    field: 'salesDept',
                    align: 'left',
                },

               /* {
                    title: '是否代收',
                    field: 'isAgencyCollect',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if (row.isAgencyCollect === 1) {
                            return '是'
                        }else {
                            return '否'
                        }

                    }
                },*/
                {
                    title: '调度组',
                    field: 'transLine',
                    align: 'left',
                },  {
                    title: '单据客服',
                    field: 'serviceName',
                    align: 'left',
                },
                {
                    title: '异常数量',
                    align: 'left',
                    field: 'exceptionCount',
                    formatter: function status(value, row, index) {
                        if(value){
                            return value == 0 ?  0:value+'次';
                        }
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '公里数',
                    align: 'left',
                    field: 'actualmileage'
                },
                {
                    title: '回单时效',
                    align: 'left',
                    field: 'receiptDays',
                    formatter: function status(value, row, index) {
                        if(value){
                            if(row.receiptDaysLess >= 0){
                                return value == 0 ?  0:value+'天'+'<span style="color:green">(剩余'+row.receiptDaysLess+'天)</span>';
                            }else{
                                return value == 0 ?  0:value+'天'+'<span style="color:red">(超期'+row.receiptDaysLess*-1+'天)</span>';
                            }

                        }
                        return '-';
                    }
                },
            ]
        };

        $.table.init(options);

        $('#bootstrap-table').on('load-success.bs.table', function (e,data) {
            // if(isFleetData == 1) { //车队数据不需要查看是否分配车队字段
            //     $.table.hideColumn("isFleetAssign","#bootstrap-table");
            // }

            //车队在途跟踪展示:是否由业务分配isFleetAssign
            if(isFleetData == 0) { //原先承运商字段隐藏
                $.table.hideColumn("originCust","#bootstrap-table");
            }
            if(isFleetData == 0) { //原始发货单号字段隐藏
                $.table.hideColumn("bizInvoiceVbillno","#bootstrap-table");
            }
        });
        //searchPre();
        $('[data-toggle="tooltip"]').tooltip({placement: 'right', html: true, trigger: 'hover'}); 
        // $('[data-toggle="tooltip"]').tooltip()
    });


    function searchPre() {
        var data = {};
        data.serviceName = $("input[name='serviceName']").val();
        data.isFreightLine= $("#isFreightLine").val();
        data.vbillstatus = $('#vbillstatus').val();//$.common.join($('#vbillstatus').selectpicker('val'));
        data.salesDept = $.common.join($('#salesDept').selectpicker('val'));
        data.transCode = $.common.join($('#transCode').selectpicker('val'));
        if ($("[name=pickStartDate]").val().trim() == '') {
            $.modal.msg("请选择要求提货日期起始日期", modal_status.WARNING);
            return;
        }
        var tmpEndDate = [[${#dates.format(#dates.createToday(), 'yyyy-MM-dd')}]]
        if ($("[name=pickEndDate]").val().trim() != '') {
            tmpEndDate = $("[name=pickEndDate]").val().trim();
        }
        var ymd1 = $("[name=pickStartDate]").val().trim().split("-");
        var ymd2 = tmpEndDate.split("-");
        if ((ymd2[0] - ymd1[0]) * 12 + (ymd2[1] - ymd1[1]) + (ymd2[2] - ymd1[2]) / 31 > 6) {
            $.modal.msg("要求提货日期跨度不能超过6个月", modal_status.WARNING);
            return;
        }
        let combinedState = $("#combinedState").val();
        if (combinedState != null) {
            data.params = new Map();
            data.params.combinedState = combinedState
        }

        $.table.search('role-form', data);
    }
    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }
    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        /*var arr = [];
        arr.push(entrustStatusEnum[1].value);
        arr.push(entrustStatusEnum[2].value);
        arr.push(entrustStatusEnum[5].value);

        $('#vbillstatus').selectpicker('val',arr);*/
        $('#vbillstatus').val("")
        $(".status1.act").removeClass("act")
        $(".status1:first").addClass("act");

        $("#combinedState").val("");
        $("#isFreightLine").val("");
        
        searchPre();
    }
    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        var deliDetailAddr= $('#deliDetailAddr').val()
        var arriDetailAddr= $('#arriDetailAddr').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        $('#deliDetailAddr').val(arriDetailAddr)
        $('#arriDetailAddr').val(deliDetailAddr)
        searchPre();
    }
    function carLocation() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        var url = prefix + "/location/"+rows.join();
        $.modal.open("轨迹查看", url);
    }


    //反确认
    function revocation() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);

        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }
        var  vbillstatus = $.table.selectColumns("vbillstatus");
        if(vbillstatus == 1){
            $.modal.open("反确认", ctx + "trustDeed/back_confirm/" + rows.join(),500,300);
        }else{
            $.modal.alertError("委托单状态需要为已确认才能进行反确认！");
        }
    }

    //反确认
    /*function pickRevocation() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);

        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }
        var  vbillstatus = $.table.selectColumns("vbillstatus");
        //是否回单标记
        var ifReceipt = $.table.selectColumns("ifReceipt");
        //是否回单确认标记
        var receiptConfirmFlag = $.table.selectColumns("receiptConfirmFlag");
        //提货到货回单反确认
        if(vbillstatus == 2){
            //验证是否可以货量更新
            $.ajax({
                type: "POST",
                url: prefix + "/checkConfirmPick?entrustId="+rows.join(),
                async: false,
                success: function(r){
                    if(r.code != 0){
                        $.modal.alertError(r.msg);
                        return false;
                    }else{
                        $.modal.open("提货反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join(),500,300);
                    }
                }
            });
        //到货反确认
        }else if(vbillstatus == 3 && ifReceipt != '1'){
            if(receiptConfirmFlag == '1'){
                $.modal.open("回单确认反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join(),500,300);
            }else{
                $.modal.open("到货反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join(),500,300);
            }
        //回单反确认
        }else if(vbillstatus == 3 && ifReceipt == '1'){
            $.modal.open("回单反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join(),500,300);
        }else{
            $.modal.alertError("委托单状态需要进行业务操作后才能进行反确认！");
        }
    }*/



    //跟踪
    function tacking(data) {
        //获取选中的行

        var bootstrapTable = JSON.parse(data);
        if (bootstrapTable["isFleetData"] == 0 && bootstrapTable["isFleetAssign"] != 0) {
            $.modal.alertWarning("分配车队的数据无法追踪，请点击详情进行跟踪！");
            return;
        }

        var rows =[bootstrapTable.entrustId]

        var isClose =bootstrapTable.isClose;
        //关账判断
        // for (var i = 0; i < isClose.length ; i++) {
        //     if(isClose == 1){
        //         $.modal.alertWarning("该月份已关账，无法进行操作！");
        //         return false;
        //     }
        // }

        //除待确认状态下才可以追踪
        var vbillstatus = bootstrapTable.vbillstatus;
        if (vbillstatus == toAffirmstatus) {
            $.modal.alertWarning("该状态下无法追踪！");
            return false;
        }

        // var carnoId = $.table.selectColumns("carnoId").join();
        // if (carnoId == null ||carnoId == "") {
        //     $.modal.alertWarning("该委托单没有车辆信息无法追踪！");
        //     return false;
        // }

        var url = prefix + "/tacking/"+rows.join() + "/0" + "/" + vbillstatus;
        // $.modal.openTab("跟踪", url);
        $.modal.openTab('跟踪详情', url);
        // layer.open({
        //     type: 2,
        //     area: ['70%', '100%'],
        //     fix: false,
        //     //不固定
        //     maxmin: true,
        //     shade: 0.3,
        //     title: "跟踪",
        //     content: url,
        //     btn: ['确定', '关闭'],
        //     // 弹层外区域关闭
        //     shadeClose: true,
        //     yes: function(index, layero) {
        //         var iframeWin = layero.find('iframe')[0];
        //         iframeWin.contentWindow.submitHandler(index, layero);
        //     },
        //     cancel: function(index) {
        //         return true;
        //     }
        // })

    }

    //提货作业
    function pick() {
        //获取选中的行
        var rows = []
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法提货，请点击详情进行提货！");
                return;
            }
        }



        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);

        /* var isClose = $.table.selectColumns("isClose");
         //关账判断
         for (var i = 0; i < isClose.length ; i++) {
             if(isClose == 1){
                 $.modal.alertWarning("该月份已关账，无法进行操作！");
                 return false;
             }
         }*/

        //判断勾选一条的情况
        if(rows.length == 1){
            var  vbillstatus = $.table.selectColumns("vbillstatus").join();
            //跳转提货作业详情页面
            if(vbillstatus == 2 || vbillstatus==3 || vbillstatus ==4 || vbillstatus==5 ){
                var url = prefix + "/pickDetail/"+rows.join();
                $.modal.openTab("提货作业", url);
            }else if(vbillstatus == 1){
                var url = prefix + "/pick/"+rows.join()+"?isRefresh=0&openType=tab";
                $.modal.openTab("提货作业", url);

                // layer.open({
                //     type: 2,
                //     area: ['70%', '100%'],
                //     fix: false,
                //     //不固定
                //     maxmin: true,
                //     shade: 0.3,
                //     title: "提货作业",
                //     content: url,
                //     btn: ['确定', '关闭'],
                //     // 弹层外区域关闭
                //     shadeClose: true,
                //     yes: function(index, layero) {
                //         var iframeWin = layero.find('iframe')[0];
                //         iframeWin.contentWindow.submitHandler(index, layero);
                //     },
                //     cancel: function(index) {
                //         return true;
                //     }
                // })

            }else{
                $.modal.alertWarning("委托单状态需要为已确认才能进行提货作业！");
            }
        }else{
            /*
            * 判断:
            *      如果都为公路零担的，要求提货日期相同
            */
            //所选择的列
            var rows2 = $.btTable.bootstrapTable('getSelections');
            //标记
            var flag = 0;
            for (var i = 0; i < rows2.length; i++) {
                //是否为公路零担
                if(rows2[i]['transCode'] === '1' || rows2[i]['transCode'] === '16'){
                    flag++;
                }
            }
            //如果都为公路零担继续比较要求提货日
            if(flag == rows2.length){
                for(var i=0;i<rows2.length;i++){
                    var reqDeliDate0 = rows2[0]['reqDeliDate'];
                    var reqDeliDate1 = rows2[i]['reqDeliDate'];
                    if(reqDeliDate0 != '' && reqDeliDate0 != null){
                        reqDeliDate0 = reqDeliDate0.substring(0 ,10);
                    }
                    if(reqDeliDate1 != '' && reqDeliDate1 != null){
                        reqDeliDate1 = reqDeliDate1.substring(0 ,10);
                    }
                    if(reqDeliDate0 != reqDeliDate1){
                        $.modal.alertWarning("请选择要求提货日一致的委托单进行提货作业！");
                        return false;
                    }
                }
            }else{
                //判断状态是否一致
                var vbillstatus = $.table.selectColumns("vbillstatus");
                var status =  vbillstatus[0];
                for( var i = 0 ; i < vbillstatus.length ; i++ ){
                    if(vbillstatus[i] != status || status != 1 ){
                        $.modal.alertWarning("请选择状态一致且为已确认的委托单进行提货作业！");
                        return false;
                    }
                }
                //判断运单号是否一致
                var lotColum = $.table.selectColumns("lot");
                var lot =  lotColum[0];
                for( var i = 0 ; i < lotColum.length ; i++ ){
                    if(lotColum[i] != lot){
                        $.modal.alertWarning("请选择运单号一致的委托单进行提货作业！");
                        return false;
                    }
                }
                //判断提货地址是否一致
                var deliAddrName = $.table.selectColumns("deliAddrName");
                var deliAddr =  deliAddrName[0];
                for( var i = 0 ; i < deliAddrName.length ; i++ ){
                    if(deliAddrName[i] != deliAddr){
                        $.modal.alertWarning("请选择提货地址一致的委托单进行提货作业！");
                        return false;
                    }
                }
            }

            var url = prefix + "/pickMany/"+rows.join()+"?isRefresh=0&openType=tab";
            $.modal.openTab("提货作业", url);
            // layer.open({
            //     type: 2,
            //     area: ['70%', '100%'],
            //     fix: false,
            //     //不固定
            //     maxmin: true,
            //     shade: 0.3,
            //     title: "提货作业",
            //     content: url,
            //     btn: ['确定', '关闭'],
            //     // 弹层外区域关闭
            //     shadeClose: true,
            //     yes: function(index, layero) {
            //         var iframeWin = layero.find('iframe')[0];
            //         iframeWin.contentWindow.submitHandler(index, layero);
            //     },
            //     cancel: function(index) {
            //         return true;
            //     }
            // })

        }
    }


    function pickAll(){
        if ($("[name=pickStartDate]").val().trim() == '') {
            $.modal.msg("请选择要求提货日期起始日期", modal_status.WARNING);
            return;
        }
        var tmpEndDate = [[${#dates.format(#dates.createToday(), 'yyyy-MM-dd')}]]
        if ($("[name=pickEndDate]").val().trim() != '') {
            tmpEndDate = $("[name=pickEndDate]").val().trim();
        }
        var ymd1 = $("[name=pickStartDate]").val().trim().split("-");
        var ymd2 = tmpEndDate.split("-");
        if ((ymd2[0] - ymd1[0]) * 12 + (ymd2[1] - ymd1[1]) + (ymd2[2] - ymd1[2]) / 31 > 6) {
            $.modal.msg("要求提货日期跨度不能超过6个月", modal_status.WARNING);
            return;
        }

        $.modal.confirm("请仔细阅读：确认将查询结果中所有【已确认】单据提货作业？", function () {
            $.modal.loading("正在处理中，请稍后...");
            var data = $('#role-form').serializeArray();
            let combinedState = $("#combinedState").val();
            if (combinedState != null) {
                data.push({ name: "params[combinedState]", value: combinedState });
            }
            data.push({ name: "serviceName", value: $("input[name='serviceName']").val() });
            data.push({ name: "isFreightLine", value:  $("#isFreightLine").val() });
            data.push({ name: "vbillstatus", value:  $('#vbillstatus').val() });
         /*   data.push({ name: "salesDept", value:  $.common.join($('#salesDept').selectpicker('val')) });
            data.push({ name: "transCode", value:  $.common.join($('#transCode').selectpicker('val')) });*/
            $.ajax({
                url: ctx + "trace/pickAll",
                type: "post",
                data: data,
                success: function(result) {
                    console.log(result);
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        $.modal.msg("操作成功", modal_status.SUCCESS);
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                }
            });


        });
    }

    function arriveAll(){
        if ($("[name=pickStartDate]").val().trim() == '') {
            $.modal.msg("请选择要求提货日期起始日期", modal_status.WARNING);
            return;
        }
        var tmpEndDate = [[${#dates.format(#dates.createToday(), 'yyyy-MM-dd')}]]
        if ($("[name=pickEndDate]").val().trim() != '') {
            tmpEndDate = $("[name=pickEndDate]").val().trim();
        }
        var ymd1 = $("[name=pickStartDate]").val().trim().split("-");
        var ymd2 = tmpEndDate.split("-");
        if ((ymd2[0] - ymd1[0]) * 12 + (ymd2[1] - ymd1[1]) + (ymd2[2] - ymd1[2]) / 31 > 6) {
            $.modal.msg("要求提货日期跨度不能超过6个月", modal_status.WARNING);
            return;
        }

        $.modal.confirm("请仔细阅读：确认将查询结果中所有【已提货】单据到货作业？", function () {
            $.modal.loading("正在处理中，请稍后...");
            var data = $('#role-form').serializeArray();
            let combinedState = $("#combinedState").val();
            if (combinedState != null) {
                data.push({ name: "params[combinedState]", value: combinedState });
            }
            data.push({ name: "serviceName", value: $("input[name='serviceName']").val() });
            data.push({ name: "isFreightLine", value:  $("#isFreightLine").val() });
            /*data.push({ name: "vbillstatus", value:  $('#vbillstatus').val() });
               data.push({ name: "salesDept", value:  $.common.join($('#salesDept').selectpicker('val')) });
               data.push({ name: "transCode", value:  $.common.join($('#transCode').selectpicker('val')) });*/
            $.ajax({
                url: ctx + "trace/arrivalAll",
                type: "post",
                data: data,
                success: function(result) {
                    console.log(result);
                    if (result.code == web_status.SUCCESS) {
                        $.modal.closeLoading();
                        $.modal.msg("操作成功", modal_status.SUCCESS);
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                }
            });


        });
    }

    //到货作业
    function arrive() {
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法到货，请点击详情进行到货！");
                return;
            }
        }
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);

        /* var isClose = $.table.selectColumns("isClose");
         //关账判断
         for (var i = 0; i < isClose.length ; i++) {
             if(isClose == 1){
                 $.modal.alertWarning("该月份已关账，无法进行操作！");
                 return false;
             }
         }*/
        //判断勾选一条的情况
        if(rows.length == 1) {
            var vbillstatus = $.table.selectColumns("vbillstatus");
            if (vbillstatus == 3 || vbillstatus == 4 || vbillstatus == 5) {
                var url = prefix + "/arrivalDetail/" + rows.join();
                $.modal.openTab("到货作业", url);

            } else if (vbillstatus == 2) {
                var url = prefix + "/arrival/" + rows.join()+"?isRefresh=0&openType=tab";
                $.modal.openTab("到货作业", url);
                // layer.open({
                //     type: 2,
                //     area: ['70%', '100%'],
                //     fix: false,
                //     //不固定
                //     maxmin: true,
                //     shade: 0.3,
                //     title: "到货作业",
                //     content: url,
                //     btn: ['确定', '关闭'],
                //     // 弹层外区域关闭
                //     shadeClose: true,
                //     yes: function(index, layero) {
                //         var iframeWin = layero.find('iframe')[0];
                //         iframeWin.contentWindow.submitHandler(index, layero);
                //     },
                //     cancel: function(index) {
                //         return true;
                //     }
                // })

            } else {
                $.modal.alertError("委托单状态需要为已提货才能进行到货作业！");
            }
        }else{
            //判断状态是否一致
            var vbillstatus = $.table.selectColumns("vbillstatus");
            var status =  vbillstatus[0];
            for( var i = 0 ; i < vbillstatus.length ; i++ ){
                if(vbillstatus[i] != status || status != 2 ){
                    $.modal.alertWarning("请选择状态一致且为已提货的委托单进行到货作业！");
                    return false;
                }
            }
            //判断运单号是否一致
            var lotColum = $.table.selectColumns("lot");
            var lot =  lotColum[0];
            for( var i = 0 ; i < lotColum.length ; i++ ){
                if(lotColum[i] != lot){
                    $.modal.alertWarning("请选择运单号一致的委托单进行到货作业！");
                    return false;
                }
            }
            //判断到货地址是否一致
            var arriAddrName = $.table.selectColumns("arriAddrName");
            var arriAddr =  arriAddrName[0];
            for( var i = 0 ; i < arriAddrName.length ; i++ ){
                if(arriAddrName[i] != arriAddr){
                    $.modal.alertWarning("请选择收货地址一致的委托单进行到货作业！");
                    return false;
                }
            }
            var url = prefix + "/arrival/" + rows.join()+"?isRefresh=0&openType=tab";
            $.modal.openTab("到货作业", url);
            // layer.open({
            //     type: 2,
            //     area: ['70%', '100%'],
            //     fix: false,
            //     //不固定
            //     maxmin: true,
            //     shade: 0.3,
            //     title: "到货作业",
            //     content: url,
            //     btn: ['确定', '关闭'],
            //     // 弹层外区域关闭
            //     shadeClose: true,
            //     yes: function(index, layero) {
            //         var iframeWin = layero.find('iframe')[0];
            //         iframeWin.contentWindow.submitHandler(index, layero);
            //     },
            //     cancel: function(index) {
            //         return true;
            //     }
            // })

        }
    }

    //费用登记
    function register() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);

        var isClose = $.table.selectColumns("isClose");
        //关账判断
        /*for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }*/

        var receiptConfirmFlag = $.table.selectColumns("receiptConfirmFlag");
        for(var i = 0 ; i < receiptConfirmFlag.length ; i++){
            if(receiptConfirmFlag[i] == 1){
                /*if(isFleetData != 0){
                    $.modal.alertWarning("单据已回单确认，无法新增费用！");
                    return false;
                }*/
                var url = ctx + "trace/adjust/"+rows.join();
                // $.modal.open("费用调整", url,'1000');
                layer.open({
                    type: 2,
                    area: ['90vw', ($(window).height() - 50)+'px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    title: "费用调整",
                    content: url,
                    btn: ['关闭'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    cancel: function(index) {
                        return true;
                    }
                });
                return ;
            }
        }

        //车次号判断
        var lot = $.table.selectColumns("lot").join();
        var lotList = lot.split(",");
        var firstLot = lotList[0];
        for(var i=0 ; i<lotList.length ; i++){
            if(lotList[i] != firstLot){
                $.modal.alertWarning("多个委托单进行费用登记需要运单号一致！");
                return false;
            }
        }

        //var lotId = $.table.selectColumns("lotId").join();

        var invoiceId = $.table.selectColumns("orderno").join();
        let width = ($(window).width() - 50);
        var url = prefix + "/register/"+rows.join()+"/0";
        $.modal.open("费用登记", url,width);

        //验证发货单是否超过五天
        /*$.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    var url = prefix + "/register/"+rows.join()+"/0";
                    $.modal.open("费用登记", url);
                }
            }
        });*/
    }

    //应付费用确认
    function confirmPayment(){
        var entrustId = $.table.selectColumns("entrustId");
        //运单号
        var lot = $.table.selectColumns("lot");
        var carrBalaType = $.table.selectColumns("carrBalaType");
        var isfleetAssign = $.table.selectColumns("isFleetAssign");
        if(carrBalaType == null || carrBalaType == "" || carrBalaType == "undefined"){
            $.modal.alertWarning("该承运商结算方式为空，无法进行费用确认！");
            return false;
        }
        var url = ctx + "trace/confirmPayment/"+lot.join()+"/"+entrustId+"/"+carrBalaType.join()+"/" +isfleetAssign.join();
        $.modal.openTab("费用确认", url);
    }

    //承运商对账
    function payCheckSheet(){
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的对账单无法调整！");
                return;
            }
        }

        var url = prefix + "/payCheckSheet";
        $.modal.openTab("承运商对账", url);
    }

    function record(id) {
        var url = prefix + "/record";
        $.modal.openTab($.table._option.modalName + "记录", url);
    }

    //异常跟踪
    function abnormal() {
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        /*for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法异常跟踪，请点击详情进行异常跟踪！");
                return;
            }
        }*/

        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);

        /*var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }*/

        var url = prefix + "/abnormal/"+rows.join();
        $.modal.openTab("异常跟踪" , url);
    }

    //回单-20200407修改为批量回单

    function receipt() {
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法回单，请点击详情进行回单！");
                return;
            }
        }
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 1 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法正本回单！");
                return;
            }
        }

        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);

        var isClose = $.table.selectColumns("isClose");
        //关账判断
        /*for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        var addrType = $.table.selectColumns("addrType");
        for(var i = 0 ; i < addrType.length ; i++){
            if(addrType[i] == '3'){
                $.modal.alertWarning("委托单到货地址为终点站时才能进行回单！");
                return false;
            }
        }*/

        var vbillstatus = $.table.selectColumns("vbillstatus");
        for(var i = 0 ; i < vbillstatus.length ; i++) {
            if (vbillstatus[i] == 0 || vbillstatus[i] == 1 || vbillstatus[i] == 2) {
                $.modal.alertWarning("委托单状态需要为已到货状态才能进行回单！");
                return false;
            }
        }

        //判断承运商结算方式
        /*var carrBalaType = $.table.selectColumns("carrBalaType");
        for(var i = 0 ; i < carrBalaType.length ; i++) {
            if (carrBalaType[i] != '1') {
                $.modal.alertWarning("请选择结算方式为单笔付款的承运商进行回单！");
                return false;
            }
        }*/

        var ifReceipt = $.table.selectColumns("ifReceipt");
        for(var i = 0 ; i < ifReceipt.length ; i++){
            if(ifReceipt[i] == '1' && rows.length > 1 ){
                $.modal.alertWarning("请勾选单条查看回单信息");
                return false;
            }
        }

        var receiptConfirmFlag = $.table.selectColumns("receiptConfirmFlag");
        for(var i = 0 ; i < receiptConfirmFlag.length ; i++){
            if(receiptConfirmFlag[i] == '0'){
                $.modal.alertWarning("请勾选已影像回单确认的委托单进行回单");
                return false;
            }
        }

        var url = prefix + "/receipt/"+rows.join()+"?monthFlag=1&isRefresh=0";
        // $.modal.openTab("回单", url);

        layer.open({
            type: 2,
            area: ['85%', '100%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "回单",
            content: url,
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function(index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function(index) {
                return true;
            }
        })

    }

    function place() {
        var url = prefix + "/place";
        $.modal.openTab("位置跟踪", url);
    }

    //回单确认
    function receiptConfirm(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);

        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法回单确认，请点击详情进行回单确认！");
                return;
            }
        }

        //判断委托单状态
        var vbillstatus = $.table.selectColumns("vbillstatus").join();
        if (vbillstatus == 0 || vbillstatus == 1 || vbillstatus == 2) {
            $.modal.alertWarning("委托单状态需要为已到货状态才能进行回单确认！");
            return false;
        }

        //判断是否回单
        var ifReceipt = $.table.selectColumns("ifReceipt").join();
        if(ifReceipt == 1){
            $.modal.alertWarning("委托单已经进行过回单，无法再确认！");
            return false;
        }
        var url;
        //跳转回单确认界面
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if(rows.length == 0){
            $.modal.alertWarning("至少勾选一条数据！");
            return false;
        }else if(rows.length == 1){
            url = prefix + "/receiptConfirm/"+rows.join()+"/"+bootstrapTable[0].isFleetData+ "/"+bootstrapTable[0].isFleetAssign+"/0";
        }else{
            var rows2 = $.btTable.bootstrapTable('getSelections');
            //相同承运商、要求到货日期、到货地址、非到货段
            for(var i=0;i<rows2.length;i++){
                var isArrivalSection = rows2[i]['isArrivalSection'];
                if(isArrivalSection == 1){
                    $.modal.alertWarning("请选择非到货地委托单进行回单确认！");
                    return false;
                }

                var arriAddrName0 = rows2[0]['arriAddrName'];
                var arriAddrName1 = rows2[i]['arriAddrName'];

                if(arriAddrName0 != arriAddrName1){
                    $.modal.alertWarning("请选择到货地址一致的委托单进行回单确认！");
                    return false;
                }

                var carrierId0 = rows2[0]['carrierId'];
                var carrierId1 = rows2[i]['carrierId'];

                if(carrierId0 != carrierId1){
                    $.modal.alertWarning("请选择承运商一致的委托单进行回单确认！");
                    return false;
                }

                var reqArriDate0 = rows2[0]['reqArriDate'];
                var reqArriDate1 = rows2[i]['reqArriDate'];
                if(reqArriDate0 != '' && reqArriDate0 != null){
                    reqArriDate0 = reqArriDate0.substring(0 ,10);
                }
                if(reqArriDate1 != '' && reqArriDate1 != null){
                    reqArriDate1 = reqArriDate1.substring(0 ,10);
                }
                if(reqArriDate0 != reqArriDate1){
                    $.modal.alertWarning("请选择要求到货日一致的委托单进行回单确认！");
                    return false;
                }


            }
            url = prefix + "/receiptConfirmMultiple/"+rows.join()+"/"+bootstrapTable[0].isFleetData+ "/"+bootstrapTable[0].isFleetAssign+"/0";
        }

        layer.open({
            skin: 'demo-class',
            type: 2,
            area: ['85%', '100%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "回单确认",
            content: url,
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function(index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function(index) {
                return true;
            }
        })

    }

    //司机代收
    function collection(lotId,entrustId) {
        var url = prefix + "/collection?lotId="+lotId+"&entrustId="+entrustId;
        $.modal.openTab("到付代收", url);
    }

    /**
     * 货量更新
     */
    function cargo() {
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        // console.log(bootstrapTable[0])
        // console.log(bootstrapTable[0].isFleetData)
        // console.log(bootstrapTable[0].isFleetAssign)
        // for (var i = 0; i < bootstrapTable.length; i++) {
        //     if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
        //         $.modal.alertWarning("分配车队的对账单无法调整！");
        //         return;
        //     }
        // }

        /*var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }*/

        var invoiceId = $.table.selectColumns("orderno").join();

        var entrustId =  $.table.selectColumns("entrustId");
        //验证是否可以货量更新
        $.ajax({
            type: "POST",
            url: ctx + "trustDeed/checkCargo?entrustId="+entrustId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    //
                    // parent.layer.open({
                    //     type: 2,
                    //     maxmin: true,
                    //     shade: false,
                    //     title: "货量更新",
                    //     area: ['70%', '100%'],
                    //     content: ctx + "trustDeed/cargo?entrustId="+entrustId + "&isFleetData=" + bootstrapTable[0].isFleetData + "&isFleetAssign=" + bootstrapTable[0].isFleetAssign,
                    //     shadeClose: true,
                    //     btn: ['<i class="fa fa-close"></i> 关闭'],
                    //     yes: function (index, layero) {
                    //         parent.layer.close(index);
                    //     }
                    // });
                    layer.open({
                        type: 2,
                        area: ['70%', '100%'],
                        fix: false,
                        //不固定
                        maxmin: true,
                        shade: 0.3,
                        title: "货量更新",
                        content: ctx + "trustDeed/cargo?entrustId="+entrustId + "&isFleetData=" + bootstrapTable[0].isFleetData + "&isFleetAssign=" + bootstrapTable[0].isFleetAssign,
                        btn: ['确定', '关闭'],
                        // 弹层外区域关闭
                        shadeClose: true,
                        yes: function(index, layero) {
                            var iframeWin = layero.find('iframe')[0];
                            iframeWin.contentWindow.submitHandler(index, layero);
                        },
                        cancel: function(index) {
                            return true;
                        }
                    })


                    // $.modal.open("货量更新", ctx + "trustDeed/cargo?entrustId="+entrustId + "&isFleetData=" + bootstrapTable[0].isFleetData + "&isFleetAssign=" + bootstrapTable[0].isFleetAssign,1050);
                }
            }
        });

        //验证发货单是否超过五天
        /* $.ajax({
             type: "POST",
             url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
             async: false,
             success: function(r){
                 if(r.code != 0){
                     $.modal.alertError(r.msg);
                     return false;
                 }else{
                     var entrustId =  $.table.selectColumns("entrustId");
                     //验证是否可以货量更新
                     $.ajax({
                         type: "POST",
                         url: ctx + "trustDeed/checkCargo?entrustId="+entrustId,
                         async: false,
                         success: function(r){
                             if(r.code != 0){
                                 $.modal.alertError(r.msg);
                                 return false;
                             }else{
                                 //
                                 // parent.layer.open({
                                 //     type: 2,
                                 //     maxmin: true,
                                 //     shade: false,
                                 //     title: "货量更新",
                                 //     area: ['70%', '100%'],
                                 //     content: ctx + "trustDeed/cargo?entrustId="+entrustId + "&isFleetData=" + bootstrapTable[0].isFleetData + "&isFleetAssign=" + bootstrapTable[0].isFleetAssign,
                                 //     shadeClose: true,
                                 //     btn: ['<i class="fa fa-close"></i> 关闭'],
                                 //     yes: function (index, layero) {
                                 //         parent.layer.close(index);
                                 //     }
                                 // });
                                 layer.open({
                                     type: 2,
                                     area: ['70%', '100%'],
                                     fix: false,
                                     //不固定
                                     maxmin: true,
                                     shade: 0.3,
                                     title: "货量更新",
                                     content: ctx + "trustDeed/cargo?entrustId="+entrustId + "&isFleetData=" + bootstrapTable[0].isFleetData + "&isFleetAssign=" + bootstrapTable[0].isFleetAssign,
                                     btn: ['确定', '关闭'],
                                     // 弹层外区域关闭
                                     shadeClose: true,
                                     yes: function(index, layero) {
                                         var iframeWin = layero.find('iframe')[0];
                                         iframeWin.contentWindow.submitHandler(index, layero);
                                     },
                                     cancel: function(index) {
                                         return true;
                                     }
                                 })


                                 // $.modal.open("货量更新", ctx + "trustDeed/cargo?entrustId="+entrustId + "&isFleetData=" + bootstrapTable[0].isFleetData + "&isFleetAssign=" + bootstrapTable[0].isFleetAssign,1050);
                             }
                         }
                     });
                 }
             }
         });*/
    }



    /**
     * 货量更新
     */
    function cargo_new() {
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var invoiceId = $.table.selectColumns("orderno").join();

        var entrustId = $.table.selectColumns("entrustId");

        //验证是否可以货量更新
        /*$.ajax({
            type: "POST",
            url: ctx + "trustDeed/checkCargo?entrustId=" + entrustId,
            async: false,
            success: function (r) {
                if (r.code != 0) {
                    $.modal.alertError(r.msg);
                    return false;
                } else {

                }
            }
        });*/

        $.modal.openTab("货量更新", ctx + "trustDeed/cargoNew?entrustId=" + entrustId + "&isFleetData=" + bootstrapTable[0].isFleetData + "&isFleetAssign=" + bootstrapTable[0].isFleetAssign);
    }

        /**
     * 修改承运商
     */
    function editCarrier() {
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法修改承运商！");
                return;
            }
        }

        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        //判断委托单状态为新建，已确认 后台要验证 运单对应下的 应付为新建状态
        var vbillstatus = $.table.selectColumns("vbillstatus").join();
        if(vbillstatus != 0 && vbillstatus != 1){
            $.modal.alertWarning("委托单状态为待确认和已确认才能修改承运商！");
            return false;
        }

        //2021-12-02 Zhangf 修改
        //var entrustId =  $.table.selectColumns("entrustId");
        //$.modal.openTab("修改承运商", ctx + "trustDeed/editCarrier?entrustId="+entrustId);
        //检查
        //验证发货单是否超过五天
        /*$.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.openTab('调度', prefix + "/dispatch/" + segmentIds);
                }
            }
        });*/
        var lotId =  $.table.selectColumns("lotId");
        $.modal.openTab("修改承运商", ctx + "tms/segment/trace_dispatch_detail/"+lotId);

    }


    /**
     * 查看定金
     * @param entrustId
     */
    function deposit(entrustId) {
        $.modal.openTab("查看定金", ctx + "tms/deposit/entrust?entrustId="+entrustId);
    }

    function urgent(entrustId) {
        $.modal.confirm("确认发送紧急推送信息吗？",function () {
            $.operate.post(ctx + "trace/push_urgent", { "entrustId": entrustId});
        });
    }

    /**
     * 锁定应付  TODO
     */
    function lockPaydetail(obj, lotId, num) {
        //确定lock锁状态
        if(num == 1) {
            /*$.modal.confirm("确定锁定该单应付吗", function() {
                //var url = prefix + "/lockPaydetail";
                //var data = {"lotId": lotId,"singleLock": num};
                //$.operate.submit(url, "post", "json", data);
                //var payDetailId = $.table.selectColumns("payDetailId");

            });*/
            $.modal.open("锁定应付", prefix + "/lockPayDetailPage/" + lotId,500,300);
        }else {
            $.modal.confirm("确定解锁该单应付吗？" , function() {
                var url = prefix + "/lockPaydetail";
                var data = {"lotId": lotId,"singleLock": num};
                $.operate.submit(url, "post", "json", data);

            });
        }
    }

    /**
     * 调整要求提货日期
     * @param entrustId
     */
    function editReqDeliDate(entrustId) {
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法调整要求提货日期！");
                return;
            }
        }

        var rows = $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (rows.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        //关账判断
        var isClose = $.table.selectColumns("isClose");
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }
        var vbillstatus = $.table.selectColumns("vbillstatus").join();
        if (vbillstatus != 0 && vbillstatus != 1) {
            $.modal.alertWarning("委托单已提货，无法调整要求提货日期！");
            return false;
        }
        var url = prefix + "/editReqDeliDate/" + rows.join() + "/5";
        $.modal.open("调整要求提货日期", url,600,500);
    }

    function editTransCode(){
        var rows = $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (rows.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        var vbillstatus = $.table.selectColumns("vbillstatus").join();
        console.log(vbillstatus)
        if(vbillstatus != 0 && vbillstatus != 1 && vbillstatus != 6){
            $.modal.alertWarning("请选择状态为新建或已确认的单据");
            return;
        }
        var url = prefix + "/editTransCode/" + rows.join();
        $.modal.open("调整运输方式", url,400,300);
    }



    /**
     * 调整要求到货日期
     * @param entrustId
     */
    function editReqArriDate(entrustId) {
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法调整要求到货日期！");
                return;
            }
        }

        var rows = $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (rows.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }
        var vbillstatus = $.table.selectColumns("vbillstatus").join();
        if (vbillstatus != 0 && vbillstatus != 1 && vbillstatus != 2) {
            $.modal.alertWarning("委托单已到货，无法调整要求到货日期！");
            return false;
        }
        var url = prefix + "/editReqArriDate/" + rows.join() + "/6";
        $.modal.open("调整要求到货日期", url,600,500);
    }

    /**
     * 确认
     */
    function affirm() {
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法确认！");
                return;
            }
        }

        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        var vbillstatus =  $.table.selectColumns("vbillstatus");
        for (var i = 0; i < vbillstatus.length ; i++) {
            if (vbillstatus[i] != '0' && vbillstatus[i] !== undefined) {
                $.modal.alertWarning("请选择待确认状态的委托单");
                return;
            }
        }
        if (vbillstatus.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var entrustId =  $.table.selectColumns("entrustId");



        $.operate.post(ctx + "trustDeed/affirm", { "entrustId": entrustId.join()});
    }

    /**
     * 跳转反确认页面
     */
    function oppositeAffirm() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法反确认！");
                return;
            }
        }

        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        var vbillstatus =  $.table.selectColumns("vbillstatus");
        for (var i = 0; i < vbillstatus.length ; i++) {
            if (vbillstatus[i] != '1' && vbillstatus[i] !== undefined) {
                $.modal.alertWarning("请选择已确认状态的委托单");
                return;
            }
        }
        if (vbillstatus.length === 0 ) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }

        var invoiceId = $.table.selectColumns("orderno").join();

        //验证发货单是否超过五天
        /*$.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    var entrustId =  $.table.selectColumns("entrustId");
                    //$.modal.open("反确认", ctx + "trustDeed/back_confirm/" + entrustId,500,300);
                    $.modal.open("反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join(),600,500);
                }
            }
        });*/

        $.modal.open("反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join(),600,500);

    }

    //业务反确认
    function pickRevocation() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        /*  var isClose = $.table.selectColumns("isClose");
          //关账判断
          for (var i = 0; i < isClose.length ; i++) {
              if(isClose == 1){
                  $.modal.alertWarning("该月份已关账，无法进行操作！");
                  return false;
              }
          }*/
        //
        var  vbillstatus = $.table.selectColumns("vbillstatus");
        //alert(vbillstatus)
        //是否回单标记
        var ifReceipt = $.table.selectColumns("ifReceipt");
        //是否回单确认标记
        var receiptConfirmFlag = $.table.selectColumns("receiptConfirmFlag");
        //发货单id
        var invoiceId = $.table.selectColumns("orderno").join();
        var lotId = $.table.selectColumns("lotId").join();


        //1:委托单反确认
        if(vbillstatus == 1) {
            //获取选中的行
            var bootstrapTable = $.btTable.bootstrapTable('getSelections');
            for (var i = 0; i < bootstrapTable.length; i++) {
                if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                    $.modal.alertWarning("分配车队的数据无法反确认！");
                    return;
                }
            }

            var isClose = $.table.selectColumns("isClose");
            //关账判断
            for (var i = 0; i < isClose.length ; i++) {
                if(isClose == 1){
                    $.modal.alertWarning("该月份已关账，无法进行操作！");
                    return false;
                }
            }
            $.modal.open("委托单反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join() + "/1",700,500);

        }else if(vbillstatus == 2){
            //提货到货回单反确认
            //验证是否可以货量更新

            //10.25修改提货反确认不做任何验证
            $.modal.open("提货反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join() + "/2",700,500);
            //到货 未回单
        }else if(vbillstatus == 3 && ifReceipt != '1'){
            if(receiptConfirmFlag == 1){  //receiptConfirmFlag == '1' 已回单确认
                $.modal.open("影像回单反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join() + "/4",700,500);
            }else{
                $.modal.open("到货反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join() + "/3",700,500);
            }
            //回单反确认
        }else if(vbillstatus == 3 && ifReceipt == '1'){ //ifReceipt == '1'  已正本回单
            $.modal.open("正本回单反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join() + "/7",700,500);
        }
            // else if(vbillstatus == 3 && receiptConfirmFlag == '1') {
            //     $.modal.open("回单反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join() + "/7",600,500);
        // }
        else{
            $.modal.alertError("委托单状态需要进行业务操作后才能进行反确认！");
        }



    }


    //删除
    function removeAll() {
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法删除！");
                return;
            }
        }

        var vbillstatus =  $.table.selectColumns("vbillstatus");
        for (var i = 0; i < vbillstatus.length ; i++) {
            if (vbillstatus[i] != '0' && vbillstatus[i] !== undefined) {
                $.modal.alertWarning("请选择待确认状态的委托单");
                return;
            }
        }
        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var lotId =  $.table.selectColumns("lotId");
        $.ajax({
            url:ctx + "payDetail/checkIfLockByLotId",
            type:"get",
            dataType:"json",
            data:{"lotId": lotId.join()},
            success: function (result) {
                if(result.code == 0){
                    $.modal.confirm("确认要删除选中的" + rows.length + "条数据吗?", function() {
                        var entrustId =  $.table.selectColumns("entrustId");
                        var data = { "entrustId": entrustId.join()};
                        $.operate.submit(ctx + "trustDeed/remove", "post", "json", data);
                    });
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });



    }

    /**
     * 委托单关闭
     */
    function entrustClose() {
        /*var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }*/

        var vbillstatus =  $.table.selectColumns("vbillstatus");


        /*for (var i = 0; i < vbillstatus.length;i++ ) {
            if ( vbillstatus[i] != '0' && vbillstatus[i] !== undefined) {
                $.modal.alertWarning("请选择待确认状态的委托单");
                return;
            }
        }*/
        if (vbillstatus.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var entrustId =  $.table.selectColumns("entrustId");
 /*       $.modal.open("关闭", ctx + "trace/close/" + entrustId,500,500);*/
        layer.open({
            type: 2, // 以 iframe 形式打开子页面
            area: ['500px', '500px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: '关闭',
            content: ctx + "trace/close/" + entrustId, // 这里请替换为实际的子页面 URL
            btn: ['关闭运单', '关闭运单及发货单' ,'关闭委托单及发货单', '取消'],
            shadeClose: true,
            yes: function (index, layero) {
                // 按钮一的点击事件处理
                var iframe = layero.find('iframe')[0];
                var subPageWindow = iframe.contentWindow;
                // 假设子页面有一个名为 getDataFromSubPage 的方法
                var result = subPageWindow.submitHandler();
                console.log('按钮一触发，子页面方法返回结果:', result);


            },
            btn2: function (index, layero) {
                // 按钮二的点击事件处理
                var iframe = layero.find('iframe')[0];
                var subPageWindow = iframe.contentWindow;
                var result = subPageWindow.submitHandler1();
                console.log('按钮二触发，子页面方法返回结果:', result);
                return false;

            },
            btn3: function (index, layero) {
                // 按钮二的点击事件处理
                var iframe = layero.find('iframe')[0];
                var subPageWindow = iframe.contentWindow;
                var result = subPageWindow.submitHandler2();
                console.log('按钮二触发，子页面方法返回结果:', result);
                return false;

            },btn4: function (index, layero) {
                layer.close(index);
            }
        });
     /*   let msg = "";

        $.ajax({
            type: "POST",
            url: prefix + "/closeInfo?entrustId="+entrustId,
            async: false,
            success: function(r) {
                let data = r.data;
                msg += "发货单号："+data.invoiceVbillno+"</br>";
                msg += "运单号："+data.lot+"</br>";
                msg += "总应收："+data.totalReceiveAmount+"元</br>";
                msg += "总应付："+data.totalPayDetailAmount+"元</br>";
                msg += "总三方："+data.totalOtherFeeAmount+"元</br>";
            }
        });
        msg += '请选择关闭类型？'


        var lock = false
        layer.confirm(msg, {
            icon: 2,
            title: msg,
            btn: ['关闭运单', '关闭运单及发货单','取消']
        }, function (index) {
            if (!lock) {
                lock = true
                $.operate.post(ctx + "trustDeed/updateStatus", { "entrustId": entrustId.join()});
                layer.close(index);
            }
        }, function(index){
            if (!lock) {
                lock = true
                $.operate.post(ctx + "trustDeed/updateStatusAll", { "entrustId": entrustId.join()});
                layer.close(index);
            }
        }, function(index){
            if (!lock) {
                lock = true
                layer.close(index);

            }
        });*/

       /* $.modal.confirm("是否确认撤销关闭？", function() {
            $.operate.post(ctx + "trustDeed/cancelCloseEntrust", { "entrustId": entrustId.join(),"vbillstatus": '0' });
        })*/

        //$.operate.post(ctx + "trustDeed/updateStatus", { "entrustId": entrustId.join()});
    };

    /**
     * 撤销关闭
     */
    function repealClose() {
        /*var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }*/

        var vbillstatus =  $.table.selectColumns("vbillstatus");

        for (var i = 0; i < vbillstatus.length;i++ ) {
            if ( vbillstatus[i] != '5' && vbillstatus[i] !== undefined) {
                $.modal.alertWarning("请选择关闭状态的委托单");
                return;
            }
        }
        if (vbillstatus.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var entrustId =  $.table.selectColumns("entrustId");
        $.modal.confirm("是否确认撤销关闭？", function() {
            $.operate.post(ctx + "trustDeed/cancelCloseEntrust", { "entrustId": entrustId.join(),"vbillstatus": '0' });
        })
    }
    //多选版
    /*function tabto(btn,status) {
        var status1 = [];
        if ($('#vbillstatus').val().trim() != '') {
            status1 = $('#vbillstatus').val().trim().split(',');
        }
        if ($(btn).attr("flag") == undefined) {
            $(".status1[flag]").removeClass("act");
            var idx = status1.indexOf(status+'')
            if (idx >= 0) {
                $(btn).removeClass("act")
                status1.splice(idx, 1)
                if (status1.length == 0) {
                    $(".status1[flag]").addClass("act")
                }
            } else {
                $(btn).addClass("act")
                status1.push(status)
            }
        } else {
            $(".status1[flag]").siblings().removeClass("act");
            $(".status1[flag]").addClass("act")
            status1 = []
        }
        $('#vbillstatus').val(status1.join(','))
        searchPre()
    }*/
    // 单选版
    function tabto(btn,status) {
        if (status == null) {
            $("#vbillstatus").val('');
        } else {
            $("#vbillstatus").val(status);
        }
        $(".status1").removeClass("act");
        $(btn).addClass("act")
        searchPre()
    }

    function formatTableUnit(value, row, index) {
        return {
            css: {
                "white-space": "nowrap",
                "text-overflow": "ellipsis",
                "overflow": "hidden",
                "max-width": "200px"
            }
        }
    }

    function formatterInformation(value, row, index){
        console.log(row)
        let htmlTextT='';
        var now = new Date();
        var isMultiple = row.isMultiple;

        let reqDeliDate=new Date(Date.parse(row.reqDeliDate))

        if (reqDeliDate < now && (row.vbillstatus == toAffirmstatus||row.vbillstatus == affirmStatus)) {
            reqDeliDate= `<span class="label label-danger">` + row.reqDeliDate.substr(0,10) + `</span>`;
        } else {
            reqDeliDate= row.reqDeliDate.substr(0,10);
        }

        let reqArriDate=new Date(Date.parse(row.reqArriDate));

        //如果到货时间小于当前时间，并且状态等于待调度状态，背景则标红
        if (reqArriDate < now && (row.vbillstatus == toAffirmstatus||row.vbillstatus == affirmStatus||row.vbillstatus == 2)) {
            reqArriDate= `<span class="label label-danger">` + row.reqArriDate.substr(0,10) + `</span>`;
        } else {
            row.reqArriDate ? reqArriDate= row.reqArriDate.substr(0,10) : null;
        }

        if(isMultiple == 0) {
            let str = ""
            let strT = ""
            if(row.deliAddrName){
                str+= '('+row.deliAddrName+')';
            }
            if(row.deliProName){
                str+=row.deliProName;
            }
            if(row.deliCityName&&row.deliCityName != '市辖区'){
                str+=row.deliCityName;
            }
            if(row.deliAreaName){
                str+=row.deliAreaName;
            }
            if(row.deliDetailAddr){
                str+=row.deliDetailAddr;
            }

            let caAdd = ''
            if(row.multipleShippingAddressList) {
                let list = row.multipleShippingAddressList;
                for (const elem of list) {
                    if (elem.addressType === 1 && elem.isChangeAddress === 1) {
                        caAdd = `<span class="label label-primaryT ml5"
                              style="padding:2px;cursor: pointer;" data-toggle="tooltip"
                              data-placement="left" data-html="true"
                              title="(${elem.caArriAddrName})${elem.caArriProName}${elem.caArriCityName}${elem.caArriAreaName}${elem.caArriDetailAddr}">原</span>`
                    }
                }
            }

            if(row.arriAddrName){
                strT+= '('+row.arriAddrName+')';
            }
            if(row.arriProName){
                strT+=row.arriProName;
            }
            if(row.arriCityName&&row.arriCityName != '市辖区'){
                strT+=row.arriCityName;
            }
            if(row.arriAreaName){
                strT+=row.arriAreaName;
            }
            if(row.arriDetailAddr){
                strT+=row.arriDetailAddr;
            }

            htmlTextT+= `<div><span class="label label-warning pa2">装</span>` + reqDeliDate + `<div class="ml5" style="display: inline-block;vertical-align: middle;">`+$.table.tooltip(str,14)+`</div></div>`;
            htmlTextT+= `<div class=""><span class="label label-success pa2">卸</span>`+reqArriDate+ caAdd + `<div class="ml5" style="display: inline-block;vertical-align: middle;">`+$.table.tooltip(strT,14)+`</div></div>`;

        }else {
            if(row.multipleShippingAddressList){
                let list = row.multipleShippingAddressList;
                for (const elem of list) {
                    let str = ""
                    if (elem.addressType === 0) {
                        let deliGoodsName =  elem.shippingGoodsList.map(x => {
                            return x.goodsName + (x.num != 0 ? x.num+'件|' : '')+ (x.weight != 0 ? x.weight+'吨|' : '')+ (x.volume != 0 ? x.volume+'m³' : '');
                        }).join(",")

                        if(elem.addrName){
                            str+= '('+elem.addrName+')';
                        }
                        if(elem.provinceName){
                            str+=elem.provinceName;
                        }
                        if(elem.cityName&&elem.cityName != '市辖区'){
                            str+=elem.cityName;
                        }
                        if(elem.areaName){
                            str+=elem.areaName;
                        }
                        if(elem.detailAddr){
                            str+=elem.detailAddr;
                        }
                        if(deliGoodsName){
                            str+= '('+deliGoodsName+')';
                        }
                        htmlTextT+= `<div><span class="label label-warning pa2">装</span>` + reqDeliDate + `<div class="ml5" style="display: inline-block;vertical-align: middle;">`+$.table.tooltip(str,14)+`</div></div>`;
                    }
                }

                for (const elem of list) {
                    let str = ""
                    if(elem.addressType === 1){
                        let arriGoodsName =  elem.shippingGoodsList.map(x => {
                            return x.goodsName + (x.num != 0 ? x.num+'件|' : '')+ (x.weight != 0 ? x.weight+'吨|' : '')+ (x.volume != 0 ? x.volume+'m³' : '');
                        }).join(",")

                        if(elem.addrName){
                            str+= '('+elem.addrName+')';
                        }
                        if(elem.provinceName){
                            str+=elem.provinceName;
                        }
                        if(elem.cityName&&elem.cityName != '市辖区'){
                            str+=elem.cityName;
                        }
                        if(elem.areaName){
                            str+=elem.areaName;
                        }
                        if(elem.detailAddr){
                            str+=elem.detailAddr;
                        }
                        if(arriGoodsName){
                            str+= '('+arriGoodsName+')';
                        }
                        let caAdd = ''

                        if (elem.isChangeAddress === 1) {
                                caAdd = `<span class="label label-primaryT ml5"
                          style="padding:2px;cursor: pointer;" data-toggle="tooltip"
                          data-placement="bottom" data-html="true"
                          title="(${elem.caArriAddrName})${elem.caArriProName}${elem.caArriCityName}${elem.caArriAreaName}${elem.caArriDetailAddr}">原</span>`
                        }


                        htmlTextT+= `<div class="mt5"><span class="label label-success pa2">卸</span>`+reqArriDate+caAdd+ `<div class="ml5" style="display: inline-block;vertical-align: middle;">`+$.table.tooltip(str,14)+`</div></div>`;
                    }
                }

            }
        }

        return htmlTextT;

    }

    function editCarrierAdmin(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        $.modal.open("承运商修改", ctx + "trustDeed/editCarrierAdmin/" + rows.join(),700,500);
    }

    function ontooltipName(data) {

        let list=JSON.parse(data.trim());
        if( list == null || list=='' || list.length==0 ){
            return '';
        }

        let html=''
        html+=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'><div class='vertical-container light-timeline'>`;

        list.forEach((item,i)=>{
            if(i<3){
                if(item.estArrivalTime!=null){
                    if(item.trackingMemo!=null){
                        html+= `<div class='vertical-timeline-block leftIcon'>
                        <div class='vertical-timeline-icon'></div>
                        <div class='vertical-timeline-content'>
                            <div class=''> `+item.trackingTime+` </div>
                            <div class='' style='white-space: initial;''> `+ (item.proName?item.proName:'') + (item.cityName?item.cityName:'') + (item.areaName?item.areaName:'') +(item.detailAddr?item.detailAddr:'')+`</div>
                            <div class='atime'>`+item.estArrivalTime+`(预计到达时间) </div>
                            <div class='note'>备注：`+item.trackingMemo+` </div>
                        </div>
                    </div>`;
                    }else{
                        html+= `<div class='vertical-timeline-block leftIcon'>
                    <div class='vertical-timeline-icon'></div>
                    <div class='vertical-timeline-content'>
                        <div > `+item.trackingTime+` </div>
                        <div style='white-space: initial;''> `+ (item.proName?item.proName:'') + (item.cityName?item.cityName:'') + (item.areaName?item.areaName:'') +(item.detailAddr?item.detailAddr:'')+`</div>
                        <div class='atime'>`+item.estArrivalTime+` (预计到达时间)</div>
                    </div>
                </div>`;
                    }
                }else{
                    html+= `<div class='vertical-timeline-block leftIcon'>
                    <div class='vertical-timeline-icon'></div>
                    <div class='vertical-timeline-content'>
                        <div > `+item.trackingTime+` </div>
                        <div style='white-space: initial;''> `+ (item.proName?item.proName:'') + (item.cityName?item.cityName:'') + (item.areaName?item.areaName:'') +(item.detailAddr?item.detailAddr:'')+`</div>
                    </div>
                </div>`;
                }

            }
        })

        html+=`</div></div></div></div>`;
        return html
    }

    function pickPush(entrustWorkId) {
        if(entrustWorkId !='' && entrustWorkId !='null'){
            $.operate.saveModal(ctx + "trace/pick_arri_push", {entrustWorkId});
        }else{
            $.modal.alertWarning("错误数据请刷新后重试");
        }
    }
    function inviteDriver() {
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        let driverId=""
        for (var i = 0; i < bootstrapTable.length; i++) {
            driverId=bootstrapTable[i]["driverId"]
        }
        if(driverId!=''){
            $.operate.saveTab(ctx + "trace/invite_driver", {driverId});
        }else{
            $.modal.alertWarning("未找到司机数据无法邀请授权！");
        }


    }
    //导出在途
    function exportTraceExcel() {
        /*var options = {
            title: '在途筛选',
            width: "600",
            height: "420",
            url: prefix + "/toExport",
            btn: ['确定导出', '关闭'],
            //callBack: doSubmit
        };*/

        $.modal.open("跟踪表导出", prefix + "/toExport",'800', '490');
        var ele = document.getElementsByClassName("layui-layer-btn0")[0];
        ele.innerText = "确定导出";

    }

    function exportTraceSelectExcel(){
        var entrustIds = $.table.selectColumns("entrustId");
        if(entrustIds.length == 0){
            $.modal.alertWarning("请勾选一条单据");
            return;
        }
        $.modal.open("跟踪表导出", prefix + "/toExport?entrustIds="+entrustIds.join(","),'800', '490');
        var ele = document.getElementsByClassName("layui-layer-btn0")[0];
        ele.innerText = "确定导出";
    }


    function scanCode(entrustLotId,driverName,driverMobile,carno,carrierName,carrierPhone,invoiceVbillno,carrName,driverCardId,carrCardId) {
        layer.open({
            type: 1,
            area: ['30%', '440px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "畅运通运单详情二维码",
            content: $("#scanCodeHtml").html(),
            btn: ['关闭'],
            shadeClose: true,
            success: function (layero, index) {
                $("#scanCodeImg").attr("src",ctx + "trace/generateQrCode?entrustLotId="+entrustLotId)
                if(invoiceVbillno&&invoiceVbillno!='null'){
                    $("#invoiceVbillnoInfo").text(invoiceVbillno)
                    $("#invoiceVbillnoInfo").parent().css('display', 'block')
                }else{
                    $("#invoiceVbillnoInfo").parent().css('display', 'none')
                }
                if(driverCardId==carrCardId){
                    $("#carrInfo").parent().css('display', 'none');
                }else{
                    $("#carrInfo").text(carrName);
                }
                if((driverName||driverMobile||carno)&&(driverName!='null'||driverMobile!='null'||carno!='null')){
                    let driverInfo=[]
                    if(driverName&&driverName!='null'){
                        driverInfo.push(driverName)
                    }
                    if(driverMobile&&driverMobile!='null'){
                        driverInfo.push(driverMobile)
                    }
                    if(carno&&carno!='null'){
                        driverInfo.push(carno)
                    }

                    if(driverInfo.length!=0){
                        $("#driverInfo span").text(driverInfo.join('/'))
                        $("#driverInfo").css('display', 'block')
                    }else{
                        $("#driverInfo").css('display', 'none')
                    }

                    $("#carrierInfoT").css('display', 'none')
                }else{
                    let carrierList=[]
                    if(carrierName&&carrierName!='null'){
                        carrierList.push(carrierName)
                    }
                    if(carrierPhone&&carrierPhone!='null'){
                        carrierList.push(carrierPhone)
                    }
                    if(carrierList.length!=0){
                        $("#carrierInfoT").text(carrierList.join('/'))
                        $("#carrierInfoT").css('display', 'block')
                    }else{
                        $("#carrierInfoT").css('display', 'none')
                    }

                    $("#driverInfo").css('display', 'none')

                }
            }
        })

    }

    function getCheckList(spNo,entrustCostId,regDate,htmlText) {
        let html="";
        html+=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'><div class='vertical-container light-timeline'>`;

        /*if(entrustCostId){
            $.ajax({
                url: ctx + "trace/entrust_cost_check/list",
                method: 'post',
                dataType: "json",
                data: {entrustCostId},
                async : false,
                success: function (result) {
                    if(result.length>0){
                        result.forEach(item=>{
                            html+=`<div class='vertical-timeline-block leftIcon'>
                                    <div class='vertical-timeline-icon'>
                                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                                    </div>
                                    <div class='vertical-timeline-content'>
                                        <div class='flex'>
                                            <div style='display: inline-block;white-space: nowrap;'>`+(item.checkType==0?`客服主管审核`:`运营部长审核`)+`: `+(item.checkStatus==0?`<span class='label label-danger'>审核不通过</span>`:`<span class='label label-success'>审核通过</span>`)+`</div>
                                            <div class='fw f18'>审核人: `+item.checkUserName+`</div>
                                            <div class='fw f18'>
                                                备注: `+(item.memo== null?'暂无数据':item.memo)+`
                                            </div>
                                            <div style='display: inline-block;white-space: nowrap;'>`+item.checkDate+`</div>
                                        </div>
                                        <div class='mt10' style='white-space: initial;'></div>
                                    </div>

                                </div>`;
                        })
                    }
                }
            });
        }*/
        html+=`<div class='vertical-timeline-block leftIcon'>
                <div class='vertical-timeline-icon'>
                    <img src='/img/pep.png' style='width: 100%;height: 100%'>
                </div>
                <div class='vertical-timeline-content'>
                    <div class='flex'>
                        <div class='fw f18 mt10' style='display: inline-block;white-space: nowrap;'>`+htmlText+`</div>
                        <div style='display: inline-block;white-space: nowrap;'>`+regDate+`</div>
                    </div>
                    <div class='mt10' style='white-space: initial;'></div>
                </div>
            </div>`;
        if (spNo) {
            showWecomProcess(spNo, list => {
                for (let i = 0; i < list.length; i++) {
                    let item = list[i];
                    let details = item.details;
                    for (let j = 0; j < details.length; j++) {
                        let t = null;
                        if (item.sp_status==3) {
                            t = "<span className='label label-danger'>审核不通过</span>";
                        } else if (item.sp_status==2) {
                            t = "<span class='label label-success'>审核通过</span>";
                        } else if (item.sp_status == 1) {
                            t = "<span class='label label-default'>审核中</span>";
                        } else {
                            t = "<span class='label label-default'>未知：" + item.sp_status + "</span>";
                        }
                        html+=`<div class='vertical-timeline-block leftIcon'>
                                    <div class='vertical-timeline-icon'>
                                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                                    </div>
                                    <div class='vertical-timeline-content'>
                                        <div class='flex'>
                                            <div style='display: inline-block;white-space: nowrap;'>${t}</div>
                                            <div class='fw f18'>审核人: ${item.details[j].approver.userid}</div>
                                            <div class='fw f18'>
                                                备注: ${item.speech?item.speech:'暂无数据'}
                                            </div>
                                            <div style='display: inline-block;white-space: nowrap;'>${details[j].sptime && new Date(details[j].sptime * 1000).pattern("yyyy-MM-dd HH:mm")}</div>
                                        </div>
                                    </div>
                                </div>`;
                    }

                }
            }, true)
        }
        html+=`</div></div></div></div>`;
        return html


    }

    function tqQrCode(entrustId) {
        var html = []
        html.push(`<div class="col-md-12"style="text-align:center;">
                <span  style="font-size:14px;">将二维码<span style="color: #18a689;">“截图”</span>发送给承运方，用 <i class="fa fa-weixin" style="font-size:18px;color:#09BB07;"></i> <span style="color: #09BB07;">微信</span>扫码打开</span>
            </div>
            <div class="col-md-12" style="text-align:center;padding:30px;">`);
        html.push("<div id='qrcode'></div>")
        html.push('</div>')
        html.push(`<div class="col-md-12 fw" style="text-align:center;font-size:18px">绑定小黑卡二维码</div>`)
        html.push(`<div class="col-md-12 fw" style="text-align:center;font-size:18px" id="binded"></div>`)
        layer.open({
            type: 1,
            area: ['500px', '440px'],
            shade: 0.3,
            title: "绑定小黑卡二维码",
            content: html.join(''),
            btn: ['关闭'],
            shadeClose: false,
            success: function (layero, index) {
                // 读取已绑的卡
                $.ajax({
                    url: ctx + "tq/binded/" + entrustId,
                    cache: false,
                    success: function (result) {
                        if (result.code == 0) {
                            if (result.data && result.data.imei) {
                                $("#binded").text("已绑卡: " + result.data.imei);
                            } else {
                                $("#binded").text("尚未绑卡");
                            }
                        } else {
                            $.modal.msgError(result.msg());
                        }
                    }
                });
                var url = "https://tms.qixin56.com/tq/i/" + entrustId;
                var qrCodeSize = 160;
                $("#qrcode").qrcode({
                    render: "canvas",
                    text: url,
                    width: qrCodeSize,               //二维码的宽度
                    height: qrCodeSize,              //二维码的高度
                    background: "#fff",       //二维码的后景色
                    foreground: "#000"        //二维码的前景色
                });
                var canvas = $("#qrcode").find("canvas").eq(0)[0];
                console.log(canvas)
                var c = canvas.getContext('2d');
                var logo = new Image();
                logo.src = ctx + "img/logo.png";
                logo.onload = function(event) {
                    console.log('%O', logo)
                    var width = 46, height = 46;
                    var top = (qrCodeSize - height) / 2,left = (qrCodeSize - width) / 2;
                    c.clearRect(left, top, width, height)
                    c.drawImage(this, left, top, width, height)
                }
            }
        })
    }

    function getPhone(phone) {

        var data = {};
        data.phone = phone;
        $.ajax({
            url:ctx + "trace/callOut",
            type:"post",
            dataType:"json",
            data:data,
            success: function (result) {
                console.log(result);
                if(result.code == 0){
                    $.modal.alertSuccess(result.msg);
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });
    }

    function bookingSend(entrustId){
        var title = '预约送货';
        var url = ctx + "trace/bookingSend?entrustId=" + entrustId;
        $.modal.open(title, url, '600', '500');
    }

    function install(entrustId){
        var title = '安装跟进';
        var url = ctx + "trace/install?entrustId=" + entrustId;
        $.modal.open(title, url, '900', '600');
    }

    function mailTrace(mailId) {
        window.open("https://www.baidu.com/s?wd="+encodeURI("快递单号查询")+mailId,"baidu")
    }

    function changeAddress(entrustId){
        $.ajax({
            url:ctx + "trace/checkIfOneAddrByEntrustId",
            type:"post",
            dataType:"json",
            data:{"entrustId": entrustId},
            success: function (result) {
                if(result.code == 0){
                    var title = '地址调整';
                    var url = ctx + "trace/changeAddress?entrustId=" + entrustId;
                    var height = document.documentElement.clientHeight - 50;
                    var width = document.documentElement.clientWidth - 320;
                    $.modal.open(title, url, width, height);
                }else{
                    $.modal.alertError("单据涉及多个货源，无法进行地址调整");
                }
            }
        });

    }

    function sunshineBackRecord(){
        var url = ctx + "trace/sunshineBackRecord";
        $.modal.openTab("阳光电源操作记录", url);
    }

    function zjhyRecord(){
        var url = ctx + "trace/zjhyRecord";
        $.modal.openTab("浙江恒逸抓单记录", url);
    }


    function zllgBackRecord(){
        var url = ctx + "trace/zllgBackRecord";
        $.modal.openTab("中粮面业操作记录", url);
    }

    function xchdRecord() {
        var url = ctx + "ext/sinochem/index";
        $.modal.openTab("中化推送回单记录", url);
    }


    function ontooltipName1(data) {
        let html=''
        html+=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'><div class='vertical-container light-timeline'>`;
        if(data.adjustUserName){
            html+=`<div class='vertical-timeline-block leftIcon'>
                                    <div class='vertical-timeline-icon'>
                                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                                    </div>
                                    <div class='vertical-timeline-content'>
                                        <div class='flex'>
                                            <div style='display: inline-block;white-space: nowrap;'</div>
                                            <div class='fw f18'>`+data.adjustUserName+`(申请)</div>
                                            <div class='fw f18'>
                                               `+data.adjustDate+`
                                            </div>
                                            <div style='display: inline-block;white-space: nowrap;'></div>
                                        </div>
                                    </div>
                                </div>`;
        }


                if(data.firstCheckUserName){

                    html+=`<div class='vertical-timeline-block leftIcon'>
                                    <div class='vertical-timeline-icon'>
                                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                                    </div>
                                    <div class='vertical-timeline-content'>
                                        <div class='flex'>
                                            <div style='display: inline-block;white-space: nowrap;'</div>
                                            <div class='fw f18'>`+data.firstCheckUserName+`(业务审核)</div>
                                            <div class='fw f18'>
                                               `+data.firstCheckDate+`
                                            </div>
                                            <div style='display: inline-block;white-space: nowrap;'></div>
                                        </div>
                                    </div>
                                </div>`;



                }else{
                    html+=`<div class='vertical-timeline-block leftIcon'>
                                    <div class='vertical-timeline-icon'>
                                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                                    </div>
                                    <div class='vertical-timeline-content'>
                                        <div class='flex'>
                                            <div style='display: inline-block;white-space: nowrap;'</div>
                                            <div class='fw f18'>待业务审核</div>
                                            <div class='fw f18'>
                                            </div>
                                            <div style='display: inline-block;white-space: nowrap;'></div>
                                        </div>
                                    </div>
                                </div>`;
                }

        if(data.secondCheckUserName){

            html+=`<div class='vertical-timeline-block leftIcon'>
                                    <div class='vertical-timeline-icon'>
                                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                                    </div>
                                    <div class='vertical-timeline-content'>
                                        <div class='flex'>
                                            <div style='display: inline-block;white-space: nowrap;'</div>
                                            <div class='fw f18'>`+data.secondCheckUserName+`(总经办审核)</div>
                                            <div class='fw f18'>
                                               `+data.secondCheckDate+`
                                            </div>
                                            <div style='display: inline-block;white-space: nowrap;'></div>
                                        </div>
                                    </div>
                                </div>`;

        }else{
            html+=`<div class='vertical-timeline-block leftIcon'>
                                    <div class='vertical-timeline-icon'>
                                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                                    </div>
                                    <div class='vertical-timeline-content'>
                                        <div class='flex'>
                                            <div style='display: inline-block;white-space: nowrap;'</div>
                                            <div class='fw f18'>待总经办审核</div>
                                            <div class='fw f18'>
                                            </div>
                                            <div style='display: inline-block;white-space: nowrap;'></div>
                                        </div>
                                    </div>
                                </div>`;
        }
        if(data.thirdCheckUserName){
            html+=`<div class='vertical-timeline-block leftIcon'>
                                    <div class='vertical-timeline-icon'>
                                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                                    </div>
                                    <div class='vertical-timeline-content'>
                                        <div class='flex'>
                                            <div style='display: inline-block;white-space: nowrap;'</div>
                                            <div class='fw f18'>`+data.thirdCheckUserName+`(财务审核)</div>
                                            <div class='fw f18'>
                                               `+data.thirdCheckDate+`
                                            </div>
                                            <div style='display: inline-block;white-space: nowrap;'></div>
                                        </div>
                                    </div>
                                </div>`;


        }else{
            html+=`<div class='vertical-timeline-block leftIcon'>
                                    <div class='vertical-timeline-icon'>
                                        <img src='/img/pep.png' style='width: 100%;height: 100%'>
                                    </div>
                                    <div class='vertical-timeline-content'>
                                        <div class='flex'>
                                            <div style='display: inline-block;white-space: nowrap;'</div>
                                            <div class='fw f18'>待财务审核</div>
                                            <div class='fw f18'>
                                            </div>
                                            <div style='display: inline-block;white-space: nowrap;'></div>
                                        </div>
                                    </div>
                                </div>`;
        }

        html+=`</div></div></div></div>`;
        return html
    }
</script>
</body>
</html>