<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('发货单明细')"/>
    <th:block th:include="include :: animate-css" />
    <th:block th:include="include :: iconfont-css" />

</head>
<style>
    table th {
        text-align: center;
        color: #272727;
    }
    table tr {
        text-align: center;
        color: #272727;
        font-size: 14px;
    }

    .strong{
        font-weight: 600;
    }
    .h4 {
        font-weight: 600;
        font-size: 18px;
        color: #272727;
    }
    .bq-1 {
        border: 2px solid #ddd;
        border-radius: 50px;
        padding: 3px 11px;
        display: inline-block;
    }
    .line20T{
        text-decoration:line-through;
        color: #666666;
    }
    /*.zt-success::before{*/
    /*    content: '';*/
    /*    display: inline-block;*/
    /*    width: 18px;*/
    /*    height: 18px;*/
    /*    margin-right: 8px;*/
    /*    border: 5px solid #1ab394;*/
    /*    border-radius: 50%;*/
    /*    vertical-align: middle;*/
    /*    margin-bottom: 2px;*/
    /*}*/
    /*.zt-success {*/
    /*    color: #1ab394;*/
    /*    font-size: 15px;*/
    /*    display: inline-block;*/
    /*}*/

     .bq-2 {
         display: inline-block;
         padding: 3px 7px;
         background-color: #FFF1E1;
         color: #B06912;
         margin-right: 5px;
         /*font-weight: 600;*/
     }
     .bq-3 {
         display: inline-block;
         padding: 3px 7px;
         background-color: #F0F0F0;
         margin-right: 5px;
         /*font-weight: 600;*/
     }
     .bq-4 {
         display: inline-block;
         padding: 3px 7px;
         background-color: #F6E6E6;
         color: #731919;
         margin-right: 5px;
         /*font-weight: 600;*/
     }

     .bq-5{
         border: 1px solid #731919;
         border-radius: 4px;
         padding: 0px 7px;
         display: inline-block;
     }
    .dashed-button {
        border: 1px dashed #337ab7; /* 设置虚线边框颜色为蓝色 (#337ab7) */
        background-color: #337ab7; /* 设置背景色为蓝色 (#337ab7) */
        color: #fff; /* 设置文字颜色为白色 */
        padding: 5px 10px; /* 设置内边距 */
        border-radius: 5px; /* 设置边框圆角 */
    }


    .timeline-badge {
        color: #fff;
        border-radius: 10%;
    }
    .timeline-badge.primary {
        background-color: #2e6da4 !important
    }

    .timeline-badge.success {
        background-color: #1ab394 !important
    }

    .timeline-badge.warning {
        background-color: #f8ac59 !important
    }

    .timeline-badge.danger {
        background-color: #d9534f !important
    }

    .timeline-badge.info {
        background-color: #5bc0de !important
    }

    .clear {
        clear: both
    }
    .timeline-box {
        background: #fff;
        padding: 20px 8px;
        position: relative;
        /*opacity: 0.7;*/
    }
    .timeline-main {
        position: relative;
    }
    .timeline-main>h1 {
        font-size: 18px;
        background: #fff;
        /*z-index: 1;*/
        position: relative;
        color: #484348;
        margin-left: 10%;
        margin-left: -moz-calc(15% - 7px);
        margin-left: -webkit-calc(15% - 7px);
        margin-left: calc(15% - 7px)
    }
    .timeline-main>h1>i {
        padding-right: 10px;
        font-size: 20px
    }
    .timeline-main>h1>span {
        display: none
    }
    .timeline-main h2,
    .timeline-main h3 {
        width: 8%;
        text-align: right
    }
    .timeline-main h2,
    .timeline-main h2>a {
        font-size: 16px;
        margin: 5px 0;
        color: #0084FF
    }
    .timeline-main h3,
    .timeline-main h3>a {
        font-size: 14px;
        margin: 2px 0;
        color: #ff5722
    }
    /*.timeline-month>ul>li {*/
    /*    padding: 10px 0*/
    /*}*/
    .timeline-month>ul>li .h4 {
        display: inline-block;
        width: 8%;
        text-align: right;
        float: left
    }
    .date {
        display: inline-block;
        padding: 2px 0px;
        color: #484348;
        font-size: 15px;
        margin-top: 5px
    }
    .dot-circle {
        color: #484348;
        width: 8%;
        text-align: center;
        font-size: 27px;
        /*z-index: 1;*/
        position: relative;
        background: #fff;
        float: left
    }
    .content {
        width: 100%;
        max-width: 86%;
        float: left;
        padding: 5px;
        margin-left: 10px;
        position: relative;
        /*z-index: 1;*/
        /*background: #484348;*/
        /*color: #fff;*/
        /*background: #ddd;*/
        color: #333
    }
    .content img {
        width: 100%
    }
    .content::before {
        position: absolute;
        left: -20px;
        top: 6px;
        height: 0;
        width: 0;
        content: '';
        border: 10px solid rgba(255, 255, 255, 0);
        border-top: 6px solid rgba(255, 255, 255, 0);
        border-bottom: 6px solid rgba(255, 255, 255, 0);
        /*border-right-color: #484348;*/
        /*border-right-color: #ddd;*/
    }
    .content .title-1 {
        font-weight: 600;
        font-size: 16px;
        color: #272727;
    }
    .content .title-2 {
        font-weight: 500;
        font-size: 15px;
        color: #272727;
    }
    .content .lab {
        font-size: 15px;
        color: #272727;
        margin-right: 15px;
    }
    .timeline-line {
        position: absolute;
        left: 15%;
        top: 0;
        height: 100%;
        width: 2px;
        background: #484348;
        z-index: 0
    }
    .timeline-year {
        margin: 0px 0
    }
    @media(min-width:768px) {
        .timeline-box {
            background: #fff;
            padding: 0px 0px;
            position: relative;
        }
        .timeline-main>h1 {
            font-size: 26px;
            margin-left: 8%;
            margin-left: -moz-calc(10% - 13px);
            margin-left: -webkit-calc(10% - 13px);
            margin-left: calc(10% - 13px)
        }
        .timeline-main>h1>i {
            font-size: 30px
        }
        .timeline-main>h1>span {
            display: inline
        }
        .timeline-main h2,
        .timeline-main h3 {
            width: 8%
        }
        .timeline-main h2,
        .timeline-main h2>a {
            font-size: 24px
        }
        .timeline-main h3,
        .timeline-main h3>a {
            font-size: 20px
        }
        .timeline-month>ul>li .h4 {
            width: 8%
        }
        .dot-circle {
            width: 4%;
            font-size: 27px
        }
        .content {
            max-width: 86%
        }
        .timeline-line {
            left: 10%
        }
    }
    @media(min-width:992px) {
        .timeline-main>h1 {
            font-size: 34px;
            background: #fff;
            z-index: 1;
            position: relative;
            color: #484348;
            margin-left: 12%;
            margin-left: -moz-calc(10% - 16px);
            margin-left: -webkit-calc(10% - 16px);
            margin-left: calc(10% - 16px)
        }
        .timeline-main>h1>i {
            font-size: 36px
        }
        .timeline-main h2,
        .timeline-main h2>a {
            font-size: 24px
        }
        .timeline-main h3,
        .timeline-main h3>a {
            font-size: 24px
        }
    }
    .pickup-label {
        width: 8%;
        letter-spacing: 4px;
        position: absolute;
        /*transform: translate(108px,47px); !* 将元素垂直居中 *!*/
        color: #1ab394;
        font-size: 22px;
        writing-mode: vertical-rl; /* 垂直排列文字 */
        text-orientation: mixed; /* 控制文字方向 */
        margin: 0 5px; /* 调整间距 */
        padding-top: 30%;

    }
    .timeline-month {
        position: relative; /* 将父元素相对定位 */
    }
    .video-container {
        position: relative;
        display: inline-block;
        width: 85px; /* 设置正方形容器的宽度 */
        height: 85px; /* 设置正方形容器的高度 */
        overflow: hidden; /* 隐藏溢出的内容 */
    }



    .play-button {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
        z-index: 1;
    }
    .play-button img {
        width: 50%; /* 设置播放按钮图标的宽度 */
        height: 50%; /* 设置播放按钮图标的高度 */
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    .video {
        width: 100% !important; /* 设置视频宽度为容器的宽度 */
        height: 100% !important;  /* 让视频的高度自动调整，以保持宽高比 */
    }
</style>
<body>
<div class="form-content">
    <div class="layui-panel">
        <div style="padding:15px;">
            <div class="row no-gutter">
                <div class="col-md-12">
                    <span class="h4">[[${client.custAbbr}]] / [[${client.custName}]]</span>
                    <span class="m-l-md bq-1">结算客户</span>
                    <span class="m-l-md">
                        <span class="iconfont icon-kehu-blue" style="font-size: 2em;vertical-align: middle;margin-right: 6px;" title=""></span>
                    </span>
                    <span class="m-l-md">
                        <span class="iconfont icon-qiyong"
                              style="font-size: 19px;vertical-align: middle;margin-right: 6px;color: #1ab394;"
                              th:if="${client.isEnabled} == 0">启用</span>
                        <span class="iconfont icon-tingyong"
                              style="font-size: 19px;vertical-align: middle;margin-right: 6px;color: #d81e06;"
                              th:if="${client.isEnabled} == 1">停用</span>
                    </span>
                </div>
                <div class="col-md-12 mt5">
                    <span class="h4" th:each="dict : ${@dict.getType('bala_corp')}"
                          th:if="${dict.dictValue} == ${client.balaCorp}"
                          th:text="${dict.dictLabel}">江苏铭源</span>
                    <span class="m-l-md bq-1">结算公司</span>
                </div>
                <div class="col-md-12 mt5">
                    <span class="bq-2"
                          th:each="mapS,status:${balanceDept}"
                          th:if="${mapS.deptId} == ${client.balaDept}"
                          th:text="|（结算组）${mapS.deptName}|"></span>
<!--                    <span class="bq-2"-->
<!--                          th:each="salesGroup : ${salesGroupList}"-->
<!--                          th:if="${client.salesId == salesGroup.id}"-->
<!--                          th:text="|（运营部）${salesGroup.salesName}|"></span>-->

<!--                    <span class="bq-2"-->
<!--                          th:each="mapS,status:${salesDept}"-->
<!--                          th:if="${client.salesDept}==${mapS.deptId} "-->
<!--                          th:text="|（运营组）${mapS.deptName}|"></span>-->

                    <span class="bq-2" th:text="|（运营组）${custSalesDept}|"></span>
                    <span class="bq-2" th:text="|（运营部）${custSalesName}|"></span>
                    <span class="bq-2" th:text="|（管理部）${mgmtDeptName}|"></span>

                    <span class="bq-2"
                          th:each="mapS,status:${stationDept}"
                          th:if="${client.stationDept}==${mapS.deptId} "
                          th:text="|（驻场组）${mapS.deptName}|"></span>
                </div>
                <div class="col-md-12 mt5">
                    <span class="bq-3"
                          th:if="${client.crtGuidePrice == 1}">设置一般指导价</span>
                    <span class="bq-3"
                          th:if="${client.isSpecialReferencePrice == 1}">设置特殊指导价</span>
                    <span class="bq-3"
                          th:if="${client.isNeedReceipt == 1}">需要回单</span>
                    <span class="bq-3"
                          th:if="${client.ifBargain == 1}">议价</span>
                    <span class="bq-3">设置指导价</span>
                </div>
<!--                <div class="col-md-12 mt5">-->
<!--                    <span >-->
<!--                        <button class="btn btn-default dashed-button">查看开票信息</button>-->
<!--                    </span>-->
<!--                    <span>-->
<!--                         <button class="btn btn-default dashed-button">查看时效信息</button>-->
<!--                    </span>-->
<!--                    <span>-->
<!--                         <button class="btn btn-default dashed-button">查看运作要求</button>-->
<!--                    </span>-->
<!--                </div>-->
            </div>
        </div>
    </div>

    <div class="layui-panel mt10" >
        <div style="padding:15px;">
            <div class="row no-gutter">
                <div class="col-md-12">
                    <span class="h4">[[${invoice.vbillno}]]</span>
                    <span class="m-l-md">
                        <span class="label label-primary label-large"
                              th:each="dict : ${invoiceStatusList}"
                              th:if="${dict.value} eq ${invoice.vbillstatus}"
                              th:text="${dict.context}"></span>
                    </span>
<!--                    <span class="m-l-md">-->
<!--                        <span class="label label-primary label-large">已对账</span>-->
<!--                    </span>-->
<!--                    <span class="m-l-md strong">-->
<!--                        管理员 2023-09-13 10:08:51-->
<!--                    </span>-->
                    <span class="m-l-md strong" style="float: right;">
                        创建人 / 时间：[[${invoice.regUserName}]] [[${#dates.format(invoice.regDate, 'yyyy-MM-dd HH:mm:ss')}]]
                    </span>
                </div>
                <div class="col-md-12 mt5">
                    <span class="bq-2">[[${#dates.format(invoice.reqDeliDate, 'yyyy-MM-dd HH')}]]点前提货</span>

                    <span class="bq-2">[[${invoice.carLenName}]]米[[${invoice.carTypeName}]]</span>

                    <span class="bq-2"
                          th:each="dict : ${@dict.getType('trans_code')}"
                          th:if="${dict.dictValue} == ${invoice.transCode}"
                          th:text="${dict.dictLabel}"></span>

                    <span class="bq-2" th:if="${invoice.isOversize == 0}">无大件</span>
                    <span class="bq-2" th:if="${invoice.isOversize == 1}">大件运输</span>

                    <span class="bq-2" th:if="${invoice.isCustomsClearance == 1}">报关</span>

                    <span class="bq-2">[[${invoice.transLineName}]]</span>

                    <span class="bq-2" th:text="|（运营组）${salesDept}|"></span>
                    <span class="bq-2" th:text="|（运营部）${invoice.custSalesName}|"></span>
                    <span class="bq-2" th:text="|（管理部）${invoice.mgmtDeptName}|"></span>

                    <span class="bq-2"
                          th:each="dict : ${@dict.getType('bala_corp')}"
                          th:if="${dict.dictValue} == ${invoice.balaCorpId}"
                          th:text="|结算公司：${dict.dictLabel}|"></span>
                    <span class="bq-2"
                          th:each="dict : ${@dict.getType('bala_corp')}"
                          th:if="${dict.dictValue} == ${invoice.operateCorp}"
                          th:text="|运营公司：${dict.dictLabel}|"></span>

                </div>
                <div class="col-md-12 mt5">
                    <span class="bq-4">
                        [[${invoice.ifBargain == 0 ? '合同价':'议价'}]]
                    </span>
                    <span class="bq-4" th:if="${invoice.bargainMemo != null and invoice.bargainMemo !=''}">
                        议价备注：[[${invoice.bargainMemo}]]
                    </span>
                    <span class="bq-4">
                        <i class="fa fa-jpy" aria-hidden="true"></i> 单价 [[${invoice.unitPrice}]]元
                    </span>
                    <span class="strong" style="color: #731919;padding-right: 5px">
                        x
                    </span>
                    <span class="bq-4">
                        <i class="fa fa-cubes" aria-hidden="true"></i> [[${invoice.numCount}]]件 | [[${invoice.weightCount}]]吨 | [[${invoice.volumeCount}]]m³
                    </span>
                    <span class="strong" style="color: #731919;padding-right: 5px">
                        =
                    </span>
                    <span class="bq-4">
                        <i class="fa fa-jpy" aria-hidden="true"></i> 应收运费 [[${invoice.costAmount}]]元
                    </span>
                </div>
                <div class="col-md-12 mt5">
                    <span class="bq-4">
                        <i class="fa fa-jpy" aria-hidden="true"></i> 应收运费 [[${costAmount}]]元
                    </span>
                    <span class="strong" style="color: #731919;padding-right: 5px">
                        +
                    </span>
                    <span class="bq-4">
                        <i class="fa fa-truck" aria-hidden="true"></i> [[${onWayAmountFee == null ? 0 : onWayAmountFee}]]元
                    </span>
                    <span class="strong" style="color: #731919;padding-right: 5px">
                        =
                    </span>
                    <span class="bq-4">
                        <i class="fa fa-jpy" aria-hidden="true"></i>
                        总应收&nbsp;&nbsp;&nbsp;&nbsp;[[${totalFee}]]元
                    </span>
                </div>
                <div class="col-md-12 mt5">
                    <div class="layui-panel mt10" style="background-color: #f8f8f8;">
                        <div style="padding:15px;">
                            <div class="layui-timeline">
                                <div class="layui-timeline-item" style="padding-bottom: 0px;"
                                     th:each="address,status:${deliShippingAddressList}">
                                    <i class="layui-icon layui-timeline-axis timeline-badge warning" >装</i>
                                    <div class="layui-timeline-content layui-text">
                                        <div class="layui-timeline-title">
                                            <div class="strong">
                                                [[${address.addrName}]]&nbsp;&nbsp;&nbsp;[[${address.contact}]] / [[${address.mobile}]]&nbsp;&nbsp;&nbsp;[[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]]
                                            </div>
                                            <div>
                                                <span class="label mr10"
                                                      th:each="goods,status:${address.shippingGoodsList}"
                                                      th:text="${(goods.custOrderno == null ? '':goods.custOrderno + '-')
                                                               + goods.goodsName + '-'
                                                               + (goods.num == null || goods.num == 0  ? '' : goods.num + '件')
                                                               + (goods.weight == null || goods.weight == 0 ? '' : goods.weight + '吨')
                                                               + (goods.volume == null || goods.volume == 0 ? '' : goods.volume + 'm³')}"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-timeline-item" style="padding-bottom: 0px;"
                                     th:each="address,status:${arriShippingAddressList}">
                                    <i class="layui-icon layui-timeline-axis timeline-badge success" >卸</i>
                                    <div class="layui-timeline-content layui-text">
                                        <div class="layui-timeline-title">
                                            <div class="strong">
                                                [[${address.addrName}]]&nbsp;&nbsp;&nbsp;[[${address.contact}]] / [[${address.mobile}]]&nbsp;&nbsp;&nbsp;[[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]]
                                            </div>
                                            <div class="strong line20T" th:if="${address.isChangeAddress == 1}">
                                                [[${address.caArriAddrName}]]&nbsp;&nbsp;&nbsp;[[${address.caArriContact}]] / [[${address.caArriMobile}]]&nbsp;&nbsp;&nbsp;[[${address.caArriProName + address.caArriCityName + address.caArriAreaName + address.caArriDetailAddr}]]
                                            </div>

                                            <div>
                                                <span class="label mr10"
                                                      th:each="goods,status:${address.shippingGoodsList}"
                                                      th:text="${(goods.custOrderno == null ? '':goods.custOrderno + '-')
                                                               + goods.goodsName + '-'
                                                               + (goods.num == null || goods.num == 0  ? '' : goods.num + '件')
                                                               + (goods.weight == null || goods.weight == 0 ? '' : goods.weight + '吨')
                                                               + (goods.volume == null || goods.volume == 0 ? '' : goods.volume + 'm³')}"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-panel mt10" th:if="${entrustLotList != null and entrustLotList.size() > 0}">
        <div style="padding:5px;">
            <div class="row no-gutter">
                <div class="col-md-12 mt5">
                    <div class="timeline-box shadow">
                        <div class="timeline-main">
<!--                            <h1 style="margin-top: 0px;"><i class="fa fa-clock-o"></i></h1>-->
                            <div class="timeline-line"></div>
                            <div class="timeline-year" th:each="lot,status:${entrustLotList}">
<!--                                <h2>-->
<!--                                    <a class="yearToggle" th:if="${lot.ltlType} == 0">提货</a>-->
<!--                                    <a class="yearToggle" th:if="${lot.ltlType} == 1">干线</a>-->
<!--                                    <a class="yearToggle" th:if="${lot.ltlType} == 2">送货</a>-->
<!--                                    <i class="fa fa-caret-down fa-fw"></i>-->
<!--                                </h2>-->
                                <div class="timeline-month">
                                    <ul>
                                        <li>
                                            <div class="pickup-label" th:if="${lot.ltlType} == 0">提货</div>
                                            <div class="pickup-label" th:if="${lot.ltlType} == 1">干线</div>
                                            <div class="pickup-label" th:if="${lot.ltlType} == 2">送货</div>

                                            <div class="h4">
                                                <p class="date">[[${#dates.format(lot.reqDeliDate, 'yyyy-MM-dd')}]]</p>
                                            </div>
                                            <p class="dot-circle animated "><i class="fa fa-dot-circle-o"></i></p>
                                            <div class="content">
                                                <div>
                                                    <div class="strong" style="margin-bottom: 5px;" th:if="${lot.params[actDeliDate]} != null">
                                                        <span th:text="${lot.params[actDeliDate]} == null ? '暂未提货' : ${#dates.format(lot.params[actDeliDate], 'yyyy-MM-dd HH:mm:ss')}"></span>
                                                        <span>~</span>
                                                        <span th:text="${lot.params[actArriDate]} == null ? '暂未到货' : ${#dates.format(lot.params[actArriDate], 'yyyy-MM-dd HH:mm:ss')}"></span>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div >
                                                                <span class="title-1" style="margin-right: 20px">
                                                                    [[${lot.params[lotDeli]}]]----[[${lot.params[lotArri]}]]
                                                                    <span class="label label-success" style="padding:1px;cursor: pointer;"
                                                                          data-toggle="tooltip" data-container="body"
                                                                          data-placement="top" data-html="true"
                                                                          th:if="${lot.params[collectAmount]} != null and ${lot.params[collectAmount]} != 0"
                                                                          th:title="|代收金额：${lot.params[collectAmount]}元|">代</span>

                                                                    <span class="label label-warning" style="padding:2px;"
                                                                          data-toggle="tooltip" data-container="body"
                                                                          data-placement="top" data-html="true"
                                                                          th:if="${lot.lockPay == '1'}"
                                                                          >锁</span>

                                                                    <span class="label label-danger" style="padding:2px;cursor: pointer;"
                                                                          data-toggle="tooltip" data-container="body"
                                                                          data-placement="top" data-html="true"
                                                                          th:if="${lot.singleLock == '1'}"
                                                                          th:title="${lot.lockMemo}">锁</span>
                                                                </span>
                                                                <span class="lab" style="margin-right: -5px;">系统成本：</span>
                                                                <span class="lab ">
                                                                    <span class="bq-5" style="font-size: 14px;border: 1px solid #333">应付</span>
                                                                    <span class="" style="color: #FF9008;margin-left: 1px">￥[[${lot.params[yf]}]]元</span>
                                                                </span>
                                                                <span class="lab ">
                                                                    <span class="bq-5" style="font-size: 14px;border: 1px solid #333">应付税金</span>
                                                                    <span class="" style="color: #FF9008;margin-left: 1px">￥[[${lot.params[yfsf]}]]元</span>
                                                                </span>
                                                            </div>
                                                            <div style="padding-top: 5px;">
                                                                <span class="title-2">承运商：[[${lot.carrierName}]]</span>
                                                            </div>
                                                            <div style="padding-top: 5px;">
                                                                <span class="lab">
                                                                    <span class="iconfont icon-truck__easyic" style="font-size: 1.5em;margin-right: 6px;" title="车号"></span>
                                                                    <span>[[${lot.carNo == null ? '暂无' : lot.carNo}]]</span>
                                                                </span>
                                                                <span class="lab">
                                                                    <span class="iconfont icon-peizhisiji" style="font-size: 1.5em;margin-right: 6px;"></span>
                                                                    <span th:if="${lot.driverName != null or lot.driverMobile != null}">
                                                                        [[${lot.driverName == null ? '-' : lot.driverName}]]/[[${lot.driverMobile == null ? '-' : lot.driverMobile}]]
                                                                    </span>
                                                                    <span th:if="${lot.driverName == null and lot.driverMobile == null}">
                                                                        暂无
                                                                    </span>
                                                                </span>
                                                                <span class="lab">
                                                                    <span class="iconfont icon-kaipiao" style="font-size: 1.5em;margin-right: 6px;"></span>
                                                                     <!--<span th:if="${lot.billingType != null or lot.oilRatio != null}">
                                                                        <span th:each="dict : ${@dict.getType('billing_type')}"
                                                                              th:if="${lot.billingType} == ${dict.dictValue}"
                                                                              th:text="${dict.dictLabel}"></span>
                                                                        <span th:if="${lot.oilRatio != null}">/ 油卡[[${lot.oilRatio}]]%</span>
                                                                     </span>
                                                                     <span th:if="${lot.billingType == null and lot.oilRatio == null}">
                                                                        暂无
                                                                     </span>-->
                                                                    <span th:if="${lot.params[cost_g7] != 0}">G7（￥[[${#numbers.formatDecimal(lot.params[cost_g7],1,'COMMA',2,'POINT')}]]）</span>
                                                                    <span th:if="${lot.params[cost_yk] != 0}">油卡（￥[[${#numbers.formatDecimal(lot.params[cost_yk],1,'COMMA',2,'POINT')}]]）</span>
                                                                    <span th:if="${lot.params[cost_kp] != 0}">开票（￥[[${#numbers.formatDecimal(lot.params[cost_kp],1,'COMMA',2,'POINT')}]]）</span>
                                                                    <span th:if="${lot.params[cost_bkp] != 0}">不开票（￥[[${#numbers.formatDecimal(lot.params[cost_bkp],1,'COMMA',2,'POINT')}]]）</span>
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="m-l-md strong" style="float: right;">
                                                                排车信息：[[${lot.regUserName}]] [[${#dates.format(lot.regDate, 'yyyy-MM-dd HH:mm:ss')}]]
                                                            </div>
                                                            <div class="m-l-md strong" style="float: right;"
                                                                 th:if="${lot.params != null}"
                                                                 th:each="arrivalMap,status:${lot.params['arrivalList']}">

                                                                <div>
                                                                    跟踪信息：[[${arrivalMap['arrivalUser']}]] [[${#dates.format(arrivalMap['arrivalDate'], 'yyyy-MM-dd HH:mm:ss')}]]
                                                                </div>
                                                                <div class="mt10" style="float: right;">
                                                                    <sapn class="picviewer mr10" th:each="pic:${arrivalMap['picList']}" th:if="${pic.filePath!=null}">
                                                                        <img style="width: 30px;height: 30px" th:src="@{${pic.filePath}}"/>
                                                                    </sapn>
                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <hr>
                                                </div>
                                            </div>
                                            <div class="clear"></div>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="timeline-year" th:each="bookingSend,status:${bookingSendList}">
<!--                                <h2>-->
<!--                                    <a class="yearToggle">安装</a>-->
<!--                                    <i class="fa fa-caret-down fa-fw"></i>-->
<!--                                </h2>-->
                                <div class="timeline-month">
                                    <ul>
                                        <li>
                                            <div class="pickup-label">安装</div>

                                            <div class="h4">
                                                <p class="date">[[${#dates.format(bookingSend['time'], 'yyyy-MM-dd')}]]</p>
                                            </div>
                                            <p class="dot-circle animated "><i class="fa fa-dot-circle-o"></i></p>
                                            <div class="content">
                                                <div>
                                                    <div class="row">
                                                        <p class="strong">[[${bookingSend['entrustNo']}]]</p>
                                                        <table class="table table-bordered mt10">
                                                            <thead style="background: #f7f8fa">
                                                            <tr>
                                                                <th style="width: 10%;">安装时间</th>
                                                                <th style="width: 10%;">状态</th>
                                                                <th style="width: 10%;">金额</th>
                                                                <th style="width: 25%;">附件</th>
                                                                <th style="width: 45%;">说明</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr th:each="sends,status:${bookingSend['bookingSends']}">
                                                                    <td>[[${#dates.format(sends.operateTime, 'yyyy-MM-dd HH:mm:ss')}]]</td>
                                                                    <td>
                                                                        <div th:if="${sends.type} == 1">预约送货</div>
                                                                        <div th:if="${sends.type} == 2">安装开始</div>
                                                                        <div th:if="${sends.type} == 3">安装完成</div>
                                                                        <div th:if="${sends.type} == 4">异常维修</div>
                                                                        <div th:if="${sends.type} == 5">送达装卸</div>
                                                                    </td>
                                                                    <td style="text-align: right;">
                                                                        <div th:if="${sends.fixAmount != null}"
                                                                             th:text="'￥'+${#numbers.formatDecimal(sends.fixAmount,1,'COMMA',2,'POINT')}"></div>
                                                                    </td>
                                                                    <td>
                                                                        <div>
                                                                            <span th:if="${!#strings.isEmpty(sends.picTid)}">
<!--                                                                                <img class="layui-upload-img" style="width: 85px;"-->
<!--                                                                                     th:each="pic:${sends.params['picFiles']}"-->
<!--                                                                                     th:src="${pic.filePath}" id="demo1"-->
<!--                                                                                     th:data-url="${pic.filePath}"-->
<!--                                                                                     onclick="imgOpen(this.getAttribute('data-url'))"/>-->

                                                                                <span class="picviewer mr10" th:each="pic:${sends.params['picFiles']}"
                                                                                     th:if="${pic.filePath!=null}">
                                                                                    <img style="width: 30px;height: 30px" th:src="@{${pic.filePath}}"/>
                                                                                </span>

                                                                            </span>
                                                                            <span th:if="${!#strings.isEmpty(sends.videoTid)}">
                                                                                <div class="video-container" th:each="video:${sends.params['videoFiles']}">
                                                                                    <video class="video"
                                                                                           th:src="${video.filePath}" >
                                                                                    </video>
                                                                                    <div class="play-button"
                                                                                         th:data-url="${video.filePath}"
                                                                                         onclick="previewVideo(this.getAttribute('data-url'))">
                                                                                        <img src="/img/play.png" alt="Play Button">
                                                                                    </div>
                                                                                </div>
                                                                            </span>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        [[${sends.remark}]]
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>

                                                    </div>
                                                    <hr>
                                                </div>
                                            </div>
                                            <div class="clear"></div>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="timeline-year" th:if="${!#lists.isEmpty(receiptPicInfoList)}">
<!--                                <h2>-->
<!--                                    <a class="yearToggle">回单</a>-->
<!--                                    <i class="fa fa-caret-down fa-fw"></i>-->
<!--                                </h2>-->
                                <div class="timeline-month">
                                    <ul>
                                        <li>
                                            <div class="pickup-label">回单</div>

                                            <div class="h4">
                                                <p class="date"></p>
                                            </div>
                                            <p class="dot-circle animated "><i class="fa fa-dot-circle-o"></i></p>
                                            <div class="content">
                                                <div>
                                                    <div class="row">
                                                        <p class="strong"></p>
                                                        <div th:each="receiptPicInfo,status:${receiptPicInfoList}">
                                                            <div>
                                                                <span th:if="${receiptPicInfo[receiptUploadDate] != null}">
                                                                    [[${#dates.format(receiptPicInfo[receiptUploadDate], 'yyyy-MM-dd HH:mm:ss')}]]-[[${receiptPicInfo[receiptUploadUserName]}]]（回单上传）
                                                                </span>
                                                                <span class="ml5" th:if="${receiptPicInfo[receiptConfirmTime] != null}">
                                                                    [[${#dates.format(receiptPicInfo[receiptConfirmTime], 'yyyy-MM-dd HH:mm:ss')}]]-[[${receiptPicInfo[receiptConfirmUser]}]]（回单确认）
                                                                </span>
                                                                <span class="ml5" th:if="${receiptPicInfo[receiptDate] != null}">
                                                                    [[${#dates.format(receiptPicInfo[receiptDate], 'yyyy-MM-dd HH:mm:ss')}]]-[[${receiptPicInfo[receiptMan]}]]（正本确认）
                                                                </span>
                                                            </div>
                                                            <div class="mt10" style="display: flex;">
                                                                <div class="picviewer mr10" th:each="pic:${receiptPicInfo[receiptUploadFiles]}" th:if="${pic.filePath!=null}">
                                                                    <img style="width: 30px;height: 30px" th:src="@{${pic.filePath}}"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <hr>
                                                </div>
                                            </div>
                                            <div class="clear"></div>
                                        </li>
                                    </ul>
                                </div>
                            </div>

<!--                            <h1 style="padding-top:4px;padding-bottom:2px;margin-top:10px;margin-bottom: 0px"><i class="fa fa-check-circle-o"></i></h1>-->
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="layui-panel mt10" >
        <div style="padding:15px;">
            <div class="row no-gutter">
                <div class="col-md-12">
                    <table class="table table-bordered mt10">
                        <thead style="background: #f7f8fa">
                            <tr>
                                <th style="width: 10%;">费用</th>
                                <th style="width: 15%;">费用类型</th>
                                <th style="width: 10%;">系统金额</th>
                                <th style="width: 15%;">状态</th>
                                <th style="width: 15%;">创建信息</th>
                                <th style="width: 10%;">合计</th>
                                <th style="width: 10%;">利润</th>
                                <th style="width: 10%;">利润率</th>
                            </tr>
                        </thead>
                        <tbody th:if="${rowspanCt > 1}">
                            <tr th:each="rece,status:${receiveDetailList}">
                                <td th:if="${status.index == 0}" th:rowspan="${status.size}">应收</td>
                                <td>
                                    <div th:text="${rece.freeType}">
                                    </div>
                                </td>
                                <td style="text-align: right;" th:text="'￥'+${#numbers.formatDecimal(rece.transFeeCount,1,'COMMA',2,'POINT')}"></td>
                                <td>
                                    <div th:each="receiveDetailStatus:${receiveDetailStatusEnum}"
                                         th:if="${rece.vbillstatus} == ${receiveDetailStatus.value}"
                                         th:text="${receiveDetailStatus.context}"></div>
                                </td>
                                <td>
                                    <div>[[${rece.regUserName}]]</div>
                                    <div>[[${#dates.format(rece.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                </td>
                                <td style="text-align: right;"
                                    th:if="${status.index == 0}"
                                    th:rowspan="${status.size}"
                                    th:text="'￥'+${#numbers.formatDecimal(#aggregates.sum(receiveDetailList.![transFeeCount==null? 0:transFeeCount]),1,'COMMA',2,'POINT')}">
                                    <div>￥[[${ys}]]</div>
                                </td>
                                <td style="text-align: right;"
                                    th:if="${status.index == 0}"
                                    th:rowspan="${rowspanCt}">
                                    <div>￥[[${profit}]]</div>
                                </td>
                                <td style="text-align: right;" th:if="${status.index == 0}" th:rowspan="${rowspanCt}">
                                    [[${profitRate}]]%
                                </td>
                            </tr>
                            <tr th:each="allocation,status:${allocationList}">
                                <td th:if="${status.index == 0}" th:rowspan="${status.size}">应付</td>
                                <td>
                                    <div th:if="${allocation.freeType=='1'}" th:text="|在途（${@dict.getLabel('cost_type_on_way',allocation.costTypeOnWay)}）|"></div>
                                    <div th:if="${allocation.freeType=='0'}" th:text="|运费（${@dict.getLabel('cost_type_freight',allocation.costTypeFreight)}）|"></div>
                                </td>
                                <td style="text-align: right;" th:text="'￥'+${#numbers.formatDecimal(allocation.costShare,1,'COMMA',2,'POINT')}"></td>
                                <td>
                                    <div th:each="payDetailStatus:${payDetailStatusEnum}"
                                         th:if="${allocation.payDetailVbillstatus} == ${payDetailStatus.value}"
                                         th:text="${payDetailStatus.context}"></div>
                                </td>
                                <td>
                                    <div>[[${allocation.payDetailRegUserName}]]</div>
                                    <div>[[${#dates.format(allocation.payDetailRegDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                </td>
                                <td style="text-align: right;" th:if="${status.index == 0}" th:rowspan="${status.size}">
                                    <div>￥[[${yf}]]</div>
                                    <div>税：￥[[${yfsf}]]</div>
                                </td>
                            </tr>
                            <tr th:each="otherFee,status:${otherFeeList}">
                                <td th:if="${status.index == 0}" th:rowspan="${status.size}">三方</td>
                                <td>
                                    <span th:text="${@dict.getLabel('cost_type_on_way',otherFee.feeType)}"></span>
                                </td>
                                <td style="text-align: right;" th:text="'￥'+${#numbers.formatDecimal(otherFee.feeAmount,1,'COMMA',2,'POINT')}"></td>
                                <td>
                                    <div th:each="otherFeeStatus:${otherFeeStatusEnum}"
                                         th:if="${otherFee.vbillstatus} == ${otherFeeStatus.value}"
                                         th:text="${otherFeeStatus.context}"></div>
                                </td>
                                <td>
                                    <div>[[${otherFee.regUserId}]]</div>
                                    <div>[[${#dates.format(otherFee.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                </td>
                                <td style="text-align: right;" th:if="${status.index == 0}" th:rowspan="${status.size}">
                                    <div>￥[[${dsf}]]</div>
                                    <div>税：￥[[${dsfsf}]]</div>
                                </td>
                            </tr>
                            <tr th:if="${ptf != 0}">
                                <td>平台费</td>
                                <td>
                                    平台费
                                </td>
                                <td style="text-align: right;" th:text="'￥'+${#numbers.formatDecimal(ptf,1,'COMMA',2,'POINT')}"></td>
                                <td>-</td>
                                <td>-</td>
                                <td style="text-align: right;"
                                    rowspan="1"
                                    th:text="'￥'+${#numbers.formatDecimal(ptf,1,'COMMA',2,'POINT')}">
                                </td>
                            </tr>
                        </tbody>
                        <tbody th:if="${rowspanCt == 1}">
                            <tr>
                                <td colspan="8" style="text-align: center;">暂无数据</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    $(function () {

    });
    function imgOpen(url) {
        layer.open({
            shift: 3,
            shadeClose: true, // 点击遮罩关闭层
            type: 1,
            title: '预览图',
            resize: true,
            area: ['90%', '90%'],
            content: "<span><img style='width:100%;height: 100%;' src=" + url + " /></span>",//注意，如果str是object，那么需要字符拼接。
        });

    }
    function previewVideo(url, width, height) {
        width = width ? width : '90%';
        height = height ? height : '90%';
        let content = '<video width="100%" height="90%"  controls="controls" autobuffer="autobuffer"  autoplay="autoplay" loop="loop">' +
            '<source src="' + url + '" type="video/mp4"></source></video>';
        layer.open({
            type: 1,
            maxmin: true, //打开放大缩小按钮
            title: '视频播放',
            area: [width, height],
            content: content,
        });
    }



</script>
</body>
</html>