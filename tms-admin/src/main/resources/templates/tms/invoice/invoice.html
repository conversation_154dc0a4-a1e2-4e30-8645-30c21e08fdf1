<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('发货单')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .table-hover tbody tr:hover {
        background-color: #f5f5f5;
    }

    .popup{
        width: 100%;
        height: 100%;
        position: fixed;
        top: 0;
        left: 0;
        background: rgba(0, 0, 0, .4);
        display: none;
    }
    .none{

    }
    .popup_content {
        width: 600px;
        left: 50%;
        margin-left: -300px;
        background: #fff;
        top: 20%;
        position: fixed;
        border-radius: 7px;
    }
    .popup_text{
        height: 24px;
        line-height: 24px;
        color: #808080;
    }
    .closed{
        width: 15px;
        height: 15px;
        position: absolute;
        right: 5px;
        top: 5px;
        color: #808080;
    }
    .table-striped{
        height: auto;
    }
    .label-warningT{
        color: #f8ac59;
        background-color: transparent;
        border: 1px solid #f8ac59;
    }
    .label-dangerT{
        color: #ed5565;
        background-color: transparent;
        border: 1px solid #ed5565;
    }
    .label-successT{
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }
    .fileNameTag span {
        background-color: #597bf8;
        color: #fff;
        border-radius: 10px;
        padding: 3px 8px;
        cursor: move;
    }
    .fileNameTag span + span {
        margin-left: 5px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter" th:if="${isFleet}">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-4">原始业务单号：</label>-->
                            <div class="col-sm-12">
                                <input name="params[bizNo]" id="bizNo" placeholder="分配车队前发货单号/运段单号/委托单号" class="form-control" type="text"
                                       maxlength="20" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-4">原始客户简称：</label>-->
                            <div class="col-sm-12">
                                <input name="bizCustAbbr" id="bizCustAbbr" placeholder="分配车队前客户简称" class="form-control" type="text"
                                       maxlength="20" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-4">原始客户简称：</label>-->
                            <div class="col-sm-12">
<!--                                <input name="params[isAssig]" placeholder="分配车队前客户简称" class="form-control" type="text"-->
<!--                                       maxlength="20" aria-required="true">-->

                                <select name="params[isAssig]" placeholder="是否是分配的数据" data-none-selected-text="是否是分配的数据" class="form-control valid noselect2 selectpicker" aria-invalid="false">
                                    <option value=""></option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row no-gutter">
                        <div class="col-md-2 col-sm-2" >
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户发货单号：</label>-->
                                <div class="col-sm-12" >
                                    <input name="custOrderno" id="custOrderno" placeholder="客户发货单号/发货单编号" class="form-control" type="text"
                                           maxlength="30" autocomplete="off">
                                </div>
                            </div>
                        </div>
<!--                        客户简称-->
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户简称：</label>-->
                                <div class="col-sm-12">
                                    <input name="custAbbr" id="custAbbr" placeholder="客户简称" class="form-control" type="text"
                                           maxlength="20" autocomplete="off">
                                </div>
                            </div>
                        </div>
<!--                        运输方式-->
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">运输方式：</label>-->
                                <div class="col-sm-12">
                                    <select name="transCode" placeholder="运输方式" id="transCode" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" data-none-selected-text="运输方式" multiple th:with="type=${@dict.getType('trans_code')}">
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
<!--                        运营组-->
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">运营组：</label>-->
                                <div class="col-sm-12">
                                    <select name="salesDept" placeholder="运营组" id="salesDept" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" data-none-selected-text="运营组" multiple>
                                        <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                                th:text="${mapS.deptName}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
<!--                        <div class="col-md-3 col-sm-3">-->
<!--                            <div class="form-group">-->
<!--                                &lt;!&ndash;                        <label class="col-sm-4">紧急程度：</label>&ndash;&gt;-->
<!--                                <div class="col-sm-12">-->
<!--                                    <select name="urgentLevel" placeholder="紧急程度" id="urgentLevel" class="form-control valid noselect2 selectpicker"-->
<!--                                            data-none-selected-text="紧急程度" aria-invalid="false" th:with="type=${@dict.getType('urgent_level')}">-->
<!--                                        <option value=""></option>-->
<!--                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>-->
<!--                                    </select>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        创建人-->
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">创建人：</label>-->
                                <div class="col-sm-12">
                                    <input name="regUserName" id="regUserName" placeholder="创建人" class="form-control" type="text"
                                           maxlength="30" autocomplete="off">
                                </div>
                            </div>
                        </div>
<!--                        结算方式-->
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">结算方式：</label>-->
                                <div class="col-sm-12">
                                    <select name="balaType" id="balaType" placeholder="结算方式" data-none-selected-text="结算方式" class="form-control valid noselect2 selectpicker" aria-invalid="false" th:with="type=${@dict.getType('bala_type')}">
                                        <option value=""></option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                </div>

                <div class="row no-gutter">
                    <!-- 应收是否核销-->
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="receivableWriteOffStatusList" id="receivableWriteOffStatusList"
                                        class="form-control valid noselect2 selectpicker"
                                        data-none-selected-text="应收是否核销" multiple>
                                    <option  value="0">未核销</option>
                                    <option  value="1">部分核销</option>
                                    <option  value="2">已核销</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <!-- 调度状态-->
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">调度状态：</label>-->
                            <div class="col-sm-12">
                                <select name="segmentStatus" placeholder="调度状态" data-none-selected-text="调度状态" id="segmentStatus" class="form-control valid noselect2 selectpicker" aria-invalid="false">
                                    <option value=""></option>
                                    <option value="0">待调度</option>
                                    <option value="1">部分调度</option>
                                    <option value="2">已调度</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <!-- 发货单状态-->
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">发货单状态：</label>-->
                            <div class="col-sm-12">
                                <select name="vbillstatus" placeholder="发货单状态" id="vbillstatus" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="发货单状态" multiple
                                        th:with="type=${invoiceStatusList}">
                                    <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <!-- 车长-->
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">车长：</label>-->
                            <div class="col-sm-12">
                                <select name="carLen" placeholder="车长" id="carLen" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="车长" multiple th:with="type=${@dict.getType('car_len')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
<!--                        <div class="col-md-3 col-sm-3">-->
<!--                            <div class="form-group">-->
<!--                                &lt;!&ndash;                            <label class="col-sm-4">车型：</label>&ndash;&gt;-->
<!--                                <div class="col-sm-12" style="padding-right: 0">-->
<!--                                    <select name="carType" placeholder="车型" id="carType" class="form-control valid noselect2 selectpicker"-->
<!--                                            aria-invalid="false" data-none-selected-text="车型" multiple th:with="type=${@dict.getType('car_type')}">-->
<!--                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>-->
<!--                                    </select>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->


                    <div class="col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-2">要求提货日期：</label>-->
                            <div class="">
                                <input type="text" placeholder="要求提货开始日" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateStart"  name="params[reqDeliDateStart]" autocomplete="off" th:value="${reqDeliDateStart}">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" placeholder="要求提货截止日" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateEnd"  name="params[reqDeliDateEnd]" autocomplete="off" th:value="${reqDeliDateEnd}">
                            </div>
                        </div>
                    </div>
<!--                        <div class="col-sm-6">-->
<!--                            <div class="form-group">-->
<!--                                &lt;!&ndash;                            <label class="col-sm-2">要求到货日期：</label>&ndash;&gt;-->
<!--                                <div class="">-->
<!--                                    <input type="text" placeholder="要求到货开始日" style="width: 45%; float: left;" class="form-control"-->
<!--                                           id="reqArriDateStart"  name="params[reqArriDateStart]" autocomplete="off">-->
<!--                                    <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>-->
<!--                                    <input type="text" placeholder="要求到货截止日" style="width: 45%; float: left;" class="form-control"-->
<!--                                           id="reqArriDateEnd"  name="params[reqArriDateEnd]" autocomplete="off">-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
                </div>
                <div class="row no-gutter">
                    <div class="col-sm-5" >
                        <div class="form-group">
<!--                            <label class="col-sm-4">提货方地址：</label>-->
                            <div class="col-sm-8">
                                <div class="col-sm-4">
                                    <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliCityId" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliAreaId" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <input name="deliDetailAddr" id="deliDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                       maxlength="50" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-5" id="arriPro">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">收货方地址：</label>-->
                            <div class="col-sm-1" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 28px;height: 28px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-7">
                                <div class="col-sm-4">
                                    <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <input name="arriDetailAddr" id="arriDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                       maxlength="50" autocomplete="off">
                            </div>
                        </div>
                    </div>
<!--
                    <div class="col-sm-5" id="arriProMultiple" style="display: none">
                        <div class="form-group">
                            &lt;!&ndash;                            <label class="col-sm-4">收货方地址：</label>&ndash;&gt;
                            <div class="col-sm-2" >
                                <img th:src="@{/img/change.png}" style="width: 40px;height: 40px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-7">
                                <div class="col-sm-12">
                                    <select  name="arriProvinceIds" id="arriProvinceIds"  data-none-selected-text="到货省" class="form-control valid noselect2 selectpicker" aria-invalid="false" multiple>
                                        <option th:each="dict:${provinceList}" th:text="${dict.PROVINCE_NAME}" th:value="${dict.PROVINCE_CODE}"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-3">

                            </div>
                        </div>
                    </div>
-->


                    <div class="col-sm-2">
                        <div class="form-group">

                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" shiro:hasAnyPermissions="tms:invoice:add,tms:fleet:invoice:add" onclick="addTab()">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-warning multiple disabled" shiro:hasAnyPermissions="tms:invoice:affirm,tms:fleet:invoice:affirm" onclick="affirm()">
                <i class="fa fa-check-circle-o"></i> 确认
            </a>

            <a class="btn btn-primary single disabled" shiro:hasPermission="tms:invoice:fee_entry" onclick="addOtherFee()">
                <i class="fa fa-jpy"></i> 三方费用录入
            </a>

<!--            <a class="btn btn-primary single disabled"  shiro:hasPermission="tms:invoice:fee_apply" onclick="otherFeeApply()">-->
<!--                <i class="fa fa-jpy"></i> 第三方应付费用-->
<!--            </a>-->

<!--            <a class="btn btn-warning multiple disabled" shiro:hasPermission="tms:invoice:fee_entry" onclick="otherFeeInput()">-->
<!--                <i class="fa fa-jpy"></i> 批量第三方费用-->
<!--            </a>-->
        <!--    <a class="btn btn-primary multiple disabled" shiro:hasPermission="tms:invoice:print" onclick="invoicePrint()">
                <i class="fa fa-object-group"></i> 托运单打印
            </a>-->
<!--            <a class="btn btn-primary multiple disabled" th:if="${!isFleet}" shiro:hasPermission="tms:invoice:getGuidePrice"-->
<!--               onclick="getGuidePrice()">-->
<!--                <i class="fa fa-dollar"></i> 获取指导价-->
<!--            </a>-->

<!--            <a class="btn btn-primary multiple disabled "  th:if="${isFleet}" onclick="checking()" shiro:hasPermission="finance:fleet:receive:checking">-->
<!--                <i class="fa fa-file-text-o"></i> 生成对账单-->
<!--            </a>-->

<!--            <a class="btn btn-primary multiple disabled " th:if="${isFleet}" onclick="insertChecking()" shiro:hasPermission="finance:fleet:receive:insertChecking">-->
<!--                <i class="fa fa-file-text-o"></i> 加入对账单-->
<!--            </a>-->

<!--            <a class="btn btn-info"  th:if="${isFleet}" onclick="adjustImport()" shiro:hasPermission="finance:fleet:receive:adjustImport">-->
<!--                <i class="fa fa-upload"></i> 调整单导入-->
<!--            </a>-->

<!--            <a class="btn btn-warning"  onclick="adjustExport()" shiro:hasPermission="finance:fleet:receive:adjustExport">-->
<!--                <i class="fa fa-download"></i> 调整单导出-->
<!--            </a>-->
<!--            <a class="btn btn-warning"  onclick="invoiceExport()" shiro:hasAnyPermissions="tms:invoice:export,tms:invoice:ledger">-->
<!--                <i class="fa fa-download"></i> 发货单导出-->
<!--            </a>-->

<!--            <a class="btn btn-info" onclick="receiptExport()" shiro:hasPermission="tms:invoice:receiptExport">-->
<!--                <i class="fa fa-download"></i> 回单导出-->
<!--            </a>-->
<!--            <a class="btn btn-danger" onclick="receiptDownload()" shiro:hasPermission="tms:invoice:receiptExport">-->
<!--                <i class="fa fa-download"></i> 回单下载-->
<!--            </a>-->

            <div class="dropdown" style="display: inline-block;"
                 shiro:hasAnyPermissions="tms:invoice:print,tms:invoice:export,tms:invoice:ledger,tms:invoice:add,tms:fleet:invoice:add,tms:invoice:receiptExport">
                <button class="btn btn-info dropdown-toggle" style="padding: 4px 12px;" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                    数据操作
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
<!--                    <li>-->
<!--                        <a class="btn single disabled" shiro:hasPermission="tms:invoice:fee_entry" onclick="addOtherFee()">-->
<!--                            <i class="fa fa-jpy"></i> 第三方费用录入-->
<!--                        </a>-->
<!--                    </li>-->
                    <li>
                        <a class="btn multiple disabled" shiro:hasPermission="tms:invoice:print" onclick="invoicePrint()">
                            <i class="fa fa-object-group"></i> 托运单打印
                        </a>
                    </li>
                    <li>
                        <a class="btn"  onclick="invoiceExport()" shiro:hasAnyPermissions="tms:invoice:export,tms:invoice:ledger">
                            <i class="fa fa-upload"></i> 发货单导出
                        </a>
                    </li>
                    <li>
                        <a class="btn" shiro:hasAnyPermissions="tms:invoice:add,tms:fleet:invoice:add" th:if="${!isFleet}" onclick="importInvoice()">
                            <i class="fa fa-download"></i> 发货单导入
                        </a>
                    </li>
                    <li>
                        <a class="btn" shiro:hasAnyPermissions="tms:invoice:jtgf" th:if="${!isFleet}" onclick="importJtgfFhd()">
                            <i class="fa fa-download"></i> 家庭光伏导入
                        </a>
                    </li>
                    <li>
                        <a class="btn" onclick="receiptExport()" shiro:hasPermission="tms:invoice:receiptExport">
                            <i class="fa fa-download"></i> 回单导出
                        </a>
                    </li>
                    <li>
                        <a class="btn" onclick="receiptDownload()" shiro:hasPermission="tms:invoice:receiptExport">
                            <i class="fa fa-download"></i> 回单下载
                        </a>
                    </li>
                </ul>
            </div>

            <div class="dropdown" style="display: inline-block;"
                 shiro:hasAnyPermissions="tms:invoice:back_confirm,tms:fleet:invoice:back_confirm,tms:invoice:close,tms:fleet:invoice:close,tms:invoice:remove,tms:fleet:invoice:remove,tms:invoice:updateContractPrice">
                <button class="btn btn-danger dropdown-toggle" style="padding: 4px 12px;" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                    单据操作
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                    <li>
                        <a class="btn multiple disabled" shiro:hasAnyPermissions="tms:invoice:updateContractPrice" onclick="updateContractPrice()">
                            <i class="fa fa-retweet"></i> 获取合同成本价
                        </a>
                    </li>

                  <!--  <li>
                        <a class="btn multiple disabled" shiro:hasAnyPermissions="tms:invoice:updateContractPrice_1" onclick="updateContractPrice_1()">
                            <i class="fa fa-retweet"></i> 2025成本价
                        </a>
                    </li>-->

                    <li>
                        <a class="btn single disabled" shiro:hasAnyPermissions="tms:invoice:back_confirm,tms:fleet:invoice:back_confirm" onclick="oppositeAffirm()">
                            <i class="fa fa-reply"></i> 反确认
                        </a>
                    </li>
                    <li>
                        <a class="btn single disabled" shiro:hasAnyPermissions="tms:invoice:close,tms:fleet:invoice:close" onclick="invoiceClose()">
                            <i class="fa fa-remove"></i> 关闭
                        </a>
                    </li>
                    <li>
                        <a class="btn multiple disabled" shiro:hasAnyPermissions="tms:invoice:remove,tms:fleet:invoice:remove" onclick="removeAll()" >
                            <i class="fa fa-remove"></i> 删除
                        </a>
                    </li>
                </ul>
            </div>

         <!--   <a class="btn btn-danger single disabled" shiro:hasAnyPermissions="tms:invoice:back_confirm,tms:fleet:invoice:back_confirm" onclick="oppositeAffirm()">
                <i class="fa fa-reply"></i> 反确认
            </a>
            <a class="btn btn-danger single disabled" shiro:hasAnyPermissions="tms:invoice:close,tms:fleet:invoice:close" onclick="invoiceClose()">
                <i class="fa fa-remove"></i> 关闭
            </a>

            <a class="btn btn-danger multiple disabled" shiro:hasAnyPermissions="tms:invoice:remove,tms:fleet:invoice:remove" onclick="removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>-->
<!--            <a class="btn btn-primary" th:if="${isFleet}"  onclick="adjustRecord()" shiro:hasPermission="tms:fleet:invoice:adjustRecord">
                <i class="fa fa-newspaper-o"></i> 调整单记录
            </a>-->

<!--            <a class="btn btn-info" shiro:hasAnyPermissions="tms:invoice:add,tms:fleet:invoice:add" th:if="${!isFleet}" onclick="importInvoice()">-->
<!--                <i class="fa fa-upload"></i> 发货单导入-->
<!--            </a>-->

            <!--<a class="btn btn-success single disabled" shiro:hasAnyPermissions="tms:invoice:recover" th:if="${!isFleet}" onclick="recoverInvoice()">
                <i class="fa"></i> 发货单转单
            </a>-->
<!--            <a class="btn btn-success" onclick="ledgerExport()" shiro:hasAnyPermissions="tms:invoice:ledger">-->
<!--                <i class="fa fa-cloud-download"></i> 台账导出-->
<!--            </a>-->
            <div class="dropdown single disabled" style="display: inline-block;"
                 shiro:hasAnyPermissions="tms:invoice:sunshineAdd,tms:invoice:zjhyAdd">
                <button class="btn btn-primary dropdown-toggle" style="padding: 4px 12px;" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                    单据获取
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                    <li>
                        <a class="btn" shiro:hasAnyPermissions="tms:invoice:sunshineAdd" onclick="sunshineAdd()">
                            <i class="fa fa-plus"></i> 阳光获取
                        </a>
                    </li>
                    <li>
                        <a class="btn" shiro:hasAnyPermissions="tms:invoice:sunshineAdd" onclick="uploadCarPic()">
                            <i class="fa fa-plus"></i> 阳光上传车辆行驶证
                        </a>
                    </li>
                    <li>
                        <a class="btn" shiro:hasAnyPermissions="tms:invoice:zjhyAdd" onclick="zjhyAdd()">
                            <i class="fa fa-plus"></i> 恒逸获取
                        </a>
                    </li>
                    <li>
                        <a class="btn" shiro:hasAnyPermissions="tms:invoice:zllgAdd" onclick="zllgAdd()">
                            <i class="fa fa-plus"></i> 中粮获取
                        </a>
                    </li>
                    <li>
                        <a class="btn" shiro:hasAnyPermissions="tms:invoice:66kc" onclick="saAdd()">
                            <i class="fa fa-plus"></i> 圣奥获取
                        </a>
                    </li>
                </ul>
            </div>


            <a class="btn btn-info" th:onclick="showSpSettings([[${templateId}]], [[${biz}]])" shiro:hasPermission="wecom:sp:setting">
                <i class="fa fa-cog"></i> 配置
            </a>
            <a class="btn btn-info single disabled" onclick="process()">
                <i class="fa fa-laptop"></i> 审批进度
            </a>
            <a class="btn btn-info multiple disabled" onclick="copyDisData()" >
                <i class="fa  fa-files-o"></i> 拷贝运力信息
            </a>
            <a class="btn btn-info single disabled" shiro:hasAnyPermissions="tms:invoice:switchBillingMethod" onclick="switchBillingMethod()">
                <i class="fa fa-exchange"></i> 切换单程/往返
            </a>

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
    <div class="popup">
        <div class="popup_content">
            <div style="position: relative;padding: 20px 20px">
                <div class="popup_text">原始发货单号：<nobr class="one"></nobr></div>
                <div class="popup_text ">原始运段号：<nobr class="two"></nobr></div>
                <div class="popup_text ">原始委托单号：<nobr class="three"></nobr></div>
                <div class="popup_text ">原始运单号：<nobr class="four"></nobr></div>
                <div class="closed">
                    <i class="glyphicon glyphicon-remove"></i>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/js/xlsx.full.min.js}"></script>
<script th:src="@{'/js/wecom.js'}"></script>
<script th:src="@{'/js/bz/invoice.js?v=20250327'}"></script>
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a th:href="@{/file/ReceiveAdjustModel.xlsx}" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
<script id="importTpl2" type="text/template">
    <div style="padding: 5px">
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"><a href="#XA" aria-controls="XA" role="tab" data-toggle="tab">精简版</a></li>
            <li role="presentation"><a href="#XB" aria-controls="XB" role="tab" data-toggle="tab">通用版</a></li>
        </ul>
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane active" id="XA" style="padding: 10px">
                <div style="font-weight: bold;color: #5daf34">说明：只支持一装一卸的发货单导入，列支持自定义顺序，标题名称不可修改，否则识别不到</div>

                <div class="mt10 pt5">
                    适用模板：
                    <a th:href="@{/file/invoiceTemplate-sim.xlsx}">下载模板</a>
                </div>
                <div id="ipt_file_div22" style="display: none">
                    <input type="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" id="ipt_file22" onchange="readExcel22()"/>
                </div>

                <a href="javascript:$('#ipt_file22').click()"class="mt10 btn btn-success btn-sm"><i class="fa fa-file-excel-o"></i> 选择要导入文件</a>

                <span id="fileMsg22" class="mt10" style="display: inline-block;line-height: 26px;vertical-align: middle"></span>

            </div>
            <div role="tabpanel" class="tab-pane" id="XB" style="padding: 10px">
                <div style="font-weight: bold;color: #5daf34">说明：支持一装一卸、多装多卸的发货单导入，列标题及顺序不支持修改</div>

                <div class="mt10 pt5">
                    适用模板：
                    <a th:href="@{/file/invoiceTemplate.xlsx}">下载模板</a>
                </div>

                <div id="ipt_file_div" style="display: none">
                    <input type="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" id="ipt_file" onchange="readExcel()"/>
                </div>

                <a href="javascript:$('#ipt_file').click()"class="mt10 btn btn-success btn-sm"><i class="fa fa-file-excel-o"></i> 选择要导入文件</a>

                <span id="fileMsg" class="mt10" style="display: inline-block;line-height: 26px;vertical-align: middle"></span>
            </div>
        </div>

        <div class="mt10" style="color:red;padding: 0 10px;">
            提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>

        <div id="templateMsg" class="mt10" style="font-weight: bold;color:blue;padding: 0 10px;"></div>
    </div>
</script>

<script th:inline="javascript">
    //是否是车队
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/invoice" : ctx + "invoice";

    var vbillstatus = [[${invoiceStatusList}]];
    var carLen = [[${@dict.getType('car_len')}]];
    var carType = [[${@dict.getType('car_type')}]];
    var transCode = [[${@dict.getType('trans_code')}]];
    var CloseAccountList = [[${CloseAccountList}]];//关账记录
    //发货单状态map
    var invoiceStatusMap = [[${invoiceStatusMap}]];
    var changeFlag = false;
    var salesDept = [[${salesDept}]]

    $(function () {
        //
        $("#reqDeliDateStart").val(getFrontFormatDate());

        let v = [[${vbillstatus}]]
        $("#vbillstatus").val([v]);

        $("#vbillstatus").selectpicker('refresh');

        let ctf = [[${receivableWriteOffStatusList}]]
        if (ctf != null && ctf != '') {
            var a = [];
            let ctfList = ctf.split(',');
            for(var i =0 ;i<ctfList.length;i++){
                a.push(ctfList[i]);
            }
            $('#receivableWriteOffStatusList').selectpicker('val',a);
        }


        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });

        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail/{id}",
            updateUrl: prefix + "/edit/{id}/edit",
            removeUrl: prefix + "/remove",
            showToggle:false,
            showColumns:true,
            modalName: "发货单",
            fixedColumns: true,
            fixedNumber: 6,
            height: 560,
            uniqueId: "invoiceId",
            clickToSelect: true,
            firstLoad: false,
            // exportTypes:['excel','csv'],
            // showExport: true,
            // exportOptions:{
            //     ignoreColumn: [0,1],
            //     fileName:"在途跟踪"
            // },
            columns: [
                {
                checkbox: true
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 20,
                    switchable:false,
                    formatter: function(value, row, index) {
                        var actions = [];

                        if ([[${@permission.hasAnyPermi('tms:invoice:edit,tms:fleet:invoice:edit')}]] != "hidden"
                            && ((row.isFleetData === '1' && row.isFleetAssign !== '1') || row.isFleetData === '0')
                            && invoiceStatusMap.NEW == row.vbillstatus) {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  onclick="edit(\'' + row.invoiceId + '\',\''+row.vbillstatus+'\',\''+row.regDate+'\')" title="修改"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>');
                        }
                        if ([[${@permission.hasAnyPermi('tms:invoice:copy,tms:fleet:invoice:copy')}]] != "hidden" && ((row.isFleetData === '1' && row.isFleetAssign !== '1') || row.isFleetData === '0')) {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" onclick="copyTab(\''+ row.invoiceId +'\')" title="复制"><i class="fa fa-copy" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasAnyPermi('tms:invoice:detail,tms:fleet:invoice:detail')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="发货单明细" onclick="$.operate.detailTab(\'' + row.invoiceId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        }
                        // if ([[${@permission.hasPermi('tms:invoice:cost_detail')}]] != "hidden") {
                        //     actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="费用明细" onclick="costDetail(\'' + row.Id + '\')"><i class="fa fa-cny" style="font-size: 15px;"></i></a>');
                        // }
                        //

                        if ([[${@permission.hasAnyPermi('finance:receive:receiveDetail,finance:fleet:receive:receiveDetail')}]] != "hidden"
                            && invoiceStatusMap.NEW != row.vbillstatus) {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="receiveDetail(\'' + row.invoiceId + '\',\'' + row.isFleetAssign +'\')"><i  class="fa fa-cny" style="font-size: 15px;" ></i></a>');
                        }

                        if ([[${@permission.hasPermi('tms:invoice:detail_all')}]] != "hidden") {
                            actions.push(`<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="invocieDetail('${row.invoiceId}')"><i  class="fa fa-bars" style="font-size: 15px;" ></i></a>`);
                        }
                        if ([[${@permission.hasPermi('tms:invoice:prebook')}]] != "hidden") {
                            if ((row.custAbbr == '亚太森博' || row.custAbbr == '赛得利（南通）' || row.custAbbr == '赛得利（常州）') && ['0', '1', '2', '3', '4', '6'].includes(row.vbillstatus)) {
                                actions.push(`<a class="btn btn-xs" href="javascript:;" title="提到货预约" onclick="preBook(${index})"><i class="fa fa-fa"></i></a>`)
                            }
                        }
                        // if ([[${@permission.hasPermi('tms:invoice:operation_history')}]] != "hidden") {
                        //     actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="操作历史" onclick="operationHistory(\'' + row.invoiceId + '\')"><i  class="fa fa-list" style="font-size: 15px;" ></i></a>');
                        // }
                        return actions.join('');
                    }
                },
                {
                    title: '客户单号',
                    field: 'custOrderno',
                    align: 'left',
                    cellStyle: formatTableUnit,
                    formatter: function(value, row, index){
                        let htmlText;
                        if(value){
                            htmlText=`<span class='label badge-white' data-toggle='tooltip' data-placement="top" data-container="body" data-html='true' title='`+value+`'>`+value+`</span>`
                        }else{
                            htmlText=value
                        }
                       
                        return htmlText
                    }
                },
                // {
                //     title: '核销状态',
                //     field: 'receivableWriteOffStatus',
                //     align: 'left',
                //     formatter: function status(value, row) {
                //         let status ='';
                //         switch (row.receivableWriteOffStatus){
                //             case '0':
                //                 status = '未核销';
                //                 break;
                //             case '1':
                //                 status = '部分核销';
                //                 break;
                //             case '2':
                //                 status = '已核销';
                //                 break;
                //         }
                //         return status;
                //     }
                // },
                {
                    title: '发货单编号',
                    field: 'vbillno',
                    align: 'left',
                    width: 20,
                    switchable: false,
                    formatter: function (value,row) {

                        let enquiryNoHtml = '';
                        if (row.enquiryNo) {
                            enquiryNoHtml = `
                                <span class="label label-warning pa2" style="cursor:pointer;" data-toggle="tooltip"
                                      data-container="body" data-placement="top" data-html="true" title=""
                                      data-original-title="${row.enquiryNo}" onclick="openEnquiry('${row.rfqEnquiryId}')">询</span>

                                `
                        }

                        var context;
                        if (row.isFleetData === '1' && row.isFleetAssign === '1') {
                            let bizInvoiceVbillno = row.bizInvoiceVbillno
                            let bizSegmentVbillno = row.bizSegmentVbillno
                            let bizEntrustVbillno = row.bizEntrustVbillno
                            let bizEntrustLotVbillno = row.bizEntrustLotVbillno

                            context = '<span class="label label-primary" onclick="getDetail(\'' + bizInvoiceVbillno + '\',\''+bizSegmentVbillno+'\',\''+bizEntrustVbillno+'\',\''+bizEntrustLotVbillno+'\')">'+value+'</span>';
                        }else {
                            context = value;
                        }
                        return enquiryNoHtml + context;
                    }
                },
                {
                    title: '发货单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    width: 20,
                    switchable: false,
                    formatter: function status(row,value) {
                        var context = '';
                        var isAddReceCheck = '';
                        if (isFleet && value.isFleetAssign == '0' && value.isAddReceCheck === '1') {
                            isAddReceCheck = '（已对账）';
                        }

                        let status ='';
                        switch (value.receivableWriteOffStatus){
                            case '0':
                                status = '<span class="label label-dangerT" style="padding:1px;vertical-align: middle;margin-left:5px;" data-toggle="tooltip" data-placement="left" data-html="true" title="应收未核销">未</span>';
                                break;
                            case '1':
                                status = '<span class="label label-warningT" style="padding:1px;vertical-align: middle;margin-left:5px;" data-toggle="tooltip" data-placement="left" data-html="true" title="应收部分核销">部分</span>';
                                break;
                            case '2':
                                status = '<span class="label label-successT" style="padding:1px;vertical-align: middle;margin-left:5px;" data-toggle="tooltip"  data-placement="left" data-html="true" title="应收已核销">已</span>';
                                break;
                        }


                        vbillstatus.forEach(function (v) {
                            if (v.value == value.vbillstatus) {

                                if (value.vbillstatus == invoiceStatusMap.NEW || value.vbillstatus == invoiceStatusMap.IN_APPROVAL) {
                                    //新建 || 确认中
                                    context = '<span class="label label-primary">'+v.context+isAddReceCheck+'</span>';
                                }else if (value.vbillstatus == invoiceStatusMap.AFFIRM) {
                                    //已确认
                                    context = '<span class="label label-warning">'+v.context+isAddReceCheck+'</span>';
                                }else if (value.vbillstatus == invoiceStatusMap.PORTION_PICK_UP
                                    || value.vbillstatus == invoiceStatusMap.PICK_UP) {
                                    //部分提货 与 已提货
                                    context = '<span class="label label-info">'+v.context+isAddReceCheck+'</span>';
                                }else if (value.vbillstatus == invoiceStatusMap.PORTION_ARRIVALS
                                    || value.vbillstatus == invoiceStatusMap.ARRIVALS) {
                                    //部分到货  已到货
                                    context = '<span class="label label-success">'+v.context+isAddReceCheck+'</span>';
                                }else if (value.vbillstatus == invoiceStatusMap.PORTION_RETURNS
                                    || value.vbillstatus == invoiceStatusMap.RETURNS) {
                                    //部分回单  已回单
                                    context = '<span class="label label-primary">' + v.context+isAddReceCheck + '</span>';
                                } else {
                                    //关闭
                                    context = '<span class="label label-inverse">' + v.context+isAddReceCheck + '</span>';
                                }

                                return false;
                            }
                        });

                        // status = '<div style="padding-left: 5px;">' + status + '</div>'
                        return context + status ;
                    }
                },
                {
                    title: '调度状态',
                    field: 'segmentStatus',
                    align: 'left',
                    width: 20,
                    switchable: false,
                    formatter: function status(value,row) {
                        var context = '';
                        if (value == 0) {
                            context = '<span class="label label-primary">待调度</span>';
                        }else if (value == 1) {
                            context = '<span class="label label-warning">部分调度</span>';
                        }else if (value == 2) {
                            context = '<span class="label label-info">已调度</span>';
                        }
                        return context;
                    }
                },
                {
                    title: '客户简称',
                    field: 'custAbbr',
                    align: 'left',
                    width: 20,
                    switchable: false,
                    formatter: function status(value,row) {
                        if (row.bizCustAbbr != null) {
                            return value + '-' + row.bizCustAbbr;
                        }else {
                            return value;
                        }
                    }
                },

                {
                    title: '要求提货日',
                    field: 'reqDeliDate',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,16);
                    }
                },
                {
                    title: '要求到货日',
                    field: 'reqArriDate',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,10);
                    }
                },
                {
                    title: '调度组',
                    field: 'transLineName',
                    align: 'left',
                    formatter: function (value, row) {
                        let segTransLineName = ''
                        if (row.segTransLineName) {
                            segTransLineName = `<span class="label label-warningT" style="padding:1px;vertical-align: middle;margin-left:5px;cursor:pointer;" data-toggle="tooltip" data-placement="left" data-html="true" title="${row.segTransLineName}">改</span>`
                        }

                        return value + segTransLineName
                    }
                },
                // {
                //     title: '提货|到货方',
                //     field: 'deliAddrName',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         return row.deliAddrName+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriAddrName;
                //     }
                // },
                {
                    title: '提货',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let html = ``
                        let list = row.shippingAddressList;
                        for (const elem of list) {
                            if (elem.addressType == 0) {
                                let addr = '';
                                let addrDe = '';
                                if(elem.provinceName=="上海市"||elem.provinceName=="北京市"||elem.provinceName=="天津市"||elem.provinceName=="重庆市"){
                                    addr = '('+ elem.addrName +')' + elem.provinceName  + elem.areaName;
                                    addrDe = '('+ elem.addrName +')'+elem.provinceName  + elem.areaName+ elem.detailAddr;
                                }else{
                                    addr = '('+ elem.addrName +')'  + elem.cityName + elem.areaName;
                                    addrDe = '('+ elem.addrName +')' + elem.cityName + elem.areaName+ elem.detailAddr;
                                }
                                html += `<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='${addrDe}'>${addr}</span></br>`;

                            }
                        }


                        /*                 if (row.isMultiple === 1) {
                                             let list = row.shippingAddressList;

                                             let indx = 0
                                             for (const elem of list) {
                                                 if (elem.addressType === 0) {
                                                     //货品信息
                                                     let goodsList = elem.shippingGoodsList

                                                     let goodsName = goodsList.map(x => x.goodsName).join(",")
                                                     let num = goodsList.reduce((pre,cur)=>{ return pre+cur.num },0)
                                                     let weight = goodsList.reduce((pre,cur)=>{ return pre+cur.weight },0)
                                                     let volume = goodsList.reduce((pre,cur)=>{ return pre+cur.volume },0)

                                                     indx ++
                                                     if (indx !== 1) {
                                                         html = html + `<br>`;
                                                     }
                                                     let htmlText=``
                                                     let dataName='';
                                                     if(num){
                                                         dataName+=(num+'件|')
                                                     }
                                                     if(weight){
                                                         dataName+=(weight+'吨|')
                                                     }
                                                     if(volume){
                                                         dataName+=(volume+'m³')
                                                     }

                                                     if(elem.provinceName=="上海市"||elem.provinceName=="北京市"||elem.provinceName=="天津市"||elem.provinceName=="重庆市"){
                                                         htmlText=`<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='(`+elem.addrName +`)`+ elem.provinceName + elem.cityName + elem.areaName + elem.detailAddr+`'>(`+elem.addrName +`)`+elem.provinceName + elem.areaName+`</span>  `+goodsName+`  `+dataName
                                                     }else{
                                                         htmlText=`<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='(`+elem.addrName +`)`+ elem.provinceName + elem.cityName + elem.areaName + elem.detailAddr+`'>(`+elem.addrName +`)`+elem.cityName + elem.areaName+`</span>  `+goodsName+`  `+dataName
                                                     }

                                                     html =html+ htmlText

                                                     // html = html + `
                                                     //     提货`+indx+`：(`+ elem.addrName+ `)`+elem.provinceName + elem.cityName + elem.areaName + elem.detailAddr
                                                     //     + `  货品：`+goodsName+` `+dataName;
                                                     // ;
                                                 }

                                             }
                                             return html;
                                         }else {
                                             let num = row.numCountAdjust != 0 && $.common.isNotEmpty(row.numCountAdjust) ? row.numCountAdjust : row.numCount
                                             let weight = row.weightCountAdjust != 0 && $.common.isNotEmpty(row.weightCountAdjust) ? row.weightCountAdjust : row.weightCount
                                             let volume  = row.volumeCountAdjust != 0 && $.common.isNotEmpty(row.volumeCountAdjust) ? row.volumeCountAdjust : row.volumeCount

                                             let htmlText=``
                                             let dataName='';
                                             if(num){
                                                 dataName+=(num+'件|')
                                             }
                                             if(weight){
                                                 dataName+=(weight+'吨|')
                                             }
                                             if(volume){
                                                 dataName+=(volume+'m³')
                                             }

                                             if(row.deliProName=="上海市"||row.deliProName=="北京市"||row.deliProName=="天津市"||row.deliProName=="重庆市"){
                                                 htmlText=`<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='(`+row.deliAddrName +`)`+row.deliProName+row.deliCityName+row.deliAreaName+row.deliDetailAddr+`'>(`+row.deliAddrName +`)`+row.deliProName+row.deliAreaName+`</span>  `+row.goodsName+`  `+dataName
                                             }else{
                                                 htmlText=`<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='(`+row.deliAddrName +`)`+row.deliProName+row.deliCityName+row.deliAreaName+row.deliDetailAddr+`'>(`+row.deliAddrName +`)`+row.deliCityName+row.deliAreaName+`</span>  `+row.goodsName+`  `+dataName
                                             }

                                             html = htmlText
                                             // html = html +`提货：(`+ row.deliAddrName+ `)`+row.deliProName+row.deliCityName+row.deliAreaName+row.deliDetailAddr
                                             //     +`  货品：`+row.params.goodsName+` `+dataName
                                         }*/


                        /*let htmlText = row.isMultiple === 1 ? '<span class="label label-successT" style="padding:1px;vertical-align: middle;margin-left:5px;" data-toggle="tooltip" data-html="true" title="多装多卸">多</span>' : '';
                        if(row.deliProName=="上海市"||row.deliProName=="北京市"||row.deliProName=="天津市"||row.deliProName=="重庆市"){
                            htmlText = htmlText + `<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='(`+row.deliAddrName +`)`+row.deliProName+row.deliCityName+row.deliAreaName+row.deliDetailAddr+`'>(`+row.deliAddrName +`)`+row.deliProName+row.deliAreaName+`</span>`
                        }else{
                            htmlText = htmlText + `<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='(`+row.deliAddrName +`)`+row.deliProName+row.deliCityName+row.deliAreaName+row.deliDetailAddr+`'>(`+row.deliAddrName +`)`+row.deliCityName+row.deliAreaName+`</span>`
                        }*/

                        return html;
                    }
                },
                {
                    title: '到货',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let html = ``
                        let list = row.shippingAddressList;
                        for (const elem of list) {
                            if (elem.addressType == 1) {
                                let addr = '';
                                let addrDe = '';
                                let caAddr = '';
                                if(elem.isChangeAddress == 1){
                                    if(elem.caArriProName=="上海市"||elem.caArriProName=="北京市"||elem.caArriProName=="天津市"||elem.caArriProName=="重庆市"){
                                        addr = '('+ elem.caArriAddrName +')' + elem.caArriProName  + elem.caArriAreaName;
                                        addrDe = '('+ elem.caArriAddrName +')'+elem.caArriProName  + elem.caArriAreaName+ elem.caArriDetailAddr;
                                    }else{
                                        addr = '('+ elem.caArriAddrName +')'  + elem.caArriCityName + elem.caArriAreaName;
                                        addrDe = '('+ elem.caArriAddrName +')' + elem.caArriCityName + elem.caArriAreaName+ elem.caArriDetailAddr;
                                    }
                                    caAddr = '('+ elem.addrName +')'+elem.provinceName + elem.cityName + elem.areaName + elem.detailAddr;
                                    html += `<span class="label label-dangerT"
                                  style="padding:1px;cursor: pointer;" data-toggle="tooltip"
                                  data-placement="left" data-html="true" title="${caAddr}">改</span>`;
                                    html += `<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='${addrDe}'>${addr}</span></br>`;
                                }else{
                                    if(elem.provinceName=="上海市"||elem.provinceName=="北京市"||elem.provinceName=="天津市"||elem.provinceName=="重庆市"){
                                        addr = '('+ elem.addrName +')' + elem.provinceName  + elem.areaName;
                                        addrDe = '('+ elem.addrName +')'+elem.provinceName  + elem.areaName+ elem.detailAddr;
                                    }else{
                                        addr = '('+ elem.addrName +')'  + elem.cityName + elem.areaName;
                                        addrDe = '('+ elem.addrName +')' + elem.cityName + elem.areaName+ elem.detailAddr;
                                    }
                                    html += `<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='${addrDe}'>${addr}</span></br>`;
                                }
                            }
                        }

          /*              if (row.isMultiple === 1) {
                            let list = row.shippingAddressList;

                            let indx = 0
                            for (const elem of list) {
                                if (elem.addressType == 1) {
                                    //货品信息
                                    let goodsList = elem.shippingGoodsList

                                    let goodsName = goodsList.map(x => x.goodsName).join(",")
                                    let num = goodsList.reduce((pre,cur)=>{ return pre+cur.num },0)
                                    let weight = goodsList.reduce((pre,cur)=>{ return pre+cur.weight },0)
                                    let volume = goodsList.reduce((pre,cur)=>{ return pre+cur.volume },0)
                                    indx ++
                                    if (indx !== 1) {
                                        html = html + `<br>`;
                                    }

                                    let htmlText=``
                                    let dataName='';
                                    if(num){
                                        dataName+=(num+'件|')
                                    }
                                    if(weight){
                                        dataName+=(weight+'吨|')
                                    }
                                    if(volume){
                                        dataName+=(volume+'m³')
                                    }
                                    //上海 北京 天津 重庆 
                                    if(elem.provinceName=="上海市"||elem.provinceName=="北京市"||elem.provinceName=="天津市"||elem.provinceName=="重庆市"){
                                        htmlText=`<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='(`+elem.addrName +`)`+elem.provinceName + elem.cityName + elem.areaName+elem.detailAddr+`'>(`+elem.addrName +`)`+ elem.provinceName + elem.areaName+`</span>  `+goodsName+`  `+dataName
                                    }else{
                                        htmlText=`<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='(`+elem.addrName +`)`+elem.provinceName + elem.cityName + elem.areaName+elem.detailAddr+`'>(`+elem.addrName +`)`+ elem.cityName + elem.areaName+`</span>  `+goodsName+`  `+dataName
                                    }

                                    
                                    
                                    html =html+ htmlText
                                    
                                    // html = html + `到货` + indx + `：(`+elem.addrName +`)` + elem.provinceName + elem.cityName + elem.areaName+elem.detailAddr
                                    //     + `  货品：`+goodsName+`  `+num+`|`+weight+`|`+volume+``;
                                }

                            }
                        }else {
                            let num = row.numCountAdjust != 0 && $.common.isNotEmpty(row.numCountAdjust) ? row.numCountAdjust : row.numCount
                            let weight = row.weightCountAdjust != 0 && $.common.isNotEmpty(row.weightCountAdjust) ? row.weightCountAdjust : row.weightCount
                            let volume  = row.volumeCountAdjust != 0 && $.common.isNotEmpty(row.volumeCountAdjust) ? row.volumeCountAdjust : row.volumeCount
                            let htmlText=``
                            let dataName='';
                            if(num){
                                dataName+=(num+'件|')
                            }
                            if(weight){
                                dataName+=(weight+'吨|')
                            }
                            if(volume){
                                dataName+=(volume+'m³')
                            }
                            //上海 北京 天津 重庆 
                            if(row.arriProName=="上海市"||row.arriProName=="北京市"||row.arriProName=="天津市"||row.arriProName=="重庆市"){
                                htmlText=`<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='(`+row.arriAddrName +`)`+row.arriProName+row.arriCityName+row.arriAreaName+row.arriDetailAddr+`'>(`+row.arriAddrName +`)`+row.arriProName+row.arriAreaName+`</span>  `+row.goodsName+`  `+dataName
                            }else{
                                htmlText=`<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='(`+row.arriAddrName +`)`+row.arriProName+row.arriCityName+row.arriAreaName+row.arriDetailAddr+`'>(`+row.arriAddrName +`)`+row.arriCityName+row.arriAreaName+`</span>  `+row.goodsName+`  `+dataName
                            }
                            
                            html = htmlText
                            // html = html +`到货：(`+row.arriAddrName +`)`+row.arriProName+row.arriCityName+row.arriAreaName+row.arriDetailAddr
                            //     +`  货品：`+row.params.goodsName+`  `+num+`|`+weight+`|`+volume
                        }*/

                        /*let htmlText=``


                        let addr = ''
                        let addrDe = ''

                        let caAddrDe = ''

                        if(row.arriProName=="上海市"||row.arriProName=="北京市"||row.arriProName=="天津市"||row.arriProName=="重庆市"){
                            if (row.isChangeAddress == 1) {
                                addr = `(${row.caArriAddrName})${row.caArriProName}${row.caArriAreaName}`
                                addrDe = `(${row.caArriAddrName})${row.caArriProName}${row.caArriCityName}${row.caArriAreaName}${row.caArriDetailAddr}`

                                caAddrDe = `(${row.arriAddrName})${row.arriProName}${row.arriCityName}${row.arriAreaName}${row.arriDetailAddr}`
                            }else {
                                addr = `(${row.arriAddrName})${row.arriProName}${row.arriAreaName}`
                                addrDe = `(${row.arriAddrName})${row.arriProName}${row.arriCityName}${row.arriAreaName}${row.arriDetailAddr}`
                            }
                        }else {
                            if (row.isChangeAddress == 1) {
                                addr = `(${row.caArriAddrName})${row.caArriCityName}${row.caArriAreaName}`;
                                addrDe = `(${row.caArriAddrName})${row.caArriProName}${row.caArriCityName}${row.caArriAreaName}${row.caArriDetailAddr}`

                                caAddrDe = `(${row.arriAddrName})${row.arriProName}${row.arriCityName}${row.arriAreaName}${row.arriDetailAddr}`
                            }else {
                                addr = `(${row.arriAddrName})${row.arriCityName}${row.arriAreaName}`;
                                addrDe = `(${row.arriAddrName})${row.arriProName}${row.arriCityName}${row.arriAreaName}${row.arriDetailAddr}`
                            }
                        }


                        if (row.isChangeAddress == 1) {
                            htmlText = `<span class="label label-dangerT"
                              style="padding:1px;cursor: pointer;" data-toggle="tooltip"
                              data-placement="left" data-html="true" title="${caAddrDe}">改</span>`;
                        }

                        htmlText= htmlText +`<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='${addrDe}'>${addr}</span>`;*/

                        //上海 北京 天津 重庆
                        // if(row.arriProName=="上海市"||row.arriProName=="北京市"||row.arriProName=="天津市"||row.arriProName=="重庆市"){
                        //     htmlText=`<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='${addrDe}'>${addr}</span>`;
                        // }else{
                        //     htmlText=`<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='(`+row.arriAddrName +`)`+row.arriProName+row.arriCityName+row.arriAreaName+row.arriDetailAddr+`'>(`+row.arriAddrName +`)`+row.arriCityName+row.arriAreaName+`</span>`;
                        // }

                        return html;

                    }
                },

                {
                    title: '总件数|总重量|总体积',
                    field: '',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let dataName='';

                        let num = row.numCountAdjust != 0 && $.common.isNotEmpty(row.numCountAdjust) ? row.numCountAdjust : row.numCount
                        let weight = row.weightCountAdjust != 0 && $.common.isNotEmpty(row.weightCountAdjust) ? row.weightCountAdjust : row.weightCount
                        let volume  = row.volumeCountAdjust != 0 && $.common.isNotEmpty(row.volumeCountAdjust) ? row.volumeCountAdjust : row.volumeCount
                        
                        if(num){
                            dataName+=(num+'件|')
                        }
                        if(weight){
                            dataName+=(weight+'吨|')
                        }
                        if(volume){
                            dataName+=(volume+'m³')
                        }

                        if (row.goodsName) {
                            dataName = row.goodsName + '|' + dataName;
                        }

                        return dataName;
                    }
                },
                {
                    title: '车长车型',
                    field: 'carLen',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(carLen, value.carLen)+$.table.selectDictLabel(carType, value.carType);
                    }
                },
                // {
                //     title: '总应收(元)',
                //     field: 'totalFee',
                //     align: 'right',
                //     formatter: function status(value, row, index) {
                //         // let detail='<a href="javascript:void(0)" onclick="detail(\'' + row.entrustId + '\',\''+ row.isFleetData +'\')">'+value+'</a>';
                //         //     actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="receiveDetail(\'' + row.invoiceId + '\',\'' + row.isFleetAssign +'\')"><i  class="fa fa-cny" style="font-size: 15px;" ></i></a>');
                //         let detail = ''
                //         if (value) {
                //             detail= '<a className="btn  btn-xs" href="javascript:void(0)" title="明细" onClick="receiveDetail(\'' + row.invoiceId + '\',' + row.isFleetAssign +')"></i>'+ value +'</a>';
                //         }
                //         return detail;
                //     }
                // },
                {
                    title: '运费(元)',
                    field: 'costAmount',
                    align: 'right',
                },
                {
                    title: '其他费(元)',
                    field: 'otherFee',
                    align: 'right',
                },
                {
                    title: '总价(元)',
                    field: 'otherFee',
                    align: 'right',
                    formatter: function status(value, row) {
                        /*let costAmount = 0
                        if (row.costAmount) {
                            costAmount = row.costAmount
                        }
                        let otherFee = 0;
                        if (value) {
                            otherFee = value
                        }

                        let all = costAmount + otherFee
                        return all;*/
                        return row.totalFee
                    }

                },
                // {
                //     title: '总在途(元)',
                //     field: 'onWayAmountFee',
                //     align: 'right',
                // },
                // {
                //     title: '指导价(元)',
                //     field: 'guidingPrice',
                //     align: 'right',
                // },
                {
                    title: '成本价(元)',
                    field: 'costPrice',
                    align: 'right',
                },
                {
                    title: '创建人',
                    field: 'regUserName',
                    align: 'left'
                },
                {
                    title: '创建时间',
                    field: 'regDate',
                    align: 'left'
                },
                // {
                //     title: '修改人',
                //     field: 'corUserName',
                //     align: 'left'
                // },
                // {
                //     title: '修改时间',
                //     field: 'corDate',
                //     align: 'left'
                // },
                {
                    title: '运输方式',
                    field: 'transCode',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(transCode, value.transCode);
                    }
                },
                // {
                //     title: '多装多卸',
                //     field: 'isMultiple',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         if (row.isMultiple === 1) {
                //             return '是'
                //         }else {
                //             return '否'
                //         }
                //
                //     }
                // },
                {
                    title: '发货单备注',
                    field: 'memo',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.memo);
                    }
                },  {
                    title: '运营组',
                    field: 'salesDept',
                    align: 'left',
                    formatter: function status(value,row) {
                        let html =''
                        if (value) {
                            salesDept.forEach((data, index) => {
                                if (data.deptId == value) {
                                    html = data.deptName;
                                }
                            });
                        }
                        return html
                    }
                },
                {
                    title: '运营部',
                    field: 'custSalesName',
                    align: 'left',
                },
                {
                    title: '管理部',
                    field: 'mgmtDeptName',
                    align: 'left',
                },
                // {
                //     title: '回单照片',
                //     field: 'receiptUploadFiles',
                //     align: 'left',
                //      formatter: function(value, row, index) {
                //         var html = "<div class='picviewer'>"
                //         if(value != null && value != '') {
                //             value.forEach(function (element, index) {
                //                 html += `<img src="`+element.filePath+`"/>`
                //             });
                //         }else {
                //             html = '-';
                //         }
                //         html +="</div>"
                //         return html;
                //     }
                // },
            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });

                $('[data-toggle="tooltip"]').tooltip()
            }
        };
        $.table.init(options);
        searchPre();

    });

    /**
     * 日期插件
     */
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        var reqDeliDateStart = laydate.render({
            elem: '#reqDeliDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqDeliDateEnd = laydate.render({
            elem: '#reqDeliDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqArriDateStart = laydate.render({
            elem: '#reqArriDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqArriDateEnd = laydate.render({
            elem: '#reqArriDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
    });

    //删除
    function removeAll() {
        /*var regDate = $.table.selectColumns("regDate");
        for (var i = 0; i < regDate.length ; i++) {
            for(var j=0 ; j< CloseAccountList.length ; j++ ){
                if(regDate[i].substring(0,7)==CloseAccountList[j].yearMonth.substring(0,7)){
                    $.modal.alertWarning("该月份已关账，无法进行操作！");
                    return false;
                }
            }
        }*/

        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        //判断发货单是否是新建状态
        var vbillstatusList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["vbillstatus"];
        });
        var b = false;
        $.each(vbillstatusList, function (i, v) {
            if (v != 0) {
                b = true;
                return false;
            }
        });
        if (b) {
            $.modal.alertWarning("只有新建状态下才能删除！");
            return;
        }

        /*if (isFleet) {
            //验证发货单是否超过五天
            $.ajax({
                type: "POST",
                url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+rows.join(),
                async: false,
                success: function(r){
                    if(r.code != 0){
                        $.modal.alertError(r.msg);
                        return false;
                    }else{
                        $.modal.confirm("确认要删除选中的" + rows.length + "条数据吗?", function() {
                            var url = $.table._option.removeUrl;
                            var data = { "ids": rows.join() };
                            $.operate.submit(url, "post", "json", data);
                        });
                    }
                }
            });
        }else {}*/
        $.modal.confirm("确认要删除选中的" + rows.length + "条数据吗?", function() {
            var url = $.table._option.removeUrl;
            var data = { "ids": rows.join() };
            $.operate.submit(url, "post", "json", data);
        });


    }

    // 添加信息，以tab页展现
    function addTab() {
        var date = new Date();
        var now = date .getFullYear() + "-" +(date .getMonth()+1);
        //关账判断
        for(var j=0 ; j< CloseAccountList.length ; j++ ){
            if(now==CloseAccountList[j].yearMonth.substring(0,7)){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }


        $.modal.openTab("添加" + $.table._option.modalName, prefix + "/add/" );
    }

    /**
     * 拷贝发货单
     * @param invoiceId 发货单单号
     */
    function copyTab(invoiceId) {
        /*var date = new Date();
        var now = date .getFullYear() + "-" +(date .getMonth()+1)
        //关账判断
        for(var j=0 ; j< CloseAccountList.length ; j++ ){
            if(now==CloseAccountList[j].yearMonth.substring(0,7)){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }*/

        $.modal.openTab("拷贝" + $.table._option.modalName, prefix + "/edit/" + invoiceId +"/copy");
    }

    function openEnquiry(rfqEnquiryId) {
        $.modal.openTab("询价明细",ctx + "rfq/enquiry-view/" + rfqEnquiryId);
    }

    /**
     * 修改发货单
     */
    function edit(invoiceId, vbillstatus,regDate) {
        //关账判断
        if (checkCloseAccount()) {
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }

        if (vbillstatus != 0) {
            $.modal.alertWarning("只有新建状态下的发货单才能修改！");
            return;
        }

        $.modal.openTab("修改" + $.table._option.modalName, prefix + "/edit/" + invoiceId + "/edit");
    }

    /**
     * 确认发货单
     */
    function affirm() {
        //关账校验
        /*if (checkCloseAccount()) {
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }*/

        var rows = $.table.selectColumns("invoiceId");
        if (rows.length == 0) {
            $.modal.alertWarning("请选择确认的数据");
            return;
        }

        //判断发货单是否是新建状态
        var vbillstatusList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["vbillstatus"];
        });
        var b = false;
        $.each(vbillstatusList, function (i, v) {
            if (v != 0) {
                b = true;
                return false;
            }
        });
        if (b) {
            $.modal.alertWarning("只有新建状态下才能确认！");
            return;
        }

        var invoiceId = $.table.selectColumns($.table._option.uniqueId)+"";

        // $.modal.confirm("确定确认该发货单吗？", function () {
        //     $.operate.post(prefix + "/affirm", { "invoiceId": invoiceId });
        // });

        layer.open({
            type: 2,
            area: ['70%', '70%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "发货单确认",
            content: ctx + "invoice/affirm?invoiceIds=" + invoiceId,
            btn: ['确认', '关闭'],
            // shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });

    }

    function switchBillingMethod() {
        var rows = $.table.selectColumns("invoiceId");
        if (rows.length == 0) {
            $.modal.alertWarning("请选择数据");
            return;
        }

        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

 /*       let b = bootstrapTable.some(function(item) {
            if (item.billingMethod != "3" && item.billingMethod != "4") {
                return true;
            }
        })
        if (b) {
            $.modal.alertWarning("请选择计价方式为包车（车型）/往返包车（车型）的数据");
            return;
        }*/

        var invoiceId = $.table.selectColumns($.table._option.uniqueId)+"";

        layer.open({
            type: 2,
            area: ['40%', '32%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "单程 / 往返切换",
            content: ctx + "invoice/switchBillingMethod?invoiceId=" + invoiceId,
            btn: ['确认', '关闭'],
            // shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });

    }

    function sunshineAdd(){
        let url = prefix + "/getSunShineLotInfo"
        $.ajax({
            type: "POST",
            url: url,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.alertSuccess("阳光抓单已请求，请刷新列表查看");
                }
            }
        });
    }

    function uploadCarPic(){
        let url = prefix + "/uploadCarPic"
        $.modal.confirm("确是否上传阳光【接受】状态单据车辆行驶证？注意：请勿重复上传，否则单据无法确认", function() {
            $.ajax({
                type: "POST",
                url: url,
                // 移除 async: false
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function(r){
                    if(r.code != 0){
                        $.modal.alertError(r.msg);
                        return false;
                    }else{
                        $.modal.alertSuccess(r.msg);
                    }
                },
                error: function(xhr, status, error){
                    // 请求失败时触发（如网络错误）
                    $.modal.alertError("上传失败，请检查网络");
                },
                complete: function(){
                    // 无论成功/失败，都会执行，确保关闭遮罩层
                    $.modal.closeLoading();
                }
            });
        });
    }

    function zjhyAdd(){
        let url = prefix + "/handleZJHYApi?flag=true"
        $.ajax({
            type: "POST",
            url: url,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.alertSuccess("恒逸抓单已请求，请刷新列表查看");
                }
            }
        });
    }

    function test(){
        let url = prefix + "/test"
        $.ajax({
            type: "POST",
            url: url,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.alertSuccess("恒逸抓单已请求，请刷新列表查看");
                }
            }
        });
    }

    function zllgAdd(){
        let url = prefix + "/handleZLInvoice"
        $.ajax({
            type: "POST",
            url: url,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.alertSuccess("中粮抓单已请求，请刷新列表查看");
                }
            }
        });
    }
    function saAdd(){
        let url = prefix + "/handle66KCInvoice"
        $.ajax({
            type: "POST",
            url: url,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.alertSuccess("圣奥抓单已请求，请刷新列表查看");
                }
            }
        });
    }


    /**
     * 跳转反确认页面
     */
    function oppositeAffirm() {
        //关账校验
        if (checkCloseAccount()) {
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }

        var rows = $.table.selectColumns("invoiceId");
        if (rows.length == 0) {
            $.modal.alertWarning("请选择反确认的数据");
            return;
        }

        //判断发货单已确认状态
        var vbillstatusList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["vbillstatus"];
        });
        var b = false;
        $.each(vbillstatusList, function (i, v) {
            if (v != 1) {
                b = true;
                return false;
            }
        });
        if (b) {
            $.modal.alertWarning("只有已确认状态下才能反确认！");
            return;
        }

        var selectColumns = $.table.selectColumns($.table._option.uniqueId);

        //验证发货单是否超过五天
        $.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+selectColumns,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.open("反确认", ctx + "invoice/back_confirm/" + selectColumns + "/0",600,500);
                    //$.modal.open("发货单反确认", ctx + "trustDeed/back_confirm_pick/" + selectColumns + "/0",600,500);
                }
            }
        });
    }

    function updateContractPrice() {
        let invoiceIds = $.table.selectColumns("invoiceId");
        if (invoiceIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        let b = bootstrapTable.some(function(item) {
            if (item.vbillstatus != 0) {
                return true;
            }
        })
        if (b) {
            // $.modal.alertWarning("请选择“新建”状态的数据");
            // return;
        }

        let b1 = bootstrapTable.some(function(item) {
            if (item.ifBargain != 0) {
                return true;
            }
        })
        if (b1) {
            $.modal.alertWarning("请选择“合同价”的数据");
            return;
        }

        layer.confirm('确定重新获取合同价并更新单据价格吗?（新建状态下更新合同价与成本价，非新建状态下只更新成本价）', function (index) {
            $.ajax({
                url: ctx + "invoice/updateContractPrice",
                type: "post",
                dataType: "json",
                data: {"invoiceIds": invoiceIds.join(",")},
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function (data) {
                    if (data.code == 0) {
                        $.modal.msgSuccess(data.msg);

                        // 处理失败的记录
                        if (data.data && Object.keys(data.data).length > 0) {
                            let failedHtml = '<div style="max-height:350px;overflow-y:auto;"><table class="table table-striped table-bordered table-hover">' +
                                '<thead><tr><th>发货单号</th><th>失败原因</th></tr></thead><tbody>';

                            for (let invoiceNo in data.data) {
                                failedHtml += '<tr><td>' + invoiceNo + '</td><td>' + data.data[invoiceNo] + '</td></tr>';
                            }

                            failedHtml += '</tbody></table></div>';

                            layer.open({
                                type: 1,
                                title: '以下发货单更新失败',
                                area: ['600px', '400px'],
                                content: failedHtml
                            });
                        }

                        $.table.refresh();
                    } else {
                        $.modal.alertError("更新失败，错误信息："+data.msg);
                    }

                    $.modal.closeLoading();
                    $.modal.enable();
                },
                error: function(){
                    $.modal.msgError(data.msg);
                },
            })
            layer.close(index);
        });


    }

    function updateContractPrice_1() {
        layer.confirm('确定重新获取合同价并更新单据价格吗?', function (index) {
            $.ajax({
                url: ctx + "invoice/updateContractPrice_1",
                type: "post",
                dataType: "json",
                // data: {"invoiceIds": invoiceIds.join(",")},
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function (data) {
                    if (data.code == 0) {
                        $.modal.msgSuccess(data.msg);
                        $.table.refresh();
                    }else {
                        $.modal.alertError("更新失败，错误信息："+data.msg);
                    }

                    $.modal.closeLoading();
                    $.modal.enable();
                },
                error: function(){
                    $.modal.msgError(data.msg);
                },
            })
            layer.close(index);
        });


    }

    /**
     * 发货单关闭
     *
     */
    function invoiceClose() {
        //关账校验
        /* if (checkCloseAccount()) {
             $.modal.alertWarning("该月份已关账，无法进行操作！");
             return false;
         }

         //判断发货单是否是新建状态
         var vbillstatusList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
             return row["vbillstatus"];
         });

         var b = false;
         $.each(vbillstatusList, function (i, v) {
             if (v != 0) {
                 b = true;
                 return false;
             }
         });
         if (b) {
             $.modal.alertWarning("只有新建状态下才能关闭！");
             return;
         }
 */

        var invoiceId =  $.table.selectColumns("invoiceId").join();
        //验证是否可以货量更新
        $.ajax({
            type: "POST",
            url: ctx + "invoice/checkClose?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.open("关闭", ctx + "invoice/close/" + invoiceId,500,500);
                }
            }
        });


    }

    /**
     * 第三方费用录入
     */
    function feeEntry() {
        //关账校验
        if (checkCloseAccount()) {
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }
        var id = $.table.selectColumns($.table._option.uniqueId);

        layer.open({
            type: 2,
            area: ['60%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "第三方费用录入",
            content: ctx + "invoice/feeEntry/" + id,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function(index) {
                return true;
            }
        });

    }

    function addOtherFee(){
        var id = $.table.selectColumns($.table._option.uniqueId);
        var url = ctx + "invoice/addOtherFee?invoiceId="+id;
        $.modal.open('新增三方费用', url, 700, $(window).height() - 50);
    }


    /**
     * 第三方费用申请
     */
    function otherFeeApply() {
        //关账校验
       /* if (checkCloseAccount()) {
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }*/
        var id = $.table.selectColumns($.table._option.uniqueId);
        var isClose = $.table.selectColumns("isClose");

        $.modal.openTab("第三方费用申请", ctx + "invoice/fee_apply/" + id+"/"+isClose);
    }

    /**
     * 关账校验
     */
    function checkCloseAccount() {
        var b = false;
        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for(var i=0 ; i< isClose.length ; i++ ){
            if(isClose[i] == 1){
                b = true;
                return true;
            }
        }
        return b;
    }

    /**
     * 发票打印
     */
    function invoicePrint(){
        var vbillno = $.table.selectColumns("vbillno").join();
        var url = ctx + "report/invoicePrint/"+vbillno;
        $.modal.openTab("托运单打印", url);
    }

    /**
     * 搜索
     */
    function searchPre() {
        if ($("#reqDeliDateStart").val().trim() == '') {
            $.modal.msg("请选择要求提货日期起始日期", modal_status.WARNING);
            return;
        }
        var tmpEndDate = [[${#dates.format(#dates.createToday(), 'yyyy-MM-dd')}]]
        if ($("#reqDeliDateEnd").val().trim() != '') {
            tmpEndDate = $("#reqDeliDateEnd").val().trim();
        }
        var ymd1 = $("#reqDeliDateStart").val().trim().split("-");
        var ymd2 = tmpEndDate.split("-");
        if ((ymd2[0] - ymd1[0]) * 12 + (ymd2[1] - ymd1[1]) + (ymd2[2] - ymd1[2]) / 31 > 6) {
            $.modal.msg("要求提货日期跨度不能超过6个月", modal_status.WARNING);
            return;
        }

        var data = {};
        data.params = new Map();

        data.carLen = $.common.join($('#carLen').selectpicker('val'));
        // data.carType = $.common.join($('#carType').selectpicker('val'));
        data.transCode = $.common.join($('#transCode').selectpicker('val'));
        data.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));
        data.salesDept = $.common.join($('#salesDept').selectpicker('val'));
        // data.arriProvinceIds = $.common.join($('#arriProvinceIds').selectpicker('val'));

        data.params.receivableWriteOffStatusList = $.common.join($('#receivableWriteOffStatusList').selectpicker('val'));

        $.table.search('role-form', data);
    }

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        var deliDetailAddr= $('#deliDetailAddr').val()
        var arriDetailAddr= $('#arriDetailAddr').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        $('#deliDetailAddr').val(arriDetailAddr)
        $('#arriDetailAddr').val(deliDetailAddr)
        searchPre()
    }

    /**
     * 重置
     */
    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    //发货单所有费用明细
    function costDetail(id) {
        var url = prefix + "/cost_detail";
        $.modal.openTab("发货单费用明细", url);
    }

    /**
     * 获取指导价
     */
    // function getGuidePrice() {
    //     var ids = $.table.selectColumns($.table._option.uniqueId);
    //     var url = ctx + "invoice/getGuidePrice/" + ids;
    //     $.modal.open("获取指导价", url);
    // }



    /**
     * 详情
     */
    function receiveDetail(invoiceId,isFleetAssign) {
        var ifa = isFleetAssign === "1" ? true : false

        var url = ctx + `receive/receive_detail?invoiceId=${invoiceId}&isFleet=${isFleet}&isFleetAssign=${ifa}`;
        $.modal.openTab('应收详情',url);
    }

    /**
     * 详情
     */
    function invocieDetail(invoiceId) {
        var url = ctx + "invoice/detail_all/" + invoiceId;
        $.modal.openTab('单据详情',url);
    }


    /**
     * 操作历史记录
     */
    function operationHistory(invoiceId) {
        // var ifa = isFleetAssign === "1" ? true : false

        var url = ctx + "invoice/operation_history/" + invoiceId;
        $.modal.openTab('操作记录',url);
    }
    /**
     * 生成对账单的方法
     */
    function checking() {
        var rows =  $.table.selectColumns("invoiceId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        let b = bootstrapTable.some(function(item) {
            if (item.isFleetAssign == "1") {
                return true;
            }
        })
        if (b) {
            $.modal.alertWarning("请选择自添加数据！分配车队数据无法生成！");
            return;
        }

        if (rows.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        //判断get请求长度
        var mb = myBrowser();
        if ("IE" == mb) {
            if(rows.join().length>2000){
                $.modal.alertWarning("勾选数量超出限制，IE最大请求长度为2000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }
        if ("Chrome" == mb) {
            if(rows.join().length>8000){
                $.modal.alertWarning("勾选数量超出限制，Chrome最大请求长度为8000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }

        $.ajax({
            type: "post",
            dataType: "json",
            data:  {invoiceIds : rows.join()},
            url:  ctx +"receive/checkAddByInvoiceId",
            success: function(result) {
                if (!result.data) {
                    $.modal.alertWarning(result.msg);
                    return;
                }

                $.modal.openTab("生成对账单", ctx + "receive/checking?invoiceId=" + rows.join() + "&receiveDetsailIds=");
            }
        });
    }



    /**
     * 加入对账单的方法
     */
    function insertChecking() {
        var rows =  $.table.selectColumns("invoiceId");

        var hasApplyLen = 0;
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        let b = bootstrapTable.some(function(item) {
            if (item.isFleetAssign == "1") {
                return true;
            }
        })
        if (b) {
            $.modal.alertWarning("请选择自添加数据！分配车队数据无法生成！");
            return;
        }

        if (rows.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        //判断get请求长度
        var mb = myBrowser();
        if ("IE" == mb) {
            if(rows.join()>2000){
                $.modal.alertWarning("勾选数量超出限制，IE最大请求长度为2000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }
        if ("Chrome" == mb) {
            if(rows.join()>8000){
                $.modal.alertWarning("勾选数量超出限制，Chrome最大请求长度为8000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }

        $.ajax({
            type: "post",
            dataType: "json",
            data:  {invoiceIds : rows.join()},
            url:  ctx +"receive/checkAddByInvoiceId",
            success: function(result) {
                if (!result.data) {
                    $.modal.alertWarning(result.msg);
                    return;
                }

                var customerId =  bootstrapTable[0]["customerId"];
                var getStr = ctx + "receive/insertChecking?customerId="+customerId+"&invoiceIds="+rows+"&receiveDetsailIds="

                $.modal.open("加入对账单", getStr);
            }
        });

    }

    /**
     * 调整单导入
     */
    function adjustImport() {
        layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入调整单数据',
            content: $('#importTpl').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var index = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#file')[0].files[0]);
                formData.append("updateSupport", $("input[name='updateSupport']").is(':checked'));
                $.ajax({
                    url: ctx + "receive/adjustImport",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code == web_status.WARNING) {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });

    }

    function invoiceExport() {
        var data = $("#role-form").serializeArray();

        $.modal.confirm("确定导出所有发货单数据吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(ctx + "invoice/export", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    /**
     *
     */
    function adjustExport(){
        var data = $("#role-form").serializeArray();
        var invoiceStatusList = {};
        invoiceStatusList.name = "invoiceStatus"
        invoiceStatusList.value = $.common.join($('#vbillstatus').selectpicker('val'));
        data.push(invoiceStatusList);
        //
        data.push({
            name: "params[deliProvinceId]",
            value: $("#deliProvinceId").val()
        })
        data.push({
            name: "params[deliCityId]",
            value: $("#deliCityId").val()
        })
        data.push({
            name: "params[deliAreaId]",
            value: $("#deliAreaId").val()
        })
        data.push({
            name: "params[deliDetailAddr]",
            value: $("#deliDetailAddr").val()
        })
        data.push({
            name: "params[arriProvinceId]",
            value: $("#arriProvinceId").val()
        })
        data.push({
            name: "params[arriCityId]",
            value: $("#arriCityId").val()
        })
        data.push({
            name: "params[arriAreaId]",
            value: $("#arriAreaId").val()
        })
        data.push({
            name: "params[arriDetailAddr]",
            value: $("#arriDetailAddr").val()
        })

        $.modal.confirm("确定导出所有" +
            $.table._option.modalName + "吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(ctx + "receive/exportAdjust", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    function receiptExport() {
        var data = $("#role-form").serializeArray();
        var obj = {}
        for (let i = 0; i < data.length; i++) {
            obj[data[i].name] = data[i].value;
        }
        obj.carLen = $.common.join($('#carLen').selectpicker('val'));
        //obj.carType = $.common.join($('#carType').selectpicker('val'));
        obj.transCode = $.common.join($('#transCode').selectpicker('val'));
        obj.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));
        obj.salesDept = $.common.join($('#salesDept').selectpicker('val'));
        var errMsg = []
        if (!obj['params[reqDeliDateStart]']) {
            errMsg.push("请选择要求提货开始日期");
        }
        if (!obj['params[reqDeliDateEnd]']) {
            errMsg.push("请选择要求提货截止日期");
        }
        if (errMsg.length > 0) {
            $.modal.msgError(errMsg.join("<br>"))
            return
        }
        var t = obj['params[reqDeliDateStart]'].split("-");
        var start = new Date(t[0], t[1] - 1, t[2], 0, 0, 0, 0);
        console.log(start)
        t = obj['params[reqDeliDateEnd]'].split("-");
        var end = new Date(t[0], t[1] - 1, t[2], 0, 0, 0, 0);
        console.log(end)
        $.modal.confirm("确定导出回单吗？", function() {
            // $.modal.loading("正在导出数据，请稍后...");
            $("#receiptForm").remove();
            //$("[name='receiptFormTarget']").remove();
            $("body").append("<form id='receiptForm' method='post' action='"+ctx + "invoice/exportReceipt"+"' target='_blank' style='display:none'></form>");
            for (var k in obj) {
                if (obj[k] != null) {
                    $("#receiptForm").append("<input name='" + k + "' value='" + obj[k] + "' />");
                }
            }
            //$("body").append("<iframe name='receiptFormTarget' style='display:none'></iframe>");
            $("#receiptForm").submit()
        });
    }
    
    function receiptDownload() {

        layer.open({
            type: 1,
            title: '回单下载',
            area: ['460px', '260px'],
            offset: '20%',
            zIndex: 10,
            //shade: false,
            content: `<div style="padding: 5px 5px 5px 10px">
                <div>
                    <label>其它条件：</label>
                    <label style="width:180px"><input class="form-control" name="dateSc" laydate-range placeholder="回单上传日期"></label>
                </div>
                <div>图片名称格式</div>
                <div style="margin-top: 5px">
                    <label style="width: 120px"><input checked type="radio" name="ff" value="date,deli,arri,car,driver"> [默认格式]</label>
                    <label class="fileNameTag"><span>提货日期</span><span>提货地</span><span>到货地</span><span>车辆</span><span>司机</span></label>
                </div>
                <div style="margin-top: 5px">
                    <label style="width: 120px"><input type="radio" name="ff" value="car,driver,deli,arri,date" gs2> 云智能格式</label>
                    <label class="fileNameTag"><span>车辆</span><span>司机</span><span>提货地</span><span>到货地</span><span>提货日期</span></label>
                </div>
                <div style="margin-top: 5px">
                    <label style="width: 120px"><input type="radio" name="ff" value="date,arriName,car,driver" gs3> 星源材质格式</label>
                    <label class="fileNameTag"><span>提货日期</span><span>到货单位</span><span>车辆</span><span>司机</span></label>
                </div>
            </div>`,
            skin: '',
            yes: function(index, layero) {
                var data = $("#role-form").serializeArray();
                var obj = {}
                for (let i = 0; i < data.length; i++) {
                    obj[data[i].name] = data[i].value;
                }
                obj.carLen = $.common.join($('#carLen').selectpicker('val'));
                //obj.carType = $.common.join($('#carType').selectpicker('val'));
                obj.transCode = $.common.join($('#transCode').selectpicker('val'));
                obj.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));
                obj.salesDept = $.common.join($('#salesDept').selectpicker('val'));
                console.log(obj)
                var errMsg = []
                if (!obj['params[reqDeliDateStart]']) {
                    errMsg.push("请选择要求提货开始日期");
                }
                if (!obj['params[reqDeliDateEnd]']) {
                    errMsg.push("请选择要求提货截止日期");
                }
                if (!obj['custAbbr']) {
                    errMsg.push("请输入客户简称(全匹配)");
                }
                if (errMsg.length > 0) {
                    $.modal.msgError(errMsg.join("<br>"))
                    return
                }
                $.modal.confirm("确定下载回单吗？", function() {
                    $.modal.loading("正在导出，请稍候...");
                    obj['format'] = layero.find('[name=ff]:checked').val()
                    var dateSc = layero.find('[name=dateSc]').val();
                    if (dateSc) {
                        dateSc = dateSc.split(' - ');
                        obj['params[dateScStart]'] = dateSc[0];
                        obj['params[dateScEnd]'] = dateSc[1];
                    }

                    $.post(prefix + '/downloadReceipt', obj, function (result) {
                        if (result.code == web_status.SUCCESS) {
                            layer.close(index);
                            window.location.href = ctx + "common/download?fileName=" + encodeURI(result.data) + "&delete=" + true;
                        } else if (result.code == web_status.WARNING) {
                            $.modal.alertWarning(result.msg)
                        } else {
                            $.modal.alertError(result.msg);
                        }
                        $.modal.closeLoading();
                    })
                });
            },
            btn: ['下载', '取消'],
            end: function() {
                $('#toolbar').show()
            },
            success: function(layero) {
                $('#toolbar').hide()
                $('#role-form').parent().show();
                let custAbbr = $('#role-form').find('[name=custAbbr]').val()
                $('.layui-layer-shade').css('top', $('#role-form').parent().outerHeight(true) + 'px');
                if (custAbbr.indexOf('云智能') >= 0) {
                    $('[gs2]').prop("checked", true);
                } else if (custAbbr.indexOf('星源材质') >= 0) {
                    $('[gs3]').prop("checked", true);
                }

                layui.use('laydate', function () {
                    var laydate = layui.laydate;
                    laydate.render({
                        elem: layero.find('[laydate-range]'),
                        type: 'date',
                        trigger: 'click',
                        range: true
                    });
                });
            }
        })
    }

    function myBrowser(){
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isOpera = userAgent.indexOf("Opera") > -1;
        if (isOpera) {
            return "Opera"
        }; //判断是否Opera浏览器
        if (userAgent.indexOf("Firefox") > -1) {
            return "FF";
        } //判断是否Firefox浏览器
        if (userAgent.indexOf("Chrome") > -1){
            return "Chrome";
        }
        if (userAgent.indexOf("Safari") > -1) {
            return "Safari";
        } //判断是否Safari浏览器
        if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
            return "IE";
        }; //判断是否IE浏览器
    }

    function getDetail(bizInvoiceVbillno,bizSegmentVbillno,bizEntrustVbillno,bizEntrustLotVbillno){
        console.log(bizInvoiceVbillno)
        $(".popup").css("display",'block');
        $(".one").text(bizInvoiceVbillno);
        $(".two").text(bizSegmentVbillno);
        $(".three").text(bizEntrustVbillno);
        $(".four").text(bizEntrustLotVbillno);
    }

    $(".closed").click(function(){
        $(".popup").css("display",'none');
    });

    function otherFeeInput(){
        let invoiceIds = $.table.selectColumns("invoiceId");
        let customerIds = $.table.selectColumns("customerId");
        if(customerIds.length > 1){
            $.modal.alertWarning("只能选择相同客户");
            return;
        }

        let url = prefix + "/otherFeeInput?invoiceIds="+invoiceIds.join(",");
        $.modal.openTab("第三方费用录入",url);
    }

    function adjustRecord(){
        var url = ctx + "receive/adjustRecordCheck";
        $.modal.openTab('调整单记录',url);
    }

    function getFrontFormatDate() {
        var date = new Date();
        date.setDate(date.getDate()-30);
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() + 1 ;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate;


        return currentdate;
    }

    function formatTableUnit(value, row, index) {
        return {
            css: {
                "white-space": "nowrap",
                "text-overflow": "ellipsis",
                "overflow": "hidden",
                "max-width": "100px"
            }
        }

    }

    function changeProvinceSel(){
        if(changeFlag){
            $("#arriProMultiple").css('display', 'none')
            $("#arriPro").css('display', 'block')
            $("#arriProvinceIds").selectpicker('deselectAll');
        }else{
            $("#arriProMultiple").css('display', 'block')
            $("#arriPro").css('display', 'none')
            $("#arriProvinceId").val("");
            $("#arriCityId").val("");
            $("#arriAreaId").val("");
            $("#arriDetailAddr").val("");
        }
        changeFlag = !changeFlag;
    }

    function importInvoice() {
        layer.open({
            type: 1,
            area: ['500px', '400px'],
            fix: false,
            //不固定
            maxmin: true,
            skin: '',
            shade: 0.3,
            title: '导入发货单',
            content: $('#importTpl2').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            success: function (layero, index) {
                layero.find('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                    if ($(e.target).attr('aria-controls') == 'XA') { // 精简版
                        $('#fileMsg22').text('');
                    } else if ($(e.target).attr('aria-controls') == 'XB') { // 通用版
                        $('#fileMsg').text('');
                    }
                    globalInvoiceList = null;
                    $("#templateMsg").html("")
                })
            },
            btn1: function(index, layero){
                if (globalInvoiceList == null || globalInvoiceList.length == 0) {
                    $.modal.alertError("没有可导入的数据")
                    return;
                }
                $.modal.confirm("确认导入这" + globalInvoiceList.length + "条发货单数据吗？", function(){
                    var tt = $.modal.layerLoading("导入中，请稍候...");
                    $.ajax({
                        url: ctx + 'invoice/importBatch',
                        contentType: "application/json;charset=UTF-8",
                        data: JSON.stringify(globalInvoiceList),
                        type: 'POST',
                        dataType: 'json',
                        success: function (result) {
                            layer.close(tt)
                            if (result.code != 0) {
                                $.modal.alertError(result.msg);
                            } else {
                                $.table.refresh();
                                layer.close(index)
                            }
                        }
                    })
                })
            }
        });
    }

    function ledgerExport() {
        var data = $("#role-form").serializeArray();
        $.modal.confirm("确定导出所有台账数据吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(prefix + "/ledgerExport", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.data) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    function process() {
        var spNo = $.table.selectColumns("spNo")[0];
        if (!spNo) {
            $.modal.msgWarning("尚未提交审批")
            return
        }
        var vbillstatus = $.table.selectColumns("vbillstatus")[0];
        var invoiceId = $.table.selectColumns("invoiceId")[0];
        wecom_process(spNo, function(status, index){
            if ((status == 3 || status == 2) && vbillstatus == 9) {
                //let n = $('#spRecordTrs').find("tr").length;
                let tr = '<tr><td colspan="5" align="center"><a href="javascript:;" onclick="resync(\''+spNo+'\', this, '+index+',\''+invoiceId+'\')">审批结束未更新状态？点击重新更新</a></td></tr>';
                $('#spRecordTrs').append(tr);
            }
        });
    }
    function resync(spNo, btn, layerIndex, invoiceId) {
        $.ajax({
            url: ctx + "invoice/resync",
            type: 'post',
            data: "spNo="+spNo,
            success: function (result) {
                if (result.code == 0) {
                    $.table.refresh();
                    layer.close(layerIndex);
                } else if (result.code == 600) {
                    $.modal.msgError(result.msg)
                    if ($('#errHandler').length == 0) {
                        let tr = '<tr id="errHandler"><td colspan="5" align="center"><a href="javascript:;" style="color:red" onclick="renew(\'' + invoiceId + '\', this, ' + layerIndex + ')">更新异常？点击重置发货单为新建状态</a></td></tr>';
                        $('#spRecordTrs').append(tr);
                    }
                } else {
                    $.modal.msgError(result.msg)
                }
            }
        })
    }
    function renew(invoiceId, btn, layerIndex) {
        $.modal.confirm("确认将该发货单重置为新建状态吗？", function(){
            $.ajax({
                url: prefix + "/renew",
                type: 'post',
                data: "invoiceId="+invoiceId,
                success: function (result) {
                    if (result.code == 0) {
                        $.table.refresh();
                        layer.close(layerIndex);
                    } else {
                        $.modal.msgError(result.msg)
                    }
                }
            })
        })
    }


    function copyDisData() {
        var rows = $.table.selectColumns("vbillno");
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        let vbillno = rows.join();
        console.log(vbillno)

        layer.open({
            type: 1,
            title: '拷贝运力信息',
            closeBtn: 0,
            area: ['85%', '85%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: $('#copy_dis_data_html').html(),
            btn: ['拷贝','取消'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {
                $.ajax({
                    url: ctx + "invoice/get_copy_dis_data",
                    type: "post",
                    dataType: "json",
                    data: {"invoiceNo": vbillno},
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function (data) {
                        if (data.code === 0){

                            let list = data.data.list;

                            let html = '';

/*                            list.forEach(item => {
                                const { invoiceNo, deliCityName, arriCityName, disDataList } = item;

                                let itemHtml = `
                                            <div>
                                              <div style="margin-top: 15px;font-size: 20px">
                                                <span>${invoiceNo}</span>
                                                <span>${deliCityName}->${arriCityName}</span>
                                              </div>
                                              <table class="table table-bordered mt5 table-hover" >
                                                <thead style="background-color: #f2f2f2; color: #333;">
                                                  <tr>
                                                    <td>车长</td>
                                                    <td>车号</td>
                                                    <td>司机姓名</td>
                                                    <td>司机电话</td>
                                                    <td>司机身份证号</td>
                                                    <td>司机姓名2</td>
                                                    <td>司机电话2</td>
                                                    <td>司机身份证号2</td>
                                                    <td>预计到厂时间</td>
                                                  </tr>
                                                </thead>
                                                <tbody id="bzjTbody">
                                          `;

                                if (disDataList.length === 0) {
                                    itemHtml += `
                                                <tr><td colspan="9" style="text-align: center;">暂无数据</td></tr>
                                            `;
                                } else {
                                    disDataList.forEach(disItem => {
                                        itemHtml += `
                                            <tr>
                                              <td>${disItem.carLen == null ? '' : disItem.carLen}</td>
                                              <td>${disItem.carno == null ? '' : disItem.carno}</td>
                                              <td>${disItem.driverName == null ? '' : disItem.driverName}</td>
                                              <td>${disItem.driverMobile == null ? '' : disItem.driverMobile}</td>
                                              <td>${disItem.driverCardId == null ? '' : disItem.driverCardId}</td>
                                              <td>${disItem.driverName2 == null ? '' : disItem.driverName2}</td>
                                              <td>${disItem.driverMobile2 == null ? '' : disItem.driverMobile2}</td>
                                              <td>${disItem.driverCardId2 == null ? '' : disItem.driverCardId2}</td>
                                              <td>${disItem.estimatedArrivalTime == null ? '' : disItem.estimatedArrivalTime}</td>
                                            </tr>
                                          `;
                                    });
                                }

                                itemHtml += `
                                            </tbody>
                                          </table>
                                        </div>
                                      `;

                                html += itemHtml;
                            });

                            html += `<div style="float: right;font-size: 14px;">共${data.data.count}条数据</div>`*/


                            let copyText = '';

                            list.forEach(item => {
                                const { invoiceNo, deliCityName, arriCityName, disDataList } = item;
                                copyText += `${invoiceNo}：${deliCityName}->${arriCityName}\n`;

                                disDataList.forEach((disItem, index) => {
                                    let carLen = disItem.carLen == null ? '' : disItem.carLen + "，"
                                    let carno = disItem.carno == null ? '' : disItem.carno + "，"
                                    let driverName = disItem.driverName == null ? '' : disItem.driverName + "，"
                                    let driverMobile = disItem.driverMobile == null ? '' : disItem.driverMobile + "，"
                                    let driverCardId = disItem.driverCardId == null ? '' : disItem.driverCardId + "，"
                                    let driverName2 = disItem.driverName2 == null ? '' : disItem.driverName2 + "，"
                                    let driverMobile2 = disItem.driverMobile2 == null ? '' : disItem.driverMobile2 + "，"
                                    let driverCardId2 = disItem.driverCardId2 == null ? '' : disItem.driverCardId2 + "，"
                                    let estimatedArrivalTime = disItem.estimatedArrivalTime == null ? '' : formatDate(disItem.estimatedArrivalTime);

                                    copyText += `${index + 1}）${carLen}${carno}${driverName}${driverMobile}${driverCardId}${driverName2}${driverMobile2}${driverCardId2}预计${estimatedArrivalTime}到场\n`;
                                });

                                copyText += '\n';
                            });

                            html = `<textarea style="width: 100%;height: 600px;resize: none;" id="copy_dis_data"
                                              rows="10" cols="50">${copyText}</textarea>
`
                            $("#copy_dis_data_html_div").empty();
                            $("#copy_dis_data_html_div").append(html)

                        }

                        $.modal.closeLoading();
                        $.modal.enable();
                    }
                })

            },
            yes: function (index, layero) {
                $.ajax({
                    url: ctx + "invoice/copy_dis_data",
                    type: "post",
                    dataType: "json",
                    data: {"invoiceNo": vbillno},
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function (data) {
                        if (data.code === 0) {
                            let list = data.data.list;

/*                            let copyText = '';

                            list.forEach(item => {
                                const { invoiceNo, deliCityName, arriCityName, disDataList } = item;
                                copyText += `${invoiceNo}：${deliCityName}->${arriCityName}\n`;

                                disDataList.forEach((disItem, index) => {
                                    let carLen = disItem.carLen == null ? '' : disItem.carLen + "，"
                                    let carno = disItem.carno == null ? '' : disItem.carno + "，"
                                    let driverName = disItem.driverName == null ? '' : disItem.driverName + "，"
                                    let driverMobile = disItem.driverMobile == null ? '' : disItem.driverMobile + "，"
                                    let driverCardId = disItem.driverCardId == null ? '' : disItem.driverCardId + "，"
                                    let driverName2 = disItem.driverName2 == null ? '' : disItem.driverName2 + "，"
                                    let driverMobile2 = disItem.driverMobile2 == null ? '' : disItem.driverMobile2 + "，"
                                    let driverCardId2 = disItem.driverCardId2 == null ? '' : disItem.driverCardId2 + "，"
                                    let estimatedArrivalTime = disItem.estimatedArrivalTime == null ? '' : formatDate(disItem.estimatedArrivalTime);

                                    copyText += `${index + 1}）${carLen}${carno}${driverName}${driverMobile}${driverCardId}${driverName2}${driverMobile2}${driverCardId2}预计${estimatedArrivalTime}到场\n`;
                                });

                                copyText += '\n';
                            });

                            // copyText += ` 共${data.data.count}条数据`

                            const tempTextarea = document.createElement('textarea');
                            tempTextarea.value = copyText;
                            document.body.appendChild(tempTextarea);
                            tempTextarea.select();
                            let b = document.execCommand('copy');
                            if (b) {
                                layer.msg('复制成功', {time: 2000, icon:1});
                            }else {
                                layer.msg('复制成功', {time: 2000, icon:2});
                            }

                            document.body.removeChild(tempTextarea);*/

                            $("#copy_dis_data").select();
                            let b = document.execCommand('copy');
                            if (b) {
                                layer.msg('复制成功', {time: 2000, icon:1});
                            }else {
                                layer.msg('复制成功', {time: 2000, icon:2});
                            }

                            $.modal.closeLoading();
                            $.modal.enable();
                        }
                    }
                })


                layer.close(index);
            }
        })


    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = padZero(date.getMonth() + 1);
        const day = padZero(date.getDate());
        const hour = padZero(date.getHours());
        const minute = padZero(date.getMinutes());
        return `${year}-${month}-${day} ${hour}时`;
    }

    function padZero(num) {
        return num < 10 ? `0${num}` : num;
    }

    function preBook(idx) {
        let row = $('#bootstrap-table').bootstrapTable('getData')[idx];
        let url = ctx + "ext/pre-book/ytsb?custOrderno=" + row.custOrderno + "&customerId=" + row.customerId;
        $.modal.open(row.custOrderno + "提到货预约", url, 980, document.documentElement.clientHeight - 50)
    }
</script>

<script id="copy_dis_data_html" type="text/template">
    <div class="form-content" id="copy_dis_data_html_div">

    </div>
</script>

</body>
</html>