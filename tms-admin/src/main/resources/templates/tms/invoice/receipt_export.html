<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<meta charset="UTF-8">
<style>
* {font-size:13px;}
</style>
<!-- 引入样式 -->
<link href="https://www.qixin56.com/js/static/element-2.15.5/theme-chalk/index.min.css" rel="stylesheet">
<!-- 引入组件库 -->
<script src="https://www.qixin56.com/js/static/<EMAIL>"></script>
<script src="https://www.qixin56.com/js/static/element-2.15.5/index.min.js"></script>
</head>
<body>
<div id="app">
<el-table :data="pageData" style="width: 100%" border :default-expand-all="true" size="small" :height="pageHeight-60+'px'">
    <el-table-column type="index" width="50"></el-table-column>
	<el-table-column type="expand">
      <template slot-scope="props">
        <el-image v-for="(item,i) in convertImgs(props.row.IMGS)" :src="item" style="width:50px;height:50px;margin-right:5px;" :preview-src-list="convertImgs(props.row.IMGS)"></el-image>
      </template>
    </el-table-column>
    <el-table-column label="发货单号" prop="VBILLNO" width="140px"></el-table-column>
    <el-table-column label="客户简称" prop="CUST_ABBR"></el-table-column>
    <el-table-column label="要求提货日期" prop="REQ_DELI_DATE" width="160px"></el-table-column>
    <el-table-column label="提货地址" :show-overflow-tooltip="true" prop="DELI_ADDR"></el-table-column>
    <el-table-column label="到货地址" :show-overflow-tooltip="true" prop="ARRI_ADDR"></el-table-column>
    <el-table-column label="要求车长车型" width="140px">
		<template slot-scope="scope">{{scope.row.CAR_LEN_NAME}}米{{scope.row.CAR_TYPE_NAME}}</template>
	</el-table-column>
  </el-table>
  <div style="text-align:right">
  <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="page"
      :page-sizes="[10, 20, 40, 100, 200]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="list.length">
    </el-pagination>
	</div>
</div>
<script>
new Vue({
	el: '#app',
	data() {
		return {
			list: [(${list})],
			page: 1,
			pageSize: 10,
			pageHeight: 100
		}
	},
	created () {
		this.pageHeight = document.documentElement.clientHeight
        var that = this;
        window.addEventListener('resize', function() {
            that.pageHeight = document.documentElement.clientHeight
        })
	},
	computed: {
		pageData () {
			var start = (this.page - 1) * this.pageSize;
			var end = this.page * this.pageSize;
			return this.list.slice(start, end)
		}
	},
	methods: {
		convertImgs: function(imgs) {
            if (imgs) {
                var arr = imgs.split(',');
                for(var i=0;i<arr.length;i++) {
                    arr[i] = "https://www.qixin56.com/g7" + arr[i];
                }
                return arr;
            } else {
                return [];
            }
		},
		handleSizeChange: function(size) {
			this.page = 1;
			this.pageSize = size;
		},
		handleCurrentChange: function(val) {
			this.page = val
		}
	}
})
</script>
</body>
</html>
