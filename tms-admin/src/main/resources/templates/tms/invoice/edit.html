<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
      xmlns="http://www.w3.org/1999/html">
<head>
    <th:block th:include="include :: header('发货单')" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>

</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
        color: #808080;
    }
    .flex_left{
        width: 100%;
        line-height: 26px;
        /* text-align: right; */
        color: #333333 !important;
    }
    .fcff.flex_left{
        color: #ff1f1f !important;
    }
    /* .input-group{
        display: block;
    } */
    .flex_right{
        width: 100%;
        /* flex:1; */
        /*line-height: 26px;*/
    }
    /*.row .form-group label[class*='col-'] {*/
    /*    text-align: right;*/
    /*    line-height: 30px;*/
    /*    margin-bottom: 0;*/
    /*}*/
    .addbtn{
        width: 120px;
        text-align: center;
        color: #fff;
        background: #1ab394;
        line-height: 30px;
        border-radius: 5px;
        cursor: pointer;
    }
    .addGoods{
        color: #1ab394;
        line-height: 30px;
        cursor: pointer;
    }
    .wap_content{
        padding: 10px 10px;
        border: 1px #eee solid;
        border-radius: 3px;

    }
    .line20{
        line-height: 20px;
    }
    .line20T{
        display: inline-block;
        /* width: calc((95% -22px)/3); */
        vertical-align: middle;

        overflow: hidden;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        white-space: nowrap;
    }
    .fh_edit .line20T{
        text-decoration:line-through;
        color: #666666;
    }
    
    .toText{
        display:inline-block;
        padding: 2px 4px;
        border-radius: 5px;
        color: #fff;
        vertical-align: middle;
    }
    .toTextT{
        width: 21px;
        display:inline-block;
        text-align: right;
    }
    .fcff6{
        color: #ff6c00;
    }
    .hisbtn{
        line-height: 25px;
        border-radius: 20px;
        width: 140px;
        text-align: center;
        color: #1ab394;
        border: 1px #1ab394 solid;
        cursor: pointer;
    }
    .eye{
        width: 20px;
        height: 20px;
        background: #1ab394 url("../../img/eyes.png") no-repeat center;
        background-size: 20px 20px;
        border-radius: 50%;
        /*display: inline-block;*/
    }
    .disin{
        display: inline-block;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #EBF5FC;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .tooltips {
        /*position: relative;*/
        display: inline-block;
        /*border-bottom: 1px dotted black;*/
    }

    .tooltips .tooltiptext {
        visibility: hidden;

        width: 400px;
        background-color: black;
        color: #fff;
        border-radius: 6px;
        text-indent: 15px;
        padding: 5px 5px;
        line-height: 20px;
        /* 定位 */
        position: absolute;
        z-index: 9999;
    }

    .tooltips:hover .tooltiptext {
        visibility: visible;
    }
    .xh{
        width: 20px;
        height: 20px;
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
        margin-top: 20px;
        background: #f1f2f6;
    }
    .fhf{
        border: 1px #eee solid;
        padding: 10px 10px;
        position: relative;
    }
    .shf{
        border: 1px #eee solid;
        padding: 10px 10px;
        position: relative;
    }
    .disf{
        display: flex;
    }
    .fh_addr{
        flex: 1;
        padding: 0 5px;
    }
    .fhf_table{
        /* background: #fefcd6; */
        padding: 10px 10px;
    }
    .fhf_table tr{
        background: #fff;
    }
    .fhf_close{
        position: absolute;
        left: -10px;
        top: -10px;
        width: 20px;
        height: 20px;
    }
    .texts{
        width: 100%;
        overflow:hidden;
        text-overflow:ellipsis;
        -o-text-overflow:ellipsis;
        white-space:nowrap;
    }
    .form-control,.input-group-addon,.table-bordered td, .table-bordered th,button[disabled], html input[disabled],.table>thead>tr>th,
    .panel,.panel-body,.table-bordered,.fhf,.shf{
        border-color: #A6A6A6 !important;
    }
    label{
        margin-bottom: 0;
    }
    .table>tbody>tr>td+.table>tbody>tr>td{
        border-left: 1px solid;
    }
    .form-control,.input-group-addon{
        border-radius: 4px;
    }
    .panel-group .panel{
        border-radius: 0;
    }
    .fa-times-circle:before{
        background-color: #ffffff;
    }
    .frT{
        display: inline-block;
        vertical-align: bottom;
        /* text-align: right; */
    }
    .nopa{
        padding: 0;
    }
    .nopa+.nopa{
        padding-left: 5px;
    }
    .checkbox{
        display: inline-block;
        vertical-align: middle;
    }
    /* 联系人信息可点击样式 */
    .contact-info-clickable {
        cursor: pointer;
        color: #1ab394;
        text-decoration: underline;
        transition: color 0.3s;
    }
    .contact-info-clickable:hover {
        color: #0d8f73;
        background-color: #f0f8ff;
        padding: 2px 4px;
        border-radius: 3px;
    }
    /* 联系人修改模态框样式 */
    .contact-edit-modal {
        display: none;
        position: fixed;
        z-index: 10000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }
    .contact-edit-content {
        background-color: #fefefe;
        margin: 15% auto;
        padding: 20px;
        border: 1px solid #888;
        border-radius: 5px;
        width: 400px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .contact-edit-header {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 20px;
        color: #333;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    .contact-edit-form .form-group {
        margin-bottom: 15px;
    }
    .contact-edit-form label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #555;
    }
    .contact-edit-form input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }
    .contact-edit-buttons {
        text-align: right;
        margin-top: 20px;
        border-top: 1px solid #eee;
        padding-top: 15px;
    }
    .contact-edit-buttons button {
        margin-left: 10px;
        padding: 8px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }
    .contact-edit-save {
        background-color: #1ab394;
        color: white;
    }
    .contact-edit-save:hover {
        background-color: #0d8f73;
    }
    .contact-edit-cancel {
        background-color: #f4f4f4;
        color: #666;
        border: 1px solid #ddd;
    }
    .contact-edit-cancel:hover {
        background-color: #e8e8e8;
    }
</style>
<body>

<!-- 联系人信息修改模态框 -->
<div id="contactEditModal" class="contact-edit-modal">
    <div class="contact-edit-content">
        <div class="contact-edit-header">
            修改联系人信息
        </div>
        <div class="contact-edit-form">
            <div class="form-group">
                <label for="editContactName">联系人姓名：</label>
                <input type="text" id="editContactName" maxlength="20" placeholder="请输入联系人姓名">
            </div>
            <div class="form-group">
                <label for="editContactMobile">联系电话：</label>
                <input type="text" id="editContactMobile" maxlength="11" placeholder="请输入联系电话">
            </div>
        </div>
        <div class="contact-edit-buttons">
            <button type="button" class="contact-edit-cancel" onclick="closeContactEditModal()">取消</button>
            <button type="button" class="contact-edit-save" onclick="saveContactInfo()">保存</button>
        </div>
    </div>
</div>
<div class="form-content">
    <form id="form-invoice-edit" class="form-horizontal" novalidate="novalidate" th:object="${invoice}">
        <input type="hidden" id="param" th:if="${type == 'copy'}">

        <input name="rfqEnquiryId" id="rfqEnquiryId" th:value="${rfqEnquiryId}" type="hidden">
        <input name="rfqEnquiryLineId" th:value="${rfqEnquiryLineId}" type="hidden">
        <input name="rfqCartypeId" th:value="${rfqCartypeId}" type="hidden">

        <input id="historyGuidingPriceParam" type="hidden">
        <input id="historyGuidingPriceDetailParam" type="hidden">

        <input th:field="*{vbillno}" type="hidden">
        <input th:field="*{invoiceId}" type="hidden">
        <input th:value="*{#dates.format(corDate, 'yyyy-MM-dd HH:mm:ss')}" name="corDate" type="hidden">
        <!--要求提货日期-->
        <input id="reqDeliDateTime" type="hidden" th:if="${type == 'copy'}" name="reqDeliDate">
        <input id="reqDeliDateTime" type="hidden" th:if="${type == 'edit'}" th:value="*{#dates.format(reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}" name="reqDeliDate">
        <input id="reqDeliDateOld" type="hidden"  th:value="*{#dates.format(reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}">
        <div class="panel-group" id="accordion">
            <div class="row">
                <div class="col-md-5">
                    <div class="panel panel-default" style="margin-bottom: 0 !important;border-bottom: 0;">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapseOne">发货单基础信息</a>
                                <span th:if="${enquiryNo != null and enquiryNo!=''}" style="float: right;">
                                    询价单号：<a onclick="openEnquiry()" href="#" class="enquiry-link">[[${enquiryNo}]]</a>
                                    <a th:if="${convert != null}" style="color:#FF9008" href="javascript:;" th:onclick="addInvoice([[${rfqEnquiryId}]])">切换路线</a>
                                </span>
                            </h5>
                        </div>
                        <div id="collapseOne" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <!--订单基础信息 begin-->
                                 <!--  集团名称-->
                                <input name="groupName" th:field="*{groupName}" type="hidden">
                                <input id="groupId" th:field="*{groupId}" type="hidden">

                                <div class="row no-gutter">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 客户名称：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <input th:field="*{custAbbr}" th:if="${type == 'edit'}" onclick="selectClient();" type="text"
                                                           placeholder="请选择客户" class="form-control valid"
                                                           aria-required="true" required autocomplete="off" disabled >
                                                    <input th:field="${client.custAbbr}" th:if="${type == 'copy'}" onclick="selectClient();getGuidingPrice();" type="text"
                                                           placeholder="请选择客户" class="form-control valid"
                                                           aria-required="true" required autocomplete="off" th:readonly="*{srcType != '2'}" th:disabled="*{srcType == '2'}">
                                                    <input th:field="*{custName}" th:if="${type == 'edit'}" type="hidden">
                                                    <input th:field="${client.custName}" th:if="${type == 'copy'}" type="hidden">
                    
                                                    <input th:field="*{custCode}" type="hidden">
                                                    <input th:field="*{customerId}" type="hidden">
                    
                                                    <input th:field="*{balaCorpId}"  th:if="${type == 'edit'}" type="hidden">
                                                    <input name="balaCorpId" th:value="${client.balaCorp}" th:if="${type == 'copy'}" type="hidden">
                    
                                                    <input th:field="*{balaDept}" th:if="${type == 'edit'}" type="hidden">
                                                    <input th:field="${client.balaDept}" th:if="${type == 'copy'}" type="hidden">
                    
                                                    <input th:field="*{salesDept}" th:if="${type == 'edit'}" type="hidden">
                                                    <input th:field="${client.salesDept}" th:if="${type == 'copy'}" type="hidden">
                    
                                                    <input th:field="*{psndoc}" th:if="${type == 'edit'}" type="hidden">
                                                    <input th:field="${client.psndoc}"th:if="${type == 'copy'}"  type="hidden">
                    
                                                    <input th:field="*{billingCorp}" th:if="${type == 'edit'}" type="hidden">
                                                    <input th:field="${client.billingCorp}" th:if="${type == 'copy'}" type="hidden">

                                                    <input th:field="*{referenceRate}" th:if="${type == 'edit'}" type="hidden">
                                                    <input th:field="${client.referenceRate}" th:if="${type == 'copy'}" type="hidden">

                                                    <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

<!--                                    <div class="col-md-6 col-sm-6">-->
<!--                                        <div class="">-->
<!--                                            <label class="flex_left">-->
<!--                                                客户发货单号：</label>-->
<!--                                            <div class="flex_right">-->
<!--                                                <input id="custOrderno" name="custOrderno"  th:if="${type == 'copy'}" placeholder="客户发货单号" class="form-control" type="text"-->
<!--                                                       maxlength="50" autocomplete="off">-->
<!--                                                <input  th:field="*{custOrderno}" th:if="${type == 'edit'}" placeholder="客户发货单号" class="form-control" type="text"-->
<!--                                                        maxlength="50" autocomplete="off">-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->


                                    <div class="col-md-6 col-sm-6">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 紧急程度：</label>
                                            <div class="flex_right">
                                                <select class="form-control valid" th:field="*{urgentLevel}" aria-invalid="false"
                                                        onchange="setUrgentLevelName();" th:with="type=${@dict.getType('urgent_level')}" required>
                                                    <option value=""></option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                                <input type="hidden" id="urgentLevelName" name="urgentLevelName" th:value="*{urgentLevelName}">
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <div class="row no-gutter">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 要求提货日：</label>
                                            <div class="flex_right">
                                                <div style="width: calc(100% - 5em);float: left;">
                                                    <div class="input-group">
                                                        <input type="text" th:if="${type == 'edit'}" class="form-control"  onchange="getCustLine()"
                                                               th:value="*{#dates.format(reqDeliDate, 'yyyy-MM-dd')}" id="reqDeliDate"
                                                        placeholder="要求提货日" lay-key="1" autocomplete="off" readonly required>
                                                        <input type="text" th:if="${type == 'copy'}" class="form-control"  id="reqDeliDate" onchange="getCustLine()"
                                                                placeholder="要求提货日" lay-key="1" autocomplete="off" readonly required>
                                                        <span class="input-group-addon" style="border-top-right-radius: 0;border-bottom-right-radius: 0;"><i class="glyphicon glyphicon-calendar"></i></span>
                                                    </div>
                                                    <label style="display:none;top:0px" id="reqDeliDate-error" class="error" for="reqDeliDate">这是必填字段</label>

                                                </div>
                                                <div style="width: 5em; float: right;">
                                                    <select style="border-top-left-radius: 0;border-bottom-left-radius: 0;border-left: 0;" class="form-control" id="reqDeliDateHour" onchange="getCustLine()"
                                                            aria-invalid="false"  th:with="type=${@dict.getType('on_the_hour')}">
                                                        <option></option>
                                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}">
                                                        </option>
                                                    </select>
                                                </div>
                                               
                                                <!-- <div class="form-group">
                                                    <div style="padding: 0">
                                                        <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                                        &nbsp&nbsp&nbsp

                                                        <input type="radio"  id="optionsRadios1" value="08:00" name="optionsRadios">8:00</label>&nbsp&nbsp
                                                        <input type="radio"  id="optionsRadios2" value="10:00" name="optionsRadios">10:00</label>&nbsp&nbsp
                                                        <input type="radio"  id="optionsRadios3" value="12:00" name="optionsRadios">12:00</label>&nbsp&nbsp
                                                        <input type="radio"  id="optionsRadios4" value="14:00" name="optionsRadios">14:00</label>
                                                    </div>
                                                </div> -->



                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 要求到货日：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <input type="text" th:if="${type == 'edit'}" class="form-control" th:value="*{#dates.format(reqArriDate, 'yyyy-MM-dd HH:mm:ss')}" id="reqArriDate" name="reqArriDate"
                                                    placeholder="要求到货日期" lay-key="2" autocomplete="off" readonly>
                                                    <input type="text" th:if="${type == 'copy'}" class=" form-control" id="reqArriDate" name="reqArriDate"
                                                            placeholder="要求到货日期" lay-key="2" autocomplete="off" readonly>
                                                    <span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
                                                </div>
                                                <label style="display:none;top:0px" id="reqArriDate-error" class="error" for="reqArriDate">这是必填字段</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 车长：</label>
                                            <div class="flex_right">
                                                <select name="carLen" th:field="*{carLen}" onchange="getPrice();setCarLenName();getGuidingPrice();"
                                                        class="form-control valid" aria-invalid="false" th:with="type=${@dict.getType('car_len')}" required>
                                                    <option value=""></option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                                <input name="carLenName" id="carLenName" type="hidden" th:value="*{carLenName}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-sm-6">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 车型：</label>
                                            <div class="flex_right">
                                                <select name="carType" th:field="*{carType}" class="form-control valid" onchange="getPrice();setCarTypeName();getGuidingPrice();"
                                                        aria-invalid="false" th:with="type=${@dict.getType('car_type')}" required>
                                                    <option value=""></option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                                <input name="carTypeName" id="carTypeName" type="hidden" th:value="*{carTypeName}">
                                            </div>
                                        </div>
                                    </div>

                                    

                                </div>

                                <div class="row no-gutter">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 运输方式：</label>
                                            <div class="flex_right">
                                                <select name="transCode" th:field="*{transCode}"
                                                        class="form-control valid" onchange="setTransName();getGuidingPrice();getCustLine();getRfqSuggest();getPrice()"
                                                        aria-invalid="false" th:with="type=${@dict.getType('trans_code')}"
                                                        required>
                                                    <option value=""></option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                                <input name="transName" id="transName" type="hidden" th:value="*{transName}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-sm-6">
                                        <div class="">
                                            <label class="flex_left">驻场组：</label>
                                            <div class="flex_right">
                                                <select name="stationDept" th:field="*{stationDept}" th:if="${type == 'edit'}" class="form-control valid" aria-invalid="false"
                                                        th:with="type=${stationDeptList}">
                                                    <option value=""></option>
                                                    <option th:each="dict : ${type}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>
                                                </select>
                                                <select name="stationDept" th:field="${client.stationDept}" th:if="${type == 'copy'}" class="form-control valid" aria-invalid="false"
                                                        th:with="type=${stationDeptList}">
                                                    <option value=""></option>
                                                    <option th:each="dict : ${type}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <div class="row no-gutter">

                                    <div class="col-md-6 col-sm-6">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 调度组：</label>
                                            <div class="flex_right">
                                                <select name="transLineId" th:field="*{transLineId}" class="form-control valid" aria-invalid="false"
                                                        th:with="type=${dispatcherDeptList}" onchange="setTransLineName();" required>
                                                    <option value=""></option>
                                                    <option th:each="dict : ${type}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>
                                                </select>
                                                <input id="transLineName" name="transLineName" type="hidden" th:value="${transLineName}"/>

                                                <!--                                                <select name="transLineId" th:if="${type == 'edit'}" th:field="*{transLineId}" class="form-control valid" aria-invalid="false"-->
                                                <!--                                                        th:with="type=${dispatcherDeptList}" onchange="setTransLineName();" required>-->
                                                <!--                                                    <option value=""></option>-->
                                                <!--                                                    <option th:each="dict : ${type}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>-->
                                                <!--                                                </select>-->
                                                <!--                                                <input id="transLineName" th:if="${type == 'edit'}" name="transLineName" type="hidden" th:value="*{transLineName}"/>-->

                                                <!--                                                <select name="transLineId" id="transLineId" th:if="${type == 'copy'}" class="form-control valid" aria-invalid="false"-->
                                                <!--                                                        th:with="type=${dispatcherDeptList}" onchange="setTransLineName();" required>-->
                                                <!--                                                    <option value=""></option>-->
                                                <!--                                                    <option th:each="dict : ${type}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>-->
                                                <!--                                                </select>-->
                                                <!--                                                <input id="transLineName" th:if="${type == 'copy'}" name="transLineName" type="hidden"/>-->
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 调度人员-->
                                    <input name="dispatcherName" type="hidden" th:field="*{dispatcherName}">
                                    <input name="dispatcherId" type="hidden" th:field="*{dispatcherId}">


                                    <div class="col-md-6 col-sm-6">
                                        <div class="">
                                            <label class="flex_left">驻场人员：</label>
                                            <div class="flex_right">
                                                <!-- <div class="input-group"> -->
                                                <input class="form-control" type="text" onclick="selectResidentsUser()"
                                                       placeholder="驻场人员" id="residentsName" name="residentsName"
                                                       th:value="${residentsName}" readonly>
                                                <input required th:field="*{residentsId}" name="residentsId" type="hidden"/>
                                                <!-- <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                            </div> -->
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt10">
                                    <select th:field="*{isMultiple}" class="form-control valid hide" aria-invalid="false" required disabled>
                                        <option value="0">否</option>
                                        <option value="1">是</option>
                                    </select>
                                    
<!--                                    <div class="col-md-3 col-sm-3">-->
<!--                                        <div class="">-->
<!--                                            <label class="flex_left"><span class="fcff">*</span> 卸货地数量：</label>-->
<!--                                            <div class="flex_right">-->
<!--                                                <select id="unloadPlaceNum" name="unloadPlaceNum" class="form-control valid" aria-invalid="false" aria-required="true" required  onchange="getGuidingPrice()">-->
<!--                                                    <option></option>-->
<!--                                                    <option value="1" th:selected="*{unloadPlaceNum == 1}">1</option>-->
<!--                                                    <option value="2" th:selected="*{unloadPlaceNum == 2}">2</option>-->
<!--                                                    <option value="3" th:selected="*{unloadPlaceNum == 3}">3</option>-->
<!--                                                    <option value="4" th:selected="*{unloadPlaceNum == 4}">4</option>-->
<!--                                                    <option value="5" th:selected="*{unloadPlaceNum == 5}">5</option>-->
<!--                                                    <option value="6" th:selected="*{unloadPlaceNum == 6}">6</option>-->
<!--                                                </select>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->

                                    <div class="col-md-4 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">* </span>大件/三超：</label>
                                            <div class="flex_right">
                                                <div style="display: flex;align-items: baseline;">
                                                    <input type="checkbox" id="isOversize" th:checked="${invoice.isOversize == 1}"
                                                           onchange="changeIsOversize()"
                                                           style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />

                                                    <input name="isOversize" th:value="*{isOversize}" type="hidden">
                                                    <label for="isOversize" style="font-size: 1.1em; vertical-align: middle;">是</label>
                                                    <i class="fa fa-question-circle ml5 cur"
                                                       style="font-size: 16px;"
                                                       data-toggle="tooltip"
                                                       data-container="body" data-placement="right" data-html="true" title=""
                                                       data-original-title="<ul>
                                                            <li>1、当填写长、宽、高中的其中一项，则代表<big>体积</big>超出规范</li>
                                                            <li>2、长、宽、高不填，则默认<big>重量</big>超出规范</li>
                                                        </ul>
                                                       "></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-8 col-sm-8">
                                        <div class="flex" id="goodsSizeDiv" th:style="*{isOversize} == 1 ? '':'display: none;'">
                                            <div id="oversizeTypeDiv" style="font-size: 12px;margin-right: 5px;display: flex;align-items: center;">
                                                [[${invoice.oversizeType != null && invoice.oversizeType == 1 ? '超重':'超体积'}]]
                                            </div>
                                            <div class="input-group " style="flex: 1;margin-right: 3px">
                                                <input name="goodsLength" id="goodsLength"
                                                       th:value="${invoice.goodsLength}"
                                                       placeholder="长" type="text" class="form-control" style="color: #000"
                                                       oninput="$.numberUtil.onlyNumber(this);changeOversizeType()" autocomplete="off">
                                                <span class="input-group-addon">米</span>
                                            </div>
                                            <div class="input-group " style="flex: 1;margin-right: 3px">
                                                <input name="goodsWidth" id="goodsWidth" placeholder="宽"
                                                       th:value="${invoice.goodsWidth}"
                                                       type="text" class="form-control " style="color: #000"
                                                       oninput="$.numberUtil.onlyNumber(this);changeOversizeType()"  autocomplete="off">
                                                <span class="input-group-addon">米</span>
                                            </div>
                                            <div class="input-group" style="flex: 1;margin-right: 3px">
                                                <input name="goodsHeight" id="goodsHeight"
                                                       th:value="${invoice.goodsHeight}"
                                                       placeholder="高" type="text" class="form-control" style="color: #000"
                                                       oninput="$.numberUtil.onlyNumber(this);changeOversizeType()" autocomplete="off">
                                                <span class="input-group-addon">米</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row ">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">* </span>是否报关：</label>
                                            <div class="flex_right">
                                                <div style="display: flex;align-items: baseline;">
                                                    <input type="checkbox" id="isCustomsClearance" th:checked="${invoice.isCustomsClearance == 1}"
                                                           onchange="changeIsCustomsClearance()"
                                                           style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />

                                                    <input name="isCustomsClearance" th:value="*{isCustomsClearance}" type="hidden">
                                                    <label for="isCustomsClearance" style="font-size: 1.1em; vertical-align: middle;">是</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-md-12 col-sm-12">
                                        <div class="">
                                            <label class="flex_left">发货单备注：</label>
                                            <div class="flex_right">
                                                <textarea name="memo"
                                                            th:text="${(type=='edit'||rfqEnquiryId!=null) ? invoice.memo:''}" maxlength="200" class="form-control valid"
                                                            rows="2"></textarea>

                                            </div>
                                        </div>
                                    </div>
                                   
                                </div>
        
                                <!--订单基础信息 end-->
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default" style="margin-top: 0;">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                    href="tabs_panels.html#collapseTwo">运单联系人</a>
                                    <!-- <span class="toop toopT"  title="车主端显示我司联系人" data-toggle="tooltip" data-placement="right">
                                        <i class="fa fa-exclamation-circle fcff" style="font-size: 16px;"></i>
                                    </span> -->
                                    <div class="frT" style="width: calc(100% - 70px);">
                                        <div class="fcff">
                                            <i class="fa fa-exclamation-circle" style="font-size: 16px;vertical-align: middle;"></i>
                                            <span style="vertical-align: middle;">车主端显示我司联系人</span>
                                        </div>
                                    </div>
                            </h4>
                        </div>
                        <div id="collapseTwo" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <!--订单提货信息 end-->
                                <div class="row">
                                    <div class="col-md-6 col-sm-6">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> APP联系人：</label>
                                            <div class="flex_right">
                                                <input th:field="*{appDeliContact}" th:if="${type == 'edit'}" placeholder="APP联系人" class="form-control" type="text"
                                                        maxlength="20" autocomplete="off" aria-required="true" required>
                                                <input name="appDeliContact" th:field="${client.appDeliContact}" th:if="${type == 'copy'}" placeholder="APP联系人" class="form-control" type="text"
                                                        maxlength="20" autocomplete="off" aria-required="true" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-6">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 手机：</label>
                                            <div class="flex_right">
                                                <input th:field="*{appDeliMobile}"th:if="${type == 'edit'}" placeholder="APP联系人" class="form-control" type="text"
                                                    maxlength="11" autocomplete="off" aria-required="true" required>
                                                <input name="appDeliMobile" th:field="${client.appDeliMobile}"th:if="${type == 'copy'}" placeholder="APP联系人" class="form-control" type="text"
                                                    maxlength="11" autocomplete="off" aria-required="true" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                              
                                <!--订单提货信息 end-->
                            </div>
                        </div>
                    </div> 
                </div>
                <div class="col-md-7">

                    <div class="row">
                        <div class="col-md-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#accordion"
                                            href="tabs_panels.html#collapseTwoT">结算方式</a>
                                            <!-- <span class="toop toopT"  title="选择到付时需填写到付金额" data-toggle="tooltip" data-placement="right">
                                                <i class="fa fa-exclamation-circle fcff" style="font-size: 16px;"></i>
                                            </span> -->
                                        <div class="frT" style="width: calc(100% - 70px);">
                                            <div class="fcff">
                                                <i class="fa fa-exclamation-circle" style="font-size: 16px;vertical-align: middle;"></i>
                                                <span style="vertical-align: middle;">选择到付时需填写到付金额,司机送达后应收金额并提醒司机</span>
                                            </div>
                                        </div>
                                    </h4>
                                </div>
                                <div id="collapseTwoT" class="panel-collapse collapse in">
                                    <div class="panel-body">
                                        <!--结算方式信息 end-->
                                        <div class="row">
                                            <div class="col-md-6 col-sm-6">
                                                <div class="flex">
                                                    <label class="flex_left" style="width: 8em;"><span class="fcff">*</span> 结算客户：</label>
                                                    <div class="flex_right">
                                                        <!-- <div class="input-group"> -->
                                                            <input th:field="*{balaName}" th:if="${type == 'edit'}" onclick="selectClearingClient()" type="text"
                                                                placeholder="请选择结算客户" class="form-control valid"
                                                                aria-required="true" autocomplete="off" readonly>
                                                            <input th:field="*{balaCode}" th:if="${type == 'edit'}" type="hidden">
                                                            <input th:field="*{balaCustomerId}" th:if="${type == 'edit'}" type="hidden">

                                                            <input name="balaName" id="balaName" th:value="${balaClient.custName}"
                                                                th:if="${type == 'copy'}" onclick="selectClearingClient()" type="text"
                                                                placeholder="请选择结算客户" class="form-control valid"
                                                                aria-required="true" autocomplete="off" readonly>
                                                            <input name="balaCode" id="balaCode" th:value="${balaClient.custCode}" th:if="${type == 'copy'}" type="hidden">
                                                            <input name="balaCustomerId" id="balaCustomerId" th:value="${balaClient.customerId}" th:if="${type == 'copy'}" type="hidden">
                                                            <!-- <span class="input-group-addon"><i class="glyphicon glyphicon-chevron-down"></i></span>
                                                        </div> -->
                                                    </div>
                                                </div>
                                            </div>
        
                                            <div class="col-md-6 col-sm-6">
                                                <div class="flex">
                                                    <label class="flex_left" style="width: 9em;"><span class="fcff">*</span> 是否开票：</label>
                                                    <div class="flex_right">
                                                        <select name="billingType" id="billingType" th:with="type=${@dict.getType('billing_type')}"
                                                                onchange="getPrice()"
                                                                class="form-control valid" aria-invalid="false" required>
                                                            <option></option>
                                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                                    th:selected="${invoice.billingType != null && dict.dictValue == invoice.billingType}"
                                                                    th:value="${dict.dictValue}" th:attr="data-rate=${dict.numVal1}"></option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mt10">
                                            <div class="col-md-6 col-sm-6">
                                                <div class="flex">
                                                    <label class="flex_left" style="width: 8em;"><span class="fcff">*</span> 结算方式：</label>
                                                    <div class="flex_right" style="display: flex;">
                                                        <span style="width: 55%">
                                                            <select th:field="*{balaType}" class="form-control valid" aria-invalid="false"
                                                                    th:with="type=${@dict.getType('bala_type')}" required
                                                                    onchange="changeBalaType()">
                                                                <option value=""></option>
                                                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                            </select>
                                                        </span>
                                                        <span>
                                                            <div class="checkbox"style="margin-left: 5px;">
                                                                <label>
                                                                    <input type="checkbox" th:field="*{payOnDelivery}" onchange="changePayOnDelivery(this)" value="1">即收即付
                                                                </label>
                                                            </div>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6 col-sm-6">
                                                <div class="flex">
                                                    <label class="flex_left" style="width: 9em;"><span class="fcff">*</span> 到付应收：</label>
                                                    <div class="flex_right">
                                                        <div class="input-group">
                                                            <span class="input-group-addon">￥</span>
                                                            <input th:field="*{collectAmount}" placeholder="到付金额"
                                                            class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                            type="text" autocomplete="off" th:disabled="${invoice.balaType != '2' && invoice.balaType != '5' && invoice.balaType != '6'}">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
<!-- 
                                        <div class="row mt10">
                                            <div class="col-md-6 col-sm-6">
                                                <div class="flex">
                                                    <label class="flex_left" style="width: 8em;"><span class="fcff">*</span> 计价方式：</label>
                                                    <div class="flex_right" style="width: 50%">
                                                        <select th:field="*{billingMethod}" class="form-control valid"
                                                                aria-invalid="false" onchange="changeBillingMethod();getPrice()" required>
                                                            <option value=""></option>
                                                            <option th:each="billingMethod:${billingMethods}" th:value="${billingMethod.value}"
                                                                    th:text="${billingMethod.context}"></option>
                                                        </select>
                                                    </div>
                                                    <div class="flex_right" style="width: 50%">
                                                        <select th:field="*{ifBargain}" class="form-control custom-select" th:disabled="${invoice.rfqEnquiryLineId!=null}" onchange="changeIfBargain()" required>
                                                            <option value="" disabled selected hidden>是否议价</option>
                                                            <option value="0">合同价</option>
                                                            <option value="1">议价</option>
                                                        </select>
                                                    </div>

                                                </div>
                                            </div>
                                            <div class="col-md-6 col-sm-6">
                                                <div class="flex" th:style="${invoice.billingMethod == '6' || invoice.billingMethod == '7'|| invoice.billingMethod == '8' || invoice.billingMethod == '9'} ? '': 'display: none;' ">
                                                    <label class="flex_left" style="width: 9em;"><span class="fcff">*</span> 公里数：</label>
                                                    <div class="flex_right">
                                                        <div class="input-group">
                                                            <input th:field="*{mileage}" placeholder="公里数"
                                                                   class="form-control" oninput="$.numberUtil.onlyNumberNotNull(this);getPrice()"
                                                                   type="text" autocomplete="off" required>
                                                            <span class="input-group-addon">KM</span>
                                                        </div>
                                                        <label style="display:none;top:0px" id="mileage-error" class="error" for="mileage">这是必填字段</label>

                                                    </div>
                                                </div>
                                            </div>
                                        </div> -->
                                        <div class="row mt10">
                                            <div class="col-md-12">
                                                <div class="flex">
                                                    <!-- 计价方式标题 -->
                                                    <label class="flex_left" style="width: 7em;"><span class="fcff">*</span> 计价方式：</label>
                                                    
                                                    <div class="flex_right" style="display: flex; align-items: center;">
                                                        <!-- 计价方式 -->
                                                        <div style="width: 25%; padding-right: 5px;">
                                                            <select th:field="*{billingMethod}" class="form-control valid"
                                                                    aria-invalid="false" onchange="changeBillingMethod();getPrice()" required>
                                                                <option value=""></option>
                                                                <option th:each="billingMethod:${billingMethods}" th:value="${billingMethod.value}"
                                                                        th:text="${billingMethod.context}"></option>
                                                            </select>
                                                        </div>
                                        
                                                        <!-- 是否议价 -->
                                                        <div style="width: 25%; padding-right: 5px;">
                                                            <select th:field="*{ifBargain}" class="form-control custom-select" 
                                                                    th:disabled="${invoice.rfqEnquiryLineId!=null}" 
                                                                    onchange="changeIfBargain()" required>
                                                                <option value="" disabled selected hidden>是否议价</option>
                                                                <option value="0">合同价</option>
                                                                <option value="1">议价</option>
                                                            </select>
                                                        </div>
                                        
                                                        <!-- 新增往返/单程选择 -->
                                                        <div style="width: 25%; padding-right: 5px;">
                                                            <select th:field="*{isRoundTrip}" class="form-control" onchange="getPrice()">
                                                                <option value="0">单程</option>
                                                                <option value="1">往返</option>
                                                            </select>
                                                        </div>
                                        
                                                        <!-- 公里数输入框 -->
                                                        <div style="width: 25%;" th:style="${invoice.billingMethod == '6' || invoice.billingMethod == '7'|| invoice.billingMethod == '8' || invoice.billingMethod == '9'} ? '': 'display: none;' ">
                                                            <div class="input-group">
                                                                <input th:field="*{mileage}" placeholder="公里数"
                                                                       class="form-control" 
                                                                       oninput="$.numberUtil.onlyNumberNotNull(this);getPrice()"
                                                                       type="text" autocomplete="off" required>
                                                                <span class="input-group-addon">KM</span>
                                                            </div>
                                                            <label style="display:none;top:0px" id="mileage-error" class="error" for="mileage">这是必填字段</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row mt10" id="bargainMemoDiv" style="display: none;">
                                            <div class="col-md-12 col-sm-12">
                                                <div class="flex">
                                                    <label class="flex_left" style="width: 7em;"><span class="fcff" th:if="${!isFleet and client.ifBargain == 0 and invoice.rfqEnquiryLineId==null}" id="bargainMemoReqSpan">*</span>议价说明：</label>
                                                    <div class="flex_right">
                                                        <input id="bargainMemo" name="bargainMemo" th:value="${invoice.bargainMemo}" type="text" placeholder="请填写议价说明" class="form-control valid"  aria-invalid="false" th:required="${!isFleet and client.ifBargain == 0 and invoice.rfqEnquiryLineId==null}">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mt10">
                                            <div class="col-md-6 col-sm-6">
                                                <div class="flex">
                                                    <label class="flex_left" style="width: 8em;"><span id="unitPriceSpan" class="fcff">*</span> 单价：</label>

                                                    <div class="flex_right">
                                                        <div class="input-group">
                                                            <span class="input-group-addon">￥</span>
                                                            <input th:field="*{unitPrice}" placeholder="单价"
                                                                   class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateSum()"
                                                                   type="text" autocomplete="off" required>
                                                            <i class="fa fa-question-circle" data-toggle="tooltip" id="unitPriceFa"
                                                               style="font-size: 20px;display: none;margin-left: 5px;" data-html="true" data-container="body"
                                                               title=""></i>

                                                        </div>
                                                        <label style="display:none;top:0px" id="unitPrice-error" class="error" for="unitPrice">这是必填字段</label>
                                                    </div>
<!--                                                    <div class="flex_right" style="margin-left: 5px;">-->
<!--                                                        <div class="input-group">-->
<!--                                                            <span class="input-group-addon">不含税￥</span>-->
<!--                                                            <input th:field="*{unitPriceIncludeTax}" placeholder="不含税单价"-->
<!--                                                                   class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"-->
<!--                                                                   type="text" autocomplete="off" disabled>-->
<!--                                                        </div>-->
<!--                                                    </div>-->

                                                </div>
                                            </div>
                                            <div class="col-md-6 col-sm-6">
                                                <div class="flex">
                                                    <label class="flex_left" style="width: 9em;">不含税单价：</label>
                                                    <div class="flex_right" style="margin-left: 5px;">
                                                        <div class="input-group">
                                                            <span class="input-group-addon">￥</span>
                                                            <input th:field="*{unitPriceIncludeTax}" placeholder="不含税单价"
                                                                   class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                   type="text" autocomplete="off" disabled>
                                                        </div>
                                                    </div>

                                                </div>

                                            </div>

                                            <input th:field="*{costAmount}" type="hidden">
                                            <input th:field="*{costAmountIncludeTax}" type="hidden">

                                           <!-- <div class="col-md-6 col-sm-6">
                                                <div class="flex">
                                                    <label class="flex_left" style="width: 12em;"><span class="fcff">*</span> 总运费：</label>
                                                    <div class="flex_right">
                                                        <div class="input-group">
                                                            <span class="input-group-addon">含税￥</span>
                                                            <input th:field="*{costAmount}"  placeholder="总运费"
                                                                   class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);changeCostAmount()"
                                                                   type="text" autocomplete="off" required disabled>
                                                        </div>
                                                        <label style="display:none;top:0px" id="costAmount-error" class="error" for="costAmount">这是必填字段</label>

                                                    </div>

                                                    <div class="flex_right" style="margin-left: 5px;">
                                                        <div class="input-group">
                                                            <span class="input-group-addon">不含税￥</span>
                                                            <input th:field="*{costAmountIncludeTax}"   placeholder="不含税总运费"
                                                                   class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                   type="text" autocomplete="off" disabled>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>-->

                                        </div>
                                        <div class="row mt10">
                                            <div class="col-md-4 col-sm-4">
                                                <div class="flex">
                                                    <label class="flex_left" style="width: 9em;"> 其他费用：</label>
                                                    <div class="flex_right">
                                                        <select class="form-control valid" aria-invalid="false" th:field="*{otherFeeType}"
                                                                th:with="type=${@dict.getType('cost_type_on_way')}">
                                                            <option value="">--费用类型--</option>
                                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                        </select>
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="col-md-2 col-sm-2">
                                                <div class="flex">
                                                    <div class="flex_right">
                                                        <div class="input-group">
                                                            <span class="input-group-addon">￥</span>
                                                            <input th:field="*{otherFee}" placeholder="金额"
                                                                   class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateSum()"
                                                                   type="text" autocomplete="off">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 col-sm-6" id="deliveryFeeHtml" th:style="${invoice.deliveryFee != null and invoice.deliveryFee != '' ? '' : 'display: none;'}">
                                                <span>送货费:</span>
                                                <span id="deliveryFeeSpan">￥[[${invoice.deliveryFee}]]</span>
                                                <input id="deliveryFee" type="hidden" th:value="${invoice.deliveryFee}">
                                            </div>
                                        </div>
                                        <div class="row mt10">
                                            <div class="col-md-12 col-sm-12">
                                                <div class="flex">
                                                    <label class="flex_left" style="width: 7em;">备注：</label>
                                                    <div class="flex_right">
                                                        <textarea th:field="*{otherFeeMemo}" maxlength="200" placeholder="请输入其他费用备注"
                                                                  class="form-control valid" rows="2"></textarea>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                        <!--结算方式信息 end-->
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="row mt10">
                        <div class="col-md-12">
                            <div class="">
                                <div class="over">
                                    <div class="fl">
                                        <div class="addbtn fw" onclick="selectDelivery()" disabled="" style="background-color: #FF9008;font-size: 14px;">选择发货方</div>
                                    </div>
                                    <div class="fr tr">
                                        <span style="display: block;margin-bottom: 2px;text-align: right;font-size:16px;font-weight:550">
                                            <span>总运费:</span>
                                            <span style="color: #FF6C00">￥<nobr id="costAmount_text" th:text="${invoice.costAmount}">0</nobr></span>
                                            <span style="font-size: 14px;">(不含税￥<nobr id="costAmount_tax_text" th:text="${invoice.costAmountIncludeTax}">0</nobr>)</span>
                                            <span> +  其他费:</span>
                                            <span style="color: #FF6C00">￥<nobr id="otherfee_text" th:text="${invoice.otherFee == null ? '0' : invoice.otherFee}">0</nobr></span>
                                            <span> = </span>

                                            <span>总金额：</span>
                                            <span style="font-size:20px;color: #FF6C00">￥<nobr id="fhdz_costAmount_text" th:text="${(invoice.costAmount == null ? 0 : invoice.costAmount) + (invoice.otherFee == null ? 0 : invoice.otherFee)}">0</nobr></span>
                                            <span id="isRoundSpan" style="font-size: 10px;font-weight:400;display: none;">四舍五入</span>

                                        </span>
                                        <span style="text-align: right;display: block;font-size:15px;font-weight:530">
                                            <span>总件数 | 总重量 | 总体积：</span>
                                            <span style="font-weight:550;color: #0d62bb">
                                                <nobr id="fhdz_numCount_text" th:text="${numCount}">0</nobr>
                                                件| <nobr id="fhdz_weightCount_text" th:text="${weightCount}">0</nobr>
                                                吨| <nobr id="fhdz_volumeCount_text" th:text="${volumeCount}">0</nobr>
                                                m³
                                            </span>
                                        </span>
                                        <input name="fhdz_numCount" id="fhdz_numCount" th:value="${numCount}" type="hidden">
                                        <input name="fhdz_weightCount" id="fhdz_weightCount" th:value="${weightCount}" type="hidden">
                                        <input name="fhdz_volumeCount" id="fhdz_volumeCount" th:value="${volumeCount}" type="hidden">

<!--                                        <input name="fhdz_costAmount" id="fhdz_costAmount" th:value="${invoice.costAmount}" type="hidden">-->
<!--                                        <span th:if="${type == 'edit'}">总件数/总重量/总体积：<nobr id="fhdz_numCount_text" th:text="${invoice.numCount}">0</nobr>/<nobr id="fhdz_weightCount_text" th:text="${invoice.weightCount}">0</nobr>/<nobr id="fhdz_volumeCount_text" th:text="${invoice.volumeCount}">0</nobr>&nbsp;&nbsp;&nbsp;&nbsp;</span>-->
<!--                                        <span th:if="${type == 'copy'}">总件数/总重量/总体积：<nobr id="fhdz_numCount_text">0</nobr>/<nobr id="fhdz_weightCount_text">0</nobr>/<nobr id="fhdz_volumeCount_text" >0</nobr>&nbsp;&nbsp;&nbsp;&nbsp;</span>-->
<!--                                        <input th:if="${type == 'edit'}" name="fhdz_numCount" id="fhdz_numCount" th:value="${invoice.numCount}" type="hidden">-->
<!--                                        <input th:if="${type == 'edit'}" name="fhdz_weightCount" id="fhdz_weightCount" th:value="${invoice.weightCount}" type="hidden">-->
<!--                                        <input th:if="${type == 'edit'}" name="fhdz_volumeCount" id="fhdz_volumeCount" th:value="${invoice.volumeCount}" type="hidden">-->
<!--        -->
<!--                                        <input th:if="${type == 'copy'}" name="fhdz_numCount" id="fhdz_numCount" type="hidden">-->
<!--                                        <input th:if="${type == 'copy'}" name="fhdz_weightCount" id="fhdz_weightCount" type="hidden">-->
<!--                                        <input th:if="${type == 'copy'}" name="fhdz_volumeCount" id="fhdz_volumeCount" type="hidden">-->
<!--        -->
<!--                                        <span th:if="${type == 'edit'}"><span>总金额：</span><span style="color: red">￥<nobr id="fhdz_costAmount_text" th:text="${invoice.costAmount}">0</nobr></span></span>-->
<!--                                        <span th:if="${type == 'copy'}"><span>总金额：</span><span style="color: red">￥<nobr id="fhdz_costAmount_text">0</nobr></span></span>-->
<!--                                        <input th:if="${type == 'edit'}" name="fhdz_costAmount" id="fhdz_costAmount" th:value="${invoice.costAmount}" type="hidden">-->
<!--                                        <input th:if="${type == 'copy'}" name="fhdz_costAmount" id="fhdz_costAmount" type="hidden">-->
                                    </div>
                                </div>
                                <div id="fhdz">
                                    <div class="fhf mt10" th:each="address,stat: ${multipleShippingAddresses}"
                                                          th:if="${address.addressType == 0}"
                                                          th:id="|fhdz_${stat.index}|">
                                        <div class="row">
                                            <div class="col-sm-12 layui-form" style="margin-left: 5px;" >
                                                <input type="checkbox" th:data-addrId="${stat.index}"
                                                       th:checked="${address.isGetContractPrice} == '1'"
                                                       lay-filter="isAddPrice" title="加入合同价获取">
                                                <input th:id="|isGetContractPrice_${stat.index}|"
                                                       th:name="|shippingAddressList[${stat.index}].isGetContractPrice|"
                                                       th:value="${address.isGetContractPrice}" type="hidden">
                                            </div>
                                            <div class="col-sm-12 ">
                                                <div class="disf">
                                                    <div class="fh_addr">
                                                        <a class="btn btn-xs" href="javascript:void(0)" title="修改" th:onclick="|editAddr(this,0,${stat.index})|"><i class="fa fa-edit" style="font-size: 15px;"></i></a>
                                                        <div class="line20 line20T" style="width:100px;">[[${address.addrName}]]</div>
                                                        <div class="line20 line20T contact-info-clickable" style="width:230px;"
                                                             onclick="editContactInfo(0, this.dataset.index, this.dataset.contact, this.dataset.mobile, '')"
                                                             th:data-index="${stat.index}"
                                                             th:data-contact="${address.contact}"
                                                             th:data-mobile="${address.mobile}"
                                                             title="点击修改联系人信息">联系人信息: [[${address.contact}]]/[[${address.mobile}]]</div>

                                                        <span th:if="${address.longitudedegree != null && address.latitudedegree != null}">
                                                            <div class="line20 line20T fw" style="width: calc(100% - 408px)" th:onclick="getLongLat([[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]],[[${address.areaId}]],[[${address.detailAddr}]],[[${stat.index}]],[[${address.longitudedegree+','+address.latitudedegree}]],this)"><i th:id="|longLatIcon_${stat.index}|" class="fa fa-map-marker" style="color: #009AFE;font-size: 16px;margin-right: 5px;" ></i>地址:[[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]](<span th:id="|longLat_${stat.index}|">[[${address.longitudedegree}]],[[${address.latitudedegree}]]</span>)</div>
                                                            <input th:name="|shippingAddressList[${stat.index}].longitudedegree|"     th:value="${address.longitudedegree}"     th:id="|longitudedegree_${stat.index}|" type="hidden">
                                                            <input th:name="|shippingAddressList[${stat.index}].latitudedegree|"     th:value="${address.latitudedegree}"     th:id="|latitudedegree_${stat.index}|" type="hidden">
                                                        </span>
                                                        <span th:if="${address.longitudedegree == null || address.latitudedegree == null}">
                                                            <div class="line20 line20T fw" style="width: calc(100% - 408px)" th:onclick="getLongLat([[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]],[[${address.areaId}]],[[${address.detailAddr}]],[[${stat.index}]],'',this)"><i th:id="|longLatIcon_${stat.index}|" class="fa fa-map-marker" style="color: #ff1f1f;font-size: 16px;margin-right: 5px;" ></i>地址:[[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]](<span th:id="|longLat_${stat.index}|">无经纬度</span>)</div>
                                                            <input th:name="|shippingAddressList[${stat.index}].longitudedegree|"      th:id="|longitudedegree_${stat.index}|" type="hidden">
                                                            <input th:name="|shippingAddressList[${stat.index}].latitudedegree|"      th:id="|latitudedegree_${stat.index}|" type="hidden">
                                                        </span>

                                                        <input th:name="|shippingAddressList[${stat.index}].contact|"      th:value="${address.contact}"      th:id="|contact_${stat.index}|" type="hidden">
                                                        <input th:name="|shippingAddressList[${stat.index}].mobile|"       th:value="${address.mobile}"       th:id="|mobile_${stat.index}|" type="hidden">
<!--                                                        <input th:name="|shippingAddressList[${stat.index}].deliveryId|"   th:value="${address.deliveryId}"   th:id="|deliveryId_${stat.index}|" type="hidden">-->
                                                        <input th:name="|shippingAddressList[${stat.index}].provinceId|"   th:value="${address.provinceId}"   th:id="|provinceId_${stat.index}|" type="hidden">
                                                        <input th:name="|shippingAddressList[${stat.index}].cityId|"       th:value="${address.cityId}"       th:id="|cityId_${stat.index}|" type="hidden">
                                                        <input th:name="|shippingAddressList[${stat.index}].areaId|"       th:value="${address.areaId}"       th:id="|areaId_${stat.index}|" type="hidden">
                                                        <input th:name="|shippingAddressList[${stat.index}].provinceName|" th:value="${address.provinceName}" th:id="|provinceName_${stat.index}|" type="hidden">
                                                        <input th:name="|shippingAddressList[${stat.index}].cityName|"     th:value="${address.cityName}"     th:id="|cityName_${stat.index}|" type="hidden">
                                                        <input th:name="|shippingAddressList[${stat.index}].areaName|"     th:value="${address.areaName}"     th:id="|areaName_${stat.index}|" type="hidden">

<!--                                                        <input th:name="|shippingAddressList[${stat.index}].addrCode|"     th:value="${address.addrCode}"     th:id="|addrCode_${stat.index}|" type="hidden">-->
                                                        <input th:name="|shippingAddressList[${stat.index}].addrName|"     th:value="${address.addrName}"     th:id="|addrName_${stat.index}|" type="hidden">
                                                        <input th:name="|shippingAddressList[${stat.index}].detailAddr|"   th:value="${address.detailAddr}"   th:id="|detailAddr_${stat.index}|" type="hidden">
                                                        <input th:name="|shippingAddressList[${stat.index}].addressType|"  th:value="${address.addressType}"  th:id="|addressType_${stat.index}|" type="hidden" >
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-12">
                                                <div class="fhf_table">
                                                    <div class="fixed-table-body" style="margin: 0px -5px;">
                                                        <table class="custom-tab tab table table-bordered">
                                                            <thead>
                                                            <tr>
                                                                <th style="width: 15%;">
                                                                    <div style="display: inline-block;">
                                                                        <a class="collapse-link" style="font-size: 22px;color: #1ab394;"
                                                                           th:onclick="|insertGoodsRow(${stat.index},0)|" title="新增行">+</a>
                                                                    </div>
                                                                </th>
                                                                <th style="width: 25%;">客户单号</th>
                                                                <th style="width: 15%;">货品名称</th>
                                                                <th style="width: 15%;">包装</th>
                                                                <th style="width: 30%;">件数/重量(吨)/体积(m³)</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            <tr th:each="goods,goodsStat: ${address.shippingGoodsList}" th:id="|fhdz_${goods.params.uid}|">
                                                                <td>
                                                                    <div style="display: inline-block;">
                                                                        <a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"
                                                                           th:data-uid="${goods.params.uid}"
                                                                           th:onclick="|removeGoodsRow(this.getAttribute('data-uid'),${stat.index},0)|"
                                                                           title="删除选择行"></a>
                                                                    </div>
                                                                    <div style="display: inline-block;margin-left:5px">
                                                                        <a class="fa fa-clone" style="color: #0092e7;font-size: 17px;"
                                                                           th:data-uid="${goods.params.uid}"
                                                                           th:onclick="|insertGoodsRow(${stat.index},0,null,this.getAttribute('data-uid'))|"
                                                                           title="复制货品"></a>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <input th:if="${type == 'edit'}"
                                                                           th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].custOrderno|"
                                                                           th:id="|custOrderno_fhdz_${goods.params.uid}|"
                                                                           th:value="${goods.custOrderno}"
                                                                           th:data-uid="${goods.params.uid}"
                                                                           placeholder="请输入客户发货单号" class="form-control" type="text"
                                                                           maxlength="50" autocomplete="off" oninput="syncArriGoodsForType(0)">
                                                                    <input th:if="${type == 'copy'}"
                                                                           th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].custOrderno|"
                                                                           th:id="|custOrderno_fhdz_${goods.params.uid}|"
                                                                           th:data-uid="${goods.params.uid}"
                                                                           placeholder="请输入客户发货单号" class="form-control" type="text"
                                                                           maxlength="50" autocomplete="off" oninput="syncArriGoodsForType(0)">
                                                                </td>

                                                                <td>
                                                                    <input th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].goodsName|"      th:id="|goodsName_fhdz_${goods.params.uid}|" th:value="${goods.goodsName}"
                                                                           th:data-uid="${goods.params.uid}" onclick="selectGoods(this.getAttribute('data-uid'),0)" type="text" placeholder="" class="form-control valid" autocomplete="off" required readonly/>
<!--                                                                    <input th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].goodsId|"        th:id="|goodsId_fhdz_${goods.params.uid}|" th:value="${goods.goodsId}" type="hidden"/>-->
<!--                                                                    <input th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].goodsCode|"      th:id="|goodsCode_fhdz_${goods.params.uid}|" th:value="${goods.goodsCode}" type="hidden"/>-->
                                                                    <input th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].goodsCharacter|" th:id="|goodsCharacter_fhdz_${goods.params.uid}|" th:value="${goods.goodsCharacter}" type="hidden"/>
                                                                    <input th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].goodsTypeName|"  th:id="|goodsTypeName_fhdz_${goods.params.uid}|" th:value="${goods.goodsTypeName}" type="hidden"/>
                                                                    <input th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].goodsType|"      th:id="|goodsTypeId_fhdz_${goods.params.uid}|" th:value="${goods.goodsType}" type="hidden"/>
                                                                </td>
                                                                <td>
                                                                    <div class="col-sm-12">
                                                                        <select
                                                                                th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].packId|"
                                                                                th:id="|packId_fhdz_${goods.params.uid}|"
                                                                                onchange="syncArriGoodsForType(0)"
                                                                                th:with="type=${@dict.getType('package_type')}" class="form-control valid" aria-invalid="false" required>
                                                                            <option value=""></option>
                                                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${goods.packId} == ${dict.dictValue}" ></option>
                                                                        </select>
<!--                                                                        <select th:if="${type == 'copy'}"-->
<!--                                                                                th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].packId|"-->
<!--                                                                                th:id="|packId_fhdz_${goods.params.uid}|"-->
<!--                                                                                onchange="syncArriGoodsForType(0)"-->
<!--                                                                                th:with="type=${@dict.getType('package_type')}" class="form-control valid" aria-invalid="false" required>-->
<!--                                                                            <option value=""></option>-->
<!--                                                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>-->
<!--                                                                        </select>-->
                                                                    </div>
                                                                </td>

                                                                <td>
                                                                    <div class="col-sm-4" style="padding-left:2px;padding-right: 2px">
                                                                        <input
                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].num|"
                                                                               th:id="|num_fhdz_${goods.params.uid}|"
                                                                               th:value="${goods.num}"
                                                                               th:data-uid="${goods.params.uid}"
                                                                               oninput="$.numberUtil.onlyNumber(this);getPriceByGoods(0);checkNumWeightVolume(this.getAttribute('data-uid'),0);syncArriGoodsForType(0);calculateSum()"
                                                                               style="text-align: right" autocomplete="off"  placeholder="" class="form-control" type="text">
<!--                                                                        <input th:if="${type == 'copy'}"-->
<!--                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].num|"-->
<!--                                                                               th:id="|num_fhdz_${goods.params.uid}|"-->
<!--                                                                               th:data-uid="${goods.params.uid}"-->
<!--                                                                               oninput="$.numberUtil.onlyNumber(this);getPrice(this.getAttribute('data-uid'),0);checkNumWeightVolume(this.getAttribute('data-uid'),0);syncArriGoodsForType(0)"-->
<!--                                                                               style="text-align: right" autocomplete="off"  placeholder="" class="form-control" type="text">-->
                                                                    </div>
                                                                    <div class="col-sm-4" style="padding-left:2px;padding-right: 2px">
                                                                        <input
                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].weight|"
                                                                               th:id="|weight_fhdz_${goods.params.uid}|"
                                                                               th:value="${goods.weight}"
                                                                               th:data-uid="${goods.params.uid}"
                                                                               oninput="$.numberUtil.onlyNumber(this);getPriceByGoods(0);checkNumWeightVolume(this.getAttribute('data-uid'),0);syncArriGoodsForType(0);calculateSum()"
                                                                               style="text-align: right" autocomplete="off" placeholder="" class="form-control" type="text">
<!--                                                                        <input th:if="${type == 'copy'}"-->
<!--                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].weight|"-->
<!--                                                                               th:id="|weight_fhdz_${goods.params.uid}|"-->
<!--                                                                               th:data-uid="${goods.params.uid}"-->
<!--                                                                               oninput="$.numberUtil.onlyNumber(this);getPrice(this.getAttribute('data-uid'),0);checkNumWeightVolume(this.getAttribute('data-uid'),0);syncArriGoodsForType(0)"-->
<!--                                                                               style="text-align: right" autocomplete="off" placeholder="" class="form-control" type="text">-->
                                                                    </div>
                                                                    <div class="col-sm-4" style="padding-left:2px;padding-right: 2px">
                                                                        <input
                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].volume|"
                                                                               th:id="|volume_fhdz_${goods.params.uid}|"
                                                                               th:value="${goods.volume}"
                                                                               th:data-uid="${goods.params.uid}"
                                                                               oninput="$.numberUtil.onlyNumber(this);getPriceByGoods(0);checkNumWeightVolume(this.getAttribute('data-uid'),0);syncArriGoodsForType(0);calculateSum()"
                                                                               style="text-align: right" autocomplete="off" placeholder="" class="form-control" type="text">
<!--                                                                        <input th:if="${type == 'copy'}"-->
<!--                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].volume|"-->
<!--                                                                               th:id="|volume_fhdz_${goods.params.uid}|"-->
<!--                                                                               th:data-uid="${goods.params.uid}"-->
<!--                                                                               oninput="$.numberUtil.onlyNumber(this);getPrice(this.getAttribute('data-uid'),0);checkNumWeightVolume(this.getAttribute('data-uid'),0);syncArriGoodsForType(0)"-->
<!--                                                                               style="text-align: right" autocomplete="off" placeholder="" class="form-control" type="text">-->
                                                                    </div>
                                                                </td>
                                                           <!--     <td>
                                                                    <div class="input-group">
                                                                        <select th:if="${type == 'edit'}" th:data-uid="${goods.params.uid}"
                                                                                onchange="judgePriceType(this.getAttribute('data-uid'),0);"
                                                                                th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].billingMethod|"
                                                                                th:id="|billingMethod_fhdz_${goods.params.uid}|" class="form-control valid" aria-invalid="false" required>
                                                                            <option value=""></option>
                                                                            <option th:each="billingMethod:${billingMethods}"  th:value="${billingMethod.value}" th:text="${billingMethod.context}" th:selected="${goods.billingMethod} == ${billingMethod.value}"></option>
                                                                        </select>
                                                                        <select th:if="${type == 'copy'}" th:data-uid="${goods.params.uid}"
                                                                                onchange="judgePriceType(this.getAttribute('data-uid'),0);"
                                                                                th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].billingMethod|"
                                                                                th:id="|billingMethod_fhdz_${goods.params.uid}|" class="form-control valid" aria-invalid="false" required>
                                                                            <option value=""></option>
                                                                            <option th:each="billingMethod:${billingMethods}"  th:value="${billingMethod.value}" th:text="${billingMethod.context}"></option>
                                                                        </select>

                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <input th:if="${type == 'edit'}"
                                                                           th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].pc|"
                                                                           th:id="|pc_fhdz_${goods.params.uid}|"  th:data-uid="${goods.params.uid}"
                                                                           th:value="${goods.pc}"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateSum(this.getAttribute('data-uid'),0);syncArriGoodsForType(0)"
                                                                           style="text-align: right" placeholder="" class="form-control" autocomplete="off" type="text">
                                                                    <input th:if="${type == 'copy'}"
                                                                           th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].pc|"
                                                                           th:id="|pc_fhdz_${goods.params.uid}|"  th:data-uid="${goods.params.uid}"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateSum(this.getAttribute('data-uid'),0);syncArriGoodsForType(0)"
                                                                           style="text-align: right" placeholder="" class="form-control" autocomplete="off" type="text">
                                                                </td>
                                                                <td>
                                                                    <div class="input-group">
                                                                        <input th:if="${type == 'edit'}"
                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].sum|"
                                                                               th:id="|sum_fhdz_${goods.params.uid}|"
                                                                               th:value="${goods.sum}"
                                                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotals(0);syncArriGoodsForType(0)"
                                                                               placeholder="" class="form-control" type="text" style="text-align: right" autocomplete="off" required>
                                                                        <input th:if="${type == 'copy'}"
                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].sum|"
                                                                               th:id="|sum_fhdz_${goods.params.uid}|"
                                                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotals(0);syncArriGoodsForType(0)"
                                                                               placeholder="" class="form-control" type="text" style="text-align: right" autocomplete="off" required>
                                                                    </div>
                                                                </td>-->

                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="fhf_close">
                                            <div th:onclick="|removeAddrRow(${stat.index},0)|" class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" ></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="row mt10">
                        <div class="col-md-12">
                            <div class="">
                                <div class="over">
                                    <div class="fl">
                                        <div class="addbtn fw" onclick="selectReceipt()"  style="background-color: #009AFE;font-size: 14px;">选择收货方</div>
                                    </div>
                                    <div class="fr tr">
                                        <span style="margin-top:10px;display:block;font-size:15px;font-weight:530">
                                            <span>总件数 | 总重量 | 总体积：</span>
                                            <span style="font-weight:550;color: #0d62bb">
                                                <nobr id="shdz_numCount_text" th:text="${numCount}">0</nobr>
                                                件| <nobr id="shdz_weightCount_text" th:text="${weightCount}">0</nobr>
                                                吨| <nobr id="shdz_volumeCount_text" th:text="${invoice.volumeCount}">0</nobr>
                                                m³
                                            </span>
                                        </span>
                                        <input name="shdz_numCount" id="shdz_numCount" th:value="${numCount}" type="hidden">
                                        <input name="shdz_weightCount" id="shdz_weightCount" th:value="${weightCount}" type="hidden">
                                        <input name="shdz_volumeCount" id="shdz_volumeCount" th:value="${volumeCount}" type="hidden">

                                        <input name="shdz_costAmount" id="shdz_costAmount" th:value="${invoice.costAmount}" type="hidden">
<!--                                        <span th:if="${type == 'edit'}">总件数/总重量/总体积：<nobr id="shdz_numCount_text" th:text="${invoice.numCount}">0</nobr>/<nobr id="shdz_weightCount_text" th:text="${invoice.weightCount}">0</nobr>/<nobr id="shdz_volumeCount_text" th:text="${invoice.volumeCount}">0</nobr>&nbsp;&nbsp;&nbsp;&nbsp;</span>-->
<!--                                        <span th:if="${type == 'copy'}">总件数/总重量/总体积：<nobr id="shdz_numCount_text">0</nobr>/<nobr id="shdz_weightCount_text">0</nobr>/<nobr id="shdz_volumeCount_text" >0</nobr>&nbsp;&nbsp;&nbsp;&nbsp;</span>-->
<!--                                        <input th:if="${type == 'edit'}" name="shdz_numCount" id="shdz_numCount" th:value="${invoice.numCount}" type="hidden">-->
<!--                                        <input th:if="${type == 'edit'}" name="shdz_weightCount" id="shdz_weightCount" th:value="${invoice.weightCount}" type="hidden">-->
<!--                                        <input th:if="${type == 'edit'}" name="shdz_volumeCount" id="shdz_volumeCount" th:value="${invoice.volumeCount}" type="hidden">-->
<!--    -->
<!--                                        <input th:if="${type == 'copy'}" name="shdz_numCount" id="shdz_numCount" type="hidden">-->
<!--                                        <input th:if="${type == 'copy'}" name="shdz_weightCount" id="shdz_weightCount" type="hidden">-->
<!--                                        <input th:if="${type == 'copy'}" name="shdz_volumeCount" id="shdz_volumeCount" type="hidden">-->
<!--    -->
<!--                                        <span th:if="${type == 'edit'}"><span>总金额：</span><span style="color: red">￥<nobr id="shdz_costAmount_text" th:text="${invoice.costAmount}">0</nobr></span></span>-->
<!--                                        <span th:if="${type == 'copy'}"><span>总金额：</span><span style="color: red">￥<nobr id="shdz_costAmount_text">0</nobr></span></span>-->
<!--                                        <input th:if="${type == 'edit'}" name="shdz_costAmount" id="shdz_costAmount" th:value="${invoice.costAmount}" type="hidden">-->
<!--                                        <input th:if="${type == 'copy'}" name="shdz_costAmount" id="shdz_costAmount" type="hidden">-->

                                    </div>
                                </div>
                                <div id="shdz">
                                    <div class="shf mt10" th:each="address,stat: ${multipleShippingAddresses}"
                                                          th:if="${address.addressType == 1}"
                                                          th:id="|shdz_${stat.index}|">
                                        <div class="row">
                                            <div class="col-sm-12 layui-form" style="margin-left: 5px;" >
                                                <input type="checkbox" th:data-addrId="${stat.index}"
                                                       th:checked="${address.isGetContractPrice} == '1'"
                                                       lay-filter="isAddPrice" title="加入合同价获取">
                                                <input th:id="|isGetContractPrice_${stat.index}|"
                                                       th:name="|shippingAddressList[${stat.index}].isGetContractPrice|"
                                                       th:value="${address.isGetContractPrice}" type="hidden">
                                            </div>

                                            <div class="col-sm-12 ">
                                                <div class="disf">
                                                        <th:block th:if="${address.isChangeAddress==1}">
                                                            <div class="fh_addr fh_edit">
                                                                <a class="btn btn-xs" href="javascript:void(0)" title="修改" th:onclick="|editAddr(this,1,${stat.index})|"><i class="fa fa-edit" style="font-size: 15px;"></i></a>

                                                                <div class="toText" style="background-color: #ababab;display:inline-block">改</div>

                                                                <div class="line20 line20T" style="width:100px;">[[${address.caArriAddrName}]]</div>
                                                                <div class="line20 line20T contact-info-clickable" style="width:230px;"
                                                                     onclick="editContactInfo(1, this.dataset.index, this.dataset.contact, this.dataset.mobile, 'ca')"
                                                                     th:data-index="${stat.index}"
                                                                     th:data-contact="${address.caArriContact}"
                                                                     th:data-mobile="${address.caArriMobile}"
                                                                     title="点击修改联系人信息">联系人信息: [[${address.caArriContact}]]/[[${address.caArriMobile}]]</div>



                                                                <span th:if="${address.caLongitudedegree != null && address.caLatitudedegree != null}">
                                                                    <div class="line20 line20T fw" style="width: calc(100% - 408px)" th:onclick="getLongLat([[${address.caArriProName + address.caArriCityName + address.caArriAreaName + address.caArriDetailAddr}]],[[${address.caArriAreaId}]],[[${address.caArriDetailAddr}]],[[${stat.index}]],[[${address.longitudedegree+','+address.latitudedegree}]]",this)><i class="fa fa-map-marker" style="color: #009AFE;font-size: 16px;margin-right: 5px;"></i>地址:[[${address.caArriProName + address.caArriCityName + address.caArriAreaName + address.caArriDetailAddr}]](<span th:id="|longLat_${stat.index}|">[[${address.caLongitudedegree}]],[[${address.caLatitudedegree}]]</span>)</div>
                                                                    <input th:name="|shippingAddressList[${stat.index}].caLongitudedegree|"     th:value="${address.caLongitudedegree}"     th:id="|caLongitudedegree${stat.index}|" type="hidden">
                                                                    <input th:name="|shippingAddressList[${stat.index}].caLatitudedegree|"     th:value="${address.caLatitudedegree}"     th:id="|caLatitudedegree${stat.index}|" type="hidden">
                                                                </span>
                                                                <span th:if="${address.caLongitudedegree == null || address.caLatitudedegree == null}">
                                                                    <div class="line20 line20T fw" style="width: calc(100% - 408px)" th:onclick="getLongLat([[${address.caArriProName + address.caArriCityName + address.caArriAreaName + address.caArriDetailAddr}]],[[${address.caArriAreaId}]],[[${address.caArriDetailAddr}]],[[${stat.index}]],'',this)"><i class="fa fa-map-marker" style="color: #ff1f1f;font-size: 16px;margin-right: 5px;"></i>地址:[[${address.caArriProName + address.caArriCityName + address.caArriAreaName + address.caArriDetailAddr}]](<span th:id="|longLat_${stat.index}|">无经纬度</span>)</div>
                                                                    <input th:name="|shippingAddressList[${stat.index}].caLongitudedegree|"   th:id="|caLongitudedegree_${stat.index}|" type="hidden">
                                                                    <input th:name="|shippingAddressList[${stat.index}].caLatitudedegree|"   th:id="|caLatitudedegree_${stat.index}|" type="hidden">
                                                                </span>


                                                                <div th:data-index="${stat.index}" th:class="|toTextT selectDeliveryT${stat.index}|" style="display:none">
                                                                    <i class="glyphicon glyphicon-pencil" style="color:#1ab394;" th:onclick="|selectDeliveryT(this,1,${stat.index})|"></i>
                                                                </div>


                                                                <input th:name="|shippingAddressList[${stat.index}].isChangeAddress|" value="1" th:id="|isChangeAddress${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].caArriProvinceId|" th:value="${address.caArriProvinceId}" th:id="|caArriProvinceId${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].caArriCityId|" th:value="${address.caArriCityId}" th:id="|caArriCityId${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].caArriAreaId|"  th:value="${address.caArriAreaId}" th:id="|caArriAreaId${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].caArriProName|" th:value="${address.caArriProName}" th:id="|caArriProName${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].caArriCityName|" th:value="${address.caArriCityName}" th:id="|caArriCityName${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].caArriAreaName|" th:value="${address.caArriAreaName}" th:id="|caArriAreaName${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].caArriAddrName|"  th:value="${address.caArriAddrName}" th:id="|caArriAddrName${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].caArriDetailAddr|" th:value="${address.caArriDetailAddr}" th:id="|caArriDetailAddr${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].caArriContact|"  th:value="${address.caArriContact}"   th:id="|caArriContact${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].caArriMobile|"  th:value="${address.caArriMobile}"  th:id="|caArriMobile${stat.index}|" type="hidden">

                                                            </div>
                                                        </th:block>
                                                        <th:block th:if="${address.isChangeAddress!=1}">
                                                            <div class="fh_addr">
                                                                <a class="btn btn-xs" href="javascript:void(0)" title="修改" th:onclick="|editAddr(this,1,${stat.index})|"><i class="fa fa-edit" style="font-size: 15px;"></i></a>

                                                                <div class="toText" style="background-color: #ababab;display:none">改</div>
                                                                <div class="line20 line20T" style="width:100px;">[[${address.addrName}]]</div>
                                                                <div class="line20 line20T contact-info-clickable" style="width:230px;"
                                                                     onclick="editContactInfo(1, this.dataset.index, this.dataset.contact, this.dataset.mobile, '')"
                                                                     th:data-index="${stat.index}"
                                                                     th:data-contact="${address.contact}"
                                                                     th:data-mobile="${address.mobile}"
                                                                     title="点击修改联系人信息">联系人信息: [[${address.contact}]]/[[${address.mobile}]]</div>

                                                                <span th:if="${address.longitudedegree != null && address.latitudedegree != null}">
                                                                    <div class="line20 line20T fw" style="width: calc(100% - 408px)" th:onclick="getLongLat([[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]],[[${address.areaId}]],[[${address.detailAddr}]],[[${stat.index}]],[[${address.longitudedegree+','+address.latitudedegree}]],this)"><i th:id="|longLatIcon_${stat.index}|" class="fa fa-map-marker" style="color: #009AFE;font-size: 16px;margin-right: 5px;" ></i>地址:[[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]](<span th:id="|longLat_${stat.index}|">[[${address.longitudedegree}]],[[${address.latitudedegree}]]</span>)</div>
                                                                    <input th:name="|shippingAddressList[${stat.index}].longitudedegree|"     th:value="${address.longitudedegree}"     th:id="|longitudedegree_${stat.index}|" type="hidden">
                                                                    <input th:name="|shippingAddressList[${stat.index}].latitudedegree|"     th:value="${address.latitudedegree}"     th:id="|latitudedegree_${stat.index}|" type="hidden">
                                                                </span>
                                                                        <span th:if="${address.longitudedegree == null || address.latitudedegree == null}">
                                                                    <div class="line20 line20T fw" style="width: calc(100% - 408px)" th:onclick="getLongLat([[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]],[[${address.areaId}]],[[${address.detailAddr}]],[[${stat.index}]],'',this)"><i th:id="|longLatIcon_${stat.index}|" class="fa fa-map-marker" style="color: #ff1f1f;font-size: 16px;margin-right: 5px;" ></i>地址:[[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]](<span th:id="|longLat_${stat.index}|">无经纬度</span>)</div>
                                                                    <input th:name="|shippingAddressList[${stat.index}].longitudedegree|"      th:id="|longitudedegree_${stat.index}|" type="hidden">
                                                                    <input th:name="|shippingAddressList[${stat.index}].latitudedegree|"      th:id="|latitudedegree_${stat.index}|" type="hidden">
                                                                </span>
                                                                <span th:if="${address.contact == '未知'}" data-toggle="tooltip" data-placement="top" title="请重新选择地址" unknown-contact trigger="manual"></span>
                                                                <div  th:data-index="${stat.index}" th:class="|toTextT selectDeliveryT${stat.index}|" >
                                                                    <i class="glyphicon glyphicon-pencil" style="color:#1ab394;" th:onclick="|selectDeliveryT(this,1,${stat.index})|"></i>
                                                                </div>

                                                                <input th:name="|shippingAddressList[${stat.index}].contact|"      th:value="${address.contact}"      th:id="|contact_${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].mobile|"       th:value="${address.mobile}"       th:id="|mobile_${stat.index}|" type="hidden">
<!--                                                                <input th:name="|shippingAddressList[${stat.index}].deliveryId|"   th:value="${address.deliveryId}"   th:id="|deliveryId_${stat.index}|" type="hidden">-->
                                                                <input th:name="|shippingAddressList[${stat.index}].provinceId|"   th:value="${address.provinceId}"   th:id="|provinceId_${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].cityId|"       th:value="${address.cityId}"       th:id="|cityId_${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].areaId|"       th:value="${address.areaId}"       th:id="|areaId_${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].provinceName|" th:value="${address.provinceName}" th:id="|provinceName_${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].cityName|"     th:value="${address.cityName}"     th:id="|cityName_${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].areaName|"     th:value="${address.areaName}"     th:id="|areaName_${stat.index}|" type="hidden">
<!--                                                                <input th:name="|shippingAddressList[${stat.index}].addrCode|"     th:value="${address.addrCode}"     th:id="|addrCode_${stat.index}|" type="hidden">-->
                                                                <input th:name="|shippingAddressList[${stat.index}].addrName|"     th:value="${address.addrName}"     th:id="|addrName_${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].detailAddr|"   th:value="${address.detailAddr}"   th:id="|detailAddr_${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].addressType|"  th:value="${address.addressType}"  th:id="|addressType_${stat.index}|" type="hidden" >
                                                            </div>
                                                        </th:block>
                                                </div>
                                            </div>
                                            <th:block th:if="${address.isChangeAddress==1}">
                                                <div class="col-sm-12 mt10" style="display: block;">
                                                    <div class="disf">
                                                        <div class="fh_addr">

                                                            <div class="toText" style="background-color: #1ab394;">新</div>

                                                            <div class="line20 line20T" style="width:100px;">[[${address.addrName}]]</div>
                                                            <div class="line20 line20T contact-info-clickable" style="width:250px;"
                                                                 onclick="editContactInfo(0, this.dataset.index, this.dataset.contact, this.dataset.mobile, '')"
                                                                 th:data-index="${stat.index}"
                                                                 th:data-contact="${address.contact}"
                                                                 th:data-mobile="${address.mobile}"
                                                                 title="点击修改联系人信息">联系人信息: [[${address.contact}]]/[[${address.mobile}]]</div>
                                                            <span th:if="${address.longitudedegree != null && address.latitudedegree != null}">
                                                                <div class="line20 line20T fw" style="width: calc(100% - 408px)" th:onclick="getLongLat([[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]],[[${address.areaId}]],[[${address.detailAddr}]],[[${stat.index}]],[[${address.longitudedegree+','+address.latitudedegree}]],this)"><i class="fa fa-map-marker" style="color: #009AFE;font-size: 16px;margin-right: 5px;" ></i>地址:[[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]](<span th:id="|longLat_${stat.index}|">[[${address.longitudedegree}]],[[${address.latitudedegree}]]</span>)</div>
                                                                <input th:name="|shippingAddressList[${stat.index}].longitudedegree|"     th:value="${address.longitudedegree}"     th:id="|longitudedegree_${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].latitudedegree|"     th:value="${address.latitudedegree}"     th:id="|latitudedegree_${stat.index}|" type="hidden">
                                                            </span>
                                                            <span th:if="${address.longitudedegree == null || address.latitudedegree == null}">
                                                                <div class="line20 line20T fw" style="width: calc(100% - 408px)" th:onclick="getLongLat([[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]],[[${address.areaId}]],[[${address.detailAddr}]],[[${stat.index}]],'',this)"><i class="fa fa-map-marker" style="color: #ff1f1f;font-size: 16px;margin-right: 5px;" ></i>地址:[[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]](<span th:id="|longLat_${stat.index}|">无经纬度</span>)</div>
                                                                <input th:name="|shippingAddressList[${stat.index}].longitudedegree|"      th:id="|longitudedegree_${stat.index}|" type="hidden">
                                                                <input th:name="|shippingAddressList[${stat.index}].latitudedegree|"      th:id="|latitudedegree_${stat.index}|" type="hidden">
                                                            </span>
                                                            <div class="toTextT"><i class="glyphicon glyphicon-trash" style="color:#ec4758;" th:onclick="|onTrash(${stat.index})|"></i></div>

                                                            <input th:name="|shippingAddressList[${stat.index}].contact|"      th:value="${address.contact}"      th:id="|contact_${stat.index}|" type="hidden">
                                                            <input th:name="|shippingAddressList[${stat.index}].mobile|"       th:value="${address.mobile}"       th:id="|mobile_${stat.index}|" type="hidden">
<!--                                                            <input th:name="|shippingAddressList[${stat.index}].deliveryId|"   th:value="${address.deliveryId}"   th:id="|deliveryId_${stat.index}|" type="hidden">-->
                                                            <input th:name="|shippingAddressList[${stat.index}].provinceId|"   th:value="${address.provinceId}"   th:id="|provinceId_${stat.index}|" type="hidden">
                                                            <input th:name="|shippingAddressList[${stat.index}].cityId|"       th:value="${address.cityId}"       th:id="|cityId_${stat.index}|" type="hidden">
                                                            <input th:name="|shippingAddressList[${stat.index}].areaId|"       th:value="${address.areaId}"       th:id="|areaId_${stat.index}|" type="hidden">
                                                            <input th:name="|shippingAddressList[${stat.index}].provinceName|" th:value="${address.provinceName}" th:id="|provinceName_${stat.index}|" type="hidden">
                                                            <input th:name="|shippingAddressList[${stat.index}].cityName|"     th:value="${address.cityName}"     th:id="|cityName_${stat.index}|" type="hidden">
                                                            <input th:name="|shippingAddressList[${stat.index}].areaName|"     th:value="${address.areaName}"     th:id="|areaName_${stat.index}|" type="hidden">
<!--                                                            <input th:name="|shippingAddressList[${stat.index}].addrCode|"     th:value="${address.addrCode}"     th:id="|addrCode_${stat.index}|" type="hidden">-->
                                                            <input th:name="|shippingAddressList[${stat.index}].addrName|"     th:value="${address.addrName}"     th:id="|addrName_${stat.index}|" type="hidden">
                                                            <input th:name="|shippingAddressList[${stat.index}].detailAddr|"   th:value="${address.detailAddr}"   th:id="|detailAddr_${stat.index}|" type="hidden">
                                                            <input th:name="|shippingAddressList[${stat.index}].addressType|"  th:value="${address.addressType}"  th:id="|addressType_${stat.index}|" type="hidden" >

                                                        </div>
                                                    </div>
                                                </div>
                                            </th:block>
                                            <th:block th:if="${address.isChangeAddress!=1}">
                                                <div class="col-sm-12 mt10" style="display: none;">

                                                </div>
                                            </th:block>

                                            <div class="col-sm-12">
                                                <div class="fhf_table">
                                                    <div class="fixed-table-body" style="margin: 0px -5px;">
                                                        <table class="custom-tab tab table table-bordered">
                                                            <thead>
                                                            <tr>
                                                                <th style="width: 15%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" th:onclick="|insertGoodsRow(${stat.index},1)|" title="新增行">+</a></th>
                                                                <th style="width: 25%;">客户单号</th>
                                                                <th style="width: 15%;">货品名称</th>
                                                                <th style="width: 15%;">包装</th>
                                                                <th style="width: 30%;">件数/重量(吨)/体积(m³)</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            <tr th:each="goods,goodsStat: ${address.shippingGoodsList}" th:id="|shdz_${goods.params.uid}|">
                                                                <td>
                                                                    <div style="display: inline-block;">
                                                                        <a th:data-uid="${goods.params.uid}"
                                                                           th:onclick="|removeGoodsRow(this.getAttribute('data-uid'),${stat.index},1)|"
                                                                           class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"
                                                                           title="删除选择行"></a>
                                                                    </div>
                                                                    <div style="display: inline-block;margin-left:5px">
                                                                        <a class="fa fa-clone" style="color: #0092e7;font-size: 17px;"
                                                                           th:data-uid="${goods.params.uid}"
                                                                           th:onclick="|insertGoodsRow(${stat.index},1,null,this.getAttribute('data-uid'))|"
                                                                           title="复制货品"></a>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <input th:if="${type == 'edit'}"
                                                                           th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].custOrderno|"
                                                                           th:id="|custOrderno_fhdz_${goods.params.uid}|"
                                                                           th:value="${goods.custOrderno}"
                                                                           th:data-uid="${goods.params.uid}"
                                                                           placeholder="请输入客户发货单号" class="form-control" type="text"
                                                                           maxlength="50" autocomplete="off" oninput="syncArriGoodsForType(0)">
                                                                    <input th:if="${type == 'copy'}"
                                                                           th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].custOrderno|"
                                                                           th:id="|custOrderno_fhdz_${goods.params.uid}|"
                                                                           th:data-uid="${goods.params.uid}"
                                                                           placeholder="请输入客户发货单号" class="form-control" type="text"
                                                                           maxlength="50" autocomplete="off" oninput="syncArriGoodsForType(0)">
                                                                </td>

                                                                <td>
                                                                    <input th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].goodsName|"      th:id="|goodsName_shdz_${goods.params.uid}|" th:value="${goods.goodsName}"
                                                                           th:data-uid="${goods.params.uid}" onclick="selectGoods(this.getAttribute('data-uid'),1)" type="text" placeholder="" class="form-control valid" autocomplete="off" required readonly/>
<!--                                                                    <input th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].goodsId|"        th:id="|goodsId_shdz_${goods.params.uid}|" th:value="${goods.goodsId}" type="hidden"/>-->
<!--                                                                    <input th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].goodsCode|"      th:id="|goodsCode_shdz_${goods.params.uid}|" th:value="${goods.goodsCode}" type="hidden"/>-->
                                                                    <input th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].goodsCharacter|" th:id="|goodsCharacter_shdz_${goods.params.uid}|" th:value="${goods.goodsCharacter}" type="hidden"/>
                                                                    <input th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].goodsTypeName|"  th:id="|goodsTypeName_shdz_${goods.params.uid}|" th:value="${goods.goodsTypeName}" type="hidden"/>
                                                                    <input th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].goodsType|"      th:id="|goodsTypeId_shdz_${goods.params.uid}|" th:value="${goods.goodsType}" type="hidden"/>
                                                                </td>
                                                                <td>
                                                                    <div class="col-sm-12">
                                                                        <select
                                                                                th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].packId|"
                                                                                th:id="|packId_shdz_${goods.params.uid}|"
                                                                                onchange="syncArriGoodsForType(1)"
                                                                                th:with="type=${@dict.getType('package_type')}" class="form-control valid" aria-invalid="false" required>
                                                                            <option value=""></option>
                                                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${goods.packId} == ${dict.dictValue}" ></option>
                                                                        </select>
<!--                                                                        <select th:if="${type == 'copy'}"-->
<!--                                                                                th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].packId|"-->
<!--                                                                                th:id="|packId_shdz_${goods.params.uid}|"-->
<!--                                                                                onchange="syncArriGoodsForType(1)"-->
<!--                                                                                th:with="type=${@dict.getType('package_type')}" class="form-control valid" aria-invalid="false" required>-->
<!--                                                                            <option value=""></option>-->
<!--                                                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>-->
<!--                                                                        </select>-->
                                                                    </div>
                                                                </td>

                                                                <td>
                                                                    <div class="col-sm-4" style="padding-left:2px;padding-right: 2px">
                                                                        <input
                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].num|"
                                                                               th:id="|num_shdz_${goods.params.uid}|"
                                                                               th:value="${goods.num}"
                                                                               th:data-uid="${goods.params.uid}"
                                                                               oninput="$.numberUtil.onlyNumber(this);getPriceByGoods(1);checkNumWeightVolume(this.getAttribute('data-uid'),1);syncArriGoodsForType(1);calculateSum()"
                                                                               style="text-align: right" autocomplete="off"  placeholder="" class="form-control" type="text">
<!--                                                                        <input th:if="${type == 'copy'}"-->
<!--                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].num|"-->
<!--                                                                               th:id="|num_shdz_${goods.params.uid}|"-->
<!--                                                                               th:data-uid="${goods.params.uid}"-->
<!--                                                                               oninput="$.numberUtil.onlyNumber(this);getPrice(this.getAttribute('data-uid'),1);checkNumWeightVolume(this.getAttribute('data-uid'),1);syncArriGoodsForType(1)"-->
<!--                                                                               style="text-align: right" autocomplete="off"  placeholder="" class="form-control" type="text">-->
                                                                    </div>
                                                                    <div class="col-sm-4" style="padding-left:2px;padding-right: 2px">
                                                                        <input
                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].weight|"
                                                                               th:id="|weight_shdz_${goods.params.uid}|"
                                                                               th:value="${goods.weight}"
                                                                               th:data-uid="${goods.params.uid}"
                                                                               oninput="$.numberUtil.onlyNumber(this);getPriceByGoods(1);checkNumWeightVolume(this.getAttribute('data-uid'),1);syncArriGoodsForType(1);calculateSum()"
                                                                               style="text-align: right" autocomplete="off" placeholder="" class="form-control" type="text">
<!--                                                                        <input th:if="${type == 'copy'}"-->
<!--                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].weight|"-->
<!--                                                                               th:id="|weight_shdz_${goods.params.uid}|"-->
<!--                                                                               th:data-uid="${goods.params.uid}"-->
<!--                                                                               oninput="$.numberUtil.onlyNumber(this);getPrice(this.getAttribute('data-uid'),1);checkNumWeightVolume(this.getAttribute('data-uid'),1);syncArriGoodsForType(1)"-->
<!--                                                                               style="text-align: right" autocomplete="off" placeholder="" class="form-control" type="text">-->
                                                                    </div>
                                                                    <div class="col-sm-4" style="padding-left:2px;padding-right: 2px">
                                                                        <input
                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].volume|"
                                                                               th:id="|volume_shdz_${goods.params.uid}|"
                                                                               th:value="${goods.volume}"
                                                                               th:data-uid="${goods.params.uid}"
                                                                               oninput="$.numberUtil.onlyNumber(this);getPriceByGoods(1);checkNumWeightVolume(this.getAttribute('data-uid'),1);syncArriGoodsForType(1);calculateSum()"
                                                                               style="text-align: right" autocomplete="off" placeholder="" class="form-control" type="text">
<!--                                                                        <input th:if="${type == 'copy'}"-->
<!--                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].volume|"-->
<!--                                                                               th:id="|volume_shdz_${goods.params.uid}|"-->
<!--                                                                               th:data-uid="${goods.params.uid}"-->
<!--                                                                               oninput="$.numberUtil.onlyNumber(this);getPrice(this.getAttribute('data-uid'),1);checkNumWeightVolume(this.getAttribute('data-uid'),1);syncArriGoodsForType(1)"-->
<!--                                                                               style="text-align: right" autocomplete="off" placeholder="" class="form-control" type="text">-->
                                                                    </div>
                                                                </td>
                                                                <!--<td>
                                                                    <div class="input-group">
                                                                        <select th:if="${type == 'edit'}" th:data-uid="${goods.params.uid}"
                                                                                onchange="judgePriceType(this.getAttribute('data-uid'),1);"
                                                                                th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].billingMethod|"
                                                                                th:id="|billingMethod_shdz_${goods.params.uid}|" class="form-control valid" aria-invalid="false" required>
                                                                            <option value=""></option>
                                                                            <option th:each="billingMethod:${billingMethods}"  th:value="${billingMethod.value}" th:text="${billingMethod.context}" th:selected="${goods.billingMethod} == ${billingMethod.value}"></option>
                                                                        </select>
                                                                        <select th:if="${type == 'copy'}" th:data-uid="${goods.params.uid}"
                                                                                onchange="judgePriceType(this.getAttribute('data-uid'),1);"
                                                                                th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].billingMethod|"
                                                                                th:id="|billingMethod_shdz_${goods.params.uid}|" class="form-control valid" aria-invalid="false" required>
                                                                            <option value=""></option>
                                                                            <option th:each="billingMethod:${billingMethods}"  th:value="${billingMethod.value}" th:text="${billingMethod.context}"></option>
                                                                        </select>

                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <input th:if="${type == 'edit'}"
                                                                           th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].pc|"
                                                                           th:id="|pc_shdz_${goods.params.uid}|"  th:data-uid="${goods.params.uid}"
                                                                           th:value="${goods.pc}"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateSum(this.getAttribute('data-uid'),1);syncArriGoodsForType(1)"
                                                                           style="text-align: right" placeholder="" class="form-control" autocomplete="off" type="text">
                                                                    <input th:if="${type == 'copy'}"
                                                                           th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].pc|"
                                                                           th:id="|pc_shdz_${goods.params.uid}|"  th:data-uid="${goods.params.uid}"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateSum(this.getAttribute('data-uid'),1);syncArriGoodsForType(1)"
                                                                           style="text-align: right" placeholder="" class="form-control" autocomplete="off" type="text">
                                                                </td>
                                                                <td>
                                                                    <div class="input-group">
                                                                        <input th:if="${type == 'edit'}"
                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].sum|"
                                                                               th:id="|sum_shdz_${goods.params.uid}|"
                                                                               th:value="${goods.sum}"
                                                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotals(1);syncArriGoodsForType(1)"
                                                                               placeholder="" class="form-control" type="text" style="text-align: right" autocomplete="off" required>
                                                                        <input th:if="${type == 'copy'}"
                                                                               th:name="|shippingAddressList[${stat.index}].shippingGoodsList[${goodsStat.index}].sum|"
                                                                               th:id="|sum_shdz_${goods.params.uid}|"
                                                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotals(1);syncArriGoodsForType(1)"
                                                                               placeholder="" class="form-control" type="text" style="text-align: right" autocomplete="off" required>
                                                                    </div>
                                                                </td>-->


                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="fhf_close">
                                            <div th:onclick="|removeAddrRow(${stat.index},1)|" class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" ></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        <!--
            <div class="row mt10">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion" href="tabs_panels.html#collapsefour">指导价信息</a>
                            </h4>
                        </div>
                        <div id="collapsefour" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <div class="row no-gutter">
                                    <div class="col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left" style="width: 5em;">指导价：</label>
                                            <div class="flex_right">
                                                <div class="over">
                                                    <div class="fl" style="width: 80%">
                                                        <input name="guidingPrice" id="guidingPrice" placeholder="指导价"
                                                               class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                               type="text" autocomplete="off">
                                                        &lt;!&ndash; <input type="hidden" id="guidePriceDetailId" name="params[guidePriceDetailId]"> &ndash;&gt;
                                                    </div>
                                                    <div class="fl ml10" style="line-height: 30px;display: none;" id="guideTipsDiv">
        &lt;!&ndash;                                                <a href="#" data-toggle="tooltip" data-placement="top" title="涉及对账收款月份非自然月的时候需要填写，例如某客户25号之后单据算到下月对账，则特殊日期填25">&ndash;&gt;
        &lt;!&ndash;                                                    <i class="fa fa-question-circle" style="color: #7f7f7f;font-size: 18px"></i>&ndash;&gt;
        &lt;!&ndash;                                                </a>&ndash;&gt;
                                                        <div class="tooltips">
                                                            <i class="fa fa-question-circle" style="color: red;font-size: 18px"></i>
                                                            <div class="tooltiptext">
                                                                <div id="guideTipsTit"></div>
                                                                <div id="guideTipsCon"></div>
                                                                <div id="guideTipsCon1"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-8">
                                        <div class="over">
                                            &lt;!&ndash; <div class="fcff6 fl" style="line-height: 25px">首次指导价：￥<nobr id="firstGuidingPrice">0</nobr></div> &ndash;&gt;
                                            <div class="hisbtn ml20 fl" onclick="getHistoryGuidePriceDetailList()">查看历史指导价</div>
                                        </div>
                                    </div>

                                </div>
        &lt;!&ndash;                        <div class="fw line20">整车实际成交运费 13米 平板货车</div>&ndash;&gt;
                                &lt;!&ndash; <div class="fixed-table-body" style="margin: 10px -10px 0;">
                                    <table border="0" class="custom-tab table">
                                        <thead>
                                        <tr>
                                            <th>时间</th>
                                            <th th:each="mapS,status:${years}" th:text="${mapS}"></th>
                                        </tr>
                                        </thead>
                                        <tbody style="border-bottom: 1px solid #A6A6A6;">
                                        <tr>
                                            <td>
                                                <div class="fc80">成交次数/平均价</div>
                                            </td>
                                            <td th:each="mapS,status:${years}">
                                                <div class="over disin" >
                                                    <a class="btn btn-xs eye fl" href="javascript:void(0)" title="明细"
                                                       th:id="|avgPriceUrl_${status.index}|">
                                                    </a>
                                                    <div class="fl line20 ml10">
                                                        <span class="fw" th:id="|avgPrice_${status.index}|">0次/￥0</span></div>
                                                </div>
                                            </td>

                                        </tr>
                                        </tbody>
                                    </table>
                                </div> &ndash;&gt;
                            </div>
                        </div>
                    </div>
                </div>
            </div>-->

            <div class="row mt10">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion" href="tabs_panels.html#collapsefour">成本价信息</a>
                            </h4>
                        </div>
                        <div id="collapsefour" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <div class="row no-gutter">
                                    <div class="col-sm-4">
                                        <div style="display: flex;align-items: center;">
                                            <div id="costPrice" style="font-size: 15px;"></div>

                                            <div class="flex ml10">

                                                <label class="" style="color: #333333;text-align: right;line-height: 26px;">成本价：</label>
                                                <div class="">
                                                    <div class="over">
                                                        <div class="fl" style="width: 80%;color: #333333;">
                                                            <input name="costPrice" id="totalCostPrice" placeholder="成本价"
                                                                   class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                   type="text" autocomplete="off" th:value="${type == 'edit' ? invoice.costPrice : ''}">
                                                            <!-- <input type="hidden" id="guidePriceDetailId" name="params[guidePriceDetailId]"> -->
                                                            <input name="costBillingType" id="costBillingType" type="hidden" />
                                                            <span id="costBillingTypeLabel"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="over" style="display: flex;align-items: center;">
                                            <div id="guidingPriceDiv" style="font-size: 17px;"></div>

                                            <div class="hisbtn ml20 fl" onclick="getHistoryGuidePriceDetailList()">查看历史指导价</div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapse-1">议价凭证</a>
                            </h4>
                        </div>
                        <div id="collapse-1" class="panel-collapse collapse in">
                            <div class="panel-body" style="padding: 15px 10px">
                                <div class="row no-gutter">
                                    <div class="col-sm-12 col-md-6">
                                        <div class="flex">
                                            <div class="flex_right">
                                                <input name="bargainFile" id="bargainFile" class="form-control" type="file" multiple>
                                                <input type="hidden" id="bargainFileId" name="bargainFileId">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapseSix">保险信息</a>
                            </h4>
                        </div>
                        <div id="collapseSix" class="panel-collapse collapse in">
                            <div class="panel-body" style="padding: 15px 10px">
                                <div class="row no-gutter">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left">保险公司名称：</label>
                                            <div class="flex_right">
                                                <input name="insuranceCompany" id="insuranceCompany" placeholder="保险公司名称" class="form-control" type="text"
                                                       maxlength="25" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left">保险单号：</label>
                                            <div class="flex_right">
                                                <input name="insuranceNo" id="insuranceNo" placeholder="保险单号" class="form-control" type="text"
                                                       maxlength="25" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="row no-gutter">
                                    <div class="col-sm-12 col-md-6">
                                        <div class="flex">
                                            <label class="flex_left">保险附件：</label>
                                            <div class="flex_right">
                                                <input name="insuranceAppendix" id="insuranceAppendix" class="form-control" type="file" multiple>
                                                <input type="hidden" id="insuranceAppendixId" name="insuranceAppendixId">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



        </div>

        <div style="height: 50px;"></div>
        <div class="row">
            <div class="col-sm-offset-5 col-sm-10" style="background-color: #fff;">
                <div class="checkbox mr5">
                    <label>
                        <input type="checkbox" name="isUrgent" value="1">紧急单推送
                    </label>
                </div>
                <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
                <button th:if="${type == 'copy'}" type="button" class="btn btn-sm btn-primary" onclick="submitHandler(1)"><i class="fa fa-check"></i>保存多单</button>&nbsp;
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
                </button>
            </div>
        </div>
    </form>

</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-fileinput-js"/>

<script th:inline="javascript">
    //是否是车队
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/invoice" : ctx + "invoice";

    //判断是 复制 还是 修改 copy为复制 edit为修改
    var type = [[${type}]];
    //图片
    var sysUploadFiles = [[${sysUploadFiles}]];
    var bargainFiles = [[${bargainFiles}]];

    // 货品明细 标识符，累加
    var goodsIndex = $("#invPackGoodsSize").val();

    //地址下标
    var addr_index = [[${addr_index}]]
    //每个地址对应的货品下标
    var addr_goods_index = [[${addr_goods_index}]]
    //发货单地址的集合
    var fhdz_addr_list = [[${fhdz_addr_list}]]
    //收货地址的集合
    var shdz_addr_list = [[${shdz_addr_list}]]

    const rfqLineId = [[${invoice.rfqEnquiryLineId}]];
    const rfqCartypeId = [[${invoice.rfqCartypeId}]];

    var dictBillingType = [[${@dict.getType('billing_type')}]];
    // 新增：客户合同有效性
    var customerContractBL = [[${customerContractBL}]];

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('hide');
        $('#collapsefour').collapse('show');
        $('#collapse-1').collapse('show');

        //件数、重量、体积必填一 校验
        for (const addr of fhdz_addr_list) {
            for (const fhGoods of addr_goods_index[addr]) {
                addNumWeightVolumeValidate('fhdz_' + fhGoods.value);
            }
        }
        for (const addr of shdz_addr_list) {
            for (const shGoods of addr_goods_index[addr]) {
                addNumWeightVolumeValidate('shdz_' + shGoods.value);
            }
        }


        if(type == 'edit'){
            //要求提货日期
            var reqDeliDateOld = $("#reqDeliDateOld").val().substr(11,2);
            // if($.common.startWith(reqDeliDateOld,'08')){
            //     $("#optionsRadios1").attr("checked","checked");
            // }else if($.common.startWith(reqDeliDateOld,10)){
            //     $("#optionsRadios2").attr("checked","checked");
            // }else if($.common.startWith(reqDeliDateOld,12)){
            //     $("#optionsRadios3").attr("checked","checked");
            // }else if($.common.startWith(reqDeliDateOld,14)){
            //     $("#optionsRadios4").attr("checked","checked");
            // }else{
                $("#reqDeliDateHour").val(reqDeliDateOld);
            // }
        }

        /*
         * 取消单选框
         */
        $("#reqDeliDateHour").change(function(){
            $('input:radio:checked').attr("checked",false);
        });
        /*
        * 取消选择整点
        */
        $("#optionsRadios1").change(function(){
            $("#reqDeliDateHour").val("");
        });
        $("#optionsRadios2").change(function(){
            $("#reqDeliDateHour").val("");
        });
        $("#optionsRadios3").change(function(){
            $("#reqDeliDateHour").val("");
        });
        $("#optionsRadios4").change(function(){
            $("#reqDeliDateHour").val("");
        });



        //图片功能
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };

        if (type == "edit") {
            $.file.loadEditFiles("insuranceAppendix", "insuranceAppendixId", sysUploadFiles, picParam);
        } else {
            $.file.initAddFiles("insuranceAppendix", "insuranceAppendixId", picParam);
        }

        //图片功能
        var bargainPicParam = {
            maxFileCount: 1,
            publish: "bargainFileCmt",
            fileType: "file"
        };
        if (type == "edit") {
            $.file.loadEditFiles("bargainFile", "bargainFileId", bargainFiles, bargainPicParam);
        } else {
            $.file.initAddFiles("bargainFile", "bargainFileId", bargainPicParam);
        }

        if ([[${client.contractPriceType}]] != 1 && rfqLineId == null) {
            //'0:合同价 1:议价'
            $("#ifBargain").val(0);

            $('#ifBargain option[value="1"]').prop('disabled', true);
        }


        changeisGetContractPrice()

        //初始化指导价
        getGuidingPrice();

        //获取合同价
        changeIfBargain();
        // 询价转议价发货单且有包车单价时，计算不含税总运费
        if (rfqLineId && $('#unitPrice').val()) {
            calculateSum()
        }

        $('[data-toggle="tooltip"]').tooltip()

        if (type == "copy") {
            getMuleage()
        }

        if (type == "edit") {

            $("#totalCostPrice").val([[${invoice.costPrice}]])

            let costBillingType = [[${invoice.costBillingType}]]
            $("#costBillingType").val(costBillingType);
            $('#costBillingTypeLabel').text(dictBillingType.find(itm => itm.dictValue == costBillingType)?.dictLabel);

            // if ([[${invoice.costPrice}]] && [[${invoice.costPrice}]] != 0) {
            //     $('#totalCostPrice').prop('readonly', true).css('background-color', '#f0f0f0');
            // }else {
            //     $('#totalCostPrice').prop('readonly', false).css('background-color', '');
            // }

        }

        $('#totalCostPrice').prop('readonly', true).css('background-color', '#f0f0f0');

        $('[unknown-contact]').tooltip('show')

        handleRoundTripSelect()

        disableRfqField();

        if (type == "copy") {
            let ifBargain = $("#ifBargain").val();
            if(ifBargain && ifBargain == 0) {
                // 客户合同校验
                if (customerContractBL !== true) {
                    // 禁用保存按钮
                    $("button[onclick='submitHandler()']").prop('disabled', true);
                    $("button[onclick='submitHandler(1)']").prop('disabled', true);
                    // 弹出提醒
                    setTimeout(function() {
                        $.modal.alertWarning('该客户合同暂无或已失效，无法下单。');
                    }, 300);
                }
            }
        }
    });

    function disableRfqField() {
        const rfqLine = [[${rfqLine}]];
        const rfqCarLenObj = [[${rfqCarLenObj}]];
        if (rfqLine != null) {
            $('#custAbbr').prop("disabled", true);
            if (rfqLine.roundTrip != null) {
                $('#isRoundTrip').prop("disabled", true);
            }
            $('#unitPrice').prop("disabled", true);
        }
        if (rfqCarLenObj != null) {
            $('#transCode').prop("disabled", true);
            if (rfqCarLenObj.billingMethod != null) {
                $('#billingMethod').prop("disabled", true);
            }
            if (rfqCarLenObj.carLen != null) {
                $('#carLen').prop("disabled", true);
            }
            if (rfqCarLenObj.carType != null) {
                $('#carType').prop("disabled", true);
            } else {
                $('#carType').prop("disabled", false);
            }
            if (rfqCarLenObj.isOversize != null) {
                $('#isOversize').prop("disabled", true);
            }
        }
    }

    layui.use('form', function(){
        var form = layui.form;

        form.on('checkbox(isAddPrice)', function(){
            var addrId = $(this).attr('data-addrId');
            $("#isGetContractPrice_" + addrId).val(this.checked ? 1 : 0);

            getPrice()
        });
    });


    function openEnquiry(enquiryId) {
        let rfqEnquiryId = $("#rfqEnquiryId").val()
        $.modal.openTab("询价明细",ctx + "rfq/enquiry-view/" + rfqEnquiryId);
    }
    /**
     * 修改结算方式
     */
    function changeBalaType() {
        let balaType = $("#balaType").val();
        $("#collectAmount").val("");
        if (balaType == '2' || balaType == '5' || balaType == '6') {
            $("#collectAmount").removeAttr("disabled");
        } else {
            $("#collectAmount").attr('disabled', "disabled");
        }

        if (balaType == '2') {
            $("#billingType").val(6)
        }else {
            $("#billingType").val('')
        }

    }


    /**
     * 件数、重量、体积必填一 校验
     * @param i 标识符
     * */
    function addNumWeightVolumeValidate(t) {
        $("#num_" + t).rules("add", {
            required: {
                depends: function () { //三选一
                    var num = $('#num_' + t).val();
                    var weight = $('#weight_' + t).val();
                    var volume = $('#volume_' + t).val();
                    return (num.length <= 0 && weight.length <= 0 && volume.length <= 0);
                }
            }
        });
        $("#weight_" + t).rules("add", {
            required: {
                depends: function () { //三选一
                    var num = $('#num_' + t).val();
                    var weight = $('#weight_' + t).val();
                    var volume = $('#volume_' + t).val();
                    return (num.length <= 0 && weight.length <= 0 && volume.length <= 0);
                }
            }
        });
        $("#volume_" + t).rules("add", {
            required: {
                depends: function () { //三选一
                    var num = $('#num_' + t).val();
                    var weight = $('#weight_' + t).val();
                    var volume = $('#volume_' + t).val();
                    return (num.length <= 0 && weight.length <= 0 && volume.length <= 0);
                }
            }
        });
    }

    /**
     * 校验件数、重量、体积 - 用于输入完成后再次校验
     *
     * @param uid   标识符
     * @param type  类型  0发货  1收货
     */
    function checkNumWeightVolume(uid, type) {
        let t = type === 0 ? 'fhdz_' : 'shdz_';

        var id = uid;
        //单独校验
        $("#form-invoice-edit").validate().element($("#num_" + t + id));
        $("#form-invoice-edit").validate().element($("#weight_" + t + id));
        $("#form-invoice-edit").validate().element($("#volume_" + t + id));
    }

    /**
     * 基础信息 - 客户名称
     */

    var crtGuidePrice = [[${client.crtGuidePrice}]]
    function selectClient() {
        $.modal.open("选择客户", ctx + "client/related?permission=sales",'','',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //清空结算客户信息
            $("#balaName").val("");
            $("#balaCode").val("");
            $("#balaCustomerId").val("");

            //清空提货地址信息
            // $("#deliAddrName").val("");
            // $("#deliAddrName_text").html("");
            //
            // $("#deliveryId").val("");
            // $("#deliProvinceId").val("");
            // $("#deliCityId").val("");
            // $("#deliAreaId").val("");
            // $("#deliMobile").val("");
            // $("#deliMobile_text").html("");
            //
            // $("#deliContact").val("");
            // $("#deliContact_text").html("");
            //
            // $("#deliView").val("");
            // $("#deliView_text").html("");
            //
            // $("#deliProName").val("");
            // $("#deliCityName").val("");
            // $("#deliAreaName").val("");
            // $("#deliAddrCode").val("");
            // $("#deliDetailAddr").val("");
            //
            // //清空收货地址信息
            // $("#arriAddrName").val("");
            // $("#arriAddrName_text").html("");
            //
            // $("#arrivalId").val("");
            // $("#arriProvinceId").val("");
            // $("#arriCityId").val("");
            // $("#arriAreaId").val("");
            // $("#arriMobile").val("");
            // $("#arriMobile_text").html("");
            //
            // $("#arriContact").val("");
            // $("#arriContact_text").html("");
            //
            // $("#addiView").val("");
            // $("#addiView_text").html("");
            //
            // $("#arriProName").val("");
            // $("#arriCityName").val("");
            // $("#arriAreaName").val("");
            // $("#arriAddrCode").val("");
            // $("#arriDetailAddr").val("");
            //清空集团
            $("#groupName").val("");
            $("#groupId").val("");

            //清空货品名称与货品类型
            // $("[id^=goodsId_]").val("")
            // $("[id^=goodsName_]").val("")
            // $("[id^=goodsCode_]").val("")
            // $("[id^=goodsCharacter_]").val("")
            // $("[id^=goodsTypeName_]").val("")

            //清空地址和货品
            clearAddrAndGoods()

            //客户id
            $("#customerId").val(rows[0]["customerId"]);
            //客户简称
            $("#custAbbr").val(rows[0]["custAbbr"]);
            //客户名称
            $("#custName").val(rows[0]["custName"]);
            //客户编码
            $("#custCode").val(rows[0]["custCode"]);
            //结算公司
            $("#balaCorpId").val(rows[0]["balaCorp"]);
            //驻场组
            $("#stationDept").val(rows[0]["stationDept"]);
            //驻场组名称
            $("#stationDeptName").val(rows[0]["stationDeptName"]);
            //结算组
            $("#balaDept").val(rows[0]["balaDept"]);
            //运营部
            $("#salesDept").val(rows[0]["salesDept"]);
            //业务员
            $("#psndoc").val(rows[0]["psndoc"]);
            //开票公司
            $("#billingCorp").val(rows[0]["billingCorp"]);
            //app联系人默认填业务员名称
            $("#appDeliContact").val(rows[0]["appDeliContact"]);
            //app联系方式默认填业务员联系方式
            $("#appDeliMobile").val(rows[0]["appDeliMobile"]);

            //指导价浮动比率
            $("#referenceRate").val(rows[0]["referenceRate"]);

            //是否需要指导价
            crtGuidePrice = rows[0]["crtGuidePrice"];

            //
            let contractPriceType = rows[0]["contractPriceType"]

            if (contractPriceType == 1) {
                //'0:合同价 1:议价'
                $("#ifBargain").val(rows[0]["ifBargain"]);

                if (rows[0]["ifBargain"] == 1) {
                    $("#bargainMemoReqSpan").hide()
                    $("#bargainMemo").removeAttr("required");

                    $("#bargainMemoDiv").show()

                }else {
                    if (!isFleet) {
                        $("#bargainMemoReqSpan").show()
                        $("#bargainMemo").attr('required', true);
                    }
                    $("#bargainMemoDiv").hide()
                }
            }else {
                //'0:合同价 1:议价'
                $("#ifBargain").val(0);

                $('#ifBargain option[value="1"]').prop('disabled', true);

            }

            //集团
            $.ajax({
                url: ctx + "group/getGroupByCustomerId",
                type: "post",
                dataType: "json",
                data: {customerId: rows[0]["customerId"]},
                success: function (result) {
                    if (result.code == 0 && result.data!=undefined) {
                        $("#groupName").val(result.data.GROUP_NAME);
                        $("#groupId").val(result.data.GROUP_ID)
                    }
                }
            });

            //获取默认结算客户
            $.ajax({
                url: ctx + "client/getDefaultCustBalaByCustomerId",
                type: "post",
                dataType: "json",
                data: {customerId: rows[0]["customerId"]},
                success: function (result) {
                    if (result.code == 0) {
                        if (result.data != null) {
                            //结算客户名称
                            $("#balaName").val(result.data.custName);
                            //结算客户编码
                            $("#balaCode").val(result.data.custCode);
                            //结算客户id
                            $("#balaCustomerId").val(result.data.customerId);
                        }
                    }
                }
            });
            // //批量获取货品单价并计算合计
            // getPriceBatch();

            //获取指导价
            // getGuidingPrice();

            checkCustomerContract()

            //选中完需单独校验
            $("#form-invoice-edit").validate().element($("#custAbbr"));
            layer.close(index);
        });
    }


    /**
     * 校验客户合同
     */
    function checkCustomerContract() {
        let ifBargain = $("#ifBargain").val();
        let customerId = $("#customerId").val();

        $("button[onclick='submitHandler()']").prop('disabled', false);
        $("button[onclick='submitHandler(1)']").prop('disabled', false);

        if(ifBargain && ifBargain == 0) {
            $.ajax({
                url: ctx + "invoice/checkCustomerContractValid",
                type: "get",
                data: {customerId: customerId},
                success: function(res) {
                    if (!res) {
                        // 禁用保存按钮
                        $("button[onclick='submitHandler()']").prop('disabled', true);
                        $("button[onclick='submitHandler(1)']").prop('disabled', true);
                        // 弹出提醒
                        setTimeout(function() {
                            $.modal.alertWarning('该客户合同暂无或已失效，无法下单。');
                        }, 300);
                    }
                }
            });
        }
    }

    /**
     * 清空地址信息和货品信息
     */
    function clearAddrAndGoods(){
        for (var i = fhdz_addr_list.length - 1; i >= 0; i--) {
            removeAddrRow(fhdz_addr_list[i], 0)
        }

        // for (let fhdz of fl) {
        //     removeAddrRow(fhdz, 0)
        // }
        for (var i = shdz_addr_list.length - 1; i >= 0; i--) {
            removeAddrRow(shdz_addr_list[i], 1)
        }

        // for (let shdz of shdz_addr_list) {
        //     removeAddrRow(shdz, 1)
        // }
    }

    /**
     * 基础信息 - 获取结算客户
     */
    function selectClearingClient() {
        // 如果 客户名称未选中，则随意传入一个不存在的id:'false',让搜索出来的列表为空
        var customerId = $("#customerId").val() === '' ? 'false' : $("#customerId").val();
        $.modal.open("选择客户", ctx + "client/related?customerId=" + customerId,'','',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            //结算客户名称
            $("#balaName").val(rows[0]["custName"]);
            //结算客户编码
            $("#balaCode").val(rows[0]["custCode"]);
            //结算客户id
            $("#balaCustomerId").val(rows[0]["customerId"]);

            //选中完需单独校验
            $("#form-invoice-edit").validate().element($("#balaName"));
            layer.close(index);
        });
    }
    /**
     * 设置紧急程度名称
     */
    function setUrgentLevelName() {
        $("#urgentLevelName").val($("#urgentLevel option:selected").text());
    }

    /**
     * 设置车型名称
     */
    function setCarTypeName() {
        $("#carTypeName").val($("#carType option:selected").text());
    }

    /**
     * 设置车长名称
     */
    function setCarLenName() {
        $("#carLenName").val($("#carLen option:selected").text());

    }

    /**
     * 设置运输方式名称
     */
    function setTransName() {
        $("#transName").val($("#transCode option:selected").text());
    }

    /**
     * 选择调度组
     */
    function setTransLineName() {
        $("#transLineName").val($("#transLineId option:selected").text());
    }

    /**
     *  选择调度人员
     */
    function selectUser(){
        layer.open({
            type: 2,
            area: ['800px', $(window).height() - 50 + 'px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "选择调度员",
            content: ctx + "system/user/selectUser/dispatcherUserList",
            btn: ['确定','清空','关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero) {
                //获取整行
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }

                //调度人员
                $("#dispatcherName").val(rows[0]["userName"]);
                $("#dispatcherId").val(rows[0]["userId"]);

                layer.close(index);
            },
            btn2: function(index, layero){
                $("#dispatcherName").val("");
                $("#dispatcherId").val("");
                layer.close(index);
            },
            cancel: function(index) {
                return true;
            }
        });
    }

    /**
     * 驻场人员选择
     */
    function selectResidentsUser() {
        layer.open({
            type: 2,
            area: ['800px', $(window).height() - 50 + 'px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "选择驻场人员",
            content: ctx + "system/user/selectUser/stationUserList",
            btn: ['确定','清空','关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero) {
                //获取整行
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }

                //调度人员
                var residentsName = $.map(rows,function (row) {
                    return row["userName"];
                }).join(",");
                $("#residentsName").val(residentsName);
                var residentsId = $.map(rows,function (row) {
                    return row["userId"];
                }).join(",");
                $("#residentsId").val(residentsId);

                layer.close(index);
            },
            btn2: function(index, layero){
                $("#residentsName").val("");
                $("#residentsId").val("");
                layer.close(index);
            },
            cancel: function(index) {
                return true;
            }
        });
    }

    /**
     * 提货信息
     */
    function selectDelivery() {
        var customerId = $("#customerId").val();
        //判断是否是多装多卸  只有多装多卸才可以选多地址
        // let val = $('#isMultiple').val();
        // if (fhdz_addr_list.length >= 1 && val !== '1') {
        //     $.modal.alertWarning("无法选择多地址，若想选择多地址，多装多卸请选择：“是”！");
        //     return
        // }

        $.modal.open("提货信息", ctx + "basic/address/selectAddress?customerId="+customerId+"&addrType=1","","",function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            //插入地址
            insertAddrRow(rows[0], 0)

            //批量获取货品单价并计算合计
            // getPriceBatch();

            //获取指导价
            getGuidingPrice();

            //获取线路信息
            getCustLine()

            //单独校验
            // $("#form-invoice-edit").validate().element($("#deliAddrName"));
            layer.close(index);
        });
    }

    /**
     * 收货信息
     */
    function selectReceipt() {
        var customerId = $("#customerId").val();

        // let val = $('#isMultiple').val();
        // if (shdz_addr_list.length >= 1 && val !== '1') {
        //     $.modal.alertWarning("无法选择多地址，若想选择多地址，多装多卸请选择：“是”！");
        //     return
        // }

        $.modal.open("收货信息", ctx + "basic/address/selectAddress?customerId="+customerId+"&addrType=0","",'',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            //添加地址
            insertAddrRow(rows[0], 1)

            //批量获取货品单价并计算合计
            // getPriceBatch();

            distance =''
            if (rows[0].distance) {
                if (shdz_addr_list.length == 1) {
                    distance = rows[0].distance
                }
            }

            addMileage()

            //获取指导价
            getGuidingPrice();

            //获取线路信息
            getCustLine()

            //单独校验
            // $("#form-invoice-edit").validate().element($("#arriAddrName"));
            layer.close(index);
        });
    }

    /**
     * 选择货品名称
     *
     * @param uid   标识符
     * @param type  类型  0发货  1收货
     */
    function selectGoods(uid,type) {
        let t = type === 0 ? 'fhdz_' : 'shdz_';

        var customerId = $("#customerId").val();
        var customerId = $("#customerId").val();
        $.modal.open("选择货品", ctx + "client/goods?goodsIndex=" + goodsIndex + "&type=0&customerId=" + customerId,"","",function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            //货品id
            // $("#goodsId_" + t + uid).val(rows[0]["goodsId"]);
            //货品名称
            $("#goodsName_" + t + uid).val(rows[0]["goodsName"]);
            //货品编码
            // $("#goodsCode_" + t + uid).val(rows[0]["goodsCode"]);
            //货品特性 - 获取单价时使用
            $("#goodsCharacter_" + t + uid).val(rows[0]["goodsCharacter"]);

            //回填货品类型id
            $("#goodsTypeId_" + t + uid).val(rows[0]["goodsType"]);
            //回填货品类型名称
            $("#goodsTypeName_" + t + uid).val(rows[0]["goodsTypeName"]);

            //单独校验
            $("#form-invoice-edit").validate().element($("#goodsName_" + t + uid));

            //获取价格
            // getPrice(uid,type);

            //同步货品
            if (type === 0) {
                syncArriGoods();
            }

            getPrice()

            layer.close(index);
        });
    }

    /**
     * 添加地址
     * @param data          地址数据
     * @param type          类型  0发货  1收货
     */
    function insertAddrRow(data,type){
        //判断是发货还是到货
        // let t = type === 0 ? 'deli_' : 'arri_';
        let t = type === 0 ? 'fhdz_' : 'shdz_';

        //地址序列
        let addr_i = addr_index
        addr_index++;
        //保存地址下货品的序号
        addr_goods_index[addr_i] = []
        //货品唯一id
        var uid = uuid(8, 16);

        if (type === 0) {
            fhdz_addr_list.push(addr_i)
        }else {
            shdz_addr_list.push(addr_i)
        }
        // <div class="toText" style="background-color: #ababab;display:none">改</div>

        let d = type === 0 ? '发货方' : '收货方'
        let html = `
            <div class="fhf mt10" id="` + t + addr_i + `">
                <div class="row">
                    <div class="col-sm-12 layui-form" style="margin-left: 5px;" >
                        <input type="checkbox" data-addrId="${addr_i}"
                               lay-filter="isAddPrice" title="加入合同价获取" checked>
                       <input id="isGetContractPrice_${addr_i}"
                              name="shippingAddressList[${addr_i}].isGetContractPrice"
                              value="1" type="hidden">
                    </div>

                    <div class="col-sm-12 ">
                        <div class="disf">
                            <div class="fh_addr">
                                <a class="btn btn-xs" href="javascript:void(0)" title="修改" onclick="editAddr(this,${type},${addr_i})"><i class="fa fa-edit" style="font-size: 15px;"></i></a>

                                <div class="toText" style="background-color: #ababab;display:none">改</div>
                                <div class="line20 line20T" style="width:100px;">` + data["addrName"] + `</div>
                                <div class="line20 line20T contact-info-clickable" style="width:230px;"
                                     onclick="editContactInfo(` + type + `, this.dataset.index, this.dataset.contact, this.dataset.mobile, '')"
                                     data-index="` + addr_i + `"
                                     data-contact="` + data["contact"] + `"
                                     data-mobile="` + data["mobile"] + `"
                                     title="点击修改联系人信息">联系人信息:` + data["contact"] + `/` + data["mobile"] + `</div>
                `;


                if(data.longitudedegree == null || data.latitudedegree == null){

                    html += `<div onclick="getLongLat('`+data.provinceName+data.cityName+data.areaName+data.detailAddr+`','`+data.areaId+`','`+data.detailAddr+`',`+addr_i+`,'',this)" class="line20 line20T fw" style="width: calc(100% - 408px)"  data-container="body" data-placement="top" data-html="true" ><i  id="longLatIcon_`+addr_i+`" class="fa fa-map-marker" style="color: #ff1f1f;font-size: 16px;margin-right: 5px;"></i>地址:` + data["provinceName"] + data["cityName"] + data["areaName"] + data["detailAddr"] + `(<span id="longLat_`+addr_i+`">无经纬度</span>)</div>`;
                    html += `<input name="shippingAddressList[` + addr_i + `].longitudedegree"  id="longitudedegree_` + addr_i + `" type="hidden">
                        <input name="shippingAddressList[` + addr_i + `].latitudedegree"  id="latitudedegree_` + addr_i + `" type="hidden">`;
                }else{

                    html += `<div onclick="getLongLat('`+data.provinceName+data.cityName+data.areaName+data.detailAddr+`','`+data.areaId+`','`+data.detailAddr+`',`+addr_i+`,'',this)" class="line20 line20T fw" style="width: calc(100% - 408px)"  data-container="body" data-placement="top" data-html="true" ><i id="longLatIcon_`+addr_i+`" class="fa fa-map-marker" style="color: #009AFE;font-size: 16px;margin-right: 5px;"></i>地址:` + data["provinceName"] + data["cityName"] + data["areaName"] + data["detailAddr"] + `(<span id="longLat_`+addr_i+`">`+data["longitudedegree"]+`,`+data["latitudedegree"]+`</span>)</div>`;
                    html += `<input name="shippingAddressList[` + addr_i + `].longitudedegree" value="` + data["longitudedegree"] + `" id="longitudedegree_` + addr_i + `" type="hidden">
                        <input name="shippingAddressList[` + addr_i + `].latitudedegree" value="` + data["latitudedegree"] + `" id="latitudedegree_` + addr_i + `" type="hidden">`;
                }

                if (type === 1) {
                    html += `
                        <div data-index="` + addr_i + `" class="`+(type === 0 ? `toTextT`: `toTextT selectDeliveryT`+addr_i+``)+`" >
                            <i class="glyphicon glyphicon-pencil" style="color:#1ab394;" onclick="selectDeliveryT(this,`+type+`,` + addr_i + `,\`` + data["custAddressId"] +`\`)"></i>
                        </div>
                    `;

                }
                html += `
                                <input name="shippingAddressList[` + addr_i + `].contact" value="` + data["contact"] + `" id="contact_` + addr_i + `" type="hidden">
                                <input name="shippingAddressList[` + addr_i + `].mobile"  value="` + data["mobile"] + `" id="mobile_` + addr_i + `" type="hidden">
<!--                                <input name="shippingAddressList[` + addr_i + `].deliveryId" value="` + data["addressId"] + `" id="deliveryId_` + addr_i + `" type="hidden">-->
                                <input name="shippingAddressList[` + addr_i + `].provinceId" value="` + data["provinceId"] + `" id="provinceId_` + addr_i + `" type="hidden">
                                <input name="shippingAddressList[` + addr_i + `].cityId" value="` + data["cityId"] + `" id="cityId_` + addr_i + `" type="hidden">
                                <input name="shippingAddressList[` + addr_i + `].areaId" value="` + data["areaId"] + `" id="areaId_` + addr_i + `" type="hidden">
                                <input name="shippingAddressList[` + addr_i + `].provinceName" value="` + data["provinceName"] + `" id="provinceName_` + addr_i + `" type="hidden">
                                <input name="shippingAddressList[` + addr_i + `].cityName" value="` + data["cityName"] + `" id="cityName_` + addr_i + `" type="hidden">
                                <input name="shippingAddressList[` + addr_i + `].areaName" value="` + data["areaName"] + `" id="areaName_` + addr_i + `" type="hidden">
<!--                                <input name="shippingAddressList[` + addr_i + `].addrCode" value="` + data["addrCode"] + `" id="addrCode_` + addr_i + `" type="hidden">-->
                                <input name="shippingAddressList[` + addr_i + `].addrName" value="` + data["addrName"] + `" id="addrName_` + addr_i + `" type="hidden">
                                <input name="shippingAddressList[` + addr_i + `].detailAddr" value="` + data["detailAddr"] + `" id="detailAddr_` + addr_i + `" type="hidden">
                                <input name="shippingAddressList[` + addr_i + `].addressType" value="` + type + `" id="addressType_` + addr_i + `" type="hidden" >
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12 mt10" style="display:none">

                    </div>
                    <div class="col-sm-12">
                        <div class="fhf_table">
                            <div class="fixed-table-body" style="margin: 0px -5px;">
                                <table class="custom-tab tab table table-bordered">
                                    <thead>
                                    <tr>
                                        <th style="width: 15%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertGoodsRow(` + addr_i + `,` + type + `)" title="新增行">+</a></th>
                                        <th style="width: 25%;">客户单号</th>
                                        <th style="width: 15%;">货品名称</th>
                                        <th style="width: 15%;">包装</th>
                                        <th style="width: 30%;">件数/重量(吨)/体积(m³)</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="fhf_close">
                    <div class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeAddrRow(` + addr_i + `,` + type + `)"></div>
                </div>
            </div>`;

        if (type === 0) {
            $('#fhdz').append(html);
        }else {
            $('#shdz').append(html);
        }

        layui.use(['form', 'layer'],
            function() {
                var form = layui.form;
                form.render('checkbox');
            });

        //添加校验
        var i = t + uid
        addNumWeightVolumeValidate(i);

        //添加默认货品
        insertGoodsRow(addr_i,type);

        toggleTrans()

        changeisGetContractPrice()

        getPrice()

    }

    /**
     * 回填公里数
     */
    var distance = ''
    function addMileage() {
        let billingMethod = $("#billingMethod").val();
        if (billingMethod == '7' || billingMethod == '8' || billingMethod == '9') {
            $("#mileage").val(distance)
        }
    }

    function getMuleage() {

        if (shdz_addr_list.length == 1) {
            var param = {};
            param.areaId = $("#areaId_"+shdz_addr_list[0]).val()

            param.addrName = $("#addrName_" + shdz_addr_list[0]).val();

            param.detailAddr = $("#detailAddr_" + shdz_addr_list[0]).val();

            param.customerId = $("#customerId").val();

            $.ajax({
                url: ctx + "basic/address/getAddressDistance",
                type: "post",
                dataType: "json",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(param),
                success: function (result) {
                    console.log(result);
                    if (result.code == 0) {
                        distance = result.msg

                        addMileage()
                    }
                }
            });
        }
    }


    function changeisGetContractPrice() {

        if (fhdz_addr_list.length == 1 ) {
            let ind = fhdz_addr_list[0]
            $(`#isGetContractPrice_${ind}`).prev('div').css('display', 'none');
        }else {
            for (let i = 0; i < fhdz_addr_list.length; i++) {
                $(`#isGetContractPrice_${i}`).prev('div').css('display', 'block');
            }

        }

        if (shdz_addr_list.length == 1) {
            let ind = shdz_addr_list[0]
            $(`#isGetContractPrice_${ind}`).prev('div').css('display', 'none');
        }else {
            for (let i = 0; i < shdz_addr_list.length; i++) {
                let ind = shdz_addr_list[i];
                $(`#isGetContractPrice_${ind}`).prev('div').css('display', 'block');
            }
        }
    }

    function editAddr(element,type, index) {
        var customerId = $("#customerId").val();

        var addrType = type == '1' ? 0 : 1

        $.modal.open("修改地址", ctx + "basic/address/selectAddress?customerId="+customerId+"&addrType="+addrType,"","",function (i, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            let data = rows[0]
            let addr_i = index

            var $parentDiv = $(element).closest('.fh_addr');

            let html = ``

            if ($parentDiv.hasClass('fh_edit')) {
                html = `
                <a class="btn btn-xs" href="javascript:void(0)" title="修改" onclick="editAddr(this,${type},${addr_i})"><i class="fa fa-edit" style="font-size: 15px;"></i></a>

                <div class="toText" style="background-color: #ababab;display:inline-block">改</div>
                <div class="line20 line20T" style="width:100px;">${data["addrName"]}</div>
                <div class="line20 line20T contact-info-clickable" style="width:230px;"
                     onclick="editContactInfo(${type}, this.dataset.index, this.dataset.contact, this.dataset.mobile, 'ca')"
                     data-index="${addr_i}"
                     data-contact="${data["contact"]}"
                     data-mobile="${data["mobile"]}"
                     title="点击修改联系人信息">联系人信息:${data["contact"]}/${data["mobile"]}</div>
            `;
                if(data.longitudedegree == null || data.latitudedegree == null){
                    html += `<div onclick="getLongLat('`+data.provinceName+data.cityName+data.areaName+data.detailAddr+`','`+data.areaId+`','`+data.detailAddr+`',`+addr_i+`,'',this)" class="line20 line20T fw" style="width: calc(100% - 408px)"  data-container="body" data-placement="top" data-html="true" ><i  id="calongLatIcon_`+addr_i+`" class="fa fa-map-marker" style="color: #ff1f1f;font-size: 16px;margin-right: 5px;"></i>地址:` + data["provinceName"] + data["cityName"] + data["areaName"] + data["detailAddr"] + `(<span id="calongLat_`+addr_i+`">无经纬度</span>)</div>`;
                    html += `<input name="shippingAddressList[` + addr_i + `].caLongitudedegree"  id="caLongitudedegree_` + addr_i + `" type="hidden">
                        <input name="shippingAddressList[` + addr_i + `].caLatitudedegree"  id="caLatitudedegree_` + addr_i + `" type="hidden">`;
                }else{
                    html += `<div onclick="getLongLat('`+data.provinceName+data.cityName+data.areaName+data.detailAddr+`','`+data.areaId+`','`+data.detailAddr+`',`+addr_i+`,'',this)" class="line20 line20T fw" style="width: calc(100% - 408px)"  data-container="body" data-placement="top" data-html="true" ><i id="calongLatIcon_`+addr_i+`" class="fa fa-map-marker" style="color: #009AFE;font-size: 16px;margin-right: 5px;"></i>地址:` + data["provinceName"] + data["cityName"] + data["areaName"] + data["detailAddr"] + `(<span id="calongLat_`+addr_i+`">`+data["longitudedegree"]+`,`+data["latitudedegree"]+`</span>)</div>`;
                    html += `<input name="shippingAddressList[` + addr_i + `].caLongitudedegree" value="` + data["longitudedegree"] + `" id="caLongitudedegree_` + addr_i + `" type="hidden">
                        <input name="shippingAddressList[` + addr_i + `].caLatitudedegree" value="` + data["latitudedegree"] + `" id="caLatitudedegree_` + addr_i + `" type="hidden">`;
                }



                html += `
                <input name="shippingAddressList[` + addr_i + `].isChangeAddress" value="1" id="isChangeAddress` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].caArriContact" value="` + data["contact"] + `" id="caArriContact` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].caArriMobile"  value="` + data["mobile"] + `" id="caArriMobile` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].caArriProvinceId" value="` + data["provinceId"] + `" id="caArriProvinceId` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].caArriCityId" value="` + data["cityId"] + `" id="caArriCityId` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].caArriAreaId" value="` + data["areaId"] + `" id="caArriAreaId` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].caArriProName" value="` + data["provinceName"] + `" id="caArriProName` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].caArriCityName" value="` + data["cityName"] + `" id="caArriCityName` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].caArriAreaName" value="` + data["areaName"] + `" id="caArriAreaName` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].caArriAddrName" value="` + data["addrName"] + `" id="caArriAddrName` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].caArriDetailAddr" value="` + data["detailAddr"] + `" id="caArriDetailAddr` + addr_i + `" type="hidden">
            `;

            } else {
                html = `
                <a class="btn btn-xs" href="javascript:void(0)" title="修改" onclick="editAddr(this,${type},${addr_i})"><i class="fa fa-edit" style="font-size: 15px;"></i></a>

                <div class="toText" style="background-color: #ababab;display:none">改</div>
                <div class="line20 line20T" style="width:100px;">${data["addrName"]}</div>
                <div class="line20 line20T contact-info-clickable" style="width:230px;"
                     onclick="editContactInfo(${type}, this.dataset.index, this.dataset.contact, this.dataset.mobile, '')"
                     data-index="${addr_i}"
                     data-contact="${data["contact"]}"
                     data-mobile="${data["mobile"]}"
                     title="点击修改联系人信息">联系人信息:${data["contact"]}/${data["mobile"]}</div>
            `;
                if(data.longitudedegree == null || data.latitudedegree == null){
                    html += `<div onclick="getLongLat('`+data.provinceName+data.cityName+data.areaName+data.detailAddr+`','`+data.areaId+`','`+data.detailAddr+`',`+addr_i+`,'',this)" class="line20 line20T fw" style="width: calc(100% - 408px)"  data-container="body" data-placement="top" data-html="true" ><i  id="longLatIcon_`+addr_i+`" class="fa fa-map-marker" style="color: #ff1f1f;font-size: 16px;margin-right: 5px;"></i>地址:` + data["provinceName"] + data["cityName"] + data["areaName"] + data["detailAddr"] + `(<span id="longLat_`+addr_i+`">无经纬度</span>)</div>`;
                    html += `<input name="shippingAddressList[` + addr_i + `].longitudedegree"  id="longitudedegree_` + addr_i + `" type="hidden">
                        <input name="shippingAddressList[` + addr_i + `].latitudedegree"  id="latitudedegree_` + addr_i + `" type="hidden">`;
                }else{
                    html += `<div onclick="getLongLat('`+data.provinceName+data.cityName+data.areaName+data.detailAddr+`','`+data.areaId+`','`+data.detailAddr+`',`+addr_i+`,'',this)" class="line20 line20T fw" style="width: calc(100% - 408px)"  data-container="body" data-placement="top" data-html="true" ><i id="longLatIcon_`+addr_i+`" class="fa fa-map-marker" style="color: #009AFE;font-size: 16px;margin-right: 5px;"></i>地址:` + data["provinceName"] + data["cityName"] + data["areaName"] + data["detailAddr"] + `(<span id="longLat_`+addr_i+`">`+data["longitudedegree"]+`,`+data["latitudedegree"]+`</span>)</div>`;
                    html += `<input name="shippingAddressList[` + addr_i + `].longitudedegree" value="` + data["longitudedegree"] + `" id="longitudedegree_` + addr_i + `" type="hidden">
                        <input name="shippingAddressList[` + addr_i + `].latitudedegree" value="` + data["latitudedegree"] + `" id="latitudedegree_` + addr_i + `" type="hidden">`;
                }

                var $parentDiv = $(element).closest('.fh_addr');

                if (type === 1) {
                    html += `
                    <div data-index="` + addr_i + `" class="`+(type === 0 ? `toTextT`: `toTextT selectDeliveryT`+addr_i+``)+`" >
                        <i class="glyphicon glyphicon-pencil" style="color:#1ab394;" onclick="selectDeliveryT(this,`+type+`,` + addr_i + `,\`` + data["custAddressId"] +`\`)"></i>
                    </div>
                `;
                }
                html += `
                <input name="shippingAddressList[` + addr_i + `].contact" value="` + data["contact"] + `" id="contact_` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].mobile"  value="` + data["mobile"] + `" id="mobile_` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].provinceId" value="` + data["provinceId"] + `" id="provinceId_` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].cityId" value="` + data["cityId"] + `" id="cityId_` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].areaId" value="` + data["areaId"] + `" id="areaId_` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].provinceName" value="` + data["provinceName"] + `" id="provinceName_` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].cityName" value="` + data["cityName"] + `" id="cityName_` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].areaName" value="` + data["areaName"] + `" id="areaName_` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].addrName" value="` + data["addrName"] + `" id="addrName_` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].detailAddr" value="` + data["detailAddr"] + `" id="detailAddr_` + addr_i + `" type="hidden">
                <input name="shippingAddressList[` + addr_i + `].addressType" value="` + type + `" id="addressType_` + addr_i + `" type="hidden" >
            `;
            }


            var $addrDiv = $(element).parent();
            // 清空父级 div 的内容
            $addrDiv.empty();

            $addrDiv.append(html);

            // //插入地址
            // insertAddrRowT(rows[0], 1,data,indexT)

            // //批量获取货品单价并计算合计
            // getPriceBatch();


            //获取线路信息
            getCustLine()

            toggleTrans()

            changeisGetContractPrice()

            getPrice()

            layer.close(i);
        });
    }


     /**
     * 修改地址信息
     */
     function selectDeliveryT(data,name,indexT,custAddressId) {
        var customerId = $("#customerId").val();
        let nameText= name==0?"提货信息":"收货信息"
        let num= name==0?1:0

        $.modal.open(nameText, ctx + "basic/address/selectAddress?customerId="+customerId+"&addrType="+num,"","",function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            if(custAddressId&&rows[0].custAddressId==custAddressId){
                $.modal.alertWarning("请选择不同的地址");
                return;
            }
            $(data).parents(".fh_addr").find(".toTextT").css("display","none")
            $(data).parents(".fh_addr").find(".toText").css('display', 'inline-block')
            $(data).parents(".fh_addr").addClass("fh_edit")

            let htex=` <input name="shippingAddressList[`+ indexT+`].isChangeAddress" value="1" id="isChangeAddress`+ indexT+`" type="hidden">
            <input name="shippingAddressList[`+ indexT+`].caArriProvinceId" value="` + $("#provinceId_"+indexT).val() + `" id="caArriProvinceId`+ indexT+`" type="hidden">
            <input name="shippingAddressList[`+ indexT+`].caArriCityId"  value="` + $("#cityId_"+indexT).val() + `" id="caArriCityId`+ indexT+`" type="hidden">
            <input name="shippingAddressList[`+ indexT+`].caArriAreaId" value="` + $("#areaId_"+indexT).val() + `" id="caArriAreaId`+ indexT+`" type="hidden">
            <input name="shippingAddressList[`+ indexT+`].caArriProName" value="` + $("#provinceName_"+indexT).val() + `" id="caArriProName`+ indexT+`" type="hidden">
            <input name="shippingAddressList[`+ indexT+`].caArriCityName"  value="` + $("#cityName_"+indexT).val() + `" id="caArriCityName`+ indexT+`" type="hidden">
            <input name="shippingAddressList[`+ indexT+`].caArriAreaName" value="` + $("#areaName_"+indexT).val() + `" id="caArriAreaName`+ indexT+`" type="hidden">
            <input name="shippingAddressList[`+ indexT+`].caArriAddrName" value="` + $("#addrName_"+indexT).val()  + `" id="caArriAddrName`+ indexT+`" type="hidden">
            <input name="shippingAddressList[`+ indexT+`].caArriDetailAddr" value="` + $("#detailAddr_"+indexT).val()  + `" id="caArriDetailAddr`+ indexT+`" type="hidden">
            <input name="shippingAddressList[`+ indexT+`].caArriContact" value="` + $("#contact_"+indexT).val()  + `" id="caArriContact`+ indexT+`" type="hidden">
            <input name="shippingAddressList[`+ indexT+`].caArriMobile" value="` + $("#mobile_"+indexT).val()  + `" id="caArriMobile`+ indexT+`" type="hidden">


                `

            if($("#longitudedegree_"+indexT).val()){
                htex += `<input name="shippingAddressList[`+ indexT+`].caLongitudedegree" value="` + $("#longitudedegree_"+indexT).val()  + `" id="caLongitudedegree`+ indexT+`" type="hidden">`
            }else{
                htex += `<input name="shippingAddressList[`+ indexT+`].caLongitudedegree"  id="caLongitudedegree`+ indexT+`" type="hidden">`
            }

            if($("#latitudedegree_"+indexT).val()){
                htex += `<input name="shippingAddressList[`+ indexT+`].caLatitudedegree" value="` + $("#latitudedegree_"+indexT).val()  + `" id="caLatitudedegree`+ indexT+`" type="hidden">`;
            }else{
                htex += `<input name="shippingAddressList[`+ indexT+`].caLatitudedegree"  id="caLatitudedegree`+ indexT+`" type="hidden">`;
            }

            $(data).parents(".fh_addr").find("input").remove();

            $(data).parents(".col-sm-12").find(".fh_addr").append(htex);

            // 获取上级 class 为 fh_addr 的 div
            var $parentDiv = $(data).closest('.fh_addr');

            // 找到 id 以 longLatIcon_ 开头的元素并修改 id
            $parentDiv.find('[id^="longLatIcon_"]').each(function() {
                var originalId = $(this).attr('id');
                $(this).attr('id', 'ca' + originalId);
            });

            // 找到 id 以 longLat_ 开头的元素并修改 id
            $parentDiv.find('[id^="longLat_"]').each(function() {
                var originalId = $(this).attr('id');
                $(this).attr('id', 'ca' + originalId);
            });

            // 更新联系人信息的editContactInfo调用，将prefix改为'ca'
            $parentDiv.find('.contact-info-clickable').each(function() {
                var $this = $(this);
                var onclick = $this.attr('onclick');
                if (onclick && !onclick.includes("'ca'")) {
                    // 将普通地址的调用改为改后地址的调用
                    var newOnclick = onclick.replace(
                        /editContactInfo\(([^,]+),([^,]+),([^,]+),([^,]+),\s*''\s*\)/,
                        "editContactInfo($1,$2,$3,$4, 'ca')"
                    );
                    $this.attr('onclick', newOnclick);
                }
            });

            // //插入地址
            insertAddrRowT(rows[0], 1,data,indexT)


            // //批量获取货品单价并计算合计
            // getPriceBatch();

            // //获取指导价
            // getGuidingPrice();
            //获取线路信息
            getCustLine()

            layer.close(index);
        });
    }
     /**
     * 修改地址
     * @param data          地址数据
     * @param type          类型  0发货  1收货
     */
     function insertAddrRowT(data,type,dataT,index){
        //判断是发货还是到货
        let t = type === 0 ? 'fhdz_' : 'shdz_';

        //地址序列
        let addr_i = index

        let html = `<div class="disf">
                        <div class="fh_addr">

                            <div class="toText" style="background-color: #1ab394;">新</div>

                            <div class="line20 line20T" style="width:100px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`+data["addrName"]+`">` + data["addrName"] + `</div>
                            <div class="line20 line20T contact-info-clickable" style="width:250px;"
                                 onclick="editContactInfo(` + type + `, this.dataset.index, this.dataset.contact, this.dataset.mobile, '')"
                                 data-index="` + addr_i + `"
                                 data-contact="` + data["contact"] + `"
                                 data-mobile="` + data["mobile"] + `"
                                 title="点击修改联系人信息">联系人信息:` + data["contact"] + `/` + data["mobile"] + `</div>`;



                         if(data.longitudedegree == null || data.latitudedegree == null){
                             html += `<div onclick="getLongLat('`+data.provinceName+data.cityName+data.areaName+data.detailAddr+`','`+data.areaId+`','`+data.detailAddr+`',`+addr_i+`,'',this)" class="line20 line20T fw" style="width: calc(100% - 408px)"  data-container="body" data-placement="top" data-html="true" ><i id="longLatIcon_`+addr_i+`" class="fa fa-map-marker" style="color: #ff1f1f;font-size: 16px;margin-right: 5px;"></i>地址:` + data["provinceName"] + data["cityName"] + data["areaName"] + data["detailAddr"] + `(<span id="longLat_`+addr_i+`">无经纬度</span>)</div>`;
                             html += `<input name="shippingAddressList[` + addr_i + `].longitudedegree"  id="longitudedegree_` + addr_i + `" type="hidden">
                                                            <input name="shippingAddressList[` + addr_i + `].latitudedegree"  id="latitudedegree_` + addr_i + `" type="hidden">`;
                         }else{
                             html += `<div onclick="getLongLat('`+data.provinceName+data.cityName+data.areaName+data.detailAddr+`','`+data.areaId+`','`+data.detailAddr+`',`+addr_i+`,'',this)" class="line20 line20T fw" style="width: calc(100% - 408px)"  data-container="body" data-placement="top" data-html="true" ><i id="longLatIcon_`+addr_i+`" class="fa fa-map-marker" style="color: #009AFE;font-size: 16px;margin-right: 5px;"></i>地址:` + data["provinceName"] + data["cityName"] + data["areaName"] + data["detailAddr"] + `(<span id="longLat_`+addr_i+`">`+data["longitudedegree"]+`,`+data["latitudedegree"]+`</span>)</div>`;
                             html += `<input name="shippingAddressList[` + addr_i + `].longitudedegree" value="` + data["longitudedegree"] + `" id="longitudedegree_` + addr_i + `" type="hidden">
                                                            <input name="shippingAddressList[` + addr_i + `].latitudedegree" value="` + data["latitudedegree"] + `" id="latitudedegree_` + addr_i + `" type="hidden">`;
                         }

                        html += `
                            <div class="toTextT"><i class="glyphicon glyphicon-trash" style="color:#ec4758;" onclick="onTrash(` + addr_i + `)"></i></div>

                            <input name="shippingAddressList[` + addr_i + `].contact" value="` + data["contact"] + `" id="contact_` + addr_i + `" type="hidden">
                            <input name="shippingAddressList[` + addr_i + `].mobile"  value="` + data["mobile"] + `" id="mobile_` + addr_i + `" type="hidden">
                            <input name="shippingAddressList[` + addr_i + `].provinceId" value="` + data["provinceId"] + `" id="provinceId_` + addr_i + `" type="hidden">
                            <input name="shippingAddressList[` + addr_i + `].cityId" value="` + data["cityId"] + `" id="cityId_` + addr_i + `" type="hidden">
                            <input name="shippingAddressList[` + addr_i + `].areaId" value="` + data["areaId"] + `" id="areaId_` + addr_i + `" type="hidden">
                            <input name="shippingAddressList[` + addr_i + `].provinceName" value="` + data["provinceName"] + `" id="provinceName_` + addr_i + `" type="hidden">
                            <input name="shippingAddressList[` + addr_i + `].cityName" value="` + data["cityName"] + `" id="cityName_` + addr_i + `" type="hidden">
                            <input name="shippingAddressList[` + addr_i + `].areaName" value="` + data["areaName"] + `" id="areaName_` + addr_i + `" type="hidden">
                            <input name="shippingAddressList[` + addr_i + `].addrName" value="` + data["addrName"] + `" id="addrName_` + addr_i + `" type="hidden">
                            <input name="shippingAddressList[` + addr_i + `].detailAddr" value="` + data["detailAddr"] + `" id="detailAddr_` + addr_i + `" type="hidden">
                            <input name="shippingAddressList[` + addr_i + `].addressType" value="` + type + `" id="addressType_` + addr_i + `" type="hidden" >
                        </div>
                    </div>`;
        $(dataT).parents(".col-sm-12").next("div").append(html);
        $(dataT).parents(".col-sm-12").next("div").css('display', 'block')

        $('[data-toggle="tooltip"]').tooltip()
    }

    /**
     * 删除提货信息
     */
     function onTrash(addr_i){
        if($("#caArriContact"+addr_i).val()){

            let htex=`


                    <input name="shippingAddressList[` + addr_i + `].contact" value="` + $("#caArriContact"+addr_i).val() + `" id="contact_` + addr_i + `" type="hidden">
                    <input name="shippingAddressList[` + addr_i + `].mobile"  value="` + $("#caArriMobile"+addr_i).val() + `" id="mobile_` + addr_i + `" type="hidden">
                    <input name="shippingAddressList[` + addr_i + `].provinceId" value="` +$("#caArriProvinceId"+addr_i).val()  + `" id="provinceId_` + addr_i + `" type="hidden">
                    <input name="shippingAddressList[` + addr_i + `].cityId" value="` + $("#caArriCityId"+addr_i).val()  + `" id="cityId_` + addr_i + `" type="hidden">
                    <input name="shippingAddressList[` + addr_i + `].areaId" value="` + $("#caArriAreaId"+addr_i).val()  + `" id="areaId_` + addr_i + `" type="hidden">
                    <input name="shippingAddressList[` + addr_i + `].provinceName" value="` + $("#caArriProName"+addr_i).val()  + `" id="provinceName_` + addr_i + `" type="hidden">
                    <input name="shippingAddressList[` + addr_i + `].cityName" value="` + $("#caArriCityName"+addr_i).val()  + `" id="cityName_` + addr_i + `" type="hidden">
                    <input name="shippingAddressList[` + addr_i + `].areaName" value="` + $("#caArriAreaName"+addr_i).val()  + `" id="areaName_` + addr_i + `" type="hidden">
                    <input name="shippingAddressList[` + addr_i + `].addrName" value="` + $("#caArriAddrName"+addr_i).val()  + `" id="addrName_` + addr_i + `" type="hidden">
                    <input name="shippingAddressList[` + addr_i + `].detailAddr" value="` + $("#caArriDetailAddr"+addr_i).val()  + `" id="detailAddr_` + addr_i + `" type="hidden">
                    <input name="shippingAddressList[` + addr_i + `].addressType" value="1" id="addressType_` + addr_i + `" type="hidden" >`

            if($("#caLongitudedegree"+addr_i).val()){
                htex += `<input name="shippingAddressList[` + addr_i + `].longitudedegree"  value="` + $("#caLongitudedegree"+addr_i).val() + `" id="longitudedegree_` + addr_i + `" type="hidden">`
            }else{
                htex += `<input name="shippingAddressList[` + addr_i + `].longitudedegree"   id="longitudedegree_` + addr_i + `" type="hidden">`
            }
            if($("#caLatitudedegree"+addr_i).val()){
                htex += `<input name="shippingAddressList[` + addr_i + `].latitudedegree" value="` + $("#caLatitudedegree"+addr_i).val() + `" id="latitudedegree_` + addr_i + `" type="hidden">`
            }else{
                 htex += `<input name="shippingAddressList[` + addr_i + `].latitudedegree"  id="latitudedegree_` + addr_i + `" type="hidden">`
            }
            $(".selectDeliveryT"+addr_i).eq(0).parents(".col-sm-12").find("input").remove();
            $(".selectDeliveryT"+addr_i).eq(0).parents(".col-sm-12").find(".fh_addr").append(htex);
        }


        $(".selectDeliveryT"+addr_i).parents(".col-sm-12").next().prev().find(".toTextT").css("display","inline-block");
        $(".selectDeliveryT"+addr_i).parents(".col-sm-12").next().prev().find(".toText").css('display', 'none')
        $(".selectDeliveryT"+addr_i).parents(".col-sm-12").next().prev().find('.fh_addr').attr("class","fh_addr")
        $(".selectDeliveryT"+addr_i).parents(".col-sm-12").next().css('display', 'none')
        $(".selectDeliveryT"+addr_i).parents(".col-sm-12").next().html("")
    }

    /**
     * 添加货品
     * @param addr_i            地址下标
     * @param type              类型  0发货  1收货
     * @param isDisabled        是否只读  false不是  true是
     * @param sourceUid         复制值 源数据的uid
     */
    function insertGoodsRow(addr_i, type, isDisabled, sourceUid) {
        //货品唯一id
        let uid = uuid(8, 16);

        let t = type === 0 ? 'fhdz_' : 'shdz_'

        //记录每个地址的货品下标信息
        let goodsLen = addr_goods_index[addr_i].length
        let goods_i = goodsLen === 0 ? 0 : addr_goods_index[addr_i][goodsLen - 1].id + 1
        addr_goods_index[addr_i].push({id: goods_i, value: uid})

        // let isDis = isDisabled ? "disabled" : "";
        let isDis = ""

        var trTtml =
            ` <tr id="` + t + uid + `">
                <td>
                    <div style="display: inline-block;">
                        <a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"
                        onclick="removeGoodsRow('` + uid + `',` + addr_i + `,` + type + `)" title="删除选择行"></a>
                    </div>
                    <div style="display: inline-block;margin-left:5px">
                        <a class="fa fa-clone" style="color: #0092e7;font-size: 17px;"
                           onclick="insertGoodsRow(` + addr_i + `,` + type + `,null,'`+ uid +`')"
                           title="复制货品"></a>
                    </div>
                </td>
                <td>
                    <input name="shippingAddressList[` + addr_i + `].shippingGoodsList[` + goods_i + `].custOrderno" id="custOrderno_` + t + uid + `" placeholder="请输入客户发货单号" class="form-control" type="text"
                           maxlength="50" autocomplete="off" oninput="syncArriGoodsForType(` + type + `)">
                </td>

                <td>
                    <input name="shippingAddressList[` + addr_i + `].shippingGoodsList[` + goods_i + `].goodsName" id="goodsName_` + t + uid + `"
                            onclick="selectGoods('` + uid + `',` + type + `)" type="text" placeholder="" class="form-control valid" autocomplete="off" required readonly ` + isDis + `/>
<!--                    <input name="shippingAddressList[` + addr_i + `].shippingGoodsList[` + goods_i + `].goodsId" id="goodsId_` + t + uid + `" type="hidden"/>-->
<!--                    <input name="shippingAddressList[` + addr_i + `].shippingGoodsList[` + goods_i + `].goodsCode" id="goodsCode_` + t + uid + `" type="hidden"/>-->
                    <input name="shippingAddressList[` + addr_i + `].shippingGoodsList[` + goods_i + `].goodsCharacter" id="goodsCharacter_` + t + uid + `" type="hidden"/>
                    <input name="shippingAddressList[` + addr_i + `].shippingGoodsList[` + goods_i + `].goodsTypeName" id="goodsTypeName_` + t + uid + `" type="hidden" class="form-control"/>
                    <input name="shippingAddressList[` + addr_i + `].shippingGoodsList[` + goods_i + `].goodsType" id="goodsTypeId_` + t + uid + `" type="hidden"/>
                </td>
                <td>
                    <div class="col-sm-12">
                          ` + getPackIdHtml(addr_i, goods_i, uid, type, isDisabled) + `
                    </div>
                </td>
                <td>
                    <div class="col-sm-4" style="padding-left:2px;padding-right: 2px">
                        <input name="shippingAddressList[` + addr_i + `].shippingGoodsList[` + goods_i + `].num" id="num_` + t + uid + `" placeholder="" class="form-control"
                            type="text" oninput="$.numberUtil.onlyNumber(this);getPriceByGoods(` + type + `);checkNumWeightVolume('` + uid + `',` + type + `);syncArriGoodsForType(` + type + `);calculateSum()" style="text-align: right" autocomplete="off" ` + isDis + `>
                    </div>
                    <div class="col-sm-4" style="padding-left:2px;padding-right: 2px">
                        <input name="shippingAddressList[` + addr_i + `].shippingGoodsList[` + goods_i + `].weight" id="weight_` + t + uid + `" placeholder="" class="form-control"
                            type="text" oninput="$.numberUtil.onlyNumber(this);getPriceByGoods(` + type + `);checkNumWeightVolume('` + uid + `',` + type + `);syncArriGoodsForType(` + type + `);calculateSum()"  style="text-align: right" autocomplete="off" ` + isDis + `>
                    </div>
                    <div class="col-sm-4" style="padding-left:2px;padding-right: 2px">
                        <input name="shippingAddressList[` + addr_i + `].shippingGoodsList[` + goods_i + `].volume" id="volume_` + t + uid + `" placeholder="" class="form-control"
                            type="text" oninput="$.numberUtil.onlyNumber(this);getPriceByGoods(` + type + `);checkNumWeightVolume('` + uid + `',` + type + `);syncArriGoodsForType(` + type + `);calculateSum()" style="text-align: right" autocomplete="off" ` + isDis + `>
                    </div>
                </td>

            </tr>
           `;
        // <td>
        //     <div className="input-group">
        //         ` + getBillingMethodHtml(addr_i, goods_i, uid, type, isDisabled) + `
        //     </div>
        // </td>
        // <td>
        //     <input name="shippingAddressList[` + addr_i + `].shippingGoodsList[` + goods_i + `].pc"
        //            id="pc_` + t + uid + `"
        //            onInput="$.numberUtil.onlyNumberTwoDecimal(this);calculateSum('` + uid + `',` + type + `);syncArriGoodsForType(`+type+`)"
        //            style="text-align: right"
        //            placeholder="" className="form-control" autoComplete="off" type="text">
        // </td>
        // <td>
        //     <div className="input-group">
        //         <input name="shippingAddressList[` + addr_i + `].shippingGoodsList[` + goods_i + `].sum"
        //                id="sum_` + t + uid + `"
        //                placeholder="" className="form-control" type="text" style="text-align: right"
        //                onInput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotals(` + type + `);syncArriGoodsForType(`+type+`)"
        //                autoComplete="off" required>
        //     </div>
        // </td>
        $('#' + t + addr_i).find('.table tbody').append(trTtml);

        //添加校验
        let i = t + uid;
        addNumWeightVolumeValidate(i);

        if (sourceUid) {
            cloneGoodsRow(uid, sourceUid, type);
        }
        if (type === 0) {
            syncArriGoods();
        }
    }


    /**
     * 拼接 货品明细下-计价方式下拉框
     *
     * @param addrIndex     地址下标
     * @param goodsIndex    货品下标
     * @param uid           货品唯一标识
     * @param type          类型  0发货  1收货
     * @param isDisabled    是否只读  false不是  true是
     * @returns {string}
     */
    // var billingMethods = [[${billingMethods}]];
    // function getBillingMethodHtml(addrIndex,goodsIndex,uid,type,isDisabled) {
    //     let t = type === 0 ? 'fhdz_' : 'shdz_';
    //     // let isDis = isDisabled ? "disabled" : "";
    //     let isDis = ""
    //     var str = '<select onchange="judgePriceType(\''+uid+'\','+type+')" name="shippingAddressList['+ addrIndex +'].shippingGoodsList['+goodsIndex+'].billingMethod" ' +
    //         'id="billingMethod_'+t+ uid +'" class="form-control valid" aria-invalid="false" required '+isDis+'>';
    //     str = str + '<option value=""></option>';
    //     for (var i=0;i < billingMethods.length;i++) {
    //         str = str + '<option value="'+ billingMethods[i].value +'">'+ billingMethods[i].context +'</option>'
    //     }
    //     return str + '</select>';
    // }

    /**
     * 拼接 货品明细下-包装下拉框
     * @param addrIndex     地址下标
     * @param goodsIndex    货品下标
     * @param uid           货品唯一标识
     * @param type          类型  0发货  1收货
     * @param isDisabled    是否只读  false不是  true是
     * @returns {string}
     */
    var packageTypeList = [[${@dict.getType('package_type')}]];
    function getPackIdHtml(addrIndex,goodsIndex,uid,type,isDisabled) {
        let t = type === 0 ? 'fhdz_' : 'shdz_';
        // let isDis = isDisabled ? "disabled" : "";
        let isDis = ""

        var str = '<select name="shippingAddressList['+addrIndex+'].shippingGoodsList['+goodsIndex+'].packId" onchange="syncArriGoodsForType('+type+')"' +
            'id="packId_'+t+uid+'" class="form-control valid" aria-invalid="false" required '+isDis+'>';
        var str = str + '<option value=""></option>';
        for (var i=0;i < packageTypeList.length;i++) {
            str = str + '<option value="'+ packageTypeList[i].dictValue +'">'+ packageTypeList[i].dictLabel +'</option>'
        }
        return str + '</select>';
    }

    /**
     * 根据type 判断是否需要同步货品
     * @param type          类型  0发货  1收货
     */
    function syncArriGoodsForType(type) {
        if (type === 0) {
            syncArriGoods()
        }
    }
    /**
     * 同步货品
     */
    function syncArriGoods() {
        let val = $('#isMultiple').val();
        if (val !== '1' && shdz_addr_list.length > 0) {
            //发货地址下标 发货后货品信息
            let fhdz_addr_i = fhdz_addr_list[0]
            let fhGoodsList = addr_goods_index[fhdz_addr_i]

            //收货
            let shdz_addr_i = shdz_addr_list[0]
            let shGoodsList = addr_goods_index[shdz_addr_i]

            //清空货品
            for (const shGoods of shGoodsList) {
                removeGoodsRow(shGoods.value, shdz_addr_i, 1)
            }

            //重新添加货品
            for (const fhGoods of fhGoodsList) {
                insertGoodsRow(shdz_addr_i, 1)
            }

            //赋值
            let shGoodsList_new = addr_goods_index[shdz_addr_i]
            for (let i = 0; i < fhGoodsList.length; i++) {
                let fh = fhGoodsList[i]
                let sh = shGoodsList_new[i]
                $("#goodsName_shdz_" + sh.value).val($("#goodsName_fhdz_" + fh.value).val())
                // $("#goodsId_shdz_" + sh.value).val($("#goodsId_fhdz_" + fh.value).val())
                // $("#goodsCode_shdz_" + sh.value).val($("#goodsCode_fhdz_" + fh.value).val())
                $("#goodsCharacter_shdz_" + sh.value).val($("#goodsCharacter_fhdz_" + fh.value).val())
                $("#goodsTypeName_shdz_" + sh.value).val($("#goodsTypeName_fhdz_" + fh.value).val())
                $("#goodsTypeId_shdz_" + sh.value).val($("#goodsTypeId_fhdz_" + fh.value).val())

                $("#packId_shdz_" + sh.value).val($("#packId_fhdz_" + fh.value).val())

                $("#custOrderno_shdz_" + sh.value).val($("#custOrderno_fhdz_" + fh.value).val())

                $("#num_shdz_" + sh.value).val($("#num_fhdz_" + fh.value).val())
                $("#weight_shdz_" + sh.value).val($("#weight_fhdz_" + fh.value).val())
                $("#volume_shdz_" + sh.value).val($("#volume_fhdz_" + fh.value).val())

                // $("#billingMethod_shdz_" + sh.value).val($("#billingMethod_fhdz_" + fh.value).val())
                // $("#pc_shdz_" + sh.value).val($("#pc_fhdz_" + fh.value).val())
                // $("#sum_shdz_" + sh.value).val($("#sum_fhdz_" + fh.value).val())

                calculateTotals (1)
            }
        }
    }

    /**
     *  删除指定表格行
     */
    // function removeRow(obj,goodsIndex) {
    //     //如果货品条数大于1，则删除
    //     if ($("#infoTab tbody").find('tr').length > 1) {
    //         $("#infoTab tbody").find(obj).closest("tr").remove();
    //         //重新计算合计
    //         calculateTotals();
    //     } else {
    //         emptyRow(goodsIndex);
    //     }
    // }

    /**
     * 删除地址
     * @param addr_i    地址下标
     * @param type      类型  0发货  1收货
     */
    function removeAddrRow(addr_i,type){
        let id = type === 0 ? 'fhdz_' : 'shdz_'

        $('#' + id + addr_i).remove();

        if (type === 0) {
            let index = fhdz_addr_list.indexOf(addr_i);
            if (index > -1) {
                fhdz_addr_list.splice(index, 1);
            }
        }else {
            let index = shdz_addr_list.indexOf(addr_i);
            if (index > -1) {
                shdz_addr_list.splice(index, 1);
            }
        }

        //删除对应的货品信息
        delete addr_goods_index[addr_i]

        calculateTotals(type)

        getPriceByGoods(type)

        toggleTrans()

        changeisGetContractPrice()

        getPrice()

        getCustLine()
    }

    /**
     * 删除货品
     * @param obj       货品的唯一标识
     * @param addr_i    地址下标
     * @param type      类型  0发货  1收货
     */
    function removeGoodsRow(uid,addr_i,type){
        let t = type === 0 ? 'fhdz_' : 'shdz_'
        // if ($('#' + t + addr_i).find('.table tbody').find('tr').length > 1) {
        $('#' + t + uid).remove();

        //移除 addr_goods_index[addr_i]  货品的下标
        addr_goods_index[addr_i] = addr_goods_index[addr_i].filter(x => x.value !== uid)
        // }
        // calculateTotals(type)

        getPriceByGoods(type)
        if (type === 0) {
            syncArriGoods();
        }
    }

    /**
     * 复制货品
     * @param targetUid       目标行uid
     * @param sourceUid       源数据行uid
     * @param type            类型  0发货  1收货
     */
    function cloneGoodsRow(targetUid, sourceUid, type) {
        let t = type === 0 ? 'fhdz_' : 'shdz_'

        $("#goodsName_" + t + targetUid).val($("#goodsName_" + t + sourceUid).val())
        $("#goodsCharacter_" + t + targetUid).val($("#goodsCharacter_" + t + sourceUid).val())
        $("#goodsTypeName_" + t + targetUid).val($("#goodsTypeName_" + t + sourceUid).val())
        $("#goodsTypeId_" + t + targetUid).val($("#goodsTypeId_" + t + sourceUid).val())

        $("#packId_" + t + targetUid).val($("#packId_" + t + sourceUid).val())

        $("#custOrderno_" + t + targetUid).val($("#custOrderno_" + t + sourceUid).val())

        $("#num_" + t + targetUid).val($("#num_" + t + sourceUid).val())
        $("#weight_" + t + targetUid).val($("#weight_" + t + sourceUid).val())
        $("#volume_" + t + targetUid).val($("#volume_" + t + sourceUid).val())

        // calculateTotals (type)
        getPriceByGoods(type)
    }

    /**
     * 清空行内容
     */
    // function emptyRow(goodsIndex) {
    //     //清空行值
    //     $("#goodsTypeName_" + goodsIndex).val("");
    //     $("#goodsTypeId_" + goodsIndex).val("");
    //     $("#goodsName_" + goodsIndex).val("");
    //     $("#goodsId_" + goodsIndex).val("");
    //     $("#goodsCode_" + goodsIndex).val("");
    //     $("#goodsCharacter_" + goodsIndex).val("");
    //     $("#num_" + goodsIndex).val("");
    //     $("#weight_" + goodsIndex).val("");
    //     $("#volume_" + goodsIndex).val("");
    //     $("#billingMethod_" + goodsIndex).val(billingMethods[0].value);
    //     $("#pc_" + goodsIndex).val("");
    //     $("#sum_" + goodsIndex).val("");
    //     $("#packId_" + goodsIndex).val(packageTypeList[0].dictValue);
    //
    //     //调整价格类型
    //     changeDisabled(goodsIndex);
    //     //重新计算合计
    //     calculateTotals();
    // }


    /**
     * 根据 计价方式 判断 价格类型：
     *              1、一日往返包车、包车（车型）类型为固定价
     *              2、其余为单价
     */
    // function changeDisabled(goodsIndex) {
    //     //计价方式为
    //     var billingMethod = $("#billingMethod_" + goodsIndex).val();
    //
    //     //计价方式为 4：一日往返包车 与 计价方式为 3：包车（车型）时，类型为固定价，其余为单价
    //     if (billingMethod == 4 || billingMethod == 3) {
    //         $("#priceType_" + goodsIndex).html("固定价");
    //     } else {
    //         $("#priceType_" + goodsIndex).html("单价");
    //     }
    // }


    function getCustLine() {

        let customerId = $("#customerId").val();

        if (customerId === '' || customerId === undefined || customerId === null) {
            return
        }

        if (fhdz_addr_list.length !== 1 || shdz_addr_list.length !== 1  ) {
            return
        }
        let deliInx = fhdz_addr_list[0];
        let arriInx = shdz_addr_list[0];

        let deliAreaId = $("#areaId_" + deliInx).val();
        let arriAreaId = $("#areaId_" + arriInx).val();

        let transCode = $("#transCode").val();
        if (transCode === '' || transCode === undefined || transCode === null) {
            return
        }


        $.ajax({
            url: ctx + `tms/custLine/getLine?customerId=${customerId}&startArea=${deliAreaId}&endArea=${arriAreaId}&transType=${transCode}`,
            type: "get",
            dataType: "json",
            contentType: "application/json; charset=utf-8",
            // data: JSON.stringify(param),
            success: function(result) {
                // $("#reqArriDate").val("")

                if (result.code === 0) {
                    let data = result.data;
                    if (result.data !== undefined) {
                        let timeLimit = result.data.timeLimit;
                        if (timeLimit !== undefined && timeLimit !== null && timeLimit !== '') {


                            let reqDeliDate = $("#reqDeliDate").val();
                            var reqDeliDateHour = $("#reqDeliDateHour").val();

                            if (reqDeliDate !== '' && reqDeliDateHour !== '') {

                                reqDeliDate = reqDeliDate + " " + reqDeliDateHour + ":00:00";

                                let dateObj = new Date(reqDeliDate);

                                // 将timeLimit从字符串转换为数值
                                timeLimit = parseFloat(timeLimit);

                                // 计算新的日期时间戳(毫秒)
                                let newDateTimeStamp = dateObj.getTime() + timeLimit * 60 * 60 * 1000;

                                // 从时间戳创建新的Date对象
                                let newDate = new Date(newDateTimeStamp);

                                console.log(newDate);

                                // 格式化新日期为字符串(可根据需要调整格式)
                                // 格式化新日期为字符串
                                let year = newDate.getFullYear();
                                let month = String(newDate.getMonth() + 1).padStart(2, '0');
                                let day = String(newDate.getDate()).padStart(2, '0');
                                let hours = String(newDate.getHours()).padStart(2, '0');
                                let minutes = String(newDate.getMinutes()).padStart(2, '0');
                                let seconds = String(newDate.getSeconds()).padStart(2, '0');

                                let newDateStr = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

                                $("#reqArriDate").val(newDateStr)
                            }
                        }
                    }
                }
            }
        });
    }


    //是否是合同价  1是
    var hasContract = 1
    /**
     * 获取合同价
     *
     */
    function getPrice() {
        //重新计算合计
        calculateTotals(0);
        calculateTotals(1);

        let ifBargain = $("#ifBargain").val();
        if (ifBargain == 1) {
            calculateSum()

            getRfqSuggest()

            $("#costPrice").text("")

            return;
        }

        //运输方式
        let val = $('#isMultiple').val();
        //不为多装多卸时  获取单价
        // if (val !== '1') {
            var param = {};

            // let t = type === 0 ? 'fhdz_' : 'shdz_'

            let t = 'fhdz_';
            let deliInx = fhdz_addr_list[0]
            let arriInx = shdz_addr_list[0]
            //客户id
            param.customerId = $("#customerId").val();

            //提货方 省id
            // param.deliProvinceId = $("#provinceId_" + deliInx).val();
            // //提货方 市id
            // param.deliCityId = $("#cityId_" + deliInx).val();
            //
            // //收货方 省id
            // param.arriProvinceId = $("#provinceId_" + arriInx).val();
            // //收货方 市id
            // param.arriCityId = $("#cityId_" + arriInx).val();
            //
            // param.deliAreaId = $("#areaId_" + deliInx).val();
            // param.arriAreaId = $("#areaId_" + arriInx).val();

            let deliAreaIdList = [];
            let arriAreaIdList = [];
            let deliAddrNameList = [];
            let arriAddrNameList = [];

            for (const deliInx of fhdz_addr_list) {
                let isGetContractPrice = $("#isGetContractPrice_" + deliInx).val();

                if (isGetContractPrice == '1') {
                    deliAreaIdList.push($("#areaId_" + deliInx).val());
                }

            }
            for (const deliInx of fhdz_addr_list) {
                let isGetContractPrice = $("#isGetContractPrice_" + deliInx).val();

                if (isGetContractPrice == '1') {
                    deliAddrNameList.push($("#addrName_" + deliInx).val());
                }

            }


            for(const deliInx of shdz_addr_list){
                if($("#isChangeAddress"+deliInx).val() ==1){
                    let caArriAreaId = $("#caArriAreaId"+deliInx).val();
                    arriAreaIdList.push(caArriAreaId);
                }else{
                    let isGetContractPrice = $("#isGetContractPrice_" + deliInx).val();
                    if (isGetContractPrice == '1') {
                        arriAreaIdList.push($("#areaId_" + deliInx).val());
                    }
                }
            }

            for(const deliInx of shdz_addr_list){
                if($("#isChangeAddress"+deliInx).val() ==1){
                    let caArriAddrName = $("#caArriAddrName"+deliInx).val();
                    arriAddrNameList.push(caArriAddrName);
                }else{
                    let isGetContractPrice = $("#isGetContractPrice_" + deliInx).val();
                    if (isGetContractPrice == '1') {
                        arriAddrNameList.push($("#addrName_" + deliInx).val());
                    }
                }
            }

           /* let isChangeAddress = $("#isChangeAddress").val();
            if (isChangeAddress == 1) {
                let caArriAreaId = $("#caArriAreaId").val();
                arriAreaIdList.push(caArriAreaId)
            }else {
                for (const deliInx of shdz_addr_list) {
                    let isGetContractPrice = $("#isGetContractPrice_" + deliInx).val();

                    if (isGetContractPrice == '1') {
                        arriAreaIdList.push($("#areaId_" + deliInx).val());
                    }
                }
            }*/

            param.deliAreaIdList = deliAreaIdList
            param.arriAreaIdList = arriAreaIdList
            param.deliAddrNameList = deliAddrNameList
            param.arriAddrNameList = arriAddrNameList


            //车长
            param.carLen = $("#carLen").val();
            param.carType = $("#carType").val();

            //计价方式
            param.billingMethod = $("#billingMethod").val();

            let transCode = $("#transCode").val();
            //货品特性
            // param.goodsCharacter = $("#goodsCharacter_" + t + uid).val();
            param.goodsCharacter = transCode == '15' || transCode == '16' ? 1 : 0;

            var goodsList = [];
            //获取所有以 "goodsName_fhdz_" 开头的 input 元素
            $('input[id^="goodsName_fhdz_"]').each(function() {
                // 获取每个匹配元素的值并添加到数组中
                var value = $(this).val();
                if (!goodsList.includes(value)) {
                    goodsList.push(value);
                }
            });

            if (goodsList.length === 1) {
                param.goodsName = goodsList[0];
            }

            //件数
            param.num = $("#fhdz_numCount").val();
            //重量
            param.weight = $("#fhdz_weightCount").val();
            //件数
            param.volume = $("#fhdz_volumeCount").val();

            //公里数
            param.mileage =  $("#mileage").val() == "" ? 0 : $("#mileage").val();

            //是否大件
            param.isOversize = $('input[name="isOversize"]').val()
            //运输方式
            param.transCode = transCode;

            //是否往返
            param.isRoundTrip = $("#isRoundTrip").val();

        // param.goodsId = $("#goodsId_" + t + uid).val();

            if (param.billingMethod === undefined|| param.billingMethod ==null || param.billingMethod =='') {
                return;
            }


            $.ajax({
                url: ctx + "invoice/getPrice",
                type: "post",
                dataType: "json",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(param),
                success: function(result) {
                    console.log(result);
                    if (result.code == 0) {
                        if (result.data.type == 0) {
                            $("#unitPrice").css("width", "75%")

                            $("#unitPriceFa").show()
                            $("#unitPriceFa").attr("data-original-title",result.data.msg)

                            //成本价
                            $("#totalCostPrice").val("")
                            $("#costBillingType").val("");
                            $('#costBillingTypeLabel').text("");

                            // $('#totalCostPrice').prop('readonly', false).css('background-color', '');

                            $("#costPrice").text("")

                            hasContract = 0

                            //单价
                            $("#unitPrice").val("");
                            //金额sum
                            $("#costAmount").val("");

                            $("#isRoundSpan").hide()

                            $("#costAmountIncludeTax").val("");
                            $("#unitPriceIncludeTax").val("");

                            $("#deliveryFeeHtml").hide();
                            $("#deliveryFeeSpan").html("");
                            $("#deliveryFee").val("")

                        }else if (result.data.type == 1) {
                            $("#unitPrice").css("width", "100%")
                            $("#unitPriceFa").hide()


                            //成本价
                            $("#totalCostPrice").val(result.data.totalCostPrice)
                            $("#costPrice").text("成本单价：" + (result.data.costPrice === "" ? "暂无" : result.data.costPrice))
                            $("#costBillingType").val(result.data.costBillingType);
                            $('#costBillingTypeLabel')
                                .text(result.data.costBillingType == null || result.data.costBillingType === ''
                                    ? '' : dictBillingType.find(itm => itm.dictValue == result.data.costBillingType)?.dictLabel);

                            // if (result.data.totalCostPrice && result.data.totalCostPrice != 0) {
                            //     $('#totalCostPrice').prop('readonly', true).css('background-color', '#f0f0f0');
                            // }else {
                            //     $('#totalCostPrice').prop('readonly', false).css('background-color', '');
                            // }

                            //送货费
                            if (result.data.deliveryFee != null) {
                                $("#deliveryFeeHtml").show();
                                $("#deliveryFeeSpan").html("￥" + result.data.deliveryFee);
                                $("#deliveryFee").val(result.data.deliveryFee)
                            }

                            hasContract = 1;

                            if (result.data.isIncludeTax === "1") {
                                //单价
                                $("#unitPrice").val(result.data.price);
                                //金额sum
                                $("#costAmount").val(result.data.totalPrice);

                                calculateNoIncludeTax()
                            }else if (result.data.isIncludeTax === "0") {
                                let price = result.data.price
                                let totalPrice = result.data.totalPrice


                                let billingType = $("#billingType").val();
                                let rate = $("#billingType").find(':selected').data('rate');

                                let costAmount;
                                let unitPrice;

                                if (billingType != "") {
                                    costAmount = totalPrice * rate
                                    unitPrice = price * rate
                                }else {
                                    costAmount = totalPrice
                                    unitPrice = price
                                }

                                // 四舍五入保留两位小数
                                costAmount = parseFloat(costAmount).toFixed(2);
                                unitPrice = parseFloat(unitPrice).toFixed(2);

                                //单价
                                $("#unitPrice").val(unitPrice);
                                //金额sum
                                $("#costAmount").val(costAmount);

                                $("#costAmountIncludeTax").val(totalPrice);
                                $("#unitPriceIncludeTax").val(price);
                            }

                            if (result.data.totalPriceScale === '1') {
                                $("#isRoundSpan").show();
                            }else {
                                $("#isRoundSpan").hide()
                            }


                        }

                        calculateCostAmountText()

                        changePriceDisabled()
                    }
                }
            });
        // }
    }


    var rfqUnitPrice = null;
    function getRfqSuggest() {
        let carLen = $("#carLen").val();
        let transCode = $("#transCode").val();
        let type = transCode == '0' || transCode == '15' || transCode == '4' ? 1 : 2;
        var isOversize = $("#isOversize").is(':checked') ? 1 : 0;
        var carType = $("#carType").val();
        /*var cb_sjdk_kp = null;*/
        var isRoundTrip = $('#isRoundTrip').val()

        let ifBargain = $("#ifBargain").val();
        rfqUnitPrice = null;
        if (rfqLineId != null && ifBargain == 1) {
            $("#totalCostPrice").val("")
            rfqUnitPrice = null;
            $.ajax({
                url: ctx + `rfq/get_suggest?lineId=${rfqLineId}&carLen=${carLen}&carType=${carType}&type=${type}&transCode=${transCode}&isOversize=${isOversize}&roundTrip=${isRoundTrip}&cartypeId=${rfqCartypeId||''}`,
                type: "get",
                beforeSend: function () {
                },
                success: function (data) {
                    if (data.code == 0) {
                        if (data.data) {
                            if (typeof data.data === 'number') {
                                $("#totalCostPrice").val(data.data)
                                //rfqTotalCostPrice = data.data
                                // $('#totalCostPrice').prop('readonly', true).css('background-color', '#f0f0f0');
                            } else if (Array.isArray(data.data)) {
                                var quoteDtls = data.data;
                                if (quoteDtls.length == 1) {
                                    var hasUnitPrice = !!quoteDtls[0].unitPrice;
                                    var unitPrice = quoteDtls[0].unitPrice||quoteDtls[0].suggest; // 没有单价时取原包车成本价字段
                                    /*if (quoteDtls[0].billingType != null && quoteDtls[0].billingType != '6' && quoteDtls[0].billingType != 'G7') {
                                        // 有票点时，扣除税金抵扣开票
                                        unitPrice = unitPrice * (1000 - cb_sjdk_kp * 1000) / 1000;
                                    }*/
                                    if (hasUnitPrice) { // 单价时，由calculateSum()填totalCostPrice
                                        rfqUnitPrice = unitPrice; //成本单价
                                        $('#unitPrice').val(quoteDtls[0].quoteUnitPrice)
                                    } else {
                                        $("#totalCostPrice").val(unitPrice) // 设置成本价,调整到calculateSum()
                                        $('#unitPrice').val(quoteDtls[0].quotePrice)
                                    }
                                    let billingType22 = quoteDtls[0].billingType == null ? '6' : quoteDtls[0].billingType;
                                    $('#costBillingType').val(billingType22);
                                    $('#costBillingTypeLabel').text(dictBillingType.find(itm => itm.dictValue == billingType22)?.dictLabel);
                                    calculateSum()
                                } else {
                                    //$.modal.msgWarning("多条报价明细，未能带出单价和成本价")
                                }
                            }
                        } else {
                            // $('#totalCostPrice').prop('readonly', false).css('background-color', '');
                        }
                    }
                }
            })
        } else {
            $("#totalCostPrice").val("");
            $("#costBillingType").val("");
            $('#costBillingTypeLabel').text("");
            $('#totalCostPrice').prop('readonly', false).css('background-color', '');
        }


    }

    function changeIsOversize() {
        var isChecked = $("#isOversize").is(':checked');
        $('input[name="isOversize"]').val(isChecked ? '1' : '0');

        if (isChecked) {
            $("#goodsSizeDiv").show()
        }else {
            $("#goodsSizeDiv").hide()

        }

        getPrice()
        getGuidingPrice()
    }


    function changeIsCustomsClearance() {
        var isCustomsClearance = $("#isCustomsClearance").is(':checked');
        $('input[name="isCustomsClearance"]').val(isCustomsClearance ? '1' : '0');

        getGuidingPrice()
    }


    /**
     * 获取合同价
     * @param type  类型  0发货  1收货
     */
    function getPriceByGoods(type) {
        //重新计算合计
        calculateTotals(0);
        calculateTotals(1);

        let ifBargain = $("#ifBargain").val();
        if (ifBargain == 1) {

            calculateSum()

            getRfqSuggest()

            $("#costPrice").text("")

            return;
        }

        //运输方式
        let val = $('#isMultiple').val()

        let billingMethod = $("#billingMethod").val();
        if (billingMethod == 3 || billingMethod == 7) {
            return;
        }

        if (type == 1) {
            return;
        }

        //不为多装多卸时  获取单价
        // if (val !== '1') {
            var param = {};

            let t = 'fhdz_';
            let deliInx = fhdz_addr_list[0];
            let arriInx = shdz_addr_list[0];
            //客户id
            param.customerId = $("#customerId").val();

            //提货方 省id
            // param.deliProvinceId = $("#provinceId_" + deliInx).val();
            // //提货方 市id
            // param.deliCityId = $("#cityId_" + deliInx).val();
            //
            // //收货方 省id
            // param.arriProvinceId = $("#provinceId_" + arriInx).val();
            // //收货方 市id
            // param.arriCityId = $("#cityId_" + arriInx).val();
            //
            // param.deliAreaId = $("#areaId_" + deliInx).val();
            // param.arriAreaId = $("#areaId_" + arriInx).val();

            let deliAreaIdList = [];
            let arriAreaIdList = [];
            let deliAddrNameList = [];
            let arriAddrNameList = [];

            for (const deliInx of fhdz_addr_list) {
                let isGetContractPrice = $("#isGetContractPrice_" + deliInx).val();
                if (isGetContractPrice == '1') {
                    deliAreaIdList.push($("#areaId_" + deliInx).val());
                }
            }
            for (const deliInx of fhdz_addr_list) {
                let isGetContractPrice = $("#isGetContractPrice_" + deliInx).val();
                if (isGetContractPrice == '1') {
                    deliAddrNameList.push($("#addrName_" + deliInx).val());
                }
            }

            for(const deliInx of shdz_addr_list){
                console.log($("#isChangeAddress"+deliInx).val())
                if($("#isChangeAddress"+deliInx).val() ==1){

                    console.log('改送地址')
                    let caArriAreaId = $("#caArriAreaId"+deliInx).val();
                    arriAreaIdList.push(caArriAreaId);
                }else{
                    let isGetContractPrice = $("#isGetContractPrice_" + deliInx).val();
                    if (isGetContractPrice == '1') {
                        arriAreaIdList.push($("#areaId_" + deliInx).val());
                    }
                }
            }

            for(const deliInx of shdz_addr_list){
                if($("#isChangeAddress"+deliInx).val() ==1){
                    let caArriAddrName = $("#caArriAddrName"+deliInx).val();
                    arriAddrNameList.push(caArriAddrName);
                }else{
                    let isGetContractPrice = $("#isGetContractPrice_" + deliInx).val();
                    if (isGetContractPrice == '1') {
                        arriAddrNameList.push($("#addrName_" + deliInx).val());
                    }
                }
            }

        /* let isChangeAddress = $("#isChangeAddress").val();
         if (isChangeAddress == 1) {
             let caArriAreaId = $("#caArriAreaId").val();
             arriAreaIdList.push(caArriAreaId)
         }else {
             for (const deliInx of shdz_addr_list) {
                 let isGetContractPrice = $("#isGetContractPrice_" + deliInx).val();
                 if (isGetContractPrice == '1') {
                     arriAreaIdList.push($("#areaId_" + deliInx).val());
                 }
             }
         }*/

            param.deliAreaIdList = deliAreaIdList
            param.arriAreaIdList = arriAreaIdList
            param.deliAddrNameList = deliAddrNameList
            param.arriAddrNameList = arriAddrNameList

            //车长
            param.carLen = $("#carLen").val();
            param.carType = $("#carType").val();

            //计价方式
            param.billingMethod = $("#billingMethod").val();

            let transCode = $("#transCode").val();
            //货品特性   0普通 1危化
            // param.goodsCharacter = $("#goodsCharacter_" + t + uid).val();
            param.goodsCharacter = transCode == '15' || transCode == '16' ? 1 : 0;

            var goodsList = [];
            //获取所有以 "goodsName_fhdz_" 开头的 input 元素
            $('input[id^="goodsName_fhdz_"]').each(function() {
                // 获取每个匹配元素的值并添加到数组中
                var value = $(this).val();
                if (!goodsList.includes(value)) {
                    goodsList.push(value);
                }
            });

            if (goodsList.length === 1) {
                param.goodsName = goodsList[0];
            }



        //件数
            param.num = $("#fhdz_numCount").val();
            //重量
            param.weight = $("#fhdz_weightCount").val();
            //件数
            param.volume = $("#fhdz_volumeCount").val();

            //公里数
            param.mileage =  $("#mileage").val() == "" ? 0 : $("#mileage").val();

            //是否大件
            param.isOversize = $('input[name="isOversize"]').val()

            //运输方式
            param.transCode = transCode;

            //是否往返
            param.isRoundTrip = $("#isRoundTrip").val();

            if (param.billingMethod === undefined || param.billingMethod ==null || param.billingMethod =='') {
                return;
            }


            $.ajax({
                url: ctx + "invoice/getPrice",
                type: "post",
                dataType: "json",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(param),
                success: function(result) {
                    console.log(result);
                    if (result.code == 0) {
                        if (result.data.type == 0) {
                            $("#unitPrice").css("width", "75%")

                            $("#unitPriceFa").show()
                            $("#unitPriceFa").attr("data-original-title",result.data.msg)


                            //成本价
                            $("#totalCostPrice").val("");
                            $("#costBillingType").val("");
                            $('#costBillingTypeLabel').text("");

                            // $('#totalCostPrice').prop('readonly', false).css('background-color', '');

                            $("#costPrice").text("")


                            hasContract = 0

                            //单价
                            $("#unitPrice").val("");
                            //金额sum
                            $("#costAmount").val("");

                            $("#isRoundSpan").hide()

                            $("#costAmountIncludeTax").val("");
                            $("#unitPriceIncludeTax").val("");

                            $("#deliveryFeeHtml").hide();
                            $("#deliveryFeeSpan").html("");
                            $("#deliveryFee").val("")

                        }else if (result.data.type == 1) {
                            $("#unitPrice").css("width", "100%")
                            $("#unitPriceFa").hide()


                            //成本价
                            $("#totalCostPrice").val(result.data.totalCostPrice)
                            $("#costBillingType").val(result.data.costBillingType);
                            $('#costBillingTypeLabel')
                                .text(result.data.costBillingType == null || result.data.costBillingType === ''
                                    ? '' : dictBillingType.find(itm => itm.dictValue == result.data.costBillingType)?.dictLabel);

                            // if (result.data.totalCostPrice && result.data.totalCostPrice != 0) {
                            //     $('#totalCostPrice').prop('readonly', true).css('background-color', '#f0f0f0');
                            // }else {
                            //     $('#totalCostPrice').prop('readonly', false).css('background-color', '');
                            // }

                            $("#costPrice").text("成本单价：" + (result.data.costPrice === "" ? "暂无" : result.data.costPrice))

                            //送货费
                            if (result.data.deliveryFee != null) {
                                $("#deliveryFeeHtml").show();
                                $("#deliveryFeeSpan").html("￥" + result.data.deliveryFee);
                                $("#deliveryFee").val(result.data.deliveryFee)
                            }


                            hasContract = 1

                            if (result.data.isIncludeTax === "1") {
                                //单价
                                $("#unitPrice").val(result.data.price);
                                //金额sum
                                $("#costAmount").val(result.data.totalPrice);

                                calculateNoIncludeTax()
                            }else if (result.data.isIncludeTax === "0") {
                                let price = result.data.price
                                let totalPrice = result.data.totalPrice


                                let billingType = $("#billingType").val();
                                let rate = $("#billingType").find(':selected').data('rate');

                                let costAmount;
                                let unitPrice;

                                if (billingType != "") {
                                    costAmount = totalPrice * rate
                                    unitPrice = price * rate
                                }else {
                                    costAmount = totalPrice
                                    unitPrice = price
                                }

                                // 四舍五入保留两位小数
                                costAmount = parseFloat(costAmount).toFixed(2);
                                unitPrice = parseFloat(unitPrice).toFixed(2);

                                //单价
                                $("#unitPrice").val(unitPrice);
                                //金额sum
                                $("#costAmount").val(costAmount);

                                $("#costAmountIncludeTax").val(totalPrice);
                                $("#unitPriceIncludeTax").val(price);
                            }

                            if (result.data.totalPriceScale === '1') {
                                $("#isRoundSpan").show();
                            }else {
                                $("#isRoundSpan").hide()
                            }

                        }

                        calculateCostAmountText()

                        changePriceDisabled()
                    }
                }
            });
        // }
    }

    /**
     * 修改单价、合同价是否可修改
     */
    function changePriceDisabled() {
        let ifBargain = $("#ifBargain").val();
        if (hasContract == 1 && ifBargain == 0) {
            $("#unitPrice").attr('disabled', "disabled");
            // $("#costAmount").attr('disabled', "disabled");
        }else {
            $("#unitPrice").removeAttr("disabled");
            // $("#costAmount").removeAttr("disabled");
        }
    }


    function changeIfBargain() {
        let ifBargain = $("#ifBargain").val();

        $('#totalCostPrice').prop('readonly', true).css('background-color', '#f0f0f0');

        $("#bargainMemoDiv").hide()
        if (ifBargain == 0) {
            getPrice()
        }else{
            getRfqSuggest()

            $("#costPrice").text("")

            $("#isRoundSpan").hide()

            $("#deliveryFeeHtml").hide();
            $("#deliveryFeeSpan").html("");
            $("#deliveryFee").val("")

            calculateCostAmountText()

            changePriceDisabled()
            $("#bargainMemoDiv").show()
        }

        checkCustomerContract()


    }


    /**
     * 修改计价方式
     */
    function changeBillingMethod() {
        var billingMethod = $("#billingMethod").val()

        if (billingMethod == 7 || billingMethod == 6 || billingMethod == 8 || billingMethod == 9) {
            //单价（元/公里） || 单价（元/吨．公里）
            $("#mileage").parent().parent().show()
        }else {
            $("#mileage").parent().parent().hide()
        }

        handleRoundTripSelect()

        calculateSum();

        addMileage()
    }

    function handleRoundTripSelect() {
        var billingMethod = $("#billingMethod").val()

        if (billingMethod == 4) {
            $("#isRoundTrip")
                .val("1")
                .css('background-color', '#f0f0f0')
                .on('mousedown', function(e) {
                    e.preventDefault();
                    return false;
                })
                .on('keydown', function(e) {
                    e.preventDefault();
                    return false;
                });
        } else {
            $("#isRoundTrip")
                .css('background-color', '')
                .off('mousedown')
                .off('keydown');
        }
    }


    /**
     * 计算金额
     *
     * @param uid   标识符
     * @param type  类型  0发货  1收货
     */
    function calculateSum() {
        let ifBargain = $("#ifBargain").val();
        if (ifBargain == 0) {
            calculateCostAmountText()

            return
        }
        //计价方式为
        var billingMethod = $("#billingMethod").val();
        //单价
        var unitPrice = $("#unitPrice").val() == "" ? 0 : $("#unitPrice").val();
        //公里数
        var mileage = $("#mileage").val() == "" ? 0 : $("#mileage").val();

        //件数
        var num = $("#fhdz_numCount").val() == "" ? 0 : $("#fhdz_numCount").val();
        //重量
        var weight = $("#fhdz_weightCount").val() == "" ? 0 : $("#fhdz_weightCount").val();
        //体积
        var volume = $("#fhdz_volumeCount").val() == "" ? 0 : $("#fhdz_volumeCount").val();


        let costAmount = 0;
        let rfqTotalCostPrice = '';
        //计算重量
        if (billingMethod == 1) {
            // $("#costAmount").val(decimal(parseFloat(parseFloat(unitPrice) * parseFloat(weight)), 2));
            costAmount = decimal(parseFloat(parseFloat(unitPrice) * parseFloat(weight)), 2);
            if (rfqUnitPrice != null) {
                rfqTotalCostPrice = decimal((rfqUnitPrice||0) * 100 * (weight*100000) / 10000000, 2);
            }
        } else if (billingMethod == 2) {
            //计算体积
            // $("#costAmount").val(decimal(parseFloat(parseFloat(unitPrice) * parseFloat(volume)), 2));
            costAmount = decimal(parseFloat(parseFloat(unitPrice) * parseFloat(volume)), 2);
            if (rfqUnitPrice != null) {
                rfqTotalCostPrice = decimal((rfqUnitPrice||0) * 100 * (volume*100000) / 10000000, 2);
            }
        } else if (billingMethod == 5) {
            //计算件数
            // $("#costAmount").val(decimal(parseFloat(parseFloat(unitPrice) * parseFloat(num)), 2));
            costAmount =decimal(parseFloat(parseFloat(unitPrice) * parseFloat(num)), 2);
            if (rfqUnitPrice != null) {
                rfqTotalCostPrice = decimal((rfqUnitPrice||0) * 100 * (num*100000) / 10000000, 2);
            }
        } else if (billingMethod == 6) {
            // $("#costAmount").val(decimal(parseFloat(parseFloat(weight) * parseFloat(mileage) * parseFloat(unitPrice)), 2));
            costAmount = decimal(parseFloat(parseFloat(weight) * parseFloat(mileage) * parseFloat(unitPrice)), 2);
        } else if (billingMethod == 7) {
            // $("#costAmount").val(decimal(parseFloat(parseFloat(mileage) * parseFloat(unitPrice)), 2));
            costAmount = decimal(parseFloat(parseFloat(mileage) * parseFloat(unitPrice)), 2);
        } else if(billingMethod == 8) {
            // $("#costAmount").val(decimal(parseFloat(parseFloat(weight) * parseFloat(mileage) * parseFloat(unitPrice)), 2));
            costAmount = decimal(parseFloat(parseFloat(weight) * parseFloat(mileage) * parseFloat(unitPrice)), 2);
        }else if(billingMethod == 9) {
            // $("#costAmount").val(decimal(parseFloat(parseFloat(num) * parseFloat(mileage) * parseFloat(unitPrice)), 2));
            costAmount = decimal(parseFloat(parseFloat(num) * parseFloat(mileage) * parseFloat(unitPrice)), 2);
        }else {
            //整车固定价  总价等于单价
            // $("#costAmount").val(unitPrice);
            costAmount = unitPrice;
            if (rfqUnitPrice != null) {
                rfqTotalCostPrice = rfqUnitPrice;
            }
        }

        $("#costAmount").val( costAmount);
        if (rfqUnitPrice != null) {
            $("#totalCostPrice").val(rfqTotalCostPrice)
        }

        calculateNoIncludeTax()
    }

    function calculateCostAmountText() {
        //其他费
        let otherFee = $("#otherFee").val() == "" ? 0 : $("#otherFee").val();
        //总运费
        let costAmount = $("#costAmount").val() == "" ? 0 : $("#costAmount").val();
        let costAmountIncludeTax = $("#costAmountIncludeTax").val() == "" ? 0 : $("#costAmountIncludeTax").val();
        //送货费
        let deliveryFee = $("#deliveryFee").val() == "" ? 0 : $("#deliveryFee").val();

        //运费
        $("#costAmount_text").html(costAmount);
        $("#costAmount_tax_text").html(costAmountIncludeTax);
        //其他费
        $("#otherfee_text").html( parseFloat(otherFee) + parseFloat(deliveryFee));
        //总费用
        $("#fhdz_costAmount_text").html((parseFloat(costAmount) + parseFloat(otherFee) + parseFloat(deliveryFee)).toFixed(2));
    }

    /**
     *
     */
/*    function changeCostAmount(){
        //计价方式为
        var billingMethod = $("#billingMethod").val();

        if (billingMethod == 3) {
            //整车固定价  总价等于单价
            $("#unitPrice").val($("#costAmount").val());
        }
        var otherFee = $("#otherFee").val() == "" ? 0 : $("#otherFee").val();
        var costAmount = $("#costAmount").val() == "" ? 0 : $("#costAmount").val();
        $("#fhdz_costAmount_text").html((parseFloat(costAmount) + parseFloat(otherFee)).toFixed(2));

        calculateNoIncludeTax()
    }*/

    /**
     * 计算不含税价
     */
    function calculateNoIncludeTax() {
        let costAmount = $("#costAmount").val();
        let unitPrice = $("#unitPrice").val();

        let billingType = $("#billingType").val();
        let rate = $("#billingType").find(':selected').data('rate');

        let costAmountIncludeTax;
        let unitPriceIncludeTax;
        if (billingType === '4' || billingType === '2') {
            //增值税专用发票（9%） || 增值税普票发票（9%）
            costAmountIncludeTax = costAmount / rate
            unitPriceIncludeTax = unitPrice / rate
        }else if (billingType === '3') {
            //增值税专用发票（6%）
            costAmountIncludeTax = costAmount / rate
            unitPriceIncludeTax = unitPrice / rate
        }else if (billingType === '5') {
            //增值税专用发票（3%）
            costAmountIncludeTax = costAmount / rate
            unitPriceIncludeTax = unitPrice / rate
        }else if (billingType === '7') {
            //增值税专用发票（13%）
            costAmountIncludeTax = costAmount / rate
            unitPriceIncludeTax = unitPrice / rate
        }else {
            costAmountIncludeTax = costAmount;
            unitPriceIncludeTax = unitPrice
        }

        // 四舍五入保留两位小数
        costAmountIncludeTax = parseFloat(costAmountIncludeTax).toFixed(2);
        unitPriceIncludeTax = parseFloat(unitPriceIncludeTax).toFixed(2);

        $("#costAmountIncludeTax").val(costAmountIncludeTax);
        $("#unitPriceIncludeTax").val(unitPriceIncludeTax);


        calculateCostAmountText()
    }



    /**
     * 计算合计
     *
     * @param type  类型  0发货  1收货
     */
    function calculateTotals(type) {
        let t = type === 0 ? 'fhdz_' : 'shdz_';

        //件数
        var numList = $("[id^=num_" + t + "]");
        var numCount = 0;
        numList.each(function(){
            if ($(this).val() != "") {
                numCount = parseFloat(decimal(numCount + parseFloat($(this).val()), 5));
            }

        });
        $("#" + t + "numCount").val(numCount);
        $("#" + t + "numCount_text").html(numCount);

        //重量
        var weightList = $("[id^=weight_" + t + "]");
        var weightCount = 0;
        weightList.each(function(){
            if ($(this).val() != "") {
                weightCount = parseFloat(decimal(weightCount + parseFloat($(this).val()), 5));
            }
        });
        $("#" + t + "weightCount").val(weightCount);
        $("#" + t + "weightCount_text").html(weightCount);

        //体积
        var volumeList = $("[id^=volume_"+ t +"]");
        var volumeCount = 0;
        volumeList.each(function(){
            if ($(this).val() != "") {
                volumeCount = parseFloat(decimal(volumeCount + parseFloat($(this).val()), 5));
            }
        });
        $("#" + t + "volumeCount").val(volumeCount);
        $("#" + t + "volumeCount_text").html(volumeCount);

        //总金额
        // var costAmountList = $("[id^=sum_" + t + "]");
        // var costAmount = 0;
        // costAmountList.each(function(){
        //     if ($(this).val() != "") {
        //         costAmount = parseFloat(decimal(costAmount + parseFloat($(this).val()), 2));
        //     }
        // });
        // $("#" + t + "costAmount").val(costAmount);
        // $("#" + t + "costAmount_text").html(costAmount);
    }

    // /**
    //  * 跳转编辑指导价
    //  */
    // function toEditGuidePrice() {
    //     //先查询线路是否存在，不存在提示，存在跳转编辑指导价页面
    //     var param = {};
    //     //客户id
    //     param.customerId = $("#customerId").val();
    //     if ($("#customerId").val() === ''){
    //         $.modal.msgError("请先选择客户！");
    //         return;
    //     }
    //     //提货方
    //     if ($("#deliProvinceId").val() === ''){
    //         $.modal.msgError("请先选择发货方！");
    //         return;
    //     }
    //     param.deliProvinceId = $("#deliProvinceId").val();
    //     param.deliCityId = $("#deliCityId").val();
    //     param.deliAreaId = $("#deliAreaId").val();
    //
    //     //到货区
    //     if ($("#arriProvinceId").val() === ''){
    //         $.modal.msgError("请先选择收货方！");
    //         return;
    //     }
    //     param.arriProvinceId = $("#arriProvinceId").val();
    //     param.arriCityId = $("#arriCityId").val();
    //     param.arriAreaId = $("#arriAreaId").val();
    //     //根据客户id和收发货地址，获取客户指导价id
    //     $.ajax({
    //         url: ctx + "tms/guide_price/getCustomerGuidePriceId",
    //         type: "get",
    //         dataType: "json",
    //         data: param,
    //         success: function(result) {
    //             if (result.code === 0) {
    //                 if ('' === result.data || undefined === result.data) {
    //                     $.modal.msgError("当前线路无指导价信息！");
    //                 } else {
    //                     //跳转指导价编辑页面
    //                     var url = ctx + "tms/guide_price/edit_price?custGuidePriceId=" + result.data + "&customerId="
    //                         + $("#customerId").val();
    //                     $.modal.openTab("编辑指导价", url);
    //                 }
    //             } else {
    //                 $.modal.msgError("获取指导价失败！");
    //             }
    //         }
    //     });
    // }

    /**
     * 只有多装多卸 才可以选择多地址
     */
    function toggleTrans(){
        /*if ((fhdz_addr_list.length < 2 && shdz_addr_list.length < 2)){
            $(".selectDeliveryT").css('display', 'inline-block')
        }else{
            onTrash($(".selectDeliveryT").data("index"))
            $(".selectDeliveryT").css('display', 'none')
        }*/
        if ((fhdz_addr_list.length > 1 || shdz_addr_list.length > 1)) {
            $('#isMultiple').val('1')
        }else {
            $('#isMultiple').val('0')
        }
        //指导价
        getGuidingPrice()
    }

    //系统带出的 历史最新指导价与状态 用作提交时比较指导价是否修改过
    var guidingPriceOld;
    // var guidingPriceCheckStust;
    var referencePriceOid;

    /**
     * 获取指导价
     */
    /*     function getGuidingPrice(){
           clearCustGuidePrice()

            let isMultiple = $('#isMultiple').val();
            if (isMultiple == '1') {
                $("#guidingPrice").attr("disabled","disabled");
                return;
            }else {
                $("#guidingPrice").removeAttr("disabled")
            }

            // let unloadPlaceNum= $('#unloadPlaceNum').val();
            let isOversize= $('#isOversize').val();

            // if(unloadPlaceNum != 1){
            //     return;
            // }
            if(isOversize == 1){
                return;
            }

            var param = {};
            //客户id
            // param.customerId = $("#customerId").val();
            // if ($("#customerId").val() === ''){
            //     return;
            // }
            //车型
            param.carType = $("#carType").val();
            if ($("#carType").val() === ''){
                return;
            }
            //车长
            param.carLen = $("#carLen").val();
            if ($("#carLen").val() === ''){
                return;
            }
            //运输方式-整车
            // param.transType = $("#transCode").val();
            if ($("#transCode").val() === ''
                || ($("#transCode").val() !== '0' && $("#transCode").val() !== '4' && $("#transCode").val() !== '15')){
                return;
            }
            //提货方 区id
            if (fhdz_addr_list.length > 0) {
                param.deliAreaId = $("#areaId_" + fhdz_addr_list[0]).val();
            }else {
                return;
            }
            // param.deliAreaId = $("#deliAreaId").val();
            // if ($("#deliAreaId").val() === ''){
            //     return;
            // }
            //到货区 区id
            if (shdz_addr_list.length > 0) {
                param.arriAreaId = $("#areaId_" + shdz_addr_list[0]).val();
            }else {
                return;
            }
            // param.arriAreaId = $("#arriAreaId").val();
            // if ($("#arriAreaId").val() === ''){
            //     return;
            // }

            // //判断计价方式是否都相同
            // var firstBillingMethod = -1;
            // $("select[id^='billingMethod']").each(function () {
            //     if(firstBillingMethod == -1){
            //         firstBillingMethod = this.value;
            //     }else if(firstBillingMethod != this.value){
            //         firstBillingMethod = -1;
            //         return false;
            //     }
            // });
            // param.billingMethod = firstBillingMethod;
            //
            // //总件数总重量总体积
            // param.numCount = $("#numCount").val();
            // param.weightCount = $("#weightCount").val();
            // param.volumeCount = $("#volumeCount").val();
            let transCode=$("#transCode").val();
            $.ajax({
                url: ctx + "invoice/get_reference_price",
                type: "post",
                dataType: "json",
                data: param,
                success: function(result) {
                    if (result.code === 0) {
                        if (result.data.length > 0) {
                            if(transCode==0&&result.data[0].priceBasic){
                                $("#guidingPrice").val(result.data[0].priceBasic);
                                guidingPriceOld = result.data[0].priceBasic
                                $("#guidingPrice").attr("disabled","disabled");
                            } else if(transCode==15&&result.data[0].priceDangerousGoods){
                                $("#guidingPrice").val(result.data[0].priceDangerousGoods);
                                guidingPriceOld = result.data[0].priceDangerousGoods
                                $("#guidingPrice").attr("disabled","disabled");
                            }else if(transCode==4&&result.data[0].priceColdChain){
                                $("#guidingPrice").val(result.data[0].priceColdChain);
                                guidingPriceOld = result.data[0].priceColdChain
                                $("#guidingPrice").attr("disabled","disabled");
                            }else{
                                $("#guidingPrice").removeAttr("disabled")
                            }
                            referencePriceOid = result.data[0].referencePriceId;
                            getReferencePriceOid(transCode)
                        }else{
                            referencePriceOid ="";
                            $("#guidingPrice").val('');
                        }
                    }
                }
            })
    }*/

    let hasGuidingPrice = 0
    function getGuidingPrice(){
        hasGuidingPrice = 0

        $("#guidingPriceDiv").text("暂无数据。")
        referencePriceOid =''

        var isOversize = $("#isOversize").is(':checked');

        if(isOversize){
            return;
        }

        var isCustomsClearance = $("#isCustomsClearance").is(':checked');

        if(isCustomsClearance){
            return;
        }

        var param = {};

        //车型
        param.carType = $("#carType").val();
        if ($("#carType").val() === ''){
            return;
        }
        //车长
        param.carLen = $("#carLen").val();
        if ($("#carLen").val() === ''){
            return;
        }
        //运输方式-整车
        if ($("#transCode").val() === ''
            || ($("#transCode").val() !== '0' && $("#transCode").val() !== '4' && $("#transCode").val() !== '15')){
            return;
        }
        //提货方 区id
        if (fhdz_addr_list.length > 0) {
            param.deliAreaId = $("#areaId_" + fhdz_addr_list[0]).val();
        }else {
            return;
        }
        //到货区 区id
        if (shdz_addr_list.length > 0) {
            param.arriAreaId = $("#areaId_" + shdz_addr_list[0]).val();
        }else {
            return;
        }
        // param.arriAreaId = $("#arriAreaId").val();
        // if ($("#arriAreaId").val() === ''){
        //     return;
        // }
        let transCode=$("#transCode").val();
        $.ajax({
            url: ctx + "invoice/get_reference_price",
            type: "post",
            dataType: "json",
            data: param,
            success: function(result) {
                if (result.code === 0) {
                    if (result.data.length > 0) {
                        hasGuidingPrice = 2

                        if(transCode==0&&result.data[0].priceBasic){
                            $("#guidingPriceDiv").text("线路车辆指导价：￥"+result.data[0].priceBasic);

                        } else if(transCode==15&&result.data[0].priceDangerousGoods){
                            $("#guidingPriceDiv").text("线路车辆指导价：￥"+result.data[0].priceDangerousGoods);

                        }else if(transCode==4&&result.data[0].priceColdChain){
                            $("#guidingPriceDiv").text("线路车辆指导价：￥"+result.data[0].priceColdChain);
                        }else{
                            $("#guidingPriceDiv").text('暂无数据');
                            hasGuidingPrice = 1

                        }

                        referencePriceOid = result.data[0].referencePriceId;

                    }else{

                        hasGuidingPrice = 1
                        $("#guidingPriceDiv").text('暂无数据');
                    }
                }
            }
        })
    }




    function getReferencePriceOid(transCode) {
        var param = {};
        param.referencePriceId=referencePriceOid;
        if(transCode==0){
            param.priceType=0
        } else if(transCode==15){
            param.priceType=1
        }else if(transCode==4){
            param.priceType=2
        }

        $.ajax({
            url: ctx + "invoice/has_reference_check_data",
            type: "post",
            dataType: "json",
            data: param,
            success: function(result) {
                if (result.code === 0) {
                    if(result.data){
                        $("#guidingPrice").attr("disabled","disabled");
                    }else{
                        $("#guidingPrice").removeAttr("disabled");
                    }
                    // let checkStatus= '';
                    // if(result.data){
                    //     checkStatus= 1;
                    // }else{
                    //     checkStatus= 0;
                    // }
                    // guidingPriceCheckStust = checkStatus


                    // if (guidingPriceCheckStust == 1 ) {
                    //     $("#guidingPrice").attr("readonly","readonly");

                    //     if (guidingPriceCheckStust == 1) {
                    //         $("#guideTipsTit").text("现有指导价尚未审核，无法新添指导价")
                    //     }

                    //     $("#guideTipsDiv").show()
                    // }else {
                    //     $("#guidingPrice").removeAttr("readonly")
                    //     $("#guideTipsDiv").hide()
                    // }
                   
                }
            }
        })
    }


    /**
     * 清空指导价信息
     */
    function clearCustGuidePrice(){
        $("#guidingPrice").val("");
        guidingPriceOld = ''
        // guidingPriceCheckStust = ''

        $("#guidingPrice").removeAttr("readonly")
        $("#guideTipsDiv").hide()

    }


    /**
     * 获取历史指导价列表
     * @param
     */
    function getHistoryGuidePriceDetailList(){
        let isMultiple = $('#isMultiple').val();
        if (isMultiple == '1') {
            $.modal.alertWarning("多装多卸暂时无法查看历史指导价！")
            return;
        }
        // let unloadPlaceNum= $('#unloadPlaceNum').val();
        var isOversize = $("#isOversize").is(':checked');
        // if(unloadPlaceNum != 1){
        //     $.modal.alertWarning("卸货地数量大于1暂时无法查看历史指导价！")
        //     return;
        // }
        if(isOversize){
            $.modal.alertWarning("大件运输暂时无法查看历史指导价！")
            return;
        }

        var isCustomsClearance = $("#isCustomsClearance").is(':checked');
        if(isCustomsClearance){
            $.modal.alertWarning("报关单据暂时无法查看历史指导价！")
            return;
        }

        var transCode = $("#transCode").val();

        var param = "referencePriceId=" + referencePriceOid;
        if(transCode==0){
            param+="&priceType=0"
        } else if(transCode==15){
            param+="&priceType=1"
        }else if(transCode==4){
            param+="&priceType=2"
        }
       
        $("#historyGuidingPriceParam").val(param);

        layer.open({
            type: 2,
            area: ['80%', '90%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '历史指导价',
            content: ctx + "invoice/history_reference_price",
            btn: ['关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            cancel: function(index) {
                return true;
            }
        });
    }


    /**
     * 日期插件
     */
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        var reqDeliDate = laydate.render({
            elem: '#reqDeliDate', //指定元素
            //   format : 'yyyy-MM-dd',
            //max: $("#reqArriDate").val(),
            isInitValue : false,
            trigger: 'click',
            type: 'date',
            min: -1,
            ready: function (date) {
                // var now = new Date();
                // this.dateTime.hours=now.getHours();
                // this.dateTime.minutes=now.getMinutes();
                // this.dateTime.seconds=now.getSeconds();
            },
            done: function(value, date, endDate){
                reqArriDate.config.min = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date,
                    hours: date.hours,
                    minutes: date.minutes,
                    seconds: date.seconds
                };
                $("#reqDeliDate").val(value);
                $("#reqDeliDate").trigger("change");
                //单独校验日期
                $("#form-invoice-edit").validate().element($("#reqDeliDate"))
            }
        });
        var reqArriDate = laydate.render({
            elem: '#reqArriDate', //指定元素
            //     format: 'yyyy-MM-dd',
            min: $("#reqDeliDate").val(),
            isInitValue: false,
            trigger: 'click',
            type: 'datetime',
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours=now.getHours();
                this.dateTime.minutes=now.getMinutes();
                this.dateTime.seconds=now.getSeconds();
            },
            done: function (value, date, endDate) {
                reqDeliDate.config.max = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date,
                    hours: date.hours,
                    minutes: date.minutes,
                    seconds: date.seconds
                };
                $("#reqArriDate").val(value);

                //单独校验日期
                $("#form-invoice-edit").validate().element($("#reqArriDate"))
            }
        });


        if (type == "copy") {
            //开始日期
            var startDate = laydate.render({
                elem: '#startDate', //指定元素
                format : 'yyyy-MM-dd',
                isInitValue : false,
                trigger: 'click',
                type: 'date',
                done: function(value, date, ed){
                    endDate.config.min = {
                        year: date.year,
                        month: date.month - 1,//关键
                        date: date.date
                    };
                    $("#startDate").val(value);
                }
            });

            //结束日期
            var endDate = laydate.render({
                elem: '#endDate', //指定元素
                format : 'yyyy-MM-dd',
                isInitValue : false,
                trigger: 'click',
                type: 'date',
                done: function(value, date, ed){
                    startDate.config.max = {
                        year: date.year,
                        month: date.month - 1,//关键
                        date: date.date
                    };
                    $("#endDate").val(value);
                }
            });
        }
    });


    /**
     * 校验
     */
    $("#form-invoice-edit").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            reqDeliDate:{
                required:true,
            },
            reqArriDate:{
                required:true,
            },
            custName:{
                required:true,
            },
            balaName:{
                required:true,
            },
            appDeliMobile:{
                isPhone:true
            }
        }

    });


    /**
     * 校验货品
     * @returns {boolean}
     */
    function checkGoods(){
        //发货
        let fhdz_numCount = $("#fhdz_numCount").val();
        let fhdz_weightCount = $("#fhdz_weightCount").val();
        let fhdz_volumeCount = $("#fhdz_volumeCount").val();
        // let fhdz_costAmount = $("#fhdz_costAmount").val();
        //收货
        let shdz_numCount = $("#shdz_numCount").val();
        let shdz_weightCount = $("#shdz_weightCount").val();
        let shdz_volumeCount = $("#shdz_volumeCount").val();
        // let shdz_costAmount = $("#shdz_costAmount").val();

        //校验数量、重量、体积 是否一致
        if (parseFloat(fhdz_numCount) != parseFloat(shdz_numCount)
            || parseFloat(fhdz_weightCount) != parseFloat(shdz_weightCount)
            || parseFloat(fhdz_volumeCount) != parseFloat(shdz_volumeCount)
            // || parseFloat(fhdz_costAmount) != parseFloat(shdz_costAmount)
        ) {
            return false;
        }

        let fhGoodsList = []
        $("[id^=goodsName_fhdz_]").each(function (i,v){
            if (v.value) {
                fhGoodsList.push(v.value);
            }
        })

        let shGoodsList = []
        $("[id^=goodsName_shdz_]").each(function (i,v){
            if (v.value) {
                shGoodsList.push(v.value)
            }
        })

        if (fhGoodsList.length === 0 || shGoodsList.length === 0) {
            return false;
        }

        //去重  比较收发货 货品是否一致
        fhGoodsList = fhGoodsList.filter((item, index) => fhGoodsList.indexOf(item) === index)
        shGoodsList = shGoodsList.filter((item, index) => shGoodsList.indexOf(item) === index)
        if (JSON.stringify(fhGoodsList.sort()) != JSON.stringify(shGoodsList.sort())) {
            return false;
        }


        return true;
    }

    function changePayOnDelivery(checkbox) {
        if (checkbox.checked) {
            checkbox.value = 1;
        } else {
            checkbox.value = 0;
        }
    }

    function changeOversizeType() {
        $("#oversizeTypeDiv").text("")
        var isOversize = $("#isOversize").is(':checked');
        if(isOversize){
            let goodsLength = $("#goodsLength").val();
            let goodsWidth = $("#goodsWidth").val();
            let goodsHeight = $("#goodsHeight").val();

            if (goodsLength !== '' || goodsWidth !== '' || goodsHeight !== '') {
                $("#oversizeTypeDiv").text("超体积");
            }else {
                $("#oversizeTypeDiv").text("超重");
            }
        }
    }

    /**
     * 提交
     */
    function submitHandler(type) {
        //计算合计
        // calculateTotals();
        if ($.validate.form()) {
            /*
             * 提货日期选择：
             *      以整点时间为准；若选择了radio,则以radio为准；否则以选择得整点时间
             *      两者必须二选一
             */
            var reqDeliDateHour = $("#reqDeliDateHour").val();
            var radio =$('input:radio:checked').val();
            if($.common.isEmpty(reqDeliDateHour) && $.common.isEmpty(radio)){
                $.modal.msgError("提货日期未选择整点时间！");
                return ;
            }
            //要求提货日期
            var reqDeliDate = $("#reqDeliDate").val();
            if($.common.isNotEmpty(radio)){
                reqDeliDate = reqDeliDate + " " + radio;
            }else{
                reqDeliDate = reqDeliDate + " "+reqDeliDateHour+":00:00";
            }
            //赋值提货日期
            $("#reqDeliDateTime").val(reqDeliDate);

            /*
             * 比较要求提货日（到货日）日期大小
             */
            var reqArriDate = $("#reqArriDate").val();
            var reqArriTime = new Date(reqArriDate.replace("-", "/").replace("-", "/"));
            var reqDeliTime = new Date(reqDeliDate.replace("-", "/").replace("-", "/"));
            if(reqDeliTime > reqArriTime){
                $.modal.msgError("要求提货日期不能大于要求到货日期！");
                return ;
            }

            //校验提货方
            if(fhdz_addr_list.length === 0){
                $.modal.msgError("请选择发货方信息！");
                return;
            }

            //校验收货方
            if(shdz_addr_list.length === 0){
                $.modal.msgError("请选择收货方信息！");
                return;
            }

            let otherFee = $("#otherFee").val();
            let otherFeeType = $("#otherFeeType").val();

            if (otherFee != null && otherFee != '' && (otherFeeType == null || otherFeeType == '')) {
                $.modal.msgError("请选择其他费用的费用类型！");
                return;
            }

         /*   var isOversize = $("#isOversize").is(':checked');

            if(isOversize){
                let goodsLength = $("#goodsLength").val();
                let goodsWidth = $("#goodsWidth").val();
                let goodsHeight = $("#goodsHeight").val();

                if ((goodsLength === undefined || goodsLength === null || goodsLength === '')
                    && (goodsWidth === undefined || goodsWidth === null || goodsWidth === '')
                    && (goodsHeight === undefined || goodsHeight === null || goodsHeight === '')) {
                    $.modal.msgError("大件三超，请填写长、宽、高中的一项。");
                    return;
                }
            }*/

            // //校验收货地址与i退货地址是否相同
            // var deliveryId = $("#deliveryId").val();
            // var arrivalId = $("#arrivalId").val();
            //
            // if (deliveryId == arrivalId) {
            //     $.modal.msgError("提货地址与收货地址不能相同！");
            //     return;
            // }
            let goodsBl = false
            Object.values(addr_goods_index).forEach((value) => {
                if (value.length === 0) {
                    goodsBl = true
                    return;
                }
            })
            if (goodsBl) {
                $.modal.msgError("货品不能为空！");
                return;
            }

            //判断货品收货 发货  是否相等
            let cg = checkGoods();
            if (!cg){
                $.modal.msgError("请检查提货货品与到货货品是否一致！");
                return;
            }

            let ifBargain = $("#ifBargain").val();
            if (hasContract == 0 && ifBargain == 0) {
                $.modal.msgError("未查询到合同价，无法下单。");
                return;
            }



            let totalCostPrice = $("#totalCostPrice").val();
            if ((ifBargain != 1 || rfqLineId) && (totalCostPrice == null || totalCostPrice == '')) {
                $.modal.msgError("”成本价“不能为空");
                return;
            }


            // let totalCostPrice = $("#totalCostPrice").val();
            // if (hasGuidingPrice == 1 && (totalCostPrice == '' || parseFloat(totalCostPrice) <= 0)) {
            //     $.modal.msgError("请输入大于0的”成本价“信息");
            //     return;
            // }


            let lonBl = false;
            $("[id^='longitudedegree_']").each(function() {
                // 获取元素的值
                var value = $(this).val();
                // 检查是否为空
                if (!value) {
                    lonBl = true
                    return false;
                }
            });
            if (lonBl) {
                $.modal.msgError("地址未经确认，请先点击地址左侧红色坐标点，确认收发货地址。");
                return;
            }



            //是否多装多卸
            let  isMultiple = $("#isMultiple").val()

            //指导价
            var guidingPrice = $("#guidingPrice").val();
 
            //校验指导价 如果带出的最新知道是待审核状态 则无法修改
            // if (guidingPriceCheckStust === 1 && Number(guidingPriceOld) != Number(guidingPrice)) {
            //     $.modal.msgError("存在待审核的指导价，无法存入新指导价！");
            //     return;
            // }

            //判断 收款金额不能大于总金额
            let costAmount = parseFloat($("#fhdz_costAmount_text").text());

            let balaType = $("#balaType").val();
            if (balaType == '2' || balaType == '5' || balaType == '6') {
                let collectAmount = parseFloat($("#collectAmount").val());
                if ($("#collectAmount").val() == null || $("#collectAmount").val() =='') {
                    $.modal.msgError("到付应收不能为空！");
                    return;
                }

                if (collectAmount > costAmount) {
                    $.modal.msgError("到付应收不能大于总金额！");
                    return;
                }
            }

            var costAmountBl = false;
            if ($.common.isEmpty(costAmount) || costAmount == 0) {
                costAmountBl = true;
            }

            //
            // var guidingPriceBl = false;
            // var transType = $("#transCode").val();
            // if (transType == '0' || transType == '4' || transType == '15') {
            //     if(($.common.isEmpty(guidingPrice) || guidingPrice === '0') && isMultiple == '0'  && crtGuidePrice ==1) {
            //         guidingPriceBl = truecheckGoods
            //     }checkGoods
            // }
            //
            // if (guidingPriceBl || costAmountBl) {
            //     let msg = '';
            //     guidingPriceBl ? msg += '指导价为0</br>' : ''
            //     costAmountBl ? msg += '总金额为0</br>' : ''
            //
            //
            //     $.modal.confirm(`${msg}是否确认提交？`, function () {
            //         subscribe(type)
            //     });
            //     return;
            // }

            subscribe(type)

        }
    }

    let addCt = 0
    function subscribe(type) {
        if (type == 1) {
            addCt = 0
            parent.layer.open({
                type: 1,
                title: '生成单数',
                // closeBtn: 0,
                shadeClose: true,
                shade: false,
                area: ['30%', '20%'],
                content: $('#invoiceCtHtml').html(),
                btn: ['确定', '取消']
                , yes: function (index, layero) {
                    parent.layer.close(index);
                    let ct = top.$("#invoiceCt").val();
                    ct = ct == null || ct == '' ? 1 : ct;

                    if (ct > 10) {
                        $.modal.msgError("最多支持同时生成10单！");
                        return
                    }
                    addCt = ct

                    // if(($.common.isEmpty(guidingPrice) || guidingPrice === '0') && isMultiple == '0'){
                    //     $.modal.confirm("指导价为0，是否确认提交？", function () {
                    //         $('#insuranceAppendix').fileinput('upload');
                    //
                    //         var dis = $(":disabled");
                    //         dis.attr("disabled", false);
                    //         var data = $("#form-invoice-edit").serializeArray();
                    //         data.push({"name": "numCount", "value": $("#fhdz_numCount").val()});
                    //         data.push({"name": "weightCount", "value": $("#fhdz_weightCount").val()});
                    //         data.push({"name": "volumeCount", "value": $("#fhdz_volumeCount").val()});
                    //         data.push({"name": "costAmount", "value": $("#fhdz_costAmount").val()});
                    //         data.push({"name": "invoiceCt", "value": ct});
                    //
                    //         $.operate.saveTab(prefix + "/add_many", data,function (result) {
                    //             if (result.code != 0) {
                    //                 dis.attr("disabled", true);
                    //             }
                    //         });
                    //
                    //     });
                    //     return;
                    // }
                    $.modal.loading("正在处理中，请稍后...")

                    $('#insuranceAppendix').fileinput('upload');
                    jQuery.subscribe("cmt", bargainFileUpload);
                    jQuery.subscribe("bargainFileCmt", commitMany);

                }
            });
        }else {
            // //提交时若指导价为0/空进行提示
            // if(($.common.isEmpty(guidingPrice) || guidingPrice === '0') && isMultiple == '0'){
            //     $.modal.confirm("指导价为0，是否确认提交？", function () {
            //         $('#insuranceAppendix').fileinput('upload');
            //         jQuery.subscribe("cmt", commit);
            //     });
            //     return;
            // }
            $.modal.loading("正在处理中，请稍后...")


            $('#insuranceAppendix').fileinput('upload');
            jQuery.subscribe("cmt", bargainFileUpload);
            jQuery.subscribe("bargainFileCmt", commit);
        }
    }


    /**
     * 普通提交
     */
    function commit() {

        var dis = $(":disabled");
        dis.attr("disabled", false);
        var data = $("#form-invoice-edit").serializeArray();
        data.push({"name": "numCount", "value": $("#fhdz_numCount").val()});
        data.push({"name": "weightCount", "value": $("#fhdz_weightCount").val()});
        data.push({"name": "volumeCount", "value": $("#fhdz_volumeCount").val()});
        // data.push({"name": "costAmount", "value": $("#fhdz_costAmount").val()});

        if (type == "edit") {
            $.operate.saveTab(prefix + "/edit", data,function (result) {
                if (result.code != 0) {
                    dis.attr("disabled", true);
                }
                $.modal.loading("正在处理中，请稍后...")

            });
        }else if (type == "copy") {
            $.operate.saveTab(prefix + "/add", data, function (result) {
                if (result.code != 0) {
                    dis.attr("disabled", true);
                }
                $.modal.loading("正在处理中，请稍后...")

            });
        }
    }

    function commitMany() {
        $.modal.loading("正在处理中，请稍后...")

        var dis = $(":disabled");
        dis.attr("disabled", false);
        var data = $("#form-invoice-edit").serializeArray();
        data.push({"name": "numCount", "value": $("#fhdz_numCount").val()});
        data.push({"name": "weightCount", "value": $("#fhdz_weightCount").val()});
        data.push({"name": "volumeCount", "value": $("#fhdz_volumeCount").val()});
        // data.push({"name": "costAmount", "value": $("#fhdz_costAmount").val()});
        data.push({"name": "invoiceCt", "value": addCt});

        $.operate.saveTab(prefix + "/add_many", data,function (result) {
            if (result.code != 0) {
                dis.attr("disabled", true);
            }

            $.modal.closeLoading()
        });

    }

    function bargainFileUpload() {
        $('#bargainFile').fileinput('upload');

    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }


    /**
     * [isDuringDate 比较当前时间是否在指定时间段内]
     * @param    date   [beginDateStr] [开始时间]
     * @param    date   [endDateStr]   [结束时间]
     * @return   Boolean
     */
    function isDuringDate(beginDateStr, endDateStr) {
        let now = new Date()
        let month = now.getMonth()+1;
        let day = now.getDate();
        let output = now.getFullYear() + '/' + (month<10 ? '0' : '') + month + '/' + (day<10 ? '0' : '') + day;
        var curDate = new Date(output)

        var beginDate = beginDateStr == null ? new Date('1900-01-01') : new Date(beginDateStr)
        var endDate = endDateStr == null ? new Date('2900-01-01') : new Date(endDateStr)

        if (curDate >= beginDate && curDate <= endDate) {
            return true;
        }
        return false;
    }

    function uuid(len, radix) {
        var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
        var uuid = [], i;
        radix = radix || chars.length;

        if (len) {
            // Compact form
            for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random()*radix];
        } else {
            // rfc4122, version 4 form
            var r;

            // rfc4122 requires these characters
            uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
            uuid[14] = '4';

            // Fill in random data.  At i==19 set the high bits of clock sequence as
            // per rfc4122, sec. 4.1.5
            for (i = 0; i < 36; i++) {
                if (!uuid[i]) {
                    r = 0 | Math.random()*16;
                    uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
                }
            }
        }

        return uuid.join('');
    }

    function getLongLat(address, areaId, detailAddr, index, lngLat,ele) {
        // var url = ctx + "basic/address/lnglat?address=" + address + "&areaId=" + areaId + "&detailAddr=" + detailAddr + "&index=" + index + "&lnglat=" + lngLat;

        var $parentDiv = $(ele).closest('.fh_addr');
        let isCa = $parentDiv.hasClass('fh_edit') ? "1" : "0"

        var url = ctx + "basic/address/lnglat?address=" + encodeURIComponent(address) +
        "&areaId=" + encodeURIComponent(areaId) +
        "&detailAddr=" + encodeURIComponent(detailAddr) +
        "&index=" + encodeURIComponent(index) +
        "&lnglat=" + encodeURIComponent(lngLat) +
        "&isCa=" + encodeURIComponent(isCa);

        $.modal.open('经纬度标记', url, "800", "600");
    }

    // 联系人信息编辑相关变量
    var currentEditingContact = {
        type: 0, // 0: 发货地址, 1: 收货地址
        index: 0,
        prefix: '' // 'ca' for changed arrival address
    };

    /**
     * 编辑联系人信息
     * @param type 地址类型 0:发货地址 1:收货地址
     * @param index 地址索引
     * @param contact 联系人姓名
     * @param mobile 联系电话
     * @param prefix 前缀，用于区分改后地址
     */
    function editContactInfo(type, index, contact, mobile, prefix) {
        // 记录当前编辑的联系人信息
        currentEditingContact.type = type;
        currentEditingContact.index = index;
        currentEditingContact.prefix = prefix || '';

        // 填充当前值到编辑框
        $('#editContactName').val(contact || '');
        $('#editContactMobile').val(mobile || '');

        // 显示模态框
        $('#contactEditModal').show();
    }

    /**
     * 关闭联系人编辑模态框
     */
    function closeContactEditModal() {
        $('#contactEditModal').hide();
        // 清空输入框
        $('#editContactName').val('');
        $('#editContactMobile').val('');
    }

    /**
     * 保存联系人信息
     */
    function saveContactInfo() {
        var newContact = $('#editContactName').val().trim();
        var newMobile = $('#editContactMobile').val().trim();

        // 验证输入
        if (!newContact) {
            $.modal.alertWarning('请输入联系人姓名');
            return;
        }
        if (!newMobile) {
            $.modal.alertWarning('请输入联系电话');
            return;
        }

        // 验证手机号格式（简单验证）
        var mobileReg = /^1[3-9]\d{9}$/;
        if (!mobileReg.test(newMobile)) {
            $.modal.alertWarning('请输入正确的手机号码');
            return;
        }

        var index = currentEditingContact.index;
        var prefix = currentEditingContact.prefix;

        // 更新隐藏字段的值
        if (prefix === 'ca') {
            // 改后地址：修改caArriContact、caArriMobile
            $('#caArriContact' + index).val(newContact);
            $('#caArriMobile' + index).val(newMobile);
        } else {
            // 普通地址：修改contact_、mobile_
            $('#contact_' + index).val(newContact);
            $('#mobile_' + index).val(newMobile);
        }

        // 更新页面显示
        updateContactDisplay(index, newContact, newMobile, prefix);

        // 关闭模态框
        closeContactEditModal();

        $.modal.alertSuccess('联系人信息修改成功');
    }

    /**
     * 更新联系人信息显示
     */
    function updateContactDisplay(index, contact, mobile, prefix) {
        $('.contact-info-clickable').each(function() {
            var $this = $(this);
            var dataIndex = $this.data('index');
            var onclick = $this.attr('onclick');

            // 根据prefix匹配对应的显示元素
            if (onclick && dataIndex == index) {
                var isMatch = false;
                if (prefix === 'ca') {
                    // 改后地址：匹配包含'ca'的onclick
                    isMatch = onclick.includes("'ca'");
                } else {
                    // 普通地址：匹配不包含'ca'的onclick
                    isMatch = !onclick.includes("'ca'");
                }

                if (isMatch) {
                    // 更新显示文本
                    $this.text('联系人信息: ' + contact + '/' + mobile);
                    // 更新data属性
                    $this.data('contact', contact);
                    $this.data('mobile', mobile);
                    $this.attr('data-contact', contact);
                    $this.attr('data-mobile', mobile);
                }
            }
        });
    }

    // 点击模态框外部关闭
    $(document).on('click', '#contactEditModal', function(event) {
        if (event.target === this) {
            closeContactEditModal();
        }
    });

    // 按ESC键关闭模态框
    $(document).on('keydown', function(event) {
        if (event.key === 'Escape') {
            closeContactEditModal();
        }
    });

</script>
</body>
<script id="invoiceCtHtml" type="text/template">
    <div class="form-content">
        <div class="row" style="margin: 0;">
            <label class="flex_left">创建数量：</label>
            <div class="flex_right" style="display: inline-block;">
                <input name="invoiceCt" id="invoiceCt"
                       type="text" min="0" max="10" autocomplete="off"
                       oninput="$.numberUtil.onlyNumberInt(this)"
                >
            </div>
            <p class="mt10">注意：批量创建最多创建10单数据，如果不填默认创建一单。</p>
        </div>
    </div>
</script>
<th:block th:if="${rfqLine != null && convert != null}" th:include="tms/rfq/order-able.html"/>
</html>