<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方费用录入')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-out-quote" class="form-horizontal" novalidate="novalidate" th:object="${otherFee}">
        <div class="panel-body">
            <!--基础信息 begin-->
            <input name="lotId" type="hidden" th:field="*{otherFeeId}">
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red">费用类型：</label>
                        <div class="col-sm-8">
                            <select id="feeType" class="form-control valid" aria-invalid="false" th:with="type=${@dict.getType('cost_type_on_way')}" disabled  required>
                                <option value="">-- 请选择 --</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${dict.dictValue} == *{feeType}"></option>
                            </select>

                            <input name="feeType" th:value="*{feeType}" type="hidden">
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red">付款方式：</label>
                        <div class="col-sm-8">
                            <select class="form-control valid" th:field="*{payMethod}" aria-invalid="false"
                                    name="payMethod" th:with="type=${@dict.getType('pay_method')}" required>
                                <option value=""></option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red">费用：</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" oninput="$.numberUtil.onlyNumber(this)" th:field="*{feeAmount}"
                                   placeholder="费用" autocomplete="off" required>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4">收款银行：</label>
                        <div class="col-sm-8">
                            <input type="text" th:field="*{recBank}" maxlength="50" class="form-control">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4">收款人：</label>
                        <div class="col-sm-8">
                            <input type="text" th:field="*{recAccount}" maxlength="25" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4">收款账号：</label>
                        <div class="col-sm-8">
                            <input type="text" th:field="*{recCardNo}" maxlength="25" class="form-control">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red">付款方式：</label>
                        <div class="col-sm-8">
                            <select class="form-control valid"  aria-invalid="false"
                                    name="payType"  required th:field="*{payType}">
                                <option value=""></option>
                                <option th:value="0">现金</option>
                                <option th:value="1">油卡</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="col-sm-2" style="color: red">备注：</label>
                        <div class="col-sm-10">
                            <textarea th:field="*{memo}" maxlength="200" class="form-control valid" required rows="4"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" th:if="${type=='apply'}">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="col-sm-2">附件：</label>
                        <div class="col-sm-10">
                            <input name="fileId"
                                   id="fileId"
                                   class="form-control" type="file" multiple>
                            <input type="hidden" id="tid" name="tid">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" th:if="${type=='apply' && (otherFee.payType == null || otherFee.payType == 0)}">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="col-sm-2 col-xs-2">含税构成：</label>
                        <div class="col-sm-10 col-xs-10">
                            <span id="taxTxtView">...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" th:if="${type=='apply' && (otherFee.payType == null || otherFee.payType == 0) && receiptList.size() > 0}">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="col-sm-2">已上传发票：</label>
                        <div class="col-sm-10">
                            <span th:each="receipt: ${receiptList}">
                                <img style="height:50px;margin:0 5px 0 0;" modal="zoomImg" th:src="${receipt.filePath}"/>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" th:if="${type=='apply' && (otherFee.payType == null || otherFee.payType == 0)}">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="col-sm-2">上传发票：</label>
                        <div class="col-sm-10">
                            <input id="receipt" class="form-control" name="receipt" type="file" multiple>
                            <input id="tid_receipt" name="tidReceipt" type="hidden">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <input type="hidden" name="taxTxt">
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">

    var type = [[${type}]];

    $(function () {
        //图片功能初始化
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("fileId", "tid", picParam);
        $('#fileId').on('filesuccessremove', function(event, key, index) {
            $('#fileId').fileinput('clear');
            $('#tid').val('');
        });

        // 只能选择 进仓费、仓储费、信息费、进门费、提货费、装卸费、送货费
        $("#feeType").find("option[value='3']").hide();
        $("#feeType").find("option[value='4']").hide();
        $("#feeType").find("option[value='7']").hide();
        $("#feeType").find("option[value='8']").hide();
        $("#feeType").find("option[value='10']").hide();
        $("#feeType").find("option[value='12']").hide();
        $("#feeType").find("option[value='13']").hide();
        $("#feeType").find("option[value='14']").hide();
        $("#feeType").find("option[value='16']").hide();
        $("#feeType").find("option[value='17']").hide();
        $("#feeType").find("option[value='18']").hide();
        $("#feeType").find("option[value='19']").hide();
        $("#feeType").find("option[value='20']").hide();
        $("#feeType").find("option[value='21']").hide();

        $.file.initAddFiles("receipt", "tid_receipt", {
            maxFileCount:0,
            publish: "cmt2",
            fileType: "image"
        });
        $('#receipt').on('filesuccessremove', function(event, key, index) {
            $('#receipt').fileinput('clear');
            $('#tid_receipt').val('');
        });
        translateTaxTxt();
    });

    /**
     * 图片提交
     */
    function submitHandler() {
        if (type === 'apply') {
            if (taxTxt && !sumTaxTxt().eq(new BigNumber($("#feeAmount").val()))) {
                $.modal.msgError("费用金额与含税构成总额必须一致")
                return;
            }
            if ($.validate.form()) {
                $.modal.confirm("确认提交费用申请吗？", function(){
                    $.unsubscribe("cmt");
                    $.unsubscribe("cmt2");
                    if ($('#fileId').fileinput('getFilesCount') > 0) {
                        $.subscribe("cmt", function(){
                            if ($('#receipt').fileinput('getFilesCount') > 0) {
                                $.subscribe("cmt2", commitForm);
                                $('#receipt').fileinput('upload');
                            } else {
                                commitForm()
                            }
                        });
                        $('#fileId').fileinput('upload');
                    } else if ($('#receipt').fileinput('getFilesCount') > 0) {
                        $.subscribe("cmt2", commitForm);
                        $('#receipt').fileinput('upload');
                    } else {
                        commitForm()
                    }
                })
            }
        } else {
            commitForm();
        }
    }

    /**
     * 表单提交
     */
    function commitForm() {
        if ($.validate.form()) {
            var url = ctx + "invoice/fee_apply/edit";
            if(type == 'apply'){
                $("[name=taxTxt]").val(taxTxt);
                url = ctx + "invoice/fee_apply/apply";
            }
            var data = $("#form-out-quote").serializeArray();
            $.operate.save(url, data);
        }
    }

    var billingType = [[${@dict.getType('billing_type')}]];//开票类型
    var taxTxt = [[${taxTxt}]];

    function translateTaxTxt() {
        if (!taxTxt) { return; }
        var array = taxTxt.split(",");
        var temp = [];
        var sum = new BigNumber("0");
        for (let j = 0; j < array.length; j++) {
            var t = array[j].split(":");
            for (let i = 0; i < billingType.length; i++) {
                if (billingType[i].dictValue == t[0]) {
                    temp.push("[", billingType[i].dictLabel, ":", t[1], "]")
                    sum = sum.plus(new BigNumber(t[1]))
                    break;
                }
            }
        }
        temp.push(' <a href="javascript:adjustTax()">调</a>')
        if (!sum.eq(new BigNumber($('#feeAmount').val()))) {
            temp.splice(0,0,"<span style='color:red' err-tax data-toggle='tooltip' title='合计不等于当前费用, 请手动调整' data-trigger='manual'>")
            temp.push("</span>")
        }
        $("#taxTxtView").html(temp.join(""))
        $('[err-tax]').tooltip('show')
    }

    function adjustTax() {
        var amount = $("#feeAmount").val();
        var tmp = [`<div style="padding: 8px 20px;">
			<form class="form-horizontal">
				<div class="form-group">
					<label class="col-xs-4 control-label" style="padding-top: 5px;text-align: right">当前费用：</label>
					<div class="col-xs-3">
					  	<p class="form-control-static">${amount}</p>
					</div>
					<div class="col-xs-4">
					  	<p class="form-control-static" id="result" style="color:#ff0000"></p>
					</div>
					<div class="col-xs-1">
					    <i class="fa fa-plus-circle" style="height: 34px;vertical-align: middle;padding: 7px 0;color:#007dcc" onclick="addTaxRow(this)"></i>
					</div>
				</div>`]
        var arr = taxTxt.split(",");
        for (let i = 0; i < arr.length; i++) {
            var tt = arr[i].split(":");
            tmp.push('<div class="form-group">')
            tmp.push('<label class="col-xs-7">') // col-sm-由于所在页面太窄，会撑成100%
            tmp.push('<select billingtype class="form-control">');
            for (let j = 0; j < billingType.length; j++) {
                tmp.push("<option value='",billingType[j].dictValue,"'",tt[0]==billingType[j].dictValue?' selected':'',">",billingType[j].dictLabel,"</option>");
            }
            tmp.push("</select>");
            tmp.push('</label>')
            tmp.push('<div class="col-xs-4">')
            tmp.push('<input class="form-control" tax oninput="$.numberUtil.onlyNumberTwoDecimal(this);check()" value="',tt[1],'" autocomplete="off">')
            tmp.push('</div>')
            tmp.push('<div class="col-xs-1">')
            tmp.push('<i class="fa fa-minus-circle" style="height: 34px;vertical-align: middle;padding: 7px 0;color:#ff0000" onclick="$(this).closest(\'.form-group\').remove();check()"></i>')
            tmp.push('</div>')
            tmp.push('</div>')
        }
        tmp.push('</form>')
        tmp.push('* 所有含税额合计必须<span style="color:blue;font-weight: bold;">等于当前费用</span>')
        tmp.push('</div>')
        layer.open({
            type: 1,
            area: ['450px', '403px'],
            fix: false,
            //不固定
            maxmin: false,
            shade: 0.3,
            shadeClose: false,
            title: '费用含税额拆分',
            content: tmp.join(""),
            btn: ['<i class="fa fa-check"></i> 保存', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(idx, layero){
                var chk = check();
                var objTemp = {};//校验开票类型时候重复的临时对象
                var tmp = []
                if (chk) {
                    var amount_origin = new BigNumber(amount)
                    var flag = true;
                    $("[billingtype]").each(function(){
                        if (objTemp[$(this).val()] != null) {
                            $.modal.msgError("相同开票类型的请合并");
                            flag = false;
                            return;
                        } else {
                            objTemp[$(this).val()] = 1;
                            var n = new BigNumber($(this).closest('.form-group').find('[tax]').val()).toNumber();
                            if (n === 0 && amount_origin.toNumber() != 0) {
                                $.modal.msgError("请输入非0的数字");
                                flag = false;
                                return;
                            } else if (n < 0 && amount_origin.toNumber() > 0) {
                                $.modal.msgError("当前费用大于0，各税额必须都大于0");
                                flag = false;
                                return;
                            } else if (n > 0 && amount_origin.toNumber() < 0) {
                                $.modal.msgError("当前费用小于0，各税额必须都小于0");
                                flag = false;
                                return;
                            }
                            tmp.push($(this).val()+":"+$(this).closest('.form-group').find('[tax]').val())
                        }
                    })
                    if (flag) {
                        taxTxt = tmp.join(",")
                        translateTaxTxt()
                        layer.close(idx)
                    }
                } else {
                    $.modal.msgError("所有含税额合计必须等于当前费用")
                }
            }
        })
        check()
    }
    function check() {
        var amount_origin = new BigNumber($("#feeAmount").val());
        var sum = calc1();
        if (sum.eq(amount_origin)) {
            $("#result").text("")
            return true;
        } else if (sum.gt(amount_origin)) {
            $("#result").text("超" + sum.minus(amount_origin))
            return false;
        } else if (sum.lt(amount_origin)) {
            $("#result").text("低" + amount_origin.minus(sum))
            return false;
        }
    }
    function addTaxRow(i){
        var tmp = [];
        tmp.push('<div class="form-group">')
        tmp.push('<label class="col-sm-7">')
        tmp.push('<select billingtype class="form-control">');
        for (let j = 0; j < billingType.length; j++) {
            tmp.push("<option value='",billingType[j].dictValue,"'>",billingType[j].dictLabel,"</option>");
        }
        tmp.push("</select>");
        tmp.push('</label>')
        tmp.push('<div class="col-sm-4">')
        tmp.push('<input class="form-control" tax oninput="$.numberUtil.onlyNumberTwoDecimal(this);check()" autocomplete="off">')
        tmp.push('</div>')
        tmp.push('<div class="col-sm-1">')
        tmp.push('<i class="fa fa-minus-circle" style="height: 34px;vertical-align: middle;padding: 7px 0;color:#ff0000" onclick="$(this).closest(\'.form-group\').remove();check()"></i>')
        tmp.push('</div>')
        tmp.push('</div>')
        $(i).closest("form").append(tmp.join(''))
    }
    function calc1() {
        var sum = new BigNumber("0")
        $("[tax]").each(function(){
            var amount = new BigNumber(this.value.trim());
            if (!amount.isNaN()) {
                sum = sum.plus(amount);
            }
        })
        return sum;
    }
    function sumTaxTxt() {
        var sum = new BigNumber("0");
        var arr = taxTxt.split(",");
        for (let i = 0; i < arr.length; i++) {
            var t = arr[i].split(":");
            sum = sum.plus(new BigNumber(t[1]))
        }
        return sum
    }
</script>
</body>
</html>