<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方费用录入')"/>
</head>

<body>
<div class="form-content">
    <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
        <div class="panel-body">
            <!--基础信息 begin-->
            <input name="lotId" type="hidden" th:value="${invoiceId}">
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red">费用类型：</label>
                        <div class="col-sm-8">
                            <select name="feeType" id="feeType" class="form-control valid" aria-invalid="false" th:with="type=${@dict.getType('cost_type_on_way')}"  required>
                                <option value="">-- 请选择 --</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:if="${dict.dictValue} == '27'"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red">付款方式：</label>
                        <div class="col-sm-8">
                            <select class="form-control valid" id="payMethod" aria-invalid="false"
                                    name="payMethod" th:with="type=${@dict.getType('pay_method')}" required>
                                <option value=""></option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red">费用：</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" id="feeAmount" name="feeAmount"
                                   placeholder="费用" autocomplete="off" required>
                        </div>
                    </div>
                </div>

                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red">付款类型：</label>
                        <div class="col-sm-8">
                            <select class="form-control valid"  aria-invalid="false"
                                    name="payType"  required>
                                <option value=""></option>
                                <option th:value="0">现金</option>
                                <option th:value="1">油卡</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4">收款银行：</label>
                        <div class="col-sm-8">
                            <input type="text" name="recBank" id="recBank" maxlength="50" class="form-control">
                        </div>
                    </div>
                </div>
            <!-- </div>
            <div class="row"> -->
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4">收款人：</label>
                        <div class="col-sm-8">
                            <input type="text" name="recAccount" id="recAccount" maxlength="25" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4">收款账号：</label>
                        <div class="col-sm-8">
                            <input type="text" name="recCardNo" id="recCardNo" maxlength="25" class="form-control">
                        </div>
                    </div>
                </div>
            <!-- </div>
            <div class="row"> -->
                <!-- <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red">付款类型：</label>
                        <div class="col-sm-8">
                            <select class="form-control valid"  aria-invalid="false"
                                    name="payType"  required>
                                <option value=""></option>
                                <option th:value="0">现金</option>
                                <option th:value="1">油卡</option>
                            </select>
                        </div>
                    </div>
                </div> -->
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-2" style="color: red">备注：</label>
                        <div class="col-sm-10">
                           <textarea name="memo" id="memo" maxlength="200" class="form-control valid" required rows="4"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    /**
     * 提交
     */
    function submitHandler(index, layero, callback) {
        if ($.validate.form()) {
            var data = $("#form-out-quote").serializeArray();
            // $.operate.save(ctx + "invoice/insertFeeEntry", data);
            $.operate.saveModal(ctx + "invoice/insertFeeEntry", data, function (res) {
                if (res.code == web_status.SUCCESS) {
                    parent.layer.close(index);
                    if (typeof callback == "function") {
                        callback()
                    }
                }
            })
        }
    }

    $(function () {
        // 只能选择 进仓费、仓储费、信息费、进门费、提货费、装卸费、送货费
        $("#feeType").find("option[value='3']").hide();
        $("#feeType").find("option[value='4']").hide();
        $("#feeType").find("option[value='7']").hide();
        $("#feeType").find("option[value='8']").hide();
        $("#feeType").find("option[value='10']").hide();
        $("#feeType").find("option[value='12']").hide();
        $("#feeType").find("option[value='13']").hide();
        $("#feeType").find("option[value='14']").hide();
        $("#feeType").find("option[value='16']").hide();
        $("#feeType").find("option[value='17']").hide();
        $("#feeType").find("option[value='18']").hide();
        $("#feeType").find("option[value='19']").hide();
        $("#feeType").find("option[value='20']").hide();
        $("#feeType").find("option[value='21']").hide();
    });

</script>
</body>
</html>