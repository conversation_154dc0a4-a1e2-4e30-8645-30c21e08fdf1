<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价')"/>
</head>
<style>
    .table-striped {
        height: calc(100% - 40px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped toofoot pm">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    //参数
    var param = parent.layui.$('#historyGuidingPriceParam').val();

    $(function () {
        var options = {
            url: ctx + "invoice/history_reference_price?" + param,
            showToggle: false,
            showSearch: false,
            showColumns: false,
            fixedColumns: true,
            showRefresh: false,
            fixedNumber: 0,
            columns: [
                {
                    title: '指导价',
                    align: 'left',
                    field: 'price',
                    formatter: function (value, row, index) {
                        if (value != null) {
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else {
                            return '';
                        }
                    }

                },
                {
                    title:'价格类型',
                    align:'left',
                    field:'priceType',
                    formatter: function (value, row, index) {
                        switch (value) {
                            case 0:
                                return '<span class="carve carve-primary">普货</span>'
                            case 1:
                                return '<span class="carve carve-warning">危险品</span>';
                            case 2:
                                return '<span class="carve carve-coral">冷链</span>';
                            case 9:
                                return '<span class="carve carve-success">平均价</span>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title:'状态',
                    align:'left',
                    field:'checkStatus',
                    formatter: function (value, row, index) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-warning">待审核</span>'
                            case 1:
                                return '<span class="label label-danger">审核不通过</span>';
                            case 2:
                                return '<span class="label label-primary">审核通过</span>';
                            default:
                                break;
                        }
                    }
                },
                // {
                //     title: '有效期',
                //     align: 'left',
                //     field: 'startDate',
                //     formatter: function (value, row, index) {
                //         let startDate = row.startDate == null ? '-' : row.startDate.substring(0,10)
                //         let endDate = row.endDate == null ? '-' : row.endDate.substring(0,10)

                //         return startDate + ' 至 ' + endDate
                //     }
                // },
                {
                    title:'审核人',
                    align:'left',
                    field:'checkUserName'
                },
                {
                    title:'审核时间',
                    align:'left',
                    field:'checkDate'
                },
                {
                    title:'创建时间',
                    align:'left',
                    field:'regDate'
                }
            ]
        };
        $.table.init(options);
    });



</script>
</body>
</html>