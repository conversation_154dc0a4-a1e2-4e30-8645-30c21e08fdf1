<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('获取指导价')"/>
</head>
<style>
    .border{
        border: 1px solid;
        border-radius: 5px;
        padding: 4px 6px;
    }
    .border+.border{
        margin-top: 10px;
    }
    .flex{
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .coef5{
        color:#ed5565
    }
    .fw{
        font-weight: bold;
    }
    .table>thead{
        background-color: #eff3f8;
    }
    .table>tbody>tr>td{
        padding: 5px 10px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-invoice-guidePrice" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="ids" th:value="${ids}">

        <table class="table table-bordered table-striped select-table">
            <thead>
                <tr>
                    <th>运单</th>
                    <th>车型</th>
                    <th>指导价</th>
                </tr>
            </thead>

            <tbody>
                <tr th:each="map,status:${lotReferencePrice}">
                    <td>
                        <div class="flex">
                            <span class="fw" th:text="${map.lotNo}"></span>

                            <span class="carve carve-primary" th:if="${map.transType=='0'}">公路整车</span>
                            <span class="carve carve-warning" th:if="${map.transType=='4'}">冷链整车</span>
                            <span class="carve carve-success" th:if="${map.transType=='15'}">危化整车</span>
                        </div>
                        <div class="mt5">
                            <span th:text="${map.deliProvinceName}"></span><span th:text="${map.deliCityName}"></span><span th:text="${map.deliAreaName}"></span>
                            <i class="fa fa-arrow-right" style="font-size:16px;color: #1ab394;margin: 0 10px;"></i>
                            <span th:text="${map.arriProvinceName}"></span><span th:text="${map.arriCityName}"></span><span th:text="${map.arriAreaName}"></span>
                        </div>
                    </td>
                    
                    <td>
                        <span th:text="${map.carLenName}"></span>米<span th:text="${map.carTypeName}"></span>
                    </td>
                    <td>
                        <div th:if="${map.isNeedReference == 1}">
                            <div class="coef5">[[${map.price == null ? '未查询到指导价':'￥' + map.price}]]</div>
                        </div>
                        <div th:if="${map.isNeedReference == 2}">
                            <div class="coef5">￥ [[${map.specialPrice}]]</div>
                        </div>
                        <div th:if="${map.isNeedReference == null || map.isNeedReference == '' || map.isNeedReference == 0}">
                            无需指导价
                        </div> 
                    </td>
                </tr>
            </tbody>
        </table>

    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "tms/segment";

    var data = [[${lotReferencePrice}]]

    function submitHandler() {
        var data = $("#form-invoice-guidePrice").serializeArray();
        $.operate.save(prefix + "/saveGuidePrice", data);
    }

</script>
</body>
</html>