<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('发货单')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .custom-select:required:invalid {
        color: gray !important;
    }
    .custom-select option[value=""][disabled] {
        display: none;
    }
    .custom-select option {
        color: black;
    }

    .label-successT {
        font-size: 18px;
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
        padding: 2px;
        vertical-align: middle;
    }

    .label-warningT {
        font-size: 18px;
        color: #f8ac59;
        background-color: transparent;
        border: 1px solid #f8ac59;
        padding: 2px;
        vertical-align: middle;

    }




    .my-table {
        border-collapse: collapse; /*将表格边框合并*/
        width: 100%; /*设置表格宽度*/
    }

    .my-table thead th {
        background-color: #f5f5f5; /*设置表头背景色*/
        font-size: 15px; /*设置表头字体大小*/
        font-weight: bold; /*设置表头字体加粗*/
        text-align: center; /*设置表头文本居中*/
        border-bottom: 2px solid #ddd; /*设置表头下方粗实线*/
        height: 40px;
    }

    .my-table tbody {
        border-bottom: 2px solid #ddd; /*设置tbody与tbody之间的细实线*/

    }

    .my-table tbody tr {
        border-bottom: none; /*取消tbody中每行之间的分隔线*/
    }

    .my-table tbody td {
        padding-inline: 1px;
        padding-block: 5px;
        border-right-style: none;
        border-left-style: none;
        border-top-style: none;
    }

    .my-table tbody:last-of-type {
        border-bottom: none; /*去掉最后一个tbody的下边框线*/
    }

    .fc80 {
        color: #808080;
    }

    .fcff {
        color: #ff1f1f;
    }

    .over {
        overflow: hidden;
    }

    .fl {
        float: left;
    }

    .fr {
        float: right;
    }

    .mt10 {
        margin-top: 10px;
    }

    .mt20 {
        margin-top: 20px;
    }

    .ml10 {
        margin-left: 10px;
    }

    .ml20 {
        margin-left: 20px;
    }

    .flex {
        display: flex;
        algin-items: center;
        just-content: space-between;
        color: #808080;
    }

    .flex_left {
        width: 100px;
        line-height: 30px;
        text-align: left;
        color: #808080;
    }

    .fcff.flex_left {
        color: #ff1f1f !important;
    }

    .flex_right {
        min-width: 0;
        flex: 1;
        line-height: 30px;
        text-align: left;
        color: #333333;
        /*line-height: 26px;*/
    }

    .addbtn {
        width: 120px;
        text-align: center;
        color: #fff;
        background: #1ab394;
        line-height: 30px;
        border-radius: 5px;
        cursor: pointer;
    }

    .addGoods {
        color: #1ab394;
        line-height: 30px;
        cursor: pointer;
    }

    .wap_content {
        padding: 10px 10px;
        border: 1px #eee solid;
        border-radius: 3px;

    }

    .line20 {
        line-height: 20px;
    }

    .line20T {
        display: inline-block;
        /* width: calc((95% -22px)/3); */
        vertical-align: middle;

        overflow: hidden;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        white-space: nowrap;
    }

    .fh_edit .line20T {
        text-decoration: line-through;
        color: #666666;
    }

    .toText {
        display: inline-block;
        padding: 2px 4px;
        border-radius: 5px;
        color: #fff;
        vertical-align: middle;
    }

    .toTextT {
        width: 21px;
        display: inline-block;
        text-align: right;
    }

    .fcff6 {
        color: #ff6c00;
    }

    .hisbtn {
        line-height: 25px;
        border-radius: 20px;
        width: 140px;
        text-align: center;
        color: #1ab394;
        border: 1px #1ab394 solid;
        cursor: pointer;
    }

    .eye {
        width: 20px;
        height: 20px;
        background: #1ab394 url("../../img/eyes.png") no-repeat center;
        background-size: 20px 20px;
        border-radius: 50%;
        /*display: inline-block;*/
    }

    .disin {
        display: inline-block;
    }

    .tc {
        text-align: center;
    }

    .fw {
        font-weight: bold;
    }

    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }

    .file-drop-zone-title {
        font-size: 13px;
    }

    .file-footer-buttons {
        border-left: 1px dashed #dadada;
    }

    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }

    .kv-upload-progress .progress {
        display: none;
    }

    .file-input-ajax-new .file-drop-zone-title {
        /*height: 80px;*/
    }

    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }

    .theme-explorer .file-preview .table tr {
        border-bottom: 1px #dadada dashed;
    }

    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242, 222, 222, 0.9);
        text-align: center;
        line-height: 70px;
    }

    .file-error-message button span {
        line-height: 70px;
    }

    .file-error-message li {
        text-align: center;
    }

    .panel-default > .panel-heading {
        font-weight: bold;
        background-color: #EBF5FC;
    }

    .panel-body {
        padding: 5px 10px 10px 10px;
    }

    .tooltips {
        /*position: relative;*/
        display: inline-block;
        /*border-bottom: 1px dotted black;*/
    }

    .tooltips .tooltiptext {
        visibility: hidden;

        width: 400px;
        background-color: black;
        color: #fff;
        border-radius: 6px;
        text-indent: 15px;
        padding: 5px 5px;
        line-height: 20px;
        /* 定位 */
        position: absolute;
        z-index: 9999;
    }

    .tooltips:hover .tooltiptext {
        visibility: visible;
    }

    .xh {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
        margin-top: 20px;
        background: #f1f2f6;
    }

    .fhf {
        border: 1px #eee solid;
        padding: 10px 10px;
        position: relative;
    }

    .shf {
        border: 1px #eee solid;
        padding: 10px 10px;
        position: relative;
    }

    .disf {
        display: flex;
    }

    .fh_addr {
        flex: 1;
        padding: 0 5px;
    }

    .fhf_table {
        /* background: #fefcd6; */
        padding: 10px 10px;
    }

    .fhf_table tr {
        background: #fff;
    }

    .fhf_close {
        position: absolute;
        left: -10px;
        top: -10px;
        width: 20px;
        height: 20px;
    }

    .texts {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        white-space: nowrap;
    }

    /*    .form-control,.input-group-addon,.table-bordered td, .table-bordered th,button[disabled], html input[disabled],.table>thead>tr>th,
        .panel,.panel-body,.table-bordered,.fhf{
            border-color: #A6A6A6 !important;
        }
        label{
            margin-bottom: 0;
        }
        .table>tbody>tr>td+.table>tbody>tr>td{
            border-left: 1px solid;
        }*/
    .form-control, .input-group-addon {
        border-radius: 4px;
    }

    .panel-group .panel {
        border-radius: 0;
    }

    .fa-times-circle:before {
        background-color: #ffffff;
    }

    .frT {
        display: inline-block;
        vertical-align: bottom;
        /* text-align: right; */
    }

    .nopa {
        padding: 0;
    }

    .nopa + .nopa {
        padding-left: 5px;
    }

    .checkbox {
        display: inline-block;
        vertical-align: middle;
    }

</style>
<body>
<div class="form-content">
    <form id="form-invoice-add" class="form-horizontal" novalidate="novalidate">
        <input id="historyGuidingPriceParam" type="hidden">
        <input id="historyGuidingPriceDetailParam" type="hidden">

        <div class="panel-group" id="accordion">
            <div class="row">
                <div class="col-md-12">
                    <!--                    发货单基础信息-->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapseOne">基础信息</a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <!--订单基础信息 begin-->
                                <div class="row">
                                    <!-- 客户名称-->
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left"> 客户名称：</label>
                                            <div class="flex_right" th:text="${detail.custAbbr}">

                                            </div>
                                        </div>
                                    </div>


                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left"> 项目名称：</label>
                                            <div class="flex_right" th:text="${detail.projectName}">

                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left"> 含税总金额：</label>
                                            <div class="flex_right" th:text="'￥' + ${detail.costAmount}">
                                            </div>
                                        </div>
                                    </div>


                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left"> 计价方式：</label>
                                            <div class="flex_right" th:each="billingMethod:${billingMethods}"
                                                 th:if="${detail.billingMethod}== ${billingMethod.value}"
                                                 th:text="${billingMethod.context}">
                                            </div>
                                        </div>
                                    </div>


                                </div>
                                <div class="row no-gutter">
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left"> 税率：</label>
                                            <div class="flex_right" th:each="dict : ${@dict.getType('billing_type')}"
                                                 th:if="${detail.billingType}== ${dict.dictValue}"
                                                 th:text="${dict.dictLabel}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left"> 预计毛利点：</label>
                                            <div class="flex_right" th:text="${detail.estimatedGrossMargin * 100} + '%'">
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <!--订单基础信息 end-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt10">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion" href="tabs_panels.html#collapsefour">详情信息</a>
                            </h4>
                        </div>
                        <div id="collapsefour" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-12 col-sm-12">
                                        <table class="my-table" id="inv_table" th:each="invoiceDetail : ${detail.invoiceDetailList}">
                                            <thead>
                                            <tr>
                                                <th style="width: 10%;">客户单号</th>
                                                <th style="width: 12%;">要求提货日期</th>
                                                <th style="width: 10%;">要求到货日期</th>
                                                <th style="width: 10%;">车型</th>
                                                <th style="width: 8%;">运输方式</th>
                                                <th style="width: 8%;">货品名称</th>
                                                <th style="width: 15%;">货量</th>
                                                <th style="width: 10%;">参考运价（元）</th>
                                                <th style="width: 14%;">调度组</th>
<!--                                                <th style="width: 10%;">备注</th>-->
                                            </tr>
                                            </thead>
                                            <tbody id="invoice_inv0">
                                            <tr>
                                                <td>
                                                    <div class="input-group">[[${invoiceDetail.custOrderno}]]</div>
                                                </td>
                                                <td>
                                                    <div class="input-group">[[${#dates.format(invoiceDetail.reqDeliDate, 'yyyy-MM-dd HH点')}]]</div>
                                                </td>
                                                <td>
                                                    <div class="input-group">[[${#dates.format(invoiceDetail.reqArriDate, 'yyyy-MM-dd')}]]</div>
                                                </td>
                                                <td>
                                                    <div th:text="${invoiceDetail.carLenName} + '米' +${invoiceDetail.carTypeName}">
                                                    </div>

                                                </td>
                                                <td>
                                                    <div class="input-group" th:each="dict : ${@dict.getType('trans_code')}"
                                                         th:if="${dict.dictValue} == ${invoiceDetail.transCode}">[[${dict.dictLabel}]]</div>
                                                </td>
                                                <td>
                                                    <div class="input-group">[[${invoiceDetail.goodsName}]]</div>
                                                </td>
                                                <td>
                                                    <div class="input-group">[[${invoiceDetail.numCount}]]件 | [[${invoiceDetail.weightCount}]]吨 | [[${invoiceDetail.volumeCount}]]m³</div>
                                                </td>
                                                <td>
                                                    <div class="input-group">[[${invoiceDetail.guidingPrice}]]</div>
                                                </td>
                                                <td>
                                                    <div class="input-group" th:each="dict : ${dispatcherDeptList}"
                                                         th:if="${dict.deptId} == ${invoiceDetail.transLineId}">[[${dict.deptName}]]</div>
                                                </td>
<!--                                                <td>-->
<!--                                                    <div class="input-group">[[${invoiceDetail.memo}]]</div>-->
<!--                                                </td>-->

                                            </tr>
                                            <tr>
                                                <td colspan="10">
                                                    <div class="input-group">备注：[[${invoiceDetail.memo}]]</div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="5">
                                                    <div>
                                                        <div class="">
                                                            <div class="col-md-12 col-sm-12">
                                                                <table style="border: 1px solid #ccc" class="table table-striped">
                                                                    <thead>
                                                                    <tr>
                                                                        <th style="width: 20%;">提货方</th>
                                                                        <th style="width: 15%;">联系人</th>
                                                                        <th style="width: 40%;">地址</th>
                                                                        <th style="width: 25%;">货量</th>
                                                                    </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                    <tr th:each="address : ${invoiceDetail.addressVOList}"
                                                                        th:if="${address.addressType == 0}">
                                                                        <td>
                                                                            <div class="input-group">[[${address.addrName}]]</div>
                                                                        </td>
                                                                        <td>
                                                                            <div class="input-group">[[${address.contact}]]</div>
                                                                            <div class="input-group">[[${address.mobile}]]</div>
                                                                        </td>
                                                                        <td>
                                                                            <div class="input-group">
                                                                                [[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]]
                                                                            </div>
                                                                        </td>
                                                                        <td>
                                                                            <div class="input-group">
                                                                                [[${address.num}]]件 | [[${address.weight}]]吨 | [[${address.volume}]]m³
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td colspan="5">
                                                    <div class="">
                                                        <div class="col-md-12 col-sm-12">
                                                            <table style="border: 1px solid #ccc" class="table table-striped">
                                                                <thead>
                                                                <tr>
                                                                    <th style="width: 15%;">到货方</th>
                                                                    <th style="width: 15%;">联系人</th>
                                                                    <th style="width: 40%;">地址</th>
                                                                    <th style="width: 30%;">货量</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <tr th:each="address : ${invoiceDetail.addressVOList}"
                                                                    th:if="${address.addressType == 1}">
                                                                    <td>
                                                                        <div class="input-group">[[${address.addrName}]]</div>
                                                                    </td>
                                                                    <td>
                                                                        <div class="input-group">[[${address.contact}]]</div>
                                                                        <div class="input-group">[[${address.mobile}]]</div>
                                                                    </td>
                                                                    <td>
                                                                        <div class="input-group">
                                                                            [[${address.provinceName + address.cityName + address.areaName + address.detailAddr}]]
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <div class="input-group">
                                                                            [[${address.num}]]件 | [[${address.weight}]]吨 | [[${address.volume}]]m³

                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            </tbody>

                                        </table>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion" href="tabs_panels.html#collapseSix">附件</a>
                            </h4>
                        </div>
                        <div id="collapseSix" class="panel-collapse collapse in">
                            <div class="panel-body" style="padding: 15px 10px">
                                <div class="row no-gutter mt10">
                                    <div class="col-sm-12 col-md-6">

                                        <div class="flex">
                                            <label class="flex_left">附件：</label>
                                            <div class="flex_right">
                                                <span th:each="pic:${projectFiles}">
                                                    <img style="height:150px" modal="zoomImg" th:src="@{${pic.filePath}}"/>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div style="height: 50px;"></div>
        <div class="row">
            <div class="col-sm-offset-5 col-sm-10" style="background-color: #fff;">
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i
                        class="fa fa-reply-all"></i>关 闭
                </button>
            </div>
        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>

<script th:inline="javascript">
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapsefour').collapse('show');
        $('#collapseSix').collapse('hide');




    });





</script>
</body>
</html>