<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('发货单')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .popup{
        width: 100%;
        height: 100%;
        position: fixed;
        top: 0;
        left: 0;
        background: rgba(0, 0, 0, .4);
        display: none;
    }
    .none{

    }
    .popup_content {
        width: 600px;
        left: 50%;
        margin-left: -300px;
        background: #fff;
        top: 20%;
        position: fixed;
        border-radius: 7px;
    }
    .popup_text{
        height: 24px;
        line-height: 24px;
        color: #808080;
    }
    .closed{
        width: 15px;
        height: 15px;
        position: absolute;
        right: 5px;
        top: 5px;
        color: #808080;
    }
    .table-striped{
        height: auto;
    }
    .label-warningT{
        color: #f8ac59;
        background-color: transparent;
        border: 1px solid #f8ac59;
    }
    .label-dangerT{
        color: #ed5565;
        background-color: transparent;
        border: 1px solid #ed5565;
    }
    .label-successT{
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }

</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-2" >
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">客户发货单号：</label>-->
                            <div class="col-sm-12" >
                                <input name="custOrderno" id="custOrderno" placeholder="发货单编号" class="form-control" type="text"
                                       maxlength="30" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="projectName" id="projectName" placeholder="项目名称" class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <!--                        客户简称-->
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="custAbbr" id="custAbbr" placeholder="客户简称" class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="regUserName" id="regUserName" placeholder="创建人" class="form-control" type="text"
                                       maxlength="30" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <!-- 发货单状态-->
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">发货单状态：</label>-->
                            <div class="col-sm-12">
                                <select name="vbillstatus" placeholder="发货单状态" id="vbillstatus" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="发货单状态" multiple
                                        th:with="type=${invoiceStatusList}">
                                    <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                    </div>

                    <div class="col-sm-2">
<!--                        <div class="form-group">-->

<!--                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>-->
<!--                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>-->
<!--                        </div>-->
                    </div>

                </div>

                <div class="row no-gutter">
                    <div class="col-sm-4">
                        <div class="form-group">
                            <div class="">
                                <input type="text" placeholder="要求提货开始日" style="width: 45%; float: left;" class="form-control"
                                       id="regDateStart"  name="params[regDateStart]" autocomplete="off" >
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" placeholder="要求提货截止日" style="width: 45%; float: left;" class="form-control"
                                       id="regDateEnd"  name="params[regDateEnd]" autocomplete="off" >
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group">

                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" shiro:hasAnyPermissions="tms:invoice:project:add" onclick="addTab()">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-warning multiple disabled" shiro:hasAnyPermissions="tms:invoice:project:affirm" onclick="affirm()">
                <i class="fa fa-check-circle-o"></i> 确认
            </a>
            <!-- <a class="btn btn-primary single disabled" shiro:hasPermission="tms:invoice:fee_entry" onclick="feeEntry()">
                 <i class="fa fa-jpy"></i> 第三方费用录入
             </a>-->
            <!--            <a class="btn btn-primary single disabled"  shiro:hasPermission="tms:invoice:fee_apply" onclick="otherFeeApply()">-->
            <!--                <i class="fa fa-jpy"></i> 第三方应付费用-->
            <!--            </a>-->

            <!--            <a class="btn btn-warning multiple disabled" shiro:hasPermission="tms:invoice:fee_entry" onclick="otherFeeInput()">-->
            <!--                <i class="fa fa-jpy"></i> 批量第三方费用-->
            <!--            </a>-->
<!--            <a class="btn btn-primary multiple disabled" shiro:hasPermission="tms:invoice:print" onclick="invoicePrint()">-->
<!--                <i class="fa fa-object-group"></i> 托运单打印-->
<!--            </a>-->
            <!--            <a class="btn btn-primary multiple disabled" th:if="${!isFleet}" shiro:hasPermission="tms:invoice:getGuidePrice"-->
            <!--               onclick="getGuidePrice()">-->
            <!--                <i class="fa fa-dollar"></i> 获取指导价-->
            <!--            </a>-->

            <!--            <a class="btn btn-primary multiple disabled "  th:if="${isFleet}" onclick="checking()" shiro:hasPermission="finance:fleet:receive:checking">-->
            <!--                <i class="fa fa-file-text-o"></i> 生成对账单-->
            <!--            </a>-->

            <!--            <a class="btn btn-primary multiple disabled " th:if="${isFleet}" onclick="insertChecking()" shiro:hasPermission="finance:fleet:receive:insertChecking">-->
            <!--                <i class="fa fa-file-text-o"></i> 加入对账单-->
            <!--            </a>-->

            <!--            <a class="btn btn-info"  th:if="${isFleet}" onclick="adjustImport()" shiro:hasPermission="finance:fleet:receive:adjustImport">-->
            <!--                <i class="fa fa-upload"></i> 调整单导入-->
            <!--            </a>-->

            <!--            <a class="btn btn-warning"  onclick="adjustExport()" shiro:hasPermission="finance:fleet:receive:adjustExport">-->
            <!--                <i class="fa fa-download"></i> 调整单导出-->
            <!--            </a>-->
<!--            <a class="btn btn-warning"  onclick="invoiceExport()" shiro:hasPermission="tms:invoice:export">-->
<!--                <i class="fa fa-download"></i> 发货单导出-->
<!--            </a>-->

<!--            <a class="btn btn-info"  onclick="receiptExport()" shiro:hasPermission="tms:invoice:receiptExport">-->
<!--                <i class="fa fa-download"></i> 回单导出-->
<!--            </a>-->
            <a class="btn btn-danger single disabled" shiro:hasAnyPermissions="tms:invoice:project:back_confirm" onclick="oppositeAffirm()">
                <i class="fa fa-reply"></i> 反确认
            </a>
<!--            <a class="btn btn-danger single disabled" shiro:hasAnyPermissions="tms:invoice:close,tms:fleet:invoice:close" onclick="invoiceClose()">-->
<!--                <i class="fa fa-remove"></i> 关闭-->
<!--            </a>-->

            <a class="btn btn-danger multiple disabled" shiro:hasAnyPermissions="tms:invoice:project:remove" onclick="removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>
            <!--            <a class="btn btn-primary" th:if="${isFleet}"  onclick="adjustRecord()" shiro:hasPermission="tms:fleet:invoice:adjustRecord">
                            <i class="fa fa-newspaper-o"></i> 调整单记录
                        </a>-->

<!--            <a class="btn btn-info" shiro:hasAnyPermissions="tms:invoice:add,tms:fleet:invoice:add" th:if="${!isFleet}" onclick="importInvoice()">-->
<!--                <i class="fa fa-upload"></i> 发货单导入-->
<!--            </a>-->

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
    <div class="popup">
        <div class="popup_content">
            <div style="position: relative;padding: 20px 20px">
                <div class="popup_text">原始发货单号：<nobr class="one"></nobr></div>
                <div class="popup_text ">原始运段号：<nobr class="two"></nobr></div>
                <div class="popup_text ">原始委托单号：<nobr class="three"></nobr></div>
                <div class="popup_text ">原始运单号：<nobr class="four"></nobr></div>
                <div class="closed">
                    <i class="glyphicon glyphicon-remove"></i>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/js/xlsx.full.min.js}"></script>

<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a th:href="@{/file/ReceiveAdjustModel.xlsx}" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
<script id="importTpl2" type="text/template">
    <div class="col-xs-offset-1">
        <a href="javascript:$('#ipt_file').click()" class="mt10" style="display: inline-block">选择文件</a> <span id="fileMsg"></span>
        <div id="ipt_file_div" style="display: none">
            <input type="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" id="ipt_file" onchange="readExcel()"/>
        </div>
        <div class="mt10 pt5">
            导入模板 ：
            &nbsp;	<a th:href="@{/file/invoiceTemplate.xlsx}" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
        </div>

        <div class="mt10" style="color:red">
            提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>

        <div id="templateMsg" class="mt10" style="font-weight: bold;color:blue"></div>
    </div>
</script>

<script th:inline="javascript">
    //是否是车队
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/invoice/project" : ctx + "invoice/project";

    var vbillstatus = [[${invoiceStatusList}]];
    var carLen = [[${@dict.getType('car_len')}]];
    var carType = [[${@dict.getType('car_type')}]];
    var transCode = [[${@dict.getType('trans_code')}]];
    var CloseAccountList = [[${CloseAccountList}]];//关账记录
    //发货单状态map
    var invoiceStatusMap = [[${invoiceStatusMap}]];
    var changeFlag = false;

    var billingMethod=[[${billingMethod}]];


    $(function () {
        //
        $("#regDateStart").val(getFrontFormatDate());

        let v = [[${vbillstatus}]]
        $("#vbillstatus").val([v]);

        $("#vbillstatus").selectpicker('refresh');

        let ctf = [[${receivableWriteOffStatusList}]]
        if (ctf != null && ctf != '') {
            var a = [];
            let ctfList = ctf.split(',');
            for(var i =0 ;i<ctfList.length;i++){
                a.push(ctfList[i]);
            }
            $('#receivableWriteOffStatusList').selectpicker('val',a);
        }


        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });

        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail/{id}",
            updateUrl: prefix + "/edit/{id}/edit",
            removeUrl: prefix + "/remove",
            showToggle:false,
            showColumns:true,
            modalName: "项目制发货单",
            fixedColumns: false,
            fixedNumber: 1,
            height: 560,
            uniqueId: "invoiceId",
            clickToSelect: true,
            firstLoad: false,
            exportTypes:['excel','csv'],
            showExport: true,
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"在途跟踪"
            },
            columns: [
                {
                    checkbox: true
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 20,
                    switchable:false,
                    formatter: function(value, row, index) {
                        var actions = [];

                        if ([[${@permission.hasAnyPermi('tms:invoice:project:edit')}]] != "hidden"
                            && ((row.isFleetData === '1' && row.isFleetAssign !== '1') || row.isFleetData === '0')
                            && invoiceStatusMap.NEW == row.vbillstatus) {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  onclick="edit(\'' + row.invoiceId + '\',\''+row.vbillstatus+'\',\''+row.regDate+'\')" title="修改"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>');
                        }
                     /*   if ([[${@permission.hasAnyPermi('tms:invoice:copy,tms:fleet:invoice:copy')}]] != "hidden" && ((row.isFleetData === '1' && row.isFleetAssign !== '1') || row.isFleetData === '0')) {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" onclick="copyTab(\''+ row.invoiceId +'\')" title="复制"><i class="fa fa-copy" style="font-size: 15px;"></i></a>');
                        }*/
                        if ([[${@permission.hasAnyPermi('tms:invoice:project:detail')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="明细" onclick="$.operate.detailTab(\'' + row.invoiceId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        }
                        // if ([[${@permission.hasPermi('tms:invoice:cost_detail')}]] != "hidden") {
                        //     actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="费用明细" onclick="costDetail(\'' + row.Id + '\')"><i class="fa fa-cny" style="font-size: 15px;"></i></a>');
                        // }


                        if ([[${@permission.hasPermi('finance:receive:receiveDetail')}]] != "hidden"
                            && invoiceStatusMap.NEW != row.vbillstatus) {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="receiveDetail(\'' + row.invoiceId + '\',\'' + row.isFleetAssign +'\')"><i  class="fa fa-cny" style="font-size: 15px;" ></i></a>');
                        }

                        // if ([[${@permission.hasPermi('tms:invoice:operation_history')}]] != "hidden") {
                        //     actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="操作历史" onclick="operationHistory(\'' + row.invoiceId + '\')"><i  class="fa fa-list" style="font-size: 15px;" ></i></a>');
                        // }
                        return actions.join('');
                    }
                },
                {
                    title: '项目名称',
                    field: 'projectName',
                    align: 'left',
                },
                {
                    title: '发货单编号',
                    field: 'vbillno',
                    align: 'left',
                    width: 20,
                    switchable: false,
                    formatter: function (value,row) {
                        var context;
                        if (row.isFleetData === '1' && row.isFleetAssign === '1') {
                            let bizInvoiceVbillno = row.bizInvoiceVbillno
                            let bizSegmentVbillno = row.bizSegmentVbillno
                            let bizEntrustVbillno = row.bizEntrustVbillno
                            let bizEntrustLotVbillno = row.bizEntrustLotVbillno

                            context = '<span class="label label-primary" onclick="getDetail(\'' + bizInvoiceVbillno + '\',\''+bizSegmentVbillno+'\',\''+bizEntrustVbillno+'\',\''+bizEntrustLotVbillno+'\')">'+value+'</span>';
                        }else {
                            context = value;
                        }
                        return context;
                    }
                },
                {
                    title: '发货单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    width: 20,
                    switchable: false,
                    formatter: function status(row,value) {
                        var context = '';
                        var isAddReceCheck = '';
                        if (isFleet && value.isFleetAssign == '0' && value.isAddReceCheck === '1') {
                            isAddReceCheck = '（已对账）';
                        }

                        let status ='';
                        switch (value.receivableWriteOffStatus){
                            case '0':
                                status = '<span class="label label-dangerT" style="padding:1px;vertical-align: middle;margin-left:5px;" data-toggle="tooltip" data-placement="left" data-html="true" title="应收未核销">未</span>';
                                break;
                            case '1':
                                status = '<span class="label label-warningT" style="padding:1px;vertical-align: middle;margin-left:5px;" data-toggle="tooltip" data-placement="left" data-html="true" title="应收部分核销">部分</span>';
                                break;
                            case '2':
                                status = '<span class="label label-successT" style="padding:1px;vertical-align: middle;margin-left:5px;" data-toggle="tooltip"  data-placement="left" data-html="true" title="应收已核销">已</span>';
                                break;
                        }


                        vbillstatus.forEach(function (v) {
                            if (v.value == value.vbillstatus) {

                                if (value.vbillstatus == invoiceStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-primary">'+v.context+isAddReceCheck+'</span>';
                                }else if (value.vbillstatus == invoiceStatusMap.AFFIRM) {
                                    //已确认
                                    context = '<span class="label label-warning">'+v.context+isAddReceCheck+'</span>';
                                }else if (value.vbillstatus == invoiceStatusMap.PORTION_PICK_UP
                                    || value.vbillstatus == invoiceStatusMap.PICK_UP) {
                                    //部分提货 与 已提货
                                    context = '<span class="label label-info">'+v.context+isAddReceCheck+'</span>';
                                }else if (value.vbillstatus == invoiceStatusMap.PORTION_ARRIVALS
                                    || value.vbillstatus == invoiceStatusMap.ARRIVALS) {
                                    //部分到货  已到货
                                    context = '<span class="label label-success">'+v.context+isAddReceCheck+'</span>';
                                }else if (value.vbillstatus == invoiceStatusMap.PORTION_RETURNS
                                    || value.vbillstatus == invoiceStatusMap.RETURNS) {
                                    //部分回单  已回单
                                    context = '<span class="label label-primary">' + v.context+isAddReceCheck + '</span>';
                                } else {
                                    //关闭
                                    context = '<span class="label label-inverse">' + v.context+isAddReceCheck + '</span>';
                                }

                                return false;
                            }
                        });

                        // status = '<div style="padding-left: 5px;">' + status + '</div>'
                        return context ;
                    }
                },

                {
                    title: '客户简称',
                    field: 'custAbbr',
                    align: 'left',
                    width: 20,
                    switchable: false,
                    formatter: function status(value,row) {
                        if (row.bizCustAbbr != null) {
                            return value + '-' + row.bizCustAbbr;
                        }else {
                            return value;
                        }
                    }
                },
                {
                    title: '总运费(元)',
                    field: 'costAmount',
                    align: 'right',
                },
                {
                    title: '计费方式',
                    align: 'left',
                    field : 'billingMethod',
                    formatter: function(value, row, index){
                        let htmlText="";
                        billingMethod.forEach(res=>{
                            if(res.value==value){
                                htmlText=res.context;
                            }
                        })
                        return htmlText;
                    }
                },

                {
                    title: '创建人',
                    field: 'regUserName',
                    align: 'left',
                    formatter: function(value, row, index){
                        return value + '<br>' + row.regDate
                    }
                },
            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });

                $('[data-toggle="tooltip"]').tooltip()
            }
        };
        $.table.init(options);
        searchPre();

    });

    /**
     * 日期插件
     */
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        var regDateStart = laydate.render({
            elem: '#regDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var regDateEnd = laydate.render({
            elem: '#regDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqArriDateStart = laydate.render({
            elem: '#reqArriDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqArriDateEnd = laydate.render({
            elem: '#reqArriDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
    });

    //删除
    function removeAll() {
        /*var regDate = $.table.selectColumns("regDate");
        for (var i = 0; i < regDate.length ; i++) {
            for(var j=0 ; j< CloseAccountList.length ; j++ ){
                if(regDate[i].substring(0,7)==CloseAccountList[j].yearMonth.substring(0,7)){
                    $.modal.alertWarning("该月份已关账，无法进行操作！");
                    return false;
                }
            }
        }*/

        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        //判断发货单是否是新建状态
        var vbillstatusList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["vbillstatus"];
        });
        var b = false;
        $.each(vbillstatusList, function (i, v) {
            if (v != 0) {
                b = true;
                return false;
            }
        });
        if (b) {
            $.modal.alertWarning("只有新建状态下才能删除！");
            return;
        }

        /*if (isFleet) {
            //验证发货单是否超过五天
            $.ajax({
                type: "POST",
                url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+rows.join(),
                async: false,
                success: function(r){
                    if(r.code != 0){
                        $.modal.alertError(r.msg);
                        return false;
                    }else{
                        $.modal.confirm("确认要删除选中的" + rows.length + "条数据吗?", function() {
                            var url = $.table._option.removeUrl;
                            var data = { "ids": rows.join() };
                            $.operate.submit(url, "post", "json", data);
                        });
                    }
                }
            });
        }else {}*/
        $.modal.confirm("确认要删除选中的" + rows.length + "条数据吗?", function() {
            var url = $.table._option.removeUrl;
            var data = { "ids": rows.join() };
            $.operate.submit(url, "post", "json", data);
        });


    }

    // 添加信息，以tab页展现
    function addTab() {
        var date = new Date();
        var now = date .getFullYear() + "-" +(date .getMonth()+1);
        //关账判断
        for(var j=0 ; j< CloseAccountList.length ; j++ ){
            if(now==CloseAccountList[j].yearMonth.substring(0,7)){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }


        $.modal.openTab("添加" + $.table._option.modalName, prefix + "/add" );
    }

    /**
     * 拷贝发货单
     * @param invoiceId 发货单单号
     */
    function copyTab(invoiceId) {
        /*var date = new Date();
        var now = date .getFullYear() + "-" +(date .getMonth()+1)
        //关账判断
        for(var j=0 ; j< CloseAccountList.length ; j++ ){
            if(now==CloseAccountList[j].yearMonth.substring(0,7)){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }*/

        $.modal.openTab("拷贝" + $.table._option.modalName, prefix + "/edit/" + invoiceId +"/copy");
    }

    /**
     * 修改发货单
     */
    function edit(invoiceId, vbillstatus,regDate) {
        //关账判断
        if (checkCloseAccount()) {
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }

        if (vbillstatus != 0) {
            $.modal.alertWarning("只有新建状态下的发货单才能修改！");
            return;
        }

        $.modal.openTab("修改" + $.table._option.modalName, prefix + "/edit/" + invoiceId + "/edit");
    }

    /**
     * 确认发货单
     */
    function affirm() {
        //关账校验
        /*if (checkCloseAccount()) {
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }*/

        var rows = $.table.selectColumns("invoiceId");
        if (rows.length == 0) {
            $.modal.alertWarning("请选择确认的数据");
            return;
        }

        //判断发货单是否是新建状态
        var vbillstatusList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["vbillstatus"];
        });
        var b = false;
        $.each(vbillstatusList, function (i, v) {
            if (v != 0) {
                b = true;
                return false;
            }
        });
        if (b) {
            $.modal.alertWarning("只有新建状态下才能确认！");
            return;
        }

        var invoiceId = $.table.selectColumns($.table._option.uniqueId)+"";

        $.modal.confirm("确定确认该发货单吗？", function () {
            $.operate.post(prefix + "/affirm", { "invoiceIds": invoiceId });
        });

        //验证发货单是否超过五天
        /*$.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.confirm("确定确认该发货单吗？", function () {
                        $.operate.post(prefix + "/affirm", { "invoiceId": invoiceId });
                    });
                }
            }
        });*/


    }

    /**
     * 跳转反确认页面
     */
    function oppositeAffirm() {
        //关账校验
        if (checkCloseAccount()) {
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }

        var rows = $.table.selectColumns("invoiceId");
        if (rows.length == 0) {
            $.modal.alertWarning("请选择反确认的数据");
            return;
        }

        //判断发货单已确认状态
        var vbillstatusList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["vbillstatus"];
        });
        var b = false;
        $.each(vbillstatusList, function (i, v) {
            if (v != 1) {
                b = true;
                return false;
            }
        });
        if (b) {
            $.modal.alertWarning("只有已确认状态下才能反确认！");
            return;
        }

        var selectColumns = $.table.selectColumns($.table._option.uniqueId);

        //验证发货单是否超过五天
        $.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+selectColumns,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.open("反确认", ctx + "invoice/back_confirm/" + selectColumns + "/0",600,500);
                    //$.modal.open("发货单反确认", ctx + "trustDeed/back_confirm_pick/" + selectColumns + "/0",600,500);
                }
            }
        });
    }

    /**
     * 发货单关闭
     *
     */
    function invoiceClose() {
        //关账校验
        /* if (checkCloseAccount()) {
             $.modal.alertWarning("该月份已关账，无法进行操作！");
             return false;
         }

         //判断发货单是否是新建状态
         var vbillstatusList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
             return row["vbillstatus"];
         });

         var b = false;
         $.each(vbillstatusList, function (i, v) {
             if (v != 0) {
                 b = true;
                 return false;
             }
         });
         if (b) {
             $.modal.alertWarning("只有新建状态下才能关闭！");
             return;
         }
 */

        var invoiceId =  $.table.selectColumns("invoiceId").join();
        //验证是否可以货量更新
        $.ajax({
            type: "POST",
            url: ctx + "invoice/checkClose?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.open("关闭", ctx + "invoice/close/" + invoiceId,500,225);
                }
            }
        });


    }

    /**
     * 第三方费用录入
     */
    function feeEntry() {
        //关账校验
        if (checkCloseAccount()) {
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }
        var id = $.table.selectColumns($.table._option.uniqueId);

        layer.open({
            type: 2,
            area: ['60%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "第三方费用录入",
            content: ctx + "invoice/feeEntry/" + id,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function(index) {
                return true;
            }
        });

    }


    /**
     * 第三方费用申请
     */
    function otherFeeApply() {
        //关账校验
        /* if (checkCloseAccount()) {
             $.modal.alertWarning("该月份已关账，无法进行操作！");
             return false;
         }*/
        var id = $.table.selectColumns($.table._option.uniqueId);
        var isClose = $.table.selectColumns("isClose");

        $.modal.openTab("第三方费用申请", ctx + "invoice/fee_apply/" + id+"/"+isClose);
    }

    /**
     * 关账校验
     */
    function checkCloseAccount() {
        var b = false;
        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for(var i=0 ; i< isClose.length ; i++ ){
            if(isClose[i] == 1){
                b = true;
                return true;
            }
        }
        return b;
    }

    /**
     * 发票打印
     */
    function invoicePrint(){
        var vbillno = $.table.selectColumns("vbillno").join();
        var url = ctx + "report/invoicePrint/"+vbillno;
        $.modal.openTab("托运单打印", url);
    }

    /**
     * 搜索
     */
    function searchPre() {

        var data = {};
        data.params = new Map();

        data.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));

        $.table.search('role-form', data);
    }

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        var deliDetailAddr= $('#deliDetailAddr').val()
        var arriDetailAddr= $('#arriDetailAddr').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        $('#deliDetailAddr').val(arriDetailAddr)
        $('#arriDetailAddr').val(deliDetailAddr)
        searchPre()
    }

    /**
     * 重置
     */
    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    //发货单所有费用明细
    function costDetail(id) {
        var url = ctx + "invoice/cost_detail";
        $.modal.openTab("发货单费用明细", url);
    }

    /**
     * 获取指导价
     */
    // function getGuidePrice() {
    //     var ids = $.table.selectColumns($.table._option.uniqueId);
    //     var url = ctx + "invoice/getGuidePrice/" + ids;
    //     $.modal.open("获取指导价", url);
    // }



    /**
     * 详情
     */
    function receiveDetail(invoiceId,isFleetAssign) {
        var ifa = isFleetAssign === "1" ? true : false

        var url = ctx + "receive/receive_detail?invoiceId=" + invoiceId + "&isFleet=true&isFleetAssign=" + ifa;
        $.modal.openTab('应收详情',url);
    }


    /**
     * 操作历史记录
     */
    function operationHistory(invoiceId) {
        // var ifa = isFleetAssign === "1" ? true : false

        var url = ctx + "invoice/operation_history/" + invoiceId;
        $.modal.openTab('操作记录',url);
    }
    /**
     * 生成对账单的方法
     */
    function checking() {
        var rows =  $.table.selectColumns("invoiceId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        let b = bootstrapTable.some(function(item) {
            if (item.isFleetAssign == "1") {
                return true;
            }
        })
        if (b) {
            $.modal.alertWarning("请选择自添加数据！分配车队数据无法生成！");
            return;
        }

        if (rows.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        //判断get请求长度
        var mb = myBrowser();
        if ("IE" == mb) {
            if(rows.join().length>2000){
                $.modal.alertWarning("勾选数量超出限制，IE最大请求长度为2000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }
        if ("Chrome" == mb) {
            if(rows.join().length>8000){
                $.modal.alertWarning("勾选数量超出限制，Chrome最大请求长度为8000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }

        $.ajax({
            type: "post",
            dataType: "json",
            data:  {invoiceIds : rows.join()},
            url:  ctx +"receive/checkAddByInvoiceId",
            success: function(result) {
                if (!result.data) {
                    $.modal.alertWarning(result.msg);
                    return;
                }

                $.modal.openTab("生成对账单", ctx + "receive/checking?invoiceId=" + rows.join() + "&receiveDetsailIds=");
            }
        });
    }



    /**
     * 加入对账单的方法
     */
    function insertChecking() {
        var rows =  $.table.selectColumns("invoiceId");

        var hasApplyLen = 0;
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        let b = bootstrapTable.some(function(item) {
            if (item.isFleetAssign == "1") {
                return true;
            }
        })
        if (b) {
            $.modal.alertWarning("请选择自添加数据！分配车队数据无法生成！");
            return;
        }

        if (rows.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        //判断get请求长度
        var mb = myBrowser();
        if ("IE" == mb) {
            if(rows.join()>2000){
                $.modal.alertWarning("勾选数量超出限制，IE最大请求长度为2000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }
        if ("Chrome" == mb) {
            if(rows.join()>8000){
                $.modal.alertWarning("勾选数量超出限制，Chrome最大请求长度为8000，当前请求长度为:"+rows.join().length);
                return false;
            }
        }

        $.ajax({
            type: "post",
            dataType: "json",
            data:  {invoiceIds : rows.join()},
            url:  ctx +"receive/checkAddByInvoiceId",
            success: function(result) {
                if (!result.data) {
                    $.modal.alertWarning(result.msg);
                    return;
                }

                var customerId =  bootstrapTable[0]["customerId"];
                var getStr = ctx + "receive/insertChecking?customerId="+customerId+"&invoiceIds="+rows+"&receiveDetsailIds="

                $.modal.open("加入对账单", getStr);
            }
        });

    }

    /**
     * 调整单导入
     */
    function adjustImport() {
        layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入调整单数据',
            content: $('#importTpl').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var index = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#file')[0].files[0]);
                formData.append("updateSupport", $("input[name='updateSupport']").is(':checked'));
                $.ajax({
                    url: ctx + "receive/adjustImport",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code == web_status.WARNING) {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });

    }

    function invoiceExport() {
        var data = $("#role-form").serializeArray();

        $.modal.confirm("确定导出所有发货单数据吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(ctx + "invoice/export", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    /**
     *
     */
    function adjustExport(){
        var data = $("#role-form").serializeArray();
        var invoiceStatusList = {};
        invoiceStatusList.name = "invoiceStatus"
        invoiceStatusList.value = $.common.join($('#vbillstatus').selectpicker('val'));
        data.push(invoiceStatusList);
        //
        data.push({
            name: "params[deliProvinceId]",
            value: $("#deliProvinceId").val()
        })
        data.push({
            name: "params[deliCityId]",
            value: $("#deliCityId").val()
        })
        data.push({
            name: "params[deliAreaId]",
            value: $("#deliAreaId").val()
        })
        data.push({
            name: "params[deliDetailAddr]",
            value: $("#deliDetailAddr").val()
        })
        data.push({
            name: "params[arriProvinceId]",
            value: $("#arriProvinceId").val()
        })
        data.push({
            name: "params[arriCityId]",
            value: $("#arriCityId").val()
        })
        data.push({
            name: "params[arriAreaId]",
            value: $("#arriAreaId").val()
        })
        data.push({
            name: "params[arriDetailAddr]",
            value: $("#arriDetailAddr").val()
        })

        $.modal.confirm("确定导出所有" +
            $.table._option.modalName + "吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(ctx + "receive/exportAdjust", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    function receiptExport() {
        var data = $("#role-form").serializeArray();
        var obj = {}
        for (let i = 0; i < data.length; i++) {
            obj[data[i].name] = data[i].value;
        }
        obj.carLen = $.common.join($('#carLen').selectpicker('val'));
        obj.carType = $.common.join($('#carType').selectpicker('val'));
        obj.transCode = $.common.join($('#transCode').selectpicker('val'));
        obj.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));
        obj.salesDept = $.common.join($('#salesDept').selectpicker('val'));
        var errMsg = []
        if (!obj['params[reqDeliDateStart]']) {
            errMsg.push("请选择要求提货开始日期");
        }
        if (!obj['params[reqDeliDateEnd]']) {
            errMsg.push("请选择要求提货截止日期");
        }
        if (errMsg.length > 0) {
            $.modal.msgError(errMsg.join("<br>"))
            return
        }
        var t = obj['params[reqDeliDateStart]'].split("-");
        var start = new Date(t[0], t[1] - 1, t[2], 0, 0, 0, 0);
        console.log(start)
        t = obj['params[reqDeliDateEnd]'].split("-");
        var end = new Date(t[0], t[1] - 1, t[2], 0, 0, 0, 0);
        console.log(end)
        $.modal.confirm("确定导出回单吗？", function() {
            // $.modal.loading("正在导出数据，请稍后...");
            $("#receiptForm").remove();
            //$("[name='receiptFormTarget']").remove();
            $("body").append("<form id='receiptForm' method='post' action='"+ctx + "invoice/exportReceipt"+"' target='_blank' style='display:none'></form>");
            for (var k in obj) {
                if (obj[k] != null) {
                    $("#receiptForm").append("<input name='" + k + "' value='" + obj[k] + "' />");
                }
            }
            //$("body").append("<iframe name='receiptFormTarget' style='display:none'></iframe>");
            $("#receiptForm").submit()
        });
    }

    function myBrowser(){
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isOpera = userAgent.indexOf("Opera") > -1;
        if (isOpera) {
            return "Opera"
        }; //判断是否Opera浏览器
        if (userAgent.indexOf("Firefox") > -1) {
            return "FF";
        } //判断是否Firefox浏览器
        if (userAgent.indexOf("Chrome") > -1){
            return "Chrome";
        }
        if (userAgent.indexOf("Safari") > -1) {
            return "Safari";
        } //判断是否Safari浏览器
        if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
            return "IE";
        }; //判断是否IE浏览器
    }

    function getDetail(bizInvoiceVbillno,bizSegmentVbillno,bizEntrustVbillno,bizEntrustLotVbillno){
        console.log(bizInvoiceVbillno)
        $(".popup").css("display",'block');
        $(".one").text(bizInvoiceVbillno);
        $(".two").text(bizSegmentVbillno);
        $(".three").text(bizEntrustVbillno);
        $(".four").text(bizEntrustLotVbillno);
    }

    $(".closed").click(function(){
        $(".popup").css("display",'none');
    });

    function otherFeeInput(){
        let invoiceIds = $.table.selectColumns("invoiceId");
        let customerIds = $.table.selectColumns("customerId");
        if(customerIds.length > 1){
            $.modal.alertWarning("只能选择相同客户");
            return;
        }

        let url = prefix + "/otherFeeInput?invoiceIds="+invoiceIds.join(",");
        $.modal.openTab("第三方费用录入",url);
    }

    function adjustRecord(){
        var url = ctx + "receive/adjustRecordCheck";
        $.modal.openTab('调整单记录',url);
    }

    function getFrontFormatDate() {
        var date = new Date();
        date.setDate(date.getDate()-30);
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() + 1 ;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate;


        return currentdate;
    }

    function formatTableUnit(value, row, index) {
        return {
            css: {
                "white-space": "nowrap",
                "text-overflow": "ellipsis",
                "overflow": "hidden",
                "max-width": "100px"
            }
        }

    }

    function changeProvinceSel(){
        if(changeFlag){
            $("#arriProMultiple").css('display', 'none')
            $("#arriPro").css('display', 'block')
            $("#arriProvinceIds").selectpicker('deselectAll');
        }else{
            $("#arriProMultiple").css('display', 'block')
            $("#arriPro").css('display', 'none')
            $("#arriProvinceId").val("");
            $("#arriCityId").val("");
            $("#arriAreaId").val("");
            $("#arriDetailAddr").val("");
        }
        changeFlag = !changeFlag;
    }

    function importInvoice() {
        layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入发货单',
            content: $('#importTpl2').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero){
                if (globalInvoiceList == null || globalInvoiceList.length == 0) {
                    $.modal.alertError("没有可导入的数据")
                    return;
                }
                $.modal.confirm("确认导入这" + globalInvoiceList.length + "条发货单数据吗？", function(){
                    var tt = $.modal.layerLoading("导入中，请稍候...");
                    $.ajax({
                        url: ctx + 'invoice/importBatch',
                        contentType: "application/json;charset=UTF-8",
                        data: JSON.stringify(globalInvoiceList),
                        type: 'POST',
                        dataType: 'json',
                        success: function (result) {
                            layer.close(tt)
                            if (result.code != 0) {
                                $.modal.alertError(result.msg);
                            } else {
                                $.table.refresh();
                                layer.close(index)
                            }
                        }
                    })
                })
            }
        });

    }
    var globalInvoiceList = null;
    function readExcel() {
        globalInvoiceList = null;
        $("#templateMsg").html("")
        var file = $('#ipt_file').val();
        if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
            $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
            return false;
        }
        $("#fileMsg").html(file.substring(file.lastIndexOf("\\") + 1));
        var tt = $.modal.layerLoading("加载中，请稍候...");
        var files = $('#ipt_file')[0].files;
        var fileReader = new FileReader();
        fileReader.onload = function(ev) {
            try {
                var data = ev.target.result
                var workbook = XLSX.read(data, {
                    type: 'binary'
                }) // 以二进制流方式读取得到整份excel表格对象

            } catch (e) {
                console.log('%O',e)
                layer.close(tt)
                $('#templateMsg').text('文件类型不正确:' + e.message);
                return;
            }
            // 表格的表格范围，可用于判断表头是否数量是否正确
            var fromTo = '';
            //console.log(workbook.Sheets)

            /*var fields_names = [客户简称	要求提货日期	要求到货日期	车长	车型	运输方式	紧急程度	调度组	开票类型	结算方式	到付应收金额
                发货方[单位名称    联系人 电话	货品名称	包装	件数	重量（吨）	体积（m³）	计价方式	单价	金额]
                收货方[单位名称    联系人 电话	货品名称	包装	件数	重量（吨）	体积（m³）	计价方式	单价	金额]
                指导价 ]*/
            var idx = 0;
            var sheetNames = workbook.SheetNames; // 工作表名称集合
            var sht = workbook.Sheets[sheetNames[0]]; // 这里我们只读取第一张sheet
            //console.log(sht)

            var result = XLSX.utils.sheet_to_json(sht, {
                header:[
                    'custAbbr','reqDeliDate','reqArriDate','carLenName','carTypeName','transName','urgentLevelName','transLineName','billingTypeName','balaTypeName','collectAmount','billingMethod','unitPrice','costAmount',
                    'start_addrName','start_contact','start_mobile','start_goodsName','start_pack','start_num','start_weight','start_volume','start_custOrderno',
                    'end_addrName','end_contact','end_mobile','end_goodsName','end_pack','end_num','end_weight','end_volume','end_custOrderno',
                    'guidingPrice'
                ], raw:false, range:2});

            if (result != null) {
                // TODO 多行按同客户合并成同一发货单
                let main_fields = ['custAbbr','reqDeliDate','reqArriDate','carLenName','carTypeName','transName','urgentLevelName','transLineName','billingTypeName','balaTypeName','collectAmount','billingMethod','unitPrice','costAmount']
                //let required_fields = ['custAbbr','reqDeliDate','reqArriDate','carLenName','carTypeName','transName','urgentLevelName','transLineName','billingTypeName','balaTypeName','collectAmount',
                //    'start_addrName','start_pack','start_weight','start_billingMethod','start_sum',
                //    'end_addrName','end_pack','end_weight','end_billingMethod','start_sum'
                //]
                // 将start、end开头的字段重新组合
                let contact_fields = ['addrName','contact','mobile']
                let goods_fields = ['goodsName','pack','num','weight','volume','custOrderno']
                let finalList = [];
                for (let i = 0; i < result.length; i++) {
                    /*for (let j = 0; j < required_fields.length; j++) {
                        if (!result[i][required_fields[j]]) {
                            $.modal.alertWarning("请将第" + (i + 1) + "条发货单的带*号数据补充完整!");
                            return;
                        }
                    }*/
                    // 检查主字段，填了一个就必须填所有，视为新发货单
                    var main_filled = 0; // 已填字段
                    for (let j = 0; j < main_fields.length; j++) {
                        var v = result[i][main_fields[j]];
                        if ($.trim(v)) {
                            main_filled++;
                            result[i][main_fields[j]] = $.trim(v);
                        }
                    }
                    var curInvoice = null;
                    if (main_filled == 0) {
                        if (finalList.length == 0) {
                            $.modal.alertWarning("请将第" + (i + 1) + "条发货单的带*号数据补充完整!");
                            layer.close(tt)
                            return;
                        }
                        curInvoice = finalList[finalList.length - 1];
                    } else {
                        if (main_filled == main_fields.length || (main_filled == main_fields.length - 1 && !result[i]['collectAmount'])) {
                            curInvoice = result[i];
                            curInvoice['start'] = [];
                            curInvoice['end'] = [];
                            finalList.push(curInvoice);
                        } else {
                            $.modal.alertWarning("请将第" + (i + 1) + "条发货单的带*号数据补充完整!");
                            layer.close(tt)
                            return;
                        }
                    }
                    var curStart = curInvoice['start']
                    var curEnd = curInvoice['end']
                    var start_contact_filled = 0;
                    for (let j = 0; j < contact_fields.length; j++) {
                        var v = result[i]['start_' + contact_fields[j]];
                        if ($.trim(v)) {
                            start_contact_filled++;
                            result[i]['start_' + contact_fields[j]] = $.trim(v);
                        }
                    }
                    var start_goods_filled = 0;
                    for (let j = 0; j < goods_fields.length; j++) {
                        var v = result[i]['start_' + goods_fields[j]];
                        if ($.trim(v)) {
                            start_goods_filled++;
                            result[i]['start_' + goods_fields[j]] = $.trim(v);
                        }
                    }
                    if (start_contact_filled == 0 && start_goods_filled == 0) {
                        // 不处理
                    } else {
                        var curStartItem = null;
                        if (start_contact_filled > 0) {
                            curStartItem = {}
                            curStartItem['goods'] = []
                            curStart.push(curStartItem);
                            for (let j = 0; j < contact_fields.length; j++) {
                                curStartItem[contact_fields[j]] = result[i]['start_' + contact_fields[j]];
                                delete result[i]['start_' + contact_fields[j]]
                            }
                        } else {
                            curStartItem = curStart[curStart.length - 1];
                        }
                        let cur_start_goods = curStartItem['goods']; // []
                        if (start_goods_filled > 0) {
                            var curStartGoodsItem = {}
                            cur_start_goods.push(curStartGoodsItem);
                            for (let j = 0; j < goods_fields.length; j++) {
                                curStartGoodsItem[goods_fields[j]] = result[i]['start_' + goods_fields[j]];
                                delete result[i]['start_' + goods_fields[j]]
                            }
                        }
                    }

                    var end_contact_filled = 0;
                    for (let j = 0; j < contact_fields.length; j++) {
                        var v = result[i]['end_' + contact_fields[j]];
                        if ($.trim(v)) {
                            end_contact_filled++;
                            result[i]['end_' + contact_fields[j]] = $.trim(v);
                        }
                    }
                    var end_goods_filled = 0;
                    for (let j = 0; j < goods_fields.length; j++) {
                        var v = result[i]['end_' + goods_fields[j]];
                        if ($.trim(v)) {
                            end_goods_filled++;
                            result[i]['end_' + goods_fields[j]] = $.trim(v);
                        }
                    }
                    if (end_contact_filled == 0 && end_goods_filled == 0) {
                        // 不处理
                    } else {
                        var curEndItem = null;
                        if (end_contact_filled > 0) {
                            curEndItem = {}
                            curEndItem['goods'] = []
                            curEnd.push(curEndItem);
                            for (let j = 0; j < contact_fields.length; j++) {
                                curEndItem[contact_fields[j]] = result[i]['end_' + contact_fields[j]];
                                delete result[i]['end_' + contact_fields[j]]
                            }
                        } else {
                            curEndItem = curEnd[curEnd.length - 1];
                        }
                        let cur_end_goods = curEndItem['goods']; // []
                        if (end_goods_filled > 0) {
                            var curEndGoodsItem = {}
                            cur_end_goods.push(curEndGoodsItem);
                            for (let j = 0; j < goods_fields.length; j++) {
                                curEndGoodsItem[goods_fields[j]] = result[i]['end_' + goods_fields[j]];
                                delete result[i]['end_' + goods_fields[j]]
                            }
                        }
                    }

                }
                //console.log(finalList, JSON.stringify(finalList))
                globalInvoiceList = finalList;
            }

            //console.log(globalInvoiceList)
            $("#templateMsg").html("检测到" + globalInvoiceList.length + "条发货单数据")

            //在控制台打印出来表格中的数据
            if (globalInvoiceList == null || globalInvoiceList.length == 0) {
                $.modal.alertError("未在第一个Sheet中找到数据")
                layer.close(tt)
                return;
            }
            layer.close(tt)
        };
        // 以二进制方式打开文件
        fileReader.readAsBinaryString(files[0]);
        $('#ipt_file').val("")
        $('#ipt_file').remove();
        $("#ipt_file_div").append('<input type="file"' +
            ' accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"' +
            ' id="ipt_file" onChange="readExcel()">')
    }

</script>
</body>
</html>