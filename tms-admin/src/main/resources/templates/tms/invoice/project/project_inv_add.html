<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('发货单')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>

    .label-successT {
        font-size: 18px;
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
        padding: 2px;
        vertical-align: middle;
    }

    .label-warningT {
        font-size: 18px;
        color: #f8ac59;
        background-color: transparent;
        border: 1px solid #f8ac59;
        padding: 2px;
        vertical-align: middle;

    }

    .eclipse {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
    }

    .btn-outline-custom {
        background-color: transparent;
        color: #337ab7;
        border-color: #337ab7;
    }

    .btn-outline-custom:hover {
        background-color: #337ab7;
        color: #fff;
    }

    .btn-outline-custom:focus {
        box-shadow: inset 0 0 0 2px #337ab7;
    }

    .my-table {
        border-collapse: collapse; /*将表格边框合并*/
        width: 100%; /*设置表格宽度*/
    }

    .my-table thead th {
        background-color: #f5f5f5; /*设置表头背景色*/
        font-size: 15px; /*设置表头字体大小*/
        font-weight: bold; /*设置表头字体加粗*/
        text-align: center; /*设置表头文本居中*/
        border-bottom: 2px solid #ddd; /*设置表头下方粗实线*/
        height: 40px;
    }

    .my-table tbody {
        border-bottom: 2px solid #ddd; /*设置tbody与tbody之间的细实线*/

    }

    .my-table tbody tr {
        border-bottom: none; /*取消tbody中每行之间的分隔线*/
    }

    .my-table tbody td {
        padding-inline: 1px;
        padding-block: 5px;
        border-right-style: none;
        border-left-style: none;
        border-top-style: none;
    }

    .my-table tbody:last-of-type {
        border-bottom: none; /*去掉最后一个tbody的下边框线*/
    }

    .fc80 {
        color: #808080;
    }

    .fcff {
        color: #ff1f1f;
    }

    .over {
        overflow: hidden;
    }

    .fl {
        float: left;
    }

    .fr {
        float: right;
    }

    .mt10 {
        margin-top: 10px;
    }

    .mt20 {
        margin-top: 20px;
    }

    .ml10 {
        margin-left: 10px;
    }

    .ml20 {
        margin-left: 20px;
    }

    .flex {
        display: flex;
        algin-items: center;
        just-content: space-between;
        color: #808080;
    }

    .flex_left {
        width: 100%;
        line-height: 26px;
        /* text-align: right; */
        color: #333333 !important;
    }

    .fcff.flex_left {
        color: #ff1f1f !important;
    }

    /* .input-group{
        display: block;
    } */
    .flex_right {
        width: 100%;
        /* flex:1; */
        /*line-height: 26px;*/
    }

    /*.row .form-group label[class*='col-'] {*/
    /*    text-align: right;*/
    /*    line-height: 30px;*/
    /*    margin-bottom: 0;*/
    /*}*/
    .addbtn {
        width: 120px;
        text-align: center;
        color: #fff;
        background: #1ab394;
        line-height: 30px;
        border-radius: 5px;
        cursor: pointer;
    }

    .addGoods {
        color: #1ab394;
        line-height: 30px;
        cursor: pointer;
    }

    .wap_content {
        padding: 10px 10px;
        border: 1px #eee solid;
        border-radius: 3px;

    }

    .line20 {
        line-height: 20px;
    }

    .line20T {
        display: inline-block;
        /* width: calc((95% -22px)/3); */
        vertical-align: middle;

        overflow: hidden;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        white-space: nowrap;
    }

    .fh_edit .line20T {
        text-decoration: line-through;
        color: #666666;
    }

    .toText {
        display: inline-block;
        padding: 2px 4px;
        border-radius: 5px;
        color: #fff;
        vertical-align: middle;
    }

    .toTextT {
        width: 21px;
        display: inline-block;
        text-align: right;
    }

    .fcff6 {
        color: #ff6c00;
    }

    .hisbtn {
        line-height: 25px;
        border-radius: 20px;
        width: 140px;
        text-align: center;
        color: #1ab394;
        border: 1px #1ab394 solid;
        cursor: pointer;
    }

    .eye {
        width: 20px;
        height: 20px;
        background: #1ab394 url("../../img/eyes.png") no-repeat center;
        background-size: 20px 20px;
        border-radius: 50%;
        /*display: inline-block;*/
    }

    .disin {
        display: inline-block;
    }

    .tc {
        text-align: center;
    }

    .fw {
        font-weight: bold;
    }

    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }

    .file-drop-zone-title {
        font-size: 13px;
    }

    .file-footer-buttons {
        border-left: 1px dashed #dadada;
    }

    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }

    .kv-upload-progress .progress {
        display: none;
    }

    .file-input-ajax-new .file-drop-zone-title {
        /*height: 80px;*/
    }

    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }

    .theme-explorer .file-preview .table tr {
        border-bottom: 1px #dadada dashed;
    }

    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242, 222, 222, 0.9);
        text-align: center;
        line-height: 70px;
    }

    .file-error-message button span {
        line-height: 70px;
    }

    .file-error-message li {
        text-align: center;
    }

    .panel-default > .panel-heading {
        font-weight: bold;
        background-color: #EBF5FC;
    }

    .panel-body {
        padding: 5px 10px 10px 10px;
    }

    .tooltips {
        /*position: relative;*/
        display: inline-block;
        /*border-bottom: 1px dotted black;*/
    }

    .tooltips .tooltiptext {
        visibility: hidden;

        width: 400px;
        background-color: black;
        color: #fff;
        border-radius: 6px;
        text-indent: 15px;
        padding: 5px 5px;
        line-height: 20px;
        /* 定位 */
        position: absolute;
        z-index: 9999;
    }

    .tooltips:hover .tooltiptext {
        visibility: visible;
    }

    .xh {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
        margin-top: 20px;
        background: #f1f2f6;
    }

    .fhf {
        border: 1px #eee solid;
        padding: 10px 10px;
        position: relative;
    }

    .shf {
        border: 1px #eee solid;
        padding: 10px 10px;
        position: relative;
    }

    .disf {
        display: flex;
    }

    .fh_addr {
        flex: 1;
        padding: 0 5px;
    }

    .fhf_table {
        /* background: #fefcd6; */
        padding: 10px 10px;
    }

    .fhf_table tr {
        background: #fff;
    }

    .fhf_close {
        position: absolute;
        left: -10px;
        top: -10px;
        width: 20px;
        height: 20px;
    }

    .texts {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        white-space: nowrap;
    }

    /*    .form-control,.input-group-addon,.table-bordered td, .table-bordered th,button[disabled], html input[disabled],.table>thead>tr>th,
        .panel,.panel-body,.table-bordered,.fhf{
            border-color: #A6A6A6 !important;
        }
        label{
            margin-bottom: 0;
        }
        .table>tbody>tr>td+.table>tbody>tr>td{
            border-left: 1px solid;
        }*/
    .form-control, .input-group-addon {
        border-radius: 4px;
    }

    .panel-group .panel {
        border-radius: 0;
    }

    .fa-times-circle:before {
        background-color: #ffffff;
    }

    .frT {
        display: inline-block;
        vertical-align: bottom;
        /* text-align: right; */
    }

    .nopa {
        padding: 0;
    }

    .nopa + .nopa {
        padding-left: 5px;
    }

    .checkbox {
        display: inline-block;
        vertical-align: middle;
    }

</style>
<body>
<div class="form-content">
    <form id="form-invoice-add" class="form-horizontal" novalidate="novalidate">
        <input id="historyGuidingPriceParam" type="hidden">
        <input id="historyGuidingPriceDetailParam" type="hidden">

        <div class="panel-group" id="accordion">
            <div class="row">
                <div class="col-md-12">
                    <!--                    发货单基础信息-->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapseOne">基础信息</a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <!--订单基础信息 begin-->
                                <div class="row no-gutter">
                                    <!-- 客户名称-->
                                    <div class="col-md-3 col-sm-3">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 客户名称：</label>
                                            <div class="flex_right">
                                                <!-- <div class="input-group"> -->
                                                <input name="custAbbr" id="custAbbr" type="text"
                                                       placeholder="请选择客户" class="form-control valid"
                                                       aria-required="true" required autocomplete="off">
                                                <input name="custName" id="custName" type="hidden">
                                                <input name="custCode" id="custCode" type="hidden">
                                                <input name="customerId" id="customerId" type="hidden">
                                                <input name="balaCorpId" id="balaCorpId" type="hidden">
                                                <input name="balaDept" id="balaDept" type="hidden">
                                                <input name="salesDept" id="salesDept" type="hidden">
                                                <input name="psndoc" id="psndoc" type="hidden">
                                                <input name="billingCorp" id="billingCorp" type="hidden">
                                                <input name="referenceRate" id="referenceRate" type="hidden">

                                                <input name="balaName" id="balaName" type="hidden">
                                                <input name="balaCode" id="balaCode" type="hidden">
                                                <input name="balaCustomerId" id="balaCustomerId" type="hidden">

                                                <!-- <span class="input-group-addon"><i class="glyphicon glyphicon-chevron-down"></i></span>
                                            </div> -->
                                                <div style="position: relative;top: -1px;">
                                                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                                </div>

                                            </div>
                                        </div>
                                    </div>


                                    <div class="col-md-3 col-sm-3">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 项目名称：</label>
                                            <div class="flex_right">
                                                <!-- <div class="input-group"> -->
                                                <input name="projectName" id="projectName" type="text"
                                                       placeholder="项目名称" class="form-control valid"
                                                       aria-required="true" required autocomplete="off">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3 col-sm-3">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 含税总金额：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <span class="input-group-addon">￥</span>
                                                    <input name="costAmount" id="costAmount" placeholder="含税总金额"
                                                           class="form-control"
                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                           type="text" autocomplete="off" required>
                                                    <i class="fa fa-question-circle" data-toggle="tooltip"
                                                       style="font-size: 20px;display: none;margin-left: 5px;"
                                                       data-html="true" data-container="body"
                                                       title=""></i>

                                                </div>
                                                <!--                                                <label style="display:none;top:0px" id="costAmount-error" class="error" for="unitPrice">这是必填字段</label>-->

                                            </div>
                                        </div>
                                    </div>


                                    <div class="col-md-3 col-sm-3">
                                        <div class="">
                                            <label class="flex_left" style="width: 8em;"><span class="fcff">*</span>
                                                计价方式：</label>
                                            <div class="flex_right">
                                                <select name="billingMethod" id="billingMethod"
                                                        class="form-control valid custom-select"
                                                        aria-invalid="false" onchange="" required>
                                                    <option value="" disabled selected hidden>计价方式</option>
                                                    <option th:each="billingMethod:${billingMethods}"
                                                            th:value="${billingMethod.value}"
                                                            th:text="${billingMethod.context}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>


                                </div>
                                <div class="row no-gutter">
                                    <div class="col-md-3 col-sm-3">
                                        <label class="flex_left" style="width: 9em;"><span class="fcff">*</span>
                                            税率：</label>
                                        <div class="flex_right">
                                            <select name="billingType" id="billingType" class="form-control valid custom-select"
                                                    th:with="type=${@dict.getType('billing_type')}"
                                                    aria-invalid="false" aria-required="true" required>
                                                <option value="" disabled selected hidden>税率</option>
                                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                        th:value="${dict.dictValue}"></option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-3">
                                        <div class="">
                                            <label class="flex_left"><span class="fcff">*</span> 预计毛利点：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <!-- <div class="input-group"> -->
                                                    <input name="estimatedGrossMargin" id="estimatedGrossMargin"
                                                           type="text"
                                                           placeholder="预计毛利点" class="form-control valid"
                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                           aria-required="true" required autocomplete="off">
                                                    <span class="input-group-addon">%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <!--订单基础信息 end-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt10">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion" href="tabs_panels.html#collapsefour">详情信息</a>
                            </h4>
                        </div>
                        <div id="collapsefour" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-12 col-sm-12">
                                        <table class="my-table" id="inv_table">
                                            <thead>
                                            <tr>
                                                <th style="width: 5%">
                                                    <a class="collapse-link" style="font-size: 22px;color: #1ab394;"
                                                       onclick="addInvoiceData()" title="新增行">+</a>
                                                </th>
                                                <th style="width: 8%;">客户单号</th>
                                                <th style="width: 12%;">要求提货日期</th>
                                                <th style="width: 10%;">要求到货日期</th>
                                                <th style="width: 5%;">车长</th>
                                                <th style="width: 8%;">车型</th>
                                                <th style="width: 8%;">运输方式</th>
                                                <th style="width: 8%;">货品名称</th>
                                                <th style="width: 5%;">件数</th>
                                                <th style="width: 5%;">重量（吨）</th>
                                                <th style="width: 5%;">体积（m³）</th>
                                                <th style="width: 9%;">参考运价（元）</th>
                                                <th style="width: 12%;">调度组</th>
                                            </tr>
                                            </thead>
                                            <tbody id="invoice_inv0">
                                            <tr>
                                                <td rowspan="2">
                                                    <div style="display: inline-block;">
                                                        <a
                                                                class="fa fa-times-circle"
                                                                style="color: #fd8481;font-size: 20px;"
                                                                onclick="rmInvoiceData(this)" title="删除选择行"></a>
                                                    </div>
                                                    <div style="display: inline-block;margin-left:5px">
                                                        <a class="fa fa-clone" style="color: #0092e7;font-size: 17px;"
                                                           onclick="cloneInvoiceData(0)" title="复制货品"></a>
                                                    </div>
                                                </td>

                                                <td>
                                                    <input name="invoiceDetailList[0].custOrderno" id="custOrderno_inv0"
                                                           placeholder="客户发货单号" class="form-control" type="text"
                                                           maxlength="50" autocomplete="off" oninput="">
                                                </td>
                                                <td>
                                                    <input name="invoiceDetailList[0].reqDeliDate" id="reqDeliDate_inv0"
                                                           type="text" class=" form-control" required
                                                           placeholder="要求提货日" autocomplete="off">
                                                </td>
                                                <td>
                                                    <input name="invoiceDetailList[0].reqArriDate" id="reqArriDate_inv0"
                                                           type="text" class=" form-control" required
                                                           placeholder="要求到货日" autocomplete="off">
                                                </td>
                                                <td>
                                                    <select name="invoiceDetailList[0].carLen" id="carLen_inv0"
                                                            onchange="getGuidingPrice(0);"
                                                            class="form-control valid custom-select"
                                                            aria-invalid="false"
                                                            th:with="type=${@dict.getType('car_len')}"
                                                            aria-required="true" required>
                                                        <option value="" disabled selected hidden>车长</option>
                                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                                th:value="${dict.dictValue}"></option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <select name="invoiceDetailList[0].carType" id="carType_inv0"
                                                            class="form-control valid custom-select" aria-invalid="false"
                                                            onchange="getGuidingPrice(0);"
                                                            th:with="type=${@dict.getType('car_type')}"
                                                            aria-required="true" required>
                                                        <option value="" disabled selected hidden>车型</option>
                                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                                th:value="${dict.dictValue}"></option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <select name="invoiceDetailList[0].transCode" id="transCode_inv0"
                                                            class="form-control valid custom-select" aria-invalid="false"
                                                            onchange="getGuidingPrice(0);"
                                                            th:with="type=${@dict.getType('trans_code')}"
                                                            aria-required="true" required>
                                                        <option value="" disabled selected hidden>运输方式</option>
                                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                                th:value="${dict.dictValue}"></option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input name="invoiceDetailList[0].goodsName" id="goodsName_inv0"
                                                           type="text" placeholder="货品名称"
                                                           class="form-control valid" autocomplete="off" required/>
                                                    <input name="invoiceDetailList[0].goodsCharacter"
                                                           id="goodsCharacter_inv0" type="hidden"/>
                                                    <input name="invoiceDetailList[0].goodsTypeName"
                                                           id="goodsTypeName_inv0" type="hidden" class="form-control"/>
                                                    <input name="invoiceDetailList[0].goodsType" id="goodsTypeId_inv0"
                                                           type="hidden"/>
                                                    <div style="position: relative;top: -1px;">
                                                        <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                                    </div>
                                                </td>
                                                <td>
                                                    <input name="invoiceDetailList[0].numCount" id="numCount_inv0"
                                                           placeholder="件数" class="form-control"
                                                           type="text" oninput="$.numberUtil.onlyNumber(this);" required disabled
                                                           autocomplete="off">
                                                </td>
                                                <td>
                                                    <input name="invoiceDetailList[0].weightCount" id="weightCount_inv0"
                                                           placeholder="重量" class="form-control"
                                                           type="text" oninput="$.numberUtil.onlyNumber(this);" required disabled
                                                           autocomplete="off">
                                                </td>
                                                <td>
                                                    <input name="invoiceDetailList[0].volumeCount" id="volumeCount_inv0"
                                                           placeholder="体积" class="form-control"
                                                           type="text" oninput="$.numberUtil.onlyNumber(this);" required disabled
                                                           autocomplete="off">
                                                </td>
                                                <td>
                                                    <input name="invoiceDetailList[0].guidingPrice"
                                                           id="guidingPrice_inv0" placeholder="指导价"
                                                           class="form-control"
                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                           type="text" autocomplete="off">
                                                </td>
                                                <td>
                                                    <select name="invoiceDetailList[0].transLineId"
                                                            id="transLineId_inv0" class="form-control valid custom-select"
                                                            aria-invalid="false"
                                                            th:with="type=${dispatcherDeptList}"
                                                            aria-required="true"
                                                            required>
                                                        <option value="" disabled selected hidden>调度组</option>
                                                        <option th:each="dict : ${type}" th:text="${dict.deptName}"
                                                                th:value="${dict.deptId}"></option>
                                                    </select>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="7">
                                                    <div id="addrBtnA_inv0">
                                                        <a class="btn btn-primary btn-sm btn-block"
                                                           style="margin-top: -10px" onclick="openAddressTab(0)">
                                                            <i class="fa fa-plus"></i> 新增提货到货信息
                                                        </a>
                                                    </div>
                                                    <input type="hidden" id="deliAddrCnt_inv0" value="0">
                                                    <input type="hidden" id="arriAddrCnt_inv0" value="0">
                                                    <div style="display: none" id="addrBtnB_inv0">
                                                        <span class="label label-warningT">装</span>
                                                        <span class="eclipse"
                                                              data-toggle="tooltip" data-placement="top" title=""
                                                              style="font-size: 15px;max-width:200px;vertical-align: middle;"
                                                              id="deliAddrSpan_inv0">
                                                            </span>

                                                        <i class="fa fa-long-arrow-right"
                                                           style="vertical-align: middle;color: #16b777;width: 20px;font-size: 20px;"
                                                           aria-hidden="true"></i>

                                                        <span class="label label-successT">卸</span>
                                                        <span class="eclipse"
                                                              data-toggle="tooltip" data-placement="top" title=""
                                                              style="font-size: 15px;max-width:200px;vertical-align: middle;"
                                                              id="arriAddrSpan_inv0">
                                                            </span>

                                                        <button type="button" class="btn btn-sm btn-outline-custom"
                                                                onclick="openAddressTab(0)">
                                                            <i class="fa fa-edit"></i> 编辑(0装0卸)
                                                        </button>
                                                    </div>

                                                    <div style="display: none" id="addr_inv0">
                                                        <div class="">
                                                            <div class="col-md-12 col-sm-12">
                                                                <table>
                                                                    <thead>
                                                                    <tr>
                                                                        <th style="width: 5%">
                                                                            <a class="collapse-link"
                                                                               style="font-size: 22px;color: #1ab394;"
                                                                               onclick="addAddressData(0,'th')"
                                                                               title="新增行">+</a>
                                                                        </th>
                                                                        <th style="width: 15%;">提货方</th>
                                                                        <th style="width: 10%;">联系人</th>
                                                                        <th style="width: 15%;">电话</th>
                                                                        <th style="width: 25%;">地址</th>
                                                                        <th style="width: 10%;">件数</th>
                                                                        <th style="width: 10%;">重量（吨）</th>
                                                                        <th style="width: 10%;">体积（m³）</th>
                                                                    </tr>
                                                                    </thead>
                                                                    <tbody id="address_inv0_th">
                                                                    <tr>
                                                                        <td>
                                                                            <div style="text-align: center;">
                                                                                <a class="fa fa-times-circle"
                                                                                   style="color: #fd8481;font-size: 20px;"
                                                                                   onclick="rmAddressData(this,0,'th')"
                                                                                   title="删除选择行"></a>
                                                                            </div>
                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[0].addrName"
                                                                                   id="addrName_inv0_0"
                                                                                   type="text" placeholder="地址名称"
                                                                                   class="form-control valid"
                                                                                   autocomplete="off" required/>
                                                                            <div style="position: relative;top: -1px;">
                                                                                <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                                                            </div>

                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[0].contact"
                                                                                   id="contact_inv0_0"
                                                                                   type="text" placeholder="联系人"
                                                                                   class="form-control valid"
                                                                                   autocomplete="off" required
                                                                                   readonly/>
                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[0].mobile"
                                                                                   id="mobile_inv0_0"
                                                                                   type="text" placeholder="电话"
                                                                                   class="form-control valid"
                                                                                   autocomplete="off" required
                                                                                   readonly/>
                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[0].detailAddr"
                                                                                   id="detailAddr_inv0_0"
                                                                                   type="text" placeholder="地址"
                                                                                   class="form-control valid"
                                                                                   autocomplete="off" required
                                                                                   readonly/>
                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[0].num"
                                                                                   id="num_inv0_0" placeholder="件数"
                                                                                   class="form-control"
                                                                                   type="text"
                                                                                   oninput="$.numberUtil.onlyNumber(this);"
                                                                                   autocomplete="off">
                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[0].weight"
                                                                                   id="weight_inv0_0" placeholder="重量"
                                                                                   class="form-control"
                                                                                   type="text"
                                                                                   oninput="$.numberUtil.onlyNumber(this);"
                                                                                   required autocomplete="off">
                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[0].volume"
                                                                                   id="volume_inv0_0" placeholder="体积"
                                                                                   class="form-control"
                                                                                   type="text"
                                                                                   oninput="$.numberUtil.onlyNumber(this);"
                                                                                   autocomplete="off">
                                                                        </td>
                                                                        <input name="invoiceDetailList[0].addressVOList[0].provinceId"
                                                                               id="provinceId_inv0_0"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                        <input name="invoiceDetailList[0].addressVOList[0].cityId"
                                                                               id="cityId_inv0_0"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                        <input name="invoiceDetailList[0].addressVOList[0].areaId"
                                                                               id="areaId_inv0_0"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                        <input name="invoiceDetailList[0].addressVOList[0].provinceName"
                                                                               id="provinceName_inv0_0"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                        <input name="invoiceDetailList[0].addressVOList[0].cityName"
                                                                               id="cityName_inv0_0"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                        <input name="invoiceDetailList[0].addressVOList[0].areaName"
                                                                               id="areaName_inv0_0"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                        <input name="invoiceDetailList[0].addressVOList[0].addressType"
                                                                               id="addressType_inv0_0" value="0"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                        <div class="">
                                                            <div class="col-md-12 col-sm-12">
                                                                <table>
                                                                    <thead>
                                                                    <tr>
                                                                        <th style="width: 5%">
                                                                            <a class="collapse-link"
                                                                               style="font-size: 22px;color: #1ab394;"
                                                                               onclick="addAddressData(0,'dh')"
                                                                               title="新增行">+</a>
                                                                        </th>
                                                                        <th style="width: 15%;">到货方</th>
                                                                        <th style="width: 10%;">联系人</th>
                                                                        <th style="width: 15%;">电话</th>
                                                                        <th style="width: 25%;">地址</th>
                                                                        <th style="width: 10%;">件数</th>
                                                                        <th style="width: 10%;">重量（吨）</th>
                                                                        <th style="width: 10%;">体积（m³）</th>
                                                                    </tr>
                                                                    </thead>
                                                                    <tbody id="address_inv0_dh">
                                                                    <tr>
                                                                        <td>
                                                                            <div style="text-align: center;">
                                                                                <a class="fa fa-times-circle"
                                                                                   style="color: #fd8481;font-size: 20px;"
                                                                                   onclick="rmAddressData(this,0,'dh')"
                                                                                   title="删除选择行"></a>
                                                                            </div>
                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[1].addrName"
                                                                                   id="addrName_inv0_1"
                                                                                   type="text" placeholder="地址名称"
                                                                                   class="form-control valid"
                                                                                   autocomplete="off" required/>
                                                                            <div style="position: relative;top: -1px;">
                                                                                <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                                                            </div>

                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[1].contact"
                                                                                   id="contact_inv0_1"
                                                                                   type="text" placeholder="联系人"
                                                                                   class="form-control valid"
                                                                                   autocomplete="off" required
                                                                                   readonly/>
                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[1].mobile"
                                                                                   id="mobile_inv0_1"
                                                                                   type="text" placeholder="电话"
                                                                                   class="form-control valid"
                                                                                   autocomplete="off" required
                                                                                   readonly/>
                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[1].detailAddr"
                                                                                   id="detailAddr_inv0_1"
                                                                                   type="text" placeholder="地址"
                                                                                   class="form-control valid"
                                                                                   autocomplete="off" required
                                                                                   readonly/>
                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[1].num"
                                                                                   id="num_inv0_1" placeholder="件数"
                                                                                   class="form-control"
                                                                                   type="text"
                                                                                   oninput="$.numberUtil.onlyNumber(this);"
                                                                                   autocomplete="off">
                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[1].weight"
                                                                                   id="weight_inv0_1" placeholder="重量"
                                                                                   class="form-control"
                                                                                   type="text"
                                                                                   oninput="$.numberUtil.onlyNumber(this);"
                                                                                   required autocomplete="off">
                                                                        </td>
                                                                        <td>
                                                                            <input name="invoiceDetailList[0].addressVOList[1].volume"
                                                                                   id="volume_inv0_1" placeholder="体积"
                                                                                   class="form-control"
                                                                                   type="text"
                                                                                   oninput="$.numberUtil.onlyNumber(this);"
                                                                                   autocomplete="off">
                                                                        </td>
                                                                        <input name="invoiceDetailList[0].addressVOList[1].provinceId"
                                                                               id="provinceId_inv0_1"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                        <input name="invoiceDetailList[0].addressVOList[1].cityId"
                                                                               id="cityId_inv0_1"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                        <input name="invoiceDetailList[0].addressVOList[1].areaId"
                                                                               id="areaId_inv0_1"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                        <input name="invoiceDetailList[0].addressVOList[1].provinceName"
                                                                               id="provinceName_inv0_1"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                        <input name="invoiceDetailList[0].addressVOList[1].cityName"
                                                                               id="cityName_inv0_1"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                        <input name="invoiceDetailList[0].addressVOList[1].areaName"
                                                                               id="areaName_inv0_1"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                        <input name="invoiceDetailList[0].addressVOList[1].addressType"
                                                                               id="addressType_inv0_1" value="1"
                                                                               type="hidden"
                                                                               class="form-control valid"/>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td colspan="5">
                                                        <textarea name="invoiceDetailList[0].memo" id="memo_inv0"
                                                                  maxlength="200" class="form-control valid"
                                                                  placeholder="备注" rows="2"></textarea>
                                                </td>
                                            </tr>
                                            </tbody>

                                        </table>


                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion" href="tabs_panels.html#collapseSix">附件</a>
                            </h4>
                        </div>
                        <div id="collapseSix" class="panel-collapse collapse in">
                            <div class="panel-body" style="padding: 15px 10px">
                                <div class="row no-gutter mt10">
                                    <div class="col-sm-12 col-md-6">
                                        <div class="flex">
                                            <label class="flex_left">附件：</label>
                                            <div class="flex_right">
                                                <input name="projectFile" id="projectFile" class="form-control"
                                                       type="file" multiple>
                                                <input type="hidden" id="projectFileId" name="projectFileId">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <input id="inquiryRecordId" type="hidden" name="inquiryRecordId">

        <div style="height: 50px;"></div>
        <div class="row">
            <div class="col-sm-offset-5 col-sm-10" style="background-color: #fff;">
                <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i
                        class="fa fa-check"></i>保 存
                </button>&nbsp;
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i
                        class="fa fa-reply-all"></i>关 闭
                </button>
            </div>
        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>

<script th:inline="javascript">
    //是否是车队
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/invoice/project" : ctx + "invoice/project";

    var balaCorp = [[${@dict.getType('bala_corp')}]];

    let inv_ind = 0;
    //保存下标
    const addrIndMap = new Map();
    addrIndMap.set(0, 1);

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapsefour').collapse('show');
        $('#collapseSix').collapse('hide');


        //图片功能
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("projectFile", "projectFileId", picParam);


        // $('[data-toggle="tooltip"]').tooltip()


        initDate(0)
        initGoodsNameBsSuggest(0)
        initGoodsNameBsSuggest(1)

        initDeliveryBsSuggest(0, 0, 0);
        initDeliveryBsSuggest(1, 0, 1);
    });


    /**
     * 基础信息 - 客户名称
     */
        //合同价精确到市或区  0区 1市
        // var accurateRegion = 0
    var crtGuidePrice

    var custAbbr = $("#custAbbr").bsSuggest({
        idField: 'customerId',                    //每组数据的哪个字段作为 data-id，优先级高于 indexId 设置（推荐）
        keyField: 'custAbbr',                   //每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        allowNoKeyword: false, //是否允许无关键字时请求数据
        multiWord: false, //以分隔符号分割的多关键字支持separator: ",", //多关键字支持时的分隔符，默认为空格
        getDataMethod: 'url', //获取数据的方式，总是从 URL 获取
        effectiveFields: ["custCode", "custAbbr", "custName", "salesDeptName", "stationDeptName", "balaCorp"],
        effectiveFieldsAlias: {
            custCode: "客户编码",
            custAbbr: "客户简称",
            custName: "客户名称",
            salesDeptName: "运营部",
            stationDeptName: "驻场组",
            balaCorp: "结算公司"
        },
        showHeader: true,
        //listAlign:'right',        //提示列表对齐位置，left/right/auto
        hideOnSelect: true,            // 鼠标从列表单击选择了值时，是否隐藏选择列表
        inputWarnColor: 'rgba(255,0,0,.1)', //输入框内容不是下拉列表选择时的警告色
        url: ctx + "client/listClient?contractSearch=contractSearch&permission=sales&custName=", //custAbbr /*优先从url ajax 请求 json 帮助数据，注意最后一个参数为关键字请求参数*/
        processData: function (json) {// url 获取数据时，对数据的处理，作为 getData 的回调函数
            var i, len, data = {value: []};
            if (!json || json.rows.length == 0) {
                return false;
            }
            len = json.rows.length;
            for (i = 0; i < len; i++) {
                data.value.push({
                    "customerId": json.rows[i].customerId,
                    "custName": json.rows[i].custName,
                    "custAbbr": json.rows[i].custAbbr,
                    "custCode": json.rows[i].custCode,
                    "balaCorpId": json.rows[i].balaCorp,
                    "stationDept": json.rows[i].stationDept,
                    "stationDeptName": json.rows[i].stationDeptName,
                    "balaDept": json.rows[i].balaDept,
                    "salesDept": json.rows[i].salesDept,
                    "psndoc": json.rows[i].psndoc,
                    "balaType": json.rows[i].balaType,
                    "billingCorp": json.rows[i].billingCorp,
                    "appDeliContact": json.rows[i].appDeliContact,
                    "appDeliMobile": json.rows[i].appDeliMobile,
                    "referenceRate": json.rows[i].referenceRate,
                    "crtGuidePrice": json.rows[i].crtGuidePrice,

                    "salesDeptName": json.rows[i].salesDeptName,
                    "balaCorp": $.table.selectDictLabel(balaCorp, json.rows[i].balaCorp),
                });
            }
            return data;
        }
    }).on('onSetSelectValue', function (e, keyword, data) {
        //清空结算客户信息
        $("#balaName").val("");
        $("#balaCode").val("");
        $("#balaCustomerId").val("");

        //清空集团
        $("#groupName").val("");
        $("#groupId").val("");

        //清空地址和货品
        clearAddrAndGoods()

        //赋值
        $('#customerId').val(data.customerId);
        $('#custName').val(data.custName);
        $('#custAbbr').val(data.custAbbr);
        $('#custCode').val(data.custCode);
        $('#balaCorpId').val(data.balaCorpId);
        $('#stationDept').val(data.stationDept);
        $('#stationDeptName').val(data.stationDeptName);
        $('#balaDept').val(data.balaDept);
        $('#salesDept').val(data.salesDept);
        $('#psndoc').val(data.psndoc);
        $('#balaType').val(data.balaType);
        $('#billingCorp').val(data.billingCorp);
        $('#appDeliContact').val(data.appDeliContact);
        $('#appDeliMobile').val(data.appDeliMobile);

        //是否需要指导价
        crtGuidePrice = data.crtGuidePrice

        //集团
        $.ajax({
            url: ctx + "group/getGroupByCustomerId",
            type: "post",
            dataType: "json",
            data: {customerId: data.customerId},
            success: function (result) {
                if (result.code == 0 && result.data != undefined) {
                    if (result.data != null) {
                        $("#groupName").val(result.data.GROUP_NAME);
                        $("#groupId").val(result.data.GROUP_ID);
                    }
                }
            }
        });

        //获取默认结算客户
        $.ajax({
            url: ctx + "client/getDefaultCustBalaByCustomerId",
            type: "post",
            dataType: "json",
            data: {customerId: data.customerId},
            success: function (result) {
                if (result.code == 0) {
                    if (result.data != null) {
                        //结算客户名称
                        $("#balaName").val(result.data.custName);
                        //结算客户编码
                        $("#balaCode").val(result.data.custCode);
                        //结算客户id
                        $("#balaCustomerId").val(result.data.customerId);
                    }
                }
            }
        });

    });


    function clearAddrAndGoods() {
        $("[id^='invoice_']").remove();
        addInvoiceData()
    }


    /**
     * 提到货信息
     * @param addressType    地址类别 0提货地址  1到货地址
     * @param invInd
     * @param addrInd
     */
    function initDeliveryBsSuggest(addressType, invInd, addrInd) {
        let customerId = $("#customerId").val();
        let addrType = addressType == 0 ? 1 : 0;
        $("#addrName_inv" + invInd + "_" + addrInd).bsSuggest({
            idField: 'custAddressId',                    //每组数据的哪个字段作为 data-id，优先级高于 indexId 设置（推荐）
            keyField: 'addrName',                   //每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            allowNoKeyword: true, //是否允许无关键字时请求数据
            multiWord: false, //以分隔符号分割的多关键字支持separator: ",", //多关键字支持时的分隔符，默认为空格
            getDataMethod: "url", //获取数据的方式，总是从 URL 获取
            effectiveFields: ["addrName", "address", "contact", "mobile"],
            effectiveFieldsAlias: {
                addrName: "地址名称",
                // provinceName: "省",
                // cityName: "市",
                // areaName: "区",
                // detailAddr: "详细地址",
                address: "详细地址",
                contact: "联系人",
                mobile: "联系电话",
            },
            showHeader: true,
            //listAlign:'right',        //提示列表对齐位置，left/right/auto
            hideOnSelect: true,            // 鼠标从列表单击选择了值时，是否隐藏选择列表
            inputWarnColor: 'rgba(255,0,0,.1)', //输入框内容不是下拉列表选择时的警告色
            url: ctx + "basic/address/selectAddressList?customerId=" + customerId + "&addrType=" + addrType + "&allSearch=",
            fnAdjustAjaxParam: function (keyword, opts) {
                let customerId = $("#customerId").val();
                return {
                    url: ctx + "basic/address/selectAddressList?customerId=" + customerId + "&addrType=" + addrType + "&allSearch="
                };
            },

            fnProcessData: function (json) {// url 获取数据时，对数据的处理，作为 getData 的回调函数
                var i, len, data = {value: []};
                if (!json || json.rows.length == 0) {
                    return false;
                }
                len = json.rows.length;
                for (i = 0; i < len; i++) {
                    data.value.push({
                        "custAddressId": json.rows[i].custAddressId,
                        "addrName": json.rows[i].addrName,

                        "address": json.rows[i].provinceName + json.rows[i].cityName + json.rows[i].areaName + json.rows[i].detailAddr,

                        "provinceName": json.rows[i].provinceName,
                        "provinceId": json.rows[i].provinceId,
                        "cityName": json.rows[i].cityName,
                        "cityId": json.rows[i].cityId,
                        "areaName": json.rows[i].areaName,
                        "areaId": json.rows[i].areaId,
                        "detailAddr": json.rows[i].detailAddr,
                        "contact": json.rows[i].contact,
                        "mobile": json.rows[i].mobile,
                        "addressType": json.rows[i].addressType,

                    });
                }

                return data
            }
        }).on('onSetSelectValue', function (e, keyword, data) {
            $("#contact_inv" + invInd + "_" + addrInd).val(data.contact);
            $("#mobile_inv" + invInd + "_" + addrInd).val(data.mobile);
            $("#provinceId_inv" + invInd + "_" + addrInd).val(data.provinceId);
            $("#cityId_inv" + invInd + "_" + addrInd).val(data.cityId);
            $("#areaId_inv" + invInd + "_" + addrInd).val(data.areaId);
            $("#provinceName_inv" + invInd + "_" + addrInd).val(data.provinceName);
            $("#cityName_inv" + invInd + "_" + addrInd).val(data.cityName);
            $("#areaName_inv" + invInd + "_" + addrInd).val(data.areaName);
            $("#addrName_inv" + invInd + "_" + addrInd).val(data.addrName);
            $("#detailAddr_inv" + invInd + "_" + addrInd).val(data.detailAddr);
        });

    }



    /**
     * 选择货品名称
     *
     * @param uid   标识符
     * @param type  类型  0发货  1收货
     */

    function initGoodsNameBsSuggest(invInd) {
        let customerId = $("#customerId").val();
        $("#goodsName_inv" + invInd).bsSuggest({
            idField: 'goodsId',                    //每组数据的哪个字段作为 data-id，优先级高于 indexId 设置（推荐）
            keyField: 'goodsName',                   //每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            allowNoKeyword: true, //是否允许无关键字时请求数据
            multiWord: false, //以分隔符号分割的多关键字支持separator: ",", //多关键字支持时的分隔符，默认为空格
            getDataMethod: "url", //获取数据的方式，总是从 URL 获取
            effectiveFields: ["goodsName", "goodsTypeName"],
            effectiveFieldsAlias: {
                goodsName: "货品名称",
                goodsTypeName: "货品类型",
            },
            showHeader: true,
            //listAlign:'right',        //提示列表对齐位置，left/right/auto
            hideOnSelect: true,            // 鼠标从列表单击选择了值时，是否隐藏选择列表
            inputWarnColor: 'rgba(255,0,0,.1)', //输入框内容不是下拉列表选择时的警告色
            url: ctx + "basic/goods/goodsList?type=0&customerId=" + customerId + "&goodsName=",
            fnAdjustAjaxParam: function (keyword, opts) {
                console.log('ajax 请求参数调整：', keyword, opts);
                let customerId = $("#customerId").val();

                return {
                    url: ctx + "basic/goods/goodsList?type=0&customerId=" + customerId + "&goodsName=",
                };
            },

            fnProcessData: function (json) {// url 获取数据时，对数据的处理，作为 getData 的回调函数
                var i, len, data = {value: []};
                if (!json || json.rows.length == 0) {
                    return false;
                }
                len = json.rows.length;
                for (i = 0; i < len; i++) {
                    data.value.push({
                        "goodsId": json.rows[i].goodsId,
                        "goodsName": json.rows[i].goodsName,
                        "goodsCharacter": json.rows[i].goodsCharacter,
                        "goodsType": json.rows[i].goodsType,
                        "goodsTypeName": json.rows[i].goodsTypeName,
                    });
                }

                return data
            }
        }).on('onSetSelectValue', function (e, keyword, data) {
            //货品名称
            $("#goodsName_inv" + invInd).val(data.goodsName);
            //货品特性 - 获取单价时使用
            $("#goodsCharacter_inv" + invInd).val(data.goodsCharacter);

            //回填货品类型id
            $("#goodsTypeId_inv" + invInd).val(data.goodsType);
            //回填货品类型名称
            $("#goodsTypeName_inv" + invInd).val(data.goodsTypeName);

            //单独校验
            $("#form-invoice-add").validate().element($("#goodsName_inv" + invInd));
        });
    }




    //系统带出的 历史最新指导价与状态 用作提交时比较指导价是否修改过
    var guidingPriceOld;
    // var guidingPriceCheckStust;
    var referencePriceOid;

    /**
     * 获取指导价
     */
    function getGuidingPrice(invInd) {

        $("#guidingPrice_inv" + invInd).val("");

        let deliAreaId;
        let arriAreaId;
        let deliAreaCt = 0;
        let arriAreaCt = 0;

        $('input[name^="invoiceDetailList[' + invInd + '].addressVOList["][name$=".areaId"]').each(function () {
            let name = $(this).attr('name');

            let parts = name.split('[');
            let index = parts[2].split(']')[0];

            // var match = name.match(/\[(\d+)\]/);
            // var index = match ? match[1] : null;

            let addressTypeId = 'addressType_inv' + invInd + '_' + index;
            let addressType = $("#" + addressTypeId).val();

            let val = $(this).val();
            if (addressType == 0) {
                deliAreaId = val
            }
            if (addressType == 1) {
                arriAreaId = val
            }

            addressType == 0 && val != '' ? deliAreaCt++ : null;
            addressType == 1 && val != '' ? arriAreaCt++ : null;

        });

        if (deliAreaCt > 1 || arriAreaCt > 1) {
            $("#guidingPrice_inv" + invInd).val("");
            $("#guidingPrice_inv" + invInd).attr("disabled", "disabled");
            return;
        } else {
            $("#guidingPrice_inv" + invInd).removeAttr("disabled")
        }

        var param = {};
        //车型
        param.carType = $("#carType_inv" + invInd).val();
        if ($("#carType_inv" + invInd).val() === '') {
            return;
        }
        //车长
        param.carLen = $("#carLen_inv" + invInd).val();
        if ($("#carLen_inv" + invInd).val() === '') {
            return;
        }
        //运输方式-整车
        let transCode = $("#transCode_inv" + invInd).val();
        if (transCode === '' || (transCode !== '0' && transCode !== '4' && transCode !== '15')) {
            return;
        }

        //提货方 区id
        if (deliAreaCt == 1) {
            param.deliAreaId = deliAreaId;
        } else {
            return;
        }
        //到货区 区id
        if (arriAreaCt == 1) {
            param.arriAreaId = arriAreaId;
        } else {
            return;
        }

        $.ajax({
            url: ctx + "invoice/get_reference_price",
            type: "post",
            dataType: "json",
            data: param,
            success: function (result) {
                if (result.code === 0) {
                    if (result.data.length > 0) {
                        if (transCode == 0 && result.data[0].priceBasic) {
                            $("#guidingPrice_inv" + invInd).val(result.data[0].priceBasic);
                            $("#guidingPrice_inv" + invInd).attr("disabled", "disabled");
                        } else if (transCode == 15 && result.data[0].priceDangerousGoods) {
                            $("#guidingPrice_inv" + invInd).val(result.data[0].priceDangerousGoods);
                            $("#guidingPrice_inv" + invInd).attr("disabled", "disabled");
                        } else if (transCode == 4 && result.data[0].priceColdChain) {
                            $("#guidingPrice_inv" + invInd).val(result.data[0].priceColdChain);
                            $("#guidingPrice_inv" + invInd).attr("disabled", "disabled");
                        } else {
                            $("#guidingPrice_inv" + invInd).removeAttr("disabled")
                        }
                        getReferencePriceOid(invInd, transCode, result.data[0].referencePriceId);
                    } else {
                        $("#guidingPrice_inv" + invInd).val('');
                    }
                }
            }
        })
    }

    function getReferencePriceOid(invInd, transCode, referencePriceId) {
        var param = {};
        param.referencePriceId = referencePriceId;
        if (transCode == 0) {
            param.priceType = 0
        } else if (transCode == 15) {
            param.priceType = 1
        } else if (transCode == 4) {
            param.priceType = 2
        }

        $.ajax({
            url: ctx + "invoice/has_reference_check_data",
            type: "post",
            dataType: "json",
            data: param,
            success: function (result) {
                if (result.code === 0) {
                    if (result.data) {
                        $("#guidingPrice_inv" + invInd).attr("disabled", "disabled");
                    } else {
                        $("#guidingPrice_inv" + invInd).removeAttr("disabled");
                    }
                }
            }
        })
    }


    /**
     * 获取历史指导价列表
     * @param
     */
    function getHistoryGuidePriceDetailList() {
        let isMultiple = $('#isMultiple').val();
        if (isMultiple == '1') {
            $.modal.alertWarning("多装多卸暂时无法查看历史指导价！")
            return;
        }

        let unloadPlaceNum = $('#unloadPlaceNum').val();
        let isOversize = $('#isOversize').val();
        if (unloadPlaceNum != 1) {
            $.modal.alertWarning("卸货地数量大于1暂时无法查看历史指导价！")
            return;
        }
        if (isOversize == 1) {
            $.modal.alertWarning("大件运输暂时无法查看历史指导价！")
            return;
        }

        var transCode = $("#transCode").val();

        var param = "referencePriceId=" + referencePriceOid;
        if (transCode == 0) {
            param += "&priceType=0"
        } else if (transCode == 15) {
            param += "&priceType=1"
        } else if (transCode == 4) {
            param += "&priceType=2"
        }

        $("#historyGuidingPriceParam").val(param);

        layer.open({
            type: 2,
            area: ['80%', '90%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '历史指导价',
            content: ctx + "invoice/history_reference_price",
            btn: ['关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            cancel: function (index) {
                return true;
            }
        });
    }


    /**
     * 校验
     */
    $("#form-invoice-add").validate({
        onkeyup: false,
        focusCleanup: true,
        errorPlacement: function (error, element) {
            // do nothing
        },
        rules: {}
    });


    /**
     * 初始化日期控件
     * @param invInd
     */
    function initDate(invInd) {
        let deliID = '#reqDeliDate_inv' + invInd
        let arriId = '#reqArriDate_inv' + invInd

        layui.use('laydate', function () {
            var laydate = layui.laydate;
            //要求提货日期
            var reqDeliDate = laydate.render({
                elem: deliID, //指定元素
                id: deliID,
                format: 'yyyy-MM-dd HH点',
                isInitValue: false,
                trigger: 'click',
                type: 'datetime',
                min: -1,
                fullPanel: true,
                ready: function (date) {
                    let inst = laydate.getInst(deliID);
                    let index = inst.config.index;

                    var styleElement = $("<style>");
                    // 在<style>元素中添加你的样式
                    styleElement.text(".layui-laydate {width: 385px !important;} .layui-laydate-header{width: 72%;} .layui-laydate-content > ul {width: 20% !important;} .laydate-time-list > li {width: 100% !important;} .laydate-time-list > li:nth-last-child(2), .laydate-time-list > li:last-child {display: none;}");

                    // 将<style>元素添加到指定的元素中
                    $('#layui-laydate' + index).append(styleElement);
                },
                done: function (value, date, endDate) {
                    reqArriDate.config.min = {
                        year: date.year,
                        month: date.month - 1,//关键
                        date: date.date,
                        hours: date.hours,
                        minutes: date.minutes,
                        seconds: date.seconds
                    };
                    $(deliID).val(value);
                    //单独校验日期
                    $("#form-invoice-add").validate().element($(deliID));
                }
            });
            //要求到货日期
            var reqArriDate = laydate.render({
                elem: arriId, //指定元素
                //  format : 'yyyy-MM-dd',
                isInitValue: false,
                trigger: 'click',
                type: 'date',
                ready: function (date) {
                    // var now = new Date();
                    // this.dateTime.hours = now.getHours();
                    // this.dateTime.minutes = now.getMinutes();
                    // this.dateTime.seconds = now.getSeconds();
                },
                done: function (value, date, endDate) {
                    /*reqDeliDate.config.max = {
                        year: date.year,
                        month: date.month - 1,//关键
                        date: date.date,
                        hours: date.hours,
                        minutes: date.minutes,
                        seconds: date.seconds
                    };*/
                    $(arriId).val(value);
                    //单独校验日期
                    $("#form-invoice-add").validate().element($(arriId))
                }
            });
        });
    }


    /**
     * 添加发货单详情
     */
    function addInvoiceData() {
        //地址序列
        inv_ind++;
        let invInd = inv_ind

        addrIndMap.set(invInd, 1);

        let html = `
        <tbody id="invoice_inv` + invInd + `">
            <tr>
                <td rowspan="2">
                    <div style="display: inline-block;">
                        <a
                           class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"
                           onclick="rmInvoiceData(this)" title="删除选择行"></a>
                    </div>
                    <div style="display: inline-block;margin-left:5px">
                        <a class="fa fa-clone" style="color: #0092e7;font-size: 17px;"
                           onclick="cloneInvoiceData(` + invInd + `)" title="复制货品"></a>
                    </div>
                </td>

                <td>
                    <input name="invoiceDetailList[` + invInd + `].custOrderno" id="custOrderno_inv` + invInd + `"
                           placeholder="客户发货单号" class="form-control" type="text"
                           maxlength="50" autocomplete="off" oninput="">
                </td>
                <td>
                    <input name="invoiceDetailList[` + invInd + `].reqDeliDate" id="reqDeliDate_inv` + invInd + `"
                           type="text" class=" form-control" required
                           placeholder="要求提货日" lay-key="` + invInd + `" autocomplete="off">
                </td>
                <td>
                    <input name="invoiceDetailList[` + invInd + `].reqArriDate" id="reqArriDate_inv` + invInd + `"
                           type="text" class=" form-control" required
                           placeholder="要求到货日" lay-key="` + invInd + `" autocomplete="off">
                </td>
                <td>
                `
            + getCarLenHtml(invInd) +
            `
                </td>
                <td>
                `
            + getCarTypeHtml(invInd) +
            `
                </td>
                <td>
                `
            + getTransCodeHtml(invInd) +
            `
                </td>
                <td>
                    <input name="invoiceDetailList[` + invInd + `].goodsName" id="goodsName_inv` + invInd + `"
                           type="text" placeholder="货品名称" class="form-control valid" autocomplete="off" required/>
                    <input name="invoiceDetailList[` + invInd + `].goodsCharacter" id="goodsCharacter_inv` + invInd + `" type="hidden"/>
                    <input name="invoiceDetailList[` + invInd + `].goodsTypeName" id="goodsTypeName_inv` + invInd + `" type="hidden" class="form-control"/>
                    <input name="invoiceDetailList[` + invInd + `].goodsType" id="goodsTypeId_inv` + invInd + `" type="hidden"/>
                    <div  style="position: relative;top: -1px;">
                        <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                    </div>
                </td>
                <td>
                    <input name="invoiceDetailList[` + invInd + `].numCount" id="numCount_inv` + invInd + `" placeholder="件数" class="form-control"
                           type="text" oninput="$.numberUtil.onlyNumber(this);"  autocomplete="off" disabled>
                </td>
                <td>
                    <input name="invoiceDetailList[` + invInd + `].weightCount" id="weightCount_inv` + invInd + `" placeholder="重量" class="form-control"
                           type="text" oninput="$.numberUtil.onlyNumber(this);" required  autocomplete="off" disabled>
                </td>
                <td>
                    <input name="invoiceDetailList[` + invInd + `].volumeCount" id="volumeCount_inv` + invInd + `" placeholder="体积" class="form-control"
                           type="text" oninput="$.numberUtil.onlyNumber(this);"  autocomplete="off" disabled>
                </td>
                <td>
                    <input name="invoiceDetailList[` + invInd + `].guidingPrice" id="guidingPrice_inv` + invInd + `" placeholder="指导价"
                           class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                           type="text" autocomplete="off">
                </td>
                <td>
                `
            + getTransLineHtml(invInd) +
            `
                </td>
            </tr>
            <tr>
                <td colspan="7">
                    <div id="addrBtnA_inv` + invInd + `">
                        <a class="btn btn-primary btn-sm btn-block" style="margin-top: -10px" onclick="openAddressTab(` + invInd + `)">
                            <i class="fa fa-plus"></i> 新增提货到货信息
                        </a>
                    </div>
                    <input type="hidden" id="deliAddrCnt_inv` + invInd + `" value="0">
                    <input type="hidden" id="arriAddrCnt_inv` + invInd + `" value="0">
                    <div style="display: none" id="addrBtnB_inv` + invInd + `">
                        <span class="label label-warningT">装</span>
                        <span class="eclipse"
                              data-toggle="tooltip" data-placement="top" title=""
                              style="font-size: 15px;max-width:200px;vertical-align: middle;"
                              id="deliAddrSpan_inv` + invInd + `">
                        </span>

                        <i class="fa fa-long-arrow-right"
                           style="vertical-align: middle;color: #16b777;width: 20px;font-size: 20px;" aria-hidden="true"></i>

                        <span class="label label-successT">卸</span>
                        <span class="eclipse"
                              data-toggle="tooltip" data-placement="top" title=""
                              style="font-size: 15px;max-width:200px;vertical-align: middle;"
                              id="arriAddrSpan_inv` + invInd + `">
                        </span>
                        <button type="button" class="btn btn-sm btn-outline-custom" onclick="openAddressTab(` + invInd + `)">
                            <i class="fa fa-edit"></i> 编辑(0装0卸)
                        </button>

                    </div>
                    <div style="display: none" id="addr_inv` + invInd + `">
                        <div>
                            <div class="col-md-12 col-sm-12">
                                <table>
                                    <thead>
                                    <tr>
                                        <th style="width: 5%">
                                            <a class="collapse-link" style="font-size: 22px;color: #1ab394;"
                                               onclick="addAddressData(` + invInd + `,'th')" title="新增行">+</a>
                                        </th>
                                        <th style="width: 15%;">提货方</th>
                                        <th style="width: 10%;">联系人</th>
                                        <th style="width: 15%;">电话</th>
                                        <th style="width: 25%;">地址</th>
                                        <th style="width: 10%;">件数</th>
                                        <th style="width: 10%;">重量（吨）</th>
                                        <th style="width: 10%;">体积（m³）</th>
                                    </tr>
                                    </thead>
                                    <tbody id="address_inv` + invInd + `_th">
                                        <tr >
                                            <td>
                                                <div style="text-align: center;">
                                                    <a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"
                                                       onclick="rmAddressData(this,` + invInd + `,'th')" title="删除选择行"></a>
                                                </div>
                                            </td>
                                            <td>
                                                <input name="invoiceDetailList[` + invInd + `].addressVOList[0].addrName" id="addrName_inv` + invInd + `_0"
                                                       type="text" placeholder="地址名称" class="form-control valid" autocomplete="off" required/>
                                                <div style="position: relative;top: -1px;">
                                                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                                </div>
                                            </td>
                                            <td>
                                                <input name="invoiceDetailList[` + invInd + `].addressVOList[0].contact" id="contact_inv` + invInd + `_0"
                                                       type="text" placeholder="联系人" class="form-control valid" autocomplete="off" required readonly/>
                                            </td>
                                            <td>
                                                <input name="invoiceDetailList[` + invInd + `].addressVOList[0].mobile" id="mobile_inv` + invInd + `_0"
                                                       type="text" placeholder="电话" class="form-control valid" autocomplete="off" required readonly/>
                                            </td>
                                            <td>
                                                <input name="invoiceDetailList[` + invInd + `].addressVOList[0].detailAddr"
                                                       id="detailAddr_inv` + invInd + `_0"
                                                       type="text"
                                                       placeholder="地址"
                                                       class="form-control valid"
                                                       autocomplete="off" required readonly/>
                                            </td>
                                            <td>
                                                <input name="invoiceDetailList[` + invInd + `].addressVOList[0].num"
                                                       id="num_inv` + invInd + `_0" placeholder="件数"
                                                       class="form-control"
                                                       type="text"
                                                       oninput="$.numberUtil.onlyNumber(this);"
                                                       autocomplete="off">
                                            </td>
                                            <td>
                                                <input name="invoiceDetailList[` + invInd + `].addressVOList[0].weight"
                                                       id="weight_inv` + invInd + `_0"
                                                       placeholder="重量"
                                                       class="form-control"
                                                       type="text"
                                                       oninput="$.numberUtil.onlyNumber(this);"
                                                       required  autocomplete="off">
                                            </td>
                                            <td>
                                                <input name="invoiceDetailList[` + invInd + `].addressVOList[0].volume"
                                                       id="volume_inv` + invInd + `_0"
                                                       placeholder="体积"
                                                       class="form-control"
                                                       type="text"
                                                       oninput="$.numberUtil.onlyNumber(this);"
                                                       autocomplete="off">
                                            </td>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[0].provinceId" id="provinceId_inv` + invInd + `_0"
                                                   type="hidden" class="form-control valid"/>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[0].cityId" id="cityId_inv` + invInd + `_0"
                                                   type="hidden" class="form-control valid"/>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[0].areaId" id="areaId_inv` + invInd + `_0"
                                                   type="hidden" class="form-control valid"/>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[0].provinceName" id="provinceName_inv` + invInd + `_0"
                                                   type="hidden" class="form-control valid"/>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[0].cityName" id="cityName_inv` + invInd + `_0"
                                                   type="hidden" class="form-control valid"/>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[0].areaName" id="areaName_inv` + invInd + `_0"
                                                   type="hidden" class="form-control valid"/>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[0].addressType" id="addressType_inv` + invInd + `_0" value="0"
                                                   type="hidden" class="form-control valid"/>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="">
                            <div class="col-md-12 col-sm-12">
                                <table>
                                    <thead>
                                    <tr>
                                        <th style="width: 5%">
                                            <a class="collapse-link" style="font-size: 22px;color: #1ab394;"
                                               onclick="addAddressData(` + invInd + `,'dh')" title="新增行">+</a>
                                        </th>
                                        <th style="width: 15%;">到货方</th>
                                        <th style="width: 10%;">联系人</th>
                                        <th style="width: 15%;">电话</th>
                                        <th style="width: 25%;">地址</th>
                                        <th style="width: 10%;">件数</th>
                                        <th style="width: 10%;">重量（吨）</th>
                                        <th style="width: 10%;">体积（m³）</th>
                                    </tr>
                                    </thead>
                                    <tbody id="address_inv` + invInd + `_dh">
                                    <tr >
                                        <td>
                                            <div style="text-align: center;">
                                                <a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"
                                                   onclick="rmAddressData(this,` + invInd + `,'dh')" title="删除选择行"></a>
                                            </div>
                                        </td>
                                        <td>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[1].addrName" id="addrName_inv` + invInd + `_1"
                                                   type="text" placeholder="地址名称" class="form-control valid" autocomplete="off" required/>
                                            <div style="position: relative;top: -1px;">
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                            </div>
                                        </td>
                                        <td>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[1].contact" id="contact_inv` + invInd + `_1"
                                                   type="text" placeholder="联系人" class="form-control valid" autocomplete="off" required readonly/>
                                        </td>
                                        <td>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[1].mobile" id="mobile_inv` + invInd + `_1"
                                                   type="text" placeholder="电话" class="form-control valid" autocomplete="off" required readonly/>
                                        </td>
                                        <td>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[1].detailAddr"
                                                   id="detailAddr_inv` + invInd + `_1"
                                                   type="text"
                                                   placeholder="地址"
                                                   class="form-control valid"
                                                   autocomplete="off" required readonly/>
                                        </td>
                                        <td>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[1].num"
                                                   id="num_inv` + invInd + `_1"
                                                   placeholder="件数"
                                                   class="form-control"
                                                   type="text"
                                                   oninput="$.numberUtil.onlyNumber(this);"
                                                   autocomplete="off">
                                        </td>
                                        <td>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[1].weight"
                                                   id="weight_inv` + invInd + `_1"
                                                   placeholder="重量"
                                                   class="form-control"
                                                   type="text"
                                                   oninput="$.numberUtil.onlyNumber(this);"
                                                   required  autocomplete="off">
                                        </td>
                                        <td>
                                            <input name="invoiceDetailList[` + invInd + `].addressVOList[1].volume"
                                                   id="volume_inv` + invInd + `_1"
                                                   placeholder="体积"
                                                   class="form-control"
                                                   type="text"
                                                   oninput="$.numberUtil.onlyNumber(this);"
                                                   autocomplete="off">
                                        </td>
                                        <input name="invoiceDetailList[` + invInd + `].addressVOList[1].provinceId" id="provinceId_inv` + invInd + `_1"
                                               type="hidden" class="form-control valid"/>
                                        <input name="invoiceDetailList[` + invInd + `].addressVOList[1].cityId" id="cityId_inv` + invInd + `_1"
                                               type="hidden" class="form-control valid"/>
                                        <input name="invoiceDetailList[` + invInd + `].addressVOList[1].areaId" id="areaId_inv` + invInd + `_1"
                                               type="hidden" class="form-control valid"/>
                                        <input name="invoiceDetailList[` + invInd + `].addressVOList[1].provinceName" id="provinceName_inv` + invInd + `_1"
                                               type="hidden" class="form-control valid"/>
                                        <input name="invoiceDetailList[` + invInd + `].addressVOList[1].cityName" id="cityName_inv` + invInd + `_1"
                                               type="hidden" class="form-control valid"/>
                                        <input name="invoiceDetailList[` + invInd + `].addressVOList[1].areaName" id="areaName_inv` + invInd + `_1"
                                               type="hidden" class="form-control valid"/>
                                        <input name="invoiceDetailList[` + invInd + `].addressVOList[1].addressType" id="addressType_inv` + invInd + `_1" value="1"
                                               type="hidden" class="form-control valid"/>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>


                    </div>
                </td>
                <td colspan="5">
                    <textarea name="invoiceDetailList[` + invInd + `].memo" id="memo_inv` + invInd + `" maxlength="200" class="form-control valid"
                              placeholder="备注" rows="2"></textarea>
                </td>
            </tr>
        </tbody>
        `

        $("#inv_table").append(html);

        initDate(invInd)
        initGoodsNameBsSuggest(invInd)

        initDeliveryBsSuggest(0, invInd, 0);
        initDeliveryBsSuggest(1, invInd, 1);
    }

    /**
     * 复制发货单详情
     * @param sourceInvIdx
     */
    function cloneInvoiceData(sourceInvIdx) {
        //地址序列
        inv_ind++;
        let invInd = inv_ind

        addrIndMap.set(invInd, addrIndMap.get(sourceInvIdx));

        let cloneInvoice = $('#invoice_inv' + sourceInvIdx).clone();
        cloneInvoice.find('select').each(function () {
            let id = $(this).attr('id');

            let val = $("#" + id).val();
            $(this).val(val)
        });
        cloneInvoice.attr('id', 'invoice_inv' + invInd);


        // 找到tbody中所有的input元素，将包含invoiceDetailList[3]的name属性替换为invoiceDetailList[4]
        cloneInvoice.find('input,select,div,tbody,textarea,span').each(function () {
            $(this).removeAttr('lay-key');
            $(this).removeAttr('layui-laydate-id');

            let name = $(this).attr('name');
            if (name && name.indexOf('invoiceDetailList[' + sourceInvIdx + ']') !== -1) {
                name = name.replace(new RegExp('invoiceDetailList\\[' + sourceInvIdx + '\\]', 'g'), 'invoiceDetailList[' + invInd + ']');
                $(this).attr('name', name);
            }

            let id = $(this).attr('id');
            if (id && id.indexOf('_inv' + sourceInvIdx) !== -1) {
                id = id.replace(new RegExp('_inv' + sourceInvIdx, 'g'), '_inv' + invInd);
                $(this).attr('id', id);
            }
        });

        // cloneInvoice.find('.eclipse').each(function () {
        //     let text = $(this).text();
        //     $(this).tooltip('dispose').tooltip();
        //     $(this).attr('data-original-title', text);
        //     $(this).attr('title', text).tooltip('dispose').tooltip();
        //
        // });


        // 找到tbody中所有的a元素，并为其绑定事件处理函数
        cloneInvoice.find('a,button').each(function () {
            let $a = $(this);
            let onclick = $a.attr('onclick');
            if (onclick) {
                if (onclick.indexOf('cloneInvoiceData(' + sourceInvIdx) !== -1) {
                    // html = html.replace(new RegExp('cloneInvoiceData\\(' + sourceInvIdx + '\\)', 'g'), 'cloneInvoiceData(' + invInd + ')');
                    let newOnclick = onclick.replace(new RegExp('cloneInvoiceData\\(' + sourceInvIdx + '\\)', 'g'), 'cloneInvoiceData(' + invInd + ')');
                    $a.attr('onclick', newOnclick);
                } else if (onclick.indexOf('openAddressTab(' + sourceInvIdx) !== -1) {
                    let newOnclick = onclick.replace(new RegExp('openAddressTab\\(' + sourceInvIdx + '\\)', 'g'), 'openAddressTab(' + invInd + ')');
                    $a.attr('onclick', newOnclick);

                } else if (onclick.indexOf('addAddressData(' + sourceInvIdx) !== -1) {
                    let newOnclick = onclick.replace(new RegExp('addAddressData\\(' + sourceInvIdx, 'g'), 'addAddressData(' + invInd);
                    $a.attr('onclick', newOnclick);

                } else if (onclick.indexOf('rmAddressData(this,' + sourceInvIdx) !== -1) {
                    let newOnclick = onclick.replace(new RegExp('rmAddressData\\(this\\,' + sourceInvIdx, 'g'), 'rmAddressData(this,' + invInd);
                    $a.attr('onclick', newOnclick);

                } else if (onclick.indexOf('selectDelivery(' + sourceInvIdx) !== -1) {
                    let newOnclick = onclick.replace(new RegExp('selectDelivery\\(' + sourceInvIdx, 'g'), 'selectDelivery(' + invInd);
                    $a.attr('onclick', newOnclick);

                }
            }
        });

        cloneInvoice.find('#goodsName_inv' + invInd).each(function () {
            let text = $(this).text();

            $(this).bsSuggest("destroy");

            $(this).removeAttr("data-id");
            $(this).removeAttr("aria-invalid");
            $(this).removeAttr("alt");
            $(this).removeAttr("style");
        });


        $("#inv_table").append(cloneInvoice);

        // $("#deliAddrSpan_inv" + invInd).tooltip('show');
        // $("#arriAddrSpan_inv" + invInd).tooltip('show');

        initDate(invInd)
        initGoodsNameBsSuggest(invInd)
    }

    /**
     * 拼接车长
     * @param invInd     发货单下标
     */
    var carLenList = [[${@dict.getType('car_len')}]];

    function getCarLenHtml(invInd) {

        let html = `
            <select name="invoiceDetailList[` + invInd + `].carLen" id="carLen_inv` + invInd + `"
                onchange="getGuidingPrice(` + invInd + `);" class="form-control valid custom-select"
                aria-invalid="false" aria-required="true" required>
        `
        html = html + `<option value="" disabled selected hidden>车长</option>`
        for (var i = 0; i < carLenList.length; i++) {
            html = html + `<option value="` + carLenList[i].dictValue + `">` + carLenList[i].dictLabel + `</option>`
        }
        html = html + `</select>`
        html = html + `<input name="invoiceDetailList[` + invInd + `].carLenName" id="carLenName_inv` + invInd + `" type="hidden">`
        return html;
    }

    /**
     * 拼接 车型
     * @param addrIndex     发货单下标
     */
    var carTypeList = [[${@dict.getType('car_type')}]];

    function getCarTypeHtml(invInd) {

        let html = `
            <select name="invoiceDetailList[` + invInd + `].carType" id="carType_inv` + invInd + `"
                onchange="getGuidingPrice(` + invInd + `);" class="form-control valid custom-select"
                aria-invalid="false" aria-required="true" required>
        `
        html = html + `<option value="" disabled selected hidden>车型</option>`
        for (var i = 0; i < carTypeList.length; i++) {
            html = html + `<option value="` + carTypeList[i].dictValue + `">` + carTypeList[i].dictLabel + `</option>`
        }
        html = html + `</select>`
        html = html + `<input name="invoiceDetailList[` + invInd + `].carTypeName" id="carTypeName_inv` + invInd + `" type="hidden">`

        return html;
    }

    /**
     * 拼接 运输方式
     * @param addrIndex     发货单下标
     */
    var transCodeList = [[${@dict.getType('trans_code')}]];

    function getTransCodeHtml(invInd) {

        let html = `
            <select name="invoiceDetailList[` + invInd + `].transCode" id="transCode_inv` + invInd + `"
                onchange="getGuidingPrice(` + invInd + `);" class="form-control valid custom-select"
                aria-invalid="false" aria-required="true" required>
        `
        html = html + `<option value="" disabled selected hidden>运输方式</option>`
        for (var i = 0; i < transCodeList.length; i++) {
            html = html + `<option value="` + transCodeList[i].dictValue + `">` + transCodeList[i].dictLabel + `</option>`
        }
        html = html + `</select>`
        html = html + `<input name="invoiceDetailList[` + invInd + `].transName" id="transName_inv` + invInd + `" type="hidden">`

        return html;
    }

    /**
     * 拼接 调度组
     * @param addrIndex     发货单下标
     */
    var dispatcherDeptList = [[${dispatcherDeptList}]];

    function getTransLineHtml(invInd) {

        let html = `
            <select name="invoiceDetailList[` + invInd + `].transLineId" id="transLineId_inv` + invInd + `"
                onchange="setTransLineName();" class="form-control valid custom-select"
                aria-invalid="false" aria-required="true" required>
        `
        html = html + `<option value="" disabled selected hidden>调度组</option>`
        for (var i = 0; i < dispatcherDeptList.length; i++) {
            html = html + `<option value="` + dispatcherDeptList[i].deptId + `">` + dispatcherDeptList[i].deptName + `</option>`
        }
        html = html + `</select>`
        html = html + `<input name="invoiceDetailList[` + invInd + `].transLineName" id="transLineName_inv` + invInd + `" type="hidden">`

        return html;
    }


    /**
     * 删除明细发货单
     *
     * @param ele
     */
    function rmInvoiceData(ele) {
        let tbodyCount = ele.closest('table').querySelectorAll('tbody[id^="invoice_"]').length;

        if (tbodyCount > 1) {
            $(ele).closest('tbody').remove();
        } else {
            //
            $(ele).closest('tbody').remove();
            addInvoiceData()
        }


    }

    /**
     * 打开地址弹框
     */
    function openAddressTab(invInd) {
        layer.open({
            type: 1,
            title: '提货到货地址',
            closeBtn: 0,
            area: ['80%', '80%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: $('#addr_inv' + invInd),
            btn: ['确认'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {

            },
            yes: function (index, layero) {
                let deliNum = 0
                let arriNum = 0
                let deliWeight = 0
                let arriWeight = 0
                let deliVolume = 0
                let arriVolume = 0

                $('input[name^="invoiceDetailList[' + invInd + '].addressVOList["][name$=".weight"]').each(function () {
                    let name = $(this).attr('name');
                    let parts = name.split('[');
                    let index = parts[2].split(']')[0];

                    let weightVal = $(this).val();

                    let addressTypeId = 'addressType_inv' + invInd + '_' + index;
                    let addressType = $("#" + addressTypeId).val();

                    //件数合计
                    let numVal = $("#num_inv"+ invInd +"_" + index).val();
                    numVal = numVal == '' || numVal == undefined? 0 : numVal
                    addressType == 0 ? deliNum = parseFloat(deliNum) + parseFloat(numVal)
                        : arriNum = parseFloat(arriNum) + parseFloat(numVal);

                    //重量合计
                    weightVal = weightVal == '' || weightVal == undefined ? 0 : weightVal
                    addressType == 0 ? deliWeight = parseFloat(deliWeight) + parseFloat(weightVal)
                        : arriWeight = parseFloat(arriWeight) + parseFloat(weightVal);

                    //体积合计
                    let volumeVal = $("#volume_inv"+ invInd +"_" + index).val();
                    volumeVal = volumeVal == '' || volumeVal == undefined ? 0 : volumeVal
                    addressType == 0 ? deliVolume = parseFloat(deliVolume) + parseFloat(volumeVal)
                        : arriVolume = parseFloat(arriVolume) + parseFloat(volumeVal);
                });

                if (deliNum != arriNum || deliWeight != arriWeight || deliVolume != arriVolume) {
                    $.modal.alertWarning("提货货量与到货货量不一致！")
                    return;
                }

                $("#numCount_inv" + invInd).val(deliNum)
                $("#weightCount_inv" + invInd).val(deliWeight)
                $("#volumeCount_inv" + invInd).val(deliVolume)


                let deliAddrSpan = '';
                let deliAddr = ''
                let arriAddrSpan = ''
                let arriAddr = ''

                let deliAddrCnt = 0
                let arriAddrCnt = 0

                $('input[name^="invoiceDetailList[' + invInd + '].addressVOList["][name$=".addrName"]').each(function () {
                    let name = $(this).attr('name');

                    let parts = name.split('[');
                    let index = parts[2].split(']')[0];

                    // var match = name.match(/\[(\d+)\]/);
                    // var index = match ? match[1] : null;

                    let addressTypeId = 'addressType_inv' + invInd + '_' + index;
                    let addressType = $("#" + addressTypeId).val();

                    let val = $(this).val();

                    if (addressType == 0 && deliAddrSpan == '') {
                        deliAddrSpan = val
                        deliAddr = $("#provinceName_inv" + invInd + "_" + index).val()
                            + $("#cityName_inv" + invInd + "_" + index).val()
                            + $("#areaName_inv" + invInd + "_" + index).val()
                            + $("#detailAddr_inv" + invInd + "_" + index).val()
                    }
                    if (addressType == 1 && arriAddrSpan == '') {
                        arriAddrSpan = val;

                        arriAddr = $("#provinceName_inv" + invInd + "_" + index).val()
                            + $("#cityName_inv" + invInd + "_" + index).val()
                            + $("#areaName_inv" + invInd + "_" + index).val()
                            + $("#detailAddr_inv" + invInd + "_" + index).val()
                    }

                    addressType == 0 && val != '' ? deliAddrCnt++ : null;
                    addressType == 1 && val != '' ? arriAddrCnt++ : null;

                });

                if (deliAddrSpan != '' && arriAddrSpan != '') {
                    $("#addrBtnA_inv" + invInd).hide()
                    $("#addrBtnB_inv" + invInd).show()

                    $("#deliAddrSpan_inv" + invInd).text(deliAddrSpan)
                    $("#deliAddrSpan_inv" + invInd).attr('data-original-title', deliAddr);
                    $("#deliAddrSpan_inv" + invInd).attr('title', deliAddr).tooltip('enable');

                    $("#arriAddrSpan_inv" + invInd).text(arriAddrSpan)
                    $("#arriAddrSpan_inv" + invInd).attr('data-original-title', arriAddr);
                    $("#arriAddrSpan_inv" + invInd).attr('title', arriAddr).tooltip('enable');

                    $("#addrBtnB_inv" + invInd + " button").html('<i class="fa fa-edit"></i> 编辑(' + deliAddrCnt + '装' + arriAddrCnt + '卸)');

                } else {
                    $("#addrBtnA_inv" + invInd).show()
                    $("#addrBtnB_inv" + invInd).hide()

                }

                $("#deliAddrCnt_inv" + invInd).val(deliAddrCnt)
                $("#arriAddrCnt_inv" + invInd).val(arriAddrCnt)


                getGuidingPrice(invInd);

                layer.close(index);

            }
        })
    }


    /**
     * 添加地址数据
     * @param invInd
     * @param type
     */
    function addAddressData(invInd, type) {

        //0提货  1到货
        let addressType = type == 'th' ? 0 : 1;

        let addrInd = 0;
        //更新下标
        if (addrIndMap.has(invInd)) {
            addrInd = addrIndMap.get(invInd) + 1;
            addrIndMap.set(invInd, addrInd);
        } else { // 如果该 id 不存在于 Map 中，则新增该键值对
            addrInd = 2
            addrIndMap.set(invInd, 2);
        }

        let html = `<tr>
                        <td>
                            <div style="text-align: center;">
                                <a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;"
                                   onclick="rmAddressData(this,` + invInd + `,'` + type + `')" title="删除选择行"></a>
                            </div>
                        </td>
                        <td>
                            <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].addrName" id="addrName_inv` + invInd + `_` + addrInd + `"
                                   type="text" placeholder="地址名称" class="form-control valid" autocomplete="off" required/>
                            <div style="position: relative;top: -1px;">
                                <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                            </div>
                        </td>
                        <td>
                            <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].contact" id="contact_inv` + invInd + `_` + addrInd + `"
                                   type="text" placeholder="联系人" class="form-control valid" autocomplete="off" required readonly/>
                        </td>
                        <td>
                            <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].mobile" id="mobile_inv` + invInd + `_` + addrInd + `"
                                   type="text" placeholder="电话" class="form-control valid" autocomplete="off" required readonly/>
                        </td>
                        <td>
                            <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].detailAddr"
                                   id="detailAddr_inv` + invInd + `_` + addrInd + `"
                                   type="text"
                                   placeholder="地址"
                                   class="form-control valid"
                                   autocomplete="off" required readonly/>
                        </td>
                        <td>
                            <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].num"
                                   id="num_inv` + invInd + `_` + addrInd + `"
                                   placeholder="件数"
                                   class="form-control"
                                   type="text"
                                   oninput="$.numberUtil.onlyNumber(this);"
                                   autocomplete="off">
                        </td>
                        <td>
                            <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].weight"
                                   id="weight_inv` + invInd + `_` + addrInd + `"
                                   placeholder="重量"
                                   class="form-control"
                                   type="text"
                                   oninput="$.numberUtil.onlyNumber(this);"
                                   required  autocomplete="off">
                        </td>
                        <td>
                            <input name="invoiceDetailList[0].addressVOList[1].volume"
                                   id="volume_inv` + invInd + `_` + addrInd + `"
                                   placeholder="体积"
                                   class="form-control"
                                   type="text"
                                   oninput="$.numberUtil.onlyNumber(this);"
                                   autocomplete="off">
                        </td>
                        <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].provinceId" id="provinceId_inv` + invInd + `_` + addrInd + `"
                               type="hidden" class="form-control valid"/>
                        <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].cityId" id="cityId_inv` + invInd + `_` + addrInd + `"
                               type="hidden" class="form-control valid"/>
                        <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].areaId" id="areaId_inv` + invInd + `_` + addrInd + `"
                               type="hidden" class="form-control valid"/>
                        <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].provinceName" id="provinceName_inv` + invInd + `_` + addrInd + `"
                               type="hidden" class="form-control valid"/>
                        <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].cityName" id="cityName_inv` + invInd + `_` + addrInd + `"
                               type="hidden" class="form-control valid"/>
                        <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].areaName" id="areaName_inv` + invInd + `_` + addrInd + `"
                               type="hidden" class="form-control valid"/>
                        <input name="invoiceDetailList[` + invInd + `].addressVOList[` + addrInd + `].addressType" id="addressType_inv` + invInd + `_` + addrInd + `" value="` + addressType + `"
                               type="hidden" class="form-control valid"/>
                    </tr>`


        $('#address_inv' + invInd + `_` + type).append(html);

        initDeliveryBsSuggest(addressType, invInd, addrInd);
    }

    function rmAddressData(ele, invInd, type) {
        let trCount = ele.closest('tbody').querySelectorAll('tr').length;

        if (trCount > 1) {
            $(ele).closest('tr').remove();
        } else {
            //
            $(ele).closest('tr').remove();
            addAddressData(invInd, type)
        }
    }

    /**
     * 提交
     */
    function submitHandler(type) {
        if ($.validate.form()) {
            /*
             * 提货日期选择：
             *      以整点时间为准；若选择了radio,则以radio为准；否则以选择得整点时间
             *      两者必须二选一
             */
            //所选整点时间
            // var reqDeliDateHour = $("#reqDeliDateHour").val();
            // var radio =$('input:radio:checked').val();
            // if($.common.isEmpty(reqDeliDateHour) && $.common.isEmpty(radio)){
            //     $.modal.msgError("提货日期未选择整点时间！");
            //     return ;
            // }

            //要求提货日期
            // var reqDeliDate = $("#reqDeliDate").val();
            // if($.common.isNotEmpty(radio)){
            //     reqDeliDate = reqDeliDate + " " + radio;
            // }else{
            //     reqDeliDate = reqDeliDate + " "+reqDeliDateHour+":00:00";
            // }
            // //赋值提货日期
            // $("#reqDeliDateTime").val(reqDeliDate);

            /*
            * 比较要求提货日（到货日）日期大小
            */
            // var reqArriDate = $("#reqArriDate").val();
            // var reqArriTime = new Date(reqArriDate.replace("-", "/").replace("-", "/"));
            // var reqDeliTime = new Date(reqDeliDate.replace("-", "/").replace("-", "/"));
            // if(reqDeliTime > reqArriTime){
            //     $.modal.msgError("要求提货日期不能大于要求到货日期！");
            //     return ;
            // }

            //校验提货方
            // if(fhdz_addr_list.length === 0){
            //     $.modal.msgError("请选择发货方信息！");
            //     return;
            // }
            //
            // //校验收货方
            // if(shdz_addr_list.length === 0){
            //     $.modal.msgError("请选择收货方信息！");
            //     return;
            // }

            // let otherFee = $("#otherFee").val();
            // let otherFeeType = $("#otherFeeType").val();
            //
            // if (otherFee != null && otherFee != '' && (otherFeeType == null || otherFeeType == '')) {
            //     $.modal.msgError("请选择其他费用的费用类型！");
            //     return;
            // }


            //校验收货地址与i退货地址是否相同
            // var deliveryId = $("#deliveryId").val();
            // var arrivalId = $("#arrivalId").val();
            //
            // if (deliveryId == arrivalId) {
            //     $.modal.msgError("提货地址与收货地址不能相同！");
            //     return;
            // }

            // let goodsBl = false;
            // Object.values(addr_goods_index).forEach((value) => {
            //     if (value.length === 0) {
            //         goodsBl = true
            //         return;
            //     }
            // })
            // if (goodsBl) {
            //     $.modal.msgError("货品不能为空！");
            //     return;
            // }
            //
            // //判断货品收货 发货  是否相等
            // let cg = checkGoods();
            // if (!cg){
            //     $.modal.msgError("请检查提货货品与到货货品是否一致！");
            //     return;
            // }

            //是否多装多卸
            // let  isMultiple = $("#isMultiple").val()
            //
            // //指导价
            // var guidingPrice = $("#guidingPrice").val();

            //校验指导价 如果带出的最新知道是待审核状态 则无法修改
            // if (guidingPriceCheckStust === 1 && Number(guidingPriceOld) != Number(guidingPrice)) {
            //     $.modal.msgError("存在待审核的指导价，无法存入新指导价！");
            //     return;
            // }

            //判断 收款金额不能大于总金额
            // let balaType = $("#balaType").val();
            // if (balaType == '2' || balaType == '6') {
            //     let collectAmount = parseFloat($("#collectAmount").val());
            //     let costAmount = parseFloat($("#costAmount").val());
            //     if (collectAmount > costAmount) {
            //         $.modal.msgError("结算金额不能大于总金额！");
            //         return;
            //     }
            // }

            //
            // var transType = $("#transCode").val();
            // if (transType == '0' || transType == '4' || transType == '15') {
            //     if(($.common.isEmpty(guidingPrice) || guidingPrice === '0') && isMultiple == '0'  && crtGuidePrice ==1) {
            //         $.modal.confirm("指导价为0，是否确认提交？", function () {
            //             subscribe(type)
            //         });
            //         return;
            //     }
            // }

            // subscribe(type)

            let weightCountB = false
            $("[id^='weightCount_inv']").each(function() {
                if ($(this).val() <= 0) {
                    weightCountB = true
                    return
                }
            });
            if (weightCountB) {
                $.modal.msgError("重量需要大于0！");
                return;
            }

            let deliAddrCnt = false
            let arriAddrCnt = false
            $("[id^='deliAddrCnt_inv']").each(function() {
                if ($(this).val() == 0) {
                    deliAddrCnt = true
                    return
                }
            });
            $("[id^='arriAddrCnt_inv']").each(function() {
                if ($(this).val() == 0) {
                    arriAddrCnt = true
                    return
                }
            });

            if (deliAddrCnt || arriAddrCnt) {
                $.modal.msgError("请选择地址！");
                return;
            }


            $('#projectFile').fileinput('upload');
            jQuery.subscribe("cmt", commit);

        }
    }


    function commit() {
        var dis = $(":disabled");
        dis.attr("disabled", false);
        var formData = $("#form-invoice-add").serializeArray();
        // data.push({"name": "numCount", "value": $("#fhdz_numCount").val()});
        // data.push({"name": "weightCount", "value": $("#fhdz_weightCount").val()});
        // data.push({"name": "volumeCount", "value": $("#fhdz_volumeCount").val()});
        // data.push({"name": "costAmount", "value": $("#fhdz_costAmount").val()});

        $.each(formData, function (index, item) {
            if (item.name.endsWith("reqDeliDate")) {
                var date = item.value.replace("点", ":00:00");
                item.value = date;
            }
        });

        $.operate.saveTab(prefix + "/add", formData, function (result) {
            if (result.code != 0) {
                dis.attr("disabled", true);
            }
        });
    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num, v) {
        var vv = Math.pow(10, v);
        return Math.round(num * vv) / vv;
    }


    /**
     * [isDuringDate 比较当前时间是否在指定时间段内]
     * @param    date   [beginDateStr] [开始时间]
     * @param    date   [endDateStr]   [结束时间]
     * @return   Boolean
     */
    function isDuringDate(beginDateStr, endDateStr) {
        let now = new Date()
        let month = now.getMonth() + 1;
        let day = now.getDate();
        let output = now.getFullYear() + '/' + (month < 10 ? '0' : '') + month + '/' + (day < 10 ? '0' : '') + day;
        var curDate = new Date(output)

        var beginDate = beginDateStr == null ? new Date('1900-01-01') : new Date(beginDateStr)
        var endDate = endDateStr == null ? new Date('2900-01-01') : new Date(endDateStr)

        if (curDate >= beginDate && curDate <= endDate) {
            return true;
        }
        return false;
    }

    function uuid(len, radix) {
        var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
        var uuid = [], i;
        radix = radix || chars.length;

        if (len) {
            // Compact form
            for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix];
        } else {
            // rfc4122, version 4 form
            var r;

            // rfc4122 requires these characters
            uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
            uuid[14] = '4';

            // Fill in random data.  At i==19 set the high bits of clock sequence as
            // per rfc4122, sec. 4.1.5
            for (i = 0; i < 36; i++) {
                if (!uuid[i]) {
                    r = 0 | Math.random() * 16;
                    uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
                }
            }
        }

        return uuid.join('');
    }
</script>
</body>
</html>