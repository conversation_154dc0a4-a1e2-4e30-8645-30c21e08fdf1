<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：detail')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
    <link th:href="@{/ruoyi/css/hg-form-detail.css}" rel="stylesheet"/>
</head>
<style>
    .timeline{
        /*width: 300px;*/

        position: relative;
    }
    .borgreen{
        border-top: 2px #0ba687 solid;
    }
    .bordark{
        border-top: 2px #bfbfbf solid;
    }
    .timeline_point{
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        /*background: #0ba687;*/
        top: -7px;
        left: 50px;
    }

    .tc{
        text-align: center;
    }

    .timeline_vertical {
        /*position: absolute;*/
        /*right: 23px;*/
        /*top: 100px;*/
        padding: 100px 0 0 0;
        margin-left: -3px;
    }
    .timeline_text{
        /*width: 150px;*/
        text-align: center;
        /*border-top-right-radius: 20px;*/
        /*height: 100px;*/
        box-sizing: border-box;
        padding: 20px 0 0 10px;
        line-height: 20px;
    }
    .borrgreen{
        border-right: 3px #0ba687 solid;
    }
    .borrdark{
        border-right: 3px #bfbfbf solid;
    }
    .lazur-bg{
        background: #bfbfbf;
        width: 20px;
        height: 20px;
    }
    .lawarn-bg{
        background: #0ba687;
        width: 20px;
        height: 20px;
    }
    .vertical-container {
        width: 100%;
        max-width: none;
        margin: 0 auto;
    }
    .vertical-timeline-content{
        margin-left: 20px;
    }
    .box_timeline{

    }
    .fl{
        float: left;
    }
    .clear{
        clear: both;
    }
    .fc80{
        color: #808080;
    }
    .fw{
        font-weight: bold;
    }
    .f12{
        font-size: 12px;
    }
    .timeline_vertical-container {
        position: relative;
        padding: 0;
        margin-top: 0em;
        margin-bottom: 0em;
    }
    .vertical-timeline-block::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0px;
        width: 3px;
        height: 100%;
        background: #0ba687;
    }
    .vertical-timeline-block-dark::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0px;
        width: 3px;
        height: 100%;
        background: #bfbfbf;
    }
    /*.timeline_vertical-container.light-timeline:before {*/
    /*    background: #0ba687;*/
    /*}*/
    /*.timeline_vertical-container::before {*/
    /*    left: 0px;*/
    /*}*/
    .vertical-timeline-icon {
        left: -8px;
        top: auto;
    }
    .vertical-timeline-block {
        margin: 0 0px;
    }
    .vertical-timeline-block-dark {
        margin: 0 0px;
    }
    .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-xs-1, .col-xs-10, .col-xs-11, .col-xs-12, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9 {
        padding-left: 0px;
        padding-right: 0px;
    }
    .bggreen{
        background: #0ba687;
    }
    .bgdark{
        background: #bfbfbf;
    }
    .fc1ab{
        color: #1ab394;
    }
    .eclipse{
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .over{
        overflow: hidden;
    }
    .mt20{
        margin-top: 20px;
    }
    .f16{
        font-size: 16px;
    }
    .round{
        width: 5px;
        height: 5px;
        border-radius: 50%;
        position: absolute;
        top: 0;
    }
    .bg00a{
        background: #00a65a;
    }
    .bgbf{
        background: #bfbfbf;
    }
    .rour{
        right: -3px;
    }
    .roul{
        left: -3px;
    }
    .dashed{
        padding: 0 0px 20px 0;
        /*border-right: 1px dashed #00a65a;*/
        position: relative;
    }
    .bordash{
        border-right: 1px dashed #00a65a;
    }
    .bordashda{
        border-right: 1px dashed #bfbfbf;
    }
    .bordashle{
        border-left: 1px dashed #00a65a;
    }
    .bordashleda{
        border-left: 1px dashed #bfbfbf;
    }
    .vercontent{
        border-right: 2px solid #00a65a;
        box-sizing: border-box;
        border-top-right-radius: 30px;
        width: 100%;
        height: 270px;
    }
    .vercontent2{
        border-left: 2px solid #00a65a;
        box-sizing: border-box;
        padding: 0px 0 20px 0;
        margin-left: -2px !important;
    }
    .row{
        margin: 0;
    }
    .flowcontent{
        border: 1px #e1e2e3 solid;
        border-top: 0;
        width: 100%;
        height: 250px;
        overflow: auto;
        box-sizing: border-box;
        padding: 0 5px;
    }
    .timeline_point2 {
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        /* background: #0ba687; */
        top: -7px;
        left: 50%;
        margin-left: -7px;
    }
    .padbt20{
        padding-bottom: 20px;
    }
</style>
<body>
<div class="form-content">
    <div class="panel">
        <div class="over">
            <div class="fl fw f16">发货单编号:[[${invoice.vbillno}]]</div>
            <div class="fl fw f16" style="margin-left: 40px">创建人:[[${invoice.regUserName}]]</div>
        </div>
        <div class="row" style="margin-top: 20px">
            <div class="col-sm-3 col-lg-2">
                <div class="timeline borgreen">
                    <div class="timeline_point bggreen"></div>
                    <div class="row">
                        <div class="col-sm-10" style="padding: 0">
                            <div class="timeline_text bordash">
                                <div class="fw">新建单据</div>
                                <div class="f12 fc80">[[${invoice.regUserName}]]</div>
                                <div class="f12 fc80">[[${#dates.format(invoice.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                            </div>
                            <div class="" th:if="${#lists.size(receiveDetailList) > 0}">
                                <div class="tc bordash fw fc1ab" style="padding: 20px 0">应收</div>
                                <div class="dashed bordash" th:each="receiveDetail:${receiveDetailList}">
                                    <div class="f12 fc80 tc">[[${#dates.format(receiveDetail.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                    <div class="f12 tc">[[${receiveDetail.regUserName + '创建：￥'+receiveDetail.transFeeCount}]]</div>
                                    <div class="round rour bg00a"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2" style="padding: 0">
                            <div class="vercontent" th:if="${#lists.size(operationHistoryVOList) > 1}"></div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="col-sm-9 col-lg-10 ">
                <div class="row"
                     th:if="${#lists.size(operationHistoryVOList) > 0}"
                     th:each="operationHistoryVO,stat:${operationHistoryVOList}"
                     th:classappend="${stat.count >1?'vercontent2':'padbt20'}">
                    <div class="timeline col-sm-4 borgreen" >
                        <div class="timeline_point bggreen"></div>
                        <div class="row">
                            <div class="col-sm-6" style="padding: 0">
                                <div class="timeline_text">
                                    <div class="fw">[[${operationHistoryVO.vbillno}]]</div>
                                    <div class="f12 fc80">[[${operationHistoryVO.regUserName}]]</div>
                                    <div class="f12 fc80">[[${#dates.format(operationHistoryVO.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
<!--                                    <div class="f12 fc80">这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注</div>-->
                                </div>
                            </div>
                            <div class="col-sm-6" style="padding: 0" >
                                <div class="flowcontent" th:if="${#lists.size(operationHistoryVO.payDetailList) > 0}">
                                    <div class="" style="padding-left: 5px">
                                        <div class="tc bordashle fw fc1ab" style="padding: 20px 0">应付</div>
                                        <div class="dashed bordashle"
                                             th:each="payDetail:${operationHistoryVO.payDetailList}">
                                            <div class="f12 fc80 tc">[[${#dates.format(payDetail.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                            <div class="f12 tc">[[${payDetail.regUserId + '创建：￥'+payDetail.transFeeCount}]]</div>
                                            <div class="round roul bg00a"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="timeline col-sm-4"
                         th:classappend="${operationHistoryVO.vbillstatus == '2'
                         || operationHistoryVO.vbillstatus == '3' ?'borgreen':'bordark'}">
                        <div class="timeline_point"
                             th:classappend="${operationHistoryVO.vbillstatus == '2'
                             || operationHistoryVO.vbillstatus == '3' ?'bggreen':'bgdark'}"></div>
                        <div class="row">
                            <div class="col-sm-6" style="padding: 0">
                                <div class="timeline_text">
                                    <div class="fw">提货</div>
                                    <div class="f12 fc80">[[${operationHistoryVO.pickUpUserName}]]</div>
                                    <div class="f12 fc80">[[${#dates.format(operationHistoryVO.pickUpDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                </div>
                            </div>
                            <div class="col-sm-6" style="padding: 0" th:if="${#lists.size(operationHistoryVO.carLocusList) > 0}">
                                <div class="flowcontent">
                                    <div class="" style="padding-left: 5px">
                                        <div class="tc bordashle fw fc1ab" style="padding: 20px 0">行程跟踪</div>
                                        <div class="dashed bordashle"
                                             th:each="carLocus:${operationHistoryVO.carLocusList}">
                                            <div class="f12 fc80 tc">[[${#dates.format(carLocus.trackingTime, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                            <div class="f12 tc">[[${carLocus.proName+carLocus.cityName+carLocus.areaName+carLocus.detailAddr}]]</div>
                                            <div class="round roul bg00a"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="timeline col-sm-2"
                         th:classappend="${operationHistoryVO.vbillstatus == '3' ?'borgreen':'bordark'}">
                        <div class="timeline_point2"
                             th:classappend="${operationHistoryVO.vbillstatus == '3' ?'bggreen':'bgdark'}"></div>
                        <div class="row">
                            <div class="col-sm-12" style="padding: 0">
                                <div class="timeline_text">
                                    <div class="fw">到货</div>
                                    <div class="f12 fc80">[[${operationHistoryVO.arrivalsUserName}]]</div>
                                    <div class="f12 fc80">[[${#dates.format(operationHistoryVO.arrivalsDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="timeline col-sm-2"
                         th:classappend="${operationHistoryVO.receiptMan != null ?'borgreen':'bordark'}">
                        <div class="timeline_point2"
                             th:classappend="${operationHistoryVO.receiptMan != null ?'bggreen':'bgdark'}"></div>
                        <div class="row">
                            <div class="col-sm-12" style="padding: 0">
                                <div class="timeline_text">
                                    <div class="fw">回单</div>
                                    <div class="f12 fc80">[[${operationHistoryVO.receiptMan}]]</div>
                                    <div class="f12 fc80">[[${#dates.format(operationHistoryVO.receiptDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <div style="padding: 0 0 20px 0" class="row" th:if="${#lists.size(operationHistoryVOList) == 0}">
                    <div class="timeline col-sm-3 bordark">
                        <div class="timeline_point bgdark"></div>
                        <div class="row">
                            <div class="col-sm-6" style="padding: 0">
                                <div class="timeline_text">
                                    <div class="fw">调度</div>
                                </div>
                            </div>
                            <div class="col-sm-6" style="padding: 0">
                            </div>

                        </div>
                    </div>
                    <div class="timeline col-sm-3 bordark">
                        <div class="timeline_point bgdark"></div>
                        <div class="row">
                            <div class="col-sm-6" style="padding: 0">
                                <div class="timeline_text">
                                    <div class="fw">提货</div>
                                </div>
                            </div>
                            <div class="col-sm-6" style="padding: 0">
                            </div>

                        </div>
                    </div>
                    <div class="timeline col-sm-2 bordark">
                        <div class="timeline_point2 bgdark"></div>
                        <div class="row">
                            <div class="col-sm-12" style="padding: 0">
                                <div class="timeline_text">
                                    <div class="fw">到货</div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="timeline col-sm-2 bordark">
                        <div class="timeline_point2 bgdark"></div>
                        <div class="row">
                            <div class="col-sm-12" style="padding: 0">
                                <div class="timeline_text">
                                    <div class="fw">回单</div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!--                <div class="row vercontent2">-->
<!--                    <div class="timeline col-sm-3 borgreen">-->
<!--                        <div class="timeline_point bggreen"></div>-->
<!--                        <div class="row">-->
<!--                            <div class="col-sm-5" style="padding: 0">-->
<!--                                <div class="timeline_text">-->
<!--                                    <div class="fw">调度一</div>-->
<!--                                    <div class="f12 fc80">张三三</div>-->
<!--                                    <div class="f12 fc80">2021-09-09 12:00:00</div>-->
<!--                                    <div class="f12 fc80 eclipse">这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注</div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-sm-7" style="padding: 0">-->
<!--                                <div class="flowcontent">-->
<!--                                    <div class="" style="padding-left: 5px">-->
<!--                                        <div class="tc bordashle fw fc1ab" style="padding: 20px 0">应付</div>-->
<!--                                        <div class="dashed bordashle">-->
<!--                                            <div class="f12 fc80 tc">2021-09-09 12:00:00</div>-->
<!--                                            <div class="f12 tc">系统创建:￥400</div>-->
<!--                                            <div class="round roul bg00a"></div>-->
<!--                                        </div>-->
<!--                                        <div class="dashed bordashle">-->
<!--                                            <div class="f12 fc80 tc">2021-09-09 12:00:00</div>-->
<!--                                            <div class="f12 tc">系统创建:￥400</div>-->
<!--                                            <div class="round roul bg00a"></div>-->
<!--                                        </div>-->
<!--                                        <div class="dashed bordashleda">-->
<!--                                            <div class="f12 fc80 tc">2021-09-09 12:00:00</div>-->
<!--                                            <div class="f12 tc">系统创建:￥400</div>-->
<!--                                            <div class="round roul bgbf"></div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="timeline col-sm-3 borgreen">-->
<!--                        <div class="timeline_point bggreen"></div>-->
<!--                        <div class="row">-->
<!--                            <div class="col-sm-5" style="padding: 0">-->
<!--                                <div class="timeline_text">-->
<!--                                    <div class="fw">提货</div>-->
<!--                                    <div class="f12 fc80">张三三</div>-->
<!--                                    <div class="f12 fc80">2021-09-09 12:00:00</div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-sm-7" style="padding: 0">-->
<!--                                <div class="flowcontent">-->
<!--                                    <div class="" style="padding-left: 5px">-->
<!--                                        <div class="tc bordashle fw fc1ab" style="padding: 20px 0">行程跟踪</div>-->
<!--                                        <div class="dashed bordashle">-->
<!--                                            <div class="f12 fc80 tc">2021-09-09 12:00:00</div>-->
<!--                                            <div class="f12 tc">系统创建:￥400</div>-->
<!--                                            <div class="round roul bg00a"></div>-->
<!--                                        </div>-->
<!--                                        <div class="dashed bordashle">-->
<!--                                            <div class="f12 fc80 tc">2021-09-09 12:00:00</div>-->
<!--                                            <div class="f12 tc">系统创建:￥400</div>-->
<!--                                            <div class="round roul bg00a"></div>-->
<!--                                        </div>-->
<!--                                        <div class="dashed bordashleda">-->
<!--                                            <div class="f12 fc80 tc">2021-09-09 12:00:00</div>-->
<!--                                            <div class="f12 tc">系统创建:￥400</div>-->
<!--                                            <div class="round roul bgbf"></div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="timeline col-sm-2 bordark">-->
<!--                        <div class="timeline_point2 bgdark"></div>-->
<!--                        <div class="row">-->
<!--                            <div class="col-sm-12" style="padding: 0">-->
<!--                                <div class="timeline_text">-->
<!--                                    <div class="fw">到货</div>-->
<!--                                    <div class="f12 fc80">张三三</div>-->
<!--                                    <div class="f12 fc80">2021-09-09 12:00:00</div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="timeline col-sm-2 bordark">-->
<!--                        <div class="timeline_point2 bgdark"></div>-->
<!--                        <div class="row">-->
<!--                            <div class="col-sm-12" style="padding: 0">-->
<!--                                <div class="timeline_text">-->
<!--                                    <div class="fw">回单</div>-->
<!--                                    <div class="f12 fc80">张三三</div>-->
<!--                                    <div class="f12 fc80">2021-09-09 12:00:00</div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                        </div>-->
<!--                    </div>-->
<!--&lt;!&ndash;                    <div class="timeline col-sm-2 bordark">&ndash;&gt;-->
<!--&lt;!&ndash;                        <div class="timeline_point2 bgdark"></div>&ndash;&gt;-->
<!--&lt;!&ndash;                        <div class="row">&ndash;&gt;-->
<!--&lt;!&ndash;                            <div class="col-sm-12" style="padding: 0">&ndash;&gt;-->
<!--&lt;!&ndash;                                <div class="timeline_text">&ndash;&gt;-->
<!--&lt;!&ndash;                                    <div class="fw">签收</div>&ndash;&gt;-->
<!--&lt;!&ndash;                                    <div class="f12 fc80">张三三</div>&ndash;&gt;-->
<!--&lt;!&ndash;                                    <div class="f12 fc80">2021-09-09 12:00:00</div>&ndash;&gt;-->
<!--&lt;!&ndash;                                </div>&ndash;&gt;-->
<!--&lt;!&ndash;                            </div>&ndash;&gt;-->

<!--&lt;!&ndash;                        </div>&ndash;&gt;-->
<!--&lt;!&ndash;                    </div>&ndash;&gt;-->
<!--                </div>-->
<!--                <div class="row vercontent2">-->
<!--                    <div class="timeline col-sm-3 borgreen">-->
<!--                        <div class="timeline_point bggreen"></div>-->
<!--                        <div class="row">-->
<!--                            <div class="col-sm-5" style="padding: 0">-->
<!--                                <div class="timeline_text">-->
<!--                                    <div class="fw">调度一</div>-->
<!--                                    <div class="f12 fc80">张三三</div>-->
<!--                                    <div class="f12 fc80">2021-09-09 12:00:00</div>-->
<!--                                    <div class="f12 fc80 eclipse">这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注</div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-sm-7" style="padding: 0">-->
<!--                                <div class="flowcontent">-->
<!--                                    <div class="" style="padding-left: 5px">-->
<!--                                        <div class="tc bordashle fw fc1ab" style="padding: 20px 0">应付</div>-->
<!--                                        <div class="dashed bordashle">-->
<!--                                            <div class="f12 fc80 tc">2021-09-09 12:00:00</div>-->
<!--                                            <div class="f12 tc">系统创建:￥400</div>-->
<!--                                            <div class="round roul bg00a"></div>-->
<!--                                        </div>-->
<!--                                        <div class="dashed bordashle">-->
<!--                                            <div class="f12 fc80 tc">2021-09-09 12:00:00</div>-->
<!--                                            <div class="f12 tc">系统创建:￥400</div>-->
<!--                                            <div class="round roul bg00a"></div>-->
<!--                                        </div>-->
<!--                                        <div class="dashed bordashleda">-->
<!--                                            <div class="f12 fc80 tc">2021-09-09 12:00:00</div>-->
<!--                                            <div class="f12 tc">系统创建:￥400</div>-->
<!--                                            <div class="round roul bgbf"></div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="timeline col-sm-3 borgreen">-->
<!--                        <div class="timeline_point bggreen"></div>-->
<!--                        <div class="row">-->
<!--                            <div class="col-sm-5" style="padding: 0">-->
<!--                                <div class="timeline_text">-->
<!--                                    <div class="fw">提货</div>-->
<!--                                    <div class="f12 fc80">张三三</div>-->
<!--                                    <div class="f12 fc80">2021-09-09 12:00:00</div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="col-sm-7" style="padding: 0">-->
<!--                                <div class="flowcontent">-->
<!--                                    <div class="" style="padding-left: 5px">-->
<!--                                        <div class="tc bordashle fw fc1ab" style="padding: 20px 0">行程跟踪</div>-->
<!--                                        <div class="dashed bordashle">-->
<!--                                            <div class="f12 fc80 tc">2021-09-09 12:00:00</div>-->
<!--                                            <div class="f12 tc">系统创建:￥400</div>-->
<!--                                            <div class="round roul bg00a"></div>-->
<!--                                        </div>-->
<!--                                        <div class="dashed bordashle">-->
<!--                                            <div class="f12 fc80 tc">2021-09-09 12:00:00</div>-->
<!--                                            <div class="f12 tc">系统创建:￥400</div>-->
<!--                                            <div class="round roul bg00a"></div>-->
<!--                                        </div>-->
<!--                                        <div class="dashed bordashleda">-->
<!--                                            <div class="f12 fc80 tc">2021-09-09 12:00:00</div>-->
<!--                                            <div class="f12 tc">系统创建:￥400</div>-->
<!--                                            <div class="round roul bgbf"></div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="timeline col-sm-2 bordark">-->
<!--                        <div class="timeline_point2 bgdark"></div>-->
<!--                        <div class="row">-->
<!--                            <div class="col-sm-12" style="padding: 0">-->
<!--                                <div class="timeline_text">-->
<!--                                    <div class="fw">到货</div>-->
<!--                                    <div class="f12 fc80">张三三</div>-->
<!--                                    <div class="f12 fc80">2021-09-09 12:00:00</div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="timeline col-sm-2 bordark">-->
<!--                        <div class="timeline_point2 bgdark"></div>-->
<!--                        <div class="row">-->
<!--                            <div class="col-sm-12" style="padding: 0">-->
<!--                                <div class="timeline_text">-->
<!--                                    <div class="fw">回单</div>-->
<!--                                    <div class="f12 fc80">张三三</div>-->
<!--                                    <div class="f12 fc80">2021-09-09 12:00:00</div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                        </div>-->
<!--                    </div>-->
<!--&lt;!&ndash;                    <div class="timeline col-sm-2 bordark">&ndash;&gt;-->
<!--&lt;!&ndash;                        <div class="timeline_point2 bgdark"></div>&ndash;&gt;-->
<!--&lt;!&ndash;                        <div class="row">&ndash;&gt;-->
<!--&lt;!&ndash;                            <div class="col-sm-12" style="padding: 0">&ndash;&gt;-->
<!--&lt;!&ndash;                                <div class="timeline_text">&ndash;&gt;-->
<!--&lt;!&ndash;                                    <div class="fw">签收</div>&ndash;&gt;-->
<!--&lt;!&ndash;                                    <div class="f12 fc80">张三三</div>&ndash;&gt;-->
<!--&lt;!&ndash;                                    <div class="f12 fc80">2021-09-09 12:00:00</div>&ndash;&gt;-->
<!--&lt;!&ndash;                                </div>&ndash;&gt;-->
<!--&lt;!&ndash;                            </div>&ndash;&gt;-->

<!--&lt;!&ndash;                        </div>&ndash;&gt;-->
<!--&lt;!&ndash;                    </div>&ndash;&gt;-->
<!--                </div>-->
            </div>
        </div>

    </div>

</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">

    var prefix = ctx + "basic/carrier";
    //车辆条数
    var carStatSizes = $("#carStatSizes").val()-0;
    //司机条数
    var driverStatSizes = $("#driverStatSizes").val()-0;

    //初始化
    $(function () {
        //展开，折叠
        $('#collapseOne').collapse('show');
        $('#collapseLine').collapse('show');

        //获取当前毫秒数
        var now = new Date(getNowFormatDate()).getTime();
        for(var i = 0;i<carStatSizes;i++){
            $('#car'+i).collapse('show');
            $('#actualCarrier'+i).collapse('show');


            var validUntilCar = $('#validUntilCar'+i).text();
            var validUntilCarTime = new Date(validUntilCar).getTime();
            if(now > validUntilCarTime){
                $('#validUntilCar'+i).css("color","red");
            }

            var validUntil = $('#validUntil'+i).text();
            var validUntilTime = new Date(validUntil).getTime();
            if(now > validUntilTime){
                $('#validUntil'+i).css("color","red");
            }
        }

        for(var i = 0;i<driverStatSizes;i++){
            $('#driverDetail'+i).collapse('show');
            var validperiodto = $('#validperiodto'+i).text();
            var validperiodtoTime = new Date(validperiodto).getTime();
            if(now > validperiodtoTime){
                $('#validperiodto'+i).css("color","red");
            }
        }

        // var nowDate = new Date();
        // //承运人有效期至
        // var validperiodtoDate = new Date(validperiodto);
        // if(nowDate.getTime() > validperiodtoDate.getTime()){
        //     console.log('第一个大');
        // } else {
        //     console.log('第二个大');
        // }
    });

    /**
     *  审核
     * @param flag
     */
    var carrierId = $("#carrierId").val();
    function check(flag){
        var data = { "ids":carrierId,"flag":flag};
        var url = prefix + "/check";
        $.operate.saveTab(url, data);
    }

    function getNowFormatDate() {
        var date = new Date();
        var seperator1 = "-";
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + seperator1 + month + seperator1 + strDate;
        return currentdate;
    }

</script>
</body>

</html>