<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('发货单明细')"/>
</head>
<style>
    .timelineA {
        list-style: none;
        padding: 0 0 30px;
        position: relative;
        margin-top: -20px
    }

    .timelineA:before {
        top: 30px;
        bottom: 25px;
        position: absolute;
        content: " ";
        width: 3px;
        background-color: #ccc;
        left: 25px;
        margin-right: -1.5px
    }

    .timelineA > li, .timelineA > li > .timelineA-panel {
        margin-bottom: -2px;
        position: relative
    }

    .timelineA > li:after, .timelineA > li:before {
        content: " ";
        display: table
    }

    .timelineA > li:after {
        clear: both
    }

    .timelineA > li > .timelineA-panel {
        margin-left: 40px;
        float: left;
        top: 24px;
        padding: 4px 10px 8px 15px;
        /*border: 1px solid #ccc;*/
        border-radius: 5px;
        width: 95%
    }

    .timelineA > li > .timelineA-badge {
        color: #fff;
        width: 22px;
        height: 22px;
        line-height: 24px;
        font-size: 1em;
        text-align: center;
        position: absolute;
        top: 30px;
        left: 15px;
        margin-right: -25px;
        background-color: #fff;
        z-index: 100;
        border-radius: 20%;
        font-weight: 600
    }

    .timelineA > li.timelineA-inverted > .timelineA-panel {
        float: left
    }

    .timelineA > li.timelineA-inverted > .timelineA-panel:before {
        border-right-width: 0;
        border-left-width: 15px;
        right: -15px;
        left: auto
    }

    .timelineA > li.timelineA-inverted > .timelineA-panel:after {
        border-right-width: 0;
        border-left-width: 14px;
        right: -14px;
        left: auto
    }

    .timelineA-badge.primary {
        background-color: #2e6da4 !important
    }

    .timelineA-badge.success {
        background-color: #1ab394 !important
    }

    .timelineA-badge.warning {
        background-color: #f8ac59 !important
    }

    .timelineA-badge.danger {
        background-color: #d9534f !important
    }

    .timelineA-badge.info {
        background-color: #5bc0de !important
    }

    .timelineA-title {
        margin-top: 0;
        color: inherit
    }

    .timelineA-body > p, .timelineA-body > ul {
        background-color: #eee;
        padding: 4px;
        margin-top: 10px;
    }

    .timelineA-body > p + p {
        /*margin-top: 10px;*/
        margin-left: 10px
    }

    .timelineA-badge > .glyphicon {
        margin-right: 0px;
        color: #fff
    }

    .input-group {
        display: block;
    }

    .flex {
        display: flex;
        algin-items: center;
        just-content: space-between;

    }

    .flex_left {
        width: 100px;
        line-height: 30px;
        text-align: right;
        color: #808080;
    }

    .flex_right {
        min-width: 0;
        flex: 1;
        line-height: 30px;
        /*line-height: 26px;*/
    }

    .panel-default > .panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }

    .timeline {
        /*width: 300px;*/

        position: relative;
    }

    .borgreen {
        border-top: 2px #0ba687 solid;
    }

    .bordark {
        border-top: 2px #bfbfbf solid;
    }

    .timeline_point {
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        /*background: #0ba687;*/
        top: -7px;
        left: 50px;
    }

    .tc {
        text-align: center;
    }

    .timeline_vertical {
        /*position: absolute;*/
        /*right: 23px;*/
        /*top: 100px;*/
        padding: 100px 0 0 0;
        margin-left: -3px;
    }

    .timeline_text {
        /*width: 150px;*/
        text-align: center;
        /*border-top-right-radius: 20px;*/
        /*height: 100px;*/
        box-sizing: border-box;
        padding: 20px 0 0 10px;
        line-height: 20px;
    }

    .borrgreen {
        border-right: 3px #0ba687 solid;
    }

    .borrdark {
        border-right: 3px #bfbfbf solid;
    }

    .lazur-bg {
        background: #bfbfbf;
        width: 20px;
        height: 20px;
    }

    .lawarn-bg {
        background: #0ba687;
        width: 20px;
        height: 20px;
    }

    .vertical-container {
        width: 100%;
        max-width: none;
        margin: 0 auto;
    }

    .vertical-timeline-content {
        margin-left: 20px;
    }

    .box_timeline {

    }

    .fl {
        float: left;
    }

    .clear {
        clear: both;
    }

    .fc80 {
        color: #808080;
    }

    .fw {
        font-weight: bold;
    }

    .f12 {
        font-size: 12px;
    }

    .timeline_vertical-container {
        position: relative;
        padding: 0;
        margin-top: 0em;
        margin-bottom: 0em;
    }

    .vertical-timeline-block::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0px;
        width: 3px;
        height: 100%;
        background: #0ba687;
    }

    .vertical-timeline-block-dark::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0px;
        width: 3px;
        height: 100%;
        background: #bfbfbf;
    }

    /*.timeline_vertical-container.light-timeline:before {*/
    /*    background: #0ba687;*/
    /*}*/
    /*.timeline_vertical-container::before {*/
    /*    left: 0px;*/
    /*}*/
    .vertical-timeline-icon {
        left: -8px;
        top: auto;
    }

    .vertical-timeline-block {
        margin: 0 0px;
    }

    .vertical-timeline-block-dark {
        margin: 0 0px;
    }

    .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-xs-1, .col-xs-10, .col-xs-11, .col-xs-12, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9 {
        padding-left: 0px;
        padding-right: 0px;
    }

    .bggreen {
        background: #0ba687;
    }

    .bgdark {
        background: #bfbfbf;
    }

    .fc1ab {
        color: #1ab394;
    }

    .eclipse {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .over {
        overflow: hidden;
    }

    .mt20 {
        margin-top: 20px;
    }

    .f16 {
        font-size: 16px;
    }

    .round {
        width: 5px;
        height: 5px;
        border-radius: 50%;
        position: absolute;
        top: 0;
    }

    .bg00a {
        background: #00a65a;
    }

    .bgbf {
        background: #bfbfbf;
    }

    .rour {
        right: -3px;
    }

    .roul {
        left: -3px;
    }

    .dashed {
        padding: 0 20px 20px 0;
        /*border-right: 1px dashed #00a65a;*/
        position: relative;
    }

    .bordash {
        border-right: 1px dashed #00a65a;
    }

    .bordashda {
        border-right: 1px dashed #bfbfbf;
    }

    .bordashle {
        border-left: 1px dashed #00a65a;
    }

    .bordashleda {
        border-left: 1px dashed #bfbfbf;
    }

    .vercontent {
        border-right: 2px solid #00a65a;
        box-sizing: border-box;
        border-top-right-radius: 30px;
        width: 100%;
        height: 270px;
    }

    .vercontent2 {
        border-left: 2px solid #00a65a;
        box-sizing: border-box;
        padding: 0px 0 20px 0;
        margin-left: -2px !important;
    }

    .recode .row {
        margin: 0;
    }

    .flowcontent {
        border: 1px #e1e2e3 solid;
        border-top: 0;
        width: 100%;
        height: 250px;
        overflow: auto;
        box-sizing: border-box;
        padding: 0 5px;
    }

    .timeline_point2 {
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        /* background: #0ba687; */
        top: -7px;
        left: 50%;
        margin-left: -7px;
    }

    .fh_edit {
        text-decoration: line-through;
        color: #666666;
    }

    .toText {
        display: inline-block;
        padding: 2px 4px;
        border-radius: 5px;
        color: #fff;
        background-color: #ababab;
        vertical-align: middle;
        position: absolute;
        left: 10px;
        top: 5px;
    }

    .line20T{
        text-decoration:line-through;
        color: #666666;
    }
</style>
<body>
<div class="form-content">
    <form id="form-invoice-add" class="form-horizontal" novalidate="novalidate" th:object="${invoice}">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">发货单基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--订单基础信息 begin-->
                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">发货单号：</label>
                                    <div class="flex_right">
                                        <span th:text="*{vbillno}"></span>
                                        <span style="margin-left: 10px;" class="label label-primary"
                                              th:if="*{vbillstatus == '0'}">新建</span>
                                        <span style="margin-left: 10px;" class="label label-warning"
                                              th:if="*{vbillstatus == '1'}">已确认</span>
                                        <span style="margin-left: 10px;" class="label label-info"
                                              th:if="*{vbillstatus == '2'}">部分提货</span>
                                        <span style="margin-left: 10px;" class="label label-info"
                                              th:if="*{vbillstatus == '3'}">已提货</span>
                                        <span style="margin-left: 10px;" class="label label-success"
                                              th:if="*{vbillstatus == '4'}">部分到货</span>
                                        <span style="margin-left: 10px;" class="label label-success"
                                              th:if="*{vbillstatus == '5'}">已到货</span>
                                        <span style="margin-left: 10px;" class="label label-primary"
                                              th:if="*{vbillstatus == '6'}">部分回单</span>
                                        <span style="margin-left: 10px;" class="label label-primary"
                                              th:if="*{vbillstatus == '7'}">已回单</span>
                                        <span style="margin-left: 10px;" class="label label-inverse"
                                              th:if="*{vbillstatus == '8'}">关闭</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">要求提货日：</label>
                                    <div class="flex_right" th:text="*{#dates.format(reqDeliDate, 'yyyy-MM-dd')}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">要求到货日：</label>
                                    <div class="flex_right" th:text="*{#dates.format(reqArriDate, 'yyyy-MM-dd')}"></div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">紧急程度：</label>
                                    <div class="flex_right" th:each="dict : ${@dict.getType('urgent_level')}"
                                         th:if="${dict.dictValue} == *{urgentLevel}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">客户名称：</label>
                                    <div class="flex_right">
                                        <div class="input-group" th:text="*{custName}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">客户简称：</label>
                                    <div class="flex_right">
                                        <div class="input-group" th:text="*{custAbbr}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">车长车型：</label>
                                    <div class="flex_right" th:text="*{carLenName + '米' + carTypeName}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">运输方式：</label>
                                    <div class="flex_right" th:each="dict : ${@dict.getType('trans_code')}"
                                         th:if="${dict.dictValue} == *{transCode}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">指导价：</label>-->
<!--                                    <div class="flex_right" th:text="*{guidingPrice}"></div>-->
<!--                                </div>-->
<!--                            </div>-->

                        </div>

                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">成本价：</label>
                                    <div class="flex_right" th:text="*{costPrice}"></div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">运营组：</label>
                                    <div class="flex_right" th:text="${salesDept}">
                                    </div>
                                </div>
                            </div>
<!--                            <div class="col-md-3 col-sm-6">-->
<!--                                <div class="flex">-->
<!--                                    <label class="flex_left">运营部：</label>-->
<!--                                    <div class="flex_right" th:text="${invoice.custSalesName}">-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">运营部/管理部：</label>
                                    <div class="flex_right">
                                        [[${invoice.custSalesName}]]/[[${invoice.mgmtDeptName}]]
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">驻场组：</label>
                                    <div class="flex_right">
                                        <span th:text="${stationDeptName}"></span>
                                        <span th:if="${residentsName != null && residentsName != ''}"
                                              th:text="${' / ' + residentsName}"></span>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">调度组：</label>
                                    <div class="flex_right">
                                        <div class="input-group" th:text="*{transLineName}">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-3">
                                <div class="flex">
                                    <label class="flex_left">APP联系人：</label>
                                    <div class="flex_right">
                                        <div class="input-group" th:text="*{appDeliContact + ' / ' + appDeliMobile}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class="flex">
                                    <label class="flex_left">结算公司：</label>
                                    <div class="flex_right">
                                        <div class="input-group" th:each="dict : ${@dict.getType('bala_corp')}"
                                             th:if="${dict.dictValue} == *{balaCorpId}" th:text="${dict.dictLabel}" ></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class="flex">
                                    <label class="flex_left">运营公司：</label>
                                    <div class="flex_right">
                                        <div class="input-group" th:each="dict : ${@dict.getType('bala_corp')}"
                                             th:if="${dict.dictValue} == *{operateCorp}" th:text="${dict.dictLabel}" ></div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">卸货地数量：</label>
                                    <div class="flex_right">
                                        <span th:if="*{unloadPlaceNum == 1}">1</span>
                                        <span th:if="*{unloadPlaceNum == 2}">2</span>
                                        <span th:if="*{unloadPlaceNum == 3}">3</span>
                                        <span th:if="*{unloadPlaceNum == 4}">4</span>
                                        <span th:if="*{unloadPlaceNum == 5}">5</span>
                                        <span th:if="*{unloadPlaceNum == 6}">6</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-3">
                                <div class="flex">
                                    <label class="flex_left">大件运输：</label>
                                    <div class="flex_right">
                                        <div class="input-group">
                                            <span th:if="*{isOversize == 0}">否</span>
                                            <span th:if="*{isOversize == 1}">是</span>

                                            <span th:if="*{isOversize == 1}" style="float: right">
                                                尺寸：[[${invoice.goodsLength == null ? 0 : invoice.goodsLength}]]m x [[${invoice.goodsWidth == null ? 0 : invoice.goodsWidth}]]m x [[${invoice.goodsHeight == null ? 0 : invoice.goodsHeight}]]m
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-3">
                                <div class="flex">
                                    <label class="flex_left">是否报关：</label>
                                    <div class="flex_right">
                                        <div class="input-group">
                                            <span th:if="*{isCustomsClearance == 0}">否</span>
                                            <span th:if="*{isCustomsClearance == 1}">是</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row no-gutter">
                            <div class="col-md-12 col-sm-12">
                                <div class="flex">
                                    <label class="flex_left">备注：</label>
                                    <div class="flex_right"
                                         th:utext="*{#strings.unescapeJava(#strings.replace(#strings.escapeJava(memo),'\n','&lt;br/&gt;'))}">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <!--订单基础信息 end-->
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">装卸货信息</a>
                    </h4>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">

                    <div class="panel-body">
                        <!--订单提货信息 end-->
                        <div class="row">
                            <div class="col-sm-12">
                                <ul class="timelineA">
                                    <li th:each="shippingAddress,status:${deliShippingAddressList}">
                                        <div class="timelineA-badge warning">装</div>
                                        <div class="timelineA-panel">
                                            <div class="timelineA-heading">
                                                <div style="display: inline-block;;font-size:15px;font-weight: 600;"
                                                     th:text="${shippingAddress.addrName}">
                                                </div>
                                                <div style="display: inline-block;font-size:14px;margin-left: 15px"
                                                     th:text="${shippingAddress.contact + ' / ' + shippingAddress.mobile}">
                                                </div>
                                                <div style="display: inline-block;font-size:14px;margin-left: 15px"
                                                     th:text="${shippingAddress.provinceName + shippingAddress.cityName + shippingAddress.areaName + shippingAddress.detailAddr}">
                                                </div>
                                                <div style="display: inline-block;font-size:14px;margin-left: 15px">
                                                    <p style="display: inline-block;background-color: #f5f5f5e8;padding-inline: 5px;padding-block: 1px;border: 1px solid #ccc"
                                                       th:each="shippingGoods,status:${shippingAddress.shippingGoodsList}"
                                                       th:text="${(shippingGoods.custOrderno == null ? '':shippingGoods.custOrderno + '-')
                                                   + shippingGoods.goodsName + '-'
                                                   + (shippingGoods.num == null || shippingGoods.num == 0  ? '' : shippingGoods.num + '件')
                                                   + (shippingGoods.weight == null || shippingGoods.weight == 0 ? '' : shippingGoods.weight + '吨')
                                                   + (shippingGoods.volume == null || shippingGoods.volume == 0 ? '' : shippingGoods.volume + 'm³')}"></p>
                                                </div>
                                            </div>
                                            <div class="timelineA-body" >
                                            </div>
                                        </div>
                                    </li>
                                    <li th:each="shippingAddress,status:${arriShippingAddressList}">
                                        <div class="timelineA-badge success">卸</div>
                                        <div class="timelineA-panel">
                                            <div class="timelineA-heading">
                                                <div style="display: inline-block;;font-size:15px;font-weight: 600;"
                                                     th:text="${shippingAddress.addrName}">
                                                </div>
                                                <div th:if="${shippingAddress.isChangeAddress == 1}"
                                                        style="display: inline-block;color: #fff;
                                                            width: 22px;
                                                            height: 22px;
                                                            line-height: 24px;
                                                            background-color: #1ab394;
                                                            text-align: center;
                                                            border-radius: 20%;
                                                            font-weight: 600;"
                                                     class="">新</div>
                                                <div style="display: inline-block;font-size:14px;margin-left: 15px"
                                                     th:text="${shippingAddress.contact + ' / ' + shippingAddress.mobile}">
                                                </div>
                                                <div style="display: inline-block;font-size:14px;margin-left: 15px"
                                                     th:text="${shippingAddress.provinceName + shippingAddress.cityName + shippingAddress.areaName + shippingAddress.detailAddr}">
                                                </div>
                                                <div style="display: inline-block;font-size:14px;margin-left: 15px">
                                                    <p style="display: inline-block;background-color: #f5f5f5e8;padding-inline: 5px;padding-block: 1px;border: 1px solid #ccc"
                                                       th:each="shippingGoods,status:${shippingAddress.shippingGoodsList}"
                                                       th:text="${(shippingGoods.custOrderno == null ? '':shippingGoods.custOrderno + '-')
                                                   + shippingGoods.goodsName + '-'
                                                   + (shippingGoods.num == null || shippingGoods.num == 0  ? '' : shippingGoods.num + '件')
                                                   + (shippingGoods.weight == null || shippingGoods.weight == 0 ? '' : shippingGoods.weight + '吨')
                                                   + (shippingGoods.volume == null || shippingGoods.volume == 0 ? '' : shippingGoods.volume + 'm³')}"></p>
                                                </div>

                                            </div>
                                            <div class="" th:if="${shippingAddress.isChangeAddress == 1}" style="margin-top: -5px;">
                                                <div style="display: inline-block;;font-size:15px;font-weight: 600;"
                                                     class="line20T"
                                                     th:text="${shippingAddress.caArriAddrName}">
                                                </div>
                                                <div  style="display: inline-block;color: #fff;
                                                            width: 22px;
                                                            height: 22px;
                                                            line-height: 24px;
                                                            background-color: #999;
                                                            text-align: center;
                                                            border-radius: 20%;
                                                            font-weight: 600;"
                                                      class="">旧</div>

                                                <div style="display: inline-block;font-size:14px;margin-left: 15px"
                                                     class="line20T"
                                                     th:text="${shippingAddress.caArriContact + ' / ' + shippingAddress.caArriMobile}">
                                                </div>
                                                <div style="display: inline-block;font-size:14px;margin-left: 15px"
                                                     class="line20T"
                                                     th:text="${shippingAddress.caArriProName + shippingAddress.caArriCityName + shippingAddress.caArriAreaName + shippingAddress.caArriDetailAddr}">
                                                </div>
                                            </div>
                                            <div class="timelineA-body" >

                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <!--订单提货信息 end-->
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFive">费用及货量信息</a>
                    </h4>
                </div>
                <div id="collapseFive" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">结算客户：</label>
                                    <div class="flex_right">
                                        <div class="input-group" th:text="*{balaName}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">结算方式：</label>
                                    <div class="flex_right"  th:each="dict : ${@dict.getType('bala_type')}" th:if="${dict.dictValue} == *{balaType}" th:text="|${dict.dictLabel}|"></div>

                                </div>
                                <div class="flex">
                                    <label class="flex_left"> </label>
                                    <div class="flex_right"  th:if="${invoice.collectAmount != null}" th:text="|代：${invoice.collectAmount}|"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">是否开票：</label>
                                    <div class="flex_right" th:each="dict : ${@dict.getType('billing_type')}"
                                         th:if="${invoice.billingType !=null && dict.dictValue == invoice.billingType} " th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">到付应收：</label>
                                    <div class="flex_right">
                                        <div class="input-group" th:text="*{collectAmount}">
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">对账：</label>
                                    <div class="flex_right">
                                        <div class="input-group">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">开票：</label>
                                    <div class="flex_right"></div>

                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">收款：</label>
                                    <div class="flex_right" >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">公里数：</label>
                                    <div class="flex_right" th:text="*{mileage}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">计价方式：</label>
                                    <div class="flex_right">
                                        <span th:each="billingMethod:${billingMethods}"
                                             th:if="*{billingMethod} == ${billingMethod.value}"
                                             th:text="${billingMethod.context}"></span>
                                        <sapn>([[*{isRoundTrip} == 1 ? '往返':'']])</sapn>
                                        <sapn>/ [[*{ifBargain} == 1 ? '议价':'合同价']]</sapn>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">单价：</label>
                                    <div class="flex_right" th:text="*{unitPrice}"></div>
                                    <div class="flex_right" th:if="${unitPriceMsg != null and unitPriceMsg != ''}">
                                        <i class="fa fa-question-circle"
                                           style="margin-left: 5px; font-size: 16px; cursor: pointer;"
                                           data-toggle="tooltip"
                                           data-container="body"

                                           data-placement="left"
                                           data-html="true"
                                           th:title="${unitPriceMsg}">
                                        </i>
                                    </div>

                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">运费：</label>
                                    <div class="flex_right" th:text="*{costAmount}"></div>
                                    <div class="flex_right" th:if="${not #maps.isEmpty(priceMap) and priceMap.isKilRound == '1'}">
                                        <span style="margin-left: 5px; color: #1ab394; cursor: pointer;"
                                              data-toggle="tooltip"
                                              data-container="body"
                                              data-placement="left"
                                              data-html="true"
                                              title="公里数*单价四舍五入">
                                            四舍五入
                                        </span>
                                    </div>
                                    <div class="flex_right" th:if="${not #maps.isEmpty(priceMap) and priceMap.isSkipMileage == '1'}">
                                        <span style="margin-left: 5px; color: #1ab394; cursor: pointer;"
                                              data-toggle="tooltip"
                                              data-container="body"
                                              data-placement="left"
                                              data-html="true"
                                              title="计算价格时忽略乘以公里数">
                                            忽略公里数
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">其他费：</label>
                                    <div class="flex_right" th:text="*{OnWayAmountFee}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row no-gutter">
                            <div class="col-md-12 col-sm-12">
                                <div class="flex">
                                    <label class="flex_left">议价备注：</label>
                                    <div class="flex_right"
                                         th:utext="*{#strings.unescapeJava(#strings.replace(#strings.escapeJava(bargainMemo),'\n','&lt;br/&gt;'))}">
                                    </div>
                                </div>
                            </div>

                        </div>


                        <div class="row no-gutter">
                            <div style="margin-block: 8px;margin-left: 15px;">
                                <span style="font-size:16px;font-weight:550;margin-right: 20px;">
                                    <span>合计货量：</span>
                                    <span style="font-weight:550;color: #0d62bb">
                                        <nobr th:text="*{numCount}">0</nobr>
                                        件| <nobr th:text="*{weightCount}">0</nobr>
                                        吨| <nobr th:text="*{volumeCount}">0</nobr>
                                        m³
                                    </span>
                                    <a class="fa fa-cubes" onclick="jumpGoodsChange()"></a>
                                </span>
                                <span style="font-size:16px;margin-right: 60px;">
<!--                                    <a href="#cubes" title="货物记录" onclick="openGoods()"><i class="fa fa-cubes"></i></a>-->
                                </span>

                                <span style="margin-bottom: 2px;font-size:16px;font-weight:550;margin-right: 20px;">
                                    <span>合计费用：</span>
                                    <span style="font-size:20px;color: #FF6C00">￥<nobr th:text="*{(totalFee == null ? 0 : totalFee)}">0</nobr></span>
                                </span>
                                <span style="font-size:16px;margin-right: 100px;">
<!--                                    <a href="#cubes" title="费用记录" onclick="openCost()"><i class="fa fa-credit-card"></i></a>-->
                                </span>

                            </div>
                            <div>

                            </div>

                        </div>
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 15%;">客户单号</th>
                                    <th style="width: 10%;">货品名称</th>
                                    <th style="width: 10%;">货品类型</th>
                                    <th style="width: 8%;">件数</th>
                                    <th style="width: 8%;">重量</th>
                                    <th style="width: 8%;">体积</th>
<!--                                    <th style="width: 10%;">计价方式</th>-->
<!--                                    <th style="width: 10%;">价格类型</th>-->
<!--                                    <th style="width: 10%;">单价</th>-->
<!--                                    <th style="width: 10%;">金额</th>-->
                                    <th style="width: 10%;">包装</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="goods,goodsStat : ${deliShippingGoodsList}">
                                    <td style="word-break: keep-all;white-space:nowrap;">
                                        <div class="input-group tc" th:text="${goods.custOrderno != null ? goods.custOrderno :'-'}"></div>
                                    </td>
                                    <td style="word-break: keep-all;white-space:nowrap;">
                                        <div class="input-group tc" th:text="${goods.goodsName}"></div>
                                    </td>
                                    <td style="word-break: keep-all;white-space:nowrap;">
                                        <div class="input-group tc" th:text="${goods.goodsTypeName}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group tc" th:text="${goods.num + '件'}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group tc" th:text="${goods.weight + '吨'}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group tc" th:text="${goods.volume + 'm³ '}"></div>
                                    </td>
<!--                                    <td style="word-break: keep-all;white-space:nowrap;">-->
<!--                                        <div class="col-sm-8" th:each="billingMethod:${billingMethods}" th:if="${invPackGoods.billingMethod} == ${billingMethod.value}" th:text="${billingMethod.context}"></div>-->
<!--                                    </td>-->
<!--                                    <td  th:switch="${invPackGoods.billingMethod}">-->
<!--                                        <div class="input-group tc" th:case="'3' or '4'" th:text="固定价"></div>-->
<!--                                        <div class="input-group tc" th:case="*" th:text="单价"></div>-->
<!--                                    </td>-->
<!--                                    <td class="tc" th:align="right" th:text="${invPackGoods.pc}"></td>-->
<!--                                    <td class="tc" th:align="right" th:text="${invPackGoods.sum}"></td>-->
                                    <td>
                                        <div class="input-group tc" th:each="dict : ${@dict.getType('package_type')}" th:if="${dict.dictValue} == ${goods.packId}" th:text="${dict.dictLabel}">
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                                <tfoot></tfoot>
                            </table>
                        </div>
                        <!--订单货品费用明细 end-->
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapsesix">操作记录</a>
                </h4>
            </div>
            <div id="collapsesix" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!--操作记录 begin-->
                    <div class="over">
                        <div class="fl" style="color: #808080">发货单编号:[[${invoice.vbillno}]]</div>
                        <div class="fl" style="margin-left: 40px;color: #808080">创建人:[[${invoice.regUserName}]]</div>
                    </div>
                    <div class="mt20 recode">
                        <div class="row">
                            <div class="col-sm-2 col-lg-1">
                                <div class="timeline borgreen">
                                    <div class="timeline_point bggreen"></div>
                                    <div class="row">
                                        <div class="col-sm-10" style="padding: 0">
                                            <div class="timeline_text bordash">
                                                <div class="fw">新建单据</div>
                                                <div class="f12 fc80">[[${invoice.regUserName}]]</div>
                                                <div class="f12 fc80">[[${#dates.format(invoice.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                            </div>
                                            <div class="" th:if="${#lists.size(receiveDetailList) > 0}">
                                                <div class="tc bordash fw fc1ab" style="padding: 20px 0">应收</div>
                                                <div class="dashed bordash" th:each="receiveDetail:${receiveDetailList}">
                                                    <div class="f12 fc80 tc">[[${#dates.format(receiveDetail.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                    <div class="f12 tc">[[${receiveDetail.regUserName + '创建：￥'+receiveDetail.transFeeCount}]]</div>
                                                    <div class="round rour bg00a"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-2" style="padding: 0">
                                            <div class="vercontent" th:if="${#lists.size(operationHistoryVOList) > 1}"></div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-10 col-lg-11">
                                <div style="padding: 0 0 20px 0" class="row"
                                     th:if="${#lists.size(operationHistoryVOList) > 0}"
                                     th:each="operationHistoryVO,stat:${operationHistoryVOList}"
                                     th:classappend="${stat.count >1?'vercontent2':'padbt20'}">
                                    <div class="timeline col-sm-3 borgreen">
                                        <div class="timeline_point bggreen"></div>
                                        <div class="row">
                                            <div class="col-sm-5" style="padding: 0">
                                                <div class="timeline_text">
                                                    <div class="fw">[[${operationHistoryVO.vbillno}]]</div>
                                                    <div class="f12 fc80">[[${operationHistoryVO.regUserName}]]</div>
                                                    <div class="f12 fc80">[[${#dates.format(operationHistoryVO.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                    <!--                                                        <div class="f12 fc80">这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注</div>-->
                                                </div>
                                            </div>
                                            <div class="col-sm-7" style="padding: 0">
                                                <div class="flowcontent" th:if="${#lists.size(operationHistoryVO.payDetailList) > 0}">
                                                    <div class="" style="padding-left: 5px">
                                                        <div class="tc bordashle fw fc1ab" style="padding: 20px 0">应付</div>
                                                        <div class="dashed bordashle"
                                                             th:each="payDetail:${operationHistoryVO.payDetailList}">
                                                            <div class="f12 fc80 tc">[[${#dates.format(payDetail.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                            <div class="f12 tc">[[${payDetail.regUserId + '创建：￥'+payDetail.transFeeCount}]]</div>
                                                            <div class="round roul bg00a"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="timeline col-sm-3"
                                         th:classappend="${operationHistoryVO.vbillstatus == '2'
                                                || operationHistoryVO.vbillstatus == '3' ?'borgreen':'bordark'}">
                                        <div class="timeline_point"
                                             th:classappend="${operationHistoryVO.vbillstatus == '2'
                                                    || operationHistoryVO.vbillstatus == '3' ?'bggreen':'bgdark'}"></div>
                                        <div class="row">
                                            <div class="col-sm-5" style="padding: 0">
                                                <div class="timeline_text">
                                                    <div class="fw">提货</div>
                                                    <div class="f12 fc80">[[${operationHistoryVO.pickUpUserName}]]</div>
                                                    <div class="f12 fc80">[[${#dates.format(operationHistoryVO.pickUpDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                </div>
                                            </div>
                                            <div class="col-sm-7" style="padding: 0" th:if="${#lists.size(operationHistoryVO.carLocusList) > 0}">
                                                <div class="flowcontent">
                                                    <div class="" style="padding-left: 5px">
                                                        <div class="tc bordashle fw fc1ab" style="padding: 20px 0">行程跟踪</div>
                                                        <div class="dashed bordashle"
                                                             th:each="carLocus:${operationHistoryVO.carLocusList}">
                                                            <div class="f12 fc80 tc">[[${#dates.format(carLocus.trackingTime, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                            <div class="f12 tc">[[${(carLocus.proName ?: '') +
                                                                (carLocus.cityName ?: '') +
                                                                (carLocus.areaName ?: '') +
                                                                (carLocus.detailAddr ?: '')}]]</div>
                                                            <div class="round roul bg00a"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="timeline col-sm-2"
                                         th:classappend="${operationHistoryVO.vbillstatus == '3' ?'borgreen':'bordark'}">
                                        <div class="timeline_point2"
                                             th:classappend="${operationHistoryVO.vbillstatus == '3' ?'bggreen':'bgdark'}"></div>
                                        <div class="row">
                                            <div class="col-sm-12" style="padding: 0">
                                                <div class="timeline_text">
                                                    <div class="fw">到货</div>
                                                    <div class="f12 fc80">[[${operationHistoryVO.arrivalsUserName}]]</div>
                                                    <div class="f12 fc80">[[${#dates.format(operationHistoryVO.arrivalsDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="timeline col-sm-2"
                                         th:classappend="${operationHistoryVO.receiptMan != null ?'borgreen':'bordark'}">
                                        <div class="timeline_point2"
                                             th:classappend="${operationHistoryVO.receiptMan != null ?'bggreen':'bgdark'}"></div>
                                        <div class="row">
                                            <div class="col-sm-12" style="padding: 0">
                                                <div class="timeline_text">
                                                    <div class="fw">回单</div>
                                                    <div class="f12 fc80">[[${operationHistoryVO.receiptMan}]]</div>
                                                    <div class="f12 fc80">[[${#dates.format(operationHistoryVO.receiptDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <!--                                        <div class="timeline col-sm-2 bordark">-->
                                    <!--                                            <div class="timeline_point2 bgdark"></div>-->
                                    <!--                                            <div class="row">-->
                                    <!--                                                <div class="col-sm-12" style="padding: 0">-->
                                    <!--                                                    <div class="timeline_text">-->
                                    <!--                                                        <div class="fw">签收</div>-->
                                    <!--                                                        <div class="f12 fc80">张三三</div>-->
                                    <!--                                                        <div class="f12 fc80">2021-09-09 12:00:00</div>-->
                                    <!--                                                    </div>-->
                                    <!--                                                </div>-->

                                    <!--                                            </div>-->
                                    <!--                                        </div>-->
                                </div>
                                <div class="row vercontent2" th:if="${#lists.size(operationHistoryVOList) == 0}">
                                    <div class="timeline col-sm-3 bordark">
                                        <div class="timeline_point bgdark"></div>
                                        <div class="row">
                                            <div class="col-sm-5" style="padding: 0">
                                                <div class="timeline_text">
                                                    <div class="fw">调度一</div>
                                                    <!--                                                        <div class="f12 fc80">张三三</div>-->
                                                    <!--                                                        <div class="f12 fc80">2021-09-09 12:00:00</div>-->
                                                    <!--                                                        <div class="f12 fc80 eclipse">这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注</div>-->
                                                </div>
                                            </div>
                                            <div class="col-sm-7" style="padding: 0">
                                            </div>

                                        </div>
                                    </div>
                                    <div class="timeline col-sm-3 bordark">
                                        <div class="timeline_point bordark"></div>
                                        <div class="row">
                                            <div class="col-sm-5" style="padding: 0">
                                                <div class="timeline_text">
                                                    <div class="fw">提货</div>
                                                </div>
                                            </div>
                                            <div class="col-sm-7" style="padding: 0">
                                            </div>

                                        </div>
                                    </div>
                                    <div class="timeline col-sm-2 bordark">
                                        <div class="timeline_point2 bgdark"></div>
                                        <div class="row">
                                            <div class="col-sm-12" style="padding: 0">
                                                <div class="timeline_text">
                                                    <div class="fw">到货</div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="timeline col-sm-2 bordark">
                                        <div class="timeline_point2 bgdark"></div>
                                        <div class="row">
                                            <div class="col-sm-12" style="padding: 0">
                                                <div class="timeline_text">
                                                    <div class="fw">回单</div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <!--                                        <div class="timeline col-sm-2 bordark">-->
                                    <!--                                            <div class="timeline_point2 bgdark"></div>-->
                                    <!--                                            <div class="row">-->
                                    <!--                                                <div class="col-sm-12" style="padding: 0">-->
                                    <!--                                                    <div class="timeline_text">-->
                                    <!--                                                        <div class="fw">签收</div>-->
                                    <!--                                                        <div class="f12 fc80">张三三</div>-->
                                    <!--                                                        <div class="f12 fc80">2021-09-09 12:00:00</div>-->
                                    <!--                                                    </div>-->
                                    <!--                                                </div>-->

                                    <!--                                            </div>-->
                                    <!--                                        </div>-->
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--操作记录 end-->
                </div>
            </div>
        </div>
        </div>

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    let invoice = [[${invoice}]];

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapsesix').collapse('show');
        $('#collapseFive').collapse('show');

    });

    function openGoods() {
        layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "货物记录",
            area: ['70%', '80%'],
            content: '',
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                layer.close(index);
            }
        });
    }
    function openCost() {
        layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "货物记录",
            area: ['70%', '80%'],
            content: '',
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                layer.close(index);
            }
        });
    }

    function jumpGoodsChange(){
        var url = ctx + "invoice/jumpGoodsChange?invoiceId="+invoice.invoiceId;
        $.modal.open("货量调整记录",url);
    }
</script>
</body>
</html>