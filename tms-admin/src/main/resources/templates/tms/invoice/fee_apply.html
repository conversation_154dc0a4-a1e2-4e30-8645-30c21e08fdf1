<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方支付')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <form id="role-form" class="form-horizontal">
            <input type="hidden" name="lotId" id="lotId" th:value="${invoiceId}">
        </form>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" shiro:hasPermission="tms:invoice:fee_entry" onclick="feeEntry()">
                <i class="fa fa-plus"></i> 新增
            </a>
             <a class="btn btn-danger single disabled" onclick="remove()" shiro:hasPermission="tms:invoice:fee_apply:del">
                 <i class="fa fa-remove"></i> 删除
             </a>
            <a class="btn btn-primary single disabled" onclick="feeApply()" shiro:hasPermission="tms:invoice:fee_apply:apply">
                <i class="fa fa-check-circle-o"></i> 申请
            </a>
            <a class="btn btn-primary single disabled" onclick="checkRecord()" shiro:hasPermission="tms:invoice:fee_apply:check_record">
                <i class="fa fa-calculator"></i> 审核记录
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "invoice/fee_apply";
    //权限
    var editFlag = [[${@permission.hasPermi('tms:invoice:fee_apply:edit')}]];
    //付款方式
    var payMethod = [[${@dict.getType('pay_method')}]];
    //费用类型
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    //结算公司
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    //第三方费用
    var otherFeeStatusList = [[${otherFeeStatusList}]];
    //第三方费用 新建状态
    var otherFeeStatusNew = [[${otherFeeStatusNew}]];

    var isClose = [[${isClose}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        var options = {
            url: prefix + "/list",
            removeUrl: prefix + "/delete",
            showToggle:false,
            showColumns:false,
            clickToSelect:true,
            showSearch: false,
            modalName: "第三方费用",
            uniqueId: "otherFeeId",
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'center',
                    field: 'otherFeeId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs ' + editFlag + '" href="javascript:void(0)" title="修改" onclick="edit(\'' + row.otherFeeId + '\',\''+ row.vbillstatus+'\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                        return actions.join('');
                    }

                },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'lotno'
                },
                {
                    title: '状态',
                    align: 'left',
                    field: 'vbillstatus',
                    formatter: function status(value,row) {
                        if (value == 0) {
                            return '<span class="label label-default">新建</span>';
                        }
                        if (value == 1) {
                            return '<span class="label label-success">已付款</span>';
                        }
                        if (value == 2) {
                            return '<span class="label label-primary">申请</span>';
                        };
                        if (value == 3) {
                            return '<span class="label label-coral">已对账</span>';
                        }
                    }
                },
                {
                    title: '费用类型',
                    align: 'left',
                    field: 'feeType',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(costTypeOnWay, value.feeType);
                    }
                },
                {
                    title: '金额',
                    align: 'right',
                    field: 'feeAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '付款类型',
                    align: 'left',
                    field: 'payType',
                    formatter: function status(value,row) {
                        if(value == 0){
                            return '现金'
                        };
                        if(value == 1){
                            return '油卡'
                        };
                    }
                },
                {
                    title: '付款方式',
                    align: 'left',
                    field: 'payMethod',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(payMethod, value.payMethod);
                    }
                },
                {
                    title: '结算公司',
                    align: 'left',
                    field: 'balaCorpId',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(balaCorp, value.balaCorpId);
                    }
                },
                {
                    title: '收款人',
                    align: 'left',
                    field: 'recAccount',
                },
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'recBank',

                },
                {
                    title: '收款账号',
                    align: 'left',
                    field: 'recCardNo',
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate',
                },
                {
                    title: '申请人',
                    align: 'left',
                    field: 'applyUserId',
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'applyDate',
                },
                {
                    title: '付款时间',
                    align: 'left',
                    field: 'pay_date',
                },
                {
                    title: '财务退回原因',
                    field: 'backMemo',
                    align: 'left',
                },

            ]
        };

        $.table.init(options);
    });

    /**
     * 修改
     * @param id
     */
    function edit(id, vbillstatus) {
        if(isClose == 1){
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }
        if (vbillstatus != otherFeeStatusNew) {
            $.modal.alertWarning("请选择“新建”状态的数据！")
            return false;
        }

        var invoiceId = $("#lotId").val();

        //验证发货单是否超过五天
        $.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    layer.open({
                        type: 2,
                        area: ['60%', '60%'],
                        fix: false,
                        maxmin: true,
                        shade: 0.3,
                        title: "第三方费用修改",
                        content: prefix + "/edit/" + id,
                        btn: ['确认', '关闭'],
                        shadeClose: true,            // 弹层外区域关闭
                        yes: function (index, layero) {
                            var iframeWin = layero.find('iframe')[0];
                            iframeWin.contentWindow.submitHandler(index, layero);
                        },
                        cancel: function (index) {
                            return true;
                        }
                    });
                }
            }
        });


    }

    /**
     * 费用申请
     */
    function feeApply() {
        var selectColumns = $.table.selectColumns("vbillstatus");
        var id = $.table.selectColumns($.table._option.uniqueId)+"";
        if (selectColumns != otherFeeStatusNew) {
            $.modal.alertWarning("请选择“新建”状态的数据！")
            return false;
        }
        layer.open({
            type: 2,
            area: ['60%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "第三方费用申请",
            content: prefix + "/apply/" + id,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });
    }

    /**
     * 删除
     */
    function remove() {
        if(isClose == 1){
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }

        var selectColumns = $.table.selectColumns("vbillstatus");

        if (selectColumns != otherFeeStatusNew) {
            $.modal.alertWarning("请选择“新建”状态的数据！")
            return false;
        }

        var id = $("#lotId").val();

        //验证发货单是否超过五天
        $.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+id,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.confirm("确认要删除选中的数据吗?", function() {
                        var rows = $.table.selectColumns($.table._option.uniqueId);

                        var data = { "ids": rows.join() };
                        $.operate.submit(prefix + "/delete", "post", "json", data);
                    });
                }
            }
        });


    }

    /**
     * 第三方费用录入
     */
    function feeEntry() {
        if(isClose == 1){
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }

        var id = $("#lotId").val();

        layer.open({
            type: 2,
            area: ['60%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "第三方费用录入",
            content: ctx + "invoice/feeEntry/" + id,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function(index) {
                return true;
            }
        });

        //验证发货单是否超过五天
        /*$.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+id,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    layer.open({
                        type: 2,
                        area: ['60%', '60%'],
                        fix: false,
                        maxmin: true,
                        shade: 0.3,
                        title: "第三方费用录入",
                        content: ctx + "invoice/feeEntry/" + id,
                        btn: ['确认', '关闭'],
                        shadeClose: true,            // 弹层外区域关闭
                        yes: function (index, layero) {
                            var iframeWin = layero.find('iframe')[0];
                            iframeWin.contentWindow.submitHandler(index, layero);
                        },
                        cancel: function(index) {
                            return true;
                        }
                    });
                }
            }
        });*/
    }

    /**
     * 审核记录
     */
    function checkRecord(){
        //第三方费用id
        var otherFeeId = $.table.selectColumns("otherFeeId");
        var url = prefix + "/check_record?otherFeeId="+otherFeeId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }
</script>
</body>
</html>