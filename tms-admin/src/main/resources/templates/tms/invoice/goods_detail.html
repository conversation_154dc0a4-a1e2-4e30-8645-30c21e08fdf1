<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('发货单明细')"/>
</head>
<style>
    .timelineA {
        list-style: none;
        padding: 0 0 30px;
        position: relative;
        margin-top: -20px
    }

    .timelineA:before {
        top: 30px;
        bottom: 25px;
        position: absolute;
        content: " ";
        width: 3px;
        background-color: #ccc;
        left: 25px;
        margin-right: -1.5px
    }

    .timelineA > li, .timelineA > li > .timelineA-panel {
        margin-bottom: -5px;
        position: relative
    }

    .timelineA > li:after, .timelineA > li:before {
        content: " ";
        display: table
    }

    .timelineA > li:after {
        clear: both
    }

    .timelineA > li > .timelineA-panel {
        margin-left: 40px;
        float: left;
        top: 24px;
        padding: 4px 10px 8px 15px;
        /*border: 1px solid #ccc;*/
        border-radius: 5px;
        width: 95%
    }

    .timelineA > li > .timelineA-badge {
        color: #fff;
        width: 22px;
        height: 22px;
        line-height: 24px;
        font-size: 1em;
        text-align: center;
        position: absolute;
        top: 30px;
        left: 15px;
        margin-right: -25px;
        background-color: #fff;
        z-index: 100;
        border-radius: 20%;
        font-weight: 600
    }

    .timelineA > li.timelineA-inverted > .timelineA-panel {
        float: left
    }

    .timelineA > li.timelineA-inverted > .timelineA-panel:before {
        border-right-width: 0;
        border-left-width: 15px;
        right: -15px;
        left: auto
    }

    .timelineA > li.timelineA-inverted > .timelineA-panel:after {
        border-right-width: 0;
        border-left-width: 14px;
        right: -14px;
        left: auto
    }

    .timelineA-badge.primary {
        background-color: #2e6da4 !important
    }

    .timelineA-badge.success {
        background-color: #1ab394 !important
    }

    .timelineA-badge.warning {
        background-color: #f8ac59 !important
    }

    .timelineA-badge.danger {
        background-color: #d9534f !important
    }

    .timelineA-badge.info {
        background-color: #5bc0de !important
    }

    .timelineA-title {
        margin-top: 0;
        color: inherit
    }

    .timelineA-body > p, .timelineA-body > ul {
        background-color: #eee;
        padding: 4px;
        margin-top: 10px;
    }

    .timelineA-body > p + p {
        /*margin-top: 10px;*/
        margin-left: 10px
    }

    .timelineA-badge > .glyphicon {
        margin-right: 0px;
        color: #fff
    }

    .input-group {
        display: block;
    }

    .flex {
        display: flex;
        algin-items: center;
        just-content: space-between;

    }

    .flex_left {
        width: 100px;
        line-height: 30px;
        text-align: right;
        color: #808080;
    }

    .flex_right {
        min-width: 0;
        flex: 1;
        line-height: 30px;
        /*line-height: 26px;*/
    }

    .panel-default > .panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }

    .timeline {
        /*width: 300px;*/

        position: relative;
    }

    .borgreen {
        border-top: 2px #0ba687 solid;
    }

    .bordark {
        border-top: 2px #bfbfbf solid;
    }

    .timeline_point {
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        /*background: #0ba687;*/
        top: -7px;
        left: 50px;
    }

    .tc {
        text-align: center;
    }

    .timeline_vertical {
        /*position: absolute;*/
        /*right: 23px;*/
        /*top: 100px;*/
        padding: 100px 0 0 0;
        margin-left: -3px;
    }

    .timeline_text {
        /*width: 150px;*/
        text-align: center;
        /*border-top-right-radius: 20px;*/
        /*height: 100px;*/
        box-sizing: border-box;
        padding: 20px 0 0 10px;
        line-height: 20px;
    }

    .borrgreen {
        border-right: 3px #0ba687 solid;
    }

    .borrdark {
        border-right: 3px #bfbfbf solid;
    }

    .lazur-bg {
        background: #bfbfbf;
        width: 20px;
        height: 20px;
    }

    .lawarn-bg {
        background: #0ba687;
        width: 20px;
        height: 20px;
    }

    .vertical-container {
        width: 100%;
        max-width: none;
        margin: 0 auto;
    }

    .vertical-timeline-content {
        margin-left: 20px;
    }

    .box_timeline {

    }

    .fl {
        float: left;
    }

    .clear {
        clear: both;
    }

    .fc80 {
        color: #808080;
    }

    .fw {
        font-weight: bold;
    }

    .f12 {
        font-size: 12px;
    }

    .timeline_vertical-container {
        position: relative;
        padding: 0;
        margin-top: 0em;
        margin-bottom: 0em;
    }

    .vertical-timeline-block::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0px;
        width: 3px;
        height: 100%;
        background: #0ba687;
    }

    .vertical-timeline-block-dark::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0px;
        width: 3px;
        height: 100%;
        background: #bfbfbf;
    }

    /*.timeline_vertical-container.light-timeline:before {*/
    /*    background: #0ba687;*/
    /*}*/
    /*.timeline_vertical-container::before {*/
    /*    left: 0px;*/
    /*}*/
    .vertical-timeline-icon {
        left: -8px;
        top: auto;
    }

    .vertical-timeline-block {
        margin: 0 0px;
    }

    .vertical-timeline-block-dark {
        margin: 0 0px;
    }

    .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-xs-1, .col-xs-10, .col-xs-11, .col-xs-12, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9 {
        padding-left: 0px;
        padding-right: 0px;
    }

    .bggreen {
        background: #0ba687;
    }

    .bgdark {
        background: #bfbfbf;
    }

    .fc1ab {
        color: #1ab394;
    }

    .eclipse {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .over {
        overflow: hidden;
    }

    .mt20 {
        margin-top: 20px;
    }

    .f16 {
        font-size: 16px;
    }

    .round {
        width: 5px;
        height: 5px;
        border-radius: 50%;
        position: absolute;
        top: 0;
    }

    .bg00a {
        background: #00a65a;
    }

    .bgbf {
        background: #bfbfbf;
    }

    .rour {
        right: -3px;
    }

    .roul {
        left: -3px;
    }

    .dashed {
        padding: 0 20px 20px 0;
        /*border-right: 1px dashed #00a65a;*/
        position: relative;
    }

    .bordash {
        border-right: 1px dashed #00a65a;
    }

    .bordashda {
        border-right: 1px dashed #bfbfbf;
    }

    .bordashle {
        border-left: 1px dashed #00a65a;
    }

    .bordashleda {
        border-left: 1px dashed #bfbfbf;
    }

    .vercontent {
        border-right: 2px solid #00a65a;
        box-sizing: border-box;
        border-top-right-radius: 30px;
        width: 100%;
        height: 270px;
    }

    .vercontent2 {
        border-left: 2px solid #00a65a;
        box-sizing: border-box;
        padding: 0px 0 20px 0;
        margin-left: -2px !important;
    }

    .recode .row {
        margin: 0;
    }

    .flowcontent {
        border: 1px #e1e2e3 solid;
        border-top: 0;
        width: 100%;
        height: 250px;
        overflow: auto;
        box-sizing: border-box;
        padding: 0 5px;
    }

    .timeline_point2 {
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        /* background: #0ba687; */
        top: -7px;
        left: 50%;
        margin-left: -7px;
    }

    .fh_edit {
        text-decoration: line-through;
        color: #666666;
    }

    .toText {
        display: inline-block;
        padding: 2px 4px;
        border-radius: 5px;
        color: #fff;
        background-color: #ababab;
        vertical-align: middle;
        position: absolute;
        left: 10px;
        top: 5px;
    }

    .line20T{
        text-decoration:line-through;
        color: #666666;
    }
</style>
<body>
<div class="form-content">
    <form id="form-invoice-add" class="form-horizontal" novalidate="novalidate" >
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">

                <div id="collapseFive" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th>调整时间</th>
                                    <th>发货单调整</th>
                                    <th>调整信息</th>
                                    <th>应收单价</th>
                                    <th>调整人</th>
                                    <th>类型</th>
                                </tr>
                                </thead>
                                <tbody id="td">

                                </tbody>
                                <tfoot></tfoot>
                            </table>
                        </div>
                        <!--订单货品费用明细 end-->
                    </div>
                </div>
            </div>

        </div>

    </form>
</div>


<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    let cargoEditHistories = [[${cargoEditHistories}]];

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapsesix').collapse('show');
        $('#collapseFive').collapse('show');


        let html = '';

        cargoEditHistories.forEach(res=>{

            if(res.isAdjust == 0){
                res.isAdjust = '货量更新'
            }else{
                res.isAdjust = '调账'
            }
            let invInfo = '';
            let nvmInfo = '';

            if(res.numDistance != 0){
                if(res.numDistance > 0){
                    nvmInfo += '件数(+'+res.numDistance +')</br>';
                }else{
                    nvmInfo += '件数('+res.numDistance +')</br>';
                }
                invInfo += '件数('+res.numBeforeDJ+'→'+res.numAfterDJ+')</br>';
            }
            if(res.weightDistance != 0){
                if(res.weightDistance > 0){
                    nvmInfo += '重量(+'+res.weightDistance +')</br>';
                }else{
                    nvmInfo += '重量('+res.weightDistance +')</br>';
                }
                invInfo += '重量('+res.weightBeforeDJ+'→'+res.weightAfterDJ+')</br>';
            }
            if(res.volumeDistance != 0){
                if(res.volumeDistance > 0){
                    nvmInfo += '体积(+'+res.volumeDistance +')</br>';
                }else{
                    nvmInfo += '体积('+res.volumeDistance +')</br>';
                }
                invInfo += '体积('+res.volumeBeforeDJ+'→'+res.volumeAfterDJ+')</br>';
            }

            html+=`<tr>
                        <td>`+res.regDate.slice(0,10)+`</td>
                        <td>`+invInfo+`</td>
                        <td>`+nvmInfo+`</td>
                        <td>`+res.unitPrice+`</td>

                        <td>`+res.regUserName+`</td>
                        <td>`+res.isAdjust+`</td>
                    </tr>`

        })
        $("#td").html(html);
    });


</script>
</body>
</html>