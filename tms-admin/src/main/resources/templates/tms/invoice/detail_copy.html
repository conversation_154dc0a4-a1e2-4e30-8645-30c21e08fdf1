<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('发货单明细')" />
</head>
<style>
    .input-group{
        display: block;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;

    }
    .flex_left{
        width: 100px;
        line-height: 30px;
        text-align: right;
        color: #808080;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;
        /*line-height: 26px;*/
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }

    .timeline{
        /*width: 300px;*/

        position: relative;
    }
    .borgreen{
        border-top: 2px #0ba687 solid;
    }
    .bordark{
        border-top: 2px #bfbfbf solid;
    }
    .timeline_point{
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        /*background: #0ba687;*/
        top: -7px;
        left: 50px;
    }

    .tc{
        text-align: center;
    }

    .timeline_vertical {
        /*position: absolute;*/
        /*right: 23px;*/
        /*top: 100px;*/
        padding: 100px 0 0 0;
        margin-left: -3px;
    }
    .timeline_text{
        /*width: 150px;*/
        text-align: center;
        /*border-top-right-radius: 20px;*/
        /*height: 100px;*/
        box-sizing: border-box;
        padding: 20px 0 0 10px;
        line-height: 20px;
    }
    .borrgreen{
        border-right: 3px #0ba687 solid;
    }
    .borrdark{
        border-right: 3px #bfbfbf solid;
    }
    .lazur-bg{
        background: #bfbfbf;
        width: 20px;
        height: 20px;
    }
    .lawarn-bg{
        background: #0ba687;
        width: 20px;
        height: 20px;
    }
    .vertical-container {
        width: 100%;
        max-width: none;
        margin: 0 auto;
    }
    .vertical-timeline-content{
        margin-left: 20px;
    }
    .box_timeline{

    }
    .fl{
        float: left;
    }
    .clear{
        clear: both;
    }
    .fc80{
        color: #808080;
    }
    .fw{
        font-weight: bold;
    }
    .f12{
        font-size: 12px;
    }
    .timeline_vertical-container {
        position: relative;
        padding: 0;
        margin-top: 0em;
        margin-bottom: 0em;
    }
    .vertical-timeline-block::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0px;
        width: 3px;
        height: 100%;
        background: #0ba687;
    }
    .vertical-timeline-block-dark::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0px;
        width: 3px;
        height: 100%;
        background: #bfbfbf;
    }
    /*.timeline_vertical-container.light-timeline:before {*/
    /*    background: #0ba687;*/
    /*}*/
    /*.timeline_vertical-container::before {*/
    /*    left: 0px;*/
    /*}*/
    .vertical-timeline-icon {
        left: -8px;
        top: auto;
    }
    .vertical-timeline-block {
        margin: 0 0px;
    }
    .vertical-timeline-block-dark {
        margin: 0 0px;
    }
    .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-xs-1, .col-xs-10, .col-xs-11, .col-xs-12, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9 {
        padding-left: 0px;
        padding-right: 0px;
    }
    .bggreen{
        background: #0ba687;
    }
    .bgdark{
        background: #bfbfbf;
    }
    .fc1ab{
        color: #1ab394;
    }
    .eclipse{
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .over{
        overflow: hidden;
    }
    .mt20{
        margin-top: 20px;
    }
    .f16{
        font-size: 16px;
    }
    .round{
        width: 5px;
        height: 5px;
        border-radius: 50%;
        position: absolute;
        top: 0;
    }
    .bg00a{
        background: #00a65a;
    }
    .bgbf{
        background: #bfbfbf;
    }
    .rour{
        right: -3px;
    }
    .roul{
        left: -3px;
    }
    .dashed{
        padding: 0 20px 20px 0;
        /*border-right: 1px dashed #00a65a;*/
        position: relative;
    }
    .bordash{
        border-right: 1px dashed #00a65a;
    }
    .bordashda{
        border-right: 1px dashed #bfbfbf;
    }
    .bordashle{
        border-left: 1px dashed #00a65a;
    }
    .bordashleda{
        border-left: 1px dashed #bfbfbf;
    }
    .vercontent{
        border-right: 2px solid #00a65a;
        box-sizing: border-box;
        border-top-right-radius: 30px;
        width: 100%;
        height: 270px;
    }
    .vercontent2{
        border-left: 2px solid #00a65a;
        box-sizing: border-box;
        padding: 0px 0 20px 0;
        margin-left: -2px !important;
    }
    .recode .row{
        margin: 0;
    }
    .flowcontent{
        border: 1px #e1e2e3 solid;
        border-top: 0;
        width: 100%;
        height: 250px;
        overflow: auto;
        box-sizing: border-box;
        padding: 0 5px;
    }
    .timeline_point2 {
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        /* background: #0ba687; */
        top: -7px;
        left: 50%;
        margin-left: -7px;
    }
    .fh_edit {
        text-decoration: line-through;
        color: #666666;
    }
    .toText{
        display:inline-block;
        padding: 2px 4px;
        border-radius: 5px;
        color: #fff;
        background-color: #ababab;
        vertical-align: middle;
        position: absolute;
        left: 10px;
        top: 5px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-invoice-add" class="form-horizontal" novalidate="novalidate" th:object="${invoice}">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">发货单基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--订单基础信息 begin-->
                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">
                                        客户发货单号：</label>
                                    <div class="flex_right" th:text="*{custOrderno}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">要求提货日期：</label>
                                    <div class="flex_right" th:text="*{#dates.format(reqDeliDate, 'yyyy-MM-dd')}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">要求到货日期：</label>
                                    <div class="flex_right" th:text="*{#dates.format(reqArriDate, 'yyyy-MM-dd')}"></div>
                                </div>
                            </div>
                           
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">紧急程度：</label>
                                    <div class="flex_right" th:each="dict : ${@dict.getType('urgent_level')}" th:if="${dict.dictValue} == *{urgentLevel}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                        </div>
                      
                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">客户名称：</label>
                                    <div class="flex_right">
                                        <div class="input-group" th:text="*{custName}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">结算客户：</label>
                                    <div class="flex_right">
                                        <div class="input-group" th:text="*{balaName}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">结算方式：</label>
                                    <div class="flex_right"  th:each="dict : ${@dict.getType('bala_type')}" th:if="${dict.dictValue} == *{balaType}" th:text="|${dict.dictLabel}|"></div>

                                </div>
                                <div class="flex">
                                    <label class="flex_left"> </label>
                                    <div class="flex_right"  th:if="${invoice.collectAmount != null}" th:text="|代：${invoice.collectAmount}|"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">是否开票：</label>
                                    <div class="flex_right" th:each="dict : ${@dict.getType('billing_type')}"
                                         th:if="${invoice.billingType !=null && dict.dictValue == invoice.billingType} " th:text="${dict.dictLabel}">
                                </div>
                            </div>
                
                        </div>
                        </div>
                        
                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">车型：</label>
                                    <div class="flex_right" th:each="dict : ${@dict.getType('car_type')}" th:if="${dict.dictValue} == *{carType}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">车长：</label>
                                    <div class="flex_right" th:each="dict : ${@dict.getType('car_len')}" th:if="${dict.dictValue} == *{carLen}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">运输方式：</label>
                                    <div class="flex_right" th:each="dict : ${@dict.getType('trans_code')}" th:if="${dict.dictValue} == *{transCode}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">调度组：</label>
                                    <div class="flex_right">
                                        <div class="input-group" th:text="*{transLineName}">
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">驻场组：</label>
                                    <div class="flex_right" th:text="${stationDeptName}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">驻场人员：</label>
                                    <div class="flex_right">
                                        <div class="input-group" th:text="${residentsName}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">指导价：</label>
                                    <div class="flex_right" th:text="*{guidingPrice}"></div>
                                </div>
                            </div>

                        </div>

                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">卸货地数量：</label>
                                    <div class="flex_right">
                                        <span th:if="*{unloadPlaceNum == 1}">1</span>
                                        <span th:if="*{unloadPlaceNum == 2}">2</span>
                                        <span th:if="*{unloadPlaceNum == 3}">3</span>
                                        <span th:if="*{unloadPlaceNum == 4}">4</span>
                                        <span th:if="*{unloadPlaceNum == 5}">5</span>
                                        <span th:if="*{unloadPlaceNum == 6}">6</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">大件运输：</label>
                                    <div class="flex_right">
                                        <div class="input-group">
                                            <span th:if="*{isOversize == 0}">否</span>
                                            <span th:if="*{isOversize == 1}">是</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row no-gutter">
                            <div class="col-sm-12">
                                <div class="flex">
                                    <label class="flex_left">发货单备注：</label>
                                    <div class="flex_right" th:utext="*{#strings.unescapeJava(#strings.replace(#strings.escapeJava(memo),'\n','&lt;br/&gt;'))}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--订单基础信息 end-->
                    </div>
                </div>
            </div>
            <div class="row">
                <div style="padding: 0 15px;" class="col-sm-6">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapseTwo">发货单提货信息</a>
                            </h4>
                        </div>
                        <div id="collapseTwo" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <!--订单提货信息 end-->
                                <div class="row">
                                    <div class="col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left">APP联系人：</label>
                                            <div class="flex_right">
                                                <div class="input-group" th:text="*{appDeliContact}"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left" style="width: 120px">APP联系人手机：</label>
                                            <div class="flex_right">
                                                <div class="input-group" th:text="*{appDeliMobile}"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div th:if="*{isMultiple == 0}">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">发货方：</label>
                                                <div class="flex_right">
                                                    <div class="input-group" th:text="*{deliAddrName}">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">联系人：</label>
                                                <div class="flex_right" th:text="|*{deliContact} / *{deliMobile}|">
                                                </div>
                                            </div>
                                        </div>
                                        <!--                                    <div class="col-sm-6">-->
                                        <!--                                        <div class="flex">-->
                                        <!--                                            <label class="flex_left">联系电话：</label>-->
                                        <!--                                            <div class="flex_right" th:text="*{deliMobile}">-->
                                        <!--                                            </div>-->
                                        <!--                                        </div>-->
                                        <!--                                    </div>-->
                                        <div class="col-sm-12">
                                            <div class="flex">
                                                <label class="flex_left">地址：</label>
                                                <div class="flex_right" th:text="*{deliProName+deliCityName+deliAreaName+deliDetailAddr}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div th:if="*{isMultiple == 1}" th:each="shippingAddress,status:${deliShippingAddressList}">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left" th:text="|发货方${status.count}：|"></label>
                                                <div class="flex_right">
                                                    <div class="input-group" th:text="${shippingAddress.addrName}">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">联系人：</label>
                                                <div class="flex_right" th:text="|${shippingAddress.contact} / ${shippingAddress.mobile}|">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-12">
                                            <div class="flex">
                                                <label class="flex_left">地址：</label>
                                                <div class="flex_right" th:text="${shippingAddress.provinceName+shippingAddress.cityName+shippingAddress.areaName+shippingAddress.detailAddr}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--订单提货信息 end-->
                            </div>
                        </div>
                    </div>
                </div>
                <div style="padding: 0 15px 0 0;" class="col-sm-6">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapseThree">发货单收货信息</a>
                            </h4>
                        </div>
                        <div id="collapseThree" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <!--订单收货信息 end-->
                                <div th:if="*{isMultiple == 0}">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">收货方：</label>
                                                <div class="flex_right">
                                                    <div class="input-group" th:text="*{arriAddrName}">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">联系人：</label>
                                                <div class="flex_right" th:text="|*{arriContact} / *{arriMobile}|">
                                                </div>
                                            </div>
                                        </div>
    <!--                                    <div class="col-sm-6">-->
    <!--                                        <div class="flex">-->
    <!--                                            <label class="flex_left">联系人：</label>-->
    <!--                                            <div class="flex_right" th:text="*{arriContact}">-->
    <!--                                            </div>-->
    <!--                                        </div>-->
    <!--                                    </div>-->
                                        <div class="col-sm-12">
                                            <div class="flex">
                                                <label class="flex_left">地址：</label>
                                                <div class="flex_right" th:text="*{arriProName+arriCityName+arriAreaName+arriDetailAddr}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row" style="position:relative;" th:if="*{isChangeAddress==1}">
                                        <div class="toText">旧</div>
                                        <div class="col-sm-6 fh_edit">
                                            <div class="flex">
                                                <label class="flex_left">收货方：</label>
                                                <div class="flex_right">
                                                    <div class="input-group" th:text="*{caArriAddrName}">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 fh_edit">
                                            <div class="flex">
                                                <label class="flex_left">联系人：</label>
                                                <div class="flex_right" th:text="|*{caArriContact} / *{caArriMobile}|">
                                                </div>
                                            </div>
                                        </div>
    <!--                                    <div class="col-sm-6">-->
    <!--                                        <div class="flex">-->
    <!--                                            <label class="flex_left">联系人：</label>-->
    <!--                                            <div class="flex_right" th:text="*{arriContact}">-->
    <!--                                            </div>-->
    <!--                                        </div>-->
    <!--                                    </div>-->
                                        <div class="col-sm-12 fh_edit">
                                            <div class="flex">
                                                <label class="flex_left">地址：</label>
                                                <div class="flex_right" th:text="*{caArriProName+caArriCityName+caArriAreaName+caArriDetailAddr}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div th:if="*{isMultiple == 1}" th:each="shippingAddress,status:${arriShippingAddressList}">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left" th:text="|发货方${status.count}：|"></label>
                                                <div class="flex_right">
                                                    <div class="input-group" th:text="${shippingAddress.addrName}">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">联系人：</label>
                                                <div class="flex_right" th:text="|${shippingAddress.contact} / ${shippingAddress.mobile}|">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-12">
                                            <div class="flex">
                                                <label class="flex_left">地址：</label>
                                                <div class="flex_right" th:text="${shippingAddress.provinceName+shippingAddress.cityName+shippingAddress.areaName+shippingAddress.detailAddr}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--订单收货信息 end-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFive">货品明细</a>
                    </h4>
                </div>
                <div id="collapseFive" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--订单货品费用明细 begin-->
                        <!--                        <div class="row">-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-4">总件数：</label>-->
                        <!--                                    <div class="col-sm-8" th:text="|*{numCount}件|">-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-4">总重量：</label>-->
                        <!--                                    <div class="col-sm-8">-->
                        <!--                                        <div class="input-group" th:text="|*{weightCount}吨|">-->
                        <!--                                        </div>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-4">总体积：</label>-->
                        <!--                                    <div class="col-sm-8">-->
                        <!--                                        <div class="input-group" th:text="|*{volumeCount}m3|">-->
                        <!--                                        </div>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-4">总金额：</label>-->
                        <!--                                    <div class="col-sm-8">-->
                        <!--                                        <div class="input-group" th:text="|¥*{costAmount}|">-->
                        <!--                                        </div>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                        </div>-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 15%;">货品名称</th>
                                    <th style="width: 14%;">货品类型</th>
                                    <th style="width: 8%;">件数</th>
                                    <th style="width: 8%;">重量</th>
                                    <th style="width: 8%;">体积</th>
                                    <th style="width: 10%;">计价方式</th>
                                    <th style="width: 10%;">价格类型</th>
                                    <th style="width: 10%;">单价</th>
                                    <th style="width: 10%;">金额</th>
                                    <th style="width: 10%;">包装</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="invPackGoods,invPackGoodsStat : *{invPackGoodsList}">
                                    <td style="word-break: keep-all;white-space:nowrap;">
                                        <div class="input-group tc" th:text="${invPackGoods.goodsName}"></div>
                                    </td>
                                    <td style="word-break: keep-all;white-space:nowrap;">
                                        <div class="input-group tc" th:text="${invPackGoods.goodsTypeName}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group tc" th:text="${invPackGoods.num}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group tc" th:text="${invPackGoods.weight}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group tc" th:text="${invPackGoods.volume}"></div>
                                    </td>
                                    <td style="word-break: keep-all;white-space:nowrap;">
                                        <div class="col-sm-8" th:each="billingMethod:${billingMethods}" th:if="${invPackGoods.billingMethod} == ${billingMethod.value}" th:text="${billingMethod.context}"></div>
                                    </td>
                                    <td  th:switch="${invPackGoods.billingMethod}">
                                        <div class="input-group tc" th:case="'3' or '4'" th:text="固定价"></div>
                                        <div class="input-group tc" th:case="*" th:text="单价"></div>
                                    </td>
                                    <td class="tc" th:align="right" th:text="${invPackGoods.pc}">
                                    </td>
                                    <td class="tc" th:align="right" th:text="${invPackGoods.sum}">
                                    </td>
                                    <td>
                                        <div class="input-group tc" th:each="dict : ${@dict.getType('package_type')}" th:if="${dict.dictValue} == ${invPackGoods.packId}" th:text="${dict.dictLabel}">
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                                <tfoot>
                                <tr style="background: #FFFCD3;text-align: center">
                                    <td>合计:</td>
                                    <td></td>
                                    <td>
                                        <div class="input-group tc" th:text="|*{numCount}件|">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group tc" th:text="|*{weightCount}吨|">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group tc" th:text="|*{volumeCount}m3|">
                                        </div>
                                    </td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td>
                                        <div class="input-group tc" th:text="|¥*{costAmount}|">
                                        </div>
                                    </td>
                                    <td></td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                        <!--订单货品费用明细 end-->
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapsesix">操作记录</a>
                    </h4>
                </div>
                <div id="collapsesix" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--操作记录 begin-->
                        <div class="over">
                            <div class="fl" style="color: #808080">发货单编号:[[${invoice.vbillno}]]</div>
                            <div class="fl" style="margin-left: 40px;color: #808080">创建人:[[${invoice.regUserName}]]</div>
                        </div>
                        <div class="mt20 recode">
                            <div class="row">
                                <div class="col-sm-2 col-lg-1">
                                    <div class="timeline borgreen">
                                        <div class="timeline_point bggreen"></div>
                                        <div class="row">
                                            <div class="col-sm-10" style="padding: 0">
                                                <div class="timeline_text bordash">
                                                    <div class="fw">新建单据</div>
                                                    <div class="f12 fc80">[[${invoice.regUserName}]]</div>
                                                    <div class="f12 fc80">[[${#dates.format(invoice.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                </div>
                                                <div class="" th:if="${#lists.size(receiveDetailList) > 0}">
                                                    <div class="tc bordash fw fc1ab" style="padding: 20px 0">应收</div>
                                                    <div class="dashed bordash" th:each="receiveDetail:${receiveDetailList}">
                                                        <div class="f12 fc80 tc">[[${#dates.format(receiveDetail.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                        <div class="f12 tc">[[${receiveDetail.regUserName + '创建：￥'+receiveDetail.transFeeCount}]]</div>
                                                        <div class="round rour bg00a"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-2" style="padding: 0">
                                                <div class="vercontent" th:if="${#lists.size(operationHistoryVOList) > 1}"></div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-10 col-lg-11">
                                    <div style="padding: 0 0 20px 0" class="row"
                                         th:if="${#lists.size(operationHistoryVOList) > 0}"
                                         th:each="operationHistoryVO,stat:${operationHistoryVOList}"
                                         th:classappend="${stat.count >1?'vercontent2':'padbt20'}">
                                        <div class="timeline col-sm-3 borgreen">
                                            <div class="timeline_point bggreen"></div>
                                            <div class="row">
                                                <div class="col-sm-5" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">[[${operationHistoryVO.vbillno}]]</div>
                                                        <div class="f12 fc80">[[${operationHistoryVO.regUserName}]]</div>
                                                        <div class="f12 fc80">[[${#dates.format(operationHistoryVO.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
<!--                                                        <div class="f12 fc80">这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注</div>-->
                                                    </div>
                                                </div>
                                                <div class="col-sm-7" style="padding: 0">
                                                    <div class="flowcontent" th:if="${#lists.size(operationHistoryVO.payDetailList) > 0}">
                                                        <div class="" style="padding-left: 5px">
                                                            <div class="tc bordashle fw fc1ab" style="padding: 20px 0">应付</div>
                                                            <div class="dashed bordashle"
                                                                 th:each="payDetail:${operationHistoryVO.payDetailList}">
                                                                <div class="f12 fc80 tc">[[${#dates.format(payDetail.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                                <div class="f12 tc">[[${payDetail.regUserId + '创建：￥'+payDetail.transFeeCount}]]</div>
                                                                <div class="round roul bg00a"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="timeline col-sm-3"
                                             th:classappend="${operationHistoryVO.vbillstatus == '2'
                                                || operationHistoryVO.vbillstatus == '3' ?'borgreen':'bordark'}">
                                            <div class="timeline_point"
                                                 th:classappend="${operationHistoryVO.vbillstatus == '2'
                                                    || operationHistoryVO.vbillstatus == '3' ?'bggreen':'bgdark'}"></div>
                                            <div class="row">
                                                <div class="col-sm-5" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">提货</div>
                                                        <div class="f12 fc80">[[${operationHistoryVO.pickUpUserName}]]</div>
                                                        <div class="f12 fc80">[[${#dates.format(operationHistoryVO.pickUpDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-7" style="padding: 0" th:if="${#lists.size(operationHistoryVO.carLocusList) > 0}">
                                                    <div class="flowcontent">
                                                        <div class="" style="padding-left: 5px">
                                                            <div class="tc bordashle fw fc1ab" style="padding: 20px 0">行程跟踪</div>
                                                            <div class="dashed bordashle"
                                                                 th:each="carLocus:${operationHistoryVO.carLocusList}">
                                                                <div class="f12 fc80 tc">[[${#dates.format(carLocus.trackingTime, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                                <div class="f12 tc">[[${carLocus.proName+carLocus.cityName+carLocus.areaName+carLocus.detailAddr}]]</div>
                                                                <div class="round roul bg00a"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="timeline col-sm-2"
                                             th:classappend="${operationHistoryVO.vbillstatus == '3' ?'borgreen':'bordark'}">
                                            <div class="timeline_point2"
                                                 th:classappend="${operationHistoryVO.vbillstatus == '3' ?'bggreen':'bgdark'}"></div>
                                            <div class="row">
                                                <div class="col-sm-12" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">到货</div>
                                                        <div class="f12 fc80">[[${operationHistoryVO.arrivalsUserName}]]</div>
                                                        <div class="f12 fc80">[[${#dates.format(operationHistoryVO.arrivalsDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="timeline col-sm-2"
                                             th:classappend="${operationHistoryVO.receiptMan != null ?'borgreen':'bordark'}">
                                            <div class="timeline_point2"
                                                 th:classappend="${operationHistoryVO.receiptMan != null ?'bggreen':'bgdark'}"></div>
                                            <div class="row">
                                                <div class="col-sm-12" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">回单</div>
                                                        <div class="f12 fc80">[[${operationHistoryVO.receiptMan}]]</div>
                                                        <div class="f12 fc80">[[${#dates.format(operationHistoryVO.receiptDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
<!--                                        <div class="timeline col-sm-2 bordark">-->
<!--                                            <div class="timeline_point2 bgdark"></div>-->
<!--                                            <div class="row">-->
<!--                                                <div class="col-sm-12" style="padding: 0">-->
<!--                                                    <div class="timeline_text">-->
<!--                                                        <div class="fw">签收</div>-->
<!--                                                        <div class="f12 fc80">张三三</div>-->
<!--                                                        <div class="f12 fc80">2021-09-09 12:00:00</div>-->
<!--                                                    </div>-->
<!--                                                </div>-->

<!--                                            </div>-->
<!--                                        </div>-->
                                    </div>
                                    <div class="row vercontent2" th:if="${#lists.size(operationHistoryVOList) == 0}">
                                        <div class="timeline col-sm-3 bordark">
                                            <div class="timeline_point bgdark"></div>
                                            <div class="row">
                                                <div class="col-sm-5" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">调度一</div>
<!--                                                        <div class="f12 fc80">张三三</div>-->
<!--                                                        <div class="f12 fc80">2021-09-09 12:00:00</div>-->
<!--                                                        <div class="f12 fc80 eclipse">这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注这是一段很长的备注</div>-->
                                                    </div>
                                                </div>
                                                <div class="col-sm-7" style="padding: 0">
                                                </div>

                                            </div>
                                        </div>
                                        <div class="timeline col-sm-3 bordark">
                                            <div class="timeline_point bordark"></div>
                                            <div class="row">
                                                <div class="col-sm-5" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">提货</div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-7" style="padding: 0">
                                                </div>

                                            </div>
                                        </div>
                                        <div class="timeline col-sm-2 bordark">
                                            <div class="timeline_point2 bgdark"></div>
                                            <div class="row">
                                                <div class="col-sm-12" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">到货</div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="timeline col-sm-2 bordark">
                                            <div class="timeline_point2 bgdark"></div>
                                            <div class="row">
                                                <div class="col-sm-12" style="padding: 0">
                                                    <div class="timeline_text">
                                                        <div class="fw">回单</div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
<!--                                        <div class="timeline col-sm-2 bordark">-->
<!--                                            <div class="timeline_point2 bgdark"></div>-->
<!--                                            <div class="row">-->
<!--                                                <div class="col-sm-12" style="padding: 0">-->
<!--                                                    <div class="timeline_text">-->
<!--                                                        <div class="fw">签收</div>-->
<!--                                                        <div class="f12 fc80">张三三</div>-->
<!--                                                        <div class="f12 fc80">2021-09-09 12:00:00</div>-->
<!--                                                    </div>-->
<!--                                                </div>-->

<!--                                            </div>-->
<!--                                        </div>-->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--操作记录 end-->
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer" />
<script th:inline="javascript">
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapsesix').collapse('show');
        $('#collapseFive').collapse('show');

    });
</script>
</body>
</html>