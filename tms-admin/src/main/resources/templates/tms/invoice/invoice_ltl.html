<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('零担列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        .container-div{
            padding: 0px 15px;
        }
        .search-collapse, .select-table{
            margin: 0;
            border-radius:0;
            padding: 5px;
        }
        .search-collapse{
            background-color: #F7F7F7;
        }
        .form-group{
            margin: 0;
        }
        .row + .row{
            margin-top: 5px;
        }
        .btn-group-sm>.btn, .btn-sm{
            padding: 3px 10px;
        }
        .table-striped {
            height: calc(100% - 70px);
            padding-top: 0;
        }
        .dropdownpad{
        padding: 1px 2px;
        }
        .navs .dropdown-menu{
            padding: 10px 0;
            left: 20px;
            top: -10px;
        }
        .btn-xsT{
            padding: 1px 5px;
            font-size: 12px        
        }
        .left-fixed-table-columns, .left-fixed-body-columns{
            z-index: 3;
        }
        .label+.label{
            margin-left: 5px;
        }
        .lHie{
            margin: 0;
            display: flex;
            align-items: center;
            width: 280px
        }
        .lHie .col-md-12 div span {
            vertical-align: middle;
            margin-right: 5px;
        }
        .lHie .col-md-12 div>div {
            max-width: calc(100% - 110px);
        }
        .lHie .col-md-12 div>div>span {
            display: block;
            width: 100%;
            text-align: left;
            overflow:hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            -o-text-overflow:ellipsis;
            
        }

        .mt5 span {
            display: inline-block;
        }
        .tah{
            text-align: right;
            overflow:hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            -o-text-overflow:ellipsis;
        }
        .sblogo{
            display: inline-block;
            margin: 0 0 0 5px;
            width: 18px;
            height: 18px;
            vertical-align: middle;
            border-radius: 4px;
        }
        .bootstrap-table .table:not(.table-condensed), .bootstrap-table .table:not(.table-condensed)>tbody>tr>td, .bootstrap-table .table:not(.table-condensed)>tbody>tr>th, .bootstrap-table .table:not(.table-condensed)>tfoot>tr>td, .bootstrap-table .table:not(.table-condensed)>tfoot>tr>th, .bootstrap-table .table:not(.table-condensed)>thead>tr>td{
            padding: 4px 8px;
        }
        .mt5{
            margin-top: 4px;
        }

        .label-primaryT{
            color: #1ab394;
            background-color: transparent;
            border: 1px solid #1ab394;
        }
        .label-successT{
            color: #1c84c6;
            background-color: transparent;
            border: 1px solid #1c84c6;
        }
        .label-defaultT{
            color: #5e5e5e;
            background-color: transparent;
            border: 1px solid #5e5e5e;
        }
        .label-dangerT{
            color: #ed5565;
            background-color: transparent;
            border: 1px solid #ed5565;  
        }
        .label-warningT{
            color: #f8ac59;
            background-color: transparent;
            border: 1px solid #f8ac59; 
        }
        .mtdiv{
            display: inline-block;
            vertical-align: middle;
        }
        .mtdiv div{
            text-align: center;
        }
        .mtdiv div:nth-child(2){
            margin-top: 4px;
        }
        .strText span{
            display: block !important;
        }
        .fw{
            font-weight: bold;
        }
        .fcred{
            color: red;
            font-size: 20px;
            font-weight: bold;
            /* vertical-align: middle; */
        }
        .fcgre{
            color: green;
            font-size: 20px;
            font-weight: bold;
            vertical-align: middle;
        }
        .butA{
            color: #000;
        }
        .butA:hover{
            text-decoration:underline;
        }
        .pa2{
            padding: 2px;
            font-weight: 100;
            margin-bottom: 4px;
            display: inline-block;
            margin-right: 5px;
        }
        .textc{
            display: inline-block;
            vertical-align: middle;
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-4">
                        <div class="col-sm-12">
                            <input name="custOrderno" id="custOrderno" placeholder="客户发货单号/发货单编号" class="form-control" type="text"
                                   maxlength="30" autocomplete="off">
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="vbillstatus" placeholder="发货单状态" id="vbillstatus" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="发货单状态" multiple
                                        th:with="type=${invoiceStatusList}">
                                    <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-2">要求提货日：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" placeholder="要求提货开始日期" class="form-control" readonly
                                       id="reqDeliDateStart"  name="reqDeliDateStart">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" placeholder="要求提货结束日期" class="form-control" readonly
                                       id="reqDeliDateEnd"  name="reqDeliDateEnd">
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-sm-2">
                        <div class="form-group" style="text-align: center;">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="exportExcel()" shiro:hasPermission="tms:receivableReconciliation:export">
                <i class="fa fa-download"></i> 导出
            </a>

        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />

<script th:inline="javascript">
    var vbillstatus = [[${invoiceStatusList}]];
    var invoiceStatusMap = [[${invoiceStatusMap}]];

    // var isFleet = [[${isFleet}]];
    // var prefix = isFleet ? ctx + "fleet/invoice" : ctx + "invoice";

    var prefix = ctx + "invoice/ltl";

    var date = new Date();
    date.setTime(date.getTime());
    var year = date.getFullYear();
    var month = ("0" + (date.getMonth() + 1)).slice(-2);
    var day = ("0" + (date.getDate())).slice(-2);
    var today = year + "-" + month + "-" + day;
    //$("#reqDeliDateEnd").val(today);

    //获取上月日期
    var days = new Date(year, month, 0);
    //获取当前日期中月的天数
    days = days.getDate();
    var year2 = year;
    var month2 = parseInt(month) - 1;
    if (month2 == 0) {
        year2 = parseInt(year2) - 1;
        month2 = 12;
    }
    var day2 = day;
    var days2 = new Date(year2, month2, 0);
    days2 = days2.getDate();
    if (day2 > days2) {
        day2 = days2;
    }
    if (month2 < 10) {
        month2 = '0' + month2;
    }
    var t2 = year2 + '-' + month2 + '-' + day2;
    $("#reqDeliDateStart").val(t2);


    $(function () { 
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: ctx + "invoice/ltl/list",
            showToggle: false,
            showColumns: true,
            clickToSelect:true,
            height: 560,
            showFooter:false,  
            showExport: true,
            uniqueId: "invoiceId",
            modalName: "发货单",
            columns:[
                [
                    { checkbox: true, valign: "middle", colspan: 1, rowspan: 2},
                    {
                        title: '发货单编号',
                        field: 'vbillno',
                        align: 'left',
                        valign: "middle",
                        colspan: 1, 
                        rowspan: 2
                    },
                    {
                        title: '发货单状态',
                        field: 'vbillstatus',
                        align: 'left',
                        valign: "middle",
                        colspan: 1, 
                        rowspan: 2,
                        formatter: function status(value,row,index) {
                            var context = '';
                            vbillstatus.forEach(function (v) {
                                if (v.value == value) {
                                    if (value == invoiceStatusMap.NEW) {
                                        context = '<span class="label label-primary">'+v.context+'</span>';
                                    }else if (value == invoiceStatusMap.AFFIRM) {
                                        context = '<span class="label label-warning">'+v.context+'</span>';
                                    }else if (value == invoiceStatusMap.PORTION_PICK_UP || value == invoiceStatusMap.PICK_UP) {
                                        context = '<span class="label label-info">'+v.context+'</span>';
                                    }else if (value == invoiceStatusMap.PORTION_ARRIVALS|| value == invoiceStatusMap.ARRIVALS) {
                                        context = '<span class="label label-success">'+v.context+'</span>';
                                    }else if (value == invoiceStatusMap.PORTION_RETURNS|| value == invoiceStatusMap.RETURNS) {
                                        context = '<span class="label label-primary">' + v.context + '</span>';
                                    } else {
                                        context = '<span class="label label-inverse">' + v.context + '</span>';
                                    }

                                    return false;
                                }
                            })
                            return context;
                        }
                    },
                    {
                        title: '要求提货信息',
                        align: 'left',
                        valign: "middle",
                        field : 'reqDeliDate',
                        colspan: 1, 
                        rowspan: 2,
                        formatter: function(value, row, index) { 
                            return $.table.tooltip(row.custAbbr)+"<br/>"+value.substring(0,10);
                        }
                    },
                    {
                        title: '要求装/卸货地址',
                        align: 'left',
                        valign: "middle",
                        field : 'deliProName',
                        colspan: 1, 
                        rowspan: 2,
                        formatter: function(value, row, index) {
                            return `<span class="label label-warning pa2">装</span>`+row.deliProName+row.deliCityName+row.deliAreaName+`<br/><span class="label label-success pa2">卸</span>`+row.arriProName+row.arriCityName+row.arriAreaName;  
                        }
                    },
                    {
                        title: '货量/车型',
                        align: 'left',
                        valign: "middle",
                        field : 'goodsName',
                        colspan: 1, 
                        rowspan: 2,
                        formatter: function(value, row, index) { 
                            let data=[];
                            if (row.goodsName) {
                                data.push(row.goodsName);
                            }
                            if(row.numCount){
                                data.push(row.numCount + '件');
                            }
                            if(row.weightCount){
                                data.push(row.weightCount + '吨');
                            }
                            if(row.volumeCount){
                                data.push(row.volumeCount + 'm³');
                            }
                            return data.join('/')+"<br/>"+row.carLen+"米"+row.carTypeName;
                        }
                    },
                    {
                        title: '到货时间',
                        align: 'left',
                        valign: "middle",
                        field : 'reqArriDate',
                        colspan: 1, 
                        rowspan: 2 
                    },
                    {
                        title: '提货段',
                        align: 'center',
                        colspan: 3, 
                        rowspan: 1,
                    },
                    {
                        title: '干线段',
                        align: 'center',
                        colspan: 3, 
                        rowspan: 1,
                    },
                    {
                        title: '送货段',
                        align: 'center',
                        colspan: 3, 
                        rowspan: 1,
                    },
                    {
                        title: '合计',
                        align: 'center',
                        valign: "middle",
                        field : 'memo',
                        colspan: 1, 
                        rowspan: 2 ,
                        formatter: function(value, row, index) { 
                            let num=Number(row.freightPickUp)+Number(row.freight)+Number(row.freightDeliver)+Number(row.deliveryFee)
                            if(num){
                                return num.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            }
                            return '-';
                        }
                    },
                    // {
                    //     title: '备注',
                    //     align: 'left',
                    //     valign: "middle",
                    //     field : 'memo',
                    //     colspan: 1, 
                    //     rowspan: 2,
                    //     formatter: function(value, row, index) {
                    //         return $.table.tooltip(value);
                    //     }
                    // },
                ],
                [
                    {
                        title: '车牌号',
                        align: 'left',
                        field : 'carnoPickUp'
                    },
                    {
                        title: '司机姓名',
                        align: 'left',
                        field : 'driverNamePickUp'
                    },
                    {
                        title: '运费',
                        align: 'left',
                        field : 'freightPickUp',
                        formatter: function(value, row, index) { 
                            if(value){
                                return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            }
                            return '-';
                        }
                    },
                    {
                        title: '承运商',
                        align: 'left',
                        field : 'carrName' 
                    },
                   
                    {
                        title: '运费',
                        align: 'left',
                        field : 'freight',
                        formatter: function(value, row, index) { 
                            if(value){
                                return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            }
                            return '-';
                        }
                    },
                    {
                        title: '送货费',
                        align: 'left',
                        field : 'deliveryFee',
                        colspan: 1, 
                        rowspan: 1,
                        formatter: function(value, row, index) { 
                            if(value){
                                return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            }
                            return '-';
                        }
                    },
                    {
                        title: '车牌号',
                        align: 'left',
                        field : 'carnoDeliver'
                    },
                    {
                        title: '司机姓名',
                        align: 'left',
                        field : 'driverNameDeliver'
                    },
                    {
                        title: '运费',
                        align: 'left',
                        field : 'freightDeliver',
                        formatter: function(value, row, index) { 
                            if(value){
                                return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            }
                            return '-';
                        }
                    },
                ]
            ]
        };
        $.table.init(options);

       /**
         * 初始化日期控件
         */
         layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqDeliDateStart',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#reqDeliDateEnd',
                type: 'date',
                trigger: 'click'
            });
        });
    });

    /**
     * 重置
     */
     function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        data.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 导出
     */
    function exportExcel() {
        var tmpEndDate = [[${#dates.format(#dates.createToday(), 'yyyy-MM-dd')}]]
        if ($("#reqDeliDateEnd").val().trim()!= '') {
            tmpEndDate = $("#reqDeliDateEnd").val().trim();
        }
        var ymd1 = $("#reqDeliDateStart").val().trim().split("-");
        var ymd2 = tmpEndDate.split("-");
        if ((ymd2[0] - ymd1[0]) * 12 + (ymd2[1] - ymd1[1]) + (ymd2[2] - ymd1[2]) / 31 > 6) {
            $.modal.msg("要求提货日期跨度不能超过6个月", modal_status.WARNING);
            return;
        }

        $.modal.confirm("确定导出所有信息吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            var search = $.common.formToJSON("role-form");
            search.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));


            $.post(prefix + "/export", search, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });

    }


</script>
</body>
</html>