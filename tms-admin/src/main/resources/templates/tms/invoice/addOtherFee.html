<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：修改结算方式')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .col-xs-4{
        line-height: 26px;
        text-align: right;
    }
    .wfkye{
        color: #0071ce;
        border-bottom: 1px sienna #0071ce;
    }
    .cur{
        cursor: pointer;
    }
    .fcff{
        color: #ff1f1f;
    }

    .btn-default,.label-default {
        color: #333;
        background-color: #fff;
        border-color: #ccc;
        margin-top: 1px;
    }
    .label-default{
        border: 1px solid;
    }
    .input{
        border: 1px solid #ccc;
        height: 32px;
        line-height: 26px;
        padding: 2px 4px;
        width:100%;
        display: inline-table;
    }
    .label {
        margin-right: 4px;
        display: inline-table;
    }
    #buttonList>button{
        margin-right: 4px;
    }
    #div_print{
        display: none;
        padding: 0 4px;
        width: 100%;
        box-sizing: border-box;
    }
    .fw{
        font-weight: 600;
    }
    .flex{
        display: flex;
        justify-content: space-between;
    }
    .bacc2{
        background-color: #c2c2c2 !important;
    }
    .tc{
        text-align: center;
    }
    .border{
        border: 1px solid #333;
        display: flex;
        border-bottom: none;
    }
    .border:last-child{
        border-bottom: .5px solid #333;
    }
    .border+.border{
        border-top: none;
    }
    .border>div{
        border: .5px solid #333;
        padding: 2px 4px;
        line-height: 26px;
    }
    .ma0{
        margin: 0;
    }
    .fcff{
        color: #ff1f1f;
    }
    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .table-bordered {
        border: 0px solid #EBEBEB;
    }
    .table-striped .table, .table-striped .table, .table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td {
        border-bottom: 0px solid #e7eaec!important;
    }
    .table-bordered td, .table-bordered th {
        border: 0px solid #ddd!important;
    }
    .input-group{
        margin-bottom: 10px;
    }
    .save{
        padding: 0px 0;
        position: fixed;
        bottom: 0px;
        left: 0;
        width: 100%;
        background: #fff;
        z-index: 99;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .eye .file-drop-zone-title{
        background: url('../../../img/eye.png') no-repeat center 10px;
        background-size: 40px 40px;
        height: 100%;
        line-height: 120px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 140px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }

    .file-drop-zone-title{
        font-size: 13px;
    }

    th,td{
        text-align: center;
        border: 1px solid #EBEBEB !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate" >
        <input name="lotId" id="lotId" type="hidden" th:value="${invoice.invoiceId}"/>

        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">发货单号：</label>
                    <div class="col-xs-4">
                        <input name="lotno" id="lotno" th:value="${invoice.vbillno}" class="form-control" type="text" minlength="1" maxlength="50" autocomplete="off" required disabled>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>费用类型：</label>
                    <div class="col-xs-4">
                        <select name="feeType" id="feeType" class="form-control valid" aria-invalid="false" th:with="type=${@dict.getType('cost_type_on_way')}"  required>
                            <option value="">-- 请选择 --</option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:if="${dict.dictValue != '18' && dict.dictValue != '27'}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>票点：</label>
                    <div class="col-xs-4">
                        <select name="billingType"  class="form-control valid" th:with="type=${@dict.getType('billing_type')}"
                                aria-invalid="false" aria-required="true" required>
                            <option value="">-- 请选择 --</option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}" th:attr="data-rate=${dict.numVal1}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>


        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>费用：</label>
                    <div class="col-xs-4">
                        <input name="feeAmount" id="feeAmount" placeholder="" class="form-control valid"
                               oninput="$.numberUtil.onlyNumberTwoDecimal(this)" type="text" required>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>三方类型：</label>
                    <div class="col-xs-4">
                        <select name="singleFlag" id="singleFlag"  class="form-control valid"
                                aria-invalid="false" aria-required="true" required onchange="statusChange()">
                            <option value="0">单笔</option>
                            <option value="1">月结</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff bank">*</span>收款银行：</label>
                    <div class="col-xs-4">
                        <input name="recBank" id="recBank" placeholder="" class="form-control valid"
                                type="text" required>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff bank">*</span>收款人：</label>
                    <div class="col-xs-4">
                        <input name="recAccount" id="recAccount" placeholder="" class="form-control valid"
                               type="text" required>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff bank">*</span>收款账号：</label>
                    <div class="col-xs-4">
                        <input name="recCardNo" id="recCardNo" placeholder="" class="form-control valid"
                               type="text" required>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span>备注：</span></label>
                    <div class="col-xs-8">
                        <textarea name="memo"  placeholder="请输入" maxlength="200" class="form-control valid" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3 col-sm-5">
                <div class="form-group">
                    <label class="col-xs-4" style="text-align: right">附件上传：</label>
                    <div class="col-xs-8" style="display:inline-table;">
                        <input name="appendix" id="appendix" class="form-control" type="file" multiple>
                        <input type="hidden" id="appendixId" name="appendixId" >
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt10">
            <div style="padding-left:10px" class="col-md-6 col-sm-6">

                <table class="table table-bordered mt10" style="border: 1px solid #EBEBEB">
                    <thead style="background: #f7f8fa">
                    <tr style="text-align: center">
                        <th style="width: 8%;">类别</th>
                        <th style="width: 15%;">申请人</th>
                        <th style="width: 27%;">费用类型</th>
                        <th style="width: 10%;">费用(元)</th>
                        <th style="width: 15%;">审核状态</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr  th:each="entrustCost:${entrustCostList}" th:if="${entrustCost.isLotFee == 1}">
                        <td>三方</td>
                        <td th:text="${entrustCost.regUserName}"></td>
                        <td th:text="${@dict.getLabel('cost_type_on_way',entrustCost.feeType)}"></td>
                        <td th:text="${entrustCost.feeAmount}"></td>
                        <td th:if="${entrustCost.checkStatus == 0}">待审核</td>
                        <td th:if="${entrustCost.checkStatus == 2}">审核通过</td>
                        <td th:if="${entrustCost.checkStatus == 3}">审核不通过</td>
                    </tr>
                    <tr th:if="${entrustCostList.size() == 0}">
                        <td colspan="5">暂无审核数据</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </form>


    </div>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">
    var prefix = ctx + "invoice";

    $(function() {
        //图片功能
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("appendix", "appendixId", picParam);

    })

    function statusChange(){
        if($("#singleFlag").val() == 0){
            $("#recBank").attr("required","required");
            $("#recAccount").attr("required","required");
            $("#recCardNo").attr("required","required");
            $(".bank").attr("style","display:inline");
        }else{
            $("#recBank").removeAttr("required");
            $("#recAccount").removeAttr("required");
            $("#recCardNo").removeAttr("required");
            $(".bank").attr("style","display:none");
        }
    }

    function calculateTotal(){
        let billingType = $("#billingType").val();
        //alert(billingType);
        if("6" == billingType){
            $("#freightFeeRate").val(0)
        }

    }

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            if ($('#appendix').fileinput('getFilesCount') > 0) {
                jQuery.subscribe("cmt", commit);
                $('#appendix').fileinput('upload');
            } else {
                commit()
            }
        }
    }

    function commit() {
        var data = $("#form-carrier-edit").serializeArray();
        $.operate.save(prefix + "/saveOtherFeeCheck", data);
    }


    function onRoadTips(data,obj){
        let roadTips=$("#roadTips").val();
        if(roadTips){
            let list=roadTips.split(",");
            list.push(data);
            $("#roadTips").val(list.join(","));
        }else{
            $("#roadTips").val(data);
        }
        $(obj).remove();
        inputLabel();
    }

    function inputLabel() {
        let roadTips=$("#roadTips").val();
        if(roadTips){
            let list=roadTips.split(",");
            let html=``;
            list.forEach((res,i) => {
                html+=`<span class="label label-default">${res}
                        <i class="fa fa-remove" style="font-size: 15px;color: #ef6776;" onclick="onRemove(${i})"></i>
                    </span>`
            });
            $("#inputLabel").html(html);
        }else{
            $("#inputLabel").html(``);
        }
    }

    function onRemove(index) {
        let roadTips=$("#roadTips").val();
        if(roadTips){
            let list=roadTips.split(",");
            $("#buttonList").prepend(`<button type="button" class="btn btn-default btn-sm" onclick="onRoadTips('${list[index]}',this)">${list[index]}</button>`)
            list.splice(index,1);
            $("#roadTips").val(list.join(","));
        }
        inputLabel();
    }




    function addTip(){
        let tip = $("#addTips").val();
        if(tip != ''){
            let data = {tip};
            $.modal.confirm("确定新增线路标签 '"+tip+"' 吗？", function() {
                $.operate.saveModalNoCloseAndRefush(prefix + "/addRoadTip", data);
            });
        }
    }

    function balaTypeChange(select) {
        if ($(select).val() == '1') {
            $('#familiarRow').show()
        } else {
            $('#familiarRow').hide()
        }
    }

</script>
</body>

</html>