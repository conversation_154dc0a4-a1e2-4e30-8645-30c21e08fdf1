<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('发货单')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        .panel-default>.panel-heading {
            font-weight: bold;
            background-color: #f7fafc;
        }
        .flex{
            display: flex;
            algin-items:center;
            just-content:space-between;
            color: #808080;
        }
        .flex_left{
            width: 100px;
            line-height: 30px;
            text-align: right;
        }
        .flex_right{
            min-width:0;
            flex:1;
            /*line-height: 26px;*/
        }
        .panel-body {
            padding: 5px 10px 10px 10px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="form-content"><form class="form-horizontal" id="addForm">
    <input type="hidden" th:name="invoiceId" th:value="${invoice.invoiceId}">
    <!-- 上面panel -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h5 class="panel-title">三方费用</h5>
        </div>
        <div class="panel-body">
            <div class="row no-gutter">
                <div class="col-sm-3">
                    <div class="flex">
                        <label for="" class="flex_left">客户名称：</label>
                        <div class="flex_right">
                            <input type="hidden" name="customerId" id="customerId" th:value="${invoice.customerId}">
                            <input type="text" id="custAbbr" class="form-control" disabled th:value="${invoice.custAbbr}"></div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="flex">
                        <label for="" class="flex_left">总金额：</label>
                        <div class="flex_right">
                            <input type="text" class="form-control" id="sumMoney" name="sumMoney" disabled>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="flex">
                        <label for="" class="flex_left">单价：</label>
                        <div class="flex_right">
                            <input type="text" id="price" required name="price" class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" >
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="flex">
                        <label for="" class="flex_left">费用类型：</label>
                        <div class="flex_right"><select required class="form-control" onchange="selectPriceType(this)" id="priceType" name="priceType">
                            <option value="" selected>请选择</option>
                            <option value="1">计价件数</option>
                            <option value="2">计价重量</option>
                            <option value="3">计价体积</option>
                        </select></div>
                    </div>
                </div>
            </div>
            <div class="row no-gutter">
                <div class="col-sm-3">
                    <div class="flex">
                        <label for="" class="flex_left">是否提供发票：</label>
                        <div class="flex_right">
                            <select id="isInvoice" name="isInvoice"  class="form-control">
                                <option value="1">是</option>
                                <option value="2" selected>否</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading">
            <h5 class="panel-title">发货单明细</h5>
        </div>
        <div class="panel-body">
            <table class="table">
                <thead>
                <th><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow()" title="新增行">+</a></th>
                <th style="width: 80px">分摊金额</th>
                <th style="width: 80px">计价件数</th>
                <th style="width: 80px">计价重量</th>
                <th style="width: 80px">计价体积</th>
                <th>发货单号</th>
                <th>要求提货日期</th>
                <th>提货到货省市区</th>
                <th style="width: 60px">总件数</th>
                <th style="width: 60px">总重量</th>
                <th style="width: 60px">总体积</th>
                </thead>
                <tbody id="addRowBody">
                <tr th:each="obj,objStat : ${invoiceList}">
                    <input type="hidden" class="invoiceId" th:id="|lotId${objStat.index}|" th:value="${obj.invoiceId}">
                    <input type="hidden" th:id="|lotno${objStat.index}|" th:value="${obj.vbillno}">
                    <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRow(this)" title="删除选择行"></a></td>
                    <td><input th:data-rowidx="${objStat.index}" th:id="|shareMoney${objStat.index}|" class="form-control shareMoney" type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this)"></td>
                    <td><input onblur="calNumCountMoney(this)" th:data-rowidx="${objStat.index}" th:id="|numCount${objStat.index}|" class="form-control priceType priceType1" type="text" disabled th:data-numcount="${obj.numCount}" oninput="$.numberUtil.onlyNumberLonger(this)" ></td>
                    <td><input onblur="calWeightCountMoney(this)" th:data-rowidx="${objStat.index}" th:id="|weightCount${objStat.index}|" class="form-control priceType priceType2" type="text" disabled th:data-weightcount="${obj.weightCount}" oninput="$.numberUtil.onlyNumberLonger(this)" ></td>
                    <td><input onblur="calVolumeCountMoney(this)" th:data-rowidx="${objStat.index}" th:id="|volumeCount${objStat.index}|" class="form-control priceType priceType3" type="text" disabled th:data-volumecount="${obj.volumeCount}" oninput="$.numberUtil.onlyNumberLonger(this)" ></td>
                    <td th:text="${obj.vbillno}"></td>
                    <td th:text="${#dates.format(obj.reqDeliDate,'yyyy-MM-dd HH:mm:ss')}"></td>
                    <td>
                        <span th:text="${obj.deliProName + obj.deliAddrName + obj.deliAreaName}"></span><i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>
                        <span th:text="${obj.arriProName + obj.arriAddrName + obj.arriAreaName}"></span>
                    </td>
                    <td th:text="${obj.numCount}"></td>
                    <td th:text="${obj.weightCount}"></td>
                    <td th:text="${obj.volumeCount}"></td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>

</form></div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script  th:inline="javascript">
    let invoiceListSize = [[${invoiceListSize}]];

    function selectPriceType(obj){
        let priceType = $(obj).val();
        if(priceType == '')return;


        let price = $("#price").val();
        if(price == ''){
            $.modal.alertWarning("请输入单价");
            $(obj).val("").change();
            return;
        }

        $(".priceType").attr("disabled","disabled");
        $(".priceType").val("");
        $(".priceType"+priceType).removeAttr("disabled");
    }

    function calNumCountMoney(obj){
        let numCount = $(obj).val();
        //let sumNumCount = $(obj).data("numcount");
        let rowIdx = $(obj).data("rowidx");
        let price = $("#price").val();

        if(price == ''){
            $.modal.alertWarning("单价不能为空");
            return;
        }

        let shareMoney = parseFloat(price) * parseFloat(numCount);
        if(shareMoney){
            $("#shareMoney"+rowIdx).val(shareMoney.toFixed(2));
            calSumShare();
        }

    }

    function calWeightCountMoney(obj){
        let weightCount = $(obj).val();
        let sumWeightCount = $(obj).data("weightcount");
        let rowIdx = $(obj).data("rowidx");
        let price = $("#price").val();

        if(price == ''){
            $.modal.alertWarning("单价不能为空");
            return;
        }

        let shareMoney = parseFloat(price) * parseFloat(weightCount);
        if(shareMoney){
            $("#shareMoney"+rowIdx).val(shareMoney.toFixed(2));
            calSumShare();
        }

    }

    function calVolumeCountMoney(obj){
        let volumeCount = $(obj).val();
        let sumVolumeCount = $(obj).data("volumecount");
        let rowIdx = $(obj).data("rowidx");
        let price = $("#price").val();

        if(price == ''){
            $.modal.alertWarning("单价不能为空");
            return;
        }

        let shareMoney = parseFloat(price) * parseFloat(volumeCount);
        if(shareMoney){
            $("#shareMoney"+rowIdx).val(shareMoney.toFixed(2));
            calSumShare();
        }
    }

    function calSumShare(){
        let sumShare = 0;
        $(".shareMoney").each(function(idx,obj){
            let tmp = parseFloat($(obj).val());
            if(tmp){
                sumShare += tmp;
            }
        });

        $("#sumMoney").val(sumShare.toFixed(2));
    }

    /**
     * 提交
     */
    function submitHandler() {
        if($.validate.form()){
            let rowIdx = 0;
            let rowErrMsg = [];
            let formData = {};
            let otherFeeList = [];
            let tmpShareMoney = 0.0;
            $(".shareMoney").each(function(idx,obj){
                rowIdx++;
                let tmp = parseFloat($(obj).val());
                if(!tmp){
                    rowErrMsg.push("第"+rowIdx+"行，分摊金额不能为空");
                }else{
                    tmpShareMoney += tmp;
                }
                let ri = $(obj).data("rowidx");
                let data = {};
                data.lotId = $("#lotId"+ri).val();
                data.lotno = $("#lotno"+ri).val();
                data.numCount = $("#numCount"+ri).val();
                data.weightCount = $("#weightCount"+ri).val();
                data.volumeCount = $("#volumeCount"+ri).val();
                data.feeAmount = tmp;
                otherFeeList.push(data);
            });

            if(rowErrMsg.length > 0){
                $.modal.alertWarning(rowErrMsg.join("<br>"));
                return;
            }

            let sumMoney = $("#sumMoney").val();

            if(tmpShareMoney != sumMoney){
                $.modal.alertWarning("分摊金额与总金额不等");
                return;
            }

            if(otherFeeList.length == 0){
                $.modal.alertWarning("请添加发货单明细")
                return;
            }

            formData.otherFeeList = otherFeeList;
            formData.custAbbr = $("#custAbbr").val();
            formData.customerId = $("#customerId").val();
            formData.sumMoney = $("#sumMoney").val();
            formData.price = $("#price").val();
            formData.priceType = $("#priceType").val();
            formData.isInvoice = $("#isInvoice").val();

            let url = ctx + "invoice/saveOtherFee";
             //$.common.formToJSON("addForm");
            console.log(JSON.stringify(formData));
            //$.modal.alertWarning(JSON.stringify(formData));

            $.operate.saveTabJson(url,formData);
        }
    }

    //添加行对话框
    function insertRow(){
        let customerId = $("#customerId").val();
        let invoiceIds = [];
        $(".invoiceId").each(function(ii,obj){
            invoiceIds.push($(obj).val())
        });
        //选择发货单
        let url = ctx + "invoice/diaInvoice?customerId="+customerId+"&invoiceIds="+invoiceIds.join(",");
        $.modal.open("选择发货单",url,"","",function(index, layero){
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            let priceType = $("#priceType").val();

            let isDisabled = ["","disabled","disabled","disabled"];

            if(priceType != ''){
                isDisabled[priceType] = "";
            }

            for(let idx in rows){
                addRow(rows[idx],isDisabled);
            }

            layer.close(index);
        });
    }

    function addRow(row,isDisabled){
        let html = [];
        html.push('<tr>');
        html.push('<input  class="invoiceId"  type="hidden" id="lotId'+invoiceListSize+'" value="'+row.invoiceId+'">');
        html.push('<input type="hidden" id="lotno'+invoiceListSize+'" value="'+row.vbillno+'">');
        html.push('<td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" onclick="removeRow(this)" title="删除选择行"></a></td>');
        html.push('<td><input data-rowidx="'+invoiceListSize+'" id="shareMoney'+invoiceListSize+'" class="form-control shareMoney" type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this)"></td>');
        html.push('<td><input onblur="calNumCountMoney(this)" data-rowidx="'+invoiceListSize+'" id="numCount'+invoiceListSize+'" class="form-control priceType priceType1" type="text" '+isDisabled[1]+' data-numcount="'+row.numCount+'" oninput="$.numberUtil.onlyNumberLonger(this)"></td>');
        html.push('<td><input onblur="calWeightCountMoney(this)" data-rowidx="'+invoiceListSize+'" id="weightCount'+invoiceListSize+'" class="form-control priceType priceType2" type="text" '+isDisabled[2]+' data-weightcount="'+row.weightCount+'" oninput="$.numberUtil.onlyNumberLonger(this)"></td>');
        html.push('<td><input onblur="calVolumeCountMoney(this)" data-rowidx="'+invoiceListSize+'" id="volumeCount'+invoiceListSize+'" class="form-control priceType priceType3" type="text" '+isDisabled[3]+' data-volumecount="'+row.volumeCount+'" oninput="$.numberUtil.onlyNumberLonger(this)"></td>');
        html.push('<td>'+row.vbillno+'</td>');
        html.push('<td>'+row.reqDeliDate+'</td>');
        html.push('<td>');
        html.push('<span >'+row.deliAddrName+'</span><i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>');
        html.push('<span >'+row.arriAddrName+'</span>');
        html.push('</td>');
        html.push('<td >'+row.numCount+'</td>');
        html.push('<td >'+row.weightCount+'</td>');
        html.push('<td >'+row.volumeCount+'</td>');
        html.push('</tr>');
        invoiceListSize++;
        $("#addRowBody").append(html.join(""))
    }
    //删除行
    function removeRow(obj){
        $(obj).parent().parent().remove();
        calSumShare();
    }

</script>
</body>
</html>