<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('发货单列表')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <th:block th:include="include :: bootstrap-editable-css"/>
</head>
<style>
    .flex{
        display: flex;
        align-items: center;
    }
    .fixed-table-toolbar{
        display: none;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="customerId" th:value="${customerId}">
                
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-4">
                        <div class="col-md-12 col-sm-12" style="padding-right: 0">
                            <div class="form-group">
                                <div class="col-sm-12" style="padding-right: 0">
                                    <input name="vbillno" placeholder="发货单号" class="form-control" type="text"
                                           maxlength="30" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--<div class="col-md-2 col-sm-4">
                        <div class="col-md-12 col-sm-12" style="padding-right: 0">
                            <div class="form-group">
                                <div class="col-sm-12" style="padding-right: 0">
                                    <input name="custAbbr" placeholder="客户简称" class="form-control" type="text"
                                           maxlength="20" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>-->
                    <div class="col-md-4 col-sm-12">
                        <div class="col-md-12 col-sm-12">
                            <div class="form-group">
                                <!--                            <label class="col-sm-2">要求提货日期：</label>-->
                                <div class="">
                                    <input type="text" placeholder="要求提货开始日" style="width: 45%; float: left;" class="form-control"
                                           id="reqDeliDateStart"  name="params[reqDeliDateStart]" autocomplete="off">
                                    <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                    <input type="text" placeholder="要求提货截止日" style="width: 45%; float: left;" class="form-control"
                                           id="reqDeliDateEnd"  name="params[reqDeliDateEnd]" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-12">
                        <div class="col-md-12 col-sm-12">
                            <div class="form-group">
                                <select name="params[receiveVbillstatus]" class="form-control valid" aria-invalid="false">
                                    <option value="">--应收状态--</option>
                                    <option th:each="dict : ${receiveDetailStatusEnum}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>


                </div>

                <div class="row no-gutter">
                    <div class="col-sm-5" style="padding-left: 20px;">
                        <div class="form-group">
<!--                            <label class="col-sm-4">提货方地址：</label>-->
                            <div class="col-sm-12">
                                <div class="col-sm-4">
                                    <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliCityId" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliAreaId" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>
                            <!-- <div class="col-sm-4">
                                <input name="deliDetailAddr" id="deliDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                       maxlength="50" autocomplete="off">
                            </div> -->
                        </div>
                    </div>

                    <div class="col-sm-5">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">收货方地址：</label>-->
                            <div class="col-sm-2" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-10">
                                <div class="col-sm-4">
                                    <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>
                            <!-- <div class="col-sm-3">
                                <input name="arriDetailAddr" id="arriDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                       maxlength="50" autocomplete="off">
                            </div> -->
                        </div>
                    </div>

                    <div class="col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="col-sm-12 select-table table-striped toofoot pm">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<script th:inline="javascript">
    var prefix = ctx + "invoice";
    var balaType=[[${@dict.getType('bala_type')}]]; 
    var receiveDetailStatusEnum=[[${receiveDetailStatusEnum}]];
    var carLen=[[${@dict.getType('car_len')}]];
    $(function () {
        var options = {
            url: prefix + "/invoiceReceiveDetailList",
            fixedColumns: false,
            // fixedNumber: 3,
            showToggle: false,
            showColumns: false,
            showSearch: false,
            showRefresh: false,
            modalName: "委托单",
            uniqueId: "invoiceId",
            height: 560,
            clickToSelect: true,
            // onEditableSave: function (field, row, oldValue, $el) {
            //     var data;
                
            // },
            columns: [
                {
                    checkbox: true
                },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'vbillno'
                },
                {
                    title: '客户简称',
                    align: 'left',
                    field: 'custAbbr'
                },

                {
                    title: '收款信息',
                    align: 'left',
                    field: 'receiveDetailList',
                    formatter: function(value, row, index){
                        let html=``;
                        value.forEach(res=>{
                            let receiveDetailStatusEnumHtml="";
                            receiveDetailStatusEnum.forEach(resT=>{
                                if(res.vbillstatus==resT.value){
                                    receiveDetailStatusEnumHtml=resT.context;
                                }
                            })
                            html =`<div class="flex">`;
                                html+=`<div>`+receiveDetailStatusEnumHtml +`</div>`
                                html+=`<div class="ml5">`;
                                    html+=`<div>`+res.transFeeCount+`-`+ $.table.selectDictLabel(balaType, res.balatype)+`</div>`
                                    html+=`<div>`+res.vbillno+`</div>`
                            html+=`</div></div>`;
                        })
                        
                        return html;
                    }
                },
                {
                    title: '要求提货时间',
                    align: 'left',
                    field: 'reqDeliDate',
                    formatter: function(value, row, index){
                        return value.slice(0,10) 
                    }
                },
                // {
                //     title: '要求到货时间',
                //     align: 'left',
                //     field: 'reqArriDate'
                // },
                {
                    title: '提货地址',
                    align: 'left',
                    field: 'shippingAddress'
                },
                {
                    title: '收货地址',
                    align: 'left',
                    field: 'receivingAddress'
                },
                {
                    title: '货量',
                    field: 'goodsName',
                    align: 'left',
                    formatter: function(value, row, index){
                        let count=[];
                        if(row.numCount){
                            count.push(row.numCount+"件")
                        }
                        if(row.weightCount){
                            count.push(row.weightCount+"吨")
                        }
                        if(row.volumeCount){
                            count.push(row.volumeCount+"m3")
                        }
                        return value+" "+count.join("|")
                    }
                }, 
                {
                    title: '车辆信息',
                    field: 'carLen',
                    align: 'left',
                    formatter: function(value, row, index){
                        return $.table.selectDictLabel(carLen, value)+row.carTypeName;
                    }
                }, 

                {
                    title: '总应收',
                    field: 'receiveDetailList',
                    align: 'left',
                    formatter: function(value, row, index){
                        let transFeeCount=0;
                        if(value.length>0){
                            value.forEach(res=>{
                                transFeeCount += Number(res.transFeeCount)
                            })
                        }                     
                        return transFeeCount;
                    }
                },
            ]
        };
        $.table.init(options);
        
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                //查询方法
                searchPre();
            }
        });

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            var reqDeliDateStart = laydate.render({
                elem: '#reqDeliDateStart', //指定元素
                isInitValue : false,
                trigger: 'click',
                type: 'date'
            });
            var reqDeliDateEnd = laydate.render({
                elem: '#reqDeliDateEnd', //指定元素
                isInitValue : false,
                trigger: 'click',
                type: 'date'
            });
        });

        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
    });

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        // var deliDetailAddr= $('#deliDetailAddr').val()
        // var arriDetailAddr= $('#arriDetailAddr').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        // $('#deliDetailAddr').val(arriDetailAddr)
        // $('#arriDetailAddr').val(deliDetailAddr)
        searchPre()
    }

    /** 获取选中行*/
    function getChecked() {
        return $.btTable.bootstrapTable('getSelections');
    }

    function searchPre() {
        var data = {};
        //data.carLenId = $.common.join($('#carLenId').selectpicker('val'));
        $.table.search('role-form', data);
    }

    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

   
</script>
</body>
</html>