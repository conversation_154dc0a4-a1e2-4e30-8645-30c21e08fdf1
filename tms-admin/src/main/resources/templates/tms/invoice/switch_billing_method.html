<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('包车/往返包车切换')" />
    <style>
        .conversion-container {
            max-width: 600px;
            /*margin: 20px auto;*/
            padding: 20px;
            background-color: #f5f5f5;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .conversion-header {
            text-align: center;
            font-size: 16px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .price-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
    </style>
</head>

<body>
<div class="form-content">
    <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
        <div class="panel-body">
            <div class="conversion-container">
                <div class="conversion-header">
                    <span th:if="${invoice.isRoundTrip == 0}">（<span th:each="billingMethod:${billingMethods}"
                                                                     th:if="${invoice.billingMethod} == ${billingMethod.value + ''}"
                                                                     th:text="${billingMethod.context}"></span>）单程 改 往返</span>
                    <span th:if="${invoice.isRoundTrip == 1}">（<span th:each="billingMethod:${billingMethods}"
                                                                     th:if="${invoice.billingMethod} == ${billingMethod.value + ''}"
                                                                     th:text="${billingMethod.context}"></span>）往返 改 单程</span>
                </div>
                <div class="price-row">
                    <span>单价：</span>
                    <span id="unitPrice">xxx元（不含税xxx元）</span>
                </div>
                <div class="price-row">
                    <span>总运费：</span>
                    <span id="totalPrice">xxx元（不含税xxx元）</span>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    // 获取后端传递的基础价格和税率
    var price = [[${price}]]
    var taxRate = [[${taxRate}]]
    var billingType = [[${billingType}]]
    var invoiceId = [[${invoice.invoiceId}]]

    $(function () {
        // 页面加载时计算价格
        calculatePrice();
    });

    // 计算价格
    function calculatePrice() {
        if (price.type == 0) {
            $("#unitPrice").text("暂无合同价");
            $("#totalPrice").text("暂无合同价");
        }else if (price.type == 1) {
            if (price.isIncludeTax === "1") {
                let unitPriceIncludeTax = price.price
                let costAmountIncludeTax = price.totalPrice

                let unitPrice;
                let totalPrice;

                if (billingType === '4' || billingType === '2' || billingType === '3' || billingType === '5' || billingType === '7') {
                    unitPrice = unitPriceIncludeTax / taxRate;
                    totalPrice = costAmountIncludeTax / taxRate;
                } else {
                    unitPrice = unitPriceIncludeTax;
                    totalPrice = costAmountIncludeTax;
                }

                // 四舍五入保留两位小数
                unitPrice = parseFloat(unitPrice).toFixed(2);
                totalPrice = parseFloat(totalPrice).toFixed(2);


                // 更新页面显示
                $("#unitPrice").text(
                    unitPriceIncludeTax + "元（不含税" +
                    unitPrice + "元）"
                );

                $("#totalPrice").text(
                    costAmountIncludeTax + "元（不含税" +
                    totalPrice + "元）"
                );
            }else if (price.isIncludeTax === "0"){
                let unitPrice = price.price;
                let totalPrice = price.totalPrice;

                let unitPriceIncludeTax;
                let costAmountIncludeTax;

                if (billingType != "") {
                    unitPriceIncludeTax = unitPrice * taxRate
                    costAmountIncludeTax = totalPrice * taxRate
                }else {
                    unitPriceIncludeTax = unitPrice
                    costAmountIncludeTax = totalPrice
                }
                // 四舍五入保留两位小数
                unitPriceIncludeTax = parseFloat(unitPriceIncludeTax).toFixed(2);
                costAmountIncludeTax = parseFloat(costAmountIncludeTax).toFixed(2);

                // 更新页面显示
                $("#unitPrice").text(
                    unitPriceIncludeTax + "元（不含税" +
                    unitPrice + "元）"
                );

                $("#totalPrice").text(
                    costAmountIncludeTax + "元（不含税" +
                    totalPrice + "元）"
                );
            }
        }
    }

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            if (price.type == 0) {
                $.modal.alertWarning("未查询到合同价，请先维护合同价")
                return;
            }

            var data = {invoiceId: invoiceId};
            $.operate.save(ctx + "invoice/switchBillingMethod", data);
        }
    }
</script>
</body>
</html>