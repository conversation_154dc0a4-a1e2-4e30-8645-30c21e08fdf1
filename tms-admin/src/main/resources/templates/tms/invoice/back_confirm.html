<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反确认')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>
<style>
    .file-drop-zone{
        height: 100% !important;
    }
</style>

<body>
<div class="form-content">
    <form id="form-invoice-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="invoiceId" name="invoiceId" type="hidden" th:value="${invoiceId}">
            <div class="panel-body">
                <!--基础信息 begin-->
                <div class="row">
                    <div class="col-xs-6">
                        <div class="form-group">
                            <label class="col-sm-5">撤销类型：</label>
                            <div class="col-sm-7">
                                <select name="unconfirmType" id="unconfirmType" th:with="type=${@dict.getType('back_confirm_type')}" class="form-control valid" aria-invalid="false">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="form-group">
                            <label class="col-sm-5">反确认类型：</label>
                            <div class="col-sm-7" >
                                <select name="deConfirmation" id="deConfirmation" th:with="type=${@dict.getType('de_confirmation')}" class="form-control valid" aria-invalid="false" onchange="checkAuthority(this.value)" disabled>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"  th:selected="${deConfirmation == dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-sm-5">撤销说明：</label>
                            <div class="col-sm-12">
                                <textarea name="unconfirmMemo" id="unconfirmMemo" class="form-control" type="text"
                                          maxlength="200" required="" aria-required="true"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-sm-5">申请人：</label>
                            <div class="col-sm-12">
                                <input name="unconfirmApplicationName" id="unconfirmApplicationName" class="form-control" type="text"
                                       maxlength="30" required="" aria-required="true"></input>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="">
                            <div class="">
                                <div class="">
                                    <input id="image" class="form-control" name="image" type="file" accept="image/*" multiple>
                                    <input id="appendixId" name="appendixId" type="hidden">
                                </div>
                                <label id="error-panel" class="error" for="image" style="display: none;"></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "invoice";

    $(function() {
        var image = {
            maxFileCount: 5,
            publish: "imgDone",  //用于绑定下一步方法
            fileType: 'image'//文件类型
        };
        $.file.initAddFiles("image", "appendixId", image);

        // 图片上传成功后
        $("#image").on('filebatchuploadsuccess', function (event, data) {
            var tid = data.response.tid;
            $("#appendixId").val(tid);
            commit();
        });
    })
    /**
     * 校验
     */
    $("#form-invoice-unconfirm").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            unconfirmType:{
                required:true,
            },
            unconfirmMemo:{
                required:true,
            },
            unconfirmApplicationName: {
                required:true
            },
            // image:{
            //     required:true
            // }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        $("#error-panel").css('display', 'none')

        if ($.validate.form()) {
            var reg= /^(([a-zA-Z+\.?\·?a-zA-Z+]{2,30}$)|([\u4e00-\u9fa5+\·?\u4e00-\u9fa5+]{2,30}$))/;
            var unconfirmApplicationName = $("#unconfirmApplicationName").val()
            if(!reg.test(unconfirmApplicationName)){
                $.modal.alertWarning("申请人姓名输入有误，请重新输入")
                return false;
            }

            // 检查是否有文件上传
            // if($('#image').fileinput('getFilesCount')  == 0) {
            //     $.modal.alertWarning("请先选择文件路径")
            //     return  false;
            // }

            var errorMsg = $(".kv-fileinput-error ul li").text()
            if(errorMsg != null  && errorMsg != '') {
                $.modal.alertWarning("请上传有效的图片格式")
                return  false;
            }

            layer.confirm('确定进行反确认操作？', {
                btn: ['确认','取消']
            }, function(){
                // if ($("#image").val() != "") {
                    $("#image").fileinput('upload');
                // }else {
                //     commit();
                // }
            }, function(){
            });
        }
    }

    function commit() {
        let query = $("#form-invoice-unconfirm").serializeArray();
        query.push({"name" : "deConfirmation", "value" : $("#deConfirmation").val()})
        // layer.confirm('确定进行反确认操作？', {
        //     btn: ['确认','取消']
        // }, function(){
            //操作完成后,trace页面的条件还需要带着一起查询    不是直接刷新父页面
            $.operate.save(prefix + "/back_confirm", query);
        // }, function(){
            //取消操作后清空
            // clearUploadFile()
        // });
    }

    $('.kv-file-remove').click(function () {
        const title = $(this).parent().parent().parent().parent().find('.file-footer-caption').attr('title')

        console.log(`>>>>>>移除文件`)
    })

    $('.fileinput-remove-button').click(function () {
        console.log(`>>>>>>移除文件`)
        clearUploadFile()
    })

    // function clearUploadFile() {
    //     $('input[name=iamge]').val("")
    //     $('input[name=appendixId]').val("")
    // }
</script>
</body>
</html>