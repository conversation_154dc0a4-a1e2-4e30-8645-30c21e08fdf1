<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('发货单')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .popup{
        width: 100%;
        height: 100%;
        position: fixed;
        top: 0;
        left: 0;
        background: rgba(0, 0, 0, .4);
        display: none;
    }
    .none{

    }
    .popup_content {
        width: 600px;
        left: 50%;
        margin-left: -300px;
        background: #fff;
        top: 20%;
        position: fixed;
        border-radius: 7px;
    }
    .popup_text{
        height: 24px;
        line-height: 24px;
        color: #808080;
    }
    .closed{
        width: 15px;
        height: 15px;
        position: absolute;
        right: 5px;
        top: 5px;
        color: #808080;
    }
    .table-striped{
        height: auto;
    }

</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <input type="hidden" name="customerId" th:value="${customerId}">
                <input type="hidden" name="invoiceIds" th:value="${invoiceIds}">

                <div class="row no-gutter">
                    <div class="col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">发货单编号：</label>-->
                            <div class="col-sm-12">
                                <input name="vbillno" id="vbillno" placeholder="发货单编号" class="form-control" type="text"
                                       maxlength="20" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group">
                            <!--                            <label class="col-sm-2">要求提货日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" placeholder="要求提货开始日期" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateStart"  name="params[reqDeliDateStart]" autocomplete="off">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" placeholder="要求提货截止日期" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateEnd"  name="params[reqDeliDateEnd]" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row no-gutter">
                    <div class="col-sm-5">
                        <div class="form-group">
<!--                            <label class="col-sm-4">提货方地址：</label>-->
                            <div class="col-sm-4">
                                <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                            <div class="col-sm-4">
                                <select name="deliCityId" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4">
                                <select name="deliAreaId" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-5">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">收货方地址：</label>-->
                            <div class="col-sm-2" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 40px;height: 40px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-10">
                                <div class="col-sm-4">
                                    <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">


        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
    <div class="popup">
        <div class="popup_content">
            <div style="position: relative;padding: 20px 20px">
                <div class="popup_text">原始发货单号：<nobr class="one"></nobr></div>
                <div class="popup_text ">原始运段号：<nobr class="two"></nobr></div>
                <div class="popup_text ">原始委托单号：<nobr class="three"></nobr></div>
                <div class="popup_text ">原始运单号：<nobr class="four"></nobr></div>
                <div class="closed">
                    <i class="glyphicon glyphicon-remove"></i>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />

<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a th:href="@{/file/ReceiveAdjustModel.xlsx}" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
<script th:inline="javascript">


    var prefix =  ctx + "invoice";



    $(function () {


        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });

        var options = {
            url: prefix + "/list",

            showToggle:false,
            showColumns:true,
            modalName: "发货单",
            showRefresh:false,
            showSearch:false,
            showColumns:false,
            height: 560,
            uniqueId: "invoiceId",
            clickToSelect: true,

            columns: [
                {
                checkbox: true
                },

                {
                    title: '发货单编号',
                    field: 'vbillno',
                    align: 'left',
                    width: 20,
                    switchable: false,
                    formatter: function (value,row) {
                        var context;
                        if (row.isFleetData === '1' && row.isFleetAssign === '1') {
                            let bizInvoiceVbillno = row.bizInvoiceVbillno
                            let bizSegmentVbillno = row.bizSegmentVbillno
                            let bizEntrustVbillno = row.bizEntrustVbillno
                            let bizEntrustLotVbillno = row.bizEntrustLotVbillno

                            context = '<span class="label label-primary" onclick="getDetail(\'' + bizInvoiceVbillno + '\',\''+bizSegmentVbillno+'\',\''+bizEntrustVbillno+'\',\''+bizEntrustLotVbillno+'\')">'+value+'</span>';
                        }else {
                            context = value;
                        }
                        return context;
                    }
                },

                {
                    title: '客户简称',
                    field: 'custAbbr',
                    align: 'left',
                    width: 20,
                    switchable: false,
                    formatter: function status(value,row) {
                        if (row.bizCustAbbr != null) {
                            return value + '-' + row.bizCustAbbr;
                        }else {
                            return value;
                        }
                    }
                },
                {
                    title: '要求提货日期',
                    field: 'reqDeliDate',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,16);
                    }
                },
                {
                    title: '提货|到货方',
                    field: 'deliAddrName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.deliAddrName+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriAddrName;
                    }
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var deliAddr = row.deliProName+row.deliCityName+row.deliAreaName;
                        var arriAddr = row.arriProName+row.arriCityName+row.arriAreaName;
                        return deliAddr+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+arriAddr;
                    }
                },

                {
                    title: '货品名称',
                    field: 'params.goodsName',
                    align: 'left',
                },
                {
                    title: '总件数',
                    field: 'numCount',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var numCountAdjust = row.numCountAdjust;
                        if(numCountAdjust != 0 && $.common.isNotEmpty(numCountAdjust)){
                            return numCountAdjust
                        }else{
                            return value
                        }
                    }
                },
                {
                    title: '总重量(吨)',
                    field: 'weightCount',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var weightCountAdjust = row.weightCountAdjust;
                        if(weightCountAdjust != 0 && $.common.isNotEmpty(weightCountAdjust)){
                            return weightCountAdjust;
                        }else{
                            return value
                        }
                    }
                },
                {
                    title: '总体积(m3)',
                    field: 'volumeCount',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var volumeCountAdjust = row.volumeCountAdjust;
                        if(volumeCountAdjust != 0 && $.common.isNotEmpty(volumeCountAdjust)){
                            return volumeCountAdjust
                        }else{
                            return value
                        }
                    }
                },
                {
                    title: '总金额(元)',
                    field: 'costAmount',
                    align: 'right',
                },
                {
                    title: '指导价(元)',
                    field: 'guidingPrice',
                    align: 'right',
                },
                {
                    title: '创建人',
                    field: 'regUserName',
                    align: 'left'
                },
                {
                    title: '创建时间',
                    field: 'regDate',
                    align: 'left'
                },
                {
                    title: '发货单备注',
                    field: 'memo',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.memo);
                    }
                }
            ]
        };
        $.table.init(options);
        searchPre();

    });

    /**
     * 日期插件
     */
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        var reqDeliDateStart = laydate.render({
            elem: '#reqDeliDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqDeliDateEnd = laydate.render({
            elem: '#reqDeliDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        /*var reqArriDateStart = laydate.render({
            elem: '#reqArriDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqArriDateEnd = laydate.render({
            elem: '#reqArriDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });*/
    });




    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        data.params = new Map();

        /*data.carLen = $.common.join($('#carLen').selectpicker('val'));
        data.carType = $.common.join($('#carType').selectpicker('val'));
        data.transCode = $.common.join($('#transCode').selectpicker('val'));
        data.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));
        data.salesDept = $.common.join($('#salesDept').selectpicker('val'));

        data.params.receivableWriteOffStatusList = $.common.join($('#receivableWriteOffStatusList').selectpicker('val'));
*/
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }
    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        searchPre();
    }

    function getChecked(){
        return $.btTable.bootstrapTable('getSelections');
    }






</script>
</body>
</html>