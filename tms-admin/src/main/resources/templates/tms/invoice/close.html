<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('关闭')"/>
</head>

<body>
<div class="form-content">
    <form id="form-invoice-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="invoiceId" name="invoiceId" type="hidden" th:value="${invoiceId}">
            <div class="panel-body">
                <!--基础信息 begin-->
                <input name="deptId" type="hidden" id="treeId">
                <div class="row">
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">撤销类型：</label>
                            <div class="col-sm-12">
                                <select name="unconfirmType" id="unconfirmType" th:with="type=${@dict.getType('back_confirm_type')}" class="form-control valid" aria-invalid="false" onchange="typeChange()">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">申请人：</label>
                            <div class="col-sm-12">
                                <input name="unconfirmApplicationName" id="unconfirmApplicationName" class="form-control" type="text"
                                       maxlength="30">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-6" id="responsibleUserNameDiv" style="display: none">
                        <div class="form-group">
                            <label class="col-sm-5"><label style="color: red">*</label>责任人：</label>
                            <div class="col-sm-12">
                                <input name="responsibleUserName" id="responsibleUserName" class="form-control" type="text"
                                       maxlength="30" onclick="selectSalesDept()" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">关闭备注：</label>
                            <div class="col-sm-12">
                                <textarea name="closeNote" id="closeNote" class="form-control" type="text"
                                          maxlength="50" required="" aria-required="true"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "invoice";

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-invoice-unconfirm").serializeArray();
            $.operate.save(prefix + "/close", data);
        }
    }

    function typeChange(){
        let unconfirmType = $("#unconfirmType").val();
        if(unconfirmType == 1){
            $("#responsibleUserNameDiv").css("display",'block');
        }else{
            $("#responsibleUserNameDiv").css("display",'none');
        }
    }

    function selectSalesDept() {
        layer.open({
            type: 2,
            area: ['90%', '90%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "选择责任人",
            content: ctx + "system/dept/opsGroupTreeZRR",
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero){
                //获取整行
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                let arr = [];
                console.log(rows)
                for(let i = 0 ; i < rows.length ; i++){
                    arr.push(rows[i].userName);
                }
                $(`#responsibleUserName`).val(arr.join(","));
                layer.close(index);
            },
            cancel: function(index) {
                return true;
            }
        });
    }
</script>
</body>
</html>