<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('关账')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: jasny-bootstrap-css" />
</head>

<body>
<div class="form-content">
    <form id="form-car-add" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">关账时间设置</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4"><span>关账时间：</span></label>
                                    <div class="col-md-8 col-sm-8">
                                        <input id="yearMonth" name="yearMonth" class="form-control" type="text" required="true" aria-required="true" maxlength="25">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>关账</button>
                                <button type="button" class="btn btn-sm btn-danger" onclick="backClose()" shiro:hasPermission="tms:closeAccount:back" ><i class="fa fa-remove"></i>撤销关账</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
            </div>
        </div>
    </form>
</div>

<div class="row">

</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "closeAccount";

    $(function () {
        var options = {
            url: prefix + "/list",
            showToggle: false,
            showColumns: true,
            modalName: "加油卡信息",
            fixedColumns: true,
            fixedNumber: 0,
            height: 550,
            showSearch: false,
            columns: [
                {
                    title: '关账年月',
                    align: 'right',
                    width: '150px',
                    field: 'yearMonth',
                    formatter: function status(value, row, index) {
                        return value.substring(0,7);
                    }
                },
                {
                    title: '操作人',
                    align: 'right',
                    width: '150px',
                    field: 'regUserName'
                },
                {
                    title: '操作时间',
                    width: '150px',
                    field: 'regDate',
                    align: 'right'
                }
            ]
        };
        $.table.init(options);
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.modal.confirm("关账选中月份财务管理、运输中心、业务中心操作将受限，确定要关账选中月份的账单吗？",function () {
                $.operate.save(prefix + "/add", $('#form-car-add').serialize(),function (result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess("保存成功！");
                        setTimeout("$.btTable.bootstrapTable('refresh');",3000)
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                });
            });
        }
    }

    function backClose(){
        if ($.validate.form()) {
            $.modal.confirm("确定要撤销关账选中月份的账单吗？",function () {
                $.operate.save(prefix + "/backAdd", $('#form-car-add').serialize(),function (result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess("保存成功！");
                        setTimeout("$.btTable.bootstrapTable('refresh');",3000)
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                });
            });
        }
    }

    //初始化
    $(function () {
        $('#collapseOne').collapse('show');

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            laydate.render({
                elem: '#yearMonth'
                ,type: 'month'
            });
        });

    });






</script>
</body>

</html>