<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('运力资源池')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        .flex{
            display: flex;
            algin-items:center;
            just-content:space-between;
        }
        .flex_left{
            width: 114px;
            line-height: 26px;
            text-align: right;
        }
        .flex_right{
            flex: 1;
        }
        .fcff {
            color: #ff1f1f;
            padding-right: 2px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="form-group">
                        <!--<div class="col-md-2 col-sm-4">
                            <div class="flex">
                                <select name="startProvinceId" id="startProvinceId" class="form-control valid" aria-invalid="false"></select>
                                <select name="startCityId" id="startCityId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <div class=" flex">
                                <select name="endProvinceId" id="endProvinceId" class="form-control valid" aria-invalid="false"></select>
                                <select name="endCityId" id="endCityId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>-->

                        <div class="col-md-4 col-sm-8" style="display: flex;">
                            <div style="width: 0;flex:1">
                                <input name="startProvinceId" type="hidden"/>
                                <input name="startCityId" type="hidden"/>
                                <input readonly class="form-control" id="city1" data-toggle="city-picker"
                                       data-simple="false"
                                       data-level="city" placeholder="点击选择省/市">
                            </div>
                            <div style="width: 26px;border-top: 1px #e5e6e7 solid;border-bottom: 1px #e5e6e7 solid;text-align: center">
                                <i style="font-size: 21px;line-height:24px;color:#598ef8;cursor: pointer" class="fa fa-long-arrow-right" onclick="exchange(this)"></i>
                                <input type="hidden" name="params[arrow]" value="R" id="arrowValue">
                                <!--<i style="font-size: 21px;line-height:24px;" class="fa fa-long-arrow-left"></i>
                                <i style="font-size: 21px;line-height:24px;" class="fa fa-exchange"></i>-->
                            </div>
                            <div style="width: 0;flex:1">
                                <input name="endProvinceId" type="hidden"/>
                                <input name="endCityId" type="hidden"/>
                                <input readonly class="form-control" id="city2" data-toggle="city-picker"
                                       data-simple="false"
                                       data-level="city" placeholder="点击选择省/市">
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <input name="params[dateRange]" class="form-control laydateRange"
                                   placeholder="日期范围" autocomplete="off" aria-required="true">
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <select class="form-control selectpicker" name="balaType" data-none-selected-text="选择结算方式">
                                <option value=""></option>
                                <option th:each="dict : ${balaType}" th:text="${dict.context}" th:value="${dict.value}"></option>
                            </select>
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <select class="form-control selectpicker" name="params[carLen]" data-none-selected-text="车长">
                                <option value=""></option>
                                <option th:each="dict : ${car_len}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <select class="form-control selectpicker" name="params[carType]" data-none-selected-text="车型">
                                <option value=""></option>
                                <option th:each="dict : ${car_type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>


                    </div>
                </div>
                <div class="row">
                    <div class="form-group">
                        <div class="col-md-2 col-sm-4">
                            <select class="form-control selectpicker" name="transportType" data-none-selected-text="运输类型">
                                <option value=""></option>
                                <option th:each="dict : ${transport_type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <input name="carrName" class="form-control" placeholder="承运商名称" autocomplete="off"
                                   aria-required="true">
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <select name="carrType" class="form-control selectpicker" data-none-selected-text="承运商类型">
                                <option></option>
                                <option th:each="dict : ${carrType}" th:if="${dict.value != '0'}" th:text="${dict.context}" th:value="${dict.value}"></option>
                            </select>
                        </div>

                        <div class="col-md-2 col-md-offset-4 col-sm-4">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="exportExcel()">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-info" onclick="smsSend()" shiro:hasPermission="trans:capacity:sms">
                <i class="fa fa-download"></i> 短信通知
            </a>
            <a class="btn btn-info" onclick="wfbjSmsSend()" shiro:hasPermission="trans:capacity:wfbjsms">
                <i class="fa fa-download"></i> 外发报价短信通知
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<th:block th:include="include :: citypicker" />
</body>
</html>

<script th:inline="javascript">
    var prefix = ctx + "trans-capacity";
    const balaType = [[${balaType}]];
    const carrType = [[${carrType}]];
    const transportType = [[${transport_type}]];

    //初始化查询条件传参
    queryParams = function (params) {
        var search = {};
        $.each($("#role-form").serializeArray(), function (i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchx();
            }
        });

        initTable()

        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '.laydateRange',
                type: 'date',
                trigger: 'click',
                range: true,
                rangeLinked: true
            });
        });

        if ($.validator) {
            $.validator.prototype.elements = function () {
                var validator = this,
                    rulesCache = {};
                return $([]).add(this.currentForm.elements)
                    .filter(":input")
                    .not(":submit, :reset, :image, [disabled]")
                    .not(this.settings.ignore)
                    .filter(function () {
                        var elementIdentification = this.id || this.name;
                        !elementIdentification && validator.settings.debug && window.console && console.error("%o has no id nor name assigned", this);
                        if (elementIdentification in rulesCache || !validator.objectLength($(this).rules()))
                            return false;
                        rulesCache[elementIdentification] = true;
                        return true;
                    });
            };
        }

        //$.provinces.init("startProvinceId", "startCityId");
        //$.provinces.init("endProvinceId", "endCityId");

        $('#city1').on('cp:updated', function(event) {
            let val = event.target.value; //320600/320613以斜杠拼接的数据
            $('[name=startProvinceId]').val('');
            $('[name=startCityId]').val('');
            if (val) {
                let arr = val.split('/');
                if (arr.length > 1) {
                    $('[name=startCityId]').val(arr[1]);
                }
                if (arr.length > 0) {
                    $('[name=startProvinceId]').val(arr[0]);
                }
            }
        });
        $('#city2').on('cp:updated', function(event) {
            let val = event.target.value; //320600/320613以斜杠拼接的数据
            $('[name=endProvinceId]').val('');
            $('[name=endCityId]').val('');
            if (val) {
                let arr = val.split('/');
                if (arr.length > 1) {
                    $('[name=endCityId]').val(arr[1]);
                }
                if (arr.length > 0) {
                    $('[name=endProvinceId]').val(arr[0]);
                }
            }
        });
    });

    function initTable() {
        var options = {
            url: prefix + "/list-new",
            queryParams: queryParams,
            uniqueId: 'id',
            showToggle: false,
            showColumns: true,
            //fixedColumns: true,
            //fixedNumber: 4,
            clickToSelect: true,
            height: 620,
            showFooter: true,
            showExport: false,
            onPostBody: function () {
                //合并页脚
            },
            onRefresh: function (params) {
            },
            onCheck: function (row, $element) {
            },
            onUncheck: function (row, $element) {
            },
            onCheckAll: function (rowsAfter) {
            },
            onUncheckAll: function () {
            },
            columns: [
                {
                    checkbox: true
                },
                {field: 'startProvinceId', title: '线路',formatter:function(value,row,index){
                    return ((row.startCityName == '市辖区' || row.startCityName == '县') ? row.startProvinceName : row.startCityName)
                        + " ~ " + ((row.endCityName == '市辖区' || row.endCityName == '县') ? row.endProvinceName : row.endCityName);
                    }},
                {field: 'carLens', title: '车长',formatter:function(value,row,index){
                    if (value) {
                        return value.split(',').join('，')
                    }
                }},
                {field: 'carTypes', title: '车型',formatter:function(value,row,index){
                        if (value) {
                            return value.split(',').join('，')
                        }
                    }},
                {field: 'carrName', title: '承运商名称',formatter:function(value,row,index){
                    let text = '';
                    if (row.setTransportPool == 2) {
                        text = '<span title="已入驻">(固)</span> ' + text + "</a>";
                    }
                    var flag = [[${@shiroUtils.getSubject().isPermitted('basic:carrier:detail')}]];
                    if (flag) {
                        text = text + '<a href="javascript:carrirerTab(\''+row.carrierId+'\')">' + value + '</a>';
                    } else {
                        text = text + value;
                    }
                    return text;
                    }},
                {field: 'carrType', title: '承运商类型',formatter:function(value,row,index){
                        return $.table.selectEnumContext(carrType, value);
                    }},
                {field: 'contact', title: '联系方式',formatter:function(value,row,index){
                    return value + " - " + row.phone
                }},
                {field: 'balaType', title: '结算方式',formatter: function(value, row, index) {
                        return $.table.selectEnumContext(balaType, value);
                    }},
                {field: 'payWay', title: '付款方式'},
                {field: 'transportType', title: '运输类型',formatter:function(value,row,index){
                    return $.table.selectDictLabel(transportType, row.transportType)
                    }},
                {field: 'times', title: '承运次数',formatter:function(value,row,index){
                    var flag = [[${@shiroUtils.getSubject().isPermitted('tms:segment:segmentHistoryPrice')}]];
                    if (flag) {
                        return "<a href='javascript:timesTab(" + index+ ")'>" + value + "</a>";
                    } else {
                        return value;
                    }
                    }},
                {field: 'smsTime', title: '最近短信发送时间', formatter: function (value,row,index){
                    if (value) {
                        return '<a href="javascript:smsLog(\''+row.carrierId+'\')">' + value + '</a>'
                    }
                }}
            ]
        };
        $.table.init(options);
    }

    function searchx() {
        var data = {};
        $.table.search('role-form', data);
    }

    function resetx() {
        $("#role-form")[0].reset();
        $('#city1').citypicker('reset');
        $('#city2').citypicker('reset');
        $('.selectpicker').selectpicker('refresh');
        searchx();
    }

    function exportExcel() {
        $.modal.confirm("确定导出数据吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            var data = $("#role-form").serializeArray();
            $.post(prefix + "/export-new", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    function exchange(i) {
        let $i = $(i);
        if ($i.hasClass("fa-long-arrow-right")) {
            $i.removeClass("fa-long-arrow-right");
            $i.addClass("fa-long-arrow-left");
            $('#arrowValue').val("L");
        } else if ($i.hasClass("fa-long-arrow-left")) {
            $i.removeClass("fa-long-arrow-left");
            $i.addClass("fa-exchange");
            $('#arrowValue').val("LR");
        } else if ($i.hasClass("fa-exchange")) {
            $i.removeClass("fa-exchange");
            $i.addClass("fa-long-arrow-right");
            $('#arrowValue').val("R");
        }
    }

    function carrirerTab(carrierId) {
        $.modal.openTab("承运商详情", ctx + "basic/carrier/infoCheck?carrierId=" + carrierId);
    }
    function timesTab(idx) {
        //$.modal.openTab("历史价格", ctx + "tms/segment/segmentHistoryPriceBtn?carrierId=" + carrierId + "&startCityId=" + startCityId + "&endCityId=" + endCityId);
        let row = $.btTable.bootstrapTable('getData')[idx];
        let url = ctx + "business/historyPrice?src=pool&carrierId=" + row.carrierId
            + "&deliProvinceId=" + row.startProvinceId + "&arriProvinceId=" + row.endProvinceId
            + "&deliCityId=" + row.startCityId + "&arriCityId=" + row.endCityId;
        if (row.startDate) {
            url = url + "&startDate=" + row.startDate
        }
        if (row.endDate) {
            url = url + "&endDate=" + row.endDate
        }
        $.modal.openTab("历史价格", url);
    }

    function smsSend() {
        var carriers = $.btTable.bootstrapTable('getSelections');
        if (carriers.length == 0) {
            $.modal.msgWarning("请勾选线路选择对应的承运商")
            return
        }
        let content = $("#smsFormTemp").html();
        layer.open({
            type: 1,
            title: '编辑短信内容',
            area: ['400px', '500px'],
            content,
            offset: '100px',
            btn: ['提交', '取消'],
            shadeClose: false,
            skin: null,
            success: function(){
                textPriview();
            },
            yes: function(index, layero) {
                let pass = $.validate.form('smsForm');
                if (!pass) {
                    return;
                }
                let carrierIds = [];
                let phones = [];
                for (let i = 0; i < carriers.length; i++) {
                    carrierIds.push(carriers[i].carrierId);
                    phones.push(carriers[i].phone);
                }
                $.modal.confirm("已确认信息无误并提交？",function(){
                    let text = $('#viewPanel').text();
                    $.ajax({
                        type: 'post',
                        url: prefix + "/smsSend",
                        data: {carrierId: carrierIds.join(","), phone: phones.join(','), text},
                        success: function(res) {
                            if (res.code == 0) {
                                $.modal.msgSuccess(res.msg)
                                layer.close(index)
                            } else {
                                $.modal.alertError(res.msg)
                            }
                        }
                    })
                });
            }
        })
    }

    function wfbjSmsSend() {
        layer.open({
            type: 1,
            title: '编辑短信内容',
            area: ['600px', '300px'],
            content:$("#wfbjSmsFormTemp").html(),
            offset: '100px',
            btn: ['提交', '取消'],
            shadeClose: false,
            skin: null,
            success: function(){
                // textPriview();
            },
            yes: function(index, layero) {
                let pass = $.validate.form('wfbjSmsForm');
                if (!pass) {
                    return;
                }
                $.modal.confirm("已确认信息无误并提交？",function(){
                    let text = $('#viewPanel').text();
                    $.ajax({
                        type: 'post',
                        url: prefix + "/wfbjSmsSend",
                        data: {wfbjSmsText: $("#wfbjSmsText").val()},
                        success: function(res) {
                            if (res.code == 0) {
                                $.modal.msgSuccess(res.msg)
                                layer.close(index)
                            } else {
                                $.modal.alertError(res.msg)
                            }
                        }
                    })
                });
            }
        })
    }

    function textPriview() {
        let fields = ['qyd','dhd','dw','bz','hp','cx','dh','memo'];
        let obj = {};
        for (let i = 0; i < fields.length; i++) {
            obj[fields[i]] = $('#smsForm').find('[name="'+fields[i]+'"]').val();
        }
        let hp = obj.dw ? ('，' + obj.dw+'吨') : '';
        if (obj.hp) {
            hp += (hp?'':'，')+obj.hp
        }
        if (obj.bz) {
            hp += '，' + obj.bz
        }
        let cx = obj.cx && ('，' + obj.cx);
        let memo = obj.memo
        let text = `吉华/铭源货源：${obj.qyd||'【起运地】'}到${obj.dhd||'【到货地】'}${hp}${cx}，联系电话：${obj.dh||'【联系电话】'}${memo && '，'+memo}`;
        $('#viewPanel').text(text);
    }
    function smsLog(carrierId) {
        layer.open({
            type: 1,
            title: '短信日志（最近10条）',
            area: ['800px', '700px'],
            skin: '',
            shadeClose: true,
            content: '<div style="padding: 5px"><table class="table table-bordered">' +
                '<thead><tr><th style="width: 90px">手机号</th><th style="width: 132px">发送时间</th><th style="width: 37px">结果</th><th>短信内容</th></tr></thead>' +
                '<tbody id="smsLogRows"></tbody>' +
                '</table></div>',
            success: function () {
                $.ajax({
                    url: prefix + "/smsLog",
                    cache: false,
                    data: 'offset=0&limit=10&carrierId=' + carrierId,
                    success: function (res) {
                        let tmp = [];
                        for (let i = 0; i < res.rows.length; i++) {
                            var row = res.rows[i];
                            tmp.push("<tr><td>",row.phone,"</td><td title='",row.sendUserName,"'>",row.time,"</td><td>",row.success==1?'成功':'失败',"</td><td>",row.text,"</td></tr>")
                        }
                        $('#smsLogRows').html(tmp.join(''))
                    }
                })
            },
        })
    }
</script>
<script type="text/template" id="smsFormTemp">
    <form id="smsForm" style="padding: 10px 40px 10px 0;">
        <div class="flex">
            <div class="flex_left"><span class="fcff">*</span>起运地：</div>
            <div class="flex_right"><input class="form-control" required name="qyd" oninput="textPriview()"></div>
        </div>
        <div class="flex" style="margin-top: 5px">
            <div class="flex_left"><span class="fcff">*</span>到货地：</div>
            <div class="flex_right"><input class="form-control" required name="dhd" oninput="textPriview()"></div>
        </div>
        <div class="flex" style="margin-top: 5px">
            <div class="flex_left">吨位：</div>
            <div class="flex_right"><input class="form-control" name="dw" oninput="textPriview()"></div>
        </div>
        <div class="flex" style="margin-top: 5px">
            <div class="flex_left">包装：</div>
            <div class="flex_right"><input class="form-control" name="bz" oninput="textPriview()"></div>
        </div>
        <div class="flex" style="margin-top: 5px">
            <div class="flex_left">货品名称：</div>
            <div class="flex_right"><input class="form-control" name="hp" oninput="textPriview()"></div>
        </div>
        <div class="flex" style="margin-top: 5px">
            <div class="flex_left">车型：</div>
            <div class="flex_right"><input class="form-control" name="cx" oninput="textPriview()"></div>
        </div>
        <div class="flex" style="margin-top: 5px">
            <div class="flex_left"><span class="fcff">*</span>联系电话：</div>
            <div class="flex_right"><input class="form-control" required name="dh" oninput="textPriview()"></div>
        </div>
        <div class="flex" style="margin-top: 5px">
            <div class="flex_left">备注：</div>
            <div class="flex_right"><textarea class="form-control" required name="memo" oninput="textPriview()">现货24小时装车，空车与我联系</textarea></div>
        </div>
        <div style="margin-top: 10px;width: 100%;overflow: auto;padding-left: 40px;">
            <legend style="font-size: 13px;margin-bottom: 0">短信预览</legend>
            <div id="viewPanel"></div>
        </div>
    </form>
</script>

<script type="text/template" id="wfbjSmsFormTemp">
    <form id="wfbjSmsForm" style="padding: 10px 40px 10px 0;">
        <div class="flex" style="margin-top: 5px">
            <div class="flex_left">备注：</div>
            <div class="flex_right">
                <textarea class="form-control" required id="wfbjSmsText" name="wfbjSmsText" rows="5"></textarea>
            </div>
        </div>
    </form>
</script>
