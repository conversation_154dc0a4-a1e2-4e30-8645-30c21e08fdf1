<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('运力资源池')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        .flex{
            display: flex;
            algin-items:center;
            just-content:space-between;
        }
        .flex_left{
            width: 114px;
            line-height: 26px;
            text-align: right;
        }
        .flex_right{
            flex: 1;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="form-group">
                        <!--<div class="col-md-2 col-sm-4">
                            <div class="flex">
                                <select name="startProvinceId" id="startProvinceId" class="form-control valid" aria-invalid="false"></select>
                                <select name="startCityId" id="startCityId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <div class=" flex">
                                <select name="endProvinceId" id="endProvinceId" class="form-control valid" aria-invalid="false"></select>
                                <select name="endCityId" id="endCityId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>-->

                        <div class="col-md-2 col-sm-4">
                            <input name="startProvinceId" type="hidden"/>
                            <input name="startCityId" type="hidden"/>
                            <input readonly class="form-control" id="city1" data-toggle="city-picker"
                                   data-simple="false"
                                   data-level="city" placeholder="点击选择省/市">
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <input name="endProvinceId" type="hidden"/>
                            <input name="endCityId" type="hidden"/>
                            <input readonly class="form-control" id="city2" data-toggle="city-picker"
                                   data-simple="false"
                                   data-level="city" placeholder="点击选择省/市">
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <select class="form-control selectpicker" name="balaType" data-none-selected-text="选择结算方式">
                                <option value=""></option>
                                <option th:each="dict : ${balaType}" th:text="${dict.context}" th:value="${dict.value}"></option>
                            </select>
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <select class="form-control selectpicker" name="params[carLen]" data-none-selected-text="车长">
                                <option value=""></option>
                                <option th:each="dict : ${car_len}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <select class="form-control selectpicker" name="params[carType]" data-none-selected-text="车型">
                                <option value=""></option>
                                <option th:each="dict : ${car_type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <select class="form-control selectpicker" name="transportType" data-none-selected-text="运输类型">
                                <option value=""></option>
                                <option th:each="dict : ${transport_type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group">

                        <div class="col-md-2 col-sm-4">
                            <input name="carrName" class="form-control" placeholder="承运商名称" autocomplete="off"
                                   aria-required="true">
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <select name="carrType" class="form-control selectpicker" data-none-selected-text="承运商类型">
                                <option></option>
                                <option th:each="dict : ${carrType}" th:if="${dict.value != '0'}" th:text="${dict.context}" th:value="${dict.value}"></option>
                            </select>
                        </div>

                        <div class="col-md-2 col-md-offset-6 col-sm-4">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="exportExcel()">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<th:block th:include="include :: citypicker" />
</body>
</html>

<script th:inline="javascript">
    var prefix = ctx + "trans-capacity";
    const balaType = [[${balaType}]];
    const carrType = [[${carrType}]];

    const transportType = [[${transport_type}]];

    //初始化查询条件传参
    queryParams = function (params) {
        var search = {};
        $.each($("#role-form").serializeArray(), function (i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchx();
            }
        });

        initTable()

        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '.laydateRange',
                type: 'date',
                trigger: 'click',
                range: true,
                rangeLinked: true
            });
        });

        if ($.validator) {
            $.validator.prototype.elements = function () {
                var validator = this,
                    rulesCache = {};
                return $([]).add(this.currentForm.elements)
                    .filter(":input")
                    .not(":submit, :reset, :image, [disabled]")
                    .not(this.settings.ignore)
                    .filter(function () {
                        var elementIdentification = this.id || this.name;
                        !elementIdentification && validator.settings.debug && window.console && console.error("%o has no id nor name assigned", this);
                        if (elementIdentification in rulesCache || !validator.objectLength($(this).rules()))
                            return false;
                        rulesCache[elementIdentification] = true;
                        return true;
                    });
            };
        }

        //$.provinces.init("startProvinceId", "startCityId");
        //$.provinces.init("endProvinceId", "endCityId");

        $('#city1').on('cp:updated', function(event) {
            let val = event.target.value; //320600/320613以斜杠拼接的数据
            $('[name=startProvinceId]').val('');
            $('[name=startCityId]').val('');
            if (val) {
                let arr = val.split('/');
                if (arr.length > 1) {
                    $('[name=startCityId]').val(arr[1]);
                }
                if (arr.length > 0) {
                    $('[name=startProvinceId]').val(arr[0]);
                }
            }
        });
        $('#city2').on('cp:updated', function(event) {
            let val = event.target.value; //320600/320613以斜杠拼接的数据
            $('[name=endProvinceId]').val('');
            $('[name=endCityId]').val('');
            if (val) {
                let arr = val.split('/');
                if (arr.length > 1) {
                    $('[name=endCityId]').val(arr[1]);
                }
                if (arr.length > 0) {
                    $('[name=endProvinceId]').val(arr[0]);
                }
            }
        });
    });

    function initTable() {
        var options = {
            url: prefix + "/list",
            queryParams: queryParams,
            uniqueId: 'id',
            showToggle: false,
            showColumns: true,
            //fixedColumns: true,
            //fixedNumber: 4,
            clickToSelect: true,
            height: 620,
            showFooter: true,
            showExport: false,
            onPostBody: function () {
                //合并页脚
            },
            onRefresh: function (params) {
            },
            onCheck: function (row, $element) {
            },
            onUncheck: function (row, $element) {
            },
            onCheckAll: function (rowsAfter) {
            },
            onUncheckAll: function () {
            },
            columns: [
                {field: 'startProvinceId', title: '线路',formatter:function(value,row,index){
                    return ((row.startCityName == '市辖区' || row.startCityName == '县') ? row.startProvinceName : row.startCityName)
                        + " ~ " + ((row.endCityName == '市辖区' || row.endCityName == '县') ? row.endProvinceName : row.endCityName);
                    }},
                {field: 'carLens', title: '车长',formatter:function(value,row,index){
                    if (value) {
                        return value.split(',').join('，')
                    }
                }},
                {field: 'carTypes', title: '车型',formatter:function(value,row,index){
                        if (value) {
                            return value.split(',').join('，')
                        }
                    }},
                {field: 'carrName', title: '承运商名称'},
                {field: 'carrType', title: '承运商类型',formatter:function(value,row,index){
                        return $.table.selectEnumContext(carrType, value);
                    }},
                {field: 'contact', title: '联系方式',formatter:function(value,row,index){
                    return value + " - " + row.phone
                }},
                {field: 'balaType', title: '结算方式',formatter: function(value, row, index) {
                        return $.table.selectEnumContext(balaType, value);
                    }},
                {field: 'oilCardRate', title: '付款方式',formatter: function (value, row, index){
                    let text = []
                    if (value) {
                        text.push(value + "%油卡");
                    } else {
                        text.push("0%油卡");
                    }
                    text.push(row.billingTypeName);
                    return text.join("，")
                    }},
                {field: 'transportType', title: '运输类型',formatter:function(value,row,index){
                    return $.table.selectDictLabel(transportType, row.transportType)
                    }},
                {field: 'times', title: '承运次数'}
            ]
        };
        $.table.init(options);
    }

    function searchx() {
        var data = {};
        $.table.search('role-form', data);
    }

    function resetx() {
        $("#role-form")[0].reset();
        $('#city1').citypicker('reset');
        $('#city2').citypicker('reset');
        $('.selectpicker').selectpicker('refresh');
        searchx();
    }

    function exportExcel() {
        $.modal.confirm("确定导出数据吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            var data = $("#role-form").serializeArray();
            $.post(prefix + "/export", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }
</script>
