<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('业务中心-第三方调整单费用')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">客户简称：</label>-->
                            <div class="col-sm-12">
                                <input name="custAbbr" placeholder="请输入客户简称" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">发货单号：</label>-->
                            <div class="col-sm-12">
                                <input name="invoiceVbillno" placeholder="请输入发货单号" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-2">要求提货日：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="startReqDeliDate" placeholder="要求提货开始日">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endtDate"  name="endReqDeliDate" placeholder="要求提货结束日">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-2">调整日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="adjustStartDate"  name="adjustStartDate" placeholder="调整日期开始">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="adjustEndDate"  name="adjustEndDate" placeholder="调整日期结束">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">调整人：</label>-->
                            <div class="col-sm-12">
                                <input name="regUserName" placeholder="请输入调整人" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">

        </div>
        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "business/custOtherFeeAdjust";
    //权限
    var editFlag = [[${@permission.hasPermi('tms:invoice:fee_apply:edit')}]];
    //付款方式
    var payMethod = [[${@dict.getType('pay_method')}]];
    //费用类型
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    //结算公司
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    //第三方费用 状态
    var otherFeeStatusList = [[${otherFeeStatusList}]];
    //第三方费用状态 map
    var otherFeeStatusMap = [[${otherFeeStatusMap}]];
    //第三方费用 新建状态
    var otherFeeStatusNew = [[${otherFeeStatusNew}]];


    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
        });

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#adjustStartDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#adjustEndDate',
                type: 'date',
                trigger: 'click'
            });
        });

        $('#vbillstatus').val(0)
        var options = {
            url: prefix + "/list",
            removeUrl: prefix + "/delete",
            showToggle:false,
            showRefresh: true,
            showColumns:false,
            clickToSelect:true,
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showSearch: true,
            pagination: true,
            showColumns:true,
            fixedColumns: true,
            fixedNumber: 5,
            modalName: "第三方费用调整单",
            uniqueId: "otherFeeId",
            columns: [{
                checkbox: true
            },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceVbillno'
                },
                {
                    title: '客户简称',
                    align: 'left',
                    field: 'custAbbr'
                },
                {
                    title: '要求提货日',
                    align: 'left',
                    field: 'reqDeliDate'
                },
                {
                    title: '提货|到货省市区',
                    field: 'arriDetailAddress',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.deliDetailAddress+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriDetailAddress;
                    }
                },
                {
                    title: '运营公司',
                    align: 'left',
                    field: 'balaCorpId',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(balaCorp, value.balaCorpId);
                    }
                },
                {
                    title: '调整前|调整后装卸费(现金)',
                    align: 'right',
                    field: 'loadingFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if(value < row.adjustLoadingFee){
                            return value+'|<label style="color:#fc2727">'+row.adjustLoadingFee+'</label>';
                        }else if(value > row.adjustLoadingFee){
                            return value+'|<label style="color:#068e48">'+row.adjustLoadingFee+'</label>';
                        }else{
                            return value+'|'+row.adjustLoadingFee;
                        }
                    }
                },
                {
                    title: '调整前|调整后仓储费(现金)',
                    align: 'right',
                    field: 'storageFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if(value < row.adjustStorageFee){
                            return value+'|<label style="color:#fc2727">'+row.adjustStorageFee+'</label>';
                        }else if(value > row.adjustStorageFee){
                            return value+'|<label style="color:#068e48">'+row.adjustStorageFee+'</label>';
                        }else{
                            return value+'|'+row.adjustStorageFee;
                        }
                    }
                },
                {
                    title: '调整前|调整后其他(现金)',
                    align: 'right',
                    field: 'otherFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if(value < row.adjustOtherFee){
                            return value+'|<label style="color:#fc2727">'+row.adjustOtherFee+'</label>';
                        }else if(value > row.adjustOtherFee){
                            return value+'|<label style="color:#068e48">'+row.adjustOtherFee+'</label>';
                        }else{
                            return value+'|'+row.adjustOtherFee;
                        }
                    }
                },
                {
                    title: '调整前|调整后装卸费(油卡)',
                    align: 'right',
                    field: 'oilLoadingFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if(value < row.adjustOilLoadingFee){
                            return value+'|<label style="color:#fc2727">'+row.adjustOilLoadingFee+'</label>';
                        }else if(value > row.adjustOilLoadingFee){
                            return value+'|<label style="color:#068e48">'+row.adjustOilLoadingFee+'</label>';
                        }else{
                            return value+'|'+row.adjustOilLoadingFee;
                        }
                    }
                },
                {
                    title: '调整前|调整后仓储费(油卡)',
                    align: 'right',
                    field: 'oilStorageFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if(value < row.adjustOilStorageFee){
                            return value+'|<label style="color:#fc2727">'+row.adjustOilStorageFee+'</label>';
                        }else if(value > row.adjustOilStorageFee){
                            return value+'|<label style="color:#068e48">'+row.adjustOilStorageFee+'</label>';
                        }else{
                            return value+'|'+row.adjustOilStorageFee;
                        }
                    }
                },
                {
                    title: '调整前|调整后其他(油卡)',
                    align: 'right',
                    field: 'oilOtherFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if(value < row.adjustOilOtherFee){
                            return value+'|<label style="color:#fc2727">'+row.adjustOilOtherFee+'</label>';
                        }else if(value > row.adjustOilOtherFee){
                            return value+'|<label style="color:#068e48">'+row.adjustOilOtherFee+'</label>';
                        }else{
                            return value+'|'+row.adjustOilOtherFee;
                        }
                    }
                },
               /* {
                    title: '调整后装卸费(现金)',
                    align: 'right',
                    field: 'adjustLoadingFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '调整后仓储费(现金)',
                    align: 'right',
                    field: 'adjustStorageFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '调整后其他(现金)',
                    align: 'right',
                    field: 'adjustOtherFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '调整后装卸费(油卡)',
                    align: 'right',
                    field: 'adjustOilLoadingFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '调整后仓储费(油卡)',
                    align: 'right',
                    field: 'adjustOilStorageFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '调整后其他(油卡)',
                    align: 'right',
                    field: 'adjustOilOtherFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },*/
                {
                    title:'调整人',
                    align:'left',
                    field:'regUserName'
                },
                {
                    title:'调整时间',
                    align:'left',
                    field:'regDate'
                }

            ]
        };

        $.table.init(options);
    });


</script>
</body>
</html>