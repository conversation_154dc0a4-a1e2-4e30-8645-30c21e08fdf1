<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    body {
        font-size: 12px;
    }
    .panel-heading {
        padding: 4px 10px;
    }
    .table-striped{
        height: calc(100% - 80px);
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .fw{
        font-weight: bold;
    }
    .fc1a{
        color: #1a1a1a;
    }
    .fc80{
        color: #808080;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .table th, .table td {
        text-align: center;
    }
    .input-group {
        width: 100%;
        text-align: center;
    }
    .round{
        width: 50px;
        height: 50px;
        width: 30px;
        height: 30px;
        /*text-align: center;*/
        /*border-radius: 50%;*/
        /*line-height: 50px;*/
        box-sizing: border-box;
    }
    .line{
        width: 1px;
        min-height: 10px;
        min-height: 5px;
        max-height: 60px;
        border-left: 2px dashed #cdcdcd;
        margin-left: 25px;
        margin-left: 15px;
    }
    .fc00a{
        color: #00A9FF;
    }
    .fcff8{
        color: #FF8900;
    }
    .th{
        /*border: 6px solid #00A9FF;*/
        /*color: #00A9FF;*/
    }
    .dh{
        /*border: 6px solid #FF8900;*/
        /*color: #FF8900;*/
    }
    .padt20{
        padding: 20px 0;
    }
    .padt5{
        padding: 5px 0;
        padding: 0px 0 5px;
    }
    .printitle{
        font-size: 16px;
        line-height: 30px;
        text-align: center;
    }
    .printtable .table tbody tr td{
        font-size: 12px;
    }

    .table>thead>tr>td, .table>tbody>tr>td{
        padding: 2px;
    }
</style>
<body class="">
<div class="form-content">
    <a class="btn btn-primary" onclick="dayin()">
        <i class="fa fa-print"></i> 打印
    </a>
    <div >
        <div class="mt0">
            <div class="mt10" id="div_print">
                <div class="printitle fw">第三方单笔费用申请</div>
                <div class="over">
                    <div class="fl">客户编码：<span class="fw">[[${invoice.custCode}]]</span></div>
                    <div class="fl ml20">客户全称：<span class="fw">[[${invoice.custName}]]</span></div>
                    <div class="fl ml20">运营组：<span class="fw">[[${sysDept.deptName}]]</span></div>
                </div>
                <div class="mt10">
                    <div class="over">
                        <div class="fl printtable" style="width: 300px">
                            <table class="table table-bordered">
                                <thead style="background: #F7F8FA">
                                <tr>
                                    <th style="width: 120px">申请单号</th>
                                    <th style="width: 100px">申请时间</th>
                                    <th style="width: 80px">费用类型</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>
                                        <div class="input-group">[[${otherFee.vbillno}]]</div>
                                    </td>
                                    <td >
                                        <div class="input-group">[[${#dates.format(otherFee.applyDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                    </td>
                                    <td>
                                        <span th:each="dict : ${@dict.getType('cost_type_on_way')}" th:if="${dict.dictValue==otherFee.feeType}" th:text="${dict.dictLabel}">

                                    </span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="fl ml10 printtable" th:if="${otherFee.payType == 0}" style="width: calc(100% - 320px)">
                            <table class="table table-bordered">
                                <thead style="background: #F7F8FA">
                                <tr>
                                    <th style="width: 20%">金额(元)</th>
                                    <th style="width: 20%">收款账户</th>
                                    <th style="width: 30%">收款卡号</th>
                                    <th style="width: 30%">收款银行</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>
                                        <div class="input-group">[[${otherFee.feeAmount}]]</div>
                                    </td>
                                    <td >
                                        <div class="input-group">[[${otherFee.recAccount}]]</div>
                                    </td>
                                    <td>
                                        <div class="input-group">[[${otherFee.recCardNo}]]</div>
                                    </td>
                                    <td>
                                        <div class="input-group">[[${otherFee.recBank}]]</div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="fl ml10 printtable" th:if="${otherFee.payType == 1}" style="width: 120px">
                            <table class="table table-bordered">
                                <thead style="background: #F7F8FA">
                                <tr>
                                    <th>油卡卡号</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>
                                        <div class="input-group">[[${otherFee.recCardNo}]]</div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row" style="margin: 0 -10px 0 -10px;">
                    <div class="col-sm-5">
                        <div  class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <a data-toggle="collapse" data-parent="#accordion"
                                    >审核记录</a>
                                </h4>
                            </div>
                            <div  class="panel-collapse collapse in" id="collapseTwo">
                                <div class="panel-body">
                                    <div class="padt5">
                                        <div class="over">
                                            <div class="fl">
                                                <div class="round th">
                                                    <img th:src="@{/img/pep.png}" style="width: 50px;height: 50px">
                                                    <img th:src="@{/img/pep.png}" style="width: 30px;height: 30px">
                                                </div>
                                                <div class="line"></div>
                                            </div>
                                            <div class="fr over ml20" style="width: calc(100% - 70px)">
                                                <div class="fl" style="width: 140px">
                                                    <div class="">
                                                        <div style="display: inline-block" class="fc00a">[[${applyUserName}]] </div>
                                                        <div style="display: inline-block" class="fc00a" th:switch="${otherFee.payType}">
                                                            <span th:case="0">现金申请：</span>
                                                            <span th:case="1">油卡申请：</span>
                                                        </div>
                                                    </div>
                                                    <div class="mt10 fc80">[[${#dates.format(otherFee.applyDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                    <div class="fc80">[[${#dates.format(otherFee.applyDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                </div>
                                                <div class="fl ml20 selector" style="width: calc(100% - 160px)">
                                                    <div style="border: 1px #eee solid;padding: 10px 10px;box-sizing: border-box;width: 100%">[[${otherFee.memo}]]</div>
                                                    <div style="border: 1px #eee solid;padding: 5px 10px;box-sizing: border-box;width: 100%">[[${otherFee.memo}]]</div>
                                                    <!--                                            <textarea name="note" class=" form-control" maxlength="100" rows="3" readonly>[[${otherFee.memo}]]</textarea>-->
                                                </div>
                                            </div>
                                        </div>
                                        <div class="" th:each="businessCheck : ${businessChecks}">
                                            <!--                                    <div class="line"></div>-->
                                            <div class="over">
                                                <div class="fl round dh">
                                                    <img th:src="@{/img/pep.png}" style="width: 50px;height: 50px">
                                                    <img th:src="@{/img/pep.png}" style="width: 30px;height: 30px">
                                                </div>
                                                <div class="fr over ml20" style="width: calc(100% - 70px)">
                                                    <div class="fl" style="width: 140px">
                                                        <div class="">
                                                            <div style="display: inline-block" class="fcff8 ">[[${businessCheck.checkMan}]] </div>
                                                            <div style="display: inline-block" class="fcff8 " th:switch="${businessCheck.checkStatus}">
                                                                <span th:case="0">待审核：</span>
                                                                <span th:case="1">审核通过：</span>
                                                                <span th:case="2">未通过：</span>
                                                                <span th:case="3">财务未通过：</span>
                                                            </div>
                                                        </div>

                                                        <div class="mt10 fc80">[[${#dates.format(businessCheck.checkDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                        <div class="fc80">[[${#dates.format(businessCheck.checkDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                    </div>
                                                    <div class="fl ml20" style="width: calc(100% - 160px)">
                                                        <div style="border: 1px #eee solid;padding: 10px 10px;box-sizing: border-box;width: 100%">[[${businessCheck.memo}]]</div>
                                                        <div style="border: 1px #eee solid;padding: 5px 10px;box-sizing: border-box;width: 100%">[[${businessCheck.memo}]]</div>
                                                        <!--                                                <textarea name="note" class=" form-control" maxlength="100" rows="3" readonly>[[${businessCheck.memo}]]</textarea>-->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-7">
                        <div  class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <a data-toggle="collapse" data-parent="#accordion"
                                    >单据明细</a>
                                </h4>
                            </div>
                            <div  class="panel-collapse collapse in" id="collapseOne">
                                <div class="panel-body">
                                    <div class="padt5">
                                        <!--startprint-->
                                        <table class="table table-bordered">
                                            <thead style="background: #F7F8FA">
                                            <tr>
                                                <th>发货单号</th>
                                                <th>要求提货日期</th>
                                                <th>提货/到货省市区</th>
                                                <th>结算公司</th>
                                                <th>费用类型</th>
                                                <th>金额</th>
                                                <th>创建时间</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td>
                                                    <div class="input-group">[[${invoice.vbillno}]]</div>
                                                </td>
                                                <td>
                                                    <div class="input-group">[[${#dates.format(invoice.reqDeliDate, 'yyyy-MM-dd')}]]</div>
                                                </td>
                                                <td >
                                                    <div class="input-group">[[${invoice.deliProName}]][[${invoice.deliCityName}]][[${invoice.deliAreaName}]]-[[${invoice.arriProName}]][[${invoice.arriCityName}]][[${invoice.arriAreaName}]]</div>
                                                </td>
                                                <td>
                                                    <div class="input-group" th:switch="${invoice.balaCorpId}">
                                                        <span th:case="JH">吉华</span>
                                                        <span th:case="MY">铭源</span>
                                                        <span th:case="MD">明达</span>
                                                        <span th:case="*">-</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="input-group" th:switch="${otherFee.feeType}">
                                                        <span th:case="1">装卸费</span>
                                                        <span th:case="2">提货费</span>
                                                        <span th:case="3">放空费</span>
                                                        <span th:case="4">停车费</span>
                                                        <span th:case="5">进门费</span>
                                                        <span th:case="6">信息费</span>
                                                        <span th:case="7">过磅费</span>
                                                        <span th:case="8">过路费</span>
                                                        <span th:case="9">仓储费</span>
                                                        <span th:case="12">改送费</span>
                                                        <span th:case="10">其他</span>
                                                        <span th:case="13">误工费</span>
                                                        <span th:case="14">客户赔偿</span>
                                                        <span th:case="15">进仓费</span>
                                                        <span th:case="16">中转费</span>
                                                        <span th:case="17">回单扣款</span>
                                                        <span th:case="18">异常扣款</span>
                                                        <span th:case="19">货损扣款</span>
                                                        <span th:case="20">合同约定调整</span>
                                                        <span th:case="21">压车费</span>
                                                        <span th:case="22">送货费</span>
                                                        <span th:case="*">-</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="input-group">[[${otherFee.feeAmount}]]</div>
                                                </td>
                                                <td>
                                                    <div class="input-group">[[${#dates.format(invoice.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                                                </td>

                                            </tr>
                                            </tbody>
                                        </table>
                                        <!--endprint-->
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="over mt10">
                    <div class="fl">签字：</div>
                    <div class="fr" style="width:150px">日期：</div>
                    <div class="fr" id="time" style="width:150px"></div>
                    <div class="fr" style="width:150px">签字：</div>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var height = $('.selector').height()
    console.log(height)
    var height1 = height - 60
    if(height>40){
        $(".line").height(height);
    }
    var myDate = new Date;
    var year = myDate.getFullYear(); //获取当前年
    var mon = myDate.getMonth() + 1; //获取当前月
    var date = myDate.getDate(); //获取当前日
    var week = myDate.getDay();
    console.log(year, mon, date)
    $("#time").html("日期：" + year + "年" + mon + "月" + date + "日");
    function dayin(){
        printdiv(div_print);
    }

    function printdiv(printpage)
    {
        var newstr = printpage.innerHTML;
        var oldstr = document.body.innerHTML;
        document.body.innerHTML =newstr;
        window.print();
        document.body.innerHTML=oldstr;
        return false;
    }
</script>
</body>
</html>
