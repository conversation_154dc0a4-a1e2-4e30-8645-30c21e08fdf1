<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('票税调整')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: jasny-bootstrap-css" />
</head>
<style>
    .fw{
        font-weight: bold;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 26px;
        text-align: right;
        color: #808080;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 26px;
    }
    .fcff3{
        color: #ff3636;
    }
    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .addbtn{
        /*width: 100px;*/
        text-align: center;
        color: #fff;
        background: #1ab394;
        line-height: 30px;
        border-radius: 5px;
        cursor: pointer;
    }
    .mt10{
        margin-top: 10px;
    }
    label.error {
        top: 25px !important;
        left: 430px;
    }
    /*父页面宽度不达768px，样式不生效修复*/
    .col-sm-4 {
        position: relative;
        width: 33.3333%;
        float: left;
    }
    .col-sm-3 {
        position: relative;
        width: 25%;
        float: left;
    }
    .col-sm-7 {
        position: relative;
        width: 58.33333333%;
        float: left;
    }
    .col-sm-1 {
        position: relative;
        width: 8.33333333%;
        float: left;
    }
    .imageBox2 {
        border: 2px transparent solid;
    }
    .imageBox2:hover {
        border-color: #dddddd;
    }
</style>
<body>
<div class="form-content">
    <form id="form-payDetail-add" class="form-horizontal" novalidate="novalidate">
        <!--应付明细id-->
        <input type="hidden" name="otherFeeId" th:value="${otherFee.otherFeeId}">
        <input type="hidden" name="invoiceId" th:value="${otherFee.lotId}">
        <div class="panel-body">
            <div class="mt10">
                <div class="flex">
                    <label class="flex_left">含税构成：</label>
                    <div class="flex_right">
                        <span id="taxTxtView"></span>
                    </div>
                </div>
            </div>
            <div class="mt10">
                <div class="flex">
                    <label class="flex_left">已上传发票：</label>
                    <div class="flex_right">
                        <span th:each="item: ${receiptList}" class="imageBox2" style="display: inline-block;margin:0 5px 5px 0;" deleter>
                            <input type="checkbox" del-btn th:file-id="${item.fileId}" th:tid="${item.tid}" th:if="${receiptCheck?.status != 1}">
                            <img style="height:50px;margin:0 5px 0 0;" modal="zoomImg" th:src="${item.filePath}"/>
                        </span>
                        <div style="float:right;text-align: right"  th:if="${receiptCheck?.status != 1}">
                            <a href="javascript:;" del-btn style="font-weight: bold" onclick="delImg()">删除勾选</a>
                            <br>
                            <span id="delImgInfo" style="color:rgb(64,158,255)"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt10" th:if="${receiptCheck?.status != 1}">
                <div class="flex">
                    <label class="flex_left">上传发票：</label>
                    <div class="flex_right">
                        <input id="receipt" class="form-control" name="receipt" type="file" multiple>
                        <input id="tid_receipt" name="tidReceipt" type="hidden">
                    </div>
                </div>
            </div>
            <div class="mt10" th:unless="${receiptCheck?.status != 1}">
                <div class="flex">
                    <label class="flex_left"></label>
                    <div class="flex_right" style="color:lightcoral;font-weight: bold">发票已确认！</div>
                </div>
            </div>
        </div>
        <input type="hidden" name="taxTxt">
        <input type="hidden" name="deletedImageJson">
    </form>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">
    var prefix = ctx + "business/custOtherFee";
    var receiptCheck = [[${receiptCheck?.status}]];

    $(function () {
        $.file.initAddFiles("receipt", "tid_receipt", {
            maxFileCount:0,
            fileType: null
        });
        $("#receipt").on('filebatchuploadsuccess', function (event, data) {
            console.log(event, data)
            //var tid = data.response.tid;
            //$("#tid_receipt").val(tid);
            //表单提交
            $.operate.save(prefix + "/saveTaxReceipt", $('#form-payDetail-add').serialize());
        });
        $('#receipt').on('filesuccessremove', function(event, key, index) {
            $('#receipt').fileinput('clear');
            $('#tid_receipt').val('');
        });


        translateTaxTxt()
    });

    /**
     * 表单提交
     */
    function submitHandler(index){
        if (receiptCheck == 1) {
            parent.layer.close(index)
        } else {
            if ($('[err-tax]').length > 0) {
                $.modal.msgError("请调整含税构成");
                return
            }
            $("[name=taxTxt]").val(taxTxt);
            if ($.validate.form()) {
                $.modal.confirm("确认提交吗？", function () {
                    commoit();
                })
            }
        }
    }

    function commoit() {
        $("[name='deletedImageJson']").val(JSON.stringify(deletedImage))
        console.log('receipt', $('#receipt').fileinput('getFilesCount'))
        //解除disabled
        var dis = $(":disabled");
        dis.attr("disabled", false);
        if($('#receipt').fileinput('getFilesCount') == 0){
            $.operate.save(prefix + "/saveTaxReceipt", $('#form-payDetail-add').serialize());
        } else if ($('#receipt').fileinput('getFilesCount') > 0){
            $.modal.loading("正在处理中，请稍后...");
            $("#receipt").fileinput('upload');
        }
    }

    var billingType = [[${@dict.getType('billing_type')}]];//开票类型
    var taxTxt = [[${taxTxt}]];

    function translateTaxTxt() {
        if (!taxTxt) { return; }
        var array = taxTxt.split(",");
        var temp = [];
        var sum = new BigNumber("0");
        for (let j = 0; j < array.length; j++) {
            var t = array[j].split(":");
            for (let i = 0; i < billingType.length; i++) {
                if (billingType[i].dictValue == t[0]) {
                    temp.push("[", billingType[i].dictLabel, ":", t[1], "]")
                    sum = sum.plus(new BigNumber(t[1]))
                    break;
                }
            }
        }
        if (receiptCheck != 1) {
            temp.push(' <a href="javascript:adjustTax()">调</a>')
        }
        if (!sum.eq(new BigNumber("[(${otherFee.feeAmount})]"))) {
            temp.splice(0,0,"<span style='color:red' err-tax data-toggle='tooltip' title='合计不等于当前费用, 请手动调整' data-trigger='manual'>")
            temp.push("</span>")
        }
        $("#taxTxtView").html(temp.join(""))
        $('[err-tax]').tooltip('show')
    }

    function adjustTax() {
        var amount = "[(${otherFee.feeAmount})]";
        var tmp = [`<div style="padding: 8px 20px;">
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-sm-4 control-label" style="padding-top: 5px;text-align: right">当前费用：</label>
							<div class="col-sm-3">
							  	<p class="form-control-static">${amount}</p>
							</div>
							<div class="col-sm-4">
							  	<p class="form-control-static" id="result" style="color:#ff0000"></p>
							</div>
							<div class="col-sm-1">
							    <i class="fa fa-plus-circle" style="height: 34px;vertical-align: middle;padding: 7px 0;color:#007dcc" onclick="addTaxRow(this)"></i>
							</div>
						</div>`]
        var arr = taxTxt.split(",");
        for (let i = 0; i < arr.length; i++) {
            var tt = arr[i].split(":");
            tmp.push('<div class="form-group">')
            tmp.push('<label class="col-sm-7">')
            tmp.push('<select billingtype class="form-control">');
            for (let j = 0; j < billingType.length; j++) {
                tmp.push("<option value='",billingType[j].dictValue,"'",tt[0]==billingType[j].dictValue?' selected':'',">",billingType[j].dictLabel,"</option>");
            }
            tmp.push("</select>");
            tmp.push('</label>')
            tmp.push('<div class="col-sm-4">')
            tmp.push('<input class="form-control" tax oninput="$.numberUtil.onlyNumberTwoDecimal(this);check()" value="',tt[1],'" autocomplete="off">')
            tmp.push('</div>')
            tmp.push('<div class="col-sm-1">')
            tmp.push('<i class="fa fa-minus-circle" style="height: 34px;vertical-align: middle;padding: 7px 0;color:#ff0000" onclick="$(this).closest(\'.form-group\').remove();check()"></i>')
            tmp.push('</div>')
            tmp.push('</div>')
        }
        tmp.push('</form>')
        tmp.push('* 所有含税额合计必须<span style="color:blue;font-weight: bold;">等于当前费用</span>')
        tmp.push('</div>')
        layer.open({
            type: 1,
            area: ['450px', '403px'],
            fix: false,
            //不固定
            maxmin: false,
            shade: 0.3,
            shadeClose: false,
            title: '费用含税额拆分',
            content: tmp.join(""),
            btn: ['<i class="fa fa-check"></i> 保存', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(idx, layero){
                var chk = check();
                var objTemp = {};//校验开票类型时候重复的临时对象
                var tmp = []
                if (chk) {
                    var amount_origin = new BigNumber(amount)
                    var flag = true;
                    $("[billingtype]").each(function(){
                        if (objTemp[$(this).val()] != null) {
                            $.modal.msgError("相同开票类型的请合并");
                            flag = false;
                            return;
                        } else {
                            objTemp[$(this).val()] = 1;
                            var n = new BigNumber($(this).closest('.form-group').find('[tax]').val()).toNumber();
                            if (n === 0 && amount_origin.toNumber() != 0) {
                                $.modal.msgError("请输入非0的数字");
                                flag = false;
                                return;
                            } else if (n < 0 && amount_origin.toNumber() > 0) {
                                $.modal.msgError("当前费用大于0，各税额必须都大于0");
                                flag = false;
                                return;
                            } else if (n > 0 && amount_origin.toNumber() < 0) {
                                $.modal.msgError("当前费用小于0，各税额必须都小于0");
                                flag = false;
                                return;
                            }
                            tmp.push($(this).val()+":"+$(this).closest('.form-group').find('[tax]').val())
                        }
                    })
                    if (flag) {
                        taxTxt = tmp.join(",")
                        translateTaxTxt()
                        layer.close(idx)
                    }
                } else {
                    $.modal.msgError("所有含税额合计必须等于当前费用")
                }
            }
        })
        check()
    }

    function addTaxRow(i){
        var tmp = [];
        tmp.push('<div class="form-group">')
        tmp.push('<label class="col-sm-7">')
        tmp.push('<select billingtype class="form-control">');
        for (let j = 0; j < billingType.length; j++) {
            tmp.push("<option value='",billingType[j].dictValue,"'>",billingType[j].dictLabel,"</option>");
        }
        tmp.push("</select>");
        tmp.push('</label>')
        tmp.push('<div class="col-sm-4">')
        tmp.push('<input class="form-control" tax oninput="$.numberUtil.onlyNumberTwoDecimal(this);check()" autocomplete="off">')
        tmp.push('</div>')
        tmp.push('<div class="col-sm-1">')
        tmp.push('<i class="fa fa-minus-circle" style="height: 34px;vertical-align: middle;padding: 7px 0;color:#ff0000" onclick="$(this).closest(\'.form-group\').remove();check()"></i>')
        tmp.push('</div>')
        tmp.push('</div>')
        $(i).closest("form").append(tmp.join(''))
    }
    function check() {
        var amount_origin = new BigNumber("[(${otherFee.feeAmount})]");
        var sum = calc1();
        if (sum.eq(amount_origin)) {
            $("#result").text("")
            return true;
        } else if (sum.gt(amount_origin)) {
            $("#result").text("超" + sum.minus(amount_origin))
            return false;
        } else if (sum.lt(amount_origin)) {
            $("#result").text("低" + amount_origin.minus(sum))
            return false;
        }
    }
    function calc1() {
        var sum = new BigNumber("0")
        $("[tax]").each(function(){
            var amount = new BigNumber(this.value.trim());
            if (!amount.isNaN()) {
                sum = sum.plus(amount);
            }
        })
        return sum;
    }

    var deletedImage = []

    function delImg() {
        if ($(":checked[del-btn]").length == 0) {
            $.modal.msgWarning("尚未选择要删除的发票")
        } else {
            $.modal.confirm("确认删除吗？", function(){
                $(":checked[del-btn]").each(function (i) {
                    deletedImage.push({fileId:$(this).attr("file-id"), tid:$(this).attr("tid")})
                    $(this).parent().remove()
                })
                $("#delImgInfo").text("已删除" + deletedImage.length + "张图片，提交后生效")

            })
        }
    }
</script>
</body>

</html>