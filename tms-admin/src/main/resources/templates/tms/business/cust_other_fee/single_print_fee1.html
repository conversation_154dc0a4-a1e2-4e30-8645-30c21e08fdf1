<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    body {
        /*font-size: 12px;*/
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 26px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 26px;
    }
    .panel-heading {
        padding: 4px 10px;
    }
    .table-striped{
        height: calc(100% - 80px);
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .ml30{
        margin-left: 30px;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .fw{
        font-weight: bold;
    }
    .fc1a{
        color: #1a1a1a;
    }
    .fc80{
        color: #808080;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .table th, .table td {
        text-align: center;
    }
    .input-group {
        width: 100%;
        text-align: center;
    }
    .round{
        width: 30px;
        height: 30px;
        /*text-align: center;*/
        /*border-radius: 50%;*/
        /*line-height: 50px;*/
        box-sizing: border-box;
    }
    .line{
        width: 1px;
        min-height: 20px;
        max-height: 60px;
        border-left: 2px dashed #cdcdcd;
        margin-left: 15px;
    }
    .fc00a{
        color: #00A9FF;
    }
    .fcff8{
        color: #FF8900;
    }
    .th{
        /*border: 6px solid #00A9FF;*/
        /*color: #00A9FF;*/
    }
    .dh{
        /*border: 6px solid #FF8900;*/
        /*color: #FF8900;*/
    }
    .padt20{
        padding: 20px 0;
    }
    .padt5{
        padding: 0px 0 5px;
    }
    .printitle{
        font-size: 16px;
        line-height: 30px;
        text-align: center;
    }
    .table>thead>tr>th {
        font-weight: normal;
        color: #808080;
    }
    .printtable .table tbody tr td{
        /*font-size: 12px;*/
    }
    .table>thead>tr>td, .table>tbody>tr>td{
        padding: 2px;
    }
    .wid30{
        width: 33%;
    }
    .flex{
        display: flex;
        justify-content: flex-start;
    }
</style>
<body class="">
<div class="form-content">
    <a class="btn btn-primary" onclick="dayin()">
        <i class="fa fa-print"></i> 打印
    </a>
    <div >
        <div class="">
            <div class="" id="div_print">
                <div class="printitle fw">第三方单笔费用申请</div>
                <div class="over flex">
                    <div class="fl">结算公司：<span class="fw" th:text="${balaCorp}"></span></div>
                    <div class="fl ml20">客户全称：<span class="fw">[[${invoice.custName}]]</span></div>
                    <div class="fl ml20">运营组：<span class="fw">[[${sysDept.deptName}]]</span></div>
                </div>
                <div class="over mt10">
                    <table class="table table-bordered">
                        <thead style="background: #F7F8FA">
                            <tr>
                                <th style="width: 6%;">类型</th>
                                <th style="width: 14%;">费用类型</th>
                                <th style="width: 6%;">金额(元)</th>
                                <th style="width: 24%;">收款信息</th>
                                <th style="width: 20%;">申请信息</th>
                                <th style="width: 30%;">申请原因</th>
                            </tr>
                            </thead>
                        <tbody>
                        <tr th:if="${otherFee.payType == 0}">
                            <td>现金</td>
                            <td>
                                <span class="fw">
                                    <span th:each="dict : ${@dict.getType('cost_type_on_way')}" th:if="${dict.dictValue==otherFee.feeType}" th:text="${dict.dictLabel}">

                                    </span>
                                </span>
                            </td>
                            <td>[[${otherFee.feeAmount}]]</td>
                            <td>[[${otherFee.recAccount}]]([[${otherFee.recCardNo}]])</td>
                            <td>[[${applyUserName}]]（[[${#dates.format(otherFee.applyDate, 'yyyy-MM-dd HH:mm:ss')}]]）</td>
                            <td>[[${otherFee.memo}]]</td>
                        </tr>
                        <tr th:if="${otherFee.payType == 1}">
                            <td>油卡</td>
                            <td>
                                <span class="fw">
                                     <span th:each="dict : ${@dict.getType('cost_type_on_way')}" th:if="${dict.dictValue==otherFee.feeType}" th:text="${dict.dictLabel}">

                                    </span>
                                </span>
                            </td>
                            <td></td>
                            <td>油卡号：[[${otherFee.recCardNo}]]</td>
                            <td>[[${applyUserName}]]（[[${#dates.format(otherFee.applyDate, 'yyyy-MM-dd HH:mm:ss')}]]）</td>
                            <td>[[${otherFee.memo}]]</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="panel panel-default mt10">
                    <div class="panel-heading">
                        <div class="over">
                            <div class="fl" >
                                <h5 class="panel-title">
                                    <a data-toggle="collapse" >发货单-[[${invoice.vbillno}]]</a>
                                </h5>
                            </div>
                            <div class="fr over">
                                <div class="fl">
                                    <span>应收：</span><span class="fcff8" th:text="${ys}"></span>
                                </div>
                                <div class="fl ml10">
                                    <span>成本：</span><span class="fcff8" th:text="${yfsf}"></span>
                                </div>
                                <div class="fl ml10">
                                    <span>税金：</span><span class="fcff8" th:text="${yfsfTax}"></span>
                                </div>
                                <div class="fl ml10">
                                    <span>毛利：</span><span class="fcff8" th:text="${lrNet}"></span>
                                </div>
                                <div class="fl ml10">
                                    <span>其它单据关联扣款：</span><span class="fcff8" th:text="${glkk}">0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="fw" style="line-height: 30px">应收明细</div>
                            <table class="table table-bordered">
                                <thead style="background: #F7F8FA">
                                <tr>
                                    <th>要求提货日期</th>
                                    <th>提货/到货省市区</th>
                                    <th>货量</th>
                                    <th>要求车长车型</th>
                                    <!-- <th>费用单号</th> -->
                                    <!-- <th>应收状态</th> -->
                                    <th>费用类型</th>
                                    <th>应收金额</th>

                                </tr>
                                </thead>
                                <tbody id="freightListId">

                                </tbody>
                                <tfoot>
                                <tr style="background: #FFFCD3;text-align: center">
                                    <td>合计：</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <!-- <td></td> -->
                                    <!-- <td></td> -->
                                    <td></td>
                                    <td>[[${receiveTransFee}]]</td>

                                </tr>

                                </tfoot>
                            </table>
                            <div class="row" style="padding: 0 5px;">
                                <div class="col-sm-8">
                                    <div class="fw" style="line-height: 30px">应付明细</div>
                                    <table class="table table-bordered">
                                        <thead style="background: #F7F8FA">
                                        <tr>
                                            <th>运单号</th>
                                            <th>承运商(联系方式)</th>
                                            <th>车牌号</th>
                                            <th>费用明细</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="payDetail : ${payDetailList}">
                                            <td th:text="${payDetail.lot}"></td>
                                            <td>[[${payDetail.carrierName}]]([[${payDetail.phone}]])</td>
                                            <td th:text="${payDetail.carNo}"></td>
                                            <td>
                                                <span th:if="${payDetail.yfShare != 0}">运费[[${payDetail.yfShare}]]/</span>
                                                <span th:if="${payDetail.yfShare != 0}">[[${payDetail.yf == payDetail.yfGot ? "已核销":(payDetail.yfGot == 0 ? "未核销":"部分核销")}]];</span>
                                                <span th:if="${payDetail.kkShare != 0}">扣款[[${payDetail.kkShare}]]/</span>
                                                <span th:if="${payDetail.kkShare != 0}">[[${payDetail.kk == payDetail.kkGot ? "已核销":(payDetail.kkGot == 0 ? "未核销":"部分核销")}]];</span>
                                            </td>
                                        </tr>
                                        </tbody>
                                        <tfoot>
                                        <tr style="background: #FFFCD3;text-align: center">
                                            <td>合计：</td>
                                            <td></td>
                                            <td></td>
                                            <td>[[${payTransFee}]]</td>
                                        </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                <div class="col-sm-4">
                                    <div class="fw" style="line-height: 30px">三方费用</div>
                                    <table class="table table-bordered">
                                        <thead style="background: #F7F8FA">
                                        <tr>
                                            <th>费用单号</th>
                                            <th>件数/体积(m³)/重量(吨)</th>
                                            <th>费用类型</th>
                                            <th>三方费用</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="fees : ${otherFees}">
                                            <td>[[${fees.vbillno}]]</td>
                                            <td>[[${fees.numCount}]]/[[${fees.weightCount}]]/[[${fees.volumeCount}]]</td>
                                            <td th:text="${@dict.getLabel('cost_type_on_way',fees.feeType)}"></td>
                                            <td>[[${fees.feeAmount}]]</td>
                                        </tr>
                                        </tbody>
                                        <tfoot>
                                        <tr style="background: #FFFCD3;text-align: center">
                                            <td>合计：</td>
                                            <td></td>
                                            <td></td>
                                            <td>[[${otherFeeAmount}]]</td>
                                        </tr>

                                        </tfoot>
                                    </table>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <div id="spRecord" class="panel panel-default mt10" style="display:none">
                    <div class="panel-heading"><h4>审批流程</h4></div>
                    <div class="panel-body">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th>审批人</th>
                                <th>操作记录</th>
                                <th>审批备注</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td colspan="3">加载中，请稍候...</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="over">
                    <div class="fr" id="time" style="width:150px"></div>
                    <div class="fr" style="width:150px">签字：</div>
                </div>
            </div>
        </div>
    </div>
</div>
<div style="display: none">
    <span case="1">装卸费</span>
    <span case="2">提货费</span>
    <span case="3">放空费</span>
    <span case="4">停车费</span>
    <span case="5">进门费</span>
    <span case="6">信息费</span>
    <span case="7">过磅费</span>
    <span case="8">过路费</span>
    <span case="9">仓储费</span>
    <span case="12">改送费</span>
    <span case="10">其他</span>
    <span case="13">误工费</span>
    <span case="14">客户赔偿</span>
    <span case="15">进仓费</span>
    <span case="16">中转费</span>
    <span case="17">回单扣款</span>
    <span case="18">异常扣款</span>
    <span case="19">货损扣款</span>
    <span case="20">合同约定调整</span>
    <span case="21">压车费</span>
    <span case="22">送货费</span>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">
    var height = $('.selector').height()

    var height1 = height - 60
    if(height>40){
        $(".line").height(height);
    }
    var myDate = new Date;
    var year = myDate.getFullYear(); //获取当前年
    var mon = myDate.getMonth() + 1; //获取当前月
    var date = myDate.getDate(); //获取当前日
    var week = myDate.getDay();

    $("#time").html("日期：" + year + "年" + mon + "月" + date + "日");
    function dayin(){
        $(".table1 tbody").css("max-height","397px");
        $(".table2 tbody").css("max-height","397px");
        $(".gd").html("查看更多")
        printdiv(div_print);
    }

    function printdiv(printpage)
    {
        var newstr = printpage.innerHTML;
        var oldstr = document.body.innerHTML;
        document.body.innerHTML =newstr;
        window.print();
        document.body.innerHTML=oldstr;
        return false;
    }

    $(function () {
        var spNo = [[${otherFee.spNo}]];
        if (spNo) {
            $("#spRecord").show();
            showWecomProcess(spNo, function(list){
                let html = [];
                let userids = []
                for (let i = 0; i < list.length; i++) {
                    if (list[i].sp_status == 2 || list[i].sp_status == 3) {
                        for (let j = 0; j < list[i].details.length; j++) {
                            let detailStatus = list[i].details[j].sp_status
                            if (detailStatus == 2 || detailStatus == 3) {
                                let userid = list[i].details[j].approver.userid;
                                userids.push(userid);
                                let time = list[i].details[j].sptime ? new Date(list[i].details[j].sptime * 1000).pattern("yyyy-MM-dd HH:mm") : '';
                                html.push("<tr><td userid='",userid,"'>",userid,"</td><td>",detailStatus==2?'同意':'驳回'," ",time,"</td><td>",list[i].details[j].speech,"</td></tr>");
                            }
                        }
                    } else {
                        for (let j = 0; j < list[i].details.length; j++) {
                            let detailStatus = list[i].details[j].sp_status
                            let statusLabel = "审核中";
                            if (detailStatus == 2) {
                                statusLabel = "同意"
                            } else if (detailStatus == 3) {
                                statusLabel = "驳回"
                            }
                            let userid = list[i].details[j].approver.userid;
                            userids.push(userid);
                            let time = list[i].details[j].sptime ? new Date(list[i].details[j].sptime * 1000).pattern("yyyy-MM-dd HH:mm") : '';
                            html.push("<tr><td userid='",userid,"'>",userid,"</td><td>",statusLabel," ",time,"</td></tr>");
                        }
                    }
                }
                $('#spRecord tbody').html(html.join(''));
                userIdTransfer(userids, function(cache) {
                    $('td[userid]').each(function(){
                        var userid = $(this).attr('userid')
                        cache[userid] && $(this).text(cache[userid])
                    })
                });
            })
        }
        let receiveDetail = [[${receiveDetailVOList}]]


        let freightList=[],routeList=[],emptyList=[]

        receiveDetail.forEach(res=>{
            
            if(res.freeType=='0'){
                freightList.push(res)
            }else if(res.freeType=='1'){
                routeList.push(res)
            }else{
                emptyList.push(res)
            }
        })

        let reqDeliDate=[[${#dates.format(invoice.reqDeliDate, 'yyyy-MM-dd')}]]

        let deliList=[[${invoice.deliProName}]]+[[${invoice.deliCityName}]]+[[${invoice.deliAreaName}]]+'-'+[[${invoice.arriProName}]]+[[${invoice.arriCityName}]]+[[${invoice.arriAreaName}]]
        let carList=[[${invoice.carLenName}]]+'-'+[[${invoice.carTypeName}]]
        let goodsName = [[${invoice.goodsName}]];
        let weightCount = [[${invoice.weightCount}]];
        let numCount = [[${invoice.numCount}]];
        let volumeCount = [[${invoice.volumeCount}]];
        if(numCount != 0){
            goodsName = goodsName + ' '+ numCount + '件';
        }
        if(weightCount != 0){
            goodsName = goodsName + ' '+ weightCount + '吨';
        }
        if(volumeCount != 0){
            goodsName = goodsName + ' '+ volumeCount + '方';
        }

        let htmlText=""


        if(freightList&&freightList.length!=0){
            let num=0
            let vbillstatus='*'
            freightList.forEach(res=>{
                num = new BigNumber(num).plus(new BigNumber(res.transFeeCount)).toNumber()
                vbillstatus= res.vbillstatus=="0"?'新建': res.vbillstatus=="1"?'已确认': res.vbillstatus=="2"?'已对账': res.vbillstatus=="3"?'部分核销': res.vbillstatus=="4"?'已核销': res.vbillstatus=="5"?'关闭':'-'
            })
            htmlText+=` <tr><td><div class="input-group">`+reqDeliDate+`</div></td><td ><div class="input-group">`+deliList+`</div>
                        </td><td>`+goodsName+`</td><td>`+carList+`</td><td><div class="input-group"><span>运费</span></div></td><td>
                        <div class="input-group">`+num+`</div></td></tr>`

        }

        if(routeList&&routeList.length!=0){
            let num=0
            let vbillstatus='*'
            routeList.forEach(res=>{
                num = new BigNumber(num).plus(new BigNumber(res.transFeeCount)).toNumber()
                vbillstatus= res.vbillstatus=="0"?'新建': res.vbillstatus=="1"?'已确认': res.vbillstatus=="2"?'已对账': res.vbillstatus=="3"?'部分核销': res.vbillstatus=="4"?'已核销': res.vbillstatus=="5"?'关闭':'-'
            })
            htmlText+=` <tr><td><div class="input-group">`+reqDeliDate+`</div></td><td ><div class="input-group">`+deliList+`</div>
                        </td><td>`+goodsName+`</td><td>`+carList+`</td><td><div class="input-group"><span>在途</span></div></td><td>
                        <div class="input-group">`+num+`</div></td></tr>`

        }

        if(emptyList&&emptyList.length!=0){
            let num=0
            let vbillstatus='*'
            emptyList.forEach(res=>{
                num = new BigNumber(num).plus(new BigNumber(res.transFeeCount)).toNumber()
                vbillstatus= res.vbillstatus=="0"?'新建': res.vbillstatus=="1"?'已确认': res.vbillstatus=="2"?'已对账': res.vbillstatus=="3"?'部分核销': res.vbillstatus=="4"?'已核销': res.vbillstatus=="5"?'关闭':'-'
            })
            htmlText+=` <tr><td><div class="input-group">`+reqDeliDate+`</div></td><td ><div class="input-group">`+deliList+`</div>
                        </td><td>`+goodsName+`</td><td>`+carList+`</td><td><div class="input-group"><span>-</span></div></td><td>
                        <div class="input-group">`+num+`</div></td></tr>`

        }

        $("#freightListId").html(htmlText)

    })

    function gd(obj) {
        let tHeight= $(obj).prev().find("tbody").css("max-height");
        if(tHeight == "max-content"){
            $(obj).prev().find("tbody").css("max-height","205px");
            $(obj).html("查看更多")
        }else{
            $(obj).prev().find("tbody").css("max-height","max-content");
            $(obj).html("收回")
        }
    }
</script>
</body>
</html>