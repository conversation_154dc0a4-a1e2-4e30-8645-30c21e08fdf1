<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('生成第三方对账')"/>
</head>

<body>
<div class="form-content">
    <form id="form-receCheck-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--第三方费用id-->
                        <input type="hidden"  name="params[otherFeeId]" readonly th:value="${otherFeeIds}">

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">客户名称：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control" disabled
                                               name="custAbbr" id="custAbbr" th:value="${custAbbr}">
                                        <input type="hidden" class="form-control" disabled
                                               name="customerId" id="customerId" th:value="${customerId}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">总金额：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control"
                                               id="feeAmount" name="totalAmount" th:value="${custOtherFee.feeAmount}"
                                                disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">未付金额：</label>
                                    <div class="col-sm-7">
                                        <input  class="form-control"
                                                id="ungotAmount" name="ungotAmount" th:value="${custOtherFee.feeAmount}"
                                                disabled >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">油卡金额：</label>
                                    <div class="col-sm-7">
                                        <input  class="form-control"
                                                 name="oilAmount" th:value="${custOtherFee.oilAmount}"
                                                disabled >
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">特殊标记：</label>
                                    <div class="col-sm-7">
                                        <input type="text"  class="form-control" id="otherFeeCheckSheetName"
                                               name="otherFeeCheckSheetName" maxlength="100">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5" style="color: red; ">对账年份：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control" name="year" id="year" required
                                               autocomplete="off" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5" style="color: red; ">对账月份：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control" name="month" id="month" required
                                               autocomplete="off" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-sm-3">备注：</label>
                                    <div class="col-sm-12">
                                            <textarea name="memo" id="memo" maxlength="500" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div  class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFive">第三方费用</a>
                    </h4>
                </div>
                <div id="collapseFive" class="panel-collapse collapse in">
                    <div class="panel-body">

                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabThree" class="custom-tab table" >
                                <thead>
                                <tr>
                                    <th style="width: 20%;text-align:center">第三方单据号</th>
                                    <th style="width: 20%;text-align:center">发货单号</th>
                                    <th style="width: 15%;text-align:center">客户名称</th>
                                    <th style="width: 15%;text-align:center">结算公司</th>
                                    <th style="width: 10%;text-align:center">金额</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="item,status:${custOtherFeeList}">

                                    <td>
                                        <div class="input-group" th:text="${item.vbillno}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" th:text="${item.invoiceVbillno}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" th:text="${item.custAbbr}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group"
                                             th:each="dict : ${@dict.getType('bala_corp')}"
                                             th:if="${dict.dictValue} == ${item.balaCorpId}"
                                             th:text="${dict.dictLabel}" ></div>
                                    </td>
                                    <td th:align="right" th:text="￥+${#numbers.formatDecimal(item.feeAmount,1,'COMMA',2,'POINT')}"></td>
                                </tr>

                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>

            </div>


        </div>

    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="commit()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "business/custOtherFee";

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseFive').collapse('show');

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            laydate.render({
                elem: '#month',
                type: 'month',
                format:"MM"
            });
            laydate.render({
                elem: '#year',
                type: 'year'
            });
        });

    });

    /**
     * 提交的方法
     */
    function commit() {
        if ($.validate.form()){
            var dis = $(":disabled");
            dis.attr("disabled", false);

            var url = prefix + "/saveGenerateOtherFeeCheckSheet";
            var data = $('#form-receCheck-add').serialize();
            $.operate.saveTab(url, data,function(result){
                if(result.code != 0){
                    dis.attr("disabled", true);
                }
            });
        }

    }



</script>
</body>

</html>