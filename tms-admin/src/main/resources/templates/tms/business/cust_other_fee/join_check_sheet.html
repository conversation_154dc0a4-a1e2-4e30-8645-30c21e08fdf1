<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方费用对账')"/>
</head>
<style>
    .table-striped{
        height: calc(100% - 80px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--客户id-->
                <input type="hidden" name="customerId" th:value="${customerId}">
                <!--单据状态 新建-->
                <input type="hidden" name="vbillstatus" th:value="0">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">对账单据号：</label>
                            <div class="col-sm-7">
                                <input type="text" name="vbillno" class="form-control" placeholder="请输入对账单据号">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                    </div>
                    <div  class="col-md-3  col-sm-6 ">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>



            </form>
        </div>



        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"  class="text-nowrap" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "business/otherFeeCheckSheet";

    //第三方费用ids
    var otherFeeIds = [[${ otherFeeIds }]];

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });

        var options = {
            url: prefix + "/list",
            showSearch: true,
            showRefresh: true,
            pagination: true,
            showToggle:false,
            showColumns:true,
            showFooter:true,
            clickToSelect:true,
            modalName: "第三方费用对账",
            rememberSelected: true,
            height: 560,
            uniqueId: 'otherFeeCheckSheetId',
            columns: [{
                radio: true
            },
                {
                    field : 'vbillno',
                    title : '单据号',
                },
                {
                    field : 'vbillstatus',
                    title : '单据状态',
                    formatter: function status(value,row) {
                        if (value == 0) {
                            return '<span class="label label-default">新建</span>';
                        }
                    }
                },

                {
                    field : 'custAbbr',
                    title : '客户简称',
                },
                {
                    field : 'year',
                    title : '对账年',
                },
                {
                    field : 'month',
                    title : '对账月',
                },
                {
                    field : 'otherFeeCheckSheetName',
                    title : '第三方费用对账单名称',
                },
                {
                    field : 'totalAmount',
                    title : '总金额',
                },
                {
                    field : 'gotAmount',
                    title : '已付金额',
                },
                {
                    field : 'ungotAmount',
                    title : '未付金额',
                },
                {
                    field : 'oilAmount',
                    title : '油卡所占金额',
                },
                {
                    field : 'applicationAmountOil',
                    title : '申请金额(油卡)',
                },
                {
                    field : 'applicationAmountCash',
                    title : '申请金额(现金)',
                }
            ]
        };

        $.table.init(options);
    });


    /**
     * 选择一条第三方对账的回调方法
     */
    function submitHandler() {

        var otherFeeCheckSheetId = $.table.selectColumns("otherFeeCheckSheetId").join();

        if (otherFeeCheckSheetId.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.close();

        var data = {};
        data.otherFeeCheckSheetId = otherFeeCheckSheetId;
        data.otherFeeIds = otherFeeIds;

        var url = ctx + "business/custOtherFee/saveJoinOtherFeeCheckSheet";

        $.operate.post(url,data,function(result){
            if(result.code == 0){
                if (result.code == web_status.SUCCESS) {
                    $.modal.close();
                    parent.$.table.refresh();
                }
            }
        });

    }



</script>

</body>

</html>