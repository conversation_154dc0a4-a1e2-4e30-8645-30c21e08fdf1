<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('业务中心-第三方费用')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
</style>
<body class="gray-bg">
<div class="container-div">

    <ul class="nav nav-tabs mt10" role="tablist">
        <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab">三方列表</a></li>
        <li role="presentation"><a href="#profile" aria-controls="profile" role="tab" data-toggle="tab">待审核三方</a></li>
    </ul>

    <div class="tab-content">
        <div role="tabpanel" class="tab-pane active" id="home">
            <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-12 col-sm-12">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">发货单号：</label>-->
                            <div class="col-sm-12">
                                <input name="invoiceVbillno" placeholder="请输入发货单号，单个发货单号为模糊搜索。多个单号搜索为精确搜索，每个单号之间用';'间隔" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-5 col-sm-8">
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户简称：</label>-->
                                <div class="">
                                    <input name="custAbbr" placeholder="请输入客户简称" class="form-control valid" type="text"
                                           aria-required="true">
                                </div>
                            </div>
                        </div>



                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">申请人：</label>-->
                                <div class="col-sm-12">
                                    <input name="applyUserId" placeholder="请输入申请人" class="form-control valid" type="text"
                                           aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                                                        <label class="col-sm-4">收款人：</label>-->
                                <div class="col-sm-12">
                                    <input name="recAccount" placeholder="请输入收款人" class="form-control valid" type="text"
                                           aria-required="true">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5 col-sm-8" style="padding: 0">
                        <div class="col-md-6 col-sm-6">
                            <div class="form-group">
                                <!--                            <label class="col-sm-2">要求提货日：</label>-->
                                <div class="">
                                    <input type="text" style="width: 45%; float: left;" class="form-control"
                                           id="startDate"  name="startReqDeliDate" placeholder="要求提货开始日">
                                    <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                    <input type="text" style="width: 45%; float: left;" class="form-control"
                                           id="endtDate"  name="endReqDeliDate" placeholder="要求提货结束日">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">状态：</label>-->
                                <div class="col-sm-12">
                                    <select class="form-control selectpicker"  aria-invalid="false" name="vbillstatus" id="vbillstatus">
                                        <option value="">--状态--</option>
                                        <option th:each="dict : ${otherFeeStatusList}" th:text="${dict.context}"
                                                th:value="${dict.value}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">付款类型：</label>-->
                                <div class="col-sm-12">
                                    <select class="form-control selectpicker" data-none-selected-text="付款类型"  aria-invalid="false" name="payType">
                                        <option value="">--付款类型--</option>
                                        <option th:value="0">现金</option>
                                        <option th:value="1">油卡</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">运输方式：</label>-->
                            <div class="col-sm-12">
                                <select name="transCode" placeholder="运输方式" id="transCode" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运输方式" multiple th:with="type=${@dict.getType('trans_code')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
                            <!--                                <label class="col-sm-4">提货地址：</label>-->
                            <div class="col-sm-12">
                                <div class="col-sm-4">
                                    <select name="deliProvinceId" id="deliProvinceId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliCityId" id="deliCityId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliAreaId" id="deliAreaId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="col-md-1 col-sm-1">
                        <div class="col-sm-12" onclick="changeDiv()">
                            <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
                            <!--                                <label class="col-sm-4">收货地址：</label>-->

                            <div class="col-sm-12">
                                <div class="col-sm-4">
                                    <select name="arriProvinceId" id="arriProvinceId"
                                            class="form-control valid"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid"></select>
                                </div>

                                <div class="col-sm-4">
                                    <select name="arriAreaId" id="arriAreaId" class="form-control valid"></select>
                                </div>
                            </div>


                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>




                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning single disabled" onclick="checkRecord()" shiro:hasPermission="tms:invoice:fee_apply:check_record">
                <i class="fa fa-calculator"></i> 审核记录
            </a>
            <a class="btn btn-primary single disabled" onclick="feeApply()" shiro:hasPermission="tms:invoice:fee_apply:apply">
                <i class="fa fa-check-circle-o"></i> 申请
            </a>

            <a class="btn btn-primary multiple disabled" onclick="generateOtherFeeCheckSheet()" shiro:hasPermission="business:custOtherFee:generateOtherFeeCheckSheet">
                <i class="fa fa-file-text-o"></i> 生成对账单
            </a>
            <a class="btn btn-primary multiple disabled" onclick="joinOtherFeeCheckSheet()" shiro:hasPermission="business:custOtherFee:joinOtherFeeCheckSheet">
                <i class="fa fa-file-text-o"></i> 加入对账单
            </a>
          <!--  <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="business:custOtherFee:import">
                <i class="fa fa-upload"></i> 调整单导入
            </a>
            <a class="btn btn-warning" onclick="exportExcel()" shiro:hasPermission="business:custOtherFee:export">
                <i class="fa fa-download"></i> 调整单导出
            </a>-->
            <a class="btn btn-primary"  onclick="adjustRecord()" shiro:hasPermission="business:custOtherFee:adjustRecord">
                <i class="fa fa-newspaper-o"></i> 调整单记录
            </a>
            <a class="btn btn-danger single disabled" onclick="remove()" shiro:hasPermission="tms:invoice:fee_apply:del">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-info" th:onclick="showSpSettings([[${templateId}]], [[${biz}]])" shiro:hasPermission="wecom:sp:setting"><!--勿开权限-->
                <i class="fa fa-cog"></i> 配置
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
        </div>
        <div role="tabpanel" class="tab-pane" id="profile">
            <form id="form-user-add"  class="form-horizontal" novalidate="novalidate" enctype="multipart/form-data">
                <div id="collapseTwo">
                    <div class="panel-body" style="padding: 5px 0px 0px;">
                        <div class="fixed-table-body">
                            <table border="0" id="infoTab" class="custom-tab table td">
                                <thead>
                                <tr>
                                    <th style="width: 12%;">发货单号</th>
                                    <th style="width: 10%;">申请人</th>
                                    <th style="width: 10%;">申请时间</th>
                                    <th style="width: 8%;">费用类型</th>
                                    <th style="width: 10%;">金额</th>
                                    <th style="width: 10%;">操作类型</th>
                                    <th style="width: 30%;">备注</th>
                                    <th style="width: 10%;">审批单号</th>

                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="mapS,status:${waitCheckList}">

                                    <td th:text="${mapS.INVOICE_VBILLNO}"></td>
                                    <td th:text="${mapS.REG_USER_NAME}"></td>
                                    <td th:text="${#dates.format(mapS.REG_DATE, 'yyyy-MM-dd')}"></td>
                                    <td th:text="${@dict.getLabel('cost_type_on_way',mapS.FEE_TYPE)}"></td>
                                    <td >¥[[${mapS.FEE_AMOUNT}]]</td>
                                    <td th:text="${mapS.TYPE}"></td>
                                    <td th:text="${mapS.MEMO}"></td>
                                    <td><a th:href="|javascript:wecom_process('${mapS.SP_NO}')|">[[${mapS.SP_NO}]]</a></td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                        <!--end-->
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
    var prefix = ctx + "business/custOtherFee";
    //权限
    var editFlag = [[${@permission.hasPermi('tms:invoice:fee_apply:edit')}]];
    //付款方式
    var payMethod = [[${@dict.getType('pay_method')}]];
    //费用类型
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var transCode = [[${@dict.getType('trans_code')}]];
    //结算公司
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    //第三方费用 状态
    var otherFeeStatusList = [[${otherFeeStatusList}]];
    //第三方费用状态 map
    var otherFeeStatusMap = [[${otherFeeStatusMap}]];
    //第三方费用 新建状态
    var otherFeeStatusNew = [[${otherFeeStatusNew}]];

    var feeAmount = 0;

        $(function () {

            $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
            $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
        });

        $('#vbillstatus').val(0)
        var options = {
            url: prefix + "/list",
            removeUrl: prefix + "/delete",
            showToggle:false,
            showRefresh: true,
            showColumns:false,
            clickToSelect:true,
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showSearch: true,
            pagination: true,
            showColumns:true,
            modalName: "第三方费用",
            uniqueId: "otherFeeId",
            showFooter:true,
            onPostBody:function () {
                //合并页脚
                merge_footer();
                //查询合计总金额
                getAmountCount();
            },
            onRefresh:function(params){
                //总数清0
                feeAmount = 0;

            },
            onCheck: function (row,$element) {

                //总数加上本行数值
                feeAmount += row.feeAmount;
                $("#feeAmountTotal").text(feeAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

            },
            onUncheck: function (row, $element) {

                //总数加上本行数值
                feeAmount -= row.feeAmount;
                $("#feeAmountTotal").text(feeAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onCheckAll: function (rowsAfter) {
                //总数清0
                feeAmount = 0;
                //循环累加
                for (var row of rowsAfter) {
                    feeAmount += row.feeAmount;
                }
                //赋值
                $("#feeAmountTotal").text(feeAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheckAll: function () {
                //总数清0
                feeAmount = 0;

                $("#feeAmountTotal").text(feeAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },

            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "金额合计：<nobr id='feeAmountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "</br>总合计:金额合计：<nobr id='sumFeeAmountTotal'>￥0</nobr>&nbsp&nbsp";
                }
            },
                {
                    title: '操作',
                    align: 'center',
                    field: 'otherFeeId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs ' + editFlag + '" href="javascript:void(0)" title="修改" onclick="edit(\'' + row.otherFeeId + '\',\''+ row.vbillstatus+'\',\''+ row.isClose+'\',\''+row.isAdjust+'\',\''+row.reqDeliDate+'\',\''+row.regDate+'\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                        if(row.vbillstatus == 2 && row.checkStatus == 1){
                            actions.push('<a class="btn btn-xs ' + editFlag + '" href="javascript:void(0)" title="打印" onclick="prints(\'' + row.otherFeeId + '\')"><i class="fa fa-print" style="font-size: 15px;"></i></a>')
                        }
                        if ((row.payType == null || row.payType == 0) && (row.vbillstatus == '1' || row.vbillstatus == '2')) {
                            //row.applyDate && new Date(row.applyDate.replace(/-/g, '/')).getTime()
                            /* <option value="0">新建</option><option value="1">已付款</option><option value="2">申请</option><option value="3">已对账</option>*/
                            if (row.checkSheetVbillno) {
                                actions.push('<a class="btn btn-xs" style="color:#ddd" href="javascript:;" data-toggle="tooltip" data-container="body" title="请至对应对账包页面编辑"><i class="fa fa-cogs" style="font-size: 15px;"></i></a>')
                            } else {
                                actions.push('<a class="btn btn-xs" href="javascript:changeTaxReceipt(\'' + row.otherFeeId + '\',\'', row.invoiceId, '\');"  title="票税调整"><i class="fa fa-cogs" style="font-size: 15px;" ></i></a>')
                            }
                        }
                        return actions.join('');
                    }
                },
                // {
                //     title: '第三方费用单据号',
                //     align: 'left',
                //     field: 'vbillno',
                //
                // },

                {
                    title: '第三方费用单据号/状态',
                    align: 'left',
                    field: 'vbillstatus',
                    formatter: function status(value,row) {
                        var context = "";
                        otherFeeStatusList.forEach(function (v) {
                            if (v.value == row.vbillstatus) {
                                if (row.vbillstatus == otherFeeStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-default">'+v.context+'</span>';
                                }else if (row.vbillstatus == otherFeeStatusMap.ALREADY_PAID) {
                                    //已付款
                                    context = '<span class="label label-success">'+v.context+'</span>';
                                }else if(row.vbillstatus == otherFeeStatusMap.APPLY_FOR){
                                    //已申请
                                    context = '<span class="label label-primary">'+v.context+'</span>';
                                }else if(row.vbillstatus == otherFeeStatusMap.RECONCILED){
                                    //已对账
                                    context = '<span class="label label-coral">'+v.context+'</span>';
                                }
                                return false;
                            }
                        });
                        return row.vbillno+ '<br />'+context;
                    }
                },
                // {
                //     title: '审核状态',
                //     align: 'left',
                //     field: 'checkStatus',
                //     formatter: function(value, item, index) {
                //         if (item.checkStatus == 0) {
                //             return '<span class="label label-default">待审核</span>';
                //         }
                //         if (item.checkStatus == 1) {
                //             return '<span class="label label-primary">审核通过</span>';
                //         }
                //         if (item.checkStatus == 2) {
                //             return '<span class="label label-danger">审核未通过</span>';
                //         }
                //     }
                //
                // },
                {
                    title: '客户简称/发货单号',
                    align: 'left',
                    field: 'custAbbr',
                    formatter: function (value, row, index) {
                        return getValue(value)+"<br/>"+getValue(row.invoiceVbillno)
                    }
                },
                // {
                //     title: '发货单号',
                //     align: 'left',
                //     field: 'invoiceVbillno'
                // },
                {
                    title: '要求提货日',
                    align: 'left',
                    field: 'reqDeliDate'
                },
                {
                    title: '提货|到货省市区',
                    field: 'arriDetailAddress',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(row.deliDetailAddress == null || row.deliDetailAddress == ""){
                            row.deliDetailAddress = '';
                        }
                        if(row.arriDetailAddress == null || row.arriDetailAddress == ""){
                            row.arriDetailAddress = ''
                        }
                        if(row.deliDetailAddress == "" && row.arriDetailAddress == ""){
                            return "";
                        }else{
                            return `<span class="label label-warning pa2">提</span>`+row.deliDetailAddress+`<br/><span class="label label-success pa2">到</span>`+row.arriDetailAddress;
                        }
                        //return row.deliDetailAddress+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriDetailAddress;

                    }
                },
                {
                    title: '总件数/总重量/总数量',
                    align: 'left',
                    field: 'numCount',
                    formatter: function (value, row, index) {
                        return getValue(value)+"/"+getValue(row.weightCount)+"/"+getValue(row.volumeCount)
                    }
                },
                //     title: '总重量',
                //     align: 'left',
                //     field: 'weightCount',
                // },
                // {
                //     title: '总数量',
                //     align: 'left',
                //     field: 'volumeCount',
                // },
                {
                    title: '单价',
                    align: 'left',
                    field: 'price',
                },

                {
                    title: '金额',
                    align: 'right',
                    field: 'feeAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '费用类型',
                    align: 'left',
                    field: 'feeType',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(costTypeOnWay, value.feeType);
                    }
                },
                {
                    title: '运输方式',
                    align: 'left',
                    field: 'transCode',
                    formatter: function status(value,row) {
                        return $.table.selectDictLabel(transCode, value);
                    }
                },
                {
                    title: '付款类型/付款方式',
                    align: 'left',
                    field: 'payType',
                    formatter: function status(value,row) {
                        if(value == 0){
                            return '现金' + '/' + $.table.selectDictLabel(payMethod, row.payMethod);
                        };
                        if(value == 1){
                            return '油卡'+ '/'+ $.table.selectDictLabel(payMethod, row.payMethod)
                        };
                    }
                },
                // {
                //     title: '付款方式',
                //     align: 'left',
                //     field: 'payMethod',
                //     formatter: function status(row,value) {
                //         return $.table.selectDictLabel(payMethod, value.payMethod);
                //     }
                // },
                {
                    title: '对账单号',
                    align: 'left',
                    field: 'checkSheetVbillno',
                },
                {
                    title: '运营公司',
                    align: 'left',
                    field: 'balaCorpId',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(balaCorp, value.balaCorpId);
                    }
                },
                {
                    title: '收款信息',
                    align: 'left',
                    field: 'recAccount',
                    formatter: function status(value, row, index) {
                        return getValue(value)+'/'+$.table.tooltip(row.recCardNo)+'<br />'+$.table.tooltip(row.recBank);
                    }
                },
                // {
                //     title: '收款银行',
                //     align: 'left',
                //     field: 'recBank',
                //     formatter: function status(value,row) {
                //         return $.table.tooltip(row.recBank);
                //     }
                // },
                // {
                //     title: '收款账号',
                //     align: 'left',
                //     field: 'recCardNo',
                //     formatter: function status(value,row) {
                //         return $.table.tooltip(row.recCardNo);
                //     }
                // },

                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate',
                },
                {
                    title: '是否为调整单',
                    align: 'left',
                    field: 'isAdjust',
                    formatter: function status(value,row) {
                        return value == 0 ? '否':'是';
                    }
                },
                {
                    title: '申请人/时间',
                    align: 'left',
                    field: 'applyUserId',
                    formatter: function (value, row, index) {
                        return getValue(value)+"<br/>"+getValue(row.applyDate)
                    }
                },
                // {
                //     title: '申请时间',
                //     align: 'left',
                //     field: 'applyDate',
                // },

                {
                    title: '支付人/时间',
                    align: 'left',
                    field: 'payUser',
                    formatter: function (value, row, index) {
                        return getValue(value)+"<br/>"+getValue(row.payDate)
                    }
                },
                // {
                //     title: '支付时间',
                //     align: 'left',
                //     field: 'payDate',
                // },
                /*{
                    title: '财务退回原因',
                    field: 'backMemo',
                    align: 'left',
                },*/
                {
                    title: '审批单号',
                    field: 'spNo',
                    align: 'left',
                }
            ]
        };

        $.table.init(options);
    });
    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }

    /**
     * 搜索
     */
    function searchPre() {

        var data = {};
        data.params = new Map();

        data.transCode = $.common.join($('#transCode').selectpicker('val'));

        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }


    /**
     * 修改
     * @param id
     */
    function edit(id, vbillstatus,isClose,isAdjust,reqDeliDate,regDate) {

        let date = new Date(),
            year = date.getFullYear(), //获取完整的年份(4位)
            month = date.getMonth() + 1; //获取当前月份(0-11,0代表1月)
        if (month < 10) month = `0${month}` // 如果月份是个位数，在前面补0
        let currentMonth = `${year}-${month}`;

        if(isAdjust == 1){
           if(regDate.substring(0,7) != currentMonth){
               $.modal.alertWarning("该月份已关账，无法进行操作！");
               return false;
           }
        }else{
            if(reqDeliDate.substring(0,7) != currentMonth){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }
       /* if(isClose == 1){
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }*/
        if (vbillstatus != otherFeeStatusNew) {
            $.modal.alertWarning("请选择“新建”状态的数据！")
            return false;
        }
        layer.open({
            type: 2,
            area: ['60%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "第三方费用修改",
            content: ctx + "invoice/fee_apply/edit/" + id,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });
    }

    /**
     * 费用申请
     */
    function feeApply() {
        var selectColumns = $.table.selectColumns("vbillstatus");
        var id = $.table.selectColumns($.table._option.uniqueId)+"";
        if (selectColumns != otherFeeStatusNew) {
            $.modal.alertWarning("请选择“新建”状态的数据！")
            return false;
        }

        var lockOtherFee = $.table.selectColumns("lockOtherFee");
        if (lockOtherFee == 1) {
            $.modal.alertWarning("该三方费用已锁定，无法申请。")
            return false;
        }

        layer.open({
            type: 2,
            area: ['60%', '100%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "第三方费用申请",
            content: ctx + "invoice/fee_apply/apply/" + id,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });

    }

    /**
     * 删除
     */
    function remove() {
        var selectColumns = $.table.selectColumns("vbillstatus");
        var isClose = $.table.selectColumns("isClose");
        if(isClose == 1){
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }
        if (selectColumns != otherFeeStatusNew) {
            $.modal.alertWarning("请选择“新建”状态的数据！")
            return false;
        }

        var invoiceId = $.table.selectColumns("invoiceId").join();
        var otherFeeId = $.table.selectColumns("otherFeeId").join();

        //验证发货单是否超过五天
        $.ajax({
            type: "POST",
            url: prefix + "/checkOtherFeeOverDate?otherFeeId="+otherFeeId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.confirm("确认要删除选中的数据吗?", function() {
                        var rows = $.table.selectColumns($.table._option.uniqueId);
                        var data = { "ids": rows.join() };
                        $.operate.submit(ctx + "invoice/fee_apply/delete", "post", "json", data);
                    });
                }
            }
        });


    }


    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;
                if (result.code == 0 && data != undefined) {
                    $("#sumFeeAmountTotal").text(data.FEEAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }


    /**
     * 审核记录
     */
    function checkRecord(){
        //第三方费用id
        /*var otherFeeId = $.table.selectColumns("otherFeeId");
        var url = ctx + "invoice/fee_apply/check_record?otherFeeId="+otherFeeId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });*/
        var spNo = $.table.selectColumns("spNo")[0];
        if (!spNo) {
            $.modal.msgWarning("未提交企业微信审批")
            return
        }
        wecom_process(spNo);
    }

    /**
     * 生成第三方对账
     */
    function generateOtherFeeCheckSheet(){
        //获取选择行
        var rows = $.btTable.bootstrapTable('getSelections');
        //客户id
        var customerId = rows[0]['customerId'];
        var custAbbr = rows[0]['custAbbr'];
        for(var i=0;i<rows.length;i++){
            if(rows[i]['vbillstatus'] !== otherFeeStatusMap.NEW){
                $.modal.alertWarning("请选择“新建”状态的数据！")
                return;
            };
            if(customerId !== rows[i]['customerId']){
                $.modal.alertWarning("请选择相同客户的数据！")
                return;
            };
            if(rows[i]['lockOtherFee'] == 1){
                $.modal.alertWarning("存在已锁定的三方费用，无法生成对账包。")
                return;
            };

        }
        //获取第三方费用id
        var otherFeeId = $.table.selectColumns("otherFeeId").join();
        var url = prefix + "/generateOtherFeeCheckSheet?otherFeeIds="+otherFeeId+"&customerId="+customerId+"&custAbbr="+custAbbr;
        $.modal.openTab("生成第三方对账",url);
    }

    /**
     * 加入对账单
     */
    function joinOtherFeeCheckSheet(){
        //获取选择行
        var rows = $.btTable.bootstrapTable('getSelections');
        //客户id
        var customerId = rows[0]['customerId'];
        for(var i=0;i<rows.length;i++){
            if(rows[i]['vbillstatus'] !== otherFeeStatusMap.NEW){
                $.modal.alertWarning("请选择“新建”状态的数据！")
                return;
            };
            if(customerId !== rows[i]['customerId']){
                $.modal.alertWarning("请选择相同客户的数据！")
                return;
            };
            if(rows[i]['lockOtherFee'] == 1){
                $.modal.alertWarning("存在已锁定的三方费用，无法加入对账包。")
                return;
            };

        }
        //获取第三方费用id
        var otherFeeId = $.table.selectColumns("otherFeeId").join();
        var url = prefix + "/joinOtherFeeCheckSheet?otherFeeIds="+otherFeeId+"&customerId="+customerId;
        $.modal.open("加入第三方对账",url);
    }

    /**
     * 导出
     */
    function exportExcel() {
        var data = $("#role-form").serializeArray();
        $.modal.confirm("确定导出所有" +
            $.table._option.modalName + "吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(prefix + "/exportExcel", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });


    }

    function adjustRecord(){
        var url = prefix + "/adjustRecordCheck";
        $.modal.openTab('调整单记录',url);
    }

    function prints(otherFeeId){
        var url =  prefix + "/printOtherFee?otherFeeId="+otherFeeId;
        $.modal.openTab('第三方单笔费用打印',url);
    }

    function changeTaxReceipt(otherFeeId, invoiceId) {
        $.ajax({
            url: prefix + "/preSaveTaxReceipt",
            data: 'otherFeeId=' + otherFeeId + "&invoiceId="+invoiceId,
            cache: false,
            success: function (result) {
                if (result.code != 0) {
                    $.modal.msgWarning(result.msg)
                } else {
                    $.modal.open('票税调整', prefix + '/editTaxReceipt?otherFeeId=' + otherFeeId + "&invoiceId="+invoiceId, 800, 600);
                }
            }
        })
    }
</script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>