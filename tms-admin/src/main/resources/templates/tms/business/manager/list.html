<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('管理费列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .table-striped{
        height: calc(100% - 100px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">对账单号：</label>
                            <div class="col-sm-8">
                                <input type="text" name="vbillno" class="form-control" placeholder="请输入对账单号">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">客户简称：</label>
                            <div class="col-sm-8">
                                <input type="text" name="custAbbr" class="form-control" placeholder="请输入客户简称">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">付款状态：</label>
                            <div class="col-sm-8">
                                <select name="status" id="payStatus" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="付款状态" multiple>
                                    <option th:value="0">新建</option>
                                    <option th:value="1">已申请</option>
                                    <option th:value="2">已付款</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div  class="col-md-3  col-sm-6 ">
                        <div class="form-group">
                            <label class="col-sm-4">年份：</label>
                            <div class="col-sm-8">
                                <input name="params[year]" id="year"  placeholder="请输入年份" class="form-control valid" type="text"
                                       aria-required="true" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div  class="col-md-3  col-sm-6 ">
                        <div class="form-group">
                            <label class="col-sm-4">月份：</label>
                            <div class="col-sm-8">
                                <input name="params[month]" id="month"  placeholder="请输入月份" class="form-control valid" type="text"
                                       aria-required="true" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2">创建时间：</label>
                            <div class="col-sm-7">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="regDate" placeholder="开始时间">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="endtDate"  name="corDate" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    <div  class="col-md-3  col-sm-6 ">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>


        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"  class="text-nowrap" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">

    var prefix = ctx + "business/manager";
    //付款方式
    var payMethod = [[${@dict.getType('pay_method')}]];

    var totalManagerFee = 0;//总金额

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
        });

        var options = {
            url: prefix + "/list",
            showSearch: true,
            showRefresh: true,
            pagination: true,
            showToggle:false,
            showColumns:true,
            showFooter:true,
            modalName: "管理费",
            rememberSelected: true,
            clickToSelect:true,
            showExport: true,
            exportTypes:['excel','csv'],
            height: 560,
            exportOptions:{
                ignoreColumn: [0],
                fileName:"管理费"
            },
            showFooter:true,
            onPostBody:function () {
                //合并页脚
                merge_footer();
                //查询合计总金额
                getAmountCount();
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "合计:&nbsp&nbsp"
                        + "管理费:<nobr id='totalManagerFee'>￥0</nobr>&nbsp&nbsp<br>"
                        +"总合计:&nbsp&nbsp"
                        + "管理费:<nobr id='totalManagerFees'>￥0</nobr>&nbsp&nbsp"
                }
            },
                {
                    title: '管理费单据号',
                    align: 'left',
                    field: 'glfVbillno'
                },
                {
                    title: '对账单名称',
                    align: 'left',
                    field: 'receCheckSheetName'
                },
                {
                    title: '应收对账单号',
                    align: 'left',
                    field: 'vbillno'
                },
                {
                    title: '客户简称',
                    align: 'left',
                    field: 'custAbbr'
                },
                {
                    title: '付款状态',
                    align: 'left',
                    field: 'payStatus',
                    formatter: function (value, row, index) {
                        if (value == 0) {
                            return '<span class="label label-default">新建</span>';
                        }else if(value == 1){
                            return '<span class="label label-success">已申请</span>';
                        }else if(value == 2){
                            return '<span class="label label-primary">已付款</span>';
                        }
                        return "";
                    }
                },
                {
                    title: '管理费',
                    align: 'right',
                    field: 'managerFee',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '付款方式',
                    align: 'left',
                    field: 'payMethod',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(payMethod, value.payMethod);
                    }
                },
                {
                    title: '收款人',
                    align: 'left',
                    field: 'receiveMan',
                },
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'receiveBank',

                },
                {
                    title: '收款账号',
                    align: 'left',
                    field: 'receiveCard',
                },
                {
                    title: '申请人',
                    align: 'left',
                    field: 'applyUser',
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'applyTime',
                },
                {
                    title: '退回原因',
                    align: 'left',
                    field: 'backMemo',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.backMemo);
                    }

                },
                {
                    title: '备注',
                    align: 'left',
                    field: 'memo',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.memo);
                    }
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate',
                },

            ]
        };

        $.table.init(options);
    });

    layui.use('laydate', function(){
        var laydate = layui.laydate;
        laydate.render({
            elem: '#month',
            type: 'month',
            format:"MM"
        });
        laydate.render({
            elem: '#year',
            type: 'year'
        });
    });

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.status = $.common.join($('#payStatus').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.status = $.common.join($('#payStatus').selectpicker('val'));
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $("#totalManagerFees").text(data.MANAGER_FEE.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        totalManagerFee = 0;//总金额
    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        totalManagerFee = totalManagerFee + row.managerFee;
    }

    function subTotal(row) {
        totalManagerFee = totalManagerFee - row.managerFee;
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#totalManagerFee").text(totalManagerFee.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }





</script>

</body>

</html>