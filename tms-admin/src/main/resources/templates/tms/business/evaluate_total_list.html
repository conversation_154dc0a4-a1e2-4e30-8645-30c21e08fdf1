<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('评价统计')"/>
</head>
<style>
    .table-striped{
        height: calc(100% - 80px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">发货单编码：</label>
                            <div class="col-sm-7">
                                <input type="text" name="vbillno" class="form-control" placeholder="请输入发货单编码">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">客户名称：</label>
                            <div class="col-sm-7">
                                <input type="text" name="custName" class="form-control" placeholder="请输入客户名称">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                    </div>
                    <div  class="col-md-3  col-sm-6 ">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>



            </form>
        </div>

        <!--<div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="insertRow()" shiro:hasPermission="tms:trace:abnormal:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger" onclick="$.operate.removeAll()" shiro:hasPermission="tms:trace:abnormal:delete">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>-->

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"  class="text-nowrap" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "business/evaluateTotal";

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });

        var options = {
            url: prefix + "/list",
            showSearch: true,
            showRefresh: true,
            pagination: true,
            showToggle:false,
            showColumns:true,
            showFooter:true,
            modalName: "评价统计",
            rememberSelected: true,
            height: 560,
            uniqueId: 'entrustExpId',
            columns: [
                {
                    title: '发货单编码',
                    align: 'left',
                    field : 'vbillno'
                },
                {
                    title: '客户名称',
                    align: 'left',
                    field : 'custName'
                },
                {
                    title: '要求提货日期',
                    align: 'left',
                    field : 'reqDeliDate'
                },
                {
                    title: '要求到货日期',
                    align: 'left',
                    field : 'reqArriDate',
                },
                {
                    title: '物流发货率分数',
                    align: 'left',
                    field : 'loisticsScore'
                },
                {
                    title: '回单及时率分数',
                    align: 'left',
                    field : 'receiptScore'
                },
                {
                    title: '货物安全分数',
                    align: 'left',
                    field : 'goodsScore'
                },
                {
                    title: '人员服务态度分数',
                    align: 'left',
                    field : 'serviceScore'
                },
                {
                    title: '履约情况分数',
                    align: 'left',
                    field : 'fulfillScore'
                },
                {
                    title: '评论内容',
                    align: 'left',
                    field : 'memo'
                }
            ]
        };

        $.table.init(options);
    });




</script>

</body>

</html>