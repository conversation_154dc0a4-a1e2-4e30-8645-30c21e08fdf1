<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('历史价格')"/>
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <input type="hidden" name="carrierId" th:value="${carrierId}">
                    <div class="col-md-5 col-sm-6">
                        <div class="col-md-4 col-sm-4" style="padding: 0">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">车长：</label>-->
                                <div class="col-sm-12">
                                    <select name="carLenStr" id="carLenStr" class="form-control selectpicker" data-none-selected-text="实际车长" multiple th:with="type=${@dict.getType('car_len')}">
                                        <option value="">-- 实际车长 --</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${dict.dictValue==#request.getParameter('carLen')}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">车型：</label>-->
                                <div class="col-sm-12">
                                    <select name="carType" id="carType" class="form-control" th:with="type=${@dict.getType('car_type')}">
                                        <option value="">-- 实际车型 --</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${dict.dictValue==#request.getParameter('carType')}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <select name="transTypes" placeholder="运输方式" id="transTypes" class="form-control valid noselect2 selectpicker"
                                    aria-invalid="false" data-none-selected-text="运输方式" multiple th:with="type=${@dict.getType('trans_code')}">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${dict.dictValue==#request.getParameter('transCode')}"></option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-3">
                      <!--  <div class="col-md-4 col-sm-4">
                            <div class="form-group">
                                &lt;!&ndash;                            <label class="col-sm-4">客户名称：</label>&ndash;&gt;
                                <div class="col-sm-12">
                                    <input name="custAbbr" id="custAbbr" placeholder="请输入客户名称" class="form-control " type="text"
                                           aria-required="true">
                                </div>
                            </div>
                        </div>-->
                        <!--<div class="col-md-4 col-sm-4">
                            <div class="form-group">
                                &lt;!&ndash;                            <label class="col-sm-4">货品名称：</label>&ndash;&gt;
                                <div class="col-sm-12">
                                    <input name="goodsName" id="goodsName" placeholder="请输入货品名称" class="form-control " type="text"
                                           aria-required="true">
                                </div>
                            </div>
                        </div>-->
                        <div class="col-md-12 col-sm-12">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">车牌号：</label>-->
                                <div class="col-sm-12">
                                    <input name="carNo" id="carNo" placeholder="请输入车牌号" class="form-control " type="text"
                                           aria-required="true">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-2">调度日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" placeholder="调度开始日期" class="time-input form-control"
                                       id="dispatcherDateStart"  name="dispatcherDateStart" th:value="${startDate}">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" placeholder="调度结束日期" class="time-input form-control"
                                       id="dispatcherDateEnd"  name="dispatcherDateEnd" th:value="${endDate}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="isOversize" id="isOversize" class="form-control custom-select" aria-invalid="false">
                                    <option value="" selected>-- 是否大件 --</option>
                                    <option value="0" >非大件</option>
                                    <option value="1">大件</option>
                                </select>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="row">
                    <div class="col-md-1 col-sm-1" >
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="reqCarLen" id="reqCarLen" class="form-control" th:with="type=${@dict.getType('car_len')}">
                                    <option value="">-- 要求车长 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 col-sm-1" th:if="${src!='pool'}">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">客户名称：</label>-->
                            <div class="col-sm-12">
                                <input name="carrierName" id="carrierName" placeholder="请输入承运商名称" class="form-control " type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4" >
                        <div class="form-group">
                            <div class="col-sm-4">
                                <select  name="deliProvinceId" id="deliProvinceId"  class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                            <div class="col-sm-4">
                                <select name="deliCityId" id="deliCityId"  class="form-control valid" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4">
                                <select name="deliAreaId" id="deliAreaId"  class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-1" onclick="changeDiv()">
                        <img th:src="@{/img/change.png}" style="width: 40px;height: 40px;display: block;margin: 0 auto">
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">

                            <div class="col-sm-4">
                                <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                            <div class="col-sm-4">
                                <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4">
                                <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-1">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre() "><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>



            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary " onclick="carLocation()" shiro:hasPermission="tms:segment:location">
                <i class="fa fa-file" ></i> 多车定位
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:segment:segmentHistoryPrice:export">
                <i class="fa fa-download"></i> 导出
            </a>

            <span id="averageAmount" style="margin-left: 15px; line-height: 30px; display: inline-block; font-weight: bold;">平均金额：0元</span>

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "business/historyPrice";

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        let src = [[${src}]];
        if (src != 'pool' && src != 'rfq') { // 不是从运力资源池而来
            $("input[name='dispatcherDateStart']").val(getFrontFormatDate())
        }
        let deliPrivinceId = [[${deliProvinceId}]];
        let arriPrivinceId = [[${arriProvinceId}]];
        let deliCityId = [[${deliCityId}]];
        let arriCityId = [[${arriCityId}]];
        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId", deliPrivinceId, deliCityId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId", arriPrivinceId, arriCityId);

        var options = {
            url: ctx + "business/historyPrice/list",
            showToggle:false,
            showColumns:false,
            clickToSelect: true,
            modalName: "",
            firstLoad: false,
            uniqueId: "carrierId",
            exportUrl: ctx + "business/historyPrice/export",
            height: 560,
            columns: [{
                checkbox: true,
            },
              
              /*  {
                    title: '客户名称',
                    field: 'custAbbr',
                    align: 'left'
                },*/
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: formatterInformation
                },
                // {
                //     title: '货品名称',
                //     field: 'goodsName',
                //     align: 'left'
                // },
                {
                    title: '公里数',
                    field: 'actualmileage',
                    align: 'right'
                },

                {
                    title: '货量',
                    field: 'numCount',
                    align: 'left',
                    formatter:function(value,row){
                        let data=[]
                        if(value){
                            data.push(value+"件")
                        }
                        if(row.weightCount){
                            data.push(row.weightCount+"吨")
                        }
                        if(row.volumeCount){
                            data.push(row.volumeCount+"m³")
                        }
                        return data.join("|");
                    }
                },
                {
                    title: '要求车长车型',
                    field: 'reqCarLen',
                    align: 'left',
                    formatter:function(value,row){
                        return value+'米' +row.reqCarTypeName
                    }
                },
                {
                    title: '承运商信息',
                    field: 'carrierName',
                    align: 'left',
                    formatter:function(value,row){
                        return value + '/'+row.phone
                    }
                },
                // {
                //     title: '联系电话',
                //     field: 'phone',
                //     align: 'left'
                // },
                // {
                //     title: '车牌',
                //     field: 'carNo',
                //     align: 'left'
                // },
                {
                    title: '车牌/车长车型',
                    field: 'carLenName',
                    align: 'left',
                    formatter:function(value,row){
                        return row.carNo + '/'+value +'米'+row.carTypeName
                    }
                },
                // {
                //     title: '车型',
                //     field: 'carTypeName',
                //     align: 'left'
                // },
                {
                    title: '总金额',
                    field: 'costAmount',
                    align: 'left'
                },

                // {
                //     title: '要求车型',
                //     field: 'reqCarTypeName',
                //     align: 'left'
                // },
                {
                    title: '油卡金额',
                    field: 'oilAmount',
                    align: 'left'
                },
               

               
                {
                    title: '调度人/调度日期',
                    field: 'dispatcherDate',
                    align: 'left',
                    formatter:function(value,row){
                        return row.dispatcher + '/'+value
                    }
                },
                // {
                //     title: '调度人',
                //     field: 'dispatcher',
                //     align: 'left'
                // },
                {
                    title: '运输方式',
                    field: 'transTypeName',
                    align: 'left'
                },
                {
                    title: '运单号',
                    field: 'lot',
                    align: 'left'
                },
                {
                    title: '是否大件',
                    field: 'isOversize',
                    align: 'center',
                    formatter:function(value,row){
                        if(value == 1){
                            return '是';
                        }else{
                            return '否';
                        }
                    }
                }
            ]
        };
        $.table.init(options);

        $.table.search();

        getAvgPrice();

    });
    //当前时间推前一个月
    function getFrontFormatDate() {
        var date = new Date();
        date.setMonth(date.getMonth() - 1);
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() + 1 ;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate;
        return currentdate;
    }
    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        // data.deliCityId = deliCityId;
        // data.arriProvinceId = arriProvinceId;
        // data.arriCityId = arriCityId;

        //$.modal.alertWarning()
        data.carLenStr = $.common.join($('#carLenStr').selectpicker('val'));
        $.table.search('role-form', data);

        getAvgPrice();

    }

    /**
     * 重置
     */
    function resetPre() {
        // $("#deliAreaId ").val("");
        // $("#arriAreaId ").val("");
        // $("#role-form")[0].reset();
        // searchPre();
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $.form.reset()
        getAvgPrice()

    }

    function carLocation() {
        //是否回单标记
        var carNo = $.table.selectColumns("carNo");
        if (carNo.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/location?carNo="+carNo.join();
        $.modal.open("车辆定位查看", url);
    }

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        searchPre()
    }

    function formatterInformation(value, row, index){

        console.log(row)
        let htmlTextT='';

        if(row.multipleShippingAddressList){
            let list = row.multipleShippingAddressList;
            for (const elem of list) {
                let str = ""
                if (elem.addressType === 0) {


                    if(elem.provinceName){
                        str+=elem.provinceName;
                    }
                    if(elem.cityName&&elem.cityName != '市辖区'){
                        str+=elem.cityName;
                    }
                    if(elem.areaName){
                        str+=elem.areaName;
                    }


                    htmlTextT+= `<div><span class="label label-warning pa2">装</span><div class="ml5" style="display: inline-block;vertical-align: middle;">`+str+`</div></div>`;
                }
            }

            for (const elem of list) {
                let str = ""
                if(elem.addressType === 1){



                    if(elem.provinceName){
                        str+=elem.provinceName;
                    }
                    if(elem.cityName&&elem.cityName != '市辖区'){
                        str+=elem.cityName;
                    }
                    if(elem.areaName){
                        str+=elem.areaName;
                    }


                    htmlTextT+= `<div class="mt5"><span class="label label-success pa2">卸</span><div class="ml5" style="display: inline-block;vertical-align: middle;">`+str+`</div></div>`;
                }
            }

        }

        return htmlTextT;

    }

    /**
     * 获取平均金额
     */
    function getAvgPrice() {
        var formData = $.common.formToJSON("role-form");
        formData.carLenList = $.common.join($('#carLenStr').selectpicker('val'));

        $.ajax({
            type: "get",
            url: prefix + "/avgPrice",
            data: formData,
            dataType: "json",
            success: function(result) {
                if (result.code == 0 && result.data) {
                    $("#averageAmount").text("平均金额：" + result.data + "元");
                } else {
                    $("#averageAmount").text("平均金额：0元");
                }
            },
            error: function() {
                $("#averageAmount").text("平均金额：0元");
            }
        });
    }
</script>
</body>
</html>