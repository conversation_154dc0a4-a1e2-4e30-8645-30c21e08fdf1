<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .table-striped{
        height: calc(100% - 80px);
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .fw{
        font-weight: bold;
    }
    .fc1a{
        color: #1a1a1a;
    }
    .fc80{
        color: #808080;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .table th, .table td {
        text-align: center;
    }
    .input-group {
        width: 100%;
        text-align: left;
    }
    .round{
        width: 50px;
        height: 50px;
        /*text-align: center;*/
        /*border-radius: 50%;*/
        /*line-height: 50px;*/
        box-sizing: border-box;
        /*background: url('../../../img/pep.png') no-repeat center;*/
        /*background-size: 60px 60px;*/
    }
    .line{
        width: 1px;
        min-height: 20px;
        max-height: 50px;
        border-left: 2px dashed #cdcdcd;
        margin-left: 25px;
    }
    .fc00a{
        color: #00A9FF;
    }
    .fcff8{
        color: #FF8900;
    }
    .th{
        /*border: 6px solid #00A9FF;*/
        /*color: #00A9FF;*/
    }
    .dh{
        /*border: 6px solid #FF8900;*/
        /*color: #FF8900;*/
    }
    .padt20{
        padding: 20px 0;
    }
    .warp{
        background: #f2f9ff;
        padding: 10px 20px;
        border: 1px solid #e7eaec;
    }
    .printitle{
        font-size: 16px;
        line-height: 30px;
        text-align: center;
    }
    .printtable .table tbody tr td{
        font-size: 12px;
    }
    .flex{
        display: flex;
        justify-content: flex-end;
    }
    .flexFs{
        display: flex;
        justify-content: flex-start;
    }
</style>
<body class="gray-bg">
<div class="form-content">
    <a class="btn btn-primary" onclick="dayin()">
        <i class="fa fa-print"></i> 打印
    </a>
    <div  class="" id='div_print'>
        <div class="fw printitle">第三方对账费用申请</div>
        <div class="padt20">
            <div class="over flexFs">
                <div class="fl">对账年月：<span class="fw">[[${otherFeeCheckSheet.year}]]年[[${otherFeeCheckSheet.month}]]月</span></div>
                <div class="fl ml20">客户简称：<span class="fw">[[${clientPopupVO.custAbbr}]]</span></div>
                <div class="fl ml20">运营组：<span class="fw">[[${clientPopupVO.salesDeptName}]]</span></div>
            </div>
            <div class="over mt10">
                <table class="table table-bordered">
                    <thead style="background: #F7F8FA">
                        <tr>
                            <th style="width: 6%;">类型</th>
                            <th style="width: 10%;">金额(元)</th>
                            <th style="width: 30%;">收款信息</th>
                            <th style="width: 20%;">申请信息</th>
                            <th style="width: 34%;">申请原因</th>
                        </tr>
                        </thead>
                    <tbody>
                    <tr th:if="${otherFeeSheetRecordXJ.otherFeeSheetRecordId != null}">
                        <td style="text-align: left;">现金</td>
                        <td style="text-align: left;">[[${otherFeeCheckSheet.applicationAmountCash}]]</td>
                        <td style="text-align: left;">[[${otherFeeSheetRecordXJ.recAccount}]]([[${otherFeeSheetRecordXJ.recCardNo}]])</td>
                        <td style="text-align: left;">[[${otherFeeSheetRecordXJ.applyPayUser}]]<br/>[[${#dates.format(otherFeeSheetRecordXJ.applyPayDate, 'yyyy-MM-dd HH:mm:ss')}]]</td>
                        <td style="text-align: left;">[[${otherFeeSheetRecordXJ.memo}]]</td>
                    </tr>
                    <tr th:if="${otherFeeSheetRecordYK.otherFeeSheetRecordId != null}">
                        <td style="text-align: left;">油卡</td>
                        <td style="text-align: left;">[[${otherFeeCheckSheet.applicationAmountOil}]]</td>
                        <td style="text-align: left;">油卡号：[[${otherFeeSheetRecordYK.oilcard}]]</td>
                        <td style="text-align: left;">[[${otherFeeSheetRecordYK.applyPayUser}]]<br/>[[${#dates.format(otherFeeSheetRecordYK.applyPayDate, 'yyyy-MM-dd HH:mm:ss')}]]</td>
                        <td style="text-align: left;">[[${otherFeeSheetRecordYK.memo}]]</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <div class="over">
                    <div class="fl" >
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" >费用明细</a>
                        </h5>
                    </div>
                    <div class="fl ml10" >
                        <h5 class="panel-title">
                            对账单号-[[${otherFeeCheckSheet.vbillno}]]
                        </h5>
                    </div>
                    <div class="fl ml10" >
                        <h5 class="panel-title">
                            合计<span class="ts"></span>单
                        </h5>
                    </div>
                </div>

               
            </div>
            <div  class="panel-collapse collapse in" id="collapseOne">
                <div class="panel-body">
                    <div class="">
                        <div class="warp">
                            <div class="over flex">
                                <div class=" ">总金额：￥[[${otherFeeCheckSheet.totalAmount}]]
                                    （ <span th:if="${(otherFeeCheckSheet.totalAmount - otherFeeCheckSheet.oilAmount) != 0}">现金：￥[[${otherFeeCheckSheet.totalAmount - otherFeeCheckSheet.oilAmount}]]</span>
                                    <span th:if="${otherFeeCheckSheet.oilAmount != 0}">油卡：￥[[${otherFeeCheckSheet.oilAmount}]]</span> ）
                                </div>
                                <div class=" ">
                                    合计货量： [[${ sumNumCount != 0 ? sumNumCount+'件|' :'' }]][[${ sumWeightCount != 0 ? sumWeightCount+'吨|' : ''}]][[${ sumVolumeCont != 0 ? sumVolumeCont+'m³' : '' }]]           
                                </div>
                            </div>
                        </div>
                    </div>

                    <table class="table table-bordered">
                        <thead style="background: #F7F8FA">
                        <tr>
<!--                                        <th>第三方费用单据号</th>-->
                            <th>发货单号</th>
                            <th>要求提货日</th>
                            <th>提货省市区</th>
                            <th>到货省市区</th>
                            <!-- <th>费用类型</th> -->

                            <th>金额(元)</th>
                            <th>货量</th>
                            <th>单价(元)</th>
                            <th>承运商</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr th:each="otherFee,status: ${custOtherFeeVOS}" th:class="${ status.index < 12 ?'':'hide' }">
<!--                                        <td>-->
<!--                                            <div class="input-group">[[${otherFee.vbillno}]]</div>-->
<!--                                        </td>-->
                            <td>
                                <div class="input-group">[[${otherFee.invoiceVbillno}]]</div>
                            </td>
                            <td>
                                <div class="input-group">[[${#dates.format(otherFee.reqDeliDate, 'yyyy-MM-dd')}]]</div>
                            </td>
                            <td >
                                <div class="input-group">[[${otherFee.deliAddress}]]</div>
                            </td>
                            <td >
                                <div class="input-group">[[${otherFee.arriAddress}]]</div>
                            </td>
                            <!-- <td>
                                <div class="input-group" th:switch="${otherFee.feeType}">
                                    <span th:case="1">装卸费</span>
                                    <span th:case="2">提货费</span>
                                    <span th:case="3">放空费</span>
                                    <span th:case="4">停车费</span>
                                    <span th:case="5">进门费</span>
                                    <span th:case="6">信息费</span>
                                    <span th:case="7">过磅费</span>
                                    <span th:case="8">过路费</span>
                                    <span th:case="9">仓储费</span>
                                    <span th:case="12">改送费</span>
                                    <span th:case="10">其他</span>
                                    <span th:case="13">误工费</span>
                                    <span th:case="14">客户赔偿</span>
                                    <span th:case="15">进仓费</span>
                                    <span th:case="16">中转费</span>
                                    <span th:case="17">回单扣款</span>
                                    <span th:case="18">异常扣款</span>
                                    <span th:case="19">货损扣款</span>
                                    <span th:case="20">合同约定调整</span>
                                    <span th:case="21">压车费</span>
                                    <span th:case="22">送货费</span>
                                    <span th:case="*">-</span>
                                </div>
                            </td> -->

                            <td>
                                <div class="input-group">
                                    <span th:if="${otherFee.xj != 0}">现金:[[${otherFee.xj}]]</span>
                                    <span th:if="${otherFee.yk != 0}">/油卡:[[${otherFee.yk}]]</span>
                                </div>
                            </td>
                            <td>
                                <div class="input-group">
                                    [[${ otherFee.numCount != 0 ? otherFee.numCount+'件|' :'' }]]
                                    [[${ otherFee.weightCount != 0 ? otherFee.weightCount+'吨|' : ''}]]
                                    [[${ otherFee.volumeCount != 0 ? otherFee.volumeCount+'m³' : '' }]]
                                </div>
                            </td>
                            <td th:text="${otherFee.price}"></td>
                            <td>
                                <div class="input-group">[[${otherFee.carrierName}]]</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <a class="gd" href="javascript:void(0)" onclick="gd(this)">剩余(<span class="yc"></span>条)</a>
                </div>
            </div>
        </div>

       
        <div class="over">
            <div class="fr" id="time" style="width:150px"></div>
            <div class="fr" style="width:150px">签字：</div>
        </div>
    </div>

</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var height = $('.selector').height();
    
    var list=[[${custOtherFeeVOS}]];
    
    $(function () { 
        
        $(".ts").html(list.length);
        if(list.length<=12){
            $(".gd").hide();
        }else{
            $(".yc").html(list.length - 12 )
        }
    })

    var height1 = height - 60
    if(height>40){
        $(".line").height(height);
    }
    var myDate = new Date;
    var year = myDate.getFullYear(); //获取当前年
    var mon = myDate.getMonth() + 1; //获取当前月
    var date = myDate.getDate(); //获取当前日
    var week = myDate.getDay();

    $("#time").html("日期：" + year + "年" + mon + "月" + date + "日");
    function dayin(){
        printdiv(div_print);
    }

    function printdiv(printpage)
    {
        var newstr = printpage.innerHTML;
        var oldstr = document.body.innerHTML;
        document.body.innerHTML =newstr;
        window.print();
        document.body.innerHTML=oldstr;
        return false;
    }
    var show = false;
    function gd(obj) {
        // let sum= $(obj).prev().find("tbody").find("tr");
        if(show){
            $(obj).prev().find("tbody").find("tr").each(function () {
                let index=$(this).index();
                if(index>11){
                    $(this).attr('class','hide');
                }
            })
            $(obj).html('剩余(<span class="yc"></span>条)');
            $(".yc").html(list.length - 12 )
            show=false;
        }else{
            $(obj).prev().find("tbody").find("tr").each(function () {
                $(this).attr('class','');
            })
            $(obj).html("隐藏");
            show=true;
        }
       
    }
</script>
</body>
</html>