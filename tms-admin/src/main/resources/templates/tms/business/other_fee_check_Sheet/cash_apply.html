<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('现金申请')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <style type="text/css">
        .imageBox2 {
            border: 2px transparent solid;
        }
        .imageBox2:hover {
            border-color: #dddddd;
        }
    </style>
</head>

<body>
<div class="form-content">
    <form id="form-payAppl-add" class="form-horizontal" novalidate="novalidate">
        <!--对账单id-->
        <input name="otherFeeCheckSheetId" type="hidden" th:value="${otherFeeCheckSheet.otherFeeCheckSheetId}">
        <!--对账单据号-->
        <input name="params[vbillno]" type="hidden" th:value="${otherFeeCheckSheet.vbillno}">
        <!--申请类型-->
        <input name="applyType" type="hidden" th:value="${applyType}">
        <!--客户简称-->
        <input name="custAbbr" type="hidden" th:value="${otherFeeCheckSheet.custAbbr}">
        <!--客户id-->
        <input name="customerId" type="hidden" th:value="${otherFeeCheckSheet.customerId}">

        <div class="panel panel-default" th:if="${ maxApplyAmount > 0}">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseOne">基本信息</a>
                </h4>
            </div>
            <div class="panel-group" id="accordion" >
                <div class="panel-default">
                    <div id="collapseOne" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <!--基础信息 begin-->
                            <div class="row">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4" style="color: red">申请金额：</label>
                                        <div class="col-sm-8">
                                            <input name="applyPayAmount" id="applyPayAmount" type="text"
                                                   oninput="$.numberUtil.onlyNumberTwoDecimal(this)" required  maxlength="15"
                                                   th:value="${maxApplyAmount}"
                                                   class="form-control" >
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4" style="color: red">申请日期：</label>
                                        <div class="col-sm-8">
                                            <input name="applyPayDate" id="applyPayDate" class="form-control" readonly required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-3 col-sm-6" th:if="${applyType == 0}">
                                    <div class="form-group">
                                        <label class="col-sm-4" style="color: red">收款银行：</label>
                                        <div class="col-sm-8">
                                            <input type="text" name="recBank" id="recBank" maxlength="50" required class="form-control">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-3" th:if="${applyType == 0}">
                                    <div class="form-group">
                                        <label class="col-sm-4" style="color: red">收款人：</label>
                                        <div class="col-sm-8">
                                            <input type="text" name="recAccount" id="recAccount" maxlength="25" required class="form-control">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-3" th:if="${applyType == 0}">
                                    <div class="form-group">
                                        <label class="col-sm-4" style="color: red">收款账号：
                                        </label>
                                        <div class="col-sm-8">
                                            <input type="text" name="recCardNo" id="recCardNo" maxlength="25" required class="form-control">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-6" th:if="${applyType == 1}">
                                    <div class="form-group">
                                        <label class="col-sm-4" style="color: red">油卡卡号：</label>
                                        <div class="col-sm-8">
                                            <input name="oilcard" id="oilcard" class="form-control" required>
                                            <input name="fuelcardId" id="fuelcardId" class="form-control" type="hidden">
                                        </div>
                                    </div>
                                </div>


                            </div>


                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-md-1 col-sm-2">备注：</label>
                                        <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" maxlength="500" class="form-control valid"
                                                      rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-md-1 col-sm-2">附件：</label>
                                        <div class="col-md-11 col-sm-10">
                                            <input name="fileId"
                                                   id="fileId"
                                                   class="form-control" type="file" multiple>
                                            <input type="hidden" id="tid" name="tid">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-md-1 col-sm-2">含税构成：</label>
                                        <div class="col-md-11 col-sm-10">
                                            <span id="sumTaxTxt"></span>
                                            <a href="javascript:adjustTax()" style="font-weight: bold;cursor: pointer;margin-left: 10px;" th:if="${receiptCheck?.status != 1}">
                                                <i class="fa fa-sliders"></i> 调整税额占比
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-md-1 col-sm-2">上传发票：</label>
                                        <div class="col-md-11 col-sm-10">
                                            <input id="receipt" class="form-control" name="receipt" type="file" multiple>
                                            <input id="tid_receipt" name="tidReceipt" type="hidden">
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseThree">申请记录</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseThree">
                <div class="panel-body">
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabThree" class="custom-tab table" >

                            <thead>
                            <tr>
                                <th >申请单据号</th>
                                <th >申请金额</th>
                                <th th:if="${applyType == 1}">油卡卡号</th>
                                <th >收款账户</th>
                                <th >收款银行</th>
                                <th >收款卡号</th>
                                <th >申请日期</th>
                                <th>备注</th>
                            </tr>

                            </thead>
                            <tbody>

                            <tr th:each="otherFeeSheetRecord:${otherFeeSheetRecordList}">
                                <td th:text="${otherFeeSheetRecord.vbillno}" style="text-align: left"></td>
                                <td th:text="￥+${#numbers.formatDecimal(otherFeeSheetRecord.applyPayAmount,1,'COMMA',2,'POINT')}" style="text-align: right"></td>
                                <td th:text="${otherFeeSheetRecord.oilcard}" style="text-align: left" th:if="${applyType == 1}"></td>
                                <td th:text="${otherFeeSheetRecord.recAccount}" style="text-align: left"></td>
                                <td th:text="${otherFeeSheetRecord.recBank}" style="text-align: left"></td>
                                <td th:text="${otherFeeSheetRecord.recCardNo}" style="text-align: left"></td>
                                <td th:text="${#dates.format(otherFeeSheetRecord.applyPayDate, 'yyyy-MM-dd HH:mm:ss')}" style="text-align: left"></td>
                                <td th:text="${otherFeeSheetRecord.memo}" style="text-align: left"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>

        <div class="panel panel-default" th:if="${applyType == 0}">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFive">已上传发票</a>
                    <a href="javascript:;" onclick="showDelCheckBox(this)" th:if="${receiptList.size() > 0 && receiptCheck?.status != 1}"
                       del-btn style="font-weight: bold;cursor: pointer;margin-left: 5px;color:rgb(64,158,255)">编辑</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseFive">
                <div class="panel-body">
                    <div th:if="${receiptList.size() > 0}">
                        <span th:each="receipt: ${receiptList}" class="imageBox2" style="display: inline-block;margin:0 5px 5px 0;" deleter>
                            <input type="checkbox" del-btn style="display: none" th:file-id="${receipt.fileId}" th:tid="${receipt.tid}">
                            <img style="height:50px;" modal="zoomImg" th:src="${receipt.filePath}"/>
                        </span>
                        <div style="float:right;">
                            <a href="javascript:;" del-btn style="display: none;font-weight: bold" onclick="delImg()">删除勾选</a>
                            <br>
                            <span id="delImgInfo" style="color:rgb(64,158,255)"></span>
                        </div>
                    </div>
                    <div th:if="${receiptList.size() == 0}"><span style="margin:0 5px 5px 0">无</span></div>
                </div>
            </div>
        </div>

        <div class="panel panel-default" th:if="${maxApplyAmount == 0 && applyType == 0}"><!-- 全部申请完了再显示 -->
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFour">税票调整</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseFour">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-md-1 col-sm-2">税额占比：</label>
                                <div class="col-md-11 col-sm-10">
                                    <span id="sumTaxTxt"></span>
                                    <a href="javascript:adjustTax()" style="font-weight: bold;cursor: pointer;margin-left: 10px;" th:if="${receiptCheck?.status != 1}">
                                        <i class="fa fa-sliders"></i> 调整税额占比
                                    </a>
                                </div>
                            </div>
                            <div class="form-group" th:if="${receiptCheck?.status != 1}">
                                <label class="col-md-1 col-sm-2">追加发票：</label>
                                <div class="col-md-11 col-sm-10">
                                    <input type="file" name="receipt2" id="receipt2" multiple>
                                    <input name="tidReceipt" id="tid_receipt2" type="hidden" />
                                </div>
                            </div>
                            <div class="form-group" th:unless="${receiptCheck?.status != 1}">
                                <label class="col-md-1 col-sm-2"></label><div class="col-md-11 col-sm-10"><span style="color: lightcoral;font-weight: bold">发票已确认</span></div>
                            </div>
                        </div>
                        <div style="margin-bottom: 5px;text-align: center">
                            <input type="button" class="btn btn-sm btn-success" value="提交税票调整" onclick="saveTaxReceipt()" th:if="${receiptCheck?.status != 1}">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <input type="hidden" name="taxSplitJson">
        <input type="hidden" name="deletedImageJson">
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary"  onclick="submitHandler()" th:if="${maxApplyAmount > 0}"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger"  onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "business/otherFeeCheckSheet";

    //最大申请金额
    var maxApplyAmount = [[${ maxApplyAmount }]]

    //默认当日日期
    var time = new Date();
    var day = ("0" + time.getDate()).slice(-2);
    var month = ("0" + (time.getMonth() + 1)).slice(-2);
    var h=time.getHours();       //获取当前小时数(0-23)
    var m=time.getMinutes();     //获取当前分钟数(0-59)
    var s=time.getSeconds();     //获取当前秒数
    var today = time.getFullYear() + "-" + (month) + "-" + (day) + " "+h+":" +m+ ":"+s;
    $("#applyPayDate").val(today);

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseThree').collapse('show');

        /** 校验 */
        $("#form-payAppl-add").validate({
            focusCleanup: true
        });

        //图片功能初始化
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("fileId", "tid", picParam);
        $('#fileId').on('filesuccessremove', function(event, key, index) {
            $('#fileId').fileinput('clear');
            $('#tid').val('');
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#applyPayDate',
                type: 'datetime',
                trigger: 'click'
            });
        });

        //油卡添加校验
        $("#oilcard").rules("add", {
            remote: {
                url: ctx + "basic/fuelCard/getOilCardNumber",
                type: "post",
                dataType: "json",
                data: {
                    fuelcardNo : function() {
                        return $.common.trim($("#oilcard").val());
                    }
                },
                dataFilter: function(data, type) {
                    if (data != '') {
                        $("#fuelcardId").val(data);
                        return true;
                    } else {
                        return false;
                    }
                }
            },
            messages: {
                remote: "无效油卡",
            }
        });


        $.file.initAddFiles("receipt", "tid_receipt", {
            maxFileCount:0,
            publish: "cmt2",
            fileType: "image"
        });
        $('#receipt').on('filesuccessremove', function(event, key, index) {
            $('#receipt').fileinput('clear');
            $('#tid_receipt').val('');
        });


        /*[# th:if="${maxApplyAmount == 0 && applyType == 0}"]*/
        $.file.initAddFiles("receipt2", "tid_receipt2", {
            maxFileCount:0,
            fileType: null,
        });
        $("#receipt2").on('filebatchuploadsuccess', function (event, data) {
            $.operate.saveTab(prefix + "/saveTaxReceipt", $('#form-payAppl-add').serialize());
        });
        $('#receipt2').on('filesuccessremove', function(event, key, index) {
            $('#receipt2').fileinput('clear');
            $('#tid_receipt2').val('');
        });
        /*[/]*/

        translateTaxTxt();

    });


    //提交
    function submitHandler() {
        $("[name=taxSplitJson]").val(adjustCache)
        $("[name=deletedImageJson]").val(JSON.stringify(deletedImage))
        //填入的申请金额
        var applyPayAmount = parseFloat($("#applyPayAmount").val());
        if(applyPayAmount > maxApplyAmount){
            $.modal.alertWarning("超出最大申请金额：￥"+maxApplyAmount);
            return ;
        }
        var fuelcardId = $("#fuelcardId").val();
        if(fuelcardId == ''){
            $.modal.alertWarning("无效油卡");
            return ;
        }
        if ($.validate.form()) {
            jQuery.unsubscribe("cmt")
            jQuery.unsubscribe("cmt2")
            if ($('#fileId').fileinput('getFilesCount') > 0) {
                jQuery.subscribe("cmt", function(){
                    if ($('#receipt').fileinput('getFilesCount') > 0) {
                        jQuery.subscribe("cmt2", commitForm);
                        $('#receipt').fileinput('upload');
                    } else {
                        commitForm()
                    }
                });
                $('#fileId').fileinput('upload');
            } else if ($('#receipt').fileinput('getFilesCount') > 0) {
                jQuery.subscribe("cmt2", commitForm);
                $('#receipt').fileinput('upload');
            } else {
                commitForm()
            }
        }
    }

    /**
     * 表单提交
     */
    function commitForm() {
        if ($.validate.form()) {
            var dis = $(":disabled");
            dis.attr("disabled", false);
            $.operate.saveTab(prefix + "/saveApply", $('#form-payAppl-add').serialize(),function (result) {
                if (result.code != 0) {
                    dis.attr("disabled", true);
                }
            });
        }
    }

    var billingType = [[${@dict.getType('billing_type')}]];//开票类型
    var sumTaxTxt = [[${sumTaxTxt}]];
    function translateTaxTxt() {
        if (sumTaxTxt) {
            var result = [];
            result.push("[")
            var sumTaxTxtArr = sumTaxTxt.split(",");
            for (let i = 0; i < sumTaxTxtArr.length; i++) {
                if (i > 0) {
                    result.push("] [");
                }
                var type_amount = sumTaxTxtArr[i].split(":");
                for (let j = 0; j < billingType.length; j++) {
                    if (type_amount[0] == billingType[j].dictValue) {
                        result.push(billingType[j].dictLabel,":",type_amount[1]);
                        break;
                    }
                }
            }
            result.push("]")
            $("#sumTaxTxt").text(result.join(""))
        } else {
            $("#sumTaxTxt").html("<span style='color: #aaa'>未配置时默认均不开票</span>")
        }
    }

    var adjustCache = null;
    function adjustTax() {
        $.modal.openFull("调整税额占比", prefix + "/detailTax/[(${otherFeeCheckSheet.otherFeeCheckSheetId})]")
    }

    function sumTaxTxtChange(sumTaxTxt) {
        if ($("#sumTaxTxt").text() != sumTaxTxt) {
            $.modal.msgSuccess("税额占比已暂存")
            $("#sumTaxTxt").html(sumTaxTxt + "<span style='color:blue'>（暂存，提交后生效）</span>")
        }
    }

    function saveTaxReceipt() {
        if (deletedImage.length == 0 && !adjustCache && $('#receipt2').fileinput('getFilesCount') == 0) {
            $.modal.msgWarning("税额占比和发票至少要提交一项");
            return;
        }
        $.modal.confirm("确认提交吗?", function() {
            $("[name=taxSplitJson]").val(adjustCache)
            $("[name=deletedImageJson]").val(JSON.stringify(deletedImage))
            if($('#receipt2').fileinput('getFilesCount') == 0) {
                $.operate.saveTab(prefix + "/saveTaxReceipt", $('#form-payAppl-add').serialize());
            } else {
                // 表单提交
                $.modal.loading("正在处理中，请稍后...");
                $("#receipt2").fileinput('upload');
            }
        });
    }

    function showDelCheckBox(a) {
        $("[del-btn]").toggle()
    }

    var deletedImage = []

    function delImg() {
        if ($(":checked[del-btn]").length == 0) {
            $.modal.msgWarning("尚未选择要删除的发票")
        } else {
            $.modal.confirm("确认删除吗？", function(){
                $(":checked[del-btn]").each(function (i) {
                    deletedImage.push({fileId:$(this).attr("file-id"), tid:$(this).attr("tid")})
                    $(this).parent().remove()
                })
                $("#delImgInfo").text("已删除" + deletedImage.length + "张图片，提交后生效")
                $("[del-btn]").toggle()
            })
        }
    }
</script>
</body>
</html>