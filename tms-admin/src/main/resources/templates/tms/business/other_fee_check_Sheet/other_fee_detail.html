<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('业务中心-第三方对账-第三方费用明细')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input th:type="hidden" name="otherFeeCheckSheetId" th:value="${otherFeeCheckSheetId}">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">第三方费用单据号：</label>
                            <div class="col-sm-8">
                                <input name="vbillno" placeholder="请输入第三方费用单据号" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <input id="hiddenText" type="text" style="display:none" />
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">发货单号：</label>
                            <div class="col-sm-8">
                                <input name="invoiceVbillno" placeholder="发货单号" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">

                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger single disabled" th:if="${vbillstatus == 0}" onclick="remove()"
               shiro:hasPermission="tms:otherFeeCheckSheet:delete">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "business/otherFeeCheckSheet";

    //付款方式
    var payMethod = [[${@dict.getType('pay_method')}]];
    //费用类型
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    //结算公司
    var balaCorp = [[${@dict.getType('bala_corp')}]];
    //第三方费用 状态
    var otherFeeStatusList = [[${otherFeeStatusList}]];
    //第三方费用状态 map
    var otherFeeStatusMap = [[${otherFeeStatusMap}]];
    //第三方费用 已对账状态
    var otherFeeStatusReconciled = [[${otherFeeStatusReconciled}]];
    //第三方对账id
    var otherFeeCheckSheetId = [[${otherFeeCheckSheetId}]];


    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });
        });

        var options = {
            url: prefix + "/otherFeeDetailList",
            removeUrl: prefix + "/delete",
            exportUrl: prefix + "/exportDetail",
            showToggle:false,
            showRefresh: true,
            showColumns:false,
            clickToSelect:true,
            showSearch: true,
            pagination: true,
            showColumns:true,
            modalName: "第三方费用",
            uniqueId: "otherFeeId",
            columns: [{
                checkbox: true
            },
                {
                    title: '第三方费用单据号',
                    align: 'left',
                    field: 'vbillno'
                },
                {
                    title: '状态',
                    align: 'left',
                    field: 'vbillstatus',
                    formatter: function status(value,row) {
                        var context = "";
                        otherFeeStatusList.forEach(function (v) {
                            if (v.value == row.vbillstatus) {
                                if (row.vbillstatus == otherFeeStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-default">'+v.context+'</span>';
                                }else if (row.vbillstatus == otherFeeStatusMap.ALREADY_PAID) {
                                    //已付款
                                    context = '<span class="label label-success">'+v.context+'</span>';
                                }else if(row.vbillstatus == otherFeeStatusMap.APPLY_FOR){
                                    //已申请
                                    context = '<span class="label label-primary">'+v.context+'</span>';
                                }else if(row.vbillstatus == otherFeeStatusMap.RECONCILED){
                                    //已对账
                                    context = '<span class="label label-coral">'+v.context+'</span>';
                                }else if(row.vbillstatus == otherFeeStatusMap.PART_ALREADY_PAID){
                                    //已对账
                                    context = '<span class="label label-info">'+v.context+'</span>';
                                }
                                return false;
                            }
                        });
                        return context;
                    }
                },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceVbillno'
                },
                {
                    title: '客户简称',
                    align: 'left',
                    field: 'custAbbr'
                },
                {
                    title: '要求提货日期',
                    align: 'left',
                    field: 'reqDeliDate'
                },
                {
                    title: '提货省市区',
                    align: 'left',
                    field: 'deliAddress'
                },
                {
                    title: '到货省市区',
                    align: 'left',
                    field: 'arriAddress'
                },
                {
                    title: '运营组',
                    align: 'left',
                    field: 'deptName'
                },
                // {
                //     title: '审核状态',
                //     align: 'left',
                //     field: 'checkStatus',
                //     formatter: function(value, item, index) {
                //         if (item.checkStatus == 0) {
                //             return '<span class="label label-default">待审核</span>';
                //         }
                //         if (item.checkStatus == 1) {
                //             return '<span class="label label-primary">审核通过</span>';
                //         }
                //         if (item.checkStatus == 2) {
                //             return '<span class="label label-danger">审核未通过</span>';
                //         }
                //     }
                //
                // },
                // {
                //     title: '客户简称',
                //     align: 'left',
                //     field: 'custAbbr'
                // },
                // {
                //     title: '发货单号',
                //     align: 'left',
                //     field: 'invoiceVbillno'
                // },
                // {
                //     title: '要求提货日',
                //     align: 'left',
                //     field: 'reqDeliDate'
                // },
                // {
                //     title: '提货|到货省市区',
                //     field: 'arriDetailAddress',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         return row.deliDetailAddress+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriDetailAddress;
                //     }
                // },
                {
                    title: '费用类型',
                    align: 'left',
                    field: 'feeType',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(costTypeOnWay, value.feeType);
                    }
                },
                {
                    title: '金额',
                    align: 'right',
                    field: 'feeAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '已付金额',
                    align: 'right',
                    field: 'gotAmount',
                    halign: "left",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '付款类型',
                    align: 'left',
                    field: 'payType',
                    formatter: function status(value,row) {
                        if(value == 0){
                            return '现金'
                        };
                        if(value == 1){
                            return '油卡'
                        };
                    }
                },
                {
                    title: '付款方式',
                    align: 'left',
                    field: 'payMethod',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(payMethod, value.payMethod);
                    }
                },
                {
                    title: '结算公司',
                    align: 'left',
                    field: 'balaCorpId',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(balaCorp, value.balaCorpId);
                    }
                },
                {
                    title: '收款人',
                    align: 'left',
                    field: 'recAccount',
                },
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'recBank',
                    formatter: function status(value,row) {
                        return $.table.tooltip(row.recBank);
                    }
                },
                {
                    title: '收款账号',
                    align: 'left',
                    field: 'recCardNo',
                    formatter: function status(value,row) {
                        return $.table.tooltip(row.recCardNo);
                    }
                },
                {
                    title: '总件数',
                    align: 'left',
                    field: 'numCount',
                },
                {
                    title: '总重量',
                    align: 'left',
                    field: 'weightCount',
                },
                {
                    title: '总数量',
                    align: 'left',
                    field: 'volumeCount',
                },
                {
                    title: '单价',
                    align: 'left',
                    field: 'price',
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate',
                },
                {
                    title: '申请人',
                    align: 'left',
                    field: 'applyUserId',
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'applyDate',
                },
                {
                    title: '支付人',
                    align: 'left',
                    field: 'payUser',
                },
                {
                    title: '支付时间',
                    align: 'left',
                    field: 'payDate',
                },
                {
                    title: '财务退回原因',
                    field: 'backMemo',
                    align: 'left',
                },

            ]
        };

        $.table.init(options);
    });

    /**
     * 删除
     */
    function remove() {
        //第三方费用id
        var otherFeeId = $.table.selectColumns("otherFeeId").join();
        //第三方状态
        var vbillstatus = $.table.selectColumns("vbillstatus");
        if(vbillstatus != otherFeeStatusReconciled){
            $.modal.alertWarning("只有已对账状态下才能删除！");
            return;
        }
        var url = prefix + "/delete"
        $.modal.confirm("确认要删除选中的数据吗?", function() {
            var url = $.table._option.removeUrl;
            var data = { "otherFeeId": otherFeeId,"otherFeeCheckSheetId":otherFeeCheckSheetId};
            $.operate.submit(url, "post", "json", data);
        });
    }











</script>
</body>
</html>