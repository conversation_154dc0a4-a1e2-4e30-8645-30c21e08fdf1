<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方费用对账')"/>
</head>
<style>
    .table-striped{
        height: calc(100% - 80px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-5">对账单据号：</label>-->
                            <div class="col-sm-12">
                                <input type="text" name="vbillno" class="form-control" th:value="${vbillno}" placeholder="对账单据号">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-5">客户简称：</label>-->
                            <div class="col-sm-12">
                                <input type="text" name="custAbbr" class="form-control" placeholder="客户简称">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <!--                            <label class="col-sm-5">客户简称：</label>-->
                            <div class="col-sm-12">
                                <select name="vbillstatus" class="form-control valid" aria-invalid="false">
                                    <option value="">-- 对账状态 --</option>
                                    <option value="0" >新建</option>
                                    <option value="1" >已确认</option>
                                    <option value="2" >部分申请</option>
                                    <option value="3" >已申请</option>
                                    <option value="4" >已付款</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div  class="col-md-3  col-sm-6 ">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>



            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning multiple disabled" onclick="affirm()" shiro:hasPermission="tms:otherFeeCheckSheet:affirm">
                <i class="fa fa-check-circle-o"></i> 确认
            </a>
            <a class="btn btn-primary single disabled" onclick="cashApply()" shiro:hasPermission="tms:otherFeeCheckSheet:apply">
                <i class="fa fa-plus"></i> 现金申请
            </a>
            <a class="btn btn-primary single disabled" onclick="oilApply()" shiro:hasPermission="tms:otherFeeCheckSheet:apply">
                <i class="fa fa-plus"></i> 油卡申请
            </a>
            <a class="btn btn-primary single disabled" onclick="checkRecord()" shiro:hasPermission="tms:otherFeeCheckSheet:checkRecord">
                <i class="fa fa-calculator"></i> 审核记录
            </a>
            <a class="btn btn-danger multiple disabled" onclick="reverse()" shiro:hasPermission="finance:payCheckSheet:backAffirm">
                <i class="fa fa-mail-reply"></i> 反确认
            </a>
            <a class="btn btn-danger single disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="finance:payCheckSheet:backAffirm:delete">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"  class="text-nowrap" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "business/otherFeeCheckSheet";

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });

        var options = {
            url: prefix + "/list",
            removeUrl: prefix + "/remove",
            showSearch: true,
            showRefresh: true,
            pagination: true,
            showToggle:false,
            showColumns:true,
            showFooter:true,
            modalName: "第三方费用对账",
            clickToSelect:true,
            height: 560,
            fixedColumns: true,
            fixedNumber: 7,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'center',
                    field : 'otherFeeCheckSheetId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:otherFeeCheckSheet:otherFeeDetailList')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="第三方费用明细" onclick="otherFeeDetail(\''+row.otherFeeCheckSheetId+'\',\''+row.vbillstatus+'\')"><i  class="fa fa fa-list" style="font-size: 15px;" ></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:otherFeeCheckSheet:oilAdjust')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="油卡比例调整" onclick="oilAdjust(\'' + row.otherFeeCheckSheetId + '\',\''+ row.vbillstatus+'\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                        }
                        if(row.vbillstatus == 2 || row.vbillstatus == 3){
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="打印" onclick="prints(\'' + row.otherFeeCheckSheetId + '\')"><i class="fa fa-print" style="font-size: 15px;"></i></a> ');
                        }
                        return actions.join('');
                    }
                },
                {
                    field : 'vbillno',
                    title : '单据号',
                },
                {
                    title: '对账状态',
                    align: 'left',
                    field: 'vbillstatus',
                    formatter: function status(value,row) {
                        if (value == 0) {
                            return '<span class="label label-default">新建</span>';
                        };
                        if (value == 1) {
                            return '<span class="label label-warning">已确认</span>';
                        };
                        if (value == 2) {
                            return '<span class="label label-info">部分申请</span>';
                        };
                        if (value == 3) {
                            return '<span class="label label-primary">已申请</span>';
                        };
                        if (value == 4) {
                            return '<span class="label label-success">已付款</span>';
                        };

                    }
                },
                {
                    field : 'custAbbr',
                    title : '客户简称',
                },
                {
                    field : 'year',
                    title : '对账年',
                },
                {
                    field : 'month',
                    title : '对账月',
                },
                {
                    field : 'otherFeeCheckSheetName',
                    title : '第三方费用对账单名称',
                },
                {
                    field : 'totalAmount',
                    title : '总金额（元）',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                // {
                //     field : 'gotAmount',
                //     title : '已付金额',
                // },
                // {
                //     field : 'ungotAmount',
                //     title : '未付金额',
                // },
                {
                    field : 'oilAmount',
                    title : '油卡金额（元）',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    field : 'oilAmount',
                    title : '现金金额（元）',
                    align: 'right',
                    formatter: function status(value,row) {
                        var cashAmount = row.totalAmount - row.oilAmount;
                        return cashAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    field : 'applicationAmountOil',
                    title : '申请金额(油卡)',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    field : 'applicationAmountCash',
                    title : '申请金额(现金)',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    field : 'price',
                    title : '单价',
                },
                {
                    field : 'numCount',
                    title : '合计件数',
                },
                {
                    field : 'weightCount',
                    title : '合计重量',
                },
                {
                    field : 'volumeCount',
                    title : '合计数量',
                },
                {
                    field : 'unconfirmMemo',
                    title : '反确认说明',
                },
                {
                    field : 'memo',
                    title : '备注',
                },
                {
                    field : 'backMemo',
                    title : '退回说明',
                }
            ]
        };

        $.table.init(options);
    });

    /**
     * 第三方费用明细
     * @param otherFeeCheckSheetId 第三方对账id
     */
    function otherFeeDetail(otherFeeCheckSheetId,vbillstatus) {
        var url = prefix + "/otherFeeDetail?otherFeeCheckSheetId="+otherFeeCheckSheetId+"&vbillstatus="+vbillstatus;
        $.modal.openTab("第三方费用明细",url);
    }

    /**
     * 确认第三方对账
     */
    function affirm() {
        // 选中的行
        var rows = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < rows.length; i++) {
            if (rows[i]["vbillstatus"] !== 0) {
                $.modal.alertWarning("对账状态为新建才能进行确认");
                return;
            }
        }
        var otherFeeCheckSheetId = $.table.selectColumns("otherFeeCheckSheetId").join();
        if (otherFeeCheckSheetId.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("是否确认？", function () {
            $.operate.post(prefix + "/affirm", {"otherFeeCheckSheetIds": otherFeeCheckSheetId});
        });
    }

    /**
     * 反确认
     */
    function reverse() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("对账单状态为已确认才能进行反确认");
                return;
            }
        }
        var otherFeeCheckSheetId = $.table.selectColumns("otherFeeCheckSheetId").join();
        if (otherFeeCheckSheetId.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.open("反确认", prefix + "/back_confirm/" + otherFeeCheckSheetId,500,300);
    }


    /**
     * 油卡调整
     * @param otherFeeCheckSheetId
     */
    function oilAdjust(otherFeeCheckSheetId,vbillstatus){
        if (vbillstatus != 0) {
            $.modal.alertWarning("新建状态才能进行油卡调整");
            return;
        }
        var url = prefix + "/oilAdjust?otherFeeCheckSheetId="+otherFeeCheckSheetId;
        $.modal.openTab("油卡调整",url);
    }

    /**
     * 现金申请
     */
    function cashApply() {
        // 对账id
        var otherFeeCheckSheetId = $.table.selectColumns("otherFeeCheckSheetId");
        // 状态
        var vbillstatus = $.table.selectColumns('vbillstatus');
        if (vbillstatus == 0) {
            $.modal.alertWarning("新建状态不能进行现金申请");
            return;
        }
        var url = prefix + "/cash_apply?otherFeeCheckSheetId=" + otherFeeCheckSheetId + "&applyType=" + 0;
        $.modal.openTab("现金申请", url);
    }

    /**
     * 油卡申请
     */
    function oilApply(){
        // 对账id
        var otherFeeCheckSheetId = $.table.selectColumns("otherFeeCheckSheetId");
        // 状态
        var vbillstatus = $.table.selectColumns('vbillstatus');
        if (vbillstatus == 0) {
            $.modal.alertWarning("新建状态不能进行油卡申请");
            return;
        }
        var url = prefix + "/cash_apply?otherFeeCheckSheetId=" + otherFeeCheckSheetId + "&applyType=" + 1;
        $.modal.openTab("油卡申请", url);
    }

    /**
     * 审核记录
     */
    function checkRecord(){
        // 对账id
        var otherFeeCheckSheetId = $.table.selectColumns("otherFeeCheckSheetId");
        var url = prefix + "/check_record?otherFeeCheckSheetId=" + otherFeeCheckSheetId;
        $.modal.openTab("审核记录", url);

    }
    function prints(otherFeeCheckSheetId){
        var url =  prefix + "/printOtherFeeCheckSheet?otherFeeCheckSheetId="+otherFeeCheckSheetId;
        $.modal.openTab('第三方对账打印',url);
    }

</script>

</body>

</html>