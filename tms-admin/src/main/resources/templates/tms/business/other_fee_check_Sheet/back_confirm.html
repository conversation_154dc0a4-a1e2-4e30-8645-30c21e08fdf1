<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反确认')"/>
</head>

<body>
<div class="form-content">
    <form id="form-receive-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input  name="otherFeeCheckSheetId" type="hidden" th:value="${otherFeeCheckSheetId}">
            <div class="panel-body">
                <!--基础信息 begin-->
                <input name="deptId" type="hidden" id="treeId">
                <div class="row">
                   <!-- <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">撤销类型：</label>
                            <div class="col-sm-7">
                                <select name="unconfirmType" id="unconfirmType" th:with="type=${@dict.getType('back_confirm_type')}" class="form-control valid" aria-invalid="false">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>-->
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5" style="color: red">反确认说明：</label>
                            <div class="col-sm-12">
                                <textarea name="unconfirmMemo" id="unconfirmMemo" class="form-control"  type="text"
                                          maxlength="500" required="" aria-required="true"></textarea>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "business/otherFeeCheckSheet";
    /**
     * 校验
     */
    $("#form-receive-unconfirm").validate({
        onkeyup: false,
        focusCleanup: true,
    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/save_back_confirm", $("#form-receive-unconfirm").serializeArray());
        }
    }




</script>
</body>
</html>