<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('第三方对账-审核记录')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <form id="role-form" class="form-horizontal">
            <input type="hidden" name="businessId"  th:value="${otherFeeId}">
        </form>
        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var prefix = ctx + "business/otherFeeCheckSheet";
    var otherFeeCheckSheetId = [[${otherFeeCheckSheetId}]];

    $(function () {
        var options = {
            url: prefix + "/check_record_list?otherFeeCheckSheetId="+otherFeeCheckSheetId,
            showToggle:false,
            showColumns:false,
            showSearch: false,
            showRefresh:false,
            modalName: "审核记录",
            fixedColumns: true,
            clickToSelect:true,
            columns: [
                {
                    title: '申请单据号',
                    field: 'vbillno',
                    align: 'left',
                },
                {
                    title: '申请类型',
                    align: 'left',
                    field: 'applyType',
                    formatter: function (value, row, index) {
                        return value == 0 ? '现金':'油卡'
                    }
                },
                {
                    title: '审核状态',
                    align: 'left',
                    field: 'checkStatus',
                    formatter: function(value, item, index) {
                        if (item.checkStatus == 0) {
                            return '<span class="label label-default">待审核</span>';
                        }
                        if (item.checkStatus == 1) {
                            return '<span class="label label-primary">审核通过</span>';
                        }
                        if (item.checkStatus == 2) {
                            return '<span class="label label-danger">业务审核未通过</span>';
                        }
                        if (item.checkStatus == 3) {
                            return '<span class="label label-danger">财务审核未通过</span>';
                        }
                    }

                },

                {
                    title: '审核人',
                    field: 'regUserId',
                    align: 'left',
                },

                {
                    title: '审核时间',
                    field: 'regDate',
                    align: 'left',
                },
                {
                    title: '备注',
                    field: 'memo',
                    align: 'left',
                },
            ]
        };

        $.table.init(options);
    });

</script>

</body>
</html>