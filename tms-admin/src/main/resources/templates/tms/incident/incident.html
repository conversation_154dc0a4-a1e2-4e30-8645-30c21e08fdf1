<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('异常事件列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">异常单号：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">承运商：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">异常单据状态：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">发货单号：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">员工异常：</label>
                            <div class="col-sm-7">
                                <select name="" class="form-control valid" aria-invalid="false">
                                    <option value="1">是</option>
                                    <option value="2">否</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">承运索赔完毕：</label>
                            <div class="col-sm-7">
                                <select name="" class="form-control valid" aria-invalid="false">
                                    <option value="1">是</option>
                                    <option value="2">否</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">客户终审：</label>
                            <div class="col-sm-7">
                                <select name="" class="form-control valid" aria-invalid="false">
                                    <option value="1">是</option>
                                    <option value="2">否</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">承运商终审：</label>
                            <div class="col-sm-7">
                                <select name="" class="form-control valid" aria-invalid="false">
                                    <option value="1">是</option>
                                    <option value="2">否</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">客户索赔完毕：</label>
                            <div class="col-sm-7">
                                <select name="" class="form-control valid" aria-invalid="false">
                                    <option value="1">是</option>
                                    <option value="2">否</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">创建人：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">反馈人：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">描述：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">创建开始日期：</label>
                            <div class="col-sm-7">
                                <input type="text" class="time-input form-control" id="startTime"
                                       placeholder="" name="params[startTime]">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">创建结束日期：</label>
                            <div class="col-sm-7">
                                <input type="text" class="time-input form-control" id="endTime"
                                       placeholder="" name="params[endTime]" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">反馈开始日期：</label>
                            <div class="col-sm-7">
                                <input type="text" class="time-input form-control" id="sTime"
                                       placeholder="" name="params[sTime]">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">反馈结束日期：</label>
                            <div class="col-sm-7">
                                <input type="text" class="time-input form-control" id="eTime"
                                       placeholder="" name="params[eTime]">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">委托单号：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" readonly class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">客户：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">公司：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">类型：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">原因类型：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">接单开始日期：</label>
                            <div class="col-sm-7">
                                <input type="text" class="time-input form-control" id="jsTime"
                                       placeholder="" name="params[jsTime]">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">接单结束日期：</label>
                            <div class="col-sm-7">
                                <input type="text" class="time-input form-control" id="jeTime"
                                       placeholder="" name="params[jeTime]" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">司机：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">客户订单号：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">承运担号：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">车牌号：</label>
                            <div class="col-sm-7">
                                <input name="" placeholder="" class="form-control" type="text"
                                       required="" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="$.operate.addTab()">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single ">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-primary multiple disabled">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-primary">
                <i class="fa fa-upload"></i> 导入
            </a>
            <a class="btn btn-primary">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-primary">
                <i class="fa fa-file"></i> 客诉
            </a>
            <a class="btn btn-primary">
                <i class="fa fa-file"></i> 处理
            </a>
            <a class="btn btn-primary">
                <i class="fa fa-file"></i> 客户索赔
            </a>
            <a class="btn btn-primary">
                <i class="fa fa-file"></i> 承运商罚款
            </a>
            <a class="btn btn-primary">
                <i class="fa fa-file"></i> 结案
            </a>
            <a class="btn btn-primary">
                <i class="fa fa-file"></i> 终审
            </a>

            <!-- Single button -->
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    下拉按钮 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="#">是</a></li>
                    <li><a href="#">否</a></li>
                </ul>
            </div>


        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "system/demo";

    var total = 0;

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            showToggle:false,
            showColumns:false,
            modalName: "异常信息",
            fixedColumns: true,
            rememberSelected: true,
            fixedNumber:1,
            showFooter:true,
            uniqueId: "test",
            columns: [{
                checkbox: true
            },
                {
                    title: '单据号',
                    filed: 'userName',
                    align: 'center'
                },
                {
                    title: '发货单号',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    },
                    footerFormatter: function () {
                        return "金额合计：<div id='total'></div>";
                    }
                },
                {
                    title: '客户订单号',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }

                },
                {
                    title: '委托单号',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '承运单号',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '状态',
                    filed: 'a6',
                    align: 'center',
                    formatter: function status(row,value) {

                        if(value.a6 =="测试") {
                            return '<span class="label label-primary">正常</span>';
                        }
                        if(value.a6 =="铭源") {
                            return '<span class="label label-danger">异常</span>';
                        }

                    }
                },
                {
                    title: '收货单位',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '最终收货地址',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '客诉',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '客户',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '承运商',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '司机',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '车牌号',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '初步责任判断',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '来源',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '异常类别',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '反馈日期',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '反馈人',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '发生日期',
                    filed: 'a1',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '发生地点',
                    width: '100px',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }

                },
                {
                    title: '描述',
                    width: '100px',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '承运商终审',
                    width: '100px',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '已生成罚',
                    width: '100px',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '承运商索赔',
                    width: '100px',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '索赔金额',
                    width: '100px',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                },
                {
                    title: '已赔金额',
                    width: '100px',
                    align: 'center',
                    formatter: function status(row,value) {

                        return value.a3;
                    }
                }

            ]
        };

        $.table.init(options);
        $.btTable.on('check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table', function (e, row, $element) {
            total = total + 100;
            $('#total').text(total)
            // alert(JSON.stringify(row));

        });
    });


</script>
</body>
</html>