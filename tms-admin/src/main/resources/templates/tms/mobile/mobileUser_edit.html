<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('资质更新')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-mobileUser-edit" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <input name="mobileUserId" id="mobileUserId" th:value="${mobileUser.mobileUserId}" type="hidden">
                        <input name="userId" id="userId" th:value="${mobileUser.userId}" type="hidden">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        姓名：</label>
                                    <div class="col-sm-7">
                                        <input name="userName" id="userName" class="form-control dis" type="text"
                                                th:value="${mobileUser.userName}">
                                        <input name="userType" id="userType" th:value="${mobileUser.userType}"
                                            type="hidden">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        营业执照：</label>
                                    <div class="col-sm-7">
                                        <input name="businessLicense" id="businessLicense" class="form-control dis" type="text"
                                               th:value="${mobileUser.businessLicense}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        身份证号码：</label>
                                    <div class="col-sm-7">
                                        <input name="cardId" id="cardId" class="form-control dis" type="text"
                                               th:value="${mobileUser.cardId}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">
                                        公司名称：</label>
                                    <div class="col-sm-7">
                                        <input name="companyName" id="companyName" class="form-control dis" type="text"
                                               th:value="${mobileUser.companyName}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6" >
                                <div class="form-group" id="userClassDiv">
                                    <label class="col-sm-5">
                                        用户性质：</label>
                                    <div class="col-sm-7">
                                        <select name="userClass" id="userClass" class="form-control valid"
                                                aria-invalid="false">
                                            <option th:each="mapS,status:${userClass}" th:value="${mapS.value}"
                                                    th:text="${mapS.context}" th:selected="${mobileUser.userClass+''==mapS.value+''}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">地址：</label>
                                    <div class="col-sm-7">
                                        <select name="provinceId" id="provinceId" class="form-control valid"
                                                aria-invalid="false"></select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-2">
                                <select name="cityId" id="cityId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-2">
                                <select name="areaId" id="areaId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-2">
                                <input name="address" id="address" placeholder="请输入详细地址" class="form-control"
                                       type="text" th:value="${mobileUser.address}"
                                       maxlength="50">
                            </div>

                        </div>
                        <div class="row">

                            <div class="col-sm-12">
                                <div class="form-group">
                                    <div class="col-sm-10">
                                        <div id="collapsePic" class="panel-collapse collapse in">
                                            <div class="panel-body" id="picType">
                                                <div class="row" th:each="dict : ${picList}" >
                                                    <div class="col-md-3 col-sm-6">
                                                        <div class="form-group">
                                                            <label class="col-sm-9" th:text="${dict.context}+'：'">
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-9 col-sm-6">
                                                        <div class="form-group">
                                                            <div class="col-sm-7">
                                                                <input th:id="'image'+${dict.value}" class="form-control"
                                                                       th:name="'image'+${dict.value}" type="file" >
                                                                <input th:id="'tid'+${dict.value}" th:name="'tid'+${dict.value}"
                                                                       type="hidden">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>


                        <!--基础信息 end-->
                    </div>
                </div>
            </div>


        </div>

    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "appUser";
    var picType = [[${picList}]];
    $(function () {

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');

        var options = {};
        $.table.init(options);

        var provinceId = [[${mobileUser.provinceId}]];
        var cityId = [[${mobileUser.cityId}]];
        var areaId = [[${mobileUser.areaId}]];
        // 初始化省市区
        $.provinces.init("provinceId","cityId","areaId",provinceId,cityId,areaId);

        var userType =  $("#userType").val();
        // 用户类型为承运商时 去除选择在用户性质框
        if (userType === '2'){
            var elem=document.getElementById('userClassDiv');
            elem.parentNode.removeChild(elem);
        }

        //获取TID数字
        var custPicList = [[${custPicList}]];
        for(var i=0 ; i<custPicList.length ; i++){
            var picType = custPicList[i]["picType"];
            $("#tid"+picType).val(custPicList[i]["appendixId"]);
        }
        //循环图片路径信息，初始化图片上传区域
        var imagePath = [[${dictMap}]];
        for (var key in imagePath) {
            var publishFlag = "done" + key;
            var param = {
                maxFileCount: 1,
                publish: "publishFlag",  //用于绑定下一步方法
                fileType: null //文件类型

            };
            var tid = "tid" + key;
            var imageId = "image" + key;
            $.file.loadEditFiles(imageId, tid, imagePath[key], param);
        }

    });

    /**
     * 校验
     */
    $("#form-mobileUser-edit").validate({
        onkeyup: false,
        rules:{
            phone:{
                isPhone:true,
                remote: {
                    url: ctx + "system/user/checkPhoneUnique",
                    type: "post",
                    dataType: "json",
                    data: {
                        "phonenumber": function () {
                            return $.common.trim($("#phone").val());
                        },
                        "userId": function() {
                            return $("#userId").val();
                        },
                        "userType": "1"
                    },
                    dataFilter: function (data, type) {
                        return $.validate.unique(data);
                    }
                }
            },
        },
        messages: {

            "phone":{
                remote: "手机号码已经存在"
            }
        },
        focusCleanup: true
    });


    //上传完成标志位
    var flag;
    /*提交表单*/
    function submitHandler() {
        if ($.validate.form()) {
            //提交表单flag置空
            flag = "";
            if (picType.length == 1) {
                var value = picType[i].value;
                //如果还没有上传图片，flag就是空的，直接上传第一个
                if ($("#image" + value).val() != "") {
                    $("#image" + value).fileinput('upload');
                    flag = "done" + value;
                    jQuery.subscribe(flag, setTimeout("commit()", "1000"));
                } else {
                    commit();
                }
            } else {
                //循环字典表图片类型
                for (var i = 0; i < picType.length; i++) {
                    var value = picType[i].value;
                    //如果还没有上传图片，flag就是空的，直接上传第一个
                    if (flag == "" && i >= 0 && i < picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                        } else {
                            continue;
                        }
                    }
                    //所有都为空，直接提交表单;如果只上传最后一个，前面都没有上传，直接上传并提交表单，设置延时等待上传完成，不然来不及回调
                    if (flag == "" && i == picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                            jQuery.subscribe(flag, setTimeout("commit()", "1000"));
                        } else {
                            commit();
                        }
                    }
                    //如果前面有上传，且input框不空，执行上传
                    if (flag != "" && i > 0 && i < picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            jQuery.subscribe(flag, uploadPic(value));
                        } else {
                            continue;
                        }
                    }
                    //判断最后一个是否为空，为空直接提交表单，不为空上传完提交表单
                    if (flag != "" && i == picType.length - 1) {
                        if ($("#image" + value).val() != "") {
                            $("#image" + value).fileinput('upload');
                            flag = "done" + value;
                            jQuery.subscribe(flag, setTimeout("commit()", "1000"));
                        } else {
                            jQuery.subscribe(flag, setTimeout("commit()", "1000"));
                        }
                    }
                }
            }
        }
    }

    function uploadPic(dictValue) {
        $("#image" + dictValue).fileinput('upload');
        flag = "done" + dictValue;
    }

    /**
     * 表单提交
     */
    function commit(){

        // 表单提交
        $.operate.saveTab(prefix + "/updateMobileUser", $('#form-mobileUser-edit').serialize());

    }
</script>
</body>

</html>