<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('客户选择页')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal">
					<div class="row">
						<div class="col-sm-6">
							<div class="form-group">
								<label class="col-sm-4">客户名称：</label>
								<div class="col-sm-8">
									<input name="" placeholder="请输入客户名称" class="form-control valid" type="text"
										   required="" aria-required="true">
									<input name="corDate" id="corDate" th:value="${corDate}" type="hidden">

								</div>
							</div>
						</div>
						<div class="col-sm-2">
							<div class="form-group">

							</div>
						</div>

						<div class="col-sm-4">
							<label class="col-sm-4"></label>
							<div class="form-group">
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>

				</form>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table id="bootstrap-table" data-mobile-responsive="true"></table>
			</div>
		</div>

	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">

		var prefix = ctx + "client";
		$(function () {
			var options = {
				url: prefix + "/list",
				showSearch: false,
				showRefresh: false,
				showToggle: false,
				showColumns: false,
				columns: [{
					radio: true
				},
					{
						title: 'id',
						field: 'customerId',
						visible: false
					},
					{
						title: '客户编码',
						width: '100px',
						field: 'custCode',
						align: 'center'
					},
					{
						title: '客户名称',
						width: '100px',
						field: 'custName',
						align: 'center'
					},
					{
						title: '客户类别',
						width: '100px',
						field: 'custType',
						align: 'center',
						formatter: function status() {

							return "重要客户";
						}

					},
					{
						title: '结算组',
						width: '100px',

						align: 'center',
						formatter: function status(row,value) {

							return "C组";
						}
					},
					{
						title: '运营部',
						width: '100px',

						align: 'center',
						formatter: function status(row,value) {

							return "C组";
						}
					},
					{
						title: '驻场组',
						width: '100px',

						align: 'center',
						formatter: function status(row,value) {

							return "C组";
						}
					},
					{
						title: '结算公司',
						width: '100px',
						field: 'balaCorp',
						align: 'center',
						formatter: function status(value) {
							if (value === '1') {
								return "江苏铭源";
							}
						}

					}
				]
			};

			$.table.init(options);
		});

		/**
		 * 选择客户后的提交方法
		 */
		function submitHandler() {
			var rows = $.table.selectFirstColumns();
			if (rows.length == 0) {
				$.modal.alertWarning("请至少选择一条记录");
				return;
			}
			$.modal.close();
			var param = {};
			param.phone = [[${phone}]];
			param.mobileUserId = [[${mobileUserId}]];
			param.userName = [[${userName}]];
			param.password = [[${password}]];
			param.salt = [[${salt}]];
			param.userType = [[${userType}]];
			param.userId = [[${userId}]];
			param.corDate = $("#corDate").val();
			param.customerId = rows.join();

			$.ajax({
				url: ctx + "appUser/addClientAccount",
				data: param,
				type: "post",
				success: function(result) {
					if (result.code == 0) {
						parent.layer.msg("保存成功,正在刷新数据请稍后……", {
							icon: 1,
							time: 500,
							shade: [0.1, '#8F8F8F']
						},function() {
							parent.location.reload();
						});
					} else {
						alert(result.msg);
					}
				}
			})
		}
	</script>
</body>
</html>