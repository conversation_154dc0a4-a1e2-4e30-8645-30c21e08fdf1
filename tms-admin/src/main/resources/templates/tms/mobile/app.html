<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('App用户信息列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-sm-3">
                        <div class="form-group">
<!--                            <label class="col-sm-4">姓名：</label>-->
                            <div class="col-sm-12">
                                <input name="userName" id="userName" placeholder="请输入姓名" class="form-control valid">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
<!--                            <label class="col-sm-4">审核状态：</label>-->
                            <div class="col-sm-12">
                                <select name="checkSatatus" id="checkSatatus" class="form-control valid"
                                        aria-invalid="false">
                                    <option value="">全部</option>
                                    <option th:each="mapS,status:${statusList}" th:value="${mapS.value}"
                                            th:text="${mapS.context}"></option>
                                </select>

                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">审核状态：</label>-->
                            <div class="col-sm-12">
                                <select name="userType" id="userType" class="form-control valid"
                                        aria-invalid="false">
                                    <option value="">-- 用户类型 --</option>
                                    <option value="1">货主</option>
                                    <option value="2">承运商</option>
                                    <option value="5">收货方</option>
                                </select>

                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <label class="col-sm-4"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                                <i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                                <i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="addClient()"
               shiro:hasPermission="app:mobileUser:addClient">
                <i class="fa fa-user"></i> 保存为客户
            </a>
            <a class="btn btn-primary single disabled" onclick="addClientAccount()"
               shiro:hasPermission="app:mobileUser:addClientAccount">
                <i class="fa fa-user"></i> 保存为客户账号
            </a>
            <a class="btn btn-primary single disabled" onclick="addCarrier()"
               shiro:hasPermission="app:mobileUser:addCarrier">
                <i class="fa fa-user"></i> 保存为承运商
            </a>
            <a class="btn btn-primary single disabled" onclick="addCarrierAccount()"
               shiro:hasPermission="app:mobileUser:addCarrierAccount">
                <i class="fa fa-user"></i> 保存为承运商账号
            </a>
            <a class="btn btn-primary multiple disabled" onclick="notApproved()"
               shiro:hasPermission="app:mobileUser:notApproved">
                <i class="fa fa-times"></i> 审核不通过
            </a>
            <a class="btn btn-primary single disabled" onclick="edit()"
               shiro:hasPermission="app:mobileUser:edit">
                <i class="fa fa-edit"></i> 资质更新
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "appUser";

    $(function () {
        var options = {
            url: prefix + "/list",
            showToggle: false,
            showColumns: true,
            modalName: "App用户",
            clickToSelect:true,
            columns: [
                {checkbox: true},
                {
                    title: '用户类型',
                    field: 'userType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch (value) {
                            case '1':
                                return '<span>货主</label>';
                            case '2':
                                return '<span>承运商</label>';
                            case '5':
                                return '<span>收货方</label>';
                            default:
                                break;
                        }
                    }

                },
                {title: '姓名', field: 'userName', align: 'left'},
                {title: '电话', field: 'phone', align: 'left'},
                // {title: '客户/承运商名称', field: 'customerOrCarrier', align: 'left'},
                {
                    title: '用户性质',
                    field: 'userClass',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch (value) {
                            case '1':
                                return '<span>企业用户</label>';
                            case '2':
                                return '<span>个人用户</label>';
                            default:
                                break;
                        }
                    }
                },
                {title: '身份证号码',field: 'cardId',align: 'left'},
                {title: '地址',field: 'address',align: 'left'},
                // {title: '公司名称',field: 'companyName',align: 'left'},
                // {title: '营业执照号码',field: 'businessLicense',align: 'left'},
                {
                    title: '用户状态',
                    field: 'userStatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch (value) {
                            case '1':
                                return '<span>注册</label>';
                            case '2':
                                return '<span>更新</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '审核状态',
                    field: 'checkStatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch (value) {
                            case '0':
                                return '<span>审核中</label>';
                            case '1':
                                return '<span>审核通过</label>';
                            case '2':
                                return '<span>审核不通过</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '注册时间',
                    field: 'createTime',
                    align: 'left',
                }
            ]
        };
        $.table.init(options);
    });

    // 保存为客户
    function addClient() {
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["checkStatus"] == "1") {
            $.modal.msgWarning("该用户已审核通过");
            return;
        }
        if (bootstrapTable[0]["userType"] == "2") {
            $.modal.msgWarning("请选择客户类型或收货方类型保存");
            return;
        }
        if (bootstrapTable[0]["userStatus"] === '2'&& bootstrapTable[0]["userType"] !== "5") {
            $.modal.alertWarning("该用户是更新状态");
            return;
        }
        $.modal.openTab("保存为客户", ctx + "client/add?phone=" + $.table.selectColumns('phone') + "&userName="
            + $.table.selectColumns('userName') + "&businessLicense=" + $.table.selectColumns('businessLicense')
            + "&mobileUserId=" + $.table.selectColumns('mobileUserId') + "&password="
            + $.table.selectColumns('password') + "&salt=" + $.table.selectColumns('salt') + "&userType="
            + $.table.selectColumns('userType') + "&loginName=" + $.table.selectColumns('loginName') + "&companyName="
            + $.table.selectColumns('companyName') + "&cardId=" + $.table.selectColumns('cardId') + "&provinceId="
            + $.table.selectColumns('provinceId') + "&cityId=" + $.table.selectColumns('cityId') + "&areaId="
            + $.table.selectColumns('areaId') + "&address=" + $.table.selectColumns('address') + "&userClassId="
            + $.table.selectColumns('userClass') + "&userId=" + $.table.selectColumns('userId')+"&corDate="
            + $.table.selectColumns('updateTime'));
    }

    // 保存为客户账号
    function addClientAccount() {
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["checkStatus"] == "1") {
            $.modal.msgWarning("该用户已审核通过");
            return;
        }
        if (bootstrapTable[0]["userType"] == "2") {
            $.modal.msgWarning("请选择客户类型或收货方类型保存");
            return;
        }
        if (bootstrapTable[0]["userStatus"] === '2' && bootstrapTable[0]["userType"] !== "5") {
            $.modal.alertWarning("该用户是更新状态");
            return;
        }
        $.modal.open("保存为客户", prefix + "/addClient?phone=" + $.table.selectColumns('phone') + "&mobileUserId="
            + $.table.selectColumns('mobileUserId') + "&userName=" + $.table.selectColumns('userName') + "&password="
            + $.table.selectColumns('password') + "&salt=" + $.table.selectColumns('salt') + "&userType="
            + $.table.selectColumns('userType') + "&loginName=" + $.table.selectColumns('loginName')
            + "&userId=" + $.table.selectColumns('userId')+"&corDate="
            + $.table.selectColumns('updateTime'));
    }

    // 保存为承运商
    function addCarrier() {
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["checkStatus"] == "1") {
            $.modal.msgWarning("该用户已审核通过");
            return;
        }
        if (bootstrapTable[0]["userType"] != "2") {
            $.modal.msgWarning("请选择承运商类型保存");
            return;
        }
        if (bootstrapTable[0]["userStatus"] === '2') {
            $.modal.alertWarning("该用户是更新状态");
            return;
        }
        $.modal.openTab("保存为承运商", ctx + "basic/carrier/add?phone=" + $.table.selectColumns('phone')
            + "&carrName=" + $.table.selectColumns('userName') + "&businessLicense="
            + $.table.selectColumns('businessLicense') + "&mobileUserId=" + $.table.selectColumns('mobileUserId')
            + "&password=" + $.table.selectColumns('password') + "&salt=" + $.table.selectColumns('salt')
            + "&userType=" + $.table.selectColumns('userType') + "&loginName=" + $.table.selectColumns('loginName')
            + "&companyName=" + $.table.selectColumns('companyName') + "&cardId=" + $.table.selectColumns('cardId')
            + "&provinceId=" + $.table.selectColumns('provinceId') + "&cityId=" + $.table.selectColumns('cityId')
            + "&transportType=" + $.table.selectColumns('transportType') + "&carriertype=" + $.table.selectColumns('carriertype')
            + "&areaId=" + $.table.selectColumns('areaId') + "&address=" + $.table.selectColumns('address')
            + "&userClassId=" + $.table.selectColumns('userClass') + "&userId=" + $.table.selectColumns('userId')+"&corDate="
            + $.table.selectColumns('updateTime'));
    }

    // 保存为承运商账号
    function addCarrierAccount() {
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["checkStatus"] == "1") {
            $.modal.msgWarning("该用户已审核通过");
            return;
        }
        if (bootstrapTable[0]["userType"] != "2") {
            $.modal.msgWarning("请选择承运商类型保存");
            return;
        }
        if (bootstrapTable[0]["userStatus"] === '2') {
            $.modal.alertWarning("该用户是更新状态");
            return;
        }
        $.modal.open("保存为承运商账号", prefix + "/addCarrier?phone=" + $.table.selectColumns('phone')
            + "&mobileUserId=" + $.table.selectColumns('mobileUserId') + "&userName="
            + $.table.selectColumns('userName') + "&password=" + $.table.selectColumns('password') + "&salt="
            + $.table.selectColumns('salt') + "&userType=" + $.table.selectColumns('userType') + "&loginName="
            + $.table.selectColumns('loginName') + "&userId=" + $.table.selectColumns('userId')+"&corDate="
            + $.table.selectColumns('updateTime'));
    }

    // 审核不通过
    function notApproved() {
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["checkStatus"] !== "0") {
                $.modal.msgWarning("请选择审核中的用户");
                return;
            }

        }
        $.modal.confirm("是否确认？", function () {
        $.ajax({
            url: ctx + "appUser/notApproved?mobileUserIds=" + $.table.selectColumns('mobileUserId'),
            type: "get",
            success: function (result) {
                if (result.code == 0) {
                    layer.msg("保存成功,正在刷新数据请稍后……", {
                        icon: 1,
                        time: 500,
                        shade: [0.1, '#8F8F8F']
                    }, function () {
                        location.reload();
                    });
                } else {
                    alert(result.msg);
                }
            }
            })
        });

    }

    /**
     * 资质更新
     */
    function edit() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["userStatus"] === '1') {
            $.modal.alertWarning("请选择更新状态的用户");
            return;
        }
        if (bootstrapTable[0]["checkStatus"] === "1") {
            $.modal.msgWarning("该用户已被审核");
            return;
        }
        if (bootstrapTable[0]["userType"] === "5") {
            $.modal.msgWarning("请选择承运商/货主类型，且状态为更新的用户");
            return;
        }
        $.modal.openTab("资质更新", prefix + "/editMobileUser?mobileUserId=" + bootstrapTable[0]["mobileUserId"]);
    }
</script>

</body>
</html>