<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('承运商选择页')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="role-form" class="form-horizontal">
					<div class="row">
						<div class="col-sm-6">
							<div class="form-group">
								<label class="col-sm-4">承运商名称：</label>
								<div class="col-sm-8">
									<input name="" placeholder="请输入承运商名称" class="form-control valid" type="text"
										   required="" aria-required="true">
								</div>
							</div>
						</div>
						<div class="col-sm-2">
							<div class="form-group"></div>
						</div>

						<div class="col-sm-4">
							<label class="col-sm-4"></label>
							<div class="form-group">
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</div>
						</div>
					</div>

				</form>
			</div>
			<div class="col-sm-12 select-table table-striped">
				<table id="bootstrap-table" data-mobile-responsive="true"></table>

			</div>
		</div>

	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">
		var priority = [[${@dict.getType('priority')}]];
		var carr_type = [[${@dict.getType('carr_type')}]];
		var prefix = ctx + "basic/carrier";
		$(function () {
			var options = {
				url: prefix + "/list",
				showSearch: false,
				showRefresh: false,
				showToggle: false,
				showColumns: false,
				columns: [{
					radio: true
				},
					{
						title: 'id',
						field: 'carrierId',
						visible: false
					},
					{
						title: '承运商名称',
						width: '120px',
						align: 'left',
						field: 'carrName'
					},
					{
						title: '承运商类别',
						width: '120px',
						align: 'left',
						field: 'carrType',
						formatter: function(value, row, index) {
							return $.table.selectDictLabel(carr_type, value);
						}
					},
					{
						title: '优先级',
						width: '120px',
						align: 'left',
						field: 'priority',
						formatter: function(value, row, index) {
							return $.table.selectDictLabel(priority, value);
						}
					},
					{
						title: '详细地址',
						width: '200px',
						align: 'left',
						field: 'address'
					},

					{
						title: '联系人',
						width: '120px',
						align: 'left',
						field: 'contact'

					},
					{
						title: '手机',
						width: '120px',
						align: 'left',
						field: 'phone'
					}
				]
			};

			$.table.init(options);
		});
		/**
		 * 选择承运商后的提交方法
		 */
		function submitHandler() {
			var rows = $.table.selectFirstColumns();
			if (rows.length == 0) {
				$.modal.alertWarning("请至少选择一条记录");
				return;
			}
			$.modal.close();
			var param = {};
			param.phone = [[${phone}]];
			param.mobileUserId = [[${mobileUserId}]];
			param.userName = [[${userName}]];
			param.password = [[${password}]];
			param.salt = [[${salt}]];
			param.userType = [[${userType}]];
			param.userId = [[${userId}]];
			param.corDate = [[${corDate}]];
			param.carrierId = rows.join();
			// 保存为承运商账号
			$.ajax({
				url: ctx + "appUser/addCarrierAccount",
				data: param,
				type: "post",
				success: function(result) {
					if (result.code == 0) {
						parent.layer.msg("保存成功,正在刷新数据请稍后……", {
							icon: 1,
							time: 500,
							shade: [0.1, '#8F8F8F']
						},function() {
							location.reload();
						});
					} else {
						alert(result.msg);
					}
				}
			})
		}
	</script>
</body>
</html>