<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <meta charset="UTF-8">
    <th:block th:include="include :: header('客户线路')" />
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/css/bootstrap-editable.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.css}" rel="stylesheet"/>

</head>
<style>

</style>
<body>
<div class="col-sm-12 search-collapse">
    <form id="role-form" class="form-horizontal">
        <div class="row no-gutter">
            <div class="col-md-2 col-sm-2">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <input name="addrName" id="addrName" placeholder="地址1" class="form-control" type="text" maxlength="20" autocomplete="off">
                    </div>
                </div>
            </div>
<!--            <div class="col-md-2 col-sm-2">-->
<!--                <div class="form-group flex">-->
<!--                    <div class="col-sm-12">-->
<!--                        <select name="transType" class="form-control valid"-->
<!--                                aria-invalid="false"  th:with="type=${@dict.getType('trans_code')}">-->
<!--                            <option value="">&#45;&#45; 运输方式 &#45;&#45;</option>-->
<!--                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>-->
<!--                        </select>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
            <div class="col-md-3 col-sm-3">
                <div class="form-group" style="text-align: left;">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                </div>
            </div>

        </div>
        <div class="row no-gutter">

        </div>
    </form>
</div>
<div class="col-sm-12 select-table  ">
    <div class="btn-group-sm" id="toolbar" role="group">
        <a class="btn btn-primary"
           onclick="openAddTab()">
            <i class="fa fa-plus"></i>新增
        </a>
        <a class="btn btn-danger multiple"  onclick="rmDataByIds()">
            <i class="fa fa-times"></i> 批量删除
        </a>

        <a class="btn btn-success btn-rounded btn-sm"
           onclick="importAllData()" >
            <i class="fa fa-upload"></i> 批量导入
        </a>


    </div>
    <!--        <div class="col-sm-12 select-table table-striped" >-->
    <table id="bootstrap-table"
           class="table table-striped table-responsive table-bordered table-hover" >
    </table>
    <!--        </div>-->
</div>

<script id="addHtml" type="text/template" th:inline="javascript">

</script>

<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a th:href="@{/file/importCustLine.xlsx}" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
<th:block th:include="include :: footer"/>
<!--<th:block th:include="include :: bootstrap-table-editable-js"/>-->
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: xm-select"/>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/js/bootstrap-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/editable/bootstrap-table-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/bootstrap-table-export.min.js}"></script>

<script th:inline="javascript">
    const clientId = [[${clientId}]]
    //运输方式
    const transCode = [[${@dict.getType('trans_code')}]];

    var $table = $('#bootstrap-table')

    $(function () {
        let options = initOptions();
        $.table.init(options);

    });
    function initOptions() {
        return {
            url: `${ctx}tms/custLine/list?customerId=${clientId}`,
            showToggle:false,
            showColumns:false,
            showSearch:false,
            // pagination:false,
            // showRefresh:false,
            modalName: "客户线路",
            // showExport: true,
            // exportTypes:['excel'],
            // exportOptions:{
            //     // fileName:""
            // },
            height: 560,
            stickyHeader: true,  // 启用固定表头功能
            stickyHeaderOffsetY: 0,  // 可选，调整固定表头距离顶部的距离
            uniqueId: "custTranslineId",
            clickToSelect: true,
            // onLoadSuccess: function(data) {
            //     let fieldList = ["deliName","arriName"];
            //     mergeCells(data.rows, 1,  $('#bootstrap-table'), fieldList);
            // },
            columns:[
                {
                    checkbox: true
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 20,
                    switchable:false,
                    formatter: function(value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasAnyPermi('client:custLine:list')}]] != "hidden") {
                            actions.push(`<a class="btn btn-xs " href="javascript:void(0)" onclick="rmDataById('${row.custTranslineId}')" title="删除"><i class="fa fa-times" style="font-size: 15px;"></i></a>`);
                        }
                        return actions.join('');
                    }
                },

                // {
                //     title: '线路名称',
                //     align: 'left',
                //     field : 'lineName',
                // },
                {
                    title: '地址A',
                    align: 'left',
                    field : 'startProvinceName',
                    formatter: function (value, row, index) {
                        return row.startProvinceName + row.startCityName + row.startAreaName
                    }
                },
                {
                    title: '地址B',
                    align: 'left',
                    field : 'endProvinceName',
                    formatter: function (value, row, index) {
                        return row.endProvinceName + row.endCityName + row.endAreaName
                    }
                },
                {
                    title: '运输方式',
                    align: 'left',
                    field : 'transType',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(transCode, value);
                    }
                },
                {
                    title: '时效',
                    align: 'left',
                    field : 'timeLimit',
                    formatter: function status(value, row, index) {
                        if (value) {
                            return `${value}小时`;
                        }
                        return ''
                    }
                },
            ]
        }
    }

    function rmDataByIds() {

        const selections = $table.bootstrapTable('getSelections');

        const selectedIds = selections.map(row => row.custTranslineId).join(',');

        var data = {}
        data['customerId'] = clientId
        data['custTranslineId'] = selectedIds
        rmData(data)
    }

    function rmDataById(id) {
        var data = {}
        data['customerId'] = clientId
        data['custTranslineId'] = id
        rmData(data);
    }

    function rmData(data) {
        layer.confirm('确定删除该条数据吗?', function (index) {
            $.ajax({
                url: ctx + "tms/custLine/rm",
                type: "post",
                dataType: "json",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(data),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                    }else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();

                    $table.bootstrapTable('refresh', {
                        silent: true
                    });
                }
            });

        });
    }

    function openAddTab() {
        layer.open({
            type: 1,
            title: '新添线路',
            area: ['60%', '60%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: $('#addHtml').html(),
            btn: ['保存','关闭'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {
                initAddr("startProvince","startCity","deliArea","startArea")
                initAddr("endProvince","endCity","arriArea","endArea")
            },
            yes: function (index, layero) {
                //数据集
                var data = {}

                var addForm = $("#addForm");
                addForm.find("[name]").each(function () {
                    var name = $(this).attr("name");
                    var value = $(this).val();
                    data[name] = value;
                });
                data['customerId'] = clientId

                let transType = $("#transType").val();
                if (transType === null || transType === '') {
                    $.modal.msgError("请选择运输方式。")
                    return
                }
                let startProvince = $("#startProvince").val();
                let startCity = $("#startCity").val();
                let startArea = $("#startArea").val();
                let endProvince = $("#endProvince").val();
                let endCity = $("#endCity").val();
                let endArea = $("#endArea").val();
                if (startProvince === '' || startCity === '' || startArea === '' || endProvince === ''
                    || endCity === '' || endArea === '') {
                    $.modal.msgError("请选择地址。")
                    return
                }

                $.ajax({
                    url: ctx + "tms/custLine/add",
                    type: "post",
                    dataType: "json",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(data),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function(result) {
                        if (result.code == 0) {
                            $.modal.msgSuccess(result.msg);
                        }else {
                            $.modal.alertError(result.msg);
                        }
                        $.modal.closeLoading();
                        $.modal.enable();
                        $table.bootstrapTable('refresh', {
                            silent: true
                        });

                        layer.close(index);
                    }
                });
            },
            cancel: function (index) {
                return true;
            }
        });
    }


    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }


    /**
     * 导入数据
     */
    function importAllData() {
        layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入数据',
            content: $('#importTpl').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx') && !$.common.endWith(file, '.xlsm'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var index = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#file')[0].files[0]);
                formData.append("customerId", clientId);
                $.ajax({
                    url: ctx + "tms/custLine/import",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code == web_status.WARNING) {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });
    }

    function initAddr(provinceId, cityId, areaId,areaValue, province, city, area) {
        $(`#${provinceId} option`).remove();
        $(`#${cityId} option`).remove();
        // $(`#${areaId} option`).remove();
        $(`#${provinceId}`).append("<option value='' disabled selected hidden>省</option>");
        $(`#${cityId}`).append("<option value='' disabled selected hidden>市</option>");
        // $(`#${areaId}`).append("<option value='' disabled selected hidden>区</option>");
        initXmSelect(areaId,areaValue, []);
        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            async: false,
            success: function (result) {
                for (var i in result) {
                    $(`#${provinceId}`).append(`<option value='${result[i].PROVINCE_CODE}'>${result[i].PROVINCE_NAME}</option>`)
                }
                $(`#${provinceId} option`).each(function () {
                    if ($(this).val() == province) {
                        $(this).attr("selected", true)
                    }
                })
            }
        });
        if (province !== undefined) {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + province,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }
                    $('#'+ cityId +' option').each(function () {
                        if ($(this).val() == city) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + city,
                async: false,
                success: function (result) {
                    initXmSelect(areaId,areaValue, result);
                    xmSelect.get('#' + areaId)[0].setValue(area.split(','))
                }
            });
        }

        $('#'+ provinceId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ cityId +' option').remove();
                    $('#'+ cityId).append("<option value='' disabled selected hidden>市</option>");
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }

                    //清空区
                    xmSelect.get('#' + areaId)[0].update({data: []});
                }
            });
        });

        $('#'+ cityId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + $(this).val(),
                success: function (result) {
                    initXmSelect(areaId,areaValue, result);
                }
            });
        });
    }

    function initXmSelect(id, value, dataList) {
        var demo4 = xmSelect.render({
            el: '#' + id,
            language: 'zn',
            size: 'mini',
            model: {
                label: {
                    type: 'block',
                    block: {
                        //最大显示数量, 0:不限制
                        showCount: 3,
                        //是否显示删除图标
                        showIcon: true,
                    }
                }
            },
            toolbar: {
                show: true,
            },
            autoRow: true,
            prop: {
                name: 'AREA_NAME',
                value: 'AREA_CODE',
            },
            on: function (data) {
                //arr:  当前多选已选中的数据
                var arr = data.arr;
                $(`#${value}`).val(arr.map(obj => obj.AREA_CODE).join(","))
            },
            data: dataList
        })
    }


</script>
</body>
</html>