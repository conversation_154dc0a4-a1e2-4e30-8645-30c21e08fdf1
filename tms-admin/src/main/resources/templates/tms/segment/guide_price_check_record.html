<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('调度价审核记录')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var segmentId = [[${segmentId}]];//运段id
    $(function () {
        var options = {
            url: ctx + "tms/segment/guidePriceCheckList/" + segmentId,
            showToggle: false,
            showColumns: false,
            showSearch: false,
            showRefresh: false,
            fixedColumns: true,
            columns: [
                {title: '审核人', field: 'checkMan', align: 'left'},
                {title: '审核日期', field: 'checkDate', align: 'left'},
                {
                    title: '审核结果', field: 'checkStatus', align: 'left',
                    formatter: function status(value, row) {
                        if (value === 0) {
                            return '<span class="label label-default">待审核</span>';
                        }
                        if (value === 1) {
                            return '<span class="label label-primary">审核通过</span>';
                        }
                        if (value === 2) {
                            return '<span class="label label-danger">审核未通过</span>';
                        }
                    }
                },
                {title: '备注', field: 'memo', align: 'left'}
            ]
        };

        $.table.init(options);
    });
</script>
</body>
</html>