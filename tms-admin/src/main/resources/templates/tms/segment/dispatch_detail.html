<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('调度')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 30px;
        text-align: right;
        color: #666;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;

    }
    /*.row .form-group label[class*='col-'] {*/
    /*    text-align: right;*/
    /*    line-height: 30px;*/
    /*    margin-bottom: 0;*/
    /*}*/
    .addbtn{
        /*width: 120px;*/
        text-align: center;
        color: #fff;
        background: #1ab394;
        line-height: 30px;
        border-radius: 5px;
        cursor: pointer;
    }
    .addGoods{
        color: #1ab394;
        line-height: 30px;
        cursor: pointer;
    }
    .wap_content{
        padding: 10px 10px;
        border: 1px #eee solid;
        border-radius: 3px;

    }
    .line20{
        line-height: 20px;
    }
    .fcff6{
        color: #ff6c00;
    }
    .hisbtn{
        line-height: 25px;
        border-radius: 20px;
        width: 140px;
        text-align: center;
        color: #1ab394;
        border: 1px #1ab394 solid;
        cursor: pointer;
    }
    .eye{
        width: 20px;
        height: 20px;
        background: #1ab394 url("../../img/eyes.png") no-repeat center;
        background-size: 20px 20px;
        border-radius: 50%;
        /*display: inline-block;*/
    }
    .disin{
        display: inline-block;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .toop{
        width: 20px;
        height: 20px;
        text-align: center;
        background: #7f7f7f;
        border-radius: 50%;
        display: inline-block;
        color: #fff;
        line-height: 20px;
        cursor: pointer;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .frT {
        display: inline-block;
        vertical-align: bottom;
        text-align: right;
    }

    .vertical-timeline-icon{
        width: 3em;
        height: 3em;
        color: #fff;
        left: calc(50% - 15px);
        top: -14px;
        font-size: 12px;
        background-color: #18a689;
        border-color: #18a689;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
    }
    
    .vertical-timeline-block{
        margin: 2em 0 !important;
    }
    .f18a{
        color: #18a689;
    }
    .vtb-tit {
        width: 6em;
        height: 20px;
        line-height: 20px;
        text-align: center;
        position: absolute;
        left: calc(50% - 2.5em);
        top: -30px;
        z-index: 1;
    }

    .vertical-timeline-block{
        position: relative;
    }
    .vertical-timeline-block::after{
        content:" ";
        position: absolute;
        /* width: calc(100% + 80px); */
        width: 100%;
        height: 0;
        border-bottom:#18a689 2px dashed;
        top: 4px;
        left: 0;
    }
    .vertical-timeline-blockF::after{
        border-bottom:#d1dade 2px dashed;
    }

    .vertical-timeline-blockF .vertical-timeline-icon{
        background-color: #d1dade;
        border-color: #d1dade;
    }
    .vertical-timeline-blockF .f18a{
        color: #666666;
    }

    .vertical-timeline-content{
        width: 200px;
        text-align: center;
        margin-left: 0;
        left: calc(50% - 88px);
        top: 20px;
    }
    .vertical-timeline-content .over{
        position: relative;
        left: 15%;
    }
    .trans_box{
        width: 100%;
        text-align: center;
        position: absolute;
        top: -75px;
        left: calc(50% - 106px);
        color: #18a689;
    }
    .th_img{
        width: 20px;
        height: 20px;
    }
    .carrierInfo{
        width: 100px;
        left: calc(100% - 50px);
        /* height: 20px; */
        line-height: 20px;
        text-align: center;
        position: absolute;
        top: -16px;
        z-index: 1;
    }
    .carrierInfo span{
        display: inline-block;
    }
    .vertical-container{
        width: 100%;
        max-width: initial;
    }
    .light-timeline+.light-timeline{
        border-top: 2px #EBECEF solid;
        margin-top: 15px;
        padding-top: 15px;
    }
    .timeBor{
        height: 50px;
        border: #ddd 1px solid;
        border-top: 0;
    }
    .timeBorF{
        border-color: #ffffff;
    }
    .timeBor+.timeBor{
        border-left: 0;
    }
    .timeBor span{
        display: inline-block;
        margin-top: 40px;
        color: #ff1f1f;
        background-color: #ffffff;
        padding: 0 5px;
    }
    .f16{
        font-size: 16px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-dispatch-add" class="form-horizontal" novalidate="novalidate">
        <input id="segmentIds" name="segmentIds" th:value="${segmentIds}" type="hidden">
        <div class="panel-group" id="accordion">

            <!-- 运输跟踪 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseThree">运输信息</a>
                    </h4>
                </div>
                <div id="collapseThree" class="panel-collapse collapse in">
                    <div class="panel-body">

                        <div class="row vertical-container light-timeline" th:each="entrustlist,stat:${list}">
                            <div class="col-md-12 col-sm-12" style="margin-bottom: 40px;position: relative;left: -10px;">
                                <div class="fw f16">
                                    <span th:text="${entrustlist.entrust.lot}"></span>
                                    <span th:text="${entrustlist.entrust.deliProName+entrustlist.entrust.deliCityName+entrustlist.entrust.deliAreaName+entrustlist.entrust.deliAddrName}"></span>
                                    <span><i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i></span>
                                    <span th:text="${entrustlist.entrust.arriProName+entrustlist.entrust.arriCityName+entrustlist.entrust.arriAreaName+entrustlist.entrust.arriAddrName}"></span>
                                    &nbsp;&nbsp;&nbsp;
                                    <span th:each="entPackGoods:${entrustlist.entPackGoodsList}" th:text="${entPackGoods.goodsName}"></span>
                                    
                                    <span th:text="${entrustlist.entrust.numCount+'件 /'}" th:if="${entrustlist.entrust.numCount!=0}"></span>
                                    <span th:text="${entrustlist.entrust.weightCount+'吨 /'}" th:if="${entrustlist.entrust.weightCount!=0}"></span>
                                    <span th:text="${entrustlist.entrust.volumeCount}+'m³'" th:if="${entrustlist.entrust.volumeCount!=0}"></span> 
                                </div>
                            </div>

                            <div class="col-md-2 col-sm-2 vertical-timeline-block">
                                
                                <div class="vertical-timeline-icon">调度</div>
                                <div class="vertical-timeline-content">
                                    <div class="trans_box">  
                                        <div th:text="${entrust.regUserName}"></div>
                                        <div th:text="${#dates.format(entrust.regDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>

                                    <div class="mt10" th:if="${not #lists.isEmpty(entrustlist.tihuowork)}">
                                        <label><span>预计到达：</span></label>
                                        <div th:text="${#dates.format(entrust.estimatedArrivalTime, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>  

                                <div class="carrierInfo" >
                                    <label><span>承运商：</span></label>
                                    <span th:text="${entrustLot.carrierName}"></span>
                                </div>
                            </div>

                            <div th:class="${not #lists.isEmpty(entrustlist.tihuowork)?'col-md-2 col-sm-2 vertical-timeline-block':'col-md-2 col-sm-2 vertical-timeline-block vertical-timeline-blockF'}">
                                <div class="vertical-timeline-icon">提货</div>
                                <div class="vertical-timeline-content" th:if="${not #lists.isEmpty(entrustlist.tihuowork)}">
                                    <div class="trans_box">  
                                        <div th:text="${entrustlist.tihuoRen}"></div>
                                        <div th:text="${#dates.format(entrustlist.tihuoRegDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>

                                    <div class="over mt10">
                                        <div class="fl">提货照片:</div>
                                        <div class="fl picviewer" >
                                            <span th:each="tihuopic:${entrustlist.tihuoList}" th:if="${tihuopic.filePath!=null}">
                                                <img class="th_img" th:src="@{${tihuopic.filePath}}"/>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="over mt10">
                                        <div class="fl">提货时间:</div>
                                        <div class="fl" th:text="${#dates.format(entrustlist.entrust.actDeliDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                            </div>

                            <div th:class="${not #lists.isEmpty(entrustlist.tihuowork)?'col-md-2 col-sm-2 vertical-timeline-block':'col-md-2 col-sm-2 vertical-timeline-block vertical-timeline-blockF'}">
                                <div class="vtb-tit" style="width: 200px;left: 0;line-height: 14px;" th:if="${not #lists.isEmpty(entrustlist.tihuowork)}"> 
                                    <img th:if="${entrustLot.carNo!=null}" th:src="@{/img/car.png}" style="width: 20px;height:20px;vertical-align: sub;"/>  
                                    <span th:text="${entrustLot.carNo}"> </span>
                                    <span th:text="${entrustLot.trailerNo}"></span>
                                    <br/>
                                    <span th:text="${entrustLot.carLenName+'米'}"  th:if="${entrustLot.carLenName!=null}"></span><span th:text="${entrustLot.carTypeName}"></span>
                                </div>
                                <div class="vertical-timeline-content" style="width: 200px;top: 6px;" th:if="${not #lists.isEmpty(entrustlist.tihuowork)}">
                                    <label th:if="${driverNames!=null&&driverPhones!=null&&driverNames!=''&&driverPhones!=''}"><span>司机：</span></label>
                                    <span th:text="${driverNames}"> </span>
                                    <span th:text="${driverPhones}"> </span>
                                </div>
                            </div>
  
                            <div th:class="${not #lists.isEmpty(entrustlist.daohuowork)?'col-md-2 col-sm-2 vertical-timeline-block':'col-md-2 col-sm-2 vertical-timeline-block vertical-timeline-blockF'}">
                                <div class="vertical-timeline-icon">到货</div>
                                <div class="vertical-timeline-content" th:if="${not #lists.isEmpty(entrustlist.daohuowork)}">
                                    <div class="trans_box">  
                                        <div th:text="${entrustlist.daohuoRen}"></div>
                                        <div th:text="${#dates.format(entrustlist.daohuoRegDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>

                                    <div class="over mt10">
                                        <div class="fl">到货照片:</div>
                                        <div class="fl picviewer" >
                                            <span th:each="daohuopic:${entrustlist.daohuoList}" th:if="${daohuopic.filePath!=null}">
                                                <img class="th_img" th:src="@{${daohuopic.filePath}}"/>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="over mt10">
                                        <div class="fl">到货时间:</div>
                                        <div class="fl" th:text="${#dates.format(entrustlist.entrust.actArriDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                            </div>

                            <div th:class="${entrustlist.entrust.receiptConfirmFlag == 1?'col-md-2 col-sm-2 vertical-timeline-block':'col-md-2 col-sm-2 vertical-timeline-block vertical-timeline-blockF'}">
                                
                                <div class="vertical-timeline-icon">回单确认</div>
                                <div class="vertical-timeline-content" th:if="${entrustlist.entrust.receiptConfirmFlag == 1}">
                                    <div class="trans_box">  
                                        <div th:text="${entrustlist.entrust.receiptConfirmUser}"></div>
                                        <div th:text="${#dates.format(entrustlist.entrust.receiptConfirmTime, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                            </div>

                            <div th:class="${entrustlist.entrust.ifReceipt == '1'?'col-md-2 col-sm-2 vertical-timeline-block':'col-md-2 col-sm-2 vertical-timeline-block vertical-timeline-blockF'}">
                                
                                <div class="vertical-timeline-icon">正本回单</div>
                                <div class="vertical-timeline-content" th:if="${entrustlist.entrust.ifReceipt == '1'}">
                                    <div class="trans_box">  
                                        <div th:text="${entrustlist.entrust.receiptMan}"></div>
                                        <div th:text="${#dates.format(entrustlist.entrust.receiptDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                    <div class="over mt10">
                                        <div class="fl">回单照片:</div>
                                        <div class="fl picviewer" >
                                            <span th:each="pic:${entrustlist.uploadFileList}" th:if="${pic.filePath!=null}">
                                                <img class="th_img" th:src="@{${pic.filePath}}"/>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-sm-6 col-md-offset-1 col-sm-offset-1 timeBor" style="text-align: center;">
                                <span th:id="'timeO_'+${stat.index}"></span>
                            </div>

                            <div class="col-md-4 col-sm-4 timeBor" style="text-align: center;">
                                <span th:id="'timeT_'+${stat.index}"></span>
                            </div>
                           
                        </div>


                        <div class="row no-gutter mt10">
                            <div class="col-md-12 col-sm-12">
                                <div class="flex">
                                    <label class="flex_left">内部备注：</label>
                                    <div class="flex_right" th:text="${entrustLot.memo}"> </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFive">费用信息</a>
                        <div class="frT" style="width: calc(100% - 56px);">
                            <span class="mr10">
                                <span>是否报关：[[${entrustLot.isCustomsClearance !=null and entrustLot.isCustomsClearance == 1 ? '是' : '否'}]]</span>
                            </span>
                            <span class="mr10">
                                <span>大件/三超：[[${entrustLot.isOversize !=null and entrustLot.isOversize == 1 ? '是' : '否'}]]</span>
                                <span th:if="${entrustLot.isOversize == 1}">
                                    （尺寸：[[${entrustLot.goodsLength == null ? 0 : entrustLot.goodsLength}]]m x [[${entrustLot.goodsWidth == null ? 0 : entrustLot.goodsWidth}]]m x [[${entrustLot.goodsHeight == null ? 0 : entrustLot.goodsHeight}]]m）
                                </span>

                            </span>
                            <span class="mr10">
                                大车配载：[[${entrustLot.isBigCartLoad !=null and entrustLot.isBigCartLoad == 1 ? '是' : '否'}]]
                            </span>
                            <span >
                                <span th:each="dict : ${@dict.getType('billing_type')}"
                                      th:if="${dict.dictValue} == ${entrustLot.billingType}"
                                      th:text="${dict.dictLabel}"></span>
                            </span>
                            <span>油卡比例：
                                <span th:if="${entrustLot.oilRatio} != null" th:text="|${entrustLot.oilRatio}%|"></span>
                                <span th:if="${entrustLot.oilRatio} == null" th:text="|${entrustLot.oilRatio}|"></span>
                            </span>
                            <span>油卡金额：<span th:text="${entrustLot.oilAmount!=null?'￥'+entrustLot.oilAmount:''}"></span></span>
                            <span >指导价：<span th:text="${segment.guidingPrice!=null?'￥'+segment.guidingPrice:''}"></span></span>
                            <span>单价：<span th:text="${entrustLot.unitPrice}"></span></span>
                            <span class="fcff">总金额：<span id="calculateTotal" th:text="${totalAmount!=null?'￥'+totalAmount:''}"></span></span>
                            <span th:each="dict : ${pricingMethodEnumList}" th:if="${dict.value} == ${entrustLot.pricingMethod}" th:text="${dict.context}"></span>
                            <i class="fa fa-edit text-success ml5" th:onclick="adjust([[${lotId}]])" th:if="${entrust.receiptConfirmFlag == 1 && payAdjustAmount == 0}"></i>
                            <span  th:if="${payAdjustAmount != 0}"><a href="javascript:viewSp()" data-toggle="tooltip" data-html="true" th:title="${spNos}">调整中：[[${payAdjustAmount}]]</a></span>
                        </div>
                    </h4>
                </div>
                <div id="collapseFive" class="panel-collapse collapse in">
                    <div class="panel-body">
                       <!-- <div class="row">
                            <div class="col-sm-6">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" data-parent="#accordion"
                                               href="tabs_panels.html">现金</a>
                                        </h4>
                                    </div>
                                    <div class="panel-collapse collapse in">
                                        <div class="panel-body" style="padding-bottom: 20px">
                                            <div class="row">
                                                <div class="col-sm-5">
                                                    <div class="flex">
                                                        <label class="flex_left " >预付现金：</label>
                                                        <div class="flex_right">[[${beforeMoney}]]</div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-5">
                                                    <div class="flex">
                                                        <label class="flex_left " >收款账号：</label>
                                                        <div class="flex_right">[[${beforeMoneyCard}]]</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-5">
                                                    <div class="flex">
                                                        <label class="flex_left " >到付现金：</label>
                                                        <div class="flex_right">[[${arriveMoney}]]</div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-5">
                                                    <div class="flex">
                                                        <label class="flex_left " >收款账户：</label>
                                                        <div class="flex_right">[[${arriveMoneyCard}]]</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-5">
                                                    <div class="flex">
                                                        <label class="flex_left " >回付现金：</label>
                                                        <div class="flex_right">[[${receiptMoney}]]</div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-5">
                                                    <div class="flex">
                                                        <label class="flex_left " >收款账户：</label>
                                                        <div class="flex_right">[[${receiptMoneyCard}]]</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div style="padding-left: 40px">[[${moneyMemo}]]</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" data-parent="#accordion"
                                               href="tabs_panels.html">油卡</a>
                                        </h4>
                                    </div>
                                    <div class="panel-collapse collapse in">
                                        <div class="panel-body" style="padding-bottom: 20px">
                                            <div class="row">
                                                <div class="col-sm-5">
                                                    <div class="flex">
                                                        <label class="flex_left " >预付油卡：</label>
                                                        <div class="flex_right">[[${beforeOil}]]</div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-5">
                                                    <div class="flex">
                                                        <label class="flex_left " >油卡号：</label>
                                                        <div class="flex_right">[[${beforeOilCard}]]</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-5">
                                                    <div class="flex">
                                                        <label class="flex_left " >到付油卡：</label>
                                                        <div class="flex_right">[[${arriveOil}]]</div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-5">
                                                    <div class="flex">
                                                        <label class="flex_left " >油卡号：</label>
                                                        <div class="flex_right">[[${arriveOilCard}]]</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-5">
                                                    <div class="flex">
                                                        <label class="flex_left " >回付油卡：</label>
                                                        <div class="flex_right">[[${receiptOil}]]</div>
                                                    </div>
                                                </div>
                                                <div class="col-sm-5">
                                                    <div class="flex">
                                                        <label class="flex_left " >油卡号：</label>
                                                        <div class="flex_right">[[${receiptOilCard}]]</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div style="padding-left: 40px">[[${oilMemo}]]</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                       -->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 15%;text-align: center">应付单号</th>
                                    <th style="width: 5%;text-align: center">状态</th>
                                    <th style="width: 10%;text-align: center">结算公司</th>
                                    <th style="width: 10%;text-align: center">费用类型</th>
                                    <th style="width: 10%;text-align: right">金额</th>

                                   
                                    <th style="width: 10%;text-align: center">收款账号</th>
                                    <th style="width: 10%;text-align: center">收款卡号</th>

                                    <th style="width: 10%;text-align: center" th:if="${balaType == 1}">付款人</th>
                                    <th style="width: 10%;text-align: center" th:if="${balaType == 1}">付款日期</th>
                                    <th style="width: 10%;text-align: center" th:if="${balaType == 1}">付款备注</th>
                                </tr>
                                </thead>
                                <tbody>

                                <tr th:each="payDetail,stat : ${payDetailList}">
                                    <td style="text-align: center" >

                                        [[${payDetail.vbillno}]]
                                        <th:block th:if="${payDetail.lotG7End == 2}">
                                            <span class="label label-success" style="padding:1px;">G7</span>
                                        </th:block>
                                        <th:block th:if="${payDetail.lotG7End != 2 and payDetail.lotG7Syn != null}">
                                            <th:block th:if="${payDetail.lotG7Syn == 0}">
                                                <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true"
                                                      title="等待G7审验" >G7</span>
                                            </th:block>
                                            <th:block th:if="${payDetail.lotG7Syn == 1}">
                                                <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true"
                                                      th:title="${payDetail.lotG7Msg}" >G7</span>
                                            </th:block>
                                            <th:block th:if="${payDetail.lotG7Syn == 2}">
                                                <th:block th:switch="${payDetail.lotG7Start}">
                                                    <th:block th:case="null">
                                                        <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true"
                                                              title="等待推送【发车】" >G7</span>
                                                    </th:block>
                                                    <th:block th:case="'0'">
                                                        <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true"
                                                              title="等待推送【发车】" >G7</span>
                                                    </th:block>
                                                    <th:block th:case="1">
                                                        <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true"
                                                              title="【发车】推送失败" >G7</span>
                                                    </th:block>
                                                    <th:block th:case="*">
                                                        <th:block th:switch="${payDetail.lotG7End}">
                                                            <th:block th:case="null">
                                                                <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true"
                                                                      title="等待推送【到达】" >G7</span>
                                                            </th:block>
                                                            <th:block th:case="0">
                                                                <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true"
                                                                      title="等待推送【到达】" >G7</span>
                                                            </th:block>
                                                            <th:block th:case="1">
                                                                <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true"
                                                                      title="【到达】推送失败" >G7</span>
                                                            </th:block>
                                                        </th:block>
                                                    </th:block>
                                                </th:block>
                                            </th:block>
                                            <th:block th:if="${payDetail.lotG7Syn} == 7">
                                                <span  class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true"
                                                      title="运单已作废" >G7</span>
                                            </th:block>
                                        </th:block>
                                    </td>
                                    <td>
                                        <span class="label label-default" th:if="${payDetail.vbillstatus} == 0">新建</span>
                                        <span class="label label-warning" th:if="${payDetail.vbillstatus} == 1">已确认</span>
                                        <span class="label label-coral" th:if="${payDetail.vbillstatus} == 2">已对账</span>
                                        <span class="label label-info" th:if="${payDetail.vbillstatus} == 3">部分核销</span>
                                        <span class="label label-success" th:if="${payDetail.vbillstatus} == 4">已核销</span>
                                        <span class="label label-inverse" th:if="${payDetail.vbillstatus} == 5">关闭</span>
                                        <span class="label label-success" th:if="${payDetail.vbillstatus} == 6">已申请</span>
                                        <span class="label label-info" th:if="${payDetail.vbillstatus} == 7">核销中</span>
                                        <span class="label label-default" th:if="${payDetail.vbillstatus} == 8">审核中</span>
                                        <span class="label label-default" th:if="${payDetail.vbillstatus} == 9">复核通过</span>
                                    </td>
                                    <td>
                                        <span th:if="${payDetail.balaCorp} == 'MD'">南通明达</span>
                                        <span th:if="${payDetail.balaCorp} == 'JH'">南通吉华</span>
                                        <span th:if="${payDetail.balaCorp} == 'MY'">江苏铭源</span>
                                        <span th:if="${payDetail.balaCorp} == 'HK'">南通皓凯</span>
                                        <span th:if="${payDetail.balaCorp} == 'DW'">南通鼎旺</span>
                                    </td>
                                    <td style="text-align: center" th:each="dict : ${@dict.getType('cost_type_on_way')}"
                                        th:if="*{payDetail.freeType} == 1 and  ${dict.dictValue} == ${payDetail.costTypeOnWay} "
                                        th:text="${dict.dictLabel}"></td>
                                    <td style="text-align: center" th:each="dict : ${@dict.getType('cost_type_freight')}"
                                        th:if="*{payDetail.freeType} == 0 and ${dict.dictValue} == ${payDetail.costTypeFreight}"
                                        th:text="${dict.dictLabel}"></td>
                                    <td style="text-align: right" th:if="${payDetail.transFeeCount != null}" th:text="${'￥'+payDetail.transFeeCount}"></td>

                                    <td th:text="${payDetail.recAccount}">
                                        <!-- <div th:if="${not #lists.isEmpty(payDetail.payRecordList)}" th:each="payRecord: ${payDetail.payRecordList}" th:text="${payRecord.recAccount}"></div> -->
                                    </td>
                                    <td th:text="${payDetail.recCardNo}">
                                        <!-- <div th:if="${not #lists.isEmpty(payDetail.payRecordList)}" th:each="payRecord: ${payDetail.payRecordList}" th:text="${payRecord.recCardNo}"></div> -->
                                    </td>

                                    <td th:if="${balaType == 1}">
                                        <div th:if="${not #lists.isEmpty(payDetail.payRecordList)&&(payDetail.vbillstatus== 3||payDetail.vbillstatus== 4)}" th:each="payRecord: ${payDetail.payRecordList}" th:text="${payRecord.payMan}"></div>
                                    </td>
                                    <td th:if="${balaType == 1}">
                                        <div th:if="${not #lists.isEmpty(payDetail.payRecordList)&&(payDetail.vbillstatus== 3||payDetail.vbillstatus== 4)}" th:each="payRecord: ${payDetail.payRecordList}" th:text="${#dates.format(payRecord.payDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </td>
                                    
                                    <td style="text-align: left" th:if="${balaType == 1}">
                                        <div th:if="${not #lists.isEmpty(payDetail.payRecordList)&&(payDetail.vbillstatus== 3||payDetail.vbillstatus== 4)}" th:each="payRecord: ${payDetail.payRecordList}" th:text="${payRecord.memo}"></div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                       
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordionTwo">
                <div  class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapseTwo">费用分摊信息</a>

                            <div class="frT" style="width: calc(100% - 82px);">
                                <span class="fcff">合计：￥<span th:text="${shareSum}"></span></span>
                                <span> &nbsp; 
                                    <span th:text="${numTotal+'件 /'}" th:if="${numTotal!=0}"></span>
                                    <span th:text="${weightTotal+'吨 /'}" th:if="${weightTotal!=0}"></span>
                                    <span th:text="${volumeTotal}+'m³'" th:if="${volumeTotal!=0}"></span> 
                                </span>
                            </div>
                        </h4>
                    </div>
                    <div id="collapseTwo" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="fixed-table-body" style="margin: 0px -5px;">
                                <table id="infoTabThree" class="custom-tab tab table table-bordered" style="white-space:nowrap;">
                                    <thead>
                                    <tr>
                                        <th>发货单号</th>
                                        <!-- <th >要求提货日期</th> -->
                                        <th >提到货方</th>
                                        <th >货品名称</th>
                                        <th >件数/重量/体积</th>
                                        <th >要求车长车型</th>

                                        <th>现金</th>
                                        <th>油卡</th>
                                        <th>在途</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <div th:remove="tag" th:each="segmentVO,segmentVOStat : ${dispatchSegmentVO}">
                                        <!-- 正常运输方式 -->
                                        <tr th:if="${not #lists.isEmpty(segmentVO.segPackGoodsList)}" th:each="segPackGoods,segPackGoodsStat : ${segmentVO.segPackGoodsList}">
                                            <div th:if="${segPackGoodsStat.index == 0}">
                                                
                                                
                                                <!-- 发货单号 -->
                                                <td th:rowspan="${segPackGoodsStat.size}" th:text="${segmentVO.invoiceVbillno}"></td>
                                               <!-- 要求提货日期 -->
                                                <!-- <td th:rowspan="${segPackGoodsStat.size}" th:text="${#dates.format(segmentVO.reqDeliDate,'yyyy-MM-dd HH:mm:ss')}"></td> -->
                                                <!-- 提到货方-->
                                                <td th:rowspan="${segPackGoodsStat.size}" style="text-align: left;">
                                                    <span th:text="${segmentVO.deliAddrName}"></span> <i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i> <span th:text="${segmentVO.arriAddrName}"></span>
                                                </td>
                                                <!-- 货品名称 -->
                                                <td style="text-align: left;">
                                                    <span th:text="${segPackGoods.goodsName}"></span><span th:each="dict : ${@dict.getType('package_type')}" th:if="${dict.dictValue} == ${segPackGoods.packId}" th:text="${'('+dict.dictLabel+')'}"></span>
                                                </td>
                                                <!-- 件数|重量|体积 -->
                                                <td>
                                                    <span th:text="${segPackGoods.num+'件/'}" th:if="${segPackGoods.num!=0}"></span>
                                                    <span th:text="${segPackGoods.weight+'吨/'}" th:if="${segPackGoods.weight!=0}"></span>
                                                    <span th:text="${segPackGoods.volume+'m³'}" th:if="${segPackGoods.volume!=0}"></span>
                                                </td>
                                                 <!-- 要求车长车型	 -->
                                                 <td th:rowspan="${segPackGoodsStat.size}">
                                                    <span th:each="dict : ${@dict.getType('car_len')}" th:if="${dict.dictValue} == ${segmentVO.carLen}" th:text="${dict.dictLabel+'-'}"></span><span th:each="dict : ${@dict.getType('car_type')}" th:if="${dict.dictValue} == ${segmentVO.carType}" th:text="${dict.dictLabel}"></span>
                                                 </td>

                                                 <!-- 现金成本分摊 -->
                                                <td th:rowspan="${segPackGoodsStat.size}" th:text="${segmentVO.moneyMsg}"></td>
                                                <!-- 油卡成本分摊 -->
                                                <td th:rowspan="${segPackGoodsStat.size}" th:text="${segmentVO.oilMsg}"></td>
                                                <!-- 在途 -->
                                                <td th:rowspan="${segPackGoodsStat.size}" th:id="${segmentVO.segmentId}" th:attr="data-invoiceid=${segmentVO.invoiceId}" th:text="${segmentVO.onWayMsg}"></td>
                                                
                                            </div>
                                            <div th:unless="${segPackGoodsStat.index == 0}">
                                                <td style="text-align: left;">
                                                    <span th:text="${segPackGoods.goodsName}"></span><span th:each="dict : ${@dict.getType('package_type')}" th:if="${dict.dictValue} == ${segPackGoods.packId}" th:text="${'('+dict.dictLabel+')'}"></span> 
                                                </td>
                                                <td>
                                                    <span th:text="${segPackGoods.num+'件/'}" th:if="${segPackGoods.num!=0}"></span>
                                                    <span th:text="${segPackGoods.weight+'吨/'}" th:if="${segPackGoods.weight!=0}"></span>
                                                    <span th:text="${segPackGoods.volume+'m³'}" th:if="${segPackGoods.volume!=0}"></span>
                                                </td>
                                            </div>
                                        </tr>

                                        <!-- 多装多卸 -->
                                        <!-- 地址 -->
                                        <tr th:if="${not #lists.isEmpty(segmentVO.multipleShippingAddressList)}">
                                            
                                            
                                            <!-- 发货单号 -->
                                            <td th:text="${segmentVO.invoiceVbillno}"></td>
                                            <!-- 要求提货日期 -->
                                            <!-- <td th:text="${#dates.format(segmentVO.reqDeliDate,'yyyy-MM-dd HH:mm:ss')}"></td> -->
                                            <td>
                                                <div th:if="${not #lists.isEmpty(segmentVO.multipleShippingAddressList)}" th:each="shippingAddress,shippingAddressStat : ${segmentVO.multipleShippingAddressList}">
                                                    <span th:text="${shippingAddress.deliAddrName}"></span>
                                                </div>
                                                <div th:if="${not #lists.isEmpty(segmentVO.multipleShippingAddressList)}" th:each="shippingAddress,shippingAddressStat : ${segmentVO.multipleShippingAddressList}">
                                                    <span th:text="${shippingAddress.arriAddrName}"></span>
                                                </div>
                                            </td>
                                            <!-- 货品名称 -->
                                            <td>
                                                <div th:if="${not #lists.isEmpty(segmentVO.multipleShippingAddressList)}" th:each="shippingAddress,shippingAddressStat : ${segmentVO.multipleShippingAddressList}">
                                                    <div th:if="${shippingAddress.addressType == 0}" th:each="shippingGoods,shippingGoodsStat : ${shippingAddress.shippingGoodsList}">
                                                        <!-- TODO -->
                                                        提货:<span th:text="${shippingGoods.goodsName}"></span>
                                                    </div>
                                                    <div th:if="${shippingAddress.addressType == 1}" th:each="shippingGoods,shippingGoodsStat : ${shippingAddress.shippingGoodsList}">
                                                        到货:<span th:text="${shippingGoods.goodsName}"></span>
                                                    </div>
                                                </div>
                                                <div th:if="${not #lists.isEmpty(segmentVO.multipleShippingAddressList)}" th:each="shippingAddress,shippingAddressStat : ${segmentVO.multipleShippingAddressList}">
                                                    <div th:if="${shippingAddress.addressType == 0}" th:each="shippingGoods,shippingGoodsStat : ${shippingAddress.shippingGoodsList}">
                                                        提货:<span th:text="${shippingGoods.goodsTypeName}"></span>
                                                    </div>
                                                    <div th:if="${shippingAddress.addressType == 1}" th:each="shippingGoods,shippingGoodsStat : ${shippingAddress.shippingGoodsList}">
                                                        到货:<span th:text="${shippingGoods.goodsTypeName}"></span>
                                                    </div>
                                                </div>
                                            </td>
                                            <!-- 件数|重量|体积 -->
                                            <td>
                                                <div th:if="${not #lists.isEmpty(segmentVO.multipleShippingAddressList)}" th:each="shippingAddress,shippingAddressStat : ${segmentVO.multipleShippingAddressList}">
                                                    <div th:if="${shippingAddress.addressType == 0}" th:each="shippingGoods,shippingGoodsStat : ${shippingAddress.shippingGoodsList}">
                                                        提货:
                                                        <span th:text="${shippingGoods.num+'件/'}" th:if="${shippingGoods.num!=0}"></span>
                                                        <span th:text="${shippingGoods.weight+'吨/'}" th:if="${shippingGoods.weight!=0}"></span>
                                                        <span th:text="${shippingGoods.volume+'m³'}" th:if="${shippingGoods.volume!=0}"></span>
                                                    </div>
                                                    <div th:if="${shippingAddress.addressType == 1}" th:each="shippingGoods,shippingGoodsStat : ${shippingAddress.shippingGoodsList}">
                                                        到货:
                                                        <span th:text="${shippingGoods.num+'件/'}" th:if="${shippingGoods.num!=0}"></span>
                                                        <span th:text="${shippingGoods.weight+'吨/'}" th:if="${shippingGoods.weight!=0}"></span>
                                                        <span th:text="${shippingGoods.volume+'m³'}" th:if="${shippingGoods.volume!=0}"></span>
                                                    </div>
                                                </div>
                                            </td>
                                             <!-- 要求车长车型 -->
                                             <td>
                                                <span th:each="dict : ${@dict.getType('car_len')}" th:if="${dict.dictValue} == ${segmentVO.carLen}" th:text="${dict.dictLabel+'-'}"></span><span th:each="dict : ${@dict.getType('car_type')}" th:if="${dict.dictValue} == ${segmentVO.carType}" th:text="${dict.dictLabel}"></span>
                                             </td>

                                             <!-- 现金成本分摊 -->
                                            <td>
                                                <div>
                                                    <div>
                                                        <input th:attr="data-invoiceid=${segmentVO.invoiceId}"  type="text" class="AAAAA shareCb" th:id="|A_${segmentVO.segmentId}|" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" onblur="calShareCostSum()" autocomplete="off" style="width:100%;" disabled  placeholder="预付">
                                                    </div>
                                                    <div class="mt10">
                                                        <input th:attr="data-invoiceid=${segmentVO.invoiceId}"  type="text" class="CCCCC shareCb" th:id="|C_${segmentVO.segmentId}|" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" onblur="calShareCostSum()" autocomplete="off" style="width:100%;" disabled  placeholder="到付">
                                                    </div>
                                                    <div class="mt10">
                                                        <input th:attr="data-invoiceid=${segmentVO.invoiceId}"  type="text" class="EEEEE shareCb" th:id="|E_${segmentVO.segmentId}|" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" onblur="calShareCostSum()" autocomplete="off" style="width:100%;" disabled  placeholder="回付">
                                                    </div>
                                                </div>
                                            </td>
                                            <!-- 油卡成本分摊 -->
                                            <td>
                                                <div>
                                                    <input th:attr="data-invoiceid=${segmentVO.invoiceId}"  type="text" class="BBBBB shareCb" th:id="|B_${segmentVO.segmentId}|" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" onblur="calShareCostSum()" autocomplete="off" style="width:100%;" disabled placeholder="预付">
                                                </div>
                                                <div class="mt10">
                                                    <input th:attr="data-invoiceid=${segmentVO.invoiceId}"  type="text" class="DDDDD shareCb" th:id="|D_${segmentVO.segmentId}|" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" onblur="calShareCostSum()" autocomplete="off" style="width:100%;" disabled placeholder="到付">
                                                </div>
                                                <div class="mt10">
                                                    <input th:attr="data-invoiceid=${segmentVO.invoiceId}"  type="text" class="FFFFF shareCb" th:id="|F_${segmentVO.segmentId}|" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" onblur="calShareCostSum()" autocomplete="off" style="width:100%;" disabled placeholder="回付">
                                                </div>
                                            </td>
                                            <td th:id="${segmentVO.segmentId}" th:attr="data-invoiceid=${segmentVO.invoiceId}"></td>
                                            
                                        </tr>
                                    </div>
                                    </tbody>
                                    <!-- <tfoot>
                                        <tr style="background: #FFFCD3;text-align: center">
                                        <td>合计：<span class="fcff" id="calculateTotal1">0</span></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>
                                            <span th:text="${numTotal}"></span>/<span th:text="${weightTotal}"></span>/<span th:text="${volumeTotal}"></span>
                                        </td>
                                    </tr>
                                    </tfoot> -->
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
let entrustlist=[[${list}]]
let entrust=[[${entrust}]]
$(function () {
    entrustlist.forEach((row,i)=>{
        if(entrust.regDate!==null&&row.daohuoRegDate!==null&&row.daohuoRegDate!==undefined){
            let data=countDay(entrust.regDate,row.daohuoRegDate)
            $("#timeO_"+i).text(data)  
        }else{
            $("#timeO_"+i).parent().addClass('timeBorF')
        }

        if(row.daohuoRegDate!==null&&row.daohuoRegDate!==undefined&&row.entrust.receiptDate!==null&&row.entrust.receiptDate!==undefined){
            let data=countDay(row.daohuoRegDate,row.entrust.receiptDate)
            $("#timeT_"+i).text(data)  
        }else{
            $("#timeT_"+i).parent().addClass('timeBorF')
        }
    })
    $('[data-toggle="tooltip"]').tooltip()
});
function countDay(time1,time2){

    let sTime = new Date(time1);     
    let eTime = new Date(time2);  
    let dTime=parseInt(eTime.getTime() - sTime.getTime())
    
    let timeText=""
    if(dTime>(1000 * 3600 * 24)){
        let day= Math.floor(dTime / parseInt(1000 * 3600 * 24))  
        if(day!=0){
            timeText+=day+"天"
        }
        dTime= dTime%parseInt(1000 * 3600 * 24)
    }

    if(dTime>(1000 * 3600)){
        let hour=Math.floor(dTime / parseInt(1000 * 3600 )) 
        if(hour!=0){
            timeText+=hour+"小时"
        }
        dTime= dTime%parseInt(1000 * 3600)
    }
    if(!timeText){
        timeText="1小时内"
    }

    return timeText;

}

function adjust(entrustLotId) {
    var url = ctx + "trace/addPayDetailAdjust?invoiceLotId="+entrustLotId+"&adjustType=1";
    $.modal.open("调账", url);
}

function viewSp() {
    let spNos = [[${spNos}]];
    if (spNos.length > 0) {
        wecom_process(spNos[0]);
    } else {
        $.modal.msgWarning("未找到审批单号")
    }
}
</script>
</body>

</html>