<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('外发抢单')"/>
</head>

<body>
<div class="form-content">
    <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
        <div class="panel-body">
            <!--基础信息 begin-->
            <input name="segmentId" type="hidden" th:value="${segmentId}">
            <input name="outType" value="1" type="hidden"/>
            <div class="row">
                <div class="col-md-6 col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-2">外发金额：</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" id="outPrice" name="outPrice"
                                   oninput="$.numberUtil.onlyNumberTwoDecimal(this)" placeholder="外发金额" autocomplete="off">
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-2">抢单报价截止时间：</label>
                        <div class="col-sm-10">
                            <input type="text" class="time-input form-control" id="cutoffDate" name="cutoffDate"
                                   placeholder="抢单报价截止时间" autocomplete="off" readonly>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-2">毛重：</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" id="grossWeight" name="grossWeight" th:value="${grossWeight}"
                                   oninput="$.numberUtil.onlyNumber(this)" placeholder="毛重" autocomplete="off" required>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-sm-12">
                    <div class="form-group">
                        <label class="col-md-1 col-sm-2">备注：</label>
                        <div class="col-md-11 col-sm-10">
                                <textarea name="memo" id="memo" maxlength="200" class="form-control valid"
                                          rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="fixed-table-body" style="margin: 0px -5px;">
                <table border="0" id="infoTab" class="custom-tab table">
                    <thead>
                        <tr>
                            <th style="width: 3%;"><a class="collapse-link add-alink" onclick="insertRow()" title="新增行">+</a></th>
                            <th style="width: 40%">联系人名称</th>
                            <th style="width: 60%;">联系电话</th>
                        </tr>
                    </thead>
                    <tbody>
                        <div>
                            <td><a class="close-link del-alink" onclick="removeRow(this,0)" title="删除选择行">-</a></td>
                            <td>
                                <div class="input-group">
                                     <input name="lotOutContactList[0].contactName" id="contactName_0" type="text" class="form-control" maxlength="100" autocomplete="off" required>
                                </div>
                            </td>
                            <td>
                                <div class="col-sm-12">
                                    <input name="lotOutContactList[0].contactTel" id="contactTel_0" type="text" class="form-control" autocomplete="off" required>
                                </div>
                            </td>
                        </div>
                    </tbody>
                </table>
            </div>
        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "tms/segment";

    $(function () {
        //电话校验
        $("#contactTel_0").rules("add", {
            isPhoneOrTel: true
        });

    });
    /**
     * 日期插件
     */
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        laydate.render({
            elem: '#cutoffDate', //指定元素
            type: 'datetime',
            format : 'yyyy-MM-dd HH:mm:ss',
            trigger: 'click',
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours=now.getHours();
                this.dateTime.minutes=now.getMinutes();
                this.dateTime.seconds=now.getSeconds();
            },
            done: function(value, date, endDate){
                $("#cutoffDate").val(value);
                //单独校验日期
                $("#form-out-quote").validate().element($("#cutoffDate"))
            }
        });
    });

    var contactIndex = 0;
    /**
     * 新增表格行
     */
    function insertRow(){
        contactIndex++;
        var trTtml =
            '<tr>'
            +       '<td><a class="close-link del-alink" onclick="removeRow(this,'+contactIndex+')" title="删除选择行">-</a></td>'
            +       '<td>'
            +            '<div class="input-group">'
            +               '<input name="lotOutContactList['+ contactIndex +'].contactName" id="contactName_'+ contactIndex +'" type="text" maxlength="100"  class="form-control" autocomplete="off" required>'
            +            '</div>'
            +       '</td>'
            +       '<td>'
            +            '<div class="col-sm-12">'
            +               '<input name="lotOutContactList['+ contactIndex +'].contactTel" id="contactTel_'+ contactIndex +'" type="text" class="form-control" autocomplete="off" required>'
            +            '</div>'
            +        '</td>'
            +   '</tr>';
        $("#infoTab tbody").append(trTtml);

        var i = contactIndex;
        //电话校验
        $("#contactTel_" + i).rules("add", {
            isPhoneOrTel: true
        });
    }

    /**
     *  删除指定表格行
     */
    function removeRow(obj,contactIndex) {
        //如果货品条数大于1，则删除
        if ($("#infoTab tbody").find('tr').length > 1) {
            $("#infoTab tbody").find(obj).closest("tr").remove();
        } else {
            //清空行值
            $("#contactName_" + contactIndex).val("");
            $("#contactTel_" + contactIndex).val("");
        }
    }

    /**
     * 校验
     */
    $("#form-out-quote").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            cutoffDate:{
                required:true,
            },
            outPrice:{
                required:true,
            }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-out-quote").serializeArray();
            $.operate.save(prefix + "/outQuoteAndGrab", data);
        }
    }

</script>
</body>
</html>