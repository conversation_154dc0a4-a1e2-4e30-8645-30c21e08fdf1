<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单调度列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <style>
        .container-div{
            padding: 0px 15px;
        }
        .search-collapse, .select-table{
            margin: 0;
            border-radius:0;
            padding: 5px;
        }
        .search-collapse{
            background-color: #F7F7F7;
        }
        .form-group{
            margin: 0;
        }
        .row + .row{
            margin-top: 5px;
        }
        .btn-group-sm>.btn, .btn-sm{
            padding: 3px 10px;
        }
        .table-striped {
            height: calc(100% - 98px);
            padding-top: 0;
        }
        .dropdownpad{
        padding: 1px 2px;
        }
        .navs .dropdown-menu{
            padding: 10px 0;
            left: 20px;
            top: -10px;
        }
        .btn-xsT{
            padding: 1px 5px;
            font-size: 12px        
        }
        .left-fixed-table-columns, .left-fixed-body-columns{
            z-index: 3;
        }
        .label+.label{
            margin-left: 5px;
        }
        .lHie{
            margin: 0;
            display: flex;
            align-items: center;
            width: 280px
        }
        .lHie .col-md-12 div span {
            vertical-align: middle;
            margin-right: 5px;
        }
        .lHie .col-md-12 div>div {
            max-width: calc(100% - 110px);
        }
        .lHie .col-md-12 div>div>span {
            display: block;
            width: 100%;
            text-align: left;
            overflow:hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            -o-text-overflow:ellipsis;
            
        }

        .mt5 span {
            display: inline-block;
        }
        .tah{
            text-align: right;
            overflow:hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            -o-text-overflow:ellipsis;
        }
        .sblogo{
            display: inline-block;
            margin: 0 0 0 5px;
            width: 18px;
            height: 18px;
            vertical-align: middle;
            border-radius: 4px;
        }
        .bootstrap-table .table:not(.table-condensed), .bootstrap-table .table:not(.table-condensed)>tbody>tr>td, .bootstrap-table .table:not(.table-condensed)>tbody>tr>th, .bootstrap-table .table:not(.table-condensed)>tfoot>tr>td, .bootstrap-table .table:not(.table-condensed)>tfoot>tr>th, .bootstrap-table .table:not(.table-condensed)>thead>tr>td{
            padding: 4px 8px;
        }
        .mt5{
            margin-top: 4px;
        }

        .label-primaryT{
            color: #1ab394;
            background-color: transparent;
            border: 1px solid #1ab394;
        }
        .label-successT{
            color: #1c84c6;
            background-color: transparent;
            border: 1px solid #1c84c6;
        }
        .label-errorT{
            color: #1c84c6;
            background-color: yellow;
            border: 1px solid #1c84c6;
        }
        .label-defaultT{
            color: #5e5e5e;
            background-color: transparent;
            border: 1px solid #5e5e5e;
        }
        .label-dangerT{
            color: #ed5565;
            background-color: transparent;
            border: 1px solid #ed5565;  
        }
        .label-warningT{
            color: #f8ac59;
            background-color: transparent;
            border: 1px solid #f8ac59; 
        }
        .mtdiv{
            display: inline-block;
            vertical-align: middle;
        }
        .mtdiv div{
            text-align: center;
        }
        .mtdiv div:nth-child(2){
            margin-top: 4px;
        }
        .strText span{
            display: block !important;
        }
        .fw{
            font-weight: bold;
        }
        .fcred{
            color: red;
            font-size: 20px;
            font-weight: bold;
            /* vertical-align: middle; */
        }
        .fcgre{
            color: green;
            font-size: 20px;
            font-weight: bold;
            vertical-align: middle;
        }
        .butA{
            color: #000;
        }
        .butA:hover{
            text-decoration:underline;
        }
        .pa2{
            padding: 2px;
            font-weight: 100;
            margin-bottom: 4px;
            display: inline-block;
            margin-right: 5px;
        }
        .textc{
            display: inline-block;
            vertical-align: middle;
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-3">
                        <div class="col-md-6" style="padding-left: 0">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <select name="vbillstatus" id="vbillstatus" class="form-control selectpicker"
                                            aria-invalid="false" data-none-selected-text="运段状态" multiple th:with="type=${segmentStatusList}">
                                        <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6" style="padding-right: 0">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input name="custAbbr" id="custAbbr" placeholder="客户简称/客户发货号" class="form-control" type="text"
                                           maxlength="20" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">调度组：</label>-->
                            <div class="col-sm-12">
                                <select name="isNewInvoice" id="isNewInvoice" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="未确认">
                                    <option value="">-请选择-</option>
                                    <option value="1">未确认</option>
                                     <option value="0">确认</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="invoiceVbillno" id="invoiceVbillno" placeholder="发货单号/运单号" class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>
                   <!-- <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            &lt;!&ndash;                            <label class="col-sm-4">运段号：</label>&ndash;&gt;
                            <div class="col-sm-12">
                                <input name="vbillno" id="vbillno" placeholder="请输入运段号" class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>-->
                    <!--<div class="col-md-2 col-sm-4">
                        <div class="form-group">
&lt;!&ndash;                            <label class="col-sm-4">客户发货单号：</label>&ndash;&gt;
                            <div class="col-sm-12">
                                <input name="custOrderno" id="custOrderno" placeholder="请输入客户发货号" class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>-->
                    <div class="col-md-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="carDriverUser" id="carDriverUser" placeholder="请输入车辆/司机/调度人" class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="deliDetailAddr" id="deliDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                       maxlength="50" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <!-- <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                           <label class="col-sm-4">提货方：</label>
                            <div class="col-sm-12">
                                <input name="deliAddrName" id="deliAddrName" placeholder="请输入提货方" class="form-control"  type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                           <label class="col-sm-4">收货方：</label>
                            <div class="col-sm-12">
                                <input name="arriAddrName" id="arriAddrName" placeholder="请输入收货方"  class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div> -->
                
                    <div class="col-md-1">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">调度组：</label>-->
                            <div class="col-sm-12">
                                <select name="transLineId" id="transLineId" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="调度组" multiple th:with="type=${dispatcherDeptList}">
                                    <option th:each="dict : ${type}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">运营组：</label>-->
                            <div class="col-sm-12">
                                <select name="salesDeptId" id="salesDeptId" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="运营组" multiple th:with="type=${salesDeptList}">
                                    <option th:each="dict : ${type}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
<!--                            <label class="col-sm-4">运输方式：</label>-->
                            <div class="col-sm-12">
                                <select name="transCode" id="transCode" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运输方式" multiple th:with="type=${@dict.getType('trans_code')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row no-gutter">
                    <div class="col-md-2">
                        <div class="form-group">
<!--                            <label class="col-sm-2">要求提货日期：</label>-->
                            <!-- <div class="col-sm-12"> -->
                                <input type="text" style="width: 45%; float: left;" placeholder="要求提货开始日期" class="form-control"
                                       id="reqDeliDateStart"  name="params[reqDeliDateStart]">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" placeholder="要求提货结束日期" class="form-control"
                                       id="reqDeliDateEnd"  name="params[reqDeliDateEnd]">
                            <!-- </div> -->
                        </div>
                    </div>
                    <!-- <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                           <label class="col-sm-2">要求到货日期：</label>
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" placeholder="要求到货开始日期" class="form-control"
                                       id="reqArriDateStart"  name="params[reqArriDateStart]">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" placeholder="要求到货结束日期" class="form-control"
                                       id="reqArriDateEnd"  name="params[reqArriDateEnd]">
                            </div>
                        </div>
                    </div> -->

                    <div class="col-md-2">
                        <div class="form-group">
                            <!-- <div class="col-sm-12"> -->
                                <input type="text" style="width: 45%; float: left;" placeholder="调度开始日期" class="form-control"
                                       id="dispatcherDateStart"  name="params[dispatcherDateStart]">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" placeholder="调度结束日期" class="form-control"
                                       id="dispatcherDateEnd"  name="params[dispatcherDateEnd]">
                            <!-- </div> -->
                        </div>
                    </div>
                
                    <div class="col-sm-3">
                        <div class="form-group">
<!--                            <label class="col-sm-4">提货方地址：</label>-->
                            
                                <div class="col-sm-4">
                                    <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliCityId" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliAreaId" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                           
                            <!-- <div class="col-sm-4">
                                <input name="deliDetailAddr" id="deliDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                       maxlength="50" autocomplete="off">
                            </div> -->
                        </div>
                    </div>
                    <div class="col-sm-3" id="arriPro">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">收货方地址：</label>-->
                            <div class="col-sm-2" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-10" style="padding-right: 0;">
                                <div class="col-sm-4">
                                    <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                                </div>
                            </div>
                            <!-- <div class="col-sm-3">
                                <input name="arriDetailAddr" id="arriDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                       maxlength="50" autocomplete="off">
                            </div> -->
                        </div>
                    </div>
                    <div class="col-sm-3" id="arriProMultiple" style="display: none">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">收货方地址：</label>-->
                            <div class="col-sm-2" >
                                <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-7">
                                <div class="col-sm-12">
                                    <select  name="arriProvinceIds" id="arriProvinceIds"  data-none-selected-text="到货省" class="form-control valid noselect2 selectpicker" aria-invalid="false" multiple>
                                        <option th:each="dict:${provinceList}" th:text="${dict.PROVINCE_NAME}" th:value="${dict.PROVINCE_CODE}"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-3">

                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group" style="text-align: left;">
                            <a class="btn btn-info btn-rounded btn-sm" onclick="changeProvinceSel()" style="margin-right: 40px"><i class="fa fa-map-signs"></i></a>
                            <a class="btn btn-primary btn-rounded btn-sm" id="search" onclick="segSearchPre();"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <input class="form-control" name="params[multiCustOrderno]" placeholder="多客户单号(模糊)查找，用半角逗号分隔">
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary multiple disabled" shiro:hasAnyPermissions="tms:segment:dispatch,tms:fleet:segment:dispatch" onclick="outQuoteBatch()">
                <i class="fa fa-cny" style="font-size: 10px;"></i> 外发报价
            </a>
            <a class="btn btn-primary multiple disabled" shiro:hasAnyPermissions="tms:segment:subsection,tms:fleet:segment:subsection" onclick="subsection()">
                <i class="fa fa-unsorted" style="font-size: 10px;"></i> 拆段
            </a>
            <a class="btn btn-primary single disabled" shiro:hasAnyPermissions="tms:segment:splitQuantity,tms:fleet:segment:splitQuantity" onclick="splitQuantity()">
                <i class="fa fa-gg" style="font-size: 10px;"></i> 拆量
            </a>
            <a class="btn btn-primary multiple disabled" shiro:hasAnyPermissions="tms:segment:dispatch,tms:fleet:segment:dispatch" onclick="dispatch()">
                <i class="fa fa-transgender-alt" style="font-size: 10px;"></i> 调度
            </a>
            <a class="btn btn-primary single disabled" th:if="${!isFleet}" shiro:hasPermission="tms:segment:assign_fleet" onclick="assignFleet()">
                <i class="fa fa-transgender-alt" style="font-size: 10px;"></i> 分配车队
            </a>
            <a class="btn btn-primary single disabled" onclick="editCarrier()" shiro:hasAnyPermissions="tms:trustDeed:editCarrier,tms:fleet:trustDeed:editCarrier">
                <i class="fa fa-pencil"></i> 修改承运商
            </a>
            <a class="btn btn-primary  " onclick="historyPriceBtn()" shiro:hasAnyPermissions="tms:segment:segmentHistoryPrice">
                <i class="fa fa-pencil"></i> 历史价格
            </a>
            <!-- <a class="btn btn-primary single disabled" onclick="paymentRecord()" >
                <i class="fa fa-pencil"></i> 付款记录
            </a> -->

            <a class="btn btn-primary" shiro:hasPermission="tms:segment:dispatch" onclick="ltlDispatch()">
                零担调度
            </a>
            <a class="btn btn-warning" shiro:hasPermission="tms:segment:ledger" onclick="dispatchLedger()">
                调度台账
            </a>
            <a class="btn btn-danger disabled single" onclick="turnDeposit()" shiro:hasPermission="tms:segment:turnDeposit">运费转定金</a>

            <a class="btn btn-warning multiple disabled" shiro:hasAnyPermissions="tms:segment:autoDis" onclick="autoDis()">
                <i class="fa fa-check-circle-o"></i> 自动调度
            </a>

            <a class="btn btn-primary multiple disabled" shiro:hasAnyPermissions="tms:segment:changReqDate,tms:fleet:segment:changReqDate" onclick="changReqDate()">
                <i class="fa fa-cny" style="font-size: 10px;"></i> 修改日期
            </a>


            <div class="dropdown" style="display: inline-block;"
                 shiro:hasAnyPermissions="tms:segment:close,tms:fleet:segment:close,tms:segment:close,tms:segment:getGuidePrice,abcxxx,wecom:sp:setting">
                <button class="btn btn-info dropdown-toggle" style="padding: 4px 12px;" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                    管理员操作
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                    <li>
                        <a class="btn  single disabled" shiro:hasAnyPermissions="tms:segment:close,tms:fleet:segment:close" onclick="segClose()">
                            <i class="fa fa-remove"></i> 关闭
                        </a>
                    </li>
                    <li>
                        <a class="btn  multiple disabled" shiro:hasAnyPermissions="tms:segment:close" onclick="delPush()">
                            <i class="fa fa-remove"></i> 删除推送
                        </a>
                    </li>
                    <li>
<!--                        <a class="btn  single disabled" shiro:hasPermission="tms:segment:getGuidePrice"-->
<!--                           onclick="getGuidePrice()">-->
<!--                            <i class="fa fa-dollar"></i> 获取指导价-->
<!--                        </a>-->
                    </li>
                    <li>
                        <a class="btn " shiro:hasPermission="abcxxx" onclick="xSegment()" rel="opener">
                            一键配载
                        </a>
                    </li>
                    <li>
                        <a class="btn " th:onclick="showSpSettings([[${templateId}]], [[${biz}]])" shiro:hasPermission="wecom:sp:setting">
                            <i class="fa fa-cog"></i> 配置
                        </a>
                    </li>
                </ul>
            </div>

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/js/xlsx.full.min.js}"></script>
<script th:src="@{'/js/wecom.js'}"></script>
<script id="invoiceCtHtml" type="text/template">
    <div class="form-content">
        <div class="row" >
            <label class="col-md-3" style="line-height: 34px;padding-right: 0;">承运商信息：</label>
            <div class="col-md-9">
                <textarea maxlength="200" class="form-control" rows="1" id="carrierInfo"></textarea>
            </div>
        </div>
        <div class="row" >
            <label class="col-md-3" style="line-height: 34px;padding-right: 0;">线路信息：</label>
            <div class="col-md-9">
                <textarea maxlength="200" class="form-control" rows="1" id="addrName"></textarea>
            </div>
        </div>
        <div class="row mt5">
            <label class="col-md-3" style="line-height: 34px;padding-right: 0;">货品信息：</label>
            <div class="col-md-9">
                <textarea maxlength="200" class="form-control" rows="1" id="goodsInfo"></textarea>
            </div>
        </div>
        <div class="row" >
            <label class="col-md-3" style="line-height: 34px;padding-right: 0;">司机车辆信息：</label>
            <div class="col-md-9">
                <textarea maxlength="200" class="form-control" rows="3" id="driverInfo"></textarea>
            </div>
        </div>
 
        <!-- <div class="row">
            <label class="col-md-3" style="line-height: 34px;padding-right: 0;" >运费：</label>
            <div class="col-md-9">
                <div class="input-group input-group-sm">
                    <span class="input-group-addon">￥</span>
                    <input type="text" class="form-control" id="freightCharge">
                </div>
            </div>
        </div> -->
        
        <div class="row" >
            <label class="col-md-3" style="line-height: 34px;padding-right: 0;">到厂时间：</label>
            <div class="col-md-9">
                <textarea maxlength="200" class="form-control" rows="1" id="reqArriDateInfo"></textarea>
            </div>
        </div>

        

    </div>
</script>
<script id="scanCodeHtml" type="text/template">
    <div class="form-content">
        <div class="row" >
            <div class="col-md-12"style="text-align:center;">
                <span  style="font-size:14px;">将二维码 <span style="color: #18a689;">“截图”</span>发送给车主或司机，用 <i class="fa fa-weixin" style="font-size:18px;color:#09BB07;"></i> <span style="color: #09BB07;">微信</span>扫码打开</span>
            </div>

            <div class="col-md-12" style="text-align:center;">
                <img src="" id="scanCodeImg" style='width: 200px;height: 200px;object-fit: contain;'>
            </div>

            <div class="col-md-12 fw" style="text-align:center;font-size:18px" >
                车主： <span id="carrInfo"></span>
            </div>

            <div class="col-md-12 fw" style="text-align:center;font-size:18px" id="driverInfo">
                司机： <span></span>
            </div>

            <div class="col-md-12 fw" style="text-align:center;font-size:18px" id="carrierInfoT"></div>

            <div class="col-md-12 mt5" style="text-align:center;font-size:14px;">
                发货单号: <span id="invoiceVbillnoInfo"></span>
            </div>
        </div>
    </div>
</script>

<script th:inline="javascript">
    // var outQuoteDeadline=[[${@config.getKey('out_quote_deadline')}]]
    //是否是车队
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/segment" : ctx + "tms/segment";

    //运段状态
    var segmentStatusList = [[${segmentStatusList}]];
    //待调度状态
    var toDispatchStatus = [[${toDispatchStatus}]];
    //要求车型
    var carType = [[${@dict.getType('car_type')}]];
    //运输方式
    var transCode = [[${@dict.getType('trans_code')}]];
    //关账记录
    var CloseAccountList = [[${CloseAccountList}]];
    //结算方式
    var balatype = [[${@dict.getType('bala_type')}]];
    var changeFlag = false;

    var dictBillingType = [[${@dict.getType('billing_type')}]];

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                segSearchPre();
            }
        });

        $("#reqDeliDateStart").val(getFrontFormatDate(-1));
        $("#reqDeliDateEnd").val(getFrontFormatDate(1));
        

        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/dispatch",
            detailUrl: prefix + "/subsection",
            showToggle:false,       // 是否显示详细视图和列表视图的切换按钮
            showColumns:true,       // 是否显示隐藏某列下拉框
            modalName: "调度",
            fixedColumns: true,      // 是否启用冻结列（左侧）
            fixedNumber: 4,     // 列冻结的个数（左侧）
            uniqueId: "segmentId",
            height: 560,
            clickToSelect: true,
            showExport: true,
            firstLoad: false,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"调度配载"
            },
            columns: [
                // {
                // checkbox: true
                // },
                {
                    // field: 'checkStatus',  //给多选框赋一个field值为“checkStatus”用于更改选择状态!!!!
                    valign : 'middle',
                    checkbox: true,
                    formatter:function(value,row,index){
                        if(row.isNewInvoice == '1'){
                            return {
                                disabled : true,
                            }
                        }else{
                            return {
                                disabled : false,
                            }
                        }
                        return value;
                    }
                },
                {
                    title: '操作',
                    align: 'left',
                    width: 10,
                    formatter: function(row,value) {
                        if(value.isNewInvoice!='1'){
                            var actions = [];


                            if ([[${@permission.hasAnyPermi('tms:segment:cancel,tms:fleet:segment:cancel')}]] != "hidden") {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="撤销拆段或拆量" onclick="cancel(\''+value.segmentId+'\',\''+value.vbillstatus+'\',\''+value.outGoType+'\',\''+value.isClose+'\',\''+value.parentSeg+'\')"><i class="fa fa-reply" style="font-size: 15px;"></i></a>');
                            }
                            if ([[${@permission.hasPermi('tms:segment:segmentHistoryPrice')}]] !== "hidden" && !isFleet) {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="调度价审核记录" '
                                    + 'onclick="guidePriceCheckList(\'' + value.segmentId + '\')"><i class="fa fa-list" '
                                    + 'style="font-size: 15px;"></i></a>');
                            }
                            if ([[${@permission.hasPermi('tms:segment:segmentHistoryPrice')}]] != "hidden") {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="历史价格" onclick="historyPrice(\''+value.segmentId+'\')"><i class="fa fa-dollar" style="font-size: 15px;"></i></a>');
                            }

                            if ([[${@permission.hasAnyPermi('tms:segment:toplist')}]] != "hidden") {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="排名" onclick="carrierTopList(\''+value.segmentId+'\',\''+value.deliArea+'\',\''+value.arriArea+'\')"><i class="fa fa-bar-chart" style="font-size: 15px;"></i></a>');
                            }
                            if ([[${@permission.hasPermi('tms:segment:outGrabOrder')}]] != "hidden" && value.vbillstatus=="0") {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="外发抢单" onclick="outGrabOrder(\''+value.segmentId+'\',\''+value.vbillstatus+'\',\''+value.outGoType+'\',\''+value.isClose+'\')"><i class="fa fa-paypal" style="font-size: 15px;"></i></a>');
                            }
                            var onDis=[]

                            if(value.outGoType == 2){
                                if ([[${@permission.hasPermi('tms:segment:outQuote')}]] != "hidden" && value.vbillstatus=="0") {
                                    onDis.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="外发报价" onclick="outQuote(\''+value.segmentId+'\',\''+value.vbillstatus+'\',\''+value.outGoType+'\',\''+value.isClose+'\')"><i  class="fa fa-cny" style="font-size: 15px;" ></i></a>');
                                }

                            }

                            if (value.vbillstatus!="0"&&[[${@permission.hasPermi('tms:segment:dispatchDetail')}]] != "hidden") {
                                onDis.push('<a class="btn btn-xs " href="javascript:void(0)" title="调度明细" onclick="dispatchDetail(\''+value.lotId+'\',\''+value.vbillstatus+'\',\''+value.outGoType+'\',\''+value.isClose+'\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                            }


                            if(value.vbillstatus!="0"){
                                let data=JSON.stringify({
                                    //segPackGoodsList:value.segPackGoodsList,
                                    multipleShippingAddressList:value.multipleShippingAddressList,
                                    numCountAdjust:value.numCountAdjust,
                                    numCount:value.numCount,
                                    weightCountAdjust:value.weightCountAdjust,
                                    weightCount:value.weightCount,
                                    volumeCountAdjust:value.volumeCountAdjust,
                                    volumeCount:value.volumeCount,
                                    carLenName:value.carLenName,
                                    carType:value.carType,
                                    isMultiple:value.isMultiple,
                                    deliProName:value.deliProName,
                                    deliAreaName:value.deliAreaName,
                                    deliCityName:value.deliCityName,
                                    arriProName:value.arriProName,
                                    arriAreaName:value.arriAreaName,
                                    arriCityName:value.arriCityName,
                                    //multipleShippingAddressList:value.multipleShippingAddressList,
                                    estimatedArrivalTime:value.estimatedArrivalTime,
                                    carrierName:value.carrierName,
                                    carrierPhone:value.carrierPhone,
                                    fee:value.fee,
                                    carno:value.carno,
                                    driverName:value.driverName,
                                    driverCardId:value.driverCardId,
                                    driverMobile:value.driverMobile
                                }).replace(/\"/g,'&quot;');
                                onDis.push('<a class="btn btn-xs " href="javascript:void(0)" title="复制信息" onclick="getCopy(\''+ data +'\')"><i class="fa fa-copy" style="font-size: 15px;"></i></a>');

                                let dataObj='\''+ value.lotId +'\',\''+ value.driverName +'\',\''+ value.driverMobile +'\',\''+ value.carno +'\',\''+ value.carrierName +'\',\''+ value.carrierPhone +'\',\''+ value.invoiceVbillno +'\',\''+ value.carrierName +'\',\''+ value.driverCardId +'\',\''+ value.carrCardId +'\''
                                onDis.push('<div style="display: inline-block;"><img src="/img/ewm.png" class="sblogo" title="二维码"  onclick="scanCode('+dataObj+')"/></div>')
                            }


                            onDis.push('<div class="btn btn-xsT dropdown navs"><div class="dropdownpad" data-toggle="dropdown"> <i class="fa fa-angle-down"></i></div><ul class="dropdown-menu">' + actions.join('') +'</ul></div>');

                            onDis.push(`<div class="mtdiv">`)
                            if (value.outGoType == 0) {
                                onDis.push(`<div><span style="padding:1px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`)
                                onDis.push(`外发报价`)
                                onDis.push(`"><img src="/img/wg.png" class="sblogo" style="margin:0;"/></span></div>`)
                            }else if (value.outGoType == 1) {
                                onDis.push(`<div><span style="padding:1px;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`)
                                onDis.push(`外发抢单`)
                                onDis.push(`"><img src="/img/wg.png" class="sblogo" style="margin:0;"/></span></div>`)
                            }


                            onDis.push(`</div>`)
                            let text=""
                            if(value.ifYmmBack == 1){
                                text+= '<img src="/img/bs/ymm.png" class="sblogo" style="width:40px;height:auto"/>'
                            }
                            // if(value.ifYmmBack == 0){
                            //     text+= '<img src="/img/bs/hll.png" class="sblogo" style="width:40px;height:auto"/>'
                            // }

                            return onDis.join('')+text;
                        }
                    }
                },
                {
                    title: '发货单号',
                    field: 'invoiceVbillno',
                    align: 'left',
                    switchable: false,
                    width: 10,
                    formatter: function(value,row){
                        var result = $.table.tooltip(value,14);

                        if (row.memo) {
                            result += `<span class="label label-success ml5 pa2" data-toggle="tooltip"
                                             data-container="body" data-placement="top" data-html="true"
                                             title="${row.memo}">备</span>`
                        }

                        if(row.isNewInvoice!='1'){
                            if(row.lotId == null){
                                // result = $.table.tooltip(value,14);
                            }else{
                                if (row.lotSbSyn == 2||row.lotSbSyn == 20){
                                    result+='<img src="/img/sb.png" class="sblogo"/>'
                                }else if (row.lotSbSyn != null){
                                    result+='<img src="/img/hsb.png" class="sblogo" data-toggle="tooltip" data-placement="left" data-html="true" title="'
                                    if (row.lotSbSyn == 0) {
                                        result += '待审验'
                                    } else if (row.lotSbSyn == 1||row.lotSbSyn == 30) {
                                        result += row.lotSbMsg
                                    } else if (row.lotSbSyn == 10) {
                                        result += '审验中'
                                    }
                                    result += '"/>'
                                }
                                let auditStatusTxt = ['待提审','审核中','审核通过','审核失败'];
                                if (row.lotG7End == 2) {
                                    let et = '';
                                    if (row.g7LotQst) {
                                        et = row.g7LotQst + "；"
                                    }
                                    if (row.g7CarExt && row.g7Corp) {
                                        let g7CarExt = JSON.parse(row.g7CarExt);
                                        if (g7CarExt.ysz) {
                                            let auditStatus = g7CarExt.ysz[row.g7Corp.toLowerCase()];
                                            if (auditStatus != 2 && auditStatus != null) {
                                                let _st_t = auditStatusTxt[auditStatus]
                                                et = et + "车辆道路运输证【" + _st_t + "】；";
                                            }
                                        }
                                    }
                                    if (row.g7DriverExt && row.g7Corp) {
                                        let g7DriverExt = JSON.parse(row.g7DriverExt);
                                        if (g7DriverExt.zgz) {
                                            let auditStatus = g7DriverExt.zgz[row.g7Corp.toLowerCase()];
                                            if (auditStatus != 2 && auditStatus != null) {
                                                let _st_t = auditStatusTxt[auditStatus]
                                                et = et + "司机从业资格证【" + _st_t + "】；";
                                            }
                                        }
                                    }
                                    result += ' <span class="label '+ (et?'label-errorT':'label-successT') +'" style="padding:1px;vertical-align: middle;"';
                                    if (et) {
                                        result += ' data-toggle="tooltip" data-container="body" data-placement="right" data-html="true" title="' + et + '"';
                                    }
                                    result += '>G7</span>'
                                } else if (row.lotG7Syn != null) {
                                    result += ' <span class="label label-dangerT" style="padding:1px;vertical-align: middle;" data-container="body" data-toggle="tooltip" data-placement="right" data-html="true" title="';
                                    if (row.g7LotQst) {
                                        result = result + row.g7LotQst + "；"
                                    }
                                    if (row.g7CarExt && row.g7Corp) {
                                        let g7CarExt = JSON.parse(row.g7CarExt);
                                        if (g7CarExt.ysz) {
                                            let auditStatus = g7CarExt.ysz[row.g7Corp.toLowerCase()];
                                            if (auditStatus != 2 && auditStatus != null) {
                                                let _st_t = auditStatusTxt[auditStatus]
                                                result = result + "车辆道路运输证【" + _st_t + "】；";
                                            }
                                        }
                                    }
                                    if (row.g7DriverExt && row.g7Corp) {
                                        let g7DriverExt = JSON.parse(row.g7DriverExt);
                                        if (g7DriverExt.zgz) {
                                            let auditStatus = g7DriverExt.zgz[row.g7Corp.toLowerCase()];
                                            if (auditStatus != 2 && auditStatus != null) {
                                                let _st_t = auditStatusTxt[auditStatus]
                                                result = result + "司机从业资格证【" + _st_t + "】；";
                                            }
                                        }
                                    }
                                    if (row.lotG7Syn == 0) {
                                        result += '等待G7审验'
                                    } else if (row.lotG7Syn == 1) {
                                        result += row.lotG7Msg
                                    } else if (row.lotG7Syn == 2) {
                                        if (row.lotG7Start == null || row.lotG7Start == 0) {
                                            result += '等待推送【发车】'
                                        } else if (row.lotG7Start == 1) {
                                            result += '【发车】推送失败'
                                        } else if (row.lotG7End == null || row.lotG7End == 0) {
                                            result += '等待推送【到达】'
                                        } else if (row.lotG7End == 1) {
                                            result += '【到达】推送失败'
                                        }
                                    } else if (row.lotG7Syn == 7) {
                                        result += '运单已作废'
                                    }
                                    result += '">G7</span>'
                                }
                            }

                            result+= '<br/><div class="mt5">';
                            if (row.segType == 0) {
                                result+='<span class="label label-primary" style="padding:2px;">拆</span>'
                            } else if(row.segType == 1){
                                result+='<span class="label label-primary" style="padding:2px;">拆</span>'
                            }

                            if(row.vbillstatus=='3'){
                                if(row.ifReceipt == '1') {
                                    result+= '<span class="label label-primaryT">已正本回单</span>'
                                }else {
                                    if(row.receiptConfirmFlag == '1') {
                                        result+= '<span class="label label-warningT">已回单确认</span>'
                                    }else {
                                        if(row.receiptStatus == '1') {
                                            result+= '<span class="label label-successT">已上传回单</span>'
                                        }else {
                                            result+= '<span class="label label-dangerT">未上传回单</span>'
                                        }
                                    }
                                }
                            }
                            result+= '</div>';


                        }

                        result += '<div class="mt5">'
                        if(row.isNewInvoice==1){
                            result+= '<span class="label label-dangerT">未确认</span></div>'
                        }
                        // else if(row.isNewInvoice==0){
                        //     result+= '<span class="label label-primaryT">确认</span></div>'
                        // }
                        // if(row.entrustVbillstatus==0){
                        //     result +=  '<span class="label label-dangerT">待确认</span>';
                        // }else if(row.entrustVbillstatus==1){
                        //     result +=  '<span class="label label-warningT">已确认</span>';
                        // }else if(row.entrustVbillstatus==2){
                        //     result +=  '<span class="label label-successT">已提货</span>';
                        // }else if(row.entrustVbillstatus==3){
                        //     result +=  '<span class="label label-successT">已到货</span>';
                        // }else if(row.entrustVbillstatus==5){
                        //     result +=  '<span class="label label-dangerT">关闭</span>';
                        // }else if(row.entrustVbillstatus==6){
                        //     result +=  '<span class="label label-warningT">分配车队</span>';
                        // }
                        
                        return result;
                    }
                },
               

                {
                    title: '客户信息',
                    field: 'custOrderno',
                    align: 'left',
                    switchable: false,
                    cellStyle: formatTableUnit,
                    formatter: function(value, row, index){
                        let htmlText;
                        if(value){
                            htmlText= $.table.tooltip(value,14);
                        }else{
                            htmlText='-'
                        }
                        
                        return htmlText+'<br/>'+row.custAbbr
                    }
                },
                {
                    title: '调度状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(row,value) {
                        var context = '';
                        segmentStatusList.forEach(function (v) {
                            let payableWriteOffStatus="";
                            if(value.carrierBalaType == 1){
                                if(value.payableWriteOffStatus==0){
                                    payableWriteOffStatus= '<span class="label label-danger ml5 pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="未付款">未</span>';
                                }else if(value.payableWriteOffStatus==1){
                                    payableWriteOffStatus= '<span class="label label-warning ml5 pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="部分付款">部</span>';
                                }else if(value.payableWriteOffStatus==2){
                                    payableWriteOffStatus= '<span class="label label-success ml5 pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="已付款">已</span>';
                                }
                            }

                            if (v.value == value.vbillstatus) {
                                if ("0" == value.vbillstatus) {
                                    context = '<span class="label label-primaryT">'+v.context+'</span>'+payableWriteOffStatus;
                                }else if ("1" == value.vbillstatus) {
                                    context = '<span class="label label-warningT">'+v.context+'</span>'+payableWriteOffStatus;
                                }else if ("2" == value.vbillstatus) {
                                    context = '<span class="label label-infoT">'+v.context+'</span>'+payableWriteOffStatus;
                                }else if ("3" == value.vbillstatus) {
                                    context = '<span class="label label-successT">'+v.context+'</span>'+payableWriteOffStatus;
                                }else if ("4" == value.vbillstatus) {
                                    context = '<span class="label label-inverseT">'+v.context+'</span>'+payableWriteOffStatus;
                                }else if ("6" == value.vbillstatus) {
                                    context = '<span class="label label-warningT">'+v.context+'</span>'+payableWriteOffStatus;
                                }else if ("11" === value.vbillstatus || "12" === value.vbillstatus) {
                                    context = '<span class="label label-primaryT">'+v.context+'</span>'+payableWriteOffStatus;
                                }else if ("99" === value.vbillstatus) {

                                }
                                return false;
                            }
                        });
                        return context;
                    }
                },

                {
                    title: '要求提/到货信息',
                    field: 'reqDeliDate',
                    align: 'left',
                    switchable: false,
                    width: 10,
                    formatter: formatterInformation
                },
                {
                    title: '提/到货地址',
                    field: 'deliProName',
                    align: 'left',
                    switchable: false,
                    formatter: function(value, row, index){
                        let html=[]
      
                        var isMultiple = row.isMultiple
                        if(isMultiple == 0) {  //0不是
                            let htmlText=``
                            if(row.deliProName){
                                htmlText+=row.deliProName;
                            }
                            if(row.deliCityName&&row.deliCityName != '市辖区'){
                                htmlText+=row.deliCityName;
                            }
                            if(row.deliAreaName){
                                htmlText+=row.deliAreaName;
                            }
                            if(row.deliDetailAddr){
                                htmlText+=row.deliDetailAddr;
                            }
                            html.push('<div class="strText" style="display: inline-block;vertical-align: middle;">'+$.table.tooltip(htmlText,6)+'</div>');
                        }else {
                            //TODO
                            if (row.multipleShippingAddressList) {
                                let list = row.multipleShippingAddressList;
                                for (const elem of list) { //地址
                                    if (elem.addressType === 0) {
                                        let htmlText=``
                                        if(elem.provinceName){
                                            htmlText+=elem.provinceName;
                                        }
                                        if(elem.cityName&&elem.cityName != '市辖区'){
                                            htmlText+=elem.cityName;
                                        }
                                        if(elem.areaName){
                                            htmlText+=elem.areaName;
                                        }
                                        if(elem.detailAddr){
                                            htmlText+=elem.detailAddr;
                                        }

                                        html.push('<div class="strText" style="display: inline-block;vertical-align: middle;">'+$.table.tooltip(htmlText,6)+'</div>');
                                    }
                                }

                            }
                        }
                        
                        
                        if(isMultiple == 0) {  //0不是

                            let htmlText=``
                            if(row.arriProName){
                                htmlText+=row.arriProName;
                            }
                            if(row.arriCityName&&row.arriCityName != '市辖区'){
                                htmlText+=row.arriCityName;
                            }
                            if(row.arriAreaName){
                                htmlText+=row.arriAreaName;
                            }
                            if(row.arriDetailAddr){
                                htmlText+=row.arriDetailAddr;
                            }
                            html.push('<div class="strText" style="display: inline-block;vertical-align: middle;">'+$.table.tooltip(htmlText,6)+'</div>');
                            }else {
                            if(row.multipleShippingAddressList){
                                let list = row.multipleShippingAddressList;
                                for (const elem of list) {
                                    if (elem.addressType === 1) {

                                        let htmlText=``

                                        if(elem.provinceName){
                                            htmlText+=elem.provinceName;
                                        }
                                        if(elem.cityName&&elem.cityName != '市辖区'){
                                            htmlText+=elem.cityName;
                                        }
                                        if(elem.areaName){
                                            htmlText+=elem.areaName;
                                        }
                                        if(elem.detailAddr){
                                            htmlText+=elem.detailAddr;
                                        }

                                        html.push('<div class="strText" style="display: inline-block;vertical-align: middle;">'+$.table.tooltip(htmlText,6)+'</div>');

                                    }
                                }
                            }
                        }
                        
                        return html.join("<br/>")
                    }
                },
                {
                    title: '货量/车型',
                    field: 'carLenName',
                    align: 'left',
                    width: 10,
                    formatter: function(value, row, index){
                        var goodsName = row.goodsName;
        
                        /*if(row.segPackGoodsList){
                            $.each(row.segPackGoodsList, function(i, item) {
                                goodsName += item.goodsName + "&nbsp;"
                            })
                        }else if(row.multipleShippingAddressList){
                            $.each(row.multipleShippingAddressList, function(i, elem) {
                                let goodsList = elem.shippingGoodsList
                                $.each(goodsList, function(i, item) {
                                    goodsName = goodsName + item.goodsName
                                })
                            })
                        }*/
                        let dataName=[];

                        let num = row.numCountAdjust != 0 && $.common.isNotEmpty(row.numCountAdjust) ? row.numCountAdjust : row.numCount
                        let weight = row.weightCountAdjust != 0 && $.common.isNotEmpty(row.weightCountAdjust) ? row.weightCountAdjust : row.weightCount
                        let volume  = row.volumeCountAdjust != 0 && $.common.isNotEmpty(row.volumeCountAdjust) ? row.volumeCountAdjust : row.volumeCount

                        if(num){
                            dataName.push(num+'件');
                        }
                        if(weight){
                            dataName.push(weight+'吨');
                        }
                        if(volume){
                            dataName.push(volume+'m³');
                        }

                        let isToAndFro = ''
                        if (row.isToAndFro == 1) {
                            isToAndFro = '<span class="label label-primaryT ml5">往返</span>'
                        }

                        return $.table.tooltip(goodsName + '(' + dataName.join("|") + ')', 14) + isToAndFro
                            + `<br/>` + row.carLenName + `米` + $.table.selectDictLabel(carType, row.carType);
                    }
                },
                {
                    title: '成本价(元)',
                    field: 'costPrice',
                    align: 'right',
                    formatter: function(value, row, index) {
                        // 获取票据类型文本（如果有）
                        let costBillingType = row.costBillingType
                            ? dictBillingType.find(itm => itm.dictValue == row.costBillingType)?.dictLabel
                            : null;

                        let formattedValue
                        // 如果金额为 null 或 undefined，直接返回 '-'
                        if (value == null) {
                            formattedValue = '暂无';
                        }else {
                            // 金额格式化
                            formattedValue = value.toLocaleString('zh', { style: 'currency', currency: 'CNY' });
                        }

                        let html = '';
                        if (row.segmentId) {
                            html = `<a class="butA" href="javascript:void(0)" title="调整成本价" onclick="adjustCostPrice('${row.segmentId}')">${formattedValue}</a>`;
                        }else{
                            html = formattedValue
                        }

                        // 如果票据类型存在，前面加上提示标签
                        if (costBillingType && row.costBillingType != '6') {
                            let tag = `<span class="label label-warning" style="padding:2px;cursor: pointer" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="${costBillingType}">票</span>&nbsp;`;
                            return tag + html;
                        }

                        // 否则只返回金额
                        return html;
                    }
                },

                {
                    title: '指导价/偏差率',
                    field: 'guidingPrice',
                    align: 'left',
                    formatter: function(value, row, index){
                        let text=[]
                        // if (row.specialGuidingPrice != null) {
                        //     let guidingPrice=row.specialGuidingPrice.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        //     text.push( $.table.tooltip(guidingPrice)
                        //         + '<span class="label label-warning ml5 pa2" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="特殊指导价">特</span>')
                        //
                        //     value = row.specialGuidingPrice
                        // }else if(value != null){
                        //     let guidingPrice=value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        //     text.push( $.table.tooltip(guidingPrice) )
                        // }else{
                        //     if (row.vbillstatus === '0' && row.expGuidingPrice != null) {
                        //         text.push($.table.tooltip(row.expGuidingPrice.toLocaleString('zh', {style: 'currency', currency: 'CNY'})))
                        //     }else {
                        //         text.push($.table.tooltip(value))
                        //     }
                        //
                        // }
                        let guidingPrice = '暂无';

                        let gValue = null

                        if (row.vbillstatus != '0') {
                            if (row.lotGuidingPrice != null) {
                                guidingPrice = row.lotGuidingPrice.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                                gValue = row.lotGuidingPrice
                            }

                        }else {
                            if(value != null){
                                guidingPrice = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                                gValue = value
                            }
                        }

                        let b =  [[${@permission.hasAnyPermi('tms:segment:adjustGuidingPrice')}]] != "hidden"
                        if (row.isNewInvoice == '1' || row.vbillstatus != '0' || !b) {
                            text.push(guidingPrice)
                        }else{
                            text.push(`<a class="butA" href="javascript:void(0)" title="调整指导价" onclick="adjustGuidingPrice('${row.segmentId}')">${guidingPrice}</a>` );
                        }

                        let compFee=row.compFee;
                        if(gValue!=null&&gValue!=0){
                            let num=((compFee-gValue)/gValue*100).toFixed(2);
                            if(num == 0){
                                text.push(num+'%')
                            }else if(num>0){
                                text.push(' <i class="fa fa-long-arrow-up fcred" aria-hidden="true"></i>'+num+'%')

                            }else{
                                text.push(' <i class="fa fa-long-arrow-down fcgre" aria-hidden="true"></i>'+num+'%')
                            }
                        }else {
                            text.push('-')
                        }


                        return text.join("<br/>");
                    }
                },
                {
                    title: '运费/税金',
                    field: 'netPayDetail',
                    align: 'left',
                    formatter: function(value, row, index){
                        let text=[]
                        let feeNum="";
                        if(row.fee != null){
                            let fee=new Number(row.fee).toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            feeNum=$.table.tooltip(fee)
                        }else{
                            feeNum=$.table.tooltip(row.fee)

                        }
                        text.push("<div>")
                        let adjustFreight=[[${@permission.hasAnyPermi('tms:segment:adjustFreight')}]];
                        if(adjustFreight!= "hidden"&&row.vbillstatus!='0'&&row.receiptConfirmFlag!=1){
                            text.push('<a class="butA" href="javascript:void(0)" title="调整运费" onclick="adjust(\''+row.segmentId+'\')">'+feeNum+'</a>' )
                        }else{
                            text.push(feeNum)
                        }
                        if (row.lotSpStatus == 1) {
                            text.push(" <a href='javascript:wecom_process(\"" + row.lotSpNo + "\")'>审批中</a>")
                        } else if (row.lotSpStatus == 2) {
                            text.push(" <a href='javascript:wecom_process(\"" + row.lotSpNo + "\")'>已通过</a>")
                        } else if (row.lotSpStatus == 4) {
                            text.push(" <a href='javascript:wecom_processx(\"" + row.lotSpNo + "\",\"" + row.lotSpMemo + "\")'>已放行</a>")
                        } else if (row.lotSpStatus == 3) {
                            text.push(" <a href='javascript:wecom_process(\"" + row.lotSpNo + "\")'>已驳回</a>")
                            /*[# th:if="${@shiroUtils.getSubject().isPermitted('tms:segment:release')}"]*/
                            text.push(" <a href='javascript:free(\""+row.lotId+"\")'>放行</a>")
                            /*[/]*/
                        }
                        text.push(row.entrustLotId)
                        text.push("</div>");

                        if (value) {
                            let netPayDetail=value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            text.push("<div>" + $.table.tooltip(netPayDetail) + "</div>");
                        }

                        return text.join("");
                    }
                },
                {
                    title: '定金',
                    field: 'depositAmount',
                    formatter: function(value, row, index) {
                        if (value != null) {
                            let st = row.depositStatus;
                            let text = "<span class='label label-success' style='padding: 1px 2px' title='";
                            if (st === 0) {
                                text += '新建'
                            } else if (st === 6) {
                                text += '已申请';
                            } else if (st === 4) {
                                text += '已核销';
                            } else if (st === 7) {
                                text += '核销中';
                            } else if (st === 3) {
                                text += '部分核销';
                            }
                            if (row.depositRecvStatus == 1) {
                                text += "/已核账";
                            } else if (row.depositRecvStatus == 2) {
                                text += "/已加包";
                            }
                            text = text + "'>" + value.toLocaleString('zh', {style: 'currency', currency: 'CNY'}) + "</span>";
                            if (st === 0 || st === 3) { // 新建或部分核销状态可修改
                                /*[# th:if="${@shiroUtils.getSubject().isPermitted('tms:segment:changeDeposit')}"]*/
                                if (row.depositRecvStatus == 0) {
                                    text = text + "<br><a class=\"label label-successT\" style='padding: 1px;' href='javascript:changeDeposit(\"" + row.lotId + "\")'>调整</a>"
                                }
                                /*[/]*/
                                /*[# th:if="${@shiroUtils.getSubject().isPermitted('tms:segment:turnDeposit')}"]*/
                                text = text + (text.indexOf('<br>') < 0 ? '<br>' : '') + '<a class="label label-warningT" style="padding: 1px" href="javascript:turnFamiliar('+index+');" title="转成熟车定金">转</a>'
                                /*[/]*/
                            }
                            return text;
                        }
                    }
                },
                {
                    title: '承运商信息',
                    field: 'carrierName',
                    align: 'left',
                    formatter: function(value, row, index){
                        let htmlText='';

                        htmlText+= '<div><span class="tah" style="text-align: left;width: 80px;display: inline-block;vertical-align: middle;" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="'+$.table.tooltip(row.carrierName)+'">'+$.table.tooltip(row.carrierName)+'</span>'

                        if(row.carrierBalaType == 2){
                            htmlText+= '<span class="label label-warning" style="padding:1px;margin-left: 5px;vertical-align: middle;">月</span><br/>'
                        }else if(row.carrierBalaType == 1){
                            htmlText+= '<span class="label label-success" style="padding:1px;margin-left: 5px;vertical-align: middle;">单</span><br/>'
                        }else{
                            htmlText+= '<br/>'
                        }
                        htmlText+= $.table.tooltip(row.carrierPhone);
                        if (row.carrCheckStatus != null) {
                            if (row.carrCheckStatus==1) {
                                htmlText += '<span class="label label-warningT" title="已审批通过" style="padding:1px;margin-left: 5px;vertical-align: middle;">审</span>';
                            } else {
                                htmlText += '<span class="label label-dangerT" title="暂未审批通过" style="padding:1px;margin-left: 5px;vertical-align: middle;">新</span>'
                            }
                        }
                        htmlText += '</div>';
                        return htmlText;
                        // return $.table.tooltip(row.carrierName)+'<br/>'+ $.table.tooltip(row.carrierPhone)
                    }
                },

                {
                    title: '车牌',
                    field: 'carno',
                    align: 'left',
                    formatter: function(value, row, index){
                        let htmlText='';

                        htmlText+='<div>'+$.table.tooltip(row.carno)

                        if(row.g7SynCar==1){
                            htmlText+='<br/><span style="color:#ed5565" data-toggle="tooltip" data-placement="top" data-container="body" data-html="true" title="'+(row.g7MsgCar||"")+'" onclick="editCar('+ '\'' + row.carnoId + "\', \'" + row.carCheckStatus + '\')">（不合规）<i class="fa fa-edit" style="font-size: 15px;"></i></span></div>'
                        }else{
                            htmlText+='</div>'
                        }

                        return htmlText
                    }
                },
                {
                    title: '司机',
                    field: 'driverName',
                    align: 'left',
                    formatter: function(value, row, index){
                        let htmlText='';
                        htmlText+='<div>'+$.table.tooltip(row.driverName)+'<br/>'+$.table.tooltip(row.driverMobile)

                        if(row.g7SynDriver==1){
                            htmlText+='<br/><span style="color:#ed5565" data-toggle="tooltip" data-placement="top" data-container="body" data-html="true" title="'+(row.g7MsgDriver||"")+'" onclick="editDriver('+ '\'' + row.driverId + "\', \'" + row.driverCheckStatus + '\')">（不合规）<i class="fa fa-edit" style="font-size: 15px;"></i></span></div>'
                        }else{
                            htmlText+='</div>'
                        }
                        return htmlText
                    }
                },
                {
                    title: '收款人信息',
                    field: 'bankAccount',
                    align: 'left',
                    formatter: function(value, row, index){
                        let htmlText='';
                        if(row.bankAccount&&row.bankCard){
                            htmlText+='<div>'+$.table.tooltip(row.bankAccount)

                            if(row.sbPayeeSynJh==2||row.sbPayeeSynMy==2){
                                let msg=(row.sbPayeeMsgJh?row.sbPayeeMsgJh:"")+(row.sbPayeeMsgMy?row.sbPayeeMsgMy:"")
                                htmlText+='<span style="color:#ed5565" data-toggle="tooltip" data-placement="top" data-container="body" data-html="true" title="'+msg+'">（不合规）</span>'
                            }
                            if([[${@permission.hasPermi('tms:segment:adjustReceiver')}]] != "hidden"){
                                htmlText+='<span style="color:#ed5565" onclick="addPayeeEvent(\''+row.carrierId+'\',\'' + row.lotId+'\',\'' + row.carrBankId+'\')"><i class="fa fa-edit" style="font-size: 15px;"></i></span>';
                            }

                            htmlText+='<br/>'+$.table.tooltip(row.bankCard)+'</div>'

                            return htmlText
                        }else if(row.vbillstatus=='1'){
                            if([[${@permission.hasPermi('tms:segment:adjustReceiver')}]] != "hidden") {
                                return '<span style="color:#ed5565" onclick="addPayeeEvent(\'' + row.carrierId + '\',\'' + row.lotId + '\')"><i class="fa fa-edit" style="font-size: 15px;"></i></span>'
                            }else{
                                return '-';
                            }
                        }

                    }
                },
                {
                    title: '车队信息',
                    field: 'params.fleetInfo',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },


                {
                    title: '实际车长车型',
                    field: 'actCarLen',
                    align: 'left',
                    formatter: function(value, row, index){
                        return (row.actCarLen?($.table.tooltip(row.actCarLen)+"米"):$.table.tooltip(row.actCarLen))+ $.table.tooltip(row.actCarType)
                    }
                },

                {
                    title: '调度组',
                    field: 'transLineName',
                    align: 'left',
                    formatter: function(value,row){
                        let retStr = [];
                        if(row.isNewInvoice!='1' && row.vbillstatus == '0'){
                            if ([[${@permission.hasAnyPermi('tms:segment:editDispatcherGroup')}]] != "hidden"){
                                retStr.push(`<span style="color:#1890ff" onclick="editDispatcherGroup('${row.segmentId}','${row.vbillstatus}')">`);
                                // retStr.push('<span style="color:#1890ff" onclick="editDispatcherGroup(\'');
                                // retStr.push(row.segmentId);
                                // retStr.push('\')">');
                                retStr.push(value);
                                // retStr.push( '<i class="fa fa-edit" title="修改调度组"></i></span>');
                            }else{
                                retStr.push(value);
                            }


                            return retStr.join("")+'<i class="fa fa-edit" style="font-size: 15px;"></i>';
                        }else{
                            retStr.push(value);
                            return retStr.join("")
                        }
                    }
                },

                {
                    title: '调度信息',
                    field: 'dispatcherName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.tooltip(row.dispatcherName)+'<br/>'+$.table.tooltip(row.dispatcherDate)
                    }
                },

                {
                    title: '订单确认信息',
                    field: 'confirmUserName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.tooltip(row.confirmUserName)+'<br/>'+$.table.tooltip(row.confirmDate)
                    }
                },

                {
                    title: '下单信息',
                    field: 'orderName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.tooltip(row.orderName)+'<br/>'+$.table.tooltip(row.orderDate)
                    }
                },



                {
                    title: '运营组',
                    field: 'salesDept',
                    align: 'left',
                },

                {
                    title: '运输方式/计价方式',
                    field: 'a1',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(transCode, value.transCode)+'<br/>'+(value.pricingMethod==0?'按吨':value.pricingMethod==1?'按方':value.pricingMethod==2?'按件':'')
                    }
                },
                {
                    title: '运单号',
                    field: 'lotNo',
                    align: 'left',
                },
                //
                // {
                //     title: '发货单备注',
                //     field: 'memo',
                //     align: 'left',
                //     formatter: function status(row,value) {
                //         return $.table.tooltip(value.memo);
                //     }
                // },

            ]
        };
        $.table.init(options);

        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        //默认设置为待调度、一级审核、二级审核
        var arr = [];
        arr.push(segmentStatusList[0].value);
        arr.push(segmentStatusList[1].value);
        // arr.push(segmentStatusList[6].value);
        // arr.push(segmentStatusList[7].value);
        $('#vbillstatus').selectpicker('val',arr);
        segSearchPre();

        var showTooltip = function() {
            $('.tooltip').remove(); // This line removes any currently showing tootltips
            $(this).tooltip('show');
        };
        var hideTooltip = function() {
            $(this).tooltip('hide');
        };
        $("[data-toggle='tooltip']").tooltip({
            trigger: 'manual'
        }).focus(showTooltip).hover(showTooltip, hideTooltip);

    });


    // $(document).on('turbolinks:load', function() {
    //     $('[data-toggle="tooltip"]').tooltip();
    // })

    /**
     * 日期插件
     */
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        var reqDeliDateStart = laydate.render({
            elem: '#reqDeliDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqDeliDateEnd = laydate.render({
            elem: '#reqDeliDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqArriDateStart = laydate.render({
            elem: '#reqArriDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqArriDateEnd = laydate.render({
            elem: '#reqArriDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });

        var reqArriDateStart = laydate.render({
            elem: '#dispatcherDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqArriDateEnd = laydate.render({
            elem: '#dispatcherDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
    });

    /**
     * 拆段
     */
    function subsection(){
        //关账判断
        /*var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }*/

        var segmentIds = $.table.selectColumns("segmentId").join();

        if (segmentIds.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (checkSegmentStatus()) {
            $.modal.alertWarning("存在非待调度状态的运段，无法拆段！");
            return;
        }
        if (checkOutGoType()) {
            $.modal.alertWarning("存在已外发的运段，无法拆段！");
            return;
        }
        //判断是否为多装多卸
        if(checkSegmentTransCode()) {
            //$.modal.alertWarning("存在多装多卸的运单，无法拆段！");
            //return;
            if ($.btTable.bootstrapTable('getSelections').length > 1) {
                $.modal.alertWarning("多装多卸的单据不支持批量拆段！");
                return;
            }
        }
        $.modal.openTab('拆段', prefix + "/subsection/" + segmentIds);
    }

    /**
     * 拆量
     */
    function splitQuantity(){
       /* var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }*/

        var segmentIds = $.table.selectColumns("segmentId").join();
        if (segmentIds.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (checkSegmentStatus()) {
            $.modal.alertWarning("存在非待调度状态的运段，无法拆量！");
            return;
        }
        if (checkOutGoType()) {
            $.modal.alertWarning("该运段已外发，无法拆量！");
            return;
        }
        //判断是否为多装多卸
        //if(checkSegmentTransCode()) {
        //    $.modal.alertWarning("存在多装多卸的运单，无法拆段！");
        //    return;
        //}
        $.modal.openTab("拆量",prefix + "/splitQuantity/" + segmentIds);
    }

    /**
     * 撤销拆段或拆量
     */
    function cancel(segmentId,vbillstatus, outGoType,isClose,parentSeg) {
        if(isClose == 1){
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }

        if (vbillstatus != 0) {
            $.modal.alertWarning("该运段已调度，无法撤销！");
            return;
        }
        if (outGoType != 2) {
            $.modal.alertWarning("该运段已外发，无法撤销！");
            return;
        }
        if (parentSeg == null || parentSeg=='null') {
            $.modal.alertWarning("初始运段，无法撤销！");
            return;
        }
        //判断是否为多装多卸
        //if(checkSegmentTransCode()) {
        //    $.modal.alertWarning("存在多装多卸的运单，无法拆段！");
        //    return;
        //}
        $.modal.confirm("确认要撤销该条运段吗?", function() {
            $.operate.post(ctx + "tms/segment/cancel", { "segmentId": segmentId });
        });

    }

    /**
     * 调度
     */
    function dispatch(){
        /*var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }*/


        var invoiceVbillno = $.table.selectColumns("invoiceVbillno");

        var segmentIdList = $.table.selectColumns("segmentId");

        if (invoiceVbillno.length != segmentIdList.length) {
            $.modal.alertWarning("相同发货单号无法合并调度！");
            return;
        }


        var segmentIds = segmentIdList.join();

        if (segmentIds.length == 0) {
            $.modal.alertWarning("请至少选择一条记录！");
            return;
        }
        if (checkSegmentStatus()) {
            $.modal.alertWarning("存在非待调度状态的运段，无法调度！");
            return;
        }
        if (checkOutGoType()) {
            $.modal.alertWarning("存在已外发的运段，无法调度！");
            return;
        }
        // if (checkOutTransCode()) {
        //     $.modal.alertWarning("整车无法合并调度！");
        //     return;
        // }
        if (checkTransLineId()) {
            $.modal.alertWarning("所属调度组不相同，无法调度！");
            return;
        }
        //判断是否为多装多卸
        //if(checkSegmentTransCode_temp()) {
       //     $.modal.alertWarning("存在多装多卸的运单，无法合并调度！");
       //     return;
       // }

        var invoiceId = $.table.selectColumns("invoiceId").join();

        $.modal.openTab('调度', prefix + "/dispatch/" + segmentIds);

        // $.ajax({
        //     type: "POST",
        //     url: ctx + "tms/segment/check_reference_price?segmentIds="+segmentIdList,
        //     async: false,
        //     success: function(r){
        //         if(r.data){
        //             $.modal.openTab('调度', prefix + "/dispatch/" + segmentIds);
        //         }else{
        //             $.modal.alertWarning("指导价为空或指导价未审核，请先新添或审核指导价。");
        //             return;
        //         }
        //     }
        // });



        //验证发货单是否超过五天
        /*$.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.openTab('调度', prefix + "/dispatch/" + segmentIds);
                }
            }
        });
*/

    }

    /**
     * 批量外发报价
     */
    function outQuoteBatch(){
        var segmentIds =  $.table.selectColumns("segmentId").join();

        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        var vbillstatus = $.table.selectColumns("vbillstatus");
        //调度判断
        for (var i = 0; i < vbillstatus.length ; i++) {
            if(vbillstatus != 0){
                $.modal.alertWarning("该运段已调度，无法外发！");
                return false;
            }
        }

        var outGoType = $.table.selectColumns("outGoType");
        //调度判断
        for (var i = 0; i < outGoType.length ; i++) {
            if(outGoType != 2){
                $.modal.alertWarning("该运段已外发，无法外发！");
                return false;
            }
        }
        $.modal.open("外发报价", prefix + "/outQuote/" + segmentIds,550,500);
    }
    /**
     * 分配车队
     */
    function assignFleet() {
        var segmentId = $.table.selectColumns("segmentId").join();
        if (segmentId.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var vbillstatus = $.table.selectColumns("vbillstatus");
        if (vbillstatus != 0 && vbillstatus != 6) {
            $.modal.alertWarning("只有‘待调度’或‘分配车队’状态下才可以分配车队！");
            return;
        }

        //判断是否为多装多卸
        if(checkSegmentTransCode_temp()) {
            $.modal.alertWarning("存在多装多卸的运单，无法合并调度！");
            return;
        }

        var invoiceId = $.table.selectColumns("invoiceId").join();

        // $.modal.open("分配车队", ctx + "tms/segment/assign_fleet/" + segmentId ,600,480);


        $.ajax({
            type: "POST",
            url: ctx + "tms/segment/check_fleet?segmentId="+segmentId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.open("分配车队", ctx + "tms/segment/assign_fleet/" + segmentId ,600,480);
                }
            }
        });


        // //验证发货单是否超过五天
        // $.ajax({
        //     type: "POST",
        //     url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
        //     async: false,
        //     success: function(r){
        //         if(r.code != 0){
        //             $.modal.alertError(r.msg);
        //             return false;
        //         }else{
        //             $.modal.open("分配车队", ctx + "tms/segment/assign_fleet/" + segmentId ,600,480);
        //         }
        //     }
        // });
    }
    /**
     * 调度明细
     */
    function dispatchDetail(lotId,vbillstatus){
        if (vbillstatus == 0) {
            $.modal.alertWarning("该运段还未调度！");
            return;
        }
        $.modal.openTab('调度明细', ctx + "tms/segment/dispatch_detail_V1/" + lotId);
    }

    /**
     * 跳转外发报价页面
     */
    function outQuote(segmentId, vbillstatus, outGoType,isClose) {
        if(isClose == 1){
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }
        if (vbillstatus != 0) {
            $.modal.alertWarning("该运段已调度，无法外发！");
            return;
        }
        if (outGoType != 2) {
            $.modal.alertWarning("该运段已外发，无法外发！");
            return;
        }
        $.modal.open("外发报价", prefix + "/outQuote/" + segmentId,550,500);
    }

    /**
     * 跳转外发抢单
     */
    function outGrabOrder(segmentId, vbillstatus, outGoType,isClose) {
        if(isClose == 1){
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }
        if (vbillstatus != 0) {
            $.modal.alertWarning("该运段已调度，无法外发！");
            return;
        }
        if (outGoType != 2) {
            $.modal.alertWarning("该运段已外发，无法外发！");
            return;
        }
        $.modal.open("外发抢单", prefix + "/outGrabOrder/" + segmentId,450,550);
    }

    function segSearchPre() {
        var data = {};
        data.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));
        data.transCode = $.common.join($('#transCode').selectpicker('val'));
        data.transLineId = $.common.join($('#transLineId').selectpicker('val'));
        data.salesDeptId = $.common.join($('#salesDeptId').selectpicker('val'));
        data.arriProvinceIds = $.common.join($('#arriProvinceIds').selectpicker('val'));
        if ($("#reqDeliDateStart").val().trim() == '') {
            $.modal.msg("请选择要求提货日期起始日期", modal_status.WARNING);
            return;
        }
        var tmpEndDate = [[${#dates.format(#dates.createToday(), 'yyyy-MM-dd')}]]
        if ($("#reqDeliDateEnd").val().trim()!= '') {
            tmpEndDate = $("#reqDeliDateEnd").val().trim();
        }
        var ymd1 = $("#reqDeliDateStart").val().trim().split("-");
        var ymd2 = tmpEndDate.split("-");
        if ((ymd2[0] - ymd1[0]) * 12 + (ymd2[1] - ymd1[1]) + (ymd2[2] - ymd1[2]) / 31 > 6) {
            $.modal.msg("要求提货日期跨度不能超过6个月", modal_status.WARNING);
            return;
        }

        $.table.search('role-form', data);
    }

    function resetPre() {
        // $("#deliProvinceId ").val("");
        // $("#deliCityId ").val("");
        // $("#deliAreaId ").val("");
        // $("#arriProvinceId ").val("");
        // $("#arriCityId ").val("");
        // $("#arriAreaId ").val("");
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        //默认设置为待调度
        $('#vbillstatus').selectpicker('val',segmentStatusList[0].value);
        segSearchPre();
    }
    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        var deliDetailAddr= $('#deliDetailAddr').val()
        var arriDetailAddr= $('#arriDetailAddr').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        $('#deliDetailAddr').val(arriDetailAddr)
        $('#arriDetailAddr').val(deliDetailAddr)
        segSearchPre();
    }

    /**
     * 查询历史价格
     */
    function historyPrice(segmentId) {
        var url = ctx + "tms/segment/segmentHistoryPrice/" + segmentId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "历史价格",
            area: ['85%','100%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });

    }

    /**
     * 查询历史价格,不带任何条件
     */
    function historyPriceBtn() {
        //let segmentIds = $.table.selectColumns("segmentId");
        var url = ctx + "tms/segment/segmentHistoryPriceBtn";
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "历史价格",
            area: ['85%','100%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });

    }
    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;
    /**
     * 查询历史价格,不带任何条件
     */
    function paymentRecord() {
        var id = $.table.selectColumns('lotId');
        if(id == null || id.length == 0){
            $.modal.alertWarning("还未调度");
            return ;
        }
        var url =  ctx + "payManage/payRecordSegment?lotId="+id.join("") + "&payDetailId=";
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "付款记录",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });

    }
    /**
     * 校验所勾选的是否存在非待调度的
     */
    function checkSegmentStatus() {
        //校验是否存在已调度运段
        var vbillstatusList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["vbillstatus"];
        });
        var b = false;
        $.each(vbillstatusList, function (i, v) {
            if (v != 0) {
                b = true;
                return false;
            }
        });
        return b;
    }

    /**
     * 校验运段是否已经外发
     */
    function checkOutGoType() {
        //勾选的外发状态list
        var outGoTypeList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["outGoType"];
        });
        var b = false;
        $.each(outGoTypeList, function (i, v) {
            if (v != 2) {
                b = true;
                return false;
            }
        });
        return b;
    }

    /**
     * 校验 运输方式是否为整车
     */
    function checkOutTransCode() {
        //勾选的 运输方式list
        var transCodeList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["transCode"];
        });
        if (transCodeList.length > 1) {
            var b = false;
            $.each(transCodeList, function (i, v) {
                //0为整车
                if (v == 0 || v == 4 || v == 15) {
                    b = true;
                    return false;
                }
            });
            return b;
        } else {
            return false;
        }

    }


    /**
     * 校验所属线路是否相等
     */
    function checkTransLineId() {
        //获取所属线路id
        var lineIdList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["transLineId"];
        });
        if (lineIdList.length === 1) {
            return false;
        }
        return !isRepeat(lineIdList);
    }
    /**
     * 校验是否为多装多卸
     */
    function checkSegmentTransCode() {
        //校验是否存在已调度运段
        var transCodeList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["isMultiple"];
        });
        var b = false;
        //单个可以调度
        // if(transCodeList.length === 1) {
        //     return false;
        // }
        $.each(transCodeList, function (i, v) {
            if (v == 1) {  //多装多卸
                b = true;
                return b;
            }
        });
        return b;
    }

    /**
     * 校验是否为多装多卸
     */
    function checkSegmentTransCode_temp() {
        //校验是否存在已调度运段
        var transCodeList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["isMultiple"];
        });
        var b = false;
        //单个可以调度
        if(transCodeList.length === 1) {
            return false;
        }
        $.each(transCodeList, function (i, v) {
            if (v == 1) {  //多装多卸
                b = true;
                return b;
            }
        });
        return b;
    }

    /**
     * 判断数组是否有相同的元素
     * @param arr
     * @returns {boolean}
     */
    function isRepeat(arr){
        var hash = {};
        for(var i in arr) {
            if(hash[arr[i]])
                return true;
            hash[arr[i]] = true;
        }
        return false;
    }

    /**
     * 调度价审核记录
     */
    function guidePriceCheckList(segmentId) {
        var url = prefix + "/guidePriceCheck/"+segmentId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "调度价审核记录",
            area: ['50%', '65%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 承运商排名
     */
    function carrierTopList(segmentId,deliArea,arriArea){
        var url =  ctx + "tms/segment/carrierTopListView?segmentId="+segmentId+"&deliArea="+deliArea+"&arriArea="+arriArea;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "承运商线路排名",
            area: ['70%', '100%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    function changeProvinceSel(){
        if(changeFlag){
            $("#arriProMultiple").css('display', 'none')
            $("#arriPro").css('display', 'block')
            $("#arriProvinceIds").selectpicker('deselectAll');
        }else{
            $("#arriProMultiple").css('display', 'block')
            $("#arriPro").css('display', 'none')
            $("#arriProvinceId").val("");
            $("#arriCityId").val("");
            $("#arriAreaId").val("");
            $("#arriDetailAddr").val("");
        }
        changeFlag = !changeFlag;
    }

    /**
     * 修改承运商
     */
    function editCarrier() {
        //获取选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        // for (var i = 0; i < bootstrapTable.length; i++) {
        //     if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
        //         $.modal.alertWarning("分配车队的数据无法修改承运商！");
        //         return;
        //     }
        // }

        var lotSpStatus = bootstrapTable[0].lotSpStatus;
        if (lotSpStatus == 1) {
            $.modal.msgWarning("审批中的运单不能修改承运商");
            return
        }

    /*    var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }
*/
        //判断委托单状态为新建，已确认 后台要验证 运单对应下的 应付为新建状态
        var vbillstatus = $.table.selectColumns("vbillstatus").join();
        if(vbillstatus != 0 && vbillstatus != 1 && vbillstatus != 6){
            $.modal.alertWarning("委托单状态为待确认和已确认才能修改承运商！");
            return false;
        }



        var lotId =  $.table.selectColumns("lotId");
        if(lotId == null || lotId == ''){
            $.modal.alertWarning("未调度不能修改");
            return;
        }
        //TODO

        var invoiceId = $.table.selectColumns("invoiceId");

        $.ajax({
            url: ctx + "trace/checkException?lotId="+lotId,
            type: "post",
            dataType: "json",
            success: function (result) {
                if (result.code === 0) {
                    $.modal.openTab("修改承运商", prefix + "/trace_dispatch_detail/"+lotId);
                } else {
                    $.modal.msgError(result.msg);
                }
            }
        });




    }

    function getFrontFormatDate(data) {
        var date = new Date();
        date.setDate(date.getDate()+data);
        var seperator1 = "-";
        var seperator2 = ":";
        var month = date.getMonth() + 1 ;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate;


        return currentdate;
    }

    /**
     * 修改调度租
     */
    function editDispatcherGroup(segmentId, vbillstatus) {
        if (vbillstatus != 0) {
            $.modal.alertWarning("请选择‘待调度’状态下的数据。");
            return;
        }
        $.modal.open("修改调度租", prefix + "/editDispatcherGroup/" + segmentId, 450, 500);
    }

    function segClose(){
        var segmentId =  $.table.selectColumns("segmentId").join()
        $.modal.open("关闭", ctx + "tms/segment/close/" + segmentId,500,500);

        /*var segmentIds =  $.table.selectColumns("segmentId").join();
        layer.confirm("确认关闭运段及发货单吗？", {
            btn: ["确认", "取消"]
        }, function (index, layero) {
            var data = {};
            data.segmentIds = segmentIds;
            $.operate.saveModalNoCloseAndNotRefush(ctx + "tms/segment/close",data,function(ret){
                if(ret.code == 500){
                    $.modal.msgWarning(""+ret.msg);
                }

                layer.close(index);
                segSearchPre();
            });
        }, function (index) {
            layer.close(index);
        });*/
    }

    /**
     * 获取指导价
     */
/*    function getGuidePrice() {
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isFleetAssign"] != 0) {
                $.modal.alertWarning("分配车队的数据无法获取指导价！");
                return;
            }
            if(bootstrapTable[i]["vbillstatus"] == 0 || bootstrapTable[i]["vbillstatus"] == 4 || bootstrapTable[i]["vbillstatus"] == 6){
                $.modal.alertWarning("请选择已调度后的数据");
                return;
            }
        }

        var ids =  $.table.selectColumns("lotId").join();

        var url = ctx + "tms/segment/getGuidePrice/" + ids;

        // $.modal.open("获取指导价", url);
        layer.open({
            type: 2,
            area: ['800px', '400px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "获取指导价",
            content: url,
            btn: ['更新指导价', '关闭'],
            shadeClose: true,
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function(index) {
                return true;
            }
        });
    }*/


    function delPush(){
        var lotId =  $.table.selectColumns("lotId");
        if(lotId == null || lotId == ''){
            $.modal.alertWarning("未调度不能删除推送");
            return;
        }

        var segmentIds =  $.table.selectColumns("segmentId").join();
        layer.confirm("确认删除推送吗？", {
            btn: ["确认", "取消"]
        }, function (index, layero) {
            var data = {};
            data.segmentIds = segmentIds;
            $.operate.saveModalNoCloseAndNotRefush(prefix+"/delPush",data,function(ret){
                log.info("++++++"+ret.code);
                if(ret.code == 500){
                    $.modal.msg(""+ret.msg);
                }
                layer.close(index);
                //segSearchPre();
                $.table.search('role-form', data);
            });
        }, function (index) {
            layer.close(index);
        });
    }

    function formatTableUnit(value, row, index) {
        return {
            css: {
                "white-space": "nowrap",
                "text-overflow": "ellipsis",
                "overflow": "hidden",
                "max-width": "200px"
            }
        }

    }

    function formatterInformation(value, row, index){
        let htmlTextT='';
        var now = new Date();
        var isMultiple = row.isMultiple

        let reqDeliDate=new Date(Date.parse(row.reqDeliDate))
        if (reqDeliDate < now && row.vbillstatus == toDispatchStatus) {
            reqDeliDate= `<span class="label label-danger">` + row.reqDeliDate.substr(0,10) + `</span>`;
        } else {
            reqDeliDate= row.reqDeliDate.substr(0,10);
        }

        let reqArriDate=new Date(Date.parse(row.reqArriDate));
        if (reqArriDate < now && row.vbillstatus == toDispatchStatus) {
            reqArriDate= `<span class="label label-danger">` + row.reqArriDate.substr(0,10) + `</span>`;
        } else {
            reqArriDate= row.reqArriDate && row.reqArriDate.substr(0,10);
        }

        if(isMultiple == 0) {  
            htmlTextT+= `<div><span class="label label-warning pa2">提</span>` + reqDeliDate + `<div class="ml5" style="display: inline-block;vertical-align: middle;">`+ $.table.tooltip(row.deliAddrName,9)+`</div></div>`;
            htmlTextT+= `<div class="mt5"><span class="label label-success pa2">到</span>`+reqArriDate+ `<div class="ml5" style="display: inline-block;vertical-align: middle;">`+$.table.tooltip(row.arriAddrName,9)+`</div></div>`;
        }else {
            if(row.multipleShippingAddressList){
                let list = row.multipleShippingAddressList;
                for (const elem of list) {      
                    if (elem.addressType === 0) {   
                        htmlTextT+= `<div><span class="label label-warning pa2">提</span>` + reqDeliDate + `<div class="ml5" style="display: inline-block;vertical-align: middle;">`+ $.table.tooltip(elem.addrName,9)+`</div></div>`;
                    }else if(elem.addressType === 1){
                        htmlTextT+= `<div class="mt5"><span class="label label-success pa2">到</span>`+reqArriDate+ `<div class="ml5" style="display: inline-block;vertical-align: middle;">`+$.table.tooltip(elem.addrName,9)+`</div></div>`;
                    }
                } 
            }
        }
        
        
        return htmlTextT;
    }

    function changReqDate() {
        var segmentIds =  $.table.selectColumns("segmentId").join();

        var invoiceId = $.table.selectColumns("invoiceId")

        var allEqual = invoiceId.every((id) => id === invoiceId[0]);
        if(!allEqual){
            $.modal.alertWarning("请选择相同发货单下的单据");
            return;
        }

        if (checkSegmentStatus()) {
            $.modal.alertWarning("存在非待调度状态的运段，无法拆量！");
            return;
        }

        layer.open({
            type: 2,
            area: ['700px', '460px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "要求提货|到货日期调整",
            content: ctx + `tms/segment/changReqDate?segmentIds=${segmentIds}&invoiceId=${invoiceId[0]}`,
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });
    }
    
    function getCopy(list){
        let data=JSON.parse(list)
        
        var goodsName = data.goodsName;
        /*if(data.segPackGoodsList){
            $.each(data.segPackGoodsList, function(i, item) {
                goodsName += item.goodsName + " "
            })
        }else if(data.multipleShippingAddressList){
            $.each(data.multipleShippingAddressList, function(i, elem) {
                let goodsList = elem.shippingGoodsList
                $.each(goodsList, function(i, item) {
                    goodsName = goodsName + item.goodsName
                })
            })
        }*/
        let num = data.numCountAdjust != 0 && $.common.isNotEmpty(data.numCountAdjust) ? data.numCountAdjust : data.numCount
        let weight = data.weightCountAdjust != 0 && $.common.isNotEmpty(data.weightCountAdjust) ? data.weightCountAdjust : data.weightCount
        let volume  = data.volumeCountAdjust != 0 && $.common.isNotEmpty(data.volumeCountAdjust) ? data.volumeCountAdjust : data.volumeCount
        
        let goodslist=[]
        if(num){
            goodslist.push(num+'件')
        }
        if(weight){
            goodslist.push(weight+'吨')
        }
        if(volume){
            goodslist.push(volume+'m³')
        }

        let actCar =  data.carLenName+"米"
        carType.forEach(item=>{
            if(item.dictValue==data.carType){
                actCar+= item.dictLabel
            }
        })
        var str = ""
        var strT = ""
        var isMultiple = data.isMultiple
        if(isMultiple == 0) {
            let htmlText=``
            if(data.deliProName=="上海市"||data.deliProName=="北京市"||data.deliProName=="天津市"||data.deliProName=="重庆市"){
                htmlText=data.deliProName+data.deliAreaName
            }else{
                htmlText=data.deliCityName+data.deliAreaName
            }
            
            str = str + htmlText

            let htmlTextT=``

            if(data.arriProName=="上海市"||data.arriProName=="北京市"||data.arriProName=="天津市"||data.arriProName=="重庆市"){
                htmlTextT=data.arriProName+data.arriAreaName
            }else{
                htmlTextT=data.arriCityName+data.arriAreaName
            }

            strT = strT + htmlTextT
        }else {
            //TODO
            let list = data.multipleShippingAddressList;
            for (const elem of list) { //地址
                if (elem.addressType === 0) {  
                    let htmlText=``
                    //上海 北京 天津 重庆 
                    if(elem.provinceName=="上海市"||elem.provinceName=="北京市"||elem.provinceName=="天津市"||elem.provinceName=="重庆市"){
                        htmlText=elem.provinceName + elem.areaName
                    }else{
                        htmlText= elem.cityName + elem.areaName
                    }
                    
                    str =str+ htmlText
                }
            }

            let listT = data.multipleShippingAddressList;
            for (const elem of listT) {
                if (elem.addressType === 1) {
                    let htmlTextT=``

                    //上海 北京 天津 重庆 
                    if(elem.provinceName=="上海市"||elem.provinceName=="北京市"||elem.provinceName=="天津市"||elem.provinceName=="重庆市"){
                        htmlTextT=elem.provinceName + elem.areaName
                    }else{
                        htmlTextT=elem.cityName + elem.areaName
                    }

                    
                    
                    strT =strT+ htmlTextT

                }
            }
            
        } 
        
        let addrName=str+'-'+strT
        let reqArriDate=data.estimatedArrivalTime  

        layer.open({
            type: 1,
            title: '复制信息',
            // closeBtn: 0,
            shadeClose: true,
            shade: false,
            area: ['40%', '60%'],
            content: $('#invoiceCtHtml').html(),
            btn: ['确定复制', '关闭'], 
            success: function (layero, index) {
                let carrierList=[]
                if(data.carrierName){
                    carrierList.push(data.carrierName)
                }
                if(data.carrierPhone){
                    carrierList.push(data.carrierPhone)
                }
                if(carrierList.length!=0){
                    $("#carrierInfo").val(carrierList.join('-'))
                }
       
                $("#addrName").val(addrName?addrName:'')   
                
                $("#goodsInfo").val((goodsName?goodsName:'')+(goodslist.length!=0?goodslist.join('/'):0))

                let driverList=[]
                if(actCar){
                    driverList.push(actCar)
                }
                if(data.fee){
                    driverList.push('￥'+data.fee)
                }
                if(data.carno){
                    driverList.push(data.carno)
                }
                if(data.driverName){
                    driverList.push(data.driverName)
                }
                if(data.driverCardId){
                    driverList.push(data.driverCardId)
                }
                if(data.driverMobile){
                    driverList.push(data.driverMobile)
                }
                if(driverList.length!=0){
                    $("#driverInfo").val(driverList.join(','))
                }
                
                $("#reqArriDateInfo").val(reqArriDate?reqArriDate:'')
                
            },   
            yes: function (index, layero) {
                let addrNameInfo=$("#addrName").val()  
                let goodsInfo=$("#goodsInfo").val() 
                let driverInfo=$("#driverInfo").val()            
                let reqArriDateInfo=$("#reqArriDateInfo").val()

                let htmlText=addrNameInfo+'，'+goodsInfo+'，'+driverInfo +'，'+reqArriDateInfo

               
                let copyed= copyText(htmlText)
                if (copyed) {
                    $.modal.alertSuccess("复制成功")
                }else{
                    $.modal.alertError("复制失败，请检查浏览器兼容")
                }
                layer.close(index);
            }
        });

    }

    function copyText(data) {
        let input = document.createElement('input') 
        input.value = data   
        document.body.appendChild(input)   
        input.select()
        var copyed = document.execCommand("copy");
        document.body.removeChild(input)
        return copyed
    }
    
    function editCar(id,checkStatus) {
        if(checkStatus == 0){
            $.modal.alertWarning("待审核状态车辆无法修改!");
            return false;
        }
        var url = ctx + "basic/car/edit/"+id;
        $.modal.openTab("修改车辆信息", url);
    }
    function editDriver(id,checkStatus) {
        if(checkStatus == 0){
            $.modal.alertWarning("待审核状态司机无法修改!");
            return false;
        }
        var url = ctx + "basic/driver/edit/" + id;
        $.modal.openTab("修改司机信息", url);
    }
    // function updateEvent(id){
    //     let url = ctx + "g7/payee/input?carrBankId="+id;
    //     $.modal.open("修改收款人信息", url, 640, 480);
    // }

    /**
     * 新增收款人账号
     */
     function addPayeeEvent(carrierId,lotId,carrBankId){

         let htmlTitle=""
         if(!carrierId){
             $.modal.alertWarning("收款人账号无法新增!");
             return false;
         }

        //验证是否可以货量更新
        $.ajax({
            type: "POST",
            url: ctx + "carrier/entrustLot/checkEditPayee?lotId=" + lotId,
            async: false,
            success: function (r) {
                if (r.code != 0) {
                    $.modal.alertError(r.msg);
                    return false;
                } else {
                    let carrId = carrierId;

                    let url = ctx + "g7/payee/add_two?dt=1";

                    if(carrId != ''){
                        url += "&collectionType=0&carrId="+carrId+"&entrustLotId="+lotId
                        htmlTitle="添加收款人信息"
                    }

                    if(carrBankId != ''&&carrBankId != undefined){
                        url += "&carrBankId="+carrBankId
                        htmlTitle="修改收款人信息"
                    }

                    let width = 800;
                    let height = ($(window).height() - 50);

                    layer.open({
                        type: 2,
                        area: [width + 'px', height + 'px'],
                        fix: false,
                        //不固定
                        maxmin: true,
                        shade: 0.3,
                        title: htmlTitle,
                        content: url,
                        btn: ['确定', '关闭'],
                        // 弹层外区域关闭
                        shadeClose: true,
                        yes: function(index, layero) {
                            // var body = layer.getChildFrame('body', index);
                            // console.log($(body).find("#collectionType").val())

                            layero.find('iframe')[0].contentWindow.submitHandler_1(addCallBack,index);
                        },
                        cancel: function(index) {
                            return true;
                        }
                    })
                }
            }
        });


    }
    function addCallBack(pageNo){
        setTimeout(function(){
            layer.close(pageNo);
        },500)
    }
    
    function scanCode(entrustLotId,driverName,driverMobile,carno,carrierName,carrierPhone,invoiceVbillno,carrierName,driverCardId,carrCardId) {
        layer.open({
            type: 1,
            area: ['30%', '440px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "畅运通运单详情二维码",
            content: $("#scanCodeHtml").html(),
            btn: ['关闭'],
            shadeClose: true,
            success: function (layero, index) {
                $("#scanCodeImg").attr("src",ctx + "trace/generateQrCode?entrustLotId="+entrustLotId)

                if(invoiceVbillno&&invoiceVbillno!='null'){
                    $("#invoiceVbillnoInfo").text(invoiceVbillno)
                    $("#invoiceVbillnoInfo").parent().css('display', 'block')
                }else{
                    $("#invoiceVbillnoInfo").parent().css('display', 'none')
                }

                if(driverCardId==carrCardId){
                    $("#carrInfo").parent().css('display', 'none');
                }else{
                    $("#carrInfo").text(carrierName);
                    $("#carrInfo").parent().css('display', 'block');
                }
               
                if((driverName||driverMobile||carno)&&(driverName!='null'||driverMobile!='null'||carno!='null')){
                    let driverInfo=[]
                    if(driverName&&driverName!='null'){
                        driverInfo.push(driverName)
                    }
                    if(driverMobile&&driverMobile!='null'){
                        driverInfo.push(driverMobile)
                    }
                    if(carno&&carno!='null'){
                        driverInfo.push(carno)
                    }

                    if(driverInfo.length!=0){
                        $("#driverInfo span").text(driverInfo.join('/'))
                        $("#driverInfo").css('display', 'block')
                    }else{
                        $("#driverInfo").css('display', 'none')
                    }
 
                    $("#carrierInfoT").css('display', 'none')
                }else{
                    let carrierList=[]
                    if(carrierName&&carrierName!='null'){
                        carrierList.push(carrierName)
                    }
                    if(carrierPhone&&carrierPhone!='null'){
                        carrierList.push(carrierPhone)
                    }
                    if(carrierList.length!=0){
                        $("#carrierInfoT").text(carrierList.join('/'))
                        $("#carrierInfoT").css('display', 'block')
                    }else{
                        $("#carrierInfoT").css('display', 'none')
                    }
                   
                    $("#driverInfo").css('display', 'none')
                   
                }    
            }
        })
    }

    function adjust(segmentId) {
        $.modal.open("调整运费", ctx + "tms/segment/adjust_freight/" + segmentId,600,540);
    }

    function adjustGuidingPrice(segmentId) {
        $.modal.open("调整指导价", ctx + "tms/segment/adjust_guiding_price/" + segmentId,600,400);
    }

    function adjustCostPrice(segmentId) {
        $.modal.open("调整成本价", ctx + "tms/segment/adjust_cost_price/" + segmentId,500,300);
    }

    function xSegment() {
        var tLayer = layer;
        tLayer.open({
            type: 2,
            area: [document.documentElement.clientWidth + 'px', document.documentElement.clientHeight + 'px'],
            //fix: false,
            skin: 'add-class',
            shade: 0.3,
            zIndex: 2000,
            title: '调度配载',
            content: prefix + '/structure',
            btn: ['提交', '取消'],
            shadeClose: false,
            success: function (layero, index) {

            },
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(function(){
                    tLayer.close(index)
                });
            },
            btn2: function (index, layero) {
                //return false; // 与btn1的关闭层逻辑不一致
            }
        });
    }

    function ltlDispatch() {
         dataList = null;
         layer.open({
             type: '1',
             shade: 0.3,
             zIndex: 10,
             title: '普货零担调度',
             area: ['400px', '230px'],
             btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
             content: $('#importTpl2').html(),
             btn1: function(index, layero) {
                 if (!dataList || dataList.length == 0) {
                     $.modal.msgError("未找到可提交数据！");
                     return;
                 }
                 $.modal.confirm("确认提交吗？", function(){
                     $.modal.loading("正在导入数据，请稍候...");
                     $.ajax({
                         url: ctx + 'dispatch-x/ltl-dispatch',
                         type: 'post',
                         data: JSON.stringify(dataList),
                         contentType: 'application/json',
                         success: function(result) {
                             $.modal.closeLoading();
                             if (result.code == 0) {
                                 layer.close(index);
                                 $.modal.msgSuccess("导入成功");
                                 $.table.refresh();
                             } else {
                                 $.modal.alertError(result.msg)
                             }
                         }
                     })
                 })
             }
         })
    }
    function ltlData() {
        $.modal.loading("正在导出数据，请稍候...");
        $.post(prefix + "/ltl-data", null, function(result) {
            if (result.code == web_status.SUCCESS) {
                window.location.href = ctx + "common/download?fileName=" + encodeURI(result.data) + "&delete=" + true;
            } else if (result.code == web_status.WARNING) {
                $.modal.alertWarning(result.msg)
            } else {
                $.modal.alertError(result.msg);
            }
            $.modal.closeLoading();
        });
    }
    var dataList = null;
    function readExcel() {
        dataList = null;
        $("#templateMsg").html("")
        var file = $('#ipt_file').val();
        if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
            $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
            return false;
        }
        $("#fileMsg").html(file.substring(file.lastIndexOf("\\") + 1));
        var tt = $.modal.layerLoading("加载中，请稍候...");
        var files = $('#ipt_file')[0].files;
        var fileReader = new FileReader();
        fileReader.onload = function(ev) {
            try {
                var data = ev.target.result
                var workbook = XLSX.read(data, {
                    type: 'binary'
                }) // 以二进制流方式读取得到整份excel表格对象

            } catch (e) {
                console.log('%O',e)
                layer.close(tt)
                $('#templateMsg').text('文件类型不正确:' + e.message);
                return;
            }
            try {
                // 表格的表格范围，可用于判断表头是否数量是否正确
                var fromTo = '';
                //console.log(workbook.Sheets)

                var idx = 0;
                var sheetNames = workbook.SheetNames; // 工作表名称集合
                var sht = workbook.Sheets[sheetNames[0]]; // 这里我们只读取第一张sheet
                //console.log(sht)
                if (sht['Q1'].w != '送货站点') {
                    $.modal.msgWarning("模板已更新，请重新导出待调度零担")
                    return
                }
                let merges = sht['!merges'];
                if (!merges) {
                    merges = [];
                }
                let fields = [
                    'reqDeliDate','custAbbr','deliAddr','arriAddr','goodsName','num','weight','volume','reqArriDate',
                    'shortCcs','shortPrice','carrName','pricingMethod','price','deliveryFee','totalFee',
                    'arriStation', 'arriCcs', 'arriPrice',
                    'remark',
                    'invoiceVbillno','memo','vbillno'
                ],fieldsName = [
                    '日期','客户','装货地址','到货地址','品名','件','吨','方','交期',
                    '短驳车号','短驳总价','承运专线','计价方式','单价','送货费','总价',
                    '送货站点','送货车号','送货总价',
                    '司机注意事项',
                    'TMS单号','下单备注','运段号(系统用)'
                ]
                var result = XLSX.utils.sheet_to_json(sht, {header:fields, raw:false, range:1});
                let pricingMethodEnum = [[${T(com.ruoyi.tms.constant.basic.CarrierProtocolPricingMethodEnum).toMap()}]];
                if (result != null) {
                    let requiredFields = ['carrName', 'pricingMethod'/*, 'price'*/]
                    for (let i = 0; i < result.length; i++) {
                        result[i]['flag'] = i;
                        let t = result[i];
                        let errors = []
                        for (let j = 0; j < requiredFields.length; j++) { // 必填校验
                            if (!t[requiredFields[j]]) {
                                errors.push(fieldsName[fields.indexOf(requiredFields[j])]);
                            }
                        }
                        if (errors.length > 0) {
                            $.modal.alertError("第" + (i + 1) + "条数据请输入：" + errors.join("、"))
                            return;
                        }
                        if (!t['vbillno']) {
                            $.modal.alertError("第" + (i + 1) + "条数据运段号丢失，请确认！")
                            return;
                        }
                        // 总价校验，只能放后台，前端件吨方可能改，而且也无法直接获取到件吨方量
                        // 计价方式文字转code
                        var find = false;
                        for (let j = 0; j < pricingMethodEnum.length; j++) {
                            if (t.pricingMethod == pricingMethodEnum[j].context) {
                                find = true;
                                t.pricingMethod = pricingMethodEnum[j].value;
                                break;
                            }
                        }
                        if (!find) {
                            $.modal.alertError("第" + (i + 1) + "条数据计价方式不存在，请确认！")
                            return;
                        }
                        // shortCcs转shortCarrier,shortCar,shortDriver
                        if (t.shortCcs) {
                            let ccs = t.shortCcs.trim().split('\n');
                            if (ccs.length != 3) {
                                $.modal.alertError("第" + (i + 1) + "条数据“短驳车号”格式错误，请确认！")
                                return;
                            }
                            t.shortCarrier = ccs[0].trim();
                            t.shortCar = ccs[1].trim();
                            t.shortDriver = ccs[2].trim();
                            if (t.shortPrice == null) {
                                $.modal.alertError("第" + (i + 1) + "条数据有“短驳车号”，请补充“短驳总价”")
                                return;
                            }
                        }
                        if (t.shortPrice) {
                            if (isNaN(t.shortPrice)) {
                                $.modal.alertError("第" + (i + 1) + "条数据“短驳总价”请输入正确的数字")
                                return;
                            }
                            if (!t.shortCcs) {
                                $.modal.alertError("第" + (i + 1) + "条数据有“短驳总价”，请补充“短驳车号”")
                                return;
                            }
                        }
                        if (t.arriCcs || t.arriPrice) {
                            if (!t.arriStation) {
                                $.modal.alertError("请输入第" + (i + 1) + "条数据的“送货站点”！")
                                return;
                            }
                            if (t.arriCcs) {
                                let ccs = t.arriCcs.trim().split('\n');
                                if (ccs.length != 3) {
                                    $.modal.alertError("第" + (i + 1) + "条数据“送货车号”格式错误，请确认！")
                                    return;
                                }
                                t.arriCarrier = ccs[0].trim();
                                t.arriCar = ccs[1].trim();
                                t.arriDriver = ccs[2].trim();
                            } else {
                                $.modal.alertError("请输入第" + (i + 1) + "条数据的“送货站点”！")
                                return;
                            }
                            if (t.arriPrice == null) {
                                $.modal.alertError("请输入第" + (i + 1) + "条数据的“送货总价”")
                                return;
                            } else if (isNaN(t.arriPrice)) {
                                $.modal.alertError("第" + (i + 1) + "条数据“送货总价”请输入正确的数字")
                                return;
                            }
                        }
                    }
                    for (let i = 0; i < merges.length; i++) {
                        let start = merges[i].s; // 合并单元格开始单元格
                        if (start.c != 9 && start.c != 10) {
                            $.modal.alertError("不支持短驳车号、短驳总价之外列的合并单元格")
                            return;
                        }
                        let end = merges[i].e; // 合并单元格结束单元格
                        if (end.c != 9 && end.c != 10) {
                            $.modal.alertError("不支持短驳车号、短驳总价之外列的合并单元格")
                            return;
                        }
                        if (start.c != end.c) {
                            $.modal.alertError("不支持跨列合并单元格")
                            return;
                        }
                        if (start.c == 9) {
                            // 查找c=10,r=r的&&end.c = end.c + 1,enc.r=end.r，如果找不到，报验证异常
                            let find = false;
                            for (let j = 0; j < merges.length; j++) {
                                if (merges[j].s.c == start.c + 1 && merges[j].s.r == start.r
                                    && merges[j].e.c == end.c + 1 && merges[j].e.r == end.r) {
                                    find = true;
                                    break;
                                }
                            }
                            if (!find) {
                                $.modal.alertError("第" + (start.r + 1) + "行‘短驳车号’未找到匹配的‘短驳总价’合并单元格")
                                return
                            }
                        } else if (start.c == 10) {
                            // 查找c=9,r=r的&&end.c = end.c - 1,enc.r=end.r，如果找不到，报验证异常
                            let find = false;
                            for (let j = 0; j < merges.length; j++) {
                                if (merges[j].s.c == start.c - 1 && merges[j].s.r == start.r
                                    && merges[j].e.c == end.c - 1 && merges[j].e.r == end.r) {
                                    find = true;
                                    break;
                                }
                            }
                            if (!find) {
                                $.modal.alertError("第" + (start.r + 1) + "行‘短驳总价’未找到匹配的‘短驳车号’合并单元格")
                                return
                            }
                        }
                        for (let j = start.r - 1; j <= end.r - 1; j++) {
                            result[j]['flag'] = result[start.r - 1]['flag']; // 同短驳标记
                        }
                    }
                    //console.log(result)
                    dataList = result;
                }

                $("#templateMsg").html("检测到" + dataList.length + "条调度数据")

                //在控制台打印出来表格中的数据
                if (dataList == null || dataList.length == 0) {
                    $.modal.alertError("未在第一个Sheet中找到数据")
                    return;
                }
            } finally {
                layer.close(tt)
            }

        };
        // 以二进制方式打开文件
        fileReader.readAsBinaryString(files[0]);
        $('#ipt_file').val("")
        $('#ipt_file').remove();
        $("#ipt_file_div").append('<input type="file"' +
            ' accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"' +
            ' id="ipt_file" onChange="readExcel()">')
    }
    function free(lotId) {
        $.modal.confirm("确定放行该调度运单", function() {
            $.ajax({
                url: ctx + "dispatch-x/releaseDd",
                data: 'lotId=' + lotId,
                success: function(result) {
                    if (result.code == 0) {
                        $.table.refresh();
                        $.modal.msgSuccess("操作成功")
                    } else {
                        $.modal.alertError(result.msg);
                    }
                },
                error: function (response) {
                    $.modal.msgError(response.responseText)
                }
            })
        });
    }
    function wecom_processx(spNo, spMemo) {
        wecom_process(spNo, function(){
            // 增加财务复核意见
            let n = $('#spRecordTrs').find("tr").length;
            let tr = '<tr><td>' + (n + 1) + '</td><td>';
            tr = tr + '放行';
            tr = tr + '</td><td colspan="3">' + spMemo + '</td></tr>';
            $('#spRecordTrs').append(tr);
        });
    }
    function dispatchLedger() {
        layer.open({
            type: 1,
            area: ['400px', '325px'],
            fix: false,
            //不固定
            maxmin: false,
            offset: '100px',
            zIndex:4,
            shade: 0.3,
            skin: 'none',
            title: '导出调度台账',
            content: $('#tpl3').html(),
            btn: ['<i class="fa fa-check"></i> 导出', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            success: function(layero) {
                var laydate = layui.laydate;
                laydate.render({
                    elem: layero.find('.ledgerDate'), //指定元素
                    //isInitValue : true,
                    trigger: 'click',
                    type: 'datetime',
                    fullPanel: true
                });
                layero.find("#ledgerDateStart").val(getFrontFormatDate(-1) + " 00:00:00");
                layero.find("#ledgerDateEnd").val(getFrontFormatDate(-1) + " 23:59:59");
            },
            btn1: function(index, layero){
                let ledgerDeliDateStart = layero.find("#ledgerDeliDateStart").val();
                let ledgerDeliDateEnd = layero.find("#ledgerDeliDateEnd").val();
                let ledgerDateStart = layero.find("#ledgerDateStart").val();
                let ledgerDateEnd = layero.find("#ledgerDateEnd").val();
                let legerUser = layero.find('#ledgerUser').val();
                if (ledgerDeliDateStart || ledgerDateStart) {
                    $.modal.loading("正在导出数据，请稍候...");
                    $.post(prefix + "/dispatch-ledger", {ledgerDeliDateStart,ledgerDeliDateEnd,ledgerDateStart,ledgerDateEnd,reg_user_name:legerUser}, function(result) {
                        if (result.code == web_status.SUCCESS) {
                            window.location.href = ctx + "common/download?fileName=" + encodeURI(result.data) + "&delete=" + true;
                        } else if (result.code == web_status.WARNING) {
                            $.modal.alertWarning(result.msg)
                        } else {
                            $.modal.alertError(result.msg);
                        }
                        $.modal.closeLoading();
                    });
                } else {
                    $.modal.alertError("提货开始日期与调度开始日期至少选其一")
                }
            }
        });
    }
    function changeDeposit(lotId) {
        var url = prefix + "/changeDeposit?lotId=" + lotId;
        $.modal.open("调整定金", url, 400, 580);
    }
    function turnDeposit() {
        let row = $.btTable.bootstrapTable('getSelections')[0];
        var vbillstatus = row.vbillstatus;
      /*  if (vbillstatus != 2 && vbillstatus != 3) {
            $.modal.msgWarning("请选择已提货的数据");
            return
        }*/
        if (row.lotSpStatus != 0 && row.lotSpStatus != 2 && row.lotSpStatus != 4) {
            $.modal.msgWarning("请选择运费审批通过的数据");
            return;
        }
        $.modal.open("【运费】转熟车定金", ctx + "new-deposit/turnDeposit?dataType=1&id=" +row.lotId+"&carrierId="+row.carrierId, 700, 500);
    }
    function turnFamiliar(index) {
        let row = $.btTable.bootstrapTable('getData')[index]
        $.modal.open("【定金】转熟车定金", ctx + "new-deposit/turnDeposit?dataType=2&id=" +row.depositId+"&carrierId="+row.carrierId, 700, 500);
    }

    function autoDis() {
        var rows = $.table.selectColumns("segmentId");
        if (rows.length == 0) {
            $.modal.alertWarning("请选择确认的数据");
            return;
        }

        //判断发货单是否是新建状态
        var vbillstatusList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["vbillstatus"];
        });
        var b = false;
        $.each(vbillstatusList, function (i, v) {
            if (v != 0) {
                b = true;
                return false;
            }
        });
        if (b) {
            $.modal.alertWarning("请选择待调度的数据。");
            return;
        }

        var segmentIds = rows.join();

        layer.open({
            type: 2,
            area: ['70%', '70%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "自动调度",
            content: ctx + "tms/segment/autoDis?segmentId=" + segmentIds,
            btn: ['确认', '关闭'],
            // shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });


    }
</script>
<script id="importTpl2" type="text/template">
    <div class="col-xs-offset-1">
        <div id="ipt_file_div" style="display: none">
            <input type="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" id="ipt_file" onchange="readExcel()"/>
        </div>
        <div class="mt10 pt5">
            待调度零担：
            &nbsp;	<a href="javascript:ltlData()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 导出数据</a>
        </div>
        <a href="javascript:$('#ipt_file').click()" class="mt10" style="display: inline-block">选择导入文件</a> <span id="fileMsg"></span>
        <div class="mt10" style="color:red">
            提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>

        <div id="templateMsg" class="mt10" style="font-weight: bold;color:blue"></div>
    </div>
</script>
<script id="tpl3" type="text/template">
    <div style="padding: 10px 20px 0;">
        <div style="display: flex;">
            <span style="width: 92px;line-height: 26px;text-align: right;">调度开始日期：</span>
            <span style="flex:1"><input class="form-control ledgerDate" id="ledgerDateStart"></span>
        </div>
        <div style="display: flex;margin-top: 5px">
            <span style="width: 92px;line-height: 26px;text-align: right;">调度结束日期：</span>
            <span style="flex:1"><input class="form-control ledgerDate" id="ledgerDateEnd"></span>
        </div>
        <div style="display: flex;margin-top: 5px">
            <span style="width: 92px;line-height: 26px;text-align: right;">提货开始日期：</span>
            <span style="flex:1"><input class="form-control ledgerDate" id="ledgerDeliDateStart"></span>
        </div>
        <div style="display: flex;margin-top: 5px">
            <span style="width: 92px;line-height: 26px;text-align: right;">提货结束日期：</span>
            <span style="flex:1"><input class="form-control ledgerDate" id="ledgerDeliDateEnd"></span>
        </div>
        <div style="display: flex;margin-top: 5px">
            <span style="width: 92px;line-height: 26px;text-align: right;">调度人：</span>
            <span style="flex:1"><input class="form-control" id="ledgerUser"></span>
        </div>
        <div style="margin-top: 5px;line-height: 26px">
            ※提示：提货开始日期与调度开始日期至少选其一；注意结束日期的时分秒；范围跨度不要超过6个月
        </div>
    </div>
</script>
</body>
</html>