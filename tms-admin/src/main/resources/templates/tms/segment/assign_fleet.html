<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('分配车队')"/>
</head>

<body>
<div class="form-content">
    <form id="form" class="form-horizontal" novalidate="novalidate">
        <div class="panel-body">
            <!--基础信息 begin-->
            <input name="segmentId" type="hidden" th:value="${segmentId}">
            <div class="row">
                <div class="col-md-4 col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red;">计价方式：</label>
                        <div class="col-sm-8">
                            <div class="input-group" style="width: 100%;display: inline-flex">

                                <select name="pricingMethod" id="pricingMethod" class="form-control" autocomplete="off" required>
                                    <option value=""></option>
                                    <option th:each="dict : ${pricingMethodEnumList}"
                                            th:text="${dict.context}"
                                            th:value="${dict.value}">
                                    </option>
                                </select>
                                <input class="form-control" type="number" id="unitPrice" name="unitPrice" placeholder="单价" onchange="unitPriceChange()">
                            </div>
                            <label id="error-pricingMethod" class="error" for="pricingMethod" style="display: none;"></label>

                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red;">分配金额：</label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <span class="input-group-addon">¥</span>
                                <input type="text" id="amount" name="amount" th:value="${assignFleet.amount}"

                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this);changeAmount()"
                                       class="form-control" autocomplete="off" th:disabled="${isAssign == 1}">
                            </div>
                            <div id="negativeGrossProfitDiv" class="mt10" style="display: none;">
                                <span class="label label-danger">负毛利区间</span>
                                <span id="negativeGrossProfit" style="padding-top: 5px;color: #ed5565;font-size: 1em"></span>
                            </div>

                            <label id="error-amount" class="error" for="amount" style="display: none;"></label>

                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red;" >车队要求提货日期：</label>
                        <div class="col-sm-8">
                            <input type="text" class="time-input form-control" id="reqDeliDate" name="reqDeliDate"
                                   placeholder="抢单报价截止时间" lay-key="1" autocomplete="off"
                                   th:value="${assignFleet.reqDeliDate == null ?reqDeliDate: #dates.format(assignFleet.reqDeliDate, 'yyyy-MM-dd')}"
                                   required readonly th:disabled="${isAssign == 1}">
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-12">
                    <div class="form-group">
                        <label class="col-sm-4" style="color: red;">车队：</label>
                        <div class="col-sm-8">
                            <select name="carDeptId" id="carDeptId" class="form-control valid" aria-invalid="false"
                                    th:with="type=${dispatcherDeptList}" onchange="setCarDeptName();"
                                    aria-required="true" required>
                                <option></option>
                                <option th:each="dict : ${type}" th:text="${dict.deptName}"
                                        th:value="${dict.deptId}" th:selected="${assignFleet.carDeptId eq dict.deptId+''}"></option>
                            </select>
                            <input id="carDeptName" name="carDeptName" th:value="${assignFleet.carDeptName}" type="hidden">
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-12">
                    <div class="form-group">
                        <label class="col-md-1 col-sm-2"  style="color: red;">运输方式：</label>
                        <div class="col-md-11 col-sm-10">
                            <select id="transCode" name="transCode" class="form-control valid"
                                    aria-invalid="false" th:with="type=${@dict.getType('trans_code')}" onchange="setTransName()" required>
                                <option value="" selected="selected"> -- 请选择 --</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}" th:field="${assignFleet.transCode}"></option>
                            </select>
                            <input type="hidden" id="transName" name="transName" th:value="${assignFleet.transName}">
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-12">
                    <div class="form-group">
                        <label class="col-md-1 col-sm-2">备注：</label>
                        <div class="col-md-11 col-sm-10">
                                <textarea name="memo" id="memo" maxlength="200" class="form-control valid"
                                          th:text="${assignFleet.memo}" rows="3" th:disabled="${isAssign == 1}"></textarea>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "tms/segment";

    var isAssign = [[${isAssign}]]
    var numCount = [[${numCount}]];
    var weightCount = [[${weightCount}]];
    var volumeCount = [[${volumeCount}]];

    var profit = [[${profit}]];
    /**
     * 选择调度组
     */
    function setCarDeptName() {
        $("#carDeptName").val($("#carDeptId option:selected").text());
    }

    /**
     * 校验
     */
    $("#form").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            amount:{
                required:true,
            },
            carDeptId:{
                required:true,
            }
        }
    });


    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {

            let pricingMethod = $("#pricingMethod").val();
            if (pricingMethod == '0' || pricingMethod == '1' || pricingMethod == '2') {

                let unitPrice = $("#unitPrice").val();
                if (unitPrice == null || unitPrice == '') {
                    $.modal.alertWarning("请填写计价方式中的单价。");
                    return;
                }
            }

            var dis = $(":disabled");
            dis.attr("disabled", false);

            var data = $("#form").serializeArray();


            if (isAssign == 0) {
                $.operate.save(prefix + "/assign_fleet", data, function (result) {
                    if (result.code != 0) {
                        dis.attr("disabled", true);
                    }
                });
            }else if (isAssign == 1) {
                $.operate.save(prefix + "/edit_assign_fleet", data, function (result) {
                    if (result.code != 0) {
                        dis.attr("disabled", true);
                    }
                });
            }
        }
    }

    function unitPriceChange(){
        var pricingMethod = $("#pricingMethod").val();
        var unitPrice = $("#unitPrice").val();
        if(pricingMethod == '0'){
            //按吨
            var price = unitPrice*weightCount;
            $("#amount").val(price.toFixed(2));
        }else if(pricingMethod == '1'){
            var price = unitPrice*volumeCount;
            $("#amount").val(price.toFixed(2));
        }else if(pricingMethod == '2'){
            var price = unitPrice*numCount;
            $("#amount").val(price.toFixed(2));
        }

        changeAmount()
    }

    function changeAmount() {
        let val = $("#amount").val();

        let msrp2 = parseFloat(val).toFixed(4);

        if (profit && msrp2 > profit) {
            let negativeGrossProfit = Math.abs((profit - msrp2).toFixed(2));

            let html = ''
            if (negativeGrossProfit >= 0 && negativeGrossProfit < 20) {
                html = '[0-20]'
            }else if (negativeGrossProfit >= 20 && negativeGrossProfit < 50) {
                html = '[20-50]'
            }else if (negativeGrossProfit >= 50 && negativeGrossProfit < 100) {
                html = '[50-100]'
            }else if (negativeGrossProfit >= 100 && negativeGrossProfit < 200) {
                html = '[100-200]'
            }else if (negativeGrossProfit >= 200 && negativeGrossProfit < 400) {
                html = '[200-400]'
            }else if (negativeGrossProfit >= 400 && negativeGrossProfit < 600) {
                html = '[400-600]'
            }else if (negativeGrossProfit >= 600) {
                html = '[600及以上]'
            }

            $("#negativeGrossProfit").html(html);
            $("#negativeGrossProfitDiv").show()
        }else {
            $("#negativeGrossProfitDiv").hide()
        }


    }
    /**
     * 设置运输方式名称
     */
    function setTransName() {
        let name = $("#transCode option:selected").text();
        $("#transName").val(name);
    }

</script>
</body>
</html>