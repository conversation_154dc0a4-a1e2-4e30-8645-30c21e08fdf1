<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('外发报价')"/>
</head>

<body>
<div class="form-content">
    <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
            <div class="panel-body">
                <!--基础信息 begin-->
                <input name="segmentId" type="hidden" th:value="${segmentId}">
                <div class="row">
                    <div class="col-md-4 col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-2">调度组：</label>
                            <div class="col-sm-10">
<input type="hidden" value="" name="transLineName" id="transLineName">
                                <select name="transLineId" id="transLineId" class="form-control " required onchange="setTransLineName(this)"
                                          th:with="type=${dispatcherDeptList}">
                                    <option value="">请选择</option>
                                    <option th:each="dict : ${type}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                </div>

            </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "tms/segment";

    $(function () {


    });

    function setTransLineName(obj){
        let transLineName = $(obj).find("option:selected").text();
        $("#transLineName").val(transLineName);

    }

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-out-quote").serializeArray();
            $.operate.save(prefix + "/editDispatcherGroup", data);
        }
    }

</script>
</body>
</html>