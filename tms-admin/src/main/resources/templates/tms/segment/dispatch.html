<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('调度')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
/*    .error{
        border-color: #ff1f1f !important;
        box-shadow: 0 0 0 0.2rem rgba(255, 31, 31, 0.25) !important;
    }*/
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
        color: #808080;
    }
    .flex_left{
        width: 95px;
        line-height: 30px;
        text-align: right;
        color: #333333 !important;
    }
    .fLeft{
        width: 80px;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .lih30{
        line-height: 30px;
    }
    /*.row .form-group label[class*='col-'] {*/
    /*    text-align: right;*/
    /*    line-height: 30px;*/
    /*    margin-bottom: 0;*/
    /*}*/
    .addbtn{
        width: 28px;
        text-align: center;
        color: #fff;
        background: #1ab394;
        line-height: 26px;
        border-radius: 5px;
        cursor: pointer;
    }
    .addGoods{
        color: #1ab394;
        line-height: 30px;
        cursor: pointer;
    }
    .wap_content{
        padding: 10px 10px;
        border: 1px #eee solid;
        border-radius: 3px;

    }
    .line20{
        line-height: 20px;
    }
    .fcff6{
        color: #ff6c00;
    }
    .hisbtn{
        line-height: 25px;
        border-radius: 20px;
        width: 140px;
        text-align: center;
        color: #1ab394;
        border: 1px #1ab394 solid;
        cursor: pointer;
    }
    .eye{
        width: 20px;
        height: 20px;
        background: #1ab394 url("../../img/eyes.png") no-repeat center;
        background-size: 20px 20px;
        border-radius: 50%;
        /*display: inline-block;*/
    }
    .disin{
        display: inline-block;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .toop{
        width: 20px;
        height: 20px;
        text-align: center;
        background: #7f7f7f;
        border-radius: 50%;
        display: inline-block;
        color: #fff;
        line-height: 20px;
        cursor: pointer;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #EBF5FC;
    }
    .sm{
        background: #fff6f6;
        padding: 5px 10px;
        margin: 0 -5px 5px;
    }
    .sm_icon{
        width: 18px;
        height: 18px;
        background: #ff1f1f;
        color: #fff;
        border-radius: 50%;
        display: inline-block;
        line-height: 18px;
        text-align: center;
        font-size: 12px;
    }
    .sm_text{
        color: #ff3636;
        display: inline-block;
        margin-left: 10px;
        line-height: 20px;
    }
    .toopT{
        position: absolute;
        top: 4px;
        right: 10px;
        background-color: #1a1a1a;
        z-index: 3;
    }
    .form-control,.input-group-addon,.table-bordered td, .table-bordered th,button[disabled], html input[disabled],.table>thead>tr>th,
    .panel,.panel-body,.table-bordered{
        border-color: #A6A6A6 !important;
    }
    .toText{
        display:inline-block;
        width: 32px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        /* padding: 8px 4px; */
        border-radius: 5px;
        color: #fff;
        vertical-align: middle;
    }
    .ff6c{
        color:#FF6C00;
    }
    .dInput{
        border-bottom: 1px solid #A6A6A6;
        padding-bottom: 10px;
        padding-left: 5px;
        padding-right: 5px;
    }
    .dInput:first-child{
        padding-top: 10px;
    }
    .dInput:last-child{
        border: 0;
    }
    .dInput+.dInput{
        margin-top: 10px;
    }
    .dInput input[disabled]{
        border: 0;
        background-color: #ffffff;
    }
    .form-control,.input-group-addon{
        border-radius: 4px;
    }
    .table>tbody>tr>td{
        border-right: 1px solid #A6A6A6;
    }
    .table>tbody>tr>td:last-child{
        border-right:0
    }
    .panel-group .panel{
        border-radius: 0;
    }
    .fw{
        font-weight: bold;
    }

    .borderB{
        position: relative;
    }
    .borderB::after{
        content:" ";
        position: absolute;
        width: calc(100% - 40px);
        height: 0;
        border-bottom:#EBECEF 2px solid;
        top: -15px;
        left: 20px;
    }
    .mab0{
        margin-bottom: 0;
        width: 5em;
    }
    .file-drop-zone{
        overflow: auto;
    }
    .selectSpan{
        display: inline-block;
        width: 30%;
    }
    .hide{
        display: none;
    }
    .pa1{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        position: relative;
        left: -70%;
        top: 5px;
    }
    .checkbox{
        display: inline-block;
        vertical-align: middle;
    }
    .unit-price {
        /*margin-left: 15px;*/
        /*font-size: 15px;*/
        /*font-weight: 600;*/
    }
    
    .clear-input {
        display: inline-block;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        border-radius: 50%;
        background-color: #f0f0f0;
        color: #999;
        font-weight: bold;
        cursor: pointer;
        user-select: none;
    }
    
    .clear-input:hover {
        background-color: #ddd;
        color: #666;
    }
</style>
<body>
<div class="form-content">
    <form id="form-dispatch-add" class="form-horizontal" novalidate="novalidate">
        <input id="segmentIds" name="segmentIds" th:value="${segmentIds}" type="hidden">

        <div class="panel-group" id="accordion">
            
            <div class="row" style="border: 1px solid #A6A6A6;margin: 0 -5px 5px;padding: 10px 5px;" th:if="${ifYmmBack == 1}">
                <div class="col-md-2">
                   <!-- <img src="/img/bs/hll_logo.png" style="width:auto;height: 26px;margin: 2px;"/>-->
                    <img src="/img/bs/ymm_logo.png" style="width:auto;height: 26px;margin: 2px;"/>
                </div>
                <div class="col-md-4">
                    <div class="flex">
                        <label class="flex_left mab0">司机信息：</label>
                        <div class="flex_right lih30">
                           [[${lotOutYmmBack.driverName}]]/[[${lotOutYmmBack.driverMobile}]]
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="flex">
                        <label class="flex_left mab0">车牌号：</label>
                        <div class="flex_right lih30">
                            [[${lotOutYmmBack.carNo}]]
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="flex">
                        <label class="flex_left mab0">车长车型：</label>
                        <div class="flex_right lih30">
                            [[${lotOutYmmBack.carLenName}]]/[[${lotOutYmmBack.carTypeName}]]
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="flex">
                        <label class="flex_left mab0">订金：</label>
                        <div class="flex_right lih30" style="color:#FF9008">
                            ￥<span th:text="${#numbers.formatDecimal(lotOutYmmBack.ymmDeposit,1,'COMMA',2,'POINT')}"></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="panel-group" id="accordionOne">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h5 class="panel-title">
                                    <a data-toggle="collapse" data-parent="#accordion"
                                       href="tabs_panels.html#collapseOne">调度信息</a>

                                       <div class="fr">
                                           <span>指导价：</span><span style="color:#FF6C00;" id="referencePriceSpan">¥0.00</span>
                                           <input id="referencePrice" type="hidden">
                                           <span style="display: none;" id="excReferencePriceSpan">
                                               <span class="label label-danger" style="margin-left: 12px;padding: 3px;">超</span>
                                               <span style="color: #ed5565;font-size: 1.2em;" id="excReferencePrice">$9999</span>
                                           </span>
                                       </div>
                                </h5>
                            </div>
                            <!--调度信息 begin-->
                            <div id="collapseOne" class="panel-collapse collapse in">
                                <div class="panel-body">
                                    <div class="row no-gutter" id="routine">
                                        <div class="col-sm-1">
                                            <div class="toText" id="balaTypeT"></div>
                                        </div>
                                        <div class="col-sm-4" style="font-size: 16px;">
                                            <div> [[${#dates.format(reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}]] </div>
                                            <div> [[${deliFullAddr}]] </div>
                                            <input name="reqDeliDate" id="reqDeliDate" type="hidden" class="form-control" autocomplete="off" disabled
                                                   th:value="${#dates.format(reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}" placeholder="要求提货日">

                                        </div>
                                        <div class="col-sm-2" style="text-align:center;">
                                            <img th:src="@{/img/car2.png}" style="width: 50%;margin-top: 12px;"/>
                                        </div>
                                        <div class="col-sm-4" style="font-size: 16px;">
                                            <div> [[${#dates.format(reqArriDate, 'yyyy-MM-dd HH:mm:ss')}]] </div>
                                            <div> [[${arriFullAddr}]] </div>
                                            <input name="reqArriDate" id="reqArriDate" type="hidden" class="form-control" autocomplete="off" disabled
                                                   th:value="${#dates.format(reqArriDate, 'yyyy-MM-dd HH:mm:ss')}" placeholder="要求到货日">
                                        </div>
                                    </div>

                                    <div class="row no-gutter mt10">
                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">* </span><span>承运商：</span></label>
                                                <div class="flex_right">
                                                    <!-- <div class="input-group"> -->
                                                        <input name="carrName" id="carrName" placeholder="请输入承运商" class="form-control valid"
                                                               type="text"
                                                               onclick="selectCarrier();" required
                                                               aria-required="true" autocomplete="off" readonly>
                                                        <input name="carrCode" id="carrCode" type="hidden">
                                                        <input name="carrierId" id="carrierId" type="hidden">
                                                        <input name="carrType" id="carrType" type="hidden">
                                                        <input name="balaType1" id="balaType" type="hidden">
                                                        <input name="carrierPhone" id="carrierPhone" type="hidden">
                                                        <input name="ifHasBill" id="ifHasBill" type="hidden">
                                                        <!-- <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                                    </div> -->
                                                </div>
                                            </div>
                                        </div>
                                       
                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">* </span><span>运输方式：</span></label>
                                                <div class="flex_right">
                                                    <div style="overflow: hidden">
                                                        <div style="float: left;width: 50%">
                                                            <select name="transCode" id="transCode" class="form-control valid" aria-invalid="false"
                                                                    onchange="setTransName();getCarrierProtocolPrice();getReferencePrice()" th:with="type=${@dict.getType('trans_code')}" required>
                                                                <option value=""></option>
                                                                <option th:selected="${transCode eq dict.dictValue}" th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                            </select>
                                                        </div>
                                                        <div class="ltlType" style="float: right;width: 49%">
                                                            <select name="ltlType" id="ltlType" class="form-control valid" aria-invalid="false" required>
                                                                <option value="">-线段类别-</option>
                                                                <option value="0">提货段</option>
                                                                <option value="1">干线</option>
                                                                <option value="2">送货段</option>
                                                            </select>
                                                        </div>
                                                        <input type="hidden" id="transName" name="transName" th:value="${transName}">
                                                    </div>


                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row no-gutter">
                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span  id="carnoSpan" class="fcff">* </span><span>车牌号：</span></label>
                                                <div class="flex_right" >
                                                     <div class="" >
                                                         <div style="display: flex; align-items: center;">
                                                             <input name="carno" id="carno" placeholder="请输入车牌号" class="form-control valid"
                                                                    type="text" onclick="selectCar()" autocomplete="off" readonly required>
                                                             <span class="clear-input" onclick="clearCarInput()" style="margin-left: 5px; cursor: pointer;">✕</span>
                                                             <input name="carnoId" id="carnoId" type="hidden">
                                                             <!--                                                         <span class="input-group-addon"><i class="fa fa-search"></i></span>-->
                                                             <span style="margin-left: 5px;color: red; cursor: pointer;font-size: 14px;white-space: nowrap;display: none;"
                                                                   title="点击补充"
                                                                   id="isDocFullSpan">
                                                                缺证件
                                                             </span>

                                                         </div>

                                                         <label class="error" for="carno" style="display: none;top: 0px;"></label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">车长：</label>
                                                <div class="flex_right">
                                                    <input name="carLenName" id="carLenName" class="form-control valid" type="text" disabled>
                                                    <input name="carLenId" id="carLenId" class="form-control valid" type="hidden">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        
                                    </div>
                                    <div class="row no-gutter">
                                        
                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span id="driverNameSpan" class="fcff">* </span><span>司机：</span></label>
                                                <div class="flex_right">
                                                     <div class="">
                                                         <div style="display: flex; align-items: center;">
                                                             <input name="driverName" id="driverName" placeholder="请输入司机" class="form-control valid" type="text"
                                                                    onclick="selectDriver()" readonly required>
                                                             <span class="clear-input" onclick="clearDriverInput()" style="margin-left: 5px; cursor: pointer;">✕</span>
                                                             <input name="driverId" id="driverId" type="hidden">
                                                             <input name="cardId" id="cardId" type="hidden">
                                                             <!-- <span class="input-group-addon"><i class="fa fa-search"></i></span>-->

                                                             <span style="margin-left: 5px;color: red; cursor: pointer;font-size: 14px;white-space: nowrap;display: none"
                                                                   title="点击补充"
                                                                   id="driverIsDocFull">
                                                                缺证件
                                                             </span>
                                                         </div>
                                                         <label class="error" for="driverName" style="display: none;top: 0px;"></label>
                                                     </div>
                                                </div>
                                            </div>
                                        </div>
    
                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">车型：</label>
                                                <div class="flex_right">
                                                    <input name="carTypeName" id="carTypeName" class="form-control valid" type="text" disabled>
                                                    <input name="carTypeCode" id="carTypeCode" class="form-control valid" type="hidden">
                                                </div>
                                            </div>
                                        </div>
                                       
                                    </div>
                                    <div class="row no-gutter">
                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">司机手机：</label>
                                                <div class="flex_right">
                                                    <input name="driverMobile" id="driverMobile" type="text" class="form-control" disabled>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff" id="estimatedArrivalTimeSpan">* </span><span>预计到场：</span></label>
                                                <div class="flex_right">
                                                    <div class="input-group">
                                                        <input name="estimatedArrivalTime" id="estimatedArrivalTime" type="text"
                                                            class="form-control" readonly autocomplete="off"
                                                            placeholder="预计到场时间" required>
                                                            <span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
                                                    </div>
                                                    <label class="error" for="estimatedArrivalTime" style="display: none;"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row no-gutter">
                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">挂车：</label>
                                                <div class="flex_right">
                                                    <div class="input-group">
                                                        <input name="trailerNo" id="trailerNo" placeholder="请输入挂车"
                                                               class="form-control" type="text" onclick="selectTrailerCar()" autocomplete="off" readonly>
                                                        <input name="trailerId" id="trailerId" type="hidden">
                                                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row no-gutter">

                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span>司机2：</span></label>
                                                <div class="flex_right">
                                                    <!-- <div class="input-group"> -->
                                                    <input name="driverName2" id="driverName2" placeholder="请输入司机姓名" class="form-control valid" type="text">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left">司机2手机：</label>
                                                <div class="flex_right">
                                                    <input name="driverMobile2" id="driverMobile2" class="form-control valid" type="text" placeholder="请输入司机手机">

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row no-gutter">

                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span>司机2身份证：</span></label>
                                                <div class="flex_right">
                                                    <!-- <div class="input-group"> -->
                                                    <input name="driverCardId2" id="driverCardId2" placeholder="请输入司机身份证" class="form-control valid" type="text">
                                                </div>
                                            </div>
                                        </div>


                                    </div>

                                    <div class="row no-gutter">
                                        <div class="col-md-6 col-sm-6">

                                            <div class="flex" style="margin-right: 45px">
                                                <label class="flex_left"><span class="fcff">* </span>大车配载：</label>
                                                <div class="flex_right">
                                                    <div style="display: flex;align-items: baseline;margin-right: 45px;">
                                                        <input type="checkbox" id="isBigCartLoad"
                                                               onchange="changeIsBigCartLoad()"
                                                               style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />

                                                        <input name="isBigCartLoad" value="0" type="hidden">
                                                        <label for="isBigCartLoad" style="font-size: 1.1em; vertical-align: middle;">是</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-sm-6">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">* </span>是否报关：</label>
                                                <div class="flex_right">
                                                    <div style="display: flex;align-items: baseline;">
                                                        <input type="checkbox" id="isCustomsClearance" value="1"
                                                               onchange="changeIsCustomsClearance()"
                                                               style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />

                                                        <input name="isCustomsClearance" value="" type="hidden">
                                                        <label for="isCustomsClearance" style="font-size: 1.1em; vertical-align: middle;">是</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                    </div>

                                    <div class="row no-gutter">
                                        <div class="col-md-4 col-sm-4">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">* </span>大件/三超：</label>
                                                <div class="flex_right">
                                                    <div style="display: flex;align-items: baseline;">
                                                        <input type="checkbox" id="isOversize" value="1"
                                                               onchange="changeIsOversize()"
                                                               style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />

                                                        <input name="isOversize" value="" type="hidden">
                                                        <label for="isOversize" style="font-size: 1.1em; vertical-align: middle;">是</label>

                                                        <i class="fa fa-question-circle ml5 cur"
                                                           style="font-size: 16px;"
                                                           data-toggle="tooltip"
                                                           data-container="body" data-placement="right" data-html="true" title=""
                                                           data-original-title="<ul>
                                                            <li>1、当填写长、宽、高中的其中一项，则代表<big>体积</big>超出规范</li>
                                                            <li>2、长、宽、高不填，则默认<big>重量</big>超出规范</li>
                                                        </ul>
                                                       "></i>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-8 col-sm-8">
                                            <div class="flex" id="goodsSizeDiv" style="display: none;">
                                                <div id="oversizeTypeDiv" style="font-size: 12px;margin-right: 5px;display: flex;align-items: center;">
                                                    [[${oversizeType != null && oversizeType == 1 ? '超重':'超体积'}]]
                                                </div>

                                                <div class="input-group " style="flex: 1;margin-right: 3px">
                                                    <input name="goodsLength" id="goodsLength" placeholder="长"
                                                           th:value="${goodsLength}"
                                                           type="text" class="form-control" style="color: #000"
                                                           oninput="$.numberUtil.onlyNumber(this);changeOversizeType()" autocomplete="off">
                                                    <span class="input-group-addon">米</span>
                                                </div>
                                                <div class="input-group " style="flex: 1;margin-right: 3px">
                                                    <input name="goodsWidth" id="goodsWidth" placeholder="宽"
                                                           th:value="${goodsWidth}"
                                                           type="text" class="form-control " style="color: #000"
                                                           oninput="$.numberUtil.onlyNumber(this);changeOversizeType()"  autocomplete="off">
                                                    <span class="input-group-addon">米</span>
                                                </div>
                                                <div class="input-group" style="flex: 1;margin-right: 3px">
                                                    <input name="goodsHeight" id="goodsHeight" placeholder="高"
                                                           th:value="${goodsHeight}"
                                                           type="text" class="form-control" style="color: #000"
                                                           oninput="$.numberUtil.onlyNumber(this);changeOversizeType()" autocomplete="off">
                                                    <span class="input-group-addon">米</span>
                                                </div>
                                            </div>
                                        </div>

                                    </div>


                                    <div class="row no-gutter">
                                        <div class="col-md-12 col-sm-12" style="margin-bottom: 10px;">
                                            <div class="flex">
                                                <label class="flex_left">内部备注：</label>
                                                <div class="flex_right">
                                                    <textarea name="memo" style="color: #333333;" placeholder="请输入" id="memo" th:text="${memo}" maxlength="200" class="form-control valid" rows="4"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row no-gutter" style="padding-bottom: 10px;">
                                        <div class="col-md-12 col-sm-12">
                                            <div class="flex">
                                                <label class="flex_left">司机注意事项：</label>
                                                <div class="flex_right">
                                                    <textarea name="appMemo" style="color: #333333;" placeholder="请输入" id="appMemo" maxlength="200" class="form-control valid"
                                                    rows="5"></textarea>
                                                </div>
                                            </div>
                                            <!-- <div class="over" style="padding-bottom: 20px">
                                                <div class="fl">
                                                    <label class="checkbox-inline check-box">
                                                        <input type="checkbox" id="rmReq" name="rmReq">发送app承运商</label>
                                                </div>
                                                <div class="fl ml20">
                                                    <label class="checkbox-inline check-box">
                                                        <input type="checkbox" id="pushApp" name="rmReq" disabled checked>推送app</label>
                                                    </label>
                                                </div>
                                                <div class="fl ml20 appShow" style="width: 200px;padding: 5px 0 0px 0px;display: block !important;">
                                                    <textarea name="appMemo" placeholder="司机注意事项" id="appMemo" maxlength="200" class="form-control valid"
                                                              rows="4"></textarea>
                                                </div>
                                            </div> -->
                                        </div>
                                    </div>

                                    <div style="height: 10px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--调度信息end-->
                </div>

                <div class="col-md-6">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapseFive">付款信息</a>
                                <div class="fr">
                                    <div id="sumAllMoney" style="display: inline-block;">油卡总金额：
                                        <span class="ff6c">¥</span> <span class="ff6c" id="calculateTotal">0</span>
                                        <!--<span id="two" style="display: none;">，预付油卡：<span class="ff6c">¥</span><span  id="twoInner" class="ff6c">0</span></span>
                                        <span id="one" style="display: none;">，预付现金：<span class="ff6c">¥</span><span  id="oneInner" class="ff6c">0</span></span>
                                        <span id="four" style="display: none;">，到付油卡：<span class="ff6c">¥</span><span  id="fourInner" class="ff6c">0</span></span>
                                        <span id="three" style="display: none;">，到付现金：<span class="ff6c">¥</span><span  id="threeInner" class="ff6c">0</span></span>
                                        <span id="six" style="display: none;">，回付油卡：<span class="ff6c">¥</span><span  id="sixInner" class="ff6c">0</span></span>
                                        <span id="five" style="display: none;">，回付现金：<span class="ff6c">¥</span><span  id="fiveInner" class="ff6c">0</span></span>-->
                                        <span>现金总金额：</span><span class="ff6c">¥</span><span  id="cashSum" class="ff6c">0</span>
                                    </div>



                                </div>
                            </h4>
                        </div>
                        <div id="collapseFive" class="panel-collapse collapse in">
                            <div class="panel-body">
<!--                                <div class="sm">-->
<!--                                    <div class="toText" id="balaTypeT"></div>-->
<!--                                    <span class="sm_text">说明：月度付款只需要填写结算金额、油卡比例或者油卡金额，预付油卡正常填写。</span>-->
<!--                                </div>-->
                                <div class="row no-gutter" style="margin-bottom: 10px;margin-left: -5px">
                                    <div class="col-md-12 col-sm-12">
                                        <!--<div class="row no-gutter">
                                            <div class="col-md-12 col-sm-12" id="g7Code">
                                                <div class="toText" id="" style="background-color: #1B84C6;">G7</div>

                                                <span style="color: #1B84C6;">达到合规条件完成G7支付。现金 <span id="cashSum2" class="ff6c">0.00</span> 元,油卡 <span id="calculateTotal2" class="ff6c">0.00</span> 元;不含税金额: <span id="msrp1" class="ff6c">0.00</span> 元</span>
                                            </div>

                                        </div>-->
                                        <div class="row no-gutter">
                                            <div class="col-md-12 col-sm-12 mt10">
                                                <div class="toText" id="" style="background-color: #1ab394;">票</div>

                                                <select name="billingType" id="billingType" class="form-control valid selectSpan" th:with="type=${@dict.getType('billing_type')}" onchange="calculateTotal()">
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" ></option>
                                                </select>
                                                <span class="text-warning">含税金额:</span> <span id="msrp3">0.00</span> 元</span>
                                                <!--<span class="text-success">不含税金额:</span> <span id="msrp2">0.00</span> 元</span>-->
                                                <i class="fa fa-question-circle" id="freightFeeRateTooltip" data-toggle="tooltip" style="font-size: 15px;display: none;" data-html="true" title=""></i>

                                                <input type="hidden" id="costAmount" name="costAmount"><!--加票点后的最终金额-->
                                                <input type="hidden" id="costAmountBilling" name="costAmountBilling">
                                                <input type="hidden" id="freightFeeRate" name="freightFeeRate">
                                            </div>

                                        </div>
                                    </div>
<!--                                    <div class="col-md-1 col-sm-1">-->
<!--                                        <div class="row">-->
<!--                                            <div class="toText" id="balaTypeT"></div>-->
<!--                                        </div>-->
<!--                                    </div>-->

                                </div>
                                <!-- 付款信息-->
                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff" id="pricingMethodSpan">*</span>计价方式：</label>
                                            <div class="flex_right">
                                                <div class="input-group" style="width: 100%;display: inline-flex">
                                                    <select name="pricingMethod" id="pricingMethod" class="form-control" autocomplete="off"
                                                            onchange="getCarrierProtocolPrice();getReferencePrice()" required>
                                                        <option value=""></option>
                                                        <option th:each="dict : ${pricingMethodEnumList}"
                                                                th:text="${dict.context}"
                                                                th:value="${dict.value}">
                                                        </option>
                                                    </select>
                                                    <!-- <input type="text" id="unitPriceSpan" name="unitPriceSpan" class="input-group-addon" style="width: 50%;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-left: 0;"> -->
                                                    <span id="unitPriceSpan" class="form-control" style="background-color: #eee;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-left: 0;line-height:23px;display: none"></span>
                                                    <input class="form-control" type="number" id="unitPrice" name="unitPrice" placeholder="单价" onchange="unitPriceChange()">

                                                </div>
<!--                                                <input type="hidden" id="transName" name="transName" th:value="${transName}">-->
                                                <label id="error-pricingMethod" class="error" for="pricingMethod" style="display: none;"></label>

                                            </div>
                                        </div>
                                    </div>
                                   
                                    
                                    
                                    <input name="numCount" id="numCount" th:value="${numCount}" type="hidden" class="form-control"  value="1"align="left" disabled>
                                    <input name="weightCount" id="weightCount" th:value="${weightCount}" type="hidden" class="form-control" align="left" disabled>
                                    <input name="volumeCount" id="volumeCount" th:value="${volumeCount}" type="hidden" class="form-control" disabled>
                                
                                    <div class="col-md-7 col-sm-7">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">* </span><span>结算金额：</span></label>
                                            <div class="flex_right">
                                                <div class="row" style="margin: 0 0 0 -5px;">
                                                    <div id="costAmountFreightDiv" class="col-sm-10">
                                                        <div>
                                                            <div class="input-group">
                                                                <input style="display: inline-block" name="costAmountFreight" id="costAmountFreight" type="text" class="form-control valid" oninput="$.numberUtil.onlyNumberTwoDecimal(this);inputMax()"
                                                                       required aria-required="true" autocomplete="off" onblur="calOilAmount(1)" onchange="getChange()">
                                                                <span class="input-group-addon">元</span>
                                                            </div>
                                                            <label class="error" for="costAmountFreight" style="display: none;height: 30px;"></label>
                                                        </div>
                                                        <div id="capital" class="ff6c" style="padding: 5px 0"></div>
                                                    </div>
                                                    <div id="negativeGrossProfitDiv" class="col-sm-5" style="display: none;">
                                                        <div class="label label-danger">负毛利区间</div>
                                                        <div id="negativeGrossProfit" style="padding-top: 5px;color: #ed5565;font-size: 1em"></div>
                                                    </div>

                                                    <!-- <div class="col-sm-2">
                                                        <span class="toop toopT" th:if="${segment.collectAmount!=null&&segment.collectAmount!=''}" 
                                                        th:title="|注意该单有到付金额${segment.collectAmount}元|"
                                                                data-toggle="tooltip" data-placement="left">?</span>
                                                    </div> -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
    
                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="flex">
                                            <label class="flex_left oilRatioColor">油卡比例/金额：</label>
                                            <div class="flex_right">
                                                <div style="width: 50%;display: inline-block;">
                                                    <div class="input-group">
                                                        <input
                                                            name="oilRatio" id="oilRatio" oninput="$.numberUtil.onlyNumberCustom(this,100,0,5,2);" type="text" class="form-control"
                                                            onblur="oilRatioInputMin();calOilAmount(2);" autocomplete="off">
                                                        <span class="input-group-addon" style="padding: 2px;border-top-right-radius: 0;border-bottom-right-radius: 0;">%</span>
                                                    </div>
                                                </div>
                                                <div style="width: 50%;display: inline-block;float: right;">
                                                    <div class="input-group">
                                                        <input style="border-top-left-radius: 0;border-bottom-left-radius: 0;border-left: 0;"
                                                            type="text" id="oilAmount" name="oilAmount" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                            onblur="calOilAmount(3)" class="form-control" autocomplete="off">
                                                           <span class="input-group-addon" style="padding: 2px">元</span>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-7 col-sm-7">
                                        <div class="flex">
                                            <label class="flex_left">到付金额：</label>
                                            <div class="flex_right">
                                                <div class="row" style="margin: 0 0 0 -5px;">
                                                    <div class="col-sm-10">
                                                        <div class="input-group">
                                                            <input  id="collectAmount" name="collectAmount" type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"  onblur="calOilAmount(3)"
                                                            class="form-control" autocomplete="off" th:value="${segment.collectAmount}" disabled>
                                                            <span class="input-group-addon">元</span>
                                                        </div>
                                                       
                                                    </div>
                                                    <div class="col-sm-2" style="padding: 6px;">
                                                        <span th:if="${segment.balatype =='5'}" class="label label-primary" title="现金交公司" style="padding:2px;cursor: pointer;">公</span>
                                                        <span th:if="${segment.balatype =='2' or segment.balatype =='6'}" class="label label-primary" title="司机代收" style="padding:2px;cursor: pointer;">司</span>
                                                    </div>

                                                </div>
                                               
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="flex">
                                            <label class="flex_left oilRatioColor">
<!--                                                <img th:src="@{/img/yk.png}" style="width: 28%;float: left;"/>-->
                                                油卡卡号：
                                            </label>
                                            <div class="flex_right">
                                                <input name="oilCardNumber" id="oilCardNumber_1" type="text" class="form-control oilCard" autocomplete="off">
                                                <!--<input name="payDetailList[0].oilCardNumber" id="oilCardNumber_0" type="hidden" class="form-control" autocomplete="off">-->
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-7 col-sm-7">
                                        <div class="flex">
                                            <label class="flex_left " >
<!--                                                <img th:src="@{/img/xj.png}" style="width: 28%;float: left;"/>-->
                                                <span class="fcff">* </span>收款账号：</label>
                                            <div class="flex_right">
                                                <div class="row" style="margin: 0 0 0 -5px;">
                                                    <div class="col-sm-10">
                                                        <select name="carrBankId" id="transCodeOne"  onchange="setTransCodeName();ifG7code()"  class="form-control valid transCode" aria-invalid="false">
                                                            <option value="">请选择</option>
                                                        </select>
                                                    </div>
                                                    <input name="idCard" id="idCard" type="hidden">
                                                    <div class="col-sm-2" style="padding: 0;">
                                                        <div class="addbtn" onclick="addPayeeEvent()">+</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="flex">
                                            <label class="flex_left " >预付油卡：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <input name="payDetailList[1].transFeeCount"   id="transFeeCount_1"   type="text" class="form-control oilCard"
                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotal();changeOli()" onchange="calPayFee()" autocomplete="off">
                                                    <input name="payDetailList[1].costTypeFreight" id="costTypeFreight_1" type="hidden" th:value="1">
                                                    <span class="input-group-addon">元</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-7 col-sm-7">
                                        <div class="flex">
                                            <label class="flex_left " >预付现金：</label>
                                            <div class="flex_right">
                                                <div class="row" style="margin: 0 0 0 -5px;">
                                                    <div class="col-sm-10">
                                                        <div class="input-group">
                                                            <input name="payDetailList[0].transFeeCount" id="transFeeCount_0" type="text" class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotal()" onchange="calPayFee()" autocomplete="off">
                                                            <input name="payDetailList[0].costTypeFreight" id="costTypeFreight_0" type="hidden" th:value="0">
                                                            <span class="input-group-addon">元</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="flex">
                                            <label class="flex_left " >到付油卡：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <input name="payDetailList[3].transFeeCount" id="transFeeCount_3" type="text" class="form-control oilCard"
                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotal();changeOli()" onchange="calPayFee()"autocomplete="off">
                                                    <input name="payDetailList[3].costTypeFreight" id="costTypeFreight_3" type="hidden" th:value="3">
                                                    <span class="input-group-addon">元</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-7 col-sm-7">
                                        <div class="flex">
                                            <label class="flex_left " >到付现金：</label>
                                            <div class="flex_right">
                                                <div class="row" style="margin: 0 0 0 -5px;">
                                                    <div class="col-sm-10">
                                                        <div class="input-group">
                                                            <input  name="payDetailList[2].transFeeCount" id="transFeeCount_2" type="text" class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotal()" onchange="calPayFee()" autocomplete="off">
                                                            <input name="payDetailList[2].costTypeFreight" id="costTypeFreight_2" type="hidden" th:value="2">
                                                            <span class="input-group-addon">元</span>
                                                        </div>
                                                    </div>
                                                </div>
                                               
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="flex">
                                            <label class="flex_left " >回付油卡：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <input name="payDetailList[5].transFeeCount" id="transFeeCount_5" type="text" class="form-control oilCard"
                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotal();changeOli()" onchange="calPayFee()" autocomplete="off">
                                                    <input name="payDetailList[5].costTypeFreight" id="costTypeFreight_5" type="hidden" th:value="5">
                                                    <span class="input-group-addon">元</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-7 col-sm-7">
                                        <div class="flex">
                                            <label class="flex_left " >回付现金：</label>
                                            <div class="flex_right">
                                                <div class="row" style="margin: 0 0 0 -5px;">
                                                    <div class="col-sm-10">
                                                        <div class="input-group">
                                                            <input  name="payDetailList[4].transFeeCount" id="transFeeCount_4" type="text" class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotal();" onchange="calPayFee();" autocomplete="off">
                                                            <input name="payDetailList[4].costTypeFreight" id="costTypeFreight_4" type="hidden" th:value="4">
                                                            <span class="input-group-addon">元</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row no-gutter" id="depositPanel" style="display: none">
                                    <div class="col-md-5 col-sm-5">
                                        <div class="flex">
                                            <label class="flex_left">定金方式：</label>
                                            <div class="flex_right">
                                                <select class="form-control" name="depositWay" th:with="way=${@dict.getType('deposit_way')}" onchange="depositWayChange(this)">
                                                    <option></option>
                                                    <option th:each="dict : ${way}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-7 col-sm-7">
                                        <div class="flex">
                                            <label class="flex_left">定金金额：</label>
                                            <div class="flex_right">
                                                <div class="row" style="margin: 0 0 0 -5px;">
                                                    <div class="col-sm-10">
                                                      <!--  <input type="hidden" name="depositAmount" />
                                                        <div class="input-group">
                                                            <div class="form-control" id="depositAmount" style="color:blue;line-height:22px;font-weight:bold;"></div>&lt;!&ndash;根据规则生成定金金额&ndash;&gt;
                                                            <span class="input-group-addon">元</span>
                                                        </div>-->
                                                        <div class="input-group">
                                                            <input  name="depositAmount" id="depositAmount"
                                                                    type="text" class="form-control"
                                                                    oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                    autocomplete="off" >
                                                            <input name="sysDepositAmount" id="sysDepositAmount" type="hidden">
                                                            <span class="input-group-addon">元</span>
                                                        </div>

                                                    </div>
                                                    <div class="col-sm-2" style="padding: 0">
                                                        <span class="glyphicon glyphicon-question-sign" aria-hidden="true" data-container="body"
                                                              data-toggle="tooltip" data-placement="top" title="非熟车或定金余额为0时，需收取定金" style="line-height: 24px;position: relative"></span>
                                                    </div>
                                                    <label id="error-depositAmount" class="error" for="depositAmount" style="display: none;"></label>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-5 col-sm-5">
                                        <div class="flex">
                                            <label class="flex_left">实际收取人：</label>
                                            <div class="flex_right">
                                                <div class="input-group" style="width: 100%">
                                                    <input name="actualPayee" id="actualPayee"
                                                           class="form-control valid" type="text" />
                                                    <input name="actualPayeeId"
                                                           id="actualPayeeId"
                                                           type="hidden"/>
                                                    <div class="input-group-btn">
                                                        <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <!-- 在途费标题行 -->
                                <div class="row no-gutter" style="margin-top: 15px;">
                                    <div class="col-md-12 col-sm-12">
                                        <div style="display: flex; align-items: center; padding: 10px; border: 1px solid #A6A6A6; background-color: #f9f9f9;">
                                            <div style="width: 130px; font-weight: bold; color: #333333;">自动生成在途费：</div>
                                            <div style="flex: 1;">
                                                <span th:each="yfMiscFee : ${yfMiscFeeList}" th:if="${yfMiscFeeList != null and !#lists.isEmpty(yfMiscFeeList)}" class="unit-price" style="display: inline-block; margin-right: 15px;">
                                                    <span style="cursor: pointer;"
                                                          th:title="'到货地址：' + ${yfMiscFee.arriAddrName} + ' '
                                                            + (${yfMiscFee.billingMethod} == '1' ? ${yfMiscFee.weight} + '(重量)' :
                                                               (${yfMiscFee.billingMethod} == '2' ? ${yfMiscFee.volume} + '(体积)' :
                                                               (${yfMiscFee.billingMethod} == '5' ? ${yfMiscFee.num} + '(件数)' : '')))
                                                            + ' * ' + ${yfMiscFee.miscYfAmount} + '(单价)'">
                                                        [[${yfMiscFee.costTypeName}]]
                                                    </span>
                                                    :[[${#numbers.formatCurrency(yfMiscFee.miscYfAmountTotal)}]]
                                                </span>
                                                <span th:if="${yfMiscFeeList == null or #lists.isEmpty(yfMiscFeeList)}" style="color: #999;">暂无在途费用</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-md-12 col-sm-12">

                                        <div class="fixed-table-body" style="margin: 0px 0px 5px 0;">
                                            <table id="infoTab" class="custom-tab tab table table-bordered">
                                                <thead style="background-color: #EFF3F9;">
                                                <tr>
                                                    <th style="width: 3%;"><a class="collapse-link add-alink" onclick="insertOtherFeeRow()" title="新增行">+</a></th>

                                                    <th style="width: 20%;">在途应付</th>
                                                    <th style="width: 30%;">费用(元)</th>
                                                    <th style="width: 45%;">备注</th>
                                                    <th style="width: 2%;">操作</th>
                                                </tr>
                                                </thead>
                                                <tbody id="otherFeeTableBody">
                                                <tr data-row-index="0">
                                                    <td></td>
                                                    <td>
                                                        <select name="entrustCostList[0].costType" id="onWayCostType_0" class="form-control valid" onchange="onCostTypeChange(this, 0)">
                                                            <option value="">-- 请选择 --</option>
                                                            <option value="1">装卸费</option>
                                                            <option value="2">提货费</option>
                                                            <option value="22">送货费</option>
                                                            <option value="40">枕木费</option>
                                                            <option value="47">报关费</option>
                                                            <option value="46">包车直送</option>
                                                            <option value="51">双驾费</option>
                                                            <option value="50">保费</option>
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <input name="entrustCostList[0].cost" id="onWayFee_0"
                                                               autocomplete="off" class="form-control valid" type="text"
                                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotal()">
                                                    </td>
                                                    <td>
                                                        <input name="entrustCostList[0].memo"
                                                               placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off">
                                                    </td>
                                                    <td>
                                                        <a href="javascript:void(0)" onclick="removeOtherFeeRow(this, 0)" title="删除行" style="color: red;">×</a>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                    </div>

                                    <!-- 三方列表 -->
                                  <!--  <div class="col-md-12 col-sm-12" style="padding-bottom: 10px">
                                        <div class="fixed-table-body" style="margin: 0px 0px 5px 0;">
                                            <table id="thirdPartyTab" class="custom-tab tab table table-bordered">
                                                <thead style="background-color: #EFF3F9;">
                                                <tr>
                                                    <th style="width: 3%;"><a class="collapse-link add-alink" onclick="insertThirdPartyFeeRow()" title="新增行">+</a></th>
                                                    <th style="width: 30%;">三方费用</th>
                                                    <th style="width: 30%;">金额(元)</th>
                                                    <th style="width: 35%;">发货单号</th>
                                                    <th style="width: 2%;">操作</th>
                                                </tr>
                                                </thead>
                                                <tbody id="thirdPartyFeeTableBody">
                                                <tr data-row-index="0">
                                                    <td></td>
                                                    <td>
                                                        <select name="otherFeeList[0].feeType" id="thirdPartyFeeType_0" class="form-control valid" onchange="clearThirdPartyFeeError(this)">
                                                            <option value="">&#45;&#45; 请选择 &#45;&#45;</option>
                                                            <option th:each="dict : ${@dict.getType('cost_type_on_way')}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <input name="otherFeeList[0].feeAmount" id="thirdPartyFeeAmount_0"
                                                               autocomplete="off" class="form-control valid" type="text"
                                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);validateThirdPartyFeeRow(this)">
                                                    </td>
                                                    <td>
                                                        <select name="otherFeeList[0].lotId" id="thirdPartyLotId_0" class="form-control valid" onchange="clearThirdPartyFeeError(this)">
                                                            <option value="">&#45;&#45; 请选择 &#45;&#45;</option>
                                                            <option th:each="invoice : ${invoiceList}" th:text="${invoice.vbillno}" th:value="${invoice.invoiceId}"></option>
                                                        </select>

                                                    </td>
                                                    <td>
                                                        <a href="javascript:void(0)" onclick="removeThirdPartyFeeRow(this, 0)" title="删除行" style="color: red;">×</a>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
-->
                                    <div class="col-md-12 col-sm-12" style="padding-bottom: 10px">
                                        <textarea placeholder="异常原因：负毛利、超指导价(月结除外)时必填" class="form-control" id="ycyy" name="ycyy" required></textarea>
                                    </div>

                                </div>

                            </div>
                        </div>
                    </div>
                    <!--付款信息end-->
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="panel-group" id="accordionTwo">
                        <div  class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title"
                                    style="display: flex;align-items: center;justify-content: space-between;">
                                    <div style="display: flex;align-items: center;justify-content: flex-start;">
                                        <div data-toggle="collapse" data-parent="#accordion" style="margin-right: 25px;cursor: pointer;"
                                           href="tabs_panels.html#collapseTwo">成本分摊</div>

                                        <select name="allocationType" id="allocationType" onchange="calculateAllocation()"
                                                class="form-control valid" style="width: 200px" aria-invalid="false" required="">
                                            <option value="" selected="selected">-- 请选择分摊方式 --</option>
                                            <option value="0">件数</option>
                                            <option value="1">重量</option>
                                            <option value="2">体积</option>
                                            <option value="3">按票</option>
                                            <option value="4">自定义</option>
                                        </select>
                                    </div>


                                    <div class="fr" style="display: flex;justify-content: flex-end;">
                                        <span>收货方总件数/总重量/总体积：</span>
                                        <span th:text="${numCount}"></span>件/<span th:text="${weightCount}"></span>吨/<span th:text="${volumeCount}"></span>m³
                                        
                                        <span>现金、油卡总合计：</span>
                                        <span style="color:#FF6C00;">¥</span><span style="color:#FF6C00;" id="calculateTotal1">0</span>
                                    </div>
                                </h4>
                            </div>
                            <div id="collapseTwo" class="panel-collapse collapse in">
                                <div class="panel-body" style="padding: 0 5px;border-top:0;">
<!--                                    <div class="row no-gutter" style="margin-top: 15px;">-->
<!--                                        <div class="col-md-4 col-sm-4">-->
<!--                                            <div class="flex">-->
<!--                                                <label class="flex_left"><span class="fcff">* </span>成本分摊方式：</label>-->
<!--                                                <div class="flex_right">-->
<!--                                                    <div class="">-->
<!--                                                        <select name="allocationType" id="allocationType" onchange="calculateAllocation()" class="form-control valid" aria-invalid="false" required="">-->
<!--                                                            <option value="" selected="selected"></option>-->
<!--                                                            <option value="0">件数</option>-->
<!--                                                            <option value="1">重量</option>-->
<!--                                                            <option value="2">体积</option>-->
<!--                                                            <option value="3">按票</option>-->
<!--                                                            <option value="4">自定义</option>-->
<!--                                                        </select>-->

<!--                                                    </div>-->
<!--                                                </div>-->
<!--                                            </div>-->

<!--                                        </div>-->
<!--                                    </div>-->


                                    <div class="fixed-table-body" style="margin: 0px -5px;">
                                        <table id="infoTabThree" class="custom-tab tab table" style="margin-bottom: 0;">
                                            <thead style="background-color: #F4F6F7;">
                                                <tr>
                                                    <th style="width: 13%;">在途</th>
                                                    <th style="width: 10%;">
                                                        现金
<!--                                                        <input type="hidden" name="dispatchAllocationVOList[0].costShareVOList[0].shareCost" id="shareCost_0" >-->
<!--                                                        <input type="hidden" name="dispatchAllocationVOList[2].costShareVOList[0].shareCost" id="shareCost_2" >-->
<!--                                                        <input type="hidden" name="dispatchAllocationVOList[4].costShareVOList[0].shareCost" id="shareCost_4" >-->
                                                    </th>
                                                    <th style="width: 10%;">油卡
<!--                                                        <input type="hidden" name="dispatchAllocationVOList[1].costShareVOList[0].shareCost" id="shareCost_1" >-->
<!--                                                        <input type="hidden" name="dispatchAllocationVOList[3].costShareVOList[0].shareCost" id="shareCost_3" >-->
<!--                                                        <input type="hidden" name="dispatchAllocationVOList[5].costShareVOList[0].shareCost" id="shareCost_5" >-->
                                                    </th>
                                                    <th style="width: 10%;">发货单号</th>
                                                    <th style="width: 10%;">要求车长车型</th>
                                                    <th style="width: 8%;">要求提货日期</th>
        
        
                                                    <th style="width: 9%;">提货方</th>
                                                    <th style="width: 9%;">收货方</th>
                                                    <th style="width: 8%;">货品名称</th>
        <!--                                            <th >货品图片</th>-->
                                                    <th style="width: 5%;">包装</th>
                                                    <th style="width: 8%;">件数/重量/体积</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <div th:remove="tag" th:each="segmentVO,segmentVOStat : ${dispatchSegmentVO}">
                                                    <input type="hidden" th:id="|${segmentVO.segmentId}_numCount|" th:value="${segmentVO.numCount}">
                                                    <input type="hidden" th:id="|${segmentVO.segmentId}_weightCount|" th:value="${segmentVO.weightCount}">
                                                    <input type="hidden" th:id="|${segmentVO.segmentId}_volumeCount|" th:value="${segmentVO.volumeCount}">

                                                    <input type="hidden" th:id="|${segmentVO.segmentId}_profit|" th:value="${segmentVO.profit}">

                                                    <input type="hidden" th:id="|invoiceVbillno_${segmentVOStat.index}|" th:value="${segmentVO.invoiceVbillno}">

                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].invoiceId|" th:value="${segmentVO.invoiceId}" type="hidden">
                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].segmentId|" th:value="${segmentVO.segmentId}" type="hidden">

                                                    <!-- 正常运输方式 -->
                                                    <tr th:if="${not #lists.isEmpty(segmentVO.segPackGoodsList)}" th:each="segPackGoods,segPackGoodsStat : ${segmentVO.segPackGoodsList}">

                                                        <div th:if="${segPackGoodsStat.index == 0}">
                                                            <td th:rowspan="${segPackGoodsStat.size}" th:id="${segmentVO.segmentId}" th:attr="data-invoiceid=${segmentVO.invoiceId}">
                                                                <div th:id="|otherFeeCostShareContainer_${segmentVO.segmentId}|" style="display: flex; flex-wrap: wrap; gap: 5px;">
                                                                    <!-- 动态生成的其他费用成本分摊输入框将在这里显示 -->
                                                                </div>
                                                            </td>
                                                            <td th:rowspan="${segPackGoodsStat.size}" style="padding:0;"><div>
                                                                <div class="dInput">
                                                                    <input th:attr="data-invoiceid=${segmentVO.invoiceId}"
                                                                           th:id="|A_${segmentVO.segmentId}|"
                                                                           th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[0].costShare|"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                           onblur="calShareCostSum()" type="text" class="AAAAA shareCb"
                                                                           autocomplete="off" style="width:100%;" disabled  placeholder="预付">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[0].costTypeFreight|" value="0" type="hidden">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[0].freeType|" value="0" type="hidden">
                                                                </div>
                                                                <div class="mt10 dInput">
                                                                    <input th:attr="data-invoiceid=${segmentVO.invoiceId}"
                                                                           th:id="|C_${segmentVO.segmentId}|"
                                                                           th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[2].costShare|"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                           onblur="calShareCostSum()" type="text" class="CCCCC shareCb"
                                                                           autocomplete="off" style="width:100%;" disabled  placeholder="到付">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[2].costTypeFreight|" value="2" type="hidden">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[2].freeType|" value="0" type="hidden">

                                                                </div>
                                                                <div class="mt10 dInput">
                                                                    <input th:attr="data-invoiceid=${segmentVO.invoiceId}"
                                                                           th:id="|E_${segmentVO.segmentId}|"
                                                                           th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[4].costShare|"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                           onblur="calShareCostSum()" type="text" class="EEEEE shareCb"
                                                                           autocomplete="off" style="width:100%;" disabled  placeholder="回付">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[4].costTypeFreight|" value="4" type="hidden">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[4].freeType|" value="0" type="hidden">

                                                                </div>
                                                            </div></td>
                                                            <td th:rowspan="${segPackGoodsStat.size}" style="padding:0;">
                                                                <div class="dInput">
                                                                    <input th:attr="data-invoiceid=${segmentVO.invoiceId}"
                                                                           th:id="|B_${segmentVO.segmentId}|"
                                                                           th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[1].costShare|"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                           onblur="calShareCostSum()" type="text" class="BBBBB shareCb"
                                                                           autocomplete="off" style="width:100%;" disabled placeholder="预付">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[1].costTypeFreight|" value="1" type="hidden">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[1].freeType|" value="0" type="hidden">

                                                                </div>
                                                                <div class="mt10 dInput">
                                                                    <input th:attr="data-invoiceid=${segmentVO.invoiceId}"
                                                                           th:id="|D_${segmentVO.segmentId}|"
                                                                           th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[3].costShare|"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                           onblur="calShareCostSum()" type="text" class="DDDDD shareCb"
                                                                           autocomplete="off" style="width:100%;" disabled placeholder="到付">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[3].costTypeFreight|" value="3" type="hidden">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[3].freeType|" value="0" type="hidden">

                                                                </div>
                                                                <div class="mt10 dInput">
                                                                    <input th:attr="data-invoiceid=${segmentVO.invoiceId}"
                                                                           th:id="|F_${segmentVO.segmentId}|"
                                                                           th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[5].costShare|"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                           onblur="calShareCostSum()" type="text" class="FFFFF shareCb"
                                                                           autocomplete="off" style="width:100%;" disabled placeholder="回付">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[5].costTypeFreight|" value="5" type="hidden">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[5].freeType|" value="0" type="hidden">

                                                                </div>
                                                            </td>
                                                            <td th:rowspan="${segPackGoodsStat.size}">
                                                                <div>[[${segmentVO.invoiceVbillno}]]</div>
                                                                <div th:id="|negativeGrossProfitDiv_${segmentVO.segmentId}|" style="display: none;">
                                                                    <div class="label label-danger">负毛利区间</div>
                                                                    <div th:id="|negativeGrossProfit_${segmentVO.segmentId}|" style="padding-top: 5px;color: #ed5565;font-size: 1em"></div>
                                                                </div>
                                                            </td>
                                                            <td th:rowspan="${segPackGoodsStat.size}" th:each="dict : ${@dict.getType('car_type')}" th:if="${dict.dictValue} == ${segmentVO.carType}" th:text="${dict.dictLabel}"></td>
                                                            <td th:rowspan="${segPackGoodsStat.size}" th:text="${#dates.format(segmentVO.reqDeliDate,'yyyy-MM-dd HH:mm:ss')}"></td>
                                                            <td th:rowspan="${segPackGoodsStat.size}" th:text="${segmentVO.deliAddrName+','+segmentVO.deliDetailAddr}"></td><!-- 提货方-->
            <!--                                                <td th:rowspan="${segPackGoodsStat.size}" th:text="${segmentVO.deliDetailAddr}"></td>-->
                                                            <td th:rowspan="${segPackGoodsStat.size}" th:text="${segmentVO.arriAddrName +','+segmentVO.arriDetailAddr}"></td>
            <!--                                                <td th:rowspan="${segPackGoodsStat.size}" th:text="${segmentVO.arriDetailAddr}"></td>-->
        
            <!--                                                <td th:rowspan="${segPackGoodsStat.size}" th:text="${#dates.format(segmentVO.reqArriDate,'yyyy-MM-dd HH:mm:ss')}"></td>-->
                                                            <td th:text="${segPackGoods.goodsName}"></td>
        
        <!--                                                    <td th:if="${segPackGoods.sysUploadFile != null}">-->
        <!--                                                        <ul>-->
        <!--                                                            <li th:each="file:${segPackGoods.sysUploadFile}">-->
        <!--                                                                <a target="view_window" th:href="@{${file.filePath}}" th:text="${file.fileName}"></a>-->
        <!--                                                            </li>-->
        <!--                                                        </ul>-->
        <!--                                                    </td>-->
                                                            <td th:each="dict : ${@dict.getType('package_type')}" th:if="${dict.dictValue} == ${segPackGoods.packId}" th:text="${dict.dictLabel}"></td>
                                                            <td>
                                                                <span th:text="${segPackGoods.num}"></span>/<span th:text="${segPackGoods.weight}"></span>/<span th:text="${segPackGoods.volume}"></span>
                                                            </td>
            <!--                                                <td th:text="${segPackGoods.weight}"></td>-->
            <!--                                                <td th:text="${segPackGoods.volume}"></td>-->
        
                                                        </div>
                                                        <div th:unless="${segPackGoodsStat.index == 0}">
                                                            <td th:text="${segPackGoods.goodsName}"></td>
        <!--                                                    <td th:if="${segPackGoods.sysUploadFile != null}">-->
        <!--                                                        <ul>-->
        <!--                                                            <li th:each="file:${segPackGoods.sysUploadFile}">-->
        <!--                                                                <a target="view_window" th:href="@{${file.filePath}}" th:text="${file.fileName}"></a>-->
        <!--                                                            </li>-->
        <!--                                                        </ul>-->
        <!--                                                    </td>-->
                                                            <td th:each="dict : ${@dict.getType('package_type')}" th:if="${dict.dictValue} == ${segPackGoods.packId}" th:text="${dict.dictLabel}"></td>
                                                            <td>
                                                                <span th:text="${segPackGoods.num}"></span>/<span th:text="${segPackGoods.weight}"></span>/<span th:text="${segPackGoods.volume}"></span>
                                                            </td>
            <!--                                                <td th:text="${segPackGoods.weight}"></td>-->
            <!--                                                <td th:text="${segPackGoods.volume}"></td>-->
                                                        </div>
                                                    </tr>
                                                    <!-- 多装多卸 -->
                                                    <!-- 地址 -->
                                                    <tr th:if="${not #lists.isEmpty(segmentVO.multipleShippingAddressList)}">
                                                        <td th:id="${segmentVO.segmentId}" th:attr="data-invoiceid=${segmentVO.invoiceId}">
                                                            <div th:id="|otherFeeCostShareContainer_${segmentVO.segmentId}|" style="display: flex; flex-wrap: wrap; gap: 5px;">
                                                                <!-- 动态生成的其他费用成本分摊输入框将在这里显示 -->
                                                            </div>
                                                        </td>
                                                        <td style="padding:0;">
                                                            <div>
                                                                <div class="dInput">
                                                                    <input th:attr="data-invoiceid=${segmentVO.invoiceId}"
                                                                           th:id="|A_${segmentVO.segmentId}|"
                                                                           th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[0].costShare|"
                                                                           type="text" class="AAAAA shareCb"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                           onblur="calShareCostSum()" autocomplete="off" style="width:100%;" disabled  placeholder="预付">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[0].costTypeFreight|" value="0" type="hidden">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[0].freeType|" value="0" type="hidden">

                                                                </div>
                                                                <div class="mt10 dInput">
                                                                    <input th:attr="data-invoiceid=${segmentVO.invoiceId}"
                                                                           th:id="|C_${segmentVO.segmentId}|"
                                                                           th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[2].costShare|"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                           onblur="calShareCostSum()" type="text" class="CCCCC shareCb"
                                                                           autocomplete="off" style="width:100%;" disabled  placeholder="到付">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[2].costTypeFreight|" value="2" type="hidden">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[2].freeType|" value="0" type="hidden">

                                                                </div>
                                                                <div class="mt10 dInput">
                                                                    <input th:attr="data-invoiceid=${segmentVO.invoiceId}"
                                                                           th:id="|E_${segmentVO.segmentId}|"
                                                                           th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[4].costShare|"
                                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                           onblur="calShareCostSum()" type="text" class="EEEEE shareCb"
                                                                           autocomplete="off" style="width:100%;" disabled  placeholder="回付">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[4].costTypeFreight|" value="4" type="hidden">
                                                                    <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[4].freeType|" value="0" type="hidden">

                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td style="padding:0;">
                                                            <div class="dInput">
                                                                <input th:attr="data-invoiceid=${segmentVO.invoiceId}"
                                                                       th:id="|B_${segmentVO.segmentId}|"
                                                                       th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[1].costShare|"
                                                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                       onblur="calShareCostSum()" type="text" class="BBBBB shareCb"
                                                                       autocomplete="off" style="width:100%;" disabled placeholder="预付">
                                                                <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[1].costTypeFreight|" value="1" type="hidden">
                                                                <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[1].freeType|" value="0" type="hidden">

                                                            </div>
                                                            <div class="mt10 dInput">
                                                                <input th:attr="data-invoiceid=${segmentVO.invoiceId}"
                                                                       th:id="|D_${segmentVO.segmentId}|"
                                                                       th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[3].costShare|"
                                                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                       onblur="calShareCostSum()"  type="text" class="DDDDD shareCb"
                                                                       autocomplete="off" style="width:100%;" disabled placeholder="到付">
                                                                <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[3].costTypeFreight|" value="3" type="hidden">
                                                                <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[3].freeType|" value="0" type="hidden">

                                                            </div>
                                                            <div class="mt10 dInput">
                                                                <input th:attr="data-invoiceid=${segmentVO.invoiceId}"
                                                                       th:id="|F_${segmentVO.segmentId}|"
                                                                       th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[5].costShare|"
                                                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                                       onblur="calShareCostSum()" type="text" class="FFFFF shareCb"
                                                                       autocomplete="off" style="width:100%;" disabled placeholder="回付">
                                                                <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[5].costTypeFreight|" value="5" type="hidden">
                                                                <input th:name="|dispatchAllocationVOList[${segmentVOStat.index}].costShareVOList[5].freeType|" value="0" type="hidden">

                                                            </div>
                                                        </td>
                                                        <!-- 发货单号 -->
                                                        <td>
                                                            <div>[[${segmentVO.invoiceVbillno}]]</div>
                                                            <div th:id="|negativeGrossProfitDiv_${segmentVO.segmentId}|" style="display: none;">
                                                                <div class="label label-danger">负毛利区间</div>
                                                                <div th:id="|negativeGrossProfit_${segmentVO.segmentId}|" style="padding-top: 5px;color: #ed5565;font-size: 1em"></div>
                                                            </div>
                                                        </td>
                                                        <!-- 要求车长车型 -->
                                                        <td th:each="dict : ${@dict.getType('car_type')}" th:if="${dict.dictValue} == ${segmentVO.carType}" th:text="${dict.dictLabel}"></td>
                                                        <!-- 要求提货日期 -->
                                                        <td th:text="${#dates.format(segmentVO.reqDeliDate,'yyyy-MM-dd HH:mm:ss')}"></td>
                                                        <!-- 提货方 -->
                                                        <td>
                                                            <div th:if="${not #lists.isEmpty(segmentVO.multipleShippingAddressList)}" th:each="shippingAddress,shippingAddressStat : ${segmentVO.multipleShippingAddressList}">
                                                                <span th:text="${shippingAddress.deliAddrName}"></span>
                                                            </div>
                                                        </td>
                                                        <!-- 到货方 -->
                                                        <td>
                                                            <div th:if="${not #lists.isEmpty(segmentVO.multipleShippingAddressList)}" th:each="shippingAddress,shippingAddressStat : ${segmentVO.multipleShippingAddressList}">
                                                                <span th:text="${shippingAddress.arriAddrName}"></span>
                                                            </div>
                                                        </td>
                                                        <!-- 提货货品名称 -->
                                                        <td>
                                                            <div th:if="${not #lists.isEmpty(segmentVO.multipleShippingAddressList)}" th:each="shippingAddress,shippingAddressStat : ${segmentVO.multipleShippingAddressList}">
                                                                <div th:if="${shippingAddress.addressType == 0}" th:each="shippingGoods,shippingGoodsStat : ${shippingAddress.shippingGoodsList}">
                                                                    提货:<span th:text="${shippingGoods.goodsName}"></span>
                                                                </div>
                                                                <div th:if="${shippingAddress.addressType == 1}" th:each="shippingGoods,shippingGoodsStat : ${shippingAddress.shippingGoodsList}">
                                                                    到货:<span th:text="${shippingGoods.goodsName}"></span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <!-- 提货货品名称 -->
                                                        <td>
                                                            <div th:if="${not #lists.isEmpty(segmentVO.multipleShippingAddressList)}" th:each="shippingAddress,shippingAddressStat : ${segmentVO.multipleShippingAddressList}">
                                                                <div th:if="${shippingAddress.addressType == 0}" th:each="shippingGoods,shippingGoodsStat : ${shippingAddress.shippingGoodsList}">
                                                                    <span th:text="${shippingGoods.goodsTypeName}"></span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div th:if="${not #lists.isEmpty(segmentVO.multipleShippingAddressList)}" th:each="shippingAddress,shippingAddressStat : ${segmentVO.multipleShippingAddressList}">
                                                                <div th:if="${shippingAddress.addressType == 0}" th:each="shippingGoods,shippingGoodsStat : ${shippingAddress.shippingGoodsList}">
                                                                    提:<span th:text="${shippingGoods.num}"></span>/<span th:text="${shippingGoods.weight}"></span>/<span th:text="${shippingGoods.volume}"></span>
                                                                </div>
                                                                <div th:if="${shippingAddress.addressType == 1}" th:each="shippingGoods,shippingGoodsStat : ${shippingAddress.shippingGoodsList}">
                                                                    到:<span th:text="${shippingGoods.num}"></span>/<span th:text="${shippingGoods.weight}"></span>/<span th:text="${shippingGoods.volume}"></span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </div>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="panel-group" id="accordionThree">
                        <div  class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <a data-toggle="collapse" data-parent="#accordion"
                                       href="tabs_panels.html#collapseThree">代收凭证</a>
                                </h4>
                            </div>
                            <div id="collapseThree" class="panel-collapse collapse in">
                                <div class="panel-body" style="border-top:0;padding-bottom: 5px;">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <input name="collect" id="collect" class="form-control" type="file" multiple>
                                                    <input type="hidden" id="collectTid" name="collectTid">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="height: 50px;"></div>
        <div class="row">
            <div class="col-sm-offset-5 col-sm-10" style="background-color: #fff;">
                <div class="checkbox mr5"> 
                    <label>
                        <input type="checkbox" name="isUrgent" value="1">紧急单推送
                    </label>
                </div>
        
                <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
                    存</button>&nbsp;
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
                </button>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:src="@{/js/decimal.js}"></script>
<th:block th:include="include :: bootstrap-suggest-js"/>


<!--<script type="text/template" id="rowTmpl">
    <tr class="regRows" id="onWayRows_ZZZINDEXZZZ" rowidx="XXXINDEXXXX">
        <td><a class="close-link del-alink" onclick="removeRow(this,XXXINDEXXXX)" title="删除行">x</a>
        <input name="entrustCostList[XXXINDEXXXX].shareCost" class="shareCost" style="display: none;">
        </td>


        <td>
            <select name="entrustCostList[XXXINDEXXXX].costType" id="costType_XXXINDEXXXX" class="form-control valid costType" onchange="onCostTypeChange(this,XXXINDEXXXX)"
                    th:with="type=${@dict.getType1('cost_type_on_way','on_way_fee')}" >
                <option value="">&#45;&#45; 请选择 &#45;&#45;</option>
                <th:block th:each="dict : ${type}">
                    <option th:if="${dict.dictValue == '1' || dict.dictValue == '22' }" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </th:block>

            </select>
        </td>
        <td><input name="entrustCostList[XXXINDEXXXX].cost" id="money_XXXINDEXXXX" placeholder="" class="form-control valid"
                   oninput="$.numberUtil.onlyNumberTwoDecimal(this)" type="text"  onblur="onMoneyChangeEvent(this,XXXINDEXXXX)"></td>
        <td><input name="entrustCostList[XXXINDEXXXX].memo" id="memo_XXXINDEXXXX" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off"></td>
    </tr>
</script>-->

<script id="scanCodeHtml" type="text/template">
    <div class="form-content">
        <div class="row" >
            <div class="col-md-12"style="text-align:center;">
                <i class="fa fa-check-circle" aria-hidden="true"style="font-size: 50px;color: #1ab394;"></i>
            </div>
            <div class="col-md-12"style="text-align:center;font-size:16px;font-weight: 600;margin-bottom: 30px;">调度成功</div>

            <div class="col-md-12"style="text-align:center;">
                <span  style="font-size:14px;">将二维码 <span style="color: #18a689;">“截图”</span>发送给车主或司机，用 <i class="fa fa-weixin" style="font-size:18px;color:#09BB07;"></i> <span style="color: #09BB07;">微信</span>扫码打开</span>
             </div>
 
             <div class="col-md-12" style="text-align:center;">
                 <img src="" id="scanCodeImg" style='width: 200px;height: 200px;object-fit: contain;'>
             </div>

             <div class="col-md-12 fw" style="text-align:center;font-size:18px" >
                车主： <span id="carrInfo"></span>
            </div>

            <div class="col-md-12 fw" style="text-align:center;font-size:18px" id="driverInfo">
                司机： <span></span>
            </div>

             <div class="col-md-12 fw" style="text-align:center;font-size:18px" id="carrierInfoT"></div>
 
             <div class="col-md-12 mt5" style="text-align:center;font-size:14px;">
                 发货单号: <span id="invoiceVbillnoInfo"></span>
             </div>

           
        </div>
    </div>
</script>


<script th:inline="javascript">


    //是否是车队
    var isFleet = [[${isFleet}]];
    let rateMap = [[${rateMap}]];

    var prefix = isFleet ? ctx + "fleet/segment" : ctx + "tms/segment";

    var carType = [[${@dict.getType('car_type')}]];//车辆类型
    var carLen = [[${@dict.getType('car_len')}]];//车长
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];//费用类型
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];//费用类型
    var invoiceList = [[${invoiceList}]];//运段id
    var segmentIds = [[${segmentIds}]];//运段id
    var privateCar = [[${privateCar}]];//自有承运商类别id
    var outsourcTeam = [[${outsourcTeam}]];//外协车队id
    var logisticsLine = [[${logisticsLine}]];//物流专线id

    var deliProvinceId = [[${segment.deliProvinceId}]];//提货省id
    var deliCityId = [[${segment.deliCityId}]];//提货市id
    var deliAreaId = [[${segment.deliAreaId}]];//提货区id
    var arriProvinceId = [[${segment.arriProvinceId}]];//到货省id
    var arriCityId = [[${segment.arriCityId}]];//到货市id
    var arriAreaId = [[${segment.arriAreaId}]];//到货区id

    var collectAmount = [[${segment.collectAmount}]];
    var numCount = [[${numCount}]];
    var weightCount = [[${weightCount}]];
    var volumeCount = [[${volumeCount}]];

    var g7Rate = [[${@sysConfigServiceImpl.selectConfigByKey("net_profits_g7_rate")}]];

    var oilTax = [[${@sysConfigServiceImpl.selectConfigByKey("oil_tax_rate")}]];
    var cashTax = [[${@sysConfigServiceImpl.selectConfigByKey("cash_tax_rate")}]];

    var dispatchSegmentVO = [[${dispatchSegmentVO}]]

    var isNoinv = [[${isNoinv}]]
    //毛利
    let profitAll = [[${profitAll}]]

    let segmentCarLen = [[${segmentCarLen}]]
    let segmentTransCodeList = [[${segmentTransCodeList}]]
    let parentDeptId = [[${parentDeptId}]]

    let guidingPriceCompRatio = [[${guidingPriceCompRatio}]]
    //发送app承运商,当为true时，去除车牌号、司机必填限制
    var isRmReq = false;

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');

        $("#billingType").val("6");
        //图片功能
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("collect", "collectTid", picParam);

        //油卡添加校验
        for (var i = 0; i < costTypeFreight.length; i++) {
            addOilCardNumberCheck(i);
        }

        //发送app承运商
        $('#rmReq').on('ifChanged', function (event) {
            if ($('#rmReq').is(':checked')) {
                isRmReq = true;

                //去除必填限制
                $("#carnoSpan").hide();
                $("#driverNameSpan").hide();
                $("#carno").removeAttr("required");
                $("#driverName").removeAttr("required");

                //校验车牌 用于去除红框
                $("#form-dispatch-add").validate().element($("#carno"));
                //校验司机 用于去除红框
                $("#form-dispatch-add").validate().element($("#driverName"));
            } else {
                isRmReq = false;
                //添加必填限制
                $("#carnoSpan").show();
                $("#driverNameSpan").show();
                $("#carno").attr("required", "required");
                $("#driverName").attr("required", "required");
            }
        });

        //$(".appShow").hide();
        //推送app
        $('#pushApp').on('ifChanged', function (event) {
            /*if($('#pushApp').is(':checked')) {
                $(".appShow").show();
            }else {
                $(".appShow").hide();
            }*/
            //$('#pushApp').prop('checked',false)
        });


        setTransName();

        // $("#ltlType").attr("required", "required");

        getReferencePrice()

        if (dispatchSegmentVO.length == 1) {
            $("#allocationType").val("3");
        }

        if ([[${isOversize}]] == 1) {
            $('#isOversize').prop('checked', true);
            $("#goodsSizeDiv").show()

        }else {
            $('#isOversize').prop('checked', false);
            $("#goodsSizeDiv").hide()

        }
        changeIsOversize()

        if ([[${isCustomsClearance}]] == 1) {
            $('#isCustomsClearance').prop('checked', true);

        }else {
            $('#isCustomsClearance').prop('checked', false);

        }
        changeIsCustomsClearance()


        initActualPayeeBsSuggest()


        // 动态控制收款账号是否必填
        function updateCollectAccountRequired() {
            var collectAmount = parseFloat($("#collectAmount").val()) || 0;
            var costAmountFreight = parseFloat($("#costAmountFreight").val()) || 0;

            if (collectAmount >= costAmountFreight) {
                // 收款金额大于等于运费成本，设为非必填
                // $("#collectAccountId").removeAttr("required");
                // 隐藏星号标记
                $("label:contains('收款账号')").find(".fcff").hide();
            } else {
                // 收款金额小于运费成本，设为必填
                // $("#collectAccountId").attr("required", "required");
                // 显示星号标记
                $("label:contains('收款账号')").find(".fcff").show();
            }
        }

        // 监听收款金额和运费成本的变化
        $(document).on("change keyup", "#collectAmount, #costAmountFreight", function() {
            updateCollectAccountRequired();
        });

        // 页面加载时初始化检查
        updateCollectAccountRequired();

    });

    function initActualPayeeBsSuggest() {
        $("#actualPayee").bsSuggest({
            idField: 'userId',                    //每组数据的哪个字段作为 data-id，优先级高于 indexId 设置（推荐）
            keyField: 'userName',                   //每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            allowNoKeyword: false, //是否允许无关键字时请求数据
            multiWord: false, //以分隔符号分割的多关键字支持separator: ",", //多关键字支持时的分隔符，默认为空格
            getDataMethod: "url", //获取数据的方式，总是从 URL 获取
            effectiveFields: ["userName", "deptName"],
            effectiveFieldsAlias: {userName: "姓名", deptName: "部门"},
            showHeader: true,
            hideOnSelect: true,            // 鼠标从列表单击选择了值时，是否隐藏选择列表
            inputWarnColor: 'rgba(255,0,0,.1)', //输入框内容不是下拉列表选择时的警告色
            url: ctx + 'system/user/bsSuggestUserByDept?deptId=123,124', //custAbbr /*优先从url ajax 请求 json 帮助数据，注意最后一个参数为关键字请求参数*/
            fnPreprocessKeyword: function (keyword) {
                //请求数据前，对输入关键字作进一步处理方法。注意，应返回字符串
                return keyword.trim();
            },
            fnAdjustAjaxParam: function (keyword, options) {  //该插件默认是GET请求  https://github.com/lzwme/bootstrap-suggest-plugin/issues?q=post
                //if(!isNull(keyword)) { //走get请求
                console.log("post")
                return {
                    type: 'POST',
                    timeout: 10000,
                    data: {
                        userName: keyword,
                        // pageSize: 20,  //承运商数据较多 默认只查20条
                        // pageNum: 1
                    }
                }
                //}
            },
            processData: function (res) {// url 获取数据时，对数据的处理，作为 getData 的回调函数
                let {code,data} = res

                if (code !== 0 || data.length === 0) {
                    return false;
                }

                var reData = {value: []}
                data.forEach(item => {
                    reData.value.push({
                        "userId": item.userId,
                        "userName": item.userName,
                        "deptName": item.deptName
                    });

                })
                return reData;
            }
        }).on('onSetSelectValue', function (e, keyword, data) {
            $("#actualPayeeId").val(data.userId);
            $("#actualPayee").val(data.userName);
        });
    }
    $('#actualPayee').on('keyup', function() {
        // if ($(this).val().trim() === '') {
            $('#actualPayeeId').val('');
        // }
    });
    $('#actualPayee').on('blur', function() {
        if ($('#actualPayeeId').val() === '') {
            $('#actualPayee').val('');
        }
    });


    /**
     * 回复现金随着其他应付值改变
     */
    function calPayFee(){
        var costAmount = $("#costAmount").val();
        if ($.common.isNotEmpty(costAmount)) {
            var transFeeCountTotal = 0;
            $("[id^=transFeeCount_]").not("[id$=-error]").each(function () {
                // if (this.id !== 'transFeeCount_6') {
                    var val = $(this).val().replace(/\s+/g, "") == "" ? 0 : $(this).val();
                    transFeeCountTotal = (parseFloat(transFeeCountTotal) + parseFloat(val)).toFixed(2);
                // }
            });
            var transFeeCount4Val = $("#transFeeCount_4").val().replace(/\s+/g, "") == "" ? 0 : $("#transFeeCount_4").val();
            transFeeCountTotal = transFeeCountTotal - parseFloat(transFeeCount4Val);
            $("#transFeeCount_4").val( (Math.round((parseFloat(costAmount) - parseFloat(transFeeCountTotal)) * 100) / 100).toFixed(4) );
        }
        calculateTotal();
        // calculateRate();
        calculateAllocation()
    }



    function addOilCardNumberCheck(ind) {
        //油卡添加校验
        $("#oilCardNumber_"+ind).rules("add", {
            remote: {
                url: ctx + "basic/fuelCard/checkOilCardNumber",
                type: "post",
                dataType: "json",
                data: {
                    fuelcardNo : function() {
                        return $.common.trim($("#oilCardNumber_" + ind).val());
                    },
                    carrierId : function () {
                        return $("#carrierId").val();
                    }
                },
                dataFilter: function(data, type) {
                    if (data > 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
            },
            messages: {
                remote: "无效油卡",
            }
        });
    }

    /**
     * 日期插件
     */
    layui.use('laydate', function(){
        var laydate = layui.laydate;

        //要求提货日期
        var reqDeliDate = laydate.render({
            elem: '#estimatedArrivalTime', //指定元素
            id: '#estimatedArrivalTime',
            format: 'yyyy-MM-dd HH点',
            isInitValue: false,
            trigger: 'click',
            type: 'datetime',
            min: -1,
            fullPanel: true,
            ready: function (date) {
                let inst = laydate.getInst("#estimatedArrivalTime");
                let index = inst.config.index;

                var styleElement = $("<style>");
                // 在<style>元素中添加你的样式
                styleElement.text(".layui-laydate {width: 385px !important;} .layui-laydate-header{width: 72%;} .layui-laydate-content > ul {width: 20% !important;} .laydate-time-list > li {width: 100% !important;} .laydate-time-list > li:nth-last-child(2), .laydate-time-list > li:last-child {display: none;}");

                // 将<style>元素添加到指定的元素中
                $('#layui-laydate' + index).append(styleElement);
            },
            done: function (value, date, endDate) {
                reqArriDate.config.min = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date,
                    hours: date.hours,
                    minutes: date.minutes,
                    seconds: date.seconds
                };
                $("#estimatedArrivalTime").val(value);
                //单独校验日期
                $("#form-dispatch-add").validate().element($("#estimatedArrivalTime"));
            }
        });

        //预计到达时间
        laydate.render({
            elem: '#estimatedArrivalTime',
            type: 'datetime',
            trigger: 'click'
        });
    });

    /**
     * 选择打款账户
     */
    function selectPaymentAccount() {
        var paymentAccount = $("#paymentAccount").val();
        //0司机
        if(paymentAccount == 0){
            $("#paymentAccountName").val($("#driverName").val());
            $("#paymentAccountPhone").val($("#driverMobile").val());
        }
        //1承运商
        if(paymentAccount == 1){
            $("#paymentAccountName").val($("#carrName").val());
            $("#paymentAccountPhone").val($("#carrierPhone").val());
        }
    }

    /**
     * 计算合计
     */
    function calculateTotal() {
        var transFeeCount_0 = $("#transFeeCount_0").val();
        if(transFeeCount_0.replace(/\s+/g, "") == "")transFeeCount_0=0;
        var transFeeCount_1 = $("#transFeeCount_1").val();
        if(transFeeCount_1.replace(/\s+/g, "") == "")transFeeCount_1=0;
        var transFeeCount_2 = $("#transFeeCount_2").val();
        if(transFeeCount_2.replace(/\s+/g, "") == "")transFeeCount_2=0;
        var transFeeCount_3 = $("#transFeeCount_3").val();
        if(transFeeCount_3.replace(/\s+/g, "") == "")transFeeCount_3=0;
        var transFeeCount_4 = $("#transFeeCount_4").val();
        if(transFeeCount_4.replace(/\s+/g, "") == "")transFeeCount_4=0;
        var transFeeCount_5 = $("#transFeeCount_5").val();
        if(transFeeCount_5.replace(/\s+/g, "") == "")transFeeCount_5=0;


        /*$("[id^=transFeeCount_]").not("[id$=-error]").each(function () {
            var val = $(this).val().replace(/\s+/g, "") == "" ? 0 : $(this).val();
            transFeeCountTotal = (parseFloat(transFeeCountTotal) + parseFloat(val)).toFixed(2);
        });*/

         //结算金额
         let costAmountFreight=parseFloat($("#costAmountFreight").val()) || 0;
        
        // let calculateTotal=(parseFloat(transFeeCount_1) + parseFloat(transFeeCount_3) + parseFloat(transFeeCount_5)).toFixed(4);
        // let cashSum=(parseFloat(transFeeCount_0) + parseFloat(transFeeCount_2) + parseFloat(transFeeCount_4)).toFixed(4);
        let calculateTotal = new Decimal(transFeeCount_1).plus(transFeeCount_3).plus(transFeeCount_5)
        let cashSum = new Decimal(transFeeCount_0).plus(transFeeCount_2).plus(transFeeCount_4)

        //油卡总金额
        $("#calculateTotal").text(calculateTotal.toFixed(2) );
        //现金总金额
        $("#cashSum").text(cashSum.toFixed(2) );

        let billingType=$("#billingType").val()


        let msrp3= costAmountFreight;
        if(freightFeeRate!=0){
            msrp3=(costAmountFreight*(1+(freightFeeRate/100))).toFixed(4);
        }

        $("#msrp3").text( Math.round(msrp3 * 100) / 100 );
        $("#costAmount").val(Math.round(msrp3 * 100) / 100);

        // let transFeeCount_6 = $("#transFeeCount_6").val();
        // let otherSum = new Decimal(transFeeCount_6 == '' ? 0 : transFeeCount_6)
        // 计算在途费用总和
        let onWayFeeSum = new Decimal(0);
        for (let i = 0; i <= 5; i++) {
            let onWayFeeValue = $("#onWayFee_" + i).val();
            if (onWayFeeValue && onWayFeeValue !== '') {
                onWayFeeSum = onWayFeeSum.plus(new Decimal(onWayFeeValue));
            }
        }

        let msrp2 = calculateExclusionTax(calculateTotal, cashSum, onWayFeeSum);

        $("#costAmountBilling").val(msrp2);


        //计算毛利
        let pHtml = calculateNegativeGrossProfitHtml(msrp2, profitAll);
        $('[name=ycyy]').prop("disabled", true);
        if (pHtml !== '') {
            $("#negativeGrossProfit").html(pHtml);
            $("#costAmountFreightDiv").attr('class', 'col-sm-7');
            $("#negativeGrossProfitDiv").show();
            $('[name=ycyy]').prop("disabled", false);
        }else {
            $("#costAmountFreightDiv").attr('class', 'col-sm-10');
            $("#negativeGrossProfitDiv").hide();
        }

        let costComp = 0;
        if (billingType == 4) {
            costComp = costAmountFreight * (1 - guidingPriceCompRatio);
        }else {
            costComp = costAmountFreight - (calculateTotal * guidingPriceCompRatio);
        }

        let referencePrice = $("#referencePrice").val();
        //是否超指导价
        if (costComp && referencePrice && referencePrice != 0 && costComp > referencePrice) {

            let excReferencePrice = Math.abs((costComp - referencePrice).toFixed(2));

            $("#excReferencePriceSpan").show()
            $("#excReferencePrice").text(excReferencePrice.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            if ($("#balaType").val() != '2') { // 非月结
                $('[name=ycyy]').prop("disabled", false);
            }
        }else {
            $("#excReferencePriceSpan").hide()
        }

        //showSumMoney();

        calculateAllocation()
        //计算成本分摊总和
        calShareCostSum();
    }

    function calculateExclusionTax(oilSum, cashSum, otherSum) {
        // console.log("oilSum", oilSum);
        // console.log("cashSum", cashSum);
        let billingType = $("#billingType").val()
        let msrp2
        if (isNoinv === 1) {
            msrp2 = oilSum.plus(cashSum).plus(otherSum).toFixed(2);
        } else {
            msrp2 = oilSum.plus(oilSum.mul(oilTax));
            // console.log("oilSum-1", msrp2);

            if (billingType == 6) {
                msrp2 = msrp2.plus(cashSum.plus(cashSum.mul(cashTax)));
                msrp2 = msrp2.plus(otherSum.plus(otherSum.mul(cashTax))).toFixed(2);
            } else {
                msrp2 = msrp2.plus(cashSum);
                msrp2 = msrp2.plus(otherSum).toFixed(2);
            }
        }

        return msrp2
    }

    function calculateNegativeGrossProfitHtml(msrp2,profit) {
        console.log("msrp2", msrp2);
        console.log("11profitAll", profit);

        profit = parseFloat(profit) || 0
        let html = ''

        if (profit && msrp2 > profit) {
            let negativeGrossProfit = Math.abs((profit - msrp2).toFixed(2));

            if (negativeGrossProfit >= 0 && negativeGrossProfit < 20) {
                html = '[0-20]'
            }else if (negativeGrossProfit >= 20 && negativeGrossProfit < 50) {
                html = '[20-50]'
            }else if (negativeGrossProfit >= 50 && negativeGrossProfit < 100) {
                html = '[50-100]'
            }else if (negativeGrossProfit >= 100 && negativeGrossProfit < 200) {
                html = '[100-200]'
            }else if (negativeGrossProfit >= 200 && negativeGrossProfit < 400) {
                html = '[200-400]'
            }else if (negativeGrossProfit >= 400 && negativeGrossProfit < 600) {
                html = '[400-600]'
            }else if (negativeGrossProfit >= 600) {
                html = '[600及以上]'
            }
        }


        return html
    }


    function changeOli() {
        var transFeeCount_1 = $("#transFeeCount_1").val();
        if(transFeeCount_1.replace(/\s+/g, "") == "")transFeeCount_1=0;

        var transFeeCount_3 = $("#transFeeCount_3").val();
        if(transFeeCount_3.replace(/\s+/g, "") == "")transFeeCount_3=0;

        var transFeeCount_5 = $("#transFeeCount_5").val();
        if(transFeeCount_5.replace(/\s+/g, "") == "")transFeeCount_5=0;

        let calculateTotal = new Decimal(transFeeCount_1).plus(transFeeCount_3).plus(transFeeCount_5)
        var costAmountFreight = $("#costAmountFreight").val();

        if (costAmountFreight) {
            let percentage = calculateTotal.div(costAmountFreight).mul(100);
            $("#oilAmount").val(calculateTotal)
            $("#oilRatio").val(percentage.toFixed(2))

        }
    }

    /**
     * 设置运输方式名称
     */
    // let guidingPrice = [[${guidingPrice}]]
    function setTransName() {
        $("#transName").val($("#transCode option:selected").text());
        //$('#ltlType').val('')
        //非整车，指导价设为空
        let transCode = $("#transCode").val();
        if(transCode=='0'||transCode=='15'||transCode=='4'){
            //$('.ltlType').removeClass('hide')
            $('#ltlType').val(1)
            $('#ltlType').attr("disabled", true);
        }else{
            //$('.ltlType').addClass('hide')
            $('#ltlType').attr("disabled", false);
            $('#ltlType').val('')
        }

        // if(transCode == '0'){
        //     $("#guidingPrice").val(guidingPrice);
        // }else{
        //     $("#guidingPrice").val(0);
        // }
    }
    
     /**
     * 获取收款人身份证
     */
    function setTransCodeName() {
        let idCard=$("#transCodeOne option:selected").attr("tid");
        $("#idCard").val(idCard)
    }

    var depositConfig = null;
    function judgeDeposit() { // 判断定金金额
        $('#depositAmount').val('');
        $('#sysDepositAmount').val('');

        // $('#depositAmount').text('');
        // $('[name=depositAmount]').val('');
        if (!genDeposit) {
            return;
        }
        /*if (!depositConfig) {
            $.ajax({
                url: ctx + 'new-deposit/config',
                cache: false,
                async: false,
                type: 'get',
                success: function (res) {
                    if (res.code == 0) {
                        depositConfig = res.data;
                    }
                }
            });
        }*/
       /* let amount = parseFloat($('#costAmountFreight').val(), 10);
        if (amount && !isNaN(amount)) {
            for (let i = 0; i < depositConfig.length; i++) {
                if (amount > depositConfig[i].startN && amount <= depositConfig[i].endN) {
                    if ($('[name=depositWay]').val() == 'yfzdj') {
                        $('#depositAmount').val(0);
                    } else {
                        $('#depositAmount').val(depositConfig[i].amount.toFixed(2));
                    }
                    $('#sysDepositAmount').val(depositConfig[i].amount.toFixed(2));

                    // $('#depositAmount').text(depositConfig[i].amount.toFixed(2));
                    // $('[name=depositAmount]').val(depositConfig[i].amount);
                    return;
                }
            }
            $('#depositAmount').val('');
            $('#sysDepositAmount').val('');

            // $('#depositAmount').text('未定');
            // $('[name=depositAmount]').val('');
        } else {
            $('#depositAmount').val('');
            $('#sysDepositAmount').val('');

            // $('#depositAmount').text('');
            // $('[name=depositAmount]').val('');
        }*/
    }

    /**
     *  选择承运商
     */
    var min_oilRatio = null

    var genDeposit = false;//是否生成定金
    var freightFeeRate=0;
    function selectCarrier(){
        let url = ctx + "basic/carrier/selectCarrierDispatch?segmentId=" + $("#segmentIds").val()+"&isFleet="+isFleet;
        $.modal.open("选择承运商", url,'1100','',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            if(rows[0]["isblaclklist"] == 1){
                $.modal.alertWarning("黑名单承运商请勿选择");
                return;
            }
            if(rows[0]["freightFeeRate"]!=null&&rows[0]["freightFeeRate"]!=0){
                freightFeeRate=rows[0]["freightFeeRate"];
                $("#freightFeeRateTooltip").attr("title","运费加票点："+freightFeeRate+"%");
                $("#freightFeeRateTooltip").css("display","inline-block")  
            }else{
                freightFeeRate=0;
                $("#freightFeeRateTooltip").attr("title"," ");
                $("#freightFeeRateTooltip").css("display","none")
            }
            $("#freightFeeRate").val(freightFeeRate);
            $("#balaType").val(rows[0]["balaType"]);
            $("#oilRatio").val(rows[0].oilCardRate);
            calOilAmount(2);
            //清空车牌号信息
            $("#carnoId").val("");
            $("#carno").val("");
            //清空车长
            $("#carLenId").val("");
            $("#carLenName").val("");
            //清空车型
            $("#carTypeCode").val("");
            $("#carTypeName").val("");
            //清空挂车
            $("#trailerId").val("");
            $("#trailerNo").val("");
            //清空司机
            $("#driverName").val("");
            $("#driverId").val("");
            //清空司机手机
            $("#driverMobile").val("");
            $("#cardId").val("");
            $("#balaTypeT").html("");

            $("#driverIsDocFull").hide().off('click');
            $("#isDocFullSpan").hide().off('click');

            $("#balaTypeT").css("background-color", "#ffffff")
            $("#ifHasBill").val("");
            $("#billingType").val("6");
            
            //赋值
            if(rows[0]["carrType"]!='0') {// 0=自有2=外协3=专线20=车队
                if (rows[0]["billingType"]/*&&rows[0]["billingType"]!="null"&&rows[0]["billingType"]!=""*/) {
                    $("#billingType").val(rows[0]["billingType"]);
                }
            }
            $("#billingType").prop("disabled", rows[0]["balaType"]==2 && $("#billingType").val() != '6'); // 月结+非不开票的禁止修改
            $("#ifHasBill").val(rows[0]["ifHasBill"]);
            $("#carrName").val(rows[0]["carrName"]);
            $("#carrCode").val(rows[0]["carrCode"]);
            $("#carrierId").val(rows[0]["carrierId"]);
            //承运商类别
            $("#carrType").val(rows[0]["carrType"]);
            //承运商电话
            $("#carrierPhone").val(rows[0]["phone"]);

            $("#unitPriceSpan").html("");
            $("#unitPrice").val("");
            
            $("#balaTypeT").html(rows[0]["balaType"]==2?'月度':'单笔');
            $("#balaTypeT").css("background-color", rows[0]["balaType"]==2?'#FF9008':'#009AFE');
            if (rows[0]["balaType"] == 2) {
                $('#depositPanel').hide();
                genDeposit = false;
                judgeDeposit()
                $('#transFeeCount_0').prop("disabled", false);// 非熟车允许预付现金
                $('#transFeeCount_2').prop("disabled", false);// 非熟车允许到付现金
            } else {
                $('#depositPanel').show();
                genDeposit = true;
                judgeDeposit()
                $('#transFeeCount_0').prop("disabled", false);// 非熟车允许预付现金
                $('#transFeeCount_2').prop("disabled", false);// 非熟车允许到付现金
                $.ajax({
                    url: ctx + "finance/carrierMargin/balance",
                    cache: false,
                    type: 'post',
                    data: {carrierId: rows[0]["carrierId"]},
                    success: function (res) {
                        if (res.code == 0) {
                            let balance = res.data;
                            if (balance > 0) {
                                $('#depositPanel').hide();
                                genDeposit = false;
                                judgeDeposit()
                            } else {
                                if (rows[0].familiar == 1) {
                                    $('#transFeeCount_0').val('').prop("disabled", true).change();// 熟车不允许预付现金
                                    $('#transFeeCount_2').val('').prop("disabled", true).change();// 熟车不允许预付现金
                                    // 查询定金余额
                                    $.ajax({
                                        url: ctx + "new-deposit/balance",
                                        cache: false,
                                        type: 'get',
                                        data: {carrierId: rows[0]["carrierId"]},
                                        success: function (res) {
                                            if (res.code == 0) {
                                                let balance = res.data;
                                                if (balance > 0) {
                                                    $('#depositPanel').hide();
                                                    genDeposit = false;
                                                    judgeDeposit()
                                                }
                                            } else {
                                                $.modal.alertError(res.msg)
                                            }
                                        }
                                    });
                                }
                            }
                        } else {
                            $.modal.alertError(res.msg)
                        }
                    }
                })

            }
            if (rows[0]["balaType"] == 2 && rows[0].oilCardRate) {
                // $("#oilRatio").attr("disabled", "disabled");
                // $("#oilAmount").attr("disabled", "disabled");
                //$("#transFeeCount_5").attr("disabled", "disabled");
                //$("#billingType").attr("disabled", "disabled");

                min_oilRatio = rows[0].oilCardRate
            }else {
                // $("#oilRatio").removeAttr("disabled");
                // $("#oilAmount").removeAttr("disabled");
                //$("#transFeeCount_5").removeAttr("disabled");
                //$("#billingType").removeAttr("disabled");

                min_oilRatio = null
            }

            ifG7code();
            //获取承运商协议 价格
            getCarrierProtocolPrice();
            /**
              * 断承运商是否维护了此线路的协议价：
              *          若存在，则计价方式必填
              *          不存在，则计价方式非必填
            */
            //judgeCarrierProtocolPriceExist();
            /*
             * 判断结算方式:1单笔，2月度付款
             *      月度：填写油卡比例；只填写回付现金
             */
            // $(".oilCard").removeAttr("disabled");
            var balaType = rows[0]["balaType"];

            if(balaType == 2){
                //油卡金额 油卡比例 二选一 必填
                // addOilRatiooilAmountValidate();
                //隐藏非回付现金
                for (var i = 0; i < 6; i++) {
                    if(i!=4){
                        $("#isShow_"+i).hide();
                    }
                }
                //专线
                if (logisticsLine === rows[0]["carrType"]){
                    //月度付款油卡变灰
                    // $(".oilCard").attr("disabled","disabled");
                }

                $("#name_4").text("回付金额");

                //删除校验
                $("#estimatedArrivalTimeSpan").hide();
                $("#estimatedArrivalTime").removeAttr("required");
                //打款账户去掉必填校验
                $("#paymentAccount").removeAttr("required");
                $("#paymentAccountSpan").css("color", "");
            }else{
                //删除校验
                $("#oilRatio").rules("remove");
                $("#oilAmount").rules("remove");
                for (var i = 0; i < 6; i++) {
                    $("#isShow_"+i).show();
                }
                $("#transFeeCount_4").val("");
                //校验油卡比例 用于去除红框
                // $("#form-dispatch-add").validate().element($("#oilRatio"));
                $("#form-dispatch-add").validate().element($("#oilAmount"));
                //预计到达时间必填
                $("#estimatedArrivalTime").attr("required", "required");
                // // $("#ltlType").attr("required", "required");
                $("#estimatedArrivalTimeSpan").show();
                //打款账户必填
                $("#paymentAccount").attr("required", "required");
                $("#paymentAccountSpan").css("color", "red");
            }



            //司机按钮放开可选
            $("#driverName").attr("disabled", false);
            $("#driverName").attr("readonly", true);


            //选择打款账户
            selectPaymentAccount()
            // $("#pricingMethod").removeAttr("required");
            // $("#pricingMethodSpan").hide();

            $("#form-dispatch-add").validate().element($("#pricingMethod"));
            if (logisticsLine === rows[0]["carrType"]) {
                //专线计价方式添加验证
                // $("#pricingMethodSpan").show();
                //
                // $("#pricingMethod").attr("required", "required");
            }
            //$("#form-dispatch-add").validate().element($("#pricingMethod"));


            let requiredFlag = rows[0]["requiredFlag"];

            if (!isRmReq) {
                if (requiredFlag === 1) {
                    //车辆司机必填
                    $("#carnoSpan").show();
                    $("#carno").attr("required", "required");

                    $("#driverNameSpan").show();
                    $("#driverName").attr("required", "required");
                }else if (requiredFlag === 2) {
                    //车辆必填
                    $("#carnoSpan").show();
                    $("#carno").attr("required", "required");

                    $("#driverNameSpan").hide();
                    $("#driverName").removeAttr("required");
                }else if (requiredFlag === 3) {
                    //司机必填
                    $("#carnoSpan").hide();
                    $("#carno").removeAttr("required");

                    $("#driverNameSpan").show();
                    $("#driverName").attr("required", "required");
                }else {
                    //都不必填
                    $("#carnoSpan").hide();
                    $("#carno").removeAttr("required");
                    $("#driverNameSpan").hide();
                    $("#driverName").removeAttr("required");
                }
                $("#form-dispatch-add").validate().element($("#carno"));
                $("#form-dispatch-add").validate().element($("#driverName"));

/*
                if (privateCar === rows[0]["carrType"]) {
                    //如果为自有车队
                    //车辆司机必填
                    $("#carnoSpan").show();
                    $("#driverNameSpan").show();
                    $("#carno").attr("required", "required");
                    $("#driverName").attr("required", "required");

                    /!**
                     * todo:暂注释 2020-06-03
                     *!/
                    // 则无法选择司机
                    // $("#driverName").attr("disabled", true);
                    // $("#driverName").attr("readonly", false);
                } else if (outsourcTeam === rows[0]["carrType"]) {
                    //外协车队
                    //车辆司机必填
                    $("#carnoSpan").show();
                    $("#driverNameSpan").show();
                    $("#carno").attr("required", "required");
                    $("#driverName").attr("required", "required");
                } else if (logisticsLine === rows[0]["carrType"]) {
                    //专线计价方式添加验证
                    $("#pricingMethod").attr("required", "required");
                    //物流专线车辆司机非必填
                    $("#carnoSpan").hide();
                    $("#driverNameSpan").hide();
                    $("#carno").removeAttr("required");
                    $("#driverName").removeAttr("required");

                    //校验车牌 用于去除红框
                    $("#form-dispatch-add").validate().element($("#carno"));
                    //校验司机 用于去除红框
                    $("#form-dispatch-add").validate().element($("#driverName"));
                }*/
            } else {
                //当勾选 发送app承运商时，不必填
                $("#carnoSpan").hide();
                $("#driverNameSpan").hide();
                $("#carno").removeAttr("required");
                $("#driverName").removeAttr("required");

                //校验车牌 用于去除红框
                // $("#form-dispatch-add").validate().element($("#carno"));
                // //校验司机 用于去除红框
                // $("#form-dispatch-add").validate().element($("#driverName"));


            }

            //校验承运商
            $("#form-dispatch-add").validate().element($("#carrName"));

            //校验油卡
            for (var i = 0; i < costTypeFreight.length; i++) {
                var id = "#oilCardNumber_" + i;
                // $("#form-dispatch-add").validate().element($(id));
            }


            /**
             *  查询承运商下的收款账户
             */
            loadCarrBankData();

            //加载车牌
            loadVehicle();

            //加载司机
            loadDriver();

            layer.close(index);
        });
    }
     /**
     * 判断是否显示g7不含税
     */
    function ifG7code() {
        let carrType=$("#carrType").val();
        let ifHasBill=$("#ifHasBill").val();
        
        let idCard=$("#idCard").val();
        let cardId=$("#cardId").val();

        if(idCard && idCard !='' && idCard !=null&&cardId && cardId !='' && cardId !=null){

            if(carrType=="0"||ifHasBill=="1"||idCard!=cardId){
                $("#g7Code").css('display', 'none')
            }else{
                $("#g7Code").css('display', 'block')
            }

        }else{
            if(carrType=="0"||ifHasBill=="1"){
                $("#g7Code").css('display', 'none')
            }else{
                $("#g7Code").css('display', 'block')
            }
        }

    }

    /**
     * 查询承运商下的收款账户
     */
    function loadCarrBankData(){
        let options = '<option value="">请选择</option>';
        $(".transCode").html(options);
        let carrierId = $("#carrierId").val();
        if('' == carrierId)return ;
        $.ajax({
            url: prefix + "/queryCarrierBankList/"+carrierId,
            type: 'post',
            dataType: 'json',
            data: {},
            success: function(res) {
                let dataList = res.data;
                if(null != dataList){
                    for(let idx in dataList){
                        $(".transCode").append('<option value="'+dataList[idx].carrBankId+'" tid="'+dataList[idx].idCard+'">'+dataList[idx].bankAccount+"-"+dataList[idx].bankCard+'</option>');
                    }
                }
            }
        })
    }

    function loadVehicle(){
        $.ajax({
            url: ctx + "basic/car/selectByCarTypeAndArri?carrierId=" + $("#carrierId").val() ,
            type: "post",
            dataType: "json",
            data: {},
            success: function (result) {
                if (result.code == 0) {
                    if(result.total > 1 || result.total == 0)return;

                    let data = result.rows[0];
                    if(data.isblacklist  != '0'){
                        return;
                    }

                    var carLengthId = data.carLengthId;

                    if (checkCarAndDrvStatus(carLengthId, data.checkStatus)) {
                        return;
                    }

                    //清空车长
                    $("#carLenId").val("");
                    $("#carLenName").val("");
                    //清空车型
                    $("#carTypeCode").val("");
                    $("#carTypeName").val("");

                    //车辆id
                    $("#carnoId").val(data.carId);
                    //车牌
                    $("#carno").val(data.carno);
                    //车长
                    carLen.forEach(function (value, index, array) {
                        if (value.dictValue == carLengthId) {
                            $("#carLenId").val(carLengthId);
                            $("#carLenName").val(value.dictLabel)
                        }
                    });
                    //车型
                    var carTypeId = data.vehicleclassificationcode;
                    carType.forEach(function (value, index, array) {
                        if (value.dictValue == carTypeId) {
                            $("#carTypeCode").val(carTypeId);
                            $("#carTypeName").val(value.dictLabel)
                        }
                    });

                   /* var carrType = $("#carrType").val();
                    //根据承运商查询对应的司机
                    $.ajax({
                        url: ctx + "basic/driver/getDriverByCarrTypeAndCarId",
                        type: "post",
                        dataType: "json",
                        data: {carId: data.carId,carrType:carrType},
                        success: function (result) {
                            if (result.code == 0) {
                                $("#driverName").val(result.data.driverName);
                                $("#driverId").val(result.data.driverId);
                                $("#driverMobile").val(result.data.driverMobile);

                                //校验司机
                                $("#form-dispatch-add").validate().element($("#driverName"));
                            }
                        }
                    });*/
                    //校验车辆
                    $("#form-dispatch-add").validate().element($("#carno"));

                    let isDocFull = data.isDocFull
                    let carId = data.carId
                    if (isDocFull != null && isDocFull == 0) {
                        $("#isDocFullSpan").show().on('click', function() {
                            let url = "/basic/car/edit/"+carId+"?source=dispatch";
                            editCar(url)
                        })
                    }else {
                        $("#isDocFullSpan").hide().off('click');
                    }

                    getReferencePrice()
                }
            }
        });
    }

    function loadDriver(){
        $.ajax({
            url: ctx + "basic/driver/selectDriverInfo?carrierId=" + $("#carrierId").val() ,
            type: "post",
            dataType: "json",
            data: {},
            success: function (result) {
                if (result.code == 0) {
                    if(result.total > 1 || result.total == 0)return;

                    let data = result.rows[0];
                    if(data.isblacklist  != '0'){
                        return;
                    }

                    if (checkCarAndDrvStatus(null, data.checkStatus)) {
                        return;
                    }


                    //清空数据
                    $("#driverMobile").val("");

                    //拼接司机名称
                    var driverName = data.driverName;

                    //拼接司机id
                    var driverId = data.driverId;
                    //拼接司机手机
                    var driverMobile =data.phone;

                    //回填数据
                    $("#driverName").val(driverName);
                    $("#driverId").val(driverId);
                    $("#driverMobile").val(driverMobile);
                    $("#cardId").val(data.cardId);
                    ifG7code();
                    $("#form-dispatch-add").validate().element($("#driverName"));

                    let isDocFull = data.isDocFull
                    if (isDocFull != null && isDocFull == 0) {
                        $("#driverIsDocFull").show().on('click', function() {
                            let url = ctx + "basic/driver/edit/"+driverId+"?source=dispatch";;
                            editDriver(url)
                        })
                    }else {
                        $("#driverIsDocFull").hide().off('click');
                    }

                    //选择打款账户
                    selectPaymentAccount();
                }
            }
        });
    }

    /**
     *  判断是否要校验 车辆 司机状态
     */
    function checkCarAndDrvStatus(carLen, checkStatus) {
        // 将低车长列表存储在数组中
        //4.2 、 2.8 、 4.8 、 5.2 、 6.2 、 6.8 、 7.8 、8.2 、8.7 、9.6
        const carLengths = [8, 6, 9, 10, 11, 12, 13, 15, 14, 16];

        let b = segmentCarLen.some(len => carLengths.includes(parseInt(len, 10)));

        let b1 = false;
        if (carLen) {
            b1 = carLengths.includes(parseInt(carLen, 10))
        }
        //公路整车
        const transCodeList = ["0"];
        let transCode = $("#transCode").val();
        let b2 = segmentTransCodeList.some(x => transCodeList.includes(x));
        let b3 = transCodeList.includes(transCode);

        //1403 调度三部
        if ((b || b1) && (b2 || b3) && parentDeptId == 1403) {
            if (checkStatus != 1) {
                // return true;
            }
        }

        return false;
    }

    /**
     *  选择选择车辆
     */
    function selectCar(){
        var carrierId = $("#carrierId").val();
        var carrType = $("#carrType").val();

        $.modal.open("选择车辆", ctx + "basic/car/selectCarLic?segmentIds=" + segmentIds + "&carrierId=" + carrierId + "&carrType=" + carrType + "&type=1", '', '', function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            if(rows[0]["isblacklist"] != '0'){
                $.modal.alertWarning("请选择非黑名单车辆");
                return;
            }

            var carLengthId = rows[0]["carLengthId"];

            if (checkCarAndDrvStatus(carLengthId, rows[0]["checkStatus"])) {
                $.modal.alertWarning("请选择审核通过的车辆");
                return;
            }



            //清空车长
            $("#carLenId").val("");
            $("#carLenName").val("");
            //清空车型
            $("#carTypeCode").val("");
            $("#carTypeName").val("");
            //清空司机
            $("#driverName").val("");
            $("#driverId").val("");
            //清空司机手机
            $("#driverMobile").val("");

            $("#cardId").val("");

            //车辆id
            $("#carnoId").val(rows[0]["carId"]);
            //车牌
            $("#carno").val(rows[0]["carno"]);
            //车长
            carLen.forEach(function (value, index, array) {
                if (value.dictValue == carLengthId) {
                    $("#carLenId").val(carLengthId);
                    $("#carLenName").val(value.dictLabel)
                }
            });
            //车型
            var carTypeId = rows[0]["vehicleclassificationcode"];
            carType.forEach(function (value, index, array) {
                if (value.dictValue == carTypeId) {
                    $("#carTypeCode").val(carTypeId);
                    $("#carTypeName").val(value.dictLabel)
                }
            });

            var carrType = $("#carrType").val();
            //根据承运商查询对应的司机
            $.ajax({
                url: ctx + "basic/driver/getDriverByCarrTypeAndCarId",
                type: "post",
                dataType: "json",
                data: {carId: rows[0]["carId"],carrType:carrType},
                success: function (result) {
                    if (result.code == 0) {

                        if (checkCarAndDrvStatus(null, result.data.checkStatus)) {
                            return;
                        }


                        $("#driverName").val(result.data.driverName);
                        $("#driverId").val(result.data.driverId);
                        $("#driverMobile").val(result.data.driverMobile);
                        $("#cardId").val(result.data.cardId);

                        //校验司机
                        $("#form-dispatch-add").validate().element($("#driverName"));

                        let isDocFull = result.data.isDocFull
                        if (isDocFull != null && isDocFull == 0) {
                            $("#driverIsDocFull").show().on('click', function() {
                                let url = ctx + "basic/driver/edit/"+driverId+"?source=dispatch";;
                                editDriver(url)
                            })
                        }else {
                            $("#driverIsDocFull").hide().off('click');
                        }

                    }
                }
            });
            //校验车辆
            $("#form-dispatch-add").validate().element($("#carno"));

            let isDocFull = rows[0]["isDocFull"];
            let carId = rows[0]["carId"]
            if (isDocFull != null && isDocFull == 0) {
                $("#isDocFullSpan").show().on('click', function() {
                    let url = "/basic/car/edit/"+carId+"?source=dispatch";
                    editCar(url)
                })
            }else {
                $("#isDocFullSpan").hide().off('click');
            }

            //获取指导价
            // getGuidePrice();
            getReferencePrice()

            layer.close(index);
        });
    }

    /**
     *  选择选择挂车
     */
    function selectTrailerCar(){
        //承运商id
        var carrierId = $("#carrierId").val();
        $.modal.open("选择挂车", ctx + "basic/car/selectCarLic?carrierId=" + carrierId + "&type=2", '', '', function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //车辆id
            $("#trailerId").val(rows[0]["carId"]);
            //车牌
            $("#trailerNo").val(rows[0]["carno"]);

            layer.close(index);
        });
    }

    /**
     * minPrice:保底价，当计算的价格小于保底价时，用保底价代替价格
     */
    var max_costAmountFreight = null
    function unitPriceChange(minPrice){
        var pricingMethod = $("#pricingMethod").val();
        var unitPrice = $("#unitPrice").val();
        max_costAmountFreight = null
        if(pricingMethod == '0'){
            //按吨
            var price = unitPrice*weightCount;
            if (minPrice && weightCount > 0 && parseFloat(price.toFixed(2)) < minPrice) {
                $.modal.msgWarning("结算金额：" + price.toFixed(2) + "，触发保底价：" + minPrice)
                price = minPrice;
            }
            $("#costAmountFreight").val(price.toFixed(2));
            max_costAmountFreight = price.toFixed(2)
            getChange();
            calOilAmount(1)
        }else if(pricingMethod == '1'){
            var price = unitPrice*volumeCount;
            if (minPrice && volumeCount > 0 && parseFloat(price.toFixed(2)) < minPrice) {
                $.modal.msgWarning("结算金额：" + price.toFixed(2) + "，触发保底价：" + minPrice)
                price = minPrice;
            }
            $("#costAmountFreight").val(price.toFixed(2));
            max_costAmountFreight = price.toFixed(2)

            getChange();
            calOilAmount(1)
        }else if(pricingMethod == '2'){
            var price = unitPrice*numCount;
            if (minPrice && numCount > 0 && parseFloat(price.toFixed(2)) < minPrice) {
                $.modal.msgWarning("结算金额：" + price.toFixed(2) + "，触发保底价：" + minPrice)
                price = minPrice;
            }
            $("#costAmountFreight").val(price.toFixed(2));
            max_costAmountFreight = price.toFixed(2)

            getChange();
            calOilAmount(1)
        }
    }

    /**
     * 选择司机
     */
    function selectDriver() {
        //承运商id
        var carrierId = $("#carrierId").val();
        $.modal.open("选择司机", ctx + "basic/driver/checkboxSelectDriver?type=1&carrierId=" + carrierId, 1000, '', function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            if(rows[0]["isblacklist"] != '0'){
                $.modal.alertWarning("请选择非黑名单司机");
                return;
            }

            if (checkCarAndDrvStatus(null, rows[0]["checkStatus"])) {
                $.modal.alertWarning("请选择审核通过的司机");
                return;
            }


            //清空数据
            $("#driverMobile").val("");

            //拼接司机名称
            var driverName = $.map(rows,function (row) {
                return row["driverName"];
            }).join(",");
            //拼接司机id
            var driverId = $.map(rows,function (row) {
                return row["driverId"];
            }).join(",");
            //拼接司机手机
            var driverMobile = $.map(rows,function (row) {
                return row["phone"];
            }).join(",");
            var cardId = $.map(rows,function(row){
                return row["cardId"]
            }).join(",");

            //回填数据
            $("#driverName").val(driverName);
            $("#driverId").val(driverId);
            $("#driverMobile").val(driverMobile);
            $("#cardId").val(cardId);

            $("#form-dispatch-add").validate().element($("#driverName"));

            let isDocFull = rows[0]["isDocFull"];
            if (isDocFull != null && isDocFull == 0) {
                $("#driverIsDocFull").show().on('click', function() {
                    let url = ctx + "basic/driver/edit/"+driverId+"?source=dispatch";;
                    editDriver(url)
                })
            }else {
                $("#driverIsDocFull").hide().off('click');
            }

            //选择打款账户
            selectPaymentAccount();
            layer.close(index);
        });
    }

    /**
     * 校验
     */
    $("#form-dispatch-add").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            reqDeliDate:{
                required:true,
            },
            reqArriDate:{
                required:true,
            },
        }
    });




    /**
     * 提交
     */
    var transFeeSum = 0;
    function submitHandler() {
        //结算类型，1：单笔结算，2：月结承运商
        var balaType = $("#balaType").val();
        var carrType = $("#carrType").val();
        let ifHasBill=$("#ifHasBill").val();

        //运输方式为公路零担、危化零担 必填
        let transCode = $("#transCode").val();
        let ltlType = $("#ltlType").val();
        // if(ltlType ==''){
        //     $.modal.alertWarning("请选择线段类别");
        //     return;
        // }


      /*  var isOversize = $("#isOversize").is(':checked');

        if(isOversize){
            let goodsLength = $("#goodsLength").val();
            let goodsWidth = $("#goodsWidth").val();
            let goodsHeight = $("#goodsHeight").val();

            if ((goodsLength === undefined || goodsLength === null || goodsLength === '')
                && (goodsWidth === undefined || goodsWidth === null || goodsWidth === '')
                && (goodsHeight === undefined || goodsHeight === null || goodsHeight === '')) {
                $.modal.msgError("大件三超，请填写长、宽、高中的一项。");
                return;
            }
        }
*/

        //预付现金不能小于0
        var transFeeCount_0 = $("#transFeeCount_0").val();
        if(transFeeCount_0 != '' && transFeeCount_0 < 0){
            $.modal.alertWarning("预付现金不能小于0");
            return;
        }
        if(transFeeCount_0 != '' ){
            transFeeSum += Number(transFeeCount_0);
        }

        let oilSum = 0
        //预付油卡不能小于0
        var transFeeCount_1 = $("#transFeeCount_1").val();
        if(transFeeCount_1 != '' && transFeeCount_1 < 0){
            $.modal.alertWarning("预付油卡不能小于0");
            return;
        }
        if (transFeeCount_1) {
            oilSum += parseFloat(transFeeCount_1);
        }

        //到付现金不能小于0
        var transFeeCount_2 = $("#transFeeCount_2").val();
        if(transFeeCount_2 != '' && transFeeCount_2 < 0){
            $.modal.alertWarning("到付现金不能小于0");
            return;
        }

        if(transFeeCount_2 != '' ){
            transFeeSum += Number(transFeeCount_2);
        }

        //到付油卡不能小于0
        var transFeeCount_3 = $("#transFeeCount_3").val();
        if(transFeeCount_3 != '' && transFeeCount_3 < 0){
            $.modal.alertWarning("到付油卡不能小于0");
            return;
        }
        if(transFeeCount_3 != '' ){
            oilSum += parseFloat(transFeeCount_3);
        }

        //检查回付现金不能小于0
        var transFeeCount_4 = $("#transFeeCount_4").val();
        if(transFeeCount_4 != '' && transFeeCount_4 < 0){
            $.modal.alertWarning("回付现金不能小于0");
            return;
        }

        if(transFeeCount_4 != '' ){
            transFeeSum +=  Number(transFeeCount_4);
        }

        //回付油卡不能小于0
        var transFeeCount_5 = $("#transFeeCount_5").val();
        if(transFeeCount_5 != '' && transFeeCount_5 < 0){
            $.modal.alertWarning("回付油卡不能小于0");
            return;
        }
        if(transFeeCount_5 != '' ){
            oilSum += parseFloat(transFeeCount_5);
        }

        //结算金额不能小于0
        var costAmount = $("#costAmount").val();
        if(costAmount < 0 ){
            $.modal.alertWarning("结算金额不能小于0");
            return;
        }


        //到付金额
        var collectAmount = parseFloat($("#collectAmount").val()) || 0;

        // 结算金额
        let costAmountFreight = Number($("#costAmountFreight").val());

        if (min_oilRatio) {
            let minAmount = (costAmountFreight * min_oilRatio / 100).toFixed(2) * 1;
            if (oilSum < minAmount) {
                $.modal.alertWarning("油卡总金额不得低于¥" + minAmount + "元（" + min_oilRatio + "%）");
                return;
            }
        }

        //costAmount > 0：全部为油卡时不报此问题
        if($("#transCodeOne").val() == '' && logisticsLine != carrType && transFeeSum > 0 && collectAmount < costAmountFreight){
            $.modal.alertWarning("请选择收款账户");
            return;
        }

        /*if (min_oilRatio && (oilSum/costAmountFreight*100).toFixed(4) < min_oilRatio) {
            $.modal.alertWarning("油卡总金额不得低于"+min_oilRatio+"%，当前" + (oilSum/costAmountFreight*100).toFixed(4) + "%");
            return;
        }*/

        var b = false;
        // 如果运输类型为 2 并且预付油卡金额不为空
        if (carrType == 2 && balaType == 1 && transFeeCount_1 != '') {
            // 计算预付油卡金额是否小于等于结算金额的百分之三十
            if (parseFloat(transFeeCount_1) > 0.5 * costAmountFreight) {
                // 如果不满足条件，设置标志变量为 true
                b = true;
            }
        }
        // if (b) {
        //     $.modal.alertWarning("预付油卡金额不能超过结算金额的百分之五十");
        //     return;
        // }

        let pricingMethod = $("#pricingMethod").val();
        if (pricingMethod == '0' || pricingMethod == '1' || pricingMethod == '2') {

            let unitPrice = $("#unitPrice").val();
            if (unitPrice == null || unitPrice == '') {
                $.modal.alertWarning("请填写计价方式中的单价。");
                return;
            }

            var price = costAmountFreight

            if(pricingMethod == '0'){
                //按吨
                price = unitPrice*weightCount;
                price = price.toFixed(2)

            }else if(pricingMethod == '1'){
                price = unitPrice*volumeCount;
                price = price.toFixed(2)

            }else if(pricingMethod == '2'){
                price = unitPrice*numCount;
                price = price.toFixed(2)

            }

            //if (price != costAmountFreight) {
            //    $.modal.alertWarning("单价乘以货量与结算金额不符。");
            //    return;
            //}

        }



        /*if((transFeeCount_1 != '' || transFeeCount_3 != 0 || transFeeCount_5 != 0) && 1 == balaType){
            $.modal.alertWarning("请选择油卡卡号");
            return;
        }*/

        // let on_way_costType = $("#on_way_costType").val();
        // let transFeeCount_6 = $("#transFeeCount_6").val();
        //
        // if (on_way_costType != '' && transFeeCount_6 == '') {
        //     $.modal.alertWarning("请选择填写其他费用金额。");
        //     return;
        // }
        // if (on_way_costType == '' && transFeeCount_6 != '') {
        //     $.modal.alertWarning("请选择填写其他费用类型。");
        //     return;
        // }



        //检查成本分摊金额
        let ret = checkCost();

        if(ret == false)return;



        let b1 = !$("#negativeGrossProfitDiv").is(":hidden");

        let b2 = (!$("#excReferencePriceSpan").is(":hidden")) && $("#balaType").val() != '2' ;

        let ycyyVal = $("#ycyy").val();

        //异常原因校验
        if ((b1 || b2) && (ycyyVal == null || ycyyVal.trim() == '')) {
            $.modal.alertWarning("请选择填写异常原因。");
            return;
        }

        if ($('#depositAmount').val().trim() != '' && $('[name=depositWay]').val() == '') {
            $.modal.msgWarning("有定金金额时，请选择‘定金方式’");
            return;
        }

        if ($.validate.form() && $("#form-dispatch-add").validate().element($("#driverName"))) {
            /*
             * 自有承运商校验 司机是否为空
             */
            var driverName = $("#driverName").val();

            // if ((privateCar == carrType || outsourcTeam == carrType) && driverName == "" && !isRmReq) {
            //     $.modal.msgError("自有车队与外协车队司机不能为空！");
            //     return;
            // }

            //结算金额   校验金额是否相对
            var costAmount = decimal($("#costAmount").val(),2);

            if(costAmount < 0 ){
                $.modal.alertWarning("结算金额不能小于0");
                return;
            }

            //付款金额合计
            var transFeeCountTotal = 0;
            $("[id^=transFeeCount_]").not("[id$=-error]").each(function () {
                // if (this.id !== 'transFeeCount_6') {
                    var val = $(this).val().replace(/\s+/g, "") ==""? 0: $(this).val();
                    transFeeCountTotal = parseFloat(transFeeCountTotal) + parseFloat(val);
                // }
            });
            if (costAmount != decimal(transFeeCountTotal,2)) {
                $.modal.msgError("付款金额合计与结算金额不相等！");
                return;
            }



            commit1();
           
        }

    }
    //2023-03-02 过期提醒改变方式，查询
    function commit1(){
        var data = {
            driverId:$("#driverId").val(),
            carrierId:$("#carrierId").val(),
            segmentIds:$("#segmentIds").val(),
            carnoId:$("#carnoId").val()
        }
        $.ajax({
            url: ctx + "tms/segment/dispatchPrevCheck",
            method: 'post',
            dataType: "json",
            data: data,
            success: function (result) {
                jQuery.unsubscribe("cmt");
                if(result.code == 500){
                    $.modal.confirm(result.msg,function(){
                        //var dis = $(":disabled");
                        //dis.attr("disabled", false);
                        //alert("OK500");
                        $('#collect').fileinput('upload');
                        jQuery.subscribe("cmt", commit);
                    });
                }else{
                    //var dis = $(":disabled");
                    //dis.attr("disabled", false);
                    //alert("OK0");
                    $('#collect').fileinput('upload');
                    jQuery.subscribe("cmt", commit);
                }
                //$.modal.msgWarning(result.msg);


            }
        });

    }
    function commit() {
        // 三方费用校验
        if (!validateThirdPartyFees()) {
            return;
        }

        //结算类型，1：单笔结算，2：月结承运商
        var balaType = $("#balaType").val();
        var carrType = $("#carrType").val();
        let ifHasBill=$("#ifHasBill").val();
         //是否需要上传附件凭证
         if(balaType==1&&ifHasBill==0){
            let idCard=$("#idCard").val();
            let cardId=$("#cardId").val();
            let collectTid=$("#collectTid").val();
            
            if(collectTid && collectTid != null && collectTid != ''){ 
                
            }else{
                var checkCollect = false;
                for (let i = 0; i < dispatchSegmentVO.length; i++) {
                    if (dispatchSegmentVO[i].transLineId != '607') { // 资源采购组的单子不需要凭证
                        checkCollect = true;
                        break;
                    }
                }

                var collectAmount = parseFloat($("#collectAmount").val()) || 0;
                var costAmountFreight = parseFloat($("#costAmountFreight").val()) || 0;

                if(checkCollect && (cardId != '' && idCard != cardId) && transFeeSum > 0 && collectAmount < costAmountFreight){
                    $.modal.alertWarning("收款账号与司机不匹配，请上传代收凭证");
                    return;
                }
            }
        }


        var costAmount = decimal($("#costAmount").val(),2);
        var dis = $(":disabled").not('[name=ycyy]');
        dis.prop("disabled", false);
        var data = $("#form-dispatch-add").serializeArray();
        dis.prop("disabled", true);
        data.costAmountOil = transFeeSum;//无效代码
        $.modal.confirm("结算金额:"+costAmount+",确认保存？", function() {
            $.ajax({
                url: prefix + "/dispatch",
                method: 'post',
                dataType: "json",
                data: data,
                success: function (result) { 
                    if (result.code !== 0) {
                        $.modal.msgError(result.msg);
                        // dis.attr("disabled", true);
                    }else{
                        let row=result.data;
                        if(result.msg!='操作成功'){
                            //$.modal.msgWarning(result.msg);
                            setTimeout(function(){
                                scanCode(row.lotId,row.driverName,row.driverMobile,row.carno,row.carrierName,row.carrierPhone,row.carrName,row.driverCardId,row.carrCardId)
                            },2000)
                        }else{
                            scanCode(row.lotId,row.driverName,row.driverMobile,row.carno,row.carrierName,row.carrierPhone,row.carrName,row.driverCardId,row.carrCardId)
                        }
                    }
                }
            });
        });

    }


    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }
    function digitUppercase(n) {
        var fraction = ['角', '分'];
        var digit = [
            '零', '壹', '贰', '叁', '肆',
            '伍', '陆', '柒', '捌', '玖'
        ];
        var unit = [
            ['元', '万', '亿'],
            ['', '拾', '佰', '仟']
        ];
        var head = n < 0 ? '欠' : '';
        n = Math.abs(n);
        var s = '';
        for (var i = 0; i < fraction.length; i++) {
            s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
        }
        s = s || '整';
        n = Math.floor(n);
        for (var i = 0; i < unit[0].length && n > 0; i++) {
            var p = '';
            for (var j = 0; j < unit[1].length && n > 0; j++) {
                p = digit[n % 10] + unit[1][j] + p;
                n = Math.floor(n / 10);
            }
            s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
        }
        return head + s.replace(/(零.)*零元/, '元')
            .replace(/(零.)+/g, '零')
            .replace(/^整$/, '零元整');
    }

    function inputMax() {
        var costAmountFreight = parseFloat($("#costAmountFreight").val()) || 0;

        if (max_costAmountFreight != null && max_costAmountFreight < costAmountFreight) {
            $("#costAmountFreight").val(max_costAmountFreight)
        }

    }

    function getChange(){
        var costAmountFreight = parseFloat($("#costAmountFreight").val()||0);

        var number = digitUppercase(costAmountFreight);

        $('#capital').html(number)

        let msrp3= costAmountFreight;
        if(freightFeeRate!=0){//运费加票点
            msrp3=(costAmountFreight*(1+(freightFeeRate/100))).toFixed(4);
        }
        $("#costAmount").val( Math.round(msrp3 * 100) / 100 );

        judgeDeposit();
    }

    function oilRatioInputMin() {
        var oilRatio = parseFloat($("#oilRatio").val()||0);
        if (min_oilRatio != null && min_oilRatio > oilRatio) {
            $("#oilRatio").val(min_oilRatio)
        }
    }

    function changeIsCustomsClearance() {
        var isCustomsClearance = $("#isCustomsClearance").is(':checked');
        $('input[name="isCustomsClearance"]').val(isCustomsClearance ? '1' : '0');

        getReferencePrice()
    }


    function changeIsOversize() {
        var isChecked = $("#isOversize").is(':checked');
        $('input[name="isOversize"]').val(isChecked ? '1' : '0');

        if (isChecked) {
            $("#goodsSizeDiv").show()
        }else {
            $("#goodsSizeDiv").hide()

        }

        getReferencePrice()
    }
    function changeIsBigCartLoad() {
        var isBigCartLoad = $("#isBigCartLoad").is(':checked');
        $('input[name="isBigCartLoad"]').val(isBigCartLoad ? '1' : '0');

        getReferencePrice()
    }
    /**
     * 计算油卡金额
     */
    function calOilAmount(type) {
        //结算金额
        var costAmount = parseFloat($("#costAmount").val());
        //油卡比例
        var oilRatio = parseFloat($("#oilRatio").val());
        //油卡金额
        var oilAmount = parseFloat($("#oilAmount").val()||0);

        if(type == 2 || type == 1){
            //计算油卡金额
            if(!isNaN(costAmount) && !isNaN(oilRatio)){
                if(costAmount==0){
                    $("#oilAmount").val(0);
                }else{
                    $("#oilAmount").val((costAmount * oilRatio / 100).toFixed(2));
                }
            }
        }
        if(type == 3){
            //计算油卡比列
            if(!isNaN(costAmount) && !isNaN(oilAmount)){
                if(costAmount==0){
                    $("#oilRatio").val(0);
                }else {
                    let oilR = (oilAmount / costAmount * 100).toFixed(2);

                    if (min_oilRatio != null && min_oilRatio > oilR) {
                        $("#oilRatio").val(min_oilRatio)

                        $("#oilAmount").val((costAmount * min_oilRatio / 100).toFixed(2))
                    }else {
                        $("#oilRatio").val(oilR);
                    }


                }
            }
        }
        $("#transFeeCount_1").val("");
        $("#transFeeCount_3").val("");
        $("#transFeeCount_5").val($("#oilAmount").val());

        //回填回付现金
        if(!isNaN(costAmount)){
            if (collectAmount != null && collectAmount != '' && !isNaN(collectAmount)) {
                let cAmount = parseFloat(collectAmount);

                if (cAmount >= costAmount) {
                    $("#transFeeCount_2").val(costAmount);
                }else {
                    $("#transFeeCount_2").val(cAmount);
                    $("#transFeeCount_4").val(Math.round(((costAmount - cAmount) * 100) / 100).toFixed(4) );
                }
            }else {
                $("#transFeeCount_4").val((Math.round(costAmount * 100) / 100).toFixed(4) );
            }
        }
        
        calPayFee();



    }

    function changeOversizeType() {
        $("#oversizeTypeDiv").text("")
        var isOversize = $("#isOversize").is(':checked');
        if(isOversize){
            let goodsLength = $("#goodsLength").val();
            let goodsWidth = $("#goodsWidth").val();
            let goodsHeight = $("#goodsHeight").val();

            if (goodsLength !== '' || goodsWidth !== '' || goodsHeight !== '') {
                $("#oversizeTypeDiv").text("超体积");
            }else {
                $("#oversizeTypeDiv").text("超重");
            }
        }
    }


    /**
     * 月度结算方式 油卡金额，油卡比例 必填一 校验
     *
     * */
    function addOilRatiooilAmountValidate() {
        $("#oilRatio").rules("add", {
            required: {
                depends: function () { //二选一
                    var oilRatio = $('#oilRatio').val();
                    var oilAmount = $('#oilAmount').val();
                    return (oilRatio.length <= 0 && oilAmount.length <= 0);
                }
            }
        });
        $("#oilAmount").rules("add", {
            required: {
                depends: function () { //二选一
                    var oilAmount = $('#oilAmount').val();
                    var oilRatio = $('#oilRatio').val();
                    return (oilAmount.length <= 0 && oilRatio.length <= 0);
                }
            }
        });


    }

    /**
     * 获取承运商协议 价格
     */
    function getCarrierProtocolPrice(){
        //承运商
        var carrierId = $("#carrierId").val();
        $('#costAmountFreight').prop("readonly", false).css("background-color", "").css("cursor", "")
        //计价方式
        var pricingMethod = $("#pricingMethod").val();
        if(pricingMethod == '0' || pricingMethod == '1' || pricingMethod == '2'){
            $("#unitPrice").attr("required",true);
            // 按量计价时不允许修改结算金额
            // $('#costAmountFreight').prop("readonly", true).css("background-color", "#eee").css("cursor","not-allowed");
        }else{
            $("#unitPrice").attr("required",false);
        }
        if($.common.isEmpty(carrierId) || $.common.isEmpty(pricingMethod)){
            return ;
        }

        let transCode = $("#transCode").val();
        if($.common.isEmpty(transCode)) {
            return;
        }
        let goodsKind = null;
        if (transCode == "0" || transCode == "1") {
            goodsKind = "1"; // 公路整车、公路零担视为普货
        } else if (transCode == "15" || transCode == "16") {
            goodsKind = "2"; // 危化整车、危化零担视为危险品
        }
        if($.common.isEmpty(goodsKind)) {
            return;
        }

        //carrType = 3,专线
        //if($("#carrType").val() != 3)return;

        //总件数
        var numCount = $("#numCount").val();
        //总体积
        var volumeCount = $("#volumeCount").val();
        //总重量
        var weightCount = $("#weightCount").val();
        var data = {
            carrierId: carrierId,
            pricingMethod: pricingMethod,
            startProvinceId: deliProvinceId,
            startCityId: deliCityId,
            startAreaId: deliAreaId,
            endProvinceId: arriProvinceId,
            endCityId: arriCityId,
            endAreaId: arriAreaId,
            numCount: numCount,
            volumeCount: volumeCount,
            weightCount: weightCount,
            goodsKind: goodsKind,
            goodsCharacter: goodsKind - 1,
            carLenId: $('#carLenId').val(),
            carTypeId: $('#carTypeCode').val(),
            //roundTrip: $('#pricingMethod').val() == 7 ? 1 : 0
        };

        $.ajax({
            url: ctx + "tms/segment/getCarrierProtocolPrice_v1",
            type: "post",
            dataType: "json",
            data: data,
            success: function (result) {
                var costAmount = result.data.costAmount;
                // $("#costAmount").val(costAmount);
                // $("#costAmount").focus();
                var minPrice = result.data.minPrice;
                $("#unitPriceSpan").html(result.data.guidingPrice);
                $("#unitPrice").val(result.data.guidingPrice);
                if (result.data.guidingPrice > 0) { // 有协议价时不允许修改单价
                    $("#unitPrice").hide();
                    $("#unitPriceSpan").show()
                } else {
                    $("#unitPrice").show();
                    $("#unitPriceSpan").hide()
                }
                //$('#onWayFee_1').val(result.data.pickFee);
                //$('#onWayFee_2').val(result.data.deliFee);
                unitPriceChange(minPrice);
                /*if($.common.isNotEmpty(costAmount) && costAmount != 0){
                    $("#costAmount").attr("disabled",true);
                }else{
                    $("#costAmount").val();
                    $("#costAmount").attr("disabled",false);
                }*/
            }
        });
    }

    function getReferencePrice() {
        $.ajax({
            url: ctx + "tms/segment/getReferencePrice?segmentId=" + segmentIds,
            type: "GET",
            dataType: "json",
            contentType: "application/json; charset=utf-8",
            // data: JSON.stringify(param),
            success: function(result) {
                $("#referencePrice").val("")
                $("#referencePriceSpan").text("¥0.00")

                if (result.code == 0) {
                    if (result.data && result.data.referencePrice) {
                        $("#referencePriceSpan").text(result.data.referencePrice.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
                        $("#referencePrice").val(result.data.referencePrice)
                    }
                    calculateTotal()
                }
            }
        });
    }
    /**
     * 断承运商是否维护了此线路的协议价：
     *          若存在，则计价方式必填
     *          不存在，则计价方式非必填
     */
    function judgeCarrierProtocolPriceExist(){
        //承运商
        // var carrierId = $("#carrierId").val();
        // if($.common.isEmpty(carrierId)){
        //     return ;
        // }
        // var data = {
        //     carrierId: carrierId,
        //     startProvinceId: deliProvinceId,
        //     // startCityId: deliCityId,
        //     // startAreaId: deliAreaId,
        //     endProvinceId: arriProvinceId,
        //     // endCityId: arriCityId,
        //     // endAreaId: arriAreaId,
        // };
        // $.ajax({
        //     url: ctx + "tms/segment/judgeCarrierProtocolPriceExist",
        //     type: "post",
        //     dataType: "json",
        //     data: data,
        //     success: function (result) {
        //         if(result.code == 0){
        //             // 计价方式必填
        //             $("#pricingMethod").attr("required",true);
        //             $("#pricingMethodSpan").css("color","red");
        //         }else{
        //             $("#pricingMethod").attr("required",false);
        //             $("#pricingMethodSpan").css("color","");
        //
        //             $("#form-dispatch-add").validate().element($("#pricingMethod"));
        //         }
        //     }
        // });
    }

    /**
     * 新增收款人账号
     */
    function addPayeeEvent(){
        let carrId = $("#carrierId").val();
        /*if(carrId == ''){
            $.modal.alertWarning("请先选择承运商");
            return;
        }*/
        let url = ctx + "g7/payee/add?dt=1";

        if(carrId != ''){
            url += "&collectionType=0&carrId="+carrId;
        }
        
        layer.open({
            type: 2,
            area: ['70%', '100%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "添加收款人",
            content: url,
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function(index, layero) {
                //var iframeWin = layero.find('iframe')[0];
                //iframeWin.contentWindow.submitHandler(index, layero);
                layero.find('iframe')[0].contentWindow.submitHandler_1(addCallBack,index);
            },
            cancel: function(index) {
                return true;
            }
        })

        /*$.modal.open("添加收款人", url,"800","600",function(pageNo,layerObj){
            layerObj.find('iframe')[0].contentWindow.submitHandler_1(addCallBack,pageNo);
        });*/
        //
    }

    function addCallBack(pageNo){
        //重新加载收款账号
        loadCarrBankData();
        setTimeout(function(){
            layer.close(pageNo);
        },500)

    }

    /*function showSumMoney(obj,idx){

                if($("#transFeeCount_0").val() != ''){
                    $("#one").show();
                    $("#oneInner").html($("#transFeeCount_0").val());
                }else{
                    $("#one").hide();
                }

                if($("#transFeeCount_1").val() != ''){
                    $("#two").show();
                    $("#twoInner").html($("#transFeeCount_1").val());
                }else{
                    $("#two").hide();
                }

                if($("#transFeeCount_2").val() != ''){
                    $("#three").show();
                    $("#threeInner").html($("#transFeeCount_2").val());
                }else{
                    $("#three").hide();
                }

                if($("#transFeeCount_3").val() != ''){
                    $("#four").show();
                    $("#fourInner").html($("#transFeeCount_3").val());
                }else{
                    $("#four").hide();
                }

                if($("#transFeeCount_4").val() != ''){
                    $("#five").show();
                    $("#fiveInner").html($("#transFeeCount_4").val());
                }else{
                    $("#five").hide();
                }

                if($("#transFeeCount_5").val() != ''){
                    $("#six").show();
                    $("#sixInner").html($("#transFeeCount_5").val());
                }else{
                    $("#six").hide();
                }

        //计算成本分摊总和
        calShareCostSum()
    }*/
    //计算成本分摊总和
    function calShareCostSum(){
        let isum = 0;
        $(".shareCb").each(function(i,obj){
            let ival = decimal(parseFloat($(obj).val()),2);
            if(ival){
                isum += ival;
            }
        });

        $("#calculateTotal1").text(decimal(isum,2));


    }

    // $(document).on('input', 'input[id^="A_"], input[id^="B_"], input[id^="C_"], input[id^="D_"], input[id^="E_"], input[id^="F_"]', function() {
    //     const inputId = $(this).attr('id').slice(2);
    //     calculateInvoiceNGP(inputId);
    // });

    $(document).on('input', 'input[id^="A_"], input[id^="B_"], input[id^="C_"], input[id^="D_"], input[id^="E_"]' +
        ', input[id^="F_"], input[class*="G"]', function() {
        const inputId = $(this).attr('id').slice($(this).attr('id').indexOf('_') + 1);
        calculateInvoiceNGP(inputId);
    });
    // 当其他 JavaScript 代码修改输入框值时,手动触发 input 事件
    function triggerInputEvent(inputSelector) {
        $(inputSelector).trigger('input');
    }


    function calculateInvoiceNGP(segmentId) {
        //现金
        let A = parseFloat($(`#A_${segmentId}`).val()) || 0;
        let C = parseFloat($(`#C_${segmentId}`).val()) || 0;
        let E = parseFloat($(`#E_${segmentId}`).val()) || 0;

        //油卡
        let B = parseFloat($(`#B_${segmentId}`).val()) || 0;
        let D = parseFloat($(`#D_${segmentId}`).val()) || 0;
        let F = parseFloat($(`#F_${segmentId}`).val()) || 0;

        //动态计算所有其他费用
        let otherSum = new Decimal(0);
        $("#otherFeeTableBody tr").each(function(index) {
            let gValue = parseFloat($(`#G${index}_${segmentId}`).val()) || 0;
            otherSum = otherSum.plus(gValue);
        });

        let calculateTotal = new Decimal(B).plus(D).plus(F)
        let cashSum = new Decimal(A).plus(C).plus(E)

        let msrp2 = calculateExclusionTax(calculateTotal, cashSum, otherSum);

        let _profit = $(`#${segmentId}_profit`).val();
        //计算毛利
        let pHtml = calculateNegativeGrossProfitHtml(msrp2, _profit);

        if (pHtml !== '') {
            $(`#negativeGrossProfit_${segmentId}`).html(pHtml);
            $(`#negativeGrossProfitDiv_${segmentId}`).show();

        }else {
            $(`#negativeGrossProfitDiv_${segmentId}`).hide();
        }

    }

    //二维码弹框
    function scanCode(entrustLotId,driverName,driverMobile,carno,carrierName,carrierPhone,carrName,driverCardId,carrCardId) {
       
       layer.open({
           type: 1,
           area: ['40%', '500px'],
           fix: false,
           maxmin: true,
           shade: 0.3,
           title: "畅运通运单详情二维码",
           content: $("#scanCodeHtml").html(),
           btn: ['关闭'],
           shadeClose: true,
           success: function (layero, index) {
               $("#scanCodeImg").attr("src",ctx + "trace/generateQrCode?entrustLotId="+entrustLotId)
               let invoiceVbillno=$("#invoiceVbillno_0").val()
               if(invoiceVbillno&&invoiceVbillno!='null'){
                   $("#invoiceVbillnoInfo").text(invoiceVbillno)
                   $("#invoiceVbillnoInfo").parent().css('display', 'block')
               }else{
                   $("#invoiceVbillnoInfo").parent().css('display', 'none')
               }
               if(driverCardId==carrCardId){
                    $("#carrInfo").parent().css('display', 'none');
                }else{
                    $("#carrInfo").text(carrName);
                }
               if((driverName||driverMobile||carno)&&(driverName!='null'||driverMobile!='null'||carno!='null')){
                   let driverInfo=[]
                   if(driverName&&driverName!='null'){
                       driverInfo.push(driverName)
                   }
                   if(driverMobile&&driverMobile!='null'){
                       driverInfo.push(driverMobile)
                   }
                   if(carno&&carno!='null'){
                       driverInfo.push(carno)
                   }

                   if(driverInfo.length!=0){
                       $("#driverInfo span").text(driverInfo.join('/'))
                       $("#driverInfo").css('display', 'block')
                   }else{
                       $("#driverInfo").css('display', 'none')
                   }

                   $("#carrierInfoT").css('display', 'none')
               }else{
                   let carrierList=[]
                   if(carrierName&&carrierName!='null'){
                       carrierList.push(carrierName)
                   }
                   if(carrierPhone&&carrierPhone!='null'){
                       carrierList.push(carrierPhone)
                   }
                   if(carrierList.length!=0){
                       $("#carrierInfoT").text(carrierList.join('/'))
                       $("#carrierInfoT").css('display', 'block')
                   }else{
                       $("#carrierInfoT").css('display', 'none')
                   }
                  
                   $("#driverInfo").css('display', 'none')
                  
               }      
           },
           yes: function (index, layero){
               $.operate.successTabCallback({code:0,msg:'关闭成功'});
           },
           cancel: function(index, layero){ 
               $.operate.successTabCallback({code:0,msg:'关闭成功'});
           }
       })
   }

    function refresh() {
        var topWindow = $(window.parent.document);
        var currentId = $('.page-tabs-content', topWindow).find('.active').attr('data-panel');
        var $Element = $('.RuoYi_iframe[data-id="' + currentId + '"]', topWindow)[0];
        if ($Element !== undefined) {
            var $contentWindow = $Element.contentWindow;
            $contentWindow.$(".layui-layer-padding").removeAttr("style");
            if ($contentWindow.$.table._option.type == table_type.bootstrapTable) {
                $contentWindow.$.table.refresh();
            } else if ($contentWindow.$.table._option.type == table_type.bootstrapTreeTable) {
                $contentWindow.$.treeTable.refresh();
            }
        }
        $.modal.closeTab();
    }

</script>
<script>
    /**
     * 检查成本分摊金额
     */
    function checkCost(){
        let checkMsg = {"A_":"预付现金成本分摊不正确"
            ,"B_":"预付油卡成本分摊不正确"
            ,"C_":"到付现金成本分摊不正确"
            ,"D_":"到付油卡成本分摊不正确"
            ,"E_":"回付现金成本分摊不正确"
            ,"F_":"回付油卡成本分摊不正确"
        };
        let idx = 0;
        let transFeeCount = 0;
        let sum = 0;

        // 处理现金和油卡费用（A_到F_）
        for(let i = 0; i < 6; i++){
            let key = Object.keys(checkMsg)[i];
            let psum = parseFloat($("#transFeeCount_"+idx).val());
            if(!isNaN(psum)){
                psum = decimal(psum,2)
                transFeeCount += psum;
                transFeeCount = decimal(transFeeCount,2);

                let inputValue = 0
                for(let seg of dispatchSegmentVO){
                    let val = parseFloat($("#" + key + seg.segmentId).val()) || 0;
                    inputValue += decimal(val,2)
                }

                sum = (parseFloat(sum) + inputValue).toFixed(2)

                inputValue = decimal(inputValue,2)
                if (inputValue != psum) {
                    $.modal.alertWarning(checkMsg[key])
                    return false;
                }
            }
            idx++;
        }

        // 动态处理其他费用
        let hasError = false;
        $("#otherFeeTableBody tr").each(function(index) {
            let costTypeSelect = $(this).find("select[name*='costType']");
            let costTypeText = costTypeSelect.find("option:selected").text();
            let key = "G" + index + "_";
            let psum = parseFloat($("#onWayFee_"+index).val());

            if(!isNaN(psum) && psum > 0){
                psum = decimal(psum,2)
                transFeeCount += psum;
                transFeeCount = decimal(transFeeCount,2);

                let inputValue = 0
                for(let seg of dispatchSegmentVO){
                    let val = parseFloat($("#" + key + seg.segmentId).val()) || 0;
                    inputValue += decimal(val,2)
                }

                sum = (parseFloat(sum) + inputValue).toFixed(2)

                inputValue = decimal(inputValue,2)
                if (inputValue != psum) {
                    $.modal.alertWarning(costTypeText + "成本分摊不正确")
                    hasError = true;
                    return false; // 跳出each循环
                }
            }
        });

        // 如果在其他费用检查中发现错误，直接返回false
        if (hasError) {
            return false;
        }

        sum = decimal(sum,2)
        transFeeCount = decimal(transFeeCount,2)
        if(sum != transFeeCount){
            $.modal.alertWarning("总金额与分摊金额不相等")
            return false;
        }else{
            return true;
        }
    }

    function calculateRate(){
        let sum_0 = 0,sum_1 = 0,sum_2 = 0,sum_3 = 0,sum_4 = 0,sum_5 = 0;
        let len = 0;
        for(let idx in rateMap){
            len++;
        }
        if(len == 0)return;

        let i = 0;
        for(let idx in rateMap){

            //预付现金
            let m_0 = parseFloat($("#transFeeCount_0").val());
            if(m_0){
                $(".AAAAA").removeAttr("disabled");
                if(i< len-1){
                    let val = Math.round(m_0 * rateMap[idx]*100)/100;
                    sum_0 += val;
                    $("#A_"+idx).val(val.toFixed(2));
                }else{
                    let val = m_0 - sum_0;
                    $("#A_"+idx).val(val.toFixed(2));
                }
            }else{
                $(".AAAAA").attr("disabled","disabled").val("");
            }
            //预付油卡
            let m_1 = parseFloat($("#transFeeCount_1").val());
            if(m_1){
                $(".BBBBB").removeAttr("disabled");
                if(i< len-1){
                    let val = Math.round(m_1 * rateMap[idx]*100)/100;
                    sum_1 += val;
                    $("#B_"+idx).val(val.toFixed(2));
                }else{
                    let val = m_1 - sum_1;
                    $("#B_"+idx).val(val.toFixed(2));
                }
            }else{
                $(".BBBBB").attr("disabled","disabled").val("");
            }

            //到付现金
            let m_2 = parseFloat($("#transFeeCount_2").val());
            if(m_2){
                $(".CCCCC").removeAttr("disabled");
                if(i< len-1){
                    let val = Math.round(m_2 * rateMap[idx]*100)/100;
                    sum_2 += val;
                    $("#C_"+idx).val(val.toFixed(2));
                }else{
                    let val = m_2 - sum_2;
                    $("#C_"+idx).val(val.toFixed(2));
                }
            }else{
                $(".CCCCC").attr("disabled","disabled").val("");
            }

            //到付油卡
            let m_3 = parseFloat($("#transFeeCount_3").val());
            if(m_3){
                $(".DDDDD").removeAttr("disabled");
                if(i< len-1){
                    let val = Math.round(m_3 * rateMap[idx]*100)/100;
                    sum_3 += val;
                    $("#D_"+idx).val(val);
                }else{
                    let val = m_3 - sum_3;
                    $("#D_"+idx).val(val.toFixed(2));
                }
            }else{
                $(".DDDDD").attr("disabled","disabled").val("");
            }

            //回付现金
            let m_4 = parseFloat($("#transFeeCount_4").val());
            
            if(m_4){
                $(".EEEEE").removeAttr("disabled");
                if(i< len-1){
                    let val = Math.round(m_4 * rateMap[idx]*100)/100;
                    sum_4 += val;
                    $("#E_"+idx).val(val);
                }else{
                    let val = m_4 - sum_4;
                    $("#E_"+idx).val(val.toFixed(2));
                }
            }else{
                $(".EEEEE").attr("disabled","disabled").val("");
            }

            //回付油卡
            let m_5 = parseFloat($("#transFeeCount_5").val());
            if(m_5){
                $(".FFFFF").removeAttr("disabled");
                if(i< len-1){
                    let val = Math.round(m_5 * rateMap[idx]*100)/100;
                    sum_5 += val;
                    $("#F_"+idx).val(val);
                }else{
                    let val = m_5 - sum_5;
                    $("#F_"+idx).val(val.toFixed(2));
                }
            }else{
                $(".FFFFF").attr("disabled","disabled").val("");
            }

            i++;
        }

        //
        calShareCostSum();
    }

    function calculateAllocation() {
        let allocationType = $("#allocationType").val();

        let segLength = dispatchSegmentVO.length;

        let sum_0 = 0,sum_1 = 0,sum_2 = 0,sum_3 = 0,sum_4 = 0,sum_5 = 0;
        let len = dispatchSegmentVO.length;
        if(len == 0)return;

        // 初始化其他费用累计金额对象
        var otherFeeSums = {};

        let i = 0;
        for(let idx of dispatchSegmentVO){
            // let dispatchSegment = dispatchSegmentVO[idx];

            let segmentId = idx.segmentId;

            let allCnt, cnt;

            let inputReadonly = true
            if (allocationType === '0') {
                //件数
                allCnt = parseFloat(numCount) || 0
                cnt = parseFloat($(`#${segmentId}_numCount`).val()) || 0
            }else if (allocationType === '1') {
                //重量
                allCnt = parseFloat(weightCount) || 0
                cnt = parseFloat($(`#${segmentId}_weightCount`).val()) || 0

            }else if (allocationType === '2') {
                //体积
                allCnt =  parseFloat(volumeCount) || 0
                cnt = parseFloat($(`#${segmentId}_volumeCount`).val()) || 0
            }else if (allocationType === '3') {
                allCnt = segLength
                cnt = 1
            } else {
                inputReadonly = false

                let m_0 = parseFloat($("#transFeeCount_0").val());
                if(m_0){
                    $(".AAAAA").removeAttr("disabled");
                }
                let m_1 = parseFloat($("#transFeeCount_1").val());
                if(m_1) {
                    $(".BBBBB").removeAttr("disabled");
                }
                let m_2 = parseFloat($("#transFeeCount_2").val());
                if(m_2) {
                    $(".CCCCC").removeAttr("disabled");
                }
                let m_3 = parseFloat($("#transFeeCount_3").val());
                if(m_3) {
                    $(".DDDDD").removeAttr("disabled");
                }
                let m_4 = parseFloat($("#transFeeCount_4").val());
                if(m_4) {
                    $(".EEEEE").removeAttr("disabled");
                }
                let m_5 = parseFloat($("#transFeeCount_5").val());
                if(m_5) {
                    $(".FFFFF").removeAttr("disabled");
                }

                // 动态处理其他费用
                $("#otherFeeTableBody tr").each(function(index) {
                    let g_value = parseFloat($("#onWayFee_"+index).val());
                    if (g_value) {
                        $(`.G${index}`).removeAttr("disabled");
                    }
                });
            }

            $(".AAAAA").prop("readonly", inputReadonly);
            $(".BBBBB").prop("readonly", inputReadonly);
            $(".CCCCC").prop("readonly", inputReadonly);
            $(".DDDDD").prop("readonly", inputReadonly);
            $(".EEEEE").prop("readonly", inputReadonly);
            $(".FFFFF").prop("readonly", inputReadonly);

            // 动态设置其他费用的readonly属性
            $("#otherFeeTableBody tr").each(function(index) {
                $(`.G${index}`).prop("readonly", inputReadonly);
            });

            if (!inputReadonly)return;

            let rate = allCnt === 0 ? 1 / segLength : cnt / allCnt

            //预付现金
            let m_0 = parseFloat($("#transFeeCount_0").val());
            if(m_0){
                $(".AAAAA").removeAttr("disabled");
                if(i< len-1){
                    let val = parseFloat(m_0 * rate).toFixed(2);
                    sum_0 =  Number(sum_0) +  Number(val);
                    sum_0 = sum_0.toFixed(2)
                    $("#A_"+segmentId).val(val);
                }else{
                    let val = m_0 - sum_0;
                    $("#A_"+segmentId).val(val.toFixed(2));
                }
            }else{
                $(".AAAAA").attr("disabled","disabled").val("");
            }
            //预付油卡
            let m_1 = parseFloat($("#transFeeCount_1").val());
            if(m_1){
                $(".BBBBB").removeAttr("disabled");
                if(i< len-1){
                    let val = parseFloat(m_1 * rate).toFixed(2);
                    sum_1 = Number(sum_1) + Number(val);
                    sum_1 = sum_1.toFixed(2)
                    $("#B_"+segmentId).val(val);
                }else{
                    let val = m_1 - sum_1;
                    $("#B_"+segmentId).val(val.toFixed(2));
                }
            }else{
                $(".BBBBB").attr("disabled","disabled").val("");
            }

            //到付现金
            let m_2 = parseFloat($("#transFeeCount_2").val());
            if(m_2){
                $(".CCCCC").removeAttr("disabled");
                if(i< len-1){
                    let val = parseFloat(m_2 * rate).toFixed(2);
                    sum_2 = Number(sum_2) + Number(val);
                    sum_2 = sum_2.toFixed(2);
                    $("#C_"+segmentId).val(val);
                }else{
                    let val = m_2 - sum_2;
                    $("#C_"+segmentId).val(val.toFixed(2));
                }
            }else{
                $(".CCCCC").attr("disabled","disabled").val("");
            }

            //到付油卡
            let m_3 = parseFloat($("#transFeeCount_3").val());
            if(m_3){
                $(".DDDDD").removeAttr("disabled");
                if(i< len-1){
                    let val = parseFloat(m_3 * rate).toFixed(2);
                    sum_3 = Number(sum_3) + Number(val);
                    sum_3 = sum_3.toFixed(2)
                    $("#D_"+segmentId).val(val);
                }else{
                    let val = m_3 - sum_3;
                    $("#D_"+segmentId).val(val.toFixed(2));
                }
            }else{
                $(".DDDDD").attr("disabled","disabled").val("");
            }

            //回付现金
            let m_4 = parseFloat($("#transFeeCount_4").val());
            if(m_4){
                $(".EEEEE").removeAttr("disabled");
                if(i< len-1){
                    let val = parseFloat(m_4 * rate).toFixed(2);
                    sum_4 = Number(sum_4) + Number(val);
                    sum_4 = sum_4.toFixed(2)
                    $("#E_"+segmentId).val(val);
                }else{
                    let val = m_4 - sum_4;
                    $("#E_"+segmentId).val(val.toFixed(2));
                }
            }else{
                $(".EEEEE").attr("disabled","disabled").val("");
            }

            //回付油卡
            let m_5 = parseFloat($("#transFeeCount_5").val());
            if(m_5){
                $(".FFFFF").removeAttr("disabled");
                if(i< len-1){
                    let val = parseFloat(m_5 * rate).toFixed(2);
                    sum_5 = Number(sum_5) + Number(val);
                    sum_5 = sum_5.toFixed(2)
                    $("#F_"+segmentId).val(val);
                }else{
                    let val = m_5 - sum_5;
                    $("#F_"+segmentId).val(val.toFixed(2));
                }
            }else{
                $(".FFFFF").attr("disabled","disabled").val("");
            }

            // 动态处理其他费用分摊
            $("#otherFeeTableBody tr").each(function(index) {
                let g_value = parseFloat($("#onWayFee_"+index).val());
                if(g_value){
                    $(`.G${index}`).removeAttr("disabled");

                    // 初始化累计金额
                    if (!otherFeeSums[`sum_g${index}`]) {
                        otherFeeSums[`sum_g${index}`] = 0;
                    }

                    if(i< len-1){
                        let val = parseFloat(g_value * rate).toFixed(2);
                        otherFeeSums[`sum_g${index}`] = Number(otherFeeSums[`sum_g${index}`]) + Number(val);
                        otherFeeSums[`sum_g${index}`] = Number(otherFeeSums[`sum_g${index}`]).toFixed(2);
                        $(`#G${index}_${segmentId}`).val(val);
                    }else{
                        let val = g_value - Number(otherFeeSums[`sum_g${index}`]);
                        $(`#G${index}_${segmentId}`).val(val.toFixed(2));
                    }
                }else{
                    $(`.G${index}`).attr("disabled","disabled").val("");
                }
            });

            triggerInputEvent("#A_"+segmentId)
            triggerInputEvent("#B_"+segmentId)
            triggerInputEvent("#C_"+segmentId)
            triggerInputEvent("#D_"+segmentId)
            triggerInputEvent("#E_"+segmentId)
            triggerInputEvent("#F_"+segmentId)

            // 动态触发其他费用的input事件
            $("#otherFeeTableBody tr").each(function(index) {
                triggerInputEvent(`#G${index}_${segmentId}`);
            });

            i++;
        }


    }

    function openCarDetailTab(url){
        $.modal.openTab( "车辆详细", url);
    }

    function editCar(url){
        $.modal.openTab( "车辆修改", url);
    }


    function openDriverDetailTab(url){
        $.modal.openTab( "司机详细", url);
    }

    function editDriver(url){
        $.modal.openTab( "司机修改", url);
    }

</script>

<script>
    var index = 0;
    var indexLot = 0;
    var indexCnt = 0;
    var otherFeeRowIndex = 1; // 其他费用行索引，从1开始（0已被初始行占用）

    /* 新增其他费用行 */
    function insertOtherFeeRow() {
        var tbody = $("#otherFeeTableBody");
        var newRowHtml = `
            <tr data-row-index="${otherFeeRowIndex}">
                <td></td>
                <td>
                    <select name="entrustCostList[${otherFeeRowIndex}].costType" id="onWayCostType_${otherFeeRowIndex}" class="form-control valid" onchange="onCostTypeChange(this, ${otherFeeRowIndex})">
                        <option value="">-- 请选择 --</option>
                        <option value="1">装卸费</option>
                        <option value="2">提货费</option>
                        <option value="22">送货费</option>
                        <option value="40">枕木费</option>
                        <option value="47">报关费</option>
                        <option value="46">包车直送</option>
                        <option value="51">双驾费</option>
                        <option value="50">保费</option>
                    </select>
                </td>
                <td>
                    <input name="entrustCostList[${otherFeeRowIndex}].cost" id="onWayFee_${otherFeeRowIndex}"
                           autocomplete="off" class="form-control valid" type="text"
                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotal()">
                </td>
                <td>
                    <input name="entrustCostList[${otherFeeRowIndex}].memo"
                           placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off">
                </td>
                <td>
                    <a href="javascript:void(0)" onclick="removeOtherFeeRow(this, ${otherFeeRowIndex})" title="删除行" style="color: red;">×</a>
                </td>
            </tr>
        `;
        tbody.append(newRowHtml);
        otherFeeRowIndex++;

        // 更新成本分摊区域
        updateCostShareContainer();
    }

    /* 删除其他费用行 */
    function removeOtherFeeRow(obj, rowIdx) {
        // 至少保留一行
        if ($("#otherFeeTableBody tr").length <= 1) {
            $.modal.alertWarning("至少需要保留一行其他费用");
            return;
        }

        $(obj).closest("tr").remove();

        // 重新编号所有行
        reindexOtherFeeRows();

        // 更新成本分摊区域
        updateCostShareContainer();

        // 重新计算总金额
        calculateTotal();
    }

    /* 重新编号其他费用行 */
    function reindexOtherFeeRows() {
        $("#otherFeeTableBody tr").each(function(index) {
            $(this).attr("data-row-index", index);

            // 更新name属性
            $(this).find("select[name*='entrustCostList']").attr("name", `entrustCostList[${index}].costType`);
            $(this).find("select[name*='entrustCostList']").attr("id", `onWayCostType_${index}`);
            $(this).find("select[name*='entrustCostList']").attr("onchange", `onCostTypeChange(this, ${index})`);

            $(this).find("input[name*='cost']").attr("name", `entrustCostList[${index}].cost`);
            $(this).find("input[name*='cost']").attr("id", `onWayFee_${index}`);

            $(this).find("input[name*='memo']").attr("name", `entrustCostList[${index}].memo`);

            $(this).find("a[onclick*='removeOtherFeeRow']").attr("onclick", `removeOtherFeeRow(this, ${index})`);
        });

        otherFeeRowIndex = $("#otherFeeTableBody tr").length;
    }

    // 三方列表相关函数
    var thirdPartyFeeRowIndex = 1; // 三方费用行索引，从1开始（0已被初始行占用）

    /* 新增三方费用行 */
    function insertThirdPartyFeeRow() {
        var tbody = $("#thirdPartyFeeTableBody");

        // 构建下拉框选项
        var optionsHtml = '<option value="">-- 请选择 --</option>';
        for (var i = 0; i < costTypeOnWay.length; i++) {
            optionsHtml += `<option value="${costTypeOnWay[i].dictValue}">${costTypeOnWay[i].dictLabel}</option>`;
        }

        var invoiceNoHtml = '<option value="">-- 请选择 --</option>'
        for (var i = 0; i < invoiceList.length; i++) {
            invoiceNoHtml += `<option value="${invoiceList[i].invoiceId}">${invoiceList[i].vbillno}</option>`;
        }

        var newRowHtml = `
            <tr data-row-index="${thirdPartyFeeRowIndex}">
                <td></td>
                <td>
                    <select name="otherFeeList[${thirdPartyFeeRowIndex}].feeType" id="thirdPartyFeeType_${thirdPartyFeeRowIndex}" class="form-control valid" onchange="clearThirdPartyFeeError(this)">
                        ${optionsHtml}
                    </select>
                </td>
                <td>
                    <input name="otherFeeList[${thirdPartyFeeRowIndex}].feeAmount" id="thirdPartyFeeAmount_${thirdPartyFeeRowIndex}"
                           autocomplete="off" class="form-control valid" type="text"
                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);validateThirdPartyFeeRow(this)">
                </td>
                <td>
                    <select name="otherFeeList[${thirdPartyFeeRowIndex}].lotId" id="thirdPartyLotId_${thirdPartyFeeRowIndex}" class="form-control valid" onchange="clearThirdPartyFeeError(this)">
                        ${invoiceNoHtml}
                    </select>
                </td>
                <td>
                    <a href="javascript:void(0)" onclick="removeThirdPartyFeeRow(this, ${thirdPartyFeeRowIndex})" title="删除行" style="color: red;">×</a>
                </td>
            </tr>
        `;
        tbody.append(newRowHtml);
        thirdPartyFeeRowIndex++;
    }

    /* 删除三方费用行 */
    function removeThirdPartyFeeRow(obj, rowIdx) {
        // 至少保留一行
        if ($("#thirdPartyFeeTableBody tr").length <= 1) {
            $.modal.alertWarning("至少需要保留一行三方费用");
            return;
        }

        $(obj).closest("tr").remove();

        // 重新编号所有行
        reindexThirdPartyFeeRows();
    }

    /* 重新编号三方费用行 */
    function reindexThirdPartyFeeRows() {
        $("#thirdPartyFeeTableBody tr").each(function(index) {
            $(this).attr("data-row-index", index);

            // 更新name属性
            $(this).find("select[name*='otherFeeList']").attr("name", `otherFeeList[${index}].feeType`);
            $(this).find("select[name*='otherFeeList']").attr("id", `thirdPartyFeeType_${index}`);

            $(this).find("input[name*='feeAmount']").attr("name", `otherFeeList[${index}].feeAmount`);
            $(this).find("input[name*='feeAmount']").attr("id", `thirdPartyFeeAmount_${index}`);

            $(this).find("select[name*='lotId']").attr("name", `otherFeeList[${index}].lotId`);
            $(this).find("select[name*='lotId']").attr("id", `thirdPartyLotId_${index}`);

            // $(this).find("input[name*='lotId']").attr("name", `otherFeeList[${index}].lotId`);
            // $(this).find("input[name*='lotId']").attr("id", `thirdPartyLotId_${index}`);

            $(this).find("a[onclick*='removeThirdPartyFeeRow']").attr("onclick", `removeThirdPartyFeeRow(this, ${index})`);
        });

        thirdPartyFeeRowIndex = $("#thirdPartyFeeTableBody tr").length;
    }

    /* 三方费用校验 */
    function validateThirdPartyFees() {
        var hasError = false;
        var errorMessage = "";

        $("#thirdPartyFeeTableBody tr").each(function(index) {
            var feeAmount = $(this).find("input[name*='feeAmount']").val();
            var feeType = $(this).find("select[name*='feeType']").val();
            var lotId = $(this).find("select[name*='lotId']").val();

            // 如果输入了金额，则费用类型和发货单号必填
            if (feeAmount && feeAmount.trim() !== "") {
                if (!feeType || feeType.trim() === "") {
                    errorMessage = "三方费用第" + (index + 1) + "行：输入金额后，费用类型必须选择";
                    hasError = true;
                    return false; // 跳出each循环
                }

                if (!lotId || lotId.trim() === "") {
                    errorMessage = "三方费用第" + (index + 1) + "行：输入金额后，发货单号必须填写";
                    hasError = true;
                    return false; // 跳出each循环
                }
            }
        });

        if (hasError) {
            $.modal.alertWarning(errorMessage);
            return false;
        }

        return true;
    }

    /* 三方费用单行实时校验 */
    function validateThirdPartyFeeRow(amountInput) {
        var $row = $(amountInput).closest("tr");
        var feeAmount = $(amountInput).val();
        var $feeTypeSelect = $row.find("select[name*='feeType']");
        var $lotIdSelect = $row.find("select[name*='lotId']");

        // 清除之前的错误样式
        $feeTypeSelect.removeClass("error");
        $lotIdSelect.removeClass("error");

        // 如果输入了金额，则添加必填样式提示
        if (feeAmount && feeAmount.trim() !== "") {
            var feeType = $feeTypeSelect.val();
            var lotId = $lotIdSelect.val();

            if (!feeType || feeType.trim() === "") {
                $feeTypeSelect.addClass("error");
            }

            if (!lotId || lotId.trim() === "") {
                $lotIdSelect.addClass("error");
            }
        }
    }

    /* 清除三方费用错误样式 */
    function clearThirdPartyFeeError(element) {
        $(element).removeClass("error");
    }

    /* 新增表格行 */
    function insertRow() {
        index++;
        indexCnt++;
        var rowTmpl = $("#rowTmpl").html();

        //rowTmpl = rowTmpl.replace("costType","costType"+index);
        rowTmpl = rowTmpl.replace("costType_","costType_"+index);
        rowTmpl = rowTmpl.replace("money","money"+index);
        rowTmpl = rowTmpl.replace("money_","money_"+index);
        rowTmpl = rowTmpl.replace("isInvoice","isInvoice"+index);
        rowTmpl = rowTmpl.replaceAll("XXXINDEXXXX",index);
        rowTmpl = rowTmpl.replace("onWayRows_ZZZINDEXZZZ","onWayRows_"+index);
        $("#infoTab tbody").append(rowTmpl);

    }

    /* 删除指定表格行 */
    function removeRow(obj,rowIdx) {
        let val = $("#costType_"+rowIdx).val();
        $(".rateMoney"+rowIdx).remove();
        $("#infoTab tbody").find(obj).closest("tr").remove();
        indexCnt--;
    }



    $(function () {
        // insertRow();
        $('[data-toggle="tooltip"]').tooltip();

        // 初始化成本分摊容器
        updateCostShareContainer();
    });



    function onMoneyChangeEvent(obj,rowIdx){

        let money = parseFloat($(obj).val());
        let len = 0;
        for(let myi in rateMap){
            len++;
        }

        if(money){
            let sumMoney = 0;
            let doi = 0;
            for(let idx in rateMap){
                let rate = rateMap[idx];
                
                if(doi < len -1){
                    let rateMoney = Math.round(money*rate*100)/100;
                    sumMoney += rateMoney;
                    $("#"+idx+rowIdx).val(rateMoney);
                }else{
                    $("#"+idx+rowIdx).val((money-sumMoney).toFixed(2));
                }
                doi++;
            }
        }

        checkShareCost();
    }

    /* 更新成本分摊容器 */
    function updateCostShareContainer() {
        // 检查dispatchSegmentVO是否存在
        if (typeof dispatchSegmentVO === 'undefined' || !dispatchSegmentVO) {
            console.log('dispatchSegmentVO未定义，跳过成本分摊容器更新');
            return;
        }

        // 为每个segment更新其对应的成本分摊容器
        dispatchSegmentVO.forEach(function(segment, segIndex) {
            var containerHtml = '';
            var otherFeeRows = $("#otherFeeTableBody tr");

            otherFeeRows.each(function(index) {
                var costTypeSelect = $(this).find("select[name*='costType']");
                var costTypeValue = costTypeSelect.val();
                var costTypeText = costTypeSelect.find("option:selected").text();

                if (costTypeValue && costTypeText !== '-- 请选择 --') {
                    var inputId = `G${index}_${segment.segmentId}`;
                    var inputName = `dispatchAllocationVOList[${segIndex}].costShareVOList[${index + 6}].costShare`;
                    var costTypeOnWayName = `dispatchAllocationVOList[${segIndex}].costShareVOList[${index + 6}].costTypeOnWay`;
                    var freeTypeName = `dispatchAllocationVOList[${segIndex}].costShareVOList[${index + 6}].freeType`;

                    containerHtml += `
                        <div class="dInput" style="width: 48%; margin-bottom: 10px;">
                            <input data-invoiceid="${segment.invoiceId}"
                                   id="${inputId}"
                                   name="${inputName}"
                                   oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                   type="text" class="G${index} form-control"
                                   autocomplete="off" style="width:100%"
                                   placeholder="${costTypeText}" disabled>
                            <input name="${costTypeOnWayName}" value="${costTypeValue}" type="hidden">
                            <input name="${freeTypeName}" value="1" type="hidden">
                        </div>
                    `;
                }
            });

            // 更新当前segment对应的成本分摊容器
            $(`#otherFeeCostShareContainer_${segment.segmentId}`).html(containerHtml);
        });
    }

    function onCostTypeChange(obj, rowIndex){
        var selectedOption = $(obj).find('option:selected');

        // 获取选中选项的值（value）
        var selectedValue = selectedOption.val();

        // 获取选中选项的文本（中文名）
        var selectedText = selectedValue == '' ? '' : selectedOption.text();

        // 更新成本分摊容器
        updateCostShareContainer();

        // 重新计算成本分摊
        calculateAllocation();
    }

    function checkShareCost(){
        let isOk = true;

        $(".regRows").each(function(idx,obj){
            let rowi = $(obj).attr("rowidx");

            //var row = {};

            //var costType = $(this).find("select[id^='costType']").val();

            //var cost = $(this).find("input[id^='money']").val();
            //var memo = $(this).find("input[id^='memo']").val();
            //row.sumCost = sumCost;
            //row.entrustLotId = entrustLotId;
            //row.entrustId = entrustDto.entrustId;
            //row.entrustNo = entrustDto.vbillno;
            //row.lot = entrustDto.lot;
            //row.budgetType = 1;
            //row.costType = costType;

            let shareCost = {};
            for(let i in rateMap){
                let eid = i;
                
                let m = parseFloat($("#"+eid+rowi).val());
                let invoice = $("#"+eid).data("invoiceid");
                if(m == NaN) {m = 0}

                shareCost[invoice] = m.toFixed("2");
            }
            $(this).find(".shareCost").val(JSON.stringify(shareCost));

            //row.shareCost = JSON.stringify(shareCost);
            //row.cost = cost;
            //row.memo = memo;
            //row.entrustIds = entrustIds;
            //row.existCostOnWay = 1;

        });
    }
    function depositWayChange(sel) {
        if ($(sel).val() == 'yfzdj') {
            $('#depositAmount').val('0');
        } else {
            //$('#depositAmount').val($("#sysDepositAmount").val());
        }
    }
    
    // 清除车牌号输入框
    function clearCarInput() {
        $('#carno').val('');
        $('#carnoId').val('');
        $('#carLenName').val('');
        $('#carLenId').val('');
        $('#carTypeName').val('');
        $('#carTypeCode').val('');

        clearDriverInput()
    }
    
    // 清除司机输入框
    function clearDriverInput() {
        $('#driverName').val('');
        $('#driverId').val('');
        $('#cardId').val('');
        $('#driverMobile').val('');

        $("#driverIsDocFull").hide().off('click');
    }
</script>
</body>

</html>
