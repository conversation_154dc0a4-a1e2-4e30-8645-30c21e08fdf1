<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('拆段')"/>
    <style>
        .address {
            padding: 7px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            -webkit-box-shadow: 0 1px 1px rgba(0,0,0,.05);
            box-shadow: 0 1px 1px rgba(0,0,0,.05);
        }
        .nwv {
            width: 56px;
            border: 1px #ddd solid;
            text-align: center;
        }
        .ellipsis {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link th:href="@{/element-ui@2.15.13/lib/theme-chalk/index.css}" rel="stylesheet"/>
    <script th:src="@{/js/vue.min.js}"></script>
    <script th:src="@{/element-ui@2.15.13/lib/index.js}"></script>
</head>

<body>
<div class="form-content">
    <form id="form-subsection-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="segmentIds" th:value="${segmentIds}">
<!--            <div class="panel panel-default">-->
<!--                <div class="panel-heading">-->
<!--                    <h4 class="panel-title">-->
<!--                        <a data-toggle="collapse" data-parent="#accordion"-->
<!--                           href="tabs_panels.html#collapseOne">中转地址</a>-->
<!--                    </h4>-->
<!--                </div>-->
<!--                <div id="collapseOne" class="panel-collapse collapse in">-->
<!--                    <div class="panel-body">-->
<!--                        &lt;!&ndash; begin&ndash;&gt;-->
<!--                        <div class="fixed-table-body" style="margin: 0px -5px;">-->
<!--                            <table border="0" id="infoTab" class="custom-tab table">-->
<!--                                <thead>-->
<!--                                <tr>-->
<!--                                    <th style="width: 3%;"><a class="collapse-link add-alink" onclick="insertRow()" title="新增行">+</a></th>-->
<!--                                    <th style="width: 15%;">地址名称</th>-->
<!--                                    <th style="width: 15%;">省份</th>-->
<!--                                    <th style="width: 15%;">城市</th>-->
<!--                                    <th style="width: 15%;">区域</th>-->
<!--                                    <th style="width: 25%;">详细地址</th>-->
<!--                                    <th style="width: 15%;">要求到货日期</th>-->
<!--                                </tr>-->
<!--                                </thead>-->
<!--                                <tbody>-->
<!--                                <tr class="distpicker">-->
<!--                                    <td><a class="close-link del-alink" onclick="removeRow(this,0)" title="删除选择行">-</a></td>-->
<!--                                    <td>-->
<!--                                        <div class="input-group">-->
<!--                                            <input id="addrName_0" name="addressList[0].addrName" onclick="selectDelivery(0)" type="text"-->
<!--                                               placeholder="请选择提货方" class="form-control valid" autocomplete="off" required="" readonly>-->
<!--                                            <input id="addressId_0" name="addressList[0].addressId" type="hidden">-->
<!--                                            <input id="addrCode_0" name="addressList[0].addrCode" type="hidden">-->
<!--                                            <input id="contact_0" name="addressList[0].contact" type="hidden">-->
<!--                                            <input id="mobile_0" name="addressList[0].mobile" type="hidden">-->
<!--                                            <span class="input-group-addon"><i class="fa fa-search"></i></span>-->
<!--                                        </div>-->
<!--                                    </td>-->
<!--                                    <td>-->
<!--                                        <input id="provinceName_0" name="addressList[0].provinceName" class="form-control" type="text" disabled>-->
<!--                                        <input id="provinceId_0" name="addressList[0].provinceId" type="hidden">-->
<!--                                    </td>-->
<!--                                    <td>-->
<!--                                        <input id="cityName_0" name="addressList[0].cityName" class="form-control" type="text" disabled>-->
<!--                                        <input id="cityId_0" name="addressList[0].cityId" type="hidden">-->
<!--                                    </td>-->
<!--                                    <td>-->
<!--                                        <input id="areaName_0" name="addressList[0].areaName" class="form-control" type="text" disabled>-->
<!--                                        <input id="areaId_0" name="addressList[0].areaId" type="hidden">-->
<!--                                    </td>-->
<!--                                    <td>-->
<!--                                        <input id="detailAddr_0" name="addressList[0].detailAddr" placeholder="" class="form-control" type="text" disabled>-->
<!--                                    </td>-->
<!--                                    <td>-->
<!--                                        <div class="input-group">-->
<!--                                            <input type="text" class=" form-control" id="reqArriDate_0" name="addressList[0].reqArriDate"-->
<!--                                                   autocomplete="off" required readonly>-->
<!--                                        </div>-->
<!--                                    </td>-->
<!--                                </tr>-->
<!--                                </tbody>-->
<!--                            </table>-->
<!--                        </div>-->
<!--                        &lt;!&ndash;订单货品费用明细 end&ndash;&gt;-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">所选运段</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab1" class="custom-tab table">
                            <thead>
                            <tr clas="distpicker">
                                <th style="width: 11%;">发货单号</th>
                                <th style="width: 11%;">运段号</th>
                                <th style="width: 11%;">起始地</th>
                                <th style="width: 11%;">起始地城市</th>
                                <th style="width: 11%;">目的地</th>
                                <th style="width: 11%;">目的地城市</th>
                                <th style="width: 11%;">要求提货日期</th>
                                <th style="width: 11%;">要求到货日期</th>
                            </tr>
                            </thead>
                            <tbody>
                           <tr th:each="segment,segmentStat : ${segmentList}">
                                <td><label style="float:left;" th:text="${segment.invoiceVbillno}"></label></td>
                                <td><label style="float:left;" th:text="${segment.vbillno}"></label></td>
                                <td><label style="float:left;" th:text="${segment.deliAddrName}"></label></td>
                                <td><label style="float:left;" th:text="${segment.deliProName} + ${segment.deliCityName} + ${segment.deliAreaName}"></label></td>
                                <td><label style="float:left;" th:text="${segment.arriAddrName}"></label></td>
                                <td><label style="float:left;" th:text="${segment.arriProName} + ${segment.arriCityName} + ${segment.arriAreaName}">天津市</label></td>
                                <td><label style="float:left;" th:text="${#dates.format(segment.reqDeliDate,'yyyy-MM-dd HH:mm:ss')}"></label></td>
                                <td><label style="float:left;" th:text="${#dates.format(segment.reqArriDate,'yyyy-MM-dd HH:mm:ss')}"></label></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--end-->

                    <div id="app">

                        <div style="margin: 5px 0;border: 1px #ddd solid;border-radius: 5px;">
                            <div style="background-color: #f3f3f3;padding:5px;display: flex;justify-content: space-between;">
                                <span style="font-weight: bold">原始参考段</span>
                                <span>※运输顺序：从左向右，由上到下</span>
                            </div>
                            <div style="display:flex;justify-content: space-around;padding:5px 0;">
                                <div style="display: flex;flex-direction: column;justify-content: space-around;">
                                    <template v-for="(item,i) in origin.from">
                                        <div class="address" style="margin: 5px">
                                            <div>
                                                <span style="font-weight: bold">{{item.addrName}}</span>
                                                <span style="color: #999">[提货点]</span>
                                                <div class="ellipsis" style="width: 220px" :title="item.provinceName+item.cityName+item.areaName+item.detailAddr">{{item.provinceName}}{{item.cityName}}{{item.areaName}}{{item.detailAddr}}</div>
                                            </div>
                                            <div v-for="(goods, j) in item.goodsList" style="color:#3b8ab8">
                                                {{goodsDesc(goods)}}
                                                <span v-if="goods.yf.n>0">{{goods.yf.n}}件</span>
                                                <span v-if="goods.yf.w>0">{{goods.yf.w}}吨</span>
                                                <span v-if="goods.yf.v>0">{{goods.yf.v}}m³</span>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                                <div style="display: flex;flex-direction: column;justify-content: space-around;">
                                    <template v-for="(item,i) in origin.to">
                                        <div class="address" style="margin: 5px">
                                            <div>
                                                <span style="font-weight: bold">{{item.addrName}}</span>
                                                <span style="color: #999">[到货点]</span>
                                                <div class="ellipsis" style="width: 220px" :title="item.provinceName+item.cityName+item.areaName+item.detailAddr">{{item.provinceName}}{{item.cityName}}{{item.areaName}}{{item.detailAddr}}</div>
                                            </div>
                                            <!--发货点作为收货点时，不显示本身的发货货品-->
                                            <div v-for="(goods, j) in item.goodsList" style="color:#3b8ab8">
                                                <span style="color:#0e9aef" title="应到货标记">[收]</span>
                                                <span>{{goodsDesc(goods)}}</span>
                                                <span v-if="goods.ys.n>0">{{goods.ys.n}}件</span>
                                                <span v-if="goods.ys.w>0">{{goods.ys.w}}吨</span>
                                                <span v-if="goods.ys.v>0">{{goods.ys.v}}m³</span>
                                                <span v-if="goods.ys.n == 0 && goods.ys.w == 0 && goods.ys.w == 0">未发货</span>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>

                        <div v-if="pass">
                            <div v-for="(line,idx) in lines" style="margin: 5px 0;border: 1px #ddd solid;border-radius: 5px;">
                                <div style="background-color: #f3f3f3;padding:5px;display: flex;justify-content: space-between;">
                                    <span style="font-weight: bold">第{{idx+1}}段</span>
                                    <a v-if="idx > 0" href="javascript:;" style="color:red;" @click="delLine(idx)"> 删除</a>
                                </div>
                                <div style="display:flex;justify-content: space-around;padding:5px 0;">
                                    <div style="display: flex;flex-direction: column;justify-content: space-around;">
                                        <template v-for="(item,i) in line.from">
                                            <div class="address" style="margin: 5px;max-width: 236px">
                                                <div>
                                                    <span style="font-weight: bold">{{item.addrName}}</span>
                                                    <span style="color: #999">[{{item.addressType==0?'提货点':'到货点'}}]</span>
                                                    <i class="fa fa-times-circle" style="color: red" title="移除" @click="rmvPoint(idx, line.from, i)"></i>
                                                    <div v-if="debug" style="width: 200px">yf:{{item.yf}}<br>wf:{{item.wf}}<br>ys:{{item.ys}}<br>ws:{{item.ws}}</div>
                                                    <div class="ellipsis" style="width: 220px" :title="item.provinceName+item.cityName+item.areaName+item.detailAddr">{{item.provinceName}}{{item.cityName}}{{item.areaName}}{{item.detailAddr}}</div>
                                                </div>
                                                <div v-for="(goods, j) in item.goodsList.concat(item.hold||[])" style="color:#3b8ab8">
                                                    {{goodsDesc(goods)}}
                                                    <span v-if="goods.yf.n>0">{{goods.yf.n}}件</span>
                                                    <span v-if="goods.yf.w>0">{{goods.yf.w}}吨</span>
                                                    <span v-if="goods.yf.v>0">{{goods.yf.v}}m³</span>
                                                    <div v-if="debug" style="width: 200px">{{goods}}</div>
                                                </div>
                                            </div>
                                        </template>
                                        <div style="text-align: center">
                                            <el-date-picker v-model="line.reqDeliDate" size="mini" type="datetime" style="width: 166px;"
                                                            format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" placeholder="要求提货日期"></el-date-picker>
                                            <a href="javascript:;" @click="addP(idx, 'from')">增加装货地</a>
                                        </div>
                                    </div>

                                    <div style="display: flex;flex-direction: column;justify-content: space-around;">
                                        <div style="flex: 1;display: flex;justify-content: space-around;flex-wrap: wrap">
                                            <template v-for="(station,j) in line.stations">
                                                <div style="display: flex;flex-direction: column;justify-content: space-around;">
                                                    <div class="address">
                                                        <span v-text="station.addrName" style="font-weight: bold"></span>
                                                        <i class="fa fa-times-circle" style="color: red" title="取消中转" @click="stationRmv(idx,j)"></i>
                                                        <i v-if="j > 0" class="fa fa-arrow-left" style="color: dodgerblue" title="调整中转顺序" @click="stationLeft(idx,j)"></i>
                                                        <i v-if="j < line.stations.length - 1" class="fa fa-arrow-right" style="color: dodgerblue" title="调整中转顺序" @click="stationRight(idx,j)"></i>
                                                        <el-date-picker v-model="station.reqArriDate" size="mini" type="datetime" style="width: 163px;display: block"
                                                                        format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" placeholder="要求到货日期"></el-date-picker>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                        <div style="padding-left:5px">
                                            <span style="display:inline-block;padding:5px;border:1px #ddd solid;color:#49A7FF;border-radius: 4px;">
                                                <a href="javascript:;" @click="addStation(idx)" class="fa fa-plus-square"> 中转站</a>
                                            </span>
                                        </div>

                                    </div>

                                    <div style="display: flex;flex-direction: column;justify-content: space-around;">
                                        <template v-for="(item,i) in line.to">
                                            <div class="address" style="margin: 5px;max-width: 236px">
                                                <div>
                                                    <span style="font-weight: bold">{{item.addrName}}</span>
                                                    <span style="color: #999">[{{item.addressType==0?'提货点':'到货点'}}]</span>
                                                    <i class="fa fa-times-circle" style="color: red" title="移除" @click="rmvPoint(idx, line.to, i)"></i>
                                                    <div v-if="debug" style="width: 200px">yf:{{item.yf}}<br>wf:{{item.wf}}<br>ys:{{item.ys}}<br>ws:{{item.ws}}</div>
                                                    <div class="ellipsis" style="width: 220px" :title="item.provinceName+item.cityName+item.areaName+item.detailAddr">{{item.provinceName}}{{item.cityName}}{{item.areaName}}{{item.detailAddr}}</div>
                                                </div>
                                                <!--发货点作为收货点时，不显示本身的发货货品-->
                                                <div v-for="(goods, j) in item.goodsList.concat(item.hold||[])" style="color:#3b8ab8">
                                                    <template v-if="j < item.goodsList.length">
                                                        <span v-if="goods.ys.n == 0 && goods.ys.w == 0 && goods.ys.w == 0"></span>
                                                    </template>
                                                    <span v-if="j < item.goodsList.length" style="color:#0e9aef" title="应到货标记">[收]</span>
                                                    <span v-if="j >= item.goodsList.length" style="color:#00B83F" title="需要转运标记">[转]</span>
                                                    <span>{{goodsDesc(goods)}}</span>
                                                    <span v-if="goods.ys.n>0">{{goods.ys.n}}件</span>
                                                    <span v-if="goods.ys.w>0">{{goods.ys.w}}吨</span>
                                                    <span v-if="goods.ys.v>0">{{goods.ys.v}}m³</span>
                                                    <span v-if="goods.ys.n == 0 && goods.ys.w == 0 && goods.ys.w == 0">未发货</span>
                                                    <div v-if="debug" style="width: 200px">{{goods}}</div>
                                                </div>
                                            </div>
                                        </template>
                                        <div style="padding-left:5px">
                                            <el-date-picker v-model="line.reqArriDate" size="mini" type="datetime" style="width: 166px;"
                                                            format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" placeholder="要求到货日期"></el-date-picker>
                                            <a href="javascript:;" @click="addP(idx, 'to')">增加卸货地</a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <span style="display:inline-block;padding:5px 10px;border:1px #ddd solid;color:#49A7FF;border-radius: 4px;margin-bottom: 5px;">
                                <a href="javascript:;" @click="addLine()" class="fa fa-plus-square"> 追加段</a>
                            </span>

                            <el-button v-if="debug" @click="checkLineFromToNwv" size="mini">校验</el-button>
                            <el-button v-if="debug" size="mini" @click="debugYfys">应该+实际</el-button>
                            <div v-if="debug">{{goodsLine}}</div>
                            <el-button v-if="debug" size="mini" @click="rollback = !rollback">回滚:{{rollback}}</el-button>
                        </div>
                        <div v-else>
                            当前多装多卸提到货货品数量不一致
                        </div>

                        <div id="pointList" style="display: none;padding: 10px;">
                            <span v-if="chooseAble.length == 0">无可选地点</span>
                            <template v-else>
                                <div v-if="chooseAble.filter(x => x.addressType == 0).length > 0"><!--提货点列表-->
                                    <span style="color:#999">提货点</span>
                                    <div v-for="(addr,x) in chooseAble.filter(x => x.addressType == 0)" style="border: 1px #ddd solid;margin-bottom: 5px;border-radius: 4px;padding:5px" @click="$set(addr, 'choosed', !addr.choosed)">
                                        <label @click.stop style="margin-bottom: 0"><el-checkbox style="margin-bottom: 0" v-model="addr.choosed"></el-checkbox>
                                            <span style="font-weight: bold">{{addr.addrName}}</span>
                                        </label>
                                        <div style="padding-left: 18px">{{addr.provinceName}}{{addr.cityName}}{{addr.areaName}}{{addr.detailAddr}}</div>
                                        <div style="color: rgb(59, 138, 184);padding-left: 18px;">
                                            <span v-for="(g,y) in addr.shippingGoodsList">{{y>0?'、':''}}{{goodsDesc(g)}}({{[].concat(g.num>0?[g.num+'件']:[])
                                                    .concat(g.weight>0?[g.weight+'吨']:[]).concat(g.volume>0?[g.volume+'m³']:[]).join('|')}})</span>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="chooseAble.filter(x => x.addressType == 1).length > 0"><!--到货点列表-->
                                    <span style="color:#999">到货点</span>
                                    <div v-for="(addr,x) in chooseAble.filter(x => x.addressType == 1)" style="border: 1px #ddd solid;margin-bottom: 5px;border-radius: 4px;padding:5px" @click="$set(addr, 'choosed', !addr.choosed)">
                                        <label @click.stop style="margin-bottom: 0"><el-checkbox style="margin-bottom: 0" v-model="addr.choosed"></el-checkbox>
                                            <span style="font-weight: bold">{{addr.addrName}}</span>
                                        </label>
                                        <div style="padding-left: 18px">{{addr.provinceName}}{{addr.cityName}}{{addr.areaName}}{{addr.detailAddr}}</div>
                                        <div style="color: rgb(59, 138, 184);padding-left: 18px">
                                            <span v-for="(g,y) in addr.shippingGoodsList">{{y>0?'、':''}}{{goodsDesc(g)}}({{[].concat(g.num>0?[g.num+'件']:[])
                                                    .concat(g.weight>0?[g.weight+'吨']:[]).concat(g.volume>0?[g.volume+'m³']:[]).join('|')}})</span>
                                        </div>
                                    </div>
                                </div>
                            </template>

                        </div>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler(1)"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler(2)"><i class="fa fa-check"></i>保存并第一段调度</button>&nbsp;

        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">
    //是否是车队
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/segment" : ctx + "tms/segment";

    var befDate = [[${befDate}]];
    var aftDate = [[${aftDate}]];
    var index = 0;
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        laydate.render({
            elem: '#reqArriDate_0', //指定元素
            // format: 'yyyy-MM-dd',
            isInitValue: false,
            trigger: 'click',
            min: befDate,
            max: aftDate,
            type: 'datetime',
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours=now.getHours();
                this.dateTime.minutes=now.getMinutes();
                this.dateTime.seconds=now.getSeconds();
            },
            done: function (value, date, endDate) {
                $("#reqArriDate_0").val(value);
                $("#form-subsection-add").validate().element($("#reqArriDate_0"));
            }
        });
    });

    var segmentList = [[${segmentList}]];

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
    });

    /* 新增表格行 */
    function insertRow() {
        index++;
        var trTtml =
                '<tr class="distpicker">'+
                    '<td><a class="close-link del-alink" onclick="removeRow(this,'+index+')" title="删除选择行">-</a></td>'+
                    '<td>' +
                        '<div class="input-group">' +
                            '<input id="addrName_'+index+'" name="addressList['+index+'].addrName" onclick="selectDelivery('+index+')" type="text" placeholder="请选择提货方" class="form-control valid" autocomplete="off" required="" readonly>' +
                            '<input id="addressId_'+index+'" name="addressList['+index+'].addressId" type="hidden">' +
                            '<input id="addrCode_'+index+'" name="addressList['+index+'].addrCode" type="hidden">' +
                            '<input id="contact_'+index+'" name="addressList['+index+'].contact" type="hidden">' +
                            '<input id="mobile_'+index+'" name="addressList['+index+'].mobile" type="hidden">' +
                            '<span class="input-group-addon"><i class="fa fa-search"></i></span>' +
                        '</div>' +
                    '</td>'+
                    '<td>' +
                        '<input id="provinceName_'+index+'" name="addressList['+index+'].provinceName" class="form-control" type="text" disabled>' +
                        '<input id="provinceId_'+index+'" name="addressList['+index+'].provinceId" type="hidden">' +
                    '</td>'+
                    '<td>' +
                        '<input id="cityName_'+index+'" name="addressList['+index+'].cityName" class="form-control" type="text" disabled>' +
                        '<input id="cityId_'+index+'" name="addressList['+index+'].cityId" type="hidden">' +
                    '</td>'+
                    '<td>' +
                        '<input id="areaName_'+index+'" name="addressList['+index+'].areaName" class="form-control" type="text" disabled>' +
                        '<input id="areaId_'+index+'" name="addressList['+index+'].areaId" type="hidden">' +
                    '</td>'+
                    '<td>' +
                        '<input id="detailAddr_'+index+'" name="addressList['+index+'].detailAddr" placeholder="" class="form-control" type="text" disabled>' +
                    '</td>'+
                    '<td>' +
                        '<div class="input-group">' +
                            ' <input type="text" class="form-control" id="reqArriDate_'+index+'" name="addressList['+index+'].reqArriDate" required readonly>' +
                        '</div>' +
                    '</td>'+
                '</tr>';

        $("#infoTab tbody").append(trTtml);

        layui.use('laydate', function(){
            var id = "#reqArriDate_" + index;
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqArriDate_' + index, //指定元素
                // format: 'yyyy-MM-dd',
                isInitValue: false,
                trigger: 'click',
                min: befDate,
                max: aftDate,
                type: 'datetime',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours=now.getHours();
                    this.dateTime.minutes=now.getMinutes();
                    this.dateTime.seconds=now.getSeconds();
                },
                done: function (value, date, endDate) {
                    $(id).val(value);
                    $("#form-subsection-add").validate().element($(id));
                }
            });
        });


    }

    /* 删除指定表格行 */
    function removeRow(obj,addIndex) {
        //如果条数大于1，则删除
        if ($("#infoTab tbody").find('tr').length > 1) {
            $("#infoTab tbody").find(obj).closest("tr").remove();
        } else {
            //清空数据
            //收货方名称
            $("#addrName_" + addIndex).val("");
            //收货地址id
            $("#addressId_" + addIndex).val("");
            //收货省id
            $("#provinceId_" + addIndex).val("");
            //收货市id
            $("#cityId_" + addIndex).val("");
            //收货区id
            $("#areaId_" + addIndex).val("");

            //收货省名称
            $("#provinceName_" + addIndex).val("");
            //收货市名称
            $("#cityName_" + addIndex).val("");
            //收货区名称
            $("#areaName_" + addIndex).val("");

            //联系人
            $("#contact_" + addIndex).val("");
            //联系人手机
            $("#mobile_" + addIndex).val("");
            //收货详细地址
            $("#detailAddr_" + addIndex).val("");
            //收货地址编码
            $("#addrCode_" + addIndex).val("");
        }

    }

    /**
     * 选择地址
     * @param index 标识符
     */
    function selectDelivery(addIndex) {
        $.modal.open("地址信息", ctx + "basic/address/selectAddress?addrType=3","","",function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            //收货方名称
            $("#addrName_" + addIndex).val(rows[0]["addrName"]);
            //收货地址id
            $("#addressId_" + addIndex).val(rows[0]["addressId"]);
            //收货省id
            $("#provinceId_" + addIndex).val(rows[0]["provinceId"]);
            //收货市id
            $("#cityId_" + addIndex).val(rows[0]["cityId"]);
            //收货区id
            $("#areaId_" + addIndex).val(rows[0]["areaId"]);

            //收货省名称
            $("#provinceName_" + addIndex).val(rows[0]["provinceName"]);
            //收货市名称
            $("#cityName_" + addIndex).val(rows[0]["cityName"]);
            //收货区名称
            $("#areaName_" + addIndex).val(rows[0]["areaName"]);

            //联系人
            $("#contact_" + addIndex).val(rows[0]["contact"]);
            //联系人手机
            $("#mobile_" + addIndex).val(rows[0]["mobile"]);
            //收货详细地址
            $("#detailAddr_" + addIndex).val(rows[0]["detailAddr"]);
            //收货地址编码
            $("#addrCode_" + addIndex).val(rows[0]["addrCode"]);

            //单独校验
            // $("#form-invoice-add").validate().element($("#deliAddrName"));
            layer.close(index);
        });
    }

    function submitHandler(btnIdx) {
        let pass = vue.checkLineFromToNwv()
        if (pass) {
            var dis = $(":disabled");
            dis.attr("disabled", false);
            if (vue.lines.length == 1 && vue.lines[0].stations.length == 0) {
                $.modal.msgError("尚未拆段，无需提交")
                return
            }
            var data = JSON.parse(JSON.stringify(vue.lines));
            // 精简数据，去除from、to的shippingGoodsList
            for (let i = 0; i < data.length; i++) {
                if (!data[i].reqDeliDate || !data[i].reqArriDate) {
                    $.modal.msgError("请将所有要求提到货日期补充完整")
                    return
                }
                for (let j = 0; j < data[i].stations.length; j++) {
                    if (!data[i].stations[j].reqArriDate) {
                        $.modal.msgError("中转站的要求到货日期必填")
                        return;
                    }
                }

                data[i].from.forEach(f => delete f['shippingGoodsList']);
                data[i].to.forEach(t => delete t['shippingGoodsList']);
            }
            let param = {segmentId: [[${segmentIds}]], lines: data, rollback: vue.rollback};
            $.operate.saveTabJson(prefix + "/subsection-multi", param, function (result) {
                if (result.code != 0) {
                    dis.attr("disabled", true);
                }else{
                    if(btnIdx == 2){
                        let segmentId = result.data;
                        console.log("dispatch segments=",segmentId);
                        window.top.topOpenTab("保存第一段调度",ctx + "tms/segment/dispatch/"+segmentId);

                    }
                }
            });

        }
    }
    function segmentDispatch(segmentIds){
        $.modal.openTab("调度",ctx + "tms/segment/dispatch/"+segmentIds);
    }

    let vue = new Vue({
        el: '#app',
        data() {
            return {
                rollback: false,
                lines: [], // {from:[], to:[], stations:[]}
                address: [[${address}]],
                theLineSeq: -1, // 当前选择的运段
                theSide: null, // 'from'、'to'
                goodsLine: {}, // {'客户单号-货品': {from:{n,w,v}, to:{n,w,v}}}，初始货品的提到货量，前后需一致，与pass一起使用
                addrMap: {}, // 地址id对应地址对象
                pass: true, // 校验提到货量是否一致
                debug: false
            }
        },
        computed: {
            chooseAble() {
                let list = [];
                let addrs = this.address;
                let yfys = this.computeYfYs(); // 应发、应收、实发、实收
                if (this.theLineSeq != -1) {
                    let line = this.lines[this.theLineSeq];
                    for (let i = 0; i < addrs.length; i++) {
                        // 当前段是否出现该地址
                        let find = false;
                        for (let j = 0; j < line.from.length; j++) {
                            if (line.from[j].multipleShippingAddressId == addrs[i].multipleShippingAddressId) {
                                find = true;
                                break;
                            }
                        }
                        for (let j = 0; !find && j < line.to.length; j++) {
                            if (line.to[j].multipleShippingAddressId == addrs[i].multipleShippingAddressId) {
                                find = true;
                                break;
                            }
                        }
                        if (!find) {
                            let x = yfys[addrs[i].multipleShippingAddressId];
                            if (this.theSide == 'from') {
                                // 如何判断该地址已出货完成？不单单是应发货，还要考虑中转发货
                                if (x) {
                                    if (
                                        x.delix.n < x.deli.n || x.delix.w < x.deli.w || x.delix.v < x.deli.v // 实发 < 应发
                                        ||
                                        x.arrix.n > x.arri.n || x.arrix.w > x.arri.w || x.arrix.v > x.arri.v // 实收 > 应收
                                    ) {
                                        list.push(addrs[i]);
                                    }
                                } else { // 没有则说明该地址在所有运段均未出现
                                    if (addrs[i].addressType == 0) {
                                        list.push(addrs[i]);
                                    }
                                }
                            } else if (this.theSide == 'to') {
                                // 只要是未出现的地址，不考虑其他
                                if (addrs[i].addressType == 1) {
                                    // 屏蔽已到货结束的到货地址，没必要再绕回去
                                    if (x) {
                                        if (x.arrix.n < x.arri.n || x.arrix.w < x.arri.w || x.arrix.v < x.arri.v) {
                                            list.push(addrs[i])
                                        }
                                    } else { // 未出现，肯定未到货
                                        list.push(addrs[i]);
                                    }
                                } else if (addrs[i].addressType == 0) {
                                    // 屏蔽已发完货的发货地址，没必要再绕回去
                                    if (x) {
                                        if (x.delix.n < x.deli.n || x.delix.w < x.deli.w || x.delix.v < x.deli.v) {
                                            list.push(addrs[i]);
                                        }
                                    } else { // 未出现，肯定未提货
                                        list.push(addrs[i])
                                    }
                                }
                                /*if (x) {
                                    if (x.arrix.n < x.arri.n || x.arri.w < x.arri.w || x.arrix.v < x.arri.v) {
                                        list.push(addrs[i]);
                                    }
                                } else { // 没有则说明该地址在所有运段均未出现
                                    if (addrs[i].addressType == 1) {
                                        list.push(addrs[i]);
                                    }
                                }*/
                            }
                        }
                    }
                }
                return list;
            },
            goodsMap() { // 收货品的已收量，发货品的
                let map = {}; // {goodsId:{ys}}
                for (let i = 0; i < this.address.length; i++) {
                    let {addressType, shippingGoodsList} = this.address[i];
                    for (let j = 0; j < shippingGoodsList.length; j++) {
                        let {multipleShippingGoodsId, custOrderno, goodsName, num, weight, volume} = shippingGoodsList[j];
                        if (addressType == 0) {
                            map[multipleShippingGoodsId] = {custOrderno, goodsName, deli: {n:num,w:weight,v:volume}, yf: {n:0,w:0,v:0}}; // 应发已发
                        } else if (addressType == 1) {
                            map[multipleShippingGoodsId] = {custOrderno, goodsName, arri: {n:num,w:weight,v:volume}, ys: {n:0,w:0,v:0}}; // 应收已收
                        }
                    }
                }
                for (let i = 0; i < this.lines.length; i++) {
                    let {from, to} = this.lines[i];
                    for (let j = 0; j < from.length; j++) {
                        /*if (from[j].addressType == 0) {
                            for (let k = 0; k < from[j].goodsList.length; k++) {
                                let {multipleShippingGoodsId, yf} = from[j].goodsList[k]; // 已发货数
                                let val = map[multipleShippingGoodsId];
                                val.yf.n = this.plus(val.yf.n, yf.n); // TODO 多次发货时，已发货量重复
                                val.yf.w = this.plus(val.yf.w, yf.w);
                                val.yf.v = this.plus(val.yf.v, yf.v);
                            }
                        }*/
                    }
                    for (let j = 0; j < to.length; j++) {
                        if (to[j].addressType == 1 && to[j].goodsList) {
                            for (let k = 0; k < to[j].goodsList.length; k++) {
                                let {multipleShippingGoodsId, ys} = to[j].goodsList[k]; // 已收货数
                                let val = map[multipleShippingGoodsId];
                                val.ys.n = this.plus(val.ys.n, ys.n);
                                val.ys.w = this.plus(val.ys.w, ys.w);
                                val.ys.v = this.plus(val.ys.v, ys.v);
                            }
                        }
                    }
                }
                return map;
            }
        },
        mounted() {
            let line = {from:[], to:[], stations:[], reqDeliDate: segmentList[0].reqDeliDate, reqArriDate: segmentList[0].reqArriDate};
            let goodsLine = {};
            let iFrom = 0; let iTo = 0;
            for (let i = 0; i < this.address.length; i++) {
                let {addrName,provinceName,cityName,areaName,detailAddr,addressType,multipleShippingAddressId,shippingGoodsList} = this.address[i]
                this.addrMap[multipleShippingAddressId] = this.address[i];
                let goodsList = [];
                for (let j = 0; j < shippingGoodsList.length; j++) {
                    let g = this.copyGoods(shippingGoodsList[j]);
                    g.yf = {n: 0, w: 0, v: 0}; // 货品已发货
                    g.wf = {n: 0, w: 0, v: 0}; // 货品未发货
                    g.ys = {n: 0, w: 0, v: 0}; // 货品已收货
                    g.ws = {n: 0, w: 0, v: 0}; // 货品未收货
                    if (addressType == 0) {
                        g.yf = {n: g.num, w: g.weight, v: g.volume};
                    } else if (addressType == 1) {
                        g.ys = {n: g.num, w: g.weight, v: g.volume}
                    }
                    goodsList.push(g)
                }
                if (addressType == 0) {
                    this.addrMap[multipleShippingAddressId]['seq'] = iFrom++;
                    let p = {addrName,provinceName,cityName,areaName,detailAddr,addressType,multipleShippingAddressId,shippingGoodsList,goodsList};
                    this.calcPointNwv(p);
                    line.from.push(p)
                } else {
                    this.addrMap[multipleShippingAddressId]['seq'] = iTo++;
                    let p = {addrName,provinceName,cityName,areaName,detailAddr,addressType,multipleShippingAddressId,shippingGoodsList,goodsList};
                    this.calcPointNwv(p);
                    line.to.push(p)
                }
                for (let j = 0; j < this.address[i].shippingGoodsList.length; j++) {
                    let g = this.address[i].shippingGoodsList[j];
                    let custOrderno = g.custOrderno;
                    let goodsKey = (custOrderno ? (custOrderno + "-") : "") + g.goodsName;
                    if (!goodsLine[goodsKey]) {
                        goodsLine[goodsKey] = {from: {n: 0, w: 0, v: 0}, to: {n: 0, w: 0, v: 0}}
                    }
                    if (this.address[i].addressType == 0) {
                        let from = goodsLine[goodsKey]['from'];
                        from.n = this.plus(from.n, g.num)
                        from.w = this.plus(from.w, g.weight)
                        from.v = this.plus(from.v, g.volume)
                    } else if (this.address[i].addressType == 1) {
                        let to = goodsLine[goodsKey]['to'];
                        to.n = this.plus(to.n, g.num)
                        to.w = this.plus(to.w, g.weight)
                        to.v = this.plus(to.v, g.volume)
                    }
                }
            }
            let pass = true
            this.goodsLine = goodsLine;
            for (const key in goodsLine) {
                let {from, to} = goodsLine[key];
                if (from.n != to.n || from.w != to.w || from.v != to.v) {
                    pass = false;
                    break;
                }
            }
            this.pass = pass;
            this.lines.push(line);
            this.origin = JSON.parse(JSON.stringify(line))
        },
        methods: {
            calcPointNwv(p) { // 计算一个提到货点的已发、未发、已收、未收
                // TODO 地址应考虑总未发和总未收，不需要考虑总已发已收？
                let f1 = ['yf', 'wf', 'ys', 'ws'];
                let f2 = ['n','w','v'];
                let d = []
                for (let i = 0; i < f1.length; i++) {
                    d[i] = {n: 0, w: 0, v: 0};
                }
                let list = p.goodsList.concat(p.hold||[]);
                for (let i = 0; i < list.length; i++) {
                    let g = list[i];
                    for (let j = 0; j < f1.length; j++) {
                        for (let k = 0; k < f2.length; k++) {
                            // 未发 = 未发之和
                            // 未收 = 未收之和
                            d[j][f2[k]] = this.plus(d[j][f2[k]], g[f1[j]][f2[k]])
                        }
                    }
                }
                for (let i = 0; i < f1.length; i++) {
                    this.$set(p, f1[i], d[i])
                }
            },
            plus(n1, n2) { // +运算
                return new BigNumber(n1).plus(new BigNumber(n2)).toNumber()
            },
            minus(n1, n2) { // -运算
                return new BigNumber(n1).minus(new BigNumber(n2)).toNumber()
            },
            times(n1, n2) {
                return new BigNumber(n1).times(new BigNumber(n2)).toNumber()
            },
            div(n1, n2, dp) { // 除运算（被除数，除数，保留小数）
                if (dp == undefined) {
                    dp = 2;
                }
                return new BigNumber(n1).div(new BigNumber(n2)).dp(dp).toNumber()
            },
            goodsDesc(goods) {
                let tmp = [];
                if (goods.custOrderno) {
                    tmp.push(goods.custOrderno, "-")
                }
                tmp.push(goods.goodsName);
                /*let nwv = [];
                if (goods.num > 0) {
                    nwv.push(goods.num + '件')
                }
                if (goods.weight > 0) {
                    nwv.push(goods.weight + '吨')
                }
                if (goods.volume > 0) {
                    nwv.push(goods.volume + 'm³')
                }
                tmp.push(" ").push(nwv.join('|'))*/
                return tmp.join('');
            },
            addStation(lineIndex) {
                let that = this;
                $.modal.open("地址信息", ctx + "basic/address/selectAddress?addrType=3","","",function (index, layero) {
                    //获取整行
                    var rows = layero.find('iframe')[0].contentWindow.getChecked();
                    if (rows.length === 0) {
                        $.modal.alertWarning("请至少选择一条记录");
                        return;
                    }

                    for (let i = 0; i < that.lines[lineIndex].stations.length; i++) {
                        if (rows[0].addressId == that.lines[lineIndex].stations[i].addressId) {
                            $.modal.msgError("该中转站已存在，不可重复添加");
                            return
                        }
                    }
                    let station = JSON.stringify(rows[0]);
                    that.lines[lineIndex].stations.push(JSON.parse(station))
                    //console.log(station)
                    layer.close(index);
                });
            },
            stationRmv(lineIndex,idx) {
                this.lines[lineIndex].stations.splice(idx, 1)
            },
            stationLeft(lineIndex,idx) {
                this.lines[lineIndex].stations.splice(idx-1, 0, ...this.lines[lineIndex].stations.splice(idx, 1))
            },
            stationRight(lineIndex,idx) {
                this.lines[lineIndex].stations.splice(idx+1, 0, ...this.lines[lineIndex].stations.splice(idx, 1))
            },
            addLine() {
                this.lines.push({from:[],to:[],stations:[]})
            },
            delLine(idx) {
                let that = this;
                let pCount = this.lines[idx].from.length + this.lines[idx].to.length; // 本段地点数
                if (pCount == 0) { // 对后续没有影响，直接删除
                    that.lines.splice(idx, 1);
                } else {
                    let afterPCount = 0; // 后续段地点数
                    for (let i = idx + 1; i < this.lines.length; i++) {
                        afterPCount += (this.lines[i].from.length + this.lines[i].to.length)
                    }
                    if (afterPCount == 0) {
                        $.modal.confirm("确认删除当前段？", function() {
                            that.lines.splice(idx, 1);
                        })
                    } else {
                        $.modal.confirm("删除该段将同步删除后续段，是否继续？", function() {
                            that.lines.splice(idx, that.lines.length - idx)
                        })
                    }
                }
            },
            rmvPoint(lineIdx, fromto, idx) {
                //$.modal.confirm("确定移除该地点？", function(){
                for (let i = 0; i < fromto.length; i++) {
                    for (let j = 0; fromto[i].hold && j < fromto[i].hold.length; j++) {
                        fromto[i].hold[j].wf.n = 0;
                        fromto[i].hold[j].wf.w = 0;
                        fromto[i].hold[j].wf.v = 0;
                    }
                }
                fromto.splice(idx, 1)
                this.balance(lineIdx);
                //})
                // TODO 后续段的依赖该节点已收未发的数据要清理
            },
            goodsKey(g) {
                return (g.custOrderno ? (g.custOrderno + "-") : "") + g.goodsName;
            },
            addP(idx, side) {
                this.theLineSeq = idx
                this.theSide = side
                let that = this;
                // 增加提货点
                layer.open({
                    type: 1,
                    zIndex: 1000,
                    area: ['500px', '400px'],
                    title: '选择'+(side=='from'?'装':'卸')+'货点',
                    shadeClose: false,
                    content: $('#pointList'),
                    btn: ['确定', '取消'],
                    end: function () {
                        that.theLineSeq = -1;
                        that.theSide = null;
                        for (let i = 0; i < that.address.length; i++) {
                            delete that.address[i]['choosed']
                        }
                    },
                    yes: function(index, layero) {
                        let tmp = []
                        let from = that.lines[idx]['from'];
                        let to = that.lines[idx]['to'];
                        for (let i = 0; i < that.chooseAble.length; i++) {
                            if (that.chooseAble[i]['choosed']) {
                                delete that.chooseAble[i]['choosed']
                                // 同一地址同一运段只能出现一次
                                let find = false;
                                for (let j = 0; j < from.length; j++) {
                                    if (from[j].multipleShippingAddressId == that.chooseAble[i].multipleShippingAddressId) {
                                        find = true;
                                        break;
                                    }
                                }
                                for (let j = 0; j < to.length; j++) {
                                    if (to[j].multipleShippingAddressId == that.chooseAble[i].multipleShippingAddressId) {
                                        find = true;
                                        break;
                                    }
                                }
                                // 同一地址同一运段只能出现一次
                                if (!find) {
                                    let {addrName,provinceName,cityName,areaName,detailAddr,addressType,multipleShippingAddressId,shippingGoodsList} = that.chooseAble[i]
                                    tmp.push({addrName,provinceName,cityName,areaName,detailAddr,addressType,multipleShippingAddressId,shippingGoodsList}); // 直接放list会导致chooseAble变动
                                }
                            }
                        }
                        for (let i = 0; i < tmp.length; i++) {
                            let goodsList = [];
                            for (let j = 0; j < tmp[i].shippingGoodsList.length; j++) {
                                let g = that.copyGoods(tmp[i].shippingGoodsList[j]);
                                g.yf = {n:0,w:0,v:0}; // 货品已发货
                                g.wf = {n:0,w:0,v:0}; // 货品未发货
                                g.ys = {n:0,w:0,v:0}; // 货品已收货
                                g.ws = {n:0,w:0,v:0}; // 货品未收货
                                if (tmp[i].addressType == 0) {
                                    g.wf = {n:g.num,w:g.weight,v:g.volume};
                                    // wf_g,ys_g,ws_g保持0
                                } else if (tmp[i].addressType == 1) {
                                    g.ws = {n:g.num,w:g.weight,v:g.volume}
                                }
                                goodsList.push(g)
                            }
                            if (side == 'from') {
                                if (tmp[i].addressType == 0) {
                                    // 当前运段之前的运段的以该地址为到货点的所有货量
                                    for (let j = idx - 1; j >= 0; j--) {
                                        for (let k = 0; k < that.lines[j].to.length; k++) {
                                            if (that.lines[j].to[k].multipleShippingAddressId == tmp[i].multipleShippingAddressId) {
                                                if (that.lines[j].to[k].hold) {
                                                    for (let l = 0; l < that.lines[j].to[k].hold.length; l++) {
                                                        let g = Object.assign({}, that.lines[j].to[k].hold[l]);
                                                        goodsList.push(g)
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    for (let j = 0; j < goodsList.length; j++) {
                                        let g = goodsList[j];
                                        g.yf = g.wf;
                                        g.wf = {n: 0, w: 0, v: 0};
                                        g.ys = {n: 0, w: 0, v: 0};
                                        g.ws = {n: 0, w: 0, v: 0};
                                    }
                                    tmp[i].goodsList = goodsList;
                                } else if (tmp[i].addressType == 1) {
                                    // 取所有以该地址为段点的数据，计算其未发的数据
                                    let _goodsList = []
                                    for (let j = 0; j < idx; j++) { // 往前取运段
                                        let {from, to} = that.lines[j];
                                        // 先有收才有发
                                        for (let k = 0; k < to.length; k++) {
                                            if (to[k].multipleShippingAddressId == tmp[i].multipleShippingAddressId) {
                                                if (to[k].hold) {
                                                    for (let l = 0; l < to[k].hold.length; l++) {
                                                        let g = Object.assign({}, to[k].hold[l]);
                                                        let {n, w, v} = g.wf;
                                                        if (n > 0 || w > 0 || v > 0) {
                                                            _goodsList.push(g)
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        // 该地点在之前段有发货，但未发货完成时，从待转发中扣除
                                        for (let k = 0; k < from.length; k++) {
                                            if (from[k].multipleShippingAddressId == tmp[i].multipleShippingAddressId) {
                                                let goodsList = from[k].goodsList;
                                                for (let l = 0; l < goodsList.length; l++) {
                                                    let {n, w, v} = goodsList[l].yf;
                                                    let goodsKey = that.goodsKey(goodsList[l]);
                                                    for (let m = 0; m < _goodsList.length; m++) {
                                                        let g2 = _goodsList[m]
                                                        let goodsKey2 = that.goodsKey(g2);
                                                        if (goodsKey == goodsKey2) {
                                                            let minn = g2.wf.n > n ? n : g2.wf.n;
                                                            let minw = g2.wf.w > w ? w : g2.wf.w;
                                                            let minv = g2.wf.v > v ? v : g2.wf.v;
                                                            g2.wf.n = that.minus(g2.wf.n, minn);
                                                            g2.wf.w = that.minus(g2.wf.w, minw);
                                                            g2.wf.v = that.minus(g2.wf.v, minv);
                                                            if (g2.wf.n == 0 && g2.wf.w == 0 && g2.wf.v == 0) {
                                                                _goodsList.splice(m, 1);
                                                                m--;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    for (let j = 0; j < _goodsList.length; j++) {
                                        _goodsList[j].yf = _goodsList[j].wf;
                                        _goodsList[j].wf = {n: 0, w: 0, v: 0};
                                        _goodsList[j].ys = {n: 0, w: 0, v: 0};
                                        _goodsList[j].ws = {n: 0, w: 0, v: 0};
                                    }
                                    tmp[i].goodsList = _goodsList;
                                }
                                that.calcPointNwv(tmp[i])
                            } else if (side == 'to') {
                                // 交由balance处理右侧货量
                            }

                            that.lines[idx][side].push(tmp[i])
                            that.balance(idx)
                        }
                        layer.close(index);
                    }
                });
            },
            balance(idx) { // 分摊发货到收货，多余的出库分摊到最后一个到货地址上
                let line = this.lines[idx];
                let that = this;
                let lineGoodsMap = {}; // 发货量：当前行待分配的到货货品，根据客户单号和货品为唯一键，用到货地址的到货货品量扣减
                let lineGoodsMap2 = {}; // 发货量2：当前行待分配的到货货品，根据shippingGoodsId为唯一键，该变量的必要性：非到货货品时，同客户单号品名合并后不能区分
                for (let j = 0; j < line.from.length; j++) {
                    for (let k = 0; k < line.from[j].goodsList.length; k++) {
                        let g = line.from[j].goodsList[k];
                        let goodsKey = that.goodsKey(g);
                        let goodsKey2 = g.multipleShippingGoodsId;
                        let d = lineGoodsMap[goodsKey];
                        if (!d) {
                            d = that.copyGoods(g);
                            d.yf = {n: 0, w: 0, v: 0};
                        }
                        d.yf.n = that.plus(d.yf.n, g.yf.n);
                        d.yf.w = that.plus(d.yf.w, g.yf.w);
                        d.yf.v = that.plus(d.yf.v, g.yf.v);
                        if (d.yf.n > 0 || d.yf.w > 0 || d.yf.v > 0) { // 有发货量
                            lineGoodsMap[goodsKey] = d;
                        }
                        let d2 = lineGoodsMap2[goodsKey2];
                        if (!d2) {
                            d2 = that.copyGoods(g);
                            d2.yf = {n: 0, w: 0, v: 0};
                        }
                        d2.yf.n = that.plus(d2.yf.n, g.yf.n);
                        d2.yf.w = that.plus(d2.yf.w, g.yf.w);
                        d2.yf.v = that.plus(d2.yf.v, g.yf.v);
                        if (d2.yf.n > 0 || d2.yf.w > 0 || d2.yf.v > 0) { // 有发货量
                            lineGoodsMap2[goodsKey2] = d2;
                        }
                    }
                }
                console.log(lineGoodsMap)
                console.log("??",lineGoodsMap2)
                for (let i = 0; i < line.to.length; i++) {
                    if (line.to[i].addressType == 1) { // 本是收货地址，扣减应收后再分
                        let goodsList = [];
                        line.to[i].goodsList = goodsList;
                        for (let j = 0; j < line.to[i].shippingGoodsList.length; j++) {
                            let g = that.copyGoods(line.to[i].shippingGoodsList[j]);
                            // 已发、未发、已收、未收
                            g.yf = {n: 0, w: 0, v: 0};
                            g.wf = {n: 0, w: 0, v: 0};
                            g.ys = {n: 0, w: 0, v: 0};
                            g.ws = {n: g.num, w: g.weight, v: g.volume};
                            // 未收应再减去该点在其他段的到货数ys
                            let t = that.goodsMap[g.multipleShippingGoodsId]; // 在其他段已收量
                            if (t && t.ys) {
                                g.ws.n = that.minus(g.ws.n, t.ys.n);
                                g.ws.w = that.minus(g.ws.w, t.ys.w);
                                g.ws.v = that.minus(g.ws.v, t.ys.v);
                            }

                            goodsList.push(g);
                            let goodsKey = that.goodsKey(g);
                            let goodsKey2 = g.multipleShippingGoodsId;
                            let g2x = lineGoodsMap2[goodsKey2];
                            if (g2x) {
                                alert('应收已收货品不应该出现在发货货品中！')
                            }
                            let g2 = lineGoodsMap[goodsKey]; // 应收的货品存在，根据客户单号和货品名扣减发货量，同时扣减发货量2
                            if (g2) {
                                // 可分摊量计算
                                let min_n = g.ws.n > g2.yf.n ? g2.yf.n : g.ws.n;
                                let min_w = g.ws.w > g2.yf.w ? g2.yf.w : g.ws.w;
                                let min_v = g.ws.v > g2.yf.v ? g2.yf.v : g.ws.v;

                                // 未收量计算
                                g.ws.n = that.minus(g.ws.n, min_n);
                                g.ws.w = that.minus(g.ws.w, min_w);
                                g.ws.v = that.minus(g.ws.v, min_v);
                                // 已收量计算
                                g.ys.n = that.plus(g.ys.n, min_n);
                                g.ys.w = that.plus(g.ys.w, min_w);
                                g.ys.v = that.plus(g.ys.v, min_v);
                                // 未分摊量计算
                                g2.yf.n = that.minus(g2.yf.n, min_n)
                                g2.yf.w = that.minus(g2.yf.w, min_w)
                                g2.yf.v = that.minus(g2.yf.v, min_v)
                                if (g2.yf.n == 0 && g2.yf.w == 0 && g2.yf.v == 0) { // 被到货量扣光，
                                    delete lineGoodsMap[goodsKey]
                                }
                                // 顺序扣减同单号同品名发货量2，消耗min_nwv
                                for (const k2 in lineGoodsMap2) {
                                    let gt = lineGoodsMap2[k2]
                                    if (goodsKey == that.goodsKey(gt)) {
                                        let min_min_n = min_n > gt.yf.n ? gt.yf.n : min_n;
                                        let min_min_w = min_w > gt.yf.w ? gt.yf.w : min_w;
                                        let min_min_v = min_v > gt.yf.v ? gt.yf.v : min_v;
                                        gt.yf.n = that.minus(gt.yf.n, min_min_n);
                                        gt.yf.w = that.minus(gt.yf.w, min_min_w);
                                        gt.yf.v = that.minus(gt.yf.v, min_min_v);
                                        min_n = that.minus(min_n, min_min_n);
                                        min_w = that.minus(min_w, min_min_w);
                                        min_v = that.minus(min_v, min_min_v);
                                        if (gt.yf.n == 0 && gt.yf.w == 0 && gt.yf.v == 0) {
                                            delete lineGoodsMap2[k2];
                                        }
                                        if (min_n == 0 && min_w == 0 && min_v == 0) {
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    } else if (line.to[i].addressType == 0) { // 提货点作为到货地址使用时，不应存在应到货货品
                        line.to[i].goodsList = [];
                    }
                }
                if (line.to.length > 0) {
                    // 原hold的wf、ys置0，待分配的量加进去
                    for (let i = 0; i < line.to.length; i++) {
                        for (let j = 0; line.to[i].hold && j < line.to[i].hold.length; j++) {
                            let ng = lineGoodsMap2[line.to[i].hold[j].multipleShippingGoodsId];
                            if (!ng) {
                                line.to[i].hold.splice(j, 1);
                                j--;
                                continue;
                            }
                            line.to[i].hold[j].wf.n = 0;
                            line.to[i].hold[j].wf.w = 0;
                            line.to[i].hold[j].wf.v = 0;
                            line.to[i].hold[j].wf.n = that.plus(line.to[i].hold[j].wf.n, ng.yf.n);
                            line.to[i].hold[j].wf.w = that.plus(line.to[i].hold[j].wf.w, ng.yf.w);
                            line.to[i].hold[j].wf.v = that.plus(line.to[i].hold[j].wf.v, ng.yf.v);
                            line.to[i].hold[j].ys = line.to[i].hold[j].wf;
                            delete lineGoodsMap2[line.to[i].hold[j].multipleShippingGoodsId]
                        }
                    }
                    let lastP = line.to[line.to.length - 1];
                    for (const key in lineGoodsMap2) { // 剩下未分配的数据
                        let g = lineGoodsMap2[key];
                        g.wf = g.yf;
                        g.yf = {n: 0, w: 0, v: 0}
                        g.ys = g.wf;
                        g.ws = {n: 0, w: 0, v: 0}
                        if (!lastP.hold) {
                            lastP.hold = [];
                        }
                        lastP.hold.push(g)
                    }
                }
                for (let i = 0; i < line.to.length; i++) {
                    that.calcPointNwv(line.to[i]);
                }
                // 此时点的wf=0
            },
            checkLineFromToNwv() { // 检查每个运段的提到货量是否前后一致
                let addressResult = {}; // 各地址的已应发货数，已发货数，应收货数，已收货数
                for (let i = 0; i < this.lines.length; i++) {
                    let tmp = {}
                    for (let j = 0; j < this.lines[i].from.length; j++) {
                        let {multipleShippingAddressId,addrName,addressType,shippingGoodsList,yf,ys,wf,ws} = this.lines[i].from[j];
                        let r = addressResult[multipleShippingAddressId];
                        if (!r) {
                            r = {
                                addrName,
                                deli: {n: 0, w: 0, v: 0}, // 应发
                                delix: {n: 0, w: 0, v: 0}, // 已发
                                arri: {n: 0, w: 0, v: 0}, // 应收
                                arrix: {n: 0, w: 0, v: 0} // 已收
                            };
                            for (let k = 0; k < shippingGoodsList.length; k++) {
                                let {num, weight, volume} = shippingGoodsList[k];
                                if (addressType == 0) {
                                    r.deli.n = this.plus(r.deli.n, num);
                                    r.deli.w = this.plus(r.deli.w, weight);
                                    r.deli.v = this.plus(r.deli.v, volume);
                                } else if (addressType == 1) {
                                    r.arri.n = this.plus(r.arri.n, num);
                                    r.arri.w = this.plus(r.arri.w, weight);
                                    r.arri.v = this.plus(r.arri.v, volume);
                                }
                            }
                            addressResult[multipleShippingAddressId] = r;
                        }
                        r.delix.n = this.plus(r.delix.n, yf.n);
                        r.delix.w = this.plus(r.delix.w, yf.w);
                        r.delix.v = this.plus(r.delix.v, yf.v);
                        r.arrix.n = this.plus(r.arrix.n, ys.n);
                        r.arrix.w = this.plus(r.arrix.w, ys.w);
                        r.arrix.v = this.plus(r.arrix.v, ys.v);

                        let list = this.lines[i].from[j].goodsList.concat(this.lines[i].from[j].hold||[])
                        for (let k = 0; k < list.length; k++) {
                            let g = list[k]
                            let goodsKey = this.goodsKey(g);
                            if (!tmp[goodsKey]) {
                                tmp[goodsKey] = {from: {n: 0, w: 0, v: 0}, to: {n: 0, w: 0, v: 0}}
                            }
                            tmp[goodsKey].from.n = this.plus(tmp[goodsKey].from.n, g.yf.n);
                            tmp[goodsKey].from.w = this.plus(tmp[goodsKey].from.w, g.yf.w);
                            tmp[goodsKey].from.v = this.plus(tmp[goodsKey].from.v, g.yf.v);
                        }
                    }
                    for (let j = 0; j < this.lines[i].to.length; j++) {
                        let {multipleShippingAddressId,addrName,addressType,shippingGoodsList,yf,ys,wf,ws} = this.lines[i].to[j];
                        let r = addressResult[multipleShippingAddressId];
                        if (!r) {
                            r = {
                                addrName,
                                deli: {n: 0, w: 0, v: 0}, // 应发
                                delix: {n: 0, w: 0, v: 0}, // 已发
                                arri: {n: 0, w: 0, v: 0}, // 应收
                                arrix: {n: 0, w: 0, v: 0} // 已收
                            };
                            for (let k = 0; k < shippingGoodsList.length; k++) {
                                let {num, weight, volume} = shippingGoodsList[k];
                                if (addressType == 0) {
                                    r.deli.n = this.plus(r.deli.n, num);
                                    r.deli.w = this.plus(r.deli.w, weight);
                                    r.deli.v = this.plus(r.deli.v, volume);
                                } else if (addressType == 1) {
                                    r.arri.n = this.plus(r.arri.n, num);
                                    r.arri.w = this.plus(r.arri.w, weight);
                                    r.arri.v = this.plus(r.arri.v, volume);
                                }
                            }
                            addressResult[multipleShippingAddressId] = r;
                        }
                        r.delix.n = this.plus(r.delix.n, yf.n);
                        r.delix.w = this.plus(r.delix.w, yf.w);
                        r.delix.v = this.plus(r.delix.v, yf.v);
                        r.arrix.n = this.plus(r.arrix.n, ys.n);
                        r.arrix.w = this.plus(r.arrix.w, ys.w);
                        r.arrix.v = this.plus(r.arrix.v, ys.v);

                        let list = this.lines[i].to[j].goodsList.concat(this.lines[i].to[j].hold||[])
                        for (let k = 0; k < list.length; k++) {
                            let g = list[k]
                            let goodsKey = this.goodsKey(g);
                            if (!tmp[goodsKey]) {
                                tmp[goodsKey] = {from: {n: 0, w: 0, v: 0}, to: {n: 0, w: 0, v: 0}}
                            }
                            tmp[goodsKey].to.n = this.plus(tmp[goodsKey].to.n, g.ys.n);
                            tmp[goodsKey].to.w = this.plus(tmp[goodsKey].to.w, g.ys.w);
                            tmp[goodsKey].to.v = this.plus(tmp[goodsKey].to.v, g.ys.v);
                        }
                    }
                    for (const tmpKey in tmp) {
                        let {from, to} = tmp[tmpKey];
                        if (from.n != to.n || from.w != to.w || from.v != to.v) {
                            $.modal.alertError("第" + (i + 1) + "段的【" + tmpKey + "】提到货量不一致");
                            return false;
                        }
                    }
                }
                console.log(addressResult)
                // 判断全部地址是否已配置
                for (let i = 0; i < this.address.length; i++) {
                    let {addrName,addressType,multipleShippingAddressId} = this.address[i];
                    let r = addressResult[multipleShippingAddressId];
                    if (!r) {
                        $.modal.alertError((addressType == 0 ? "发":"收") + "货点【" + addrName + "】尚未配置进运段，请配置完成后再继续！");
                        return false;
                    }
                }
                // 在此基础上判断总发，总收
                for (let i = 0; i < this.address.length; i++) {
                    let {addrName,addressType,multipleShippingAddressId} = this.address[i];
                    let r = addressResult[multipleShippingAddressId];
                    if (addressType == 0) {
                        // 发货点实收抵消实发
                        r.delix.n = this.minus(r.delix.n, r.arrix.n); r.arrix.n = 0;
                        r.delix.w = this.minus(r.delix.w, r.arrix.w); r.arrix.w = 0;
                        r.delix.v = this.minus(r.delix.v, r.arrix.v); r.arrix.v = 0;
                        // 判断发货数
                        if (r.deli.n != r.delix.n || r.deli.w != r.delix.w || r.deli.v != r.delix.v) {
                            $.modal.alertError("发货点【" + addrName + "】应发货(" + r.deli.n + "件/" + r.deli.w + "吨/" + r.deli.v
                                + "m³)与实际发货(" + r.delix.n + "件/" + r.delix.w + "吨/" + r.delix.v + "m³)不符");
                            return false;
                        }
                    } else if (addressType == 1) {
                        r.arrix.n = this.minus(r.arrix.n, r.delix.n); r.delix.n = 0;
                        r.arrix.w = this.minus(r.arrix.w, r.delix.w); r.delix.w = 0;
                        r.arrix.v = this.minus(r.arrix.v, r.delix.v); r.delix.v = 0;
                        // 判断收货数
                        if (r.arri.n != r.arrix.n || r.arri.w != r.arrix.w || r.arri.v != r.arrix.v) {
                            $.modal.alertError("收货点【" + addrName + "】应收货(" + r.arri.n + "件/" + r.arri.w + "吨/" + r.arri.v
                                + "m³)与实际收货(" + r.arrix.n + "件/" + r.arrix.w + "吨/" + r.arrix.v + "m³)不符");
                            return false;
                        }
                    }
                }
                return true;
            },
            copyGoods(g) {
                let {multipleShippingGoodsId, invoiceGoodsId, goodsName, custOrderno, num, weight, volume} = g;
                return {multipleShippingGoodsId, invoiceGoodsId, goodsName, custOrderno, num, weight, volume};
            },
            computeYfYs() { // 统计应发应收
                let addressResult = {}; // 各地址的已应发货数，已发货数，应收货数，已收货数
                for (let i = 0; i < this.lines.length; i++) {
                    let {from, to} = this.lines[i];
                    for (let j = 0; j < from.length; j++) {
                        let {multipleShippingAddressId,addrName,addressType,shippingGoodsList,yf,ys,wf,ws} = from[j];
                        let r = addressResult[multipleShippingAddressId];
                        if (!r) {
                            r = {
                                addrName,
                                deli: {n: 0, w: 0, v: 0}, // 应发
                                delix: {n: 0, w: 0, v: 0}, // 已发
                                arri: {n: 0, w: 0, v: 0}, // 应收
                                arrix: {n: 0, w: 0, v: 0} // 已收
                            };
                            for (let k = 0; k < shippingGoodsList.length; k++) {
                                let {num, weight, volume} = shippingGoodsList[k];
                                if (addressType == 0) {
                                    r.deli.n = this.plus(r.deli.n, num);
                                    r.deli.w = this.plus(r.deli.w, weight);
                                    r.deli.v = this.plus(r.deli.v, volume);
                                } else if (addressType == 1) {
                                    r.arri.n = this.plus(r.arri.n, num);
                                    r.arri.w = this.plus(r.arri.w, weight);
                                    r.arri.v = this.plus(r.arri.v, volume);
                                }
                            }
                            addressResult[multipleShippingAddressId] = r;
                        }
                        r.delix.n = this.plus(r.delix.n, yf.n);
                        r.delix.w = this.plus(r.delix.w, yf.w);
                        r.delix.v = this.plus(r.delix.v, yf.v);
                        r.arrix.n = this.plus(r.arrix.n, ys.n);
                        r.arrix.w = this.plus(r.arrix.w, ys.w);
                        r.arrix.v = this.plus(r.arrix.v, ys.v);
                    }
                    for (let j = 0; j < to.length; j++) {
                        let {multipleShippingAddressId,addrName,addressType,shippingGoodsList,yf,ys,wf,ws} = to[j];
                        let r = addressResult[multipleShippingAddressId];
                        if (!r) {
                            r = {
                                addrName,
                                deli: {n: 0, w: 0, v: 0}, // 应发
                                delix: {n: 0, w: 0, v: 0}, // 已发
                                arri: {n: 0, w: 0, v: 0}, // 应收
                                arrix: {n: 0, w: 0, v: 0} // 已收
                            };
                            for (let k = 0; k < shippingGoodsList.length; k++) {
                                let {num, weight, volume} = shippingGoodsList[k];
                                if (addressType == 0) {
                                    r.deli.n = this.plus(r.deli.n, num);
                                    r.deli.w = this.plus(r.deli.w, weight);
                                    r.deli.v = this.plus(r.deli.v, volume);
                                } else if (addressType == 1) {
                                    r.arri.n = this.plus(r.arri.n, num);
                                    r.arri.w = this.plus(r.arri.w, weight);
                                    r.arri.v = this.plus(r.arri.v, volume);
                                }
                            }
                            addressResult[multipleShippingAddressId] = r;
                        }
                        r.delix.n = this.plus(r.delix.n, yf.n);
                        r.delix.w = this.plus(r.delix.w, yf.w);
                        r.delix.v = this.plus(r.delix.v, yf.v);
                        r.arrix.n = this.plus(r.arrix.n, ys.n);
                        r.arrix.w = this.plus(r.arrix.w, ys.w);
                        r.arrix.v = this.plus(r.arrix.v, ys.v);
                    }
                }

                for (let i = 0; i < this.address.length; i++) {
                    let {addrName,addressType,multipleShippingAddressId} = this.address[i];
                    let r = addressResult[multipleShippingAddressId];
                    if (!r) {
                        continue;
                    }
                    if (addressType == 0) {
                        // 发货点实收抵消实发：总已发 - 总已收 = 总实发
                        if (r.arrix.n > 0) {
                            r.delix.n = this.minus(r.delix.n, r.arrix.n);
                            r.arrix.n = 0;
                        }
                        if (r.arrix.w > 0) {
                            r.delix.w = this.minus(r.delix.w, r.arrix.w);
                            r.arrix.w = 0;
                        }
                        if (r.arrix.v > 0) {
                            r.delix.v = this.minus(r.delix.v, r.arrix.v);
                            r.arrix.v = 0;
                        }
                    } else if (addressType == 1) {
                        // 判断收货数
                        // 总收 - 总发 = 总实收
                        if (r.delix.n > 0) {
                            r.arrix.n = this.minus(r.arrix.n, r.delix.n);
                            r.delix.n = 0;
                        }
                        if (r.delix.w > 0) {
                            r.arrix.w = this.minus(r.arrix.w, r.delix.w);
                            r.delix.w = 0;
                        }
                        if (r.delix.v > 0) {
                            r.arrix.v = this.minus(r.arrix.v, r.delix.v);
                            r.delix.v = 0;
                        }
                    }
                }
                return addressResult;
            },
            debugYfys() {
                console.log(this.computeYfYs())
            },
            nwvChange() {
                for (let i = 0; i < this.lines.length; i++) {
                    this.balance(i);
                }
            }
        }
    })
</script>
</body>

</html>