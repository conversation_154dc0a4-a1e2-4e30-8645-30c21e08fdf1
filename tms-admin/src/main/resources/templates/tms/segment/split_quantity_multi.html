<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('多装多卸拆量')" />
    <style type="text/css">
        [v-clock]{
            display: none;
        }
        .address {
            padding: 7px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            -webkit-box-shadow: 0 1px 1px rgba(0,0,0,.05);
            box-shadow: 0 1px 1px rgba(0,0,0,.05);
        }
        .nwv {
            width: 56px;
            border: 1px #ddd solid;
            text-align: center;
        }
        .ellipsis {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>

<body>



<div id="app" class="form-content" v-clock>
    <form id="form-add" class="form-horizontal">
    <div class="panel panel-default" v-for="(addr,idx) in deliAddrs">
        <div class="panel-heading"><b>提货地{{idx+1}}：{{addr.addrName}}</b> {{addr.provinceName}}{{addr.cityName}}{{addr.areaName}}{{addr.detailAddr}}</div>
        <div class="panel-body fixed-table-body">
            <table class="custom-tab table table-bordered">
                <thead>
                    <tr>
                        <th style="width: 9%;">货品编码</th>
                        <th style="width: 9%;">货品名称</th>
                        <th style="width: 9%;">客户单号</th>
                        <th style="width: 8%;">件数</th>
                        <th style="width: 8%;">重量</th>
                        <th style="width: 6%;">体积</th>
                        <th style="width: 9%;">新段提货件数</th>
                        <th style="width: 9%;">新段提货重量</th>
                        <th style="width: 9%;">新段提货体积</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(goods,j) in addr.shippingGoodsList">
                        <td>{{goods.goodsCode}}</td>
                        <td>{{goods.goodsName}}</td>
                        <td>{{goods.custOrderno}}</td>
                        <td>{{goods.num}}</td>
                        <td>{{goods.weight}}</td>
                        <td>{{goods.volume}}</td>
                        <td style="padding: 1px"><input v-model="goods.splitNum" :max="goods.num" :disabled="goods.num == 0" :name="'dn_'+idx+'_'+j" oninput="$.numberUtil.onlyNumber(this)" class="form-control" autocomplete="off" aria-autocomplete="none"></td>
                        <td style="padding: 1px"><input v-model="goods.splitWeight" :max="goods.weight" :disabled="goods.weight == 0" :name="'dw_'+idx+'_'+j" oninput="$.numberUtil.onlyNumber(this)" class="form-control" autocomplete="off" aria-autocomplete="none"></td>
                        <td style="padding: 1px"><input v-model="goods.splitVolume" :max="goods.volume" :disabled="goods.volume == 0" :name="'dv_'+idx+'_'+j" oninput="$.numberUtil.onlyNumber(this)" class="form-control" autocomplete="off" aria-autocomplete="none"></td>

                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="panel panel-info" v-for="(addr,idx) in arriAddrs">
        <div class="panel-heading"><b>到货地{{idx+1}}：{{addr.addrName}}</b> {{addr.provinceName}}{{addr.cityName}}{{addr.areaName}}{{addr.detailAddr}}</div>
        <div class="panel-body fixed-table-body">
            <table class="custom-tab table table-bordered">
                <thead>
                <tr>
                    <th style="width: 9%;">货品编码</th>
                    <th style="width: 9%;">货品名称</th>
                    <th style="width: 9%;">客户单号</th>
                    <th style="width: 8%;">件数</th>
                    <th style="width: 8%;">重量</th>
                    <th style="width: 6%;">体积</th>
                    <th style="width: 9%;">新段到货件数</th>
                    <th style="width: 9%;">新段到货重量</th>
                    <th style="width: 9%;">新段到货体积</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(goods,j) in addr.shippingGoodsList">
                    <td>{{goods.goodsCode}}</td>
                    <td>{{goods.goodsName}}</td>
                    <td>{{goods.custOrderno}}</td>
                    <td>{{goods.num}}</td>
                    <td>{{goods.weight}}</td>
                    <td>{{goods.volume}}</td>
                    <td style="padding: 1px"><input v-model="goods.splitNum" :max="goods.num" :disabled="goods.num == 0" :name="'an_'+idx+'_'+j" oninput="$.numberUtil.onlyNumber(this)" class="form-control" autocomplete="off" aria-autocomplete="none"></td>
                    <td style="padding: 1px"><input v-model="goods.splitWeight" :max="goods.weight" :disabled="goods.weight == 0" :name="'aw_'+idx+'_'+j" oninput="$.numberUtil.onlyNumber(this)" class="form-control" autocomplete="off" aria-autocomplete="none"></td>
                    <td style="padding: 1px"><input v-model="goods.splitVolume" :max="goods.volume" :disabled="goods.volume == 0" :name="'av_'+idx+'_'+j" oninput="$.numberUtil.onlyNumber(this)" class="form-control" autocomplete="off" aria-autocomplete="none"></td>

                </tr>
                </tbody>
            </table>
        </div>
    </div>

        <div class="panel panel-default" v-if="preview.deliAddrs.length > 0 || preview.arriAddrs.length > 0">
            <div class="panel-heading">原始运段</div>
            <div class="panel-body fixed-table-body">
                <div style="display:flex;flex-direction: row;justify-content: space-around;">
                    <div style="width:400px;display: flex;flex-direction: column;justify-content: space-around;">
                        <div v-for="addr in current.deliAddrs">
                            <div class="address" style="margin: 5px">
                                <div>
                                    <span style="font-weight: bold">{{addr.addrName}}</span>
                                    <span style="color: #999">[提货点]</span>
                                    <div style="color: #888">{{addr.provinceName}}{{addr.cityName}}{{addr.areaName}}{{addr.detailAddr}}</div>
                                </div>
                                <div v-for="(goods, j) in addr.shippingGoodsList" style="color:#3b8ab8">
                                    {{goods.custOrderno?(goods.custOrderno + ','):''}}{{goods.goodsName}}
                                    <span v-if="goods.num>0">{{goods.num}}件</span>
                                    <span v-if="goods.weight>0">{{goods.weight}}吨</span>
                                    <span v-if="goods.volume>0">{{goods.volume}}m³</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="display:flex;flex-direction: column;justify-content: space-around;">
                        <span style="font-weight: bold;color: #0e9aef">&gt;&gt;</span>
                    </div>
                    <div style="width:400px;display: flex;flex-direction: column;justify-content: space-around;">
                        <div v-for="addr in current.arriAddrs">
                            <div class="address" style="margin: 5px">
                                <div>
                                    <span style="font-weight: bold">{{addr.addrName}}</span>
                                    <span style="color: #999">[到货点]</span>
                                    <div style="color: #888">{{addr.provinceName}}{{addr.cityName}}{{addr.areaName}}{{addr.detailAddr}}</div>
                                </div>
                                <div v-for="(goods, j) in addr.shippingGoodsList" style="color:#3b8ab8">
                                    {{goods.custOrderno?(goods.custOrderno + ','):''}}{{goods.goodsName}}
                                    <span v-if="goods.num>0">{{goods.num}}件</span>
                                    <span v-if="goods.weight>0">{{goods.weight}}吨</span>
                                    <span v-if="goods.volume>0">{{goods.volume}}m³</span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

    <div class="panel panel-default" v-if="preview.deliAddrs.length > 0 || preview.arriAddrs.length > 0">
        <div class="panel-heading">新运段</div>
        <div class="panel-body fixed-table-body">
            <div style="display:flex;flex-direction: row;justify-content: space-around;">
                <div style="width:400px;display: flex;flex-direction: column;justify-content: space-around;">
                    <div v-for="addr in preview.deliAddrs">
                        <div class="address" style="margin: 5px">
                            <div>
                                <span style="font-weight: bold">{{addr.addrName}}</span>
                                <span style="color: #999">[提货点]</span>
                                <div style="color: #888">{{addr.provinceName}}{{addr.cityName}}{{addr.areaName}}{{addr.detailAddr}}</div>
                            </div>
                            <div v-for="(goods, j) in addr.shippingGoodsList" style="color:#3b8ab8">
                                {{goods.custOrderno?(goods.custOrderno + ','):''}}{{goods.goodsName}}
                                <span v-if="goods.splitNum>0">{{goods.splitNum}}件</span>
                                <span v-if="goods.splitWeight>0">{{goods.splitWeight}}吨</span>
                                <span v-if="goods.splitVolume>0">{{goods.splitVolume}}m³</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="display:flex;flex-direction: column;justify-content: space-around;">
                    <span style="font-weight: bold;color: #0e9aef">&gt;&gt;</span>
                </div>
                <div style="width:400px;display: flex;flex-direction: column;justify-content: space-around;">
                    <div v-for="addr in preview.arriAddrs">
                        <div class="address" style="margin: 5px">
                            <div>
                                <span style="font-weight: bold">{{addr.addrName}}</span>
                                <span style="color: #999">[到货点]</span>
                                <div style="color: #888">{{addr.provinceName}}{{addr.cityName}}{{addr.areaName}}{{addr.detailAddr}}</div>
                            </div>
                            <div v-for="(goods, j) in addr.shippingGoodsList" style="color:#3b8ab8">
                                {{goods.custOrderno?(goods.custOrderno + ','):''}}{{goods.goodsName}}
                                <span v-if="goods.splitNum>0">{{goods.splitNum}}件</span>
                                <span v-if="goods.splitWeight>0">{{goods.splitWeight}}吨</span>
                                <span v-if="goods.splitVolume>0">{{goods.splitVolume}}m³</span>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer" />
<script th:src="@{/js/vue.min.js}"></script>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">
    //是否是车队
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/segment" : ctx + "tms/segment";

    //$(function () {
    //    $('#collapseOne').collapse('show');
    //    $('#collapseTwo').collapse('show');
    //});

    /**
     * 校验
     */
    // $("#form-add").validate({
    //     onkeyup: false,
    //     focusCleanup: true,
    //     rules:{
    //         splitNum: {
    //             required: {
    //                 depends:function(){ //二选一
    //                     return ($('#splitWeight').val().length <= 0);
    //                 }
    //             }
    //         },
    //         splitWeight: {
    //             required:{
    //                 depends:function(){ //二选一
    //                     return ($('#splitNum').val().length <= 0);
    //                 }
    //             }
    //         }
    //     }
    // });

    /**
     * 提交
     */
    function submitHandler() {
        /*var segPackGoodsList = [];
        var splitCount = $("#splitCount").val();
        for (var i = 0; i < splitCount; i++) {
            var splitNum = $("#splitNum_" + i).val();
            var splitWeight = $("#splitWeight_" + i).val();
            var splitVolume = $("#splitVolume_" + i).val();
            var segPackGoodsId = $("#segPackGoodsId_" + i).val();
            var segmentId = $("#segmentId_" + i).val();
            segPackGoodsList.push({num:splitNum,weight:splitWeight, volume: splitVolume,segPackGoodsId:segPackGoodsId,segmentId:segmentId})
        }

        $.ajax({
            url: prefix + "/splitQuantity",
            type: "post",
            dataType: "json",
            data: JSON.stringify(segPackGoodsList),
            contentType : 'application/json;charset=utf-8',
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function(result) {
                if (typeof callback == "function") {
                    callback(result);
                }
                $.operate.successTabCallback(result);
            }
        })*/
        if (!$.validate.form('form-add')) {
            return;
        }
        if (vue.preview.deliAddrs.length == 0 && vue.preview.arriAddrs.length == 0) {
            $.modal.msgError("未拆段，不需要提交");
            return;
        }
        // 校验提到货量是否一致
        for (var k in vue.goodsQuantity) {
            let g = vue.goodsQuantity[k]
            if (g.deli.n != g.arri.n || g.deli.w != g.arri.w || g.deli.v != g.arri.v) {
                $.modal.msgError("“" + k + "”提到货量不一致")
                return;
            }
        }
        if (vue.current.deliAddrs.length == 0 && vue.current.arriAddrs.length == 0) {
            $.modal.msgError("原始运段不能全部拆出")
            return;
        }
        $.modal.confirm("确定提交该拆量方案？", function(){
            let segment = {segmentId:[[${segmentId}]],multipleShippingAddressList:[]};
            let addrs = [].concat(vue.deliAddrs).concat(vue.arriAddrs)
            for (let i = 0; i < addrs.length; i++) {
                let {multipleShippingAddressId,addressType,shippingGoodsList} = addrs[i];
                let goodsList = [];
                for (let j = 0; j < shippingGoodsList.length; j++) {
                    let {multipleShippingGoodsId,splitNum,splitWeight,splitVolume} = shippingGoodsList[j];
                    goodsList.push({multipleShippingGoodsId,splitNum,splitWeight,splitVolume});
                }
                segment.multipleShippingAddressList.push({multipleShippingAddressId,addressType,shippingGoodsList:goodsList})
            }
            $.ajax({
                url: prefix + "/splitQuantityMulti",
                type: "post",
                dataType: "json",
                data: JSON.stringify(segment),
                contentType : 'application/json;charset=utf-8',
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function(result) {
                    if (typeof callback == "function") {
                        callback(result);
                    }
                    $.operate.successTabCallback(result);
                }
            })
        })
    }

    const vue = new Vue({
        el: '#app',
        data() {
            return {
                deliAddrs: [[${deliAddrs}]],
                arriAddrs: [[${arriAddrs}]]
            }
        },
        computed: {
            current () {
                let result = {deliAddrs: [], arriAddrs: []};
                for (let i = 0; i < this.deliAddrs.length; i++) {
                    let newDeliAddr = Object.assign({}, this.deliAddrs[i]);
                    newDeliAddr.shippingGoodsList = []
                    let goodsList = this.deliAddrs[i].shippingGoodsList;
                    for (let j = 0; j < goodsList.length; j++) {
                        let num = new BigNumber(goodsList[j].num).minus(goodsList[j].splitNum || 0).toNumber();
                        let weight = new BigNumber(goodsList[j].weight).minus(goodsList[j].splitWeight || 0).toNumber();
                        let volume = new BigNumber(goodsList[j].volume).minus(goodsList[j].splitVolume || 0).toNumber();
                        if (num || weight || volume) {
                            let g = Object.assign({}, goodsList[j])
                            g.num = num;
                            g.weight = weight;
                            g.volume = volume;
                            newDeliAddr.shippingGoodsList.push(g)
                        }
                    }
                    if (newDeliAddr.shippingGoodsList.length > 0) {
                        result.deliAddrs.push(newDeliAddr);
                    }
                }
                for (let i = 0; i < this.arriAddrs.length; i++) {
                    let newArriAddr = Object.assign({}, this.arriAddrs[i]);
                    newArriAddr.shippingGoodsList = []
                    let goodsList = this.arriAddrs[i].shippingGoodsList;
                    for (let j = 0; j < goodsList.length; j++) {
                        let num = new BigNumber(goodsList[j].num).minus(goodsList[j].splitNum || 0).toNumber();
                        let weight = new BigNumber(goodsList[j].weight).minus(goodsList[j].splitWeight || 0).toNumber();
                        let volume = new BigNumber(goodsList[j].volume).minus(goodsList[j].splitVolume || 0).toNumber();
                        if (num || weight || volume) {
                            let g = Object.assign({}, goodsList[j])
                            g.num = num;
                            g.weight = weight;
                            g.volume = volume;
                            newArriAddr.shippingGoodsList.push(g)
                        }
                    }
                    if (newArriAddr.shippingGoodsList.length > 0) {
                        result.arriAddrs.push(newArriAddr);
                    }
                }
                return result;
            },
            preview () { // 拆出量作为新运段展示
                let result = {deliAddrs: [], arriAddrs: []};
                for (let i = 0; i < this.deliAddrs.length; i++) {
                    let newDeliAddr = Object.assign({}, this.deliAddrs[i]);
                    newDeliAddr.shippingGoodsList = []
                    let goodsList = this.deliAddrs[i].shippingGoodsList;
                    for (let j = 0; j < goodsList.length; j++) {
                        if (goodsList[j].splitNum || goodsList[j].splitWeight || goodsList[j].splitVolume) {
                            newDeliAddr.shippingGoodsList.push(goodsList[j])
                        }
                    }
                    if (newDeliAddr.shippingGoodsList.length > 0) {
                        result.deliAddrs.push(newDeliAddr);
                    }
                }
                for (let i = 0; i < this.arriAddrs.length; i++) {
                    let newArriAddr = Object.assign({}, this.arriAddrs[i]);
                    newArriAddr.shippingGoodsList = []
                    let goodsList = this.arriAddrs[i].shippingGoodsList;
                    for (let j = 0; j < goodsList.length; j++) {
                        if (goodsList[j].splitNum || goodsList[j].splitWeight || goodsList[j].splitVolume) {
                            newArriAddr.shippingGoodsList.push(goodsList[j])
                        }
                    }
                    if (newArriAddr.shippingGoodsList.length > 0) {
                        result.arriAddrs.push(newArriAddr);
                    }
                }
                return result;
            },
            goodsQuantity () {
                let result = {}
                // 统计旧段或新段的所有货品的提到货量是否一致
                for (let i = 0; i < this.deliAddrs.length; i++) {
                    let goodsList = this.deliAddrs[i].shippingGoodsList;
                    for (let j = 0; j < goodsList.length; j++) {
                        let goodsKey = goodsList[j].custOrderno + "," + goodsList[j].goodsName; // 客户单号 +,+ 货品名称
                        let g = result[goodsKey]
                        if (!g) {g = {deli:{n:0,w:0,v:0},arri:{n:0,w:0,v:0}};result[goodsKey] = g}
                        let deli = g['deli'];
                        deli.n = new BigNumber(deli.n).plus(goodsList[j].num).minus(goodsList[j].splitNum||0).toNumber();
                        deli.w = new BigNumber(deli.w).plus(goodsList[j].weight).minus(goodsList[j].splitWeight||0).toNumber();
                        deli.v = new BigNumber(deli.v).plus(goodsList[j].volume).minus(goodsList[j].splitVolume||0).toNumber();
                    }
                }
                for (let i = 0; i < this.arriAddrs.length; i++) {
                    let goodsList = this.arriAddrs[i].shippingGoodsList;
                    for (let j = 0; j < goodsList.length; j++) {
                        let goodsKey = goodsList[j].custOrderno + "," + goodsList[j].goodsName; // 客户单号 +,+ 货品名称
                        let g = result[goodsKey]
                        if (!g) {g = {deli:{n:0,w:0,v:0},arri:{n:0,w:0,v:0}};result[goodsKey] = g}
                        let arri = g['arri'];
                        arri.n = new BigNumber(arri.n).plus(goodsList[j].num).minus(goodsList[j].splitNum||0).toNumber();
                        arri.w = new BigNumber(arri.w).plus(goodsList[j].weight).minus(goodsList[j].splitWeight||0).toNumber();
                        arri.v = new BigNumber(arri.v).plus(goodsList[j].volume).minus(goodsList[j].splitVolume||0).toNumber();
                    }
                }
                return result;
            }
        }
    })
</script>
</body>

</html>