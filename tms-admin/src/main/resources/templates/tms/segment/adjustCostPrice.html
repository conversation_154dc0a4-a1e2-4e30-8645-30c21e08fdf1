<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('调整运费指导价')"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            background-color: #f4f6f9;
            font-size: 14px;
        }
        .card-container {
            max-width: 600px;
            margin: 15px auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card-body {
            background-color: #ffffff;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }

    </style>
</head>
<body>
<div class="container">
    <div class="card-container">
        <div class="card-body">
            <form id="form-guiding-price" class="form-horizontal" novalidate="novalidate">
                <!-- Hidden input for segmentId -->
                <input name="segmentId" th:value="${segmentId}" type="hidden">
                <!-- Guiding Price Input Section -->
                <div class="form-group price-input-container">
                    <label>新成本价</label>
                    <input type="text" class="form-control" id="costPrice" name="costPrice"
                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);" th:value="${segment.costPrice}"
                           placeholder="输入新指导价">
                </div>
                <div class="form-group price-input-container">
                    <label>成本票点</label>
                    <select id="costBillingType" name="costBillingType" class="form-control">
                        <option value="">请选择</option>
                        <option th:each="dict : ${@dict.getType('billing_type')}" th:text="${dict.dictLabel}"
                                th:value="${dict.dictValue}" th:attr="data-rate=${dict.numVal1}"
                                th:selected="${dict.dictValue == segment.costBillingType}"
                        ></option>
                    </select>
                </div>

            </form>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    $(function () {
    });

    /**
     * 提交处理函数
     */
    function submitHandler() {
        var newPrice = $('#costPrice').val();

        // 简单验证
        if (!newPrice) {
            layer.msg('请输入新的成本价', {icon: 2});
            return false;
        }

        let costBillingType = $("#costBillingType").val();
        if(!costBillingType){
            layer.msg('请选择成本票点', {icon: 2});
            return false;
        }

        // 序列化表单数据
        var data = $('#form-guiding-price').serialize();
        // 发送保存请求
        $.operate.save(ctx + "tms/segment/adjust_cost_price", data);
    }
</script>
</body>
</html>