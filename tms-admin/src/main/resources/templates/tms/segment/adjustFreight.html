<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('调整运费')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
        color: #808080;
    }
    .flex_left{
        width: 110px;
        line-height: 30px;
        text-align: right;
        color: #333333 !important;
    }
    .fLeft{
        width: 80px;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .lih30{
        line-height: 30px;
    }
    /*.row .form-group label[class*='col-'] {*/
    /*    text-align: right;*/
    /*    line-height: 30px;*/
    /*    margin-bottom: 0;*/
    /*}*/
    .addbtn{
        width: 28px;
        text-align: center;
        color: #fff;
        background: #1ab394;
        line-height: 26px;
        border-radius: 5px;
        cursor: pointer;
    }
    .addGoods{
        color: #1ab394;
        line-height: 30px;
        cursor: pointer;
    }
    .wap_content{
        padding: 10px 10px;
        border: 1px #eee solid;
        border-radius: 3px;

    }
    .line20{
        line-height: 20px;
    }
    .fcff6{
        color: #ff6c00;
    }
    .hisbtn{
        line-height: 25px;
        border-radius: 20px;
        width: 140px;
        text-align: center;
        color: #1ab394;
        border: 1px #1ab394 solid;
        cursor: pointer;
    }
    .eye{
        width: 20px;
        height: 20px;
        background: #1ab394 url("../../img/eyes.png") no-repeat center;
        background-size: 20px 20px;
        border-radius: 50%;
        /*display: inline-block;*/
    }
    .disin{
        display: inline-block;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .toop{
        width: 20px;
        height: 20px;
        text-align: center;
        background: #7f7f7f;
        border-radius: 50%;
        display: inline-block;
        color: #fff;
        line-height: 20px;
        cursor: pointer;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #EBF5FC;
    }
    .sm{
        background: #fff6f6;
        padding: 5px 10px;
        margin: 0 -5px 5px;
    }
    .sm_icon{
        width: 18px;
        height: 18px;
        background: #ff1f1f;
        color: #fff;
        border-radius: 50%;
        display: inline-block;
        line-height: 18px;
        text-align: center;
        font-size: 12px;
    }
    .sm_text{
        color: #ff3636;
        display: inline-block;
        margin-left: 10px;
        line-height: 20px;
    }
    .toopT{
        position: absolute;
        top: 4px;
        right: 10px;
        background-color: #1a1a1a;
        z-index: 3;
    }
    .form-control,.input-group-addon,.table-bordered td, .table-bordered th,button[disabled], html input[disabled],.table>thead>tr>th,
    .panel,.panel-body,.table-bordered{
        border-color: #A6A6A6 !important;
    }
    .toText{
        display:inline-block;
        width: 32px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        /* padding: 8px 4px; */
        border-radius: 5px;
        color: #fff;
        vertical-align: middle;
    }
    .ff6c{
        color:#FF6C00;
    }
    .dInput{
        border-bottom: 1px solid #A6A6A6;
        padding-bottom: 10px;
        padding-left: 5px;
        padding-right: 5px;
    }
    .dInput:first-child{
        padding-top: 10px;
    }
    .dInput:last-child{
        border: 0;
    }
    .dInput+.dInput{
        margin-top: 10px;
    }
    .dInput input[disabled]{
        border: 0;
        background-color: #ffffff;
    }
    .form-control,.input-group-addon{
        border-radius: 4px;
    }
    .table>tbody>tr>td{
        border-right: 1px solid #A6A6A6;
    }
    .table>tbody>tr>td:last-child{
        border-right:0
    }
    .panel-group .panel{
        border-radius: 0;
    }
    .fw{
        font-weight: bold;
    }

    .borderB{
        position: relative;
    }
    .borderB::after{
        content:" ";
        position: absolute;
        width: calc(100% - 40px);
        height: 0;
        border-bottom:#EBECEF 2px solid;
        top: -15px;
        left: 20px;
    }
    .mab0{
        margin-bottom: 0;
        width: 5em;
    }
    .file-drop-zone{
        overflow: auto;
    }
    .selectSpan{
        display: inline-block;
        width: 30%;
    }
</style>
<body>
<div class="form-content">
    <form id="form-dispatch-add" class="form-horizontal" novalidate="novalidate">
        <input name="segmentId" th:value="${segmentId}" type="hidden">

        <div class="panel-group">
            <div class="row no-gutter">
                <div class="col-xs-5">
                    <div class="flex">
                        <label class="flex_left " >预付油卡：</label>
                        <div class="flex_right">
                            <div class="input-group">
                                <input name="payDetailList[1].transFeeCount" id="transFeeCount_1" type="text" class="form-control oilCard" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off">
                                <input name="payDetailList[1].costTypeFreight" id="costTypeFreight_1" type="hidden" th:value="1">
                                <span class="input-group-addon">元</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xs-7">
                    <div class="flex">
                        <label class="flex_left " >预付现金：</label>
                        <div class="flex_right">
                            <div class="row" style="margin: 0 0 0 -5px;">
                                <div class="col-xs-10">
                                    <div class="input-group">
                                        <input name="payDetailList[0].transFeeCount" id="transFeeCount_0" type="text" class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off">
                                        <input name="payDetailList[0].costTypeFreight" id="costTypeFreight_0" type="hidden" th:value="0">
                                        <span class="input-group-addon">元</span>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>

            <div class="row no-gutter">
                <div class="col-xs-5">
                    <div class="flex">
                        <label class="flex_left " >到付油卡：</label>
                        <div class="flex_right">
                            <div class="input-group">
                                <input name="payDetailList[3].transFeeCount" id="transFeeCount_3" type="text" class="form-control oilCard" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off">
                                <input name="payDetailList[3].costTypeFreight" id="costTypeFreight_3" type="hidden" th:value="3">
                                <span class="input-group-addon">元</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xs-7">
                    <div class="flex">
                        <label class="flex_left " >到付现金：</label>
                        <div class="flex_right">
                            <div class="row" style="margin: 0 0 0 -5px;">
                                <div class="col-xs-10">
                                    <div class="input-group">
                                        <input  name="payDetailList[2].transFeeCount" id="transFeeCount_2" type="text" class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off">
                                        <input name="payDetailList[2].costTypeFreight" id="costTypeFreight_2" type="hidden" th:value="2">
                                        <span class="input-group-addon">元</span>
                                    </div>
                                </div>
                            </div>
                           
                        </div>
                    </div>
                </div>
            </div>

            <div class="row no-gutter">
                <div class="col-xs-5">
                    <div class="flex">
                        <label class="flex_left " >回付油卡：</label>
                        <div class="flex_right">
                            <div class="input-group">
                                <input name="payDetailList[5].transFeeCount" id="transFeeCount_5" type="text" class="form-control oilCard" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off">
                                <input name="payDetailList[5].costTypeFreight" id="costTypeFreight_5" type="hidden" th:value="5">
                                <span class="input-group-addon">元</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xs-7">
                    <div class="flex">
                        <label class="flex_left " >回付现金：</label>
                        <div class="flex_right">
                            <div class="row" style="margin: 0 0 0 -5px;">
                                <div class="col-xs-10">
                                    <div class="input-group">
                                        <input  name="payDetailList[4].transFeeCount" id="transFeeCount_4" type="text" class="form-control" oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off">
                                        <input name="payDetailList[4].costTypeFreight" id="costTypeFreight_4" type="hidden" th:value="4">
                                        <span class="input-group-addon">元</span>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>


            <!-- 添加分割线和在途费部分 -->
            <div class="row no-gutter">
                <div class="col-xs-12">
                    <hr style="border-top: 1px solid #ddd; margin: 20px 0;">
                    <h4 style="margin-bottom: 15px; color: #333; font-weight: bold; padding-left: 15px;">在途费用</h4>
                </div>
            </div>

            <!-- 在途费用区域 - 与上方风格一致 -->
            <div class="row no-gutter">
                <div class="col-xs-5">
                    <div class="flex">
                        <label class="flex_left">装卸费：</label>
                        <div class="flex_right">
                            <div class="input-group">
                                <input name="zxf" th:value="${zxf}" type="text" class="form-control"
                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off">
                                <span class="input-group-addon">元</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xs-7">
                    <div class="flex">
                        <label class="flex_left">送货费：</label>
                        <div class="flex_right">
                            <div class="row" style="margin: 0 0 0 -5px;">
                                <div class="col-xs-10">
                                    <div class="input-group">
                                        <input name="shf" th:value="${shf}" type="text" class="form-control"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off">
                                        <span class="input-group-addon">元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row no-gutter">
                <div class="col-xs-5">
                    <div class="flex">
                        <label class="flex_left">枕木费：</label>
                        <div class="flex_right">
                            <div class="input-group">
                                <input name="zmf" th:value="${zmf}" type="text" class="form-control"
                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off">
                                <span class="input-group-addon">元</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xs-7">
                    <div class="flex">
                        <label class="flex_left">报关费：</label>
                        <div class="flex_right">
                            <div class="row" style="margin: 0 0 0 -5px;">
                                <div class="col-xs-10">
                                    <div class="input-group">
                                        <input name="bgf" th:value="${bgf}" type="text" class="form-control"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off">
                                        <span class="input-group-addon">元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row no-gutter">
                <div class="col-xs-5">
                    <div class="flex">
                        <label class="flex_left">提货费：</label>
                        <div class="flex_right">
                            <div class="input-group">
                                <input name="thf" th:value="${thf}" type="text" class="form-control"
                                       oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off">
                                <span class="input-group-addon">元</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xs-7">
                    <div class="flex">
                        <label class="flex_left">包车直送：</label>
                        <div class="flex_right">
                            <div class="row" style="margin: 0 0 0 -5px;">
                                <div class="col-xs-10">
                                    <div class="input-group">
                                        <input name="bczs" th:value="${bczs}" type="text" class="form-control"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off">
                                        <span class="input-group-addon">元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </form>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>


<script th:inline="javascript">


    $(function () {
        let payDetailList=[[${payDetailList}]];

        if(payDetailList.length>0){
            if(payDetailList[0].oilCardNumber){
                $("#oilCardNumber").val(payDetailList[0].oilCardNumber)
            }
            payDetailList.forEach(item => {
                $('#transFeeCount_'+item.costTypeFreight).val(item.transFeeCount);
            });
        }
        
    });



    /**
     * 提交
     */
    function submitHandler() {
        let data=$('#form-dispatch-add').serialize();
        $.operate.save(ctx + "tms/segment/change_freight", data);
    }
    

</script>

</body>

</html>
