<!doctype html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <meta charset="utf-8">
    <link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <link rel="stylesheet" href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css" />

    <title>车辆定位</title>
    <style>
        html,
        body,
        #container {
            width: 100%;
            height: 100%;
        }
        .amap-marker-label{
            border: 0;
            background-color: transparent;
        }
        .info{
            position: relative;
            top: 0;
            right: 0;
            min-width: 0;
        }
    </style>
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode: "72638f3422929c02ed0c705dce189281",
        };
    </script>
    <script type="text/javascript" src='https://webapi.amap.com/maps?v=1.4.15&key=2e974bb54660966a52ff5f899d225b21&plugin=AMap.Driving'></script>
    <script src="https://a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js"></script>
</head>
<body>
<div id="container"></div>
</div>
<script th:src="@{/js/jquery.min.js}"></script>
<script th:inline="javascript">
    var ctx = [[@{/}]];
    var addrMap = [[${addrMap}]];

    if (addrMap != null) {
        var lon = addrMap.lngitude;
            var lat = addrMap.latitude;
            var map = new AMap.Map("container", {
                resizeEnable: true, //是否监控地图容器尺寸变化
            zoom: 11, //初始化地图层级
            center: [lon, lat] //初始化地图中心点
        });
        var marker = new AMap.Marker({
            position: new AMap.LngLat(lon, lat),
            icon: ctx + 'img/car.png',
            //icon: '//vdata.amap.com/icons/b18/1/2.png',
            offset: new AMap.Pixel(-13, -30),
        });

        marker.setMap(map);

        marker.setLabel({
            offset: new AMap.Pixel(10, 10),  //设置文本标注偏移量
            content: "<div class='info'><p>车牌号：" + addrMap.carNo +"</p>" +
                "<p>地址："+addrMap.address+"</p>"+
                "<p>经度："+addrMap.latitude+"</p>"+
                "<p>纬度："+addrMap.lngitude+"</p>"+
                "<p>定位时间："+addrMap.getlocationtime+"</p>"+
                "<p>里程(km)："+addrMap.mile+"</p>"+
                "<p>速度(km/h)："+addrMap.speed+"</p>"+
            "</div>", //设置文本标注内容
            direction: 'right' //设置文本标注方位
        });

    } else {
        var map = new AMap.Map("container", {
            resizeEnable: true, //是否监控地图容器尺寸变化
            zoom: 11, //初始化地图层级
        });
        log.error('暂无车辆信息');
    }



</script>
</body>
</html>