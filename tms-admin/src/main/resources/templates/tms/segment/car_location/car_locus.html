<!doctype html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <meta charset="utf-8">
    <link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>物流跟踪</title>
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode: "72638f3422929c02ed0c705dce189281",
        };
    </script>
    <script type="text/javascript" src='https://webapi.amap.com/maps?v=1.4.15&key=2e974bb54660966a52ff5f899d225b21&plugin=AMap.Driving,AMap.Geocoder'></script>
    <style>
        html,
        body,
        #container {
            width: 100%;
            height: 100%;
        }
        #panel {
            position: fixed;
            background-color: white;
            max-height: 90%;
            overflow-y: auto;
            top: 10px;
            right: 10px;
            width: 280px;
        }
        #panel .amap-lib-driving {
            border-radius: 4px;
            overflow: hidden;
        }
        .info{
            position: relative;
            top: 0;
            right: 0;
            min-width: 0;
        }
        .amap-marker-label{
            border: 0;
            background-color: transparent;
        }
    </style>
    <link rel="stylesheet" href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css" />
    <script src="https://a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js"></script>
</head>
<body>
<div id="container"></div>
<div id="panel"></div>
</div>
<script th:src="@{/js/jquery.min.js}"></script>
<script th:inline="javascript">

    // var pointMap = [[${pointMap}]];
    // var deliAddr = pointMap.deliAddr;//起始地址
    // var arriAddr = pointMap.arriAddr;//到达地址
    var carLocusList = [[${carLocusList}]];
    console.log(carLocusList)

    //初始化地图
    var map = new AMap.Map("container", {
        resizeEnable: true
    });

    //初始化文字转坐标
    var geocoder = new AMap.Geocoder({});

    //起始ICON
    var startIcon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(25, 34),
        // 图标的取图地址
        image: '//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png',
        // 图标所用图片大小
        imageSize: new AMap.Size(135, 40),
        // 图标取图偏移量
        imageOffset: new AMap.Pixel(-9, -3)
    });
    //起始地址获取经纬度并标点
    var deliMarker = new AMap.Marker({
        icon: startIcon,
        offset: new AMap.Pixel(-13, -30)
    });
    // geocoder.getLocation(deliAddr, function(status, result) {
    //     if (status === 'complete'&&result.geocodes.length) {
    //         var lnglat = result.geocodes[0].location;
    //         deliMarker.setPosition(lnglat);
    //         map.add(deliMarker);
    //     }else{
    //         log.error('根据地址查询位置失败');
    //     }
    // });
    // 创建一个 icon
    var endIcon = new AMap.Icon({
        size: new AMap.Size(25, 34),
        image: '//a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png',
        imageSize: new AMap.Size(135, 40),
        imageOffset: new AMap.Pixel(-95, -3)
    });
    //到达地址获取经纬度并标点
    var arriMarker = new AMap.Marker({
        icon: endIcon,
        offset: new AMap.Pixel(-13, -30)
    });
    // geocoder.getLocation(arriAddr, function(status, result) {
    //     if (status === 'complete'&&result.geocodes.length) {
    //         var lnglat = result.geocodes[0].location;
    //         arriMarker.setPosition(lnglat);
    //         map.add(arriMarker);
    //         map.setFitView(deliMarker);
    //         //map.setFitView(arriMarker);
    //     }else{
    //         log.error('根据地址查询位置失败');
    //     }
    // });

    if( carLocusList.length > 0 ){
        //取出车辆轨迹点
        var path = [];
        for(var i = 1 ; i < carLocusList.length-1 ; i++){
            path.push(new AMap.LngLat(carLocusList[i].lngitude, carLocusList[i].latitude));
        }

        //车辆轨迹图
        var map = new AMap.Map("container", {});
        var drivringOptions = {
            map: map,
            hideMarkers:true,
            panel:'panel'
        };
        var driving = new AMap.Driving(drivringOptions);
        //线路
        driving.search(new AMap.LngLat(carLocusList[0].lngitude, carLocusList[0].latitude),
            new AMap.LngLat(carLocusList[carLocusList.length-1].lngitude, carLocusList[carLocusList.length-1].latitude),
            path, function(status, result) {
                //  result 即是对应的驾车导航信息，相关数据结构文档请参考  https://lbs.amap.com/api/javascript-api/reference/route-search#m_DrivingResult
                if (status === 'complete') {
                    log.success('绘制货车路线完成')
                } else {
                    log.error('获取货车规划数据失败')
                }
            });

        //取出最后一个点作为车辆位置
        var i = carLocusList.length - 1;
        var lon = carLocusList[i].lngitude;
        var lat = carLocusList[i].latitude;
        var marker = new AMap.Marker({
            position: new AMap.LngLat(lon,lat),
            icon: '/img/car.png',
            offset: new AMap.Pixel(-13, -30)
        });
        map.add(marker);
    }
</script>
</body>
</html>