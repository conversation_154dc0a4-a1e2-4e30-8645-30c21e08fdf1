<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车辆定位')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-goods-add" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">车牌号：</label>
                                    <div class="col-sm-8">
                                        <input name="carno" id="carno" class="form-control valid" th:placeholder="请输入车牌号"
                                               type="text">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-2">时间范围：</label>
                                    <div class="col-sm-8">
                                        <input type="text" style="width: 45%; float: left;" class="form-control"
                                               id="startDate"  placeholder="开始时间">
                                        <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                        <input type="text" style="width: 45%; float: left;" class="form-control"
                                               id="endDate"   placeholder="结束时间">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <a class="btn btn-warning btn-rounded btn-xs" onclick="searchCarLocation()">
                                        <i class="fa fa-search"></i>&nbsp;车辆轨迹</a>
                                    <a class="btn btn-info btn-rounded btn-xs" onclick="searchCarLocation2()">
                                        <i class="fa fa-search"></i>&nbsp;车辆定位</a>
                                    <a class="btn btn-success btn-rounded btn-xs" onclick="resultExport()">
                                        <i class="fa fa-download"></i>&nbsp;结果导出</a>
                                </div>
                            </div>

                        </div>


                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">解析结果</a>
                    </h4>
                </div>
                <div  class="panel-collapse collapse in">
                    <div class="panel-body">

                        <div class="row">
                            <div class="fixed-table-body" style="margin: 10px 10px 10px 10px">
                                <table border="0" id="infoTabThree" class="custom-tab table"style= "border:1px solid #e7eaec;" >
                                    <thead>
                                    <tr style="height: 30px">
                                        <th style="width: 10%;">车牌号</th>
                                        <th style="width: 15%;">定位时间</th>
                                        <th style="width: 10%;">车速</th>
                                        <th style="width: 25%;">位置</th>
                                        <th style="width: 16.6%;">经纬度</th>
                                    </tr>

                                    </thead>
                                    <tbody id="resTr">

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script type="text/javascript">

    $(function () {
        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
    });

    /**
     * 查询车辆定位
     */
    function searchCarLocation(){
        //车牌号
        var carno =  $("#carno").val();
        if($.common.isEmpty(carno)){
            $.modal.alertWarning("请输入车牌号！");
            return;
        }
        var endDate = $("#endDate").val();
        var startDate = $("#startDate").val();

        $.ajax({
            url : ctx + "tms/carLocation/getCarLocusData?carno=" + carno + "&startDate=" + startDate + "&endDate=" +
                endDate,
            type: "post",
            dataType: "json",
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
                $.modal.disable();
            },
            success: function(result) {
                if(result.code == web_status.SUCCESS){
                    $.modal.closeLoading();
                    console.log(result);
                    let data = result.data;
                    if(data == null || data.length == 0){
                        $.modal.alertWarning("未查询到车辆轨迹");
                        return;
                    }
                    let html = '';
                    for(let i = 0; i < data.length; i++){
                        let item = data[i];
                         html = html+ `
                         <tr>
                            <td style="border-left: 1px solid #e7eaec;  border-right: 1px solid #e7eaec;">`+item.carNo+`</td>
                            <td style="border-left: 1px solid #e7eaec;  border-right: 1px solid #e7eaec;">`+item.time+`</td>
                            <td style="border-left: 1px solid #e7eaec;  border-right: 1px solid #e7eaec;">`+item.sped+`km/h</td>
                            <td style="border-left: 1px solid #e7eaec;  border-right: 1px solid #e7eaec;">`+item.address+`</td>
                            <td style="border-left: 1px solid #e7eaec;  border-right: 1px solid #e7eaec;">`+item.lon+`,`+item.lat+`</td>
                         </tr>
                    `;
                    }
                    $("#resTr").html(html)
                }else{
                    $.modal.closeLoading();
                    $.modal.alertWarning("未查询到车辆轨迹");
                }

            },error: function(){
                $.modal.closeLoading();
                $.modal.alertWarning("未查询到车辆轨迹");
            }

        });

    }



    function searchCarLocation2() {
        //车牌号
        var carno =  $("#carno").val();
        if($.common.isEmpty(carno)){
            $.modal.alertWarning("请输入车牌号！");
            return;
        }
        var url = ctx + "business/historyPrice/location?carNo="+carno;
        $.modal.open("车辆定位", url);
    }

    function resultExport(){

        $.modal.confirm("即将导出，是否继续？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            let trArr = $("#resTr").find("tr");
            let dataArr = [];
            for (let i = 0; i < trArr.length; i++) {
                let tr = trArr.get(i);
                let carNo = $(tr).find("td").get(0).innerHTML;
                let time = $(tr).find("td").get(1).innerHTML;
                let sped = $(tr).find("td").get(2).innerHTML;
                let address = $(tr).find("td").get(3).innerHTML;
                let lonlat = $(tr).find("td").get(4).innerHTML;


                let data = {
                    "carNo": carNo,
                    "time": time,
                    "sped": sped,
                    "address": address,
                    "lonlat": lonlat
                }
                dataArr.push(data);
            }
            $.ajax({
                type: "post",
                dataType: "json",
                contentType: "application/json",
                data: JSON.stringify({"carLocusAnalyseList": dataArr}),
                url: ctx + "addressTools/exportData1",
                success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                }
            });
        });
    }
</script>
</body>

</html>