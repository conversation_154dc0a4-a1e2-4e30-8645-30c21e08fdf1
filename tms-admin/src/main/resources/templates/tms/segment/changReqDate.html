<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('要求提货|到货日期调整')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <link rel="stylesheet" href="/layui/css/layui.css">
</head>
<style>
    .form-content {
        padding: 30px 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0,0,0,0.05);
    }

    /* 新日期选择区域 */
    .new-dates-section {
        background: linear-gradient(135deg, #f6f8ff 0%, #f0f4ff 100%);
        padding: 25px;
        border-radius: 12px;
        margin-bottom: 30px;
        position: relative;
    }

    .section-title {
        color: #2d3748;
        font-size: 15px;
        margin-bottom: 20px;
        font-weight: 500;
        text-align: center;
    }

    .date-range-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        position: relative;
    }

    .date-input-group {
        position: relative;
        flex: 1;
        /*max-width: 180px;*/
    }

    .date-input-group .layui-input {
        height: 40px;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 14px;
        transition: all 0.3s ease;
        background: white;
    }

    .date-input-group .layui-input:hover {
        border-color: #cbd5e0;
    }

    .date-input-group .layui-input:focus {
        border-color: #4299e1;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
    }

    .date-separator {
        font-size: 18px;
        color: #718096;
        margin: 0 5px;
        font-weight: 500;
    }

    /* 原日期显示区域 */
    .original-dates {
        background: #fbfbfd;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        border: 1px solid #edf2f7;
    }

    .original-dates .title {
        color: #4a5568;
        font-size: 14px;
        margin-bottom: 12px;
        font-weight: 500;
    }

    .original-dates .dates {
        color: #2d3748;
        font-size: 15px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
    }

    .original-dates .date-value {
        /*background: white;*/
        padding: 8px 16px;
        border-radius: 6px;
        /*border: 1px solid #e2e8f0;*/
        /*min-width: 120px;*/
    }

    /* 错误提示样式 */
    .error-msg {
        color: #e53e3e;
        font-size: 12px;
        text-align: center;
        margin-top: 8px;
        display: none;
        background: #fff5f5;
        padding: 6px 12px;
        border-radius: 4px;
        border: 1px solid #fed7d7;
    }

    /* 标签样式 */
    .input-label {
        position: absolute;
        top: -8px;
        left: 12px;
        background: white;
        padding: 0 4px;
        font-size: 12px;
        color: #718096;
        z-index: 1;
    }

    /* 日期图标 */
    .date-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #a0aec0;
        pointer-events: none;
    }
</style>
<body>
<div class="form-content">
    <form id="form-receive-add" class="form-horizontal">
        <input name="segmentIds" id="segmentIds" type="hidden" th:value="${segmentIds}">

        <!-- 新日期选择区域 -->
        <div class="new-dates-section">
            <div class="section-title">请选择新的提货/到货日期</div>
            <div class="date-range-container">
                <div class="date-input-group">
                    <span class="input-label">提货日期</span>
                    <input class="layui-input" type="text" name="reqDeliDate" id="reqDeliDate" readonly required lay-verify="required" placeholder="请选择">
                    <i class="layui-icon layui-icon-date date-icon"></i>
                </div>
                <span class="date-separator">~</span>
                <div class="date-input-group">
                    <span class="input-label">到货日期</span>
                    <input class="layui-input" type="text" name="reqArriDate" id="reqArriDate" readonly required lay-verify="required" placeholder="请选择">
                    <i class="layui-icon layui-icon-date date-icon"></i>
                </div>
            </div>
            <div class="error-msg" id="deliDateError">新提货日期不能早于原提货日期</div>
            <div class="error-msg" id="arriDateError">到货日期不能早于提货日期</div>
        </div>

        <!-- 原日期显示区域 -->
        <div class="original-dates">
            <div class="title">原要求日期</div>
            <div class="dates">
                <span class="date-value" th:text="${reqDeliDate}">2024-01-01</span>
                <span class="date-separator">~</span>
                <span class="date-value" th:text="${reqArriDate}">2024-01-02</span>
            </div>
        </div>
    </form>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script src="/layui/layui.js"></script>
<script th:inline="javascript">
    let segmentIds = [[${segmentIds}]];
    let originalDeliDate = [[${reqDeliDate}]];
    let originalArriDate = [[${reqArriDate}]];

    layui.use(['laydate', 'layer', 'form'], function(){
        var laydate = layui.laydate;
        var layer = layui.layer;
        var form = layui.form;

        // 初始化提货日期选择器
        laydate.render({
            elem: '#reqDeliDate',
            id: '#reqDeliDate',
            type: 'datetime',
            format: 'yyyy-MM-dd',
            trigger: 'click',
            min: originalDeliDate,
            // theme: '#4299e1',
            calendar: true,
            fullPanel: true,
            ready: function (date) {
                let inst = laydate.getInst("#reqDeliDate");
                let index = inst.config.index;

                var styleElement = $("<style>");
                // 在<style>元素中添加你的样式
                styleElement.text(".layui-laydate {width: 385px !important;} .layui-laydate-header{width: 72%;} .layui-laydate-content > ul {width: 20% !important;} .laydate-time-list > li {width: 100% !important;} .laydate-time-list > li:nth-last-child(2), .laydate-time-list > li:last-child {display: none;}");

                // 将<style>元素添加到指定的元素中
                $('#layui-laydate' + index).append(styleElement);
            },
            done: function(value, date) {
                // 更新到货日期选择器的最小值
                laydate.render({
                    elem: '#reqArriDate',
                    type: 'datetime',
                    format: 'yyyy-MM-dd',
                    trigger: 'click',
                    min: value,
                    // theme: '#4299e1',
                    calendar: true,
                    done: function(value, date) {
                        validateDates();
                    }
                });
                validateDates();

                // 如果到货日期早于新的提货日期，清空到货日期
                let arriDate = $('#reqArriDate').val();
                if (arriDate && new Date(arriDate) < new Date(value)) {
                    $('#reqArriDate').val('');
                }
            }
        });

        // 初始化到货日期选择器
        laydate.render({
            elem: '#reqArriDate',
            type: 'datetime',
            format: 'yyyy-MM-dd',
            trigger: 'click',
            // theme: '#4299e1',
            calendar: true,
            done: function(value, date) {
                validateDates();
            }
        });
    });

    // 日期验证
    function validateDates() {
        let isValid = true;
        let newDeliDate = $('#reqDeliDate').val();
        let newArriDate = $('#reqArriDate').val();

        if (!newDeliDate || !newArriDate) {
            return false;
        }

        let newDeliDateTime = new Date(newDeliDate);
        let newArriDateTime = new Date(newArriDate);
        let origDeliDateTime = new Date(originalDeliDate);

        // 验证新提货日期不能早于原提货日期
        if (newDeliDateTime < origDeliDateTime) {
            $('#deliDateError').fadeIn();
            isValid = false;
        } else {
            $('#deliDateError').fadeOut();
        }

        // 验证到货日期不能早于提货日期
        if (newArriDateTime < newDeliDateTime) {
            $('#arriDateError').fadeIn();
            isValid = false;
        } else {
            $('#arriDateError').fadeOut();
        }

        return isValid;
    }

    //提交处理
    function submitHandler(index, layero) {
        if (!validateDates()) {
            layer.msg('请检查日期输入是否正确', {
                icon: 2,
                skin: 'layer-ext-moon'
            });
            return;
        }

        if ($.validate.form()) {
            let reqDeliDate = $("#reqDeliDate").val();
            let reqArriDate = $("#reqArriDate").val();

            if (!reqDeliDate || !reqArriDate) {
                layer.msg('请选择日期', {
                    icon: 2,
                    skin: 'layer-ext-moon'
                });
                return;
            }

            let data = {
                segmentIds: segmentIds,
                reqDeliDate: reqDeliDate,
                reqArriDate: reqArriDate
            }

            $.ajax({
                url: ctx + "tms/segment/changReqDate",
                type: "post",
                dataType: "json",
                data: data,
                success: function (result) {
                    if (result.code === 0) {
                        parent.$.btTable.bootstrapTable('refresh', {
                            silent: true
                        });
                        $.modal.close();
                        parent.$.modal.msgSuccess(result.msg)
                    } else {
                        $.modal.msgError(result.msg);
                    }
                }
            });
        }
    }
</script>
</body>
</html>