<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('拆量')" />

</head>

<body>
<div class="form-content">
    <form id="form-split_quantity-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">


            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">拆量</a>

                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">

                        <div class="fixed-table-body">
                            <table id="infoTab" class="custom-tab table" border="0">
                                <thead>
                                <tr>
                                    <th style="width: 9%;">货品编码</th>
                                    <th style="width: 9%;">货品名称</th>
                                    <th style="width: 9%;">客户单号</th>
                                    <th style="width: 8%;">件数</th>
                                    <th style="width: 8%;">重量</th>
                                    <th style="width: 6%;">体积</th>
                                    <th style="width: 9%;">拆分件数</th>
                                    <th style="width: 9%;">拆分重量</th>
                                    <th style="width: 9%;">拆分体积</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="segPackGoods,segPackGoodsStat : ${segPackGoodsList}">
                                    <input type="hidden" id="splitCount" th:value="${segPackGoodsStat.size}">
                                    <td>
                                        <label style="float:left;" th:text="${segPackGoods.goodsCode}"></label>
                                        <input th:id="|segPackGoodsId_${segPackGoodsStat.index}|" name="segPackGoodsId" th:value="${segPackGoods.segPackGoodsId}" type="hidden">
                                        <input th:id="|segmentId_${segPackGoodsStat.index}|" name="segmentId" th:value="${segPackGoods.segmentId}" type="hidden">
                                    </td>
                                    <td><label style="float:left;" th:text="${segPackGoods.goodsName}"></label></td>
                                    <td><label style="float:left;" th:text="${segPackGoods.custOrderno}"></label></td>
                                    <td><label style="float:left;" th:text="${segPackGoods.num}"></label></td>
                                    <td><label style="float:left;" th:text="${segPackGoods.weight}"></label></td>
                                    <td><label style="float:left;" th:text="${segPackGoods.volume}"></label></td>
                                    <td>
                                        <input th:id="|splitNum_${segPackGoodsStat.index}|" th:name="splitNum" th:oninput="$.numberUtil.onlyNumber(this)" placeholder="" class="form-control" type="text" autocomplete="off" >
                                    </td>
                                    <td><input th:id="|splitWeight_${segPackGoodsStat.index}|" th:name="splitWeight" th:oninput="$.numberUtil.onlyNumber(this)" placeholder="" class="form-control" type="text" autocomplete="off"></td>
                                    <td><input th:id="|splitVolume_${segPackGoodsStat.index}|" th:name="splitVolume" th:oninput="$.numberUtil.onlyNumber(this)" placeholder="" class="form-control" type="text" autocomplete="off"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--订单拆量 end-->
                    </div>
                </div>
            </div>

        </div>

    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer" />
<script>
    //是否是车队
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/segment" : ctx + "tms/segment";

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
    });

    /**
     * 校验
     */
    // $("#form-split_quantity-add").validate({
    //     onkeyup: false,
    //     focusCleanup: true,
    //     rules:{
    //         splitNum: {
    //             required: {
    //                 depends:function(){ //二选一
    //                     return ($('#splitWeight').val().length <= 0);
    //                 }
    //             }
    //         },
    //         splitWeight: {
    //             required:{
    //                 depends:function(){ //二选一
    //                     return ($('#splitNum').val().length <= 0);
    //                 }
    //             }
    //         }
    //     }
    // });

    /**
     * 提交
     */
    function submitHandler() {
        var segPackGoodsList = [];
        var splitCount = $("#splitCount").val();
        for (var i = 0; i < splitCount; i++) {
            var splitNum = $("#splitNum_" + i).val();
            var splitWeight = $("#splitWeight_" + i).val();
            var splitVolume = $("#splitVolume_" + i).val();
            var segPackGoodsId = $("#segPackGoodsId_" + i).val();
            var segmentId = $("#segmentId_" + i).val();
            segPackGoodsList.push({num:splitNum,weight:splitWeight, volume: splitVolume,segPackGoodsId:segPackGoodsId,segmentId:segmentId})
        }

        $.ajax({
            url: prefix + "/splitQuantity",
            type: "post",
            dataType: "json",
            data: JSON.stringify(segPackGoodsList),
            contentType : 'application/json;charset=utf-8',
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function(result) {
                if (typeof callback == "function") {
                    callback(result);
                }
                $.operate.successTabCallback(result);
            }
        })
    }
</script>
</body>

</html>