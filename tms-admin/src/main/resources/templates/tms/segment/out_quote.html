<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('外发报价')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: select2-css" />
</head>
<style>
    .lh26{
        line-height: 26px;
    }
    .fw{
        font-weight: 600;
    }
    .f16{
        font-size: 16px;
    }
    .tr{
        text-align: right;
    }
    .radio-inline{
        padding-top: 0 !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-out-quote" class="form-horizontal" novalidate="novalidate">
            <div class="panel-body">
                <!--基础信息 begin-->
                <input name="segmentId" type="hidden" th:value="${segmentId}">
                <input name="outType" value="0" type="hidden"/>
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-xs-4 tr"><span style="color: red">*</span>平台选择：</label>
                            <div class="col-xs-8">
                                <div class="checkbox" style="padding-top: 0;">
                                    <label class="checkbox-inline" style="padding-top: 0;">
                                        <input type="checkbox" name="quoteWay" value="1" checked id="quoteWayOne"> 内部运力池
                                    </label>
<!--                                    <label class="checkbox-inline" style="padding-top: 0;">-->
<!--                                        <input type="checkbox" name="quoteWay" value="2"> 运满满-->
<!--                                    </label>-->
                                    <!-- <label class="checkbox-inline" style="padding-top: 0;">
                                        <input type="checkbox" name="quoteWay" value="4"> 货拉拉
                                    </label> -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class=" col-xs-12" id="nbylc">
                        <div class="form-group">
                            <label class="col-xs-4 lh26 tr">抢单报价截止时间：</label>
                            <div class="col-xs-8 lh26" id="cutoffDate">
                                <!-- (内部运力池)当前时间后24个小时 -->
                                <!-- <input type="hidden" id="cutoffDate" name="lotOutList[0].cutoffDate"> -->
                            </div>
                        </div>
                    </div>
                    <div class=" col-xs-12" id="ymmdj">
                        <div class="form-group">
                            <label class="col-xs-4 lh26 tr">运满满定金：</label>
                            <div class="col-xs-8 lh26">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="ymmDeposit" value="100"
                                        oninput="if(value<50)value=50;if(value>1000)value=1000;$.numberUtil.onlyNumber(this)" placeholder="请输入运满满定金" autocomplete="off" required>
                                    <span class="input-group-addon">元</span>
                                </div>
                                <label class="error" for="ymmDeposit" style="display: none;height: 30px;"></label>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-xs-4 lh26 tr">增加货量：</label>
                            <div class="col-xs-8">
                                <div class="input-group">
                                    <span class="input-group-addon">基数+</span>
                                    <input type="text" class="form-control" name="weightAdd" value="0"
                                        oninput="$.numberUtil.onlyNumber(this)" placeholder="请输入增加的毛重" autocomplete="off">
                                    <span class="input-group-addon">吨</span>
                                </div>     
                                <label class="error" for="weightAdd" style="display: none;height: 30px;"></label>
                                <input id="grossWeight" name="grossWeight" th:value="${grossWeight}" type="hidden">
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-xs-4 lh26 tr"></label>
                            <div class="col-xs-8">
                                <div class="input-group">
                                    <span class="input-group-addon">基数+</span>
                                    <input type="text" class="form-control" name="volumeAdd" value="0"
                                        oninput="$.numberUtil.onlyNumber(this)" placeholder="请输入增加的体积0~999" autocomplete="off">
                                    <span class="input-group-addon">m³</span>
                                </div>
                                 <!-- <label class="error" for="costAmount" style="display: none;height: 30px;"></label> -->
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-xs-4"></label>
                            <div class="col-xs-8" style="color: red;">
                                * 基数为当前运单货量，需填写增加的部分
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-xs-4 lh26 tr">备注：</label>
                            <div class="col-xs-8">
                                <textarea name="memo" id="memo" maxlength="200" class="form-control valid" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-xs-4 lh26 tr" style="line-height: 34px;">联系人：</label>
                            <div class="col-xs-7">
                                <select class="form-control select2-multiple" aria-invalid="false" data-none-selected-text="联系人" onchange="getContactName(0)"
                                    name="lotOutContactList[0].contactName" id="lotOutContactList0">
                                    <option th:each="user:${userList}" th:value="${user.userName}" th:text="${user.userName+'/'+user.phonenumber}" th:selected="${user.userId} eq ${userId}"></option>
                                </select>
                                <input name="lotOutContactList[0].contactTel"  class="form-control" type="hidden">
                                <input name="lotOutContactList[0].contactUserId"  class="form-control" type="hidden"> 
                            </div>
                            <div class="col-xs-1 lh26" style="line-height: 34px;">
                                <a class="collapse-link add-alink" title="新增" onclick="insertContact()">+</a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row" id="contactList">
                </div>
            </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: select2-js" />
<script th:inline="javascript">
    var prefix = ctx + "tms/segment";
    var userPrefix = ctx + "system/user"  //  /listSys
    // var outQuoteDeadline=[[${@config.getKey('out_quote_deadline')}]]
    var userList=[[${userList}]]

    $(function () {
        //电话校验
        $("#contactTel_0").rules("add", {
            isPhoneOrTel: true
        });

        $("#ymmdj").css('display', 'none')

        $("input[name='quoteWay']").click(function() {
            var checkids = [];
            $("input[name='quoteWay']:checked").each(function(i){
                checkids[i] = $(this).val();
            });
            $("#ymmdj").css('display', 'none');
            $("#nbylc").css('display', 'none');
            
            if(checkids.length>0){
                checkids.forEach(item=>{
                    if(item==1){
                        $("#nbylc").css('display', 'block')
                    }
                    if(item==2){
                        $("#ymmdj").css('display', 'block')
                    }
                })
            }
        });
        let userId=[[${userId}]]
        if(userId != null && userId !='' && userId != "1"){
            userList.forEach(item=>{
                if(item.userId==userId){
                    $("input[name='lotOutContactList[0].contactTel']").val(item.phonenumber);
                    $("input[name='lotOutContactList[0].contactUserId']").val(item.userId); 
                }
            })
        }else{
            $("input[name='lotOutContactList[0].contactTel']").val(userList[0].phonenumber);
            $("input[name='lotOutContactList[0].contactUserId']").val(userList[0].userId);
        }

        $("#cutoffDate").text( cutoffDate() )
    });

    function cutoffDate() {
        var now = new Date();
        now.setDate(now.getDate() + 1);

        var date = new Date(now),
        year = date.getFullYear(),
        month = date.getMonth() + 1,
        day = date.getDate(),
        hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
        month >= 1 && month <= 9 ? (month = "0" + month) : "";
        day >= 0 && day <= 9 ? (day = "0" + day) : "";
        return year + '年' + month + '月' + day + '日' + hour+ '时';  
    }

    var contactIndex = 0;
    /**
     * 新增表格行
     */
    function insertContact() {
        contactIndex++;
        let html=""
        userList.forEach(item=>{
            html+=`<option value="`+item.userName+`">`+item.userName+'/'+item.phonenumber+`</option>`
        })

        var trTtml =`<div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-xs-4 lh26 tr">联系人：</label>
                            <div class="col-xs-7">
                                <select class="form-control select2-multiple" aria-invalid="false" data-none-selected-text="联系人" onchange="getContactName(`+contactIndex+`)"
                                    name="lotOutContactList[`+contactIndex+`].contactName" id="lotOutContactList`+contactIndex+`">`+html+`</select>

                                <input name="lotOutContactList[`+contactIndex+`].contactTel"  class="form-control" type="hidden">
                                <input name="lotOutContactList[`+contactIndex+`].contactUserId"  class="form-control" type="hidden">
                            </div>
                            <div class="col-xs-1 lh26">
                                <a class="close-link del-alink" onclick="removeContact(this,`+contactIndex+`)" title="删除选择行">-</a>
                            </div>
                        </div>
                    </div>`;
        $("#contactList").append(trTtml);

        $("input[name='lotOutContactList["+contactIndex+"].contactTel']").val(userList[0].phonenumber);
        $("input[name='lotOutContactList["+contactIndex+"].contactUserId']").val(userList[0].userId);
        $('.select2-multiple').select2({});
    }
    
    /**
     *  删除指定表格行
     */
    function removeContact(obj,addrIndex) {
        $("#contactList").find(obj).closest(".col-xs-12").remove();
        emptyRow(addrIndex);
    }
    /**
     * 清空行内容
     */
     function emptyRow(addrIndex) {
        //清空行值
        $("input[name='lotOutContactList["+addrIndex+"].contactName']").val("");
        $("input[name='lotOutContactList["+addrIndex+"].contactTel']").val("");
        $("input[name='lotOutContactList["+addrIndex+"].contactUserId']").val("");
    }
    
    function getContactName(num){
        let contactName=$("#lotOutContactList"+num).val();

        userList.forEach(item=>{
            if(item.userName==contactName){
                $("input[name='lotOutContactList["+num+"].contactTel']").val(item.phonenumber);
                $("input[name='lotOutContactList["+num+"].contactUserId']").val(item.userId);
            }
        })
    }

    /**
     * 校验
     */
    $("#form-out-quote").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            // cutoffDate:{
            //     required:true,
            // }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-out-quote").serialize();
            $.operate.saveTab(prefix + "/outQuote", data,function(result){
                if(result.code==0){
                    $.modal.close();
                    parent.$.table.refresh();
                }
            });
        }
    }

</script>
</body>
</html>