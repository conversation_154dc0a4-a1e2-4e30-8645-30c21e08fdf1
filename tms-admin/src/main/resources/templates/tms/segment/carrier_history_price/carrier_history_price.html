<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单调度列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">车长：</label>
                            <div class="col-sm-8">
                                <select name="carLenId" id="carLenId" class="form-control valid"
                                        th:with="type=${@dict.getType('car_len')}">
                                    <option value=""></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">车型：</label>
                            <div class="col-sm-8">
                                <select name="carTypeId" id="carTypeId" class="form-control valid"
                                        th:with="type=${@dict.getType('car_type')}">
                                    <option value=""></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6"></div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });

        var options = {
            url: ctx + "tms/carrierHistoryPrice/list",
            showToggle:false,
            showColumns:false,
            modalName: "",
            uniqueId: "carrierId",
            height: 560,
            detailView: true,
            onExpandRow : function(index, row, $detail) {
                initChildTable(index, row, $detail);
            },
            columns: [
                {
                    title: '承运商名称',
                    field: 'carrName',
                    align: 'left'
                },{
                    title: '联系电话',
                    field: 'phone',
                    align: 'left'
                },
                {
                    title: '车牌',
                    field: 'carno',
                    align: 'left'
                },
                {
                    title: '车长',
                    field: 'carLenName',
                    align: 'left'
                },
                {
                    title: '车型',
                    field: 'carTypeName',
                    align: 'left'
                }
            ]
        };
        $.table.init(options);
    });

    initChildTable = function(index, row, $detail) {
        var childTable = $detail.html('<div class="table-responsive"><table id="child-table" class="table text-nowrap"></table></div>').find('table');
        $(childTable).bootstrapTable({
            url: ctx + "tms/carrierHistoryPrice/historyPriceList/list",
            method: 'post',
            sidePagination: "server",
            pagination: true,
            pageNumber: 1,
            pageSize: 10,
            pageList: [],
            contentType: "application/x-www-form-urlencoded",
            queryParams : function(params) {
                var curParams = {
                    // 传递参数查询参数
                    pageSize:       params.limit,
                    pageNum:        params.offset / params.limit + 1,
                    searchValue:    params.search,
                    orderByColumn:  params.sort,
                    isAsc:          params.order,
                    carrierId:      row.carrierId
                };
                return curParams;
            },
            columns: [
                {
                    title: '调度日期',
                    field: 'dispatcherDate',
                    align: 'left'
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.deliName+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriName;
                    }
                },
                {
                    title: '总金额',
                    field: 'costAmount',
                    align: 'left'
                },
                {
                    title: '油卡金额',
                    field: 'oilAmount',
                    align: 'left'
                },
                {
                    title: '调度备注',
                    field: 'memo',
                    align: 'left'
                },

            ]
        });
    };

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }
</script>
</body>
</html>