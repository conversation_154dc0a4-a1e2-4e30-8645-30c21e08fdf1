<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('拆段')"/>
</head>

<body>
<div class="form-content">
    <form id="form-subsection-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="segmentIds" th:value="${segmentIds}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">中转地址</a>
                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!-- begin-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 3%;"><a class="collapse-link add-alink" onclick="insertRow()" title="新增行">+</a></th>
                                    <th style="width: 15%;">地址名称</th>
                                    <th style="width: 15%;">省份</th>
                                    <th style="width: 15%;">城市</th>
                                    <th style="width: 15%;">区域</th>
                                    <th style="width: 25%;">详细地址</th>
                                    <th style="width: 15%;">要求到货日期</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr class="distpicker">
                                    <td><a class="close-link del-alink" onclick="removeRow(this,0)" title="删除选择行">-</a></td>
                                    <td>
                                        <div class="input-group">
                                            <input id="addrName_0" name="addressList[0].addrName" onclick="selectDelivery(0)" type="text"
                                               placeholder="请选择提货方" class="form-control valid" autocomplete="off" required="" readonly>
                                            <input id="addressId_0" name="addressList[0].addressId" type="hidden">
                                            <input id="addrCode_0" name="addressList[0].addrCode" type="hidden">
                                            <input id="contact_0" name="addressList[0].contact" type="hidden">
                                            <input id="mobile_0" name="addressList[0].mobile" type="hidden">
                                            <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                        </div>
                                    </td>
                                    <td>
                                        <input id="provinceName_0" name="addressList[0].provinceName" class="form-control" type="text" disabled>
                                        <input id="provinceId_0" name="addressList[0].provinceId" type="hidden">
                                    </td>
                                    <td>
                                        <input id="cityName_0" name="addressList[0].cityName" class="form-control" type="text" disabled>
                                        <input id="cityId_0" name="addressList[0].cityId" type="hidden">
                                    </td>
                                    <td>
                                        <input id="areaName_0" name="addressList[0].areaName" class="form-control" type="text" disabled>
                                        <input id="areaId_0" name="addressList[0].areaId" type="hidden">
                                    </td>
                                    <td>
                                        <input id="detailAddr_0" name="addressList[0].detailAddr" placeholder="" class="form-control" type="text" disabled>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class=" form-control" id="reqArriDate_0" name="addressList[0].reqArriDate"
                                                   autocomplete="off" required readonly>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--订单货品费用明细 end-->
                    </div>
                </div>
            </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">所选运段</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!-- begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab1" class="custom-tab table">
                            <thead>
                            <tr clas="distpicker">
                                <th style="width: 11%;">发货单号</th>
                                <th style="width: 11%;">运段号</th>
                                <th style="width: 11%;">起始地</th>
                                <th style="width: 11%;">起始地城市</th>
                                <th style="width: 11%;">目的地</th>
                                <th style="width: 11%;">目的地城市</th>
                                <th style="width: 11%;">要求提货日期</th>
                                <th style="width: 11%;">要求到货日期</th>
                            </tr>
                            </thead>
                            <tbody>
                           <tr th:each="segment,segmentStat : ${segmentList}">
                                <td><label style="float:left;" th:text="${segment.invoiceVbillno}"></label></td>
                                <td><label style="float:left;" th:text="${segment.vbillno}"></label></td>
                                <td><label style="float:left;" th:text="${segment.deliAddrName}"></label></td>
                                <td><label style="float:left;" th:text="${segment.deliProName} + ${segment.deliCityName} + ${segment.deliAreaName}"></label></td>
                                <td><label style="float:left;" th:text="${segment.arriAddrName}"></label></td>
                                <td><label style="float:left;" th:text="${segment.arriProName} + ${segment.arriCityName} + ${segment.arriAreaName}">天津市</label></td>
                                <td><label style="float:left;" th:text="${#dates.format(segment.reqDeliDate,'yyyy-MM-dd HH:mm:ss')}"></label></td>
                                <td><label style="float:left;" th:text="${#dates.format(segment.reqArriDate,'yyyy-MM-dd HH:mm:ss')}"></label></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--end-->
                </div>
            </div>
        </div>

    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler(1)"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler(2)"><i class="fa fa-check"></i>保存并第一段调度</button>&nbsp;

        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    //是否是车队
    var isFleet = [[${isFleet}]];

    var prefix = isFleet ? ctx + "fleet/segment" : ctx + "tms/segment";

    var befDate = [[${befDate}]];
    var aftDate = [[${aftDate}]];
    var index = 0;
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        laydate.render({
            elem: '#reqArriDate_0', //指定元素
            // format: 'yyyy-MM-dd',
            isInitValue: false,
            trigger: 'click',
            min: befDate,
            max: aftDate,
            type: 'datetime',
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours=now.getHours();
                this.dateTime.minutes=now.getMinutes();
                this.dateTime.seconds=now.getSeconds();
            },
            done: function (value, date, endDate) {
                $("#reqArriDate_0").val(value);
                $("#form-subsection-add").validate().element($("#reqArriDate_0"));
            }
        });
    });



    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
    });

    /* 新增表格行 */
    function insertRow() {
        index++;
        var trTtml =
                '<tr class="distpicker">'+
                    '<td><a class="close-link del-alink" onclick="removeRow(this,'+index+')" title="删除选择行">-</a></td>'+
                    '<td>' +
                        '<div class="input-group">' +
                            '<input id="addrName_'+index+'" name="addressList['+index+'].addrName" onclick="selectDelivery('+index+')" type="text" placeholder="请选择提货方" class="form-control valid" autocomplete="off" required="" readonly>' +
                            '<input id="addressId_'+index+'" name="addressList['+index+'].addressId" type="hidden">' +
                            '<input id="addrCode_'+index+'" name="addressList['+index+'].addrCode" type="hidden">' +
                            '<input id="contact_'+index+'" name="addressList['+index+'].contact" type="hidden">' +
                            '<input id="mobile_'+index+'" name="addressList['+index+'].mobile" type="hidden">' +
                            '<span class="input-group-addon"><i class="fa fa-search"></i></span>' +
                        '</div>' +
                    '</td>'+
                    '<td>' +
                        '<input id="provinceName_'+index+'" name="addressList['+index+'].provinceName" class="form-control" type="text" disabled>' +
                        '<input id="provinceId_'+index+'" name="addressList['+index+'].provinceId" type="hidden">' +
                    '</td>'+
                    '<td>' +
                        '<input id="cityName_'+index+'" name="addressList['+index+'].cityName" class="form-control" type="text" disabled>' +
                        '<input id="cityId_'+index+'" name="addressList['+index+'].cityId" type="hidden">' +
                    '</td>'+
                    '<td>' +
                        '<input id="areaName_'+index+'" name="addressList['+index+'].areaName" class="form-control" type="text" disabled>' +
                        '<input id="areaId_'+index+'" name="addressList['+index+'].areaId" type="hidden">' +
                    '</td>'+
                    '<td>' +
                        '<input id="detailAddr_'+index+'" name="addressList['+index+'].detailAddr" placeholder="" class="form-control" type="text" disabled>' +
                    '</td>'+
                    '<td>' +
                        '<div class="input-group">' +
                            ' <input type="text" class="form-control" id="reqArriDate_'+index+'" name="addressList['+index+'].reqArriDate" required readonly>' +
                        '</div>' +
                    '</td>'+
                '</tr>';

        $("#infoTab tbody").append(trTtml);

        layui.use('laydate', function(){
            var id = "#reqArriDate_" + index;
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqArriDate_' + index, //指定元素
                // format: 'yyyy-MM-dd',
                isInitValue: false,
                trigger: 'click',
                min: befDate,
                max: aftDate,
                type: 'datetime',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours=now.getHours();
                    this.dateTime.minutes=now.getMinutes();
                    this.dateTime.seconds=now.getSeconds();
                },
                done: function (value, date, endDate) {
                    $(id).val(value);
                    $("#form-subsection-add").validate().element($(id));
                }
            });
        });


    }

    /* 删除指定表格行 */
    function removeRow(obj,addIndex) {
        //如果条数大于1，则删除
        if ($("#infoTab tbody").find('tr').length > 1) {
            $("#infoTab tbody").find(obj).closest("tr").remove();
        } else {
            //清空数据
            //收货方名称
            $("#addrName_" + addIndex).val("");
            //收货地址id
            $("#addressId_" + addIndex).val("");
            //收货省id
            $("#provinceId_" + addIndex).val("");
            //收货市id
            $("#cityId_" + addIndex).val("");
            //收货区id
            $("#areaId_" + addIndex).val("");

            //收货省名称
            $("#provinceName_" + addIndex).val("");
            //收货市名称
            $("#cityName_" + addIndex).val("");
            //收货区名称
            $("#areaName_" + addIndex).val("");

            //联系人
            $("#contact_" + addIndex).val("");
            //联系人手机
            $("#mobile_" + addIndex).val("");
            //收货详细地址
            $("#detailAddr_" + addIndex).val("");
            //收货地址编码
            $("#addrCode_" + addIndex).val("");
        }

    }

    /**
     * 选择地址
     * @param index 标识符
     */
    function selectDelivery(addIndex) {
        $.modal.open("地址信息", ctx + "basic/address/selectAddress?addrType=3","","",function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            //收货方名称
            $("#addrName_" + addIndex).val(rows[0]["addrName"]);
            //收货地址id
            $("#addressId_" + addIndex).val(rows[0]["addressId"]);
            //收货省id
            $("#provinceId_" + addIndex).val(rows[0]["provinceId"]);
            //收货市id
            $("#cityId_" + addIndex).val(rows[0]["cityId"]);
            //收货区id
            $("#areaId_" + addIndex).val(rows[0]["areaId"]);

            //收货省名称
            $("#provinceName_" + addIndex).val(rows[0]["provinceName"]);
            //收货市名称
            $("#cityName_" + addIndex).val(rows[0]["cityName"]);
            //收货区名称
            $("#areaName_" + addIndex).val(rows[0]["areaName"]);

            //联系人
            $("#contact_" + addIndex).val(rows[0]["contact"]);
            //联系人手机
            $("#mobile_" + addIndex).val(rows[0]["mobile"]);
            //收货详细地址
            $("#detailAddr_" + addIndex).val(rows[0]["detailAddr"]);
            //收货地址编码
            $("#addrCode_" + addIndex).val(rows[0]["addrCode"]);

            //单独校验
            // $("#form-invoice-add").validate().element($("#deliAddrName"));
            layer.close(index);
        });
    }

    function submitHandler(btnIdx) {
        if ($.validate.form()) {
            var dis = $(":disabled");
            dis.attr("disabled", false);
            var data = $("#form-subsection-add").serializeArray();

            $.operate.saveTab(prefix + "/subsection", data, function (result) {
                if (result.code != 0) {
                    dis.attr("disabled", true);
                }else{
                    if(btnIdx == 2){
                        let segmentIds = result.data.join(",");
                        console.log("dispatch segments=",segmentIds);
                        window.top.topOpenTab("保存第一段调度",ctx + "tms/segment/dispatch/"+segmentIds);

                    }
                }
            });

        }
    }
    function segmentDispatch(segmentIds){
        $.modal.openTab("调度",ctx + "tms/segment/dispatch/"+segmentIds);
    }



</script>
</body>

</html>