<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单调度列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 60px;
        line-height: 26px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" th:name="segmentId" th:value="${segmentId}">
                <div class="row">
                    <div class="col-md-2 col-sm-2">
                        <div class="flex">
                            <label class="flex_left" style="width: 80px;">要求车长：</label>
                            <div class="flex_right">
                                <select name="reqCarLen" id="reqCarLen" class="form-control" th:with="type=${@dict.getType('car_len')}">
                                    <option value="">-- 请选择 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="${invoice.carLen}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="flex">
                            <label class="flex_left">车长：</label>
                            <div class="flex_right">
                                <select name="carLen" id="carLen" class="form-control" th:with="type=${@dict.getType('car_len')}">
                                    <option value="">-- 请选择 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="${invoice.carLen}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="flex">
                            <label class="flex_left">车型：</label>
                            <div class="flex_right">
                                <select name="carType" id="carType" class="form-control" th:with="type=${@dict.getType('car_type')}">
                                    <option value="">-- 请选择 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="${invoice.carType}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <!--                            <label class="col-sm-3">车牌号：</label>-->
                            <div class="col-sm-12">
                                <input name="carNo" id="carNo" placeholder="请输入车牌号" class="form-control " type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <!--<div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="custAbbr" id="custAbbr" placeholder="请输入客户名称" class="form-control " type="text" th:value="${invoice.custAbbr}"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>-->
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-3">调度日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" placeholder="调度开始日期" class="time-input form-control"
                                       id="dispatcherDateStart"  name="dispatcherDateStart" th:value="${dispatcherDateStart}">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" placeholder="调度结束日期" class="time-input form-control"
                                       id="dispatcherDateEnd"  name="dispatcherDateEnd" th:value="${dispatcherDateEnd}">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-5">
                        <div class="col-sm-4">
                            <div class="form-group">
<!--                                <label class="col-sm-6">提货方地址：</label>-->
                                <div class="col-sm-12">
                                    <select  name="deliProvinceId" id="deliProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <select name="deliCityId" id="deliCityId"  class="form-control valid" aria-invalid="false"></select>
                        </div>
                        <div class="col-sm-4">
                            <select name="deliAreaId" id="deliAreaId"  class="form-control valid" aria-invalid="false"></select>
                        </div>
                    </div>
                    <div class="col-sm-5">
                        <div class="col-sm-2" onclick="changeDiv()">
                            <img th:src="@{/img/change.png}" style="width: 40px;height: 40px;display: block;margin: 0 auto">
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
<!--                                <label class="col-sm-6">收货方地址：</label>-->
                                <div class="col-sm-12">
                                    <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                        </div>
                        <div class="col-sm-3">
                            <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:segment:segmentHistoryPrice:export">
                <i class="fa fa-download"></i> 导出
            </a>

        </div>

        <div class="col-sm-12 select-table table-striped toofoot">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var deliProvinceId = [[${deliProvinceId}]];
    var deliCityId = [[${deliCityId}]];
    var deliAreaId = [[${deliAreaId}]];
    var arriProvinceId = [[${arriProvinceId}]];
    var arriCityId = [[${arriCityId}]];
    var arriAreaId = [[${arriAreaId}]];

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        // $("#deliProvinceId").val(deliProvinceId);
        // $("#deliCityId").val(deliCityId);
        // $("#arriProvinceId").val(arriProvinceId);
        // $("#arriCityId").val(arriCityId)

        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",deliProvinceId,deliCityId,deliAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",arriProvinceId,arriCityId,arriAreaId);

        var options = {
            url: ctx + "tms/segment/segmentHistoryPrice/list",
            showToggle:false,
            showColumns:false,
            modalName: "",
            uniqueId: "carrierId",
            exportUrl: ctx + "tms/segment/segmentHistoryPrice/export",
            height: 560,
            firstLoad: false,
            columns: [
                {
                    title: '承运商名称',
                    field: 'carrierName',
                    align: 'left'
                },
                /*{
                    title: '客户名称',
                    field: 'custAbbr',
                    align: 'left'
                },
                {
                    title: '货品',
                    field: 'goodsName',
                    align: 'left'
                },*/
                {
                    title: '总件数|总重量|总体积',
                    field: 'numCount',
                    align: 'left',
                    formatter:function(value,row){
                        let data=[]
                        if(value){
                            data.push(value+"件")
                        }
                        if(row.weightCount){
                            data.push(row.weightCount+"吨")
                        }
                        if(row.volumeCount){
                            data.push(row.volumeCount+"m³")
                        }
                        return data.join("|");;
                    }
                },
                {
                    title: '联系电话',
                    field: 'phone',
                    align: 'left'
                },
                {
                    title: '车牌',
                    field: 'carNo',
                    align: 'left'
                },
                {
                    title: '车长',
                    field: 'carLenName',
                    align: 'left'
                },
                {
                    title: '车型',
                    field: 'carTypeName',
                    align: 'left'
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.deliProName+row.deliCityName+row.deliAreaName+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriProName+row.arriCityName+row.arriAreaName;
                    }
                },
                {
                    title: '总金额',
                    field: 'costAmount',
                    align: 'left'
                },
                {
                    title: '要求车长',
                    field: 'reqCarLen',
                    align: 'left'
                },
                {
                    title: '要求车型',
                    field: 'reqCarTypeName',
                    align: 'left'
                },
                {
                    title: '油卡金额',
                    field: 'oilAmount',
                    align: 'left'
                },
                {
                    title: '调度日期',
                    field: 'dispatcherDate',
                    align: 'left'
                },
                {
                    title: '调度人',
                    field: 'dispatcher',
                    align: 'left'
                },
                {
                    title: '运单号',
                    field: 'lot',
                    align: 'left'
                }
            ]
        };
        $.table.init(options);

        $.table.search();

    });

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        // data.deliCityId = deliCityId;
        // data.arriProvinceId = arriProvinceId;
        // data.arriCityId = arriCityId;
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        // $("#deliAreaId ").val("");
        // $("#arriAreaId ").val("");
        // $("#role-form")[0].reset();
        // searchPre();
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $.form.reset()
    }

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        searchPre()
    }
</script>
</body>
</html>