<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('获取指导价')"/>
</head>

<body>
<div class="form-content">
    <form id="form-invoice-guidePrice" class="form-horizontal" novalidate="novalidate">
        <div class="panel-body">
            <div class="row">
                <input type="hidden" name="ids" th:value="${ids}">

                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-sm-4">指导价：</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" oninput="$.numberUtil.onlyNumber(this)"
                                   th:value="${pageGuidingPrice}"
                                   disabled
                                   autocomplete="off" >
                        </div>
                    </div>
                </div>

            </div>


        </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "invoice";

    function submitHandler() {
        var data = $("#form-invoice-guidePrice").serializeArray();
        $.operate.save(ctx + "segment/saveGuidePrice", data);
    }

</script>
</body>
</html>