<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单调度列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">客户简称：</label>
                            <div class="col-sm-8">
                                <input name="custAbbr" id="custAbbr" placeholder="请输入客户简称" class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">发货单号：</label>
                            <div class="col-sm-8">
                                <input name="invoiceVbillno" id="invoiceVbillno" placeholder="请输入发货单号" class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">客户发货单号：</label>
                            <div class="col-sm-8">
                                <input name="custOrderno" id="custOrderno" placeholder="请输入客户发货号" class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">提货方：</label>
                            <div class="col-sm-8">
                                <input name="deliAddrName" id="deliAddrName" placeholder="请输入提货方" class="form-control"  type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row no-gutter">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">收货方：</label>
                            <div class="col-sm-8">
                                <input name="arriAddrName" id="arriAddrName" placeholder="请输入收货方"  class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运段号：</label>
                            <div class="col-sm-8">
                                <input name="vbillno" id="vbillno" placeholder="请输入运段号" class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运段状态：</label>
                            <div class="col-sm-8">
                                <select name="vbillstatus" id="vbillstatus" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="运段状态" multiple th:with="type=${segmentStatusList}">
                                    <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运输方式：</label>
                            <div class="col-sm-8">
                                <select name="transCode" id="transCode" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运输方式" multiple th:with="type=${@dict.getType('trans_code')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row no-gutter">
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2">要求提货日期：</label>
                            <div class="col-sm-8">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateStart"  name="params[reqDeliDateStart]">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateEnd"  name="params[reqDeliDateEnd]">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2">要求到货日期：</label>
                            <div class="col-sm-8">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="reqArriDateStart"  name="params[reqArriDateStart]">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="reqArriDateEnd"  name="params[reqArriDateEnd]">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row no-gutter">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">调度组：</label>
                            <div class="col-sm-8">
                                <select name="transLineId" id="transLineId" class="form-control selectpicker"
                                        aria-invalid="false" data-none-selected-text="调度组" multiple th:with="type=${dispatcherDeptList}">
                                    <option th:each="dict : ${type}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row no-gutter">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">提货方地址：</label>
                            <div class="col-sm-8">
                                <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <select name="deliCityId" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                    </div>
                    <div class="col-sm-2">
                        <select name="deliAreaId" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                    </div>
                    <div class="col-sm-3">
                        <input name="deliDetailAddr" id="deliDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                               maxlength="50" autocomplete="off">
                    </div>
                </div>
                <div class="row no-gutter" >
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">收货方地址：</label>
                            <div class="col-sm-8">
                                <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                    </div>
                    <div class="col-sm-2">
                        <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                    </div>
                    <div class="col-sm-3">
                        <input name="arriDetailAddr" id="arriDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                               maxlength="50" autocomplete="off">
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" id="search" onclick="segSearchPre();"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary multiple disabled" shiro:hasPermission="tms:segment:subsection" onclick="subsection()">
                <i class="fa fa-unsorted" style="font-size: 10px;"></i> 拆段
            </a>
           <a class="btn btn-primary single disabled" shiro:hasPermission="tms:segment:splitQuantity" onclick="splitQuantity()">
                <i class="fa fa-gg" style="font-size: 10px;"></i> 拆量
            </a>
            <a class="btn btn-primary multiple disabled" shiro:hasPermission="tms:segment:dispatch" onclick="dispatch()">
                <i class="fa fa-transgender-alt" style="font-size: 10px;"></i> 调度
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />

<script th:inline="javascript">
    var prefix = ctx + "tms/segment";
    //运段状态
    var segmentStatusList = [[${segmentStatusList}]];
    //待调度状态
    var toDispatchStatus = [[${toDispatchStatus}]];
    //要求车型
    var carType = [[${@dict.getType('car_type')}]];
    //运输方式
    var transCode = [[${@dict.getType('trans_code')}]];
    //关账记录
    var CloseAccountList = [[${CloseAccountList}]];
    //结算方式
    var balatype = [[${@dict.getType('bala_type')}]];

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                segSearchPre();
            }
        });

        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/dispatch",
            detailUrl: prefix + "/subsection",
            showToggle:false,       // 是否显示详细视图和列表视图的切换按钮
            showColumns:true,       // 是否显示隐藏某列下拉框
            modalName: "调度",
            fixedColumns: true,      // 是否启用冻结列（左侧）
            fixedNumber: 5,     // 列冻结的个数（左侧）
            uniqueId: "segmentId",
            height: 560,
            clickToSelect: true,
            showExport: true,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"调度配载"
            },
            columns: [
                {
                checkbox: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(row,value) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:segment:cancel')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="撤销拆段或拆量" onclick="cancel(\''+value.segmentId+'\',\''+value.vbillstatus+'\',\''+value.outGoType+'\',\''+value.regDate+'\',\''+value.parentSeg+'\')"><i class="fa fa-reply" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:segment:outQuote')}]] != "hidden") {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="外发报价" onclick="outQuote(\''+value.segmentId+'\',\''+value.vbillstatus+'\',\''+value.outGoType+'\',\''+value.regDate+'\')"><i  class="fa fa-cny" style="font-size: 15px;" ></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:segment:outGrabOrder')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="外发抢单" onclick="outGrabOrder(\''+value.segmentId+'\',\''+value.vbillstatus+'\',\''+value.outGoType+'\',\''+value.regDate+'\')"><i class="fa fa-paypal" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:segment:dispatchDetail')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="调度明细" onclick="dispatchDetail(\''+value.segmentId+'\',\''+value.vbillstatus+'\',\''+value.outGoType+'\',\''+value.regDate+'\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:segment:segmentHistoryPrice')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="历史价格" onclick="historyPrice(\''+value.segmentId+'\')"><i class="fa fa-dollar" style="font-size: 15px;"></i></a>');
                        }

                        return actions.join('');
                    }
                },
                {
                    title: '发货客户',
                    field: 'custAbbr',
                    align: 'left',
                    switchable: false
                },
                {
                    title: '发货单号',
                    field: 'invoiceVbillno',
                    align: 'left',
                    switchable: false
                },
                {
                    title: '客户发货单号',
                    field: 'custOrderno',
                    align: 'left',
                    switchable: false
                },
                {
                    title: '要求提货时间',
                    field: 'reqDeliDate',
                    align: 'left',
                    formatter: function status(row,value) {
                        var reqArriDate = new Date(Date.parse(value.reqDeliDate));
                        value.reqDeliDate.substr(0,10)
                        var now = new Date();
                        //如果到货时间小于当前时间，并且状态等于待调度状态，背景则标红
                        if (reqArriDate < now && value.vbillstatus == toDispatchStatus) {
                            return '<span class="label label-danger">' + value.reqDeliDate.substr(0,10) + '</span>';
                        } else {
                            return value.reqDeliDate.substr(0,10);
                        }
                    }
                },
                {
                    title: '要求到货时间',
                    field: 'reqArriDate',
                    align: 'left',
                    formatter: function status(row,value) {
                        var reqArriDate = new Date(Date.parse(value.reqArriDate));
                        var now = new Date();
                        //如果到货时间小于当前时间，并且状态等于待调度状态，背景则标红
                        if (reqArriDate < now && value.vbillstatus == toDispatchStatus) {
                            return '<span class="label label-danger">' + value.reqArriDate.substr(0,10) + '</span>';
                        } else {
                            return value.reqArriDate.substr(0,10);
                        }
                    }
                },
                {
                    title: '运段状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(row,value) {
                        var context = '';
                        segmentStatusList.forEach(function (v) {
                            if (v.value == value.vbillstatus) {
                                if ("0" == value.vbillstatus) {
                                    context = '<span class="label label-primary">'+v.context+'</span>';
                                }else if ("1" == value.vbillstatus) {
                                    context = '<span class="label label-warning">'+v.context+'</span>';
                                }else if ("2" == value.vbillstatus) {
                                    context = '<span class="label label-info">'+v.context+'</span>';
                                }else if ("3" == value.vbillstatus) {
                                    context = '<span class="label label-success">'+v.context+'</span>';
                                }else if ("4" == value.vbillstatus) {
                                    context = '<span class="label label-inverse">'+v.context+'</span>';
                                }else if ("9" == value.vbillstatus) {
                                    context = '<span class="label label-primary">'+v.context+'</span>';
                                }
                                return false;
                            }
                        });
                        return context;
                    }
                },
                {
                    title: '外发状态',
                    field: 'outGoType',
                    align: 'left',
                    formatter: function status(row,value) {
                        if (value.outGoType == 0) {
                            return "外发报价";
                        }else if (value.outGoType == 1) {
                            return "外发抢单";
                        } else {
                            return "不外发";
                        }
                    }
                },
                {
                    title: '指导价',
                    field: 'guidingPrice',
                    align: 'left',
                },
                {
                    title: '调度组',
                    field: 'transLineName',
                    align: 'left',
                },
                {
                    title: '要求车长',
                    field: 'carLenName',
                    align: 'left'
                },
                {
                    title: '要求车型',
                    field: 'carType',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(carType, value.carType);
                    }
                },
                {
                    title: '发货单备注',
                    field: 'memo',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.memo);
                    }
                },
                {
                    title: '提货方',
                    field: 'deliAddrName',
                    align: 'left'
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var deliAddr = row.deliProName+row.deliCityName+row.deliAreaName+row.deliDetailAddr;
                        var arriAddr = row.arriProName+row.arriCityName+row.arriAreaName+row.arriDetailAddr;
                        return deliAddr+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+arriAddr;
                    }
                },
                // {
                //     title: '提货省名称',
                //     field: 'deliProName',
                //     align: 'left'
                // }, {
                //     title: '提货市名称',
                //     field: 'deliCityName',
                //     align: 'left'
                // },{
                //     title: '提货区名称',
                //     field: 'deliAreaName',
                //     align: 'left'
                // },{
                //     title: '提货详细地址',
                //     field: 'deliDetailAddr',
                //     align: 'left'
                // },
                {
                    title: '收货方',
                    field: 'arriAddrName',
                    align: 'left'
                },
                // {
                //     title: '到货省名称',
                //     field: 'arriProName',
                //     align: 'left'
                // },{
                //     title: '到货市名称',
                //     field: 'arriCityName',
                //     align: 'left'
                // },{
                //     title: '到货区名称',
                //     field: 'arriAreaName',
                //     align: 'left'
                // },{
                //     title: '到货详细地址',
                //     field: 'arriDetailAddr',
                //     align: 'left'
                // },
                {
                    title: '收货人',
                    field: 'arriContact',
                    align: 'left'
                },
                {
                    title: '总件数',
                    field: 'numCount',
                    align: 'left'
                },
                {
                    title: '总重量(吨)',
                    field: 'weightCount',
                    align: 'left'
                },
                {
                    title: '总体积(m3)',
                    field: 'volumeCount',
                    align: 'left'
                },
                {
                    title: '运输方式',
                    field: 'a1',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(transCode, value.transCode);
                    }
                },
                {
                    title: '运段类型',
                    field: 'segmentType',
                    align: 'left',
                    formatter: function status(row,value) {
                        if (value.segType == 0) {
                            return "拆段";
                        } else if(value.segType == 1){
                            return "拆量";
                        }else {
                            return "原始";
                        }
                    }
                },
                {
                    title: '结算方式',
                    field: 'balatype',
                    align: 'left',
                    formatter: function status(row,value) {
                        return $.table.selectDictLabel(balatype, value.balatype);
                    }
                },
                {
                    title: '创建人',
                    field: 'regUserName',
                    align: 'left'
                },
                {
                    title: '创建时间',
                    field: 'regDate',
                    align: 'left'
                },
                {
                    title: '发货单创建人',
                    field: 'invoiceRegUserName',
                    align: 'left'
                },
                {
                    title: '发货单创建时间',
                    field: 'invoiceRegDate',
                    align: 'left'
                },
                {
                    title: '运段号',
                    field: 'vbillno',
                    align: 'left',
                },
            ]
        };
        $.table.init(options);

        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        //默认设置为待调度
        $('#vbillstatus').selectpicker('val',segmentStatusList[0].value);
    });

    /**
     * 日期插件
     */
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        var reqDeliDateStart = laydate.render({
            elem: '#reqDeliDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqDeliDateEnd = laydate.render({
            elem: '#reqDeliDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqArriDateStart = laydate.render({
            elem: '#reqArriDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var reqArriDateEnd = laydate.render({
            elem: '#reqArriDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
    });

    /**
     * 拆段
     */
    function subsection(){
        var regDate = $.table.selectColumns("regDate");
        //关账判断
        for (var i = 0; i < regDate.length ; i++) {
            for(var j=0 ; j< CloseAccountList.length ; j++ ){
                if(regDate[i].substring(0,7)==CloseAccountList[j].yearMonth.substring(0,7)){
                    $.modal.alertWarning("该月份已关账，无法进行操作！");
                    return false;
                }
            }
        }

        var segmentIds = $.table.selectColumns("segmentId").join();

        if (segmentIds.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (checkSegmentStatus()) {
            $.modal.alertWarning("存在非待调度状态的运段，无法拆段！");
            return;
        }
        if (checkOutGoType()) {
            $.modal.alertWarning("存在已外发的运段，无法拆段！");
            return;
        }
        $.modal.openTab('拆段', prefix + "/subsection/" + segmentIds);
    }

    /**
     * 拆量
     */
    function splitQuantity(){
        var regDate = $.table.selectColumns("regDate").join();
        //关账判断
        for(var i=0 ; i< CloseAccountList.length ; i++ ){
            if(regDate.substring(0,7)==CloseAccountList[i].yearMonth.substring(0,7)){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        var segmentIds = $.table.selectColumns("segmentId").join();
        if (segmentIds.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (checkSegmentStatus()) {
            $.modal.alertWarning("存在非待调度状态的运段，无法拆量！");
            return;
        }
        if (checkOutGoType()) {
            $.modal.alertWarning("该运段已外发，无法拆量！");
            return;
        }

        $.modal.openTab("拆量",prefix + "/splitQuantity/" + segmentIds);
    }

    /**
     * 撤销拆段或拆量
     */
    function cancel(segmentId,vbillstatus, outGoType,regDate,parentSeg) {
        //关账判断
        for(var i=0 ; i< CloseAccountList.length ; i++ ){
            if(regDate.substring(0,7)==CloseAccountList[i].yearMonth.substring(0,7)){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }
        if (vbillstatus != 0) {
            $.modal.alertWarning("该运段已调度，无法撤销！");
            return;
        }
        if (outGoType != 2) {
            $.modal.alertWarning("该运段已外发，无法撤销！");
            return;
        }
        if (parentSeg == null || parentSeg=='null') {
            $.modal.alertWarning("初始运段，无法撤销！");
            return;
        }
        $.modal.confirm("确认要撤销该条运段吗?", function() {
            $.operate.post(prefix + "/cancel", { "segmentId": segmentId });
        });

    }

    /**
     * 调度
     */
    function dispatch(){
        var regDate = $.table.selectColumns("regDate").join();
        //关账判断
        for(var i=0 ; i< CloseAccountList.length ; i++ ){
            if(regDate.substring(0,7)==CloseAccountList[i].yearMonth.substring(0,7)){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        var segmentIds = $.table.selectColumns("segmentId").join();

        if (segmentIds.length == 0) {
            $.modal.alertWarning("请至少选择一条记录！");
            return;
        }
        if (checkSegmentStatus()) {
            $.modal.alertWarning("存在非待调度状态的运段，无法调度！");
            return;
        }
        if (checkOutGoType()) {
            $.modal.alertWarning("存在已外发的运段，无法调度！");
            return;
        }
        if (checkOutTransCode()) {
            $.modal.alertWarning("整车无法合并调度！");
            return;
        }
        if (checkTransLineId()) {
            $.modal.alertWarning("所属调度组不相同，无法调度！");
            return;
        }

        $.modal.openTab('调度', prefix + "/dispatch/" + segmentIds);
    }

    /**
     * 调度明细
     */
    function dispatchDetail(segmentId,vbillstatus){
        if (vbillstatus == 0) {
            $.modal.alertWarning("该运段还未调度！");
            return;
        }
        $.modal.openTab('调度明细', prefix + "/dispatch_detail/" + segmentId);
    }

    /**
     * 跳转外发报价页面
     */
    function outQuote(segmentId, vbillstatus, outGoType,regDate) {
        //关账判断
        for(var i=0 ; i< CloseAccountList.length ; i++ ){
            if(regDate.substring(0,7)==CloseAccountList[i].yearMonth.substring(0,7)){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }
        if (vbillstatus != 0) {
            $.modal.alertWarning("该运段已调度，无法外发！");
            return;
        }
        if (outGoType != 2) {
            $.modal.alertWarning("该运段已外发，无法外发！");
            return;
        }
        $.modal.open("外发报价", prefix + "/outQuote/" + segmentId,450,500);
    }

    /**
     * 跳转外发抢单
     */
    function outGrabOrder(segmentId, vbillstatus, outGoType,regDate) {
        //关账判断
        for(var i=0 ; i< CloseAccountList.length ; i++ ){
            if(regDate.substring(0,7)==CloseAccountList[i].yearMonth.substring(0,7)){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }
        if (vbillstatus != 0) {
            $.modal.alertWarning("该运段已调度，无法外发！");
            return;
        }
        if (outGoType != 2) {
            $.modal.alertWarning("该运段已外发，无法外发！");
            return;
        }
        $.modal.open("外发抢单", prefix + "/outGrabOrder/" + segmentId,450,550);
    }

    function segSearchPre() {
        var data = {};
        data.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));
        data.transCode = $.common.join($('#transCode').selectpicker('val'));
        data.transLineId = $.common.join($('#transLineId').selectpicker('val'));

        $.table.search('role-form', data);
    }

    function resetPre() {
        $("#deliProvinceId ").val("");
        $("#deliCityId ").val("");
        $("#deliAreaId ").val("");
        $("#arriProvinceId ").val("");
        $("#arriCityId ").val("");
        $("#arriAreaId ").val("");

        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        //默认设置为待调度
        $('#vbillstatus').selectpicker('val',segmentStatusList[0].value);
        segSearchPre();
    }

    /**
     * 查询历史价格
     */
    function historyPrice(segmentId) {
        var url = prefix + "/segmentHistoryPrice/" + segmentId;
        layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "历史价格",
            area: ['80%', '85%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                layer.close(index);
            }
        });

    }
    /**
     * 校验所勾选的是否存在非待调度的
     */
    function checkSegmentStatus() {
        //校验是否存在已调度运段
        var vbillstatusList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["vbillstatus"];
        });
        var b = false;
        $.each(vbillstatusList, function (i, v) {
            if (v != 0) {
                b = true;
                return false;
            }
        });
        return b;
    }

    /**
     * 校验运段是否已经外发
     */
    function checkOutGoType() {
        //勾选的外发状态list
        var outGoTypeList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["outGoType"];
        });
        var b = false;
        $.each(outGoTypeList, function (i, v) {
            if (v != 2) {
                b = true;
                return false;
            }
        });
        return b;
    }

    /**
     * 校验 运输方式是否为整车
     */
    function checkOutTransCode() {
        //勾选的 运输方式list
        var transCodeList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["transCode"];
        });
        if (transCodeList.length > 1) {
            var b = false;
            $.each(transCodeList, function (i, v) {
                //0为整车
                if (v == 0) {
                    b = true;
                    return false;
                }
            });
            return b;
        } else {
            return false;
        }

    }


    /**
     * 校验所属线路是否相等
     */
    function checkTransLineId() {
        //获取所属线路id
        var lineIdList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["transLineId"];
        });
        if (lineIdList.length === 1) {
            return false;
        }
        return !isRepeat(lineIdList);
    }
    /**
     * 判断数组是否有相同的元素
     * @param arr
     * @returns {boolean}
     */
    function isRepeat(arr){
        var hash = {};
        for(var i in arr) {
            if(hash[arr[i]])
                return true;
            hash[arr[i]] = true;
        }
        return false;
    }
</script>
</body>
</html>