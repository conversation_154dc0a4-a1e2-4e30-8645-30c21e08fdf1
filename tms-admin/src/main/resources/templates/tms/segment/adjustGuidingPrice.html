<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('调整运费指导价')"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            background-color: #f4f6f9;
            font-size: 14px;
        }
        .card-container {
            max-width: 600px;
            margin: 15px auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card-body {
            background-color: #ffffff;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .section-divider {
            height: 1px;
            background-color: #e9ecef;
            margin: 15px 0;
            width: 100%;
        }
        .price-input-container {
            display: flex;
            align-items: center;
        }
        .price-input-container label {
            margin-right: 10px;
            white-space: nowrap;
        }
        #guidingPrice {
            max-width: 150px;
        }
        .address-row {
            display: flex;
            margin-bottom: 10px;
            align-items: center;
            justify-content: space-around; /* Center the entire row */
        }
        .address-column {
            flex: 0 1 auto; /* Allow columns to shrink */
            margin: 0 10px; /* Reduce margin */
            text-align: center; /* Center text within columns */
        }
        .address-route {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .address-route i {
            color: #007bff;
            font-size: 18px;
        }
        .address-item {
            text-align: left;
            padding: 3px 0;
            margin-bottom: 3px;
            font-size: 17px;
        }
        /* Rest of the previous CSS remains the same */
        .vehicle-cargo-row {
            display: flex;
            margin-bottom: 10px;
            justify-content: space-evenly;
            align-items: center;
        }
        .vehicle-column, .cargo-column {
            /*flex: 1;*/
            /*margin-right: 10px;*/
        }
        .vehicle-item, .cargo-item {
            padding: 5px 0;
            margin-bottom: 5px;
            font-size: 14px;
        }
        .price-summary {
            display: flex;
            justify-content: space-around;
            background-color: #f1f3f5;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 15px;
        }
        .price-item {
            text-align: left;
        }
        .price-item strong {
            color: #495057;
            margin-right: 5px;
        }
        .price-item span {
            color: #007bff;
            font-weight: 600;
        }
        .guidance-hint {
            font-size: 13px;
            margin-left: 10px;
            color: #007bff;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="card-container">
        <div class="card-body">
            <form id="form-guiding-price" class="form-horizontal" novalidate="novalidate">
                <!-- Hidden input for segmentId -->
                <input name="segmentId" th:value="${segmentId}" type="hidden">

                <!-- Guiding Price Input Section -->
                <div class="form-group price-input-container">
                    <label>新指导价</label>
                    <input type="text" class="form-control" id="guidingPrice" name="guidingPrice"
                           oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                           placeholder="输入新指导价">
                    <select id="updateType" name="updateType" class="form-control" style="width: auto; margin-left: 10px;">
                        <option value="0">仅更新单据指导价</option>
                        <option value="1" th:disabled="${refPriceCode == 0}">更新单据与系统指导价</option>
                    </select>
                </div>

                <!-- Section Divider -->
                <div class="section-divider"></div>

                <!-- Shipping Addresses Section -->
                <div class="form-group">
                    <div class="address-row">
                        <!-- 发货地址 -->
                        <div class="address-column">
                            <div class="address-item">
                                <th:block th:each="addr, iter : ${address}" th:if="${addr.addressType == 0}">
                                    <span th:text="${addr.provinceName + ' ' + addr.cityName + ' ' + addr.areaName}"></span>
                                </th:block>
                            </div>
                        </div>

                        <!-- 路线箭头 -->
                        <div class="address-route">
                            <i class="fas fa-long-arrow-alt-right"></i>
                        </div>

                        <!-- 到货地址 -->
                        <div class="address-column">
                            <div class="address-item">
                                <th:block th:each="addr, iter : ${address}" th:if="${addr.addressType == 1}">
                                    <div th:text="${addr.provinceName + ' ' + addr.cityName + ' ' + addr.areaName}"></div>
                                </th:block>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vehicle and Cargo Section -->
                <div class="form-group">
                    <div class="vehicle-cargo-row">
                        <!-- 车型 -->
                        <div class="vehicle-column">
                            <div class="vehicle-item">
                                <span th:text="${segCar}"></span>
                            </div>
                        </div>

                        <!-- 货品 -->
                        <div class="cargo-column">
                            <div class="cargo-item">
                                <span th:text="${goodsName}"></span>
                            </div>
                        </div>
                        <div class="cargo-column">
                            <div class="cargo-item">
                                <span th:text="${isFullCar}"></span>
                            </div>
                        </div>
                        <div class="cargo-column">
                            <div class="cargo-item">
                                <span th:text="${isOversize}"></span>
                            </div>
                        </div>
                    </div>
                </div>

<!--                &lt;!&ndash; Section Divider &ndash;&gt;-->
<!--                <div class="section-divider"></div>-->

                <!-- Price Information Section -->
                <div class="price-summary">
                    <div class="price-item">
                        <strong>单据指导价：</strong>
                        <span th:text="${segRefPrice != null and segRefPrice != '' ? '￥' + #numbers.formatDecimal(segRefPrice, 1, 2) : '暂无'}" id="documentGuidingPrice"></span>
                    </div>
                    <div class="price-item">
                        <strong>系统指导价：</strong>
                        <span th:text="${refPrice != null and refPrice != '' ? '￥' + #numbers.formatDecimal(refPrice, 1, 2) : '暂无'}" id="systemGuidingPrice"></span>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    $(function () {
        // 初始化页面时根据refPriceCode判断是否需要更新系统指导价
/*        var refPriceCode = [[${refPriceCode}]];
        var newGuidingPriceInput = $('#newGuidingPrice');
        var refPriceHint = $('#refPriceHint');

        newGuidingPriceInput.on('input', function() {
            var newPrice = parseFloat($(this).val());
            var refPrice = parseFloat([[${refPrice}]]);

            if (newPrice !== refPrice) {

            }else {
                refPriceHint.html('输入金额与系统指导价相同');

            }

            // 根据refPriceCode判断是否需要提示更新系统指导价
            if (refPriceCode === '1') {
                if (newPrice !== documentPrice) {
                    refPriceHint.html('系统将更新指导价');
                } else {
                    refPriceHint.html('');
                }
            } else {
                refPriceHint.html('');
            }
        });*/
    });

    /**
     * 提交处理函数
     */
    function submitHandler() {
        var newPrice = $('#guidingPrice').val();

        // 简单验证
        if (!newPrice) {
            layer.msg('请输入新的指导价', {icon: 2});
            return false;
        }

        // 序列化表单数据
        var data = $('#form-guiding-price').serialize();
        // 发送保存请求
        $.operate.save(ctx + "tms/segment/adjust_guiding_price", data);
    }
</script>
</body>
</html>