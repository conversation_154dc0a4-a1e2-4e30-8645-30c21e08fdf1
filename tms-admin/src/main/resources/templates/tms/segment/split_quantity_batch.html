<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('拆量')" />

</head>

<style>
    .my-table {
        border-collapse: collapse; /*将表格边框合并*/
        width: 100%; /*设置表格宽度*/
    }

    .my-table thead th {
        background-color: #f5f5f5; /*设置表头背景色*/
        font-size: 15px; /*设置表头字体大小*/
        font-weight: bold; /*设置表头字体加粗*/
        text-align: center; /*设置表头文本居中*/
        border-bottom: 2px solid #ddd; /*设置表头下方粗实线*/
        height: 40px;
    }

    .my-table tbody {
        border-bottom: 2px solid #ddd; /*设置tbody与tbody之间的细实线*/

    }

    .my-table tbody tr {
        border-bottom: none; /*取消tbody中每行之间的分隔线*/
    }

    .my-table tbody td {
        padding-inline: 1px;
        padding-block: 5px;
        border-right-style: none;
        border-left-style: none;
        border-top-style: none;
    }

    .my-table tbody:last-of-type {
        border-bottom: none; /*去掉最后一个tbody的下边框线*/
    }
    thead th {
        text-align: center;
    }

</style>
<body>
<div class="form-content">
    <form id="form-split_quantity-add" class="form-horizontal" novalidate="novalidate">
        <div class="layui-panel mt10">
            <div style="padding:10px;">
                <div class="row no-gutter">
                    <div class="col-md-12">
                        <table class="table table-bordered mt10">
                            <thead style="background: #f7f8fa">
                            <tr>
                                <th style="width: 15%;" colspan="3">货品信息</th>
                                <th style="width: 15%;" colspan="3">原始货量</th>
                                <th style="width: 15%;" colspan="3">待拆货量</th>
                            </tr>
                            <tr>
                                <th style="width: 10%;">货品编码</th>
                                <th style="width: 10%;">货品名称</th>
                                <th style="width: 20%;">客户单号</th>
                                <th style="width: 10%;">件数</th>
                                <th style="width: 10%;">重量</th>
                                <th style="width: 10%;">体积</th>
                                <th style="width: 10%;">件数</th>
                                <th style="width: 10%;">重量</th>
                                <th style="width: 10%;">体积</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="segPackGoods,segPackGoodsStat : ${segPackGoodsList}">
                                <td>
                                    <span th:text="${segPackGoods.goodsCode}"></span>
                                </td>
                                <td><span th:text="${segPackGoods.goodsName}"></span></td>
                                <td><span th:text="${segPackGoods.custOrderno}"></span></td>
                                <td><span th:id="|num_all_${segPackGoods.segPackGoodsId}|" th:text="${segPackGoods.num}"></span></td>
                                <td><span th:id="|weight_all_${segPackGoods.segPackGoodsId}|" th:text="${segPackGoods.weight}"></span></td>
                                <td><span th:id="|volume_all_${segPackGoods.segPackGoodsId}|" th:text="${segPackGoods.volume}"></span></td>
                                <td><span th:id="|num_over_${segPackGoods.segPackGoodsId}|"
                                          th:text="${segPackGoods.num}"
                                          th:style="${segPackGoods.num > 0 ? 'color: #ec4758;':''}"></span></td>
                                <td><span th:id="|weight_over_${segPackGoods.segPackGoodsId}|"
                                          th:text="${segPackGoods.weight}"
                                          th:style="${segPackGoods.weight > 0 ? 'color: #ec4758;':''}"></span></td>
                                <td><span th:id="|volume_over_${segPackGoods.segPackGoodsId}|"
                                          th:text="${segPackGoods.volume}"
                                          th:style="${segPackGoods.volume > 0 ? 'color: #ec4758;':''}"></span></td>
                            </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group mt10" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">拆量</a>

                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body" id="infoTab">

                        <div style="display: flex; align-items: center;">
                            <span style="margin-right: 10px;">
                                <a class="btn btn-primary btn-sm" onclick="addData()">
                                    <i class="fa fa-align-justify"></i> 新增运段
                                </a>
                            </span>
                            <span style="margin-right: 5px;">
                                <button type="button" class="btn btn-xs btn-primary" onclick="avgSplit()" style="margin-left: 5px;">
<!--                                    <i class="fa fa-reply-all"></i>-->
                                    平均拆分
                                </button>
                            </span>
                            <span style="display: flex; gap: 5px;">
                                <input id="splitNum" oninput="$.numberUtil.onlyNumber(this);calculateSplits(0)" placeholder="件数"
                                       class="form-control" type="text" style="width: 80px;" autocomplete="off">
                                <input id="splitWeight" oninput="$.numberUtil.onlyNumber(this);calculateSplits(1)" placeholder="重量"
                                       class="form-control" type="text" style="width: 80px;" autocomplete="off">
                                <input id="splitVolume" oninput="$.numberUtil.onlyNumber(this);calculateSplits(2)" placeholder="体积"
                                       class="form-control" type="text" style="width: 80px;" autocomplete="off">
                            </span>
                            <span id="splitInfo" style="margin-left: 10px;display: none">共拆分
                                <span id="splitCount">0</span> 单
                            </span>

                        </div>

                        <div class="pt5" id="splitSegment_0" >
                            <table class="table table-bordered mt5">
                                <thead style="background: #f7f8fa">
                                <tr>
<!--                                    <th style="width: 5%">-->
<!--                                        <a class="collapse-link" style="font-size: 22px;color: #1ab394;"-->
<!--                                           onclick="addData()" title="新增行">+</a>-->
<!--                                    </th>-->
                                    <th style="width: 15%;">
                                        <div style="display: inline-block;float:left;">
                                            <a
                                                    class="fa fa-exchange"
                                                    style="color: #1ab394;font-size: 20px;"
                                                    onclick="repData(0)" title="剩余货量回填"></a>
                                        </div>
                                        <span>货品编号（1）</span>
                                    </th>
                                    <th style="width: 15%;">货品名称</th>
                                    <th style="width: 20%;">客户单号</th>
                                    <th style="width: 15%;">件数</th>
                                    <th style="width: 15%;">重量</th>
                                    <th style="width: 15%;">体积</th>
                                </tr>
                                </thead>
                                <tbody >
                                    <tr th:each="segPackGoods,segPackGoodsStat : ${segPackGoodsList}">
                                        <input type="hidden" th:id="|segPackGoodsId_0_${segPackGoods.segPackGoodsId}|" th:value="${segPackGoods.segPackGoodsId}">
                                        <td>
                                            <label style="float:left;" th:text="${segPackGoods.goodsCode}"></label>
                                        </td>
                                        <td><label style="float:left;" th:text="${segPackGoods.goodsName}"></label></td>
                                        <td><label style="float:left;" th:text="${segPackGoods.custOrderno}"></label></td>
                                        <td>
                                            <input th:id="|num_0_${segPackGoods.segPackGoodsId}|"
                                                   name="splitNum" th:data-id="${segPackGoods.segPackGoodsId}"
                                                   oninput="$.numberUtil.onlyNumber(this);calculateCount('num',0,this.getAttribute('data-id'))"
                                                   placeholder="" class="form-control" type="text" autocomplete="off" >
                                        </td>
                                        <td><input th:id="|weight_0_${segPackGoods.segPackGoodsId}|"
                                                   name="splitWeight" th:data-id="${segPackGoods.segPackGoodsId}"
                                                   oninput="$.numberUtil.onlyNumber(this);calculateCount('weight',0,this.getAttribute('data-id'))"
                                                   placeholder="" class="form-control" type="text" autocomplete="off"></td>
                                        <td><input th:id="|volume_0_${segPackGoods.segPackGoodsId}|"
                                                   name="splitVolume"  th:data-id="${segPackGoods.segPackGoodsId}"
                                                   oninput="$.numberUtil.onlyNumber(this);calculateCount('volume',0,this.getAttribute('data-id'))"
                                                   placeholder="" class="form-control" type="text" autocomplete="off"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="pt5" id="splitSegment_1">
                            <table class="table table-bordered">
                                <thead style="background: #f7f8fa">
                                <tr>
<!--                                    <th style="width: 5%">-->
<!--                                        <a class="collapse-link" style="font-size: 22px;color: #1ab394;"-->
<!--                                           onclick="addData()" title="新增行">+</a>-->
<!--                                    </th>-->
                                    <th style="width: 15%;">
                                        <div style="display: inline-block;float:left;">
                                            <a
                                                    class="fa fa-exchange"
                                                    style="color: #1ab394;font-size: 20px;"
                                                    onclick="repData(1)" title="剩余货量回填"></a>
                                        </div>
                                        <span>货品编号（2）</span>
                                    </th>
                                    <th style="width: 15%;">货品名称</th>
                                    <th style="width: 20%;">客户单号</th>
                                    <th style="width: 15%;">件数</th>
                                    <th style="width: 15%;">重量</th>
                                    <th style="width: 15%;">体积</th>
                                </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="segPackGoods,segPackGoodsStat : ${segPackGoodsList}">
                                        <input type="hidden" th:id="|segPackGoodsId_1_${segPackGoods.segPackGoodsId}|" th:value="${segPackGoods.segPackGoodsId}">
                                        <td>
                                            <label style="float:left;" th:text="${segPackGoods.goodsCode}"></label>
                                        </td>
                                        <td><label style="float:left;" th:text="${segPackGoods.goodsName}"></label></td>
                                        <td><label style="float:left;" th:text="${segPackGoods.custOrderno}"></label></td>
                                        <td>
                                            <input th:id="|num_1_${segPackGoods.segPackGoodsId}|"
                                                   name="splitNum" th:data-id="${segPackGoods.segPackGoodsId}"
                                                   oninput="$.numberUtil.onlyNumber(this);calculateCount('num',1,this.getAttribute('data-id'))"
                                                   placeholder="" class="form-control" type="text" autocomplete="off" >
                                        </td>
                                        <td><input th:id="|weight_1_${segPackGoods.segPackGoodsId}|"
                                                   name="splitWeight" th:data-id="${segPackGoods.segPackGoodsId}"
                                                   oninput="$.numberUtil.onlyNumber(this);calculateCount('weight',1,this.getAttribute('data-id'))"
                                                   placeholder="" class="form-control" type="text" autocomplete="off"></td>
                                        <td><input th:id="|volume_1_${segPackGoods.segPackGoodsId}|"
                                                   name="splitVolume"  th:data-id="${segPackGoods.segPackGoodsId}"
                                                   oninput="$.numberUtil.onlyNumber(this);calculateCount('volume',1,this.getAttribute('data-id'))"
                                                   placeholder="" class="form-control" type="text" autocomplete="off"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--订单拆量 end-->
                    </div>
                </div>
            </div>

        </div>

    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer" />
<script th:inline="javascript">
    //是否是车队
    var isFleet = [[${isFleet}]];
    var segmentId = [[${segmentId}]];

    let segPackGoodsList = [[${segPackGoodsList}]];

    var prefix = isFleet ? ctx + "fleet/segment" : ctx + "tms/segment";

    const numCount = [[${numCount}]];
    const weightCount = [[${weightCount}]];
    const volumeCount = [[${volumeCount}]];

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
    });

    var split_segment_index = 1

    function addData() {
        split_segment_index++

        let ind = split_segment_index

        let goodsHtml = ``
        let segPackGoodsLength = segPackGoodsList.length;
        for (let i = 0; i < segPackGoodsLength; i++) {
            let segPackGoods = segPackGoodsList[i]

            goodsHtml = goodsHtml
                + `<tr th:each="segPackGoods,segPackGoodsStat : ${segPackGoodsList}">
                        <input type="hidden" id="segPackGoodsId_${ind}_${segPackGoods.segPackGoodsId}" value="${segPackGoods.segPackGoodsId}">
                        <td>
                            <label>${segPackGoods.goodsCode == null ? '' : segPackGoods.goodsCode}</label>
                        </td>
                        <td><label>${segPackGoods.goodsName}</label></td>
                        <td><label>${segPackGoods.custOrderno == null ? '' : segPackGoods.custOrderno}</label></td>
                        <td>
                            <input id="num_${ind}_${segPackGoods.segPackGoodsId}"
                                   name="splitNum"
                                   oninput="$.numberUtil.onlyNumber(this);calculateCount('num',${ind},'${segPackGoods.segPackGoodsId}')"
                                   placeholder="" class="form-control" type="text" autocomplete="off" >
                        </td>
                        <td><input id="weight_${ind}_${segPackGoods.segPackGoodsId}"
                                   name="splitWeight"
                                   oninput="$.numberUtil.onlyNumber(this);calculateCount('weight',${ind},'${segPackGoods.segPackGoodsId}')"
                                   placeholder="" class="form-control" type="text" autocomplete="off"></td>
                        <td><input id="volume_${ind}_${segPackGoods.segPackGoodsId}"
                                   name="splitVolume"
                                   oninput="$.numberUtil.onlyNumber(this);calculateCount('volume',${ind},'${segPackGoods.segPackGoodsId}')"
                                   placeholder="" class="form-control" type="text" autocomplete="off"></td>
                    </tr>
                `;
        }

        let html = `
                    <div class="pt5" id="splitSegment_${ind}">
                        <table class="table table-bordered">
                            <thead style="background: #f7f8fa">
                                <tr>
                                    <th style="width: 15%;">
                                        <div style="display: inline-block;float:left;">
                                            <a
                                                class="fa fa-times-circle"
                                                style="color: #fd8481;font-size: 20px;"
                                                onclick="rmData(${ind})" title="删除选择行"></a>
                                        </div>
                                        <div style="display: inline-block;float:left;margin-left: 10px">
                                            <a
                                                class="fa fa-exchange"
                                                style="color: #1ab394;font-size: 20px;"
                                                onclick="repData(${ind})" title="剩余货量回填"></a>
                                        </div>
                                        <span>货品编号</span>
                                    </th>
                                    <th style="width: 15%;">货品名称</th>
                                    <th style="width: 20%;">客户单号</th>
                                    <th style="width: 15%;">件数</th>
                                    <th style="width: 15%;">重量</th>
                                    <th style="width: 15%;">体积</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${goodsHtml}
                            </tbody>
                        </table>
                    </div>

        `
        $("#infoTab").append(html);

        updateGoodsNumberIndex()
        return ind
    }

    function rmData(id) {
        $("#splitSegment_"+id).remove();

        for (let j = 0; j < segPackGoodsList.length; j++) {
            let oldGoods = segPackGoodsList[j];

            let numAll = parseFloat($(`#num_all_${oldGoods.segPackGoodsId}`).text()) || 0;
            let weightAll = parseFloat($(`#weight_all_${oldGoods.segPackGoodsId}`).text()) || 0;
            let volumeAll = parseFloat($(`#volume_all_${oldGoods.segPackGoodsId}`).text()) || 0;


            var numTotal = 0;
            $('[id^="num_"][id$="' + oldGoods.segPackGoodsId + '"]').each(function () {
                var value = parseFloat($(this).val()) || 0;
                numTotal += value;
            });

            var weightTotal = 0;
            $('[id^="weight_"][id$="' + oldGoods.segPackGoodsId + '"]').each(function () {
                var value = parseFloat($(this).val()) || 0;
                weightTotal += value;
            });
            var volumeTotal = 0;
            $('[id^="volume_"][id$="' + oldGoods.segPackGoodsId + '"]').each(function () {
                var value = parseFloat($(this).val()) || 0;
                volumeTotal += value;
            });

            $(`#num_over_${oldGoods.segPackGoodsId}`).text(parseFloat(numAll-numTotal))
            if (numAll - numTotal > 0) {
                $(`#num_over_${oldGoods.segPackGoodsId}`).css("color", "#ec4758");
            }else {
                $(`#num_over_${oldGoods.segPackGoodsId}`).css("color", "");
            }

            $(`#weight_over_${oldGoods.segPackGoodsId}`).text(parseFloat(weightAll-weightTotal));
            if (weightAll-weightTotal > 0) {
                $(`#weight_over_${oldGoods.segPackGoodsId}`).css("color", "#ec4758");
            }else {
                $(`#weight_over_${oldGoods.segPackGoodsId}`).css("color", "");
            }
            $(`#volume_over_${oldGoods.segPackGoodsId}`).text(parseFloat(volumeAll-volumeTotal))
            if (volumeAll-volumeTotal > 0) {
                $(`#volume_over_${oldGoods.segPackGoodsId}`).css("color", "#ec4758");
            }else {
                $(`#volume_over_${oldGoods.segPackGoodsId}`).css("color", "");
            }
        }

        updateGoodsNumberIndex()
    }

    function repData(segId) {
        for (let i = 0; i < segPackGoodsList.length; i++) {
            let oldGoods = segPackGoodsList[i];

            let num_over = $(`#num_over_${oldGoods.segPackGoodsId}`).text();
            $(`#num_${segId}_${oldGoods.segPackGoodsId}`).val(num_over)

            let weight_over = $(`#weight_over_${oldGoods.segPackGoodsId}`).text();
            $(`#weight_${segId}_${oldGoods.segPackGoodsId}`).val(weight_over)

            let volume_over = $(`#volume_over_${oldGoods.segPackGoodsId}`).text();
            $(`#volume_${segId}_${oldGoods.segPackGoodsId}`).val(volume_over)

            calculateCount("num", segId, oldGoods.segPackGoodsId);
            calculateCount("weight", segId, oldGoods.segPackGoodsId);
            calculateCount("volume", segId, oldGoods.segPackGoodsId);
        }
    }

    function calculateCount(type, segId, segPackGoodsId) {
        //该货品金额合计
        let all = parseFloat($(`#${type}_all_${segPackGoodsId}`).text()) || 0;
        let over = parseFloat($(`#${type}_over_${segPackGoodsId}`).text()) || 0;

        //当前输入框的值
        let inputVal = parseFloat($(`#${type}_${segId}_${segPackGoodsId}`).val()) || 0;

        var total = 0;
        // 使用属性选择器来获取所有匹配的元素
        $('[id^="' + type + '_"][id$="' + segPackGoodsId + '"]').each(function () {
            var value = parseFloat($(this).val()) || 0;
            total += value;
        });

        //已经拆的量
        let already = total-inputVal;

        if (total >= all) {
            let number = parseFloat(all-already).toFixed(5);
            $(`#${type}_${segId}_${segPackGoodsId}`).val(number)

            $(`#${type}_over_${segPackGoodsId}`).text(0.0)
            $(`#${type}_over_${segPackGoodsId}`).css("color", "");
        }else {
            $(`#${type}_over_${segPackGoodsId}`).text(parseFloat(all-total).toFixed(5))
            $(`#${type}_over_${segPackGoodsId}`).css("color", "#ec4758");
        }

    }

    let splitCount = 0;

    function calculateSplits(type) {
        if (segPackGoodsList.length > 1) {
            // $.modal.msgWarning("仅支持单货品拆分。")
            return
        }

        const splitNum = parseFloat($('#splitNum').val()) || null;
        const splitWeight = parseFloat($('#splitWeight').val()) || null;
        const splitVolume = parseFloat($('#splitVolume').val()) || null;

        //总单数
        let lastBatchItems
        if (type === 0) {
            splitCount = Math.ceil(numCount / splitNum);
            // lastBatchItems = numCount % splitNum;
            // let ct = lastBatchItems === 0 ? splitCount : splitCount -1
            // let ct = splitCount

            let rate = splitNum/numCount

            let weight = (weightCount * rate).toFixed(2);
            $('#splitWeight').val(weight);

            let volume = (volumeCount * rate).toFixed(2);
            $('#splitVolume').val(volume);

        }else if (type === 1) {
            splitCount = Math.ceil(weightCount / splitWeight);
            // lastBatchItems = weightCount % splitWeight;
            // let ct = lastBatchItems === 0 ? splitCount : splitCount -1
            // let ct = splitCount
            let rate = splitWeight/weightCount


            let num = (numCount * rate).toFixed(2);
            $('#splitNum').val(num);

            let volume = (volumeCount * rate).toFixed(2);
            $('#splitVolume').val(volume);

        }else if (type === 2) {
            splitCount = Math.ceil(volumeCount / splitVolume);
            // lastBatchItems = volumeCount % splitVolume;
            // let ct = lastBatchItems === 0 ? splitCount : splitCount -1
            // let ct = splitCount
            let rate = splitVolume/volumeCount

            let num = (numCount * rate).toFixed(2);
            $('#splitNum').val(num);

            let weight = (weightCount * rate).toFixed(2);
            $('#splitWeight').val(weight);
        }

        if (splitCount > 0) {
            $("#splitInfo").show()
            $("#splitCount").text(splitCount)
        }else {
            $('#splitInfo').hide();
        }
    }

    function avgSplit() {
        if (segPackGoodsList.length > 1) {
            $.modal.msgWarning("仅支持单货品拆分。")
            return
        }

        let segPackGoodsId = segPackGoodsList[0].segPackGoodsId;


        $(`#num_0_${segPackGoodsId}`).val("");
        $(`#weight_0_${segPackGoodsId}`).val("");
        $(`#volume_0_${segPackGoodsId}`).val("");
        $(`#num_1_${segPackGoodsId}`).val("");
        $(`#weight_1_${segPackGoodsId}`).val("");
        $(`#volume_1_${segPackGoodsId}`).val("");
        for (let i = 2; i <= split_segment_index; i++) {
            if ($(`#splitSegment_${i}`).length) {
                rmData(i)
            }
        }
        split_segment_index = 1

        const splitNum = parseFloat($('#splitNum').val()) || null;
        const splitWeight = parseFloat($('#splitWeight').val()) || null;
        const splitVolume = parseFloat($('#splitVolume').val()) || null;


        for (let i = 0; i < splitCount; i++) {
            let number = 0;
            if (i < 2) {
                number = i;
            }else {
                number = addData();
            }

            let num = splitNum
            let weight = splitWeight
            let colume = splitVolume

            if (i === splitCount - 1) {
                repData(number)
            }else {
                $(`#num_${number}_${segPackGoodsId}`).val(num).trigger('input');

                $(`#weight_${number}_${segPackGoodsId}`).val(weight).trigger('input');

                $(`#volume_${number}_${segPackGoodsId}`).val(colume).trigger('input');
            }

        }

    }

    function updateGoodsNumberIndex() {
        $('[id^="splitSegment_"]').each(function(index) {
            // 找到当前splitSegment中的货品编号文本
            var numberSpan = $(this).find('thead tr th:nth-child(1) span');
            // 更新序号，index从0开始，显示需要+1
            numberSpan.text('货品编号（' + (index + 1) + '）');
        });
    }

    /**
     * 提交
     */
    function submitHandler() {
        for (let i = 0; i < segPackGoodsList.length; i++) {
            let segPackGoodsId = segPackGoodsList[i].segPackGoodsId;

            let overNum = parseFloat($(`#num_over_${segPackGoodsId}`).text()) || 0;
            let overWeight = parseFloat($(`#weight_over_${segPackGoodsId}`).text()) || 0;
            let overVolume = parseFloat($(`#volume_over_${segPackGoodsId}`).text()) || 0;

            if (overNum !== 0 || overWeight !== 0 || overVolume !== 0) {
                $.modal.alertWarning("存在未拆的货量。")
                return
            }
        }

        let data = {}
        data["segmentId"] = segmentId;
        //运段
        let splitSegmentVOList = []


        //循环运段
        for (let i = 0; i < split_segment_index + 1; i++) {
            let splitGoodsVOList = []
            let splitSegment = {}

            //循环货品
            for (let j = 0; j < segPackGoodsList.length; j++) {
                let splitGoodsVO = {}
                let oldGoods = segPackGoodsList[j];

                let num = parseFloat($(`#num_${i}_${oldGoods.segPackGoodsId}`).val()) || 0;
                let weight = parseFloat($(`#weight_${i}_${oldGoods.segPackGoodsId}`).val()) || 0;
                let volume = parseFloat($(`#volume_${i}_${oldGoods.segPackGoodsId}`).val()) || 0;

                if (num === 0 && weight === 0 && volume === 0) {
                    continue;
                }

                splitGoodsVO["segPackGoodsId"] = oldGoods.segPackGoodsId;
                splitGoodsVO["num"] = num
                splitGoodsVO["weight"] = weight
                splitGoodsVO["volume"] = volume

                if (weight < 0) {
                    $.modal.alertWarning("重量必须大于0。")
                    return
                }
                splitGoodsVOList.push(splitGoodsVO);

            }

            splitSegment["splitGoodsVOList"] = splitGoodsVOList
            splitSegmentVOList.push(splitSegment)
        }

        splitSegmentVOList = splitSegmentVOList.filter(item => {
            return item.splitGoodsVOList.length > 0;
        })

        if (splitSegmentVOList.length < 2) {
            $.modal.alertWarning("请确保至少有两个运段。")
            return
        }

        data["splitSegmentVOList"] = splitSegmentVOList;

        $.ajax({
            url: prefix + "/splitQuantity_batch",
            type: "post",
            dataType: "json",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify(data),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
                $.modal.disable();
            },
            success: function(result) {
                if (result.code == 0) {
                    $.modal.msgSuccess(result.msg);
                }else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
                $.modal.enable();

                $.operate.successTabCallback(result);
            }
        });

    }
</script>
</body>

</html>