<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<!-- 容器 -->
<div class="container-div">
    <!-- 一行 -->
    <div class="row">
        <!-- 搜索条件区域 -->
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
<input type="hidden" th:value="${carrierId}" name="carrierId">
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="col-sm-4">车牌：</label>
                            <div class="col-sm-8">
                                <input class="form-control" name="carno">
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-5">

                    </div>
                    <div class="col-sm-2">
                        <div class="form-group">

                            <a class="btn btn-primary btn-rounded btn-sm" onclick="_$this.searchPre();"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="_$this.resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- 按钮区域 -->
        <div class="btn-group-sm" id="toolbar">

        </div>
        <!-- 列表 -->
        <div class="col-sm-12 select-table table-striped toofoot pm">
            <table id="bootstrap-table" class="text-nowrap" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: distpicker" />
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    let prefix  = ctx + "tms/segment";
    let vehicle = {};
    let _$this  = vehicle;


    /* *************************************************************
     **************  初始化部分 **************************************
     **************************************************************/
    /**
     *  初始化列表列
     */
    vehicle.initColumn = function(){
        let columns              = [
            {
                title: '车牌',
                align: 'left',
                field: 'carno',

            },
            {
                title: '车辆类型',
                align: 'left',
                field: 'carTypeName',

            },
            {
                title: '车长',
                align: 'left',
                field: 'carLenName'
            },
            {
                title: '车辆合规状态',
                align: 'left',
                field: 'g7Syn',
                formatter: function status(value,row) {
                    if(value == 2){
                        return '合规';
                    }
                    if(value == 1){
                        return '不合规';
                    }
                    return '-';
                }
            }
        ];

        let options = {};
        options.url              = prefix + "/carrierTopCarList";
        //options.exportUrl        = prefix + "/delExport";
        options.showToggle       = false;
        options.showSearch       = false;
        options.showRefresh       = false;
        options.singleSelect       = false;
        options.showColumns      = false;
        options.clickToSelect    = true;
        options.fixedColumns     = true;
        options.rememberSelected = true;
        options.showFooter       = true;
        //options.fixedNumber      = 3;
        options.height           = 560;
        //options.uniqueId         = "entrustId";
        //options.modalName        = "运单";
        options.columns          = columns;
        /*options.onPostBody       = function () {
            //查询合计总金额
            _$this.summaryG7Shipping();
        }*/

        $.table.init(options);
    }
    /**
     * 初始化控件
     */
    _$this.initControl = function(){
        /*layui.use('laydate', function() {
           var laydate = layui.laydate;
           laydate.render({
               elem: '#startDate',
               type: 'date',
               trigger: 'click'
           });
           laydate.render({
               elem: '#endDate',
               type: 'date',
               trigger: 'click'
           });


       });*/
        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",[[${deliProvinceId}]],[[${deliCityId}]],[[${deliAreaId}]]);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",[[${arriProvinceId}]],[[${arriCityId}]],[[${arriAreaId}]]);
    }


    /**
     * 初始化画面
     */
    vehicle.initPage = function(){
        //初始化控件
        _$this.initControl();

        //初始化列表列
        _$this.initColumn();
    }

    /* *************************************************************
     ************** 事件处理部分 *************************************
     **************************************************************/


    /**
     * 搜索
     */
    _$this.searchPre = function() {
        var data = {};
        //data.salesDept = $.common.join($('#salesDept').selectpicker('val'));


        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    _$this.resetPre = function() {
        //$(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }


    /* *************************************************************
     ************** 页面加载 *************************************
     **************************************************************/
    $(function(){
        _$this.initPage();
    });
</script>
</body>
</html>