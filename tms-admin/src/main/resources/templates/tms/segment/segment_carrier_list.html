<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单调度列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 60px;
        line-height: 26px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .avgprice{
        background: #fffde9;
        padding: 0px 10px;
        min-width: 700px;
        margin-left: 10px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" th:name="segmentId" th:value="${segmentId}">
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="flex">
                            <label class="flex_left">车长：</label>
                            <div class="flex_right">
                                <select name="carLen" id="carLen" class="form-control"  th:with="type=${@dict.getType('car_len')}">
                                    <option value="">-- 请选择 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${carLen eq dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="flex">
                            <label class="flex_left">车型：</label>
                            <div class="flex_right">
                                <select name="carType" id="carType" class="form-control" th:with="type=${@dict.getType('car_type')}">
                                    <option value="">-- 请选择 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${carType eq dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-3">车牌号：</label>-->
                            <div class="col-sm-12">
                                <input name="carNo" id="carNo" placeholder="请输入车牌号" class="form-control " type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="flex">
                            <label class="flex_left" style="width: 80px">承运商：</label>
                            <div class="flex_right">
                                <input name="carrierName" id="carrierName" placeholder="请输承运商名称" class="form-control " type="text" th:value="${carrierName}"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <!--                            <label class="col-sm-3">调度日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" placeholder="调度开始日期" class="time-input form-control"
                                       id="dispatcherDateStart"  name="dispatcherDateStart">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" placeholder="调度结束日期" class="time-input form-control"
                                       id="dispatcherDateEnd"  name="dispatcherDateEnd">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-5">
                        <div class="col-sm-4">
                            <div class="form-group">
<!--                                <label class="col-sm-6">提货方地址：</label>-->
                                <div class="col-sm-12">
                                    <select  name="deliProvinceId" id="deliProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <select name="deliCityId" id="deliCityId"  class="form-control valid" aria-invalid="false"></select>
                        </div>
                        <div class="col-sm-4">
                            <select name="deliAreaId" id="deliAreaId"  class="form-control valid" aria-invalid="false"></select>
                        </div>
                    </div>
                    <div class="col-sm-5">
                        <div class="col-sm-1">
                            <i class="fa fa-arrow-circle-right" style="font-size:22px;color: #1ab394"></i>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
<!--                                <label class="col-sm-6">收货方地址：</label>-->
                                <div class="col-sm-12">
                                    <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                        </div>
                        <div class="col-sm-3">
                            <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="flex">
                            <label class="flex_left" style="width: 80px">客户名称：</label>
                            <div class="flex_right">
                                <input name="custAbbr" id="custAbbr" placeholder="请输入客户名称" class="form-control " type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-sm-10">
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <div class="over">
                <div class="fl">
                    <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:segment:segmentHistoryPrice:export">
                        <i class="fa fa-download"></i> 导出
                    </a>
                </div>
                <div class="fl avgprice">
                    <span style="">平均价格：</span><span id="avgPrice" style="color: #ff9521">0</span>
                </div>
            </div>
        </div>

        <div class="col-sm-12 select-table table-striped toofoot">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var deliProvinceId = [[${deliProvinceId}]];
    var deliCityId = [[${deliCityId}]];
    var arriProvinceId = [[${arriProvinceId}]];
    var arriCityId = [[${arriCityId}]];

    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        // $("#deliProvinceId").val(deliProvinceId);
        // $("#deliCityId").val(deliCityId);
        // $("#arriProvinceId").val(arriProvinceId);
        // $("#arriCityId").val(arriCityId)

        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",[[${deliProvinceId}]],[[${deliCityId}]],[[${deliAreaId}]]);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",[[${arriProvinceId}]],[[${arriCityId}]],[[${arriAreaId}]]);

        var options = {
            url: ctx + "tms/segment/segmentHistoryPrice/list",
            showToggle:false,
            showColumns:false,
            showRefresh: false,
            showSearch:false,
            modalName: "",
            uniqueId: "carrierId",
            exportUrl: ctx + "tms/segment/segmentHistoryPrice/export",
            height: 560,
            columns: [
                {
                    title: '承运商名称',
                    field: 'carrierName',
                    align: 'left'
                },
                {
                    title: '客户名称',
                    field: 'custAbbr',
                    align: 'left'
                },
                {
                    title: '联系电话',
                    field: 'phone',
                    align: 'left'
                },
                {
                    title: '车牌',
                    field: 'carNo',
                    align: 'left'
                },
                {
                    title: '车长',
                    field: 'carLenName',
                    align: 'left'
                },
                {
                    title: '车型',
                    field: 'carTypeName',
                    align: 'left'
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.deliProName+row.deliCityName+row.deliAreaName+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriProName+row.arriCityName+row.arriAreaName;
                    }
                },
                {
                    title: '总金额',
                    field: 'costAmount',
                    align: 'left'
                },
                {
                    title: '油卡金额',
                    field: 'oilAmount',
                    align: 'left'
                },
                {
                    title: '调度日期',
                    field: 'dispatcherDate',
                    align: 'left'
                },
                {
                    title: '调度人',
                    field: 'dispatcher',
                    align: 'left'
                },
                {
                    title: '运单号',
                    field: 'lot',
                    align: 'left'
                }
            ]
        };
        $.table.init(options);

        $.table.search();
        loadArv();
    });

    /**
     * 搜索
     */
    function searchPre() {
        // data.deliCityId = deliCityId;
        // data.arriProvinceId = arriProvinceId;
        // data.arriCityId = arriCityId;
        //$.table.search('role-form', data);
        $.table.search();
        loadArv();
    }

    function loadArv(){
        let data = $("#role-form").serializeArray();

        let config = {
            url: ctx + "tms/segment/segmentHistoryPrice/listAvg",
            type: "post",
            dataType: "json",
            data: data,
            beforeSend: function () {
                //$.modal.loading("正在处理中，请稍后...");
                //$.modal.disable();
            },
            success: function(result) {
                let retData = result.data;
                $("#avgPrice").html(retData);
            }
        };
        $.ajax(config)
    }

    /**
     * 重置
     */
    function resetPre() {
        $("#deliAreaId ").val("");
        $("#arriAreaId ").val("");
        $("#role-form")[0].reset();
        searchPre();
    }
</script>
</body>
</html>