<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('新增调度审核')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-segmentCheck-add">
			<div class="form-group">	
				<label class="col-sm-3 control-label">承运商ID：</label>
				<div class="col-sm-8">
					<input id="carrierId" name="carrierId" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">承运商编码：</label>
				<div class="col-sm-8">
					<input id="carrCode" name="carrCode" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">承运商名称：</label>
				<div class="col-sm-8">
					<input id="carrName" name="carrName" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">车牌号ID：</label>
				<div class="col-sm-8">
					<input id="carnoId" name="carnoId" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">车牌号：</label>
				<div class="col-sm-8">
					<input id="carno" name="carno" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">车长：</label>
				<div class="col-sm-8">
					<input id="carLenId" name="carLenId" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">车长名称：</label>
				<div class="col-sm-8">
					<input id="carLenName" name="carLenName" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">车型编码：</label>
				<div class="col-sm-8">
					<input id="carTypeCode" name="carTypeCode" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">车型名称：</label>
				<div class="col-sm-8">
					<input id="carTypeName" name="carTypeName" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">挂车：</label>
				<div class="col-sm-8">
					<input id="trailerId" name="trailerId" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">挂车车牌号：</label>
				<div class="col-sm-8">
					<input id="trailerNo" name="trailerNo" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">司机ID：</label>
				<div class="col-sm-8">
					<input id="driverId" name="driverId" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">司机姓名：</label>
				<div class="col-sm-8">
					<input id="driverName" name="driverName" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">司机手机：</label>
				<div class="col-sm-8">
					<input id="driverMobile" name="driverMobile" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">司机结算方式：</label>
				<div class="col-sm-8">
					<input id="balaType" name="balaType" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">运输方式编码：</label>
				<div class="col-sm-8">
					<input id="transCode" name="transCode" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">运输方式名称：</label>
				<div class="col-sm-8">
					<input id="transName" name="transName" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">要求提货时间：</label>
				<div class="col-sm-8">
					<input id="reqDeliDate" name="reqDeliDate" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">要求到货时间：</label>
				<div class="col-sm-8">
					<input id="reqArriDate" name="reqArriDate" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">备注：</label>
				<div class="col-sm-8">
					<input id="memo" name="memo" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">承运商司机APP显示备注：</label>
				<div class="col-sm-8">
					<input id="appMemo" name="appMemo" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">总件数：</label>
				<div class="col-sm-8">
					<input id="numCount" name="numCount" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">总重量：</label>
				<div class="col-sm-8">
					<input id="weightCount" name="weightCount" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">总体积：</label>
				<div class="col-sm-8">
					<input id="volumeCount" name="volumeCount" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">结算金额：</label>
				<div class="col-sm-8">
					<input id="costAmount" name="costAmount" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">指导价：</label>
				<div class="col-sm-8">
					<input id="guidingprice" name="guidingprice" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">油卡比例：</label>
				<div class="col-sm-8">
					<input id="oilRatio" name="oilRatio" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">运段id集合，逗号分隔：</label>
				<div class="col-sm-8">
					<input id="segmentIds" name="segmentIds" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">预计到达时间：</label>
				<div class="col-sm-8">
					<input id="estimatedArrivalTime" name="estimatedArrivalTime" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">${column.columnComment}：</label>
				<div class="col-sm-8">
					<input id="createUserId" name="createUserId" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">${column.columnComment}：</label>
				<div class="col-sm-8">
					<input id="createTime" name="createTime" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">${column.columnComment}：</label>
				<div class="col-sm-8">
					<input id="updateTime" name="updateTime" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">${column.columnComment}：</label>
				<div class="col-sm-8">
					<input id="updateUserId" name="updateUserId" class="form-control" type="text">
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-3 control-label">${column.columnComment}：</label>
				<div class="col-sm-8">
					<input id="deleteFlag" name="deleteFlag" class="form-control" type="text">
				</div>
			</div>
		</form>
	</div>
    <div th:include="include::footer"></div>
    <script type="text/javascript">
		var prefix = ctx + "tms/segmentCheck"
		$("#form-segmentCheck-add").validate({
			rules:{
				xxxx:{
					required:true,
				},
			},
			focusCleanup: true
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	            $.operate.save(prefix + "/add", $('#form-segmentCheck-add').serialize());
	        }
	    }
	</script>
</body>
</html>
