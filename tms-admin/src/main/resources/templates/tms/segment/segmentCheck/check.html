<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('调度')"/>
</head>
<body>
<div class="form-content">
    <div class="panel-group" id="accordion" th:if="${checkType} == 'single'">
        <input id="segmentIds" name="segmentIds" th:value="${segmentIds}" type="hidden">
        <div class="panel-group" id="accordionOne">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">调度详情</a>
                    </h5>
                </div>
                <!--调度信息 begin-->
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>承运商：</span></label>
                                    <div class="col-sm-8" th:text="${segmentCheck.carrName}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>车牌号：</span></label>
                                    <div class="col-sm-8" th:text="${segmentCheck.carno}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">车长：</label>
                                    <div class="col-sm-8" th:text="${segmentCheck.carLenName}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">车型：</label>
                                    <div class="col-sm-8" th:text="${segmentCheck.carTypeName}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">挂车：</label>
                                    <div class="col-sm-8" th:text="${segmentCheck.trailerNo}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span id="driverNameSpan">司机：</span></label>
                                    <div class="col-sm-8" th:text="${segmentCheck.driverName}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">司机手机：</label>
                                    <div class="col-sm-8" th:text="${segmentCheck.driverMobile}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">指导价：</label>
                                    <div class="col-sm-8" th:text="${segmentCheck.guidingprice}">

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>运输方式：</span></label>
                                    <div class="col-sm-8" th:text="${transName}"></div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>要求提货日期：</span></label>
                                    <div class="col-sm-8"
                                         th:text="${#dates.format(segmentCheck.reqDeliDate, 'yyyy-MM-dd HH时')}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>要求到货日期：</span></label>
                                    <div class="col-sm-8"
                                         th:text="${#dates.format(segmentCheck.reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}">

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>结算金额：</span></label>
                                    <div class="col-sm-8" th:text="${segmentCheck.costAmount}">

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row no-gutter">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总件数：</label>
                                    <div class="col-sm-8" th:text="|${segmentCheck.numCount}件|">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总重量：</label>
                                    <div class="col-sm-8" th:text="|${segmentCheck.weightCount}吨|">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总体积：</label>
                                    <div class="col-sm-8" th:text="|${segmentCheck.volumeCount}m³|">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">油卡比例：</label>
                                    <div class="col-sm-8" th:if="${segmentCheck.oilRatio} != null"
                                         th:text="|${segmentCheck.oilRatio}%|"></div>
                                    <div class="col-sm-8" th:if="${segmentCheck.oilRatio} == null"
                                         th:text="|${segmentCheck.oilRatio}|"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row no-gutter">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10" th:text="${segmentCheck.memo}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row no-gutter">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">app备注：</label>
                                    <div class="col-md-11 col-sm-10" th:text="${segmentCheck.appMemo}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--调度信息end-->

        <div class="panel-group" id="accordionTwo">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">发货单信息</a>
                    </h4>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabThree" class="custom-tab tab table"
                                   style="white-space:nowrap;">
                                <thead>
                                <tr>
                                    <th>发货单号</th>
                                    <th>要求车型</th>
                                    <th>提货方</th>
                                    <th>提货地址</th>
                                    <th>收货方</th>
                                    <th>收货地址</th>
                                    <th>要求提货日期</th>
                                    <th>要求到货日期</th>
                                    <th>货品名称</th>
                                    <th>包装</th>
                                    <th>件数</th>
                                    <th>重量</th>
                                    <th>体积</th>
                                </tr>
                                </thead>
                                <tbody>
                                <div th:remove="tag" th:each="segmentVO,segmentVOStat : ${dispatchSegmentVO}">
                                    <tr th:each="segPackGoods,segPackGoodsStat : ${segmentVO.segPackGoodsList}">
                                        <div th:if="${segPackGoodsStat.index == 0}">
                                            <td th:rowspan="${segPackGoodsStat.size}"
                                                th:text="${segmentVO.invoiceVbillno}"></td>
                                            <td th:rowspan="${segPackGoodsStat.size}"
                                                th:each="dict : ${@dict.getType('car_type')}"
                                                th:if="${dict.dictValue} == ${segmentVO.carType}"
                                                th:text="${dict.dictLabel}"></td>
                                            <td th:rowspan="${segPackGoodsStat.size}"
                                                th:text="${segmentVO.deliAddrName}"></td>
                                            <td th:rowspan="${segPackGoodsStat.size}"
                                                th:text="${segmentVO.deliDetailAddr}"></td>
                                            <td th:rowspan="${segPackGoodsStat.size}"
                                                th:text="${segmentVO.arriAddrName}"></td>
                                            <td th:rowspan="${segPackGoodsStat.size}"
                                                th:text="${segmentVO.arriDetailAddr}"></td>
                                            <td th:rowspan="${segPackGoodsStat.size}"
                                                th:text="${#dates.format(segmentVO.reqDeliDate,'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td th:rowspan="${segPackGoodsStat.size}"
                                                th:text="${#dates.format(segmentVO.reqArriDate,'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td th:text="${segPackGoods.goodsName}"></td>
                                            <td th:each="dict : ${@dict.getType('package_type')}"
                                                th:if="${dict.dictValue} == ${segPackGoods.packId}"
                                                th:text="${dict.dictLabel}"></td>
                                            <td th:text="${segPackGoods.num}"></td>
                                            <td th:text="${segPackGoods.weight}"></td>
                                            <td th:text="${segPackGoods.volume}"></td>
                                        </div>
                                        <div th:unless="${segPackGoodsStat.index == 0}">
                                            <td th:text="${segPackGoods.goodsName}"></td>
                                            <td th:each="dict : ${@dict.getType('package_type')}"
                                                th:if="${dict.dictValue} == ${segPackGoods.packId}"
                                                th:text="${dict.dictLabel}"></td>
                                            <td th:text="${segPackGoods.num}"></td>
                                            <td th:text="${segPackGoods.weight}"></td>
                                            <td th:text="${segPackGoods.volume}"></td>
                                        </div>
                                    </tr>
                                </div>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFive">付款信息</a>
                </h4>
            </div>
            <div id="collapseFive" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTab" class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 10%;text-align: left">费用类型</th>
                                <th style="width: 10%;text-align: left">费用详情</th>
                                <th style="width: 10%;text-align: right">金额</th>
                                <th style="width: 20%;text-align: left">油卡号</th>
                                <th style="width: 50%;text-align: left">备注</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="payDetailList,stat : ${payDetailList}">
                                <td style="text-align: left" th:if="*{payDetailList.freeType} == 0"
                                    th:text="运费"></td>
                                <td style="text-align: left" th:if="*{payDetailList.freeType} == 1"
                                    th:text="在途费用"></td>
                                <td style="text-align: left" th:each="dict : ${@dict.getType('cost_type_on_way')}"
                                    th:if="*{payDetailList.freeType} == 1 and  ${dict.dictValue} == ${payDetailList.costTypeOnWay} "
                                    th:text="${dict.dictLabel}"></td>
                                <td style="text-align: left" th:each="dict : ${@dict.getType('cost_type_freight')}"
                                    th:if="*{payDetailList.freeType} == 0 and ${dict.dictValue} == ${payDetailList.costTypeFreight}"
                                    th:text="${dict.dictLabel}"></td>
                                <td style="text-align: right" th:text="${payDetailList.transFeeCount}"></td>
                                <td style="text-align: left" th:text="${payDetailList.oilCardNumber}"></td>
                                <td style="text-align: left" th:text="${payDetailList.memo}"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <form id="form-check" class="form-horizontal" novalidate="novalidate">
        <input  name="businessId" type="hidden" th:value="${id}">
        <div class="panel-body">
            <div class="row">
                <label class="col-sm-5">退回备注：</label>
                <div class="col-sm-12">
                    <textarea id="memo" name="memo" class="form-control" type="text" placeholder="如不通过请填写退回备注"
                              maxlength="250" required="" rows="5" aria-required="true"></textarea>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row" th:if="${checkType} == 'single'">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitApprove()"><i class="fa fa-check"></i>通 过
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="submitBack()"><i class="fa fa-reply-all"></i>不通过
        </button>
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-close"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var url = ctx + "tms/segmentCheck/saveCheck";

    //审核类型：single 单个审核；multiple 多个审核
    var checkType = [[${checkType}]];

    if(checkType == 'multiple'){
        url = ctx + "tms/segmentCheck/saveBatchCheck";
    }

    /**
     * 校验
     */
    $("#form-invoice-unconfirm").validate({});

    /**
     * 审核通过
     */
    function submitApprove() {
        var data = $("#form-check").serializeArray();
        data.push({"name": "checkStatus", "value": 1});
        //单个审核
        if(checkType == 'single'){
            $.operate.saveTab(url, data);
        }
        //批量审核
        if(checkType == 'multiple'){
            $.operate.save(url, data);
        }

    }

    /**
     * 审核不通过
     */
    function submitBack() {
        var data = $("#form-check").serializeArray();
        data.push({"name": "checkStatus", "value": 2});
        if ($("#memo").val() === '') {
            $.modal.msgWarning("请填写退回备注！");
            return;
        }
        //单个审核
        if(checkType == 'single'){
            $.operate.saveTab(url, data);
        }
        //批量审核
        if(checkType == 'multiple'){
            $.operate.save(url, data);
        }
    }
</script>
</body>

</html>