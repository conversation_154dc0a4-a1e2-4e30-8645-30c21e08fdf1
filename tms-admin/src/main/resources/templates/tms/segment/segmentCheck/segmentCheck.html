<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('调度审核列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">承运商名称：</label>
                            <div class="col-sm-8">
                                <input name="carrName" id="carrName" placeholder="请输入承运商名称" class="form-control"
                                       type="text" maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">车牌号：</label>
                            <div class="col-sm-8">
                                <input name="carno" id="carno" placeholder="请输入车牌号" class="form-control" type="text"
                                       maxlength="20" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                                <i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                                <i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="check()" shiro:hasPermission="tms:segmentCheck:check">
                <i class="fa fa-plus"></i> 审核
            </a>
            <a class="btn btn-primary multiple disabled" onclick="batchCheck()" shiro:hasPermission="tms:segmentCheck:check">
                <i class="fa fa-plus"></i> 批量审核
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:segmentCheck:edit')}]];
    var removeFlag = [[${@permission.hasPermi('tms:segmentCheck:remove')}]];
    var prefix = ctx + "tms/segmentCheck";
    var checkType = [[${checkType}]];
    $(function () {
        var options = {
            url: prefix + "/list?checkType=" + checkType,
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "调度审核",
            fixedColumns: true,
            fixedNumber: 3,
            uniqueId: "id",
            showExport: false,
            clickToSelect: true,
            columns: [
                {checkbox: true},
                {field: 'custAbbr', title: '客户简称',align: 'left'},
                {field: 'reqDeliDate', title: '要求提货时间',align: 'left',
                    formatter: function status(value, row, index) {
                        if(value === "" || value == null || value === 'undefined'){
                            return "";
                        }
                        return value.substring(0,10);
                    }
                },
                {
                    field: 'deliAddress', title: '提货|到货省市区', align: 'left',
                    formatter: function status(value, row, index) {
                        return row.deliAddress
                            + '<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'
                            + row.arriAddress;
                    }
                },
                {field: 'costAmount', title: '调度价', align: 'right', halign: "center",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }},
                {field: 'guidingprice', title: '指导价', align: 'right', halign: "center",
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }},
                {field: 'weightCount', title: '总重量',align: 'left'},
                {field: 'numCount', title: '总件数',align: 'left'},
                {field: 'volumeCount', title: '总体积',align: 'left'},
                {field: 'carrName', title: '承运商',align: 'left'},
                {field: 'carno', title: '车牌',align: 'left'},
                {field: 'carrPhone', title: '承运商联系方式',align: 'left'},
                {field: 'dispatcherName', title: '调度人',align: 'left'},
                {field: 'dispatchDate', title: '调度时间',align: 'left'}
            ]
        };
        $.table.init(options);
    });

    /**
     * 审核
     */
    function check() {
        var id = $.table.selectColumns('id');
        var segmentIds = $.table.selectColumns("segmentIds");
        $.modal.openTab('调度明细', prefix + "/check?id=" + id + "&segmentIds=" + segmentIds);
    }

    /**
     * 批量审核
     */
    function batchCheck() {
        var id = $.table.selectColumns('id');
        var url =  prefix + "/batchCheck?id=" + id;
        layer.open({
            type: 2,
            area: ['40%', '50%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "批量审核",
            content: url,
            btn: ['通过', '不通过', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitApprove(index, layero);
            },
            btn2: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitBack(index, layero);
                return false;
            },
            btn3: function (index, layero) {
                return true;
            }
        });
    }
</script>
</body>
</html>
