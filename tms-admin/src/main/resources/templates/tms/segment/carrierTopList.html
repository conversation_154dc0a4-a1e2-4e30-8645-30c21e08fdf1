<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<!-- 容器 -->
<div class="container-div">
    <!-- 一行 -->
    <div class="row">
        <!-- 搜索条件区域 -->
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">

                    <div class="col-sm-3">
                        <div class="form-group">
                            <label class="col-sm-4">车长：</label>
                            <div class="col-sm-8">
                                <select name="carLen" id="carLen" class="form-control" th:with="type=${@dict.getType('car_len')}">
                                    <option value="">-- 请选择 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="${invoice.carLen}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">车型：</label>-->
                            <div class="col-sm-12">
                                <select name="carType" id="carType" class="form-control" th:with="type=${@dict.getType('car_type')}">
                                    <option value="">-- 车型 --</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="${invoice.carType}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-sm-5">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <!--                                <label class="col-sm-6">提货方地址：</label>-->
                                <div class="col-sm-12">
                                    <select  name="deliProvinceId" id="deliProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <select name="deliCityId" id="deliCityId"  class="form-control valid" aria-invalid="false"></select>
                        </div>
                        <div class="col-sm-4">
                            <select name="deliAreaId" id="deliAreaId"  class="form-control valid" aria-invalid="false"></select>
                        </div>
                    </div>
                    <div class="col-sm-5">
                        <div class="col-sm-2" onclick="changeDiv()">
                            <img th:src="@{/img/change.png}" style="width: 40px;height: 40px;display: block;margin: 0 auto">
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <!--                                <label class="col-sm-6">收货方地址：</label>-->
                                <div class="col-sm-12">
                                    <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <select name="arriCityId" id="arriCityId" class="form-control" aria-invalid="false"></select>
                        </div>
                        <div class="col-sm-3">
                            <select name="arriAreaId" id="arriAreaId" class="form-control" aria-invalid="false"></select>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <div class="form-group">
                            <!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="_$this.searchPre();"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="_$this.resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- 按钮区域 -->
        <div class="btn-group-sm" id="toolbar">

        </div>
        <!-- 列表 -->
        <div class="col-sm-12 select-table table-striped toofoot pm">
            <table id="bootstrap-table" class="text-nowrap" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: distpicker" />
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    let prefix  = ctx + "tms/segment";
    let vehicle = {};
    let _$this  = vehicle;

    var deliProvinceId = [[${deliProvinceId}]];
    var deliCityId = [[${deliCityId}]];
    var arriProvinceId = [[${arriProvinceId}]];
    var arriCityId = [[${arriCityId}]];
    let segmentId = [[${segmentId}]]
    /* *************************************************************
     **************  初始化部分 **************************************
     **************************************************************/
    /**
     *  初始化列表列
     */
    vehicle.initColumn = function(){
        let columns              = [
            {
                title: '线路',
                align: 'left',
                field: '',
                formatter: function status(value,row) {
                    let actions = [];
                    actions.push(row.deliAreaName+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriAreaName);
                    return actions.join('');
                }
            },
            {
                title: '承运商',
                align: 'left',
                field: 'carrierName',

            },
            {
                title: '联系电话',
                align: 'left',
                field: 'carrierPhone'
            },
            {
                title: '车长、车型',
                align: 'left',
                field: 'carLenName',
                formatter: function status(value,row) {
                    let carLenName = row.carLenName;
                    let carTypeName = row.carTypeName;

                    if(carLenName == null || carLenName == 'null'){
                        carLenName = "";
                    }

                    if(carTypeName == null || carTypeName == 'null'){
                        carTypeName = "";
                    }


                    return carLenName+"-"+carTypeName;
                }
            },
            {
                title: '平均价格',
                align: 'left',
                field: 'avgCostAmount',
                formatter: function status(value,row) {
                    return value;
                }
            },
            {
                title: '承运次数',
                align: 'left',
                field: 'routeCnt',
                formatter: function status(value,row) {
                    return '<a href="javascript:;" onclick="showCarrierList(\''+row.carrierId+'\',\''+row.deliAreaCode+'\',\''+row.arriAreaCode+'\',\''+row.carLen+'\',\''+row.carType+'\')">'+value+'</a>';
                }
            },
            {
                title: '司机（合规/总数）',
                align: 'left',
                field: 'driverCnt',
                formatter: function status(value,row) {
                    return '<a href="javascript:;" onclick="showCarrierDriverList(\''+row.carrierId+'\')">'+value+'</a>';
                }
            },
            {
                title: '车辆（合规/总数）',
                align: 'left',
                field: 'carCnt',
                formatter: function status(value,row) {
                    return '<a href="javascript:;" onclick="showCarrierCarList(\''+row.carrierId+'\')">'+value+'</a>';
                }
            }
        ];

        let options = {};
        options.url              = prefix + "/carrierTopList";
        //options.exportUrl        = prefix + "/delExport";
        options.showToggle       = false;
        options.showSearch       = false;
        options.showRefresh       = false;
        options.singleSelect       = false;
        options.showColumns      = false;
        options.clickToSelect    = true;
        options.fixedColumns     = true;
        options.rememberSelected = true;
        options.showFooter       = true;
        //options.fixedNumber      = 3;
        options.height           = 560;
        //options.uniqueId         = "entrustId";
        //options.modalName        = "运单";
        options.columns          = columns;
        /*options.onPostBody       = function () {
            //查询合计总金额
            _$this.summaryG7Shipping();
        }*/

        $.table.init(options);
    }
    /**
     * 初始化控件
     */
    _$this.initControl = function(){
        /*layui.use('laydate', function() {
           var laydate = layui.laydate;
           laydate.render({
               elem: '#startDate',
               type: 'date',
               trigger: 'click'
           });
           laydate.render({
               elem: '#endDate',
               type: 'date',
               trigger: 'click'
           });


       });*/
        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",[[${deliProvinceId}]],[[${deliCityId}]],[[${deliAreaId}]]);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",[[${arriProvinceId}]],[[${arriCityId}]],[[${arriAreaId}]]);
    }


    /**
     * 初始化画面
     */
    vehicle.initPage = function(){
        //初始化控件
        _$this.initControl();

        //初始化列表列
        _$this.initColumn();
    }

    /* *************************************************************
     ************** 事件处理部分 *************************************
     **************************************************************/


    /**
     * 搜索
     */
    _$this.searchPre = function() {
        var data = {};
        //data.salesDept = $.common.join($('#salesDept').selectpicker('val'));
        if($("#deliProvinceId").val() == ''){
            $.modal.alertWarning("提货方地址不能为空，请选择！");
            return;
        }

        if($("#arriProvinceId").val() == ''){
            $.modal.alertWarning("收货方地址不能为空，请选择！");
            return;
        }

        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    _$this.resetPre = function() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        _$this.searchPre();
    }
    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        _$this.searchPre();
    }

    /**
     * 跳转委托单详情页
     */
    function detailEntrust(entrustId) {
        var url = ctx + "trustDeed" + "/detail?entrustId="+entrustId;
        $.modal.openTab( "委托单详细", url);
    }

    /**
     * 跳转发货单详情页
     */
    function detailInvoice(invoiceId) {
        var prefix =  ctx + "invoice";

        var url = ctx + "invoice/detail/"+invoiceId;
        $.modal.openTab("发货单详细", url);
    }
    /**
     * 显示承运商的列表
     */
    function showCarrierList(carrierId,deliAreaCode,arriAreaCode,carLen,carType){
        let deliProvinceId = $("#deliProvinceId").val();
        var url =  ctx + "tms/segment/segmentCarrierList/"+segmentId+"/"+carrierId+"?deliAreaCode="+deliAreaCode+"&arriAreaCode="+arriAreaCode+"&carLen="+carLen+"&carType="+carType;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "承运列表",
            area: ['70%', '100%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }
    /**
     * 跳转承运商司机列表
     */
    function showCarrierDriverList(carrierId){
        //alert(carrierId);
        var url =  ctx + "tms/segment/carrierTopDriverListView?carrierId="+carrierId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "司机列表",
            area: ['70%', '100%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }
    /**
     * 跳转承运商车子列表
     */

    function showCarrierCarList(carrierId){
        //alert(carrierId);
        let url =  ctx + "tms/segment/carrierTopCarListView?carrierId="+carrierId;

        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "车辆列表",
            area: ['70%', '100%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /* *************************************************************
     ************** 页面加载 *************************************
     **************************************************************/
    $(function(){
        _$this.initPage();
    });
</script>
</body>
</html>