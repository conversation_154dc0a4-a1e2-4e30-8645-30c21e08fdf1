<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('司机代收核销')" />

</head>

<body>
<div class="form-content">
    <form id="form-driverWriteOff" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="payDetailId" th:value="${payDetailId}">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">应收明细</a>

                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">

                        <div class="fixed-table-body">
                            <table class="custom-tab table-hover table" border="0" id="receiveDetailTable">
                                <thead>
                                <tr>
                                    <th style="width: 15%;">应收单号</th>
                                    <th style="width: 10%;">应收单据状态</th>
                                    <th style="width: 15%;">发货单号</th>
                                    <th style="width: 10%;">结算方式</th>
                                    <th style="width: 10%;">费用类型</th>
                                    <th style="width: 10%;">总金额</th>
                                    <th style="width: 10%;">已收金额</th>
                                    <th style="width: 10%;">未收金额</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="receiveDetail,stat : ${receiveDetailList}">
                                    <td th:text="${receiveDetail.vbillno}"></td>
                                    <td>
                                        <div th:each="receiveDetailStatus:${receiveDetailStatusEnum}"
                                             th:if="${receiveDetail.vbillstatus} == ${receiveDetailStatus.value}"
                                             th:text="${receiveDetailStatus.context}"></div>
                                        <input type="hidden" th:id="rdVbillstatus_ + ${stat.index}" th:value="${receiveDetail.vbillstatus}">
                                    </td>
                                    <td th:text="${receiveDetail.invoiceVbillno}"></td>
                                    <td>
                                        <div th:each="dict : ${@dict.getType('bala_type')}" th:if="${dict.dictValue == receiveDetail.balatype}"
                                             th:text="${dict.dictLabel}" ></div>
                                    </td>
                                    <td><div th:each="dict : ${freeTypeEnum}" th:if="${dict.value == receiveDetail.freeType}"
                                             th:text="${dict.context}"></div>
                                    </td>

                                    <td th:text="${receiveDetail.transFeeCount}"></td>
                                    <td th:text="${receiveDetail.gotAmount}"></td>
                                    <td th:text="${receiveDetail.ungotAmount}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">应付明细</a>
                    </h4>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="fixed-table-body">
                            <table class="custom-tab table-hover table" border="0" id="payDetailTable">
                                <thead>
                                <tr>
                                    <th style="width: 15%;">应付单号</th>
                                    <th style="width: 10%;">应付单状态</th>
                                    <th style="width: 15%;">费用类型</th>
                                    <th style="width: 15%;">付款类型</th>
                                    <th style="width: 10%;">核销金额</th>
                                    <th style="width: 10%;">总金额</th>
                                    <th style="width: 10%;">已付金额</th>
                                    <th style="width: 10%;">未付金额</th>
                                </tr>
                                </thead>
                                <tbody>
                                <!--th:each="segPackGoods,segPackGoodsStat : ${segPackGoodsList}"-->
                                <tr th:each="payDetail,stat : ${payDetailList}">
                                    <td th:text="${payDetail.vbillno}"></td>
                                    <td>
                                        <div th:each="payDetailStatus:${payDetailStatusEnum}"
                                             th:if="${payDetail.vbillstatus} == ${payDetailStatus.value}"
                                             th:text="${payDetailStatus.context}"></div>
                                    </td>
                                    <td>
                                        <div th:if="${payDetail.freeType == '0'}">运费</div>
                                        <div th:if="${payDetail.freeType == '1'}">在途费用</div>
                                        <div th:if="${payDetail.freeType == '2'}">调整费用</div>
                                    </td>
                                    <td>
                                        <div th:each="dict : ${@dict.getType('cost_type_freight')}"
                                             th:if="${dict.dictValue == payDetail.costTypeFreight and payDetail.freeType == '0'}"
                                             th:text="${dict.dictLabel}"></div>
                                        <div th:each="dict : ${@dict.getType('cost_type_on_way')}"
                                             th:if="${dict.dictValue == payDetail.costTypeOnWay and payDetail.freeType == '1'}"
                                             th:text="${dict.dictLabel}"></div>
                                    </td>

                                    <td th:text="${payDetail.writeOffAmount}"></td>
                                    <td th:text="${payDetail.transFeeCount}"></td>
                                    <td th:text="${payDetail.gotAmount}"></td>
                                    <td th:id="ungotAmount_+${stat.index}" th:text="${payDetail.ungotAmount}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="panel-group">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseThree">收款信息</a>
                    </h4>
                </div>
                <div id="collapseThree" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >转入账户：</label>
                                    <div class="col-sm-8">
                                        <div th:text="${account}"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >收款日期：</label>
                                    <div class="col-sm-8">
                                        <div th:text="*{#dates.format(driverCollection.receivableDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >实收金额：</label>
                                    <div class="col-sm-8">
                                        <div th:text="${driverCollection.actualAmount}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >核销人：</label>
                                    <div class="col-sm-8">
                                        <div th:text="${driverCollection.writeOffUserName}"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >核销时间：</label>
                                    <div class="col-sm-8">
                                        <div th:text="*{#dates.format(driverCollection.writeOffDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                        <div th:utext="*{#strings.unescapeJava(#strings.replace(#strings.escapeJava(driverCollection.memo),'\n','&lt;br/&gt;'))}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </form>
</div>
<!---->
<th:block th:include="include :: footer" />
<script>

</script>
</body>

</html>