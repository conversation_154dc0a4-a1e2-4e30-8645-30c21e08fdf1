<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('司机代收核销记录')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运单号：</label>
                            <div class="col-sm-8">
                                <input name="lotno" id="lotno" class="form-control"
                                       placeholder="请输入运单号" maxlength="30">
                            </div>
                        </div>
                    </div>


                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">承运商：</label>
                            <div class="col-sm-8">
                                <input name="carrName" id="carrName" placeholder="请输入承运商名称" class="form-control valid">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">代收单号：</label>
                            <div class="col-sm-8">
                                <input name="vbillno" id="vbillno" class="form-control"
                                       placeholder="请输入代收单号" maxlength="30">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="driverWriteOffRecord()" shiro:hasPermission="tms:driverCollection:driverWriteOffRecord">
                <i class="fa fa-newspaper-o"></i> 司机代收核销记录
            </a>
            <a class="btn btn-primary single disabled" onclick="backWriteOff()" shiro:hasPermission="tms:driverCollection:back">
                <i class="fa fa-newspaper-o"></i> 代收反核销
            </a>
            <a class="btn btn-primary" onclick="backWriteOffLog()" shiro:hasPermission="tms:driverCollection:back">
                <i class="fa fa-newspaper-o"></i> 反核销记录
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var prefix = ctx + "tms/driverCollection";

    $(function () {
        var options = {
            url: prefix + "/list",
            showToggle: false,
            showColumns: true,
            modalName: "司机代收核销记录",
            rememberSelected: false,
            clickToSelect:true,
            height: 560,
            uniqueId: "payDetailId",
            columns: [{
                checkbox: true
            },
                {
                    title: '代收单号',
                    field: 'vbillno',
                    align: 'left'
                },
                {
                    title: '运单号',
                    align: 'left',
                    field: 'lotno'
                },
                {
                    title: '承运商',
                    align: 'left',
                    field: 'carrName'
                },

                {
                    title: '司机',
                    align: 'left',
                    field: 'driverName'
                },
                /*{
                    title: '司机电话',
                    align: 'left',
                    field: 'driverMobile'
                },*/
                {
                    title: '代收金额',
                    align: 'right',
                    field: 'collectionAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '核销金额',
                    align: 'right',
                    field: 'writeOffAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '核销人',
                    align: 'left',
                    field: 'writeOffUserName'
                },
                {
                    title: '核销日期',
                    align: 'left',
                    field: 'writeOffDate'
                },
                {
                    title: '实收金额',
                    align: 'right',
                    field: 'actualAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
    });

    /**
     * 司机代收核销
     */
    function driverWriteOffRecord() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var rows = $.table.selectFirstColumns();
        if (rows.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        var payDetailId = $.table.selectColumns("payDetailId").join();

        $.modal.openTab("应付单据详情", prefix + "/driverWriteOffRecord/" + payDetailId );
    }

    function backWriteOff() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var rows = $.table.selectFirstColumns();
        if (rows.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        var payDetailId = $.table.selectColumns("payDetailId").join();

        $.modal.confirm("确定反核销改数据吗？", function () {
            // let data = {"payDetailId": payDetailId}
            let data = "payDetailId=" + payDetailId;

            $.ajax({
                url: ctx + "tms/driverCollection/backWriteOff?",
                type: "post",
                data: data,
                contentType: "application/x-www-form-urlencoded",
                // dataType: "json",
                // contentType: "application/json; charset=utf-8",
                // data: JSON.stringify(data),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (data) {
                    if (data.code == 0) {
                        $.modal.msgSuccess("反核销成功。");
                        $.table.refresh();
                    }else {
                        $.modal.alertError(data.msg);
                    }
                    $.modal.closeLoading();
                }
            })
        });
    }

    function backWriteOffLog() {
        layer.open({
            type: 2,
            area: ['90%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "反核销记录",
            content: ctx + "tms/driverCollection/backWriteOffLog",
            btn: ['关闭'],
            // shadeClose: true,            // 弹层外区域关闭
            cancel: function (index) {
                return true;
            }
        });

    }

</script>
</body>
</html>