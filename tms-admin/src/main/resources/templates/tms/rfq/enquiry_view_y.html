<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('询价查看')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link th:href="@{/element-ui@2.15.13/lib/theme-chalk/index.css}" rel="stylesheet"/>
    <script th:src="@{/js/vue.min.js}"></script>
    <script th:src="@{/element-ui@2.15.13/lib/index.js}"></script>
    <th:block th:include="include :: bootstrap-fileinput553-css"/>
    <style type="text/css">
        .flex{
            display: flex;
            align-items:flex-start;
            justify-content:space-between;
        }
        .flex_left{
            width: 114px;
            line-height: 26px;
            text-align: right;
        }
        .flex_right{
            flex: 1;
        }
        .flex_left2{
            width: 114px;
            line-height: 18px;
            text-align: right;
        }
        .flex_right2{
            flex: 1;
            line-height: 18px;
        }
        .fcff3{
            color: #ff3636;
        }
        .icon-btn {
            font-size: 15px;
            cursor: pointer;
            vertical-align: middle;
        }
        .primary {
            color: #409EFF;
        }
        .danger {
            color: #f56c6c;
        }
        .success {
            color: #67c23a;
        }
        .info {
            color: #909399;
        }
        .fake-form-control .el-input__inner{
            border: 1px solid #e5e6e7;
            border-radius: 1px;
            line-height: 26px;
            height: 26px;
            font-size: 13px;
            padding: 2px 4px;
        }
        [v-clock]{
            display: none;
        }
        .eclipse{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .table-thin>thead>tr>td, .table-thin>tbody>tr>td, .table-thin>tfoot>tr>td {
            line-height: 1;
        }
        .hover-highlight tr:hover {
            background-color: #eee;
        }
    </style>
</head>
<body>
    <div id="app" style="padding: 5px" v-clock>

        <div class="panel panel-default" id="accordion" v-if="form != null">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion" href="tabs_panels.html#collapseOne">基础信息</a>
                </h4>
            </div>
            <div id="collapseOne" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-4 flex">
                            <label class="flex_left2">发货单位：</label>
                            <label class="flex_right2">{{form.custAbbr}}</label>
                        </div>
                        <div class="col-md-3 col-sm-4 flex">
                            <label class="flex_left2">报价部门：</label>
                            <label class="flex_right2 eclipse" :title="form.quoteDeptNames ? form.quoteDeptNames.join('、') : ''">{{form.quoteDeptNames ? form.quoteDeptNames.join('、') : ''}}</label>
                        </div>
                        <div class="col-md-3 col-sm-4 flex">
                            <label class="flex_left2">报价截止时间：</label>
                            <label class="flex_right2">{{form.reqDate}}</label>
                        </div>
                        <div class="col-md-3 col-sm-4">
                            <span v-if="form.status > 4"><!--图章制作：https://www.btbsm.com/oblongseal-->
                                <img v-if="form.hit == 1" th:src="@{/img/zb1.png}" style="position: absolute;left:50%;transform: translate(-50%, 0)" />
                                <img v-else th:src="@{/img/zb0.png}" style="position: absolute" />
                            </span>
                        </div>
                        <div class="col-md-6 col-sm-12 flex">
                            <label class="flex_left2">发运时间段：</label>
                            <label class="flex_right2"><span v-if="form.shippingStartDate">{{form.shippingStartDate.substr(0,10)}} ~ {{form.shippingEndDate.substr(0,10)}}</span>
                                <span v-else>{{form.deliveryTime==1?'常年':''}}{{form.otherDeliveryTime}}</span></label>
                        </div>
                        <div class="col-md-12 col-sm-12 flex" v-if="form.attachMainList && form.attachMainList.length > 0">
                            <label class="flex_left2">附件：</label>
                            <label class="flex_right2 imgPreview flex" style="margin-bottom: 3px;justify-content: flex-start">
                                <div v-for="(file,xx) in form.attachMainList" style="margin-right: 10px;border:1px #e7e7e7 solid;background-color: #fcfcfc;padding: 3px;">
                                    <img v-if="file.fileName.toLowerCase().endsWith('.jpg') ||
                                          file.fileName.toLowerCase().endsWith('.png') ||
                                          file.fileName.toLowerCase().endsWith('.gif') ||
                                          file.fileName.toLowerCase().endsWith('.bmp')" :src="file.filePath" style="width:60px;height:40px;" />
                                    <a v-else :href="file.filePath" target="_blank">{{file.fileName}}</a>
                                </div>
                            </label>
                        </div>

                        <div v-for="(line,l) in form.lines" :key="line.idx" class="col-md-12">
                            <div class="row" style="padding: 0 5px;">
                                <div style="padding: 5px 10px;background-color: #eee;margin: 0 0 5px;border: 1px #ddd solid;" class="flex">
                                    <div class="primary" style="flex:1;font-weight: bold;">路线{{l+1}}<span title="装货第1行地址与卸货第1行地址的距离">{{line.distance ? ': 单程约' + (line.distance/1000).toFixed(2) + 'km':''}}</span></div>
                                </div>
                                <div class="col-md-12 row" style="margin: 0 0 2px 0;padding: 0;">
                                    <div class="col-md-3 col-sm-4 flex">
                                        <span class="flex_left2">是否往返：</span>
                                        <span class="flex_right2">{{line.roundTrip==1?'往返':'单程'}}</span>
                                    </div>
                                </div>
                                <div class="col-md-12 row" style="margin: 0 0 5px 0;padding: 0;">
                                    <div class="col-md-6 ">
                                        <div style="border: 1px #e5e6e7 solid;padding: 2px 4px;">
                                            <div v-for="(deli,i) in line.deliList" :key="deli.idx" style="padding: 2px;">
                                                <div class="flex" style="justify-content: flex-start;align-items:center;">
                                                    <span class="label label-warning" style="padding: 3px;margin-right: 5px">装</span>
                                                    <span style="line-height: 1">{{deli.provinceName}}{{deli.cityName}}{{deli.areaName}}{{deli.detailAddr}}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div style="border: 1px #e5e6e7 solid;padding: 2px 4px;">
                                            <div v-for="(arri,i) in line.arriList" :key="arri.idx" style="padding: 2px;">
                                                <div class="flex" style="justify-content: flex-start;align-items:center;">
                                                    <span class="label label-success" style="padding: 3px;margin-right: 5px">卸</span>
                                                    <span style="line-height: 1">{{arri.provinceName}}{{arri.cityName}}{{arri.areaName}}{{arri.detailAddr}}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-sm-4 flex">
                                    <label class="flex_left2">货物名称：</label>
                                    <span class="flex_right2">{{line.goodsName}}</span>
                                </div>
                                <div class="col-md-3 col-sm-4 flex">
                                    <label class="flex_left2">发运日期：</label>
                                    <span class="flex_right2">{{line.deliDate}}</span>
                                </div>
                                <div class="col-md-3 col-sm-4 flex">
                                    <label class="flex_left2">到达日期：</label>
                                    <span class="flex_right2">{{line.arriDate}}</span>
                                </div>



                                <div class="col-md-12 col-sm-12" style="padding: 0" v-for="(carLenObj,j) in line.carLenObjs" :key="j">
                                    <div class="col-md-2 col-sm-4 flex">
                                        <span class="flex_left2">运输方式：</span>
                                        <span class="flex_right2">{{transNameByCode(carLenObj.transCode)}}</span>
                                    </div>
                                    <div class="col-md-2 col-sm-4 flex">
                                        <span class="flex_left2">计价方式：</span>
                                        <span class="flex_right2">{{billingMethod[carLenObj.billingMethod]}}</span>
                                    </div>
                                    <div class="col-md-5 col-sm-8" style="padding: 0">
                                        <div class="col-md-6 col-sm-6 flex">
                                            <label class="flex_left2">货量：</label>
                                            <span class="flex_right2" v-if="carLenObj.num">{{carLenObj.num}}件</span>
                                            <span class="flex_right2" v-if="carLenObj.weight">{{carLenObj.weight}}吨</span>
                                            <span class="flex_right2" v-if="carLenObj.volume">{{carLenObj.volume}}方</span>
                                        </div>
                                        <div class="col-md-6 col-sm-6 flex">
                                            <span class="flex_left2">货物尺寸(米)：</span>
                                            <span class="flex_right2">
                                                    <span v-if="carLenObj.length">长{{carLenObj.length}}m</span>
                                                    <span v-if="carLenObj.width">宽{{carLenObj.width}}m</span>
                                                    <span v-if="carLenObj.height">高{{carLenObj.height}}m</span>
                                                </span>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-12" style="padding: 0">
                                        <div class="col-md-7 col-sm-7 flex">
                                            <span class="flex_left2" style="width:50px">车型：</span>
                                            <span class="flex_right2">{{carLenObj.carLenName}}{{carLenObj.carTypeName}}{{carLenObj.isOversize?'(大件)':''}}</span>
                                        </div>
                                        <div class="col-md-5 col-sm-5 flex">
                                            <span class="flex_left2" style="width:50px">车次：</span>
                                            <span class="flex_right2">{{carLenObj.times}}</span>
                                        </div>
                                    </div>
                                </div>

                                <!--<div class="col-md-6 col-sm-12 flex">
                                    <label class="flex_left2">车型：</label>
                                    <span class="flex_right2">{{carTypeLabel(line)}}</span>
                                </div>-->

                                <div class="col-md-12 col-sm-12 flex">
                                    <label class="flex_left2">备注：</label>
                                    <label class="flex_right2" style="white-space: pre">{{line.remark}}</textarea></label>
                                </div>
                            </div>

                            <table v-if="line.quoteDtls && line.quoteDtls.length > 0" class="table table-bordered table-thin hover-highlight">
                                <thead>
                                <tr>
                                    <th style="width: 37px">采用</th>
                                    <th style="width: 110px">运输方式</th>
                                    <th style="width: 160px">货物信息</th>
                                    <th style="width: 220px;">车长</th>
                                    <th style="width: 40px;text-align: right">车次</th>
                                    <th style="width: 90px">成本来源</th>
                                    <th style="width: 100px;text-align: right">建议单车成本</th>
                                    <th style="width: 80px;text-align: right">油卡比例</th>
                                    <th style="width: 80px;">是否开票</th>
                                    <th>成本情况</th>
                                    <th style="width: 220px">成本提供人</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="(q,i) in line.quoteDtls" :key="i">
                                    <td align="center" style="padding: 0">
                                        <input v-if="act=='quote'" type="checkbox" :value="q.id" style="margin-top: 2px"
                                               :dtl-id="q.id" :line-id="line.id" :cartype-id="q.cartypeId" :true-value="1" :false-value="0"
                                               :biz-type="q.type"
                                               :disabled="line.quoteDtls.filter(itm => itm.choosed == 1 && itm.cartypeId==q.cartypeId).length > 0
                                                && line.quoteDtls.filter(itm => itm.choosed == 1 && itm.cartypeId==q.cartypeId)[0].id != q.id"
                                               @change="calc_amount($event.target)" v-model="q.choosed" title="选择采用成本" />
                                        <span v-else style="color:#00B83F">{{q.choosed ? '是':''}}</span>
                                    </td>
                                    <td v-if="q.rowspan>0" :rowspan="q.rowspan">{{transNameByCode(q.transCode)}}{{line.roundTrip==1?' (往返)':''}}</td>
                                    <td v-if="q.rowspan>0" :rowspan="q.rowspan">{{q.num && q.num+'件'}}{{q.weight && q.weight+'吨'}}{{q.volume && q.volume+'方'}} {{q['length'] && (q['length']+'×'+q['width']+'×'+q['height'])}}</td>
                                    <td v-if="q.rowspan>0" :rowspan="q.rowspan">
                                        <div v-if="q.type == 1" style="display: flex;justify-content: space-between;">
                                            <span style="width: 110px;display: inline-block">{{q.carLenName}}{{q.carTypeName}}{{q.isOversize?'(大件)':''}}</span>
                                            <span title="指导价" style="color:green;flex: 1">{{q.guidingPrice||'无'}}</span>
                                            <a href="javascript:;" @click="historyPrice(line, q)">历史价</a>
                                        </div>
                                    </td>
                                    <td v-if="q.rowspan>0" :rowspan="q.rowspan" align="right">{{q.times}}</td>
                                    <td>{{q.wayName}}</td>

                                    <td align="right">¥{{q.suggest.toFixed(2)}}</td>
                                    <td align="right"><span v-if="q.oilRatio != null">{{q.oilRatio.toFixed(2)}}%</span></td>
                                    <td>{{dictLabel(q.billingType, billingType)}}</td>
                                    <td>{{q.state}}</td>
                                    <td><!--{{q.quoteDeptName ? (q.quoteDeptName + ' / ') : ''}}-->{{q.createByName}} / {{q.updateTime}}</td>
                                </tr>

                                </tbody>
                            </table>
                        </div><!--line end-->

                    </div>
                </div>


            </div>
        </div>
        <form id="form-choose">
        <div class="panel panel-default" id="accordion2" v-if="form != null && (form.status >= 2 || (form.status >= 1 && form.quoteDtlCount > 0))">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion2" href="tabs_panels.html#collapseTwo">成本信息</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">
                    审核备注：{{form != null && form.verifyRemark?form.verifyRemark:'无备注'}}
                </div>

                <div style="text-align: center;margin: 5px"></div>
            </div>
        </div>

            <!--旧版兼容展示-->
            <div class="panel panel-default" id="accordion3x" v-if="form != null && form.status >= 3 && isOld">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion3x" href="tabs_panels.html#collapseThreex">策略报价(旧版)</a>
                    </h4>
                </div>
                <div id="collapseThreex" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-4 flex">
                                <label class="flex_left2">拟报价毛利率：</label>
                                <span class="flex_right2">{{form.quoteRate}}%</span>
                            </div>
                            <div class="col-md-3 col-sm-4 flex">
                                <label class="flex_left2">报价金额：</label>
                                <span class="flex_right2">{{form.quoteAmount}}元</span>
                            </div>

                            <div v-if="form.status >= 4" class="col-sm-6 flex">
                                <label class="flex_left2">报价确认人：</label>
                                <span class="flex_right2">{{form.confirmUserName}} / {{form.confirmTime}}</span>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: center;margin: 5px"></div>
                </div>
            </div>

        <div class="panel panel-default" id="accordion3" v-if="form != null && (form.status >= 3 || act =='quote') && !isOld">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion3" href="tabs_panels.html#collapseThree">策略报价</a>
                </h4>
            </div>
            <div id="collapseThree" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">

                        <div class="col-sm-12">
                            <table class="table table-bordered">
                                <tr style="background-color: #f5f5f5">
                                    <th style="width: 38px">序号</th>
                                    <th>装卸货</th>
                                    <th v-if="act == 'result' || act == 'view'" style="width: 38px">中标</th>
                                    <th style="width: 94px">运输方式</th>
                                    <th style="width: 120px">货物信息</th>
                                    <th style="width: 80px">车型</th>
                                    <th style="width: 100px;">成本</th>
                                    <th style="width: 100px"><span class="fcff3" v-if="act == 'quote'">*</span> 报价单价</th>
                                    <th style="width: 50px"><span class="fcff3" v-if="act == 'quote'">*</span> 车次</th>
                                    <th style="width: 90px">总成本</th>
                                    <th style="width: 90px"><span class="fcff3" v-if="act == 'quote'">*</span> <span data-toggle="tooltip" data-container="body"
                                         title="(总报价-总成本-税金)/总报价"
                                         style="text-decoration-line: underline;text-decoration-style: dashed;text-decoration-color: #409eff;">报价毛利率</span></th>
                                    <th style="width: 100px">总报价</th>
                                    <th style="width: 80px">其他费用</th>
                                    <th style="width: 100px"><span class="fcff3" v-if="act == 'quote'">*</span> 合计报价</th>
                                </tr>
                                <template v-for="(line,x) in choosedLines">
                                    <tr v-for="(item,i) in line.choosedList">
                                        <td v-if="i == 0" :rowspan="line.choosedList.length" style="text-align: center">{{x+1}}</td>
                                        <td v-if="i == 0" :rowspan="line.choosedList.length">
                                            <div class="flex">
                                                <div v-if="line.roundTrip==1" style="padding-right: 3px;color:#888;">往返</div>
                                                <div style="flex: 1">
                                                    <div class="flex">
                                                        <span>装：</span>
                                                        <div style="flex:1"><div v-for="a in line.deliList">{{a.provinceName}}{{a.cityName}}{{a.areaName}}</div></div>
                                                    </div>
                                                    <div class="flex">
                                                        <span>卸：</span>
                                                        <div style="flex:1"><div v-for="a in line.arriList">{{a.provinceName}}{{a.cityName}}{{a.areaName}}</div></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td v-if="act == 'result'" style="padding: 1px;text-align: center"><input type="checkbox" v-model="item.hit" v-if="form.hit == 1" :true-value="1" :false-value="0">{{form.hit==2?'否':''}}</td>
                                        <td v-if="act == 'view'">{{item.hit == 1 ? '是':''}}</td>
                                        <td style="line-height: 1;padding: 1px 5px;">{{transNameByCode(item.transCode)}}<br>{{billingMethod[item.billingMethod]}}</td>
                                        <td style="line-height: 1;">{{item.num && item.num+'件'}}{{item.weight && item.weight+'吨'}}{{item.volume && item.volume+'方'}} {{item['length'] && (item['length']+'×'+item['width']+'×'+item['height'])}}</td>
                                        <td style="line-height: 1;padding: 1px 5px;">{{item.carLenName}}{{item.isOversize?'(大件)':''}}<br>{{item.carTypeName}}</td>
                                        <td v-if="act == 'quote' || act == 'result'" style="padding: 1px;">
                                            <div style="line-height: 22px;text-align: right;display: flex;justify-content: space-between;padding: 2px 5px;color:#888">
                                                <span>单车：</span>
                                                <span>{{item.suggest}}</span>
                                            </div>
                                            <div v-if="item.billingMethod != null && item.billingMethod != 3" class="input-group" style="margin-top: 1px">
                                                <input style="border-color:#ff9900" class="form-control" :value="item.unitPrice" @input="setUnitPrice($event, x, i)" :name="'unit_price'+x+'_'+i"
                                                       title="总成本除以总货量反推而来"
                                                       required placeholder="货量单价" oninput="$.numberUtil.onlyNumberTwoDecimal(this)">
                                                <div class="input-group-addon" style="padding: 2px">{{item.billingMethod == 1 ? '/吨':''}}{{item.billingMethod == 2 ? '/方':''}}{{item.billingMethod == 5 ? '/件':''}}</div>
                                            </div>
                                        </td>
                                        <td v-else style="line-height: 1">
                                            单车：{{item.suggest}}
                                            <div v-if="item.billingMethod != null && item.billingMethod != 3">
                                                {{item.unitPrice}}元{{item.billingMethod == 1 ? '/吨':''}}{{item.billingMethod == 2 ? '/方':''}}{{item.billingMethod == 5 ? '/件':''}}
                                            </div>
                                        </td>

                                        <td v-if="act == 'quote' || act == 'result'" style="padding: 1px;">
                                            <div v-if="item.billingMethod == null || item.billingMethod == 3" class="input-group">
                                            <input class="form-control"
                                                   :value="item.quotePrice" @input="setQuotePrice($event, x, i)" style="border-color:deepskyblue" required
                                               placeholder="单车报价" :name="'quote_price'+x+'_'+i" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" autocomplete="off">
                                                <div class="input-group-addon" style="padding: 2px">/车</div>
                                            </div>
                                            <div v-else class="input-group" style="margin-top: 1px">
                                                <input style="border-color:#ff9900" class="form-control" @input="setQuotePrice($event, x, i)"
                                                       :name="'quote_unit_price'+x+'_'+i" :value="item.quoteUnitPrice" title="触发总报价和毛利率计算"
                                                       required placeholder="货量单价" oninput="$.numberUtil.onlyNumberTwoDecimal(this)">
                                                <div class="input-group-addon" style="padding: 2px">{{item.billingMethod == 1 ? '/吨':''}}{{item.billingMethod == 2 ? '/方':''}}{{item.billingMethod == 5 ? '/件':''}}</div>
                                            </div>
                                        </td>
                                        <td v-else style="line-height: 1">
                                            <span v-if="item.billingMethod == null || item.billingMethod == 3 || item.quotePrice">单车：{{item.quotePrice}}</span>
                                            <div v-if="item.billingMethod != null && item.billingMethod != 3">
                                                {{item.quoteUnitPrice}}元{{item.billingMethod == 1 ? '/吨':''}}{{item.billingMethod == 2 ? '/方':''}}{{item.billingMethod == 5 ? '/件':''}}
                                            </div>
                                        </td>

                                        <td v-if="act == 'quote' || act == 'result'" style="padding: 1px;">
                                            <input class="form-control" :value="item.times" @input="setTimes($event, x, i)" style="border-color:deepskyblue"  required
                                                   title="触发总成本和货量成本计算"
                                                   :name="'times'+x+'_'+i" oninput="$.numberUtil.onlyNumberInt(this)" placeholder="整数" autocomplete="off"></td>
                                        <td v-else style="text-align: right;line-height: 1;">{{item.times}}</td>

                                        <td style="text-align: right;line-height: 1;padding: 1px 5px;" :style="{color:act=='quote'?'#888':'#000'}">{{item.timesCost}}元<br>税：{{item.tax}}</td>

                                        <td v-if="act == 'quote' || act == 'result'" style="padding: 1px;"><div class="input-group">
                                            <input class="form-control" required :name="'quote_rate_'+x+'_'+i" style="border-color:deepskyblue"
                                                   oninput="$.numberUtil.onlyNumberTwoDecimal(this)" title="触发报价单价和总报价计算"
                                                   :value="item.quoteRate" @input="setQuoteRate($event, x, i)" autocomplete="off">
                                            <div class="input-group-addon" style="padding: 2px 6px">%</div>
                                        </div></td>
                                        <td v-else style="text-align: right;">{{item.quoteRate}}%</td>

                                        <td style="text-align: right;line-height: 1" :style="{color:act=='quote'?'#888':'#000'}">{{item.totalCost}}元</td>

                                        <template v-if="i == 0">
                                            <td v-if="act == 'quote' || act == 'result'" :rowspan="line.choosedList.length" style="padding: 1px;">
                                                <input class="form-control" style="border-color:deepskyblue" autocomplete="off" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" v-model="line.otherCost">
                                            </td>
                                            <td v-else style="text-align: right" :rowspan="line.choosedList.length">{{line.otherCost}}元</td>

                                            <td v-if="act == 'quote' || act == 'result'" :rowspan="line.choosedList.length" style="padding: 1px;text-align: center">
                                                <el-tooltip placement="top" content="光标离开后将根据【合计报价】调整【报价单价】" :disabled="line.quoteAmount == line.quoteAmountOrigin"
                                                            :manual="true" :value="line.quoteAmount != line.quoteAmountOrigin">
                                                    <input class="form-control" style="border-color:deepskyblue" required :name="'quoteAmount'+x" autocomplete="off"
                                                           oninput="$.numberUtil.onlyNumberTwoDecimal(this)" v-model="line.quoteAmount" @blur="backChngPrice(line)">

                                                </el-tooltip>
                                                <span v-if="line.quoteAmountOrigin != line.quoteAmountAct" style="color:#aaa;">实际<span style="color: red">{{line.quoteAmountAct}}</span></span>
                                            </td>
                                            <td v-else style="text-align: right" :rowspan="line.choosedList.length" >
                                                <span v-if="act!='quote'" style="color:#666" title="实际计算金额">({{line.quoteAmountOrigin}})</span>
                                                {{line.quoteAmount}}元
                                            </td>
                                        </template>
                                    </tr>
                                </template>

                                <tr style="background-color: #f5f5f5">
                                    <th>合计</th>
                                    <th></th>
                                    <th v-if="act == 'result' || act == 'view'"></th>
                                    <th></th><th></th><th></th><th></th><th></th>
                                    <th style="text-align: right">{{choosedSum.sumTimes}}</th>
                                    <th style="text-align: right;line-height: 0.9;">{{choosedSum.sumTimesCost}}元<br>{{choosedSum.sumTax}}</th>
                                    <th style="text-align: right">{{choosedSum.quoteRate}}</th>
                                    <th style="text-align: right">{{choosedSum.sumTotalCost}}元</th>
                                    <th style="text-align: right">{{choosedSum.sumOtherCost}}元</th>
                                    <th style="text-align: right">{{choosedSum.sumQuoteAmount}}元</th>
                                </tr>
                            </table>

                        </div>
                        <div class="col-sm-12 flex">
                            <span class="flex_left" style="width: 95px">策略报价备注：</span>
                            <span class="flex_right">
                                <textarea v-if="act == 'quote'" style="border-color:deepskyblue" class="form-control" v-model="form.policyMemo" rows="4"></textarea>
                                <div v-else style="height: auto;min-height: 26px;white-space: pre;line-height: 20px;padding:3px 0;">{{form.policyMemo||'无备注'}}</div>
                            </span>
                        </div>
                        <div v-if="form.status >= 4" class="col-sm-6 flex">
                            <label class="flex_left2" style="width: 95px">报价确认人：</label>
                            <span class="flex_right2">{{form.confirmUserName}} / {{form.confirmTime}}</span>
                        </div>
                    </div>
                </div>
                <div style="text-align: center;margin: 5px"></div>
            </div>
        </div>
        </form>

            <div class="panel panel-default" id="accordion4" v-if="form != null && (form.status > 4 || act == 'result')">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion4" href="tabs_panels.html#collapseFour">中标情况</a>
                    </h4>
                </div>
                <div id="collapseFour" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <form id="form-result">
                        <div class="row">
                            <div v-if="act == 'result'" class="col-md-3 col-sm-4 flex">
                                <label class="flex_left"><span class="fcff3">*</span> 是否中标：</label>
                                <span class="flex_right">
                                    <label class="radio-inline">
                                        <input type="radio" name="hit" v-model="form.hit" required value="1"> 是
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="hit" v-model="form.hit" required value="2"> 否
                                    </label>
                                </span>
                            </div>
                            <div v-else class="col-md-3 col-sm-4 flex">
                                <label class="flex_left2">是否中标：</label>
                                <span class="flex_right2">{{form.hit == 1?'是':'否'}}</span>
                            </div>
                            <div v-if="act == 'result'" class="col-md-3 col-sm-4 flex">
                                <label class="flex_left">{{form.hit == 2 ? '竞对':''}}中标价格：</label>
                                <span class="flex_right">
                                    <div v-if="form.hit == 1" class="form-control" style="background-color: #f6f6f6;line-height: 22px;">{{sumHitAmount}}</div>
                                    <input v-if="form.hit == 2" class="form-control" name="hitAmount" v-model="form.hitAmount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" autocomplete="off" aria-autocomplete="none">
                                </span>
                            </div>
                            <div v-else class="col-md-3 col-sm-4 flex">
                                <label class="flex_left2">{{form.hit == 2 ? '竞对':''}}中标价格：</label>
                                <span class="flex_right2">{{form.hitAmount}}</span>
                            </div>

                            <template v-if="form.hit == 2">
                                <div v-if="act == 'result'" class="col-md-3 col-sm-4 flex">
                                    <label class="flex_left">中标公司：</label>
                                    <span class="flex_right"><input class="form-control" name="hitCompany" v-model="form.hitCompany" autocomplete="off" aria-autocomplete="none"></span>
                                </div>
                                <div v-else class="col-md-3 col-sm-4 flex">
                                    <label class="flex_left2">中标公司：</label>
                                    <span class="flex_right2">{{form.hitCompany}}</span>
                                </div>
                            </template>

                            <div v-if="act == 'result'" class="col-md-12 col-sm-12 flex">
                                <label class="flex_left">{{form.hit == 2 ? '失':'中'}}标备注：</label>
                                <span class="flex_right" style="margin-bottom: 5px"><textarea class="form-control" name="missReason" v-model="form.missReason" autocomplete="off" aria-autocomplete="none"></textarea></span>
                            </div>
                            <div v-else class="col-md-12 col-sm-12 flex">
                                <label class="flex_left2">{{form.hit == 2 ? '失':'中'}}标备注：</label>
                                <span class="flex_right2">{{form.missReason}}</span>
                            </div>

                            <div class="col-md-3 col-sm-4 flex" v-if="form.status > 4">
                                <label class="flex_left2">录入人：</label>
                                <span class="flex_right2">{{form.resultUserName}}</span>
                            </div>
                            <div class="col-md-3 col-sm-4 flex" v-if="form.status > 4">
                                <label class="flex_left2">录入时间：</label>
                                <span class="flex_right2">{{form.resultTime}}</span>
                            </div>

                            <div v-if="act == 'result'" class="col-md-12 flex">
                                <label class="flex_left">附件：</label>
                                <span class="flex_right"><input type="file" name="attachs" multiple /></span>
                            </div>
                            <div v-else class="col-md-12 flex">
                                <label class="flex_left2">附件：</label>
                                <div class="flex_right2 imgPreview">
                                    <template v-for="file in form.attachs">
                                        <img v-if="file.fileName.toLowerCase().endsWith('.jpg') ||
                                          file.fileName.toLowerCase().endsWith('.png') ||
                                          file.fileName.toLowerCase().endsWith('.gif') ||
                                          file.fileName.toLowerCase().endsWith('.bmp')" :src="file.filePath" style="width:70px; height:50px;margin-right:5px;" />
                                        <a v-else :href="file.filePath" target="_blank">{{file.fileName}}</a>
                                    </template>
                                </div>
                            </div>
                        </div>

                        </form>
                    </div>
                    <div style="text-align: center;margin: 5px"></div>
                </div>
            </div>


        <div style="text-align: center">
            <input v-if="act == 'quote'" type="button" class="btn btn-primary btn-sm" @click="choose" value="保存策略"/>
            <input v-if="act == 'confirm'" type="button" class="btn btn-success btn-sm" @click="confirm" value="确 认"/>
            <input v-if="act == 'result'" type="button" class="btn btn-success btn-sm" @click="result" value="保 存"/>
            <input type="button" class="btn btn-white btn-sm" style="margin-left: 10px" onclick="closeItem()" value="关 闭"/>
        </div>
    </div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<th:block th:include="include :: bootstrap-fileinput553-js"/>
<script th:src="@{/dayjs@1.8.21/dayjs.min.js}"></script>
<script th:src="@{/js/bignumber.min.js}"></script>
    <script th:inline="javascript">
        var uploadCache = {}; // 每一行对应一个tid
        var vue = new Vue({
            el: '#app',
            data() {
                return {
                    act: [[${act}]],
                    form: null,
                    quote: null,
                    dtls: [],
                    carTypeDict: [[${@dict.getType('car_type')}]],
                    transCodeDict: [[${@dict.getType('trans_code')}]],
                    billingTypeX: [[${@dict.getType('billing_type')}]],
                    billingMethod: [[${T(com.ruoyi.tms.constant.BillingMethod).asMap()}]],
                    g7TaxRate: [(${@sysConfigServiceImpl.selectConfigByKey("g7_tax_rate")})],
                    oilTaxRate: [(${@sysConfigServiceImpl.selectConfigByKey("oil_tax_rate")})],
                    cashTaxRate: [(${@sysConfigServiceImpl.selectConfigByKey("cash_tax_rate")})]
                }
            },
            computed: {
                billingType () {
                    let arr = [{dictValue:'G7',dictLabel:'G7'}];
                    for (let i=0;i<this.billingTypeX.length;i++) {
                        let {dictValue,dictLabel} = this.billingTypeX[i];
                        dictLabel = dictLabel.replace('增值税','').replace('用发','').replace('（','(').replace('）',')').replace('发票','');
                        arr.push({dictValue,dictLabel})
                    }
                    return arr;
                },
                isOld() { // 判断是否是旧版报价,旧版没有毛利率
                    if (this.act == 'quote') {
                        return false;
                    }
                    if (this.form && this.form.status >= 3) {
                        let lines = this.form.lines;
                        for (let i = 0; i < lines.length; i++) {
                            let dtls = lines[i].quoteDtls;
                            for (let j = 0; dtls && j < dtls.length; j++) {
                                if (dtls[j].choosed == 1) {
                                    return dtls[j].quoteRate == null;
                                }
                            }
                        }
                        //return this.choosedList.length > 0 && this.choosedList[0].quoteRate == null;
                    }
                    return false
                },
                choosed() {
                    let lines = this.form.lines;
                    for (let i = 0; i < lines.length; i++) {
                        let dtls = lines[i].quoteDtls;
                        for (let j = 0; dtls && j < dtls.length; j++) {
                            if (dtls[j].choosed == 1) {
                                return true;
                            }
                        }
                    }
                    return false
                },
                choosedLines() {
                    let list = [];
                    let lines = this.form.lines;
                    for (let i = 0; i < lines.length; i++) {
                        delete lines[i]['choosedList']

                        this.$set(lines[i], 'choosedList', []);//采用的

                        let lineQuoteAmount = 0;
                        //let lineQuoteAmount2 = 0;
                        let choosedList = lines[i]['choosedList'];

                        let dtls = lines[i].quoteDtls;
                        for (let j = 0; dtls && j < dtls.length; j++) {
                            //console.log(dtls[j])
                            if (dtls[j].choosed == 1) {
                                choosedList.push(dtls[j]);
                                this.$set(dtls[j], 'timesCost', this.times(dtls[j].suggest, dtls[j].times||1)); // 设置总成本
                                this.$set(dtls[j], 'tax', this.taxOf(dtls[j]));
                                // 计算成本单价
                                if (dtls[j].billingMethod == '1') {
                                    this.$set(dtls[j], 'unitPrice', this.div(dtls[j].timesCost, dtls[j].weight, 2)); // 总成本除以总吨位
                                } else if (dtls[j].billingMethod == '2') {
                                    this.$set(dtls[j], 'unitPrice', this.div(dtls[j].timesCost, dtls[j].volume, 2)); // 总成本除以总方量
                                } else if (dtls[j].billingMethod == '5') {
                                    this.$set(dtls[j], 'unitPrice', this.div(dtls[j].timesCost, dtls[j].num, 2)); // 总成本除以总件数
                                }
                                if (dtls[j]['totalCost']) {
                                    lineQuoteAmount = this.plus(lineQuoteAmount, dtls[j]['totalCost']);
                                }
                            }
                        }
                        if (lines[i]['otherCost']) {
                            lineQuoteAmount = this.plus(lineQuoteAmount, lines[i]['otherCost']);
                        }
                        if (this.act == 'quote' || this.act == 'result') {
                            if (lines[i].amountFromDb) {
                                this.$set(lines[i], 'quoteAmountOrigin', lines[i].tempAmount); // 使用quoteAmount触发
                                this.$set(lines[i], 'quoteAmountAct', lineQuoteAmount);
                                delete lines[i].amountFromDb;
                            } else {
                                this.$set(lines[i], 'quoteAmount', lineQuoteAmount);
                                this.$set(lines[i], 'quoteAmountOrigin', lineQuoteAmount);
                                this.$set(lines[i], 'quoteAmountAct', lineQuoteAmount);
                            }
                        } else {
                            this.$set(lines[i], 'quoteAmountOrigin', lineQuoteAmount);
                        }
                        list.push(lines[i])
                    }
                    return list;
                },

                choosedSum() {
                    let sum = {}
                    let sumQuoteAmount = 0;
                    let sumTimes = 0;
                    let sumTotalCost = 0;
                    let sumOtherCost = 0;
                    let sumTimesCost = 0;
                    let sumTax = 0;
                    let quoteRate = null;

                    for (let i = 0; i < this.choosedLines.length; i++) {
                        let line = this.choosedLines[i];
                        let choosedList = line.choosedList;
                        for (let j = 0; j < choosedList.length; j++) {
                            sumTimes = this.plus(sumTimes, choosedList[j].times||1);
                            sumTimesCost = this.plus(sumTimesCost, choosedList[j].timesCost||0);
                            sumTotalCost = this.plus(sumTotalCost, choosedList[j].totalCost||0);
                            sumTax = this.plus(sumTax, choosedList[j].tax||0);
                        }

                        if (choosedList.length > 0) {
                            sumOtherCost = this.plus(sumOtherCost, line.otherCost || 0);
                            sumQuoteAmount = this.plus(sumQuoteAmount, line.quoteAmount || 0);
                        }

                    }
                    sum['sumTimes'] = sumTimes;
                    sum['sumTimesCost'] = sumTimesCost;
                    sum['sumTax'] = sumTax||'';
                    sum['sumTotalCost'] = sumTotalCost;
                    if (sumTotalCost) {
                        sum['quoteRate'] = ((sumTotalCost - sumTimesCost - sumTax) / sumTotalCost * 100).toFixed(2) + '%';
                    }
                    sum['sumOtherCost'] = sumOtherCost;
                    sum['sumQuoteAmount'] = sumQuoteAmount;

                    return sum;
                },
                sumHitAmount() {
                    if (this.form.hit == 1) {
                        let sum = 0;
                        for (let i = 0; i < this.choosedLines.length; i++) {
                            let lineSum = 0;
                            var hitCount = 0;//中标数
                            for (let j = 0; j < this.choosedLines[i].choosedList.length; j++) {
                                if (this.choosedLines[i].choosedList[j].hit == 1) {
                                    lineSum = this.plus(lineSum, this.choosedLines[i].choosedList[j].totalCost);
                                    hitCount++;
                                }
                            }
                            if (hitCount > 0 && this.choosedLines[i].otherCost) { // 有中标的才计算其他费用
                                lineSum = this.plus(lineSum, this.choosedLines[i].otherCost);
                            }
                            //console.log(lineSum, this.choosedLines[i].quoteAmountAct, this.choosedLines[i].quoteAmountOrigin)
                            sum = this.plus(sum, lineSum)
                        }
                        return sum;
                    }
                    return null;
                }
            },
            created() {
                $.ajax({
                    url: ctx + "rfq/enquiry-info-x/[(${id})]",
                    cache: false,
                    success: function (result) {
                        if (result.code === 0) {
                            var lines = result.data.enquiry.lines;
                            // 成本采用区合并同一车长数据
                            for (let i = 0; i < lines.length; i++) {
                                for (let j = 0; lines[i].quoteDtls && j < lines[i].quoteDtls.length; j++) {
                                    let rowspan = 1;
                                    let theTr = lines[i].quoteDtls[j];
                                    for (let k = j + 1; k < lines[i].quoteDtls.length; k++) {
                                        if (theTr.cartypeId == lines[i].quoteDtls[k].cartypeId) {
                                            lines[i].quoteDtls[k]['rowspan'] = 0;
                                            rowspan++;
                                        } else {
                                            theTr['rowspan'] = rowspan;
                                            j = k - 1;
                                            break;
                                        }
                                    }
                                    if (theTr['rowspan'] == null) {
                                        theTr['rowspan'] = rowspan;
                                    }
                                }
                                if (lines[i].quoteAmount != null) {
                                    lines[i].tempAmount = lines[i].quoteAmount;
                                    lines[i].amountFromDb = true; // 当前价格取自db
                                }
                            }
                            vue.form = result.data.enquiry;
                            //vue.dtls = result.data.dtls;
                            if (vue.act == 'result') {
                                if (vue.form.hit == null || vue.form.hit == 0) {
                                    vue.$set(vue.form, "hit", 1)
                                    //vue.$set(vue.form, "hitAmount", vue.form.quoteAmount);
                                    for (let i = 0; i < vue.choosedLines.length; i++) {
                                        for (let j = 0; j < vue.choosedLines[i].choosedList.length; j++) {
                                            vue.$set(vue.choosedLines[i].choosedList[j], 'hit', 1);
                                        }
                                    }
                                }
                                vue.$nextTick(function(){
                                    // 填报中标情况时，渲染附件控件
                                    let option = {
                                        theme: "explorer-fa5", //主题
                                        language: 'zh',
                                        uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                                        //deleteUrl: ctx + "common/deleteImage",
                                        uploadExtraData: {key: 'attachs'},   //上传id，传入后台的参数
                                        deleteExtraData: {key: 'id'},
                                        // extra" {key: ''}, // 上面两个一致则可使用该字段？
                                        enctype: 'multipart/form-data',
                                        allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                                        initialPreviewAsData: true,
                                        overwriteInitial: false,
                                        //initialPreviewConfig: [
                                        //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                                        //],
                                        //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                                        maxFileCount: 0, // 0:不限制上传数
                                        showUpload: false,  // 不显示上传按钮，选择后直接上传
                                        //previewClass:"uploadPreview",
                                        minFileSize: 1, // 1KB
                                        previewFileIcon: '<i class="fa fa-file"></i>',
                                        allowedPreviewTypes: ['image'],
                                        showClose: false,  //是否显示右上角叉按钮
                                        showUpload: false, //是否显示下方上传按钮
                                        showRemove: false, // 是否显示下方移除按钮
                                        //autoReplace: true,
                                        //showPreview: false,//是否显示预览(false=只剩按钮)
                                        showCaption: false,//底部上传按钮左侧文本
                                        uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                                        fileActionSettings: {
                                            showUpload: false,		//每个文件的上传按钮
                                            showDrag: false,
                                            //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                                        },
                                    };
                                    $('[name=attachs]').fileinput(option).on("filebatchselected", function (e, files) {
                                        //uploading = true;
                                        //console.log('filebatchselected', files)
                                        $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                                    }).on("fileuploaded", function (event, data, previewId, index) {
                                        //单个上传成功事件
                                        //console.log("fileuploaded", event, data, previewId, index)
                                        var code = data.response.code;
                                        if (code !== 0) {
                                            $.modal.closeLoading();
                                            $.modal.alertError("上传失败：" + data.response.msg);
                                            return;
                                        }
                                        uploadCache[previewId] = data.response.tid;
                                        var curTids = []
                                        for (const uploadCacheKey in uploadCache) {
                                            curTids.push(uploadCache[uploadCacheKey])
                                        }
                                        vue.$set(vue.form, 'attachTids', curTids)
                                    }).on('filesuccessremove', function (event, previewId, index) {
                                        //上传后删除事件
                                        //console.log("filesuccessremove", event, previewId, index)
                                        var tid = uploadCache[previewId];
                                        if (tid) {
                                            $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                                                console.log(result)
                                            }, 'json')
                                        }
                                        delete uploadCache[previewId]
                                        var curTids = []
                                        for (const uploadCacheKey in uploadCache) {
                                            curTids.push(uploadCache[uploadCacheKey])
                                        }
                                        vue.$set(vue.form, 'attachTids', curTids)
                                    });


                                })
                            }

                            vue.$nextTick(function () {
                                $('.imgPreview').viewer({url: 'data-original', title: false, navbar: false});
                                $('[data-toggle="tooltip"]').tooltip()
                            });

                        } else {
                            $.modal.alertError(result.msg);
                        }
                    }
                })
            },
            mounted() {
            },
            methods: {
                /*transCodeName(line) {
                    let arr = [];
                    if (line.transCodeArr) {
                        for (let i = 0; i < line.transCodeArr.length; i++) {
                            for (let j = 0; j < this.transCodeDict.length; j++) {
                                if (line.transCodeArr[i] == this.transCodeDict[j].dictValue) {
                                    arr.push(this.transCodeDict[j].dictLabel);
                                    break;
                                }
                            }
                        }
                    }
                    return arr.join('，')
                },*/
                transNameByCode(code) {
                    for (let j = 0; j < this.transCodeDict.length; j++) {
                        if (code == this.transCodeDict[j].dictValue) {
                            return this.transCodeDict[j].dictLabel;
                        }
                    }
                    return null;
                },
                dictLabel(code, dict) {
                    for (let j = 0; j < dict.length; j++) {
                        if (code == dict[j].dictValue) {
                            return dict[j].dictLabel;
                        }
                    }
                    return null;
                },
                setTimes(e, x, i) {
                    let dtl = this.choosedLines[x].choosedList[i];
                    this.$set(dtl, 'times', e.target.value);
                    let bm = dtl.billingMethod == null ? '3' : dtl.billingMethod;
                    // 计算总成本、车次总报价
                    this.$set(dtl, 'timesCost', this.times(dtl.suggest, dtl.times || 1))
                    if (bm == '3') { // 包车时，总报价跟着车次走
                        if (dtl.quotePrice) {
                            this.$set(dtl, 'totalCost', this.times(dtl.quotePrice, dtl.times || 1))
                        } else {
                            this.$set(dtl, 'totalCost', '')
                        }
                    } else {
                        this.doSetQuotePrice(dtl.quoteUnitPrice, dtl)
                    }
                },
                setUnitPrice(e, x, i) {
                    let v = e.target.value;
                    let dtl = this.choosedLines[x].choosedList[i];
                    this.$set(dtl, 'unitPrice', v);
                },
                setQuotePrice(e, x, i) {
                    let v = e.target.value;
                    let dtl = this.choosedLines[x].choosedList[i];
                    this.doSetQuotePrice(v, dtl);
                },
                doSetQuotePrice(v, dtl) {
                    // 计算毛利率
                    if (v !='' && v != null) {
                        let vol = null;
                        if (dtl.billingMethod == '1') { vol = dtl.weight; } // 按吨
                        if (dtl.billingMethod == '2') { vol = dtl.volume; } // 按方
                        if (dtl.billingMethod == '5') { vol = dtl.num; } // 按件
                        if (dtl.billingMethod == null || dtl.billingMethod == '3') {
                            this.$set(dtl, 'quotePrice', v);
                            let totalCost = this.times(v, dtl.times || 1, 2);
                            this.$set(dtl, 'totalCost', totalCost)
                            let n = this.minus(totalCost, dtl.timesCost); // 计算毛利
                            let tax = dtl.tax||0;//this.taxOf(dtl);
                            n = this.minus(n, tax);// 减去税金
                            n = this.div(n, totalCost, 4) // 毛利小数比
                            n = this.times(n, 100); // 转为百分比
                            this.$set(dtl, 'quoteRate', n);
                        } else {
                            this.$set(dtl, 'quoteUnitPrice', v);
                            this.$set(dtl, 'totalCost', this.times(dtl.quoteUnitPrice, vol)) //总报价
                            // timesCost=车次成本、totalCost=总报价（不含其他费用）
                            let n = this.minus(dtl.totalCost, dtl.timesCost); // 计算毛利
                            let tax = dtl.tax||0;//this.taxOf(dtl);
                            n = this.minus(n, tax);// 减去税金
                            n = this.div(n, dtl.totalCost, 4) // 毛利小数比
                            n = this.times(n, 100); // 转为百分比
                            this.$set(dtl, 'quoteRate', n);//毛利率
                        }
                    } else {
                        this.$set(dtl, 'quoteRate', '');
                        this.$set(dtl, 'totalCost', '');
                    }
                },
                taxOf (dtl) {
                    if (this.form.custBillingType == '6') {//客户开票点
                        return 0;
                    }
                    let zcb = dtl.timesCost||0;
                    let ykbl = dtl.oilRatio||0;
                    let yk = this.times(zcb, ykbl, 4);
                    yk = this.div(yk, 100, 2); // 油卡金额
                    let yktax = this.times(yk, this.oilTaxRate, 2);
                    let xj = this.minus(zcb, yk); // 现金金额
                    let xjtax = 0;
                    if (dtl.billingType == null || dtl.billingType == '6') {//不开票
                        xjtax = this.times(xj, this.cashTaxRate, 2);
                    }
                    return this.plus(yktax, xjtax);
                },
                /*doSetQuoteUnitPrice(v, dtl) {
                    this.$set(dtl, 'quoteUnitPrice', v);
                    // 计算毛利率
                    if (v !='') {
                        let vol = null;
                        if (dtl.billingMethod == '1') { vol = dtl.weight; } // 按吨
                        if (dtl.billingMethod == '2') { vol = dtl.volume; } // 按方
                        if (dtl.billingMethod == '5') { vol = dtl.num; } // 按件
                        this.$set(dtl, 'totalCost', this.times(dtl.quoteUnitPrice, vol)) //总报价
                        // timesCost=车次成本、totalCost=总报价（不含其他费用）
                        let n = this.minus(dtl.totalCost, dtl.timesCost); // 计算毛利
                        n = this.div(n, dtl.totalCost, 4) // 毛利小数比
                        n = this.times(n, 100); // 转为百分比
                        this.$set(dtl, 'quoteRate', n);//毛利率
                    } else {
                        this.$set(dtl, 'quoteRate', '');
                        this.$set(dtl, 'totalCost', '');
                    }
                },*/
                setQuoteRate(e, x, i) { // 调整毛利率计算单位报价和总报价
                    let dtl = this.choosedLines[x].choosedList[i];
                    this.$set(dtl, 'quoteRate', e.target.value);
                    // 计算车次总报价
                    if (e.target.value !='') {
                        let vol = null;
                        if (dtl.billingMethod == '1') { vol = dtl.weight; } // 按吨
                        if (dtl.billingMethod == '2') { vol = dtl.volume; } // 按方
                        if (dtl.billingMethod == '5') { vol = dtl.num; } // 按件
                        if (dtl.billingMethod == null || dtl.billingMethod == '3') {// 按车次
                            let rate = this.minus(100, dtl.quoteRate); // (单车总成本+税金)占百分比
                            rate = this.div(rate, 100, 4); // (单车总成本+税金)占小数比
                            let zcb_tax = this.plus(dtl.timesCost, dtl.tax||0);//单车总成本+税金
                            let totalCost = this.div(zcb_tax, rate); // (单车总成本+税金)/小数比=单车总报价
                            let quotePrice = this.div(totalCost, dtl.times||1, 2) // 单车总报价/车次=单车报价
                            this.$set(dtl, 'quotePrice', quotePrice);
                            totalCost = this.times(dtl.quotePrice, dtl.times || 1, 2);
                            this.$set(dtl, 'totalCost', totalCost)
                        } else {
                            // 毛利率推算总报价
                            let rate = this.minus(100, dtl.quoteRate); //(单车总成本+税金)占百分比
                            rate = this.div(rate, 100, 4); // (单车总成本+税金)占小数比
                            let zcb_tax = this.plus(dtl.timesCost, dtl.tax||0);//单车总成本+税金
                            let totalCost = this.div(zcb_tax, rate);
                            // 总报价除以总货量反推报价单价
                            let quoteUnitPrice = this.div(totalCost, vol, 2);
                            this.$set(dtl, 'quoteUnitPrice', quoteUnitPrice);
                            // 再用单价推总价，平掉总价小数点
                            totalCost = this.times(dtl.quoteUnitPrice, vol, 2);
                            this.$set(dtl, 'totalCost', totalCost)
                        }
                    } else {
                        this.$set(dtl, 'quotePrice', '');
                        this.$set(dtl, 'quoteUnitPrice', '');
                        this.$set(dtl, 'totalCost', '');
                    }
                },
                /*setQuoteRate2(e, x, i) {
                    let dtl = this.choosedLines[x].choosedList2[i];
                    this.$set(dtl, 'quoteRate', e.target.value);
                    if (e.target.value !='') {
                        let n = this.minus(100, dtl.quoteRate); // 总成本占总报价百分比
                        n = this.div(n, 100, 4); // 成本占报价小数比
                        n = this.div(dtl.suggest, n); // 总成本反推总报价
                        this.$set(dtl, 'totalCost', n)
                    } else {
                        this.$set(dtl, 'totalCost', '');
                    }
                },*/
                /*setTotalCost2(e, x, i) {
                    let dtl = this.choosedLines[x].choosedList2[i];
                    this.$set(dtl, 'totalCost', e.target.value);
                    if (e.target.value !='') {
                        let n = this.minus(dtl.totalCost, dtl.suggest); // 毛利金额
                        n = this.div(n, dtl.totalCost, 4); // 毛利占报价四位小数比
                        n = this.times(n, 100); // 转百分比
                        this.$set(dtl, 'quoteRate', n);
                    } else {
                        this.$set(dtl, 'quoteRate', '');
                    }
                },*/
                plus(n1, n2) { // +运算
                    return new BigNumber(n1).plus(new BigNumber(n2)).toNumber()
                },
                minus(n1, n2) { // -运算
                    return new BigNumber(n1).minus(new BigNumber(n2)).toNumber()
                },
                times(n1, n2, dp) {
                    let bn = new BigNumber(n1||0).times(new BigNumber(n2||0));
                    if (dp != undefined) {
                        bn = bn.dp(dp);
                    }
                    return bn.toNumber()
                },
                div(n1, n2, dp) { // 除运算（被除数，除数，保留小数）
                    if (dp == undefined) {
                        dp = 2;
                    }
                    return new BigNumber(n1).div(new BigNumber(n2)).dp(dp).toNumber()
                },
                countDecimalPlaces(num) {
                    if (num % 1 !== 0) {
                        return num.toString().split('.')[1].replace(/0+$/, '').length;
                    }
                    return 0;
                },
                backChngPrice(line) {
                    let total = this.minus(line.quoteAmount, line.otherCost||0);
                    let totalOrigin = this.minus(line.quoteAmountOrigin, line.otherCost||0);
                    let cha = this.minus(total, totalOrigin); // 变动差额，按比例均摊车次总报价，再均摊到报价
                    if (cha == 0) {
                        return
                    }
                    // 车次总报价如果全部有值，按车次总报价均摊，如果不全有值，按总成本均摊（毛利率统一）
                    let totalTimesQuote = 0;
                    let totalTimesSuggest = 0;
                    let allvalue = true; // 是否车次总报价均有值
                    for (let i = 0; i < line.choosedList.length; i++) {
                        allvalue = allvalue && !!line.choosedList[i].totalCost;
                        if (line.choosedList[i].totalCost) {
                            totalTimesQuote = this.plus(totalTimesQuote, line.choosedList[i].totalCost);
                        }
                        if (!line.choosedList[i].timesCost) {
                            $.modal.alertError("第" + (i+1) + "行缺少总成本或车次");
                            return
                        }
                        totalTimesSuggest = this.plus(totalTimesSuggest, line.choosedList[i].timesCost);
                    }
                    //allvalue = false;
                    var fm = allvalue ? totalTimesQuote : totalTimesSuggest;
                    var passed = 0;
                    for (let i = 0; i < line.choosedList.length; i++) {
                        let rate = null; // 成本占总成本比例
                        var dtl = line.choosedList[i];
                        var fz = null;
                        if (allvalue) {
                            // 按总报价分摊
                            fz = dtl.totalCost;
                        } else {
                            // 按总成本分摊，报价毛利率将一致
                            fz = dtl.timesCost;
                        }
                        rate = fz / fm;
                        let newTotalCost = null;
                        if (i == line.choosedList.length - 1) {
                            newTotalCost = this.minus(total, passed);
                        } else {
                            newTotalCost = this.times(total, rate, 2);
                        }
                        let vol = null;
                        if (dtl.billingMethod == '1') { vol = dtl.weight; } // 按吨
                        if (dtl.billingMethod == '2') { vol = dtl.volume; } // 按方
                        if (dtl.billingMethod == '5') { vol = dtl.num; } // 按件
                        if (dtl.billingMethod == null || dtl.billingMethod == '3') { vol = dtl.times || 1; } // 按车次
                        //console.log('新总报价', newTotalCost)
                        let newQuotePrice = this.div(newTotalCost, vol);
                        //console.log('newbj', newQuotePrice)
                        this.doSetQuotePrice(0, dtl); // 触发computed.choosedLines
                        this.doSetQuotePrice(newQuotePrice, dtl);
                        passed = this.plus(passed, dtl.totalCost || 0);
                    }
                    // 如果合计报价与当前修改后的合计报价不一致，将零头放到零担上，没有零担时放到1次车长上或可整除车次上
                    this.$nextTick(()=>{
                        //console.log(line.quoteAmount, total)
                        let calcTotal = this.minus(line.quoteAmount, line.otherCost||0)
                        //console.log('calc', calcTotal, total)
                        if (calcTotal != total) {
                            let diff = this.minus(calcTotal, total);
                            let idx = -1; // 可补差的数据行索引
                            for (let i = 0; i < line.choosedList.length; i++) {
                                let dtl = line.choosedList[i];
                                let bm = dtl.billingMethod;
                                let vol = null;
                                if (bm == '1') {vol = dtl.weight;}
                                else if (bm == '2') {vol = dtl.volume;}
                                else if (bm == '5') {vol = dtl.num;}
                                else if (bm == null || bm == '3') {vol = dtl.times||1}
                                if (dtl.totalCost > diff) {
                                    // 单价小数点两位以内
                                    //console.log('单价',this.div(diff, vol, 10))
                                    if (this.countDecimalPlaces(this.div(diff, vol, 10)) <= 2) {
                                        console.log('符合',i)
                                        idx = i;
                                        break;
                                    }
                                }
                            }
                            //console.log('diff',diff,'idx',idx)
                            if (idx >= 0) {
                                let dtl = line.choosedList[idx];
                                let newQuotePrice = null;
                                if (dtl.billingMethod == null || dtl.billingMethod == '3') {
                                    //newQuotePrice = this.minus(dtl.quotePrice, diff);
                                    newQuotePrice = this.minus(dtl.quotePrice, this.div(diff, dtl.times || 1))
                                } else {
                                    let vol = null;
                                    let bm = dtl.billingMethod;
                                    if (bm == '1') {vol = dtl.weight;}
                                    else if (bm == '2') {vol = dtl.volume;}
                                    else if (bm == '5') {vol = dtl.num;}
                                    newQuotePrice = this.minus(dtl.quoteUnitPrice, this.div(diff, vol, 10))
                                }
                                this.doSetQuotePrice(newQuotePrice, dtl);
                            } else { // 找不到可补差数据时，提示偏差
                                //console.log('强制==')
                                //line.quoteAmount = this.plus(total, line.otherCost||0);
                                //this.$set(line, 'quoteAmountOrigin', line.quoteAmount);

                                // == 未凑到数字时，用穷举遍历 new start ==
                                let fn = new BigNumber(diff).abs();
                                let step = new BigNumber(0.01);
                                let nums = new Array(line.choosedList.length);
                                let counts = new Array(line.choosedList.length)
                                for (let i = 0; i < line.choosedList.length; i++) {
                                    let dtl = line.choosedList[i];
                                    let bm = dtl.billingMethod;
                                    if (bm == '1') {counts[i] = dtl.weight;}
                                    else if (bm == '2') {counts[i] = dtl.volume;}
                                    else if (bm == '5') {counts[i] = dtl.num;}
                                    else if (bm == null || bm == '3') {counts[i] = dtl.times||1}
                                }

                                let matched = calcNext(new BigNumber(0), nums, counts, 0, fn, step);
                                if (matched) {
                                    console.log('匹配到')
                                    for (let i = 0; i < nums.length; i++) {
                                        if (nums[i].toNumber()) {
                                            let dtl = line.choosedList[i];
                                            let newQuotePrice = null;
                                            if (diff < 0) {
                                                if (dtl.billingMethod == null || dtl.billingMethod == '3') {
                                                    newQuotePrice = this.plus(dtl.quotePrice, nums[i])
                                                } else {
                                                    newQuotePrice = this.plus(dtl.quoteUnitPrice, nums[i])
                                                }
                                            } else if (diff > 0) {
                                                if (dtl.billingMethod == null || dtl.billingMethod == '3') {
                                                    newQuotePrice = this.minus(dtl.quotePrice, nums[i])
                                                } else {
                                                    newQuotePrice = this.minus(dtl.quoteUnitPrice, nums[i])
                                                }
                                            }
                                            this.doSetQuotePrice(newQuotePrice, dtl);
                                        }
                                    }
                                } else {
                                    console.log('未匹配到')
                                    line.quoteAmount = this.plus(total, line.otherCost||0);
                                    this.$set(line, 'quoteAmountOrigin', line.quoteAmount);
                                }
                                // == new end ==

                            }
                        }
                    })

                },
                choose() {
                    let pass = $('#form-choose').validate({
                        errorPlacement:function(error,element){
                            if (element.parent().hasClass("input-group")){
                                element.parent().after(error);
                            } else {
                                element.after(error);
                            }
                        }
                    }).form();
                    if (!pass) {
                        return;
                    }
                    if (!this.choosed) {
                        $.modal.msgError("请先选择采用的成本");
                        return;
                    }
                    $.modal.confirm("确定提交策略报价吗？", function(){
                        let data = {id: [[${id}]]}
                        //data['quoteRate'] = vue.form.quoteRate;
                        data['otherCost'] = vue.choosedSum['sumOtherCost']||null;
                        data['quoteAmount'] = vue.choosedSum['sumQuoteAmount']||null;
                        //data['otherCost2'] = vue.choosedSum['sumOtherCost2']||null;
                        //data['quoteAmount2'] = vue.choosedSum['sumQuoteAmount2']||null;
                        data['policyMemo'] = vue.form.policyMemo;
                        /*let choosedDtlIds = []
                        let lines = vue.form.lines;
                        for (let i = 0; i < lines.length; i++) {
                            let dtls = lines[i].quoteDtls;
                            for (let j = 0; j < dtls.length; j++) {
                                if (dtls[j]['choosed'] == 1) {
                                    choosedDtlIds.push(dtls[j].id);
                                }
                            }
                        }
                        data['choosedDtlIds'] = choosedDtlIds;*/
                        data['lines'] = vue.choosedLines;
                        $.operate.saveTabJson(ctx + "rfq/enquiry-choose-x", data, function(){
                            // 结束回调
                        });
                    })
                },
                confirm() {
                    $.modal.confirm('确认提交吗？', function(){
                        let data = {id: [[${id}]]}
                        $.operate.saveTabJson(ctx + "rfq/confirm-enquiry", data, function(){
                            // 结束回调
                        });
                    })
                },
                result() {
                    if (!$.validate.form('form-result')) {
                        return;
                    }
                    let hitQuoteDtls = [];
                    if (vue.form.hit == 1) {
                        for (let i = 0; i < vue.choosedLines.length; i++) {
                            for (let j = 0; j < vue.choosedLines[i].choosedList.length; j++) {
                                if (vue.choosedLines[i].choosedList[j].hit == 1) {
                                    hitQuoteDtls.push(vue.choosedLines[i].choosedList[j].id)
                                }
                            }
                        }
                        if (hitQuoteDtls.length == 0) {
                            $.modal.msgError("请勾选中标的策略报价");
                            return;
                        }
                    }
                    $.modal.confirm('确认保存中标结果吗？', function(){
                        let data = {id: [[${id}]]};
                        data['hit'] = vue.form.hit;
                        data['hitQuoteDtls'] = hitQuoteDtls;
                        data['otherCost'] = vue.choosedSum['sumOtherCost']||null;
                        data['quoteAmount'] = vue.choosedSum['sumQuoteAmount']||null;
                        data['lines'] = vue.choosedLines;
                        data['hitAmount'] = vue.form.hitAmount;
                        data['missReason'] = vue.form.missReason;
                        if (data['hit'] == 1) {
                            data['hitAmount'] = vue.sumHitAmount;
                        }
                        if (data['hit'] == 2) {
                            data['hitCompany'] = vue.form.hitCompany;
                        }
                        data['attachTids'] = vue.form.attachTids;
                        $.operate.saveTabJson(ctx + "rfq/result-enquiry", data, function(){
                            // 结束回调
                        });
                    })
                },
                calc_amount(checkbox) {
                    if (checkbox) {
                        let dtlId = $(checkbox).attr('dtl-id');
                        let lineId = $(checkbox).attr('line-id');
                        let cartypeId = $(checkbox).attr('cartype-id');
                        let type = $(checkbox).attr('biz-type');

                        // 校验同路线、同车型只能采用一个,disabled掉其他
                        $('[dtl-id][line-id="' + lineId + '"][biz-type="' + type + '"][cartype-id="' + cartypeId + '"]').each(function () {
                            if ($(this).attr('dtl-id') != dtlId) {
                                $(this).prop("disabled", $(checkbox).is(":checked")).prop("checked", false);
                                for (let i = 0; i < vue.form.lines.length; i++) {
                                    if (vue.form.lines[i].id == lineId) {
                                        for (let j = 0; j < vue.form.lines[i].quoteDtls.length; j++) {
                                            if (vue.form.lines[i].quoteDtls[j].id == $(this).attr('dtl-id')) {
                                                vue.$set(vue.form.lines[i].quoteDtls[j], 'choosed', 0);
                                            }
                                        }
                                    }
                                }
                            }
                        })
                    }
                },
                carTypeLabel(line) {
                    if (!line.carTypeArr) {
                        return ''
                    }
                    let tmp = [];
                    for (let i = 0; i < this.carTypeDict.length; i++) {
                        if (line.carTypeArr.indexOf(this.carTypeDict[i].dictValue) >= 0) {
                            if (tmp.length > 0) {
                                tmp.push("、");
                            }
                            tmp.push(this.carTypeDict[i].dictLabel);
                        }
                    }
                    return tmp.join('');
                },
                getLineRoad(line) {
                    // 取公共省市区
                    let road = {
                        deliProvinceId:line.deliList[0].provinceId, deliCityId:line.deliList[0].cityId, deliAreaId:line.deliList[0].areaId,
                        arriProvinceId:line.arriList[0].provinceId, arriCityId:line.arriList[0].cityId, arriAreaId:line.arriList[0].areaId
                    };
                    for (let i = 1; i < line.deliList.length; i++) {
                        if (line.deliList[i].provinceId != road.deliProvinceId) {
                            road.deliProvinceId = null;
                            break;
                        }
                        if (line.deliList[i].cityId != road.deliCityId) {
                            road.deliCityId = null;
                            break;
                        }
                        if (line.deliList[i].areaId != road.deliAreaId) {
                            road.deliAreaId = null;
                            break;
                        }
                    }
                    for (let i = 1; i < line.arriList.length; i++) {
                        if (line.arriList[i].provinceId != road.arriProvinceId) {
                            road.arriProvinceId = null;
                            break;
                        }
                        if (line.arriList[i].cityId != road.arriCityId) {
                            road.arriCityId = null;
                            break;
                        }
                        if (line.arriList[i].areaId != road.arriAreaId) {
                            road.deliAreaId = null;
                            break;
                        }
                    }
                    return road;
                },
                historyPrice(line, quote) {
                    // 历史成交价格界面
                    let road = this.getLineRoad(line);
                    let currentDate = dayjs();
                    let oneMonthBefore = currentDate.subtract(1, 'month');
                    let url = ctx + "business/historyPrice?src=rfq&startDate=" + oneMonthBefore.format('YYYY-MM-DD')
                    for (const key in road) {
                        if (road[key]) {
                            url = url + "&" + key + "=" + road[key];
                        }
                    }
                    let carLen = null;
                    let transCode = null;
                    let carType = null;
                    for (let i = 0; i < line.carLenObjs.length; i++) {
                        if (line.carLenObjs[i].id == quote.cartypeId) {
                            carLen = line.carLenObjs[i].carLen;
                            transCode = line.carLenObjs[i].transCode;
                            carType = line.carLenObjs[i].carType;
                            break;
                        }
                    }
                    if (carLen) {
                        url = url + "&carLen=" + carLen;
                    }
                    if (transCode) {
                        url = url + "&transCode=" + transCode;
                    }
                    if (carType) {
                        url = url + "&carType=" + carType;
                    }
                    $.modal.openTab("历史价格", url);
                },
            }
        });

        /**
         * 穷举（递归）计算
         *
         * @param prefix 当前已凑数字
         * @param nums 数字队列1
         * @param counts 数字队列2
         * @param idx 当前数字索引
         * @param target 要求凑出来的数字
         * @param step 每个数字变动值
         * @returns {boolean}
         */
        function calcNext(prefix, nums, counts, idx, target, step) {
            let isLast = idx == nums.length - 1;
            let downLimit = target.times(-2);
            let upLimit = target.times(2);
            for (nums[idx] = downLimit; nums[idx].lte(upLimit); nums[idx] = nums[idx].plus(step)) {
                let y = nums[idx].times(counts[idx]);
                if (y.dp() > 2) {
                    continue;
                }
                let x = prefix.plus(y);
                if (x.eq(target)) {
                    for (let i = idx + 1; i < nums.length; i++) {
                        nums[i] = new BigNumber(0);
                    }
                    return true;
                } else if (isLast && x.gt(target)) {
                    return false;
                } else if (!isLast) {
                    let b = calcNext(x, nums, counts, idx + 1, target, step);
                    if (b) {
                        return true;
                    }
                }
            }
            return false;
        }


    </script>

</body>
</html>