<div id="layer1" style="display: none;padding: 0 5px;">
    <div id="app1"><!--转发货单-->
        <form id="chooseForm">
            <div style="display: flex;justify-content: space-between;align-items: center;padding:6px 3px;">
                <div v-if="!inInvoice">
                    <label><input type="radio" name="mode" checked v-model="mode" value="single" @change="modeChange">
                        单个模式</label>
                    <label><input type="radio" name="mode" v-model="mode" value="multi" @change="modeChange">
                        批量模式</label>
                </div><div v-else></div>
                <div style="font-weight: bold;color:orangered">※省市区+详细地址匹配现有地址库；联系人、电话将更新(覆盖)到该客户地址库中</div>
            </div>
            <div class="flex" style="margin-bottom: 5px" def-param v-if="mode == 'multi'">
                <input class="form-control laydate" style="width: 116px" name="reqDeliDate" placeholder="要求提货日期">
                <input class="form-control laydate" style="width: 116px;margin-left: 5px" name="reqArriDate"
                       placeholder="要求到货日期">
                <div style="width: 64px;margin-left: 5px">
                    <select class="form-control selectpicker" data-none-selected-text="车长" name="carLenId">
                        <option></option>
                        <option v-for="dct in carLenDict" :value="dct.dictValue">{{dct.dictLabel}}</option>
                    </select></div>
                <div style="width: 100px;margin-left: 5px">
                    <select class="form-control selectpicker" data-none-selected-text="车型" name="carTypeId">
                        <option></option>
                        <option v-for="dct in carTypeDict" :value="dct.dictValue">{{dct.dictLabel}}</option>
                    </select></div>
                <div style="width: 120px;margin-left: 5px">
                    <select class="form-control selectpicker" data-none-selected-text="调度组" name="transLineId">
                        <option></option>
                        <option v-for="dpt in ddl" :value="dpt.deptId">{{dpt.deptName}}</option>
                    </select></div>
                <div style="width: 70px;margin-left: 5px">
                    <select class="form-control selectpicker" data-none-selected-text="包装" name="packId">
                    <option></option>
                    <option v-for="dct in pkgList" :value="dct.dictValue">{{dct.dictLabel}}</option>
                </select></div>
                <label style="line-height: 26px;margin: 0 0 0 5px;color:#aaa">
                    <input type="checkbox" style="transform: translateY(2px)" flag="override">覆盖已有参数</label>
                <input type="button" class="form-control success-bg" @click="fetchToAll"
                       style="width: 106px;margin-left: 5px;" value="应用到勾选路线">
            </div>
            <template v-for="(line,l) in lines">

                <table class="table table-bordered" :style="{marginTop:l>0?'15px':''}">
                    <caption
                            style="padding: 3px 6px;border:1px #ebebeb solid;border-bottom: none;background-color: rgb(245,245,245);border-top:3px solid rgb(214, 214, 244);">
                        <div style="display: flex;justify-content: space-between;align-items: center;font-weight: bold;">
                            <label class="primary">
                                <input type="radio" v-if="mode == 'single'" name="lineId" :value="line.id"
                                       v-model="lineId" @change="lineSelected1(l)">
                                <input type="checkbox" v-if="mode == 'multi'" name="lineId" :value="line.id"
                                       v-model="lineIds" @change="lineSelected(l)">
                                路线{{l + 1}}
                            </label>
                            <span v-if="mode=='multi'&&(line.deliList.length>1||line.arriList.length>1)"
                                  style="color:red">[多装多卸转单后请注意发货单货量是否正确]</span>
                        </div>
                    </caption>
                    <thead>
                    <tr>
                        <th style="text-align: center;width: 30px"></th>
                        <th style="width: 120px">地址名称</th>
                        <th>省市区</th>
                        <th style="width: 340px">详细地址</th>
                        <th style="width: 80px">联系人</th>
                        <th style="width: 100px">联系人电话</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(addr,i) in line.deliList.concat(line.arriList)" :key="addr.id">
                        <td align="center" style="padding: 4px">
                            <span v-if="addr.type==0" class="label label-warning ml5 pa2"
                                  style="margin-bottom: 0">装</span>
                            <span v-else class="label label-success ml5 pa2" style="margin-bottom: 0">卸</span>
                        </td>
                        <td :style="{padding:addr.addressId == null ? '1px':''}"
                            style="vertical-align:top;line-height: 18px">
                            <input v-if="addr.addressId == null"
                                   :disabled="(mode=='single' && line.id!=lineId) || (mode=='multi'&&lineIds.indexOf(line.id)<0)"
                                   class="form-control" :id="'addrName'+l+'_'+i" required v-model="addr.addrName"
                                   autocomplete="off" aria-autocomplete="none">
                            <template v-else>{{addr.addrName}}</template>
                        </td>
                        <td :style="{padding:addr.addressId == null ? '1px':''}"
                            style="vertical-align:top;line-height: 18px">
                            <div v-if="addr.addressId == null" class="flex">
                                <i v-if="mode == 'multi' && lineIds.indexOf(line.id)>=0" class="fa fa-map-marker"
                                   :style="{color:addr.longitudedegree?'#009AFE':'#ff1f1f'}"
                                   style="width:13px;line-height:26px;margin-left:4px;cursor:pointer;font-size: 15px"
                                   @click="getLongLat(addr,l,i)"></i>
                                <span class="flex_right"><select :id="'addrprov'+l+'_'+i" disabled :name="'prov'+i"
                                                                 v-model="addr.provinceId" required
                                                                 class="form-control"><option>省（必选）</option></select></span>
                                <span class="flex_right"><select :id="'addrcity'+l+'_'+i" disabled :name="'city'+i"
                                                                 v-model="addr.cityId" required class="form-control"><option>市（必选）</option></select></span>
                                <span class="flex_right"><select :id="'addrarea'+l+'_'+i"
                                                                 :disabled="addr.addressId != null || (mode=='single' && line.id!=lineId) || (mode=='multi'&&lineIds.indexOf(line.id)<0)"
                                                                 :name="'area'+i" v-model="addr.areaId" required
                                                                 class="form-control"><option>区（可选）</option></select></span>
                            </div>
                            <template v-else><i v-if="mode == 'multi' && lineIds.indexOf(line.id)>=0"
                                                class="fa fa-map-marker"
                                                :style="{color:addr.longitudedegree?'#009AFE':'#ff1f1f'}"
                                                style="width:13px;cursor:pointer;font-size: 15px"
                                                @click="getLongLat(addr,l,i)"></i>{{addr.provinceName}}{{addr.cityName}}{{addr.areaName}}
                            </template>
                        </td>
                        <td :style="{padding:addr.addressId == null?'1px':''}"
                            style="vertical-align:top;line-height: 18px">
                            <input v-if="addr.addressId == null"
                                   :disabled="addr.addressId != null || (mode=='single' && line.id!=lineId) || (mode=='multi'&&lineIds.indexOf(line.id)<0)"
                                   class="form-control" :id="'detailAddr'+l+'_'+i" required v-model="addr.detailAddr"
                                   autocomplete="off" aria-autocomplete="none">
                            <template v-else>{{addr.detailAddr}}</template>
                        </td>
                        <!--首次加载，匹配到联系人和电话时，不赋值custAddressId,下一步之后重新匹配联系人和电话【有则匹配、无则新增】，有custAddressId的不允许修改联系人和电话-->
                        <td :style="{padding:addr.custAddressId == null?'1px':''}"
                            style="vertical-align:top;line-height: 18px">
                            <input v-if="addr.custAddressId == null"
                                   :disabled="(mode=='single' && line.id!=lineId)||(mode=='multi'&&lineIds.indexOf(line.id)<0)"
                                   class="form-control" :id="'contact'+l+'_'+i" required v-model="addr.contact"
                                   minlength="2" oninput="this.value=this.value.replace(/ /g,'')"
                                   autocomplete="off" aria-autocomplete="none">
                            <template v-else>{{addr.contact}}</template>
                        </td>
                        <td :style="{padding:addr.custAddressId == null?'1px':''}"
                            style="vertical-align:top;line-height: 18px">
                            <input v-if="addr.custAddressId == null"
                                   :disabled="(mode=='single' && line.id!=lineId)||(mode=='multi'&&lineIds.indexOf(line.id)<0)"
                                   class="form-control" :id="'contactMobile'+l+'_'+i" required
                                   oninput="this.value=this.value.replace(/ /g,'')"
                                   v-model="addr.contactMobile" autocomplete="off" aria-autocomplete="none">
                            <template v-else>{{addr.contactMobile}}</template>
                        </td>
                    </tr>
                    <tr v-if="mode=='multi'&&lineIds.indexOf(line.id)>=0">
                        <td colspan="6" style="padding-top:1px;padding-bottom: 1px;" :ext-param="l">
                            <div style="display:flex;justify-content: left">
                                <div style="line-height: 26px;">必要参数：</div>
                                <div style="flex:1">
                                    <div v-for="(carLenObj,k) in line.params.orderCarLenObjs" :key="carLenObj.id" :obj-index="k" style="display: flex;" :style="{marginTop:(k>0?'1px':'')}">
                                        <label style="width: 188px;line-height:1;display: flex;align-items: center;margin: 0">
                                            <input type="checkbox" style="margin: 0 3px 0 0;accent-color:#008Aff;" true-value="1" false-value="0" v-model="carLenObj.checked" @change="checkChange(l,k)"> {{disp(carLenObj)}}
                                        </label>
                                        <div style="width: 116px;margin-left: 1px">
                                            <input class="form-control" required :id="'reqDeliDate'+l+'_'+k" :disabled="carLenObj.checked!=1"
                                                 req-date="reqDeliDate" placeholder="要求提货日期" :value="carLenObj.reqDeliDate"/>
                                        </div>
                                        <div style="width: 116px;margin-left: 1px">
                                            <input class="form-control" required :id="'reqArriDate'+l+'_'+k" :disabled="carLenObj.checked!=1"
                                                 req-date="reqArriDate" placeholder="要求到货日期" :value="carLenObj.reqArriDate"/>
                                        </div>
                                        <div style="width: 64px;margin-left: 1px">
                                            <select required :id="'car_len_'+l+'_'+k" :disabled="carLenObj.carLenDisable || carLenObj.checked!=1" class="form-control selectpicker"
                                                 data-none-selected-text="车长" name="carLenId" :value="carLenObj.carLenId">
                                            <option></option>
                                            <option v-for="dct in carLenDict" :value="dct.dictValue">{{dct.dictLabel}}</option>
                                        </select></div>
                                        <div style="width: 100px;margin-left: 1px">
                                            <select required :id="'car_type_'+l+'_'+k" :disabled="carLenObj.carTypeDisable || carLenObj.checked!=1" class="form-control selectpicker"
                                                  data-none-selected-text="车型" name="carTypeId" :value="carLenObj.carTypeId">
                                            <option></option>
                                            <option v-for="dct in carTypeDict" :value="dct.dictValue">{{dct.dictLabel}}</option>
                                        </select></div>
                                        <div style="width: 120px;margin-left: 1px">
                                            <select required :id="'trans_line_id_'+l" :disabled="carLenObj.checked!=1"
                                                 class="form-control selectpicker" data-none-selected-text="调度组"
                                                 name="transLineId" :value="carLenObj.transLineId">
                                            <option></option>
                                            <option v-for="dpt in ddl" :value="dpt.deptId">{{dpt.deptName}}</option>
                                        </select></div>
                                        <div style="line-height: 26px;margin-left: 6px">{{line.goodsName}}</div>
                                        <div style="width: 70px;margin-left: 1px">
                                            <select required :id="'pack_id_'+l" class="form-control selectpicker" :disabled="carLenObj.checked!=1"
                                                  data-none-selected-text="包装" name="packId" :value="carLenObj.packId">
                                            <option></option>
                                            <option v-for="dct in pkgList" :value="dct.dictValue">{{dct.dictLabel}}</option>
                                        </select></div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr v-if="mode=='single'&&lineId==line.id">
                        <td colspan="6">
                            <div style="display:flex;justify-content: left">
                                <div style="line-height: 26px;">询价项目：</div>
                                <label v-for="obj in line.carLenObjs"
                                       style="margin: 0 5px 0 0;display: flex;align-items: center;border:1px #f0e9e3 solid;background-color:rgb(242,251,195);padding: 0 3px;">
                                    <input type="radio" style="accent-color: #0093ff;margin: 0 2px 0 0;" required
                                           v-model="line.params.carLenObjId" :name="'invoice_' + l" :value="obj.id">
                                    <span style="line-height: 1;">{{disp(obj)}}</span>
                                </label>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </template>
        </form>
    </div>
</div>
<script th:src="@{/js/vue.min.js}"></script>
<script th:inline="javascript">

    const vue = new Vue({
        el: '#app1',
        data: () => {
            return {
                lineId: null,
                lineIds: [],
                lines: [],
                mode: 'single', // single、multi
                carLenDict: [[${@dict.getType('car_len')}]],
                carTypeDict: [[${@dict.getType('car_type')}]],
                ddl: [[${dispatcherDeptList}]],
                pkgList: [[${@dict.getType('package_type')}]],
                transCodeDict: [[${@dict.getType('trans_code')}]],
                billingMethod: [[${T(com.ruoyi.tms.constant.BillingMethod).asMap()}]],
                inInvoice: false
            }
        },
        created() {
            this.inInvoice = location.pathname.startsWith("/invoice/")
        },
        methods: {
            reset() {
                this.lineId = null;
                this.lineIds = [];
                this.lines = [];
                this.mode = 'single';
            },
            lineSelected1(i) {
                //var checked = $(e.target).prop('checked');
                //console.log(checked)
                var line = this.lines[i];
                if (!line.params) {
                    this.$set(line, 'params', {});
                }
                this.lineId = line.id;
                if (!line.params.carLenObjId) {
                    if (line.carLenObjs.length == 1) {
                        this.$set(line.params, 'carLenObjId', line.carLenObjs[0].id);
                    }
                }
            },
            lineSelected(i) {
                var line = this.lines[i];
                if (!line.params) {
                    this.$set(line, 'params', {});
                }
                if (!line.params['orderCarLenObjs']) {
                    if (line.carLenObjs) {
                        let orderCarLenObjs = JSON.parse(JSON.stringify(line.carLenObjs)); // 复制一份
                        for (let j = 0; j < orderCarLenObjs.length; j++) {
                            let o = orderCarLenObjs[j];
                            o.checked = '1' // 默认选中所有询价项目
                            if (line.deliDate) {
                                o['reqDeliDate'] = line.deliDate + ' 00点';
                            }
                            if (line.arriDate) {
                                o['reqArriDate'] = line.arriDate + ' 00点';
                            }
                            if (o.carLen) {
                                o.carLenId = o.carLen;
                                o.carLenDisable = true;
                            }
                            if (o.carType) {
                                o.carTypeId = o.carType;
                                o.carTypeDisable = true;
                            }
                        }
                        this.$set(line.params, 'orderCarLenObjs', orderCarLenObjs);
                    }
                }

                this.$nextTick(() => {
                    $('[ext-param=' + i + ']').find('.selectpicker').selectpicker({container: 'body'})
                            .on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
                                console.log(this)
                                let objIdx = $(this).closest('[obj-index]').attr("obj-index");
                                let obj = vue.lines[i].params.orderCarLenObjs[objIdx];
                                vue.$set(obj, $(this).attr('name'), $(this).val());
                            })

                    layui.use('laydate', function () {
                        var laydate = layui.laydate;
                        $('[ext-param=' + i + ']').find('[req-date]').each(function () {
                            laydate.render({
                                elem: this,
                                format: 'yyyy-MM-dd HH点',
                                trigger: 'click',
                                type: 'datetime',
                                //min: -1,
                                fullPanel: true,
                                ready: function (date) {
                                    // 展示结束时调用
                                    $('#layui-laydate'+this.index).css('width', '385px')
                                        .find('.layui-laydate-list').css('width', '107px')
                                        .find('>li').each(function(x){
                                            if (x == 0) {
                                                $(this).css('width', '100%')
                                            } else {
                                                $(this).remove()
                                            }
                                        });
                                    $('#layui-laydate'+this.index)
                                        .find('.layui-laydate-main').css('width', '385px')
                                },
                                change: function(value, date) {//面板点击时触发
                                    $('#layui-laydate'+this.index)
                                        .find('.layui-laydate-list').css('width', '107px')
                                        .find('>li').each(function(x){
                                        if (x == 0) {
                                            $(this).css('width', '100%')
                                        } else {
                                            $(this).remove()
                                        }
                                    });
                                },
                                done: function (value, date, endDate) {
                                    let objIdx = $(this.elem).closest('[obj-index]').attr('obj-index')
                                    let obj = vue.lines[i].params.orderCarLenObjs[objIdx]
                                    let field = $(this.elem).attr('req-date');
                                    vue.$set(obj, field, value);
                                }
                            });
                            //var styleElement = $("<style>");
                            // 在<style>元素中添加你的样式
                            //styleElement.text(".layui-laydate {width: 385px !important;} .layui-laydate-header{width: 72%;} .layui-laydate-content > ul {width: 20% !important;} .laydate-time-list > li {width: 100% !important;} .laydate-time-list > li:nth-last-child(2), .laydate-time-list > li:last-child {display: none;}");
                            // 将<style>元素添加到指定的元素中
                            //$('#layui-laydate' + index).append(styleElement);
                        })
                    })
                })

            },
            modeChange() {
                if (this.mode == 'multi') {
                    $('.layui-layer-btn0').text('生成发货单');
                    var rendered = false; // 路线区域的日期控件、下拉款控件是否已渲染
                    if (this.lineIds.length == 0) {
                        if (this.lines.length == 1) {
                            this.lineIds.push(this.lines[0].id)
                            this.lineSelected(0);
                            rendered = true;
                        }
                    }
                    this.$nextTick(() => {
                        $('[def-param]').find('.selectpicker').selectpicker({container: 'body'});
                        layui.use('laydate', function () {
                            var laydate = layui.laydate;
                            // 默认参数日期部分处理
                            let d = laydate.render({
                                elem: $('[def-param]').find('.laydate'), //指定元素
                                format: 'yyyy-MM-dd HH点',
                                trigger: 'click',
                                type: 'datetime',
                                fullPanel: true,
                                ready: function (date) {
                                    $('#layui-laydate'+this.index).css('width', '385px')
                                        .find('.layui-laydate-list').css('width', '107px')
                                        .find('>li').each(function(x){
                                        if (x == 0) {
                                            $(this).css('width', '100%')
                                        } else {
                                            $(this).remove()
                                        }
                                    });
                                    $('#layui-laydate'+this.index)
                                        .find('.layui-laydate-main').css('width', '385px')
                                },
                                change: function(value, date) {//面板点击时触发
                                    $('#layui-laydate'+this.index)
                                        .find('.layui-laydate-list').css('width', '107px')
                                        .find('>li').each(function(x){
                                        if (x == 0) {
                                            $(this).css('width', '100%')
                                        } else {
                                            $(this).remove()
                                        }
                                    });
                                },
                                done: function (value, date, endDate) {

                                }
                            });
                        })
                        if (rendered) {
                            return;
                        }
                        $('[ext-param]').each(function () {
                            var $row = $(this);
                            let idx = $row.attr('ext-param');
                            $row.find('.selectpicker').selectpicker({container: 'body'})
                                .on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
                                    let objIdx = $(this).closest('[obj-index]').attr('obj-index')
                                    let obj = vue.lines[idx].params.orderCarLenObjs[objIdx]
                                    vue.$set(obj, $(this).attr('name'), $(this).val());
                                });
                            layui.use('laydate', function () {
                                var laydate = layui.laydate;
                                $row.find('[req-date]').each(function () {
                                    let d = laydate.render({
                                        elem: this, //指定元素
                                        format: 'yyyy-MM-dd HH点',
                                        trigger: 'click',
                                        type: 'datetime',
                                        //min: -1,
                                        fullPanel: true,
                                        ready: function (date) {
                                            // 展示结束时调用
                                            $('#layui-laydate'+this.index).css('width', '385px')
                                                .find('.layui-laydate-list').css('width', '107px')
                                                .find('>li').each(function(x){
                                                if (x == 0) {
                                                    $(this).css('width', '100%')
                                                } else {
                                                    $(this).remove()
                                                }
                                            });
                                            $('#layui-laydate'+this.index)
                                                .find('.layui-laydate-main').css('width', '385px')
                                        },
                                        change: function(value, date) {//面板点击时触发
                                            $('#layui-laydate'+this.index)
                                                .find('.layui-laydate-list').css('width', '107px')
                                                .find('>li').each(function(x){
                                                if (x == 0) {
                                                    $(this).css('width', '100%')
                                                } else {
                                                    $(this).remove()
                                                }
                                            });
                                        },
                                        done: function (value, date, endDate) {
                                            let objIdx = $(this.elem).closest('[obj-index]').attr('obj-index')
                                            let obj = vue.lines[idx].params.orderCarLenObjs[objIdx]
                                            let field = $(this.elem).attr('req-date');
                                            vue.$set(obj, field, value);
                                        }
                                    });
                                })

                            })
                        })

                    })

                } else {
                    $('.layui-layer-btn0').text('下一步');
                }
            },
            checkChange(lineIdx,objIdx) {
                this.$nextTick(function(){
                    $('[ext-param='+lineIdx+']').find('[obj-index='+objIdx+']').find(".selectpicker").selectpicker('refresh');
                })
            },
            getLongLat(addr, lineIdx, addrIdx) {
                console.log(addr)
                let err = [];
                if (!addr.addrName || !addr.addrName.trim()) {
                    err.push("录入地址名称");
                }
                if (!addr.areaId) {
                    err.push("选择区县");
                }
                let detailAddr = addr.detailAddr;
                if (!detailAddr || !detailAddr.trim()) {
                    err.push("补全详细地址");
                }
                if (err.length > 0) {
                    $.modal.msgWarning("请先" + err.join("、"));
                    return
                }
                let address = [];
                address.push(addr.provinceName)
                address.push(addr.cityName)
                if (addr.addressId) {
                    address.push(addr.areaName)
                } else {
                    addr.areaName = $('#addrarea' + lineIdx + '_' + addrIdx).find('option:selected').text();
                    address.push(addr.areaName)
                }

                var fetchAddr = function (gen, callback) {
                    $.ajax({
                        url: ctx + "rfq/fetch-addr?gen=" + gen,
                        type: 'post',
                        contentType: 'application/json',
                        data: JSON.stringify(addr),
                        success: function (result) {
                            if (result.code == 0) {
                                vue.$set(addr, 'addressId', result.data.addressId);
                                vue.$set(addr, 'longitudedegree', result.data.longitudedegree);
                                vue.$set(addr, 'latitudedegree', result.data.latitudedegree);
                                vue.$set(addr, 'custAddressId', result.data.custAddressId);
                                if (callback) {
                                    callback();
                                }
                            } else {
                                $.modal.msgError(result.msg);
                            }
                        }
                    })
                }

                var addressIdEmpty = addr.addressId == null; // 原先addressId是否为null

                let _locate = function () {
                    address.push(detailAddr.trim())
                    let lnglat = '';
                    if (addr.longitudedegree && addr.latitudedegree) {
                        lnglat = addr.longitudedegree + ',' + addr.latitudedegree;
                    }
                    var url = ctx + "basic/address/lnglat?address=" + encodeURIComponent(address.join('')) +
                        "&areaId=" + addr.areaId +
                        "&detailAddr=" + encodeURIComponent(detailAddr.trim()) +
                        "&index=0" +
                        "&lnglat=" + encodeURIComponent(lnglat) +
                        "&isCa=0";
                    $.modal.openCallAfterSubmit('经纬度标记', url, "800", "600", function (res, _lnglat) {
                        if (res.code == 0) {
                            if (addr.addressId) {
                                // 回写经纬度，图标换色
                                var ll = _lnglat.split(",");
                                vue.$set(addr, 'longitudedegree', ll[0]);
                                vue.$set(addr, 'latitudedegree', ll[1]);
                            } else {
                                // 定位成功后，创建地址库
                                fetchAddr(true);
                            }
                        }
                    });
                }

                // 重新匹配地址库(但不创建地址库)，防止前端修改了地址
                if (addressIdEmpty) {
                    var tt = $.modal.layerLoading("匹配地址库中，请稍候...");
                    fetchAddr(false, function () {
                        layer.close(tt);
                        if (addr.longitudedegree) {
                            $.modal.confirm('已匹配地址库地址，是否继续定位？', _locate);
                        } else {
                            _locate();
                        }
                    })
                } else {
                    _locate();
                }
            },
            getLabel(arr, value) {
                const item = arr.find(item => item.dictValue === value);
                return item ? item.dictLabel : null;
            },
            disp(obj) {
                let tmp = [];
                tmp.push(this.getLabel(this.transCodeDict, obj.transCode));
                if (obj.billingMethod != null) {
                    tmp.push(this.billingMethod[obj.billingMethod])
                }
                if (obj.transCode == '0' || obj.transCode == '4' || obj.transCode == '15') {//整车
                    tmp.push('车长' + obj.carLenName);
                    obj.carTypeName && tmp.push(obj.carTypeName)
                    obj.isOversize && tmp.push('大件');
                } else {

                }
                obj.length && tmp.push('长' + obj.length);
                obj.width && tmp.push('宽' + obj.width);
                obj.height && tmp.push('高' + obj.height);
                obj.num && tmp.push(obj.num + '件');
                obj.weight && tmp.push(obj.weight + '吨');
                obj.volume && tmp.push(obj.volume + '方');
                return tmp.join('/')
            },
            /*showCarLen(carLenObj) {
                let transCode = carLenObj.transCode;
                return transCode != '0' && transCode != '4' && transCode != '15'; // 非整车时展示车长
            },*/
            fetchToAll() {
                let that = this;
                let override = $('[flag=override]').prop('checked');
                $('[def-param]').find('[name]').each(function () {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    for (var i = 0; value && i < that.lines.length; i++) {
                        if (that.lineIds.indexOf(that.lines[i].id) >= 0) { // 是否勾选了路线
                            let carlenObjs = that.lines[i].params.orderCarLenObjs;
                            for (let j = 0; j < carlenObjs.length; j++) {
                                if (carlenObjs[j].checked == '1') { // 是否勾选了项目
                                    if (override || !carlenObjs[j][name]) {
                                        if (name == 'carLenId' && carlenObjs[j].carLenDisable) {
                                            continue;
                                        } else if (name == 'carTypeId' && carlenObjs[j].carTypeDisable) {
                                            continue;
                                        }
                                        that.$set(carlenObjs[j], name, value);
                                    }
                                }
                            }
                        }
                    }
                })
                this.$nextTick(function () {
                    $('[ext-param]').find('.selectpicker').selectpicker('refresh');
                })
            }
        }
    })

    function addInvoice(id) {
        vue.reset();
        // 校验生成发货单条件
        $.ajax({
            url: ctx + 'rfq/orderable-lines/' + id,
            cache: false,
            success: function(result) {
                if (result.code == 0) {
                    for (let i = 0; i < result.data.length; i++) {
                        let line = result.data[i];
                        let addrs = line.deliList.concat(line.arriList)
                        for (let j = 0; j < addrs.length; j++) {
                            if (addrs[j].areaId == null) {
                                addrs[j]['areaId'] = '';
                            }
                            if (addrs[j].detailAddr == null || addrs[j].detailAddr.trim() == '') {
                                addrs[j]['detailAddr'] = '';
                            }
                        }
                    }
                    vue.lines = result.data;
                    if (vue.lines.length == 1) {
                        vue.lineSelected1(0);// 只有一条路线时，默认选中此路线
                    }
                    vue.$nextTick(function(){
                        for (let i = 0; i < vue.lines.length; i++) {
                            let addrs = vue.lines[i].deliList.concat(vue.lines[i].arriList)
                            for (let j = 0; j < addrs.length; j++) {
                                if (addrs[j].addressId == null) {
                                    $.provinces.init('addrprov' + i + '_' + j, 'addrcity' + i + '_' + j, 'addrarea' + i + '_' + j, addrs[j].provinceId, addrs[j].cityId, addrs[j].areaId);
                                }
                            }
                        }
                    })
                    layer.open({
                        type: 1,
                        area: ['990px', '600px'],
                        fix: false,
                        skin: 'none',
                        //不固定
                        maxmin: true,
                        shade: 0.3,
                        zIndex: 3,
                        title: '选择创建发货单的路线',
                        content: $('#layer1'),
                        offset: 100,
                        btn: ['下一步', '取消'],
                        shadeClose: false,
                        success: function (layero) {

                        },
                        btn1: function (index, layero) {
                            if ((vue.mode == 'single' && vue.lineId == null) || (vue.mode == 'multi' && vue.lineIds.length == 0)) {
                                $.modal.msgWarning("请选择路线")
                                return;
                            }
                            let pass = $('#chooseForm').validate({
                                errorPlacement: function(error,element){
                                    if (element.parent().hasClass("input-group")){
                                        element.parent().after(error);
                                    } else if (element.parent().hasClass("bootstrap-select")) {
                                        element.parent().after(error);
                                    } else {
                                        element.after(error);
                                    }
                                }
                            }).form()
                            if (!pass) return;

                            if (vue.mode == 'single') {
                                for (let i = 0; i < vue.lines.length; i++) {
                                    if (vue.lines[i].id == vue.lineId) {
                                        let addrs = vue.lines[i].deliList.concat(vue.lines[i].arriList);
                                        let needFetch = false; // 是否需要补全地址
                                        for (let j = 0; j < addrs.length; j++) {
                                            if (!addrs[j].custAddressId) {
                                                needFetch = true;
                                                break;
                                            }
                                        }
                                        let cartypeId = vue.lines[i].params.carLenObjId;
                                        const initInvoice = function() {
                                            if (vue.inInvoice) {//发货单页面时切换
                                                location.replace(ctx + "invoice/convert/" + id + "/" + vue.lineId + "/" + cartypeId);
                                            } else {
                                                $.modal.openTab("创建发货单", ctx + "invoice/convert/" + id + "/" + vue.lineId + "/" + cartypeId);
                                                layer.close(index)
                                                vue.lines = []
                                            }
                                        }
                                        if (needFetch) {
                                            $.ajax({
                                                url: ctx + "rfq/fetch-line-address",
                                                type: 'post',
                                                contentType: 'application/json',
                                                data: JSON.stringify(vue.lines[i]),
                                                success: function (result) {
                                                    if (result.code == 0) {
                                                        initInvoice();
                                                    } else {
                                                        $.modal.msgError(result.msg);
                                                    }
                                                }
                                            })
                                        } else {
                                            initInvoice()
                                        }
                                        break;
                                    }
                                }
                            } else if (vue.mode == 'multi') {
                                let lines = [];
                                let invoiceCount = 0;
                                for (let i = 0; i < vue.lines.length; i++) {
                                    if (vue.lineIds.indexOf(vue.lines[i].id) >= 0) {
                                        lines.push(vue.lines[i]);
                                        let _addrs = vue.lines[i].deliList.concat(vue.lines[i].arriList);
                                        for (let j = 0; j < _addrs.length; j++) {
                                            if (!_addrs[j].longitudedegree) {
                                                $.modal.msgError("地址未经确认，请先点击红色坐标点，确认收发货地址。");
                                                return;
                                            }
                                        }
                                        for (var j = 0;j<vue.lines[i].params.orderCarLenObjs.length; j++) {
                                            if (vue.lines[i].params.orderCarLenObjs[j].checked == '1') {
                                                invoiceCount++;
                                            }
                                        }
                                    }
                                }
                                $.modal.confirm('确认生成选中的'+invoiceCount+'条发货单吗？', function(){
                                    $.ajax({
                                        url: ctx + "rfq/direct-rfq-invoice",
                                        type: 'post',
                                        contentType: 'application/json',
                                        data: JSON.stringify(lines),
                                        success: function(res) {
                                            if (res.code == 0) {
                                                layer.close(index);
                                                $.modal.msgSuccess(res.msg)
                                                $.table.refresh()
                                            } else {
                                                $.modal.msgError(res.msg);
                                            }
                                        }
                                    })
                                })
                            }
                        }
                    })
                } else {
                    $.modal.msgError(result.msg)
                }
            }
        })
    }
</script>
