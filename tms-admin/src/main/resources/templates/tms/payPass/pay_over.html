<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反确认')"/>
</head>

<body>
<div class="form-content">
    <form id="form-invoice-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="ids" name="ids" type="hidden" th:value="${ids}">
            <div class="fixed-table-body" style="margin: 0px -5px;">
                <table border="0" id="infoTab" class="custom-tab table" >

                    <thead>
                    <tr>
                        <th >申请金额</th>
                        <th style="width: 15%;">收款人</th>
                        <th style="width: 20%;">收款账号</th>
                        <th style="width: 20%;">收款银行</th>
                        <th style="width: 20%;">油卡卡号</th>
                    </tr>

                    </thead>
                    <tbody>

                    <tr th:each="payPassCheck1 : ${payPassCheckDetail}">
                        <td>
                            [[${payPassCheck1.applyAmount}]]
                        </td>
                        <td>
                            [[${payPassCheck1.bankAccount}]]
                        </td>
                        <td>
                            [[${payPassCheck1.recCardNo}]]
                        </td>
                        <td>
                            [[${payPassCheck1.recBank}]]
                        </td>
                        <td>
                            [[${payPassCheck1.oilCardNumber}]]
                        </td>

                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="panel-body">
                <!--基础信息 begin-->
                <input name="deptId" type="hidden" id="treeId">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">核销备注：</label>
                            <div class="col-sm-12">
                                <textarea name="payMemo" id="payMemo" class="form-control" type="text"
                                          maxlength="30" required="" aria-required="true"></textarea>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "payPass";

    /**
     * 提交
     */
    function submitHandler() {
        var data = $("#form-invoice-unconfirm").serializeArray();
        $.operate.save(prefix + "/payOver", data);
    }




</script>
</body>
</html>