<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-4">单号：</label>-->
                            <div class="col-sm-12">
                                <input name="payNo" id="payNo" class="form-control" placeholder="请输入单号"
                                       maxlength="30">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-3">
                    </div>
                    <div class="col-md-3 col-sm-3">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="check()" shiro:hasPermission="tms:payPassCheck:check">
                <i class="fa fa-file"></i> 审核
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "payPass";

    var transFeeCount = 0;
    var gotAmountCount = 0;
    var ungotAmountCount = 0;

    var collection_type = [[${@dict.getType('collection_type')}]];


    $(function () {
        var options = {
            url: prefix + "/checkList",
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 0,
            height: 570,
            uniqueId:"id",
            firstLoad: false,
            clickToSelect:true,
            showFooter:true,
            columns: [{
                checkbox: true
            }, {
                title: '代付类型',
                field: 'accountType',
                align: 'left',
                formatter: function status(value, row, index) {
                    return $.table.selectDictLabel(collection_type, value-0);
                }
            },
                {
                    title: '单号',
                    field: 'payNo',
                    align: 'left'
                },
                {
                    title: '申请人',
                    field: 'applyUserName',
                    align: 'left'
                },
                {
                    title: '申请时间',
                    field: 'applyDate',
                    align: 'left'
                },
                {
                    title: '申请金额',
                    field: 'applyAmount',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
               /* {
                    title: '油卡卡号',
                    align: 'left',
                    field: 'oilCardNumber'
                },
                {
                    title: '收款账户',
                    align: 'left',
                    field: 'bankAccount'
                },
                {
                    title: '收款卡号',
                    align: 'left',
                    field: 'recCardNo'
                },
                {
                    title: '收款银行',
                    align: 'left',
                    field: 'recBank'
                }*/
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });


        searchPre();
    });

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }

 /*   function check(){


        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["status"] !== 0 ) {
            $.modal.alertWarning("请选择待申请的单据");
            return;
        }

        var url = prefix + "/applyPay?id="+bootstrapTable[0]["id"];
        $.modal.open("申请付款", url);
    }*/

    /**
     * 审核
     */
    function check() {

        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }

        var id = $.table.selectColumns('id').join();
        layer.open({
            type: 2,
            area: ['800px', '600px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "代付费用审核",
            content: prefix + "/payPassCheck?id="+id,
            btn: ['通过', '不通过','关闭'],
            shadeClose: true,            // 弹层外区域关闭
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitApprove(index, layero);
            },
            btn2: function(index, layero){
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitBack(index, layero);
                return false;
            },
            btn3: function (index, layero) {
                return true;
            }
        });
    }

    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();

        //应付状态为已申请和部分核销
        searchPre();
    }

</script>
</body>
</html>