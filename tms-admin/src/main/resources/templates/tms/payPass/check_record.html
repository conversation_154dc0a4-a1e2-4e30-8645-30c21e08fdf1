<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('付款申请-付款记录')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped" >
            <input name="payId" id="payId" type="hidden" th:value="${payId}">

            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">
    var payId = [[${payId}]];

    var prefix = ctx + "payPass";
    //付款记录id
    var url = prefix + "/checkRecordList?payId="+payId;

    $(function () {
        var options = {
            url: url,
            showToggle:false,
            showColumns:false,
            showSearch: false,
            showRefresh:false,
            modalName: "收款记录",
            fixedColumns: true,
            clickToSelect:true,
            columns: [
                {
                    title: '单号',
                    field: 'payNo',
                    align: 'left'
                },
                {
                    title: '申请人',
                    field: 'applyUserName',
                    align: 'left'
                },
                {
                    title: '申请时间',
                    field: 'applyDate',
                    align: 'left'
                }, {
                    title: '申请金额',
                    field: 'applyAmount',
                    align: 'left'
                },{
                    title: '单据状态',
                    field: 'status',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '待审核';
                            case 1:
                                return '审核通过';
                            case 2:
                                return '审核不通过';
                            case 3:
                                return '已核销';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '审核人',
                    field: 'checkMan',
                    align: 'left'
                },
                {
                    title: '审核时间',
                    field: 'checkDate',
                    align: 'left'
                },
                 {
                    title: '审核备注',
                    field: 'checkMemo',
                    align: 'left'
                }, {
                    title: '付款人',
                    field: 'payMan',
                    align: 'left'
                }, {
                    title: '付款时间',
                    field: 'payDate',
                    align: 'left'
                },
                {
                    title: '付款备注',
                    field: 'payMemo',
                    align: 'left'
                }



            ]
        };

        $.table.init(options);
    });

</script>

</body>
</html>