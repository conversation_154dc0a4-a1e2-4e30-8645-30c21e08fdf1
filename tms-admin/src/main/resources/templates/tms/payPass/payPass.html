<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-4">单号：</label>-->
                            <div class="col-sm-12">
                                <input name="payNo" id="payNo" class="form-control" placeholder="请输入单号"
                                       maxlength="30">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-3">
                    </div>
                    <div class="col-md-3 col-sm-3">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="apply()" shiro:hasPermission="tms:payPass:apply">
                <i class="fa fa-file"></i> 申请付款
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "payPass";

    var transFeeCount = 0;
    var gotAmountCount = 0;
    var ungotAmountCount = 0;

    var collection_type = [[${@dict.getType('collection_type')}]];


    $(function () {
        var options = {
            url: prefix + "/list",
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 0,
            height: 570,
            uniqueId:"id",
            firstLoad: false,
            clickToSelect:true,
            showFooter:true,
            columns: [{
                checkbox: true
            }, {
                title: '操作',
                align: 'left',
                field: 'payCheckSheetId',
                formatter: function(value, row, index) {
                    var actions = [];
                    actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="审核记录" onclick="checkHistory(\''+row.payId+'\')"><i  class="fa fa fa-list" style="font-size: 15px;" ></i></a>');
                    return actions.join('');
                }

            },
                {
                    title: '单号',
                    field: 'payNo',
                    align: 'left'
                },
                {
                    title: '类型',
                    field: 'type',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '单笔';
                            case 1:
                                return '月结';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '代付类型',
                    field: 'accountType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(collection_type, value-0);
                    }
                },
                {
                    title: '应付单状态',
                    field: 'status',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '<span class="label label-primary">待申请</label>';
                            case 1:
                                return '<span class="label label-info">待审核</label>';
                            case 2:
                                return '<span class="label label-warning">待付款</label>';
                            case 3:
                                return '<span class="label label-success">已付款</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '代付金额',
                    field: 'transFeeCount',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '创建人',
                    align: 'left',
                    field: 'regUserName'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate'
                }
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });


        searchPre();
    });

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }

    function apply(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length != 1) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }

        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        if (bootstrapTable[0]["status"] !== 0 ) {
            $.modal.alertWarning("请选择待申请的单据");
            return;
        }

        var url = prefix + "/applyPay?id="+bootstrapTable[0]["id"];
        $.modal.open("申请付款", url);
    }

    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();

        //应付状态为已申请和部分核销
        searchPre();
    }

    function checkHistory(payId){
        var url = prefix + "/checkHistory?payId="+payId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

</script>
</body>
</html>