<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('申请付款')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: jasny-bootstrap-css" />
</head>

<body>
<div class="form-content">
    <form id="form-check" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <input name="id" th:value="${payPassCheck.id}" type="hidden">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">
                                        单号：</label>
                                    <div class="col-sm-8">
                                        [[${payPassCheck.payNo}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">
                                        总金额：</label>
                                    <div class="col-sm-8">
                                        [[${payPassCheck.applyAmount}]]
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >要求支付日期：</label>
                                    <div class="col-sm-8">
                                        [[${#dates.format(payPassCheck.applyDate, 'yyyy-MM-dd HH:mm:ss')}]]
                                    </div>
                                </div>
                            </div>

                            <!--<div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 color">
                                        油卡卡号：</label>
                                    <div class="col-sm-8">
                                        [[${payPassCheck.oilCardNumber}]]
                                    </div>
                                </div>
                            </div>-->
                        </div>
                     <!--   <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">
                                        收款人：</label>
                                    <div class="col-sm-8">
                                        [[${payPassCheck.applyUserName}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">
                                        收款卡号：</label>
                                    <div class="col-sm-8">
                                        [[${payPassCheck.recCardNo}]]
                                    </div>
                                </div>
                            </div>
                        </div>-->
                        <!--<div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">
                                        收款银行：</label>
                                    <div class="col-sm-8">
                                        [[${payPassCheck.recBank}]]
                                    </div>
                                </div>
                            </div>
                        </div>-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table" >

                                <thead>
                                <tr>
                                    <th >申请金额</th>
                                    <th style="width: 15%;">收款人</th>
                                    <th style="width: 20%;">收款账号</th>
                                    <th style="width: 20%;">收款银行</th>
                                    <th style="width: 20%;">油卡卡号</th>
                                </tr>

                                </thead>
                                <tbody>

                                <tr th:each="payPassCheck1 : ${payPassCheckDetail}">
                                    <td>
                                        [[${payPassCheck1.applyAmount}]]
                                    </td>
                                    <td>
                                        [[${payPassCheck1.bankAccount}]]
                                    </td>
                                    <td>
                                        [[${payPassCheck1.recCardNo}]]
                                    </td>
                                    <td>
                                        [[${payPassCheck1.recBank}]]
                                    </td>
                                    <td>
                                        [[${payPassCheck1.oilCardNumber}]]
                                    </td>

                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">申请备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                        [[${payPassCheck.applyMemo}]]
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">支付凭证：</label>
                                    <div class="col-sm-8">
                                        <div class="form-group">
                                            <div class="col-sm-7" th:each="pic:${sysUploadFiles}">
                                                <img style="height:80px" modal="zoomImg" th:src="@{${pic.filePath}}"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">退回备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea id="checkMemo" name="checkMemo" maxlength="250"
                                                      class="form-control valid" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "payPass";
    var url = ctx + "payPass/saveCheck";
    $(function () {
        $('#collapseOne').collapse('show');

    });

    /**
     * 审核通过
     */
    function submitApprove() {
        var data = $("#form-check").serializeArray();
        data.push({"name": "checkStatus", "value": 1});
        $.operate.save(url, data);
    }
    /**
     * 审核不通过
     */
    function submitBack() {
        var data = $("#form-check").serializeArray();
        data.push({"name": "checkStatus", "value": 2});

        if ($("#checkMemo").val() === '') {
            $.modal.msgWarning("请填写退回备注！");
            return false;
        }

        $.operate.save(url, data);
    }


</script>
</body>

</html>