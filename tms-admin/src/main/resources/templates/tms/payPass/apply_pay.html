<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('申请付款')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: jasny-bootstrap-css" />
</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 70px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;
    }
    .fcff{
        color: #ff1f1f;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 10px;
    }

    .file-drop-zone-title{
        font-size: 13px;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .file-input-ajax-new .file-drop-zone-title{
        /*height: 80px;*/
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .uploads .table-bordered td{
        border: 0 !important;;
    }
</style>
<body>
<div class="form-content">
    <form id="form-payDetail-add" class="form-horizontal" novalidate="novalidate">
        <!--应付明细id-->
        <input name = "payPassId" th:value="${payPassId}" type="hidden">
        <input name = "applyAmount" th:value="${totalAmount}" type="hidden">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">

                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left" style="width: 120px"><span class="fcff">*</span> 要求支付日期：</label>
                                    <div class="flex_right">
                                        <input type="text" class=" form-control dis"
                                               name="reqPayDate" id="reqPayDate" autocomplete="off" required>
                                    </div>
                                </div>
                            </div>
                            <!--                            <div class="col-md-3 col-sm-6">-->
                            <!--                                <div class="form-group">-->
                            <!--                                    <label class="col-sm-4 color">-->
                            <!--                                        申请金额：</label>-->
                            <!--                                    <div class="col-sm-8">-->
                            <!--                                        <input type="number" class="form-control"  maxlength="25"-->
                            <!--                                               autocomplete="off" id="applyAmount" name="applyAmount" required th:value="${totalAmount}">-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->

                        </div>
                        <!--                        <div class="row">-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-4">-->
                        <!--                                        收款人：</label>-->
                        <!--                                    <div class="col-sm-8">-->
                        <!--                                        <input name="bankAccount" id="bankAccount" type="hidden">-->
                        <!--                                        <select name="carrBankId" id="carrBankId" class="form-control valid" onchange="bankChange()">-->
                        <!--                                            <option value="">&#45;&#45;请选择&#45;&#45;</option>-->
                        <!--                                            <option th:each="dict : ${carrBankList}" th:text="${dict.bankAccount}"-->
                        <!--                                                    th:value="${dict.carrBankId}" ></option>-->
                        <!--                                        </select>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-4">-->
                        <!--                                        收款卡号：</label>-->
                        <!--                                    <div class="col-sm-8">-->
                        <!--                                        <input id="recCardNo" name="recCardNo" class="form-control dis" type="text"  disabled>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                        </div>-->
                        <!--                        <div class="row">-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-4">-->
                        <!--                                        收款银行：</label>-->
                        <!--                                    <div class="col-sm-8">-->
                        <!--                                        <input id="recBank" name="recBank" class="form-control dis" type="text"  disabled>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                            <div class="col-md-3 col-sm-6">-->
                        <!--                                <div class="form-group">-->
                        <!--                                    <label class="col-sm-4 color">-->
                        <!--                                        油卡卡号：</label>-->
                        <!--                                    <div class="col-sm-8">-->
                        <!--                                        <input type="text" class="form-control dis"  maxlength="25"-->
                        <!--                                               autocomplete="off" id="oilCardNumber" name="oilCardNumber">-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                        </div>-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table" >

                                <thead>
                                <tr>
                                    <th style="width: 5%;"><a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow()" title="新增行">+</a></th>
                                    <th >申请金额</th>
                                    <th style="width: 15%;">收款人</th>
                                    <th style="width: 20%;">收款账号</th>
                                    <th style="width: 20%;">收款银行</th>
                                    <th style="width: 20%;">油卡卡号</th>
                                </tr>

                                </thead>
                                <tbody>

                                <tr>
                                    <td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" title="删除选择行" onclick="removeRow(this,0)"></a></td>
                                    <td>
                                        <input type="number" class="form-control"  maxlength="25"
                                               autocomplete="off" id="applyAmount_0" name="accountList[0].applyAmount" required th:value="${totalAmount}">
                                    </td>
                                    <td>
                                        <input name="accountList[0].bankAccount" id="bankAccount_0" type="hidden">
                                        <select name="accountList[0].carrBankId" id="carrBankId_0" class="form-control valid" onchange="bankChange(this,0)">
                                            <option value="">--请选择--</option>
                                            <option th:each="dict : ${carrBankList}" th:text="${dict.bankAccount}"
                                                    th:value="${dict.carrBankId}" ></option>
                                        </select>
                                    </td>
                                    <td>
                                        <input id="recCardNo_0" name="accountList[0].recCardNo" class="form-control dis" type="text"  disabled>
                                    </td>
                                    <td>
                                        <input id="recBank_0" name="accountList[0].recBank" class="form-control dis" type="text"  disabled>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control dis"  maxlength="25"
                                               autocomplete="off" id="oilCardNumber_0" name="accountList[0].oilCardNumber">
                                    </td>

                                </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="row mt20">
                            <div class="col-sm-12">
                                <div class="flex">
                                    <label class="flex_left">申请备注：</label>
                                    <div class="flex_right">
                                            <textarea name="applyMemo" maxlength="250"
                                                      class="form-control valid" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt10">
                            <div class="col-md-4 col-sm-8">
                                <div class="flex">
                                    <label class="flex_left">支付凭证：</label>
                                    <div class="flex_right">
                                        <input id="image" class="form-control" name="image" type="file" multiple>
                                        <input id="tid" name="tid" type="hidden">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>
</form>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "payPass";

    var carrBankList = [[${carrBankList}]];

    $(function () {
        $('#collapseOne').collapse('show');


        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqPayDate',
                type: 'date',
                trigger: 'click',
                done: function(value, date, endDate){
                    $("#reqPayDate").val(value);
                    //单独校验日期
                    $("#form-payDetail-add").validate().element($("#reqPayDate"));
                }
            });
        });

        $("#form-payDetail-add").validate({
            onkeyup: false,
            focusCleanup: true
        });

        var picParam = {
            maxFileCount:0,
            publish: "uploadSuccess",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        var tid = "tid";
        var imageId = "image";
        $.file.initAddFiles(imageId, tid, picParam);

        // 图片上传成功后
        $("#image").on('filebatchuploadsuccess', function (event, data) {
            var tid = data.response.tid;
            $("#tid").val(tid);
            //表单提交
            $.operate.save(prefix + "/saveApplyCheck", $('#form-payDetail-add').serialize());
        });

        //油卡添加校验
        // $("#oilCardNumber").rules("add", {
        //     remote: {
        //         url: ctx + "basic/fuelCard/checkEditCardNumber",
        //         type: "post",
        //         async: false,
        //         dataType: "json",
        //         data: {
        //             fuelcardNo : function() {
        //                 return $.common.trim($("#oilCardNumber" ).val());
        //             },
        //             payDetailId : function() {
        //                 return $.common.trim($("#payDetailId" ).val());
        //             },
        //             carrierId : function() {
        //                 return $.common.trim($("#carrierId" ).val());
        //             }
        //         },
        //         dataFilter: function(data, type) {
        //             flag = data;
        //             return $.validate.unique(data);
        //         }
        //     },
        //     messages: {
        //         remote: "无效油卡"
        //     }
        // });

    });

    /**
     * 费用类型修改
     */
    function typeChange() {
        if (costTypeFreight === '1' || costTypeFreight === '3' || costTypeFreight === '5'){
            $("#oilCardNumber").attr("required","required");
            $(".color").attr("style","color: red");
            $("#oilCardNumber").removeAttr("disabled");
        }else{
            $("#oilCardNumber").removeAttr("required");
            $(".color").removeAttr("style ");
            $("#oilCardNumber").attr("disabled","disabled");
        }
    }

    //收款人更换
    function bankChange(obj,index) {
        $("#recCardNo_"+index).val("");
        $("#recBank_"+index).val("");
        $("#bankAccount_"+index).val("");

        for(var i = 0 ; i < carrBankList.length ; i++){
            if(carrBankList[i].carrBankId == $("#carrBankId_"+index).val()){
                $("#recCardNo_"+index).val(carrBankList[i].bankCard);
                $("#recBank_"+index).val(carrBankList[i].bankName);
                $("#bankAccount_"+index).val(carrBankList[i].bankAccount);
            }
        }

    }



    var relatedIndex = 0;
    function insertRow() {

        var carrBankListHTML = '';
        for(var i = 0 ; i < carrBankList.length ; i++){
            carrBankListHTML += '<option  value=' + carrBankList[i].carrBankId + ' >' +
                carrBankList[i].bankAccount + '</option>'
        }

        relatedIndex += 1;
        var trTtml = ' <tr>' +
            '<td><a class="fa fa-times-circle" style="color: #fd8481;font-size: 20px;" title="删除选择行" onClick="removeRow(this,'+relatedIndex+')"></a></td>' +
            '<td>' +
            '<input type="number" class="form-control" maxLength="25" autoComplete="off" id="applyAmount_'+relatedIndex+'" name="accountList['+relatedIndex+'].applyAmount" required>' +
            '</td>' +
            '<td>' +
            '<input name="accountList['+relatedIndex+'].bankAccount" id="bankAccount_'+relatedIndex+'" type="hidden">' +
            '<select name="accountList['+relatedIndex+'].carrBankId" id="carrBankId_'+relatedIndex+'" class="form-control valid" onChange="bankChange(this,'+relatedIndex+')">' +
            '<option value="">--请选择--</option>' +
            carrBankListHTML+
            '</select>' +
            '</td>' +
            '<td>' +
            '<input id="recCardNo_'+relatedIndex+'" name="accountList['+relatedIndex+'].recCardNo" class="form-control dis" type="text" disabled>' +
            '</td>' +
            '<td>' +
            '<input id="recBank_'+relatedIndex+'" name="accountList['+relatedIndex+'].recBank" class="form-control dis" type="text" disabled>' +
            '</td>' +
            '<td>' +
            '<input id="oilCardNumber_'+relatedIndex+'" name="accountList['+relatedIndex+'].oilCardNumber" type="text" class="form-control dis" maxLength="25" autoComplete="off">' +
            '</td>' +
            '</tr>'
        $("#infoTab tbody").append(trTtml);
    }

    function removeRow(obj,index){
        if ($("#infoTab tbody").find('tr').length > 1) {
            $("#infoTab tbody").find(obj).closest("tr").remove();
        }
    }


    /**
     * 表单提交
     */
    function submitHandler(){
        if ($.validate.form()) {
            commit();
        }
    }

    function commit() {
        //解除disabled
        var dis = $(":disabled");
        dis.attr("disabled", false);

        if($("#image").val() == "" || $("#image").val() == null){
            $.operate.save(prefix + "/saveApplyCheck", $('#form-payDetail-add').serialize());
        }else{
            // 表单提交
            $.modal.loading("正在处理中，请稍后...");
            $("#image").fileinput('upload');
        }
    }
</script>
</body>

</html>