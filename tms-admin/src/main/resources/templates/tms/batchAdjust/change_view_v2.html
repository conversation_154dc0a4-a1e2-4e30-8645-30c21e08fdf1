<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('批量调账')"/>
    <link th:href="@{/element-ui@2.15.13/lib/theme-chalk/index.css}" rel="stylesheet"/>
    <style>
        .ellipsis {
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .x-block .el-tag+.el-tag {
            margin-left: 10px;
        }

        .flex{
            display: flex;
        }
        .flex_left{
            width: 80px;
            line-height: 26px;
            text-align: right;
            color: #808080;
        }
        .flex_right{
            min-width:0;
            flex:1;
            box-sizing: border-box;
        }
        [v-clock]{
            display: none;
        }
        .flex-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }
        .flex-row * {
            line-height: 1.8;
        }
        .primary {
            color: #409EFF;
            font-weight: bold;
        }
        .danger {
            color: #f56c6c;
        }
        .success {
            color: #67c23a;
        }
        .info {
            color: #909399;
        }
        .corner-badge {
            position: absolute;
            top: -7px;
            right: -7px;
            padding: 2px 5px;
            border-radius: 1px;
            background-color: rgb(237,133,91);
            color: #fff;
            line-height: 13px;
            font-size: 13px;
        }
        .n_up {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            border-bottom: 1px #ddd solid;
            margin: 0 5px;
            padding: 0 3px;
        }
        .n_down {
            border-bottom: 1px #ddd solid;
            line-height: 14px;
            padding: 3px 4px;
            margin: 0 6px;
            color: #aaa;
            text-align: center;
        }
        .eclipse{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
<div id="app" style="padding: 5px;padding-bottom: 37px;" v-clock>
    <div style="display: flex; justify-content: center;margin-bottom: 10px">
        <el-card>
            <div style="display: flex;justify-content: space-between;align-items: center;">
                <el-tag type="primary" size="small" effect="dark">应收</el-tag>
                <div>
                    <div class="n_up">{{batchAdjust.netInOrigin}}</div>
                    <div class="n_down" v-if="batchAdjust.netInChange!=0">原金额(元)</div>
                    <div class="n_down" v-else>未调整(元)</div>
                </div>
                <div v-if="batchAdjust.netInChange!=0">{{batchAdjust.netInChange>0?'+':''}}{{batchAdjust.netInChange}}</div>
                <div v-if="batchAdjust.netInChange!=0">
                    <div class="n_up">{{batchAdjust.netInAfter}}</div>
                    <div class="n_down">调整后(元)</div>
                </div>
            </div>
        </el-card>
        <el-card style="margin-left: 15px" v-if="revenueP">
            <div style="display: flex;justify-content: space-between;align-items: center;">
                <el-tag type="info" size="small" effect="dark">成本</el-tag>
                <div style="text-align: center">
                    <div class="n_up">{{batchAdjust.netOutOrigin}}</div>
                    <div class="n_down" v-if="batchAdjust.netOutChange!=0">原金额(元)</div>
                    <div class="n_down" v-else>未调整(元)</div>
                </div>
                <div v-if="batchAdjust.netOutChange!=0">{{batchAdjust.netOutChange>0?'+':''}}{{batchAdjust.netOutChange}}</div>
                <div v-if="batchAdjust.netOutChange!=0">
                    <div class="n_up">{{batchAdjust.netOutAfter}}</div>
                    <div class="n_down">调整后(元)</div>
                </div>
            </div>
        </el-card>
        <el-card style="margin-left: 15px" v-if="revenueP">
            <div style="display: flex;justify-content: space-between;align-items: center;">
                <el-tag type="info" size="small" effect="dark">成本税金</el-tag>
                <div style="text-align: center">
                    <div class="n_up">{{batchAdjust.taxOrigin}}</div>
                    <div class="n_down" v-if="batchAdjust.taxChange!=0">原金额(元)</div>
                    <div class="n_down" v-else>未调整(元)</div>
                </div>
                <div v-if="batchAdjust.taxChange!=0">{{batchAdjust.taxChange>0?'+':''}}{{batchAdjust.taxChange}}</div>
                <div v-if="batchAdjust.taxChange!=0">
                    <div class="n_up">{{batchAdjust.taxAfter}}</div>
                    <div class="n_down">调整后(元)</div>
                </div>
            </div>
        </el-card>
        <el-card style="margin-left: 15px" v-if="revenueP">
            <div style="display: flex;justify-content: space-between;align-items: center;">
                <el-tag type="info" size="small" style="border-color: #000;color:#000" effect="plain">利润</el-tag>
                <div style="text-align: center">
                    <div class="n_up">{{batchAdjust.netProfitsOrigin}}</div>
                    <div class="n_down" v-if="batchAdjust.netProfitsChange!=0">原金额(元)</div>
                    <div class="n_down" v-else>未调整(元)</div>
                </div>
                <div v-if="batchAdjust.netProfitsChange!=0">{{batchAdjust.netProfitsChange>0?'+':''}}{{batchAdjust.netProfitsChange}}</div>
                <div v-if="batchAdjust.netProfitsChange!=0">
                    <div class="n_up">{{batchAdjust.netProfitsAfter}}</div>
                    <div class="n_down">调整后(元)</div>
                </div>
            </div>
        </el-card>
    </div>
    <div v-for="(i, idx1) in invoiceList" style="margin-bottom: 10px;margin-right:5px;">
        <div style="background-color: #edfa2e;color:red;padding: 4px 6px;" v-if="i.expBx > 0 || i.expFbx > 0">
            <span v-if="i.expBx > 0">走保险异常数：{{i.expBx}}；</span>
            <span v-if="i.expFbx > 0">非保险异常数：{{i.expFbx}}；</span>
        </div>
        <div style="background-color: rgb(240,242,245);padding: 5px;position: relative;border:1px #ddd solid;">
            <div class="corner-badge">应收</div>
            <div class="flex-row">
                <div class="ellipsis" style="width:125px;font-weight: bold;"><el-tooltip placement="right" trigger="hover">
                    <div slot="content">票点：{{billingTypeLabel[i.billingType]}}<br>平台费率：{{i.platRate}}<br>平台费税点：{{i.platTax}}</div>
                    <a href="javascript:;">{{i.vbillno}}</a>
                </el-tooltip></div>
                <div class="ellipsis" :title="i.custAbbr" style="width:250px;font-weight: bold;">
                    {{i.custAbbr}}
                    {{i.deliCityName=='市辖区'?i.deliProName:i.deliCityName}}~{{i.arriCityName=='市辖区'?i.arriProName:i.arriCityName}}
                    <span class="success">({{i.reqDeliDate && i.reqDeliDate.substring(0, 10)}})</span>
                </div>
                <div style="flex: 1;display: flex;justify-content: space-between">
                    <div style="flex:1 0 100px" class="ellipsis" :title="billingMethod[i.billingMethod]">单价：{{i.unitPrice}}【{{billingMethod[i.billingMethod].replace('（', '(').replace('）', ')')}}】</div>
                    <div style="flex:1 0 110px" class="ellipsis" :title="i.freightAmount">应收运费：<span v-if="i.diffFreight==0" v-text="i.freightAmount"></span><span v-else>{{i.originFreight}}{{i.diffFreight>0?'+':''}}{{i.diffFreight}}={{i.freightAmount}}</span></div>
                    <div style="flex:1 0 110px" class="ellipsis" :title="i.onWayAmount">应收在途：<span v-if="i.diffOnway==0" v-text="i.onWayAmount"></span><span v-else>{{i.originOnway}}{{i.diffOnway>0?'+':''}}<el-tooltip placement="top" :content="[costTypeOnWay[i.onwayType],i.onwayMemo].join('；')"><span class="primary">{{i.diffOnway}}</span></el-tooltip>={{i.onWayAmount}}</span></div>
                    <div style="flex:1 0 110px" class="ellipsis" :title="i.afterNetPlatFee">平台费：<span v-if="i.diffNetPlatFee==0">{{i.afterNetPlatFee}}</span><span v-else>{{i.originNetPlatFee}}{{i.diffNetPlatFee>0?'+':''}}{{i.diffNetPlatFee}}={{i.afterNetPlatFee}}</span></div>
                    <div style="flex:1 0 110px" class="ellipsis" :title="i.otherFee">三方费用：<span v-if="i.diffOther==0" v-text="i.otherFee"></span><span v-else>{{i.originOther}}{{i.diffOther>0?'+':''}}<el-tooltip placement="top" :content="costTypeOnWay[i.otherType]"><span class="primary">{{i.diffOther}}</span></el-tooltip>={{i.otherFee}}</span></div>
                </div>
            </div>
            <div class="flex-row">
                <div style="width:125px;" class="ellipsis" :title="i.carTypeName"><img th:src="@{/img/cl.png}" style="width: 20px;"> {{i.carLenName?(i.carLenName+'米'):''}}{{i.carTypeName}}</div>
                <div class="ellipsis" style="width:250px" :title="nwv(i.numCount,i.weightCount,i.volumeCount)"><img th:src="@{/img/wp.png}" style="width: 20px;"> {{i.goodsName}} {{nwv(i.numCount,i.weightCount,i.volumeCount)}}</div>
                <div style="flex: 1;display: flex;justify-content: space-between">
                    <div style="flex: 1 0 120px" class="ellipsis" :title="i.afterNetIn">总收入：<span v-if="i.diffNetIn==0">{{i.afterNetIn}}</span><span v-else>{{i.originNetIn}}{{i.diffNetIn>0?'+':''}}{{i.diffNetIn}}={{i.afterNetIn}}</span></div>
                    <div v-if="revenueP" style="flex: 1 0 120px" class="ellipsis" :title="i.afterNetOut">总成本：<span v-if="i.diffNetOut==0">{{i.afterNetOut}}</span><span v-else>{{i.originNetOut}}{{i.diffNetOut>0?'+':''}}{{i.diffNetOut}}={{i.afterNetOut}}</span></div>
                    <div v-if="revenueP" style="flex: 1 0 100px" class="ellipsis" :title="i.afterTax">总税金：<span v-if="i.diffTax==0">{{i.afterTax}}</span><span v-else>{{i.originTax}}{{i.diffTax>0?'+':''}}{{i.diffTax}}={{i.afterTax}}</span></div>
                    <div v-if="revenueP" style="flex: 1 0 120px" class="ellipsis" :title="i.afterNetProfits">利润：<span v-if="i.diffNetProfits==0">{{i.afterNetProfits}}</span><span v-else>{{i.originNetProfits}}{{i.diffNetProfits>0?'+':''}}{{i.diffNetProfits}}={{i.afterNetProfits}}</span></div>
                    <div v-if="revenueP" style="flex: 1 0 116px" class="ellipsis">利润率：<span v-if="profitsRateBefore(i)==profitsRateAfter(i)">{{profitsRateBefore(i)}}%</span><span v-else>{{profitsRateBefore(i)}}%{{profitsRateBefore(i)>profitsRateAfter(i)?'↘':'↗'}}{{profitsRateAfter(i)}}%</span></div>
                </div>
            </div>
        </div>
        <div style="background-color: #fff;padding: 5px;position: relative;border:1px #ddd solid;border-top:none" v-for="(e, idx2) in i.adjustedEntrustList">
            <div class="corner-badge" style="background-color: rgb(99,195,198)">应付</div>
            <div class="flex-row" style="">
                <div style="width:375px">
                    <div style="display: flex;justify-content: space-between">
                        <div style="flex: 0 0 135px"><el-tooltip placement="right" trigger="hover" :content="billingTypeLabel[e.billingType]">
                            <a href="javascript:;">{{e.lot}}</a>{{e.billingType}}
                        </el-tooltip></div>
                        <div style="flex: 1 1 240px" class="ellipsis">{{e.carrName}}</div>
                    </div>
                    <div style="display: flex;">
                        <div class="ellipsis" style="flex:0 0 100px"><img th:src="@{/img/cl.png}" alt="车号" style="width: 20px;"> {{e.carno}}</div>
                        <div class="ellipsis" style="flex:1 1 105px;" :title="e.driverName+'/'+e.driverMobile"><img th:src="@{/img/sj.png}" alt="司机信息" style="width: 20px;"> {{e.driverName}}/{{e.driverMobile}}</div>
                        <div class="ellipsis" style="flex:1 1 170px;"><img th:src="@{/img/wp.png}" alt="货品信息" style="width: 20px;"> {{e.goodsName}} {{nwv(e.numCount,e.weightCount,e.volumeCount)}}</div>
                    </div>
                </div>
                <div style="flex: 1;display: flex;justify-content: space-between">
                    <div class="ellipsis" :title="pricingMethod[e.pricingMethod]" style="flex:1 0 110px;">计价方式：{{pricingMethod[e.pricingMethod]}}</div>
                    <div v-if="revenueP" class="ellipsis" :title="e.unitPrice" style="flex:1 0 70px;">单价：{{e.unitPrice}}</div>
                    <div v-if="revenueP" class="ellipsis" :title="e.frightAmount" style="flex:1 0 140px;">现金运费：<span v-if="e.diffFreight==0" v-text="e.frightAmount"></span><span v-else>{{e.originFreight}}{{e.diffFreight>0?'+':''}}<el-tooltip placement="top" :content="costTypeFreight[e.freightType]"><span class="primary">{{e.diffFreight}}</span></el-tooltip>={{e.frightAmount}}</span></div>
                    <div class="ellipsis" :title="e.onWayAmount" style="flex:1 0 130px;">现金在途：<span v-if="e.diffOnway==0" v-text="e.onWayAmount"></span><span v-else>{{e.originOnway}}{{e.diffOnway>0?'+':''}}<el-tooltip placement="top" :content="costTypeOnWay[e.onwayType]"><span class="primary">{{e.diffOnway}}</span></el-tooltip>={{e.onWayAmount}}</span></div>
                    <div v-if="revenueP" class="ellipsis" :title="e.oilAmount" style="flex:1 0 146px;">应付油卡：<span v-if="e.diffOil==0" v-text="e.oilAmount"></span><span v-else>{{e.originOil}}{{e.diffOil>0?'+':''}}<el-tooltip placement="top" :content="costTypeFreight[e.oilType]"><span class="primary">{{e.diffOil}}</span></el-tooltip>={{e.oilAmount}}</span></div>
                </div>
            </div>

        </div>
    </div>

<!--    <table class="table table-bordered" style="width: 100%;table-layout: fixed">-->
<!--        <thead>-->
<!--            <tr style="background-color: rgb(247, 242, 152)">-->
<!--                <th style="width: 136px">发货单</th>-->
<!--                <th style="width: 78px">要求提货</th>-->
<!--                <th style="width: 64px">客户</th>-->
<!--                <th>客户单号</th>-->
<!--                <th>线路/要求车型/货品/货量</th>-->
<!--                <th style="width: 80px">计价方式</th>-->
<!--                <th style="width: 50px">单价</th>-->
<!--                <th style="width: 96px">应收运费</th>-->
<!--                <th style="width: 72px">应收在途</th>-->
<!--                <th style="width: 72px"><el-tooltip class="item" effect="dark" placement="top" content="三方费用按不开票计算">-->
<!--                        <a href="javascript:;">三方费用</a>-->
<!--                </el-tooltip></th>-->
<!--                <th style="width: 76px"><el-tooltip class="item" effect="dark" placement="top" content="含税总应收 × 费率 ÷ 税点">-->
<!--                    <a href="javascript:;">未税平台费</a>-->
<!--                </el-tooltip></th>-->
<!--                <th style="width: 76px"><el-tooltip class="item" effect="dark" placement="top" content="未税应收运费 + 未税应收在途">-->
<!--                    <a href="javascript:;">未税总收入</a>-->
<!--                </el-tooltip></th>-->
<!--                <th style="width: 76px"><el-tooltip class="item" effect="dark" placement="top" content="三方 + 平台费 + 未税现金运费 + 现金在途 + 未税应付油卡">-->
<!--                    <a href="javascript:;">未税总成本</a>-->
<!--                </el-tooltip></th>-->
<!--                <th style="width: 76px"><el-tooltip class="item" effect="dark" placement="top" content="未税总收入 - 未税总成本">-->
<!--                    <a href="javascript:;">未税利润</a>-->
<!--                </el-tooltip></th>-->
<!--                <th style="width: 76px"><el-tooltip class="item" effect="dark" placement="top" content="未税利润 ÷ 未税总收入">-->
<!--                    <a href="javascript:;">未税利润率</a>-->
<!--                </el-tooltip></th>-->
<!--            </tr>-->
<!--        </thead>-->
<!--        <tbody>-->
<!--        <template v-for="(i, idx1) in invoiceList">-->
<!--        <tr style="background-color: #f9f9f9">-->
<!--            <td>-->
<!--                <el-tooltip placement="right" trigger="hover">-->
<!--                <div slot="content">票点：{{billingTypeLabel[i.billingType]}}<br>平台费率：{{i.platRate}}<br>平台费税点：{{i.platTax}}</div>-->
<!--                <a href="javascript:;">{{i.vbillno}}</a>-->
<!--            </el-tooltip></td>-->
<!--            <td>{{i.reqDeliDate && i.reqDeliDate.substring(0, 10)}}</td>-->
<!--            <td><div class="ellipsis" :title="i.custAbbr">{{i.custAbbr}}</div></td>-->
<!--            <td><el-tooltip placement="top-start" trigger="hover">-->
<!--                <div slot="content">{{i.custOrderno}}</div>-->
<!--                <div class="ellipsis">{{i.custOrderno}}</div>-->
<!--            </el-tooltip></td>-->
<!--            <td><el-tooltip placement="top-start" trigger="hover">-->
<!--                <div slot="content">{{i.deliCityName=='市辖区'?i.deliProName:i.deliCityName}}~{{i.arriCityName=='市辖区'?i.arriProName:i.arriCityName}}<br>-->
<!--                    {{i.carLenName?(i.carLenName+'米'):''}}{{i.carTypeName}}<br>-->
<!--                    {{i.goodsName}}<br>-->
<!--                    {{nwv(i.numCount,i.weightCount,i.volumeCount)}}</div>-->
<!--                <div class="ellipsis">{{i.deliCityName=='市辖区'?i.deliProName:i.deliCityName}}~{{i.arriCityName=='市辖区'?i.arriProName:i.arriCityName}}/{{i.carLenName?(i.carLenName+'米'):''}}{{i.carTypeName}}/{{i.goodsName}}/{{nwv(i.numCount,i.weightCount,i.volumeCount)}}</div>-->
<!--            </el-tooltip></td>-->
<!--            <td><div class="ellipsis" :title="billingMethod[i.billingMethod]">{{billingMethod[i.billingMethod]}}</div></td>-->
<!--            <td style="text-align: right">{{i.unitPrice}}</td>-->
<!--            <td><el-tooltip placement="top" :disabled="i.diffFreight==0"><div slot="content">原应收运费：{{i.originFreight}}<br>{{i.diffFreight > 0 ? '+':''}}{{i.diffFreight}}</div><span :style="highLightStyle(i.originFreight, i.freightAmount)" v-text="i.freightAmount"></span></el-tooltip></td>-->
<!--            <td><el-tooltip placement="top" :disabled="i.diffOnway==0"><div slot="content">原应收在途：{{i.originOnway}}<br>{{costTypeOnWay[i.onwayType]}}：{{i.diffOnway}}</div><span :style="highLightStyle(i.originOnway, i.onWayAmount)" v-text="i.onWayAmount"></span></el-tooltip></td>-->
<!--            <td><el-tooltip placement="top" :disabled="i.diffOther==0"><div slot="content">原三方：{{i.originOther}}<br>{{costTypeOnWay[i.otherType]}}：{{i.diffOther}}</div><span :style="highLightStyle(i.otherFee, i.originOther)" v-text="i.otherFee"></span></el-tooltip></td>-->
<!--            <td style="text-align: right">{{i.afterNetPlatFee.toFixed(2)}}</td>-->
<!--            <td style="text-align: right">{{i.afterNetIn.toFixed(2)}}</td>-->
<!--            <td style="text-align: right">{{i.afterNetOut.toFixed(2)}}</td>-->
<!--            <td style="text-align: right">{{i.afterNetProfits.toFixed(2)}}</td>-->
<!--            <td style="text-align: right">{{profitsRateAfter(i)}}%</td>-->
<!--        </tr>-->
<!--        <tr v-if="i.adjustedEntrustList.length > 0">-->
<!--            <td colspan="15" style="padding: 0px 0px 0px 9px;">-->
<!--                <table class="table" style="margin:-1px -1px 10px 1px">-->
<!--                    <tr style="background-color: rgb(147, 252, 252)">-->
<!--                        <th style="width: 125px">运单号</th>-->
<!--                        <th>承运商</th>-->
<!--                        <th>车号</th>-->
<!--                        <th>司机/联系方式</th>-->
<!--                        <th style="width: 150px">货量</th>-->
<!--                        <th style="width: 64px">计价方式</th>-->
<!--                        <th style="width: 64px">单价</th>-->
<!--                        <th style="width: 96px">现金运费 <el-tooltip class="item" effect="dark" placement="top" content="按运单票点计算">-->
<!--                            <i class="el-icon-question" style="font-size: 13px"></i>-->
<!--                        </el-tooltip></th>-->
<!--                        <th style="width: 96px">现金在途 <el-tooltip class="item" effect="dark" placement="top" content="现金在途按不开票计算">-->
<!--                            <i class="el-icon-question" style="font-size: 13px"></i>-->
<!--                        </el-tooltip></th>-->
<!--                        <th style="width: 96px">应付油卡 <el-tooltip class="item" effect="dark" placement="top" :content="'统一税点：' + oilTax">-->
<!--                            <i class="el-icon-question" style="font-size: 13px"></i>-->
<!--                        </el-tooltip></th>-->
<!--                    </tr>-->
<!--                    <tr v-for="(e, idx2) in i.adjustedEntrustList">-->
<!--                        <td><el-tooltip placement="right" trigger="hover" :content="billingTypeLabel[e.billingType]">-->
<!--                            <a href="javascript:;">{{e.lot}}</a>-->
<!--                        </el-tooltip></td>-->
<!--                        <td>{{e.carrName}}</td>-->
<!--                        <td>{{e.carno}}</td>-->
<!--                        <td>{{e.driverName}}/{{e.driverMobile}}</td>-->
<!--                        <td>{{nwv(e.numCount,e.weightCount,e.volumeCount)}}</td>-->
<!--                        <td>{{pricingMethod[e.pricingMethod]}}</td>-->
<!--                        <td style="text-align: right">{{e.unitPrice}}</td>-->
<!--                        <td style="text-align: right"><el-tooltip placement="top" :disabled="e.diffFreight==0"><div slot="content">原现金运费：{{e.originFreight}}<br>{{costTypeFreight[e.freightType]}}：{{e.diffFreight}}</div><span :style="highLightStyle(e.frightAmount, e.originFreight)" v-text="e.frightAmount"></span></el-tooltip></td>-->
<!--                        <td style="text-align: right"><el-tooltip placement="top" :disabled="e.diffOnway==0"><div slot="content">原现金在途：{{e.originOnway}}<br>{{costTypeOnWay[e.onwayType]}}：{{e.diffOnway}}</div><span :style="highLightStyle(e.onWayAmount, e.originOnway)" v-text="e.onWayAmount"></span></el-tooltip></td>-->
<!--                        <td style="text-align: right"><el-tooltip placement="top" :disabled="e.diffOil==0"><div slot="content">原油卡：{{e.originOil}}<br>{{costTypeFreight[e.oilType]}}：{{e.diffOil}}</div><span :style="highLightStyle(e.oilAmount, e.originOil)" v-text="e.oilAmount"></span></el-tooltip></td>-->
<!--                    </tr>-->
<!--                </table>-->
<!--            </td>-->
<!--        </tr>-->
<!--        </template>-->
<!--        </tbody>-->
<!--    </table>-->

    <div class="flex">
        <span class="flex_left">附件：</span>
        <div class="flex-right imgPreview" style="margin: 0 5px 5px 0;">
            <template v-for="file in fjList">
                <img v-if="file.fileName.toLowerCase().endsWith('.jpg') ||
                                          file.fileName.toLowerCase().endsWith('.png') ||
                                          file.fileName.toLowerCase().endsWith('.gif') ||
                                          file.fileName.toLowerCase().endsWith('.bmp')" :src="file.filePath" style="width:70px; height:50px;margin-right:5px;" />
                <a v-else :href="file.filePath" class="eclipse" style="max-width: 100px;margin-right: 5px;line-height:26px;display: inline-block" target="_blank" :title="file.fileName">{{file.fileName}}</a>
            </template>
        </div>
    </div>
    <div class="flex">
        <span class="flex_left">调整原因：</span>
        <div class="flex_right" style="margin-right: 5px">
            <div class="form-control" style="white-space: pre;min-height: 52px;height: auto;">[[${batchAdjust.memo}]]</div>
        </div>
    </div>

</div>
<th:block th:include="include :: footer"/>

<script th:src="@{/js/vue.min.js}"></script>
<script th:src="@{/element-ui@2.15.13/lib/index.js}"></script>
<script th:src="@{/js/bignumber.min.js}"></script>
<script type="text/javascript" th:inline="javascript">
    var batchId = [[${param.batchId[0]}]];
    var act = [[${act}]];
    var taskId = [[${taskId}]];
    if (!taskId && act == 'verify') {
        $.modal.alertWarning("当前单据已被其他人审批，无需再审批");
    }

    var prefix = ctx + "batch-adjust";

    function plus(n1, n2) { // +运算
        let x = new BigNumber(n1);
        if (x.isNaN()) {
            x = new BigNumber(0)
        }
        let y = new BigNumber(n2);
        if (y.isNaN()) {
            y = new BigNumber(0)
        }
        return x.dp(10).plus(y.dp(10)).dp(10).toNumber()
    }
    function minus(n1, n2) { // -运算
        let x = new BigNumber(n1);
        if (x.isNaN()) {
            x = new BigNumber(0)
        }
        let y = new BigNumber(n2);
        if (y.isNaN()) {
            y = new BigNumber(0)
        }
        return x.dp(10).minus(y.dp(10)).dp(10).toNumber()
    }
    function times(n1, n2) {
        return new BigNumber(n1).dp(10).times(new BigNumber(n2).dp(16)).toNumber()
    }
    function div(n1, n2, dp) { // 除运算（被除数，除数，保留小数）
        if (dp == undefined) {
            dp = 2;
        }
        return new BigNumber(n1).dp(10).div(new BigNumber(n2).dp(10)).dp(dp).toNumber()
    }

    function listToMap(list, keyField, valueField) {
        let r = {};
        for (let i = 0; i < list.length; i++) {
            r[list[i][keyField]] = list[i][valueField];
        }
        return r
    }

    const vue = new Vue({
        el: '#app',
        data() {
            return {
                batchAdjust: {},
                invoiceList: [],
                billingMethod: listToMap([[${T(com.ruoyi.tms.constant.BillingMethod).getAllToMap()}]], 'value', 'context'),
                pricingMethod: listToMap([[${T(com.ruoyi.tms.constant.basic.CarrierProtocolPricingMethodEnum).toMap()}]], 'value', 'context'),
                billingType: [[${@dict.getType("billing_type")}]],
                oilTax: [[${@sysConfigServiceImpl.selectConfigByKey("net_profits_oil_tax")}]],
                costTypeOnWay: listToMap([[${@dict.getType("cost_type_on_way")}]], 'dictValue', 'dictLabel'), // 在途&三方
                costTypeFreight: listToMap([[${@dict.getType("cost_type_freight")}]], 'dictValue', 'dictLabel'), // 预付、回付、到付
                fjList: [[${batchAdjust.fjList}]],
                revenueP: [[${@shiroUtils.getSubject().isPermitted('tms:revenue:view') || @shiroUtils.getSubject().isPermitted('tms:revenue:verify')}]]
            }
        },
        created() {
            this.loadChangeList(batchId)
        },
        mounted() {
            this.$nextTick(function () {
                $('.imgPreview').viewer({url: 'data-original', title: false, navbar: false});
            });
        },
        computed: {
            billingTypeLabel() {
                return listToMap(this.billingType, 'dictValue', 'dictLabel')
            },
            billingTypeTax() {
                return listToMap(this.billingType, 'dictValue', 'numVal1')
            },
            // changed() {
            //     let r = [];
            //     let netIn = 0; // 未税总收入变化
            //     let netOut = 0; // 未税总成本变化
            //
            //     for (let i = 0; i < this.invoiceList.length; i++) {
            //         let invoice = this.invoiceList[i];
            //         let netInOrigin = this.netIn(invoice, true);
            //         let netInAfter = this.netIn(invoice);
            //         netIn = plus(netIn, minus(netInAfter, netInOrigin));
            //         let netOutOrigin = this.netOut(invoice, true);
            //         let netOutAfter = this.netOut(invoice);
            //         netOut = plus(netOut, minus(netOutAfter, netOutOrigin));
            //     }
            //     if (netIn != 0) {
            //         r.push({type: netIn > 0 ? 'success' : 'danger', text:["未税总应收：", netIn > 0 ? '+' : '', netIn.toFixed(2)].join('')});
            //     }
            //     if (netOut != 0) {
            //         r.push({type: netOut > 0 ? 'danger' : 'success', text:["未税总成本：", netOut > 0 ? '+' : '', netOut.toFixed(2)].join('')});
            //     }
            //     let netProfits = minus(netIn, netOut); // 未税利润变化
            //     if (netProfits != 0) {
            //         r.push({type: netProfits > 0 ? 'success' : 'danger', text:["未税利润：", netProfits > 0 ? '+' : '', netProfits.toFixed(2)].join('')});
            //     }
            //     return r;
            // }
        },
        methods: {
            loadChangeList(batchId) {
                $.ajax({
                    url: prefix + "/change-data",
                    data: "batchId=" + batchId,
                    type: 'post',
                    success: function (result) {
                        if (result.code == 0) {
                            vue.batchAdjust = result.data.batchAdjust;
                            //vue.$set(vue.batchAdjust, 'netInChange',  0)
                            vue.invoiceList.push(...result.data.invoiceList)
                        } else {
                            $.modal.alertError(result.msg)
                        }
                    }
                })
            },
            nwv(num,weight,volume,showZero) {
                let nwv = [];
                (num||showZero) && nwv.push(num + '件');
                (weight||showZero) && nwv.push(weight + '吨');
                (volume||showZero) && nwv.push(volume + 'm³');
                return nwv.join(' | ');
            },
            // ptf(invoice, origin) { // 平台费
            //     if (invoice.platRate != null) {
            //         return div(times(plus(invoice[origin?'freightAmountBak':'freightAmount'], invoice[origin?'onWayAmountBak':'onWayAmount']), invoice.platRate), invoice.platTax).toFixed(2);
            //     }
            //     return "0";
            // },
            // netIn(invoice, origin) { // 未税总收入
            //     // (应收运费 + 应收在途) / 发货单开票税率
            //     return div(plus(invoice[origin?'freightAmountBak':'freightAmount'], invoice[origin?'onWayAmountBak':'onWayAmount']), this.billingTypeTax[invoice.billingType]).toFixed(2);
            // },
            // netOut(invoice, origin) {
            //     // 三方 + 平台费 + 总应付
            //     let sum = invoice[origin?'otherFeeBak':'otherFee']; // 三方不计税
            //     sum = plus(sum, this.ptf(invoice, origin));
            //     for (let i = 0; i < invoice.adjustedEntrustList.length; i++) {
            //         let e = invoice.adjustedEntrustList[i];
            //         sum = plus(sum, div(e[origin?'frightAmountBak':'frightAmount'], this.billingTypeTax[e.billingType], 10));
            //         sum = plus(sum, e[origin?'onWayAmountBak':'onWayAmount']);
            //         sum = plus(sum, div(e[origin?'oilAmountBak':'oilAmount'], this.oilTax, 10));
            //     }
            //     return sum.toFixed(2);
            // },
            // profits(invoice, origin) {
            //     return minus(this.netIn(invoice, origin), this.netOut(invoice, origin)).toFixed(2);
            // },
            profitsRateBefore(invoice) {
                return times(div(invoice.originNetProfits, invoice.originNetIn, 4), 100);
            },
            profitsRateAfter(invoice) {
                return times(div(invoice.afterNetProfits, invoice.afterNetIn, 4), 100);
            },
            highLightStyle(numBak, num) {
                let stl = {}
                if (numBak < num) {
                    stl['color'] = 'green';
                    stl['font-weight'] = 'bold';
                } else if (numBak > num) {
                    stl['color'] = 'red';
                    stl['font-weight'] = 'bold';
                } else {
                    stl['color'] = '#aaa'
                }
                return stl;
            }
        }
    });

    function submitApprove() {
        if (!taskId) {
            $.modal.msgWarning("已审批结束，无需再审批");
            return
        }
        layer.closeAll();
        // 通过
        layer.open({
            type: 1,
            area: ['800px', '200px'],
            fix: false,
            maxmin: false,
            shade: 0.3,
            zIndex: 1,
            title: '提交审批',
            content: `<form id="verifyForm"><div class="flex" style="margin: 5px">
                <span class="flex_left">审批意见：</span>
                <div class="flex_right">
                    <textarea class="form-control" id="opinion" rows="3"></textarea>
                </div>
            </div></form>`,
            btn: ['审批通过', '关闭'],
            // 弹层外区域关闭
            shadeClose: false,
            yes: function (index, layero) {
                if (!$.validate.form('verifyForm')) {
                    return;
                }
                $.modal.confirm("确认提交审批吗？", function(){
                    var data = {batchId: batchId, taskId: taskId, agree: 1, opinion: $('#opinion').val().trim()};
                    $.operate.save(prefix + "/do-verify", data);
                })
            }
        })

    }
    function submitBack() {
        if (!taskId) {
            $.modal.msgWarning("已审批结束，无需再审批");
            return
        }
        layer.closeAll();
        // 不通过
        layer.open({
            type: 1,
            area: ['800px', '200px'],
            fix: false,
            maxmin: false,
            shade: 0.3,
            zIndex: 1,
            title: '提交审批',
            content: `<form id="verifyForm"><div class="flex" style="margin: 5px">
                <span class="flex_left"><span style="color: red">*</span> 审批意见：</span>
                <div class="flex_right">
                    <textarea class="form-control" id="opinion" rows="3" required></textarea>
                </div>
            </div></form>`,
            btn: ['审批不通过', '关闭'],
            // 弹层外区域关闭
            shadeClose: false,
            yes: function (index, layero) {
                if (!$.validate.form('verifyForm')) {
                    return;
                }
                $.modal.confirm("确认提交审批吗？", function(){
                    var data = {batchId: batchId, taskId: taskId, agree: 0, opinion: $('#opinion').val().trim()};
                    $.operate.save(prefix + "/do-verify", data);
                })
            }
        })
    }
</script>
</body>
</html>