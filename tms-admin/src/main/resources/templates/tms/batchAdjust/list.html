<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('批量调账审批列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 36px);
        padding-top: 0;
    }
    .tooltip-inner{
        /* background: transparent !important;
        text-align: left !important;
        color:#363636 !important;
        border:1px solid transparent; */
        max-width: 400px !important;
    }
    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .vertical-timeline-icon{
        width: 10px;
        height: 10px;
        background-color: #18a689;
        border-color: #18a689;
        top: 20px;
    }
    .vertical-timeline-content{
        margin-left: 16px;
    }
    .leftIcon{
        position: relative;
        margin: 0;
    }
    .leftIcon::after{
        content:" ";
        position: absolute;
        width: 1px;
        height: 100%;
        background-color: #18a689;
        top: 20px;
        left: 4px;
    }
    .leftIcon:last-child::after{
        height: 50%;
    }
    .leftIcon:first-child{
        font-size: 14px;
        color: #1ab394;
    }
    .flex{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 0 -5px;
    }
    .flex>span{
        margin:0 5px;
    }
    .pa1{
        white-space: pre-wrap;
        width: 4em;
        display: inline-block;
        line-height: 14px;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .mtdiv{
        display: inline-block;
        vertical-align: middle;
        margin-left: 5px;
    }
    .mtdiv div{
        text-align: center;
    }
    .mtdiv div:nth-child(2){
        margin-top: 4px;
    }
    .whpr{
        white-space: pre-wrap;
        width: 8em;
        display: inline-block;
    }
    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .vertical-timeline-icon{
        width: 10px;
        height: 10px;
        background-color: #18a689;
        border-color: #18a689;
        top: 20px;
    }
    .vertical-timeline-content{
        margin-left: 16px;
    }
    .leftIcon{
        position: relative;
        margin: 0;
    }
    .leftIcon::after{
        content:" ";
        position: absolute;
        width: 1px;
        height: 100%;
        background-color: #18a689;
        top: 20px;
        left: 4px;
    }
    .leftIcon:last-child::after{
        height: 50%;
    }
    .leftIcon:first-child{
        font-size: 14px;
        color: #1ab394;
    }
    .cpx{
        display: inline-block;
        width: 35px;
        height: 35px;
        background: url("/img/cpx.png") no-repeat 100%/100%;
        vertical-align: middle;
    }
    .ssx{
        display: inline-block;
        width: 45px;
        height: 40px;
        background: url("/img/ssx.png") no-repeat 100%/100%;
        vertical-align: middle;
        /*position: relative;
        top: 6px;*/
    }

    .xjx{
        display: inline-block;
        width: 50px;
        height: 40px;
        background: url("/img/xjx.png") no-repeat 100%/100%;
        vertical-align: middle;
        /*position: relative;*/
        /*top: 6px;*/
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="col-md-2 col-sm-3">
                    <div class="form-group">
                        <div class="col-sm-12">
                            <select name="range" id="history" class="form-control valid noselect2 selectpicker" onchange="searchPre()"
                                    aria-invalid="false" data-none-selected-text="数据范围">
                                <option></option>
                                <option value="mytask">待我审批</option>
                                <option value="mytasked">我已审批</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-3">
                    <input name="custAbbr" class="form-control" placeholder="客户简称" />
                </div>
                <div class="col-md-2 col-sm-3">
                    <div class="form-group">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                class="fa fa-refresh"></i>&nbsp;重置</a>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">


        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    //运单状态
    var prefix = ctx + "batch-adjust";

    //调账状态
    //var adjustCheckStatusArr = [[${adjustCheckStatusArr}]];
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    let cur_rows = [];

    $(function () {
        var options = {
            url: prefix + "/list",
            showToggle: false,
            showColumns: true,
            uniqueId: "batchId",
            firstLoad: false,
            height: 580,
            rememberSelected: false,
            fixedNumber: 0,
            clickToSelect:true,
            showFooter:false,
            onPostBody: function (rows) {
                cur_rows = rows;
                $('[process]').on('show.bs.tooltip', function () {
                    if ($(this).attr('data-original-title') == "加载中...") {
                        var that = this;
                        let index = $(this).attr('process')
                        var item = cur_rows[index];
                        let li = [];
                        li.push(`<div class='vertical-timeline-block leftIcon'>
                                                        <div class='vertical-timeline-icon'>
                                                            <img src='/img/pep.png' style='width: 100%;height: 100%'>
                                                        </div>
                                                        <div class='vertical-timeline-content'>
                                                            <div class=''>
                                                                <div class='fw f18 mt10' style='white-space: nowrap;'>${item.applyUserName}创建了批量调整单</div>
                                                                <div class='fw f18' style='white-space: nowrap;'></div>
                                                                <div style='white-space: nowrap;'>${item.createTime}</div>
                                                            </div>
                                                            <div class='mt10' style='white-space: initial;'></div>
                                                        </div>
                                                    </div>`)
                        if (!cur_rows[index].processId) {
                            let html = `<div class='ontooltip'>
                                        <div class='panel-body tooltipBody'>
                                            <div class='padt5'>
                                                <div class='vertical-container light-timeline'>
                                                    ${li.join('')}
                                                </div>
                                            </div>
                                        </div>
                                    </div>`
                            $(that).attr('title', html).tooltip('fixTitle').tooltip('show');
                            return;
                        }
                        $.ajax({
                            url: prefix + '/processHistory?processId=' + cur_rows[index].processId,
                            cache: false,
                            success: function (res) {
                                if (res.code == 0) {
                                    for (let i = 0; i < res.data.length; i++) {
                                        li.unshift(`<div class='vertical-timeline-block leftIcon'>
                                                        <div class='vertical-timeline-icon'>
                                                            <img src='/img/pep.png' style='width: 100%;height: 100%'>
                                                        </div>
                                                        <div class='vertical-timeline-content'>
                                                            <div class=''>
                                                                <div class='fw f18 mt10'>[${res.data[i].taskName}] ${res.data[i].assign ? res.data[i].assign:''}：${res.data[i].comment}</div>

                                                                <div style='white-space: nowrap;'>${res.data[i].time ? res.data[i].time : ''}</div>
                                                            </div>

                                                        </div>
                                                    </div>`)
                                    }
                                    let html = `<div class='ontooltip'>
                                        <div class='panel-body tooltipBody'>
                                            <div class='padt5'>
                                                <div class='vertical-container light-timeline'>
                                                    ${li.join('')}
                                                </div>
                                            </div>
                                        </div>
                                    </div>`
                                    $(that).attr('title', html).tooltip('fixTitle').tooltip('show');
                                } else {
                                    $(that).attr('title', res.msg).tooltip('fixTitle').tooltip('show');
                                }
                            },
                            error: function(res) {
                                $(that).attr('title', res.responseText).tooltip('fixTitle').tooltip('show');
                            }
                        })
                    }
                })
            },
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'left',
                    field: 'batchId',
                    formatter: function (value,row,index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="明细" onclick="detail(\'' + value + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="导出明细" onclick="exportDetail(\'' + value + '\')"><i class="fa fa-cloud-download" style="font-size: 15px;"></i></a>');

                        if (row.verifyAble == 1) {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)" title="审批" onclick="verify(\'' + value + '\',\'',row.status,'\')"><i class="fa fa-eye" style="font-size: 15px;"></i></a>');
                        }
                        /*[# th:if="${@shiroUtils.getUserId() == 1}"]*/
                        if (row.status == 1) {
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)" title="删除" onclick="delproc(\'' + row.processId + '\',\''+value+'\')"><i class="fa fa-times" style="font-size: 15px;"></i></a>');
                        }
                        /*[/]*/
                        return actions.join('');
                    }
                },
                {
                    title: '审核状态',
                    field: 'status',
                    align: 'left',
                    formatter: function status(value,row,index) {
                        switch (value) {
                            case 1:
                                return '<span class="label label-warning" data-toggle="tooltip" data-container="body" data-delay="500" data-placement="bottom" data-html="true" process="'+index+'" title="加载中...">审批中</span><br/>'+row.applyDate;
                            case 2:
                                return '<span class="label label-success" data-toggle="tooltip" data-container="body" data-delay="500" data-placement="bottom" data-html="true" process="'+index+'" title="加载中...">审批通过</span><br/>'+row.applyDate;
                            case 3:
                                return '<span class="label label-danger" data-toggle="tooltip" data-container="body" data-delay="500" data-placement="bottom" data-html="true" process="'+index+'" title="加载中...">审批不通过</span><br/>'+row.applyDate;
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '申请人/申请原因',
                    align: 'left',
                    field: 'applyUserName',
                    formatter: function status(value,row) {
                        return value + '<br />' + $.table.tooltip(row.memo);
                    }
                },
                {
                    title: '客户简称',
                    align: 'left',
                    field : 'custAbbr'
                },
                {
                    title: '应收',
                    align: 'left',
                    field: 'netInAfter',
                    formatter: function(value, row) {
                        let html = []
                        html.push(row.netInOrigin.toFixed(2));
                        if (row.netInChange >= 0) {
                            html.push('+');
                        }
                        html.push(row.netInChange.toFixed(2));
                        html.push('=');
                        html.push(value.toFixed(2));
                        return html.join('')
                    }
                },/*[# th:if="${@shiroUtils.getSubject().isPermitted('tms:revenue:view') || @shiroUtils.getSubject().isPermitted('tms:revenue:verify')}"]*/
                {
                    title: '成本',
                    align: 'left',
                    field: 'netOutAfter',
                    formatter: function(value, row) {
                        let html = []
                        html.push(row.netOutOrigin.toFixed(2));
                        if (row.netOutChange >= 0) {
                            html.push('+');
                        }
                        html.push(row.netOutChange.toFixed(2));
                        html.push('=', value.toFixed(2));
                        return html.join('')
                    }
                },
                {
                    title: '税金',
                    align: 'left',
                    field: 'taxAfter',
                    formatter: function(value, row) {
                        if (value == null) {
                            return
                        }
                        let html = []
                        html.push(row.taxOrigin.toFixed(2));
                        if (row.taxChange >= 0) {
                            html.push('+');
                        }
                        html.push(row.taxChange.toFixed(2));
                        html.push('=', value.toFixed(2));
                        return html.join('')
                    }
                },
                {
                    title: '利润',
                    align: 'left',
                    field: 'netProfitsAfter',
                    formatter: function(value, row) {
                        let html = []
                        html.push(row.netProfitsOrigin.toFixed(2));
                        if (row.netProfitsChange >= 0) {
                            html.push('+');
                        }
                        html.push(row.netProfitsChange.toFixed(2));
                        html.push('=', value.toFixed(2));
                        return html.join('')
                    }
                }/*[/]*/
            ]
        };

        //$("#adjustCheckStatusArr").val(adjustCheckStatusArr);

        $.table.init(options);

        // var arr = [];
        // arr.push(segmentStatusList[0].value);
        // arr.push(segmentStatusList[1].value);
        // arr.push(segmentStatusList[6].value);
        // arr.push(segmentStatusList[7].value);
        //$('#adjustCheckStatusArr').selectpicker('val',arr);
        //$("#adjustCheckStatusArr").find('option').eq(0).attr("selected",true);
        //$("#adjustCheckStatusArr").find('option').eq(1).attr("selected",true);
        //$("#adjustCheckStatusArr").find('option').eq(2).attr("selected",true);

        searchPre();

        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        // layui.use('laydate', function() {
        //     var laydate = layui.laydate;
        //     laydate.render({
        //         elem: '#startDate',
        //         type: 'datetime',
        //         trigger: 'click'
        //     });
        // });
        // layui.use('laydate', function() {
        //     var laydate = layui.laydate;
        //     laydate.render({
        //         elem: '#endtDate',
        //         type: 'datetime',
        //         trigger: 'click'
        //     });
        // });
    });


    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        //data.adjustCheckStatusArr = $.common.join($('#adjustCheckStatusArr').selectpicker('val'));
        //data.salesDeptPayDetail = $.common.join($('#salesDeptPayDetail').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        //$("#adjustCheckStatusArr").find('option').eq(0).attr("selected",true);
        //$("#adjustCheckStatusArr").find('option').eq(1).attr("selected",true);
        //$("#adjustCheckStatusArr").find('option').eq(2).attr("selected",true);
        searchPre();
    }

    function detail(batchId) {
        layer.open({
            type: 2,
            area: [document.documentElement.clientWidth + 'px', document.documentElement.clientHeight+'px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "调帐单明细",
            skin: '',
            content:  prefix + "/changeView?batchId=" + batchId,
            btn: ['导出明细', '关闭'],
            shadeClose: true,
            success: function (layero, index) {
                layer.full(index);
            },
            btn1: function (index, layero) {
                exportDetail(batchId)
            }
        });
    }

    function exportDetail(batchId) {
        $.modal.confirm("即将导出，是否继续？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(prefix + "/export-dtl", {batchId: batchId}, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    /**
     * 审核
     */
    function verify(batchId, status) {
        //var batchId = $.table.selectColumns('batchId')[0];
        //var status = $.table.selectColumns('status')[0];
        if(status != 1){
            $.modal.alertWarning("当前审核状态不需要审核！");
            return false;
        }
        layer.open({
            type: 2,
            area: [document.documentElement.clientWidth + 'px', document.documentElement.clientHeight+'px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "审批",
            content:  prefix + "/verifyView?batchId=" + batchId,
            btn: ['通过', '不通过','关闭'],
            shadeClose: true,
            success: function (layero, index) {
                layer.full(index);
            },
            btn1: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitApprove(index, layero);
            },
            btn2: function(index, layero){
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitBack(index, layero);
                return false;
            },
            btn3: function (index, layero) {
                return true;
            }
        });
    }

    // function getCheckList(item) {
    //     let html="";
    //     html+=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'><div class='vertical-container light-timeline'>`;
    //     if(item.thirdCheckUserName){
    //         html+=`<div class='vertical-timeline-block leftIcon'>
    //                 <div class='vertical-timeline-icon'>
    //                     <img src='/img/pep.png' style='width: 100%;height: 100%'>
    //                 </div>
    //                 <div class='vertical-timeline-content'>
    //                     <div class=''>
    //                         <div class='fw f18'> 三级审核人: `+item.thirdCheckUserName+`</div>
    //                         <div class='fw f18'>
    //                             备注: `+(item.thirdCheckMemo== null?'暂无数据':item.thirdCheckMemo)+`
    //                         </div>
    //                         <div style='white-space: nowrap;'>`+item.thirdCheckDate+`</div>
    //                     </div>
    //                     <div class='mt10' style='white-space: initial;'></div>
    //                 </div>
    //
    //             </div>`;
    //     }
    //     if(item.secondCheckUserName){
    //         html+=`<div class='vertical-timeline-block leftIcon'>
    //                 <div class='vertical-timeline-icon'>
    //                     <img src='/img/pep.png' style='width: 100%;height: 100%'>
    //                 </div>
    //                 <div class='vertical-timeline-content'>
    //                     <div class=''>
    //                         <div class='fw f18'> 二级审核人: `+item.secondCheckUserName+`</div>
    //                         <div class='fw f18'>
    //                             备注: `+(item.secondCheckMemo== null?'暂无数据':item.secondCheckMemo)+`
    //                         </div>
    //                         <div style='white-space: nowrap;'>`+item.secondCheckDate+`</div>
    //                     </div>
    //                     <div class='mt10' style='white-space: initial;'></div>
    //                 </div>
    //
    //             </div>`;
    //     }
    //     if(item.firstCheckUserName){
    //         html+=`<div class='vertical-timeline-block leftIcon'>
    //                 <div class='vertical-timeline-icon'>
    //                     <img src='/img/pep.png' style='width: 100%;height: 100%'>
    //                 </div>
    //                 <div class='vertical-timeline-content'>
    //                     <div class=''>
    //                         <div class='fw f18'> 一级审核人: `+item.firstCheckUserName+`</div>
    //                         <div class='fw f18'>
    //                             备注: `+(item.firstCheckMemo== null?'暂无数据':item.firstCheckMemo)+`
    //                         </div>
    //                         <div style='white-space: nowrap;'>`+item.firstCheckDate+`</div>
    //                     </div>
    //                     <div class='mt10' style='white-space: initial;'></div>
    //                 </div>
    //
    //             </div>`;
    //     }
    //
    //
    //
    //
    //     let title="",selectDictLabel=""
    //     if(item.budgetType==2||item.budgetType==4){
    //         title= "在途应收";
    //         selectDictLabel=$.table.selectDictLabel(payOrCollect, item.budgetType)+"/"+$.table.selectDictLabel(costTypeOnWay, item.costType)
    //     }else if(item.budgetType==1||item.budgetType==5||item.budgetType==null){
    //         if(item.isLotFee==0){
    //             title= "在途应付";
    //             selectDictLabel=$.table.selectDictLabel(payOrCollect, item.budgetType)+"/"+$.table.selectDictLabel(costTypeOnWay, item.costType)
    //         }else{
    //             title= "三方应付";
    //             selectDictLabel=$.table.selectDictLabel(costTypeOnWay, item.feeType)
    //         }
    //     }
    //
    //     html+=`<div class='vertical-timeline-block leftIcon'>
    //             <div class='vertical-timeline-icon'>
    //                 <img src='/img/pep.png' style='width: 100%;height: 100%'>
    //             </div>
    //             <div class='vertical-timeline-content'>
    //                 <div class=''>
    //                     <div class='fw f18 mt10' style='white-space: nowrap;'>`+item.regUserName+`创建了`+title+`费用</div>
    //                     <div class='fw f18' style='white-space: nowrap;'>`+selectDictLabel+`</div>
    //                     <div style='white-space: nowrap;'>`+item.regDate+`</div>
    //                 </div>
    //                 <div class='mt10' style='white-space: initial;'></div>
    //             </div>
    //         </div>`;
    //
    //     html+=`</div></div></div></div>`;
    //     return html
    // }

    function delproc(id, batchId) {
        $.modal.confirm('确认删除该流程吗？', function(){
            $.operate.submit(prefix + "/del-proc", "post", "json", {procId:id, batchId: batchId});
        })
    }
</script>
</body>
</html>