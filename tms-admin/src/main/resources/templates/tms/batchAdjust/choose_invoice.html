<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('选择发货单')"/>
</head>
<style>
    .table-striped {
        height: calc(100% - 88px);
    }

    .bootstrap-table .fixed-table-container {
        height: calc(100% - 120px) !important;
    }

    .fixed-table-pagination .pagination-detail {
        margin: 0;
    }

    div.pagination {
        margin-top: 10px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="select-list">
                    <ul>
                        <li>
                            发货单号：<input type="text" name="vbillno" autocomplete="off" aria-autocomplete="none"/>
                        </li>
                        <li>
                            客户简称：<input type="text" name="custAbbr" autocomplete="off" aria-autocomplete="none"/>
                        </li>
                        <li>
                            客户单号：<input type="text" name="custOrderno" autocomplete="off" aria-autocomplete="none"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table id="bootstrap-table" data-mobile-responsive="true" style="table-layout: fixed;"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "batch-adjust";
    var vbillstatus = [[${invoiceStatusList}]]; //发货单状态
    //发货单状态map
    var invoiceStatusMap = [[${invoiceStatusMap}]];
    var customerId = [[${param.customerId[0]}]];

    $(function () {
        if (customerId) {
            $('[name=custAbbr]').closest("li").remove();
        }
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                $.table.search();
            }
        });
        var options = {
            url: prefix + `/invoiceList?customerId=${customerId}`,
            showSearch: false,
            showToggle: false,
            showColumns: false,
            clickToSelect: true,
            columns: [{
                checkbox: true
            },
                {
                    title: '发货单',
                    field: 'vbillno',
                    width: 150 // CD-XXXXXX
                },
                {
                    title: '客户单号',
                    field: 'custOrderno',
                    width: 120
                },
                {
                    title: '要求提货日期',
                    field: 'reqDeliDate',
                    width: 95,
                    formatter: function (value, row, index) {
                        if (value) {
                            return value.substring(0, 10)
                        }
                    }
                },
                {
                    title: '发货单状态',
                    field: 'vbillstatus',
                    width: 85,
                    formatter: function(value, row) {
                        var context = '';
                        vbillstatus.forEach(function (v) {
                            if (v.value === row.vbillstatus) {
                                if (row.vbillstatus === invoiceStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-primary">' + v.context  + '</span>';
                                } else if (row.vbillstatus == invoiceStatusMap.AFFIRM) {
                                    //已确认
                                    context = '<span class="label label-warning">' + v.context  + '</span>';
                                } else if (row.vbillstatus == invoiceStatusMap.PORTION_PICK_UP
                                    || row.vbillstatus == invoiceStatusMap.PICK_UP) {
                                    //部分提货 与 已提货
                                    context = '<span class="label label-info">' + v.context  +'</span>';
                                } else if (row.vbillstatus == invoiceStatusMap.PORTION_ARRIVALS
                                    || row.vbillstatus == invoiceStatusMap.ARRIVALS) {
                                    //部分到货  已到货
                                    context = '<span class="label label-success">' + v.context  +'</span>';
                                } else if (row.vbillstatus == invoiceStatusMap.PORTION_RETURNS
                                    || row.vbillstatus == invoiceStatusMap.RETURNS) {
                                    //部分回单  已回单
                                    context = '<span class="label label-primary">' + v.context  +'</span>';
                                } else {
                                    //关闭
                                    context = '<span class="label label-inverse">' + v.context + '</span>';
                                }

                                return false;
                            }
                        });
                        return context;
                    }
                },
                {
                    title: '客户简称',
                    field: 'custAbbr'
                },
                {
                    title: '线路',
                    field: 'deliProName',
                    formatter: function (value, row, index) {
                        let tmp = [];
                        if (row.deliCityName == '市辖区') {
                            tmp.push(row.deliProName);
                        } else {
                            tmp.push(row.deliCityName);
                        }
                        tmp.push(' ~ ')
                        if (row.arriCityName == '市辖区') {
                            tmp.push(row.arriProName);
                        } else {
                            tmp.push(row.arriCityName);
                        }
                        return tmp.join('')
                    }
                },
                {
                    title: '应收运费',
                    width: '72px',
                    field: 'freightAmount',
                    align: 'right'
                },
                {
                    title: '应收在途',
                    width: '72px',
                    field: 'onWayAmount',
                    align: 'right'
                }

            ]
        };
        $.table.init(options);
    });

    /** 获取选中行*/
    function getChecked() {
        return $.table.selectColumns("invoiceId")
    }

    function getCustomerIds() {
        return $.table.selectColumns("customerId"); // 去重后的结果
    }
</script>
</body>
</html>