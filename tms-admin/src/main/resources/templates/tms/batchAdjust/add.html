<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('批量调账')"/>
    <link th:href="@{/element-ui@2.15.13/lib/theme-chalk/index.css}" rel="stylesheet"/>
    <style>
        .ellipsis {
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .x-block .el-tag+.el-tag {
            margin-left: 10px;
        }
        .flex{
            display: flex;
        }
        .flex_left{
            width: 88px;
            line-height: 26px;
            text-align: right;

        }
        .flex_right{
            min-width:0;
            flex:1;
            box-sizing: border-box;
        }
        .editable {
            cursor: pointer;
            color: #aaa;
            font-weight: bold;
            border-bottom: 1px #409EFF dashed;
            height: 26px;
            line-height: 18px;
            padding: 3px;
            font-style: italic;
        }
        .editdisable {
            cursor: pointer;
            color: #bbb;
            font-weight: bold;
            /*border-bottom: 2px #ddd dashed;*/
            /*height: 26px;*/
            /*line-height: 16px;*/
            padding: 2px 3px;
            font-style: italic;
        }
        .editable:hover {
            /*font-weight: bold;*/
            font-style: normal;
            /*color: blue;*/
        }
        .amount {
            font-family: "Microsoft YaHei", "PingHei 200", "PingHei", "Helvetica Neue", "Helvetica", "Arial", "Verdana", "sans-serif";
            line-height: 20px;
        }
        [v-clock]{
            display: none;
        }
        .flex-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }
        .flex-row * {
            line-height: 1.8;
        }
        .corner-badge-left {
            position: absolute;
            top: -8px;
            left: -8px;
            line-height: 16px;
            font-size: 16px;
            background-color: #fff;
            border-radius: 50%;
        }
        .corner-badge {
            position: absolute;
            top: -1px;
            right: -1px;
            padding: 2px 5px;
            border-radius: 2px;
            background-color: rgb(237,133,91);
            color: #fff;
            line-height: 13px;
            font-size: 13px;
        }
        .primary {
            color: #409EFF;
            font-weight: bold;
        }
        .danger {
            color: #f56c6c;
        }
        .success {
            color: #67c23a;
        }
        .info {
            color: #909399;
        }
        .n_up {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            border-bottom: 1px #ddd solid;
            margin: 0 5px;
            padding: 0 3px;
        }
        .n_down {
            border-bottom: 1px #ddd solid;
            line-height: 14px;
            padding: 3px 4px;
            margin: 0 6px;
            color: #aaa;
            text-align: center;
        }
        .file-preview {
            padding: 0!important;
            border: none!important;
        }
        .file-drop-zone {
            height: auto !important;
            min-height: auto !important;
            margin: 0!important;
        }
    </style>
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/bootstrap-icons.min.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/fileinput.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/all.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/jquery-editable-select/jquery-editable-select.min.css}" media="all" type="text/css"/>
</head>
<body>
<div id="app" style="padding: 5px;padding-bottom: 37px;" v-clock>
    <div style="position: fixed;height: 110px;width: 100%;z-index: 2;background-color: #fff;margin-top: -5px;padding-top: 15px;margin-left: -5px;border-bottom: 1px #eee solid;">
        <div style="display: flex; justify-content: center;margin-bottom: 10px;position: relative">
            <div style="position: absolute;right: 10px;bottom: 0;text-align: right">
                <button type="button" class="btn btn-success btn-sm" style="margin-bottom: 5px" @click="batchImport"><i class="el-icon-circle-plus-outline"></i> 导入Excel</button>
                <br>
                <button type="button" class="btn btn-warning btn-sm" @click="chooseInvoice"><i class="el-icon-circle-plus-outline"></i> 追加发货单</button>
            </div>
            <el-card>
                <div style="display: flex;justify-content: space-between;align-items: center;">
                    <el-tag type="primary" size="small" effect="dark">应收</el-tag>
                    <div>
                        <div class="n_up">{{batchAdjust.netInOrigin}}</div>
                        <div class="n_down" v-if="batchAdjust.netInChange!=0">原金额(元)</div>
                        <div class="n_down" v-else>未调整(元)</div>
                    </div>
                    <div v-if="batchAdjust.netInChange!=0">{{batchAdjust.netInChange>0?'+':''}}{{batchAdjust.netInChange}}</div>
                    <div v-if="batchAdjust.netInChange!=0">
                        <div class="n_up">{{batchAdjust.netInAfter}}</div>
                        <div class="n_down">调整后(元)</div>
                    </div>
                </div>
            </el-card>
            <el-card style="margin-left: 15px" v-if="revenueP">
                <div style="display: flex;justify-content: space-between;align-items: center;">
                    <el-tag type="info" size="small" effect="dark">成本</el-tag>
                    <div style="text-align: center">
                        <div class="n_up">{{batchAdjust.netOutOrigin}}</div>
                        <div class="n_down" v-if="batchAdjust.netOutChange!=0">原金额(元)</div>
                        <div class="n_down" v-else>未调整(元)</div>
                    </div>
                    <div v-if="batchAdjust.netOutChange!=0">{{batchAdjust.netOutChange>0?'+':''}}{{batchAdjust.netOutChange}}</div>
                    <div v-if="batchAdjust.netOutChange!=0">
                        <div class="n_up">{{batchAdjust.netOutAfter}}</div>
                        <div class="n_down">调整后(元)</div>
                    </div>
                </div>
            </el-card>
            <el-card style="margin-left: 15px" v-if="revenueP">
                <div style="display: flex;justify-content: space-between;align-items: center;">
                    <el-tag type="info" size="small" effect="dark">成本税金</el-tag>
                    <div style="text-align: center">
                        <div class="n_up">{{batchAdjust.taxOrigin}}</div>
                        <div class="n_down" v-if="batchAdjust.taxChange!=0">原金额(元)</div>
                        <div class="n_down" v-else>未调整(元)</div>
                    </div>
                    <div v-if="batchAdjust.taxChange!=0">{{batchAdjust.taxChange>0?'+':''}}{{batchAdjust.taxChange}}</div>
                    <div v-if="batchAdjust.taxChange!=0">
                        <div class="n_up">{{batchAdjust.taxAfter}}</div>
                        <div class="n_down">调整后(元)</div>
                    </div>
                </div>
            </el-card>
            <el-card style="margin-left: 15px" v-if="revenueP">
                <div style="display: flex;justify-content: space-between;align-items: center;">
                    <el-tag type="info" size="small" style="border-color: #000;color:#000" effect="plain">利润</el-tag>
                    <div style="text-align: center">
                        <div class="n_up">{{batchAdjust.netProfitsOrigin}}</div>
                        <div class="n_down" v-if="batchAdjust.netProfitsChange!=0">原金额(元)</div>
                        <div class="n_down" v-else>未调整(元)</div>
                    </div>
                    <div v-if="batchAdjust.netProfitsChange!=0">{{batchAdjust.netProfitsChange>0?'+':''}}{{batchAdjust.netProfitsChange}}</div>
                    <div v-if="batchAdjust.netProfitsChange!=0">
                        <div class="n_up">{{batchAdjust.netProfitsAfter}}</div>
                        <div class="n_down">调整后(元)</div>
                    </div>
                </div>
            </el-card>
        </div>
    </div>
    <form id="form-add" style="margin-top: 114px">
        <div v-for="(i, idx1) in invoiceList" :key="i.invoiceId" style="margin-bottom: 10px;margin-left:5px;margin-right:5px;">
            <div style="background-color: rgb(240,242,245);padding: 5px;position: relative;border:1px #ddd solid;">
                <a class="corner-badge-left el-icon-remove-outline" title="从队列中移除发货单" style="color:red" @click="rmvInvoice(idx1)"></a>
                <div class="corner-badge">应收</div>
                <div class="flex-row">
                    <div style="width:135px;font-weight: bold;"><el-tooltip placement="right" trigger="hover">
                        <div slot="content">票点：{{billingTypeLabel[i.billingType]}}<br>平台费率：{{i.platRate}}<br>平台费税点：{{i.platTax}}</div>
                        <a href="javascript:;">{{i.vbillno}}</a>
                    </el-tooltip></div>
                    <div class="ellipsis success" style="width:90px" :title="'客户单号：' + i.custOrderno">{{i.custOrderno}}</div>
                    <div class="ellipsis" :title="i.custAbbr" style="width:260px;font-weight: bold;">
                        {{i.custAbbr}}
                        {{i.deliCityName=='市辖区'?i.deliProName:i.deliCityName}}~{{i.arriCityName=='市辖区'?i.arriProName:i.arriCityName}}
                        <span class="success">({{i.reqDeliDate && i.reqDeliDate.substring(0, 10)}})</span>
                    </div>
                    <div style="flex: 1;display: flex;justify-content: space-between;">
                        <div style="flex:1 0 120px" class="ellipsis" :title="billingMethod[i.billingMethod]">单价：{{i.unitPrice}}【{{billingMethod[i.billingMethod].replace('（','(').replace('）',')')}}】</div>
                        <div style="flex:1 0 120px">应收运费：<el-tooltip placement="left" effect="light" :disabled="!i.diffFreight">
                            <div slot="content">原运费：{{i.freightAmountBak.toFixed(2)}} <el-link size="mini" type="primary" @click="i.diffFreight=undefined;i.freightAmount=i.freightAmountBak">还原</el-link></div>
                            <span :class="i.complete==1?'editable':'editdisable'" :style="highLightStyle(i.freightAmountBak, i.freightAmount)" @click="editRecvFreight(i)">{{i.freightAmount.toFixed(2)}}</span>
                        </el-tooltip></div>
                        <div style="flex:1 0 110px">应收在途：<el-tooltip placement="left" effect="light" :disabled="!i.diffOnway">
                            <div slot="content">原在途：{{i.onWayAmountBak.toFixed(2)}} <el-link size="mini" type="primary" @click="i.diffOnway=undefined;i.onwayType=undefined;i.onWayAmount=i.onWayAmountBak;i.onwayMemo=undefined;">还原</el-link></div>
                            <span :class="i.complete==1?'editable':'editdisable'" :style="highLightStyle(i.onWayAmountBak, i.onWayAmount)" @click="editRecvOnway(i)">{{i.onWayAmount.toFixed(2)}}</span>
                        </el-tooltip></div>
                        <div style="flex:1 0 110px">平台费：<el-tooltip placement="top" :disabled="i.diffNetPlatFee==0"><div slot="content">原平台费：{{i.originNetPlatFee}}<br>变动：{{i.diffNetPlatFee}}</div><span :class="{primary:i.diffNetPlatFee!=0}">{{i.afterNetPlatFee}}</span></el-tooltip></div>
                        <div style="flex:1 0 106px">三方费用：<el-tooltip placement="left" effect="light" :disabled="!i.diffOther">
                            <div slot="content">原三方：{{i.otherFeeBak.toFixed(2)}} <el-link size="mini" type="primary" @click="i.diffOther=undefined;i.otherType=undefined;i.otherFee=i.otherFeeBak">还原</el-link></div>
                            <span :class="i.complete==1?'editable':'editdisable'" :style="highLightStyle(i.otherFee, i.otherFeeBak)" @click="editOther(i)">{{i.otherFee.toFixed(2)}}</span>
                        </el-tooltip></div>
                    </div>
                </div>
                <div class="flex-row">
                    <div style="width:135px;"><img th:src="@{/img/cl.png}" style="width: 20px;"> {{i.carLenName?(i.carLenName+'米'):''}}{{i.carTypeName}}</div>
                    <div class="ellipsis" style="width:300px" :title="nwv(i.numCount,i.weightCount,i.volumeCount)"><img th:src="@{/img/wp.png}" style="width: 20px;"> {{i.goodsName}} {{nwv(i.numCount,i.weightCount,i.volumeCount)}}</div>
                    <div style="flex: 1;display: flex;justify-content: space-between;">
                        <div style="flex: 1 0 120px">总收入：<el-tooltip placement="top" :disabled="i.diffNetIn==0"><div slot="content">原收入：{{i.originNetIn}}<br>变动：{{i.diffNetIn}}</div><span :class="{primary:i.diffNetIn!=0}">{{i.afterNetIn}}</span></el-tooltip></div>
                        <div style="flex: 1 0 120px" v-if="revenueP">总成本：<el-tooltip placement="top" :disabled="i.diffNetOut==0"><div slot="content">原成本：{{i.originNetOut}}<br>变动：{{i.diffNetOut}}</div><span :class="{primary:i.diffNetOut!=0}">{{i.afterNetOut}}</span></el-tooltip></div>
                        <div style="flex: 1 0 110px" v-if="revenueP">总税金：<el-tooltip placement="top" :disabled="i.diffTax==0"><div slot="content">原税金：{{i.originTax}}<br>变动：{{i.diffTax}}</div><span :class="{primary:i.diffTax!=0}">{{i.afterTax}}</span></el-tooltip></div>
                        <div style="flex: 1 0 110px" v-if="revenueP">利润：<el-tooltip placement="top" :disabled="i.diffNetProfits==0"><div slot="content">原利润：{{i.originNetProfits}}<br>变动：{{i.diffNetProfits}}</div><span :class="{primary:i.diffNetProfits!=0}">{{i.afterNetProfits}}</span></el-tooltip></div>
                        <div style="flex: 1 0 106px" v-if="revenueP">利润率：<el-tooltip placement="top"><div slot="content">原利润率：{{profitsRate(i, true)}}%</div><span :class="{primary:profitsRate(i, true)!=profitsRate(i)}">{{profitsRate(i, false)}}%</span></el-tooltip></div>
                    </div>
                </div>
            </div>
            <div style="background-color: #fff;padding: 5px;position: relative;border:1px #ddd solid;border-top:none" v-for="(e, idx2) in i.adjustedEntrustList" :key="e.lotId">
                <div class="corner-badge" style="background-color: rgb(99,195,198)">应付</div>
                <div class="flex-row" style="">
                    <div style="width:435px">
                        <div style="display: flex;justify-content: space-between">
                            <div style="width:135px"><el-tooltip placement="right" trigger="hover" :content="billingTypeLabel[e.billingType]">
                                <a href="javascript:;">{{e.lot}}</a>{{e.billingType}}
                            </el-tooltip></div>
                            <div style="width:300px" class="ellipsis">{{e.carrName}}</div>
                        </div>
                        <div style="display: flex;">
                            <div style="flex:0 0 100px"><img th:src="@{/img/cl.png}" alt="车号" style="width: 20px;"> {{e.carno}}</div>
                            <div class="ellipsis" style="flex:1 1 135px;"><img th:src="@{/img/sj.png}" alt="司机信息" style="width: 20px;"> {{e.driverName}}/{{e.driverMobile}}</div>
                            <div class="ellipsis" style="flex:1 1 160px;"><img th:src="@{/img/wp.png}" alt="货品信息" style="width: 20px;"> {{e.goodsName}} {{nwv(e.numCount,e.weightCount,e.volumeCount)}}</div>
                        </div>
                    </div>
                    <div style="flex: 1;display: flex;just-content: space-between;">
                        <div style="flex:1 0 130px;">计价方式：{{pricingMethod[e.pricingMethod]}}</div>
                        <div style="flex:1 0 140px;" v-if="revenueP">单价：{{e.unitPrice}}</div>
                        <div style="flex:1 0 140px;" v-if="revenueP">现金运费：<el-tooltip placement="left" effect="light" :disabled="!e.diffFreight">
                            <div slot="content">原运费：{{e.frightAmountBak.toFixed(2)}} <el-link size="mini" type="primary" @click="e.diffFreight=undefined;e.freightType=undefined;e.frightAmount=e.frightAmountBak">还原</el-link></div>
                            <span :class="e.complete==1?'editable':'editdisable'" :style="highLightStyle(e.frightAmount, e.frightAmountBak)" @click="editPayFright(e)">{{e.frightAmount.toFixed(2)}}</span>
                        </el-tooltip></div>
                        <div style="flex:1 0 130px;">现金在途：<el-tooltip placement="left" effect="light" :disabled="!e.diffOnway">
                            <div slot="content">原在途：{{e.onWayAmountBak.toFixed(2)}} <el-link size="mini" type="primary" @click="e.diffOnway=undefined;e.onwayType=undefined;e.onWayAmount=e.onWayAmountBak">还原</el-link></div>
                            <span :class="e.complete==1?'editable':'editdisable'" :style="highLightStyle(e.onWayAmount, e.onWayAmountBak)" @click="editPayOnway(e)">{{e.onWayAmount.toFixed(2)}}</span>
                        </el-tooltip></div>
                        <div style="flex:1 0 146px;" v-if="revenueP">应付油卡：<el-tooltip placement="left" effect="light" :disabled="!e.diffOil">
                            <div slot="content">原油卡：{{e.oilAmountBak.toFixed(2)}} <el-link size="mini" type="primary" @click="e.diffOil=undefined;e.oilType=undefined;e.oilAmount=e.oilAmountBak">还原</el-link></div>
                            <span :class="e.complete==1?'editable':'editdisable'" :style="highLightStyle(e.oilAmount, e.oilAmountBak)" @click="editPayOil(e)">{{e.oilAmount.toFixed(2)}}</span>
                        </el-tooltip></div>
                    </div>
                </div>

            </div>
        </div>

        <div class="flex">
            <label class="flex_left" for="fj">附件：</label>
            <div class="flex_right" style="margin: 0 5px 5px 0;">
                <input type="file" name="fj" id="fj" multiple>
                <input type="hidden" name="fjTidList">
            </div>
        </div>
        <div class="flex">
            <label class="flex_left" for="memo"><span style="color: red">*</span> 调整原因：</label>
            <div class="flex_right" style="margin-right: 5px">
                <textarea class="form-control" required id="memo" rows="3" autocomplete="off"></textarea>
            </div>
        </div>
        <div style="position: fixed;bottom: 0;height: 32px;width: 100%;text-align: center;background-color: #fff">
            <el-tooltip content="提交后走审批流程，审批通过之后才会生成对应的应收应付明细">
                <button type="button" class="btn btn-primary btn-sm" onclick="submitHandler()"><i class="fa fa-check"></i> 保 存</button>
            </el-tooltip>
            <button type="button" class="btn btn-danger btn-sm" style="margin-left: 30px" onclick="$.modal.confirm('确定取消吗？', function(){closeItem();})"><i class="fa fa-reply-all"></i> 取 消</button>
        </div>
    </form>
</div>
<div id="app2" style="padding: 5px;padding-bottom: 37px;" v-clock>
    <div id="editRecvFreight" style="display: none">
        <form id="editForm1" v-if="editForm != null && curHandling == 'editRecvFreight'" style="padding: 5px">
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 调整差额：</label>
                <div class="flex_right" style="width: 270px">
                    <input class="form-control" v-model="editForm.amount" name="amount" required oninput="$.numberUtil.onlyNumberTwoDecimal(this);" autocomplete="off" placeholder="增加金额请填正数，减少金额请填负数">
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 调整后金额：</label>
                <div class="flex_right" style="width: 270px">
                    <div class="input-group">
                        <!--<div class="form-control amount" style="border-style: dashed">{{plus(curInvoice.freightAmountBak, editForm.amount).toFixed(2)}}</div>-->
                        <input class="form-control" required :value="plus(curInvoice.freightAmountBak, editForm.amount)"
                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);diff(this, 'freightAmountBak', 'amount')">
                        <div class="input-group-addon">元</div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div id="editRecvOnway" style="display: none">
        <form id="editForm2" v-if="editForm != null && curHandling == 'editRecvOnway'" style="padding: 5px">
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 调整差额：</label>
                <div class="flex_right" style="width: 270px">
                    <input class="form-control" v-model="editForm.amount" name="amount" required oninput="$.numberUtil.onlyNumberTwoDecimal(this)" autocomplete="off" placeholder="增加金额请填正数，减少金额请填负数">
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 费用类型：</label>
                <div class="flex_right" style="width: 270px">
                    <select class="form-control" v-model="editForm.type" name="type" required>
                        <option value=""></option><option v-for="dict in costTypeOnWay" :key="dict.dictValue" :value="dict.dictValue">{{dict.dictLabel}}</option>
                    </select>
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 调整后金额：</label>
                <div class="flex_right" style="width: 270px">
                    <div class="input-group">
                        <!--                        <div class="form-control amount" style="border-style: dashed">{{plus(curInvoice.onWayAmountBak, editForm.amount).toFixed(2)}}</div>-->
                        <input class="form-control" required :value="plus(curInvoice.onWayAmountBak, editForm.amount)"
                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);diff(this, 'onWayAmountBak', 'amount')">
                        <div class="input-group-addon">元</div>
                    </div>
                </div>
            </div>
            <div class="flex">
                <label class="flex_left">备注：</label>
                <div class="flex_right" style="width: 270px">
                    <textarea class="form-control" rows="4" v-model="editForm.memo"></textarea>
                </div>
            </div>
        </form>
    </div>
    <div id="editOther" style="display: none">
        <form id="editForm3" v-if="editForm != null && curHandling == 'editOther'" style="padding: 5px">
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 调整差额：</label>
                <div class="flex_right" style="width: 270px">
                    <input class="form-control" v-model="editForm.amount" name="amount" required oninput="$.numberUtil.onlyNumberTwoDecimal(this)" autocomplete="off" placeholder="增加金额请填正数，减少金额请填负数">
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 费用类型：</label>
                <div class="flex_right" style="width: 270px">
                    <select class="form-control" v-model="editForm.type" name="type" required="required">
                        <option value=""></option><option v-for="dict in costTypeOnWay" v-if="dict.dictValue != '18' && dict.dictValue != '27'" :key="dict.dictValue" :value="dict.dictValue">{{dict.dictLabel}}</option>
                    </select>
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 调整后金额：</label>
                <div class="flex_right" style="width: 270px">
                    <div class="input-group">
                        <!--<div class="form-control amount" style="border-style: dashed">{{plus(curInvoice.otherFeeBak, editForm.amount).toFixed(2)}}</div>-->
                        <input class="form-control" required :value="plus(curInvoice.otherFeeBak, editForm.amount)"
                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);diff(this, 'otherFeeBak', 'amount')">
                        <div class="input-group-addon">元</div>
                    </div>
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 三方类型：</label>
                <div class="flex_right" style="width: 270px">
                    <select class="form-control" v-model="editForm.singleFlag" name="singleFlag" id="singleFlag" required="required" onchange="statusChange()">
                        <option value="0">单笔</option>
                        <option value="1">月结</option>
                    </select>
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span class="bank" style="color: red">*</span> 收款银行：</label>
                <div class="flex_right" style="width: 270px">
                    <input name="recBank" v-model="editForm.recBank" id="recBank" placeholder="" class="form-control valid"
                           type="text" required>
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span  class="bank" style="color: red">*</span> 收款人：</label>
                <div class="flex_right" style="width: 270px">
                    <input name="recBank" v-model="editForm.recAccount" id="recAccount" placeholder="" class="form-control valid"
                           type="text" required>
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span   class="bank" style="color: red">*</span> 收款账号：</label>
                <div class="flex_right" style="width: 270px">
                    <input name="recBank" v-model="editForm.recCardNo" id="recCardNo" placeholder="" class="form-control valid"
                           type="text" required>
                </div>
            </div>
        </form>
    </div>
    <div id="editPayFreight" style="display: none">
        <form id="editForm4" v-if="editForm != null && curHandling == 'editPayFreight'" style="padding: 5px">
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 调整差额：</label>
                <div class="flex_right" style="width: 270px">
                    <input class="form-control" v-model="editForm.amount" name="amount" required oninput="$.numberUtil.onlyNumberTwoDecimal(this)" autocomplete="off" placeholder="增加金额请填正数，减少金额请填负数">
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 费用类型：</label>
                <div class="flex_right" style="width: 270px">
                    <select class="form-control" v-model="editForm.type" name="type" required="required">
                        <option value=""></option><option v-for="dict in costTypeFreight" v-if="dict.dictValue == '0' || dict.dictValue == '2' || dict.dictValue == '4'" :key="dict.dictValue" :value="dict.dictValue">{{dict.dictLabel}}</option>
                    </select>
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 调整后金额：</label>
                <div class="flex_right" style="width: 270px">
                    <div class="input-group">
                        <!--<div class="form-control amount" style="border-style: dashed">{{plus(curEntrust.frightAmountBak, editForm.amount).toFixed(2)}}</div>-->
                        <input class="form-control" required :value="plus(curEntrust.frightAmountBak, editForm.amount)"
                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);diff2(this, 'frightAmountBak', 'amount')">
                        <div class="input-group-addon">元</div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div id="editPayOnway" style="display: none">
        <form id="editForm5" v-if="editForm != null && curHandling == 'editPayOnway'" style="padding: 5px">
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 调整差额：</label>
                <div class="flex_right" style="width: 270px">
                    <input class="form-control" v-model="editForm.amount" name="amount" required oninput="$.numberUtil.onlyNumberTwoDecimal(this)" autocomplete="off" placeholder="增加金额请填正数，减少金额请填负数">
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 费用类型：</label>
                <div class="flex_right" style="width: 270px">
                    <select class="form-control" v-model="editForm.type" name="type" required="required">
                        <option value=""></option><option v-for="dict in costTypeOnWay" :key="dict.dictValue" :value="dict.dictValue">{{dict.dictLabel}}</option>
                    </select>
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 调整后金额：</label>
                <div class="flex_right" style="width: 270px">
                    <div class="input-group">
                        <!--<div class="form-control amount" style="border-style: dashed">{{plus(curEntrust.onWayAmountBak, editForm.amount).toFixed(2)}}</div>-->
                        <input class="form-control" required :value="plus(curEntrust.onWayAmountBak, editForm.amount)"
                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);diff2(this, 'onWayAmountBak', 'amount')">
                        <div class="input-group-addon">元</div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div id="editPayOil" style="display: none">
        <form id="editForm6" v-if="editForm != null && curHandling == 'editPayOil'" style="padding: 5px">
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 调整差额：</label>
                <div class="flex_right" style="width: 270px">
                    <input class="form-control" v-model="editForm.amount" name="amount" required oninput="$.numberUtil.onlyNumberTwoDecimal(this)" autocomplete="off" placeholder="增加金额请填正数，减少金额请填负数">
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 费用类型：</label>
                <div class="flex_right" style="width: 270px">
                    <select class="form-control" v-model="editForm.type" name="type" required="required">
                        <option value=""></option><option v-for="dict in costTypeFreight" v-if="dict.dictValue == '1' || dict.dictValue == '3' || dict.dictValue == '5'" :key="dict.dictValue" :value="dict.dictValue">{{dict.dictLabel}}</option>
                    </select>
                </div>
            </div>
            <div class="flex">
                <label class="flex_left"><span style="color: red">*</span> 调整后金额：</label>
                <div class="flex_right" style="width: 270px">
                    <div class="input-group">
                        <!--<div class="form-control amount" style="border-style: dashed">{{plus(curEntrust.oilAmountBak, editForm.amount).toFixed(2)}}</div>-->
                        <input class="form-control" required :value="plus(curEntrust.oilAmountBak, editForm.amount)"
                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);diff2(this, 'oilAmountBak', 'amount')">
                        <div class="input-group-addon">元</div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:src="@{/js/vue.min.js}"></script>
<script th:src="@{/element-ui@2.15.13/lib/index.js}"></script>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/fileinput.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/locales/zh.js}"></script>
<script th:src="@{/js/xlsx.full.min.js}"></script>
<script type="text/javascript" th:inline="javascript">
    if ($.validator) {
        $.validator.prototype.elements = function () {
            var validator = this,
                rulesCache = {};
            return $([]).add(this.currentForm.elements)
                .filter(":input")
                .not(":submit, :reset, :image, [disabled]")
                .not(this.settings.ignore)
                .filter(function () {
                    var elementIdentification = this.id || this.name;
                    !elementIdentification && validator.settings.debug && window.console && console.error("%o has no id nor name assigned", this);
                    if (elementIdentification in rulesCache || !validator.objectLength($(this).rules()))
                        return false;
                    rulesCache[elementIdentification] = true;
                    return true;
                });
        };
    }
    var prefix = ctx + "batch-adjust";

    function plus(n1, n2) { // +运算
        let x = new BigNumber(n1);
        if (x.isNaN()) {
            x = new BigNumber(0)
        }
        let y = new BigNumber(n2);
        if (y.isNaN()) {
            y = new BigNumber(0)
        }
        return x.dp(10).plus(y.dp(10)).dp(10).toNumber()
    }
    function minus(n1, n2) { // -运算
        let x = new BigNumber(n1);
        if (x.isNaN()) {
            x = new BigNumber(0)
        }
        let y = new BigNumber(n2);
        if (y.isNaN()) {
            y = new BigNumber(0)
        }
        return x.dp(10).minus(y.dp(10)).dp(10).toNumber()
    }
    function times(n1, n2) {
        return new BigNumber(n1).dp(10).times(new BigNumber(n2).dp(16)).toNumber()
    }
    function div(n1, n2, dp) { // 除运算（被除数，除数，保留小数）
        if (dp == undefined) {
            dp = 2;
        }
        return new BigNumber(n1).dp(10).div(new BigNumber(n2).dp(10)).dp(dp).toNumber()
    }

    function listToMap(list, keyField, valueField) {
        let r = {};
        for (let i = 0; i < list.length; i++) {
            r[list[i][keyField]] = list[i][valueField];
        }
        return r
    }

    function statusChange(){
        if($("#singleFlag").val() == 0){
            $("#recBank").attr("required","required");
            $("#recAccount").attr("required","required");
            $("#recCardNo").attr("required","required");
            $(".bank").attr("style","display:inline;color: red");
        }else{
            $("#recBank").removeAttr("required");
            $("#recAccount").removeAttr("required");
            $("#recCardNo").removeAttr("required");
            $(".bank").attr("style","display:none");
        }
    }

    const vue = new Vue({
        el: '#app',
        data() {
            return {
                invoiceList: [],
                billingMethod: listToMap([[${T(com.ruoyi.tms.constant.BillingMethod).getAllToMap()}]], 'value', 'context'),
                pricingMethod: listToMap([[${T(com.ruoyi.tms.constant.basic.CarrierProtocolPricingMethodEnum).toMap()}]], 'value', 'context'),
                billingType: [[${@dict.getType("billing_type")}]],
                oilTax: [[${@sysConfigServiceImpl.selectConfigByKey("net_profits_oil_tax")}]],
                costTypeOnWay: [[${@dict.getType("cost_type_on_way")}]], // 在途&三方
                costTypeFreight: [[${@dict.getType("cost_type_freight")}]], // 预付、回付、到付
                //editForm: null,
                //curHandling: null,
                curInvoice: null,
                curEntrust: null,
                oilRate: [[${@sysConfigServiceImpl.selectConfigByKey("oil_tax_rate")}]],
                cashRate: [[${@sysConfigServiceImpl.selectConfigByKey("cash_tax_rate")}]],
                revenueP: [[${@shiroUtils.getSubject().isPermitted('tms:revenue:view')}]],
                importRows: []
            }
        },
        created() {
            this.loadInvoice([[${ids}]])
        },
        computed: {
            batchAdjust () {
                let adjust = {netInOrigin:0,netInChange:0,netInAfter:0,
                    netOutOrigin:0,netOutChange:0,netOutAfter:0,
                    taxOrigin:0,taxChange:0,taxAfter:0,
                    netProfitsOrigin:0,netProfitsChange:0,netProfitsAfter:0}
                for (let i = 0; i < this.invoiceList.length; i++) {
                    let invoice = this.invoiceList[i];
                    let netInOrigin = this.netIn(invoice, true);
                    let netInAfter = this.netIn(invoice);
                    let netInChange = minus(netInAfter, netInOrigin)
                    this.$set(invoice, 'originNetIn', netInOrigin);
                    this.$set(invoice, 'afterNetIn', netInAfter);
                    this.$set(invoice, 'diffNetIn', netInChange);
                    adjust.netInOrigin = plus(adjust.netInOrigin, netInOrigin);
                    adjust.netInAfter = plus(adjust.netInAfter, netInAfter);
                    adjust.netInChange = plus(adjust.netInChange, netInChange);

                    let netOutOrigin = this.netOut(invoice, true);
                    let netOutAfter = this.netOut(invoice);
                    let netOutChange = minus(netOutAfter, netOutOrigin)
                    this.$set(invoice, 'originNetOut', netOutOrigin);
                    this.$set(invoice, 'afterNetOut', netOutAfter);
                    this.$set(invoice, 'diffNetOut', netOutChange);
                    adjust.netOutOrigin = plus(adjust.netOutOrigin, netOutOrigin);
                    adjust.netOutAfter = plus(adjust.netOutAfter, netOutAfter);
                    adjust.netOutChange = plus(adjust.netOutChange, netOutChange);

                    let originNetPlatFee = this.ptf(invoice, true)
                    let afterNetPlatFee = this.ptf(invoice, false)
                    let diffNetPlatFee = minus(afterNetPlatFee, originNetPlatFee)
                    this.$set(invoice, 'originNetPlatFee', originNetPlatFee);
                    this.$set(invoice, 'afterNetPlatFee', afterNetPlatFee);
                    this.$set(invoice, 'diffNetPlatFee', diffNetPlatFee);

                    let taxOrigin = this.tax(invoice, true);
                    let taxAfter = this.tax(invoice);
                    let taxChange = minus(taxAfter, taxOrigin);
                    this.$set(invoice, 'originTax', taxOrigin);
                    this.$set(invoice, 'afterTax', taxAfter);
                    this.$set(invoice, 'diffTax', taxChange);
                    adjust.taxOrigin = plus(adjust.taxOrigin, taxOrigin)
                    adjust.taxChange = plus(adjust.taxChange, taxChange)
                    adjust.taxAfter = plus(adjust.taxAfter, taxAfter)

                    let originNetProfits = minus(minus(netInOrigin, netOutOrigin), taxOrigin);
                    let afterNetProfits = minus(minus(netInAfter, netOutAfter), taxAfter);
                    let diffNetProfits = minus(afterNetProfits, originNetProfits)
                    this.$set(invoice, 'originNetProfits', originNetProfits);
                    this.$set(invoice, 'afterNetProfits', afterNetProfits);
                    this.$set(invoice, 'diffNetProfits', diffNetProfits);

                }
                adjust.netProfitsOrigin = minus(minus(adjust.netInOrigin, adjust.netOutOrigin), adjust.taxOrigin);
                adjust.netProfitsAfter = minus(minus(adjust.netInAfter, adjust.netOutAfter), adjust.taxAfter);
                adjust.netProfitsChange = minus(adjust.netProfitsAfter, adjust.netProfitsOrigin)
                return adjust;
            },
            customerId () {
                if (this.invoiceList.length > 0) {
                    return this.invoiceList[0].customerId;
                }
                return null
            },
            billingTypeLabel() {
                return listToMap(this.billingType, 'dictValue', 'dictLabel')
            },
            billingTypeTax() {
                return listToMap(this.billingType, 'dictValue', 'numVal1')
            },
            /*changed() {
                let r = [];
                let netIn = 0; // 未税总收入变化
                let netOut = 0; // 未税总成本变化

                for (let i = 0; i < this.invoiceList.length; i++) {
                    let invoice = this.invoiceList[i];
                    let netInOrigin = this.netIn(invoice, true);
                    let netInAfter = this.netIn(invoice);
                    netIn = plus(netIn, minus(netInAfter, netInOrigin));
                    let netOutOrigin = this.netOut(invoice, true);
                    let netOutAfter = this.netOut(invoice);
                    netOut = plus(netOut, minus(netOutAfter, netOutOrigin));
                }
                if (netIn != 0) {
                    r.push({type: netIn > 0 ? 'success' : 'danger', text:["未税总应收：", netIn > 0 ? '+' : '', netIn.toFixed(2)].join('')});
                }
                if (netOut != 0) {
                    r.push({type: netOut > 0 ? 'danger' : 'success', text:["未税总成本：", netOut > 0 ? '+' : '', netOut.toFixed(2)].join('')});
                }
                let netProfits = minus(netIn, netOut); // 未税利润变化
                if (netProfits != 0) {
                    r.push({type: netProfits > 0 ? 'success' : 'danger', text:["未税利润：", netProfits > 0 ? '+' : '', netProfits.toFixed(2)].join('')});
                }
                return r;
            }*/
        },
        methods: {
            plus: plus,
            loadInvoice(ids) {
                if (!ids) {
                    return;
                }
                $.ajax({
                    url: prefix + "/invoice-info",
                    data: "ids=" + ids,
                    type: 'post',
                    success: function (result) {
                        if (result.code == 0) {
                            for (let i = 0; i < result.data.length; i++) {
                                let invoice = result.data[i];
                                // 备份原始金额
                                invoice.freightAmountBak = invoice.freightAmount;
                                invoice.onWayAmountBak = invoice.onWayAmount;
                                invoice.otherFeeBak = invoice.otherFee;
                                for (let j = 0; j < invoice.adjustedEntrustList.length; j++) {
                                    let entrust = invoice.adjustedEntrustList[j];
                                    entrust.frightAmountBak = entrust.frightAmount;
                                    entrust.onWayAmountBak = entrust.onWayAmount;
                                    entrust.oilAmountBak = entrust.oilAmount;
                                }
                                vue.invoiceList.push(invoice)
                            }
                        } else {
                            $.modal.alertError(result.msg)
                        }
                    }
                })
            },
            nwv(num,weight,volume,showZero) {
                let nwv = [];
                (num||showZero) && nwv.push(num + '件');
                (weight||showZero) && nwv.push(weight + '吨');
                (volume||showZero) && nwv.push(volume + 'm³');
                return nwv.join(' | ');
            },
            ptf(invoice, origin) { // 平台费
                if (invoice.platRate != null) {
                    return times(plus(invoice[origin?'freightAmountBak':'freightAmount'], invoice[origin?'onWayAmountBak':'onWayAmount']), invoice.platRate).toFixed(2);
                }
                return "0";
            },
            netIn(invoice, origin) { // 未税总收入
                // (应收运费 + 应收在途) / 发货单开票税率
                return plus(invoice[origin?'freightAmountBak':'freightAmount'], invoice[origin?'onWayAmountBak':'onWayAmount']);
            },
            netOut(invoice, origin) {
                // 三方 + 平台费 + 总应付
                let sum = invoice[origin?'otherFeeBak':'otherFee']; // 三方不计税
                sum = plus(sum, this.ptf(invoice, origin));
                for (let i = 0; i < invoice.adjustedEntrustList.length; i++) {
                    let e = invoice.adjustedEntrustList[i];
                    sum = plus(sum, e[origin?'frightAmountBak':'frightAmount']);
                    sum = plus(sum, e[origin?'onWayAmountBak':'onWayAmount']);
                    sum = plus(sum, e[origin?'oilAmountBak':'oilAmount']);
                }
                return sum.toFixed(2);
            },
            tax(invoice, origin) {
                let tax = invoice.sfTax;
                if (!origin && invoice.diffOther != null) {
                    // 发货单不开票不计算税金
                    if (invoice.billingType != '6') {
                        tax = plus(tax, times(invoice.diffOther, this.cashRate))
                    }
                }
                for (let i = 0; i < invoice.adjustedEntrustList.length; i++) {
                    let e = invoice.adjustedEntrustList[i];
                    tax = plus(tax, e.yfTax);
                    if (!origin) {
                        // 发货单不开票不计算税金
                        if (invoice.billingType != '6') {
                            if (e.diffOil != null) {
                                tax = plus(tax, times(e.diffOil, this.oilRate));
                            }
                            if (e.billingType == '6') { // 运单不开票时计算税金成本
                                if (e.diffFreight != null) {
                                    tax = plus(tax, times(e.diffFreight, this.cashRate));
                                }
                                if (e.diffOnway != null) {
                                    tax = plus(tax, times(e.diffOnway, this.cashRate));
                                }
                            }
                        }
                    }
                }
                return tax.toFixed(2);
            },
            profits(invoice, origin) {
                return minus(minus(this.netIn(invoice, origin), this.netOut(invoice, origin)),this.tax(invoice, origin)).toFixed(2);
            },
            profitsRate(invoice, origin) {
                return times(div(this.profits(invoice, origin), this.netIn(invoice, origin), 10), 100).toFixed(2);
            },
            highLightStyle(numBak, num) {
                let stl = {}
                if (numBak < num) {
                    // stl['color'] = 'green';
                    stl['color'] = '#409EFF';
                    stl['font-weight'] = 'bold';
                } else if (numBak > num) {
                    // stl['color'] = 'red';
                    stl['color'] = '#409EFF';
                    stl['font-weight'] = 'bold';
                } else {
                    //stl['color'] = '#aaa'
                }
                return stl;
            },
            rmvInvoice(idx) {
                $.modal.confirm("确定移除该发货单吗？", () => {
                    this.invoiceList.splice(idx, 1)
                })
            },
            batchImport() {
                let that = this;
                let curFile = null;
                that.importRows = [];
                layer.open({
                    type: 1,
                    area: ['480px', '440px'],
                    fix: false,
                    //不固定
                    maxmin: false,
                    shade: 0.3,
                    title: '批量调账导入',
                    skin: '',
                    zIndex: 2,
                    content: `<div style="padding: 5px 15px;">
                        <div style="margin-top: 5px;color: #22509c">
                        模板：<a href="/file/batch-adjust-temp.xlsx" target="_blank">点击下载</a>
                        <br><span style="color:#5aac34">当前支持应收运费、应收在途的批量导入</span></div>
                        <div id="uploadArea" style="margin-top:5px;border: 1px #dadada dashed;background-color: #f0f9f3;height: 80px;display: flex;justify-content: center;align-items: center;flex-direction:column">
                            <span>将Excel拖拽到此处或单击选择</span>
                            <span id="fileResult" style="color:darkgoldenrod"></span>
                        </div>
                        <div id="fileMsg" style="margin-top: 5px"></div>
                    </div>`,
                    btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    end: function () {

                    },
                    success: function (layero, index) {
                        const uploadArea = document.getElementById('uploadArea');

                        // 阻止默认行为
                        uploadArea.addEventListener('dragover', (e) => {
                            e.preventDefault();
                        });

                        function readXls(file) {
                            curFile = file;
                            err = [];
                            $('#fileMsg').text('');
                            //console.log(file)
                            $('#fileResult').text("已选择：" + file.name);
                            var fileReader = new FileReader();
                            fileReader.onload = function (ev) {
                                try {
                                    var data = ev.target.result
                                    var workbook = XLSX.read(data, {
                                        type: 'binary'
                                    }) // 以二进制流方式读取得到整份excel表格对象
                                } catch (e) {
                                    $.modal.msgError('文件类型不正确:' + e.message);
                                    return;
                                }

                                var sheetNames = workbook.SheetNames; // 工作表名称集合
                                var sht = workbook.Sheets[sheetNames[0]]; // 这里我们只读取第一张sheet
                                const array2D = XLSX.utils.sheet_to_json(sht, {header: ['fhd','diffFreight','diffOnway','onwayType','onwayMemo'], raw: false});
                                //console.log(array2D)

                                let rows = [];
                                for (let i = 1; i < array2D.length; i++) {
                                    //console.log(array2D[i])
                                    var {fhd,diffFreight,diffOnway,onwayType,onwayMemo} = array2D[i];
                                    if (!fhd) { // 没有发货单号的认为空行
                                        continue;
                                    }
                                    if (diffOnway || onwayType || onwayMemo) {
                                        if (!diffOnway) {
                                            err.push("第"+(array2D[i]['__rowNum__']+1)+"行有在途类型、备注时，请补充应收在途调整额；")
                                        }
                                        if (!onwayType) {
                                            err.push("第"+(array2D[i]['__rowNum__']+1)+"行有在途调整额、备注时，请补充应收在途类型；")
                                        }
                                    }
                                    if (onwayType) {
                                        var onwayOption = costTypeOnWayDict.find(item => item.dictLabel == onwayType);
                                        if (!onwayOption) {
                                            err.push("第"+(array2D[i]['__rowNum__']+1)+"行应收在途类型未匹配到系统字典；")
                                        } else {
                                            array2D[i]['onwayType'] = onwayOption.dictValue;
                                        }
                                    }
                                    if (!diffFreight && !diffOnway) {
                                        err.push("第"+(array2D[i]['__rowNum__']+1)+"行应收运费、应收在途至少录入一个调整额；")
                                    }
                                    rows.push(array2D[i]);
                                }

                                $('#fileMsg').html('解析到' + rows.length + "条数据");
                                if (err.length > 0) {
                                    $('#fileMsg').append("<br>").append(err.join('<br>'));
                                    return;
                                }

                                that.importRows = rows;
                            };
                            // 以二进制方式打开文件
                            fileReader.readAsBinaryString(file);
                        }

                        uploadArea.addEventListener('drop', (e) => {
                            e.preventDefault();
                            const files = e.dataTransfer.files;
                            if (files.length > 0) {
                                readXls(files[0]);
                            }
                        });

                        // 点击上传
                        uploadArea.addEventListener('click', () => {
                            const input = document.createElement('input');
                            input.type = 'file';
                            input.accept = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; // 可以上传的文件类型
                            input.onchange = (e) => {
                                const file = e.target.files[0];
                                if (file) {
                                    readXls(file);
                                }
                            };
                            input.click();
                        });
                    },
                    btn1: function (index, layero) {
                        if (that.importRows.length == 0) {
                            $.modal.msgWarning("未找到可导入数据");
                            return
                        }
                        $.ajax({
                            url: prefix + "/importXlsx",
                            data: JSON.stringify(that.importRows),
                            type: 'post',
                            contentType: 'application/json',
                            success: function (res) {
                                if (res.code == 0) {
                                    let exist = [];
                                    for (let i = 0; i < vue.invoiceList.length; i++) {
                                        let id = vue.invoiceList[i].invoiceId;
                                        for (let j = 0; j < res.data.length; j++) {
                                            if (res.data[j].invoiceId == id) {
                                                res.data.splice(j, 1); // 删掉从后台过来的重复发货单
                                                j--
                                                exist.push(vue.invoiceList[i].vbillno);
                                            }
                                        }

                                    }
                                    if (exist.length > 0) {
                                        $.modal.alertWarning("已忽略重复发货单：" + exist.join('、'));
                                    }

                                    for (let i = 0; i < res.data.length; i++) {
                                        let invoice = res.data[i];
                                        var excelRowInvoice = that.importRows.find(itm => itm.fhd == res.data[i].vbillno);
                                        // 备份原始金额
                                        invoice.freightAmountBak = invoice.freightAmount;
                                        invoice.diffFreight = excelRowInvoice.diffFreight;
                                        invoice.freightAmount = that.plus(invoice.freightAmountBak, excelRowInvoice.diffFreight);
                                        invoice.onWayAmountBak = invoice.onWayAmount;
                                        invoice.diffOnway = excelRowInvoice.diffOnway;
                                        invoice.onwayType = excelRowInvoice.onwayType;
                                        invoice.onwayMemo = excelRowInvoice.onwayMemo;
                                        invoice.onWayAmount = that.plus(invoice.onWayAmountBak, excelRowInvoice.diffOnway);
                                        invoice.otherFeeBak = invoice.otherFee;
                                        for (let j = 0; j < invoice.adjustedEntrustList.length; j++) {
                                            let entrust = invoice.adjustedEntrustList[j];
                                            entrust.frightAmountBak = entrust.frightAmount;
                                            entrust.onWayAmountBak = entrust.onWayAmount;
                                            entrust.oilAmountBak = entrust.oilAmount;
                                        }
                                        vue.invoiceList.push(invoice)
                                    }
                                    layer.close(index)
                                } else {
                                    $.modal.msgWarning(res.msg);
                                }
                            }
                        })
                    }
                })
            },
            chooseInvoice() {
                let that = this;
                layer.open({
                    type: 2,
                    area: ['1000px', '730px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    title: "追加发货单",
                    skin: null,
                    content: prefix + `/choose-invoice?customerId=${that.customerId||''}`,
                    btn: ['确定', '取消'],
                    // 弹层外区域关闭
                    shadeClose: true,
                    zIndex: 3,
                    yes: function (index, layero) {
                        //获取整行
                        var customerIds = layero.find('iframe')[0].contentWindow.getCustomerIds();
                        if (customerIds.length > 1) {
                            ELEMENT.Message({message:"不同客户请分开调账",type:'warning'});
                            return;
                        }
                        var invoiceIds = layero.find('iframe')[0].contentWindow.getChecked();
                        if (invoiceIds.length === 0) {
                            ELEMENT.Message({message:"请选择要追加的发货单",type:'warning'});
                            return;
                        }
                        let exist = [];
                        for (let i = 0; i < vue.invoiceList.length; i++) {
                            let id = vue.invoiceList[i].invoiceId;
                            let idx = invoiceIds.indexOf(id);
                            if (idx >= 0) {
                                invoiceIds.splice(idx, 1);
                                exist.push(vue.invoiceList[i].vbillno);
                            }
                        }
                        if (exist.length > 0) {
                            $.modal.alertWarning("已忽略重复发货单：" + exist.join('、'));
                        }
                        vue.loadInvoice(invoiceIds.join(','))
                        layer.close(index);
                    }
                });
            },
            editRecvFreight(invoice) {
                if (invoice.complete == 0) {
                    $.modal.msgWarning("不允许调账：有未审批完成调账数据")
                    return;
                }
                this.curInvoice = invoice;
                vue2.curInvoice = invoice;
                vue2.editForm = {amount: invoice.diffFreight};
                vue2.curHandling = 'editRecvFreight'
                layer.open({
                    type: 1,
                    zIndex: 2,
                    area: ['360px', '200px'],
                    title: '调整应收运费',
                    shadeClose: false,
                    skin: '',
                    maxmin: false,
                    content: $('#editRecvFreight'),
                    btn: ['保存', '取消'],
                    end: function () {
                        vue.curInvoice = null;
                        vue2.curInvoice = null;
                        vue2.editForm = null;
                    },
                    yes: function(index, layero) {
                        if (!$.validate.form('editForm1')) {
                            return;
                        }
                        vue.curInvoice.diffFreight = vue2.editForm.amount;
                        vue.curInvoice.freightAmount = plus(vue.curInvoice.freightAmountBak, vue2.editForm.amount);
                        layer.close(index)
                    }
                })
            },
            editRecvOnway(invoice) {
                if (invoice.complete == 0) {
                    $.modal.msgWarning("不允许调账：有未审批完成调账数据")
                    return;
                }
                this.curInvoice = invoice;
                vue2.curInvoice = invoice;
                vue2.editForm = {amount: invoice.diffOnway, type: invoice.onwayType, memo: invoice.onwayMemo}
                vue2.curHandling = 'editRecvOnway'
                layer.open({
                    type: 1,
                    zIndex: 2,
                    area: ['360px', '290px'],
                    title: '调整应收在途',
                    shadeClose: false,
                    skin: '',
                    maxmin: false,
                    content: $('#editRecvOnway'),
                    btn: ['保存', '取消'],
                    end: function () {
                        vue.curInvoice = null;
                        vue2.curInvoice = null;
                        vue2.editForm = null;
                    },
                    yes: function(index, layero) {
                        if (!$.validate.form('editForm2')) {
                            return;
                        }
                        vue.curInvoice.diffOnway = vue2.editForm.amount;
                        vue.curInvoice.onWayAmount = plus(vue.curInvoice.onWayAmountBak, vue2.editForm.amount);
                        vue.curInvoice.onwayType = vue2.editForm.type;
                        vue.curInvoice.onwayMemo = vue2.editForm.memo;
                        layer.close(index)
                    }
                })
            },
            editOther(invoice) {
                if (invoice.complete == 0) {
                    $.modal.msgWarning("不允许调账：有未审批完成调账数据")
                    return;
                }
                this.curInvoice = invoice;
                vue2.curInvoice = invoice;
                vue2.editForm = {amount: invoice.diffOther, type: invoice.otherType,
                    singleFlag:invoice.singleFlag,
                    recBank: invoice.recBank,
                    recAccount: invoice.recAccount,
                    recCardNo: invoice.recCardNo}
                vue2.curHandling = 'editOther'
                layer.open({
                    type: 1,
                    zIndex: 2,
                    area: ['360px', '400px'],
                    title: '调整三方费用',
                    shadeClose: false,
                    maxmin: false,
                    skin: '',
                    content: $('#editOther'),
                    btn: ['保存', '取消'],
                    end: function () {
                        vue.curInvoice = null;
                        vue2.curInvoice = null;
                        vue2.editForm = null;
                    },
                    yes: function(index, layero) {
                        if (!$.validate.form('editForm3')) {
                            return;
                        }
                        vue.curInvoice.diffOther = vue2.editForm.amount;
                        vue.curInvoice.otherFee = plus(vue.curInvoice.otherFeeBak, vue2.editForm.amount);
                        vue.curInvoice.otherType = vue2.editForm.type;
                        vue.curInvoice.singleFlag = vue2.editForm.singleFlag;
                        vue.curInvoice.recBank = vue2.editForm.recBank;
                        vue.curInvoice.recAccount = vue2.editForm.recAccount;
                        vue.curInvoice.recCardNo = vue2.editForm.recCardNo;
                        layer.close(index)
                    }
                })
            },
            editPayFright(entrust) {
                if (entrust.complete == 0) {
                    $.modal.msgWarning("不允许调账：有未审批完成调账数据")
                    return;
                }
                this.curEntrust = entrust;
                vue2.curEntrust = entrust;
                vue2.editForm = {amount: entrust.diffFreight, type: entrust.freightType || '4'};
                vue2.curHandling = 'editPayFreight'
                layer.open({
                    type: 1,
                    zIndex: 2,
                    area: ['360px', '250px'],
                    title: '调整应付现金运费',
                    shadeClose: false,
                    skin: '',
                    maxmin: false,
                    content: $('#editPayFreight'),
                    btn: ['保存', '取消'],
                    end: function () {
                        vue.curEntrust = null;
                        vue2.curEntrust = null;
                        vue2.editForm = null;
                    },
                    yes: function(index, layero) {
                        if (!$.validate.form('editForm4')) {
                            return;
                        }
                        vue.curEntrust.diffFreight = vue2.editForm.amount;
                        vue.curEntrust.frightAmount = plus(vue.curEntrust.frightAmountBak, vue2.editForm.amount);
                        vue.curEntrust.freightType = vue2.editForm.type;
                        layer.close(index)
                    }
                })
            },
            editPayOnway(entrust) {
                if (entrust.complete == 0) {
                    $.modal.msgWarning("不允许调账：有未审批完成调账数据")
                    return;
                }
                this.curEntrust = entrust;
                vue2.curEntrust = entrust;
                vue2.editForm = {amount: entrust.diffOnway, type: entrust.onwayType};
                vue2.curHandling = 'editPayOnway'
                layer.open({
                    type: 1,
                    zIndex: 2,
                    area: ['360px', '250px'],
                    title: '调整应付现金在途',
                    shadeClose: false,
                    maxmin: false,
                    skin: '',
                    content: $('#editPayOnway'),
                    btn: ['保存', '取消'],
                    end: function () {
                        vue.curEntrust = null;
                        vue2.curEntrust = null;
                        vue2.editForm = null;
                    },
                    yes: function(index, layero) {
                        if (!$.validate.form('editForm5')) {
                            return;
                        }
                        vue.curEntrust.diffOnway = vue2.editForm.amount;
                        vue.curEntrust.onWayAmount = plus(vue.curEntrust.onWayAmountBak, vue2.editForm.amount);
                        vue.curEntrust.onwayType = vue2.editForm.type;
                        layer.close(index)
                    }
                })
            },
            editPayOil(entrust) {
                if (entrust.complete == 0) {
                    $.modal.msgWarning("不允许调账：有未审批完成调账数据")
                    return;
                }
                this.curEntrust = entrust;
                vue2.curEntrust = entrust;
                vue2.editForm = {amount: entrust.diffOil, type: entrust.oilType || '5'};
                vue2.curHandling = 'editPayOil'
                layer.open({
                    type: 1,
                    zIndex: 2,
                    area: ['360px', '250px'],
                    title: '调整应付油卡',
                    shadeClose: false,
                    maxmin: false,
                    skin: '',
                    content: $('#editPayOil'),
                    btn: ['保存', '取消'],
                    end: function () {
                        vue.curEntrust = null;
                        vue2.curEntrust = null;
                        vue2.editForm = null;
                    },
                    yes: function(index, layero) {
                        if (!$.validate.form('editForm6')) {
                            return;
                        }
                        vue.curEntrust.diffOil = vue2.editForm.amount;
                        vue.curEntrust.oilAmount = plus(vue.curEntrust.oilAmountBak, vue2.editForm.amount);
                        vue.curEntrust.oilType = vue2.editForm.type;
                        layer.close(index)
                    }
                })
            }
        }
    });

    const costTypeOnWayDict = [[${@dict.getType("cost_type_on_way")}]];

    const vue2 = new Vue({
        el: '#app2',
        data () {
            return {
                costTypeOnWay: costTypeOnWayDict, // 在途&三方
                costTypeFreight: [[${@dict.getType("cost_type_freight")}]], // 预付、回付、到付
                editForm: null,
                curHandling: null,
                curInvoice: null,
                curEntrust: null
            }
        },
        methods: {
            plus,minus
        }
    })

    function diff(e, f1, f2) {
        vue2.$set(vue2.editForm, f2, minus(e.value, vue2.curInvoice[f1]))
    }
    function diff2(e, f1, f2) {
        vue2.$set(vue2.editForm, f2, minus(e.value, vue2.curEntrust[f1]))
    }

    function submitHandler() {
        if (vue.invoiceList.length == 0) {
            $.modal.msgWarning("请追加对应的发货单！");
            return;
        }
        // vue.batchAdjust.netInChange == 0 && vue.batchAdjust.netOutChange == 0，单纯判断总成本应收变化为0不能知道是否调账
        if (!$.validate.form('form-add')) {
            //$(":visible.error").eq(0)[0].scrollIntoView({ block: "nearest" })
            //console.log($(":visible.error").eq(0).offset())
            window.scroll(0, $(":visible.error").eq(0).offset().top)
            return;
        }
        $.modal.confirm("确定提交审批吗？", function() {
            let param = {
                memo: $('#memo').val().trim(),
                invoiceList: vue.invoiceList,
                fjTidList: $('[name=fjTidList]').val() ? $('[name=fjTidList]').val().split(',') : []
            }
            $.operate.saveTabJson(prefix + "/saveBatchAdjust", param, function(result){

            });
        })
    }

    $(function(){
        let uploadCache = {};
        let option = {
            theme: "explorer-fa5", //主题
            language: 'zh',
            uploadUrl: ctx + "common/uploadBatch",  //上传的地址
            //deleteUrl: ctx + "common/deleteImage",
            uploadExtraData: {key: "fj"},   //上传id，传入后台的参数
            deleteExtraData: {key: 'id'},
            // extra" {key: ''}, // 上面两个一致则可使用该字段？
            enctype: 'multipart/form-data',
            //allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
            initialPreviewAsData: true,
            overwriteInitial: false,
            //initialPreviewConfig: [
            //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
            //],
            //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
            maxFileCount: 0, // 0:不限制上传数
            showUpload: false,  // 不显示上传按钮，选择后直接上传
            //previewClass:"uploadPreview",
            minFileSize: 5, // 5KB
            previewFileIcon: '<i class="fa fa-file"></i>',
            //allowedPreviewTypes: ['image'],
            showClose: false,  //是否显示右上角叉按钮
            showUpload: false, //是否显示下方上传按钮
            showRemove: false, // 是否显示下方移除按钮
            //autoReplace: true,
            //showPreview: false,//是否显示预览(false=只剩按钮)
            showCaption: false,//底部上传按钮左侧文本
            uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
            fileActionSettings: {
                showUpload: false,		//每个文件的上传按钮
                showDrag: false,
                //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
            },
        }
        $("#fj").fileinput(option).on("filebatchselected", function (e, files) {
            $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
        }).on("fileuploaded", function (event, data, previewId, index) {
            //单个上传成功事件
            //console.log("fileuploaded", event, data, previewId, index)
            var code = data.response.code;
            if (code !== 0) {
                $.modal.closeLoading();
                $.modal.alertError("上传失败：" + data.response.msg);
                return;
            }
            uploadCache[previewId] = data.response.tid;
            let ids = [];
            for(let k in uploadCache) {
                ids.push(uploadCache[k])
            }
            $('[name=fjTidList]').val(ids.join(','));
        }).on('filesuccessremove', function (event, previewId, index) {
            //上传后删除事件
            console.log("filesuccessremove", event, previewId, index)
            //delete cache[previewId] //bug
            //fileArr.splice(index, 1) //bug
            //$(this).fileinput('clear');
            //$('[name="' + hideName + '"]').val('')
            var tid = uploadCache[previewId];
            $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                console.log(result)
            }, 'json')
            delete uploadCache[previewId]
            let ids = [];
            for(let k in uploadCache) {
                ids.push(uploadCache[k])
            }
            $('[name=fjTidList]').val(ids.join(','));
        });
    })
</script>
</body>
</html>