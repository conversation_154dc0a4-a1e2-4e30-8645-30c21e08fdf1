<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('转成熟车定金')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        [v-clock]{
            display: none;
        }
    </style>
</head>
<body>

<div class="container-div">
    <div id="app" v-clock>
        <form id="form">
        <div class="alert alert-info">
            <span v-if="dataType == 1">只支持【运费】<strong>回付现金</strong>(新建/已确认/已申请/已复核)转定金的操作！</span>
            <div v-else-if="dataType == 2">目前只支持新建、已确认状态下的定金转熟车定金的操作！
            <span style="color: red;font-weight: bold">请确认该定金已向承运商收取!!!</span></div>
        </div>
        <template v-if="dataType == 1">
            <div v-if="payDetailList.length == 0" class="alert alert-danger">
                当前运单没有符合可转定金的应付！
            </div>
            <div v-else class="panel panel-default">
                <div class="panel-heading" style="display: flex;justify-content: space-between">
                    <strong>所有回付现金</strong>
                    <span>[[${carrier.carrName}]] [['[' + ${carrier.familiar == 0 ? "非熟车" : "熟车"} + ']']]</span>
                </div>
                <table class="table">
                    <tr>
                        <th></th><th>单号</th><th>状态</th><th>金额</th><th>费用类型</th>
                    </tr>
                    <tr v-for="(item,idx) in payDetailList" :key="item.payDetailId" onclick="$(this).find('[flg]').click()">
                        <td><input type="checkbox" @click.stop flg v-model="ids" :value="item.payDetailId" v-if="item.lotSpLock == 0 && item.transFeeCount > 0 && item.freeType == '0' && [0,1,6,9].indexOf(item.vbillstatus) >= 0"></td>
                        <td>{{item.vbillno}}</td>
                        <td>{{enumContext(statusEnum, item.vbillstatus)}}</td>
                        <td><span v-text="item.transFeeCount" v-if="item.fromId == null"></span>
                            <input v-else class="form-control" style="display: inline-block;width:50px;" :value="item.transFeeCount"
                                   min="0.01" required name="transFeeCount" :id="'transFeeCount'+idx" :max="item.maxTransFeeView"
                                   oninput="$.numberUtil.onlyNumberTwoDecimal(this)" @click.stop
                                   @input="iptValue(idx, $event)" />
                            <a v-if="item.lotSpLock == 0 && item.transFeeCount > 0 && item.freeType == '0' && item.vbillstatus == 0 && item.fromId == null && !item.splited"
                               href="javascript:;" @click.stop="splitTo(idx)" class="label label-warning">拆</a>
                            <a v-if="item.fromId" class="label label-danger" @click.stop="del(idx)">删</a>
                        </td>
                        <td>{{item.freeType=='0'?'运费':'在途'}} - {{enumContext(costTypeEnum, item.costTypeFreight||'0')}}</td>
                    </tr>
                    <tr>
                        <td colspan="5">总勾选金额：{{totalAmount}}</td>
                    </tr>
                </table>
            </div>
        </template>
        <template v-else-if="dataType == 2">
            <div v-if="deposit.vbillstatus != 0 && deposit.vbillstatus != 3" class="alert alert-danger">
                当前定金状态非【新建】，不可转熟车定金
            </div>
            <div v-else class="panel panel-default">
                <div class="panel-heading" style="display: flex;justify-content: space-between">
                    <strong>所有定金</strong>
                    <span>[[${carrier.carrName}]] [['[' + ${carrier.familiar == 0 ? "非熟车" : "熟车"} + ']']]</span>
                </div>
                <table class="table">
                    <tr>
                        <th></th><th>单号</th><th>状态</th><th>金额</th><th>费用类型</th>
                    </tr>
                    <tr onclick="$(this).find('[flg]').click()">
                        <td><input type="checkbox" @click.stop flg v-model="ids" :value="deposit.id" v-if="deposit.amount > 0 && [0,3].indexOf(deposit.vbillstatus) >= 0"></td>
                        <td>{{deposit.vbillno}}</td>
                        <td>{{enumContext(statusEnum, deposit.vbillstatus)}}</td>
                        <td>{{deposit.ungotAmount}}</td>
                        <td>运单定金</td>
                    </tr>
                    <tr>
                        <td colspan="5">总勾选金额：{{totalAmount}}</td>
                    </tr>
                </table>
            </div>
        </template>
        </form>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:src="@{/js/vue.min.js}"></script>
<script th:src="@{/js/bignumber.min.js}"></script>
<script type="text/javascript" th:inline="javascript">
    var prefix = ctx + "new-deposit";
    let vue = new Vue({
        el: '#app',
        data () {
            return {
                dataType: [[${dataType}]],
                payDetailList: [[${payDetailList}]],
                deposit: [[${deposit}]],
                ids: [],
                statusEnum: [[${statusEnum}]],
                costTypeEnum: [[${costTypeEnum}]]
            }
        },
        computed: {
            totalAmount() {
                let total = new BigNumber(0);
                if (this.dataType == 2) {
                    if (this.ids.indexOf(this.deposit.id) >= 0) {
                        total = total.plus(this.deposit.ungotAmount);
                    }
                } else if (this.dataType == 1) {
                    for (let i = 0; i < this.payDetailList.length; i++) {
                        if (this.ids.indexOf(this.payDetailList[i].payDetailId) >= 0) {
                            total = total.plus(this.payDetailList[i].transFeeCount);
                        }
                    }
                }
                return total.toNumber().toFixed(2);
            }
        },
        methods: {
            submit() {
                if (!$("#form").validate().form()) {
                    return;
                }
                if (this.ids.length == 0) {
                    $.modal.msgWarning("未选择转定金的数据！");
                    return
                }
                $.modal.confirm("确认提交吗？", () => {
                    let data = {};
                    data.carrierId = [[${carrier.carrierId}]];
                    data.dataType = this.dataType;
                    data.ids = this.ids.join(',');
                    if (this.dataType == 1) {
                        let payArray = [];
                        for (let j = 0; j < this.payDetailList.length; j++) {
                            if (this.ids.indexOf(this.payDetailList[j].payDetailId) >= 0 || this.payDetailList[j].splited || this.payDetailList[j].fromId) {
                                let {payDetailId, transFeeCount, fromId, vbillno, splited} = this.payDetailList[j];
                                payArray.push({payDetailId:fromId?null:payDetailId, transFeeCount, fromId, vbillno, splited, choosed: this.ids.indexOf(this.payDetailList[j].payDetailId) >= 0});
                            }
                        }
                        data.payArrayJson = JSON.stringify(payArray);
                    }
                    data.totalAmount = this.totalAmount;
                    $.operate.save(prefix + "/doTurnDeposit", data);
                })
            },
            enumContext(enumList, value) {
                return $.table.selectEnumContext(enumList, value);
            },
            splitTo(idx) {
                let row = this.payDetailList[idx];
                let newRow = JSON.parse(JSON.stringify(row));
                row.splited = true;
                row.originTransFeeCount = row.transFeeCount;
                newRow.fromId = newRow.payDetailId;
                let plus = 1;
                var newVbillno = row.vbillno + '-' + plus;
                for (let j = 0; j < this.payDetailList.length; j++) {
                    if (this.payDetailList[j].vbillno == newVbillno) {
                        newVbillno = row.vbillno + '-' + (++plus);
                        j = -1;
                    }
                }
                newRow.vbillno = newVbillno;
                newRow.payDetailId = newRow.payDetailId + "-" + plus;
                newRow.maxTransFeeCount = newRow.transFeeCount;
                newRow.maxTransFeeView = new BigNumber(newRow.transFeeCount).minus(0.01).toNumber();
                newRow.transFeeCount = null;
                this.payDetailList.push(newRow);
                let i = this.ids.indexOf(row.payDetailId);
                if (i >= 0) {
                    this.ids.splice(i, 1);
                }
            },
            iptValue(idx, event) {
                let row = this.payDetailList[idx];
                let v = event.target.value;
                if (v > row.maxTransFeeCount) {
                    v = row.maxTransFeeCount;
                    event.target.value = v;
                }
                row.transFeeCount = v;
                for (let i = 0; i < this.payDetailList.length; i++) {
                    if (this.payDetailList[i].payDetailId == row.fromId) {
                        this.payDetailList[i].transFeeCount = new BigNumber(this.payDetailList[i].originTransFeeCount).minus(v||0).toNumber();
                        break;
                    }
                }
            },
            del(idx) {
                let row = this.payDetailList[idx];
                for (let i = 0; i < this.payDetailList.length; i++) {
                    if (this.payDetailList[i].payDetailId == row.fromId) {
                        let r = this.payDetailList[i];
                        r.transFeeCount = r.originTransFeeCount;
                        delete r.splited;
                        break;
                    }
                }
                this.payDetailList.splice(idx, 1);
            }
        }
    })
    function submitHandler(index, layero) {
        vue.submit();
    }

    $(function(){
        if ($("[flg]").length == 1) {
            $("[flg]").click();
        }
    });
</script>
</body>
</html>