<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('定金')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.css}" rel="stylesheet"/>
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .flex {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .flex_left {
        width: 100px;
        line-height: 26px;
        text-align: right;
    }

    .flex_right {
        flex: 1;
        width: 0;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <ul class="nav nav-tabs mt10" role="tablist">
        <li role="presentation" class="active"><a href="#home" onclick="checkTap(0)" aria-controls="home" role="tab" data-toggle="tab">熟车</a></li>
        <li role="presentation"><a href="#profile" onclick="checkTap(1)" aria-controls="profile" role="tab" data-toggle="tab">非熟车</a></li>
    </ul>

    <div class="tab-content">
        <div role="tabpanel" class="tab-pane active" id="home">
            <div class="col-sm-12 search-collapse">
                <form id="familiarCar-form" class="form-horizontal">
                    <div class="row no-gutter">
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input id="dateLimit" type="text" class=" form-control"
                                           placeholder="创建时间段" autocomplete="off">
                                    <input id="startDate" name="startDate" value="" type="hidden">
                                    <input id="endDate" name="endDate" value="" type="hidden">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input name="regUserName" id="regUserName" placeholder="创建人" class="form-control" type="text" maxlength="20" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input name="carrName" id="carrName" placeholder="承运商" class="form-control" type="text" maxlength="20" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input name="packNo" placeholder="对账包号" class="form-control" type="text" maxlength="20" autocomplete="off">
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <select name="status" id="status" class="form-control valid"
                                            aria-invalid="false" data-none-selected-text="收款状态">
                                        <option value="">-- 收款状态 --</option>
                                        <option value="0">新建</option>
                                        <option value="2">加入对账包</option>
                                        <option value="1">已核销</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <select name="source" id="source" class="form-control valid"
                                            aria-invalid="false" data-none-selected-text="来源">
                                        <option value="">-- 来源 --</option>
                                        <option value="1">手动创建</option>
                                        <option value="2">运费转入</option>
                                        <option value="3">非熟车定金转入</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row no-gutter">
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input id="writeOffTimeDateLimit" type="text" class=" form-control"
                                           placeholder="收款核账时间段" autocomplete="off">
                                    <input id="writeOffTimeStart" name="writeOffTimeStart" value="" type="hidden">
                                    <input id="writeOffTimeEnd" name="writeOffTimeEnd" value="" type="hidden">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input name="writeOffUserName" id="writeOffUserName" placeholder="收款核账人" class="form-control" type="text" maxlength="20" autocomplete="off">
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2 col-sm-2">
                            <!--                        <label class="col-sm-4"></label>-->
                            <div class="form-group" style="text-align: left;">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre(0)"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre(0)"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>

                        </div>

                    </div>
                </form>
            </div>

            <div class="col-sm-12 select-table  ">
                <div class="btn-group-sm" id="familiarCar-toolbar" role="group">
                    <a class="btn btn-danger multiple" shiro:hasAnyPermissions="carrier:deposit:depositTabListRm" onclick="rmDataByIds(0)">
                        <i class="fa fa-times"></i> 批量删除
                    </a>
                    <a class="btn btn-warning"  onclick="exportAll(0)">
                        <i class="fa fa-download"></i> 导出
                    </a>
                    <a class="btn btn-primary multiple"
                       shiro:hasPermission="lotDeposit:package:create"
                       onclick="createPackage(0)">
                        <i class="fa fa-file-text-o"></i> 生成对账单
                    </a>

                    <a class="btn btn-primary multiple disabled " onclick="addPackage(0)"
                       shiro:hasPermission="lotDeposit:package:add">
                        <i class="fa fa-file-text-o"></i> 加入对账单
                    </a>

                </div>

                <table id="familiarCar-table"
                       class="table table-striped table-responsive table-bordered table-hover" >
                </table>
            </div>
        </div>
        <div role="tabpanel" class="tab-pane" id="profile">
            <div class="col-sm-12 search-collapse">
                <form id="unfamiliarCar-form" class="form-horizontal">
                    <div class="row no-gutter">
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input id="undateLimit" type="text" class=" form-control"
                                           placeholder="创建时间段" autocomplete="off">
                                    <input id="unstartDate" name="startDate" value="" type="hidden">
                                    <input id="unendDate" name="endDate" value="" type="hidden">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 col-sm-1">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input name="actualPayee" id="actualPayee" placeholder="实际收款人" class="form-control" type="text" maxlength="20" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 col-sm-1">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input name="lotRegUser" id="lotRegUser" placeholder="调度人" class="form-control" type="text" maxlength="20" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 col-sm-1">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input name="carrName" id="carrName" placeholder="承运商" class="form-control" type="text" maxlength="20" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 col-sm-1">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input name="packNo" placeholder="对账包号" class="form-control" type="text" maxlength="20" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <select name="way" class="form-control" th:with="type=${@dict.getType('deposit_way')}">
                                        <option value="">-- 来源 --</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <select name="vbillstatus" id="vbillstatus" class="form-control valid"
                                            aria-invalid="false" data-none-selected-text="状态">
                                        <option value="">-- 付款状态 --</option>
<!--                                        <option th:each="dict : ${payDetailStatusEnum}" th:text="${dict.context}"-->
<!--                                                th:value="${dict.value}"></option>-->
                                        <option value="0">新建</option>
                                        <option value="4">已核销</option>
                                        <option value="6">已申请</option>
                                        <option value="7">核销中</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <select id="recvStatus" class="form-control selectpicker"
                                            aria-invalid="false" data-none-selected-text="收款核账状态" multiple>
                                        <option value="0">未核账</option>
                                        <option value="2">加入对账包</option>
                                        <option value="1">已核账</option>
                                    </select>
                                    <input name="recvStatusSearch" id="recvStatusSearch" type="hidden">
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row no-gutter">
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input id="recvTimeDateLimit" type="text" class=" form-control"
                                           placeholder="收款核账时间段" autocomplete="off">
                                    <input id="recvTimeStart" name="recvTimeStart" value="" type="hidden">
                                    <input id="recvTimeEnd" name="recvTimeEnd" value="" type="hidden">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <div class="form-group flex">
                                <div class="col-sm-12">
                                    <input name="recvUserName" id="recvUserName" placeholder="收款核账人" class="form-control" type="text" maxlength="20" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-2">
                            <!--                        <label class="col-sm-4"></label>-->
                            <div class="form-group" style="text-align: left;">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre(1)"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre(1)"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>

                        </div>

                    </div>
                </form>
            </div>

            <div class="col-sm-12 select-table  ">
                <div class="btn-group-sm" id="unfamiliarCar-toolbar" role="group">
                    <a class="btn btn-danger multiple" shiro:hasAnyPermissions="carrier:deposit:depositTabListRm" onclick="rmDataByIds(1)">
                        <i class="fa fa-times"></i> 批量删除
                    </a>
                    <a class="btn btn-warning"  onclick="exportAll(1)">
                        <i class="fa fa-download"></i> 导出
                    </a>
                    <a class="btn btn-primary multiple"
                       shiro:hasPermission="lotDeposit:package:create"
                       onclick="createPackage(1)">
                        <i class="fa fa-file-text-o"></i> 生成对账单
                    </a>
                    <a class="btn btn-primary multiple disabled " onclick="addPackage(1)"
                       shiro:hasPermission="lotDeposit:package:add">
                        <i class="fa fa-file-text-o"></i> 加入对账单
                    </a>

                    <a class="btn btn-danger multiple disabled" onclick="batchCheck()" shiro:hasPermission="carrier:deposit:batchCheck">
                        <i class="fa fa-bars"></i> 批量核账
                    </a>
                    <a class="btn btn-success single disabled" onclick="checkRecord()" shiro:hasPermission="carrier:deposit:checkRecord">
                        <i class="fa fa-clock-o"></i> 核账记录
                    </a>
                </div>

                <table id="unfamiliarCar-table"
                       class="table table-striped table-responsive table-bordered table-hover" >
                </table>
            </div>
        </div>
    </div>
</div>
<div id="checkingPanel" style="display: none;padding: 15px 20px 0;">
    <form id="crudForm">
        <div class="flex">
            <span class="flex_left">总定金金额：</span>
            <span class="flex_right">
                <div style="line-height: 22px">￥{{form.totalAmount}}元</div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">核账备注：</span>
            <span class="flex_right">
                <textarea v-model="form.recvMemo" class="form-control" rows="5"></textarea>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left"><span class="fcff3">*</span> 核账凭证：</span>
            <span class="flex_right">
                <input type="file" name="recvTid" multiple>
            </span>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/bootstrap-table-export.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/export/tableExport.min.js}"></script>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/js/vue.min.js}"></script>
<th:block th:include="include :: bootstrap-fileinput553-css"/>
<th:block th:include="include :: bootstrap-fileinput553-js"/>
<script th:inline="javascript">
    var payDetailStatusEnum = [[${payDetailStatusEnum}]];
    var depositWay = [[${@dict.getType('deposit_way')}]];

    var $table
    $(function () {
        let familiarCarOptions = initFamiliarCarOptions()
        $.table.init(familiarCarOptions);

        $table = $('#familiarCar-table')

        initDate("dateLimit","startDate","endDate")
        initDate("undateLimit","unstartDate","unendDate")
        initDate("writeOffTimeDateLimit","writeOffTimeStart","writeOffTimeEnd")
        initDate("recvTimeDateLimit","recvTimeStart","recvTimeEnd")


        $('#recvStatus').on('changed.bs.select', function() {
            var selectedOptions = $(this).val(); // 获取选中的值
            $('#recvStatusSearch').val(selectedOptions == null ? '' : selectedOptions.join(',')); // 将选中的值用逗号分隔并设置到隐藏的输入字段
        });

    });


    //熟车
    function initFamiliarCarOptions() {
        return {
            id: "familiarCar-table",
            toolbar: "familiarCar-toolbar",
            formId: "familiarCar-form",
            url: `${ctx}new-deposit/familiarCarList`,
            showToggle:false,
            showColumns:false,
            showSearch:false,
            // pagination:false,
            // showRefresh:false,
            modalName: "定金",
            showExport: true,
            exportTypes:['excel'],
            exportDataType: 'all',
            exportOptions: {
                ignoreColumn: [0],
                fileName: '熟车定金',
            },
            // height: 560,
            uniqueId: "dtlId",
            clickToSelect: true,
            showFooter:true,
            onCheck: function (row,$element) {
                updateFooter(this);
            },
            onUncheck: function (row, $element) {
                updateFooter(this);
            },
            onCheckAll: function (rowsAfter) {
                updateFooter(this);
            },
            onUncheckAll: function () {
                updateFooter(this);
            },
            onPostBody: function () {
                getAmountCount(0);
            },

            columns:[
                {
                    checkbox: true,
                    // forceHide: true,
                    footerFormatter:function (){
                        return '合'
                    }
                },
                // {
                //     title: '操作',
                //     align: 'center',
                //     width: 20,
                //     switchable:false,
                //     formatter: function(value, row, index) {
                //         var actions = [];
                //         return actions.join('');
                //     }
                // },
                {
                    title: '承运商',
                    align: 'left',
                    field : 'carrName',
                    formatter: function (value, row, index) {
                        return `${row.carrName}<br/>${row.phone}`;
                    },
                    footerFormatter:function (){
                        return '总合计：<span id="zhj">¥0.00</span>'
                    }

                },
                {
                    title: '来源',
                    align: 'left',
                    field : 'source',
                    formatter: function (value, row, index) {
                        let html = '-'
                        if (value === 1) {
                            html = '手动创建';
                        }else if (value === 2) {
                            html = '运费转入';
                        }else if (value === 3) {
                            html = '非熟车定金转入';
                        }

                        return html;
                    },
                },
                {
                    title: '金额',
                    align: 'left',
                    field : 'amount',
                    formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {
                            style: 'currency',
                            currency: 'CNY'
                        })
                    },
                    footerFormatter:function (){
                        return '<div id="heji">¥0.00</div>'
                    }

                },
                {
                    title: '收款状态',
                    align: 'left',
                    field : 'status',
                    formatter: function (value, row, index) {

                        let status =''
                        let packNo = ''
                        if (row.status == 0) {
                            status = '<span class="label" style="margin-left: 2px;">新建</span>';
                        }else if (row.status == 1) {
                            status = '<span class="label label-success" style="margin-left: 2px;">已核账</span>';
                        }else if (row.status == 2) {
                            status = '<span class="label label-primary" style="margin-left: 2px;">加入对账单</span>';
                        }

                        if ([[${@permission.hasAnyPermi('lotDeposit:package:list')}]] != "hidden"
                            && (row.status == 1 || row.status == 2) && row.packId != null && row.packId != '') {
                            packNo = `<span class="label label-primary" title="${row.packNo}"
                                            style="margin-left: 2px;cursor:pointer;" onclick="openPack('${row.packNo}')">包</span>`;
                        }


                        return `${status}${packNo}`
                    }
                },
                {
                    title: '创建人',
                    align: 'left',
                    field : 'regUserId',
                    formatter: function (value, row, index) {
                        return `${row.regUserName}<br/>${row.regDate}`;
                    }
                },
                {
                    title: '收款核账人',
                    align: 'left',
                    field : 'writeOffUser',
                    formatter: function (value, row, index) {
                        let writeOffUserName = row.writeOffUserName === null ? '-' : row.writeOffUserName
                        let writeOffTime = row.writeOffTime === null ? '-' : row.writeOffTime

                        return `${writeOffUserName}<br/>${writeOffTime}`;
                    }
                },
            ]
        }

    }
    //非熟车
    function initUnfamiliarCarOptions() {
        return {
            id: "unfamiliarCar-table",
            toolbar: "unfamiliarCar-toolbar",
            formId: "unfamiliarCar-form",
            url: `${ctx}new-deposit/unfamiliarCarList`,
            showToggle:false,
            showColumns:false,
            showSearch:false,
            // pagination:false,
            // showRefresh:false,
            modalName: "定金",
            showExport: true,
            exportDataType: 'all',
            exportTypes:['excel'],
            exportOptions:{
                ignoreColumn: [0]
            },
            // height: 560,
            // stickyHeader: true,  // 启用固定表头功能
            // stickyHeaderOffsetY: 0,  // 可选，调整固定表头距离顶部的距离
            uniqueId: "id",
            clickToSelect: true,
            showFooter:true,
            onCheck: function (row,$element) {
                updateFooter(this);
            },
            onUncheck: function (row, $element) {
                updateFooter(this);
            },
            onCheckAll: function (rowsAfter) {
                updateFooter(this);
            },
            onUncheckAll: function () {
                updateFooter(this);
            },
            onPostBody: function () {
                getAmountCount(1);
            },

            columns:[
                {
                    checkbox: true,
                    footerFormatter:function (){
                        return '合'
                    }

                },
                // {
                //     title: '操作',
                //     align: 'center',
                //     width: 20,
                //     switchable:false,
                //     formatter: function(value, row, index) {
                //         var actions = [];
                //         return actions.join('');
                //     }
                // },
                {
                    title: '单据号',
                    align: 'left',
                    field : 'vbillno',
                    formatter: function (value, row, index) {
                        let labelClass = "";
                        if (row.vbillstatus == 0) {
                            labelClass = "label-default";
                        } else if (row.vbillstatus == 6) {
                            labelClass = "label-warning";
                        } else if (row.vbillstatus == 4) {
                            labelClass = "label-success";
                        } else if (row.vbillstatus == 7 || row.vbillstatus == 3) {
                            labelClass = "label-primary";
                        }
                        let vbillstatus =  '<span class="label ' + labelClass + '">' + $.table.selectEnumContext(payDetailStatusEnum, row.vbillstatus) + '</span>';


                        let recvStatus =''
                        let packNo = ''
                        if (row.recvStatus == 0) {
                            recvStatus = '<span class="label" style="margin-left: 2px;">待核账</span>';
                        }else if (row.recvStatus == 1) {
                            recvStatus = '<span class="label label-success" style="margin-left: 2px;">已核账</span>';
                        }else if (row.recvStatus == 2) {
                            recvStatus = '<span class="label label-primary" style="margin-left: 2px;">加入对账单</span>';
                        }

                        if ([[${@permission.hasAnyPermi('lotDeposit:package:list')}]] != "hidden"
                            && (row.recvStatus == 1 || row.recvStatus == 2) && row.packId != null && row.packId != '') {
                            packNo = `<span class="label label-primary" title="${row.packNo}"
                                            style="margin-left: 2px;cursor:pointer;" onclick="openPack('${row.packNo}')">包</span>`;
                        }

                        return `${value}<br/><div style="white-space: nowrap;">${vbillstatus}${recvStatus}${packNo}</div>`
                    },
                    footerFormatter:function (){
                        return '总合计：<span id="zhj">¥0.00</span>'
                    }

                },
                {
                    title: '承运商',
                    align: 'left',
                    field : 'carrName',
                    formatter: function (value, row, index) {
                        return `${row.carrName}<br/>${row.phone}`;
                    }
                },

                {
                    title: '金额',
                    align: 'left',
                    field : 'amount',
                    formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {
                            style: 'currency',
                            currency: 'CNY'
                        })
                    },
                    footerFormatter:function (){
                        return '<div id="heji">¥0.00</div>'
                    }
                },
                {
                    title: '来源',
                    align: 'left',
                    field : 'way',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(depositWay,value)
                    }
                },
                {
                    title: '实际收取人',
                    align: 'left',
                    field : 'actualPayee',
                    formatter: function (value, row, index) {
                        return value
                    }
                },
                // {
                //     title: '创建时间',
                //     align: 'left',
                //     field : 'regDate',
                //     formatter: function (value, row, index) {
                //         return value
                //     }
                // },
                {
                    title: '运单号',
                    align: 'left',
                    field : 'lotno',
                    formatter: function (value, row, index) {
                        let lotDelFlag = row.lotDelFlag === 1 ? `<span class="label label-danger pa2">已删除</span>` : ''
                        return `${value}<br/>${lotDelFlag}`
                    }
                },
                {
                    title: '线路',
                    align: 'left',
                    field : 'deliProvinceName',
                    formatter: function (value, row, index) {
                        let deliAddr = row.deliProvinceName + row.deliCityName + row.deliAreaName;
                        let arriAddr = row.arriProvinceName + row.arriCityName + row.arriAreaName;

                        return `<span class="label label-warning pa2">提</span>`+deliAddr+`<br/><span class="label label-success pa2">到</span>`+arriAddr;
                    }
                },
                {
                    title: '调度人',
                    align: 'left',
                    field : 'lotRegUser',
                    formatter: function (value, row, index) {
                        return `${row.lotRegUser}<br/>${row.lotRegDate}`;
                    }
                },
                {
                    title: '付款核销',
                    align: 'left',
                    field : 'writeOffUser',
                    formatter: function (value, row, index) {
                        let writeOffTime = row.writeOffTime === null ? '-' : row.writeOffTime
                        let writeOffUserName = row.writeOffUserName === null ? '-' : row.writeOffUserName

                        return `${writeOffUserName}<br/>${writeOffTime}`;
                    }
                },
                {
                    title: '收款核账',
                    align: 'left',
                    field : 'recvUser',
                    formatter: function (value, row, index) {
                        let recvTime = row.recvTime === null ? '-' : row.recvTime
                        let recvUserName = row.recvUserName === null ? '-' : row.recvUserName

                        return `${recvUserName}<br/>${recvTime}`;
                    }
                },


            ]
        }
    }

    function updateFooter($table) {
        var total = 0;
        var selectedRows = $.btTable.bootstrapTable('getSelections');
        selectedRows.forEach(function(row) {
            total += row.amount;
        });

        $("#heji").text(total.toLocaleString('zh', {
            style: 'currency',
            currency: 'CNY'
        }))
    }

    function getAmountCount(type) {
        var data,url
        if (type === 0) {
            data = $.common.formToJSON("familiarCar-form");

            url =  ctx + "new-deposit/familiarCarList/amountTotal"
        }else if (type === 1) {
            data = $.common.formToJSON("unfamiliarCar-form");

            url =  ctx + "new-deposit/unfamiliarCarList/amountTotal"
        }

        data["type"] = type
        $.ajax({
            url: url,
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;
                if (result.code == 0 && data != undefined) {
                    $("#zhj").text(data.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });

    }

    function checkTap(type) {
        $.table.destroy()

        if (type === 0) {
            let familiarCarOptions = initFamiliarCarOptions();
            $.table.init(familiarCarOptions);

            $table = $('#familiarCar-table')

        }else {
            let unfamiliarCarOptions = initUnfamiliarCarOptions()
            $.table.init(unfamiliarCarOptions);

            $table = $('#unfamiliarCar-table')

        }
    }

    function searchPre(type) {
        var data = {};
        $.table.search(data);
    }

    function resetPre(type) {
        if (type === 0) {
            $("#familiarCar-form")[0].reset();
            $("#startDate").val("")
            $("#endDate").val("")
            $("#writeOffTimeStart").val("")
            $("#writeOffTimeEnd").val("")

        }else {
            $("#unfamiliarCar-form")[0].reset();
            $("#unstartDate").val("")
            $("#unendDate").val("")
            $("#recvTimeStart").val("")
            $("#recvTimeEnd").val("")

        }
        searchPre();
    }

    function rmDataByIds(type) {
        let idArr =[]
        if (type === 0) {
            idArr = $.table.selectColumns("dtlId");

            let bootstrapTable = $table.bootstrapTable('getSelections');

            for (var i = 0; i < bootstrapTable.length; i++) {
                if (bootstrapTable[i].status !== 0) {
                    $.modal.alertWarning("请选择“新建”状态下的数据。");
                    return;
                }
            }
        }else {
            idArr = $.table.selectColumns("id");

            let bootstrapTable = $table.bootstrapTable('getSelections');

            for (var i = 0; i < bootstrapTable.length; i++) {
                if (bootstrapTable[i].vbillstatus != 0 || bootstrapTable[i].recvStatus != 0) {
                    $.modal.alertWarning("请选择“新建”并且“待核账”状态下的数据。");
                    return;
                }
            }

        }
        if (idArr.length == 0) {
            $.modal.msgWarning("请至少选择一条数据");
            return;
        }

        layer.confirm('确定删除该条数据吗?', function (index) {
            var data = {}
            data['type'] = type
            data['id'] = idArr.join(',')

            $.ajax({
                url: ctx + "new-deposit/depositTab/rm",
                type: "post",
                dataType: "json",
                // contentType: "application/json; charset=utf-8",
                data: data,
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                    }else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();

                    $.table.refresh()
                }
            });

        });
    }

    function exportAll(type) {
        var data
        var url
        if (type === 0) {
            data = $("#familiarCar-form").serializeArray();
            url = `${ctx}new-deposit/familiarCarList/export`;
        }else {
            data = $("#unfamiliarCar-form").serializeArray();
            url = `${ctx}new-deposit/unfamiliarCarList/export`;
        }


        $.modal.confirm("确定导出所有数据吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(url, data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    function createPackage(type) {
        let idArr = [];

        let bootstrapTable = $table.bootstrapTable('getSelections');

        if (type === 0) {
            idArr = $.table.selectColumns("dtlId");
            for (var i = 0; i < bootstrapTable.length; i++) {
                if (bootstrapTable[i].status != 0) {
                    $.modal.alertWarning("请选择“新建”状态下的数据。");
                    return;
                }
            }
        }else {
            idArr = $.table.selectColumns("id");
            for (var i = 0; i < bootstrapTable.length; i++) {
                if (bootstrapTable[i].recvStatus != 0) {
                    $.modal.alertWarning("请选择“待核账”状态下的数据。");
                    return;
                }
            }
        }

        layer.confirm('确定创建对账吗?', function (index) {
            var data = {}
            data['type'] = type
            data['lotDepositIdList'] = idArr

            $.ajax({
                url: ctx + "lotDepositPackage/create",
                type: "post",
                dataType: "json",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(data),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                    }else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();

                    $.table.refresh()
                }
            });
        });

    }

    function addPackage(type) {
        let idArr = [];

        let bootstrapTable = $table.bootstrapTable('getSelections');

        if (type === 0) {
            idArr = $.table.selectColumns("dtlId");
            for (var i = 0; i < bootstrapTable.length; i++) {
                if (bootstrapTable[i].status != 0) {
                    $.modal.alertWarning("请选择“新建”状态下的数据。");
                    return;
                }
            }
        }else {
            idArr = $.table.selectColumns("id");
            for (var i = 0; i < bootstrapTable.length; i++) {
                if (bootstrapTable[i].recvStatus != 0) {
                    $.modal.alertWarning("请选择“待核账”状态下的数据。");
                    return;
                }
            }
        }


        layer.open({
            type: 2,
            title: '非熟车定金对账单列表',
            closeBtn: 0,
            area: ['80%', '80%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: ctx + "lotDepositPackage/chooseList",
            btn: ['保存','取消'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {

            },
            yes: function (index, layero) {
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }

                if (rows[0]["status"] != 0) {
                    $.modal.alertWarning("请选择“新建”状态下的数据。");
                    return;
                }

                var data = {}
                data['type'] = type
                data['lotDepositPackageId'] = rows[0]["id"]
                data['lotDepositIdList'] = idArr

                $.ajax({
                    url: ctx + "lotDepositPackage/add",
                    type: "post",
                    dataType: "json",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(data),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function(result) {
                        if (result.code == 0) {
                            $.modal.msgSuccess(result.msg);
                        }else {
                            $.modal.alertError(result.msg);
                        }
                        $.modal.closeLoading();
                        $.modal.enable();

                        $.table.refresh()
                    }
                });



                layer.close(index);
            }
        })
    }

    function openDtl(packageId) {
        layer.open({
            type: 2,
            title: '定金对账单列表',
            closeBtn: 1,
            area: ['95%', '95%'],
            content: ctx + `lotDepositPackage/dtl/page?packageId=${packageId}`,
            btn: ['关闭'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {

            },
            btn1: function(index, layero, that){
                $.table.refresh()
                layer.close(index);
            },
            cancel: function(index, layero, that){
                $.table.refresh()
            }

        })
    }

    function openPack(packNo) {
        $.modal.openTab("定金对账包",ctx + "lotDepositPackage/page?packNo=" + packNo);

    }


    function initDate(id, sid, eid) {
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            //要求提货日期
            let myDate = laydate.render({
                elem: "#" + id,
                id: "id",
                range: true,
                rangeLinked: true,
                done: function (value, date, endDate) {
                    if (value !== '') {
                        let sDate = date.year + '-' + date.month + '-' + date.date;
                        let eDate = endDate.year + '-' + endDate.month + '-' + endDate.date;

                        $("#" + sid).val(sDate);
                        $("#" + eid).val(eDate);

                    } else {
                        $("#" + sid).val("")
                        $("#" + eid).val("")

                    }
                }
            });
        });


    }


/*    layui.use('laydate', function () {
        var laydate = layui.laydate;
        //要求提货日期
        let myDate = laydate.render({
            elem: "#dateLimit",
            id: "dateLimit",
            range: true,
            rangeLinked: true,
            done: function(value, date, endDate){
                if (value !== '') {
                    let sDate = date.year + '-' + date.month + '-' + date.date;
                    let eDate = endDate.year + '-' + endDate.month + '-' + endDate.date;

                    $("#startDate").val(sDate)
                    $("#endDate").val(eDate)

                }else {
                    $("#startDate").val("")
                    $("#endDate").val("")

                }
            }
        });
    });

    layui.use('laydate', function () {
        var laydate = layui.laydate;
        //要求提货日期
        let myDate = laydate.render({
            elem: "#undateLimit",
            id: "undateLimit",
            range: true,
            rangeLinked: true,
            done: function(value, date, endDate){
                if (value !== '') {
                    let sDate = date.year + '-' + date.month + '-' + date.date;
                    let eDate = endDate.year + '-' + endDate.month + '-' + endDate.date;

                    $("#unstartDate").val(sDate)
                    $("#unendDate").val(eDate)

                }else {
                    $("#unstartDate").val("")
                    $("#unendDate").val("")

                }
            }
        });
    });*/

    let vue = new Vue({
        el: '#checkingPanel',
        data: ()=>{
            return {
                act: '',
                form: {}
            }
        },
        methods: {

        }
    });

    function batchCheck() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        let ids = []
        for (let i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[0]["recvStatus"] !== 0) {
                $.modal.msgWarning("请选择待核账单据");
                return;
            }
            ids.push(bootstrapTable[i]["id"]);
        }
        var tt = layer.load(2, { //icon0-2 加载中,页面显示不同样式
            shade: [0.5, '#000'], //0.4为透明度 ，#000 为颜色
            content: '加载数据中...',
            success: function (layero) {
                layero.find('.layui-layer-content').css({
                    color: '#fff'
                });
            }
        });
        $.ajax({
            url: ctx + "new-deposit/batchCheckInfo",
            data: "ids=" + ids,
            type: "post",
            dataType: "json",
            success: function (result) {
                if (result.code == 0) {
                    vue.form = result.data;
                    layer.open({
                        type: 1,
                        title: '批量核账',
                        content: $('#checkingPanel'),
                        skin: '',
                        area: ['800px', '600px'],
                        btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
                        end: function (index, layero) {
                            layero.find(':input[type=file]').each(function(){
                                $(this).fileinput('destroy');
                            });
                        },
                        success: function (layero, index) {
                            const globalBatchUploading = {}
                            layero.find(':input[type=file]').each(function(idx){
                                var uploadCache = {}
                                let field = $(this).attr('name');
                                /*let hideName = field + "_hide_tid";
                                if ($(this).siblings().find('[name="' + hideName + '"]').length == 0) {
                                    $(this).after('<input type="hidden" name="' + hideName + '"/>');
                                }*/
                                let option = {
                                    theme: "explorer-fa5", //主题
                                    language: 'zh',
                                    uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                                    //deleteUrl: ctx + "common/deleteImage",
                                    uploadExtraData: {key: field},   //上传id，传入后台的参数
                                    deleteExtraData: {key: 'id'},
                                    // extra" {key: ''}, // 上面两个一致则可使用该字段？
                                    enctype: 'multipart/form-data',
                                    allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                                    initialPreviewAsData: true,
                                    overwriteInitial: false,
                                    //initialPreviewConfig: [
                                    //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                                    //],
                                    //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                                    maxFileCount: 0, // 0:不限制上传数
                                    showUpload: false,  // 不显示上传按钮，选择后直接上传
                                    //previewClass:"uploadPreview",
                                    minFileSize: 5, // 5KB
                                    previewFileIcon: '<i class="fa fa-file"></i>',
                                    allowedPreviewTypes: ['image'],
                                    showClose: false,  //是否显示右上角叉按钮
                                    showUpload: false, //是否显示下方上传按钮
                                    showRemove: false, // 是否显示下方移除按钮
                                    //autoReplace: true,
                                    //showPreview: false,//是否显示预览(false=只剩按钮)
                                    showCaption: false,//底部上传按钮左侧文本
                                    uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                                    fileActionSettings: {
                                        showUpload: false,		//每个文件的上传按钮
                                        showDrag: false,
                                        //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                                    },
                                };
                                $(this).fileinput(option).on("filebatchselected", function (e, files) {
                                    //uploading = true;
                                    //console.log('filebatchselected', files)
                                    globalBatchUploading[field+'_'+idx] = 1;
                                    $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                                }).on("fileuploaded", function (event, data, previewId, index) {
                                    //单个上传成功事件
                                    //console.log("fileuploaded", event, data, previewId, index)
                                    var code = data.response.code;
                                    if (code !== 0) {
                                        $.modal.closeLoading();
                                        $.modal.alertError("上传失败：" + data.response.msg);
                                        return;
                                    }

                                    var tid = data.response.tid;
                                    uploadCache[previewId] = tid;
                                    var curTids = []
                                    for (const uploadCacheKey in uploadCache) {
                                        if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                            curTids.push(uploadCache[uploadCacheKey])
                                        }
                                    }
                                    vue.$set(vue.form, field, curTids.join(','))
                                }).on('filesuccessremove', function (event, previewId, index) {
                                    //上传后删除事件
                                    //console.log("filesuccessremove", event, previewId, index)
                                    var tid = uploadCache[previewId];
                                    if (tid) {
                                        $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                                            console.log(result)
                                        }, 'json')
                                    }
                                    delete uploadCache[previewId]
                                    var curTids = []
                                    for (const uploadCacheKey in uploadCache) {
                                        if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                            curTids.push(uploadCache[uploadCacheKey])
                                        }
                                    }
                                    //$('[name="' + hideName + '"]').val(curTids.join(','));
                                    vue.$set(vue.form, field, curTids.join(','))
                                }).on('filebatchuploadcomplete', function (a, b, c) {
                                    //console.log('filebatchuploadcomplete', a, b, c)
                                    delete globalBatchUploading[field+'_'+idx];
                                });
                            })
                        },
                        btn1: function (index, layero) {
                            if (!vue.form['recvTid']) {
                                $.modal.msgWarning("请上传核账凭证");
                                return
                            }
                            $.modal.confirm('确定提交吗？', function(){
                                $.operate.submit(ctx + "new-deposit/saveChecking", "post", "json", vue.form, function(res){
                                    if (res.code == 0) {
                                        layer.close(index)
                                        $.table.refresh();
                                    }
                                });
                            });
                        }
                    })
                } else {
                    $.modal.msgError(result.msg);
                }
            },
            complete: function(){
                layer.close(tt);
            }
        })

    }

    //付款记录
    function checkRecord() {
        var id = $.table.selectColumns('id')[0];
        var recvStatus = $.table.selectColumns('recvStatus')[0];
        if (recvStatus !== 1) {
            $.modal.msgWarning("请选择已核账的数据")
            return
        }
        var url = ctx + "new-deposit/recvRecord?id="+id;
        layer.open({
            type: 2,
            maxmin: true,
            skin: '',
            //shade: false,
            title: "核账记录",
            area: [1000 + 'px', 700 + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                layer.close(index);
            }
        });
    }
</script>
</body>
</html>