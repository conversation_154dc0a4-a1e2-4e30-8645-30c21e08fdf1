<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('核账记录')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<body>

<div style="padding: 10px">
    <div class="panel panel-default">
        <div class="panel-body" style="padding: 15px">

            <div style="line-height: 2">
                <span style="display: inline-block;width: 200px">核账人：[[${deposit.recvUserName}]]</span>
                <span style="display: inline-block;margin-right: 10px">核账时间：[[${#dates.format(deposit.recvTime,'yyyy-MM-dd HH:mm')}]]</span>
            </div>

            <div style="display: flex;line-height: 2">
                <span style="width:65px">核账备注：</span>
                <div style="white-space: pre-wrap;width:0;flex:1;">[[${deposit.recvMemo}]]</div>
            </div>

            <div style="display: flex;line-height: 2">
                <span style="width:65px">核账凭证：</span>
                <div style="width:0;flex:1;" id="attachView"></div>
            </div>

        </div>
    </div>


    <div class="panel panel-default">
        <div class="panel-heading" style="font-weight: bold">同批次核账定金单据</div>

        <table id="bootstrap-table" class="table" data-mobile-responsive="true">
            <tr>
                <th>序号</th><th>定金单号</th><th>金额</th>
                <th>运单号</th><th>承运商</th>
                <th>创建人</th><th>创建时间</th>
            </tr>
            <tr th:each="row, stat : ${otherlist}">
                <td>[[${stat.index+1}]]</td>
                <td>[[${row.vbillno}]]</td>
                <td>[[${row.amount}]]</td>
                <td>[[${row.lotno}]]</td>
                <td>[[${row.carrName}]]</td>
                <td>[[${row.regUserName}]]</td>
                <td>[[${#dates.format(row.regDate,'yyyy-MM-dd HH:mm')}]]</td>
            </tr>
        </table>
        <div class="panel-footer">
            总定金金额：￥[[${totalAmount}]]元
        </div>
    </div>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
$(function(){
    const files = [[${files}]];
    let tmp = [];
    for (let i = 0; i < files.length; i++) {
        tmp.push('<img src="',files[i].filePath,'" style="width:70px;height:50px;margin-right:10px;"/>');
    }
    $('#attachView').html(tmp.join(''));
    $('#attachView').viewer({
        url: 'data-original',
        title: false,
        navbar: false,
    })
});
</script>
</body>
</html>