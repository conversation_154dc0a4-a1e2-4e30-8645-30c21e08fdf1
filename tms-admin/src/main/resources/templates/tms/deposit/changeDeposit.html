<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('定金调整')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        .flex{
            display: flex;
            align-content: center;
            justify-content: space-between;
        }
        .flex_left{
            width: 120px;
            line-height: 26px;
            text-align: right;
            color: #808080;
        }
        .flex_right{
            min-width:0;
            flex:1;
            line-height: 26px;
        }
    </style>
</head>
<body>
<div class="form-content">
    <form id="form-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="id" th:value="${deposit.id}">
        <div class="flex">
            <label class="flex_left"><span class="fcff3">*</span>定金收取方式：</label>
            <div class="flex_right">
                <select class="form-control" required name="way" th:with="way=${@dict.getType('deposit_way')}">
                    <option></option>
                    <option th:each="dict : ${way}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${dict.dictValue} == ${deposit.way}"></option>
                </select>
            </div>
        </div>
        <div class="flex">
            <label class="flex_left"><span class="fcff3">*</span>定金金额：</label>
            <div class="flex_right">
                <input class="form-control" name="amount" autocomplete="off" th:value="${deposit.amount}" min="0" required oninput="$.numberUtil.onlyNumberTwoDecimal(this)">
            </div>
        </div>
        <div class="flex">
            <label class="flex_left"><span class="fcff3">*</span>实际收取人：</label>
            <div class="flex_right">
                <div class="input-group" style="width: 100%">
                    <input class="form-control" id="actualPayee" name="actualPayee" autocomplete="off" th:value="${deposit.actualPayee}" required>
                    <input type="hidden" id="actualPayeeId" name="actualPayeeId" th:value="${deposit.actualPayeeId}">
                    <div class="input-group-btn">
                        <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                    </div>

                </div>
            </div>
        </div>
        <div th:if="${deposit.vbillstatus != 0 && deposit.vbillstatus != 3}" style="text-align: center;font-weight: bold;color: lightcoral;">
            当前定金单不是【新建】状态，不可调整！
        </div>
    </form>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>

<script type="text/javascript">
    const vbillstatus = [[${deposit.vbillstatus}]];
    $(function () {

        initActualPayeeBsSuggest()
    });

    function initActualPayeeBsSuggest() {
        $("#actualPayee").bsSuggest({
            idField: 'userId',                    //每组数据的哪个字段作为 data-id，优先级高于 indexId 设置（推荐）
            keyField: 'userName',                   //每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            allowNoKeyword: false, //是否允许无关键字时请求数据
            multiWord: false, //以分隔符号分割的多关键字支持separator: ",", //多关键字支持时的分隔符，默认为空格
            getDataMethod: "url", //获取数据的方式，总是从 URL 获取
            effectiveFields: ["userName", "deptName"],
            effectiveFieldsAlias: {userName: "姓名", deptName: "部门"},
            showHeader: true,
            hideOnSelect: true,            // 鼠标从列表单击选择了值时，是否隐藏选择列表
            inputWarnColor: 'rgba(255,0,0,.1)', //输入框内容不是下拉列表选择时的警告色
            url: ctx + 'system/user/bsSuggestUserByDept?deptId=123,124', //custAbbr /*优先从url ajax 请求 json 帮助数据，注意最后一个参数为关键字请求参数*/
            fnPreprocessKeyword: function (keyword) {
                //请求数据前，对输入关键字作进一步处理方法。注意，应返回字符串
                return keyword.trim();
            },
            fnAdjustAjaxParam: function (keyword, options) {  //该插件默认是GET请求  https://github.com/lzwme/bootstrap-suggest-plugin/issues?q=post
                //if(!isNull(keyword)) { //走get请求
                console.log("post")
                return {
                    type: 'POST',
                    timeout: 10000,
                    data: {
                        userName: keyword,
                        // pageSize: 20,  //承运商数据较多 默认只查20条
                        // pageNum: 1
                    }
                }
                //}
            },
            processData: function (res) {// url 获取数据时，对数据的处理，作为 getData 的回调函数
                let {code,data} = res

                if (code !== 0 || data.length === 0) {
                    return false;
                }

                var reData = {value: []}
                data.forEach(item => {
                    reData.value.push({
                        "userId": item.userId,
                        "userName": item.userName,
                        "deptName": item.deptName
                    });

                })
                return reData;
            }
        }).on('onSetSelectValue', function (e, keyword, data) {
            $("#actualPayeeId").val(data.userId);
            $("#actualPayee").val(data.userName);
        });
    }

    $('#actualPayee').on('keyup', function() {
        // if ($(this).val().trim() === '') {
            $('#actualPayeeId').val('');
        // }
    });
    $('#actualPayee').on('blur', function() {
        if ($('#actualPayeeId').val() === '') {
            $('#actualPayee').val('');
        }
    });


    function submitHandler() {
        if (vbillstatus !== 0 && vbillstatus !== 3) {
            $.modal.alertWarning("当前定金单不是新建状态，不可调整！");
            return
        }
        if ($.validate.form()) {
            let msg = "确认提交吗？";
            if (parseFloat($('[name=amount]').val(), 10) == [[${deposit.gotAmount}]]) {
                msg = "调整后金额=已核销金额(全部核销)，将不可再修改，是否继续？";
            }
            $.modal.confirm(msg, function(){
                $.operate.save(ctx + "tms/segment/doChangeDeposit", $('#form-add').serialize());
            })
        }
    }
</script>
</body>
</html>