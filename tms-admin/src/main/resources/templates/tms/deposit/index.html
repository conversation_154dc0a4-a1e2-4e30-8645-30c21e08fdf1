<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('熟车定金管理')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        .label-primaryT{
            color: #1ab394;
            background-color: transparent;
            border: 1px solid #1ab394;
        }
        .label-successT{
            color: #1c84c6;
            background-color: transparent;
            border: 1px solid #1c84c6;
        }
        .label-defaultT{
            color: #5e5e5e;
            background-color: transparent;
            border: 1px solid #5e5e5e;
        }
        .label-warningT{
            color: #f8ac59;
            background-color: transparent;
            border: 1px solid #f8ac59;
        }
        .label-infoT{
            color: #23c6c8;
            background-color: transparent;
            border: 1px solid #23c6c8;
        }
        .label-dangerT{
            color: #ed5565;
            background-color: transparent;
            border: 1px solid #ed5565;
        }
        .label-inverseT{
            color: #262626;
            background-color: transparent;
            border: 1px solid #262626;
        }
        .primary {
            color: #409EFF;
        }
        .flex{
            display: flex;
            align-items:start;
            justify-content:space-between;
        }
        .flex_left{
            width: 100px;
            line-height: 26px;
            text-align: right;
        }
        .flex_right{
            flex: 1;
            width: 0;
        }
        .fake-form-control {
            width: 100%;
        }
        .fake-form-control .el-input__inner{
            border: 1px solid #e5e6e7;
            border-radius: 1px;
            line-height: 26px;
            height: 26px;
            font-size: 13px;
            padding: 2px 4px;
        }
        .fake-form-control .el-input {
            position: static !important;
        }
        .fake-form-control .el-input__icon {
            width: 15px!important;
            line-height: 26px!important;
        }
        .abbrpop {
            width:400px !important;
        }
        .file-preview {
            border: none !important;
            padding: 0 !important;
        }
        .file-drop-zone {
            margin: 0 !important;
        }
    </style>
</head>

<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="form-group">

                        <div class="col-md-2 col-sm-4">
                            <input name="carrName" class="form-control" placeholder="承运商名称" autocomplete="off" aria-required="true">
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="add()" shiro:hasPermission="carrier:deposit:crud">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-success disabled single" onclick="applyBack()" shiro:hasPermission="carrier:deposit:applyBack">
                <i class="fa fa-rmb"></i> 申请退款
            </a>
            <!--<a class="btn btn-danger disabled single" onclick="pay_back()" shiro:hasPermission="carrier:deposit:backoff">
                <i class="fa fa-rmb"></i> 退还承运商
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<div id="addPanel" style="display: none;padding: 15px 20px 0;">
    <form id="crudForm">
        <!--<div class="flex">
            <span class="flex_left"><span class="fcff3" v-if="act=='edit'">*</span> 熟车承运商：</span>
            <span class="flex_right">
                <el-autocomplete v-if="act=='edit'" class="fake-form-control" v-model="form.carrName" required name="carrName"
                                 :fetch-suggestions="querySearch" value-key="carrName" placeholder="承运商名称关键字(只匹配单笔熟车)"
                                 :trigger-on-focus="false" @select="handleSelect" popper-class="abbrpop">
                    <template slot-scope="{item}"><div>{{item.carrName}} <span style="color:#aaa">{{item.contact}} {{item.phone}}</span></div></template>
                </el-autocomplete>
                <div v-else class="form-control" style="line-height: 22px">{{form.carrName}}</div>
            </span>
        </div>-->
        <div class="flex">
            <span class="flex_left"><span class="fcff3" v-if="act=='edit'">*</span> 熟车承运商：</span>
            <span class="flex_right">
                <el-select v-if="act=='edit'" v-model="form.carrierId" name="carrName" placeholder="承运商名称关键字(只匹配单笔熟车)"
                           class="fake-form-control" filterable remote :remote-method="queryCarrier">
                    <el-option v-for="item in carrierList" :key="item.carrierId" :label="item.carrName" :value="item.carrierId">
                        <div style="width: 280px" class="flex"><span>{{item.carrName}}</span><span style="color:#aaa">{{item.contact}} / {{item.phone}}</span></div>
                    </el-option>
                </el-select>
                <div v-else class="form-control" style="line-height: 22px">{{form.carrName}}</div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left"><span class="fcff3" v-if="act=='edit'">*</span> 定金金额：</span>
            <span class="flex_right">
                <input class="form-control" v-if="act=='edit'" v-model="form.amount" placeholder="定金金额" required name="amount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)">
                <div v-else class="form-control" style="line-height: 22px">{{form.amount}}</div>
            </span>
        </div>
        <div v-if="act == 'writeOff'" class="flex" style="margin-top: 5px">
            <span class="flex_left"><span class="fcff3">*</span> 核销凭证：</span>
            <span class="flex_right">
                <input type="file" name="writeOffTid" multiple>
            </span>
        </div>
    </form>
</div>
<div id="payPanel" style="display: none;padding: 15px 20px 0;">
    <form id="payForm">
        <div class="flex">
            <span class="flex_left">承运商：</span>
            <span class="flex_right">
                <div class="form-control" style="line-height: 22px">{{form.carrName}}</div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">可退还金额：</span>
            <span class="flex_right">
                <div class="form-control" style="line-height: 22px">{{form.balance}}</div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left"><span class="fcff3">*</span> 退还金额：</span>
            <span class="flex_right">
                <input class="form-control" v-model="form.amount" placeholder="退还金额" required name="amount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" :max="form.balance" min="0.01">
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left"><span class="fcff3">*</span> 退还凭证：</span>
            <span class="flex_right">
                <input type="file" name="writeOffTid" multiple>
            </span>
        </div>
    </form>
</div>
<div id="applyPanel" style="display: none;padding: 15px 20px 0;">
    <form id="applyForm">
        <input type="hidden" id="cysid" name="carrierId">
        <input type="hidden" name="skmTid">
        <div class="flex">
            <span class="flex_left">承运商：</span>
            <span class="flex_right">
                <div class="form-control" style="line-height: 22px" id="cys"></div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">可申请金额：</span>
            <span class="flex_right">
                <div class="form-control" style="line-height: 22px" id="ksqje"></div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left"><span class="fcff3">*</span> 申请金额：</span>
            <span class="flex_right">
                <input class="form-control" id="sqthje" placeholder="申请退还金额" required name="amount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" max="9999" min="0.01">
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left"><span class="fcff3">*</span> <span style="margin-right: 12px">承运商收款码</span><br><span>或银行卡：</span></span>
            <span class="flex_right">
                <input type="file" name="skm">
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">申请事由：</span>
            <span class="flex_right">
                <textarea class="form-control" id="sqsy" name="applyMemo" rows="4"></textarea>
            </span>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<link th:href="@{/element-ui@2.15.13/lib/theme-chalk/index.css}" rel="stylesheet"/>
<script th:src="@{/js/vue.min.js}"></script>
<script th:src="@{/element-ui@2.15.13/lib/index.js}"></script>
<th:block th:include="include :: bootstrap-fileinput553-css"/>
<th:block th:include="include :: bootstrap-fileinput553-js"/>
<script th:inline="javascript">
    var prefix = ctx + "new-deposit";

    //初始化查询条件传参
    queryParams = function (params) {
        var search = {};
        $.each($("#role-form").serializeArray(), function (i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchx();
            }
        });

        initTable()

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '.laydateRange',
                type: 'date',
                trigger: 'click',
                range: true,
                rangeLinked: true
            });
        });

        if ($.validator) {
            $.validator.prototype.elements = function () {
                var validator = this,
                    rulesCache = {};
                return $([]).add(this.currentForm.elements)
                    .filter(":input")
                    .not(":submit, :reset, :image, [disabled]")
                    .not(this.settings.ignore)
                    .filter(function () {
                        var elementIdentification = this.id || this.name;
                        !elementIdentification && validator.settings.debug && window.console && console.error("%o has no id nor name assigned", this);
                        if (elementIdentification in rulesCache || !validator.objectLength($(this).rules()))
                            return false;
                        rulesCache[elementIdentification] = true;
                        return true;
                    });
            };
        }

    });

    function initTable() {
        var options = {
            url: prefix + "/list",
            queryParams: queryParams,
            uniqueId: 'id',
            showToggle: false,
            showColumns: true,
            //fixedColumns: true,
            //fixedNumber: 4,
            clickToSelect: true,
            height: 620,
            showFooter: true,
            showExport: false,
            onPostBody: function () {
                //合并页脚
            },
            onRefresh: function (params) {
            },
            onCheck: function (row, $element) {
            },
            onUncheck: function (row, $element) {
            },
            onCheckAll: function (rowsAfter) {
            },
            onUncheckAll: function () {
            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "";
                    }
                },
                {field: 'id', title: '操作', formatter: function (value, row, index) {
                        let actions = [];
                        actions.push('<a class="fa fa-list" href="javascript:detail(\'' + row.carrierId + '\')" title="明细"></a>')
                        return actions.join("");
                    }
                },
                {field: 'carrName',title: '承运商名称'},
                //{field: 'familiar',title: '熟车'},

                {field: 'balance', title: '定金余额',formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }},
                {field: 'amount', title: '待收取定金',formatter: function (value, row, index) {
                        return (value - row.balance).toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }}
            ]
        };
        $.table.init(options);
    }

    function searchx() {
        var data = {};
        /*var range = $('[name="params[writeOffTimeRange]"]').val();
        if (range) {
            var strings = range.split(" - ");
            data['params[writeOffTimeStart]'] = strings[0];
            data['params[writeOffTimeEnd]'] = strings[1];
        }*/
        $.table.search('role-form', data);
    }

    function resetx() {
        $("#role-form")[0].reset();
        $('.selectpicker').selectpicker('refresh');
        searchx();
    }

    let vue = new Vue({
        el: '#addPanel',
        data: ()=>{
            return {
                act: '',
                form: {},
                carrierList: []
            }
        },
        methods: {
            /*querySearch(queryString, cb) {
                if(!queryString) {
                    cb([])
                    return;
                }
                $.ajax({
                    url: prefix + '/carrQuery',
                    data: "pageSize=20&pageNum=1&carrName="+encodeURIComponent(queryString||''),
                    type: 'post',
                    success: function(results) {
                        // 调用 callback 返回建议列表的数据
                        cb(results);
                    }
                })
            },
            handleSelect(item) {
                this.form.carrierId = item.carrierId;
            },*/
            queryCarrier(query) {
                let that = this;
                query = query.trim();
                $.ajax({
                    url: prefix + '/carrQuery',
                    data: "pageSize=20&pageNum=1&carrName="+encodeURIComponent(query),
                    type: 'post',
                    success: function(results) {
                        // 调用 callback 返回建议列表的数据
                        that.carrierList = results;
                    }
                })
            }
        }
    });

    function add(idx) {
        $("#crudForm").validate().resetForm();
        $("#crudForm").find('.error').removeClass("error")
        vue.act = 'edit'
        if (idx != undefined) {
            let row = $.btTable.bootstrapTable('getData')[idx];
            let {id,carrierId,carrName,amount} = row;
            vue.form = {id,carrierId,carrName,amount}
        } else {
            vue.form = {};
        }
        layer.open({
            type: 1,
            title: (idx == undefined ? '新增' : '修改') + '定金',
            content: $('#addPanel'),
            skin: '',
            area: ['460px', '220px'],
            zIndex: 10,
            offset: '100px',
            btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
            success: function (layero, index) {
                layero.find("input[name=carrName]").prop("required", true);
            },
            btn1: function (index, layero) {
                if (!$('#crudForm').validate().form())
                    return;
                if (vue.form.amount <= 0) {
                    $.modal.msgWarning("金额必须大于0");
                    return;
                }
                $.modal.confirm('确认提交吗？', function(){
                    $.operate.submit(prefix + "/submit", "post", "json", vue.form, function(res){
                        if (res.code == 0) {
                            layer.close(index)
                        }
                    });
                });
            }
        })
    }

    function del() {
        var selected = $.btTable.bootstrapTable('getSelections');
        let ids = [];
        for (let i = 0; i < selected.length; i++) {
            if (selected[i].status == 1) {
                $.modal.msgWarning("已核销的数据不可删除");
                return
            }
            ids.push(selected[i].id);
        }
        $.modal.confirm("确认删除这" + ids.length + "条单据？", function(){
            $.operate.submit(prefix + "/remove", "post", "json", {"ids":ids.join(',')});
        })
    }
    const globalBatchUploading = {}
    function writeoff(idx) {
        $("#crudForm").validate().resetForm();
        $("#crudForm").find('.error').removeClass("error")
        let row = $.btTable.bootstrapTable('getData')[idx];
        let {id,carrName,amount,corDate} = row;
        vue.form = {id,carrName,amount,corDate}
        vue.act = 'writeOff';
        vue.$nextTick(()=>{
            layer.open({
                type: 1,
                title: '定金收取',
                content: $('#addPanel'),
                skin: '',
                area: ['560px', '360px'],
                zIndex: 10,
                offset: '100px',
                btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
                end: function(){
                    vue.act = '';
                },
                success: function (layero, index) {
                    layero.find(':input[type=file]').each(function(idx){
                        var uploadCache = {}
                        let field = $(this).attr('name');
                        /*let hideName = field + "_hide_tid";
                        if ($(this).siblings().find('[name="' + hideName + '"]').length == 0) {
                            $(this).after('<input type="hidden" name="' + hideName + '"/>');
                        }*/
                        let option = {
                            theme: "explorer-fa5", //主题
                            language: 'zh',
                            uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                            //deleteUrl: ctx + "common/deleteImage",
                            uploadExtraData: {key: field},   //上传id，传入后台的参数
                            deleteExtraData: {key: 'id'},
                            // extra" {key: ''}, // 上面两个一致则可使用该字段？
                            enctype: 'multipart/form-data',
                            allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                            initialPreviewAsData: true,
                            overwriteInitial: false,
                            //initialPreviewConfig: [
                            //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                            //],
                            //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                            maxFileCount: 0, // 0:不限制上传数
                            showUpload: false,  // 不显示上传按钮，选择后直接上传
                            //previewClass:"uploadPreview",
                            minFileSize: 5, // 5KB
                            previewFileIcon: '<i class="fa fa-file"></i>',
                            allowedPreviewTypes: ['image'],
                            showClose: false,  //是否显示右上角叉按钮
                            showUpload: false, //是否显示下方上传按钮
                            showRemove: false, // 是否显示下方移除按钮
                            //autoReplace: true,
                            //showPreview: false,//是否显示预览(false=只剩按钮)
                            showCaption: false,//底部上传按钮左侧文本
                            uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                            fileActionSettings: {
                                showUpload: false,		//每个文件的上传按钮
                                showDrag: false,
                                //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                            },
                        };
                        $(this).fileinput(option).on("filebatchselected", function (e, files) {
                            //uploading = true;
                            console.log('filebatchselected', files)
                            globalBatchUploading[field+'_'+idx] = 1;
                            $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                        }).on("fileuploaded", function (event, data, previewId, index) {
                            //单个上传成功事件
                            console.log("fileuploaded", event, data, previewId, index)
                            var code = data.response.code;
                            if (code !== 0) {
                                $.modal.closeLoading();
                                $.modal.alertError("上传失败：" + data.response.msg);
                                return;
                            }

                            var tid = data.response.tid;
                            uploadCache[previewId] = tid;
                            var curTids = []
                            for (const uploadCacheKey in uploadCache) {
                                if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                    curTids.push(uploadCache[uploadCacheKey])
                                }
                            }
                            vue.$set(vue.form, field, curTids.join(','))
                        }).on('filesuccessremove', function (event, previewId, index) {
                            //上传后删除事件
                            console.log("filesuccessremove", event, previewId, index)
                            var tid = uploadCache[previewId];
                            if (tid) {
                                $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                                    console.log(result)
                                }, 'json')
                            }
                            delete uploadCache[previewId]
                            var curTids = []
                            for (const uploadCacheKey in uploadCache) {
                                if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                    curTids.push(uploadCache[uploadCacheKey])
                                }
                            }
                            //$('[name="' + hideName + '"]').val(curTids.join(','));
                            vue.$set(vue.form, field, curTids.join(','))
                        }).on('filebatchuploadcomplete', function (a, b, c) {
                            console.log('filebatchuploadcomplete', a, b, c)
                            delete globalBatchUploading[field+'_'+idx];
                        });
                    })
                },
                btn1: function (index, layero) {
                    if (!vue.form['writeOffTid']) {
                        $.modal.msgWarning("请上传核销凭证");
                        return
                    }
                    $.modal.confirm('确认核销吗？', function(){
                        $.operate.submit(prefix + "/writeOff", "post", "json", vue.form, function(res){
                            if (res.code == 0) {
                                layer.close(index)
                            }
                        });
                    });
                }
            })
        })
    }

    function detail(carrierId) {
        layer.open({
            title: '定金明细',
            type: 2,
            skin: '',
            maxmin: true,
            shadeClose: true,
            area: [document.documentElement.clientWidth - 100 + 'px', document.documentElement.clientHeight - 100 + 'px'],
            content: prefix + "/carrier-deposit-dtl?carrierId="+carrierId,
            btn: ['关闭']
        })
        //$.modal.open('定金明细', prefix + "/carrier-deposit-dtl?carrier_id="+carrierId, document.documentElement.clientWidth - 100, document.documentElement.clientHeight - 100)
    }

    let vue2 = new Vue({
        el: '#payPanel',
        data: ()=>{
            return {
                form: {}
            }
        },
        methods: {
        }
    });


    function pay_back() {
        let row = $.btTable.bootstrapTable('getSelections')[0];
        $("#payForm").validate().resetForm();
        $("#payForm").find('.error').removeClass("error")
        let {id,carrName,balance} = row;
        vue2.form = {id,carrName,balance}
        vue2.$nextTick(()=>{
            layer.open({
                type: 1,
                title: '退还定金',
                content: $('#payPanel'),
                skin: '',
                area: ['560px', '480px'],
                zIndex: 10,
                offset: '100px',
                btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
                end: function(){
                    $('#payPanel').find(':input[type=file]').each(function(){
                        $(this).fileinput('destroy');
                    });
                },
                success: function (layero, index) {
                    layero.find(':input[type=file]').each(function(idx){
                        var uploadCache = {}
                        let field = $(this).attr('name');
                        /*let hideName = field + "_hide_tid";
                        if ($(this).siblings().find('[name="' + hideName + '"]').length == 0) {
                            $(this).after('<input type="hidden" name="' + hideName + '"/>');
                        }*/
                        let option = {
                            theme: "explorer-fa5", //主题
                            language: 'zh',
                            uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                            //deleteUrl: ctx + "common/deleteImage",
                            uploadExtraData: {key: field},   //上传id，传入后台的参数
                            deleteExtraData: {key: 'id'},
                            // extra" {key: ''}, // 上面两个一致则可使用该字段？
                            enctype: 'multipart/form-data',
                            allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                            initialPreviewAsData: true,
                            overwriteInitial: false,
                            //initialPreviewConfig: [
                            //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                            //],
                            //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                            maxFileCount: 0, // 0:不限制上传数
                            showUpload: false,  // 不显示上传按钮，选择后直接上传
                            //previewClass:"uploadPreview",
                            minFileSize: 5, // 5KB
                            previewFileIcon: '<i class="fa fa-file"></i>',
                            allowedPreviewTypes: ['image'],
                            showClose: false,  //是否显示右上角叉按钮
                            showUpload: false, //是否显示下方上传按钮
                            showRemove: false, // 是否显示下方移除按钮
                            //autoReplace: true,
                            //showPreview: false,//是否显示预览(false=只剩按钮)
                            showCaption: false,//底部上传按钮左侧文本
                            uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                            fileActionSettings: {
                                showUpload: false,		//每个文件的上传按钮
                                showDrag: false,
                                //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                            },
                        };
                        $(this).fileinput(option).on("filebatchselected", function (e, files) {
                            globalBatchUploading[field+'_'+idx] = 1;
                            $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                        }).on("fileuploaded", function (event, data, previewId, index) {
                            var code = data.response.code;
                            if (code !== 0) {
                                $.modal.closeLoading();
                                $.modal.alertError("上传失败：" + data.response.msg);
                                return;
                            }

                            var tid = data.response.tid;
                            uploadCache[previewId] = tid;
                            var curTids = []
                            for (const uploadCacheKey in uploadCache) {
                                if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                    curTids.push(uploadCache[uploadCacheKey])
                                }
                            }
                            vue2.$set(vue2.form, field, curTids.join(','))
                        }).on('filesuccessremove', function (event, previewId, index) {
                            //上传后删除事件
                            var tid = uploadCache[previewId];
                            if (tid) {
                                $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                                    console.log(result)
                                }, 'json')
                            }
                            delete uploadCache[previewId]
                            var curTids = []
                            for (const uploadCacheKey in uploadCache) {
                                if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                    curTids.push(uploadCache[uploadCacheKey])
                                }
                            }
                            vue2.$set(vue2.form, field, curTids.join(','))
                        }).on('filebatchuploadcomplete', function (a, b, c) {
                            delete globalBatchUploading[field+'_'+idx];
                        });
                    })
                },
                btn1: function (index, layero) {
                    if (!vue2.form['writeOffTid']) {
                        $.modal.msgWarning("请上传退还凭证");
                        return
                    }
                    if ($("#payForm").validate().form()) {
                        $.modal.confirm('确认提交吗？', function(){
                            $.operate.submit(prefix + "/backOff", "post", "json", vue2.form, function(res){
                                if (res.code == 0) {
                                    layer.close(index)
                                }
                            });
                        });
                    }
                }
            })
        })
    }

    function applyBack() {
        let row = $.btTable.bootstrapTable('getSelections')[0];
        $.ajax({
            url: prefix + "/backAble?carrierId="+row.carrierId,
            type: 'get',
            cache: false,
            success: function (res) {
                if (res.code == 0) {
                    layer.open({
                        type: 1,
                        title: '申请退款',
                        content: $('#applyPanel'),
                        skin: '',
                        area: ['560px', '480px'],
                        zIndex: 10,
                        offset: '100px',
                        btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
                        end: function () {
                            $('#sqthje').val('');
                            $('#sqsy').val('');
                            $('#ksqje').text('');
                            $('#applyPanel').find(':input[type=file]').each(function(){
                                $(this).fileinput('destroy');
                            });
                            $('[name=skmTid]').val('')
                        },
                        success: function (layero, index) {
                            $('#ksqje').text(res.data.backAble);
                            if (res.data.backing) {
                                $('#ksqje').text($('#ksqje').text() + '（' + res.data.backing + '元正在退款中）');
                            }
                            $('#sqthje').attr('max', res.data.backAble);
                            $('#cysid').val(row.carrierId);
                            $('#cys').text(row.carrName);
                            layero.find(':input[type=file]').each(function(idx){
                                var uploadCache = {}
                                let field = $(this).attr('name');
                                /*let hideName = field + "_hide_tid";
                                if ($(this).siblings().find('[name="' + hideName + '"]').length == 0) {
                                    $(this).after('<input type="hidden" name="' + hideName + '"/>');
                                }*/
                                let option = {
                                    theme: "explorer-fa5", //主题
                                    language: 'zh',
                                    uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                                    //deleteUrl: ctx + "common/deleteImage",
                                    uploadExtraData: {key: field},   //上传id，传入后台的参数
                                    deleteExtraData: {key: 'id'},
                                    // extra" {key: ''}, // 上面两个一致则可使用该字段？
                                    enctype: 'multipart/form-data',
                                    allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                                    initialPreviewAsData: true,
                                    overwriteInitial: false,
                                    //initialPreviewConfig: [
                                    //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                                    //],
                                    //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                                    maxFileCount: 1, // 0:不限制上传数
                                    showUpload: false,  // 不显示上传按钮，选择后直接上传
                                    //previewClass:"uploadPreview",
                                    minFileSize: 5, // 5KB
                                    previewFileIcon: '<i class="fa fa-file"></i>',
                                    allowedPreviewTypes: ['image'],
                                    showClose: false,  //是否显示右上角叉按钮
                                    showUpload: false, //是否显示下方上传按钮
                                    showRemove: false, // 是否显示下方移除按钮
                                    //autoReplace: true,
                                    //showPreview: false,//是否显示预览(false=只剩按钮)
                                    showCaption: false,//底部上传按钮左侧文本
                                    uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                                    fileActionSettings: {
                                        showUpload: false,		//每个文件的上传按钮
                                        showDrag: false,
                                        //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                                    },
                                };
                                $(this).fileinput(option).on("filebatchselected", function (e, files) {
                                    globalBatchUploading[field+'_'+idx] = 1;
                                    $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                                }).on("fileuploaded", function (event, data, previewId, index) {
                                    var code = data.response.code;
                                    if (code !== 0) {
                                        $.modal.closeLoading();
                                        $.modal.alertError("上传失败：" + data.response.msg);
                                        return;
                                    }

                                    var tid = data.response.tid;
                                    uploadCache[previewId] = tid;
                                    var curTids = []
                                    for (const uploadCacheKey in uploadCache) {
                                        if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                            curTids.push(uploadCache[uploadCacheKey])
                                        }
                                    }
                                    $('#applyForm').find('[name=skmTid]').val(curTids.join(','));
                                }).on('filesuccessremove', function (event, previewId, index) {
                                    //上传后删除事件
                                    var tid = uploadCache[previewId];
                                    if (tid) {
                                        $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                                            console.log(result)
                                        }, 'json')
                                    }
                                    delete uploadCache[previewId]
                                    var curTids = []
                                    for (const uploadCacheKey in uploadCache) {
                                        if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                            curTids.push(uploadCache[uploadCacheKey])
                                        }
                                    }
                                    $('#applyForm').find('[name=skmTid]').val(curTids.join(','));
                                }).on('filebatchuploadcomplete', function (a, b, c) {
                                    delete globalBatchUploading[field+'_'+idx];
                                });
                            })
                        },
                        btn1: function (index, layero) {
                            if (!layero.find('[name=skmTid]').val()) {
                                $.modal.msgWarning("请上传承运商收款码或银行卡");
                                return
                            }
                            if ($("#applyForm").validate().form()) {
                                $.modal.confirm('确认提交吗？', function () {
                                    $.operate.submit(prefix + "/applyBack", "post", "json", $('#applyForm').serialize(), function (res) {
                                        if (res.code == 0) {
                                            layer.close(index)
                                        }
                                    });
                                });
                            }
                        }
                    });

                } else {
                    $.modal.alertError(res.msg);
                }
            }
        })

    }
</script>
</body>
</html>