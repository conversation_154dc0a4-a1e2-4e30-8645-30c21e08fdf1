<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('定金核账')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        .label-primaryT {
            color: #1ab394;
            background-color: transparent;
            border: 1px solid #1ab394;
        }

        .label-successT {
            color: #1c84c6;
            background-color: transparent;
            border: 1px solid #1c84c6;
        }

        .label-defaultT {
            color: #5e5e5e;
            background-color: transparent;
            border: 1px solid #5e5e5e;
        }

        .label-warningT {
            color: #f8ac59;
            background-color: transparent;
            border: 1px solid #f8ac59;
        }

        .label-infoT {
            color: #23c6c8;
            background-color: transparent;
            border: 1px solid #23c6c8;
        }

        .label-dangerT {
            color: #ed5565;
            background-color: transparent;
            border: 1px solid #ed5565;
        }

        .label-inverseT {
            color: #262626;
            background-color: transparent;
            border: 1px solid #262626;
        }

        .primary {
            color: #409EFF;
        }

        .flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .flex_left {
            width: 100px;
            line-height: 26px;
            text-align: right;
        }

        .flex_right {
            flex: 1;
            width: 0;
        }

        .fake-form-control {
            width: 100%;
        }

        .fake-form-control .el-input__inner {
            border: 1px solid #e5e6e7;
            border-radius: 1px;
            line-height: 26px;
            height: 26px;
            font-size: 13px;
            padding: 2px 4px;
        }

        .fake-form-control .el-input {
            position: static !important;
        }

        .fake-form-control .el-input__icon {
            width: 15px !important;
            line-height: 26px !important;
        }

        .abbrpop {
            width: 400px !important;
        }

        .file-preview {
            border: none !important;
            padding: 0 !important;
        }

        .file-drop-zone {
            margin: 0 !important;
        }
    </style>
</head>

<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="form-group">
                        <div class="col-md-2 col-sm-4">
                            <select name="transLineId" id="transLineId" class="form-control selectpicker"
                                    aria-invalid="false" data-none-selected-text="调度组" th:with="type=${dispatcherDeptList}">
                                <option></option>
                                <option th:each="dict : ${type}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>
                            </select>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <input name="regUserName" class="form-control" placeholder="调度人" autocomplete="off"
                                   aria-required="true">
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <select name="recvStatus" class="form-control selectpicker" data-none-selected-text="核账状态">
                                <option></option><option value="0" selected>待核账</option><option value="1">已核账</option>
                            </select>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <input name="carrName" class="form-control" placeholder="承运商名称" autocomplete="off"
                                   aria-required="true">
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <input name="lotno" class="form-control" placeholder="运单号" autocomplete="off"
                                   aria-required="true">
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger multiple disabled" onclick="batchCheck()">
                <i class="fa fa-bars"></i> 批量核账
            </a>
            <a class="btn btn-success single disabled" onclick="checkRecord()">
                <i class="fa fa-clock-o"></i> 核账记录
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<div id="checkingPanel" style="display: none;padding: 15px 20px 0;">
    <form id="crudForm">
        <div class="flex">
            <span class="flex_left">总定金金额：</span>
            <span class="flex_right">
                <div style="line-height: 22px">￥{{form.totalAmount}}元</div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">核账备注：</span>
            <span class="flex_right">
                <textarea v-model="form.recvMemo" class="form-control" rows="5"></textarea>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left"><span class="fcff3">*</span> 核账凭证：</span>
            <span class="flex_right">
                <input type="file" name="recvTid" multiple>
            </span>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:src="@{/js/vue.min.js}"></script>
<th:block th:include="include :: bootstrap-fileinput553-css"/>
<th:block th:include="include :: bootstrap-fileinput553-js"/>
<script th:inline="javascript">
    var prefix = ctx + "new-deposit";

    //初始化查询条件传参
    queryParams = function (params) {
        var search = {};
        $.each($("#role-form").serializeArray(), function (i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchx();
            }
        });

        initTable()

        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '.laydateRange',
                type: 'date',
                trigger: 'click',
                range: true,
                rangeLinked: true
            });
        });

        if ($.validator) {
            $.validator.prototype.elements = function () {
                var validator = this,
                    rulesCache = {};
                return $([]).add(this.currentForm.elements)
                    .filter(":input")
                    .not(":submit, :reset, :image, [disabled]")
                    .not(this.settings.ignore)
                    .filter(function () {
                        var elementIdentification = this.id || this.name;
                        !elementIdentification && validator.settings.debug && window.console && console.error("%o has no id nor name assigned", this);
                        if (elementIdentification in rulesCache || !validator.objectLength($(this).rules()))
                            return false;
                        rulesCache[elementIdentification] = true;
                        return true;
                    });
            };
        }

    });

    var payDetailStatusEnum = [[${payDetailStatusEnum}]];

    function initTable() {
        var options = {
            url: prefix + "/recvlist",
            queryParams: queryParams,
            uniqueId: 'id',
            showToggle: false,
            showColumns: true,
            //fixedColumns: true,
            //fixedNumber: 4,
            clickToSelect: true,
            height: 620,
            showFooter: true,
            showExport: false,
            onPostBody: function () {
                //合并页脚
            },
            onRefresh: function (params) {
            },
            onCheck: function (row, $element) {
            },
            onUncheck: function (row, $element) {
            },
            onCheckAll: function (rowsAfter) {
            },
            onUncheckAll: function () {
            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "";
                    }
                },
                /*{
                    field: 'id', title: '操作', formatter: function (value, row, index) {
                        let actions = [];
                        if (row.status == 0) {

                        }
                        return actions.join("");
                    }
                },*/
                {field: 'transLineName', title: '调度组'},
                {
                    field: 'vbillno', title: '定金单号/状态', formatter: function (value, row, index) {
                        let txt = row.vbillno;
                        let labelClass = "";
                        if (row.vbillstatus == 0) {
                            labelClass = "label-default";
                        } else if (row.vbillstatus == 6) {
                            labelClass = "label-warning";
                        } else if (row.vbillstatus == 4) {
                            labelClass = "label-success";
                        } else if (row.vbillstatus == 7 || row.vbillstatus == 3) {
                            labelClass = "label-primary";
                        }
                        txt += ('<br>' + '<span class="label ' + labelClass + '">' + $.table.selectEnumContext(payDetailStatusEnum, row.vbillstatus) + '</span>');
                        if (row.bankBackFlag == 1){
                            txt += '<span class="label label-danger" style="margin-left: 2px;">银退</span>';
                        }
                        txt += ('<span class="label ' + (row.recvStatus == 0 ? '':'label-successT') + '" style="margin-left: 2px;">' + (row.recvStatus == 0 ? '待核账':'已核账') + '</span>');
                        return txt;
                    }
                },
                {
                    title: '申请信息',
                    field: 'applyUserName',
                    formatter: function (value, row, index) {
                        if (value) {
                            return value + '&nbsp;&nbsp;' + row.applyDate + '<br />' + (row.applyMemo||'');
                        }
                    }
                },
                {
                    title: '金额',
                    field: 'amount',
                    formatter: function (value, row, index) {
                        let data = [];
                        if (row.amount) {
                            data.push(row.amount.toLocaleString('zh', {
                                style: 'currency',
                                currency: 'CNY'
                            }))
                        }
                        return data.join("<br/>")
                    }
                },
                /*{
                    title: '收款信息',
                    align: 'left',
                    field: 'recAccount',
                    formatter: function(value, row, index) {
                        return value + '&nbsp;&nbsp;' + row.recCardNo+ '<br />' + row.recBank
                    }
                },*/
                /*{
                    title: '要求支付日期',
                    align: 'left',
                    field: 'reqPayDate',
                    formatter: function(row,value) {
                        if (value.reqPayDate){
                            var reqPayDate = new Date(Date.parse(value.reqPayDate));
                            value.reqPayDate.substr(0,10)
                            var now = new Date();
                            //如果要求支付日期小于当前时间,则标红
                            if (reqPayDate <= now) {
                                return '<span class="label label-danger">' + value.reqPayDate.substr(0,10) + '</span>';
                            } else {
                                return value.reqPayDate.substr(0,10);
                            }
                        }
                    }
                },*/
                {
                    title: '支付信息',
                    field: 'writeOffUserName',
                    formatter: function(value, row, index) {
                        if (value) {
                            return value + '<br>' + row.writeOffTime;
                        }
                    }
                },
                {
                    title: '承运商/身份证',
                    align: 'left',
                    field: 'carrName',
                    formatter: function status(value, row, index) {
                        return value + '<br />' + row.legalCard
                    }
                },
                {
                    title: '实际收取人',
                    align: 'left',
                    field: 'actualPayee'
                },

                {
                    title: '创建人/时间',
                    align: 'left',
                    field: 'regUserName',
                    formatter: function (value, row, index) {
                        return  value + '<br />' + row.regDate
                    }
                },
                {
                    title: '运单号',
                    align: 'left',
                    field: 'lotno'
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(!row.deliAddr){
                            row.deliAddr = '';
                        }
                        if(!row.arriAddr){
                            row.arriAddr = ''
                        }
                        if(row.deliAddr == "" && row.arriAddr == ""){
                            return "";
                        }else{
                            return `<span class="label label-warning pa2">提</span>`+row.deliAddr+`<br/><span class="label label-success pa2">到</span>`+row.arriAddr;
                        }

                    }
                }
            ]
        };
        $.table.init(options);
    }

    function searchx() {
        var data = {};
        var range = $('[name="params[writeOffTimeRange]"]').val();
        if (range) {
            var strings = range.split(" - ");
            data['params[writeOffTimeStart]'] = strings[0];
            data['params[writeOffTimeEnd]'] = strings[1];
        }
        $.table.search('role-form', data);
    }

    function resetx() {
        $("#role-form")[0].reset();
        $('.selectpicker').selectpicker('refresh');
        searchx();
    }

    let vue = new Vue({
        el: '#checkingPanel',
        data: ()=>{
            return {
                act: '',
                form: {}
            }
        },
        methods: {

        }
    });

    function batchCheck() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        let ids = []
        for (let i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[0]["recvStatus"] !== 0) {
                $.modal.msgWarning("请选择待核账单据");
                return;
            }
            ids.push(bootstrapTable[i]["id"]);
        }
        var tt = layer.load(2, { //icon0-2 加载中,页面显示不同样式
            shade: [0.5, '#000'], //0.4为透明度 ，#000 为颜色
            content: '加载数据中...',
            success: function (layero) {
                layero.find('.layui-layer-content').css({
                    color: '#fff'
                });
            }
        });
        $.ajax({
            url: prefix + "/batchCheckInfo",
            data: "ids=" + ids,
            type: "post",
            dataType: "json",
            success: function (result) {
                if (result.code == 0) {
                    vue.form = result.data;
                    layer.open({
                        type: 1,
                        title: '批量核账',
                        content: $('#checkingPanel'),
                        skin: '',
                        area: ['800px', '600px'],
                        btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
                        end: function () {
                            $('#checkingPanel').find(':input[type=file]').each(function(){
                                $(this).fileinput('destroy');
                            });
                        },
                        success: function (layero, index) {
                            const globalBatchUploading = {}
                            layero.find(':input[type=file]').each(function(idx){
                                var uploadCache = {}
                                let field = $(this).attr('name');
                                /*let hideName = field + "_hide_tid";
                                if ($(this).siblings().find('[name="' + hideName + '"]').length == 0) {
                                    $(this).after('<input type="hidden" name="' + hideName + '"/>');
                                }*/
                                let option = {
                                    theme: "explorer-fa5", //主题
                                    language: 'zh',
                                    uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                                    //deleteUrl: ctx + "common/deleteImage",
                                    uploadExtraData: {key: field},   //上传id，传入后台的参数
                                    deleteExtraData: {key: 'id'},
                                    // extra" {key: ''}, // 上面两个一致则可使用该字段？
                                    enctype: 'multipart/form-data',
                                    allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                                    initialPreviewAsData: true,
                                    overwriteInitial: false,
                                    //initialPreviewConfig: [
                                    //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                                    //],
                                    //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                                    maxFileCount: 0, // 0:不限制上传数
                                    showUpload: false,  // 不显示上传按钮，选择后直接上传
                                    //previewClass:"uploadPreview",
                                    minFileSize: 5, // 5KB
                                    previewFileIcon: '<i class="fa fa-file"></i>',
                                    allowedPreviewTypes: ['image'],
                                    showClose: false,  //是否显示右上角叉按钮
                                    showUpload: false, //是否显示下方上传按钮
                                    showRemove: false, // 是否显示下方移除按钮
                                    //autoReplace: true,
                                    //showPreview: false,//是否显示预览(false=只剩按钮)
                                    showCaption: false,//底部上传按钮左侧文本
                                    uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                                    fileActionSettings: {
                                        showUpload: false,		//每个文件的上传按钮
                                        showDrag: false,
                                        //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                                    },
                                };
                                $(this).fileinput(option).on("filebatchselected", function (e, files) {
                                    //uploading = true;
                                    //console.log('filebatchselected', files)
                                    globalBatchUploading[field+'_'+idx] = 1;
                                    $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                                }).on("fileuploaded", function (event, data, previewId, index) {
                                    //单个上传成功事件
                                    //console.log("fileuploaded", event, data, previewId, index)
                                    var code = data.response.code;
                                    if (code !== 0) {
                                        $.modal.closeLoading();
                                        $.modal.alertError("上传失败：" + data.response.msg);
                                        return;
                                    }

                                    var tid = data.response.tid;
                                    uploadCache[previewId] = tid;
                                    var curTids = []
                                    for (const uploadCacheKey in uploadCache) {
                                        if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                            curTids.push(uploadCache[uploadCacheKey])
                                        }
                                    }
                                    vue.$set(vue.form, field, curTids.join(','))
                                }).on('filesuccessremove', function (event, previewId, index) {
                                    //上传后删除事件
                                    //console.log("filesuccessremove", event, previewId, index)
                                    var tid = uploadCache[previewId];
                                    if (tid) {
                                        $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                                            console.log(result)
                                        }, 'json')
                                    }
                                    delete uploadCache[previewId]
                                    var curTids = []
                                    for (const uploadCacheKey in uploadCache) {
                                        if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                            curTids.push(uploadCache[uploadCacheKey])
                                        }
                                    }
                                    //$('[name="' + hideName + '"]').val(curTids.join(','));
                                    vue.$set(vue.form, field, curTids.join(','))
                                }).on('filebatchuploadcomplete', function (a, b, c) {
                                    //console.log('filebatchuploadcomplete', a, b, c)
                                    delete globalBatchUploading[field+'_'+idx];
                                });
                            })
                        },
                        btn1: function (index, layero) {
                            if (!vue.form['recvTid']) {
                                $.modal.msgWarning("请上传核账凭证");
                                return
                            }
                            $.modal.confirm('确定提交吗？', function(){
                                $.operate.submit(prefix + "/saveChecking", "post", "json", vue.form, function(res){
                                    if (res.code == 0) {
                                        layer.close(index)
                                        $.table.refresh();
                                    }
                                });
                            });
                        }
                    })
                } else {
                    $.modal.msgError(result.msg);
                }
            },
            complete: function(){
                layer.close(tt);
            }
        })

    }

    //付款记录
    function checkRecord() {
        var id = $.table.selectColumns('id')[0];
        var recvStatus = $.table.selectColumns('recvStatus')[0];
        if (recvStatus !== 1) {
            $.modal.msgWarning("请选择已核账的数据")
            return
        }
        var url = prefix + "/recvRecord?id="+id;
        layer.open({
            type: 2,
            maxmin: true,
            skin: '',
            //shade: false,
            title: "核账记录",
            area: [1000 + 'px', 700 + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                layer.close(index);
            }
        });
    }
</script>
</body>
</html>