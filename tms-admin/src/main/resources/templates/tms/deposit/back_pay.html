<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反核销')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <style type="text/css">
        .file-drop-zone {
            height: auto !important;
        }
    </style>
</head>

<body>
<div class="form-content">
    <form id="form-invoice-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="payDetailId" name="payDetailId" type="hidden" th:value="${id}">
            <input name="dataType" type="hidden" value="2">
            <table class="table table-bordered">
                <caption style="font-weight: bold;color:blue">选择需要反核销付款记录</caption>
                <thead>
                <tr>
                    <th>选择</th>
                    <th>付款日期</th>
                    <th>付款金额</th>
                    <th>付款类型</th>
                    <th>付款方式</th>
                    <th>收款账户</th>
                    <th>收款卡号</th>
                    <th>付款备注</th>
                    <th>付款人</th>
                </tr>
                </thead>
                <tbody id="tbody">
                </tbody>
            </table>
            <div class="panel-body">
                <!--基础信息 begin-->
                <input name="deptId" type="hidden" id="treeId">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-2">反核销人：</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" maxlength="50" required name="backPerson">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-2">反核销类型：</label>
                            <div class="col-sm-10">
                                <select name="payBackType" id="payBackType" th:with="type=${@dict.getType('back_confirm_type')}" class="form-control valid" aria-invalid="false">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-2">反核销说明：</label>
                            <div class="col-sm-10">
                                <textarea name="payBackMemo" id="payBackMemo" class="form-control" type="text"
                                          maxlength="50" required="" aria-required="true"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-2">反核销证明：</label>
                            <div class="col-sm-10">
                                <input id="image" class="form-control" name="image" type="file" multiple>
                                <input id="tid" name="tid" type="hidden" >
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </form>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">

    var payRecordList = [[${payRecordList}]];
    var pay_method = [[${@dict.getTypeAll('pay_method')}]];//付款方式
    var pay_type = [[${@dict.getTypeAll('pay_type')}]];//付款类型

    $(function () {
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        var tid = "tid";
        var imageId = "image";
        $.file.initAddFiles(imageId, tid, picParam);

        let tmp = [];
        for (let i = 0; i < payRecordList.length; i++) {
            tmp.push(`<tr>
                    <td><input type="checkbox" name="payRecordId" value="${payRecordList[i].payRecordId}" required title="至少选择一条" /></td>
                    <td>${payRecordList[i].payDate}</td>
                    <td>${payRecordList[i].payAmount}</td>
                    <td>${$.table.selectDictLabel(pay_type, payRecordList[i].payType)}</td>
                    <td>${$.table.selectDictLabel(pay_method, payRecordList[i].payMethod)}</td>
                    <td>${payRecordList[i].recAccount||''}</td>
                    <td>${payRecordList[i].recCardNo||''}</td>
                    <td>${payRecordList[i].memo||''}</td>
                    <td>${payRecordList[i].payMan}</td>
                </tr>`)
        }
        $('#tbody').html(tmp.join(''));
    });

    var prefix = ctx + "new-deposit";
    /**
     * 校验
     */
    $("#form-invoice-unconfirm").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            unconfirmType:{
                required:true,
            },
            unconfirmMemo:{
                required:true,
            }
        }
    });


    /**
     * 提交
     */
    function submitHandler() {
        if($('#image').fileinput('getFilesCount') == 0){
            $.modal.alertWarning("请上传反核销证明");
            return false;
        }
        jQuery.unsubscribe("cmt");
        if ($.validate.form()) {
            $("#image").fileinput('upload');
            jQuery.subscribe("cmt",commit);
        }
    }

    function commit(){
        var data = $("#form-invoice-unconfirm").serializeArray();
        $.operate.save(prefix + "/back_pay", data);
    }

</script>
</body>
</html>