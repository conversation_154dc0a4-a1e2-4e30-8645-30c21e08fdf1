<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('熟车定金退款')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        .label-primaryT{
            color: #1ab394;
            background-color: transparent;
            border: 1px solid #1ab394;
        }
        .label-successT{
            color: #1c84c6;
            background-color: transparent;
            border: 1px solid #1c84c6;
        }
        .label-defaultT{
            color: #5e5e5e;
            background-color: transparent;
            border: 1px solid #5e5e5e;
        }
        .label-warningT{
            color: #f8ac59;
            background-color: transparent;
            border: 1px solid #f8ac59;
        }
        .label-infoT{
            color: #23c6c8;
            background-color: transparent;
            border: 1px solid #23c6c8;
        }
        .label-dangerT{
            color: #ed5565;
            background-color: transparent;
            border: 1px solid #ed5565;
        }
        .label-inverseT{
            color: #262626;
            background-color: transparent;
            border: 1px solid #262626;
        }
        .primary {
            color: #409EFF;
        }
        .flex{
            display: flex;
            align-items:start;
            justify-content:space-between;
        }
        .flex_left{
            width: 100px;
            line-height: 26px;
            text-align: right;
        }
        .flex_right{
            flex: 1;
            width: 0;
        }
        .fake-form-control {
            width: 100%;
        }
        .fake-form-control .el-input__inner{
            border: 1px solid #e5e6e7;
            border-radius: 1px;
            line-height: 26px;
            height: 26px;
            font-size: 13px;
            padding: 2px 4px;
        }
        .fake-form-control .el-input {
            position: static !important;
        }
        .fake-form-control .el-input__icon {
            width: 15px!important;
            line-height: 26px!important;
        }
        .abbrpop {
            width:400px !important;
        }
        .file-preview {
            border: none !important;
            padding: 0 !important;
        }
        .file-drop-zone {
            margin: 0 !important;
        }
        .skin1 .layui-layer-btn0 {
            /*background-color: #1ab394;
            border-color: #1ab394;
            color: #FFFFFF;*/
        }
        .skin1 .layui-layer-btn1 {
            background-color: rgb(237, 85, 101);
            border-color: rgb(237, 85, 101);
            color: #FFFFFF;
        }
    </style>
</head>

<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="form-group">

                        <div class="col-md-2 col-sm-4">
                            <input name="carrName" class="form-control" placeholder="承运商名称" autocomplete="off" aria-required="true">
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <select class="form-control selectpicker" name="status" data-none-selected-text="状态">
                                <option value=""></option>
                                <option value="1">待审核</option>
                                <option value="2">已审核通过</option>
                                <option value="3">审核不通过</option>
                                <option value="4">已退款</option>
                            </select>
                        </div>

                        <div class="col-md-2 col-sm-4">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <!--<a class="btn btn-success" onclick="applyBack()" shiro:hasPermission="carrier:deposit:applyBack">
                <i class="fa fa-rmb"></i> 新增退款申请
            </a>-->
            <!--<a class="btn btn-danger disabled single" onclick="pay_back()" shiro:hasPermission="carrier:deposit:backoff">
                <i class="fa fa-rmb"></i> 退款承运商
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<div id="payPanel" style="display: none;padding: 15px 20px 0;">
    <form id="payForm">
        <input type="hidden" name="id">
        <input type="hidden" name="backTid">
        <div class="flex">
            <span class="flex_left">熟车承运商：</span>
            <span class="flex_right">
                <div class="form-control" style="line-height: 22px" cys></div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">当前余额：</span>
            <span class="flex_right">
                <div class="form-control" style="line-height: 22px" dqye></div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">退还金额：</span>
            <span class="flex_right">
                <div class="form-control" style="line-height: 22px" thje></div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left"><span class="fcff3">*</span> 已退还凭证：</span>
            <span class="flex_right">
                <input type="file" name="backFile" multiple>
            </span>
        </div>
    </form>
</div>
<!--<div id="applyPanel" style="display: none;padding: 15px 20px 0;">
    <form id="applyForm">
        <input type="hidden" id="cysid" name="carrierId">
        <div class="flex">
            <span class="flex_left">承运商：</span>
            <span class="flex_right">
                <div class="form-control" style="line-height: 22px" id="cys"></div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">可申请金额：</span>
            <span class="flex_right">
                <div class="form-control" style="line-height: 22px" id="ksqje"></div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left"><span class="fcff3">*</span> 申请金额：</span>
            <span class="flex_right">
                <input class="form-control" id="sqthje" placeholder="申请退还金额" required name="amount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" max="9999" min="0.01">
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">申请事由：</span>
            <span class="flex_right">
                <textarea class="form-control" id="sqsy" name="applyMemo" rows="5"></textarea>
            </span>
        </div>
    </form>
</div>-->
<div id="verifyPanel" style="display: none;padding: 15px 20px 0;">
    <form id="verifyForm">
        <input type="hidden" name="id">
        <div class="flex">
            <span class="flex_left">承运商：</span>
            <span class="flex_right">
                <div class="form-control" style="line-height: 22px" cys></div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">退款金额：</span>
            <span class="flex_right">
                <div class="form-control" style="line-height: 22px" tkje></div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">申请事由：</span>
            <span class="flex_right">
                <div class="form-control" sqsy style="white-space:pre-wrap;height: auto;min-height: 26px;line-height: 22px"></div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">退款后金额：</span>
            <span class="flex_right">
                <div class="form-control" tkhje style="white-space:pre-wrap;height: auto;min-height: 26px;line-height: 22px"></div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left">审核意见：</span>
            <span class="flex_right">
                <textarea class="form-control" name="verifyMemo" rows="5" placeholder="不同意时请填写"></textarea>
            </span>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<link th:href="@{/element-ui@2.15.13/lib/theme-chalk/index.css}" rel="stylesheet"/>
<script th:src="@{/js/vue.min.js}"></script>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/element-ui@2.15.13/lib/index.js}"></script>
<th:block th:include="include :: bootstrap-fileinput553-css"/>
<th:block th:include="include :: bootstrap-fileinput553-js"/>
<script th:inline="javascript">
    var prefix = ctx + "new-deposit";

    //初始化查询条件传参
    queryParams = function (params) {
        var search = {};
        $.each($("#role-form").serializeArray(), function (i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchx();
            }
        });

        initTable()

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '.laydateRange',
                type: 'date',
                trigger: 'click',
                range: true,
                rangeLinked: true
            });
        });

        if ($.validator) {
            $.validator.prototype.elements = function () {
                var validator = this,
                    rulesCache = {};
                return $([]).add(this.currentForm.elements)
                    .filter(":input")
                    .not(":submit, :reset, :image, [disabled]")
                    .not(this.settings.ignore)
                    .filter(function () {
                        var elementIdentification = this.id || this.name;
                        !elementIdentification && validator.settings.debug && window.console && console.error("%o has no id nor name assigned", this);
                        if (elementIdentification in rulesCache || !validator.objectLength($(this).rules()))
                            return false;
                        rulesCache[elementIdentification] = true;
                        return true;
                    });
            };
        }

    });

    function initTable() {
        var options = {
            url: prefix + "/backList",
            queryParams: queryParams,
            uniqueId: 'id',
            showToggle: false,
            showColumns: true,
            //fixedColumns: true,
            //fixedNumber: 4,
            clickToSelect: true,
            height: 620,
            showFooter: true,
            showExport: false,
            onPostBody: function () {
                //合并页脚
            },
            onRefresh: function (params) {
            },
            onCheck: function (row, $element) {
            },
            onUncheck: function (row, $element) {
            },
            onCheckAll: function (rowsAfter) {
            },
            onUncheckAll: function () {
            },
            columns: [
                /*{
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "";
                    }
                },*/
                {field: 'id', title: '操作', formatter: function (value, row, index) {
                        let actions = [];
                        if (row.status == 1) {
                            /*[# th:if="${@shiroUtils.getSubject().isPermitted('carrier:deposit:verify')}"]*/
                            actions.push('<a href="javascript:verify(' + index + ')">审核</a>')
                            /*[/]*/
                        } else if (row.status == 2) {
                            /*[# th:if="${@shiroUtils.getSubject().isPermitted('carrier:deposit:backoff')}"]*/
                            actions.push(' <a href="javascript:refund(' + index + ')">退款</a>')
                            /*[/]*/
                        }
                        actions.push(' <a href="javascript:detail(\'' + row.carrierId + '\')">明细</a>')
                        return actions.join("");
                    }
                },
                {field: 'status', title: '状态', formatter: function (value, row, index) {
                    if (value == 1) {
                        return '<span class="label label-default">待审核</span>'
                    } else if (value == 2) {
                        return '<span class="label label-success">审核通过</span>';
                    } else if (value == 3) {
                        return '<span class="label label-danger">审核不通过</span>';
                    } else if (value == 4) {
                        return '<span class="label label-warning">已退款</span>';
                    }
                    }},
                {field: 'carrName',title: '承运商名称'},
                {field: 'amount', title: '申请退款金额',formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }},
                {field: 'regUserId', title: '申请信息',formatter: function (value, row, index) {
                        return row.regUserName + " " + row.regDate + "<br>" + ($.table.tooltip(row.applyMemo, 15)||'');
                    }},
                {field: 'skmTid', title: '收款码/银行卡',formatter: function (value, row, index) {
                    if (value) {
                        return '<a href="javascript:viewfj(\'' + value + '\')">查看</a>'
                    }
                }},
                {field: 'verifyUserId', title: '审核记录',formatter: function (value, row, index) {
                    if (value) {
                        return row.verifyUserName + " " + row.verifyTime + "<br>" + ($.table.tooltip(row.verifyMemo,15) || '');
                    }
                }},
                {field: 'backUserId', title: '退款记录',formatter: function (value, row, index) {
                    if (value) {
                        return row.backUserName + " " + row.backTime + "<br>" + (row.backTid ? '<a href="javascript:viewfj(\''+row.backTid+'\')">凭证</a>' : '');
                    }
                }}
            ]
        };
        $.table.init(options);
    }

    function searchx() {
        var data = {};
        /*var range = $('[name="params[writeOffTimeRange]"]').val();
        if (range) {
            var strings = range.split(" - ");
            data['params[writeOffTimeStart]'] = strings[0];
            data['params[writeOffTimeEnd]'] = strings[1];
        }*/
        $.table.search('role-form', data);
    }

    function resetx() {
        $("#role-form")[0].reset();
        $('.selectpicker').selectpicker('refresh');
        searchx();
    }


    const globalBatchUploading = {}

    function detail(carrierId) {
        layer.open({
            title: '定金明细',
            type: 2,
            skin: '',
            maxmin: true,
            shadeClose: true,
            area: [document.documentElement.clientWidth - 100 + 'px', document.documentElement.clientHeight - 100 + 'px'],
            content: prefix + "/carrier-deposit-dtl?carrierId="+carrierId,
            btn: ['关闭']
        })
        //$.modal.open('定金明细', prefix + "/carrier-deposit-dtl?carrier_id="+carrierId, document.documentElement.clientWidth - 100, document.documentElement.clientHeight - 100)
    }

    function pay_back() {

    }

    /*function applyBack() {
        let row = $.btTable.bootstrapTable('getSelections')[0];
        layer.open({
            type: 1,
            title: '申请退款',
            content: $('#applyPanel'),
            skin: '',
            area: ['560px', '320px'],
            zIndex: 10,
            offset: '100px',
            btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
            end: function () {
                $('#sqthje').val('');
                $('#sqsy').val('');
            },
            success: function (layero, index) {
                $('#cysid').val(row.carrierId);
                $('#cys').text(row.carrName);
                $('#ksqje').text(row.balance);
                $('#sqthje').attr('max', row.balance);
            },
            btn1: function (index, layero) {
                if ($("#applyForm").validate().form()) {
                    $.modal.confirm('确认提交吗？', function () {
                        $.operate.submit(prefix + "/applyBack", "post", "json", $('#applyForm').serialize(), function (res) {
                            if (res.code == 0) {
                                layer.close(index)
                            }
                        });
                    });
                }
            }
        });
    }*/

    function verify(idx) {
        let row = $.btTable.bootstrapTable('getData')[idx];
        layer.open({
            type: 1,
            title: '定金退款审核',
            content: $('#verifyPanel'),
            skin: 'skin1',
            area: ['560px', '420px'],
            zIndex: 10,
            offset: '100px',
            btn: ['<i class="fa fa-check"></i> 同意', '<i class="fa fa-remove"></i> 不同意', '取消'],
            end: function () {
                $('#verifyForm').find('[sqsy]').html('');
                $('#verifyForm').find('[name=verifyMemo]').val('');
            },
            success: function (layero, index) {
                $('#verifyForm').find('[name=id]').val(row.id);
                $('#verifyForm').find('[cys]').text(row.carrName);
                $('#verifyForm').find('[tkje]').text(row.amount.toFixed(2));
                $('#verifyForm').find('[tkhje]').text('...');
                $.ajax({
                    url: prefix + "/balance",
                    data: 'carrierId=' + row.carrierId,
                    cache: false,
                    success: function (res) {
                        $('#verifyForm').find('[tkhje]').text(new BigNumber(res.data).minus(row.amount).toNumber().toFixed(2));
                    }
                })
                $('#verifyForm').find('[sqsy]').html(row.applyMemo||'<span style="color:#999999">未填写</span>');
            },
            btn1: function (index, layero) {
                $('#verifyForm').find('[name=verifyMemo]').prop('required',false)
                if ($("#verifyForm").validate().form()) {
                    let data = $('#verifyForm').serialize();
                    data = data + "&agree=1";
                    $.modal.confirm('确认同意吗？', function () {
                        $.operate.submit(prefix + "/doVerify", "post", "json", data, function (res) {
                            if (res.code == 0) {
                                layer.close(index)
                            }
                        });
                    });
                }
            },
            btn2: function (index, layero) {
                $('#verifyForm').find('[name=verifyMemo]').prop('required',true)
                if ($("#verifyForm").validate().form()) {
                    let data = $('#verifyForm').serialize();
                    data = data + "&agree=2";
                    $.modal.confirm('确认不同意吗？', function () {
                        $.operate.submit(prefix + "/doVerify", "post", "json", data, function (res) {
                            if (res.code == 0) {
                                layer.close(index)
                            }
                        });
                    });
                }
                return false;
            }
        })
    }

    function refund(idx) {
        let row = $.btTable.bootstrapTable('getData')[idx];
        $("#payForm").validate().resetForm();
        $("#payForm").find('.error').removeClass("error")
        layer.open({
            type: 1,
            title: '退还定金',
            content: $('#payPanel'),
            skin: '',
            area: ['560px', '480px'],
            zIndex: 10,
            offset: '100px',
            btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
            end: function(){
                $('#backTid').val('');
                $("#payForm").find('[dqye]').text('');
                $('#payPanel').find(':input[type=file]').each(function(){
                    $(this).fileinput('destroy');
                });
            },
            success: function (layero, index) {
                $.ajax({
                    url: prefix + "/balance",
                    data: 'carrierId=' + row.carrierId,
                    cache: false,
                    success: function (res) {
                        $("#payForm").find('[dqye]').text(res.data);
                    }
                })
                $('#payPanel').find('[name=id]').val(row.id);
                $('#payPanel').find('[thje]').text(row.amount);
                $('#payPanel').find('[cys]').text(row.carrName);
                layero.find(':input[type=file]').each(function(idx){
                    var uploadCache = {}
                    let field = $(this).attr('name');
                    /*let hideName = field + "_hide_tid";
                    if ($(this).siblings().find('[name="' + hideName + '"]').length == 0) {
                        $(this).after('<input type="hidden" name="' + hideName + '"/>');
                    }*/
                    let option = {
                        theme: "explorer-fa5", //主题
                        language: 'zh',
                        uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                        //deleteUrl: ctx + "common/deleteImage",
                        uploadExtraData: {key: field},   //上传id，传入后台的参数
                        deleteExtraData: {key: 'id'},
                        // extra" {key: ''}, // 上面两个一致则可使用该字段？
                        enctype: 'multipart/form-data',
                        allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                        initialPreviewAsData: true,
                        overwriteInitial: false,
                        //initialPreviewConfig: [
                        //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                        //],
                        //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                        maxFileCount: 0, // 0:不限制上传数
                        showUpload: false,  // 不显示上传按钮，选择后直接上传
                        //previewClass:"uploadPreview",
                        minFileSize: 5, // 5KB
                        previewFileIcon: '<i class="fa fa-file"></i>',
                        allowedPreviewTypes: ['image'],
                        showClose: false,  //是否显示右上角叉按钮
                        showUpload: false, //是否显示下方上传按钮
                        showRemove: false, // 是否显示下方移除按钮
                        //autoReplace: true,
                        //showPreview: false,//是否显示预览(false=只剩按钮)
                        showCaption: false,//底部上传按钮左侧文本
                        uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                        fileActionSettings: {
                            showUpload: false,		//每个文件的上传按钮
                            showDrag: false,
                            //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                        },
                    };
                    $(this).fileinput(option).on("filebatchselected", function (e, files) {
                        globalBatchUploading[field+'_'+idx] = 1;
                        $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                    }).on("fileuploaded", function (event, data, previewId, index) {
                        var code = data.response.code;
                        if (code !== 0) {
                            $.modal.closeLoading();
                            $.modal.alertError("上传失败：" + data.response.msg);
                            return;
                        }

                        var tid = data.response.tid;
                        uploadCache[previewId] = tid;
                        var curTids = []
                        for (const uploadCacheKey in uploadCache) {
                            if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                curTids.push(uploadCache[uploadCacheKey])
                            }
                        }
                        $('#payForm').find('[name=backTid]').val(curTids.join(','))
                    }).on('filesuccessremove', function (event, previewId, index) {
                        //上传后删除事件
                        var tid = uploadCache[previewId];
                        if (tid) {
                            $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                                console.log(result)
                            }, 'json')
                        }
                        delete uploadCache[previewId]
                        var curTids = []
                        for (const uploadCacheKey in uploadCache) {
                            if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                curTids.push(uploadCache[uploadCacheKey])
                            }
                        }
                        $('#payForm').find('[name=backTid]').val(curTids.join(','))
                    }).on('filebatchuploadcomplete', function () {
                        delete globalBatchUploading[field+'_'+idx];
                    });
                })
            },
            btn1: function (index, layero) {
                if ($('#payForm').find('[name=backTid]').val() == '') {
                    $.modal.msgWarning("请上传退还凭证");
                    return
                }
                if ($("#payForm").validate().form()) {
                    $.modal.confirm('确认提交吗？', function(){
                        $.operate.submit(prefix + "/backOff", "post", "json", $('#payForm').serialize(), function(res){
                            if (res.code == 0) {
                                layer.close(index)
                            }
                        });
                    });
                }
            }
        })
    }

    function viewfj(tid) {
        $.ajax({
            url: ctx + 'common/tid-files/' + tid,
            cache: false,
            success: function(res) {
                if (res.code == 0) {
                    let tmp = ['<div style="display: flex;flex-wrap: wrap;" class="imgPreview">'];
                    for (let i = 0; i < res.data.length; i++) {
                        tmp.push('<img src="',res.data[i].filePath,'" style="width:90px;height:60px;margin:10px 0 0 10px;"/>');
                    }
                    tmp.push('</div>');
                    layer.open({
                        type: 1,
                        skin: null,
                        content: tmp.join(''),
                        area: ['510px', '400px'],
                        offset: '100px',
                        title: '查看凭证',
                        btn: ['关闭'],
                        success: function(layero, index) {
                            layero.find('.imgPreview').viewer({
                                url: 'data-original',
                                title: false,
                                navbar: false,
                            });
                        }
                    })
                } else {
                    $.modal.alertError(res.msg);
                }
            }
        })
    }
</script>
</body>
</html>