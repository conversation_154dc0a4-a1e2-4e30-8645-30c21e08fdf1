<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('熟车定金管理')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        .label-primaryT{
            color: #1ab394;
            background-color: transparent;
            border: 1px solid #1ab394;
        }
        .label-successT{
            color: #1c84c6;
            background-color: transparent;
            border: 1px solid #1c84c6;
        }
        .label-defaultT{
            color: #5e5e5e;
            background-color: transparent;
            border: 1px solid #5e5e5e;
        }
        .label-warningT{
            color: #f8ac59;
            background-color: transparent;
            border: 1px solid #f8ac59;
        }
        .label-infoT{
            color: #23c6c8;
            background-color: transparent;
            border: 1px solid #23c6c8;
        }
        .label-dangerT{
            color: #ed5565;
            background-color: transparent;
            border: 1px solid #ed5565;
        }
        .label-inverseT{
            color: #262626;
            background-color: transparent;
            border: 1px solid #262626;
        }
        .primary {
            color: #409EFF;
        }
        .flex{
            display: flex;
            align-items:start;
            justify-content:space-between;
        }
        .flex_left{
            width: 100px;
            line-height: 26px;
            text-align: right;
        }
        .flex_right{
            flex: 1;
            width: 0;
        }
        .fake-form-control {
            width: 100%;
        }
        .fake-form-control .el-input__inner{
            border: 1px solid #e5e6e7;
            border-radius: 1px;
            line-height: 26px;
            height: 26px;
            font-size: 13px;
            padding: 2px 4px;
        }
        .fake-form-control .el-input {
            position: static !important;
        }
        .fake-form-control .el-input__icon {
            width: 15px!important;
            line-height: 26px!important;
        }
        .abbrpop {
            width:400px !important;
        }
        .file-preview {
            border: none !important;
            padding: 0 !important;
        }
        .file-drop-zone {
            margin: 0 !important;
        }
        .table-striped {
            height: calc(100% - 80px);
        }
    </style>
</head>

<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="carrierId" th:value="${carrierId}">
                <div class="row">
                    <div class="form-group">

                        <div class="col-md-2 col-sm-4">
                            <input name="params[writeOffTimeRange]" class="form-control laydateRange" type="text"
                                   placeholder="核销时间范围" autocomplete="off" aria-required="true">
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="add()" shiro:hasPermission="carrier:deposit:crud">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger disabled multiple" onclick="del()" shiro:hasPermission="carrier:deposit:crud">
                <i class="fa fa-close"></i> 删除
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<div id="addPanel" style="display: none;padding: 15px 20px 0;">
    <form id="crudForm">
        <div class="flex">
            <span class="flex_left"><span class="fcff3" v-if="act=='edit'">*</span> 熟车承运商：</span>
            <span class="flex_right">
                <!--<el-autocomplete v-if="act=='edit'" class="fake-form-control" v-model="form.carrName" required name="carrName"
                                 :fetch-suggestions="querySearch" value-key="carrName" placeholder="承运商名称关键字(只匹配单笔熟车)"
                                 :trigger-on-focus="false" @select="handleSelect" popper-class="abbrpop">
                    <template slot-scope="{item}"><div>{{item.carrName}} <span style="color:#aaa">{{item.contact}} {{item.phone}}</span></div></template>
                </el-autocomplete>-->
                <div class="form-control" style="line-height: 22px">{{form.carrName}}</div>
            </span>
        </div>
        <div class="flex" style="margin-top: 5px">
            <span class="flex_left"><span class="fcff3" v-if="act=='edit'">*</span> 定金金额：</span>
            <span class="flex_right">
                <input class="form-control" v-if="act=='edit'" v-model="form.amount" placeholder="定金金额" required name="amount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)">
                <div v-else class="form-control" style="line-height: 22px">{{form.amount}}</div>
            </span>
        </div>
        <div v-if="act == 'writeOff'" class="flex" style="margin-top: 5px">
            <span class="flex_left"><span class="fcff3">*</span> 核销凭证：</span>
            <span class="flex_right">
                <input type="file" name="writeOffTid" multiple>
            </span>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<link th:href="@{/element-ui@2.15.13/lib/theme-chalk/index.css}" rel="stylesheet"/>
<script th:src="@{/js/vue.min.js}"></script>
<script th:src="@{/element-ui@2.15.13/lib/index.js}"></script>
<th:block th:include="include :: bootstrap-fileinput553-css"/>
<th:block th:include="include :: bootstrap-fileinput553-js"/>
<script th:inline="javascript">
    var prefix = ctx + "new-deposit";
    const carrName = [[${carrier?.carrName}]];
    const carrierId = [[${carrier?.carrierId}]];

    //初始化查询条件传参
    queryParams = function (params) {
        var search = {};
        $.each($("#role-form").serializeArray(), function (i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchx();
            }
        });

        initTable()

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '.laydateRange',
                type: 'date',
                trigger: 'click',
                range: true,
                rangeLinked: true
            });
        });

        if ($.validator) {
            $.validator.prototype.elements = function () {
                var validator = this,
                    rulesCache = {};
                return $([]).add(this.currentForm.elements)
                    .filter(":input")
                    .not(":submit, :reset, :image, [disabled]")
                    .not(this.settings.ignore)
                    .filter(function () {
                        var elementIdentification = this.id || this.name;
                        !elementIdentification && validator.settings.debug && window.console && console.error("%o has no id nor name assigned", this);
                        if (elementIdentification in rulesCache || !validator.objectLength($(this).rules()))
                            return false;
                        rulesCache[elementIdentification] = true;
                        return true;
                    });
            };
        }

    });

    function initTable() {
        var options = {
            url: prefix + "/dtl-list",
            queryParams: queryParams,
            uniqueId: 'dtlId',
            showToggle: false,
            showColumns: true,
            //fixedColumns: true,
            //fixedNumber: 4,
            clickToSelect: true,
            height: 620,
            showFooter: true,
            showExport: false,
            onPostBody: function () {
                //合并页脚
            },
            onRefresh: function (params) {
            },
            onCheck: function (row, $element) {
            },
            onUncheck: function (row, $element) {
            },
            onCheckAll: function (rowsAfter) {
            },
            onUncheckAll: function () {
            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "";
                    }
                },
                {field: 'id', title: '操作', formatter: function (value, row, index) {
                        let actions = [];
                        if (row.status == 0) {
                            /*[# th:if="${@shiroUtils.getSubject().isPermitted('carrier:deposit:crud')}"]*/
                            if (row.source == 1) {
                                actions.push("<a href='javascript:add(" + index + ")'>修改</a> ")
                            }
                            /*[/]*/
                            /*[# th:if="${@shiroUtils.getSubject().isPermitted('carrier:deposit:writeoff')}"]*/
                            actions.push("<a href='javascript:writeoff("+index+")'>收款核销</a> ")
                            /*[/]*/
                        }
                        return actions.join("");
                    }
                },
                {field: 'status', title: '状态', formatter: function (value, row, index) {
                        let t = value
                        if (value == 0) {
                            t =  '<span class="label label-defaultT">新建</span>'
                        } else if (value == 1) {
                            t =  '<span class="label label-primaryT">已核销</span>'
                        }else if (value == 2) {
                            t =  '<span class="label label-primaryT">加入对账包</span>'
                        }
                        return t;
                    }
                },
                {field: 'amount', title: '定金金额',align:'right',formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }},
                {field: 'source', title: '数据来源',formatter: function (value, row, index) {
                        let html = ''
                        if (value == 1) {
                            html = '手动新增'
                        }else if (value == 2) {
                            html = '运费转入<br>' + row.sourceVbillno
                        }else if (value == 3) {
                            html = '非熟车定金转入<br>' + row.sourceVbillno
                        }
                        return html;
                    }},
                {field: 'corUserName', title: '编辑人'},
                {field: 'corDate', title: '编辑时间'},
                {field: 'writeOffUserName', title: '核销人'},
                {field: 'writeOffTime', title: '核销时间'},
                {field: 'writeOffTid', title: '核销凭证', formatter: function (value, row, index) {
                    if (value) {
                        return '<a href="javascript:view(\'' + value + '\');">查看</a>'
                    }
                }}
            ]
        };
        $.table.init(options);
    }

    function searchx() {
        var data = {};
        var range = $('[name="params[writeOffTimeRange]"]').val();
        if (range) {
            var strings = range.split(" - ");
            data['params[writeOffTimeStart]'] = strings[0];
            data['params[writeOffTimeEnd]'] = strings[1];
        }
        $.table.search('role-form', data);
    }

    function resetx() {
        $("#role-form")[0].reset();
        $('.selectpicker').selectpicker('refresh');
        searchx();
    }

    let vue = new Vue({
        el: '#addPanel',
        data: ()=>{
            return {
                act: '',
                form: {}
            }
        },
        methods: {
            querySearch(queryString, cb) {
                if(!queryString) {
                    cb([])
                    return;
                }
                $.ajax({
                    url: prefix + '/carrQuery',
                    data: "pageSize=20&pageNum=1&carrName="+encodeURIComponent(queryString||''),
                    type: 'post',
                    success: function(results) {
                        // 调用 callback 返回建议列表的数据
                        cb(results);
                    }
                })
            },
            handleSelect(item) {
                this.form.carrierId = item.carrierId;
            },
        }
    });

    function add(idx) {
        $("#crudForm").validate().resetForm();
        $("#crudForm").find('.error').removeClass("error")
        vue.act = 'edit'
        if (idx != undefined) {
            let row = $.btTable.bootstrapTable('getData')[idx];
            let {dtlId,amount} = row;
            vue.form = {dtlId,carrierId,carrName,amount}
        } else {
            vue.form = {carrName: carrName, carrierId: carrierId};
        }
        layer.open({
            type: 1,
            title: (idx == undefined ? '新增' : '修改') + '定金',
            content: $('#addPanel'),
            skin: '',
            area: ['460px', '220px'],
            zIndex: 10,
            offset: '100px',
            btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
            btn1: function (index, layero) {
                if (!$('#crudForm').validate().form())
                    return;
                if (vue.form.amount <= 0) {
                    $.modal.msgWarning("金额必须大于0");
                    return;
                }
                $.modal.confirm('确认提交吗？', function(){
                    $.operate.submit(prefix + "/submit", "post", "json", vue.form, function(res){
                        if (res.code == 0) {
                            layer.close(index);
                            parent.$.table.refresh();
                        }
                    });
                });
            }
        })
    }

    function del() {
        var selected = $.btTable.bootstrapTable('getSelections');
        let ids = [];
        for (let i = 0; i < selected.length; i++) {
            if (selected[i].status == 1) {
                $.modal.msgWarning("已核销的数据不可删除");
                return
            }
            ids.push(selected[i].dtlId);
        }
        $.modal.confirm("确认删除这" + ids.length + "条单据？", function(){
            $.operate.submit(prefix + "/remove-dtl", "post", "json", {"ids":ids.join(',')}, function(res){
                if (res.code == 0) {
                    parent.$.table.refresh();
                }
            });
        })
    }

    function writeoff(idx) {
        $("#crudForm").validate().resetForm();
        $("#crudForm").find('.error').removeClass("error")
        let row = $.btTable.bootstrapTable('getData')[idx];
        let {dtlId,amount,corDate} = row;
        vue.form = {dtlId,carrName,amount,corDate}
        vue.act = 'writeOff';
        vue.$nextTick(()=>{
            layer.open({
                type: 1,
                title: '定金收取',
                content: $('#addPanel'),
                skin: '',
                area: ['560px', '360px'],
                zIndex: 10,
                offset: '100px',
                btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
                end: function(){
                    vue.act = '';
                },
                success: function (layero, index) {
                    const globalBatchUploading = {}
                    layero.find(':input[type=file]').each(function(idx){
                        var uploadCache = {}
                        let field = $(this).attr('name');
                        /*let hideName = field + "_hide_tid";
                        if ($(this).siblings().find('[name="' + hideName + '"]').length == 0) {
                            $(this).after('<input type="hidden" name="' + hideName + '"/>');
                        }*/
                        let option = {
                            theme: "explorer-fa5", //主题
                            language: 'zh',
                            uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                            //deleteUrl: ctx + "common/deleteImage",
                            uploadExtraData: {key: field},   //上传id，传入后台的参数
                            deleteExtraData: {key: 'id'},
                            // extra" {key: ''}, // 上面两个一致则可使用该字段？
                            enctype: 'multipart/form-data',
                            allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                            initialPreviewAsData: true,
                            overwriteInitial: false,
                            //initialPreviewConfig: [
                            //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                            //],
                            //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                            maxFileCount: 0, // 0:不限制上传数
                            showUpload: false,  // 不显示上传按钮，选择后直接上传
                            //previewClass:"uploadPreview",
                            minFileSize: 5, // 5KB
                            previewFileIcon: '<i class="fa fa-file"></i>',
                            allowedPreviewTypes: ['image'],
                            showClose: false,  //是否显示右上角叉按钮
                            showUpload: false, //是否显示下方上传按钮
                            showRemove: false, // 是否显示下方移除按钮
                            //autoReplace: true,
                            //showPreview: false,//是否显示预览(false=只剩按钮)
                            showCaption: false,//底部上传按钮左侧文本
                            uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                            fileActionSettings: {
                                showUpload: false,		//每个文件的上传按钮
                                showDrag: false,
                                //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                            },
                        };
                        $(this).fileinput(option).on("filebatchselected", function (e, files) {
                            //uploading = true;
                            console.log('filebatchselected', files)
                            globalBatchUploading[field+'_'+idx] = 1;
                            $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                        }).on("fileuploaded", function (event, data, previewId, index) {
                            //单个上传成功事件
                            console.log("fileuploaded", event, data, previewId, index)
                            var code = data.response.code;
                            if (code !== 0) {
                                $.modal.closeLoading();
                                $.modal.alertError("上传失败：" + data.response.msg);
                                return;
                            }

                            var tid = data.response.tid;
                            uploadCache[previewId] = tid;
                            var curTids = []
                            for (const uploadCacheKey in uploadCache) {
                                if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                    curTids.push(uploadCache[uploadCacheKey])
                                }
                            }
                            vue.$set(vue.form, field, curTids.join(','))
                        }).on('filesuccessremove', function (event, previewId, index) {
                            //上传后删除事件
                            console.log("filesuccessremove", event, previewId, index)
                            var tid = uploadCache[previewId];
                            if (tid) {
                                $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                                    console.log(result)
                                }, 'json')
                            }
                            delete uploadCache[previewId]
                            var curTids = []
                            for (const uploadCacheKey in uploadCache) {
                                if (curTids.indexOf(uploadCache[uploadCacheKey]) < 0) {
                                    curTids.push(uploadCache[uploadCacheKey])
                                }
                            }
                            //$('[name="' + hideName + '"]').val(curTids.join(','));
                            vue.$set(vue.form, field, curTids.join(','))
                        }).on('filebatchuploadcomplete', function (a, b, c) {
                            console.log('filebatchuploadcomplete', a, b, c)
                            delete globalBatchUploading[field+'_'+idx];
                        });
                    })
                },
                btn1: function (index, layero) {
                    if (!vue.form['writeOffTid']) {
                        $.modal.msgWarning("请上传核销凭证");
                        return
                    }
                    $.modal.confirm('确认核销吗？', function(){
                        $.operate.submit(prefix + "/writeOff", "post", "json", vue.form, function(res){
                            if (res.code == 0) {
                                layer.close(index)
                                parent.$.table.refresh();
                            }
                        });
                    });
                }
            })
        })
    }

    function view(tid) {
        $.ajax({
            url: ctx + 'common/tid-files/' + tid,
            cache: false,
            success: function(res) {
                if (res.code == 0) {
                    let tmp = ['<div style="display: flex;flex-wrap: wrap;" class="imgPreview">'];
                    for (let i = 0; i < res.data.length; i++) {
                        tmp.push('<img src="',res.data[i].filePath,'" style="width:90px;height:60px;margin:10px 0 0 10px;"/>');
                    }
                    tmp.push('</div>');
                    layer.open({
                        type: 1,
                        skin: null,
                        content: tmp.join(''),
                        area: ['510px', '400px'],
                        offset: '100px',
                        title: '查看凭证',
                        btn: ['关闭'],
                        success: function(layero, index) {
                            layero.find('.imgPreview').viewer({
                                url: 'data-original',
                                title: false,
                                navbar: false,
                            });
                        }
                    })
                } else {
                    $.modal.alertError(res.msg);
                }
            }
        })
    }
</script>
</body>
</html>