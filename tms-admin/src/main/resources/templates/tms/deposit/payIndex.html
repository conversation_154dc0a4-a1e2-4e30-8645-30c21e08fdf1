<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('定金付款')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <style type="text/css">
        .label-primaryT {
            color: #1ab394;
            background-color: transparent;
            border: 1px solid #1ab394;
        }

        .label-successT {
            color: #1c84c6;
            background-color: transparent;
            border: 1px solid #1c84c6;
        }

        .label-defaultT {
            color: #5e5e5e;
            background-color: transparent;
            border: 1px solid #5e5e5e;
        }

        .label-warningT {
            color: #f8ac59;
            background-color: transparent;
            border: 1px solid #f8ac59;
        }

        .label-infoT {
            color: #23c6c8;
            background-color: transparent;
            border: 1px solid #23c6c8;
        }

        .label-dangerT {
            color: #ed5565;
            background-color: transparent;
            border: 1px solid #ed5565;
        }

        .label-inverseT {
            color: #262626;
            background-color: transparent;
            border: 1px solid #262626;
        }

        .primary {
            color: #409EFF;
        }

        .flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .flex_left {
            width: 100px;
            line-height: 26px;
            text-align: right;
        }

        .flex_right {
            flex: 1;
            width: 0;
        }

        .fake-form-control {
            width: 100%;
        }

        .fake-form-control .el-input__inner {
            border: 1px solid #e5e6e7;
            border-radius: 1px;
            line-height: 26px;
            height: 26px;
            font-size: 13px;
            padding: 2px 4px;
        }

        .fake-form-control .el-input {
            position: static !important;
        }

        .fake-form-control .el-input__icon {
            width: 15px !important;
            line-height: 26px !important;
        }

        .abbrpop {
            width: 400px !important;
        }

        .file-preview {
            border: none !important;
            padding: 0 !important;
        }

        .file-drop-zone {
            margin: 0 !important;
        }
    </style>
</head>

<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="form-group">
                        <div class="col-md-2 col-sm-4">
                            <select name="vbillstatus" class="form-control selectpicker" data-none-selected-text="定金单状态">
                                <option></option>
                                <option value="0">新建</option>
                                <option value="6" selected>已申请</option>
                                <option value="3">部分核销</option>
                                <option value="7">核销中</option>
                                <option value="4">已核销</option>
                            </select>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <input name="carrName" class="form-control" placeholder="承运商名称" autocomplete="off"
                                   aria-required="true">
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <input name="lotno" class="form-control" placeholder="运单号" autocomplete="off"
                                   aria-required="true">
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <select id="billingCorp" name="billingCorp" class="form-control selectpicker" data-none-selected-text="结算公司"
                                    th:with="type=${@dict.getType('bala_corp')}">
                                <option value=""></option>
                                <option th:each="dict : ${type}"
                                        th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchx()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="batchPay()">
                <i class="fa fa-rmb"></i> 付款
            </a>
            <a class="btn btn-warning multiple disabled" onclick="batchPayTogether()">
                <i class="fa fa-rmb"></i> 现金批量付款
            </a>
            <a class="btn btn-success single disabled" onclick="payRecord()">
                <i class="fa fa-clock-o"></i> 付款记录
            </a>
            <a class="btn btn-danger single disabled" onclick="cancelAppl()">
                <i class="fa fa-reply"></i> 撤销申请
            </a>
            <a class="btn btn-danger single disabled" onclick="payBack()">
                <i class="fa fa-reply"></i> 付款反核销
            </a>

        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>

<th:block th:include="include :: bootstrap-fileinput553-css"/>
<th:block th:include="include :: bootstrap-fileinput553-js"/>
<script th:inline="javascript">
    var prefix = ctx + "new-deposit";

    //初始化查询条件传参
    queryParams = function (params) {
        var search = {};
        $.each($("#role-form").serializeArray(), function (i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchx();
            }
        });

        initTable()

        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '.laydateRange',
                type: 'date',
                trigger: 'click',
                range: true,
                rangeLinked: true
            });
        });

        if ($.validator) {
            $.validator.prototype.elements = function () {
                var validator = this,
                    rulesCache = {};
                return $([]).add(this.currentForm.elements)
                    .filter(":input")
                    .not(":submit, :reset, :image, [disabled]")
                    .not(this.settings.ignore)
                    .filter(function () {
                        var elementIdentification = this.id || this.name;
                        !elementIdentification && validator.settings.debug && window.console && console.error("%o has no id nor name assigned", this);
                        if (elementIdentification in rulesCache || !validator.objectLength($(this).rules()))
                            return false;
                        rulesCache[elementIdentification] = true;
                        return true;
                    });
            };
        }

    });

    var payDetailStatusEnum = [[${payDetailStatusEnum}]];

    function initTable() {
        var options = {
            url: prefix + "/paylist",
            queryParams: queryParams,
            uniqueId: 'id',
            showToggle: false,
            showColumns: true,
            //fixedColumns: true,
            //fixedNumber: 4,
            clickToSelect: true,
            height: 620,
            showFooter: true,
            showExport: false,
            onPostBody: function () {
                //合并页脚
            },
            onRefresh: function (params) {
            },
            onCheck: function (row, $element) {
            },
            onUncheck: function (row, $element) {
            },
            onCheckAll: function (rowsAfter) {
            },
            onUncheckAll: function () {
            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "";
                    }
                },
                /*{
                    field: 'id', title: '操作', formatter: function (value, row, index) {
                        let actions = [];
                        if (row.status == 0) {

                        }
                        return actions.join("");
                    }
                },*/
                {field: 'transLineName', title: '调度组',formatter:function (value,row,index) {
                        return `${value} <br /> ${row.billingCorp}`
                    }
                },
                {
                    field: 'vbillno', title: '定金单号/状态', formatter: function (value, row, index) {
                        let txt = row.vbillno;
                        let labelClass = "";
                        if (row.vbillstatus == 0) {
                            labelClass = "label-default";
                        } else if (row.vbillstatus == 6) {
                            labelClass = "label-warning";
                        } else if (row.vbillstatus == 4) {
                            labelClass = "label-success";
                        } else if (row.vbillstatus == 7 || row.vbillstatus == 3) {
                            labelClass = "label-primary";
                        }
                        txt += ('<br>' + '<span class="label ' + labelClass + '">' + $.table.selectEnumContext(payDetailStatusEnum, row.vbillstatus) + '</span>');
                        if (row.bankBackFlag == 1){
                            txt += '<span class="label label-danger" style="margin-left: 2px;">银退</span>';
                        }
                        if (row.writeOffTo == 1) {
                            txt += '<span class="label label-successT" style="margin-left: 2px;">转熟车定金</span>';
                        }
                        return txt;
                    }
                },
                {
                    title: '申请信息',
                    field: 'applyUserName',
                    formatter: function (value, row, index) {
                        if (value) {
                            return value + '&nbsp;&nbsp;' + row.applyDate + '<br />' + (row.applyMemo || '');
                        }
                    }
                },
                {
                    title: '金额明细',
                    field: 'amount',
                    formatter: function (value, row, index) {
                        let data = [];
                        if (row.amount != null) {
                            data.push(`<span class="label label-primary pa2">总额</span> ` + row.amount.toLocaleString('zh', {
                                style: 'currency',
                                currency: 'CNY'
                            }))
                        }
                        if (row.gotAmount) {
                            data.push(`<span class="label badge-info pa2">已付</span> ` + row.gotAmount.toLocaleString('zh', {
                                style: 'currency',
                                currency: 'CNY'
                            }))
                        }
                        if (row.ungotAmount) {
                            data.push(`<span class="label label-coral pa2">未付</span> ` + row.ungotAmount.toLocaleString('zh', {
                                style: 'currency',
                                currency: 'CNY'
                            }))
                        }
                        return data.join("<br/>")
                    }
                },
                {
                    title: '收款信息',
                    align: 'left',
                    field: 'recAccount',
                    formatter: function(value, row, index) {
                        if (value) {
                            return value + '&nbsp;&nbsp;' + row.recCardNo + '<br />' + row.recBank
                        }
                    }
                },
                {
                    title: '要求支付日期',
                    align: 'left',
                    field: 'reqPayDate',
                    formatter: function(row,value) {
                        if (value.reqPayDate){
                            var reqPayDate = new Date(Date.parse(value.reqPayDate));
                            value.reqPayDate.substr(0,10)
                            var now = new Date();
                            //如果要求支付日期小于当前时间,则标红
                            if (reqPayDate <= now) {
                                return '<span class="label label-danger">' + value.reqPayDate.substr(0,10) + '</span>';
                            } else {
                                return value.reqPayDate.substr(0,10);
                            }
                        }
                    }
                },
                {
                    title: '承运商/身份证',
                    align: 'left',
                    field: 'carrName',
                    formatter: function status(value, row, index) {
                        return value + '<br />' + row.legalCard
                    }
                },
                {
                    title: '创建人/时间',
                    align: 'left',
                    field: 'regUserName',
                    formatter: function (value, row, index) {
                        return  value + '<br />' + row.regDate
                    }
                },
                {
                    title: '运单号',
                    align: 'left',
                    field: 'lotno'
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(!row.deliAddr){
                            row.deliAddr = '';
                        }
                        if(!row.arriAddr){
                            row.arriAddr = ''
                        }
                        if(row.deliAddr == "" && row.arriAddr == ""){
                            return "";
                        }else{
                            return `<span class="label label-warning pa2">提</span>`+row.deliAddr+`<br/><span class="label label-success pa2">到</span>`+row.arriAddr;
                        }

                    }
                }
            ]
        };
        $.table.init(options);
    }

    function searchx() {
        var data = {};
        var range = $('[name="params[writeOffTimeRange]"]').val();
        if (range) {
            var strings = range.split(" - ");
            data['params[writeOffTimeStart]'] = strings[0];
            data['params[writeOffTimeEnd]'] = strings[1];
        }
        $.table.search('role-form', data);
    }

    function resetx() {
        $("#role-form")[0].reset();
        $('.selectpicker').selectpicker('refresh');
        searchx();
    }

    function batchPay() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        if (bootstrapTable[0]["vbillstatus"] !== 6) {
            $.modal.msgWarning("请选择已申请单据");
            return;
        }
        //总金额 == 已付金额 && vbillstatus == 已核销
        //if (bootstrapTable[0]["amount"] === bootstrapTable[0]["gotAmount"] && bootstrapTable[0]["vbillstatus"] == 4) {
        //    $.modal.msgWarning("该单已核销");
        //    return;
        //}
        var url = prefix + "/batchPay?id="+bootstrapTable[0]["id"];
        $.modal.open('定金付款', url);
    }

    //付款记录
    function payRecord() {
        var id = $.table.selectColumns('id');
        var url =  ctx + "payManage/payRecord?payDetailId="+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            //shade: false,
            title: "付款记录",
            area: [1000 + 'px', 700 + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    function payBack() {
        var id = $.table.selectColumns("id");
        if (id.length != 1 ) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        var vbillstatus = $.table.selectColumns("vbillstatus");
        if (vbillstatus != 4 && vbillstatus != 3) {
            $.modal.alertWarning("请选择已核销的单据");
            return;
        }
        $.modal.open("付款反核销", prefix + "/back_pay/" + id,800,700);
    }

    function cancelAppl() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 6) {
                $.modal.alertWarning("只能为已申请状态下的单据撤销申请");
                return;
            }
        }
        var ids = $.table.selectColumns("id");
        $.modal.confirm("确认要将选中的数据撤销申请吗?", function () {
            $.operate.post(prefix + "/cancel_appl", "ids="+ids);
        });
    }
    function batchPayTogether() {
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        //var balaCorp = bootstrapTable[0]["balaCorp"];
        for(var i = 0;i<bootstrapTable.length;i++){
            if (bootstrapTable[i]["vbillstatus"] !== 6) {
                $.modal.alertWarning("请选择已申请状态的数据");
                return;
            }
            //总金额 == 已付金额 && vbillstatus == 已核销
            if (bootstrapTable[i]["amount"] === bootstrapTable[i]["gotAmount"] || bootstrapTable[0]["vbillstatus"] == 4) {
                $.modal.alertWarning("支付已完成");
                return;
            }
            //负数 加入对账单付款
            if(bootstrapTable[i]["amount"] < 0 ){
                $.modal.alertWarning("该应付单总金额为负数，请加入对账单付款");
                return;
            }
            //判断结算公司
            /*if(bootstrapTable[i]["balaCorp"] != balaCorp){
                $.modal.alertWarning("请选择结算公司一致的应付单");
                return;
            }*/

        }
        var url = prefix + "/batchPayTogether?ids="+$.table.selectColumns('id');
        $.modal.open('现金批量付款',url);
    }
</script>
</body>
</html>