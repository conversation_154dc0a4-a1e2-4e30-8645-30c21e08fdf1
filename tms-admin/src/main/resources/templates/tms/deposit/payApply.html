<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('定金付款申请')"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <style>
        .fw{
            font-weight: bold;
        }
        .flex{
            display: flex;
            algin-items:center;
            just-content:space-between;
        }
        .flex_left{
            width: 120px;
            line-height: 26px;
            text-align: right;
            color: #808080;
        }
        .flex_right{
            min-width:0;
            flex:1;
            line-height: 26px;
        }
        .fcff3{
            color: #ff3636;
        }
        .file-input .btn-default {
            border: 0px;
            color: #808080;
        }
        .file-drop-zone-title{
            font-size: 13px;
        }
        .file-footer-buttons{
            border-left: 1px dashed #dadada;
        }
        .file-drop-zone {
            height: 100px !important;
            border: 1px #dadada dashed;
            overflow: auto;
        }
        .kv-upload-progress .progress {
            display: none;
        }
        .file-input-ajax-new .file-drop-zone-title{
            /*height: 80px;*/
        }
        .theme-explorer .explorer-caption {
            color: #1a1a1a;
            font-size: 16px;
        }
        .theme-explorer .file-preview .table tr{
            border-bottom: 1px #dadada dashed;
        }
        .file-error-message {
            position: absolute;
            top: 20px;
            width: calc(100% - 40px);
            left: 20px;
            height: 100px;
            background: rgba(242,222,222,0.9);
            text-align: center;
            line-height: 70px;
        }
        .file-error-message button span{
            line-height: 70px;
        }
        .file-error-message li{
            text-align: center;
        }
        .addbtn{
            /*width: 100px;*/
            text-align: center;
            color: #fff;
            background: #1ab394;
            line-height: 30px;
            border-radius: 5px;
            cursor: pointer;
        }
        .mt10{
            margin-top: 10px;
        }
        label.error {
            top: 25px !important;
            left: 430px;
        }
        /*父页面宽度不达768px，样式不生效修复*/
        .col-sm-4 {
            position: relative;
            width: 33.3333%;
            float: left;
        }
        .col-sm-3 {
            position: relative;
            width: 25%;
            float: left;
        }
        .col-sm-11 {
            position: relative;
            width: 91.66666667%;
            float: left;
        }
        .col-sm-1 {
            position: relative;
            width: 8.33333333%;
            float: left;
        }
        .col-sm-6 {
            position: relative;
            width: 50%;
            float: left;
        }
        .col-sm-7 {
            position: relative;
            width: 58.33333333%;
            float: left;
        }
        .col-sm-1 {
            position: relative;
            width: 8.33333333%;
            float: left;
        }
    </style>
</head>
<body>
<div class="form-content">
    <form id="form-payDetail-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="id" th:value="${id}">
        <div class="panel-body">
            <div class="row">
                <div class="col-sm-6 flex">
                    <label class="flex_left"><span class="fcff3">*</span>要求支付日期：</label>
                    <div class="flex_right">
                        <input type="text" class=" form-control dis" name="reqPayDate" id="reqPayDate" autocomplete="off" required>
                    </div>
                </div>

            </div>
            <div class="row mt10">
                <div class="col-sm-6 flex">
                    <label class="flex_left">付款类型：</label>
                    <div class="flex_right">
                        定金[[${deposit.ungotAmount}]]元
                    </div>
                </div>
            </div>
            <div class="row mt10">
                <div class="col-sm-6 flex">
                    <div class="flex_left"><span class="fcff3">*</span> 收款人：</div>
                    <div class="flex_right">
                        <select name="carrBankId" id="carrBankId" class="form-control valid" required onchange="bankChange()">
                            <option value="">--请选择--</option>
                            <option th:each="dict : ${carrBankList}" th:text="${dict.bankAccount}"
                                    th:value="${dict.carrBankId}" th:field="${deposit.carrBankId}"></option>
                        </select>

                    </div>
                    <div style="width: 32px;text-align: center">
                        <div class="fa fa-plus-square" style="color: #1ab394;line-height: 26px;margin-bottom:4px;font-size: 18px;" onclick="addReceiver()"></div>
                    </div>
                </div>

            </div>

            <div class="row mt10">
                <div class="col-sm-6">
                    <div class="flex">
                        <label class="flex_left"><span class="fcff3">*</span> 收款银行：</label>
                        <div class="flex_right">
                            <input id="recBank" name="recBank" class="form-control dis" type="text" required th:value="${deposit.recBank}" disabled>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="flex">
                        <label class="flex_left"><span class="fcff3">*</span> 收款卡号：</label>
                        <div class="flex_right">
                            <input id="recCardNo" name="recCardNo" class="form-control dis" type="text" required th:value="${deposit.recCardNo}" disabled>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row mt10">
                <div class="col-sm-12">
                    <div class="flex">
                        <label class="flex_left">申请备注：</label>
                        <div class="flex_right">
                            <textarea name="applyMemo" maxlength="250" class="form-control valid" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </form>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">
    var prefix = ctx + "new-deposit";

    var carrBankList = [[${carrBankList}]];

    $(function () {

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#reqPayDate',
                type: 'date',
                trigger: 'click',
                done: function(value, date, endDate){
                    $("#reqPayDate").val(value);
                    //单独校验日期
                    $("#form-payDetail-add").validate().element($("#reqPayDate"));
                }
            });
        });

        $("#form-payDetail-add").validate({
            onkeyup: false,
            focusCleanup: true
        });

        var picParam = {
            maxFileCount:0,
            //publish: "uploadSuccess",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        var tid = "tid";
        var imageId = "image";
        $.file.initAddFiles(imageId, tid, picParam);

        // 图片上传成功后
        $("#image").on('filebatchuploadsuccess', function (event, data) {
            console.log(event, data)
            //var tid = data.response.tid;
            //$("#tid").val(tid); // 追加有问题，ry-ui.js里封装的filebatchuploadsuccess直接覆盖了tid
            if ($('#receipt').length > 0 && $('#receipt').fileinput('getFilesCount') > 0){
                $("#receipt").fileinput('upload');
            } else {
                //表单提交
                $.operate.save(prefix + "/saveApplyCheck", $('#form-payDetail-add').serialize());
            }
        });

        //$("#image").on('fileDragDrop', function (event, data) {console.log(event, data)});//拖拽完成事件，
        //$("#image").on('change', function (event) {console.log(event)});//浏览器选择文件事件
        //$("#image").on('fileselect', function (event, numFiles, label) {console.log(event, numFiles, label)});//拖拽多个文件会回调多次，numFiles是总拖拽文件数，但label一直是第一个文件的名称
        //$("#image").on('fileuploaded', function (event, data, previewId, index) {console.log(event, data, previewId, index)})
        //$('#image').on('filebatchuploadcomplete',function (event,files,extra) {console.log(event,files,extra)}) // files,extra没有有效信息
        // 已上传再删除事件
        $('#image').on('filesuccessremove', function(event, key, index) {//filepreremove（未上传文件删除前）、fileremoved（未上传文件删除后）
            console.log(event, key, index)
            //return false; 阻止删除
            // 可根据index和tid（或配合filebatchuploadsuccess的回调参数data）删除后台数据，根据后台返回剩余附件数，控制是否清空tid，当前采用直接清空所有的方式
            $('#image').fileinput('clear');
            $('#tid').val('');
        });

        $.file.initAddFiles("receipt", "tid_receipt", {
            maxFileCount:0,
            fileType: null
        });
        $("#receipt").on('filebatchuploadsuccess', function (event, data) {
            console.log(event, data)
            //var tid = data.response.tid;
            //$("#tid_receipt").val(tid);
            //表单提交
            $.operate.save(prefix + "/saveApplyCheck", $('#form-payDetail-add').serialize());
        });
        $('#receipt').on('filesuccessremove', function(event, key, index) {
            $('#receipt').fileinput('clear');
            $('#tid_receipt').val('');
        });


    });



    //收款人更换
    function bankChange() {
        $("#recCardNo").val("");
        $("#recBank").val("");
        for(var i = 0 ; i < carrBankList.length ; i++){
            if(carrBankList[i].carrBankId == $("#carrBankId").val()){
                $("#recCardNo").val(carrBankList[i].bankCard);
                $("#recBank").val(carrBankList[i].bankName);
            }
        }

    }

    //收款类型调整
    /*function accountTypeChange(){
        $("#recCardNo").val("");
        $("#recBank").val("");

        var str = '<option value="">--请选择--</option>';
        var accountType = $("#accountType").val();
        var isOilDeposit = $("#isOilDeposit").val();
        if(accountType == 0){
            $("#writeFuelcardDiv").css('display', 'none');
            //承运商收款人
            for(var i = 0 ; i < carrBankList.length ; i++){
                str = str +  '<option value="'+carrBankList[i].carrBankId+'">'+carrBankList[i].bankAccount+"</option>";
            }

        }else if(accountType == 1){
            $("#writeFuelcardDiv").css('display', 'none');
            //专项收款人
            for(var i = 0 ; i < specialCarrBankList.length ; i++){
                str = str +  '<option value="'+specialCarrBankList[i].carrBankId+'">'+specialCarrBankList[i].bankAccount+"</option>";
            }
        }else if(accountType == 2){
            $("#writeFuelcardDiv").css('display', 'none');
            //异常收款人
            for(var i = 0 ; i < oilCarrBankList.length ; i++){
                str = str +  '<option value="'+oilCarrBankList[i].carrBankId+'">'+oilCarrBankList[i].bankAccount+"</option>";
            }
        }else if(accountType == 3){
            $("#writeFuelcardDiv").css('display', 'block');
            //异常收款人
            for(var i = 0 ; i < oilPayCarrBankList.length ; i++){
                str = str +  '<option value="'+oilPayCarrBankList[i].carrBankId+'">'+oilPayCarrBankList[i].bankAccount+"</option>";
            }
        }else if(accountType == 4){
            $("#writeFuelcardDiv").css('display', 'block');
            //异常收款人
            for(var i = 0 ; i < exceptionPayCarrBanks.length ; i++){
                str = str +  '<option value="'+exceptionPayCarrBanks[i].carrBankId+'">'+exceptionPayCarrBanks[i].bankAccount+"</option>";
            }
        }
        $("#carrBankId").html(str);
    }*/

    /**
     * 表单提交
     */
    function submitHandler(index, layero, callback){
        var recBank = $("#recBank").val();
        var recCardNo = $("#recBank").val();
        if($.common.isEmpty(recBank) || $.common.isEmpty(recCardNo) ){
            $.modal.alertWarning("收款人和收款银行不能为空，请先完善信息")
            return;
        }
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function(){
                commit(callback);
            })
        }
    }

    function commit(callback) {
        //console.log('image', $('#image').fileinput('getFilesCount'))
        //console.log('receipt', $('#receipt').fileinput('getFilesCount'))

        //if($('#image').fileinput('getFilesCount') == 0 && ($('#receipt').length == 0 || $('#receipt').fileinput('getFilesCount') == 0)){
        let data = $('#form-payDetail-add').serialize()
        $.operate.save(prefix + "/saveApply", data, callback);
        //} else if ($('#image').fileinput('getFilesCount') > 0){
            // 表单提交
            //$.modal.loading("正在处理中，请稍后...");
            //$("#image").fileinput('upload');
        //} else if ($('#receipt').fileinput('getFilesCount') > 0){
        //    $.modal.loading("正在处理中，请稍后...");
        //    $("#receipt").fileinput('upload');
        //}
    }

    function addReceiver(){
        var carrierId = $("#carrierId").val();
        var accountType = $("#accountType").val();
        $.modal.open("新增收款人", ctx + "g7/payee/add?collectionType="+0+"&carrId="+carrierId,500);
    }


</script>
</body>

</html>