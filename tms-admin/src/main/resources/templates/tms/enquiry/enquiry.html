<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('询价')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .fl{
        float: left;
    }
    .status1{
        width: 80px;
        background: #fff;
        border: 1px #eee solid;
        text-align: center;
        cursor: pointer;
        line-height: 20px;
        vertical-align: middle;
    }
    .act{
        background: #1ab492;
        color: #fff;
        border: 1px #1ab492 solid;
    }
    .label-warningT{
        color: #8a5a00;
        background-color: rgba(248, 172, 89,.2);
        border: 0; 
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #f3f3f4;
        border: 0;
        box-shadow: none;
        padding:5px 0 0;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <div class="form-group" style="margin: 0 -10px 0;">
                <form id="role-form" class="form-horizontal">
                    <div class="row">
                        <div class="col-md-12 col-sm-12">
                            <div class="fl over">
                                <div th:class="${vbillstatus != null ? 'fl status1':'fl status1 act'}" flag onclick="tabto(this,0)">进行中</div>
                                <div th:class="${vbillstatus != 1 ? 'fl status1':'fl status1 act'}" flag onclick="tabto(this,1)">已完成</div>
                                <div th:class="${vbillstatus != 2 ? 'fl status1':'fl status1 act'}" flag onclick="tabto(this,2)">已关闭</div>
                            </div>
                            <input type="hidden" name="vbillstatus" id="vbillstatus" value="0">
                        </div>
                    </div>

                    <div class="row mt5">
                        <div class="col-md-2 col-sm-3">
                            <input type="text" class="form-control" placeholder="发货单编号/客户简称" name="invoiceNoAndCustAbbr">
                        </div>
                        <div class="col-md-2 col-sm-3">
                            <input id="startTimeStr" name="startTimeStr" type="text" class="form-control" readonly
                                placeholder="要求提到货日期">
                            <input type="hidden" name="startTime" id="startTime">
                            <input type="hidden" name="endTime" id="endTime">
                        </div>
                        <div class="col-md-1 col-sm-3">
                            <select name="carLen" class="form-control selectpicker" th:with="type=${@dict.getType('car_len')}" multiple data-none-selected-text="车长">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                        <div class="col-md-1 col-sm-3">
                            <select name="carType" class="form-control selectpicker" th:with="type=${@dict.getType('car_type')}" multiple data-none-selected-text="车型">
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                        <div class="col-md-2 col-sm-3">
                            <select name="transLineId" id="transLineId" class="form-control valid" aria-invalid="false"
                                    th:with="type=${dispatcherDeptList}" aria-required="true" required>
                                <option value="">-- 调度组 --</option>
                                <option th:each="dict : ${type}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>
                            </select>
                        </div>
                        <div class="col-md-2 col-sm-3">
                            <input id="reqDeliDate" name="reqDeliDateRangeStr" type="text" class=" form-control" readonly
                                placeholder="外发日期">
                            <input type="hidden" name="regDateStart" id="regDateStart">
                            <input type="hidden" name="regDateEnd" id="regDateEnd">
                        </div>
                        <div class="col-md-2 col-sm-3">
                            <input type="text" placeholder="外发人" class="form-control" id="createBy"  name="createBy">
                        </div>
                    </div>
                    <div class="row mt5">
                        <div class="col-md-4 col-sm-12">
                            <div class="form-group">
                                <div class="col-md-4 col-sm-4">
                                    <select id="province0" name="deliProName"
                                            class="form-control valid"
                                            aria-invalid="false"></select>
                                    </select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="city0" name="deliCityName" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="area0" name="deliAreaName" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-1 col-sm-12" style="text-align: center;" onclick="changeDiv()">
                            <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                        </div>
                        <div class="col-md-4 col-sm-12">
                            <div class="form-group">
                                <div class="col-md-4 col-sm-4">
                                    <select id="province1" name="arriProName"
                                            class="form-control valid"
                                            aria-invalid="false"></select>
                                    </select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="city1" name="arriCityName" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-md-4 col-sm-4">
                                    <select id="area1" name="arriAreaName" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-12">
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                        class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                        class="fa fa-refresh"></i>&nbsp;重置</a>  
                            </div>
                        </div>
                    

                        <!-- <div class="col-sm-2">
                            <input name="deliDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                maxlength="30" required="" aria-required="true">
                        </div> -->
                        <!-- <div class="col-sm-2">
                            <input name="arriDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                maxlength="30" required="" aria-required="true">
                        </div> -->
                    </div>
                </form>
            </div>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger multiple disabled" onclick="closeAll()"
               shiro:hasPermission="enquiry:enquiry:close">
                <i class="fa fa-remove"></i> 关闭
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var vbillstatus = [[${@dict.getType('out_vbillstatus')}]];
    var prefix = ctx + "enquiry/enquiry";
    var findCarType = [[${@dict.getType('car_type')}]];
    var findCarLength = [[${@dict.getType('car_len')}]];
    var findOutType = [[${@dict.getType('out_type')}]];
    var findTransCode = [[${@dict.getType('trans_code')}]];

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        $.provinces.init("province0", "city0", "area0");
        $.provinces.init("province1", "city1", "area1");

        var options = {
            url: prefix + "/list",
            showToggle: false,
            showColumns: true,
            modalName: "询价",
            // fixedColumns: true,
            // fixedNumber: 3,
            clickToSelect: true,
            height: 560,
            exportTypes:['excel','csv'],
            showExport: true,
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"询价数据"
            },
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    width: '20px',
                    field: 'lotOutId',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if(row.outType == 0 && row.lotOutDtlList.length>0){
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="查看报价" onclick="quotation(\'' + row.lotOutId + '\',\'' + row.invoiceId + '\',\'' + row.outType + '\')">查看报价</a>');
                        }else if(row.outType == 1){
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="查看抢单" onclick="quotation(\'' + row.lotOutId + '\',\'' + row.invoiceId + '\',\'' + row.outType + '\')">查看抢单</a>');
                        }
                        return actions.join('');
                    }
                },
                // {
                //     title: '发货单号/截止时间',
                //     align: 'left',
                //     width: '120px',
                //     field: 'invoiceVbillno',
                //     formatter: function (value, row, index) {
                //         let html= $.table.tooltip(value)+'<br/>';
                //         if (row.cutoffDate == null) {
                //             html+='';
                //         } else {
                //             let oldDate=new Date(row.cutoffDate);
                //             oldDate=oldDate.setDate(oldDate.getDate()-1);
                //             let newDate=new Date(oldDate)
                //             let now = new Date();
                //             if (newDate < now) {
                //                 html+= '<span style="color: #ed5565;">' + row.cutoffDate + '</span>';
                //             } else {
                //                 html+= row.cutoffDate;
                //             }
                //         }
                //         return html;
                //     }
                // },
                {
                    title: '客户名称/截止时间',
                    align: 'left',
                    width: '100px',
                    field: 'custAbbr',
                    formatter: function (value, row, index) {
                        let html= $.table.tooltip(value)+'<br/>';
                        html= html + $.table.tooltip(row.invoiceNo)+'<br/>';

                        if (row.cutoffDate == null) {
                            html+='';
                        } else {
                            let oldDate=new Date(row.cutoffDate);
                            oldDate=oldDate.setDate(oldDate.getDate()-1);
                            let newDate=new Date(oldDate)
                            let now = new Date();
                            if (newDate < now) {
                                html+= '<span style="color: #ed5565;">' + row.cutoffDate + '</span>';
                            } else {
                                html+= row.cutoffDate;
                            }
                        }

                        return html
                    }
                },
                {
                    title: '外发类型/报价数',
                    align: 'left',
                    field: 'outType',
                    formatter: function (value, row, index) {
                        let length=""
                        if(row.lotOutDtlList == null || row.lotOutDtlList.length==0){
                            length= $.table.tooltip(row.lotOutDtlList);
                        }else{
                            length= $.table.tooltip(row.lotOutDtlList.length);
                        }
                        

                        return $.table.selectDictLabel(findOutType, value)+'<br/>'+length;
                    }
                },
                {
                    title: '金额/联系人',
                    halign: "center",
                    align: 'right',
                    width: '100px',
                    formatter: function (value, row, index) {
                        let text="";
                        if(row.outType == 0){
                            if (row.minQuotedPrice != null) {
                                text=`<span class="label label-warningT">最低报价</span>
                                        <span style="color:#FF9008">`+row.minQuotedPrice.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span> <br/>`;
                            } 
                            if (row.minCarrierContact != null) {
                                text+= $.table.tooltip(row.minCarrierContact)+'/'+$.table.tooltip(row.minCarrierPhone);
                            } 
                        }else{
                            if (row.outPrice != null) {
                                text=`<span style="color:#FF9008">`+row.outPrice.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span> <br/>`;
                            } 
                        }
                        return text;
                    }
                },
                
                // {
                //     title: '抢单金额/抢单人',
                //     halign: "center",
                //     align: 'right',
                //     width: '100px',
                //     field: 'outPrice',
                //     formatter: function (value, row, index) {
                //         let text=""
                //         if(row.outType == 1){
                //             if (value != null) {
                //                 text+=`<span style="color:#FF9008">`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span> <br/>`;
                //             } 
                //         }
                //         return text;
                //     }
                // },

                {
                    title: '要求提货/到货日期',
                    field: 'reqDeliDate',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let html=''
                        if(value == "" || value == null || value == 'undefined'){
                            html+= "";
                        }else{
                            html+=value;
                        }
                        html+='<br/>';

                        if(row.reqArriDate == "" || row.reqArriDate == null || row.reqArriDate == 'undefined'){
                            html+= "";
                        }else{
                            html+=row.reqArriDate;
                        }
                        return html;
                    }
                },
                {
                    title: '装卸货地址',
                    align: 'left',
                    formatter: function status(row,value) {
                        if(value.isMultiple==1){
                            return value.shippingAddressList.sort(sortNum).map(item=>{
                                if(item.addressType == 0){
                                    return '<span class="label label-warning pa2">装</span>'+$.table.tooltip(item.provinceName+item.cityName+item.areaName);
                                }
                                if(item.addressType == 1){
                                    return '<span class="label label-success pa2">卸</span>'+$.table.tooltip(item.provinceName+item.cityName+item.areaName);
                                }
                            }).join("<br/>");
                        }else{
                            return '<span class="label label-warning pa2">装</span>'+$.table.tooltip(value.deliDetailAddr+value.deliAddrName)+'<br/><span class="label label-success pa2">卸</span>'+$.table.tooltip(value.arriDetailAddr+value.arriAddrName);
                        }
                    }
                },
               
                
                                // {
                //     title: '外发状态',
                //     align: 'left',
                //     field: 'vbillstatus',
                //     formatter: function (value, row, index) {
                //         switch(value - 0) {
                //             case 0:
                //                 return '<span class="label label-default">待确认</label>';
                //             case 1:
                //                 return '<span class="label label-primary">已确认</label>';
                //             case 2:
                //                 return '<span class="label label-success">已支付</label>';
                //             case 3:
                //                 return '<span class="label label-inverse">已结束</label>';
                //             case 4:
                //                 return '<span class="label label-inverse">已关闭</label>';
                //             default:
                //                 break;
                //         }
                //     }
                // },
                // {
                //     title: '结算金额(元)',
                //     halign: "center",
                //     align: 'right',
                //     formatter: function (value, row, index) {
                //         if (row.transFee != null) {
                //             return row.transFee.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //         } else {
                //             return "";
                //         }
                //     }
                // },
                {
                    title: '货品信息',
                    align: 'left',
                    field: 'segPackGoodsList',
                    formatter: function (value, row, index) {
                        let isToAndFro = ''
                        if (row.isToAndFro == 1) {
                            isToAndFro = '<span class="label label-primaryT ml5">往返</span>'
                        }

                        let text = $.table.tooltip(row.goodsName) + isToAndFro + "<br/>";
                        if(row.weightCount != null && row.weightCountMost  != null){
                            let list=[];
                            if(row.weightCount != 0){
                                list.push($.table.tooltip(row.weightCount)+'吨')
                            }
                            if(row.weightCountMost != 0){
                                list.push($.table.tooltip(row.weightCountMost)+'吨')
                            }
                            text+= list.join("~")
                        }
                        if(row.volumeCount != null && row.volumeCountMost  != null){
                            let list=[];
                            if(row.volumeCount != 0){
                                list.push($.table.tooltip(row.volumeCount)+'m³')
                            }
                            if(row.volumeCountMost != 0){
                                list.push($.table.tooltip(row.volumeCountMost)+'m³')
                            }
                            text+= list.join("~")
                        }
                        return text;
                    }
                },
                {
                    title: '要求车长车型',
                    align: 'left',
                    field: 'carType',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(findCarLength, row.carLen)+$.table.selectDictLabel(findCarType, value);
                    }
                },
                {
                    field: 'memo',
                    title: '备注',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value, 20)
                    },
                },
                {
                    field: 'quoteWay',
                    title: '平台选择',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(value != null && value != ''){
                            let quoteWayList=value.split(",").map(item=>{ return item==1?'内部运力':item==2?'运满满':item==4?'货拉拉':'' });
                            return $.table.tooltip(quoteWayList.join(','));
                        }
                        return $.table.tooltip(value);
                    }
                },
                {
                    field: 'quoteSuccessWay',
                    title: '确定平台',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let text="" 
                        if(value != null){
                            text= value==1?'内部运力':value==2?'运满满':value==4?'货拉拉':''
                        }
                        return $.table.tooltip(text);
                    }
                },
                {
                    title: '外发人',
                    align: 'left',
                    // width: '100px',
                    field: 'createBy',
                    formatter: function (value, row, index) {
                        let text = value + "<br/>" + row.createTime;
                        return text
                    }
                },
                {
                    title: '关闭人',
                    align: 'left',
                    field: 'backOutGoUser',
                    formatter: function (value, row, index) {
                        let text = '';
                        if (value) {
                            text = value;
                        }

                        if (row.backOutGoDate) {
                            if (text) {
                                text = text + "<br/>";
                            }
                            text = text + row.backOutGoDate;
                        }
                        return text
                    }
                },
                // {
                //     title: '外发时间',
                //     align: 'left',
                //     width: '100px',
                //     field: 'createTime'
                // }
                
                // {
                //     title: '运输方式',
                //     align: 'left',
                //     field: 'transType',
                //     formatter: function (value, row, index) {
                //         return $.table.selectDictLabel(findTransCode, value);
                //     }
                // },
                // {
                //     title: '外发单号',
                //     align: 'left',
                //     field: 'vbillno'
                // },
               
                // {
                //     title: '运段号',
                //     align: 'left',
                //     field: 'segmentVbillno'
                // }
            ]
        };
        $.table.init(options);

    });

    // 地址排序
    function sortNum(a,b) {
        return a.addressType-b.addressType;
    }

    /**
     * 日期插件
     */
    layui.use(function(){
        var laydate = layui.laydate;
        //日期时间选择器
        laydate.render({
            elem: '#startTimeStr', 
            type: 'date',
            range: true,
            rangeLinked: true,
            done: function (value, date, endDate) {
                let time=value.split(" - ");
                $("#startTime").val(time[0]);
                $("#endTime").val(time[1]);
            },
        });
        laydate.render({
            elem: '#reqDeliDate', //指定元素
            type: 'date',
            range: true,
            rangeLinked: true,
            done: function (value, date, endDate) {
                let time=value.split(" - ");
                $("#regDateStart").val(time[0]);
                $("#regDateEnd").val(time[1]);
            }
        });  
    })
    /* function operateFormatter(value, row, index) {
         var actions = [];
         if (row['OUT_TYPE'] === 0) {
             actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="货品明细" onclick="detail(\'' + row.INVOICE_ID + '\')"><i class="fa fa-truck" style="font-size: 15px;"></i></a>');
             actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="报价明细" onclick="quotation(\'' + row.LOT_OUT_ID + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
         }
         if (row['OUT_TYPE'] === 1) {
             actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="货品明细" onclick="detail(\'' + row.INVOICE_ID + '\')"><i class="fa fa-truck" style="font-size: 15px;"></i></a>');
             actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="设置定金" onclick="book(\'' + row.LOT_OUT_ID + '\')"><i class="fa fa-dollar" style="font-size: 15px;"></i></a>');
         }
         return actions.join('');
     }*/

    /**
     * 选择货品名称
     */
    function selectGoods(goodsIndex) {
        $.modal.open("选择货品", ctx + "client/goods?goodsIndex=" + goodsIndex, "", "", function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //货品id
            $("#goodsId").val(rows[0]["goodsId"]);
            //货品名称
            $("#goodsName").val(rows[0]["goodsName"]);

            layer.close(index);
        });
    }

    //货品明细
    function detail(id) {
        var url = prefix + "/detail/" + id;
        $.modal.openTab("货品明细", url);
    }

    //报价明细 报价
    function quotation(lotOutId, invoiceId, outType) {
        var url;
        if(outType==0){
            url = prefix + "/offerDetails?lotOutId=" + lotOutId + "&invoiceId=" + invoiceId + "&outType=" + outType;
            $.modal.openTab("报价明细", url);
        }else{
            url = prefix + "/quotation?lotOutId=" + lotOutId + "&invoiceId=" + invoiceId + "&outType=" + outType;
            $.modal.openTab("抢单明细", url);
        }
    }

    //设置定金 抢单
    function book(id) {
        layer.prompt({title: '输入定金金额，并确认', formType: 0}, function (pass, index) {
            $.operate.submit(prefix + "/addBookPrice", "post", "json", {amount: pass, enquiryId: id});
            layer.close(index);
        });
    }

    function NowTime() {
        var myDate = new Date();
        var y = myDate.getFullYear();
        var M = myDate.getMonth() + 1;     //获取当前月份(0-11,0代表1月)
        var d = myDate.getDate();        //获取当前日(1-31)
        var h = myDate.getHours();       //获取当前小时数(0-23)
        var m = myDate.getMinutes();     //获取当前分钟数(0-59)
        var s = myDate.getSeconds();     //获取当前秒数(0-59)

        //检查是否小于10
        M = check(M);
        d = check(d);
        h = check(h);
        m = check(m);
        s = check(s);
        var timestr = y + "-" + M + "-" + d + " " + h + ":" + m + ":" + s;
        return timestr;
    }

    //时间数字小于10，则在之前加个“0”补位。
    function check(i) {
        var num = (i < 10) ? ("0" + i) : i;
        return num;
    }

    function closeAll() {
        var rows = $.table.selectColumns("lotOutId");
        var vbillstatusList = $.table.selectColumns("vbillstatus");

        for (var i = 0; i < vbillstatusList.length; i++) {
            if (0 != vbillstatusList[i]) {
                $.modal.alertWarning("只能关闭待确认的外发单！");
                return;
            }
        }

        $.modal.confirm("确认要关闭选中的" + rows.length + "条数据吗?", function () {
            var data = {"lotOutId": rows.join()};
            var url = prefix + "/close_mul";
            $.operate.submit(url, "post", "json", data);
        });
    }

    function tabto(btn,status) {
        if (status == null) {
            $("#vbillstatus").val('');
        } else {
            $("#vbillstatus").val(status);
        }
        $(".status1").removeClass("act");
        $(btn).addClass("act")
        $.table.search()
    }

    function changeDiv(){
        var deliProvinceId= $('#province0').val()
        var arriProvinceId= $('#province1').val()
        var deliCityId= $('#city0').val()
        var arriCityId= $('#city1').val()
        var deliAreaId= $('#area0').val()
        var arriAreaId= $('#area1').val()
        $.provinces.init("province0","city0","area0",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("province1","city1","area1",deliProvinceId,deliCityId,deliAreaId);
        $.table.search()
    }

    function resetPre() {
        $.provinces.init("province0", "city0", "area0");
        $.provinces.init("province1", "city1", "area1");
        $.form.reset()
    }
</script>
</body>
</html>