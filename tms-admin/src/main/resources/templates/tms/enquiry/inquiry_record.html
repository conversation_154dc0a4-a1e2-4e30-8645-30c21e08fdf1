<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('询价列表')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
    <th:block th:include="include :: bootstrap-editable-css" />
</head>
<style>
    .row-margin-top {
        margin-top: 10px;
    }
    .select-table .table td {
        /* 超出部分隐藏 */
        overflow:hidden;
        /* 超出部分显示省略号 */
        text-overflow:ellipsis;
        /*规定段落中的文本不进行换行 */
        white-space:nowrap;
        /* 配合宽度来使用 */
        height:40px;
    }
    .pa0{
        padding: 0;
    }
    .fr{
        float: right;
    }
    .rSp{
        font-size: 16px;
        vertical-align: middle;
        color: #ff1f1f;
    }
</style>
<body class="gray-bg">

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="inquiry-record-form">
                    <input type="hidden" id="parentId" name="parentId">
                    <div class="row row-margin-top">
                        <div class="col-sm-3 pa0">
                            <div class="input-group">
                                <span class="input-group-addon">从</span>
                                <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 询价开始时间" style="font-size: 14px" id="starttime" name="startTime" >
                                <span class="input-group-addon">到</span>
                                <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder=" 询价结束时间" style="font-size: 14px" id="endtime" name="endTime">
                            </div>
                        </div>

                        <div class="col-sm-3 pa0">
                            <div class="col-sm-4" style="padding-right: 0;">
                                <div class="form-group">
                                    <select  name="deliProvinceId" id="deliProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-4" style="padding-right: 0;">
                                <select name="deliCityId" id="deliCityId"  class="form-control valid" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4" style="padding-right: 0;">
                                <select name="deliAreaId" id="deliAreaId"  class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>

                        <div class="col-sm-1 pa0" style="text-align:center;">
                            <i class="fa fa-arrow-circle-right" style="font-size:22px;color: #1ab394"></i>
                        </div>

                        <div class="col-sm-3 pa0">
                            <div class="col-sm-4" style="padding-right: 0;">
                                <div class="form-group">
                                    <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-4" style="padding-right: 0;">
                                <select name="arriCityId" id="arriCityId" class="form-control" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4" style="padding-right: 0;">
                                <select name="arriAreaId" id="arriAreaId" class="form-control" aria-invalid="false"></select>
                            </div>
                        </div>

                        <div class="col-sm-2 pa0">
                            <label class="col-sm-4"></label>
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" shiro:hasPermission="tms:inquiry_record:add">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="tms:inquiry_record:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="tms:inquiry_record:import">
                    <i class="fa fa-upload"></i> 导入
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:inquiry_record:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info multiple disabled" onclick="push()" shiro:hasPermission="tms:inquiry_record:batchOutGoingYmm">
                    <i class="fa fa-plane"></i> 运满满推送
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: ztree-js" />
<th:block th:include="include :: bootstrap-table-editable-js"/>
<script th:inline="javascript">
    var carLen = [[${@dict.getType('car_len')}]];//车长

    var editFlag = [[${@permission.hasPermi('tms:inquiry_record:edit')}]];
    var removeFlag = [[${@permission.hasPermi('tms:inquiry_record:remove')}]];
    var addInvoice = [[${@permission.hasPermi('tms:inquiry_record:addInvoice')}]];

    var prefix = ctx + "inquiry";

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#starttime").removeAttr("lay-key");
        $("#endtime").removeAttr("lay-key");
        laydate.render({
            elem: '#endtime'
            , type: 'date'
        });

        laydate.render({
            elem: '#starttime'
            , type: 'date'
        });
    })

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 185 });
        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        queryInquiryRecordList();
        $.table.hideColumn("id");
    });

    //初始化查询条件传参
    queryParams = function(params) {
        var search = {};
        $.each($("#inquiry-record-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    function queryInquiryRecordList() {
        var options = {
            url: prefix + "/inquiryRecordPage",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            sortName: "inquiryDate",
            sortOrder: "desc",
            modalName: "询价记录",
            height: 560,
            fixedColumns: true,
            showFooter:true,
            fixedNumber:5,
            showExport: true,
            clickToSelect: true,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"询价记录列表"
            },
            queryParams: queryParams,
            onEditableSave: function (field, row, oldValue, $el) {
                // if(row.costStatus == 3) {
                //     $.modal.alertWarning("力资费用已核销，无法调整！");
                //     //刷新
                //     $.btTable.bootstrapTable('refresh', {
                //         silent: true
                //     });
                //     return;
                // }
                if (field === 'offer') {
                    let data = {id: row.id, offer: row.offer};
                    $.ajax({
                        url: prefix + "/editInquiryRecord",
                        type: "post",
                        dataType: "json",
                        data: data,
                        success: function (result) {
                            if (result.code === 0) {
                                var data = result.data;
                                //刷新
                                $.btTable.bootstrapTable('refresh', {
                                    silent: true
                                });
                            } else {
                                $.modal.msgError(result.msg);
                                //刷新
                                $.btTable.bootstrapTable('refresh', {
                                    silent: true
                                });
                            }
                        }
                    });
                }
            },
            columns: [
                {
                    checkbox:true
                },
                {
                    title: '主键',
                    align: 'left',
                    field : 'id'  //主键加载完成就隐藏  多选删除的时候是按照第一列的值是否相同来判断一共有几项的
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.id + '\')"><i class="fa fa-edit"></i></a> ');
                        actions.push('<a class="btn btn-info btn-xs ' + addInvoice + '" href="javascript:void(0)" onclick="generate(\'' + row.id + '\')"><i class="fa fa-wpforms"></i>生成发货单</a> ');
                        return actions.join('');
                    }
                },
                {
                    title: '询价日期',
                    align: 'left',
                    field : 'inquiryDate'
                },
                {
                    title: '提货地址',
                    align: 'left',
                    field : 'deliProvinceName',
                    formatter: function status(row,value) {
                        if(value.deliProvinceName == null){
                            value.deliProvinceName = "";
                        }
                        if(value.deliCityName == null){
                            value.deliCityName = "";
                        }
                        if(value.deliAreaName == null){
                            value.deliAreaName = "";
                        }
                        if(value.deliDetailAddr == null){
                            value.deliDetailAddr = "";
                        }
                        var deliAddr = value.deliProvinceName + value.deliCityName + value.deliAreaName + value.deliDetailAddr;
                        return deliAddr;
                    }
                },
                {
                    title: '到货地址',
                    align: 'left',
                    field : 'arriProvinceName',
                    formatter: function status(row,value) {
                        if(value.arriProvinceName == null){
                            value.arriProvinceName = "";
                        }
                        if(value.arriCityName == null){
                            value.arriCityName = "";
                        }
                        if(value.arriAreaName == null){
                            value.arriAreaName = "";
                        }
                        if(value.arriDetailAddr == null){
                            value.arriDetailAddr = "";
                        }
                        var arriAddr = value.arriProvinceName + value.arriCityName + value.arriAreaName + value.arriDetailAddr;
                        return arriAddr;
                    }
                },
                {
                    title: '车长(米)',
                    align: 'left',
                    field : 'carLength',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(carLen, value);
                    }
                },
                {
                    title: '询价(元)',
                    align: 'left',
                    field : 'inquiry'
                },
                {
                    title: '里程(公里)',
                    align: 'right',
                    field: 'mileage'
                },
                {
                    title: '最低价（元）',
                    align: 'right',
                    field: 'offer',
                    // formatter: function (value, row, index) {
                    //     if (value === null) {
                    //         return ;
                    //     }
                    //     return value.toLocaleString('zh', {style: 'currency', currency: 'CNY', minimumFractionDigits: 2});
                    // },
                    editable:{
                        type: 'text',
                        title: '修改最低价',
                        validate:  function (v) {
                            if (!onlyNumberTwoDecimal(v)) {
                                return "请输入正确的数字！";
                            }
                        }
                    }
                },
                {
                    title: '司机报价(元)',
                    align: 'left',
                    field : 'driverOffer'
                },
                {
                    title: '推荐价(元)',
                    align: 'left',
                    field : 'recommendPrice',
                    formatter: function status(value,row){
                        let htmlText = $.table.tooltip(value)
                        if(row.remark !='' && row.remark !=null){
                            htmlText+=`<i class="fa fa-exclamation-circle fr rSp" data-toggle='tooltip' data-container="body" data-placement='top' data-html='true' title='`+$.table.tooltip(row.remark)+`'></i>`  
                        }
                        return htmlText
                    }
                },
                {
                    title: '运满满',
                    align: 'left',
                    field : 'ymmStatus',
                    formatter: function status(value,row){
                        let htmlText = '';
                        if(value == 1) {
                            htmlText = '已推送'
                        }else {
                            htmlText = '未推送'
                        }
                        return htmlText
                    }
                },
                {
                    title: '发货单',
                    align: 'left',
                    field : 'invoiceStatus',
                    formatter: function status(value,row){
                        let htmlText = '';
                        if(value == 1) {
                            htmlText = '已创建'
                        }else {
                            htmlText = '未创建'
                        }
                        return htmlText
                    }
                },
                // {
                //     title: '备注',
                //     align: 'left',
                //     field : 'remark'
                // },
                {
                    title: '创建人',
                    align: 'left',
                    field : 'regUserName'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field : 'regDate'
                }
            ]
        };
        $.table.init(options);
    }

    function onlyNumberTwoDecimal (obj) {
        regExp =  /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(obj)
        return regExp
    }
    function push() {
        var ids =  $.table.selectColumns("id").join();
        $.modal.open("外发报价", prefix + "/outQuote/"+ids,550,500);
    }
    function generate(id) {
        $.modal.openTab("一键生成发货单", prefix + "/addInvoice/"+id);
    }
</script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <input type="checkbox" id="updateSupport" name="updateSupport" title="如果登录账户已经存在，更新这条数据。"> 是否更新已经存在的用户数据
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>