<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('报价明细')"/>
</head>
<style type="text/css">
    .custom-tab td {
        text-align: left;
    }

    .quotePrice {
        text-align: right !important;
    }

</style>
<body>
<div class="form-content">
    <form id="form-enquiry-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" id="outType" th:value="${outType}"/>
        <input type="hidden" id="lotOutId" name="lotOutId" th:value="${enquiry.lotOutId}"/>
        <input type="hidden" id="vbillstatus" th:value="${enquiry.vbillstatus}"/>
        <input type="hidden" id="dtlId" name="lotOutDtlId" th:value="${lotOutDtlId}">

        <input name="corDate" type="hidden" class="time-input"
               th:value="${#dates.format(enquiry.corDate, 'yyyy-MM-dd HH:mm:ss')}">

        <div class="panel-group">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseOne">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">客户名称：</label>
                                    <div class="col-sm-8" th:text="${enquiry.custName }">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">外发状态：</label>
                                    <div class="col-sm-8"
                                         th:text="${@dict.getLabel('out_vbillstatus',enquiry.vbillstatus)}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">车型：</label>
                                    <div class="col-sm-8" th:text="${@dict.getLabel('car_type',enquiry.carType)}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">车长（米）：</label>
                                    <div class="col-sm-8" th:text="${@dict.getLabel('car_len',enquiry.carLen)}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">运输方式：</label>
                                    <div class="col-sm-8" th:if="${enquiry.transType!=null}"
                                         th:text="${@dict.getLabel('trans_code',enquiry.transType)}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收货地址：</label>
                                    <div class="col-sm-8" th:text="${enquiry.arriDetailAddr }">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">发货地址：</label>
                                    <div class="col-sm-8" th:text="${enquiry.deliDetailAddr }">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">货品明细</a>
                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseTwo">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">总毛重：</label>
                                    <div class="col-sm-8" th:text="${enquiry.grossWeight}+'吨'">
                                    </div>
                                    <input type="hidden" name="grossWeight" th:if="${enquiry.grossWeight!=null}"
                                           th:value="${enquiry.grossWeight}">
                                </div>
                            </div>
                        </div>
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 50%;">货品类型</th>
                                    <th style="width: 50%;">货品名称</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="good : ${goodsList}">
                                    <td th:text="${good.goodsTypeName }"></td>
                                    <td th:text="${good.goodsName }"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFour">外发联系人</a>
                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseFour">
                    <div class="panel-body">
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 50%;">联系人</th>
                                    <th style="width: 50%;">联系电话</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="item : ${contactList}">
                                    <td th:text="${item.contactName }"></td>
                                    <td th:text="${item.contactTel }"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseThree">报价明细</a>
                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseThree">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">外发类型：</label>
                                    <div class="col-sm-8"
                                         th:text="${@dict.getLabel('out_type',enquiry.outType)}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6" th:if="${outType=='1'}">
                                <div class="form-group">
                                    <label class="col-sm-4">价格(¥)：</label>
                                    <div class="col-sm-8"
                                         th:text="${#numbers.formatDecimal(enquiry.outPrice,0,'COMMA',2,'POINT') }">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">抢单报价截止时间：</label>
                                    <div th:text="${#dates.format(enquiry.cutoffDate, 'yyyy-MM-dd HH:mm:ss')}"
                                         class="col-sm-8">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" class="custom-tab table">
                                <thead>
                                <tr th:if="${outType=='0'}">
                                    <th style="width: 6%;" th:if="${enquiry.vbillstatus=='0'}">选择</th>
                                    <th style="width: 6%;">承运商名称</th>
                                    <th style="width: 6%;">联系人</th>
                                    <th style="width: 6%;">联系方式</th>
                                    <th style="width: 6%;">报价金额(¥)</th>
                                    <th style="width: 10%;">报价抢单时间</th>
                                    <th style="width: 6%;">是否有订金</th>
                                    <th style="width: 6%;">订金金额(¥)</th>
                                    <th style="width: 10%;">订金截止时间</th>
                                    <th style="width: 10%;">缴纳订金时间</th>
                                    <th style="width: 10%;">状态</th>
                                </tr>
                                <tr th:if="${outType=='1'}">
                                    <th style="width: 6%;">承运商名称</th>
                                    <th style="width: 6%;">联系人</th>
                                    <th style="width: 6%;">联系方式</th>
                                    <th style="width: 10%;">报价抢单时间</th>
                                    <th style="width: 10%;">是否有订金</th>
                                    <th style="width: 10%;">订金金额(¥)</th>
                                    <th style="width: 10%;">订金截止时间</th>
                                    <th style="width: 10%;">缴纳订金时间</th>
                                    <th style="width: 10%;">操作</th>
                                </tr>
                                </thead>
                                <tbody th:if="${outType=='0'}">
                                <tr th:each="quotation,stat:${quotationList}"
                                    th:onclick="'selectTr('+${stat.index}+')'">
                                    <td style="text-align: center" th:if="${enquiry.vbillstatus=='0'}"><input
                                            th:id="'radio_'+${stat.index}" name="radio" type="radio"
                                            th:value="${quotation.lotOutDtlId}"/>
                                    </td>
                                    <td th:text="${quotation.carrierName }"></td>
                                    <td th:text="${quotation.contact }"></td>
                                    <td th:text="${quotation.phone }"></td>
                                    <td th:text="${#numbers.formatDecimal(quotation.quotedPrice,0,'COMMA',2,'POINT')}"></td>
                                    <td th:text="${#dates.format(quotation.commitDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                    <td th:if="${quotation.isDeposit==null }"></td>
                                    <td th:if="${quotation.isDeposit!=null }"
                                        th:text="${@dict.getLabel('is_deposit',quotation.isDeposit)}"></td>
                                    <td style="text-align:right!important"
                                        th:text="${#numbers.formatDecimal(quotation.deposit,0,'COMMA',2,'POINT')}"></td>
                                    <td th:text="${#dates.format(quotation.depositCutoffDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                    <td th:text="${#dates.format(quotation.depositDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                    <td th:text="${@dict.getLabel('out_vbillstatus',quotation.status)}"></td>
                                </tr>
                                </tbody>
                                <tbody th:if="${outType=='1'}">
                                <tr th:each="quotation : ${quotationList}">
                                    <input type="hidden" id="lotOutDtlId"
                                           th:value="${quotation.lotOutDtlId}"/>
                                    <td th:text="${quotation.carrierName }"></td>
                                    <td th:text="${quotation.contact }"></td>
                                    <td th:text="${quotation.phone }"></td>
                                    <td th:text="${#dates.format(quotation.commitDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                    <td th:if="${quotation.isDeposit==null }"></td>
                                    <td th:if="${quotation.isDeposit!=null }"
                                        th:text="${@dict.getLabel('is_deposit',quotation.isDeposit)}"></td>
                                    <td style="text-align:right!important"
                                        th:text="${#numbers.formatDecimal(quotation.deposit,0,'COMMA',2,'POINT')}"></td>
                                    <td th:text="${#dates.format(quotation.depositCutoffDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                    <td th:text="${#dates.format(quotation.depositDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                    <td><a th:if="${quotation.status=='0'}"
                                           th:onclick=returnQuotation([[${quotation.lotOutDtlId}]])>取消抢单</a>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFive">付款信息</a>
                    </h4>
                </div>
                <div id="collapseFive" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!-- 付款信息-->
                        <div class="row" th:if="${enquiry.vbillstatus=='0'}">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span style="color: red">结算金额：</span></label>
                                    <div class="col-sm-8">
                                        <div class="input-group">
                                            <span class="input-group-addon">¥</span>
                                            <input name="costAmount" id="costAmount" type="text"
                                                   oninput="$.numberUtil.onlyNumber(this);getCostShare()"
                                                   class="form-control valid" maxlength="14" 97654321234578
                                                   required aria-required="true"
                                                   autocomplete="off">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row" th:if="${enquiry.vbillstatus=='1'}">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">结算金额：</label>
                                    <div class="col-sm-8" th:text="${enquiry.transFee}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 8%;">付款类型</th>
                                    <th style="width: 10%;">金额</th>
                                    <th style="width: 15%;">油卡号</th>
                                    <th style="width: 20%;">备注</th>
                                </tr>
                                </thead>
                                <tr>
                                <tr th:if="${enquiry.vbillstatus=='1' &&enquiry.isDeposit=='1'}"
                                    th:each="paydetail : ${lotOutPayDetailList}">
                                    <td th:text="${@dict.getLabel('cost_type_freight',paydetail.payType)}">
                                    </td>
                                    <td th:text="${paydetail.transFeeCount}">
                                    </td>
                                    <td th:text="${paydetail.oilCardNumber}">
                                    </td>
                                    <td th:text="${paydetail.memo}">
                                    </td>
                                </tr>

                                <tr th:if="${enquiry.vbillstatus=='1' &&enquiry.isDeposit=='0'}"
                                    th:each="item: ${payDetailList}">
                                    <td th:text="${@dict.getLabel('cost_type_freight',item.costTypeFreight)}">
                                    </td>
                                    <td th:text="${item.transFeeCount}">
                                    </td>
                                    <td th:text="${item.oilCardNumber}">
                                    </td>
                                    <td th:text="${item.memo}">
                                    </td>
                                </tr>

                                <tr th:if="${enquiry.vbillstatus=='0'}"
                                    th:each="dict,stat : ${@dict.getType('cost_type_freight')}">
                                    <td>
                                        <div th:text="${dict.dictLabel}"></div>
                                        <input type="hidden" th:id="|payType_${stat.index}|" th:name="|payDetailList[${stat.index}].payType|" th:value="${dict.dictValue}">
                                    </td>
                                    <td>
                                        <div class="col-md-12">
                                            <input th:name="|payDetailList[${stat.index}].transFeeCount|" th:id="|transFeeCount_${stat.index}|" placeholder=""
                                                   class="form-control" type="text" oninput="$.numberUtil.onlyNumber(this);calculateTotal()"
                                                   autocomplete="off">
                                        </div>
                                    </td>
                                    <td>
                                        <div class="col-md-12">
                                            <input th:name="|payDetailList[${stat.index}].oilCardNumber|" th:id="|oilCardNumber_${stat.index}|" placeholder=""
                                                   class="form-control" type="text" maxlength="20" autocomplete="off"
                                                   th:disabled="${dict.dictValue == '0' or dict.dictValue == '2' or dict.dictValue == '4'}">
                                        </div>
                                    </td>
                                    <td>
                                        <input th:name="|payDetailList[${stat.index}].memo|" th:id="|memo_${stat.index}|" placeholder="" class="form-control"
                                               type="text" maxlength="200" autocomplete="off">
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--付款信息 end-->
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseSix">定金信息</a>
                    </h4>
                </div>
                <div id="collapseSix" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row" th:if="${confirmQuotation==null}">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">是否设置定金：</label>
                                    <div class="col-md-8 col-sm-8">
                                        <select id="ifConfirmMoney" name="ifConfirmMoney"
                                                class="form-control m-b"
                                                onchange="updateScr()" required>
                                            <option th:value="0">是
                                            </option>
                                            <option th:value="1">否
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">定金金额：</label>
                                    <div class="col-md-8 col-sm-8">
                                        <input id="deposit" name="deposit" type="text"
                                               oninput="$.numberUtil.onlyNumber(this)"
                                               class="form-control" maxlength="14"
                                               aria-required="true" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">缴纳定金截止时间：</label>
                                    <div class="col-md-8 col-sm-8">
                                        <input type="text" class="time-input form-control" id="depositCutoffDate"
                                               name="depositCutoffDate"
                                               placeholder="" lay-key="2" autocomplete="off" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" th:if="${enquiry.vbillstatus=='1' && confirmQuotation!=null}">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">是否设置定金：</label>
                                    <div class="col-sm-8" th:if="${confirmQuotation.isDeposit=='1'}">是
                                    </div>
                                    <div class="col-sm-8" th:if="${confirmQuotation.isDeposit=='0'}">否
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <div class="form-group">
                                        <label class="col-sm-4">定金金额：</label>
                                        <div class="col-sm-8" th:text="${confirmQuotation.deposit}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <div class="form-group">
                                        <label class="col-sm-4">缴纳定金截止时间：</label>
                                        <div class="col-sm-8"
                                             th:text="${#dates.format(confirmQuotation.depositCutoffDate, 'yyyy-MM-dd HH:mm:ss')}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" id="confirm" class="btn btn-sm btn-primary" onclick="submitHandler()"><i
                class="fa fa-check"></i>确认
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];//费用类型

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');

        var options = {};
        $.table.init(options);

        var quotationList = [[${quotationList}]];
        if (quotationList == "" || quotationList == null) {
            $("#confirm").attr("disabled", true);
        }

        if ($('#vbillstatus').val() != "0") {
            $("#confirm").attr("disabled", true);
        }


        //油卡添加校验
        for (var i = 0; i < costTypeFreight.length; i++) {
            addOilCardNumberCheck(i);
        }
    });

    function addOilCardNumberCheck(ind) {
        //油卡添加校验
        $("#oilCardNumber_"+ind).rules("add", {
            remote: {
                url: ctx + "basic/fuelCard/checkOilCardNumber",
                type: "post",
                dataType: "json",
                data: {
                    fuelcardNo : function() {
                        return $.common.trim($("#oilCardNumber_" + ind).val());
                    },
                    carrierId : function () {
                        //默认传空
                        return "";
                    }
                },
                dataFilter: function(data, type) {
                    if (data > 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
            },
            messages: {
                remote: "无效油卡",
            }
        });
    }

    var prefix = ctx + "enquiry/enquiry";

    //取消抢单
    function returnQuotation(id) {
        var lotOutId = $('#lotOutId').val();
        var lotOutDtlId = $("#lotOutDtlId").val();
        $.modal.confirm("确认要取消此抢单信息吗?", function () {
            $.ajax({
                url: prefix + "/returnQuotation",
                type: "post",
                dataType: "json",
                data: {
                    lotOutDtlId: lotOutDtlId,
                    lotOutId: lotOutId
                },
                success: function (result) {
                    if (result.code == 0) {
                        location.reload();
                    }
                }
            });
        });
    }

    /**
     * 校验
     */
    $("#form-enquiry-add").validate({
        onkeyup: false,
        rules: {
        },
        focusCleanup: true,
    });

    /**
     * 根据金额比率计算分摊成本
     */
    function getCostShare() {
        //结算金额
        var costAmount = $("#costAmount").val().replace(/\s+/g, "");
        if (costAmount == "") {
            costAmount = 0;
        }

        var costShareList = $("[id^=costShare_]");
        costShareList.each(function (index, element) {
            //金额比率
            var rate = $("#rate_" + index).val();

            //计算分摊成本并回填
            $("#costShare_" + index).val(decimal(parseFloat(parseFloat(costAmount) * parseFloat(rate)), 2));

            $("#form-dispatch-add").validate().element($("#costShare_" + index))
        });

    }


    var lotOutId = $('#lotOutId').val();
    var outType = $('#outType').val();
    var lotOutDtlId;

    function submitHandler() {
        if ($.validate.form()) {
            if (outType == '0') {
                lotOutDtlId = $('input[name="radio"]:checked').val();
                if (lotOutDtlId == undefined) {
                    $.modal.alertError("请选择报价")
                    return;
                }
                $('#dtlId').val(lotOutDtlId);
            } else {
                lotOutDtlId = $("#lotOutDtlId").val();
                $('#dtlId').val(lotOutDtlId);
            }

            var data = $("#form-enquiry-add").serializeArray();
            //结算金额   校验金额是否相对
            var costAmount = decimal($("#costAmount").val(), 2);
            //付款金额合计
            var transFeeCountTotal = 0;
            $("[id^=transFeeCount_]").not("[id$=-error]").each(function () {
                var val = $(this).val().replace(/\s+/g, "") == "" ? 0 : $(this).val();
                transFeeCountTotal = parseFloat(transFeeCountTotal) + parseFloat(val);
            });
            if (costAmount != decimal(transFeeCountTotal, 2)) {
                $.modal.msgError("付款金额合计与结算金额不相等！");
                return;
            }
            if ($("#ifConfirmMoney").val() == 0&&$("#deposit").val()<=0) {
                $.modal.msgError("请输入大于0的定金金额");
                return;
            }
            if ($("#ifConfirmMoney").val() == 0) {
                $.operate.saveTab(prefix + "/updateQuotePrice", data, function (result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.msgSuccess("保存成功！");
                        $.modal.close();
                        location.reload();
                    }
                });
            } else {
                $.operate.saveTab(prefix + "/addEntrust", data, function (result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.msgSuccess("保存成功！");
                        $.modal.close();
                        location.reload();
                    }
                });
            }
        }
    }

    function updateScr() {
        if ($('#ifConfirmMoney').val() == 1) {
            $('#deposit').val("");
            $('#deposit').attr("disabled", true);
            $('#depositCutoffDate').val("");
            $('#depositCutoffDate').attr("disabled", true);
            /*         $("#form-enquiry-add").clearValidate(['deposit']);
                     $("#form-enquiry-add").clearValidate(['depositCutoffDate']);*/
        } else {
            $('#deposit').attr("required", true);
            $('#depositCutoffDate').attr("required", true);
            $('#deposit').attr("disabled", false);
            $('#depositCutoffDate').attr("disabled", false);
        }
    }


    layui.use('laydate', function () {
        var laydate = layui.laydate;
        var depositCutoffDate = laydate.render({
            elem: '#depositCutoffDate', //指定元素
            format: 'yyyy-MM-dd HH:mm:ss', //指定时间格式
            isInitValue: false,
            trigger: 'click',
            type: 'datetime',
            ready: function (date) {
                var now = new Date();
                this.dateTime.hours = now.getHours();
                this.dateTime.minutes = now.getMinutes();
                this.dateTime.seconds = now.getSeconds();
            },
            done: function (value, date, endDate) {
                depositCutoffDate.config.min = {
                    year: date.year,
                    month: date.month - 1,//关键
                    date: date.date,
                    hours: date.hours,
                    minutes: date.minutes,
                    seconds: date.seconds
                };
                $("#depositCutoffDate").val(value);
                $("#form-enquiry-add").validate().element($("#depositCutoffDate"));
            }
        });
    });

    function selectTr(trIndex) {
        $("input[name='radio']").each(function () {
            $(this).prop("checked", false);
        });
        $("#radio_" + trIndex).prop("checked", true);
    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num, v) {
        var vv = Math.pow(10, v);
        return Math.round(num * vv) / vv;
    }
</script>
</body>

</html>