<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('添加询价')" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <!-- <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: select2-css" /> -->
</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 30px;
        text-align: right;
        margin-bottom: 0;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /* line-height: 26px; */
    }
    .disin{
        display: inline-block;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .infotitle{
        line-height: 30px;
        font-weight: bold;
    }
    .titlebg{
        background: #eff3f9;
        padding: 5px 10px;

    }
    a.del-alink {
        display: block;
        margin: 120px auto 0;
    }
    .bggray{
        background: #f7f8fa;
    }
    .dropdown-menu {
        overflow: auto;
        height: 120px;
    }
    .pa0{
        padding: 0;
    }
    .no-gutter{
        margin: 0 -10px;
    }
    .divBot{
        border-top: 1px solid #ddd;
        padding: 10px;
        text-align: right;
    }
    .panel-default{
        border-color: transparent;
    }
    .lh26{
        height: 26px;
        line-height: 26px;
        margin: 0 5px;
    }
    .select2-container--bootstrap .select2-selection--single{
        height: 26px;
        line-height: 26px;
        padding: 0 6px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-inquiry-add" class="form-horizontal" novalidate="novalidate">
        <!--要求提货日期-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="over">
                                <!-- 台账begin  发货地 收货地-->
                                <div class="infotitle">基础信息</div>
                                <div class="row no-gutter">
                                    <div class="col-md-3 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 要求提货日：</label>
                                            <div class="flex_right">
                                                <input id="reqDeliDate" name="reqDeliDate" type="text" class="form-control" required
                                                       placeholder="要求提货日期" autocomplete="off" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 车长：</label>
                                            <div class="flex_right">
                                                <select name="carLength" id="carLength" class="form-control" th:with="type=${@dict.getType('car_len')}">
                                                    <option value="">-- 请选择 --</option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 车型：</label>
                                            <div class="flex_right">
                                                <select name="carType" id="carType" class="form-control" th:with="type=${@dict.getType('car_type')}">
                                                    <option value="">-- 请选择 --</option>
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                                <input id="carTypeName" name="carTypeName" type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter" style="background-color: #f3f3f3;line-height: 32px;padding: 12px 10px 10px;margin: 6px 0 10px;">
                                    <div class="col-md-2 col-sm-3" style="line-height: 30px;">
                                        <span class="fcff">*货品信息(重量和体积选填)</span>
                                    </div>
                                    <div class="col-md-3 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 货品：</label>
                                            <div class="flex_right">
                                                <input name="goodsName" id="goodsName" placeholder="请输入" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"> 货品重量(吨)：</label>
                                            <div class="flex_right">
                                                <div class="flex">
                                                    <input name="weightStart" id="weightStart" placeholder="请输入" class="form-control" type="text" maxlength="50" autocomplete="off" oninput="onlyNumberTwoDecimal(this)">
                                                    <div class="lh26"> - </div>
                                                    <input name="weightEnd" id="weightEnd" placeholder="请输入" class="form-control" type="text" maxlength="50" autocomplete="off" oninput="onlyNumberTwoDecimal(this)">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"> 货品体积(m³)：</label>
                                            <div class="flex_right">
                                                <div class="flex">
                                                    <input name="volumeStart" id="volumeStart" placeholder="请输入" class="form-control" type="text" maxlength="50" autocomplete="off" oninput="onlyNumberTwoDecimal(this)">
                                                    <div class="lh26"> - </div>
                                                    <input name="volumeEnd" id="volumeEnd" placeholder="请输入" class="form-control" type="text" maxlength="50" autocomplete="off" oninput="onlyNumberTwoDecimal(this)">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                   
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-sm-12 pa0">
                                        <div class="col-sm-3" style="padding-right: 0;">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span>发货地址：</label>
                                                <div class="flex_right">
                                                    <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-2" style="padding-right: 0;">
                                            <div class="flex">
                                                <div class="flex_right">
                                                    <select name="deliCityId" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-2" style="padding-right: 0;">
                                            <select name="deliAreaId" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                                        </div>
                                        <div class="col-sm-5">
                                            <input name="deliDetailAddr" id="deliDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                                   maxlength="50" autocomplete="off">
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-sm-12 pa0">
                                        <div class="col-sm-3" style="padding-right: 0;">
                                            <div class="flex">
                                                <label class="flex_left"><span class="fcff">*</span>收货地址：</label>
                                                <div class="flex_right">
                                                    <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-2" style="padding-right: 0;">
                                            <div class="flex">
                                                <div class="flex_right">
                                                    <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-2" style="padding-right: 0;">
                                            <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                                        </div>
                                        <div class="col-sm-5">
                                            <input name="arriDetailAddr" id="arriDetailAddr" placeholder="请输入详细地址" class="form-control" type="text"
                                                   maxlength="50" autocomplete="off">
                                        </div>
                                    </div>
                                </div>

                                <!-- 省市区名称 TODO -->
                                <input type="hidden" id="deliProvinceName" name="deliProvinceName">
                                <input type="hidden" id="deliCityName" name="deliCityName">
                                <input type="hidden" id="deliAreaName" name="deliAreaName">
                                <input type="hidden" id="arriProvinceName" name="arriProvinceName">
                                <input type="hidden" id="arriCityName" name="arriCityName">
                                <input type="hidden" id="arriAreaName" name="arriAreaName">

                                <div class="infotitle">询价信息</div>
                                <div class="row no-gutter">
                                    <div class="col-md-3 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 询价日期：</label>
                                            <div class="flex_right">
                                                <input type="text" class=" form-control" id="inquiryDate" name="inquiryDate" required
                                                       placeholder="询价日期" autocomplete="off" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 询价：</label>
                                            <div class="flex_right">
                                                <input name="inquiry" id="inquiry" placeholder="请输入询价..." class="form-control" type="text" maxlength="50" autocomplete="off"
                                                       oninput="onlyNumberTwoDecimal(this)">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 司机开价：</label>
                                            <div class="flex_right">
                                                <input name="driverOffer" id="driverOffer" placeholder="请输入司机报价..." class="form-control" type="text" maxlength="50" autocomplete="off"
                                                       oninput="onlyNumberTwoDecimal(this)">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 最低价：</label>
                                            <div class="flex_right">
                                                <input name="offer" id="offer" placeholder="请输入最低价..." class="form-control" type="text" maxlength="50" autocomplete="off"
                                                       oninput="onlyNumberTwoDecimal(this)">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row no-gutter">
                                    <div class="col-md-3 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 推荐价：</label>
                                            <div class="flex_right">
                                                <input name="recommendPrice" id="recommendPrice" placeholder="请输入推荐价格..." class="form-control" type="text" maxlength="50" autocomplete="off"
                                                       oninput="onlyNumberTwoDecimal(this)">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"><span class="fcff">*</span> 里程：</label>
                                            <div class="flex_right">
                                                <input name="mileage" id="mileage" placeholder="请输入里程..." class="form-control" type="text" maxlength="50" autocomplete="off"
                                                       oninput="onlyNumberTwoDecimal(this)">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-12">
                                        <div class="flex">
                                            <label class="flex_left"> 备注：</label>
                                            <div class="flex_right">
                                                <input name="remark" id="remark" placeholder="请输入备注..." class="form-control" type="text" maxlength="80" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 备注 -->
<!--                                <div class="row no-gutter">-->
<!--                                    <div class="col-sm-12">-->
<!--                                        <div class="flex">-->
<!--                                            <label class="flex_left">备注：</label>-->
<!--                                            <div class="flex_right">-->
<!--                                            <textarea name="parkWaybillList[0].remark" id="remark" maxlength="200" class="form-control valid"-->
<!--                                                      rows="2"></textarea>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal -->
        <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">保存并外发运满满</h4>
                </div>
                <div class="modal-body">
                    <div class="row no-gutter">
                        <div class="col-xs-12">
                            <div class="flex">
                                <label class="flex_left"><span class="fcff">*</span> 运满满定金：</label>
                                <div class="flex_right">
                                    <div class="input-group">
                                        <input name="ymmDeposit" id="ymmDeposit" placeholder="请输入运满满定金" class="form-control" type="text" maxlength="50" autocomplete="off"
                                        oninput="onlyNumberTwoDecimal(this)"  max="1000" min="20">
                                        <span class="input-group-addon">元</span>
                                    </div>
                                    <label class="error" for="ymmDeposit"></label>
                                </div>
                            </div>
                        </div>

                        <div class="col-xs-12">
                            <div class="flex">
                                <label class="flex_left"> 联系人：</label>
                                <div class="flex_right">
                                    <select class="form-control select2-multiple" aria-invalid="false" data-none-selected-text="联系人" onchange="getContactName()" style="width: 100%;"
                                        name="contactName" id="lotOutContactList">
                                        <option th:each="user:${userList}" th:value="${user.userName}" th:text="${user.userName+'/'+user.phonenumber}" th:selected="${user.userId} eq ${userId}"></option>
                                    </select>
                                    <input name="contactTel"  class="form-control" type="hidden">
                                    <input name="contactUserId"  class="form-control" type="hidden">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                <!-- <button type="button" class="btn btn-default" data-dismiss="modal">Close</button> -->
                <button type="button" class="btn btn-success" onclick="submitYmm()">确定</button>
                </div>
            </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10 divBot">
        <button type="button" class="btn btn-sm btn-success" onclick="submitPush()">保存并外发运满满</button>&nbsp;
        <button type="button" class="btn btn-sm btn-success" onclick="submitHandler()">保存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-white" onclick="closeItem()">关闭 </button>
    </div>
</div>
  
  

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<!-- <th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: select2-js" /> -->

<script th:inline="javascript">
    var carLen = [[${@dict.getType('car_len')}]];//车长
    var prefix = ctx + "inquiry";
    var userList=[[${userList}]];

    $(function () {
        // 初始化省市区  一共两行
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        let length = $("#collapseOne .over").length;
        
        let userId=[[${userId}]]
        if(userId != null && userId !='' && userId != "1"){
            userList.forEach(item=>{
                if(item.userId==userId){
                    $("input[name='contactTel']").val(item.phonenumber);
                    $("input[name='contactUserId']").val(item.userId); 
                }
            })
        }else{
            $("input[name='contactTel']").val(userList[0].phonenumber);
            $("input[name='contactUserId']").val(userList[0].userId);
        }

        $("#carType").on("change",function(){
            $("#carTypeName").val($(this).find("option:selected").text());
        })
    });

    function getContactName(){
        let contactName=$("#lotOutContactList").val();

        userList.forEach(item=>{
            if(item.userName==contactName){
                $("input[name='contactTel']").val(item.phonenumber);
                $("input[name='contactUserId']").val(item.userId);
            }
        })
    }



    /**
     * 数字 保留两位小数
     */
    function onlyNumberTwoDecimal (obj) {
        //得到第一个字符是否为负号
        var t = obj.value.charAt(0);
        //先把非数字的都替换掉，除了数字和.
        obj.value = obj.value.replace(/[^\d\.]/g, '');
        //必须保证第一个为数字而不是.
        obj.value = obj.value.replace(/^\./g, '');
        //保证只有出现一个.而没有多个.
        obj.value = obj.value.replace(/\.{2,}/g, '.');
        //保证.只出现一次，而不能出现两次以上
        obj.value = obj.value.replace('.', '$#$').replace(/\./g, '').replace(
            '$#$', '.');
        obj.value = obj.value.slice(0, 15);
        // 只能输入两个小数
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d).*$/,'$1$2.$3');
        //如果第一位是负号，则允许添加
        if (t == '-') {
            obj.value = '-' + obj.value;
        }
    }


    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#inquiryDate").removeAttr("lay-key");
        laydate.render({
            elem: '#inquiryDate', 
            type: 'datetime', 
            value: new Date()
        });

        laydate.render({ 
            elem: '#reqDeliDate',
            type: 'datetime',
            trigger: 'click'
        });
    })

    /**
     * 校验
     */
    $("#form-inquiry-add").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            inquiryDate:{
                required:true,
            },
            reqDeliDate:{
                required:true,
            },
            carLength:{
                required:true,
            },
            carType:{
                required:true,
            },
            goodsName:{
                required:true,
            },
            // inquiry:{
            //     required:true,
            // },
            // offer:{
            //     required:true,
            // },
            // mileage:{
            //     required:true
            // },
            deliProvinceId: {
                required:true
            },
            deliCityId: {
                required:true
            },
            arriProvinceId: {
                required:true
            },
            arriCityId: {
                required:true
            },
            // driverOffer: {
            //     required:true
            // },
            // recommendPrice: {
            //     required:true
            // }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        changePcaNmae();
        // console.log(JSON.stringify($('#form-inquiry-add').serializeArray()))
        // console.log("===========")
        // console.log($('#form-inquiry-add').serializeArray())
        if ($.validate.form()) {
            //commit();
            $.operate.saveTab(prefix + "/addInquiryRecord", $('#form-inquiry-add').serialize());
            //jQuery.subscribe("cmt", commit);
        }
    }
    /**
     * 保存外发运满满
     */
    function submitPush(){
        changePcaNmae();
        if ($.validate.form()) {
            $('#myModal').modal({
                keyboard: false
            })
        }
    }
    function submitYmm() {
        $.operate.saveTab(prefix + "/addInquiryRecordAndOutGoingYmm", $('#form-inquiry-add').serialize());
        $('#myModal').modal('hide');
    }

    function commit() {
        console.log(JSON.stringify($('#form-inquiry-add').serializeArray()))
        console.log("===========")
        console.log($('#form-inquiry-add').serializeArray())
        $.ajax({
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            url: prefix + "/addParkWayBill",
            data: JSON.stringify($('#form-inquiry-add').serializeArray()),//JSON.stringify(data.field),
            dataType: 'json',
            success: function(result) {
                console.log(result)
                // if(result.code == 0) {
                //     layer.msg('注册成功！');
                // } else {
                //     layer.msg(result.msg);
                // }
            }
        });

        //$.operate.saveTab(prefix + "/addParkWayBill", $('#form-invoice-add').serialize());
    }

    /**
     * 转换省市区名称
     */
    function changePcaNmae(){
        //基本信息转换省市区保存name
        var deliProvinceName = $("#deliProvinceId").find(":selected").text();
        if(deliProvinceName === '-- 请选择 --'){
            $("#deliProvinceName").val("");
        }else{
            $("#deliProvinceName").val(deliProvinceName);
        }
        var deliCityName = $("#deliCityId").find(":selected").text();
        if(deliCityName === '-- 请选择 --'){
            $("#deliCityName").val("");
        }else{
            $("#deliCityName").val(deliCityName);
        }
        var deliAreaName = $("#deliAreaId").find(":selected").text();
        if(deliAreaName === '-- 请选择 --'){
            $("#deliAreaName").val("");
        }else{
            $("#deliAreaName").val(deliAreaName);
        }

        var arriProvinceName = $("#arriProvinceId").find(":selected").text();
        if(arriProvinceName === '-- 请选择 --'){
            $("#arriProvinceName").val("");
        }else{
            $("#arriProvinceName").val(arriProvinceName);
        }
        var arriCityName = $("#arriCityId").find(":selected").text();
        if(arriCityName === '-- 请选择 --'){
            $("#arriCityName").val("");
        }else{
            $("#arriCityName").val(arriCityName);
        }
        var arriAreaName = $("#arriAreaId").find(":selected").text();
        if(arriAreaName === '-- 请选择 --'){
            $("#arriAreaName").val("");
        }else{
            $("#arriAreaName").val(arriAreaName);
        }
    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }

</script>
</body>

</html>