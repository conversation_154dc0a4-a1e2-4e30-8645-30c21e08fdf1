<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('报价明细')"/>
</head>
<style type="text/css">
    .custom-tab td {
        text-align: left;
    }

    .quotePrice {
        text-align: right !important;
    }
    .lh26{
        line-height: 26px;
    }
    .ffcd{
        color:#FFFCD3;
    }
    .f16{
        font-size: 16px;
    }
    .f15{
        font-size: 15px;
    }
    .f12{
        font-size: 12px;
    }
    .fw{
        font-weight: 600;
    }
    .dif{
        display: flex
    }
    .addbtn{
        width: 28px;
        text-align: center;
        color: #fff;
        background: #1ab394;
        line-height: 26px;
        border-radius: 5px;
        cursor: pointer;
    }
    .col-sm-4{
        text-align: right;
    }
    .resize{
        resize:auto;
    }
    .flex{
        display: flex;
        align-items:center;
        justify-content:flex-start;
        color: #808080;
    }
    .flex_left{
        width: 80px;
        text-align: right;
        color: #333333 !important;
        margin-bottom: 0;
    }
    .flex_right{
        min-width:0;
        flex:1;
    }

    .toText{
        display:inline-block;
        width: 32px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        /* padding: 8px 4px; */
        border-radius: 5px;
        color: #fff;
        vertical-align: middle;
    }

    .label-primaryT{
        color: #1ab394;
        background-color: transparent;
        border: 1px solid #1ab394;
    }



    .tabs {
        display: flex;
        height: 100%;
        font-family: Arial, sans-serif;
    }

    .tabs-menu {
        width: 150px;
        background-color: #fff0;
        border-right: 1px solid #ddd;
        position: relative;
        /*height: 300px; !* 设置固定高度 *!*/
        /*overflow-y: auto; !* 添加垂直滚动条 *!*/

    }

    /*.tabs-menu::-webkit-scrollbar {*/
    /*    width: 6px; !* 滚动条宽度 *!*/
    /*}*/

    /*.tabs-menu::-webkit-scrollbar-track {*/
    /*    background-color: transparent; !* 滚动条背景颜色 *!*/
    /*}*/

    /*.tabs-menu::-webkit-scrollbar-thumb {*/
    /*    background-color: #409eff; !* 滚动条颜色 *!*/
    /*    border-radius: 3px; !* 滚动条圆角 *!*/
    /*}*/


    .tabs-menu-item {
        padding: 10px;
        cursor: pointer;
        position: relative;
        transition: color 0.3s;
        text-align: right;
        padding-right: 20px;
        z-index: 1; /* 添加这一行 */
    }

    .tabs-indicator {
        position: absolute;
        right: 0;
        top: 0; /* 修改这一行 */
        width: 2px;
        height: 40px;
        background-color: #409eff;
        transition: transform 0.3s ease-out;
        transform: translateY(0);
    }

    .tabs-menu-item.active {
        color: #409eff;
    }

    .tabs-content {
        flex: 1;
        padding: 10px;
    }

    .tabs-pane {
        display: none;
        padding: 10px;
    }

    .tabs-pane.active {
        display: block;
    }


    .wfp{
        cursor: pointer;
    }
    .yfp {
        background-color: #f5f5f5;
        border-radius: 5px;
        text-decoration: line-through;
        text-decoration-color: rgba(0, 0, 0, 0.3);
        cursor: default;
    }

    .yfp td:nth-child(2) {
        position: relative;
    }

    /*.yfp td:nth-child(2)::before {*/
    /*    content: "已分配";*/
    /*    position: absolute;*/
    /*    left: 0;*/
    /*    top: 50%;*/
    /*    transform: translateY(-50%);*/
    /*    background-color: #ccc;*/
    /*    padding: 2px 5px;*/
    /*    border-radius: 3px;*/
    /*    font-size: 12px;*/
    /*    margin-right: 10px;*/
    /*}*/

</style>
<body>
<div class="form-content" style="padding: 0;">
    <form id="form-enquiry-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" id="outType" th:value="${outType}"/>
        <input type="hidden" id="lotOutId" name="lotOutId" th:value="${enquiry.lotOutId}"/>
        <input type="hidden" id="vbillstatus" th:value="${enquiry.vbillstatus}"/>

<!--        <input name="carrierId" id="carrierId" type="hidden">-->

        <input name="corDate" type="hidden"  th:value="${#dates.format(enquiry.corDate, 'yyyy-MM-dd HH:mm:ss')}">
        <div style="padding: 20px 20px 10px;border-bottom: 1px solid #ddd;background-color: #FAFBFC;">
            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <span class="fw" th:text="${invoiceVbillno }"></span>
                    <div style="float: right;">
                        <span> 报价截止时间：</span>
                        <span style="color: red;" th:text="${#dates.format(enquiry.cutoffDate, 'yyyy-MM-dd HH:mm:ss')}"></span>
                    </div>
                </div>   
            </div>
            <div class="row mt5">
                <div class="col-md-8 col-sm-8 f16 fw">
                    <span th:text="${enquiry.deliDetailAddr }"></span>
                    <span> <i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394;"></i></span>
                    <span th:text="${enquiry.arriDetailAddr }"></span>
                    <span class="label label-primaryT" style="font-size: 13px;margin-left: 45px;font-weight: 500;" th:if="${enquiry.isToAndFro} == '1'">往返</span>
                </div>

                <div class="col-md-4 col-sm-4" style="float: right;">
                    <span> 指导价：</span>
                    <span style="color:#FF6C00;" id="referencePriceSpan">暂无</span>
                </div>

            </div>  
            <div class="row mt5">
                <div class="col-md-3 col-sm-12">
                    <span> 要求装货日期：</span>
                    <span th:text="${#dates.format(enquiry.reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}"></span>
                </div>
                <div class="col-md-3 col-sm-12">
                    <span> 货品信息：</span>
                    <span th:each="good : ${goodsList}" th:text="${good.goodsName }"></span>
                    <span th:text="${enquiry.weightCount}+'吨'" th:if="${enquiry.weightCount!=null}"></span>
                    <input type="hidden" name="weightCount" th:value="${enquiry.weightCount}">
                </div>
                <div class="col-md-3 col-sm-12">
                    <span> 要求车长车型：</span>
                    <span th:text="${@dict.getLabel('car_len',enquiry.carLen)}"></span>米
                    <span th:text="${@dict.getLabel('car_type',enquiry.carType)}"></span>
                </div>
            </div>      
        </div>
        <div style="padding: 20px 20px 10px;border-bottom: 1px solid #ddd;background-color: #FAFBFC;">
            <div class="tabs">
                <div class="tabs-menu">
                    <div th:each="lotOutInv,stat:${lotOutInvList}"
                         th:class="${stat.index} == 0 ? 'tabs-menu-item active': 'tabs-menu-item'"
                         th:data-lot-out-inv-id="${lotOutInv.id}"
                         onclick="changeLotOutInv(this.getAttribute('data-lot-out-inv-id'));">
                        <div class="f15">[[${lotOutInv.invoiceVbillno}]]</div>
                        <div class="f12">[[${lotOutInv.segmentNo}]]</div>
                        <div th:id="|inv_${lotOutInv.id}|">[[${lotOutInv.status} == 0 ? '未分配承运商' : '已分配承运商']]</div>
                    </div>
                    <div class="tabs-indicator"></div>
                </div>
                <div class="tabs-content">
                    <div class="tabs-pane active" id="ctDiv">

                    </div>
                </div>
            </div>

        </div>

        <div style="padding: 20px 20px 10px;border-bottom: 1px solid #ddd;background-color: #FAFBFC;">
            <div class="row mt10">
                <div class="col-md-12 col-sm-12" style="margin-left: 5px;">
                    <div>
                        <span style="font-size: 20px;color: #000;">已报价承运商：</span>
                        <span style="margin-left: 10px">*点击承运商名称可选择承运商</span>
                    </div>
                    <div class="fixed-table-body mt10">
                        <table border="0" class="custom-tab table">
                            <thead>
                            <tr th:if="${outType=='0'}">
<!--                                <th style="width: 10%;">选择</th>-->
                                <th style="width: 5%;">序号</th>
                                <th style="width: 65%;">承运商名称</th>
                                <th style="width: 20%;text-align: right;">报价金额(元)</th>
                            </tr>
                            </thead>
                            <tbody th:if="${outType=='0'}">
                            <tr  th:each="quotation,stat:${quotationList}"
                                 th:data-lot-out-dtl-id="${quotation.lotOutDtlId}"
                                 th:onclick="${quotation.status == '0'} ? 'selectTr(this.getAttribute(\'data-lot-out-dtl-id\'))' : ''">
                                <td style="text-align:center;" th:text="${stat.index+1}"></td>
                                <td th:class="${quotation.status} == '1' ? '' : 'wfp'">
                                    <span class="toText" style="background-color: #009AFE;" th:if="${quotation.carrierBalaType==1}">单笔</span>
                                    <span class="toText" style="background-color: #FF9008;" th:if="${quotation.carrierBalaType==2}">月度</span>

                                    <span th:class="${quotation.status} == '1' ? 'yfp' : ''" th:text="${quotation.carrierName }"></span>/
                                    <span th:class="${quotation.status} == '1' ? 'yfp' : ''" th:text="${quotation.phone }"></span>/
                                    <span th:class="${quotation.status} == '1' ? 'yfp' : ''" th:text="${quotation.driverName }"></span>/
                                    <span th:class="${quotation.status} == '1' ? 'yfp' : ''" th:text="${quotation.carno }"></span>/
                                    <span th:class="${quotation.status} == '1' ? 'yfp' : ''" th:text="${quotation.carLenName == null ? '':quotation.carLenName+'米'}"></span>
                                    <span th:class="${quotation.status} == '1' ? 'yfp' : ''" th:text="${quotation.carTypeName }"></span>

                                    <span th:if="${quotation.status} == '1'"><span class="label label-primaryT ">已分配</span></span>
                                </td>

                                <td style="text-align: right;">
                                    <span style="color:#FF9008;" th:text="|￥${#numbers.formatDecimal(quotation.quotedPrice,0,'COMMA',2,'POINT')}|"></span>
                                    <br>
                                    <span th:text="${#dates.format(quotation.commitDate, 'yyyy-MM-dd HH:mm:ss')}"></span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
<!--        <button type="button" id="confirm" class="btn btn-sm btn-primary" onclick="submitHandler()"><i-->
<!--                class="fa fa-check"></i>确认-->
<!--        </button>&nbsp;-->
<!--        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭-->
<!--        </button>-->
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:src="@{/js/vue.min.js}"></script>

<script th:src="@{/element-ui@2.15.13/lib/index.js}"></script>

<script th:inline="javascript">
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];//费用类型
    var prefix = ctx + "enquiry/enquiry";
    var quotationList = [[${quotationList}]];
    var lotOutInvList = [[${lotOutInvList}]];


    var vbillstatus =[[${enquiry.vbillstatus}]]

    var weightCount = [[${enquiry.weightCount}]]
    var numCount = [[${enquiry.numCount}]]
    var volumeCount = [[${enquiry.volumeCount}]]

    var oilTax = [[${@sysConfigServiceImpl.selectConfigByKey("net_profits_oil_tax")}]];


    const tabsMenuItems = document.querySelectorAll('.tabs-menu-item');
    // const tabsPanes = document.querySelectorAll('.tabs-pane');
    const tabsIndicator = document.querySelector('.tabs-indicator');
    tabsMenuItems.forEach((item, index) => {
        item.addEventListener('click', () => {
            // 移除所有选项卡的活动状态
            tabsMenuItems.forEach(item => item.classList.remove('active'));
            // tabsPanes.forEach(pane => pane.classList.remove('active'));

            // 设置当前选项卡为活动状态
            item.classList.add('active');
            // tabsPanes[index].classList.add('active');

            // 移动激活指示器
            const itemHeight = item.offsetHeight;
            const itemTop = item.offsetTop;
            tabsIndicator.style.transform = `translateY(${itemTop}px)`;
            tabsIndicator.style.height = `${itemHeight}px`;
        });
    });

    $(function () {
        var options = {};
        $.table.init(options);

        if (quotationList == "" || quotationList == null) {
            $("#confirm").attr("disabled", true);
        }

        if ($('#vbillstatus').val() != "0") {
            $("#confirm").attr("disabled", true);
        }


        //油卡添加校验
        for (var i = 0; i < costTypeFreight.length; i++) {
            addOilCardNumberCheck(i);
        }

        // if (vbillstatus !== '0') {
        //     const quotations = quotationList.filter(q => q.status === "1");
        //
        //     getReferencePrice(quotations[0].carLenId, quotations[0].carTypeId);
        // }

        if ([[${enquiry.isOversize}]] == 1) {
            $('#isOversize').prop('checked', true);
        }else {
            $('#isOversize').prop('checked', false);
        }
        changeIsOversize()

        if ([[${enquiry.isCustomsClearance}]] == 1) {
            $('#isCustomsClearance').prop('checked', true);
        }else {
            $('#isCustomsClearance').prop('checked', false);
        }
        changeIsCustomsClearance()

        changeLotOutInv(lotOutInvList[0].id)
    });

    function addOilCardNumberCheck(ind) {
        //油卡添加校验
        $("#oilCardNumber_"+ind).rules("add", {
            remote: {
                url: ctx + "basic/fuelCard/checkOilCardNumber",
                type: "post",
                dataType: "json",
                data: {
                    fuelcardNo : function() {
                        return $.common.trim($("#oilCardNumber_" + ind).val());
                    },
                    carrierId : function () {
                        //默认传空
                        return "";
                    }
                },
                dataFilter: function(data, type) {
                    if (data > 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
            },
            messages: {
                remote: "无效油卡",
            }
        });
    }


    const pricingMethodEnumList = [[${pricingMethodEnumList}]]



    var quotationHtml = '';
    for ( var i = 0; i < quotationList.length; i++) {
        let quotation = quotationList[i]
        if (quotation.status === "1") {
            continue;
        }
        quotationHtml +=
            `<option  value='${quotation.carrierId}' data-lotOutDtlId="${quotation.lotOutDtlId}">
                ${quotation.carrierName}/${quotation.phone}/${quotation.driverName}/${quotation.carno}/${quotation.carLenName == null ? '':quotation.carLenName+'米'}/${quotation.carTypeName}
            </option>`;
    }

    function getPricingMethodHtml(value){
        var pricingMethodHtml = '';
        value = value === 4 ? 7 : 3

        for ( var i = 0; i < pricingMethodEnumList.length; i++) {
            pricingMethodHtml += `<option  value='${pricingMethodEnumList[i].value}' ${pricingMethodEnumList[i].value === value ? 'selected':''}>
                                        ${pricingMethodEnumList[i].context}
                                  </option>`
        }
        return pricingMethodHtml;
    };

    function getPricingMethodContext(value) {
        for ( var i = 0; i < pricingMethodEnumList.length; i++) {
            if (pricingMethodEnumList[i].value == value) {
                return pricingMethodEnumList[i].context;
            }
        }
        return "";
    }

    var profit = 0
    function changeLotOutInv(lotOutInvId) {
        if (lotOutInvId == null || lotOutInvId == "") {
            return;
        }
        $.ajax({
            url: ctx + "enquiry/enquiry/getLotOutInv",
            type: "post",
            dataType: "json",
            data: {"lotOutInvId": lotOutInvId},
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
                $.modal.disable();
            },
            success: function (data) {
                if (data.code === 0){
                    let lotOutInv = data.data;
                    if (lotOutInv.status === 1){
                        let detailHtml = `
                            <div>
                                <input type="hidden" id="segmentId"  value="${lotOutInv.segmentId}">
                                <input type="hidden" id="transCode"  value="${lotOutInv.transCode}">
                                <input type="hidden" id="carLenId"  value="${lotOutInv.carLen}">
                                <input type="hidden" id="carTypeId"  value="${lotOutInv.carType}">
                                <input type="hidden" id="carrierId"  value="${lotOutInv.carrierId}">
                                <input type="hidden" id="pricingMethod"  value="${lotOutInv.pricingMethod}">
                                <input type="hidden" name="isOversize"  value="${lotOutInv.isOversize}">
                                <input type="hidden" name="isCustomsClearance"  value="${lotOutInv.isCustomsClearance}">
                                <input type="hidden" name="isBigCartLoad"  value="${lotOutInv.isBigCartLoad}">

                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left lh26" >计价方式：</label>
                                            <div class="flex_right lh26" style="color: #000000;">
                                                ${getPricingMethodContext(lotOutInv.pricingMethod)}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left lh26" >单价：</label>
                                            <div class="flex_right lh26"  style="color: #000000;">
                                                ${lotOutInv.unitPrice ==null ? '':lotOutInv.unitPrice+'元' }
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left ">结算金额：</label>
                                            <div class="flex_right lh26" style="color: #000000;">
                                                ${lotOutInv.transFee==null ? '':lotOutInv.transFee +'元' }
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left lh26" >收款账户：</label>
                                            <div class="flex_right lh26" style="color: #000000;">
                                                ${lotOutInv.bankAccount+'/'+lotOutInv.bankCard}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt10">
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                油卡号：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                ${lotOutInv.oilCardNumber == null ? '': lotOutInv.oilCardNumber}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                预付油卡：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                 ${lotOutInv.feeYfyk == null ? '':lotOutInv.feeYfyk + '元' }
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                回付油卡：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                 ${lotOutInv.feeHfyk == null ? '':lotOutInv.feeHfyk + '元' }
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="row mt10">
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                回付现金：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                ${lotOutInv.feeHfxj == null ? '':lotOutInv.feeHfxj + '元' }
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                到付现金：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                ${lotOutInv.feeDfxj == null ? '':lotOutInv.feeDfxj + '元' }
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="row mt10">
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                大车配载：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                ${lotOutInv.isBigCartLoad != null && lotOutInv.isBigCartLoad == 1 ? '是' : '否'}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                是否报关：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                ${lotOutInv.isCustomsClearance != null && lotOutInv.isCustomsClearance == 1 ? '是' : '否'}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                大件/三超：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                ${lotOutInv.isOversize != null && lotOutInv.isOversize == 1 ? '是' : '否'}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                长宽高：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                ${lotOutInv.goodsLength != null ? lotOutInv.goodsLength + '米；' : ''}
                                                ${lotOutInv.goodsWidth != null ? lotOutInv.goodsWidth + '米；' : ''}
                                                ${lotOutInv.goodsHeight != null ? lotOutInv.goodsHeight + '米；' : ''}
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="row mt10">
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                司机2：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                ${lotOutInv.driverName2 == null ? '' : lotOutInv.driverName2}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                手机：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                ${lotOutInv.driverMobile2 == null ? '' : lotOutInv.driverMobile2}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                身份证：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                ${lotOutInv.driverCardId2 == null ? '' : lotOutInv.driverCardId2}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-3">
                                        <div class="flex">
                                            <label class="flex_left">
                                                预计到场：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                ${lotOutInv.estimatedArrivalTime == null ? '' : lotOutInv.estimatedArrivalTime}
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="row mt10">
                                    <div class="col-md-10 col-sm-10">
                                        <div class="flex">
                                            <label class="flex_left">
                                                备注：
                                            </label>
                                            <div class="flex_right"  style="color: #000000;">
                                                ${lotOutInv.note == null ? '':lotOutInv.note}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt10">
                                    <div class="flex">
                                        <label class="flex_left">
                                            承运商：
                                        </label>
                                        <div class="flex_right"  style="color: #000000;">
                                            ${lotOutInv.carrierName}/${lotOutInv.carrierPhone}/${lotOutInv.driverName}/${lotOutInv.carno}/${lotOutInv.carLenName == null ? '':lotOutInv.carLenName+'米'}/${lotOutInv.carTypeName}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            `

                        $("#ctDiv").empty().append(detailHtml);
                    }else {
                        let closeHtml = '';
                        if ([[${@permission.hasPermi('enquiry:enquiry:close')}]] != "hidden"){
                            closeHtml =
                                `
                                <button type="button" class="btn btn-xl btn-danger" onclick="closeSingle('${lotOutInvId}')">
                                    <i class="fa fa-remove"></i>关闭运段
                                </button>
                                `
                        }
                        let addHtml =
                            `
                            <div >
                                <input type="hidden" id="lotOutInvId" name="lotOutInvId" value="${lotOutInvId}">
                                <input type="hidden" id="segmentId"  value="${lotOutInv.segmentId}">
                                <input type="hidden" id="transCode"  value="${lotOutInv.transCode}">
                                <input type="hidden" id="carLenId"  value="${lotOutInv.carLen}">
                                <input type="hidden" id="carTypeId"  value="${lotOutInv.carType}">

                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left lh26"><span style="color: red">*</span>计价方式：</label>
                                            <div class="flex_right">
                                                <div class="input-group" style="width: 100%;">
                                                    <select name="pricingMethod" id="pricingMethod" class="form-control valid" autocomplete="off"
                                                            required onchange="changeUnitPrice();getReferencePrice()">
                                                        <option value=""></option>
                                                        ${getPricingMethodHtml(lotOutInv.pricingMethod)}
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left lh26">单价：</label>
                                            <div class="flex_right">
                                                <div class="input-group" style="width: 100%;">
                                                    <input class="form-control" type="number" id="unitPrice" name="unitPrice"
                                                           placeholder="单价" onchange="changeUnitPrice()">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left lh26"><span style="color: red">*</span>结算金额：</label>
                                            <div class="flex_right">
                                                <div class="input-group">
                                                    <span class="input-group-addon">¥</span>
                                                    <input name="costAmount" id="costAmount" type="text"
                                                           oninput="$.numberUtil.onlyNumber(this);calculateFee()"
                                                           class="form-control valid" maxlength="14"
                                                           required aria-required="true"
                                                           autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left lh26"><span style="color: red">*</span>收款账户：</label>
                                            <div class="flex_right" style="margin-right: 5px;">
                                                <select name="carrBankId" id="carrBankId" class="form-control valid" aria-invalid="false" required>
                                                    <option value="">请选择</option>
                                                </select>
                                            </div>
                                            <div>
                                                <div class="addbtn" onclick="addPayeeEvent()">+</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="row mt5">
                                        <div class="col-md-3 col-sm-3">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    预付油卡：
                                                </label>
                                                <div class="flex_right">
                                                    <input name="feeYfyk" id="feeYfyk" placeholder="请输入价格"
                                                           class="form-control" type="text" oninput="$.numberUtil.onlyNumber(this);calculateTotal()"
                                                           onchange="calculateTransFeeCount()"
                                                           autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-3">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    回付油卡：
                                                </label>
                                                <div class="flex_right">
                                                    <input name="feeHfyk" id="feeHfyk" placeholder="请输入价格"
                                                           class="form-control" type="text" oninput="$.numberUtil.onlyNumber(this);calculateTotal()"
                                                           onchange="calculateTransFeeCount()"
                                                           autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-3">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    油卡号：
                                                </label>
                                                <div class="flex_right">
                                                    <input name="oilCardNumber" id="oilCardNumber" placeholder="请输入油卡号"
                                                           class="form-control" type="text" maxlength="20" autocomplete="off">
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="row mt5">
                                        <div class="col-md-3 col-sm-3">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    回付现金：
                                                </label>
                                                <div class="flex_right">
                                                    <input name="feeHfxj" id="feeHfxj" placeholder="请输入价格"
                                                           class="form-control" type="text" oninput="$.numberUtil.onlyNumber(this);calculateTotal()"
                                                           onchange="calculateTransFeeCount()"
                                                           autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-3">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    到付现金：
                                                </label>
                                                <div class="flex_right">
                                                    <input name="feeDfxj" id="feeDfxj" placeholder="请输入价格"
                                                           class="form-control" type="text" oninput="$.numberUtil.onlyNumber(this);calculateTotal()"
                                                           onchange="calculateTransFeeCount()"
                                                           autocomplete="off">
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <div id="negativeGrossProfitDiv" class="col-sm-5" style="display: none;">
                                                <div class="label label-danger">负毛利区间</div>
                                                <div id="negativeGrossProfit" style="padding-top: 5px;color: #ed5565;font-size: 1em"></div>
                                            </div>

                                        </div>

                                    </div>
                                    <div class="row mt5">
                                        <div class="col-md-3 col-sm-3">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    <span style="color: red">*</span>大车配载：
                                                </label>
                                                <div class="flex_right">
                                                    <div style="display: flex;align-items: baseline;">
                                                        <input type="checkbox" id="isBigCartLoad"
                                                               onchange="changeIsBigCartLoad();getReferencePrice()"
                                                               style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />

                                                        <input name="isBigCartLoad" value="0" type="hidden">
                                                        <label for="isBigCartLoad" style="font-size: 1.1em; user-select:none; vertical-align: middle;">是</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-3 col-sm-3">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    <span style="color: red">*</span>是否报关：
                                                </label>
                                                <div class="flex_right">
                                                    <div style="display: flex;align-items: baseline;">
                                                        <input type="checkbox" id="isCustomsClearance"
                                                               onchange="changeIsCustomsClearance();getReferencePrice()"
                                                               style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />

                                                        <input name="isCustomsClearance" value="0" type="hidden">
                                                        <label for="isCustomsClearance" style="font-size: 1.1em; user-select:none; vertical-align: middle;">是</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-2 col-sm-2">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    <span style="color: red">*</span>大件/三超：
                                                </label>
                                                <div class="flex_right">
                                                    <div style="display: flex;align-items: baseline;">
                                                        <input type="checkbox" id="isOversize"
                                                               onchange="changeIsOversize();getReferencePrice()"
                                                               style="transform: scale(1.2); vertical-align: middle; margin-right: 8px;" />

                                                        <input name="isOversize" value="0" type="hidden">
                                                        <label for="isOversize" style="font-size: 1.1em; user-select:none; vertical-align: middle;">是</label>
                                                        <i class="fa fa-question-circle ml5 cur"
                                                           style="font-size: 16px;"
                                                           data-toggle="tooltip"
                                                           data-container="body" data-placement="right" data-html="true" title=""
                                                           data-original-title="<ul>
                                                                <li>1、当填写长、宽、高中的其中一项，则代表<big>体积</big>超出规范</li>
                                                                <li>2、长、宽、高不填，则默认<big>重量</big>超出规范</li>
                                                            </ul>
                                                           "></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-4 col-sm-4">
                                            <div class="flex" id="goodsSizeDiv" style="display: none;">
                                                <div id="oversizeTypeDiv" style="font-size: 12px;margin-right: 5px;display: flex;align-items: center;">
                                                    超重
                                                </div>

                                                <div class="input-group " style="flex: 1;margin-right: 3px">
                                                    <input name="goodsLength" id="goodsLength" placeholder="长" type="text" class="form-control" style="color: #000" oninput="$.numberUtil.onlyNumber(this);changeOversizeType()" autocomplete="off">
                                                    <span class="input-group-addon">米</span>
                                                </div>
                                                <div class="input-group " style="flex: 1;margin-right: 3px">
                                                    <input name="goodsWidth" id="goodsWidth" placeholder="宽" type="text" class="form-control " style="color: #000" oninput="$.numberUtil.onlyNumber(this);changeOversizeType()"  autocomplete="off">
                                                    <span class="input-group-addon">米</span>
                                                </div>
                                                <div class="input-group" style="flex: 1;margin-right: 3px">
                                                    <input name="goodsHeight" id="goodsHeight" placeholder="高" type="text" class="form-control" style="color: #000" oninput="$.numberUtil.onlyNumber(this);changeOversizeType()" autocomplete="off">
                                                    <span class="input-group-addon">米</span>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="row mt5">
                                        <div class="col-md-3 col-sm-3">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    司机2：
                                                </label>
                                                <div class="flex_right">
                                                    <input name="driverName2" id="driverName2" placeholder="请输入司机姓名" class="form-control valid" type="text">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-3">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    手机：
                                                </label>
                                                <div class="flex_right">
                                                    <input name="driverMobile2" id="driverMobile2" placeholder="请输入司机手机" class="form-control valid" type="text">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-3">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    身份证：
                                                </label>
                                                <div class="flex_right">
                                                    <input name="driverCardId2" id="driverCardId2" placeholder="请输入司机身份证" class="form-control valid" type="text">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-sm-3">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    <span style="color: red">*</span>预计到场：
                                                </label>
                                                <div class="flex_right">
                                                    <input name="estimatedArrivalTime" id="estimatedArrivalTime"
                                                           placeholder="预计到场时间" class="form-control valid" type="text" required readonly>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt5">
                                        <div class="col-md-10 col-sm-10">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    备注：
                                                </label>
                                                <div class="flex_right">
                                                    <textarea name="note" id="note" placeholder="请输入备注"
                                                              maxlength="200" class="form-control" rows="1" autocomplete="off"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt5">
                                       <div class="col-md-8 col-sm-8">
                                            <div class="flex">
                                                <label class="flex_left">
                                                    <span style="color: red">*</span>承运商：
                                                </label>
                                                <div class="flex_right">
                                                    <select name="carrierId" id="carrierId" class="form-control valid"
                                                            onchange="selectTr()" aria-invalid="false" required>
                                                        <option value="">请选择</option>
                                                        ${quotationHtml}
                                                    </select>
                                                    <input type="hidden" id="lotOutDtlId" name="lotOutDtlId" value="">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                                    <div >
                                    </div>
                                <div >
                                    <button type="button" id="confirm" class="btn btn-xl btn-primary" onclick="submitHandler('${lotOutInvId}')">
                                        <i class="fa fa-check"></i>分配
                                    </button>&nbsp;
                                    ${closeHtml}
                                </div>
                            </div>
                        `
                        $("#ctDiv").empty().append(addHtml);

                        $('[data-toggle="tooltip"]').tooltip();

                        layui.use('laydate', function(){
                            var laydate = layui.laydate;

                            //要求提货日期
                            var reqDeliDate = laydate.render({
                                elem: '#estimatedArrivalTime', //指定元素
                                id: '#estimatedArrivalTime',
                                format: 'yyyy-MM-dd HH点',
                                isInitValue: false,
                                trigger: 'click',
                                type: 'datetime',
                                min: -1,
                                fullPanel: true,
                                ready: function (date) {
                                    let inst = laydate.getInst("#estimatedArrivalTime");
                                    let index = inst.config.index;

                                    var styleElement = $("<style>");
                                    // 在<style>元素中添加你的样式
                                    styleElement.text(".layui-laydate {width: 385px !important;} .layui-laydate-header{width: 72%;} .layui-laydate-content > ul {width: 20% !important;} .laydate-time-list > li {width: 100% !important;} .laydate-time-list > li:nth-last-child(2), .laydate-time-list > li:last-child {display: none;}");

                                    // 将<style>元素添加到指定的元素中
                                    $('#layui-laydate' + index).append(styleElement);
                                },
                                done: function (value, date, endDate) {
                                    reqArriDate.config.min = {
                                        year: date.year,
                                        month: date.month - 1,//关键
                                        date: date.date,
                                        hours: date.hours,
                                        minutes: date.minutes,
                                        seconds: date.seconds
                                    };
                                    $("#estimatedArrivalTime").val(value);
                                    //单独校验日期
                                    $("#form-dispatch-add").validate().element($("#estimatedArrivalTime"));
                                }
                            });

                            //预计到达时间
                            laydate.render({
                                elem: '#estimatedArrivalTime',
                                type: 'datetime',
                                trigger: 'click'
                            });
                        });


                        profit = lotOutInv.params.profit
                    }

                    getReferencePrice()
                }

                $.modal.closeLoading();
                $.modal.enable();

            }
        })
    }



    /**
     * 校验
     */
    $("#form-enquiry-add").validate({
        onkeyup: false,
        rules: {
        },
        focusCleanup: true,
    });

    /**
     * 根据金额比率计算分摊成本
     */
    function getCostShare() {
        //结算金额
        var costAmount = $("#costAmount").val().replace(/\s+/g, "");
        if (costAmount == "") {
            costAmount = 0;
        }

        var costShareList = $("[id^=costShare_]");
        costShareList.each(function (index, element) {
            //金额比率
            var rate = $("#rate_" + index).val();

            //计算分摊成本并回填
            $("#costShare_" + index).val(decimal(parseFloat(parseFloat(costAmount) * parseFloat(rate)), 2));

            $("#form-dispatch-add").validate().element($("#costShare_" + index))
        });

    }

    function changeOversizeType() {
        $("#oversizeTypeDiv").text("")
        var isOversize = $("#isOversize").is(':checked');
        if(isOversize){
            let goodsLength = $("#goodsLength").val();
            let goodsWidth = $("#goodsWidth").val();
            let goodsHeight = $("#goodsHeight").val();

            if (goodsLength !== '' || goodsWidth !== '' || goodsHeight !== '') {
                $("#oversizeTypeDiv").text("超体积");
            }else {
                $("#oversizeTypeDiv").text("超重");
            }
        }
    }



    var lotOutId = $('#lotOutId').val();
    var outType = $('#outType').val();

    function submitHandler(lotOutInvId) {
        if ($.validate.form()) {
            let lotOutDtlId = $("#lotOutDtlId").val();

            var isOversize = $("#isOversize").is(':checked');
            if(isOversize){
                let goodsLength = $("#goodsLength").val();
                let goodsWidth = $("#goodsWidth").val();
                let goodsHeight = $("#goodsHeight").val();

                if ((goodsLength === undefined || goodsLength === null || goodsLength === '')
                    && (goodsWidth === undefined || goodsWidth === null || goodsWidth === '')
                    && (goodsHeight === undefined || goodsHeight === null || goodsHeight === '')) {
                    $.modal.msgError("大件三超，请填写长、宽、高中的一项。");
                    return;
                }
            }


            //结算金额   校验金额是否相对
            let costAmount = decimal($("#costAmount").val(), 2);

            let transFeeCountTotal = parseValue($("#feeYfyk").val()) + parseValue($("#feeHfyk").val())
                + parseValue($("#feeHfxj").val()) + parseValue($("#feeDfxj").val())

            if (costAmount != decimal(transFeeCountTotal, 2)) {
                $.modal.msgError("付款金额合计与结算金额不相等！");
                return;
            }

            let pricingMethod = $("#pricingMethod").val();
            let unitPrice = $("#unitPrice").val();
            if ((pricingMethod == "0" || pricingMethod == "1" || pricingMethod == "2") && (unitPrice == null || unitPrice == "")) {
                $.modal.msgError("计价方式为”按吨“、”按方“、”按件“时，单价必填。");
                return;

            }

            //数据集
            var data = {};

            var ctDiv = $("#ctDiv");
            ctDiv.find("[name]").each(function () {
                var name = $(this).attr("name");
                var value = $(this).val();
                data[name] = value;
            });
            data["lotOutId"] = $("#lotOutId").val();


            let t = '确定提交吗？'
            if (costAmount == 0) {
                t = '结算金额为"0"确认提交吗？'
            }

            $.modal.confirm(t, function () {
                $.ajax({
                    url: ctx + "enquiry/enquiry/addEntrust",
                    type: "post",
                    dataType: "json",
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(data),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                        $.modal.disable();
                    },
                    success: function(result) {
                        if (result.code == 0) {
                            $.modal.msgSuccess(result.msg);
                            changeLotOutInv(lotOutInvId)

                            $(`#inv_${lotOutInvId}`).text("已分配承运商")
                            changeCarrierStyle(lotOutDtlId);
                        }else {
                            $.modal.alertError(result.msg);
                        }
                        $.modal.closeLoading();
                        $.modal.enable();
                    }
                });
            })

        }
    }

    function changeCarrierStyle(lotOutDtlId) {
        var trElement = $("tr[data-lot-out-dtl-id='" + lotOutDtlId + "']");
        if (trElement.length > 0) {
            trElement.removeAttr("onclick");
            let tdElement = trElement.find("td:eq(1)");
            tdElement.removeClass("wfp");
            tdElement.find("span").addClass("yfp");
            tdElement.find("span:last").after(`<span><span class="label label-primaryT ">已分配</span></span>`)
        }



    }

    function findQuotationByLotOutDtlId(lotOutDtlId) {
        return quotationList.find(function(quotation) {
            return quotation.lotOutDtlId === lotOutDtlId;
        });
    }

    function closeSingle(lotOutInvId) {
        $.modal.confirm("确认要关闭该运段吗?关闭后可在调度配载页面重新操作。", function () {
            $.ajax({
                url: ctx + "enquiry/enquiry/close_single",
                type: "post",
                data: {
                    "lotOutInvId": lotOutInvId
                },
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                    $.modal.disable();
                },
                success: function (result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);

                        var divElement = $("div[data-lot-out-inv-id='" + lotOutInvId + "']");
                        divElement.remove();

                        if ($('.tabs-menu-item').length === 0) {
                            $.operate.successTabCallback({code:0,msg:'关闭成功'});
                        } else {
                            // 模拟点击tabs-menu下的第一个div
                            $('.tabs-menu-item:first').click();
                        }

                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                    $.modal.enable();
                }
            });
        });
    }

    var oilCardRate = 0
    function selectTr(lotOutDtlId) {
        if (lotOutDtlId == "" || lotOutDtlId == null || lotOutDtlId == undefined) {
            lotOutDtlId = $("#carrierId option:selected").data("lotoutdtlid");
        }
        let quotation = findQuotationByLotOutDtlId(lotOutDtlId);

        oilCardRate = parseFloat(quotation.oilCardRate).toFixed(4)

        let quotedPrice = parseFloat(quotation.quotedPrice).toFixed(2)

        $("#unitPrice").val("");
        $("#costAmount").val(quotedPrice);
        $("#carrierId").val(quotation.carrierId);
        $("#lotOutDtlId").val(lotOutDtlId);

        $("#carLenId").val(quotation.carLenId);
        $("#carTypeId").val(quotation.carTypeId);

        calculateFee()

        loadCarrBankData();

        getReferencePrice();

        calculateNegGrossProfit()
    }

    function calculateNegGrossProfit() {
        let lotOutDtlId = $("#carrierId option:selected").data("lotoutdtlid");
        if (lotOutDtlId === '') {
            return
        }
        let quotation = findQuotationByLotOutDtlId(lotOutDtlId);

        let billingType = quotation.billingType

        let billing=0;
        if(billingType==4){
            billing = 1+0.09;
        }else if(billingType==3){
            billing = 1+0.06;
        }else if(billingType==5){
            billing = 1+0.03;
        }else if(billingType==7){
            billing = 1+0.13;
        }else{
            billing= 1;
        }


        //预付油卡
        let feeYfyk = $("#feeYfyk").val();
        feeYfyk = feeYfyk ? parseFloat(feeYfyk) : 0;
        //回付油卡
        let feeHfyk = $("#feeHfyk").val();
        feeHfyk = feeHfyk ? parseFloat(feeHfyk) : 0;
        //回付现金
        let feeHfxj = $("#feeHfxj").val();
        feeHfxj = feeHfxj ? parseFloat(feeHfxj) : 0;
        //到付现金
        let feeDfxj = $("#feeDfxj").val();
        feeDfxj = feeDfxj ? parseFloat(feeDfxj) : 0;

        let calculateTotal=(feeYfyk + feeHfyk).toFixed(4);
        let cashSum=(feeHfxj + feeDfxj).toFixed(4);

        let msrp2= ( (calculateTotal/oilTax)+(cashSum/billing)).toFixed(4); // 不含税金额


        if (profit && msrp2 > profit) {
            let negativeGrossProfit = Math.abs((profit - msrp2).toFixed(2));

            let html = ''
            if (negativeGrossProfit >= 0 && negativeGrossProfit < 20) {
                html = '[0-20]'
            }else if (negativeGrossProfit >= 20 && negativeGrossProfit < 50) {
                html = '[20-50]'
            }else if (negativeGrossProfit >= 50 && negativeGrossProfit < 100) {
                html = '[50-100]'
            }else if (negativeGrossProfit >= 100 && negativeGrossProfit < 200) {
                html = '[100-200]'
            }else if (negativeGrossProfit >= 200 && negativeGrossProfit < 400) {
                html = '[200-400]'
            }else if (negativeGrossProfit >= 400 && negativeGrossProfit < 600) {
                html = '[400-600]'
            }else if (negativeGrossProfit >= 600) {
                html = '[600及以上]'
            }

            $("#negativeGrossProfit").html(html);
            $("#negativeGrossProfitDiv").show()
        }else {
            $("#negativeGrossProfitDiv").hide()
        }


    }


    function calculateFee() {
        let costAmount = $("#costAmount").val();

        //回付现金
        let feeHfxj = costAmount;
        //回付油卡
        let feeHfyk = 0;

        //计算油卡金额
        if(!isNaN(costAmount) && !isNaN(oilCardRate)){
            if(costAmount==0){
                feeHfyk = 0;
                feeHfxj = 0
            }else{
                feeHfyk = parseFloat(costAmount * oilCardRate / 100).toFixed(2)
                feeHfxj = parseFloat(costAmount - feeHfyk).toFixed(2)
            }
        }

        $("#feeYfyk").val("");
        $("#feeDfxj").val("");
        $("#feeHfxj").val(feeHfxj);
        $("#feeHfyk").val(feeHfyk);

        calculateNegGrossProfit()
    }

    function changeIsCustomsClearance() {
        var isCustomsClearance = $("#isCustomsClearance").is(':checked');
        $('input[name="isCustomsClearance"]').val(isCustomsClearance ? '1' : '0');
    }


    function changeIsOversize() {
        var isChecked = $("#isOversize").is(':checked');
        $('input[name="isOversize"]').val(isChecked ? '1' : '0');

        if (isChecked) {
            $("#goodsSizeDiv").show()
        }else {
            $("#goodsSizeDiv").hide()

        }

    }
    function changeIsBigCartLoad() {
        var isBigCartLoad = $("#isBigCartLoad").is(':checked');
        $('input[name="isBigCartLoad"]').val(isBigCartLoad ? '1' : '0');
    }

    function calculateTransFeeCount() {
        let costAmount = $("#costAmount").val();
        costAmount = costAmount ? parseFloat(costAmount) : 0;
        //预付油卡
        let feeYfyk = $("#feeYfyk").val();
        feeYfyk = feeYfyk ? parseFloat(feeYfyk) : 0;
        //回付油卡
        let feeHfyk = $("#feeHfyk").val();
        feeHfyk = feeHfyk ? parseFloat(feeHfyk) : 0;
        //到付现金
        let feeDfxj = $("#feeDfxj").val();
        feeDfxj = feeDfxj ? parseFloat(feeDfxj) : 0;

        let number = costAmount - feeYfyk - feeHfyk - feeDfxj;

        number = Math.round(number * 100) / 100;

        $("#feeHfxj").val(number);

        calculateNegGrossProfit()
    }

    function changeUnitPrice() {
        let pricingMethod = $("#pricingMethod").val() ? parseFloat($("#pricingMethod").val()) : 0;

        let unitPrice = $("#unitPrice").val() ? parseFloat($("#unitPrice").val()) : 0;

        //数量
        let count = 0

        if (pricingMethod == '0') {
            //按吨
            count = weightCount ? parseFloat(weightCount).toFixed(2) : 0;
        }else if (pricingMethod == '1') {
            //按方
            count = weightCount ? parseFloat(numCount).toFixed(2) : 0;
        }else if (pricingMethod == '2') {
            //按件
            count = weightCount ? parseFloat(volumeCount).toFixed(2) : 0;
        }else{
            count = 1;
        }

        $("#costAmount").val(parseFloat(unitPrice * count).toFixed(2))

        calculateFee()
    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num, v) {
        var vv = Math.pow(10, v);
        return Math.round(num * vv) / vv;
    }

    /**
     * 查询承运商下的收款账户
     */
     function loadCarrBankData(){
        let carrierId = $("#carrierId").val();

        let options = '<option value="">请选择</option>';
        $("#carrBankId").html(options);
        if('' == carrierId)return ;
        $.ajax({
            url: ctx + "tms/segment/queryCarrierBankList/"+carrierId,
            type: 'post',
            dataType: 'json',
            data: {},
            success: function(res) {
                let dataList = res.data;
                if(null != dataList){
                    for(let idx in dataList){
                        $("#carrBankId").append('<option value="'+dataList[idx].carrBankId+'">'+dataList[idx].bankAccount+"-"+dataList[idx].bankCard+'</option>');
                    }
                }
            }
        })
    }

    /**
     * 新增收款人账号
     */
     function addPayeeEvent(){
        let carrId = $("#carrierId").val();
        let url = ctx + "g7/payee/add?dt=1";

        if(carrId != ''){
            url += "&collectionType=0&carrId="+carrId;
        }
        
        layer.open({
            type: 2,
            area: ['70%', '100%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "添加收款人",
            content: url,
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function(index, layero) {
                layero.find('iframe')[0].contentWindow.submitHandler_1(addCallBack,index);
            },
            cancel: function(index) {
                return true;
            }
        })
    }
    function addCallBack(pageNo){
        //重新加载收款账号
        loadCarrBankData();
        setTimeout(function(){
            layer.close(pageNo);
        },500)

    }

    function getReferencePrice() {
        let segmentId = $("#segmentId").val();
        if (segmentId == '') {
            return
        }

        $.ajax({
            url: ctx + "tms/segment/getReferencePrice?segmentId=" + segmentId,
            type: "GET",
            dataType: "json",
            contentType: "application/json; charset=utf-8",
            // data: JSON.stringify(param),
            success: function (result) {
                $("#referencePriceSpan").text("暂无")

                if (result.code == 0) {
                    let data = result.data;
                    if (data && data.referencePrice) {
                        $("#referencePriceSpan").text(data.referencePrice.toLocaleString('zh', {
                            style: 'currency',
                            currency: 'CNY'
                        }))
                        // $("#referencePrice").val(result.data)
                    }
                }
            }
        });
    }

    function parseValue(value) {
        return isNaN(parseFloat(value)) ? 0 : parseFloat(value);
    }


    /**
     * 回复现金随着其他应付值改变
     */
     function calPayFee(){
        /*var costAmount = $("#costAmount").val();
        var transFeeCount5 = $("#feeHfyk").val();
        if(new Number(transFeeCount5)>new Number(costAmount)){
            $.modal.alertWarning("回付金额不能超过结算金额！");
            return;
        }else{
            $("#feeHfxj").val(costAmount-transFeeCount5);
        }*/
    }

    function calculateTotal() {
         //console.log('...')
    }
</script>
</body>

</html>