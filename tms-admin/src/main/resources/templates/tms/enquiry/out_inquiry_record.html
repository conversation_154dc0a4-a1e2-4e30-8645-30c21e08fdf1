<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('外发询价')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: select2-css" />
</head>
<style>
    .lh26{
        line-height: 26px;
    }
    .fw{
        font-weight: 600;
    }
    .f16{
        font-size: 16px;
    }
    .tr{
        text-align: right;
    }
    .radio-inline{
        padding-top: 0 !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-out-inquiry-record" class="form-horizontal" novalidate="novalidate">
            <div class="panel-body">
                <input name="id" type="hidden" th:value="${id}">
                <div class="row no-gutter">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-xs-3 tr"><span style="color: red">*</span>运满满定金：</label>
                            <div class="col-xs-9">
                                <div class="input-group">
                                    <input name="ymmDeposit" id="ymmDeposit" placeholder="请输入运满满定金" class="form-control" type="text" maxlength="50" autocomplete="off"
                                    oninput="onlyNumberTwoDecimal(this)" max="1000" min="20">
                                    <span class="input-group-addon">元</span>
                                </div>
                                <label class="error" for="ymmDeposit"></label>
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-xs-3 lh26 tr" style="line-height: 34px;">联系人：</label>
                            <div class="col-xs-9">
                                <select class="form-control select2-multiple" aria-invalid="false" data-none-selected-text="联系人" onchange="getContactName()" style="width: 100%;"
                                    name="contactName" id="lotOutContactList">
                                    <option th:each="user:${userList}" th:value="${user.userName}" th:text="${user.userName+'/'+user.phonenumber}" th:selected="${user.userId} eq ${userId}"></option>
                                </select>
                                <input name="contactTel"  class="form-control" type="hidden">
                                <input name="contactUserId"  class="form-control" type="hidden"> 
                            </div>
                        </div>
                    </div>
                </div>

            </div>

    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: select2-js" />
<script th:inline="javascript">
    var prefix = ctx + "inquiry";
    var userList=[[${userList}]];
    
    $(function () {
        let userId=[[${userId}]]
        if(userId != null && userId !='' && userId != "1"){
            userList.forEach(item=>{
                if(item.userId==userId){
                    $("input[name='contactTel']").val(item.phonenumber);
                    $("input[name='contactUserId']").val(item.userId); 
                }
            })
        }else{
            $("input[name='contactTel']").val(userList[0].phonenumber);
            $("input[name='contactUserId']").val(userList[0].userId);
        }
    });

    /**
     * 数字 保留两位小数
     */
     function onlyNumberTwoDecimal (obj) {
        //得到第一个字符是否为负号
        var t = obj.value.charAt(0);
        //先把非数字的都替换掉，除了数字和.
        obj.value = obj.value.replace(/[^\d\.]/g, '');
        //必须保证第一个为数字而不是.
        obj.value = obj.value.replace(/^\./g, '');
        //保证只有出现一个.而没有多个.
        obj.value = obj.value.replace(/\.{2,}/g, '.');
        //保证.只出现一次，而不能出现两次以上
        obj.value = obj.value.replace('.', '$#$').replace(/\./g, '').replace(
            '$#$', '.');
        obj.value = obj.value.slice(0, 15);
        // 只能输入两个小数
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d).*$/,'$1$2.$3');
        //如果第一位是负号，则允许添加
        if (t == '-') {
            obj.value = '-' + obj.value;
        }
    }

    /**
     * 校验
     */
    $("#form-out-inquiry-record").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            ymmDeposit:{
                required:true,
            }
        }
    });

    function getContactName(){
        let contactName=$("#lotOutContactList").val();

        userList.forEach(item=>{
            if(item.userName==contactName){
                $("input[name='contactTel']").val(item.phonenumber);
                $("input[name='contactUserId']").val(item.userId);
            }
        })
    }

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-out-inquiry-record").serialize();
            $.operate.saveTab(prefix + "/batchOutGoingYmm", data,function(result){
                if(result.code==0){
                    $.modal.close();
                    parent.$.table.refresh();
                }
            });
        }
    }

</script>
</body>
</html>