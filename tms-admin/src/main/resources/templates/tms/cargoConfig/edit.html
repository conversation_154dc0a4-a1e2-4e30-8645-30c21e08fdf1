<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：修改结算方式')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .col-xs-4{
        line-height: 26px;
        text-align: right;
    }
    .wfkye{
        color: #0071ce;
        border-bottom: 1px sienna #0071ce;
    }
    .cur{
        cursor: pointer;
    }
    .fcff{
        color: #ff1f1f;
    }

    .btn-default,.label-default {
        color: #333;
        background-color: #fff;
        border-color: #ccc;
        margin-top: 1px;
    }
    .label-default{
        border: 1px solid;
    }
    .input{
        border: 1px solid #ccc;
        height: 32px;
        line-height: 26px;
        padding: 2px 4px;
        width:100%;
        display: inline-table;
    }
    .label {
        margin-right: 4px;
        display: inline-table;
    }
    #buttonList>button{
        margin-right: 4px;
    }
    #div_print{
        display: none;
        padding: 0 4px;
        width: 100%;
        box-sizing: border-box;
    }
    .fw{
        font-weight: 600;
    }
    .flex{
        display: flex;
        justify-content: space-between;
    }
    .bacc2{
        background-color: #c2c2c2 !important;
    }
    .tc{
        text-align: center;
    }
    .border{
        border: 1px solid #333;
        display: flex;
        border-bottom: none;
    }
    .border:last-child{
        border-bottom: .5px solid #333;
    }
    .border+.border{
        border-top: none;
    }
    .border>div{
        border: .5px solid #333;
        padding: 2px 4px;
        line-height: 26px;
    }
    .ma0{
        margin: 0;
    }
    .fcff{
        color: #ff1f1f;
    }
    .file-input .btn-default {
        border: 0px;
        color: #808080;
    }
    .file-drop-zone-title{
        font-size: 13px;
    }
    .table-bordered {
        border: 0px solid #EBEBEB;
    }
    .table-striped .table, .table-striped .table, .table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td {
        border-bottom: 0px solid #e7eaec!important;
    }
    .table-bordered td, .table-bordered th {
        border: 0px solid #ddd!important;
    }
    .input-group{
        margin-bottom: 10px;
    }
    .save{
        padding: 0px 0;
        position: fixed;
        bottom: 0px;
        left: 0;
        width: 100%;
        background: #fff;
        z-index: 99;
    }
    .file-footer-buttons{
        border-left: 1px dashed #dadada;
    }
    .file-drop-zone {
        height: 100px !important;
        border: 1px #dadada dashed;
        overflow: auto;
    }
    .kv-upload-progress .progress {
        display: none;
    }
    .btn.btn-file {
        padding: 0px 8px !important;
    }
    .input-group{
        width: 100%;
    }
    .theme-explorer{
        width: 100%;
    }
    .theme-explorer .explorer-caption {
        color: #1a1a1a;
        font-size: 16px;
    }
    .theme-explorer .file-preview .table tr{
        border-bottom: 1px #dadada dashed;
    }
    .file-error-message {
        position: absolute;
        top: 20px;
        width: calc(100% - 40px);
        left: 20px;
        height: 100px;
        background: rgba(242,222,222,0.9);
        text-align: center;
        line-height: 70px;
    }
    .file-error-message button span{
        line-height: 70px;
    }
    .file-error-message li{
        text-align: center;
    }
    .eye .file-drop-zone-title{
        background: url('../../../img/eye.png') no-repeat center 10px;
        background-size: 40px 40px;
        height: 100%;
        line-height: 120px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 140px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }

    .file-drop-zone-title{
        font-size: 13px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate" th:object="${config}">
        <input name="carrierId" id="carrierId" type="hidden" th:field="*{id}"/>
        <!--<input name="carrierId" id="carrName" type="hidden" th:field="*{carrName}"/>-->


        <div class="row" >
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span>车长：</span></label>
                    <div class="col-xs-4">
                        <select name="carLen" id="carLen"  class="form-control valid selectSpan" th:value="${config.carLen}" th:with="type=${@dict.getType('car_len')}" >
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" th:selected="${ dict.dictValue == config.carLen }"></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span>车型：</span></label>
                    <div class="col-xs-4">
                        <select name="carType" id="carType"  class="form-control valid selectSpan" th:value="${config.carLen}" th:with="type=${@dict.getType('car_type')}" >
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" th:selected="${ dict.dictValue == config.carType }"></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span>运输方式：</span></label>
                    <div class="col-xs-4">
                        <select name="transCode" id="transCode"  class="form-control valid selectSpan" th:value="${config.transCode}" th:with="type=${@dict.getType('trans_code')}" >
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" th:selected="${ dict.dictValue == config.transCode }"></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span>调度组：</span></label>
                    <div class="col-xs-4">
                        <input id="transLineName" name="transLineName"  class="form-control valid selectSpan" type="text" th:value="${config.transLineName}" onclick="selectSalesDept()"  readonly>
                        <input  name="transLineId" id="transLineId" th:value="${config.transLineId}" class="form-control" type="hidden">
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<script th:inline="javascript">
    var prefix = ctx + "cargoConfig";
    let files = [[${files}]];

    $(function() {
        onCode();
        initLabel();


        // 表单验证
        $("#form-carrier-edit").validate({
            rules: {
                legalCard: {
                    //isIdentity: true,
                    remote: {
                        url: prefix + "/checkLegalCardUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "carrierId": function() {
                                return $.common.trim($("#carrierId").val());
                            },
                            "legalCard": function () {
                                return $.common.trim($("#legalCard").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
            },
            messages: {
                "legalCard":{
                    remote: "身份证号已经存在"
                },
            },
            focusCleanup: true
        });

        //图片功能初始化
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: null
        };

        $.file.loadEditFiles("fileId", "transportTid",files, picParam);
        $('#fileId').on('filesuccessremove', function(event, key, index) {
            $('#fileId').fileinput('clear');
            $('#transportTid').val('');
        });

    })

    function onCode() {
        let ifHasBill=$("#ifHasBill").val();
        
        if(ifHasBill=="0"){
            $("#billingTypeCode").css('display', 'none')
            $("#freightFeeRateRow").css('display','none')
            $("#freightFeeRate").val(0)
        }else{
            $("#billingTypeCode").css('display', 'block')
            $("#freightFeeRateRow").css('display','block')
        }
    }

    function calculateTotal(){
        let billingType = $("#billingType").val();
        //alert(billingType);
        if("6" == billingType){
            $("#freightFeeRate").val(0)
        }

    }

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            commit()
        }
    }

    function commit() {
        var data = $("#form-carrier-edit").serializeArray();
        $.operate.save(prefix + "/editConfig", data);
    }

    function payDetailOver() {
        var carrName = $("#carrName").val();
        parent.$.modal.openTab("应付款余额", ctx+ "finance/payDetailOver?carrName="+carrName);
    }

    function onRoadTips(data,obj){
        let roadTips=$("#roadTips").val();
        if(roadTips){
            let list=roadTips.split(",");
            list.push(data);
            $("#roadTips").val(list.join(","));
        }else{
            $("#roadTips").val(data);
        }
        $(obj).remove();
        inputLabel();
    }

    function inputLabel() {
        let roadTips=$("#roadTips").val();
        if(roadTips){
            let list=roadTips.split(",");
            let html=``;
            list.forEach((res,i) => {
                html+=`<span class="label label-default">${res}
                        <i class="fa fa-remove" style="font-size: 15px;color: #ef6776;" onclick="onRemove(${i})"></i>
                    </span>`
            });
            $("#inputLabel").html(html);
        }else{
            $("#inputLabel").html(``);
        }
    }

    function onRemove(index) {
        let roadTips=$("#roadTips").val();
        if(roadTips){
            let list=roadTips.split(",");
            $("#buttonList").prepend(`<button type="button" class="btn btn-default btn-sm" onclick="onRoadTips('${list[index]}',this)">${list[index]}</button>`)
            list.splice(index,1);
            $("#roadTips").val(list.join(","));
        }
        inputLabel();
    }

    function initLabel() {
        let roadTips=$("#roadTips").val();
        if(roadTips){
            let list=roadTips.split(",");
            let html=``;
            list.forEach((res,i) => {
                html+=`<span class="label label-default">${res}
                        <i class="fa fa-remove" style="font-size: 15px;color: #ef6776;" onclick="onRemove(${i})"></i>
                    </span>`
                $("button[type='button']").each(function () {
                    if(res == $(this).context.innerHTML){
                        $(this).remove();
                    }
                });
            });
            $("#inputLabel").html(html);
        }

    }


    function addTip(){
        let tip = $("#addTips").val();
        if(tip != ''){
            let data = {tip};
            $.modal.confirm("确定新增线路标签 '"+tip+"' 吗？", function() {
                $.operate.saveModalNoCloseAndRefush(prefix + "/addRoadTip", data);
            });
        }
    }

    function balaTypeChange(select) {
        if ($(select).val() == '1') {
            $('#familiarRow').show()
        } else {
            $('#familiarRow').hide()
        }
    }

    function selectSalesDept() {
        layer.open({
            type: 2,
            area: ['80%', '80%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "选择运营组",
            content: ctx + "system/dept/opsGroupTreeTrans",
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero){
                //获取整行
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                $(`#transLineName`).val(rows[0].deptName);
                $(`#transLineId`).val(rows[0].deptId);
                layer.close(index);
            },
            cancel: function(index) {
                return true;
            }
        });
    }


</script>
</body>

</html>