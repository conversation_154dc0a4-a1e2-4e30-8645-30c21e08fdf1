<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('参数列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
				<form id="config-form">
					<div class="select-list">
						<ul>

							<li>
								客户简称：<input type="text" name="custAbbr"/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
								<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
			
	        <div class="btn-group-sm" id="toolbar" role="group">
		      <!--  <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:config:add">
		            <i class="fa fa-plus"></i> 新增
		        </a>
		        <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:config:edit">
		            <i class="fa fa-edit"></i> 修改
		        </a>
		        <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:config:remove">
		            <i class="fa fa-remove"></i> 删除
		        </a>
		        <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:config:export">
		            <i class="fa fa-download"></i> 导出
		        </a>-->
	        </div>
	        <div class="col-sm-12 select-table table-striped">
	            <table id="bootstrap-table" data-mobile-responsive="true"></table>
	        </div>
	    </div>
	</div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('tms:cargoConfig:edit')}]];


        var prefix = ctx + "cargoConfig";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "参数",
                columns: [{
                    checkbox: true
                },
					{
						field: 'id',
						title: '主键'
					},
                {
                    field: 'custAbbr',
                    title: '客户简称'
                },
                {
                    field: 'carLenName',
                    title: '车长'
                },
                {
                    field: 'carTypeName',
                    title: '车型'
                },
                {
                    field: 'transName',
                    title: '运输方式'
                },
					{
						field: 'transLineName',
						title: '调度组'
					},
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="ifHasBill(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');

                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });


		function ifHasBill(id){
			var url = prefix + "/edit/"+id;
			$.modal.open('修改配置', url, 700, $(window).height() - 50);
		}
    </script>
</body>
</html>