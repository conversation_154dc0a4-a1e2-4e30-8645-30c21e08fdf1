<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('系统消息列表')" />
</head>
<style>
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
       <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <div class="fl" style="height: 30px;line-height: 30px;"> 查询日期：</div>
                            <div class="fr">
                                <input type="text" style="width: 45%; float: left;height: 30px;line-height: 30px;" class="time-input form-control" name="startDate" placeholder="开始日期" readonly>
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;height: 30px;line-height: 30px;">-</span>
                                <input type="text" style="width: 45%; float: left;height: 30px;line-height: 30px;" class="time-input form-control" name="endDate" placeholder="结束日期" readonly>
                            </div>
                        </li>
                        <!-- <li>
                            标题：<input type="text" name="messageTitle"/>
                        </li>

                        <li>
                            消息内容：<input type="text" name="messageContent"/>
                        </li>

                        <li>
                            阅读状态（1：已读取；0：未读取）：<input type="text" name="readStatus"/>
                        </li>

                        <li>
                            消息时间：<input type="text" name="messageDate"/>
                        </li>

                        <li>
                            创建人：<input type="text" name="regUserId"/>
                        </li>

                        <li>
                            创建画面ID：<input type="text" name="regScrId"/>
                        </li>

                        <li>
                            创建时间：<input type="text" name="regDate"/>
                        </li>

                        <li>
                            修改人：<input type="text" name="corUserId"/>
                        </li>

                        <li>
                            修改画面ID：<input type="text" name="corScrId"/>
                        </li>

                        <li>
                            修改时间：<input type="text" name="corDate"/>
                        </li> -->

                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <!--<a class="btn btn-primary" onclick="readAll();">
                标记全部已读
            </a>-->
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var prefix = ctx + "tms/sysMessage";

    $(function() {
        var options = {
            url: prefix + "/selectSysMessageSummary",
            showToggle: false,
            showColumns: false,
            showRefresh:false,
            showSearch:false,
            modalName: "系统消息",
            columns: [
                {
                    field : 'regDate',
                    title : '日期',
                    width : 100
                },
                {
                    field : 'deliCnt',
                    title : '提货操作数',
                },
                {
                    field : 'deliDriver',
                    title : '提货操作司机'
                },
                {
                    field : 'arriCnt',
                    title : '到货操作数',
                },
                {
                    field : 'arriDriver',
                    title : '到货操作司机'
                },
                {
                    field : 'billCnt',
                    title : '回单操作数',
                },
                {
                    field : 'billDriver',
                    title : '回单操作司机'
                }
                ]
        };
        $.table.init(options);
    });

</script>
</body>
</html>