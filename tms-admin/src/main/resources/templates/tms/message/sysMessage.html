<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('系统消息列表')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
       <!-- <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            标题：<input type="text" name="messageTitle"/>
                        </li>

                        <li>
                            消息内容：<input type="text" name="messageContent"/>
                        </li>

                        <li>
                            阅读状态（1：已读取；0：未读取）：<input type="text" name="readStatus"/>
                        </li>

                        <li>
                            消息时间：<input type="text" name="messageDate"/>
                        </li>

                        <li>
                            创建人：<input type="text" name="regUserId"/>
                        </li>

                        <li>
                            创建画面ID：<input type="text" name="regScrId"/>
                        </li>

                        <li>
                            创建时间：<input type="text" name="regDate"/>
                        </li>

                        <li>
                            修改人：<input type="text" name="corUserId"/>
                        </li>

                        <li>
                            修改画面ID：<input type="text" name="corScrId"/>
                        </li>

                        <li>
                            修改时间：<input type="text" name="corDate"/>
                        </li>

                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>-->

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="readAll();">
               <!-- <i class="fa fa-plus"></i>--> 标记全部已读
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var prefix = ctx + "tms/sysMessage";

    $(function() {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            detailUrl: prefix + "/detail/{id}",
            showToggle: false,
            showColumns: false,
            showRefresh:false,
            showSearch:false,
            modalName: "系统消息",
            columns: [
                {
                    field : 'sysMessageReceId',
                    title : '系统消息主键ID',
                    visible: false
                },
                {
                    field : 'messageTitle',
                    title : '标题',
                },
                {
                    field : 'messageContent',
                    title : '消息内容',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.messageContent,30);
                    }
                },
                {
                    field : 'readStatus',
                    title : '阅读状态',
                    formatter: function (value, row, index) {
                        if (row.readStatus == 1) {
                            return '<span class="badge badge-primary">已读</span>';
                        } else {
                            return '<span class="badge badge-danger">未读</span>';
                        }
                    }
                },
                {
                    field : 'messageDate',
                    title : '消息时间',
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="消息详情" onclick="openDetail(\'' + row.sysMessageId + '\',\'' + row.sysMessageReceId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        // actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="readAll(\'' + row.sysMessageId + '\')"><i class="fa fa-newspaper-o"></i> 详情</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    /**
     * 标记已读
     */
    function readAll() {
        $.modal.confirm("确定标记全部已读吗？", function () {
            $.operate.post(prefix + "/readAll");
        });
    }

    /**
     * 打开详情页
     * @param sysMessageId
     */
    function openDetail(sysMessageId, sysMessageReceId) {
        var options = [];
        options.url = prefix + "/detail/" + sysMessageId + "/" + sysMessageReceId;
        options.title = "消息详情";
        options.width = "500";
        options.height = "320";
        options.btn = ['<i class="fa fa-close"></i> 关闭'];
        options.yes = function (index, layero) {
            layer.close(index);
            $.table.refresh();
        };
        $.modal.openOptions(options);
    }

</script>
</body>
</html>