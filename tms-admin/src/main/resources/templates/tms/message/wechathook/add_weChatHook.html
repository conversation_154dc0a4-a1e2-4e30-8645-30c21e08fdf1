<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('仓储')" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .fc80{
        color: #808080;
    }
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 80px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .disin{
        display: inline-block;
    }
    .tc{
        text-align: center;
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .infotitle{
        line-height: 30px;
        font-weight: bold;
    }
    .titlebg{
        background: #eff3f9;
        padding: 5px 10px;

    }
    a.del-alink {
        display: block;
        margin: 120px auto 0;
    }
    .bggray{
        background: #f7f8fa;
    }
    .dropdown-menu {
        overflow: auto;
        height: 120px;
    }
</style>
<body>
<div class="form-content">
    <form id="wechat-hook-add" class="form-horizontal">
        <input name="bindDept" type="hidden" id="treeId"/>
        <h4 class="form-header h4">企业微信群小助手</h4>
        <div class="row">
            <div class="col-sm-6 col-md-offset-2">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><span style="color: red; ">*</span>key：</label>
                    <div class="col-sm-8">
                        <input name="hookKey" placeholder="请输入key..." class="form-control" type="text" maxlength="100" required>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-6 col-md-offset-2">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><span style="color: red; ">*</span>名称：</label>
                    <div class="col-sm-8">
                        <input name="hookName" placeholder="请输入名称..." class="form-control" type="text" maxlength="100" required>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-6 col-md-offset-2">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><span style="color: red; ">*</span>归属部门：</label>
                    <div class="col-sm-8">
                        <div class="input-group">
                            <input name="deptName" onclick="selectDeptTree()" id="treeName" type="text" placeholder="请选择归属部门" class="form-control" required>
                            <span class="input-group-addon"><i class="fa fa-search"></i></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-6 col-md-offset-2">
                <div class="form-group">
                    <label class="col-sm-4 control-label"><span style="color: red; ">*</span>类型：</label>
                    <div class="col-sm-8">
<!--                        <select name="sex" class="form-control m-b" th:with="type=${@dict.getType('sys_user_sex')}">-->
<!--                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>-->
<!--                        </select>-->
                        <select name="deptType" class="form-control m-b">
                            <option value="1">业务</option>
                            <option value="2">调度</option>
                            <option value="3">指导价</option>
                            <option value="4">付款</option>
                            <option value="5">报价</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-6 col-md-offset-2">
                <div class="form-group">
                    <label class="col-sm-4 control-label">启停状态：</label>
                    <div class="col-sm-8">
                        <label class="toggle-switch switch-solid">
                            <input type="checkbox" id="isClose" checked>
                            <span></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <h4 class="form-header h4">其他信息</h4>
        <div class="row">
            <div class="col-sm-12">
                <div class="form-group">
                    <label class="col-xs-2 control-label">备注：</label>
                    <div class="col-xs-10">
                        <textarea name="remark" maxlength="500" class="form-control" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭 </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-typeahead-js"/>

<script th:inline="javascript">

    var prefix = ctx + "wechathook";

    $(function () {

    });

    /*用户管理-新增-选择部门树*/
    function selectDeptTree() {
        var treeId = $("#treeId").val();
        var deptId = $.common.isEmpty(treeId) ? "100" : $("#treeId").val();
        var url = ctx + "system/dept/selectDeptTree/" + deptId;
        var options = {
            title: '选择部门',
            width: "380",
            url: url,
            callBack: doSubmit
        };
        $.modal.openOptions(options);
    }
    function doSubmit(index, layero){
        var tree = layero.find("iframe")[0].contentWindow.$._tree;
        if ($.tree.notAllowParents(tree)) {
            var body = layer.getChildFrame('body', index);
            $("#treeId").val(body.find('#treeId').val());
            $("#treeName").val(body.find('#treeName').val());
            layer.close(index);
        }
    }
    /**
     * 校验
     */
    $("#wechat-hook-add").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            reqDeliDate:{
                required:true,
            },
            reqArriDate:{
                required:true,
            },
            custName:{
                required:true,
            },
            balaName:{
                required:true,
            },
            appDeliMobile:{
                isPhone:true
            }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#wechat-hook-add").serializeArray();
            var isClose = $("input[id='isClose']").is(':checked') == true ? 0 : 1;

            data.push({"name": "isClose", "value": isClose});
            $.operate.saveTab(prefix + "/addWeChatHook", data);
        }
    }
</script>
</body>

</html>