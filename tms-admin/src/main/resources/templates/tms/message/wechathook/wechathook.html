<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('企业微信群小助手')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
</head>
<style>
    .row-margin-top {
        margin-top: 10px;
    }
    .select-table .table td {
        /* 超出部分隐藏 */
        overflow:hidden;
        /* 超出部分显示省略号 */
        text-overflow:ellipsis;
        /*规定段落中的文本不进行换行 */
        white-space:nowrap;
        /* 配合宽度来使用 */
        height:40px;
    }
</style>
<body class="gray-bg">

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="park-form">
                    <div class="row">
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">发货单编号：</label>-->
                                <div class="col-sm-12">
                                    <input type="text" class="form-control" placeholder="请输入名称"  name="hookName">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <select name="deptType" class="form-control m-b">
                                    <option value="0">--- 请选择类型 ---</option>
                                    <option value="1">业务</option>
                                    <option value="2">调度</option>
                                    <option value="3">指导价</option>
                                    <option value="4">付款</option>
                                </select>
                            </div>
                        </div>
<!--                        <div class="col-md-2 col-sm-4">-->
<!--                            <div class="form-group">-->
<!--                                &lt;!&ndash;                            <label class="col-sm-4">车长：</label>&ndash;&gt;-->
<!--                                <div class="col-sm-12">-->
<!--                                    <select name="storageOrLabor" id="storageOrLabor" class="form-control valid" aria-invalid="false"  aria-required="true" required>-->
<!--                                        <option value="">&#45;&#45; 请选择部门 &#45;&#45;</option>-->
<!--                                        <option value="1">仓储费</option>-->
<!--                                        <option value="2">力资费</option>-->
<!--                                    </select>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="col-sm-2">
                            <label class="col-sm-4"></label>
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" shiro:hasPermission="tms:message:wechathook:add">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="tms:message:wechathook:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
<!--                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="tms:park_data:waybill:import">-->
<!--                    <i class="fa fa-upload"></i> 导入-->
<!--                </a>-->
<!--                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:park_data:waybill:export">-->
<!--                    <i class="fa fa-download"></i> 导出-->
<!--                </a>-->
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: ztree-js" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:message:wechathook:edit')}]];
    var removeFlag = [[${@permission.hasPermi('tms:message:wechathook:remove')}]];

    var prefix = ctx + "wechathook";


    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#starttime").removeAttr("lay-key");
        $("#endtime").removeAttr("lay-key");
        laydate.render({
            elem: '#endtime'
            , type: 'date'
        });

        laydate.render({
            elem: '#starttime'
            , type: 'date'
        });
        laydate.render({
            elem: '#payStartTime'
            , type: 'date'
        });

        laydate.render({
            elem: '#payEndTime'
            , type: 'date'
        });
    })

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 185 });

        queryWeChatHookList();
        $.table.hideColumn("wechatHookId");
    });


    //初始化查询条件传参
    queryParams = function(params) {
        var search = {};
        $.each($("#wechat-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }
    function queryWeChatHookList() {
        var options = {
            url: prefix + "/wechatHookPage",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            sortName: "regDate",
            sortOrder: "desc",
            modalName: "企业微信群聊助手",
            fixedColumns: true,
            showFooter:true,
            //fixedNumber:5,
            showExport: true,
            clickToSelect: true,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"企业微信群聊助手表"
            },
            queryParams: queryParams,
            columns: [
                {
                    checkbox:true
                },
                {
                    title: '主键',
                    align: 'left',
                    field : 'wechatHookId'  //主键加载完成就隐藏  多选删除的时候是按照第一列的值是否相同来判断一共有几项的
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        //未做确认功能  如果核销中的仓储费会造成金额不对等  暂时隐藏修改
                        if ([[${@permission.hasPermi('tms:message:wechathook:edit')}]] != "hidden") {
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)"  title="编辑" onclick="$.operate.editTab(\'' + row.wechatHookId + '\')"><i class="fa fa-edit"></i></a> ');
                        }
                        if ([[${@permission.hasPermi('ttms:message:wechathook:remove')}]] != "hidden") {
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" title="删除" onclick="$.operate.remove(\'' + row.wechatHookId + '\')"><i class="fa fa-remove"></i></a> ');
                        }
                        return actions.join('');
                    }
                },
                {
                    title: 'key',
                    align: 'left',
                    field : 'hookKey'
                },
                {
                    title: '名称',
                    align: 'left',
                    field : 'hookName'
                },
                {
                    title: '绑定部门',
                    align: 'left',
                    field : 'deptName'
                },
                {
                    title: '类型',
                    align: 'center',
                    field : 'deptType',
                    formatter: function (value, row, index) {
                        if(value == 1){
                            return "业务";
                        }else if(value == 2){
                            return "调度";
                        }else if (value == 3){
                            return "指导价";
                        }else if (value == 4){
                            return "付款";
                        }else if (value == 5){
                            return "报价";
                        }else{
                            return "-";
                        }

                    }
                },
                {
                    visible: editFlag == 'hidden' ? false : true,
                    title: '状态',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return statusTools(row);
                    }
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field : 'regDate'
                },
                {
                    title: '备注',
                    align: 'left',
                    field : 'remark'
                }
            ]
        };
        $.table.init(options);
    }

    /* 状态显示 0:正常 1:关闭 */
    function statusTools(row) {
        if (row.isClose == 1) {
            return '<i class=\"fa fa-toggle-off text-info fa-2x\" onclick="enable(\'' + row.wechatHookId + '\')"></i> ';
        } else {
            return '<i class=\"fa fa-toggle-on text-info fa-2x\" onclick="disable(\'' + row.wechatHookId + '\')"></i> ';
        }
    }

    /* 停用 */
    function disable(wechatHookId) {
        $.modal.confirm("确认要停用吗？", function() {
            $.operate.post(prefix + "/editWeChatHook", { "wechatHookId": wechatHookId, "isClose": 1 });
        })
    }

    /* 启用 */
    function enable(wechatHookId) {
        $.modal.confirm("确认要启用吗？", function() {
            $.operate.post(prefix + "/editWeChatHook", { "wechatHookId": wechatHookId, "isClose": 0 });
        })
    }
</script>
</body>
</html>