<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('公告')" />
</head>
<body class="gray-bg">
<div th:if="${sysMessage != null}">
    <div class="mail-box-header">
        <h2 th:text="${sysMessage.messageTitle}"></h2>
        <div class="mail-tools tooltip-demo m-t-md">
            <h5>
                <span class="pull-right font-noraml" th:text="${#dates.format(sysMessage.messageDate, 'yyyy年MM月dd日 HH:mm')}"></span>
            </h5>
        </div>
    </div>
    <div class="mail-box">
        <div class="mail-body">
            <div th:utext="${sysMessage.messageContent}"></div>
            <a th:if="${sysMessage.url != null}" href="javascript:jump()" >点击跳转</a>
        </div>

    </div>
</div>
<div th:if="${sysMessage == null}">
    <div class="middle-box text-center animated fadeInDown">
        <h3 class="font-bold">找不到该消息！</h3>
        <div class="error-desc">
            该消息已被删除，请刷新首页！
        </div>
    </div>
</div>
<script th:inline="javascript">
    /*[# th:if="${sysMessage != null}"]*/
    function jump() {
        var url = [[${sysMessage.url}]];
        parent.$.modal.openTab([[${sysMessage.messageTitle}]], url);
    }
    /*[/]*/
</script>
</body>
</html>