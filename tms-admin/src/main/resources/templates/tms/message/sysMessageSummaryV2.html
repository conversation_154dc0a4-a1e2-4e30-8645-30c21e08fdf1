<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('系统消息列表')" />
</head>
<style>
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
       <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <div class="fl" style="height: 30px;line-height: 30px;"> 查询日期：</div>
                            <div class="fr">
                                <input type="text" style="width: 45%; float: left;height: 30px;line-height: 30px;" class="time-input form-control" readonly name="startDate" placeholder="开始日期">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;height: 30px;line-height: 30px;">-</span>
                                <input type="text" style="width: 45%; float: left;height: 30px;line-height: 30px;" class="time-input form-control" name="endDate" readonly placeholder="结束日期">
                            </div>
                        </li>
                        <li>
                            司机：<input type="text" name="driverName"/>
                        </li>

                        <li>
                            <!--发货单号：<input type="text" name="vbillno"/>-->
                            <div class="fl" style="height: 30px;line-height: 30px;"> 入驻日期：</div>
                            <div class="fr">
                                <input type="text" style="width: 45%; float: left;height: 30px;line-height: 30px;" class="time-input form-control" readonly name="startRegisterDate" placeholder="开始日期">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;height: 30px;line-height: 30px;">-</span>
                                <input type="text" style="width: 45%; float: left;height: 30px;line-height: 30px;" class="time-input form-control" name="endRegisterDate" readonly placeholder="结束日期">
                            </div>
                        </li>

                        <!-- <li>
                            阅读状态（1：已读取；0：未读取）：<input type="text" name="readStatus"/>
                        </li>

                        <li>
                            消息时间：<input type="text" name="messageDate"/>
                        </li>

                        <li>
                            创建人：<input type="text" name="regUserId"/>
                        </li>

                        <li>
                            创建画面ID：<input type="text" name="regScrId"/>
                        </li>

                        <li>
                            创建时间：<input type="text" name="regDate"/>
                        </li>

                        <li>
                            修改人：<input type="text" name="corUserId"/>
                        </li>

                        <li>
                            修改画面ID：<input type="text" name="corScrId"/>
                        </li>

                        <li>
                            修改时间：<input type="text" name="corDate"/>
                        </li> -->

                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>

                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <!--<a class="btn btn-primary" onclick="readAll();">
                标记全部已读
            </a>-->
            <a class="btn btn-warning" onclick="$.table.exportExcel()" ><i class="fa fa-download"></i> 导出 </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var prefix = ctx + "tms/sysMessage";

    $(function() {
        var options = {
            url: prefix + "/selectSysMessageSummaryV2",
            exportUrl : prefix + "/exportSummary",
            showToggle: false,
            showColumns: false,
            showRefresh:false,
            showSearch:false,
            showFooter : true,
            height:"auto",
            modalName: "系统消息",
            columns: [
                {
                    field : 'driverName',
                    title : '司机',
                    footerFormatter: function (data) {
                        return '总计';
                    }
                },
                {
                    field : 'phonenumber',
                    title : '手机号码',
                },
                {
                    field : 'driverRegisterDate',
                    title : '入驻日期',
                },
                {
                    field : 'driverType',
                    title : '司机类型',
                    formatter:function(value){
                        if(value == null)return '';

                        if(value == 4){
                            return '自有';
                        }
                        if(value == 3){
                            return '外协';
                        }
                    }
                }/*,
                {
                    field : 'vbillno',
                    title : '发货单号',
                }*/,
                {
                    field : 'deliCnt',
                    title : '提货操作数',
                    footerFormatter: function (value) {
                        var count = 0;
                        for (var i in value) {
                            if (value[i].deliCnt != null) {
                                count += value[i].deliCnt;
                            }
                        }
                        /*if (count < 0) {
                            return '<span style="color:red;">' + count.toFixed(2) + '</span>';
                        } else {
                            return '<span style="color:green;">' + count.toFixed(2) + '</span>';
                        }*/
                        return count
                    }
                },
                {
                    field : 'arriCnt',
                    title : '到货操作数',
                    footerFormatter: function (value) {
                        var count = 0;
                        for (var i in value) {
                            if (value[i].arriCnt != null) {
                                count += value[i].arriCnt;
                            }
                        }
                        /*if (count < 0) {
                            return '<span style="color:red;">' + count.toFixed(2) + '</span>';
                        } else {
                            return '<span style="color:green;">' + count.toFixed(2) + '</span>';
                        }*/
                        return count
                    }
                },
                {
                    field : 'billCnt',
                    title : '回单操作数',
                    footerFormatter: function (value) {
                        var count = 0;
                        for (var i in value) {
                            if (value[i].billCnt != null) {
                                count += value[i].billCnt;
                            }
                        }
                        /*if (count < 0) {
                            return '<span style="color:red;">' + count.toFixed(2) + '</span>';
                        } else {
                            return '<span style="color:green;">' + count.toFixed(2) + '</span>';
                        }*/
                        return count
                    }
                },
                {
                    field : 'certificationCnt',
                    title : '证件操作数',
                    footerFormatter: function (value) {
                        var count = 0;
                        for (var i in value) {
                            if (value[i].certificationCnt != null) {
                                count += value[i].certificationCnt;
                            }
                        }
                        /*if (count < 0) {
                            return '<span style="color:red;">' + count.toFixed(2) + '</span>';
                        } else {
                            return '<span style="color:green;">' + count.toFixed(2) + '</span>';
                        }*/
                        return count
                    }
                }
                ]
        };
        $.table.init(options);
    });

</script>
</body>
</html>