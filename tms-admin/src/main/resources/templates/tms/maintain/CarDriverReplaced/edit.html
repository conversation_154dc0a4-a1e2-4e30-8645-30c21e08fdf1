<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('maintain-车辆更换司机')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>
<style>
    .dropdown-menu-right{
        left: auto !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="id" th:value="${carDriverReplaced.id}">
        <!--联系方式 start-->
        <!--   第 1 -->
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-3"><span >司机姓名：</span></label>
                    <div class="col-xs-9">
                        <div class="w100">
                            <input name="replaceDriver"  id="replaceDriver" class="form-control" type="text" th:value="${carDriverReplaced.replaceDriver}">
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                </ul>
                            </div>
                        </div> 
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-3"><span >司机手机：</span></label>
                    <div class="col-xs-9">
                        <input  name="driverPhoneNumber"  id="driverPhoneNumber"  class="form-control" type="text" th:value="${carDriverReplaced.driverPhoneNumber}"
                                 >
                    </div>
                </div>
            </div>
        
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-3"><span >车辆里程：</span></label>
                    <div class="col-xs-9">
                        <input  name="replaceMiles"  id="replaceMiles"  class="form-control" type="text" th:value="${carDriverReplaced.replaceMiles}"
                                >
                    </div>
                </div>
            </div>
        </div>
        <!-- 第 1 结束 -->
        <!--联系方式end-->
        <!--<div class="panel-group" id="accordionTwo">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">凭证上传</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row no-gutter">
                            <div class="col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group">
                                    <label class="col-sm-2">凭证附件：</label>
                                    <div class="col-sm-10">
                                        <input name="evidence" id="evidence" class="form-control" type="file">
                                        <input type="hidden" id="evidenceId" name="evidenceId" th:value="*{evidenceId}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>-->
    </form>
</div>

<!--<div class="row">-->
<!--    <div class="col-sm-offset-5 col-sm-10">-->
<!--        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保-->
<!--            存-->
<!--        </button>&nbsp;-->
<!--        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭-->
<!--        </button>-->
<!--    </div>-->
<!--</div>-->

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js" />
<script th:inline="javascript">

    var prefix = ctx + "maintain/carDriverReplaced";
    /**
     * 初始化
     */
    $(function () {
        // 表单验证
        $("#form-carrier-add").validate({
            focusCleanup: true
        });
        //展开，折叠
        $('#collapseTwo').collapse('show');
        //图片功能
        //图片
        /*var sysUploadFiles = [[${sysUploadFiles}]];
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        console.log(sysUploadFiles)
        if(sysUploadFiles != null) {
            $.file.loadEditFiles("evidence", "evidenceId", sysUploadFiles, picParam);
        }else {
            $.file.initAddFiles("evidence", "evidenceId", picParam);
        }*/
         /**
         * 关键字提示查询
         */
         $("#replaceDriver").bsSuggest('init', {
            url: ctx + "basic/driver/loadDriverList?driverName=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "driverName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["driverName","cardId"],
            effectiveFieldsAlias: {"driverName":"姓名","cardId":"身份证"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
            fnProcessData: function (res) {
                return { value: res.data }
            },
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
            $("#replaceDriver").val(data.driverName);
            $("#driverPhoneNumber").val(data.phone);
        })
    });

   //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            /*$('#evidence').fileinput('upload');
             jQuery.subscribe("cmt", commit);*/
            commit();
        }
    }

    function commit() {
        var data = $("#form-carrier-add").serializeArray();
        //$.operate.saveTab(prefix + "/editSave", data);
        $.operate.saveTab(prefix + "/editSave", data,function (result){
            let isRefresh = [[${isRefresh}]]
            if (isRefresh) {
                if (result.code == web_status.SUCCESS) {
                    parent.location.reload();
                }
            }
            $.operate.successCallback(result);
        });
    }
</script>
</body>
</html>