<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('maintain-车辆更换司机')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>
<style>
    .dropdown-menu-right{
        left: auto !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="id" id="id">
        <input type="hidden" name="carId"  id="carId" th:value="${carId}" >
        <!-- 联系方式 start -->
        <div class="row">
                <div class="col-md-3 col-sm-6">
                    <div class="form-group">
                        <label class="col-xs-3"><span >司机姓名：</span></label>
                        <div class="col-xs-9">
                            <div class="w100">
                                <input name="replaceDriver"  id="replaceDriver" class="form-control" type="text">
                                <div class="input-group-btn">
                                    <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                        <span class="caret"></span>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                    </ul>
                                </div>
                            </div> 
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="form-group">
                        <label class="col-xs-3"><span >司机手机：</span></label>
                        <div class="col-xs-9">
                            <input name="driverPhoneNumber"  id="driverPhoneNumber" class="form-control" type="text" >
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="form-group">
                        <label class="col-xs-3"><span >车辆里程：</span></label>
                        <div class="col-xs-9">
                            <input name="replaceMiles"  id="replaceMiles" class="form-control" type="text" >
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </form>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js" />
<script th:inline="javascript">

    var prefix = ctx + "maintain/carDriverReplaced";
    /**
     * 初始化
     */
    $(function () {
        // 表单验证
        $("#form-carrier-add").validate({
            focusCleanup: true
        });
        //展开，折叠
        $('#collapseTwo').collapse('show');
        //图片功能
        /*var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("evidence", "evidenceId", picParam);*/

        /**
         * 关键字提示查询
         */
         $("#replaceDriver").bsSuggest('init', {
            url: ctx + "basic/driver/loadDriverList?driverName=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "driverName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["driverName","cardId"],
            effectiveFieldsAlias: {"driverName":"姓名","cardId":"身份证"},
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
            fnProcessData: function (res) {
                return { value: res.data }
            },
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
            $("#replaceDriver").val(data.driverName);
            $("#driverPhoneNumber").val(data.phone);
        })
    });

    

    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            /*$('#evidence').fileinput('upload');
            jQuery.subscribe("cmt", commit);*/
            commit();
        }
    }

    function commit() {
        var data = $("#form-carrier-add").serializeArray();
        $.operate.save(prefix + "/addSave", data);
    }
</script>
</body>
</html>