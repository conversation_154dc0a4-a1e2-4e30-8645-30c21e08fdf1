<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车辆更换司机列表')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }

    .table-striped {
        height: calc(100% - 170px);
    }
    .info{
        margin-bottom: 10px;
        background: #fff;
        padding: 5px 10px;
    }
    .info_title{
        font-weight: bold;
        font-size: 16px;
        line-height: 30px;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        justify-content: space-between;
    }
    .info_img{
        width: 70px;
        height: 70px;
    }
    .info_text{
        line-height: 24px;
    }
    .dgx{
        width: 60px;
        text-align: center;
        height: 24px;
        line-height: 24px;
        background: #f8e5e1;
        color: #ed5565;
        border-radius: 10px;
        margin-left: 5px;
        cursor: pointer;
    }
    .fw{
        font-weight: bold;
    }
    .f16{
        font-size: 16px;
    }
    .tc{
        text-align: center;
    }
    .mt5{
        margin-top: 5px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 info">
            <div class="info_title">基本信息</div>
            <div class="row">
                <div class="col-sm-5">
                    <div class="over">
                        <div class="info_img fl picviewer" th:if="${carVO.filePath3 !=''&&carVO.filePath3 != null}">
                            <img th:src="${carVO.filePath3}" style="width: 100%;height: 100%">
                        </div>
                        <div class="info_img fl" th:unless="${carVO.filePath3 !=''&&carVO.filePath3 != null}">
                            <img th:src="@{/img/zwtp.png}" style="width: 100%;height: 100%">
                        </div>
                        <div class="info_text fl ml10">
                            <div>车牌/品牌：[[${carVO.carno}]]/[[*{carVO.brand}]]</div>
                            <div>驾驶员：[[*{carVO.replaceDriver}]]/[[*{carVO.driverPhoneNumber}]]</div>
                            <div>换人时间/里程：[[*{#dates.format(carVO.replaceDate,'yyyy-MM-dd')}]]/[[*{carVO.replaceMiles}]]万km</div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-5">
                    <div class="over">
                        <div class="fl over">
                            <div class="fl">行驶证：</div>
                            <div class="fl ml10 picviewer"  th:if="${carVO.filePath1 !=''&&carVO.filePath1 != null}">
                                <img  th:src="${carVO.filePath1}" style="width: 40px;height: 40px">
                            </div>
                            <div class="fl ml10" th:unless="${carVO.filePath1 !=''&&carVO.filePath1 != null}">
                                <img th:src="@{/img/zwtp.png}" style="width: 100%;height: 100%">
                            </div>
                            <!--<div class="dgx ml10 fl">待更新</div>-->
                        </div>
                        <div class="fl over ml20">
                            <div class="fl">运营证：</div>
                            <div class="fl ml10 picviewer" th:if="${carVO.filePath2 !=''&&carVO.filePath2 != null}">
                                <img  th:src="${carVO.filePath2}" style="width: 40px;height: 40px">
                            </div>
                            <div class="fl ml10" th:unless="${carVO.filePath2 !=''&&carVO.filePath2 != null}">
                                <img th:src="@{/img/zwtp.png}" style="width: 100%;height: 100%">
                            </div>
                        </div>

                    </div>
                </div>
                <div class="col-sm-2 over">
                    <div class="fr" style="width: 140px">
                        <div class="fw f16 tc">[[*{carVO.carrName}]]</div>
                        <div class="tc mt5">[[*{carVO.carrCode}]]</div>
                        <div class="tc mt5" style="color: #666">所有人</div>
                    </div>
                </div>
            </div>

        </div>
        <!-- 搜索条件区域 -->
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">司机姓名：</label>-->
                            <div class="col-sm-12">
                                <input name="replaceDriver" placeholder="请输入司机姓名" class="form-control valid" type="text" aria-required="true">
                            </div>
                        </div>
                    </div>

                    <!--
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">名称：</label>
                                    <div class="col-sm-8">
                                        <input name="xxx" placeholder="请输入名称" class="form-control valid" type="text" aria-required="true">
                                    </div>
                                </div>
                            </div>
                    -->


                    <!--                    <div class="col-md-3 col-sm-6">-->
                    <!--                        <div class="form-group">-->


                    <!--                        </div>-->
                    <!--                    </div>-->

                    <div class="col-md-3 col-sm-6">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <!-- 按钮区域 -->
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="addEvent()" >
                <i class="fa fa-plus"></i> 绑定司机
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <!-- 列表 -->
        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "maintain/carDriverReplaced";
    let CarDriverReplaced = {};
    let _$this  = CarDriverReplaced;
    
    /* *************************************************************
     **************  初始化部分 **************************************
     **************************************************************/
    /**
     *  初始化列表列
     */
     _$this.initColumn = function(){
        let columns              = [
            /** checkbox 列 */
            {checkbox: true},
            /** 操作列 */
            {
                title: '操作',
                align: 'center',
                width:80,
                formatter: function(value, row, index) {
                    var actions = [];
                    /*if ([[${@permission.hasPermi('tms:car:edit')}]] != "hidden") {
                        actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="修改" onclick="edit(\'' + row.carId + '\')" ><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                    }*/
                    //actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="明细" onclick="detail(\'' + row.carId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                    actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="修改" onclick="editEvent(\'' + row.id + '\')" ><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                    return actions.join('');
                }
            },
            /** 数据列 */
           	{
                title: '司机姓名',
                align: 'left',
                field: 'replaceDriver',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
           	{
                title: '司机手机',
                align: 'left',
                field: 'driverPhoneNumber',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
            {
                title: '车辆里程(万km)',
                align: 'left',
                field: 'replaceMiles',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
            {
                title: '更换时间',
                align: 'left',
                field: 'regDate',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            }

        ];
        
        let options = {};
        options.url                      = prefix + "/list?carId="+[[${carId}]];
        options.createUrl                = prefix + "/add?carId="+[[${carId}]];
        options.detailUrl                = prefix + "/detail";
        options.editUrl                  = prefix + "/editSave";
        options.updateUrl                = prefix + "/edit/{id}";
        options.removeUrl                = prefix + "/removes";
        options.showToggle               = false;
        options.clickToSelect            = true;
        options.showColumns              = true;
        options.modalName                = "车辆更换司机";
        options.uniqueId                 = "id";
        options.fixedColumns             = true;
        options.fixedNumber              = 0;
        options.height                   = 560;
        options.columns                  = columns
        
        $.table.init(options);
    }
        
    /**
     * 初始化画面
     */
    _$this.initPage = function(){
        //初始化列表列
        _$this.initColumn();
    }
        
    //table加载完成  客服按钮置灰  判断该客户是否有银行账户
    //定义一个全局的变量接收
    /*$('#bootstrap-table').on('load-success.bs.table', function (e,data) {
        var num = $('#bootstrap-table').bootstrapTable('getOptions').totalRows;
        if(num > 0) {
            $('#toolbar a:last').addClass("disabled")
        }
    });*/
       
    /* ************************************************************
     ************** 事件处理部分 *********************************
     **************************************************************/
    /**
     * 跳转添加画面
     */
    function addEvent() {
        var url = prefix + "/add?carId="+[[${carId}]];
        $.modal.open('绑定司机',url,'400','240');
    }
    
    /**
     * 跳转编辑画面
     */
     function editEvent(id) {
        var url = prefix + "/edit/"+id;
        $.modal.open('修改绑定司机',url,'400','240');
    }
    /*_$this.editEvent = function (id) {
        var url = prefix + "/edit/"+id;
        $.modal.openTab('车辆更换司机修改',url);
    }*/

    /**
     * 跳转明细画面
     */
    /*_$this.detail = function (id) {
        var url = prefix + "/detail/"+id;
        $.modal.openTab('车辆更换司机明细',url);
    }*/


   $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        
        //初始化列表
        _$this.initPage();
    });

</script>

</body>
</html>