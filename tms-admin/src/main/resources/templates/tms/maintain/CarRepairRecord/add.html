<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('maintain-车辆维修记录')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-carrier-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="id" id="id">
        <input name="carId"  id="carId" type="hidden" th:value="${carId}">
        <!-- 联系方式 start -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseOne">车辆维修记录</a>
                </h4>
            </div>
            <div id="collapseOne" class="panel-collapse collapse in">
                <div class="panel-body">
            	<!--   第 0 -->
                    <!--<div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >车辆ID：</span></label>
                                <div class="col-sm-8">
                                    <input name="carId"  id="carId" class="form-control" type="text"
                                           maxlength="25" required>
                                </div>
                            </div>
                        </div>
                    </div>-->
            	<!--   第 1 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >维修项目：</span></label>
                                <div class="col-sm-8">
                                    <input name="itemName"  id="itemName" class="form-control" type="text" maxlength="500" >
                                    <!--<select name="itemId"  id="itemId" data-none-selected-text="维修项目" class="form-control valid noselect2 selectpicker" aria-invalid="false">
                                        <option value="">&#45;&#45;请选择&#45;&#45;</option>
                                        <option th:each="item : ${itemList}"
                                                th:text="${item.itemName}"
                                                th:value="${item.id}"></option>
                                    </select>-->
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >维修日期：</span></label>
                                <div class="col-sm-8">
                                    <input name="repairDate"  id="repairDate" class="form-control" type="text" readonly >
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >配件名称：</span></label>
                                <div class="col-sm-8">
                                    <input name="materialName"  id="materialName" class="form-control" type="text"
                                           >
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >配件价格：</span></label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input name="materialPrice"  id="materialPrice" class="form-control" type="text" >
                                        <span class="input-group-addon">元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            	<!--   第 5 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >进货源：</span></label>
                                <div class="col-sm-8">
                                    <input name="materialFrom"  id="materialFrom" class="form-control" type="text"
                                           >
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >维修人：</span></label>
                                <div class="col-sm-8">
                                    <input name="repairName"  id="repairName" class="form-control" type="text"
                                           >
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >维修地点：</span></label>
                                <div class="col-sm-8">
                                    <input name="repairPlace"  id="repairPlace" class="form-control" type="text"
                                           >
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >维修价格：</span></label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input name="repairPrice"  id="repairPrice" class="form-control" type="text"  >
                                        <span class="input-group-addon">元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
        <!--联系方式end-->
        <!--凭证上传 start-->
        <!--<div class="panel-group" id="accordionTwo">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">凭证上传</a>
                    </h5>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row no-gutter">
                            <div class="col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group">
                                    <label class="col-sm-2">凭证附件：</label>
                                    <div class="col-sm-10">
                                        <input name="evidence" id="evidence" class="form-control" type="file">
                                        <input type="hidden" id="evidenceId" name="evidenceId">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>-->
        <!--凭证上传 end-->
    </form>
</div>

<!--<div class="row">-->
<!--    <div class="col-sm-offset-5 col-sm-10">-->
<!--        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保-->
<!--            存-->
<!--        </button>&nbsp;-->
<!--        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭-->
<!--        </button>-->
<!--    </div>-->
<!--</div>-->

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "maintain/carRepairRecord";
    /**
     * 初始化
     */
    $(function () {
        // 表单验证
        $("#form-carrier-add").validate({
            focusCleanup: true
        });
        //展开，折叠
        $('#collapseTwo').collapse('show');
        //图片功能
        /*var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("evidence", "evidenceId", picParam);*/

        /**
         * 日期插件
         */
        layui.use('laydate', function(){
            var laydate = layui.laydate;
            var maintainDate = laydate.render({
                elem: '#repairDate', //指定元素
                isInitValue : false,
                trigger: 'click',
                type: 'date'
            });
            /*var nextDate = laydate.render({
                elem: '#nextDate', //指定元素
                isInitValue : false,
                trigger: 'click',
                type: 'datetime'
            });*/
        });
    });

    

    //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            /*$('#evidence').fileinput('upload');
            jQuery.subscribe("cmt", commit);*/
            commit();
        }
    }

    function commit() {
        var data = $("#form-carrier-add").serializeArray();
        $.operate.save(prefix + "/addSave", data);
    }
</script>
</body>
</html>