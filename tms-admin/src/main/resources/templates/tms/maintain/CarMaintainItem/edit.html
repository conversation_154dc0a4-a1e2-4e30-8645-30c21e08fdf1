<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('maintain-车辆维护项目')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 80px;
        line-height: 26px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 26px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="id" th:value="${carMaintainItem.id}">
        <!--联系方式 start-->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseOne">基本信息</a>
                </h4>
            </div>
            <div id="collapseOne" class="panel-collapse collapse in">
                <div class="panel-body">
            	<!--   第 0 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left"><span >项目类型：</span></label>
                                <div class="flex_right">
<!--                                    <input  name="itemType"  id="itemType"  class="form-control" type="text"  th:value="${carMaintainItem.itemType}"-->
<!--                                           maxlength="25"  required >-->
<!--                                    <span th:if="${carMaintainItem.itemType==1}">保养项目</span>-->
<!--                                    <span th:if="${carMaintainItem.itemType==2}">维修项目</span>-->
<!--                                    <span th:if="${carMaintainItem.itemType==3}">换轮胎项目</span>-->
                                    <select name="itemType" id="itemType" placeholder="项目类型" data-none-selected-text="项目类型" class="form-control valid noselect2 selectpicker" aria-invalid="false">
                                        <option value="">--项目类型--</option>
                                        <option value="1" th:selected="${carMaintainItem.itemType== 1}">保养项目</option>
                                        <option value="2" th:selected="${carMaintainItem.itemType== 2}">维修项目</option>
                                        <option value="3" th:selected="${carMaintainItem.itemType== 3}">换轮胎项目</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="flex">
                                <label class="flex_left"><span >项目名称：</span></label>
                                <div class="flex_right">
                                    <input  name="itemName"  id="itemName"  class="form-control" type="text" th:value="${carMaintainItem.itemName}"
                                           maxlength="25"  required >
<!--                                    <span th:value="${carMaintainItem.itemName}"></span>-->
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 第 0 结束 -->
            	<!--   第 2 -->
                    <div class="row">
                        <div class="col-md-12 col-sm-12">
                            <div class="flex">
                                <label class="flex_left"><span >备注：</span></label>
                                <div class="flex_right">
<!--                                    <input  name="itemBak"  id="itemBak"  class="form-control" type="text" th:value="${carMaintainItem.itemBak}"-->
<!--                                           maxlength="25"  required >-->
                                    <textarea name="itemBak" id="itemBak" placeholder="备注" th:text="*{carMaintainItem.itemBak}" maxlength="100" class="form-control valid"
                                              rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 第 2 结束 -->
                </div>
            </div>
        </div>
    </form>
</div>

<!--<div class="row">-->
<!--    <div class="col-sm-offset-5 col-sm-10">-->
<!--        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保-->
<!--            存-->
<!--        </button>&nbsp;-->
<!--        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭-->
<!--        </button>-->
<!--    </div>-->
<!--</div>-->

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "maintain/carMaintainItem";
    /**
     * 初始化
     */
    $(function () {
        // 表单验证
        $("#form-carrier-add").validate({
            focusCleanup: true
        });

    });

   //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            commit();
            
        }
    }

    function commit() {
        var data = $("#form-carrier-add").serializeArray();
        //$.operate.saveTab(prefix + "/editSave", data);
        $.operate.saveTab(prefix + "/editSave", data,function (result){
            let isRefresh = [[${isRefresh}]]
            if (isRefresh) {
                if (result.code == web_status.SUCCESS) {
                    parent.location.reload();
                }
            }
            $.operate.successCallback(result);
        });
    }
</script>
</body>
</html>