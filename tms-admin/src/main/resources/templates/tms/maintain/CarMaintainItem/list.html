<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车辆维护项目列表')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <!-- 搜索条件区域 -->
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">项目名称：</label>
                            <div class="col-sm-8">
                                <input name="itemName" placeholder="请输入项目名称" class="form-control valid" type="text" aria-required="true">
                            </div>
                        </div>
                    </div>

			<!--
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">名称：</label>
                            <div class="col-sm-8">
                                <input name="xxx" placeholder="请输入名称" class="form-control valid" type="text" aria-required="true">
                            </div>
                        </div>
                    </div>
			-->


                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">


                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
	<!-- 按钮区域 -->
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="$.operate.add()" >
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
	<!-- 列表 -->
        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "maintain/carMaintainItem";
    let CarMaintainItem = {};
    let _$this  = CarMaintainItem;
    
    /* *************************************************************
     **************  初始化部分 **************************************
     **************************************************************/
    /**
     *  初始化列表列
     */
     _$this.initColumn = function(){
        let columns              = [
            /** checkbox 列 */
            {checkbox: true},
            /** 操作列 */
            {
                title: '操作',
                align: 'center',
                width:80,
                formatter: function(value, row, index) {
                    var actions = [];
                    /*if ([[${@permission.hasPermi('tms:car:edit')}]] != "hidden") {
                        actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="修改" onclick="edit(\'' + row.carId + '\')" ><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                    }*/
                    actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="修改" onclick="$.operate.edit(\'' + row.id + '\')" ><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                    return actions.join('');
                }
            },
            /** 数据列 */
           	{
                title: '项目类型',
                align: 'left',
                field: 'itemType',
                formatter: function status(value,row) {
                    if(value==1){
                        return '保养项目'
                    }else if(value==2){
                        return '维修项目'
                    }else if(value==3){
                        return '换轮胎项目'
                    }
                }
            },
           	{
                title: '项目名称',
                align: 'left',
                field: 'itemName',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
           	{
                title: '备注',
                align: 'left',
                field: 'itemBak',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
        ];
        
        let options = {};
        options.url                      = prefix + "/list";
        options.createUrl                = prefix + "/add";
        options.detailUrl                = prefix + "/detail";
        options.editUrl                  = prefix + "/editSave";
        options.updateUrl                = prefix + "/edit/{id}";
        options.removeUrl                = prefix + "/removes";
        options.showToggle               = false;
        options.clickToSelect            = true;
        options.showColumns              = true;
        options.modalName                = "车辆维护项目";
        options.uniqueId                 = "id";
        options.fixedColumns             = true;
        options.fixedNumber              = 0;
        options.height                   = 560;
        options.columns                  = columns
        
        $.table.init(options);
    }
        
    /**
     * 初始化画面
     */
    _$this.initPage = function(){
        //初始化列表列
        _$this.initColumn();
    }
        
    //table加载完成  客服按钮置灰  判断该客户是否有银行账户
    //定义一个全局的变量接收
    /*$('#bootstrap-table').on('load-success.bs.table', function (e,data) {
        var num = $('#bootstrap-table').bootstrapTable('getOptions').totalRows;
        if(num > 0) {
            $('#toolbar a:last').addClass("disabled")
        }
    });*/
       
    /* ************************************************************
     ************** 事件处理部分 *********************************
     **************************************************************/
    /**
     * 跳转添加画面
     */
    /*_$this.addEvent = function () {
        var url = prefix + "/add";
        $.modal.openTab('车辆维护项目修改',url);
    }*/
    
    /**
     * 跳转编辑画面
     */
    /*_$this.editEvent = function (id) {
        var url = prefix + "/edit/"+id;
        $.modal.openTab('车辆维护项目修改',url);
    }*/

    /**
     * 跳转明细画面
     */
    /*_$this.detail = function (id) {
        var url = prefix + "/detail/"+id;
        $.modal.openTab('车辆维护项目明细',url);
    }*/


   $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        
        //初始化列表
        _$this.initPage();
    });

</script>

</body>
</html>