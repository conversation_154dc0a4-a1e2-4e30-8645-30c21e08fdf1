<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('maintain-车辆维护项目')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-carrier-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="id" id="id">
        <!-- 联系方式 start -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseOne">车辆维护项目</a>
                </h4>
            </div>
            <div id="collapseOne" class="panel-collapse collapse in">
                <div class="panel-body">
            	<!--   第 0 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
<!--                                <label class="col-sm-4"><span >项目类型：</span></label>-->
                                <div class="col-sm-12">
                                    <select name="itemType" id="itemType" placeholder="项目类型" data-none-selected-text="项目类型" class="form-control valid noselect2 selectpicker" aria-invalid="false">
                                        <option value="">--项目类型--</option>
                                        <option value="1">保养项目</option>
                                        <option value="2">维修项目</option>
                                        <option value="3">换轮胎项目</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-md-4 col-sm-6">
                            <div class="form-group" id="info">
                                <!--                                <label class="col-sm-4"><span >项目名称：</span></label>-->
                                <div class="col-sm-9">
                                    <input name="itemNameList[0]" id="itemNameList_0" placeholder="项目名称" class="form-control" type="text"
                                           maxlength="25" required>
                                </div>
                                <div class="col-sm-3" style="padding: 0;font-size: 22px;">
                                    <a class="collapse-link add" onclick="insertName()">+</a>

                                </div>
                            </div>
                        </div>
                    </div>
            	<!--   第 2 -->
                    <div class="row">
                        <div class="col-md-12 col-sm-12">
                            <div class="form-group">
<!--                                <label class="col-sm-4"><span >备注：</span></label>-->
                                <div class="col-sm-12">
<!--                                    <input name="itemBak"  id="itemBak" class="form-control" type="text"-->
<!--                                           maxlength="25">-->
                                    <textarea name="itemBak" id="itemBak" placeholder="备注" maxlength="100" class="form-control valid"
                                              rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>

    </form>
</div>

<!--<div class="row">-->
<!--    <div class="col-sm-offset-5 col-sm-10">-->
<!--        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保-->
<!--            存-->
<!--        </button>&nbsp;-->
<!--        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭-->
<!--        </button>-->
<!--    </div>-->
<!--</div>-->

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "maintain/carMaintainItem";
    /**
     * 初始化
     */
    $(function () {
        // 表单验证
        $("#form-carrier-add").validate({
            focusCleanup: true
        });

    });
    var index = 0;
    function insertName(){
        index += 1;
        var trTtml = '<div class="col-sm-10" style="margin-top: 5px">'+
            '<input name="itemNameList['+index+']" id="itemNameList_'+index+'" placeholder="项目名称" class="form-control" type="text" maxLength="25" required>'+
        '</div>'
        $("#info").append(trTtml);
    }
    

    //提交表单
    function submitHandler() {
        // if($('#itemNameList_0').val()==''){
        //     $.modal.alertError('请至少填写一个项目名称');
        //     return;
        // }
        if ($.validate.form()) {
            commit();
        }
    }

    function commit() {
        var data = $("#form-carrier-add").serializeArray();
        $.operate.save(prefix + "/addSave", data);
    }
</script>
</body>
</html>