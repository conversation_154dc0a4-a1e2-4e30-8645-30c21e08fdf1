<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车辆基本信息列表')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse,.f7f7{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }

    .table-striped {
        height: calc(100% - 90px);
    }
    .sm{
        background: #fef5f6;
        padding: 5px 0;
    }
    .sm_icon{
        width: 18px;
        height: 18px;
        display: inline-block;
        line-height: 18px;
        text-align: center;
        font-size: 16px;
        color: #ed5565;
    }
    .sm_text{
        display: inline-block;
        margin-left: 10px;
        line-height: 30px;
        /* color: #ed5565; */
    }
    .fcff3{
        color: #ed5565;
    }
    .sm_btn{
        display: inline-block;
        width: 100px;
        height: 30px;
        line-height: 30px;
        margin-left: 20px;
        text-align: center;
        background: #fde6de;
        border: 1px #ff9999 solid;
        color: #ed5565;
        border-radius: 20px;
        cursor: pointer;
    }
    .dgx{
        width: 60px;
        text-align: center;
        height: 24px;
        line-height: 24px;
        background: #f8e5e1;
        color: #ed5565;
        display: inline-block;
        border-radius: 10px;
        margin-left: 5px;
        cursor: pointer;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <!-- 搜索条件区域 -->
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter sm" th:if="${ countExpired>0 || countExpiredDriver>0 || countExpiredCar>0 || countExpiredYearly>0 }">
                    <span class="sm_icon">
                        <i class="fa fa-bell-o"></i>
                    </span>

                    <span th:if="${countExpired} > 0">
                        <span class="sm_text" id="warn_msg">
                            共有
                            <span class="fcff3">[[${countExpired}]]辆 </span>车临近保养周期，请及时更新
                        </span>
                        <a href="javascript:;" onclick="selectExpired()">
                            <input type="hidden" id="expiredFlag" name="expiredFlag"/>
                            快速筛选
                        </a>
                    </span>

                    <span th:if="${countExpiredDriver} > 0">
                        <span class="sm_text" id="warn_msg">
                            共有
                            <span class="fcff3">[[${countExpiredDriver}]]辆 </span>行驶证有效期即将到期,请及时更新
                        </span>
                        <a href="javascript:;" onclick="selectExpiredDriver()">
                            <input type="hidden" id="expiredDriverFlag" name="expiredDriverFlag"/>
                            快速筛选
                        </a>
                    </span>

                    <span th:if="${countExpiredCar} > 0">
                        <span class="sm_text" id="warn_msg">
                            共有
                            <span class="fcff3">[[${countExpiredCar}]]辆 </span>运营证即将到期，请及时更新
                        </span>
                        <a href="javascript:;" onclick="selectExpiredCar()">
                            <input type="hidden" id="expiredCarFlag" name="expiredCarFlag"/>
                            快速筛选
                        </a>
                    </span>

                    <span th:if="${countExpiredYearly} > 0">
                        <span class="sm_text" id="warn_msg">
                            共有
                            <span class="fcff3">[[${countExpiredYearly}]]辆 </span>年检有效期即将到期，请及时更新
                        </span>
                        <a href="javascript:;" onclick="selectExpiredYearly()">
                            <input type="hidden" id="expiredYearlyFlag" name="expiredYearlyFlag"/>
                            快速筛选
                        </a>
                    </span>

                </div>

                <div class="row no-gutter">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <!--<label class="col-sm-4">：</label>-->
                            <div class="col-sm-12">
                                <input name="carno" placeholder="请输入车牌" class="form-control valid" type="text" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <!--<div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="carrName" placeholder="请输入车队" class="form-control valid" type="text" aria-required="true">
                            </div>
                        </div>
                    </div>-->

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-4">到期时间：</label>-->
                            <div class="col-sm-12">
                                <input name="replaceDriver" id="replaceDriver" placeholder="驾驶员" class="form-control valid" type="text" aria-required="true" >
                            </div>
                        </div>
                    </div>


                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">


                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="tableSearch();"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetFast();$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
	<!-- 按钮区域 -->
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="$.operate.addTab()" >
                <i class="fa fa-plus"></i> 新增车辆
            </a>
            <!--<a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="maintain:M_CAR_MAINTAIN_RECORD:remove">
                <i class="fa fa-remove"></i> 删除
            </a>-->
        </div>
	<!-- 列表 -->
        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "maintain/carMaintain";
    let CarMaintainRecord = {};
    let _$this  = CarMaintainRecord;
    
    /* *************************************************************
     **************  初始化部分 **************************************
     **************************************************************/
    /**
     *  初始化列表列
     */
     _$this.initColumn = function(){
        let columns              = [
            /** checkbox 列
            {checkbox: true},*/
            /** 操作列 */
            {
                title: '操作',
                align: 'center',
                width:80,
                formatter: function(value, row, index) {
                    var actions = [];

                    //actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="保养记录" onclick="maintenanceTab(\'' + row.carId + '\')" style="font-size: 13px">保养</a>');
                    //actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="维修记录" onclick="maintenRecord(\'' + row.carId + '\')" style="font-size: 13px">维修</a>');
                    actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="换轮胎记录" onclick="changeTire(\'' + row.carId + '\')" style="font-size: 13px">轮胎</a>');
                    // actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="更换司机" onclick="changeDriver(\'' + row.carId + '\')" style="font-size: 13px">司机</a>');
                    // actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="编辑车辆" onclick="toEditCar(\''+row.carId+'\','+row.checkStatus+')" style="font-size: 13px">编辑车辆</a>');

                    return actions.join('');
                }
            },
            /** 数据列 */
           	{
                title: '车牌',
                align: 'left',
                field: 'carno',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push( '<a class="btn btn-xs" href="javascript:void(0)" title="编辑车辆" onclick="toEditCar(\''+row.carId+'\','+row.checkStatus+')" >'+value+'</a>' );
                    if(row.expiredCnt != null && row.expiredCnt > 0){
                        actions.push("<span class=\"label label-danger\" style=\"padding:2px;\">超</span>")
                    }
                    return actions.join('');
                }
            },
           	{
                title: '驾驶员',
                align: 'left',
                field: 'replaceDriver',
                formatter: function status(value,row) {
                    var actions = [];
                    if(null == value){
                        return '<a class="btn btn-xs" href="javascript:void(0)" title="添加司机" onclick="changeDriver(\'' + row.carId + '\')"><i>暂无</i></a>';
                    }
                    if(null != value){
                        actions.push(value);
                    }
                    if(null != row.driverPhoneNumber){
                        actions.push(row.driverPhoneNumber);
                    }

                    return '<a class="btn btn-xs" href="javascript:void(0)" title="更换司机" onclick="changeDriver(\'' + row.carId + '\')">'+actions.join('/')+'</a>';
                }
            },
            // {
            //     title: '前驾驶员',
            //     align: 'left',
            //     field: 'prevReplaceDriver',
            //     formatter: function status(value,row) {
            //         var actions = [];
            //         if(null != value){
            //             actions.push(value);
            //         }
            //         if(null != row.prevDriverPhoneNumber){
            //             actions.push(row.prevDriverPhoneNumber);
            //         }

            //         return actions.join('/');
            //     }
            // },
            // {
            //     title: '换人时间',
            //     align: 'left',
            //     field: 'replaceDate',
            //     formatter: function status(value,row) {
            //         var actions = [];
            //         actions.push(value);
            //         return actions.join('');
            //     }
            // },
           	// {
            //     title: '换人里程',
            //     align: 'left',
            //     field: 'replaceMiles',
            //     formatter: function status(value,row) {
            //         var actions = [];
            //         actions.push(value);
            //         return actions.join('');
            //     }
            // },
            /*{
                title: '下次保养时间',
                align: 'left',
                field: 'nextDate',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },*/
            {
                title: '保养次数',
                align: 'left',
                field: 'maintainCnt',
                formatter:function(value,row){
                    var actions = [];
                    actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="保养记录" onclick="maintenanceTab(\'' + row.carId + '\')" style="font-size: 13px">'+value+'</a>');
                    return actions.join('');
                }
            },
            {
                title: '维修次数',
                align: 'left',
                field: 'repairCnt',
                formatter:function(value,row){
                    var actions = [];
                    actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="维修记录" onclick="maintenRecord(\'' + row.carId + '\')" style="font-size: 13px">'+value+'</a>');
                    return actions.join('');
                }
            },
            
           	{
                title: '车辆品牌',
                align: 'left',
                field: 'brand',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
           	{
                title: '车辆类型名称',
                align: 'left',
                field: 'carPropName',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
            {
                title: '所有人',
                align: 'left',
                field: 'carrName',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
            {
                title: '行驶证有效期',
                align: 'left',
                field: 'validUntil',
                formatter: function status(value,row) {
                    var actions = [];
                    if(row.validUntilExpired != null && row.validUntilExpired < 0){
                        actions.push("<span style='color:red;'>"+value+"</span>");
                    }else{
                        actions.push(value);
                    }

                    return actions.join('');
                }
            },
            {
                title: '运营证有效期',
                align: 'left',
                field: 'validUntilCarrier',
                formatter: function status(value,row) {
                    var actions = [];
                    if(row.validUntilCarrierExpired != null && row.validUntilCarrierExpired < 0){
                        actions.push("<span style='color:red;'>"+value+"</span>");
                    }else{
                        actions.push(value);
                    }
                    return actions.join('');
                }
            },
            {
                title: '年检有效期',
                align: 'left',
                field: 'yearlyInspectionDate',
                formatter: function status(value,row) {
                    var actions = [];
                    if(row.yearlyInspectionDateExpired != null && row.yearlyInspectionDateExpired < 0){
                        actions.push("<span style='color:red;'>"+value+"</span>");
                    }else{
                        actions.push(value);
                    }
                    return actions.join('');
                }
            },
           	{
                title: '行驶证',
                align: 'left',
                field: 'filePath1',
                formatter: function status(value,row,index) {
                    // var actions = [];
                    // actions.push(value);
                    // return actions.join('');
                    // var html = ""
                    // if(value != null && value != '') {
                    //     html =  $.table.imageView(value) ;//+ '<span class="dgx" onclick="toEditCar(\''+row.carId+'\','+row.checkStatus+')">待更新</span>'
                    // }else {
                    //     html = '-';
                    // }
                    var html = ""
                    if(value != null && value != '') {
                        html = `<div class='picviewer'><img style="height:32px" src="`+value+`"/></div>`
                    }else {
                        html = `<img style="height:32px" src="/img/zwtp.png"/>`;
                    }
                    return html;
                }
            },
           	{
                title: '运营证',
                align: 'left',
                field: 'filePath2',
                formatter: function status(value,row) {
                    // var actions = [];
                    // actions.push(value);
                    // return actions.join('');
                    // var html = ""
                    // if(value != null && value != '') {
                    //     html =  $.table.imageView(value) ;//+ '<span class="dgx" onclick="toEditCar(\''+row.carId+'\','+row.checkStatus+')">待更新</span>'
                    // }else {
                    //     html = '-';
                    // }
                    var html = ""
                    if(value != null && value != '') {
                        html = `<div class='picviewer'><img style="height:32px" src="`+value+`"/></div>`
                    }else {
                        html = `<img style="height:32px" src="/img/zwtp.png"/>`;
                    }
                    return html;
                }
            },
            {
                title: '车辆图片',
                align: 'left',
                field: 'filePath3',
                formatter: function status(value,row) {
                    // var actions = [];
                    // actions.push(value);
                    // return actions.join('');
                    // var html = ""
                    // if(value != null && value != '') {
                    //     html =  $.table.imageView(value)
                    // }else {
                    //     html = '-';
                    // }
                    var html = ""
                    if(value != null && value != '') {
                        html = `<div class='picviewer'><img style="height:32px" src="`+value+`"/></div>`
                    }else {
                        html = `<img style="height:32px" src="/img/zwtp.png"/>`;
                    }
                    return html;
                }
            }
        ];
        
        let options = {};
        options.url                      = prefix + "/list";
        options.createUrl                = ctx  + "basic/car/addCarTeam";
        options.detailUrl                = prefix + "/detail";
        options.editUrl                  = prefix + "/editSave";
        options.updateUrl                = prefix + "/edit/{id}";
        options.removeUrl                = prefix + "/removes";
        options.showToggle               = false;
        options.clickToSelect            = true;
        options.showColumns              = true;
        options.modalName                = "车辆";
        options.uniqueId                 = "id";
        options.fixedColumns             = true;
        options.fixedNumber              = 0;
        options.height                   = 560;
        options.columns                  = columns;
        options.onLoadSuccess = function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }
        
        $.table.init(options);
    }
        
    /**
     * 初始化画面
     */
    _$this.initPage = function(){
        //日期插件
        layui.use('laydate',function(){
            layui.laydate.render({elem:'#expiredDate',isInitValue:false,trigger:'click',type:'date'})

        });
        //初始化列表列
        _$this.initColumn();
    }
        
    //table加载完成  客服按钮置灰  判断该客户是否有银行账户
    //定义一个全局的变量接收
    /*$('#bootstrap-table').on('load-success.bs.table', function (e,data) {
        var num = $('#bootstrap-table').bootstrapTable('getOptions').totalRows;
        if(num > 0) {
            $('#toolbar a:last').addClass("disabled")
        }
    });*/
       
    /* ************************************************************
     ************** 事件处理部分 *********************************
     **************************************************************/
    /**
     * 跳转添加画面
     */
    /*_$this.addEvent = function () {
        var url = prefix + "/add";
        $.modal.openTab('车辆保养记录修改',url);
    }*/
    
    /**
     * 跳转编辑画面
     */
    /*_$this.editEvent = function (id) {
        var url = prefix + "/edit/"+id;
        $.modal.openTab('车辆保养记录修改',url);
    }*/

    /**
     * 跳转明细画面
     */
    /*_$this.detail = function (id) {
        var url = prefix + "/detail/"+id;
        $.modal.openTab('车辆保养记录明细',url);
    }*/


   $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        
        //初始化列表
        _$this.initPage();
    });

   function maintenanceTab(carId){
       $.modal.openTab("车辆保养记录", ctx + "maintain/carMaintainRecord?carId=" + carId);
   }
   function maintenRecord(carId){
       $.modal.openTab("车辆维修记录", ctx + "maintain/carRepairRecord?carId=" + carId);
   }
   function changeTire(carId){
       $.modal.openTab("车辆更换轮胎", ctx + "maintain/carTire?carId=" + carId);
   }
   function changeDriver(carId){
       $.modal.openTab("车辆更换司机", ctx + "maintain/carDriverReplaced?carId=" + carId);
   }
   function tableSearch(){
        $("#expiredFlag").val("");
        $("#expiredDriverFlag").val("");
        $("#expiredCarFlag").val("");
       $.table.search();
   }
   function selectExpired(){
       $("#expiredFlag").val("1");
       $("#expiredDriverFlag").val("");
       $("#expiredCarFlag").val("");
       $("#expiredYearlyFlag").val("");
       $.table.search();
   }
    function selectExpiredDriver(){
        $("#expiredFlag").val("");
        $("#expiredDriverFlag").val("1");
        $("#expiredCarFlag").val("");
        $("#expiredYearlyFlag").val("");
        $.table.search();
    }
    function selectExpiredCar(){
        $("#expiredFlag").val("");
        $("#expiredDriverFlag").val("");
        $("#expiredCarFlag").val("1");
        $("#expiredYearlyFlag").val("");
        $.table.search();
    }

    function selectExpiredYearly(){
        $("#expiredFlag").val("");
        $("#expiredDriverFlag").val("");
        $("#expiredCarFlag").val("");
        $("#expiredYearlyFlag").val("1");
        $.table.search();
    }

    function resetFast(){
        $("#expiredFlag").val("");
        $("#expiredDriverFlag").val("");
        $("#expiredCarFlag").val("");
        $("#expiredYearlyFlag").val("");
    }

   function toEditCar(carId,checkStatus){
       if(checkStatus == 0){
           $.modal.alertWarning("待审核状态车辆无法修改!");
           return false;
       }
       var url = ctx + "basic/car" + "/editCarTeam/"+carId;
       $.modal.openTab( "车辆基本信息修改", url);
   }

</script>

</body>
</html>