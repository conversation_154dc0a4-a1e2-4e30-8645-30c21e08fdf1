<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车辆保养记录列表')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 140px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .cur{
        cursor: pointer;
    }
    .tabr{
        border: 1px dashed;
        padding: 5px 15px;
        border-radius: 5px;
        display: inline-block;
    }
    .poTabr{
        position: absolute;
        right: 15px;
        top: 5px;
    }
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .lH26{
        line-height: 26px;
    }
    .w100{
        width: 100%;
    }
    input:disabled{
        background-color: #fff !important;
        border: 0;
    }
    .table-striped {
        height: calc(100% - 170px);
    }

    .white-bg {
        padding: 0px 15px;
        border-radius: 5px;
    }
    .taTX{
        min-width: 5em;
    }
    .taTX>div:first-child{
        height: 24px;
        line-height: 24px;
    }
    .naTx{
        margin-top: 5px;
        padding: 5px 0;
        border-top: 1px solid #e7eaec;
        border-bottom: 1px solid #e7eaec;
        text-align: center;
    }
    .info{
        margin-bottom: 10px;
        background: #fff;
        padding: 5px 10px;
    }
    .info_title{
        font-weight: bold;
        font-size: 16px;
        line-height: 30px;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        justify-content: space-between;
    }
    .info_img{
        width: 70px;
        height: 70px;
    }
    .info_text{
        line-height: 24px;
    }
    .dgx{
        width: 60px;
        text-align: center;
        height: 24px;
        line-height: 24px;
        background: #f8e5e1;
        color: #ed5565;
        border-radius: 10px;
        margin-left: 5px;
        cursor: pointer;
    }
    .fw{
        font-weight: bold;
    }
    .f16{
        font-size: 16px;
    }
    .tc{
        text-align: center;
    }
    .mt5{
        margin-top: 5px;
    }
    .fixed-table-toolbar{
        display: none;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="white-bg mt10">
        <div class="row">
            <div class="col-sm-12 info">
                <div class="row" style="position:relative;">
                    <div class="col-sm-5">
                        <div class="over">
                            <div class="info_img fl picviewer" th:if="${carVO.filePath3 !=''&&carVO.filePath3 != null}">
                                <img th:src="${carVO.filePath3}" style="width: 100%;height: 100%">
                            </div>
                            <div class="info_img fl" th:unless="${carVO.filePath3 !=''&&carVO.filePath3 != null}">
                                <img th:src="@{/img/zwtp.png}" style="width: 100%;height: 100%">
                            </div>
                            <div class="flex" style="justify-content: flex-start;padding-left: 5px;">
                                <div class="taTX">
                                    <div class="f18 fw" th:text="${carVO.carno}"></div>
                                    <div class="naTx">车牌号</div>
                                </div>
                                <div class="taTX ml20">
                                    <div class="f18 fw" th:text="${carVO.carrName}"></div>
                                    <div class="naTx">所有人</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>
        <div class="form-content">
            <form id="form-car" class="form-horizontal" novalidate="novalidate">
                <input name="carId" type="hidden" th:value="${carId}">

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="flex">
                            <label class="flex_left"><span style="color:red">*</span><span >本次保养时间：</span></label>
                            <div class="flex_right">
                                <input name="maintainDate" class="time-input form-control valid" required="true" type="text" readonly >
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="flex">
                            <label class="flex_left"><span style="color:red">*</span><span >本次保养里程：</span></label>
                            <div class="flex_right">
                                <div class="input-group">
                                    <input name="maintainMiles" class="form-control" type="text valid" required="true" onput="$.numberUtil.onlyNumberTwoDecimal(this);">
                                    <span class="input-group-addon">万公里</span>
                                </div>
                                <label id="maintainMiles-error" class="error" for="maintainMiles"></label>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="flex">
                            <label class="flex_left"><span style="color:red">*</span><span >保养人：</span></label>
                            <div class="flex_right">
                                <div class="input-group">
                                    <input name="maintainer" class="form-control valid" required="true" type="text">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <div class="flex">
                            <label class="flex_left"><span style="color:red">*</span><span >保养地点：</span></label>
                            <div class="flex_right">
                                <input name="maintainPlace" class="form-control valid" required="true" type="text">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="panel-group" id="accordionOne">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#accordion"  href="tabs_panels.html#collapseOne">请勾选本次维修项目:</a>
                                    </h5>
                                </div>
                                <!--调度信息 begin-->
                                <div id="collapseOne" class="panel-collapse collapse in">
                                    <div class="panel-body" style="padding: 0;">
                                        <table class="table table-bordered" id="infoTab" style="margin-bottom: 0;">
                                            <thead>
                                            <tr>
                                                <th style="width: 2%"></th>
                                                <th style="text-align: center;width: 30%;">保养项目</th>
                                                <th style="text-align: center;width: 20%;">保养周期</th>
                                                <th style="text-align: center;width: 15%;">延保设定</th>
                                                <th style="text-align: center;width: 15%;">材料数量</th>
                                                <th style="text-align: center;width: 18%;">进货源</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr th:each="item,status: ${itemList}">
                                                <td>
                                                    <div class="checkbox"> <label> <input type="checkbox" th:id="${item.id}" th:name="|CarMaintainRecordDetailVOList[${status.index}].itemId|" th:value="${item.id}"> </label> </div>
                                                </td>
                                                <td th:onclick="linkCheckBoxClick([[${item.id}]])" >
                                                    <span th:text="${item.itemName}"></span>
                                                    <input th:name="|CarMaintainRecordDetailVOList[${status.index}].itemName|" type="hidden" th:value="${item.itemName}">
                                                </td>
                                                <td>
                                                    <div class="over inputSta">
                                                        <select style="width: calc((100% - 40px)/2)" class="form-control valid noselect2 selectpicker fl" th:name="|CarMaintainRecordDetailVOList[${status.index}].periodMiles|" aria-invalid="false">
                                                            <option value="2">2万公里</option>
                                                            <option value="4">4万公里</option>
                                                            <option value="5">5万公里</option>
                                                            <option value="8">8万公里</option>
                                                            <option value="10">10万公里</option>
                                                            <option value="15">15万公里</option>
                                                            <option value="30">30万公里</option>
                                                            <option value="40">40万公里</option>
                                                        </select>
                                                        <div class="fl " style="width: 40px;line-height: 30px;text-align: center">或</div>
                                                        <select style="width: calc((100% - 40px)/2)" class="form-control valid noselect2 selectpicker fl" th:name="|CarMaintainRecordDetailVOList[${status.index}].periodMonth|" aria-invalid="false">
                                                            <option value="3">3个月</option>
                                                            <option value="6">6个月</option>
                                                            <option value="9">9个月</option>
                                                            <option value="12">12个月</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="inputSta">
                                                        <select class="form-control valid noselect2 selectpicker" th:name="|CarMaintainRecordDetailVOList[${status.index}].extendSet|" aria-invalid="false">
                                                            <option value="0">不提醒</option>
                                                            <option value="3">3个月</option>
                                                            <option value="6">6个月</option>
                                                            <option value="12">12个月</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>
                                                    <input th:name="|CarMaintainRecordDetailVOList[${status.index}].materialNumber|" class="form-control inputSta" type="text">
                                                </td>
                                                <td>
                                                    <input th:name="|CarMaintainRecordDetailVOList[${status.index}].materialFrom|" class="form-control inputSta" type="text">
                                                </td>
                                            </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="white-bg mt10">
        <div class="row">
            <!-- 搜索条件区域 -->
            <div class="col-sm-12 search-collapse">
                <form id="role-form" class="form-horizontal">
                    <div class="row no-gutter">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">保养项目：</label>-->
                                <div class="col-sm-12">
                                    <input name="itemName" placeholder="请输入保养项目" class="form-control valid" type="text" aria-required="true">
                                </div>
                            </div>
                        </div>

                        <!--
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">名称：</label>
                                        <div class="col-sm-8">
                                            <input name="xxx" placeholder="请输入名称" class="form-control valid" type="text" aria-required="true">
                                        </div>
                                    </div>
                                </div>
                        -->
                        <div class="col-md-3 col-sm-6">
                            <label class="col-sm-6"></label>
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('role-form')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('role-form')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>

                </form>
            </div>
            <!-- 列表 -->
            <div class="col-sm-12 select-table table-striped" >
                <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10 white-bg" style="padding: 5px 0;">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "maintain/carMaintainRecord";
    let CarMaintainRecord = {};
    let _$this  = CarMaintainRecord;
    
    /* *************************************************************
     **************  初始化部分 **************************************
     **************************************************************/
    /**
     *  初始化列表列
     */
     _$this.initColumn = function(){
        let columns              = [
            /** checkbox 列 */
            // {checkbox: true},
            /** 操作列 */
            {
                title: '操作',
                align: 'center',
                width:80,
                formatter: function(value, row, index) {
                    var actions = [];
                    actions.push('<a class="btn btn-xs text-danger" href="javascript:void(0)" title="删除记录" onclick="$.operate.remove(\'' + row.id + '\')" style="font-size: 13px">删除</a>');
                    
                    /*if ([[${@permission.hasPermi('tms:car:edit')}]] != "hidden") {
                        actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="修改" onclick="edit(\'' + row.carId + '\')" ><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                    }*/
                    //actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="明细" onclick="detail(\'' + row.carId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                    	// actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="修改" onclick="$.operate.edit(\'' + row.id + '\')" ><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                    return actions.join('');
                }
            },
            /** 数据列 */
           	// {
            //     title: '车辆ID',
            //     align: 'left',
            //     field: 'carId',
            //     formatter: function status(value,row) {
            //         var actions = [];
            //         actions.push(value);
            //         return actions.join('');
            //     }
            // },
           
           	{
                title: '本次保养时间',
                align: 'left',
                field: 'maintainDate',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
           	{
                title: '本次保养里程(万公里)',
                align: 'left',
                field: 'maintainMiles',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
           	{
                title: '保养人',
                align: 'left',
                field: 'maintainer',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
           	{
                title: '保养地点',
                align: 'left',
                field: 'maintainPlace',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
        ];
        
        let options = {};
        options.url                      = prefix + "/list?carId="+[[${carId}]];
        options.createUrl                = prefix + "/add?carId="+[[${carId}]];
        options.detailUrl                = prefix + "/detail";
        options.editUrl                  = prefix + "/editSave";
        options.updateUrl                = prefix + "/edit/{id}";
        options.removeUrl                = prefix + "/remove";
        options.showToggle               = false;
        options.clickToSelect            = true;
        options.showColumns              = true;
        options.modalName                = "车辆保养记录";
        options.uniqueId                 = "id";
        options.fixedColumns             = true;
        options.fixedNumber              = 0;
        options.height                   = 560;
        options.formId                   ='role-form';
        options.columns                  = columns;
        options.detailView= true;
        options.onExpandRow = function (index, row, $detail) {
            InitSubTable(index, row, $detail);
        };
        
        $.table.init(options);
    }
        
    /**
     * 初始化画面
     */
    _$this.initPage = function(){
        //初始化列表列
        _$this.initColumn();
    }

    function InitSubTable(index, row, $detail) {
        var childTable = $detail.html('<div class="table-responsive"><table id="child-table" class="table table-bordered table-striped taCl"></table></div>').find('table');
        $(childTable).bootstrapTable({
            url: ctx + "maintain/carMaintainRecordDetail/list",
            method: 'post',
            sidePagination: "server",
            contentType: "application/x-www-form-urlencoded",
            queryParams:{
                mainId:row.id
            },
            columns: [
            {
                title: '保养项目',
                align: 'left',
                field: 'itemName' 
            },
           	{
                title: '保养周期',
                align: 'left',
                field: 'periodMiles',
                formatter: function status(value,row) {
                    return value+'万公里,'+row.periodMonth+'个月';
                }
            },
            {
                title: '下次保养时间',
                align: 'left',
                field: 'nextDate',
                formatter: function status(value,row) {
                    return value.substring(0,10);
                }
            },
           	{
                title: '延保设定',
                align: 'left',
                field: 'extendSet',
                formatter: function status(value,row) {
                    if(value==0){
                        return '不提醒';
                    }else{
                        return value+'个月';
                    }
                }
            },
           	{
                title: '材料数量',
                align: 'left',
                field: 'materialNumber'
            },
           	{
                title: '进货源',
                align: 'left',
                field: 'materialFrom'
            }
            ]
        });
    }

        
    //table加载完成  客服按钮置灰  判断该客户是否有银行账户
    //定义一个全局的变量接收
    /*$('#bootstrap-table').on('load-success.bs.table', function (e,data) {
        var num = $('#bootstrap-table').bootstrapTable('getOptions').totalRows;
        if(num > 0) {
            $('#toolbar a:last').addClass("disabled")
        }
    });*/
       
    /* ************************************************************
     ************** 事件处理部分 *********************************
     **************************************************************/
    /**
     * 跳转添加画面
     */
    /*_$this.addEvent = function () {
        var url = prefix + "/add";
        $.modal.openTab('车辆保养记录修改',url);
    }*/
    
    /**
     * 跳转编辑画面
     */
    /*_$this.editEvent = function (id) {
        var url = prefix + "/edit/"+id;
        $.modal.openTab('车辆保养记录修改',url);
    }*/

    /**
     * 跳转明细画面
     */
    /*_$this.detail = function (id) {
        var url = prefix + "/detail/"+id;
        $.modal.openTab('车辆保养记录明细',url);
    }*/


   $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        
        //初始化列表
        _$this.initPage();

        $(".inputSta").hide();
        $('.inputSta:input').attr('disabled',true)
        $('.inputSta select').attr('disabled',true)
        $('input:checkbox').click(function () {
            let val=$(this).prop("checked");
            if(val){
                $(this).parents("tr").find(".inputSta").show();
                $(this).parents("tr").find('.inputSta:input').attr('disabled',false)
                $(this).parents("tr").find('.inputSta select').attr('disabled',false)
            }else{
                $(this).parents("tr").find(".inputSta").hide();
                $(this).parents("tr").find('.inputSta:input').attr('disabled',true)
                $(this).parents("tr").find('.inputSta select').attr('disabled',true)
            }
        });
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/addSave", $('#form-car').serializeArray());
        }
    }

    function linkCheckBoxClick(chkId){
        $("#"+chkId).click();
    }

</script>

</body>
</html>