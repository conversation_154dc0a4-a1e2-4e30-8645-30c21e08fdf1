<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('maintain-车辆保养记录')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-carrier-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="id" th:value="${carMaintainRecord.id}">
        <!--联系方式 start-->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">基本信息</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">

            	<!--   第 1 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >保养项目：</span></label>
                                <div class="col-sm-8">
                                    <input  name="itemName"  id="itemName"  class="form-control" type="text" th:value="${carMaintainRecord.itemName}"  >
                                    <!--<select name="itemId"  id="itemId" data-none-selected-text="保养项目" class="form-control valid noselect2 selectpicker" aria-invalid="false">
                                        <option value="">&#45;&#45;请选择&#45;&#45;</option>
                                        <option th:each="item : ${itemList}"
                                                th:text="${item.itemName}"
                                                th:value="${item.id}" th:selected="${ item.id==carMaintainRecord.itemId}"></option>
                                    </select>-->
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >保养周期：</span></label>
                                <div class="col-sm-8">
                                    <input  name="maintainCycle"  id="maintainCycle"  class="form-control" type="text" th:value="${carMaintainRecord.maintainCycle}"
                                            >
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >本次保养时间：</span></label>
                                <div class="col-sm-8">
                                    <input  name="maintainDate"  id="maintainDate"  class="form-control" type="text" th:value="*{#dates.format(carMaintainRecord.maintainDate, 'yyyy-MM-dd HH:mm:ss')}"
                                            readonly >
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >本次保养里程：</span></label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input  name="maintainMiles"  id="maintainMiles"  class="form-control" type="text" th:value="${carMaintainRecord.maintainMiles}"
                                          >
                                        <span class="input-group-addon">万公里</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 第 1 结束 -->
            	<!--   第 5 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >下次保养时间：</span></label>
                                <div class="col-sm-8">
                                    <input  name="nextDate"  id="nextDate"  class="form-control" type="text" th:value="*{#dates.format(carMaintainRecord.nextDate, 'yyyy-MM-dd HH:mm:ss')}"
                                            readonly >
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >延保设定：</span></label>
                                <div class="col-sm-8">
                                    <input  name="delayDays"  id="delayDays"  class="form-control" type="text" th:value="${carMaintainRecord.delayDays}"
                                           >
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >材料数量：</span></label>
                                <div class="col-sm-8">
                                    <input  name="materialNumber"  id="materialNumber"  class="form-control" type="text" th:value="${carMaintainRecord.materialNumber}"
                                             >
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >进货源：</span></label>
                                <div class="col-sm-8">
                                    <input  name="materialFrom"  id="materialFrom"  class="form-control" type="text" th:value="${carMaintainRecord.materialFrom}"
                                            >
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 第 5 结束 -->

                    <!-- 第 8 结束 -->
            	<!--   第 13 -->
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >保养人：</span></label>
                                <div class="col-sm-8">
                                    <input  name="maintainer"  id="maintainer"  class="form-control" type="text" th:value="${carMaintainRecord.maintainer}"
                                          >
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4"><span >保养地点：</span></label>
                                <div class="col-sm-8">
                                    <input  name="maintainPlace"  id="maintainPlace"  class="form-control" type="text" th:value="${carMaintainRecord.maintainPlace}"
                                            >
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 第 13 结束 -->
                </div>
            </div>
        </div>
        <!--联系方式end-->
        <!--<div class="panel-group" id="accordionTwo">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">凭证上传</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row no-gutter">
                            <div class="col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group">
                                    <label class="col-sm-2">凭证附件：</label>
                                    <div class="col-sm-10">
                                        <input name="evidence" id="evidence" class="form-control" type="file">
                                        <input type="hidden" id="evidenceId" name="evidenceId" th:value="*{evidenceId}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>-->
    </form>
</div>

<!--<div class="row">-->
<!--    <div class="col-sm-offset-5 col-sm-10">-->
<!--        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保-->
<!--            存-->
<!--        </button>&nbsp;-->
<!--        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭-->
<!--        </button>-->
<!--    </div>-->
<!--</div>-->

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "maintain/carMaintainRecord";
    /**
     * 初始化
     */
    $(function () {
        // 表单验证
        $("#form-carrier-add").validate({
            focusCleanup: true
        });
        //展开，折叠
        $('#collapseTwo').collapse('show');

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            var maintainDate = laydate.render({
                elem: '#maintainDate', //指定元素
                isInitValue : false,
                trigger: 'click',
                type: 'date'
            });
            var nextDate = laydate.render({
                elem: '#nextDate', //指定元素
                isInitValue : false,
                trigger: 'click',
                type: 'date'
            });
        });
        // $.ajax({
        //     type: "get",
        //     url: ctx + "maintain/carMaintainItem/itemList/1",
        //     success: function (result) {
        //         var list = result.data
        //         for(var i=0;i<list.length;i++){
        //             document.getElementById("itemId").options.add(new Option(list[i].itemName,list[i].id));
        //             if(list[i].id=='bea8f5b6246145999b1db3ab8a378e14'){
        //                 document.getElementById("itemId").options[i].selected = true;
        //             }
        //
        //         }
        //
        //     }
        // })
        //图片功能
        //图片
        /*var sysUploadFiles = [[${sysUploadFiles}]];
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        console.log(sysUploadFiles)
        if(sysUploadFiles != null) {
            $.file.loadEditFiles("evidence", "evidenceId", sysUploadFiles, picParam);
        }else {
            $.file.initAddFiles("evidence", "evidenceId", picParam);
        }*/
    });

   //提交表单
    function submitHandler() {
        if ($.validate.form()) {
            /*$('#evidence').fileinput('upload');
             jQuery.subscribe("cmt", commit);*/
            commit();
            
        }
    }

    function commit() {
        var data = $("#form-carrier-add").serializeArray();
        //$.operate.saveTab(prefix + "/editSave", data);
        $.operate.saveTab(prefix + "/editSave", data,function (result){
            let isRefresh = [[${isRefresh}]]
            if (isRefresh) {
                if (result.code == web_status.SUCCESS) {
                    parent.location.reload();
                }
            }
            $.operate.successCallback(result);
        });
    }
</script>
</body>
</html>