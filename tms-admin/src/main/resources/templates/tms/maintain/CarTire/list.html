<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车辆更换轮胎列表')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 140px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }

    .cur{
        cursor: pointer;
    }
    .tabr{
        border: 1px dashed;
        padding: 5px 15px;
        border-radius: 5px;
        display: inline-block;
    }
    .poTabr{
        position: absolute;
        right: 15px;
        top: 5px;
    }
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .lH26{
        line-height: 26px;
    }
    .w100{
        width: 100%;
    }
    input:disabled{
        background-color: #fff !important;
        border: 0;
    }
    .table-striped {
        height: calc(100% - 170px);
    }

    .white-bg {
        padding: 0px 15px;
        border-radius: 5px;
    }
    .taTX{
        min-width: 5em;
    }
    .taTX>div:first-child{
        height: 24px;
        line-height: 24px;
    }
    .naTx{
        margin-top: 5px;
        padding: 5px 0;
        border-top: 1px solid #e7eaec;
        border-bottom: 1px solid #e7eaec;
        text-align: center;
    }
    .info{
        margin-bottom: 10px;
        background: #fff;
        padding: 5px 10px;
    }
    .info_title{
        font-weight: bold;
        font-size: 16px;
        line-height: 30px;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .ml10{
        margin-left: 10px;
    }
    .ml20{
        margin-left: 20px;
    }
    .flex{
        display: flex;
        justify-content: space-between;
    }
    .info_img{
        width: 70px;
        height: 70px;
    }
    .info_text{
        line-height: 24px;
    }
    .dgx{
        width: 60px;
        text-align: center;
        height: 24px;
        line-height: 24px;
        background: #f8e5e1;
        color: #ed5565;
        border-radius: 10px;
        margin-left: 5px;
        cursor: pointer;
    }
    .fw{
        font-weight: bold;
    }
    .f16{
        font-size: 16px;
    }
    .tc{
        text-align: center;
    }
    .mt5{
        margin-top: 5px;
    }
    .label-success{
        padding: 2px;
        border: 1px solid #1c84c6;
        color: #1c84c6;
        background-color: rgba(28, 132, 198,.2);
    }
    .label-primary{
        padding: 2px;
        border: 1px solid #1ab394;
        color: #1ab394;
        background-color: rgba(26, 179, 148,.2);
    }
    .borR{
        border: 1px solid;
        border-radius: 50%;
        display: inline-block;
        width: 24px;
        height: 24px;
        text-align: center;
        line-height: 24px;
    }
    .fixed-table-toolbar{
        display: none;
    }

    .pofp{
        position: fixed;
        top: 0;
        right: 0;
        width: 40%;
        padding: 1vh;
        background-color: #ffffff;
        z-index: 2;
    }
    .tlDiv {
       
        height: 94vh;
        overflow-y: auto;
        background-color: #ffffff;
        border-radius: 5px;
        border: 1px solid #b1d8f7;
    }
    .leftIcon{
        position: absolute;
        z-index: 2;
        left: -20px;
        top: calc(50% - 12px);
        background-color: #ffffff;
        /* border-top-left-radius: 4px; */
        /* border-bottom-left-radius: 4px; */
        padding: 6px 0 6px 6px ;
        border-left: 1px solid #b1d8f7;
        cursor:pointer;
    }
    .leftIcon::before{
        content:" ";
        width: 43px;
        height: 24px;
        position: absolute;
        left: 1px;
        top: -19px;
        background-color: #ffffff;
        transform: rotateZ(-45deg);
        border-top: 1px solid #b1d8f7;
        border-bottom-right-radius: 50%;
    }
    .leftIcon::after{
        content:" ";
        width: 43px;
        height: 24px;
        position: absolute;
        left: 1px;
        top: 33px;
        background-color: #ffffff;
        transform: rotateZ(45deg);
        border-bottom: 1px solid #b1d8f7;
        border-top-right-radius: 50%;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="white-bg mt10">
        <div class="row">
            <div class="col-sm-12 info">
                <div class="info_title">基本信息</div>
                <div class="row" style="position:relative;">
                    <div class="col-sm-5">
                        <div class="over">
                            <div class="info_img fl picviewer" th:if="${carVO.filePath3 !=''&&carVO.filePath3 != null}">
                                <img th:src="${carVO.filePath3}" style="width: 100%;height: 100%">
                            </div>
                            <div class="info_img fl" th:unless="${carVO.filePath3 !=''&&carVO.filePath3 != null}">
                                <img th:src="@{/img/zwtp.png}" style="width: 100%;height: 100%">
                            </div>
                            <div class="flex" style="justify-content: flex-start;padding-left: 5px;">
                                <div class="taTX">
                                    <div class="f18 fw" th:text="${carVO.carno}"></div>
                                    <div class="naTx">车牌号</div>
                                </div>
                                <div class="taTX ml20">
                                    <div class="f18 fw" th:text="${carVO.carrName}"></div>
                                    <div class="naTx">所有人</div>
                                </div>
                            </div>
                            <!-- <div class="info_text fl ml10">
                                <div>车牌/品牌：[[${carVO.carno}]]/[[*{carVO.brand}]]</div>
                                <div>驾驶员：[[*{carVO.replaceDriver}]]/[[*{carVO.driverPhoneNumber}]]</div>
                                <div>换人时间/里程：[[*{carVO.replaceDate}]]/[[*{carVO.replaceMiles}]]万km</div>
                            </div> -->
                        </div>
                    </div>

                    <!-- <div class="poTabr">
                        <div class="tabr cur">
                            <i class="fa fa-plus text-navy" style="font-size: 12px;"></i>
                            <span class="text-success">上传附件(<span>0</span>)</span>
                        </div>
                    </div> -->
                    <!-- <div class="col-sm-5">
                        <div class="over">
                            <div class="fl over">
                                <div class="fl">行驶证：</div>
                                <div class="fl ml10">
                                    <img  th:src="${carVO.filePath1}" style="width: 40px;height: 40px">
                                </div>
                            </div>
                            <div class="fl over ml20">
                                <div class="fl">运营证：</div>
                                <div class="fl ml10">
                                    <img  th:src="${carVO.filePath2}" style="width: 40px;height: 40px">
                                </div>
                            </div>
    
                        </div>
                    </div>
                    <div class="col-sm-2 over">
                        <div class="fr" style="width: 140px">
                            <div class="fw f16 tc">[[*{carVO.carrName}]]</div>
                            <div class="tc mt5">[[*{carVO.carrCode}]]</div>
                            <div class="tc mt5" style="color: #666">车队信息</div>
                        </div>
                    </div> -->
                </div>
    
            </div>
        </div>

        <form id="form-car" class="form-horizontal" novalidate="novalidate">
            <input name="carId" type="hidden" th:value="${carId}">

            <div class="row">
                <div class="col-md-3 col-sm-6">
                    <div class="flex">
                        <label class="flex_left"><span style="color:red">*</span><span >更换日期：</span></label>
                        <div class="flex_right">
                            <input name="replaceDate" class="time-input form-control valid" required="true" type="text" readonly>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 col-sm-6">
                    <div class="flex">
                        <label class="flex_left"><span style="color:red">*</span><span >维修人：</span></label>
                        <div class="flex_right">
                            <input name="repairName" class="form-control valid" required="true" type="text">
                        </div>
                    </div>
                </div>

                <div class="col-md-3 col-sm-6">
                    <div class="flex">
                        <label class="flex_left"><span style="color:red">*</span><span >维修价格：</span></label>
                        <div class="flex_right">
                            <div class="input-group">
                                <input name="repairPrice" class="form-control valid" required="true" type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this)">
                                <span class="input-group-addon">元</span>
                            </div>
                            <label id="repairPrice-error" class="error" for="repairPrice"></label>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 col-sm-6">
                    <div class="flex">
                        <label class="flex_left"><span style="color:red">*</span><span >维修地点：</span></label>
                        <div class="flex_right">
                            <input name="repairPlace" class="form-control valid" required="true" type="text">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="panel-group" id="accordionOne">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h5 class="panel-title">
                                    <a data-toggle="collapse" data-parent="#accordion"  href="tabs_panels.html#collapseOne">请勾选本次更换项目:</a>
                                </h5>
                            </div>
                            <!--调度信息 begin-->
                            <div id="collapseOne" class="panel-collapse collapse in">
                                <div class="panel-body" style="padding: 0;">
                                    <table class="table table-bordered" style="margin-bottom: 0;">
                                        <thead>
                                            <tr>
                                                <th style="width: 2%"></th>
                                                <th style="text-align: center;width: 26%;">轮胎项目</th>
                                                <th style="text-align: center;width: 12%;">配件价格</th>
                                                <th style="text-align: center;width: 12%;">更换后轮胎型号</th>
                                                <th style="text-align: center;width: 12%;">更换后胎号</th>
                                                <th style="text-align: center;width: 12%;">是否新旧</th>
                                                <th style="text-align: center;width: 12%;">更换原因</th>
                                                <th style="text-align: center;width: 12%;">进货源</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr th:each="item,status: ${itemList}">
                                                <td>
                                                    <div class="checkbox">
                                                        <label>
                                                            <input type="checkbox" th:name="|carTireDetailVOList[${status.index}].itemId|" th:value="${item.id}" th:id="${item.id}">
                                                        </label>
                                                    </div>
                                                </td>
                                                <td th:onclick="linkCheckBoxClick([[${item.id}]])">
                                                    <span th:text="${item.itemName}"></span>
                                                    <input th:name="|carTireDetailVOList[${status.index}].itemName|" type="hidden" th:value="${item.itemName}">
                                                </td>
                                                <td>
                                                    <div class="input-group inputSta" >
                                                        <input th:name="|carTireDetailVOList[${status.index}].materialPrice|" class="form-control" type="text">
                                                        <span class="input-group-addon">元</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <input th:name="|carTireDetailVOList[${status.index}].tireModel|" class="form-control inputSta" type="text">
                                                </td>
                                                <td>
                                                    <input th:name="|carTireDetailVOList[${status.index}].tireNumber|" class="form-control inputSta"  type="text">
                                                </td>
                                                <td>
                                                    <div class="inputSta" style="width: 100%;">
                                                        <div class="flex" style="justify-content: space-around;">
                                                            <label>
                                                                <input type="radio" th:name="|carTireDetailVOList[${status.index}].isNew|" value="1" checked>新
                                                            </label>
    
                                                            <label>
                                                                <input type="radio" th:name="|carTireDetailVOList[${status.index}].isNew|" value="2">旧
                                                            </label>
                                                        </div>
                                                      </div>
                                                </td>
                                                <td>
                                                    <input th:name="|carTireDetailVOList[${status.index}].reason|" class="form-control inputSta"  type="text">
                                                </td>
                                                <td>
                                                    <input th:name="|carTireDetailVOList[${status.index}].materialFrom|" class="form-control inputSta"  type="text">
                                                </td>
                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="white-bg mt10">
        <div class="row">
      
            <!-- 搜索条件区域 -->
            <div class="col-sm-12 search-collapse">
                <form id="role-form" class="form-horizontal">
                    <div class="row no-gutter">
                        <div class="col-md-2 col-sm-6">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">项目名称：</label>-->
                                <div class="col-sm-12">
                                    <input name="itemName" placeholder="请输入轮胎项目" class="form-control valid" type="text" aria-required="true">
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2 col-sm-6">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input type="text" style="width: 100%; float: left;" class="time-input form-control"  readonly   name="accidentTimeStart" placeholder="请选择更换日期">
                                </div>
                            </div>
                        </div>
    
                        <div class="col-md-3 col-sm-6">
                            <label class="col-sm-6"></label>
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('role-form')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('role-form')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>
    
                </form>
            </div>
            <!-- 按钮区域 -->
            <!-- <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-primary" onclick="$.operate.add()" >
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div> -->
            <!-- 列表 -->
            <div class="col-sm-12 select-table table-striped" >
                <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
            </div>
    
        </div>
    </div>

    <div style="height: 50px;"></div>
</div>

<!-- <div class="pofp selectOpen">
    <div class="leftIcon">
        <i class="glyphicon glyphicon-indent-left" style="font-size: 24px;z-index: 3;" onclick="fadeOutLeft(this)"></i>
    </div>
   
    <div class="tlDiv">
       156
    </div>
</div> -->

<div class="row">
    <div class="col-sm-offset-5 col-sm-10 white-bg" style="padding: 5px 0;">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">

    var prefix = ctx + "maintain/carTire";
    let CarTire = {};
    let _$this  = CarTire;
    
    /* *************************************************************
     **************  初始化部分 **************************************
     **************************************************************/
    /**
     *  初始化列表列
     */
     _$this.initColumn = function(){
        let columns              = [
            /** checkbox 列 */
            // {checkbox: true},
            /** 操作列 */
            {
                title: '操作',
                align: 'center',
                width:80,
                formatter: function(value, row, index) {
                    var actions = [];
                    actions.push('<a class="btn btn-xs text-danger" href="javascript:void(0)" title="删除记录" onclick="$.operate.remove(\'' + row.id + '\')" style="font-size: 13px">删除</a>');
                    
                    /*if ([[${@permission.hasPermi('tms:car:edit')}]] != "hidden") {
                        actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="修改" onclick="edit(\'' + row.carId + '\')" ><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                    }*/
                    //actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="明细" onclick="detail(\'' + row.carId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                    // actions.push('<a class="btn btn-xs" href="javascript:void(0)"   title="修改" onclick="$.operate.edit(\'' + row.id + '\')" ><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                    return actions.join('');
                }
            },
            /** 数据列 */
           
           	{
                title: '更换日期',
                align: 'left',
                field: 'replaceDate',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
            {
                title: '进货源',
                align: 'left',
                field: 'materialFrom',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
            {
                title: '维修价格',
                align: 'left',
                field: 'repairPrice',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
           	{
                title: '维修人',
                align: 'left',
                field: 'repairName',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            },
           	{
                title: '维修地点',
                align: 'left',
                field: 'repairPlace',
                formatter: function status(value,row) {
                    var actions = [];
                    actions.push(value);
                    return actions.join('');
                }
            }
           	
        ];
        
        let options = {};
        options.url                      = prefix + "/list?carId="+[[${carId}]];
        options.createUrl                = prefix + "/add?carId="+[[${carId}]];
        options.detailUrl                = prefix + "/detail";
        options.editUrl                  = prefix + "/editSave";
        options.updateUrl                = prefix + "/edit/{id}";
        options.removeUrl                = prefix + "/remove";
        options.showToggle               = false;
        options.clickToSelect            = true;
        options.showColumns              = true;
        options.modalName                = "车辆更换轮胎";
        options.uniqueId                 = "id";
        options.fixedColumns             = true;
        options.fixedNumber              = 0;
        options.height                   = 560;
        options.showSearch               = false;
        options.showRefresh              = false;
        options.showColumns              = false;
        options.formId                   ='role-form';
        options.columns                  = columns;
        options.detailView= true;
        options.onExpandRow = function (index, row, $detail) {
            InitSubTable(index, row, $detail);
        };
        
        $.table.init(options);
    }
        
    /**
     * 初始化画面
     */
    _$this.initPage = function(){
        //初始化列表列
        _$this.initColumn();
    }

    function InitSubTable(index, row, $detail) {
        var childTable = $detail.html('<div class="table-responsive"><table id="child-table" class="table table-bordered table-striped taCl"></table></div>').find('table');
        $(childTable).bootstrapTable({
            url: ctx + "maintain/carTireDetail/list",
            method: 'post',
            sidePagination: "server",
            contentType: "application/x-www-form-urlencoded",
            queryParams:{
                mainId:row.id
            },
            columns: [
            {
                title: '轮胎项目',
                align: 'left',
                field: 'itemName'
            },
            {
                title: '配件价格（元）',
                align: 'left',
                field: 'materialPrice'
            },
            {
                title: '轮胎型号',
                align: 'left',
                field: 'tireModel'
            },
           	{
                title: '胎号',
                align: 'left',
                field: 'tireNumber'
            },
            {
                title: '是否新旧',
                align: 'left',
                field: 'isNew',
                formatter: function status(value,row) {
                    if(value==1){
                        return "新";
                    }else{
                        return "旧";
                    }
                }
            },
           	{
                title: '更换原因',
                align: 'left',
                field: 'reason'
            }
            ]
        });
    }

    
        
    //table加载完成  客服按钮置灰  判断该客户是否有银行账户
    //定义一个全局的变量接收
    /*$('#bootstrap-table').on('load-success.bs.table', function (e,data) {
        var num = $('#bootstrap-table').bootstrapTable('getOptions').totalRows;
        if(num > 0) {
            $('#toolbar a:last').addClass("disabled")
        }
    });*/
       
    /* ************************************************************
     ************** 事件处理部分 *********************************
     **************************************************************/
    /**
     * 跳转添加画面
     */
    /*_$this.addEvent = function () {
        var url = prefix + "/add";
        $.modal.openTab('车辆更换轮胎修改',url);
    }*/
    
    /**
     * 跳转编辑画面
     */
    /*_$this.editEvent = function (id) {
        var url = prefix + "/edit/"+id;
        $.modal.openTab('车辆更换轮胎修改',url);
    }*/

    /**
     * 跳转明细画面
     */
    /*_$this.detail = function (id) {
        var url = prefix + "/detail/"+id;
        $.modal.openTab('车辆更换轮胎明细',url);
    }*/


   $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');

        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        
        //初始化列表
        _$this.initPage();
        $(".inputSta").hide();
        $('.inputSta:input').attr('disabled',true)
        $('.inputSta input').attr('disabled',true)
        $('.inputSta select').attr('disabled',true)
        $('input:checkbox').click(function () {
            let val=$(this).prop("checked");
            if(val){
                $(this).parents("tr").find(".inputSta").show();
                $(this).parents("tr").find('.inputSta:input').attr('disabled',false)
                $(this).parents("tr").find('.inputSta input').attr('disabled',false)
                $(this).parents("tr").find('.inputSta select').attr('disabled',false)
            }else{
                $(this).parents("tr").find(".inputSta").hide();
                $(this).parents("tr").find('.inputSta:input').attr('disabled',true)
                $(this).parents("tr").find('.inputSta input').attr('disabled',true)
                $(this).parents("tr").find('.inputSta select').attr('disabled',true)
            }
        });
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/addSave", $('#form-car').serializeArray());
        }
    }

    function linkCheckBoxClick(chkId){
        $("#"+chkId).click();
    }

    function fadeOutLeft(t) {
        let right=$(t).parent().parent().css("right");
        if(right=="0px"){
            $(t).parent().parent().css( 'right','calc(-39%)')
            $(t).removeClass("glyphicon-indent-left")
            $(t).addClass("glyphicon-indent-right")
        }else{
            $(t).parent().parent().css( 'right','0px')
            $(t).removeClass("glyphicon-indent-right")
            $(t).addClass("glyphicon-indent-left")
        }
    } 
        
</script>

</body>
</html>