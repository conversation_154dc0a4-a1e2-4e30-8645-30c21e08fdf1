<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户应收列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">客户名称：</label>
                            <div class="col-sm-7">
                                <input name="custName" id="custName" class="form-control" type="text" placeholder="请输入客户名称"
                                       maxlength="30"  aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group"></div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group"></div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>

            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    var prefix = ctx + "custReceiveView";

    $(function () {
        var options = {
            url: prefix + "/list",
            showToggle: false,
            showColumns: true,
            modalName: "客户应收",
            rememberSelected: true,
            uniqueId: "customerId",
            columns: [
                {
                    title: '操作',
                    align: 'left',
                    field: 'customerId',
                    formatter: function (value,row,index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="明细" onclick="detail(\'' + value + '\')"><i  class="fa fa-newspaper-o" style="font-size: 15px;" ></i></a>');
                        return actions.join('');
                    }

                },
                {
                    title: '客户名称',
                    align: 'left',
                    field: 'custName'

                },

                {
                    title: '客户编码',
                    align: 'left',
                    field: 'custCode'
                },
                {
                    title: '计费件数',
                    field: 'numCount'
                },
                {
                    title: '计费重量',
                    align: 'left',
                    field: 'feeWeightCount'
                },
                {
                    title: '计费体积',
                    align: 'left',
                    field: 'volumeCount'
                },

                {
                    title: '总金额',
                    align: 'right',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },

                {
                    title: '已收金额',
                    align: 'right',
                    field: 'gotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未收金额',
                    align: 'right',
                    field: 'ungotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });


</script>
<script>
    /**
     * 跳转应收明细
     * @param customerId 客户Id
     */
    function detail(customerId) {
        var url = ctx + "custReceiveView/receiveView?customerId="+customerId;
        $.modal.openTab("客户应收明细", url);
    }



</script>

</body>
</html>