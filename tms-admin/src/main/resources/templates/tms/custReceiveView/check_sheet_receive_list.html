<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应收明细')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-5">发货单号：</label>
                            <div class="col-sm-7">
                                <input name="invoiceVbillno" id="invoiceVbillno" class="form-control" type="text"
                                       placeholder="请输入发货单号"
                                       maxlength="25" required="" aria-required="true">
                                <input id="hiddenText" type="text" style="display:none" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group"></div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>


        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var balatype = [[${@dict.getType('bala_type')}]];
    var receCheckSheetId = [[${receCheckSheetId}]];
    var prefix = ctx + "custReceiveView/receCheckSheetView/receive";
    $(function () {
        var options = {
            url: prefix + "/list?receCheckSheetId="+receCheckSheetId,
            showToggle: false,
            showColumns: true,
            modalName: "应收明细",
            fixedColumns: true,
            fixedNumber: 1,
            uniqueId: "customerId",
            columns: [
                {
                    title: '应收单号',
                    field: 'vbillno',
                    align: 'left'
                },
                {
                    title: '应收单据状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value) {
                            case 0:
                                return '<span>新建</label>';
                            case 1:
                                return '<span>已确认</label>';
                            case 2:
                                return '<span>已对账</label>';
                            case 3:
                                return '<span>部分核销 </label>';
                            case 4:
                                return '<span>已核销 </label>';
                            case 5:
                                return '<span>关闭 </label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceVbillno'
                },
                {
                    title: '是否符合无车承运人',
                    align: 'left',
                    field: 'isNtocc',
                    formatter: function status(value, row, index) {
                        if (value === 0) {
                            return '否';
                        }
                        return '是';
                    }
                },
                {
                    title: '客户名称',
                    align: 'left',
                    field: 'custName'

                },
                {
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '<span>运费</label>';
                            case 1:
                                return '<span>在途费用</label>';
                            case 2:
                                return '<span>调整费用</label>';
                            default:
                                break;
                        }
                    }

                },
                {
                    title: '计费件数',
                    field: 'numCount'
                },
                {
                    title: '计费重量',
                    align: 'left',
                    field: 'feeWeightCount'
                },
                {
                    title: '计费体积',
                    align: 'left',
                    field: 'volumeCount'
                },
                {
                    title: '总金额',
                    align: 'right',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '已收金额',
                    align: 'right',
                    field: 'gotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未收金额',
                    align: 'right',
                    field: 'ungotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '要求提货日期',
                    field: 'reqDeliDate',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,10);
                    }

                },

                {
                    title: '要求到货日期',
                    field: 'reqArriDate',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,10);
                    }

                },

                {
                    title: '结算客户',
                    align: 'left',
                    field: 'balaName'
                },

                {
                    title: '备注',
                    align: 'left',
                    field: 'memo',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '结算方式',
                    align: 'left',
                    field: 'balatype',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(balatype, value);
                    }
                }
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });


</script>
<script>

    /**
     * 客户的选择框
     */
    function selectClient() {
        $.modal.open("选择客户", ctx + "client/related",'','',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                parent.$.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //客户id
            $("#balaName").val(rows[0]["customerId"]);
            //客户名称
            $("#balaCustomerId").val(rows[0]["custName"]);

            layer.close(index);
        });
    }


    /**
     * 跳转应收修改页面
     * @param id
     */
    function edit(id,receiveStatus) {
        if (receiveStatus != 0) {
            $.modal.alertWarning("只能修改新建状态的应收单");
            return;
        }
        var url = prefix + "/edit?receiveDetailId="+id;
        $.modal.openTab("应收明细修改", url);
    }


</script>

</body>
</html>