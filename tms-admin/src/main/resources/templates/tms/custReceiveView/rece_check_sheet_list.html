<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户对账')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">客户名称：</label>
                            <div class="col-sm-8">
                                <input name="custName" id="custName" placeholder="请输入客户名称" class="form-control valid" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">年份：</label>
                            <div class="col-sm-8">

                                <input name="year" id="year"  placeholder="请输入年份" class="form-control valid" type="text"
                                       aria-required="true" autocomplete="off">

                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">月份：</label>
                            <div class="col-sm-8">

                                <input name="month" id="month" placeholder="请输入月份" class="form-control valid" type="text"
                                       aria-required="true"  autocomplete="off">

                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-6">
                        <label class="col-sm-6"></label>
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

                <div class="row">

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">

                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">

                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">

                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">

                    </div>

                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="verification()" shiro:hasPermission="finance:receCheckSheet:verification">
                <i class="fa fa-gg"></i> 核销
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">

    var prefix = ctx + "custReceiveView/receCheckSheetView";

    var billingType = [[${@dict.getType('billing_type')}]];
    var CloseAccountList = [[${CloseAccountList}]];//关账记录

    $(function () {
        var options = {
            url: prefix + "/list",
            showToggle:false,
            showColumns:false,
            modalName: "客户对账",
            fixedColumns: true,
            fixedNumber:2,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'left',
                    field: 'receCheckSheetId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        // actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="开票申请" onclick="receiptAppl(\''+row.applicationAmount+'\')"><i  class="fa fa-dollar" style="font-size: 15px;" ></i></a>');
                        // actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="往来明细" onclick="receExchangeDetails()"><i  class="fa fa-exchange" style="font-size: 15px;" ></i></a>');
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="应收明细" onclick="receive(\''+row.receCheckSheetId+'\')"><i  class="fa fa fa-list" style="font-size: 15px;" ></i></a>');
                        return actions.join('');
                    }

                },
                {
                    title: '应收对账单号',
                    field: 'vbillno',
                    align: 'left'
                },
                {
                    title: '应收对账单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value,row,index) {

                        switch(value) {
                            case 0:
                                return '<span>新建</label>';
                            case 1:
                                return '<span>已确认</label>';
                            case 2:
                                return '<span>部分核销</label>';
                            case 3:
                                return '<span>已核销</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '是否手动核销',
                    field: 'handVerification',
                    align: 'left',
                    formatter: function status(value,row,index) {

                        switch(value) {
                            case '0':
                                return '<span>非手动核销</label>';
                            case '1':
                                return '<span>手动核销</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '是否符合无车承运人',
                    align: 'left',
                    field: 'isNtocc',
                    formatter: function status(value, row, index) {
                        if (value === 0) {
                            return '否';
                        }
                        return '是';
                    }
                },
                {
                    title: '对账年份',
                    field: 'year',
                    align: 'left'
                },
                {
                    title: '对账月份',
                    field: 'month',
                    align: 'left'
                },
                {
                    title: '客户名称',
                    field: 'custName',
                    align: 'left'
                },
                {
                    title: '结算客户',
                    field: 'balaName',
                    align: 'left'
                },
                {
                    title: '总金额',
                    field: 'totalAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }


                },
                {
                    title: '已收金额',
                    field: 'gotAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '未收金额',
                    field: 'ungotAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '已申请金额',
                    field: 'applicationAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },


                {
                    title: '发票抬头',
                    field: 'checkHead',
                    align: 'left'
                },
                {
                    title: '发票类型',
                    field: 'checkType',
                    align: 'left',
                    formatter: function status(value,row,index) {

                        return $.table.selectDictLabel(billingType, value);
                    }
                },
                {
                    title: '创建人',
                    align: 'left',
                    field: 'regUserId'
                },
                {
                    title: '备注',
                    align: 'left',
                    field: 'memo'
                }
            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });

    layui.use('laydate', function(){
        var laydate = layui.laydate;
        laydate.render({
            elem: '#month',
            type: 'month',
            format:"MM"
        });
        laydate.render({
            elem: '#year',
            type: 'year'
        });
    });
    function detail() {
        var url = prefix + "/detail";
        $.modal.openTab($.table._option.modalName + "详细", url);
    }
    //获取当前年月
    var date = new Date();
    var now = date .getFullYear() + "-" +("0" + (date.getMonth() + 1)).slice(-2);

    //开票申请
    function receiptAppl(){
        var applicationAmount = $.table.selectColumns('applicationAmount').join(); //已申请金额
        var totalAmount = $.table.selectColumns('totalAmount').join();//总金额
        var vbillstatus = $.table.selectColumns('vbillstatus').join();//对账单状态
        if(vbillstatus == 0){
            $.modal.alertError("该账单在新建状态下，不能开票申请");
            return ;
        }
        if(applicationAmount == totalAmount){
            $.modal.alertError("该账单已申请完成");
            return ;
        }
        var id = $.table.selectColumns('receCheckSheetId');//对账单id
        var url = prefix + "/receiptAppl/"+id;
        $.modal.open('开票申请',url);


    }

    //收款记录
    function receRecord() {
        var id = $.table.selectColumns('receCheckSheetId');
        var url = prefix + "/receRecord/"+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "付款记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    //往来明细
    function receExchangeDetails(customerId){
        if(customerId == null){
            var year = $.table.selectColumns('year');
            var month = $.table.selectColumns('month');
            var customerId = $.table.selectColumns('customerId');
        }
        var url = prefix + "/receExchangeDetails?customerId="+customerId+"&year="+year+"&month="+month;
        $.modal.openTab('客户往来明细',url);
    }

    // 跳转对应的应收明细页面
    function receive(receCheckSheetId) {
        var url = ctx + "custReceiveView/receCheckSheetView/receive?receCheckSheetId="+receCheckSheetId;
        $.modal.openTab('应收明细',url);
    }

    // 核销
    function verification() {
        var receCheckSheetIds = $.table.selectColumns('receCheckSheetId');
        var gotAmountList = $.table.selectColumns('gotAmount');
        var totalAmountList = $.table.selectColumns('totalAmount');
        var handVerificationList = $.table.selectColumns('handVerification');
        for (var i = 0; i < gotAmountList.length;i++ ) {
            if (gotAmountList[i] !== totalAmountList[i]) {
                $.modal.alertWarning("请选择已付清的对账单");
                return;
            }
            if (handVerificationList[i] === '0') {
                $.modal.alertWarning("请选择状态为手动核销的对账单");
                return;
            }
        }
        $.operate.post(prefix + "/verification", { "receCheckSheetIds": receCheckSheetIds.join()},location.reload());
    }

    /**
     * 确认应付明细
     */
    function affirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0) {
                $.modal.alertWarning("应收单据只能为新建状态下才能确认");
                return;
            }
        }
        var receCheckSheetIds = $.table.selectColumns("receCheckSheetId");
        if (receCheckSheetIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("是否确认？", function () {
            $.operate.post(prefix + "/affirm", {"receCheckSheetIds": receCheckSheetIds.join()});
        });
    }

    /**
     * 反确认
     */
    function reverse() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("应收对账单状态为已确认才能进行反确认");
                return;
            }
        }
        var receCheckSheetIds = $.table.selectColumns("receCheckSheetId").join();
        $.modal.open("反确认", prefix + "/back_confirm/" + receCheckSheetIds,500,300);
    }
</script>

</body>
</html>