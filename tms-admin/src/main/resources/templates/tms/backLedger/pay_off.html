<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：修改结算方式')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .col-xs-4{
        line-height: 26px;
        text-align: right;
    }

    .fcff{
        color: #ff1f1f;
    }


    #buttonList>button{
        margin-right: 4px;
    }

    .border+.border{
        border-top: none;
    }
    .border>div{
        border: .5px solid #333;
        padding: 2px 4px;
        line-height: 26px;
    }

    .dropdown-menu{
        left:0px !important;
    }
    .custDiv{
        width: 50% !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate" >
        <input type="hidden" name="prepaymentId" th:value="${id}">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>付款日期：</label>
                    <div class="col-xs-6" >
                        <input type="text" class="form-control" id="payDate" name="payDate" autocomplete="off" required>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>付款金额：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input id="payAmount" name="payAmount"  class="form-control" type="text"   th:oninput="|$.numberUtil.onlyNumberNegative(this);|" maxlength="20" autocomplete="off" required>
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>
        </div>


        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">付款账户：</label>
                    <div class="col-xs-6 custDiv" >
                        <!--账户名称-->
                        <input name="accountName" id="accountName" required class="form-control valid" type="text" aria-required="true">
                        <!--账户id-->
                        <input name="outAccount" id="outAccount"  class="form-control valid" type="hidden" aria-required="true">
                        <div class="input-group-btn">
                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>付款方式：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <select name="payMethod" id="payMethod" class="form-control" th:with="type=${@dict.getType('pay_method')}" required>
                            <option value=""></option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                                    th:disabled="${dict.dictValue=='77'}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-2" style="text-align: right">凭证上传：</label>
                    <div class="col-xs-9" style="display:inline-table;">
                        <input name="fileId" id="fileId" class="form-control" type="file" multiple>
                        <input type="hidden" id="tid" name="tid">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-2" style="text-align: right">备注：</label>
                    <div class="col-xs-9" style="display:inline-table;">
                        <textarea id="remark" name="remark"  class="form-control" autocomplete="off" rows="5"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var prefix = ctx + "backLedger";



    $(function() {
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#payDate',
                type: 'datetime',
                trigger: 'click',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours=now.getHours();
                    this.dateTime.minutes=now.getMinutes();
                    this.dateTime.seconds=now.getSeconds();
                }
            });
        });


        //图片功能初始化
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("fileId", "tid", picParam);
        $('#fileId').on('filesuccessremove', function(event, key, index) {
            $('#fileId').fileinput('clear');
            $('#tid').val('');
        });
    });

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            if ($('#fileId').fileinput('getFilesCount') > 0) {
                jQuery.subscribe("cmt", commit);
                $('#fileId').fileinput('upload');
            } else {
                commit()
            }
        }
    }

    function commit(){
        var data = $("#form-carrier-edit").serializeArray();
        $.operate.save(prefix + "/savePayOff", data);
    }

    /**
     * 关键字提示查询 转入账户
     */
    $("#accountName").bsSuggest('init', {
        url: ctx + "finance/account/findAccount?paymentType=1",
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["accountName","account"],
        effectiveFieldsAlias: {"accountName":"账户名称","account":"账号"},
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        console.log(data)
        $("#accountName").val(data.accountName);
        $("#outAccount").val(data.accountId);
    })


</script>
</body>

</html>