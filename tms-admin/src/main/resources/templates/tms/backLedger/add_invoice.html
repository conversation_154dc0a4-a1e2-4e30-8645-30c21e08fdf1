<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：修改结算方式')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .col-xs-4{
        line-height: 26px;
        text-align: right;
    }

    .fcff{
        color: #ff1f1f;
    }


    #buttonList>button{
        margin-right: 4px;
    }

    .border+.border{
        border-top: none;
    }
    .border>div{
        border: .5px solid #333;
        padding: 2px 4px;
        line-height: 26px;
    }

    .dropdown-menu{
        left:0px !important;
    }
    .custDiv{
        width: 50% !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate" >
        <input type="hidden" name="backLedgerId" th:value="${backLedger.id}">
        <input type="hidden" name="id" th:value="${backLedgerBilling.id}">
        <div class="row">
            <!--<div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>开票日期：</label>
                    <div class="col-xs-7" >
                        <input type="text" class="form-control" id="billingDate" name="billingDate" autocomplete="off" required>
                    </div>
                </div>
            </div>-->
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>开票金额：</label>
                    <div class="col-xs-7" style="display:inline-table;">
                        <input id="billingAmount" name="billingAmount"  class="form-control" type="text" min="0"
                               th:value="${backLedgerBilling.billingAmount}"
                               th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="20" autocomplete="off" required>
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>金额类型：</label>
                    <div class="col-xs-7">
                        <select id="moneyType" name="moneyType" class="form-control" autocomplete="off" required>
                            <option value=""></option>
                            <option value="0" th:selected="${backLedgerBilling.moneyType == 0}">运费</option>
                            <option value="1"  th:selected="${backLedgerBilling.moneyType == 1}">装卸费</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>业务发生月：</label>
                    <div class="col-xs-7">
                        <input id="yearMonth" name="yearMonth" th:value="${backLedgerBilling.yearMonth}"
                               class="form-control" type="text"  readonly required>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>发票类型：</label>
                    <div class="col-xs-7">
                        <select id="billingType" name="billingType" class="form-control" th:with="type=${@dict.getType('billing_type')}" required onchange="billTypeChange()">
                            <option value=""></option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}" th:selected="${backLedgerBilling.billingType}==${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff reqtex">*</span>开票公司：</label>
                    <div class="col-xs-7" style="display:inline-table;">
                        <select id="billingCorp" name="billingCorp" class="form-control req" th:with="type=${@dict.getType('bala_corp')}" required>
                            <option value=""></option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}" th:selected="${backLedgerBilling.billingCorp}==${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff reqtex">*</span>发票抬头：</label>
                    <div class="col-xs-7" >
                        <select id="custBillingId" name="custBillingId" class="form-control req" autocomplete="off" required onchange="billChange()" th:value="${backLedgerBilling.custBillingId}">
                            <option value=""></option>
                            <option th:each="dict : ${custBillings}" th:text="${dict.billingPayable}"
                                    th:value="${dict.custBillingId}" th:selected="${backLedgerBilling.custBillingId} == ${dict.custBillingId}"></option>
                        </select>
                        <input type="hidden" name="billingPayable" id="billingPayable" th:value="${backLedgerBilling.billingPayable}">
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff reqtex">*</span>纳税人识别号：</label>
                    <div class="col-xs-7" style="display:inline-table;">
                        <input id="taxIdentify" name="taxIdentify" th:value="${backLedgerBilling.taxIdentify}" class="form-control req" type="text" autocomplete="off" required>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff reqtex">*</span>开户行：</label>
                    <div class="col-xs-7" >
                        <input type="text" class="form-control req" th:value="${backLedgerBilling.bank}" id="bank" name="bank" autocomplete="off" required>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff reqtex">*</span>银行账号：</label>
                    <div class="col-xs-7" style="display:inline-table;">
                        <input id="bankAccount" name="bankAccount" th:value="${backLedgerBilling.bankAccount}" class="form-control req" type="text" autocomplete="off" required>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-2" style="text-align: right">地址及电话：</label>
                    <div class="col-xs-9" style="display:inline-table;">
                        <textarea id="addressPhone" name="addressPhone" th:text="${backLedgerBilling.addressPhone}" class="form-control" autocomplete="off" rows="2"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-2" style="text-align: right">备注：</label>
                    <div class="col-xs-9" style="display:inline-table;">
                        <textarea id="remark" name="remark" th:text="${backLedgerBilling.remark}"  class="form-control" autocomplete="off" rows="5"></textarea>
                    </div>
                </div>
            </div>
        </div>
       <!-- <div class="row">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-2" style="text-align: right">开票附件：</label>
                    <div class="col-xs-9" style="display:inline-table;">
                        <input name="fileId" id="fileId" class="form-control" type="file" multiple>
                        <input type="hidden" id="tid" name="tid">
                    </div>
                </div>
            </div>
        </div>-->

    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    let custBillings = [[${custBillings}]];
    let backLedgerBilling = [[${backLedgerBilling}]];

    $(function() {
        billTypeChange()
    })

    layui.use(function(){
        var laydate = layui.laydate;

        // 年月选择器
        laydate.render({
            elem: '#yearMonth',
            type: 'month'
        });
    });


    const type = [[${type}]];
    //提交
    function submitHandler() {
      /*  if ($.validate.form()) {
            if ($('#fileId').fileinput('getFilesCount') > 0) {
                jQuery.subscribe("cmt", commit);
                $('#fileId').fileinput('upload');
            } else {
                commit()
            }
        }*/
        if ($.validate.form()) {
            if (type === "0") {
                var data = $("#form-carrier-edit").serializeArray();
                $.operate.save(prefix + "/saveInvoice", data);

            } else if (type === "1") {
                var data = $("#form-carrier-edit").serializeArray();
                $.operate.save(prefix + "/editInvoice", data);

            }
        }

    }

    function commit(){

    }

    function billChange(){
        let custBillingId = $("#custBillingId").val();
        for(let i = 0 ; i < custBillings.length; i++){
            let custBilling = custBillings[i];
            if(custBilling.custBillingId === custBillingId){
                $("#billingPayable").val(custBilling.billingPayable);
                $("#billingCorp").val(custBilling.billingCorp);
                $("#billingType").val(custBilling.billingType);
                $("#taxIdentify").val(custBilling.taxIdentify);
                $("#bank").val(custBilling.bank);
                $("#bankAccount").val(custBilling.bankAccount);
                $("#addressPhone").text(custBilling.addressPhone);
            }
        }
    }

    function billTypeChange(){
       if($("#billingType").val() == '6'){
            $(".req").removeAttr("required");
            $(".reqtex").css("display","none");
       }else{
           $(".req").attr("required","true");
           $(".reqtex").css("display","-webkit-inline-box");
       }
    }

</script>
</body>

</html>