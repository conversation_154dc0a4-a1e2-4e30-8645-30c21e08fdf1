<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <meta charset="UTF-8">
    <title></title>
    <style>
        @page {
            /*size: 210mm 297mm;*/
            /*margin: 1.54cm 1.17cm 1.54cm 1.17cm;*/
            margin: 6mm;
            /*mso-header-margin: 1.5cm;
            mso-footer-margin: 1.75cm;
            mso-paper-source: 0;*/
        }
        html {
            /*width: 210mm;
            height: 297mm;*/
            border: 0px #000 solid;
            margin: 0;
            padding: 0;
        }
        body {
            margin: 0;
            padding: 0;
        }
        * {
            font-family: SimHei;
            font-size: 14px;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }
        .right {
            text-align: right;
        }
        .center {
            text-align: center;
        }
        .dtl {
            width: 100%;
            border-left: 1px #000 solid;
            border-top: 1px #000 solid;
            border-collapse: collapse;
            margin-top: 0.5mm;
        }
        .dtl td {
            border-right: 1px #000 solid;
            border-bottom: 1px #000 solid;
            padding: 1px 3px;
            line-height: 30px;
        }
        .tdTitle {
            background-color: #dedede;
            width:16.66%;
            text-align: center;
            -webkit-print-color-adjust: exact; /*控制打印的时候有背景色*/
        }
        .bold {
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="title">[[${typeTitle}]]申请</div>
<div class="right">申请单号：<span>[[${backLedgerPrepayment.vbillno}]]</span></div>
<table class="dtl">
    <tr>
        <td class="tdTitle">客户名称</td>
        <td style="width: auto" colspan="2">[[${backLedger.custAbbr}]]</td>
        <td class="tdTitle">台账单号</td>
        <td style="width: auto" colspan="2">[[${backLedger.vbillno}]]</td>
    </tr>
    <tr>
        <td class="tdTitle">付票单位</td>
        <td style="width: auto" colspan="2">[[${backLedgerPrepayment.billingPayable}]]</td>
        <td class="tdTitle">申请人/时间</td>
        <td style="width: auto" colspan="2">[[${backLedgerPrepayment.regUserName}]]/[[${#dates.format(backLedgerPrepayment.createTime, 'yyyy-MM-dd HH:mm:ss')}]]</td>
    </tr>
    <tr>
        <td class="tdTitle">申请金额</td>
        <td style="width: auto" colspan="2">¥[[${#numbers.formatDecimal(backLedgerPrepayment.payAmount,1,'COMMA',2,'POINT')}]]</td>
        <td class="tdTitle">大写金额</td>
        <td style="width: auto" colspan="2">[[${digitUppercase}]]</td>
    </tr>
    <tr>
        <td class="tdTitle">付款公司</td>
        <td style="width: auto" colspan="4">[[${@dict.getLabel('bala_corp',backLedgerPrepayment.billingCorp)}]]</td>

    </tr>
    <tr style="height: 50px">
        <td class="tdTitle" >付款事由</td>
        <td colspan="5" >[[${backLedgerPrepayment.remark}]]&nbsp;</td>
    </tr>

    <tr>
        <td class="tdTitle">收款单位</td>
        <td style="width: auto" colspan="2">[[${backLedgerPrepayment.bankAccount}]]</td>
        <td class="tdTitle">银行账号</td>
        <td style="width: auto" colspan="2">[[${backLedgerPrepayment.bankCard}]]</td>
    </tr>
    <tr>
        <td class="tdTitle">开户行</td>
        <td style="width: auto" colspan="2">[[${backLedgerPrepayment.bankName}]]</td>
        <td class="tdTitle">付款方式</td>
        <td style="width: auto" colspan="2" th:each="dict : ${@dict.getType('pay_method')}" th:if="${ dict.dictValue == backLedgerPrepayment.payMethod }" th:text="${dict.dictLabel}"></td>
    </tr>
    <tr>
        <td class="tdTitle" colspan="6">审批意见</td>
    </tr>
    <tr style="height:80px;">
        <td  colspan="6">&nbsp;</td>
    </tr>


</table>

</body>
</html>