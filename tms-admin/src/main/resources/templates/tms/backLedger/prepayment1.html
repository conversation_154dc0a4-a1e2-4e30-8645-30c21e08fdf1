<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：修改结算方式')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .col-xs-4{
        line-height: 26px;
        text-align: right;
    }

    .fcff{
        color: #ff1f1f;
    }


    #buttonList>button{
        margin-right: 4px;
    }

    .border+.border{
        border-top: none;
    }
    .border>div{
        border: .5px solid #333;
        padding: 2px 4px;
        line-height: 26px;
    }

    .dropdown-menu{
        left:0px !important;
    }
    .custDiv{
        width: 50% !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate" >

        <input type="hidden" id="backLedgerId" name="backLedgerId" th:value="${bill.id}">
        <input type="hidden" id="type" name="type" th:value="${type}">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4" th:if="${type == 0}"><span class="fcff">*</span>预付金额：</label>
                    <label class="col-xs-4" th:if="${type == 1}"><span class="fcff">*</span>付款金额：</label>
                    <label class="col-xs-4" th:if="${type == 2}"><span class="fcff">*</span>承兑金额：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input id="payAmount" name="payAmount"  class="form-control" type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|"
                               maxlength="10" autocomplete="off" required th:value="${bill.payBackAmount}" readonly>
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>付款方式：</label>
                    <div class="col-xs-6" >
                        <select name="payMethod" class="form-control valid" required
                                th:with="type=${@dict.getType('pay_method')}">
                            <option value=""></option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>收款人：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <select class="form-control" id="carrBankId" name="carrBankId" onchange="bankChange()">
                            <option value=""></option>
                            <option th:each="dict : ${custBillings}" th:text="${dict.billingPayable}"
                                    th:value="${dict.custBillingId}"></option>
                        </select>
                    </div>
                    <input type="hidden" id="bankAccount" name="bankAccount">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">收款账号：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input id="bankCard" name="bankCard"  class="form-control" type="text"  th:oninput="|$.numberUtil.onlyNumber(this);|"  autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">开户行：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input id="bankName" name="bankName"  class="form-control" type="text"  th:oninput="|$.numberUtil.onlyNumber(this);|"  autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">付款事由：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <textarea id="remark" name="remark"  class="form-control" autocomplete="off" rows="5"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    let backLedger = [[${backLedger}]];
    let carrBankList = [[${carrBankList}]];
    let custBillings = [[${custBillings}]];
    $(function() {

    });


    function bankChange(){

        let carrBankId = $("#carrBankId").val();
        for(let i = 0 ; i < custBillings.length; i++){
            let custBilling = custBillings[i];
            if(custBilling.custBillingId === carrBankId){
                $("#bankName").val(custBilling.bank);
                $("#bankCard").val(custBilling.bankAccount);
                $("#bankAccount").val(custBilling.billingPayable);
            }
        }
    }

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-carrier-edit").serializeArray();
            $.operate.save(prefix + "/savePrepayment", data);
        }
    }


</script>
</body>

</html>