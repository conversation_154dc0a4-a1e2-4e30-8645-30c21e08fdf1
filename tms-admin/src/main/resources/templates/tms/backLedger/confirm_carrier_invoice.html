<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：修改结算方式')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .col-xs-4{
        line-height: 26px;
        text-align: right;
    }

    .fcff{
        color: #ff1f1f;
    }


    #buttonList>button{
        margin-right: 4px;
    }

    .border+.border{
        border-top: none;
    }
    .border>div{
        border: .5px solid #333;
        padding: 2px 4px;
        line-height: 26px;
    }

    .dropdown-menu{
        left:0px !important;
    }
    .custDiv{
        width: 50% !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate" >

        <input type="hidden" name="id" th:value="${id}">
        <div class="col-md-3 col-sm-6">
            <div class="form-group">
                <label class="col-xs-4"><span class="fcff">*</span>收票日期：</label>
                <div class="col-xs-6" >
                    <input type="text" class="form-control" id="invoiceDate" name="invoiceDate" autocomplete="off" required>
                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    $(function() {

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#invoiceDate',
                type: 'datetime',
                trigger: 'click',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours=now.getHours();
                    this.dateTime.minutes=now.getMinutes();
                    this.dateTime.seconds=now.getSeconds();
                }
            });
        });
    })


    //提交
    function submitHandler() {
        var data = $("#form-carrier-edit").serializeArray();
        $.operate.save(prefix + "/saveConfirmCarrierInvoice", data);
    }


</script>
</body>

</html>