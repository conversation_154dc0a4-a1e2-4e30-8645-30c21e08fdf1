<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('在途费审核')"/>
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse,.f7f7{
        background-color: #F7F7F7;
    }
    .table-striped {
        height: calc(100% - 50px);
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .tooltip-inner{
        /* background: transparent !important; 
        text-align: left !important;
        color:#363636 !important;
        border:1px solid transparent; */
        max-width: 700px !important;
    }

    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .vertical-timeline-icon{
        width: 10px;
        height: 10px;
        background-color: #18a689;
        border-color: #18a689;
        top: 20px;
    }
    .vertical-timeline-content{
        margin-left: 16px;
    }
    .leftIcon{
        position: relative;
        margin: 0;
    }
    .leftIcon::after{
        content:" ";
        position: absolute;
        width: 1px;
        height: 100%;
        background-color: #18a689;
        top: 20px;
        left: 4px;
    }
    .leftIcon:last-child::after{
        height: 50%;
    }
    .leftIcon:first-child{
        font-size: 14px;
        color: #1ab394;
    }
    .text-coral{
        color: coral;
    }
    .flexT{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .form-group{
        margin-bottom: 0;
    }
    .tooltips {
        display: inline-block;
        max-width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .btn-group-sm>.btn, .btn-sm {
        padding: 3px 10px;
    }
    .cpx{
        display: inline-block;
        width: 35px;
        height: 35px;
        background: url("/img/cpx.png") no-repeat 100%/100%;
        vertical-align: middle;
    }
    .ssx{
        display: inline-block;
        width: 45px;
        height: 40px;
        background: url("/img/ssx.png") no-repeat 100%/100%;
        vertical-align: middle;
        position: relative;
        top: 6px;
    }

    .xjx{
        display: inline-block;
        width: 50px;
        height: 40px;
        background: url("/img/xjx.png") no-repeat 100%/100%;
        vertical-align: middle;
        position: relative;
        top: 6px;
    }
    .w100{
        width: 100%;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" id="prepaymentId" name="prepaymentId" th:value="${id}">

            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
    var payMethod = [[${@dict.getType('pay_method')}]];
    let prepaymentId = [[${id}]];
    var prefix = ctx + "backLedger";
    
    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: ctx + "backLedger/payRecordList",
            showToggle: false,
            showColumns: true,
            clickToSelect:true,
            height: 560,
            showFooter:true,
            uniqueId: "id",
            columns: [
                {
                    title: '操作',
                    align: 'center',
                    field: 'id',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if(row.unconfirmUsername == null){
                            if ([[${@permission.hasPermi('tms:backLedger:payBackOff')}]] != "hidden" && row.payMethod != 98) {
                                actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="反核销" onclick="backOff(\'' + row.id + '\')"><i class="fa fa-mail-reply" style="font-size: 15px;"></i></a>');
                            }
                        }

                        return actions.join('');
                    }
                },
                {
                    field: 'payDate',
                    title: '付款日期',
                    align: 'left'
                },

                {
                    field: 'payAmount',
                    title: '付款金额',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }
                    }
                },
                {
                    field: 'payMethod',
                    title: '付款方式',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(value == 98){
                            return '运费转保证金'
                        }
                        return $.table.selectDictLabel(payMethod, value);
                    }
                },
                {
                    title: '付款账户',
                    align: 'left',
                    field: 'accountName'
                },
                {
                    title: '附件',
                    field: 'sysUploadFiles',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var html = "<div class='picviewer'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html += `<img src="`+element.filePath+`"/>`
                            });
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                },

                {
                    title: '操作人',
                    align: 'left',
                    field: 'regUserName'
                },
                {
                    title: '操作时间',
                    align: 'left',
                    field: 'createTime'
                },
                {
                    title: '反核销人',
                    align: 'left',
                    field: 'unconfirmUsername'
                },
                {
                    title: '反核销时间',
                    align: 'left',
                    field: 'unconfirmTime'
                }, {
                    title: '反核销原因',
                    align: 'left',
                    field: 'unconfirmMemo',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }

        };

        $.table.init(options);
    });


    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }

    function backOff(id){
       $.modal.open("反核销", prefix + "/backConfirmPay?id=" + id,500,300);
    }

</script>


</body>
</html>