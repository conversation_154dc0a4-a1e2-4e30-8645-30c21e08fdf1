<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细申请审核')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" id="backLedgerId" name="backLedgerId" th:value="${id}">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="vbillno" class="form-control" type="text" placeholder="请输入单号"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="billingPayable" class="form-control" type="text" placeholder="请输入发票抬头"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input id="yearMonth" name="yearMonth" class="form-control" type="text" placeholder="业务发生年月" readonly>
                                <input id="yearMonthStart" name="yearMonthStart" class="form-control" type="hidden" placeholder="业务发生年月" >
                                <input id="yearMonthEnd" name="yearMonthEnd" class="form-control" type="hidden" placeholder="业务发生年月" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="billingCorp" id="billingCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" required>
                                    <option value="">---请选择开票公司---</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 col-sm-1">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="moneyType" id="moneyType" class="form-control valid" required>
                                    <option value="">---金额类型---</option>
                                    <option value="0">运费</option>
                                    <option value="1">装卸费</option>

                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="addInvoice()" shiro:hasPermission="tms:backLedger:invoiceApply">
                <i class="fa fa-plus"></i> 开票申请
            </a>
            <a class="btn btn-danger" onclick="removeInvoice()" shiro:hasPermission="tms:backLedger:removeInvoice">
                <i class="fa fa-remove"></i> 开票申请作废
            </a>
            <a class="btn btn-info" onclick="batchPay()" shiro:hasPermission="tms:backLedger:batchPay">
                <i class="fa fa-hand-stop-o"></i> 批量收款核销
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<script id="kpHtml" type="text/template">
    <div class="form-content">
        <div>
            <table class="table table-bordered mt5">
                <thead>
                <tr>
                    <td>操作</td>
                    <td>开票日期</td>
                    <td>开票附件</td>
                </tr>
                </thead>
                <tbody id="kpTbody">
                <tr><td colspan="2" style="text-align: center;">暂无数据</td></tr>
                </tbody>

            </table>
        </div>
    </div>
</script>
<script id="skHtml" type="text/template">
    <div class="form-content">
        <div>
            <table class="table table-bordered mt5">
                <thead>
                <tr>
                    <td>操作</td>
                    <td>收款日期</td>
                    <td>收款方式</td>
                    <td>收款金额</td>
                    <td>转入账户</td>
                    <td>备注</td>
                </tr>
                </thead>
                <tbody id="skTbody">
                <tr><td colspan="5" style="text-align: center;">暂无数据</td></tr>
                </tbody>

            </table>
        </div>
    </div>
</script>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    var billingType = [[${@dict.getType('billing_type')}]];
    var billingCorp = [[${@dict.getType('bala_corp')}]];
    var receivableMethod = [[${@dict.getType('receivable_method')}]];

    //开票金额合计
    var kpje = 0;
    var yskje = 0;
    var wskje = 0;

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: ctx + "backLedger/invoiceList",
            removeUrl: prefix + "/removeBillings",
            showToggle: false,
            showColumns: true,
            clickToSelect:true,
            height: 560,
            showFooter:true,
            uniqueId: "id",
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onPostBody: function () {
                clearTotal();
                //合并页脚
                merge_footer();
                getAmountCount();
            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "开票金额：<nobr id='kpje'>¥0.00</nobr>" +
                            " 已收款金额：<nobr id='yskje'>¥0.00</nobr>" +
                            " 未收款金额：<nobr id='wskje'>¥0.00</nobr>" +
                            "<br>" +
                            "总合计：开票金额：<nobr id='kpjeTotal'></nobr>" +
                            " 已收款金额：<nobr id='yskjeTotal'></nobr>"+
                            " 未收款金额：<nobr id='wskjeTotal'></nobr>";
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    field: 'id',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.billStatus == 0 && [[${@permission.hasPermi('tms:backLedger:confirmBilling')}]] != "hidden") {
                            actions.push(`<a class="btn btn-xs" href="javascript:void(0)" title="确认开票" onclick="confirmBilling('${row.id}','${row.billStatus}',${row.receiveAmount})"><i class="fa fa-file-powerpoint-o" style="font-size: 15px;"></i></a>`);
                        }
                        if (row.receiveAmount < row.payBackApplyAmount && [[${@permission.hasPermi('tms:backLedger:receiveCheck')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="收款核销" onclick="receiveCheck(\'' + row.id + '\')"><i class="fa fa-hand-stop-o" style="font-size: 15px;"></i></a>')
                        }
                        if (row.billStatus !== 1 &&  row.receiveAmount == null &&  row.payBackAmount == null) {
                            actions.push(`<a class="btn  btn-xs" href="javascript:void(0)"  onclick="editInvoice('${row.id}')" title="修改"><i  class="fa fa-edit" style="font-size: 15px;" ></i></a>`);
                        }
                        if(row.payBackAmount != null && row.ifPayBack == 0) {
                            actions.push(`<a class="btn  btn-xs" href="javascript:void(0)"  onclick="payBackApply('${row.id}')" title="承兑付款申请"><i  class="fa fa-refresh" style="font-size: 15px;" ></i></a>`);
                        }
                        if(row.payBackAmount == null && row.receiveAmount < row.billingAmount){
                            actions.push(`<a class="btn  btn-xs" href="javascript:void(0)"  onclick="beforePay('${row.id}')" title="承兑预收申请"><i  class="fa fa-handshake-o" style="font-size: 15px;" ></i></a>`);
                        }
                        return actions.join('');
                    }
                },
                {
                    field: 'vbillno',
                    title: '申请单号',
                    align: 'left'
                },
                {
                    field: 'billStatus',
                    title: '开票状态',
                    align: 'left',
                    formatter: function (value, row, index) {
                       if(value == 0){
                           return '<span class="label label-danger">未开票</label>'
                       }else if(value == 1){
                           return '<span class="label label-success">已开票</label>'
                       }else{
                           return '-'
                       }
                    }
                },
                {
                    field: 'moneyType',
                    title: '金额类型',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(value == 0){
                            return '<span class="label label-success">运费</label>'
                        }else if(value == 1){
                            return '<span class="label label-info">装卸费</label>'
                        }else{
                            return '-'
                        }
                    }
                },
                {
                    field: 'billNo',
                    title: '发票号',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let html = ``
                        if (value != null) {
                            html += `<span class='label badge-white' data-toggle='tooltip' data-placement='top' data-html='true' title='${value}'>${$.table.tooltip(value)}</span></br>`;
                            return html
                        }else {
                            return '-'
                        }
                    }
                },
                {
                    field: 'yearMonth',
                    title: '业务发生年月',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value != null) {
                            return value
                        }else {
                            return '-'
                        }
                    }
                },
                {
                    title: '开票金额',
                    align: 'right',
                    field: 'billingAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            let s = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            return `<div><a onclick="openDetail('${row.id}',0)">${s}</a></div>`;
                        }else{
                            return '-';
                        }
                    }
                },
                {
                    title: '已收款金额',
                    align: 'right',
                    field: 'receiveAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            let s = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});

                            if(row.payBackAmount !=null && value < row.payBackApplyAmount){
                                    return `<div><a onclick="openDetail('${row.id}',1)">${s}</a>        <i class="fa fa-question-circle" data-toggle="tooltip"
                                   style="font-size: 15px" data-html="true" data-container="body"
                                   title=总应收：¥`+row.payBackApplyAmount+`></i></div>`;
                            }else{
                                return `<div><a onclick="openDetail('${row.id}',1)">${s}</a></div>`;
                            }

                        }else{
                            return '-';
                        }
                    }
                },
                {
                    title: '承兑差额',
                    align: 'right',
                    field: 'payBackAmount',
                    formatter: function (value, row, index) {
                        if(row.ifPayBack == 1){
                            let s = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            return `<div><a onclick="openBackDetail('${row.id}',1)">${s}</a><span class="label label-success" style="padding-left: 3px;padding-right: 3px">已申</span></div>`;
                        }else if(row.ifPayBack == 2){
                            let s = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            return `<div><a onclick="openBackDetail('${row.id}',1)">${s}</a><span class="label label-info" style="padding-left: 3px;padding-right: 3px">已付</span></div>`;
                        }else if(value != null){
                            let s = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                            return `<div>${s}<span class="label label-warning" style="padding-left: 3px;padding-right: 3px">未申</span></div>`;
                        }else{
                            return '-';
                        }
                    }
                },
                {
                    title: '开票公司',
                    align: 'left',
                    field: 'billingCorp',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(billingCorp, value);
                    }
                },
                {
                    title: '发票类型',
                    align: 'left',
                    field: 'billingType',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(billingType, value);
                    }
                },
                {
                    title: '发票抬头',
                    align: 'left',
                    field: 'billingPayable'
                },
                {
                    title: '纳税人识别号',
                    align: 'left',
                    field: 'taxIdentify'
                },
                {
                    title: '开户行',
                    align: 'left',
                    field: 'bank'
                },
                {
                    title: '银行账号',
                    align: 'left',
                    field: 'bankAccount'
                },
                {
                    title: '地址电话',
                    align: 'left',
                    field: 'addressPhone' ,
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '备注',
                    align: 'left',
                    field: 'remark',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '申请人',
                    align: 'left',
                    field: 'regUserName'
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'createTime'
                }

            ]
        };

        $.table.init(options);
    });

    layui.use(function(){
        var laydate = layui.laydate;

        // 年月选择器
        laydate.render({
            elem: '#yearMonth',
            type: 'month',
            range: true,
            done: function (value, date, endDate) {
                console.log(value)
                let dateRange = value.split(' - ');
                $("#yearMonthStart").val(dateRange[0]);
                $("#yearMonthEnd").val(dateRange[1]);
            }
        });
    });

    //审核
    function payOff(id,type) {
        let url = prefix + "/payOff?id=" + id;
        if(type == 0){
            $.modal.open("预付款核销" , url,800,550);
        }else if(type == 1){
            $.modal.open("应付款核销" , url,800,550);
        }

    }

    function removeInvoice(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var billStatus = $.table.selectColumns("billStatus");
        for(var i = 0; i < billStatus.length ; i++){
            if(billStatus[i] == 1){
                $.modal.alertWarning("请选择未开票记录");
                return;
            }
        }

        var receiveAmount = $.table.selectColumns("receiveAmount");
        for(var i = 0; i < receiveAmount.length ; i++){
            if(receiveAmount[i] != null && receiveAmount[i] != 0){
                $.modal.alertWarning("存在已收款金额，请先反核销");
                return;
            }
        }

        $.modal.confirm("确认要作废选中的" + rows.length + "条数据吗?", function() {
            var url = $.table._option.removeUrl;
            var data = { "ids": rows.join() ,backLedgerId:$("#backLedgerId").val()};
            $.operate.submit(url, "post", "json", data);
        });
    }

    function prints(id){
        let iframe = document.getElementById("print-frame");
        if (!iframe) {
            iframe = document.createElement('IFRAME');
            iframe.id = "print-frame"
            document.body.appendChild(iframe);
            iframe.setAttribute('style', 'display:none;');
        }
        let carrierId = $("#carrierId").val();
        let amount = $("#amount").val();
        let payType = $("#payType").val();
        console.log(carrierId);
        iframe.src = prefix + "/applyPrint?id="+id;
        iframe.onload = function () { //解决图片显示不了的问题
            iframe.contentWindow.focus();
            iframe.contentWindow.print();
        };
    }

    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }

    function getValue(val){
        if(val == null){
            val = "/";
        }
        return val
    }



    function addInvoice(){
         let id = $("#backLedgerId").val();
         let url = prefix + "/addInvoice?backLedgerId=" + id;
         $.modal.open("开票登记" , url,800,550);
    }



    function editInvoice(backLedgerBillingId){
         let id = $("#backLedgerId").val();
         let url = prefix + `/addInvoice?backLedgerId=${id}&&backLedgerBillingId=${backLedgerBillingId}&&type=1`  ;
         $.modal.open("开票修改" , url,800,550);
    }

    function beforePay(id){
        let url = prefix + "/beforePay?id=" + id;
        $.modal.open("承兑预收申请" , url,500,150);
    }

    function confirmBilling(id,status,receiveAmount){
        if (status == 1 && receiveAmount != null) {
            $.modal.alertWarning("该申请单已开票，请勿重复操作");
            return false;
        }
        let url = prefix + "/confirmBilling?id=" + id;
        $.modal.open("开票记录" , url,800,550);
    }

    function receiveCheck(id){
        let url = prefix + "/receiveCheck?id=" + id;
        $.modal.open("收款核销" , url,800,550);
    }

    function batchPay(){
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        let url = prefix + "/receiveCheck?id=" + rows.join();
        $.modal.open("收款核销" , url,800,550);
    }

    function openDetail(id,type) {
        let title = type === 0 ? '开票明细' : '收款核销明细';
        let htmlId = type === 0 ? 'kpHtml' : 'skHtml';
        layer.open({
            type: 1,
            title: title,
            area: ['85%', '75%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: $(`#${htmlId}`).html(),
            btn: ['关闭'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {
                $.ajax({
                    url: ctx + `backLedger/get_detail?id=${id}&type=${type}`,
                    type: "get",
                    beforeSend: function () {


                    },
                    success: function (data) {
                        if (data.code == 0) {
                            if (type === 0) {
                                let kpData = data.data;

                                $("#kpTbody").empty();

                                let html = kpData ===null || kpData.billingDate == null ? `<tr><td colspan="2" style="text-align: center;">暂无数据</td></tr>` : ``

                                var imgHtml = "<div class='picviewer'>"
                                if(kpData.files != null) {
                                    kpData.files.forEach(function (element, index) {
                                        // imgHtml += `<img src="`+element.filePath+`"/>`

                                        imgHtml += `<span class="mr10"><a href="${element.filePath}" download="${element.fileName}" title="点击下载">${element.fileName}</a></span>`
                                    });
                                }else {
                                    imgHtml = '-';
                                }
                                imgHtml +="</div>"


                                if (kpData.billingDate != null) {
                                    if([[${@permission.hasPermi('tms:backLedger:removeBilling')}]] != "hidden"){
                                        html += `
                                        <tr>
                                          <td><a class="btn btn-xs btn-danger" onclick="removeBilling('${kpData.id}')"><i class="fa fa-remove"></i>删除开票</a></td>
                                            <td>${kpData.billingDate}</td>
                                            <td>${imgHtml}</td>
                                        </tr>
                                    `;
                                    }else{
                                        html += `
                                        <tr>
                                            <td></td>
                                            <td>${kpData.billingDate}</td>
                                            <td>${imgHtml}</td>
                                        </tr>
                                    `;
                                    }

                                }
                                $("#kpTbody").append(html);


                                $('.picviewer').viewer({
                                    url: 'data-original',
                                    title: false,
                                    navbar:false,
                                });

                            }else if (type === 1) {
                                let skList = data.data;

                                $("#skTbody").empty();


                                let html = skList ===null || skList.length === 0 ? `<tr><td colspan="5" style="text-align: center;">暂无数据</td></tr>` : ``
                                skList.forEach(item => {

                                    let ticketNumber = ``
                                    if (item.ticketNumber) {
                                        ticketNumber = `（${item.ticketNumber}）`
                                    }
                                    if ([[${@permission.hasPermi('tms:backLedger:removeReceive')}]] != "hidden") {
                                        html += `
                                        <tr>
                                            <td><a class="btn btn-xs btn-danger" onclick="removeReceive('${item.id}')"><i class="fa fa-remove"></i>反核销</a></td>
                                            <td>${item.receiveDate}</td>
                                            <td>${$.table.selectDictLabel(receivableMethod, item.receiveMethod)}${ticketNumber}</td>
                                            <td>${item.receiveAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})}</td>
                                            <td>${item.accountName}</td>
                                            <td>${item.remark == null ? '' : item.remark}</td>
                                        </tr>
                                    `;
                                    }else{
                                        html += `
                                        <tr>
                                            <td></td>
                                            <td>${item.receiveDate}</td>
                                            <td>${$.table.selectDictLabel(receivableMethod, item.receiveMethod)}${ticketNumber}</td>
                                            <td>${item.receiveAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})}</td>
                                            <td>${item.accountName}</td>
                                            <td>${item.remark == null ? '' : item.remark}</td>
                                        </tr>
                                    `;
                                    }

                                });
                                $("#skTbody").append(html);

                            }
                        }

                    }
                })


            },
            cancel: function (index) {
                return true;
            }
        })
    }

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: prefix + "/getInvoiceCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;
                if (result.code == 0 && data != undefined) {
                    $("#kpjeTotal").text(data.KPJETOTAL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#yskjeTotal").text(data.YSKJETOTAL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#wskjeTotal").text(data.FSKYETOTAL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

                }
            }
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
         kpje = 0;
         yskje = 0;
         wskje = 0;

    }

    function addTotal(row) {
        kpje += row.billingAmount;
        yskje += row.receiveAmount;

        let amount = row.billingAmount - row.receiveAmount;
        if(row.payBackAmount != null) {
            amount += row.payBackAmount;
        }
        wskje += amount;
    }


    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#kpje").text(kpje.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#yskje").text(yskje.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#wskje").text(wskje.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }

    function subTotal(row) {
        kpje -= row.billingAmount;
        yskje -= row.receiveAmount;

        let amount = row.billingAmount - row.receiveAmount;
        if(row.payBackAmount != null) {
            amount += row.payBackAmount;
        }
        wskje -= amount;
    }

    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }


    function payBackApply(id){
        let url = prefix + "/payBackApply?id=" + id;
        $.modal.open("承兑差额申请" , url,500,480);
    }

    function openBackDetail(id){
        var url = prefix + "/prepaymentBackView?id=" + id;
        $.modal.openTab("承兑差额记录", url);
    }

    function removeReceive(id){
        $.modal.confirm("是否反核销此收款记录？", function() {
            var data = { "id": id };
            $.operate.saveAndRefreshAll(prefix + "/removeReceive", data);
        })
    }


    function removeBilling(id){
        $.modal.confirm("是否删除此开票信息？", function() {
            var data = { "id": id };
            $.operate.saveAndRefreshAll(prefix + "/removeBilling", data);
        })
    }
</script>


</body>
</html>