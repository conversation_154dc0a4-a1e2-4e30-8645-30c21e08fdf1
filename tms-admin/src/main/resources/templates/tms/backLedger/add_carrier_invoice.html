<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：修改结算方式')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .col-xs-4{
        line-height: 26px;
        text-align: right;
    }

    .fcff{
        color: #ff1f1f;
    }


    #buttonList>button{
        margin-right: 4px;
    }

    .border+.border{
        border-top: none;
    }
    .border>div{
        border: .5px solid #333;
        padding: 2px 4px;
        line-height: 26px;
    }

    .dropdown-menu{
        left:0px !important;
    }
    .custDiv{
        width: 50% !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate" >

        <input type="hidden" name="backLedgerId" th:value="${id}">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">收票金额：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input id="invoiceAmount" name="invoiceAmount"  class="form-control" type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="false" onchange="calPayAmount()">
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4" style="text-align: right">发票上传：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input name="fileId" id="fileId" class="form-control" type="file" multiple>
                        <input type="hidden" id="tid" name="tid">
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    $(function() {

        //图片功能初始化
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.initAddFiles("fileId", "tid", picParam);
        $('#fileId').on('filesuccessremove', function(event, key, index) {
            $('#fileId').fileinput('clear');
            $('#tid').val('');
        });
    })


    //提交
    function submitHandler() {
        if ($.validate.form()) {
            if ($('#fileId').fileinput('getFilesCount') > 0) {
                jQuery.subscribe("cmt", commit);
                $('#fileId').fileinput('upload');
            } else {
                commit()
            }
        }
    }

    function commit(){
        var data = $("#form-carrier-edit").serializeArray();
        $.operate.save(prefix + "/saveCarrierInvoice", data);
    }


</script>
</body>

</html>