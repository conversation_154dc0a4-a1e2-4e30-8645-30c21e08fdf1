<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：修改结算方式')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .col-xs-4{
        line-height: 26px;
        text-align: right;
    }

    .fcff{
        color: #ff1f1f;
    }


    #buttonList>button{
        margin-right: 4px;
    }

    .border+.border{
        border-top: none;
    }
    .border>div{
        border: .5px solid #333;
        padding: 2px 4px;
        line-height: 26px;
    }

    .dropdown-menu{
        left:0px !important;
    }
    .custDiv{
        width: 50% !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate" >


        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>客户：</label>
                    <div class="col-xs-6 custDiv" >
                        <input type="hidden" id="customerId" name="customerId">
                        <input name="custAbbr" id="custAbbr" class="form-control" type="text" minlength="1" maxlength="30" autocomplete="off" required>
                        <div class="input-group-btn">
                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right" role="menu" style="left: 0px">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>承运商：</label>
                    <div class="col-xs-6 custDiv">
                        <input type="hidden" id="carrierId" name="carrierId">
                        <input name="carrName" id="carrName" class="form-control" type="text" minlength="1" maxlength="30" autocomplete="off" required>
                        <div class="input-group-btn">
                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right" role="menu" style="left: 0px">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--<div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">应收运费：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input id="receiveAmount" name="receiveAmount"  class="form-control" type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="false" onchange="calPayAmount()">
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>
        </div>-->
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>运费比例：</label>
                    <div class="col-xs-6">
                        <input id="payRate" name="payRate"  class="form-control" type="text" min="0" max="1" oninput="$.numberUtil.onlyNumberCustom(this,1,0,6,4)" maxlength="10" autocomplete="off" onchange="calPayAmount()" required>
                    </div>
                </div>
            </div>
        </div>
       <!-- <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">应付运费：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input id="payAmount" name="payAmount"  class="form-control" type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="off">
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>
        </div>-->
       <!-- <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">应收装卸费：</label>
                    <div class="col-xs-6"  style="display:inline-table;">
                        <input id="receiveZxfAmount" name="receiveZxfAmount"  class="form-control" type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="off" onchange="calPayZxfAmount()">
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>
        </div>-->
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>装卸费比例：</label>
                    <div class="col-xs-6">
                        <input id="payZxfRate" name="payZxfRate"  class="form-control" type="text" min="0" max="1" oninput="$.numberUtil.onlyNumberCustom(this,1,0,6,4)" maxlength="10" autocomplete="off" onchange="calPayZxfAmount()" required>
                    </div>
                </div>
            </div>
        </div>
       <!-- <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">应付装卸费：</label>
                    <div class="col-xs-6"  style="display:inline-table;">
                        <input id="payZxfAmount" name="payZxfAmount"  class="form-control" type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="off">
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>
        </div>-->
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">备注：</label>
                    <div class="col-xs-6">
                        <textarea class="form-control" name="remark" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>
        </br>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group" style="background-color: #fff8eb">
                    <label class="col-xs-12" style="padding-top: 20px;padding-left: 20px;padding-right: 20px;padding-bottom: 20px">提示：运费比例和装卸费比例必填（例：50%填入0.5）。应收运费、应付运费、应收装卸费、应付装卸费将根据开票金额和比例进行计算。</label>
                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    $(function() {

    })

    function calPayAmount(){
       /* let receiveAmount = $("#receiveAmount").val();
        let payRate = $("#payRate").val();
        let payAmount = Number(receiveAmount)*Number(payRate);
        $("#payAmount").val(handleCutZero(payAmount.toFixed(2)));*/
    }

    function calPayZxfAmount(){
     /*   let receiveZxfAmount = $("#receiveZxfAmount").val();
        let payZxfRate = $("#payZxfRate").val();
        let payZxfAmount = Number(payZxfRate)*Number(receiveZxfAmount);
        $("#payZxfAmount").val(handleCutZero(payZxfAmount.toFixed(2)));*/
    }

    //模糊查询
    $("#custAbbr").bsSuggest('init', {
        url: ctx + "client/findClientInfo?keyword=",
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "custAbbr",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["custAbbr"],
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
       $("#customerId").val(data.customerId);
       $("#custAbbr").val(data.custAbbr);
    })

    //模糊查询
    $("#carrName").bsSuggest('init', {
        url: ctx + "backLedger/findCarrierInfo?keyword=",
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "carrName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["carrName"],
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#carrierId").val(data.carrierId);
        $("#carrName").val(data.carrName);
    })

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-carrier-edit").serializeArray();
            $.operate.save(prefix + "/addBackLedger", data);
        }
    }

    function handleCutZero(num) {
        //拷贝一份 返回去掉零的新串
        let newstr = num;
        //循环变量 小数部分长度
        let leng = num.length - num.indexOf('.') - 1;
        //判断是否有效数
        if (num.indexOf('.') > -1) {
            //循环小数部分
            for (let i = leng; i > 0; i--) {
                //如果newstr末尾有0
                if (
                    newstr.lastIndexOf('0') > -1 &&
                    newstr.substr(newstr.length - 1, 1) == 0
                ) {
                    let k = newstr.lastIndexOf('0');
                    //如果小数点后只有一个0 去掉小数点
                    if (newstr.charAt(k - 1) == '.') {
                        return newstr.substring(0, k - 1);
                    } else {
                        //否则 去掉一个0
                        newstr = newstr.substring(0, k);
                    }
                } else {
                    //如果末尾没有0
                    return newstr;
                }
            }
        }
        return num;
    }


</script>
</body>

</html>