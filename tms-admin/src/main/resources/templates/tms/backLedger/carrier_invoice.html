<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('在途费审核')"/>
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse,.f7f7{
        background-color: #F7F7F7;
    }
    .table-striped {
        height: calc(100% - 50px);
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .tooltip-inner{
        /* background: transparent !important; 
        text-align: left !important;
        color:#363636 !important;
        border:1px solid transparent; */
        max-width: 700px !important;
    }

    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .vertical-timeline-icon{
        width: 10px;
        height: 10px;
        background-color: #18a689;
        border-color: #18a689;
        top: 20px;
    }
    .vertical-timeline-content{
        margin-left: 16px;
    }
    .leftIcon{
        position: relative;
        margin: 0;
    }
    .leftIcon::after{
        content:" ";
        position: absolute;
        width: 1px;
        height: 100%;
        background-color: #18a689;
        top: 20px;
        left: 4px;
    }
    .leftIcon:last-child::after{
        height: 50%;
    }
    .leftIcon:first-child{
        font-size: 14px;
        color: #1ab394;
    }
    .text-coral{
        color: coral;
    }
    .flexT{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .form-group{
        margin-bottom: 0;
    }
    .tooltips {
        display: inline-block;
        max-width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .btn-group-sm>.btn, .btn-sm {
        padding: 3px 10px;
    }
    .cpx{
        display: inline-block;
        width: 35px;
        height: 35px;
        background: url("/img/cpx.png") no-repeat 100%/100%;
        vertical-align: middle;
    }
    .ssx{
        display: inline-block;
        width: 45px;
        height: 40px;
        background: url("/img/ssx.png") no-repeat 100%/100%;
        vertical-align: middle;
        position: relative;
        top: 6px;
    }

    .xjx{
        display: inline-block;
        width: 50px;
        height: 40px;
        background: url("/img/xjx.png") no-repeat 100%/100%;
        vertical-align: middle;
        position: relative;
        top: 6px;
    }
    .w100{
        width: 100%;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" id="backLedgerId" name="backLedgerId" th:value="${id}">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="vbillno" class="form-control" type="text" placeholder="请输入单号"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="addCarrierInvoice()" shiro:hasPermission="tms:backLedger:addCarrierInvoice">
                <i class="fa fa-plus"></i> 收票登记
            </a>
            <a class="btn btn-danger multiple disabled"  onclick="$.operate.removeAll()"
               shiro:hasPermission="tms:backLedger:removeCarrierInvoice">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:src="@{'/js/wecom.js'}"></script>
<script th:inline="javascript">
    var payOrCollect = [[${@dict.getType('pay_or_collect')}]];
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var menuType = [[${menuType}]]

    var prefix = ctx + "backLedger";
    
    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: ctx + "backLedger/carrierInvoiceList",
            removeUrl: prefix + "/removeCarrierInvoice",
            showToggle: false,
            showColumns: true,
            clickToSelect:true,
            height: 560,
            showFooter:true,
            uniqueId: "id",
            columns: [
                {
                    checkbox: true
                },
                {
                    title: '操作',
                    align: 'center',
                    field: 'id',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:backLedger:confirmCarrierInvoice')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="收票核销" onclick="confirmCarrierInvoice(\'' + row.id + '\')"><i class="fa fa-calendar" style="font-size: 15px;"></i></a>')
                        }
                        return actions.join('');
                    }
                },
                {
                    field: 'vbillno',
                    title: '申请单号',
                    align: 'left'
                },
                {
                    title: '收票日期',
                    align: 'left',
                    field: 'invoiceDate'
                },
                {
                    title: '收票金额',
                    align: 'right',
                    field: 'invoiceAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }
                    }
                },
                {
                    title: '附件',
                    field: 'sysUploadFiles',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var html = "<div class='picviewer'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html += `<img src="`+element.filePath+`"/>`
                            });
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                },
                {
                    title: '申请人',
                    align: 'left',
                    field: 'regUserName'
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'createTime'
                },
                {
                    title: '核销人',
                    align: 'left',
                    field: 'checkUserName'
                },
                {
                    title: '核销时间',
                    align: 'left',
                    field: 'checkDate'
                }


            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });
            }

        };

        $.table.init(options);
    });


    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }

    function addCarrierInvoice(){
        let id = $("#backLedgerId").val();
        let url = prefix + "/addCarrierInvoice?id=" + id;
        $.modal.open("收票登记" , url,500,480);
    }

    function confirmCarrierInvoice(id){
        let url = prefix + "/confirmCarrierInvoice?id=" + id;
        $.modal.open("收票核销" , url,500,480);
    }
</script>


</body>
</html>