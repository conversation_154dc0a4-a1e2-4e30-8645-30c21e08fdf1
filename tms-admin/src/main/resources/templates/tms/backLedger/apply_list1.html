<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细申请审核')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" id="backLedgerIdOnly" name="backLedgerIdOnly" th:value="${backLedgerId}">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="vbillno" class="form-control" type="text" placeholder="请输入单号"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <!--<a class="btn btn-primary" onclick="add()" shiro:hasPermission="tms:backLedger:payPreApply">
                <i class="fa fa-plus"></i> 预付款申请
            </a>
            <a class="btn btn-warning" onclick="addPay()" shiro:hasPermission="tms:backLedger:payApply">
                <i class="fa fa-plus"></i> 付款申请
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: ctx + "backLedger/applyList",
            showToggle: false,
            showColumns: true,
            clickToSelect:true,
            height: 560,
            showFooter:true,
            uniqueId: "id",
            columns: [
                {
                    checkbox: true
                },
                {
                    title: '操作',
                    align: 'center',
                    field: 'id',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if(row.payAmount != row.hasPayAmount){
                            actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="打印" onclick="prints(\'' + row.id + '\')"><i class="fa fa-print" style="font-size: 15px;"></i></a>');
                        }
                        return actions.join('');
                    }
                },
                {
                    field: 'vbillno',
                    title: '申请单号',
                    align: 'left'
                },
                {
                    field: 'type',
                    title: '申请类型',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return '<span class="label label-danger">承兑差额</label>'
                    }
                },
                {
                    title: '申请金额',
                    align: 'right',
                    field: 'payAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }
                    }
                },
                {
                    title: '已付金额',
                    align: 'right',
                    field: 'hasPayAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return `<a onclick="payRecord('`+row.id+`')">`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</a>`;
                        }else{
                            return '-';
                        }
                    }
                },
                {
                    title: '付款事由',
                    align: 'left',
                    field: 'remark',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '收款人',
                    align: 'left',
                    field: 'bankAccount'
                },
                {
                    title: '收款账户',
                    align: 'left',
                    field: 'bankCard'
                },
                {
                    title: '开户行',
                    align: 'left',
                    field: 'bankName'
                },
                {
                    title: '申请人',
                    align: 'left',
                    field: 'regUserName'
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'createTime'
                },
                {
                    title: '客户',
                    align: 'left',
                    field: 'custAbbr'
                }


            ]
        };

        $.table.init(options);
    });

    //审核
    function payOff(id,type) {
        let url = prefix + "/payOff?id=" + id;
        if(type == 0){
            $.modal.open("预付款核销" , url,800,550);
        }else if(type == 1){
            $.modal.open("应付款核销" , url,800,550);
        }else if(type == 2){
            $.modal.open("承兑差额核销" , url,800,550);
        }

    }

    function prints(id){
        let iframe = document.getElementById("print-frame");
        if (!iframe) {
            iframe = document.createElement('IFRAME');
            iframe.id = "print-frame"
            document.body.appendChild(iframe);
            iframe.setAttribute('style', 'display:none;');
        }
        let carrierId = $("#carrierId").val();
        let amount = $("#amount").val();
        let payType = $("#payType").val();
        console.log(carrierId);
        iframe.src = prefix + "/applyPrint?id="+id;
        iframe.onload = function () { //解决图片显示不了的问题
            iframe.contentWindow.focus();
            iframe.contentWindow.print();
        };
    }

    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }

    function getValue(val){
        if(val == null){
            val = "/";
        }
        return val
    }

    function add(){
        let backLedgerId = $("#backLedgerId").val();
        let url = prefix + "/prepayment?id=" + backLedgerId;
        $.modal.open("预付登记" , url,500,480);
    }

    function addPay(){
        let backLedgerId = $("#backLedgerId").val();
        let url = prefix + "/prepaymentPay?id=" + backLedgerId;
        $.modal.open("应付登记" , url,500,480);
    }

    function payRecord(id){
        var height = document.documentElement.clientHeight - 50;
        var width = document.documentElement.clientWidth - 320;
        var url =  prefix + "/payRecord?id="+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "付款记录",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    function remove(id){
        $.modal.confirm("确认要删除选中的申请单吗?", function() {
            $.operate.submit(prefix + "/deletePayApply?id="+id, "post", "json", {});
        });
    }


</script>


</body>
</html>