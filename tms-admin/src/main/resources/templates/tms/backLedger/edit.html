<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：修改结算方式')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .col-xs-4{
        line-height: 26px;
        text-align: right;
    }

    .fcff{
        color: #ff1f1f;
    }


    #buttonList>button{
        margin-right: 4px;
    }

    .border+.border{
        border-top: none;
    }
    .border>div{
        border: .5px solid #333;
        padding: 2px 4px;
        line-height: 26px;
    }

    .dropdown-menu{
        left:0px !important;
    }
    .custDiv{
        width: 50% !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate" >


        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>客户：</label>
                    <div class="col-xs-6 custDiv" >
                        <input type="hidden" id="id" name="id" th:value="${backLedger.id}">
                        <input type="hidden" id="borrowAmount" name="borrowAmount" th:value="${backLedger.borrowAmount}">
                        <input name="custAbbr" id="custAbbr" class="form-control" type="text" minlength="1" maxlength="30" autocomplete="off" required th:value="${backLedger.custAbbr}" disabled>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>承运商：</label>
                    <div class="col-xs-6 custDiv">
                        <input name="carrName" id="carrName" class="form-control" type="text" minlength="1" maxlength="30" autocomplete="off" required th:value="${backLedger.carrName}" disabled>
                    </div>
                </div>
            </div>
        </div>
        <!--<div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">应收运费：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input id="receiveAmount" name="receiveAmount"  class="form-control" type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="false" onchange="calPayAmount()" th:value="${backLedger.receiveAmount}">
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>
        </div>-->
        <input id="receiveAmount" name="receiveAmount"  class="form-control" type="hidden" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="false" onchange="calPayAmount()" th:value="${backLedger.receiveAmount}">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>运费比例：</label>
                    <div class="col-xs-6">
                        <input id="payRate" name="payRate"  class="form-control" type="text" min="0" max="1" oninput="$.numberUtil.onlyNumberCustom(this,1,0,6,4)" maxlength="10" autocomplete="off" onchange="calPayAmount()" th:value="${backLedger.payRate}" required>
                    </div>
                </div>
            </div>
        </div>
        <input id="payAmount" name="payAmount"  class="form-control" type="hidden" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="off" th:value="${backLedger.payAmount}">
        <!--<div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">应付运费：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input id="payAmount" name="payAmount"  class="form-control" type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="off" th:value="${backLedger.payAmount}">
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>
        </div>-->
        <!--<div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">应收装卸费：</label>
                    <div class="col-xs-6"  style="display:inline-table;">
                        <input id="receiveZxfAmount" name="receiveZxfAmount"  class="form-control" type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="off" onchange="calPayZxfAmount()" th:value="${backLedger.receiveZxfAmount}">
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>
        </div>-->
        <input id="receiveZxfAmount" name="receiveZxfAmount"  class="form-control" type="hidden" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="off" onchange="calPayZxfAmount()" th:value="${backLedger.receiveZxfAmount}">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>装卸费比例：</label>
                    <div class="col-xs-6">
                        <input id="payZxfRate" name="payZxfRate"  class="form-control" type="text" min="0" max="1" oninput="$.numberUtil.onlyNumberCustom(this,1,0,6,4)" maxlength="10" autocomplete="off" onchange="calPayZxfAmount()"  th:value="${backLedger.payZxfRate}" required>
                    </div>
                </div>
            </div>
        </div>
        <input id="payZxfAmount" name="payZxfAmount"  class="form-control" type="hidden" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="off" th:value="${backLedger.payZxfAmount}">
        <!--<div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">应付装卸费：</label>
                    <div class="col-xs-6"  style="display:inline-table;">
                        <input id="payZxfAmount" name="payZxfAmount"  class="form-control" type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);|" maxlength="10" autocomplete="off" th:value="${backLedger.payZxfAmount}">
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>
        </div>-->
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">备注：</label>
                    <div class="col-xs-6">
                        <textarea class="form-control" name="remark" rows="3">[[${backLedger.remark}]]</textarea>
                    </div>
                </div>
            </div>
        </div>
        </br>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group" style="background-color: #fff8eb">
                    <label class="col-xs-12" style="padding-top: 20px;padding-left: 20px;padding-right: 20px;padding-bottom: 20px">提示：运费比例和装卸费比例必填（例：50%填入0.5）。应收运费、应付运费、应收装卸费、应付装卸费将根据开票金额和比例进行计算。</label>
                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    $(function() {

    })

    function calPayAmount(){
        let receiveAmount = $("#receiveAmount").val();
        let payRate = $("#payRate").val();
        let payAmount = Number(receiveAmount)*Number(payRate);
        $("#payAmount").val(handleCutZero(payAmount.toFixed(2)));
    }

    function calPayZxfAmount(){
        let receiveZxfAmount = $("#receiveZxfAmount").val();
        let payZxfRate = $("#payZxfRate").val();
        let payZxfAmount = Number(payZxfRate)*Number(receiveZxfAmount);
        $("#payZxfAmount").val(handleCutZero(payZxfAmount.toFixed(2)));
    }



    //提交
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-carrier-edit").serializeArray();
            $.operate.save(prefix + "/editBackLedger", data);
        }
    }

    function handleCutZero(num) {
        //拷贝一份 返回去掉零的新串
        let newstr = num;
        //循环变量 小数部分长度
        let leng = num.length - num.indexOf('.') - 1;
        //判断是否有效数
        if (num.indexOf('.') > -1) {
            //循环小数部分
            for (let i = leng; i > 0; i--) {
                //如果newstr末尾有0
                if (
                    newstr.lastIndexOf('0') > -1 &&
                    newstr.substr(newstr.length - 1, 1) == 0
                ) {
                    let k = newstr.lastIndexOf('0');
                    //如果小数点后只有一个0 去掉小数点
                    if (newstr.charAt(k - 1) == '.') {
                        return newstr.substring(0, k - 1);
                    } else {
                        //否则 去掉一个0
                        newstr = newstr.substring(0, k);
                    }
                } else {
                    //如果末尾没有0
                    return newstr;
                }
            }
        }
        return num;
    }


</script>
</body>

</html>