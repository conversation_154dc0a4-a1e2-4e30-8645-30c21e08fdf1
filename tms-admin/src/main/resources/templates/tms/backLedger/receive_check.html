<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：修改结算方式')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .col-xs-4{
        line-height: 26px;
        text-align: right;
    }

    .fcff{
        color: #ff1f1f;
    }


    #buttonList>button{
        margin-right: 4px;
    }

    .border+.border{
        border-top: none;
    }
    .border>div{
        border: .5px solid #333;
        padding: 2px 4px;
        line-height: 26px;
    }


    .custDiv{
        width: 50% !important;
    }

    label.invalid {
        background-color: rgb(237, 85, 101);
        color: #fff;
        line-height: 26px;
        height: 26px;
        font-size: 12px;
        padding: 0 5px;
    }

</style>
<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate" >

        <input type="hidden" id="billId" name="billId" th:value="${id}">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>收款日期：</label>
                    <div class="col-xs-8" >
                        <input type="text" class="form-control" id="receiveDate" name="receiveDate" autocomplete="off" required>
                    </div>
                </div>
            </div>

            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>收款方式：</label>
                    <div class="col-xs-8" >
                        <select id="receiveMethod" name="receiveMethod" class="form-control" onchange="changeReceivableMethod()"
                                th:with="type=${@dict.getType('receivable_method')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"  th:value="${dict.dictValue}" ></option>
                        </select>
                    </div>
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4" ><span class="fcff">*</span>收款金额：</label>
                    <div class="col-xs-8" style="display:inline-table;">
                        <input id="receiveAmount" name="receiveAmount"  class="form-control" type="text" min="0"
                               oninput="$.numberUtil.onlyNumber(this);changeReceivableAmount()" maxlength="10" autocomplete="off" required>
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>

            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>转入账户：</label>
                    <div class="col-xs-8" style="display:inline-table;">
                        <div class="input-group">
                            <input name="accountName" id="accountName" required class="form-control valid"
                                   type="text" aria-required="true" >
                            <!--账户id-->
                            <input name="inAccount" id="inAccount"  class="form-control valid"
                                   type="hidden" aria-required="true" >
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-left" role="menu" >
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" id="draftDiv" style="display:none">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>票据号：</label>
                    <div class="col-xs-8" >
                        <!--                                        <input type="text" id="ticketNumber" name="ticketNumber" placeholder="票据号" class="form-control valid">-->
                        <div class="input-group">
                            <input class="form-control" type="text" id="ticketNumber" name="ticketNumber" placeholder="票据号">
                            <input class="form-control" type="hidden" id="draftId" name="draftId">
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-right" role="menu" >
                                </ul>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>承兑类型：</label>
                    <div class="col-xs-8">
                        <select name="billTypeId" id="billTypeId" class="form-control"
                                th:with="bill=${@dict.getType('draft_bill_type')}">
                            <option value=""></option>
                            <option th:each="billDict : ${bill}" th:text="${billDict.dictLabel}"  th:value="${billDict.dictValue}" ></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>承兑金额：</label>
                    <div class="col-xs-8">
                        <span id="amountMoneySpan">0元（原汇票金额）</span> +
                        <span id="amountMoneyAddSpan">0元</span>
                    </div>
                </div>
            </div>


            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4" ><span class="fcff">*</span>出票日期：</label>
                    <div class="col-xs-8" >
                        <input name="issueDate" id="issueDate" placeholder="请输入出票日" class="form-control"
                               type="text" maxlength="50" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>到期日期：</label>
                    <div class="col-xs-8" >
                        <input name="dueDate" id="dueDate" placeholder="请输入到期日" class="form-control"
                               type="text" maxlength="50" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4" ><span class="fcff">*</span>付票单位：</label>
                    <div class="col-xs-8">
                        <select name="payCompany" id="payCompany" class="form-control" onchange="changePayCompany()">
                            <option value=""></option>
                            <option th:each="custBilling : ${custBillings}"
                                    th:data-bank="${custBilling.bank}"
                                    th:text="${custBilling.billingPayable}"
                                    th:value="${custBilling.billingPayable}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">付票行：</label>
                    <div class="col-xs-8">
                        <input name="issuingBank" id="issuingBank" placeholder="付票行" class="form-control"
                               type="text" maxlength="50" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-2" style="text-align:right">备注：</label>
                    <div class="col-xs-10" style="display:inline-table;">
                        <textarea id="remark" name="remark"  class="form-control" autocomplete="off" rows="5"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    let customerId = [[${backLedger.customerId}]]
    let billingCorp = [[${billingCorp}]]


    let backLedger = [[${backLedger}]];
    let carrBankList = [[${carrBankList}]];

    $(function() {
        $("#form-carrier-edit").validate({
            focusCleanup: true
        });


        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#receiveDate',
                type: 'datetime',
                trigger: 'click',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours=now.getHours();
                    this.dateTime.minutes=now.getMinutes();
                    this.dateTime.seconds=now.getSeconds();
                }
            });

            laydate.render({
                elem: '#issueDate',
                type: 'datetime',
                trigger: 'click'
            });
            laydate.render({
                elem: '#dueDate',
                type: 'datetime',
                trigger: 'click'
            });

        });

        /**
         * 关键字提示查询 转入账户
         */
        $("#accountName").bsSuggest('init', {
            url: ctx + "finance/account/findAccount?paymentType=0&keyword=",
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: ["accountName"],
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
            $("#accountName").val(data.accountName);
            $("#inAccount").val(data.accountId);
        });

        initBsSuggest()

    });


    function bankChange(){
        let carrBankId = $("#carrBankId").val();
        for(let i = 0 ; i < carrBankList.length ; i++){
            let carrBank = carrBankList[i];
            if(carrBank.carrBankId == carrBankId){
                $("#bankAccount").val(carrBank.bankAccount);
                $("#bankCard").val(carrBank.bankCard);
                $("#bankName").val(carrBank.bankName);
            }
        }
    }

    function changePayCompany() {
        var selectedOption = $('#payCompany').find('option:selected');
        var bankValue = selectedOption.data('bank');
        $('#issuingBank').val(bankValue);
    }

    function changeReceivableMethod() {
        let receiveMethod = $("#receiveMethod").val();
        if (receiveMethod == 10) {
            $('#draftDiv').show()
        }else {
            $('#draftDiv').hide()
        }
    }

    function changeReceivableAmount() {
        let val = $("#receiveAmount").val();
        $("#amountMoneyAddSpan").text(val + "元");
    }

    function initBsSuggest() {
        $(`#ticketNumber`).bsSuggest('init', {
            url: ctx + `finance/draft/listByTicketNumber?balaCustomerId=${customerId}&billingCorp=${billingCorp}&keyword=`,
            indexId: 0,
            showBtn: false,
            allowNoKeyword: false,
            getDataMethod: "url",
            keyField: "id",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
            effectiveFields: [
                "ticketNumber",

                "billType",
                // "issueDate",
                // "dueDate",
                "payCompany",
                // "issuingBank"
                "balaCorp"
            ],
            effectiveFieldsAlias: {
                "ticketNumber": "票号",
                "billType": "承兑类型",
                // "issueDate": "出票日期",
                // "dueDate": "到期日期",
                "payCompany": "付票单位",
                // "issuingBank": "付票行"
                "balaCorp": "结算公司"
            },
            delay: 300,
            searchingTip: '搜索中...',
            hideOnSelect: true,
            maxOptionCount: 10,
            inputWarnColor: '',
        }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参
            $(`#draftId`).val(data.id);
            $(`#ticketNumber`).val(data.ticketNumber);
            $(`#billTypeId`).val(data.billTypeId);
            $(`#issueDate`).val(data.issueDate);
            $(`#dueDate`).val(data.dueDate);
            $(`#payCompany`).val(data.payCompany);
            $(`#issuingBank`).val(data.issuingBank);

            $("#amountMoneySpan").text(data.amountMoney + "元（原汇票金额）");
            // $("#form-batchRece-add").valid();
        })


        $('#ticketNumber').on('keyup', function() {
            $('#draftId').val('');

            $("#amountMoneySpan").text("0元（原汇票金额）");
        });
        // $('#userName').on('blur', function() {
        //     if ($('#userId').val() === '') {
        //         $('#userName').val('');
        //     }
        // });

    }

    $("#form-carrier-edit").validate({
        errorClass:"invalid",
        rules:{
            ticketNumber: {
                required: function() {
                    return $("#receiveMethod").val() == "10";
                }
            },
            billTypeId: {
                required: function() {
                    return $("#receiveMethod").val() == "10";
                }
            },
            issueDate: {
                required: function() {
                    return $("#receiveMethod").val() == "10";
                }
            },
            dueDate: {
                required: function() {
                    return $("#receiveMethod").val() == "10";
                }
            },
            payCompany: {
                required: function() {
                    return $("#receiveMethod").val() == "10";
                }
            },
        },
        messages: {
            ticketNumber: {
                required: "当收款方式为承兑时，票据号为必填"
            },
            billTypeId: {
                required: "当收款方式为承兑时，承兑类型为必填"
            },
            issueDate: {
                required: "当收款方式为承兑时，出票日期为必填"
            },
            dueDate: {
                required: "当收款方式为承兑时，到期日期为必填"
            },
            payCompany: {
                required: "当收款方式为承兑时，付票单位为必填"
            },
        },
        focusCleanup: true
    });



    //提交
    function submitHandler() {
       if ($.validate.form()) {
            var data = $("#form-carrier-edit").serializeArray();
            $.operate.save(prefix + "/saveReceiveCheck", data);
       }

    }


</script>
</body>

</html>