<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：修改结算方式')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .col-xs-4{
        line-height: 26px;
        text-align: right;
    }

    .fcff{
        color: #ff1f1f;
    }


    #buttonList>button{
        margin-right: 4px;
    }

    .border+.border{
        border-top: none;
    }
    .border>div{
        border: .5px solid #333;
        padding: 2px 4px;
        line-height: 26px;
    }

    .dropdown-menu{
        left:0px !important;
    }
    .custDiv{
        width: 50% !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate" >
        <input type="hidden" name="id" th:value="${id}">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff">*</span>开票日期：</label>
                    <div class="col-xs-7" >
                        <input  id="billingDate" name="billingDate" th:value="${#dates.format(backLedgerBilling.billingDate, 'yyyy-MM-dd HH:mm:ss')}" type="text" class="form-control"autocomplete="off" required>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-2" style="text-align: right"><span class="fcff">*</span>发票号：</label>
                    <div class="col-xs-9" >
                        <textarea name="billNo" maxlength="500" th:text="${backLedgerBilling.billNo}" class="form-control valid" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>

       <div class="row">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-2" style="text-align: right">开票附件：</label>
                    <div class="col-xs-9" style="display:inline-table;">
                        <input name="fileId" id="fileId" class="form-control" type="file" multiple>
                        <input type="hidden" id="tid" name="tid" th:value="${backLedgerBilling.tid}">
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    let custBillings = [[${custBillings}]];
    let files = [[${files}]];

    $(function() {
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#billingDate',
                type: 'datetime',
                trigger: 'click',
                ready: function (date) {
                    var now = new Date();
                    this.dateTime.hours=now.getHours();
                    this.dateTime.minutes=now.getMinutes();
                    this.dateTime.seconds=now.getSeconds();
                }
            });
        });

        //图片功能初始化
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: "file"
        };
        $.file.loadEditFiles("fileId", "tid",files, picParam);
        $('#fileId').on('filesuccessremove', function(event, key, index) {
            $('#fileId').fileinput('clear');
            $('#tid').val('');
        });
    })


    //提交
    function submitHandler() {
        if ($.validate.form()) {
            if ($('#fileId').fileinput('getFilesCount') > 0) {
                jQuery.subscribe("cmt", commit);
                $('#fileId').fileinput('upload');
            } else {
                commit()
            }
        }
    }

    function commit(){
        var data = $("#form-carrier-edit").serializeArray();
        $.operate.save(prefix + "/saveConfirmBilling", data);
    }


</script>
</body>

</html>