<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细申请审核')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="vbillno" class="form-control" type="text" placeholder="请输入单号"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="custAbbr" class="form-control" type="text" placeholder="请输入客户"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="carrName" class="form-control" type="text" placeholder="请输入承运商"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <!--                            <label class="col-sm-2">创建时间：</label>-->
                            <div class="col-sm-12">
                                <input type="text" placeholder="创建开始时间" style="width: 45%; float: left;" class="form-control"
                                       id="startDate"  name="params[startDate]">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" placeholder="创建结束时间" style="width: 45%; float: left;" class="form-control"
                                       id="endtDate"  name="params[endtDate]">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="add()" shiro:hasPermission="tms:backLedger:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-danger" onclick="$.operate.removeAll()" shiro:hasPermission="tms:backLedger:delete">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:backLedger:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    //开票金额合计
    var ysyf = 0;

    var yfzje = 0;

    var yfk = 0;
    var yfye = 0;

    $(function () {

        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'date',
                trigger: 'click'
            });
            laydate.render({
                elem: '#endtDate',
                type: 'date',
                trigger: 'click'
            });

        });
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: ctx + "backLedger/list",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            showToggle: false,
            showColumns: true,
            clickToSelect:true,
            height: 560,
            showFooter:true,
            uniqueId: "id",
            modalName: "背靠背台账",
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onPostBody: function () {
                clearTotal();
                //合并页脚
                merge_footer();
                getAmountCount();
            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "应收总金额：<nobr id='ysyf'>¥0.00</nobr>" +

                            " 应收：<nobr id='zys'>¥0.00</nobr>" +
                            " 已收：<nobr id='zhs'>¥0.00</nobr>" +
                            " 未收：<nobr id='zws'>¥0.00</nobr>" +

                            " 应付总金额：<nobr id='yfzje'>¥0.00</nobr>" +

                            " 已付款金额：<nobr id='yfk'>¥0.00</nobr>" +
                            " 应付余额：<nobr id='yfye'>¥0.00</nobr>" +
                            "<br>" +
                            "总合计：应收总金额：<nobr id='ysyfTotal'></nobr>" +

                            " 总应收：<nobr id='zysTotal'></nobr>" +
                            " 总已收：<nobr id='hasReceiveTotal'></nobr>" +
                            " 总未收：<nobr id='noReceiveTotal'></nobr>" +

                            " 应付总金额：<nobr id='yfzjeTotal'></nobr>" +
                            " 已付款金额：<nobr id='yfkTotal'></nobr>" +
                            " 应付余额：<nobr id='yfyeTotal'>¥0.00</nobr>";
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    field: 'id',
                    formatter: function (value, row, index) {
                        var actionsT = [];
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:backLedger:edit')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="修改" onclick="edit(\'' + row.id + '\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:backLedger:prepayment')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="付款申请" onclick="prepayment(\'' + row.id + '\')"><i class="fa fa-ticket" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:backLedger:addInvoice')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="收款开票" onclick="addInvoice(\'' + row.id + '\')"><i class="fa fa-handshake-o" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:backLedger:carrierInvoice')}]] != "hidden") {
                            actionsT.push('<a class="btn btn-xs  " href="javascript:void(0)" title="承运商收票" onclick="carrierInvoice(\'' + row.id + '\')"><i class="fa fa-map" style="font-size: 15px;"></i></a>');
                        }
                        return actions.join('')+'<div class="btn-group btn-xs">' +
                            '<div class="dropdownpad" data-toggle="dropdown">' +
                            '<i class="fa fa-angle-down"></i>' +
                            '</div>' +
                            '<ul class="dropdown-menu">' + actionsT.join('') +
                            '</ul>' +
                            '</div>';
                    }
                },
                {
                    field: 'vbillno',
                    title: '单号',
                    align: 'left'
                },
                {
                    field: 'custAbbr',
                    title: '客户简称',
                    align: 'left'
                },
             /*   {
                    title: '应收运费',
                    align: 'right',
                    field: 'receiveAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }
                    }
                },
                {
                    title: '应收装卸费',
                    align: 'right',
                    field: 'receiveZxfAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }
                    }
                },*/
                {
                    title: '应收总金额',
                    align: 'right',
                    field: 'receiveAmount',
                    formatter: function (value, row, index) {
                        let amount = 0;
                        if(value != null){
                            amount += value;
                        }
                        if(row.receiveZxfAmount != null){
                            amount += row.receiveZxfAmount;
                        }
                        return amount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '已开票',
                    align: 'right',
                    field: 'hasBillingAmount',
                    formatter: function (value, row, index) {
                        let html = '';
                        if(value != null){
                            html += `<span>`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                        }else{
                            html += `<span>-</span>`;
                        }
                        if(row.notBillingAmount != 0 && row.notBillingAmount){
                            html +='<span data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="申请未开票金额：'+row.notBillingAmount+'元">  <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px;color:#f7a54a" data-html="true" data-container="body"></i></span>'
                        }
                        return html;
                    }
                }, {
                    title: '已收款',
                    align: 'right',
                    field: 'hasReceiveAmount',
                    formatter: function (value, row, index) {

                        if(value != null){
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }

                    }
                },
                {
                    title: '承兑差额',
                    align: 'right',
                    field: 'payBackAmount',
                    formatter: function (value, row, index) {
                        let html = '';
                        if(value != null){
                            html += `<span>`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                        }else{
                            return '-';
                        }
                        if(row.applyNotPayAmountCd != 0 && row.applyNotPayAmountCd){
                            html +='<span data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="申请未付款金额：'+row.applyNotPayAmountCd+'元">  <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px;color:#f7a54a" data-html="true" data-container="body"></i></span>'
                        }
                        return html;
                    }
                },
                {
                    title: '应收款余额',
                    align: 'right',
                    field: 'hasReceiveAmount',
                    formatter: function (value, row, index) {
                        let amount = 0;
                        if(row.receiveAmount != null){
                            amount += row.receiveAmount;
                        }
                        if(row.receiveZxfAmount != null){
                            amount += row.receiveZxfAmount;
                        }
                        if(row.payBackAmount != null){
                            amount += row.payBackAmount;
                        }
                        if(row.hasReceiveAmount != null){
                            amount -= row.hasReceiveAmount;
                        }
                        return amount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '承运商',
                    align: 'left',
                    field: 'carrName'
                },
                {
                    title: '应付总金额',
                    align: 'right',
                    field: 'payAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            if(row.payZxfAmount != null){
                               value += row.payZxfAmount;
                            }
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }
                    }
                },
                /*{
                    title: '应付装卸费',
                    align: 'right',
                    field: 'payZxfAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }
                    }
                },*/
                {
                    title: '已付款金额',
                    align: 'right',
                    field: 'hasPayAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }
                    }
                },
                {
                    title: '应付余额',
                    align: 'right',
                    field: 'payLessAmount',
                    formatter: function (value, row, index) {
                        let html = '';
                        if(value != null){
                            if(value < 0 ){
                                html+= `<span style="color:red">`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                            }else{
                                html += `<span>`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                            }
                        }else{
                            html+= '-';
                        }
                        if(row.applyNotPayAmount != 0 && row.applyNotPayAmount){
                            html += '<span data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="申请未付款金额：'+row.applyNotPayAmount+'元">  <i class="fa fa-question-circle" data-toggle="tooltip" style="font-size: 15px;color:#f7a54a" data-html="true" data-container="body"></i></span>'
                        }
                        return html;
                    }
                },
                {
                    title: '收票金额',
                    align: 'right',
                    field: 'invoiceAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }
                    }
                },
                {
                    title: '备注',
                    align: 'left',
                    field: 'remark',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '创建人',
                    align: 'left',
                    field: 'regUserName'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'createTime'
                }

            ]
        };

        $.table.init(options);
    });

    //审核
    function payOff() {
        var status = $.table.selectColumns("status").join();
        if(status != 0){
            $.modal.alertWarning("请选择新建的申请进行核销");
            return;
        }
        var id = $.table.selectColumns("id").join();
        var url = prefix + "/payOff?id="+id;
        $.modal.open('承运商预付核销',url,500,700);
    }

    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }

    function getValue(val){
        if(val == null){
            val = "/";
        }
        return val
    }

    function add(){
        let url = prefix + "/add";
        $.modal.open("新增台账" , url,500,480);
    }

    function addInvoice(id){

        var url = prefix + "/invoiceView?id=" + id;
        $.modal.openTab("应收记录", url);


       /* let url = prefix + "/addInvoice?id=" + id;
        $.modal.open("开票登记" , url,800,650);*/
    }

    function prepayment(id){
        var url = prefix + "/prepaymentView?id=" + id;
        $.modal.openTab("付款记录", url);
    }

    function edit(id){
        let url = prefix + "/edit?id=" + id;
        $.modal.open("修改台账" , url,500,480);
    }

    function carrierInvoice(id){
        var url = prefix + "/carrierInvoice?id=" + id;
        $.modal.openTab("承运商收票", url);
    }

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;
                if (result.code == 0 && data != undefined) {
                    $("#ysyfTotal").text((data.RECEIVE_AMOUNT+data.RECEIVE_ZXF_AMOUNT).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

                    $("#zysTotal").text((data.RECEIVE_ZXF_AMOUNT+data.RECEIVE_AMOUNT+data.PAYBACKAMOUNT).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#hasReceiveTotal").text(data.HASRECEIVEAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#noReceiveTotal").text((data.RECEIVE_ZXF_AMOUNT+data.RECEIVE_AMOUNT+data.PAYBACKAMOUNT-data.HASRECEIVEAMOUNT).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#yfzjeTotal").text(data.PAY_AMOUNT_TOTAL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

                    $("#yfkTotal").text(data.HASPAYAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#yfyeTotal").text(data.PAY_LESS_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        ysyf = 0;

        zys = 0;
        zhs = 0;
        zws = 0;
        yfzje = 0;
        yfk = 0;
        yfye = 0;
    }

    function addTotal(row) {
        ysyf =ysyf + row.receiveAmount + row.receiveZxfAmount;
        zys = zys + row.receiveAmount + row.receiveZxfAmount + row.payBackAmount;
        zhs += row.hasReceiveAmount;
        zws = zys - zhs;
        yfzje = yfzje + row.payAmount +row.payZxfAmount;
        yfk += row.hasPayAmount;
        yfye += row.payLessAmount;
    }


    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#ysyf").text(ysyf.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#zys").text(zys.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#zhs").text(zhs.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#zws").text(zws.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#yfzje").text(yfzje.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#yfk").text(yfk.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#yfye").text(yfye.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }

    function subTotal(row) {
        ysyf =ysyf - row.receiveAmount - row.receiveZxfAmount;

        zys = zys - row.receiveAmount - row.receiveZxfAmount -row.payBackAmount;
        zhs -= row.hasReceiveAmount;
        zws = zys - zhs;
        yfzje = yfzje - row.payAmount - row.payZxfAmount;
        yfk -= row.hasPayAmount;
        yfye -= row.payLessAmount;
    }

    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }
</script>


</body>
</html>