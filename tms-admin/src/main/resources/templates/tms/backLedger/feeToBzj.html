<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反确认')"/>
</head>

<body>
<div class="form-content">
    <form id="form-invoice-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="prepaymentId" name="prepaymentId" type="hidden" th:value="${id}">
            <div class="panel-body">
                <!--基础信息 begin-->
                <div class="row">
                    <div class="col-md-10 col-sm-10">
                        <div class="form-group">
                            <label class="col-xs-4">可用现金余额：</label>
                            <div class="col-xs-6">
                                [[${allowAmount}]]
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10 col-sm-10">
                        <div class="form-group">
                            <label class="col-xs-4">运费转保证金金额：</label>
                            <div class="col-xs-6">
                                <input name="payAmount" id="payAmount" th:oninput="|$.numberUtil.onlyNumber(this);changeEnt(this,'num')|"  class="form-control" type="text"  autocomplete="off" required placeholder="请输入金额"  >
                            </div>
                        </div>
                    </div>
                </div>


            </div>

        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var prefix = ctx + "backLedger";



    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-invoice-unconfirm").serializeArray();
            $.operate.save(prefix + "/feeToBzjSave", data);
        }
    }


</script>
</body>
</html>