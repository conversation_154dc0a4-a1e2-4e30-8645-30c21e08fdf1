<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反确认')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-invoice-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="id" name="id" type="hidden" th:value="${id}">
            <div class="panel-body">
                <!--基础信息 begin-->
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-sm-5">撤销说明：</label>
                            <div class="col-sm-12">
                                <textarea name="unconfirmMemo" id="unconfirmMemo" class="form-control" type="text"
                                          maxlength="200"  aria-required="true" required></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    $(function() {

    })


    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            commit()
        }
    }

    function commit() {
        let query = $("#form-invoice-unconfirm").serializeArray();
        layer.confirm('确定进行反核销操作？', {
            btn: ['确认','取消'] //按钮
        }, function(){
            //操作完成后,trace页面的条件还需要带着一起查询    不是直接刷新父页面
            $.operate.save(prefix + "/saveBackConfirmPay", query);
        }, function(){

        });
    }


</script>
</body>
</html>