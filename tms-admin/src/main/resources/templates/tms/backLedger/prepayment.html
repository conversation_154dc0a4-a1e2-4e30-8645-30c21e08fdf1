<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('基础数据-承运商：修改结算方式')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style type="text/css">
    .table td {
        position: relative
    }
    .col-xs-4{
        line-height: 26px;
        text-align: right;
    }

    .fcff{
        color: #ff1f1f;
    }


    #buttonList>button{
        margin-right: 4px;
    }

    .border+.border{
        border-top: none;
    }
    .border>div{
        border: .5px solid #333;
        padding: 2px 4px;
        line-height: 26px;
    }

    .dropdown-menu{
        left:0px !important;
    }
    .custDiv{
        width: 50% !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate" >

        <input type="hidden" id="backLedgerId" name="backLedgerId" th:value="${backLedger.id}">
        <input type="hidden" id="type" name="type" th:value="${type}">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff zhClass">*</span>付款公司：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <select name="billingCorp" id="billingCorp" class="form-control valid" th:with="type=${@dict.getType('bala_corp')}" required>
                            <option value=""></option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4" th:if="${type == 0}"><span class="fcff">*</span>预付金额：</label>
                    <label class="col-xs-4" th:if="${type == 1}"><span class="fcff">*</span>付款金额：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input id="payAmount" name="payAmount"  class="form-control" type="text"   th:oninput="|$.numberUtil.onlyNumberNegative(this);|" maxlength="20" autocomplete="off" required>
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff ">*</span>付款方式：</label>
                    <div class="col-xs-6" >
                        <select id="payMethod" name="payMethod" class="form-control valid" required
                                th:with="type=${@dict.getType('pay_method')}" onchange="methodChange()">
                            <option value=""></option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                    th:value="${dict.dictValue}"></option>
                            <option value="98">运费转保证金</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff zhClass">*</span>付票单位：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <select class="form-control" id="billingId" name="billingId" onchange="billChange()" required>
                            <option value=""></option>
                            <option th:each="bill:${carrierBillings}" th:text="${bill.billingPayable}"
                                    th:value="${bill.id}"></option>
                        </select>
                        <input type="hidden" id="billingPayable" name="billingPayable">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4"><span class="fcff zhClass">*</span>收款人：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <select class="form-control" id="carrBankId" name="carrBankId" onchange="bankChange()" required>
                            <option value=""></option>
                            <option th:each="bank:${carrBankList}" th:text="${bank.bankAccount}"
                                    th:value="${bank.carrBankId}"></option>
                        </select>
                    </div>
                    <input type="hidden" id="bankAccount" name="bankAccount">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">收款账号：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input id="bankCard" name="bankCard"  class="form-control" type="text"  th:oninput="|$.numberUtil.onlyNumber(this);|"  autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">开户行：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <input id="bankName" name="bankName"  class="form-control" type="text"  th:oninput="|$.numberUtil.onlyNumber(this);|"  autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="form-group">
                    <label class="col-xs-4">付款事由：</label>
                    <div class="col-xs-6" style="display:inline-table;">
                        <textarea id="remark" name="remark"  class="form-control" autocomplete="off" rows="5"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>



<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: distpicker"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    let backLedger = [[${backLedger}]];
    let carrBankList = [[${carrBankList}]];
    let carrierBillings = [[${carrierBillings}]];

    $(function() {

    });


    function bankChange(){
        let carrBankId = $("#carrBankId").val();
        for(let i = 0 ; i < carrBankList.length ; i++){
            let carrBank = carrBankList[i];
            if(carrBank.carrBankId == carrBankId){
                $("#bankAccount").val(carrBank.bankAccount);
                $("#bankCard").val(carrBank.bankCard);
                $("#bankName").val(carrBank.bankName);
            }
        }
    }

    function billChange(){
        let billingId = $("#billingId").val();
        for(let i = 0 ; i < carrierBillings.length ; i++){
            let bill = carrierBillings[i];
            if(bill.id == billingId){
                $("#billingPayable").val(bill.billingPayable);
            }
        }
    }

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-carrier-edit").serializeArray();
            $.operate.save(prefix + "/savePrepayment", data);
        }
    }

    function methodChange(){
        var payMethod = $("#payMethod").val();
        $("#carrBankId").attr("required","true");
        $(".zhClass").css('display', 'inline');
        if( payMethod == 98){
            $("#carrBankId").removeAttr("required")
            $(".zhClass").css('display', 'none');
        }
    }


</script>
</body>

</html>