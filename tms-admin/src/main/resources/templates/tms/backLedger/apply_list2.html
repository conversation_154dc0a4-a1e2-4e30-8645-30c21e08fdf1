<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细申请审核')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
        min-width: 16px;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row ">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="vbillno" class="form-control" type="text" placeholder="请输入单号"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="bankAccount" class="form-control" type="text" placeholder="请输入收款人"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="custAbbr" class="form-control" type="text" placeholder="请输入客户简称"
                                       maxlength="30"  aria-required="true" >
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="type" class="form-control">
                                    <option value="">--申请类型--</option>
                                    <option value="0">预付款</option>
                                    <option value="1">应付款</option>
                                    <option value="2">承兑差额</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
          <!--  <a class="btn btn-primary" onclick="add()" shiro:hasPermission="tms:backLedger:payPreApply">
                <i class="fa fa-plus"></i> 预付款申请
            </a>
            <a class="btn btn-warning" onclick="addPay()" shiro:hasPermission="tms:backLedger:payApply">
                <i class="fa fa-plus"></i> 付款申请
            </a>-->
            <a class="btn btn-warning" onclick="$.table.exportExcel()">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "backLedger";

    var pay_method = [[${@dict.getType('pay_method')}]];//付款方式
    var bala_corp = [[${@dict.getType('bala_corp')}]];//付款方式
    //开票金额合计
    var sqje = 0;
    var yfje = 0;

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre();
            }
        });
        var options = {
            url: ctx + "backLedger/applyList",
            exportUrl: prefix + "/exportPay",
            showToggle: false,
            showColumns: true,
            clickToSelect:true,
            modalName: "付款核销",
            height: 560,
            showFooter:true,
            uniqueId: "id",
                onRefresh:function(params){
                    clearTotal();
                },
                onCheck: function (row,$element) {
                    addTotal(row);
                    setTotal();
                },
                onUncheck: function (row, $element) {
                    subTotal(row);
                    setTotal();
                },
                onCheckAll: function (rowsAfter) {
                    clearTotal();
                    //循环累加
                    for (var row of rowsAfter) {
                        addTotal(row);
                    }
                    //赋值
                    setTotal();
                },
                onUncheckAll: function () {
                    //总数清0
                    clearTotal();
                    //赋值
                    setTotal();
                },
                onPostBody: function () {
                    clearTotal();
                    //合并页脚
                    merge_footer();
                    getAmountCount();
                },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "申请金额：<nobr id='sqje'>¥0.00</nobr>" +
                            " 已付金额：<nobr id='yfje'>¥0.00</nobr>" +
                            "<br>" +
                            "总合计：申请金额：<nobr id='sqjeTotal'></nobr>" +
                            " 已付金额：<nobr id='yfjeTotal'></nobr>" ;
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    field: 'id',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if(row.payAmount != row.hasPayAmount){
                            if ([[${@permission.hasPermi('tms:backLedger:payPreCheck1')}]] != "hidden" && row.payMethod != 98) {
                                actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="预付核销" onclick="payOff(\'' + row.id + '\',' + row.type + ')"><i class="fa fa-gavel" style="font-size: 15px;"></i></a>');
                            }
                           /* if ([[${@permission.hasPermi('tms:backLedger:feeToBzj')}]] != "hidden" && row.type != 2) {
                                actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="运费转保证金" onclick="feeToBzj(\'' + row.id + '\',' + row.type + ')"><i class="fa fa-credit-card" style="font-size: 15px;"></i></a>');
                            }*/
                            if ([[${@permission.hasPermi('tms:backLedger:payPrint1')}]] != "hidden") {
                                actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="打印" onclick="prints(\'' + row.id + '\')"><i class="fa fa-print" style="font-size: 15px;"></i></a>');
                            }
                        }
                        if((row.hasPayAmount == null || row.hasPayAmount == 0) && row.checkStatus != 0){
                            if ([[${@permission.hasPermi('tms:backLedger:payRemove1')}]] != "hidden") {
                                actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="删除" onclick="remove(\'' + row.id + '\')"><i class="fa fa-times" style="font-size: 15px;"></i></a>');
                            }
                        }
                        return actions.join('');
                    }
                },
                {
                    field: 'vbillno',
                    title: '申请单号',
                    align: 'left'
                },
                {
                    field: 'type',
                    title: '申请类型',
                    align: 'left',
                    formatter: function (value, row, index) {
                       if(value == 0){
                           return '<span class="label label-danger">预付款</label>'
                       }else if(value == 1){
                           return '<span class="label label-warning">应付款</label>'
                       }else if(value == 2){
                           return '<span class="label label-info">承兑差额</label>'
                       }else{
                           return '-'
                       }
                    }
                },
                {
                    title: '申请金额',
                    align: 'right',
                    field: 'payAmount',
                    formatter: function (value, row, index) {
                        let html = '';
                        if(value != null){
                            html += value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else{
                            return '-';
                        }
                        let str = "";
                        for(let i = 0; i < row.feeToBZJChecks.length ; i++){
                            const element = row.feeToBZJChecks[i];
                            str += element.checkMan+" ";
                            str += element.checkDate+" ";
                            if(element.checkStatus == 1){
                                str += "审核通过"
                            }else{
                                str += "审核不通过"
                            }
                        }
                        if(row.checkStatus == 0){
                            html += '<span class="label label-info">审</label>';
                        }else  if(row.checkStatus == 1){
                            html += '<span class="label label-success" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="'+str+'" >过</label>';
                        }else  if(row.checkStatus == 2){
                            html += '<span class="label label-danger" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="'+str+'" >退</label>';
                        }
                        return html;
                    }
                },
                {
                    title: '已付金额',
                    align: 'right',
                    field: 'hasPayAmount',
                    formatter: function (value, row, index) {
                        if(value != null){
                            return `<a onclick="payRecord('`+row.id+`')">`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</a>`;
                        }else{
                            return '-';
                        }
                    }
                },
                {
                    title: '付款方式',
                    align: 'left',
                    field: 'payMethod',
                    formatter: function status(value, row, index) {
                        if(value == 98){
                            return "运费转保证金";
                        }
                        return $.table.selectDictLabel(pay_method, value);
                    }
                },
                {
                    title: '付款公司',
                    align: 'left',
                    field: 'billingCorp',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(bala_corp, value);
                    }
                },
                {
                    title: '付款单位',
                    align: 'left',
                    field: 'billingPayable'
                },
                {
                    title: '付款事由',
                    align: 'left',
                    field: 'remark',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '收款人',
                    align: 'left',
                    field: 'bankAccount'
                },
                {
                    title: '收款账户',
                    align: 'left',
                    field: 'bankCard'
                },
                {
                    title: '开户行',
                    align: 'left',
                    field: 'bankName'
                },
                {
                    title: '申请人',
                    align: 'left',
                    field: 'regUserName'
                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'createTime'
                },
                {
                    title: '客户',
                    align: 'left',
                    field: 'custAbbr'
                },
                {
                    title: '承运商',
                    align: 'left',
                    field: 'carrName'
                },
                {
                    title: '台账单号',
                    align: 'left',
                    field: 'backLedgerVbillno'
                }

            ]
        };

        $.table.init(options);
    });

    //审核
    function payOff(id,type) {
        let url = prefix + "/payOff?id=" + id;
        if(type == 0){
            $.modal.open("预付款核销" , url,800,550);
        }else if(type == 1){
            $.modal.open("应付款核销" , url,800,550);
        }else if(type == 2){
            $.modal.open("承兑差额核销" , url,800,550);
        }

    }

    function prints(id){
        let iframe = document.getElementById("print-frame");
        if (!iframe) {
            iframe = document.createElement('IFRAME');
            iframe.id = "print-frame"
            document.body.appendChild(iframe);
            iframe.setAttribute('style', 'display:none;');
        }
        let carrierId = $("#carrierId").val();
        let amount = $("#amount").val();
        let payType = $("#payType").val();
        console.log(carrierId);
        iframe.src = prefix + "/applyPrint?id="+id;
        iframe.onload = function () { //解决图片显示不了的问题
            iframe.contentWindow.focus();
            iframe.contentWindow.print();
        };
    }

    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }

    function getValue(val){
        if(val == null){
            val = "/";
        }
        return val
    }

    function add(){
        let backLedgerId = $("#backLedgerId").val();
        let url = prefix + "/prepayment?id=" + backLedgerId;
        $.modal.open("预付登记" , url,500,480);
    }

    function addPay(){
        let backLedgerId = $("#backLedgerId").val();
        let url = prefix + "/prepaymentPay?id=" + backLedgerId;
        $.modal.open("应付登记" , url,500,480);
    }

    function payRecord(id){
        var height = document.documentElement.clientHeight - 50;
        var width = document.documentElement.clientWidth - 320;
        var url =  prefix + "/payRecord?id="+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "付款记录",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    function remove(id){
        $.modal.confirm("确认要删除选中的申请单吗?", function() {
            $.operate.submit(prefix + "/deletePayApply?id="+id, "post", "json", {});
        });
    }

    /**
     * 获取所有数据金额合计
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: prefix + "/getPayCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                var data = result.data;
                if (result.code == 0 && data != undefined) {
                    $("#sqjeTotal").text(data.PAYAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#yfjeTotal").text(data.HASPAYAMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        sqje = 0;
        yfje = 0;

    }

    function addTotal(row) {
        sqje += row.payAmount;
        yfje += row.hasPayAmount;

    }

    function feeToBzj(id,type){
        let url = prefix + "/feeToBzj?id=" + id;
        $.modal.open("运费转保证金" , url,500,300);
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#sqje").text(sqje.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#yfje").text(yfje.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));

    }

    function subTotal(row) {
        sqje -= row.payAmount;
        yfje -= row.hasPayAmount;

    }

    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

</script>


</body>
</html>