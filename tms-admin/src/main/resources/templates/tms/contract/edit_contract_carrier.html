<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运合同修改')" />
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/bootstrap-icons.min.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/fileinput.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/all.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/jquery-editable-select/jquery-editable-select.min.css}" media="all" type="text/css"/>
</head>
<style>
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: left;
        margin-top: 5px;
    }
    .mt10{
        margin-top: 10px;
    }
    .ml10{
        margin-left: 10px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 80px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .infotitle{
        line-height: 30px;
        font-weight: bold;
    }
    .titlebg{
        background: #eff3f9;
        padding: 5px 10px;

    }
    a.del-alink {
        display: block;
        margin: 120px auto 0;
    }
    .bggray{
        background: #f7f8fa;
    }

    .dropdown-menu {
        width: 50% !important; /* 与输入框宽度一致 */
        left: 0 !important; /* 覆盖Bootstrap默认的left: 0 */
        right: auto;
    }

    .section-subtitle {
        font-size: 14px;
        color: #6c757d;
        margin: 10px 0;
        padding-left: 10px;
        border-left: 3px solid #007bff;
    }
    .section-divider {
        border-top: 1px solid #e9ecef;
        margin: 8px 0;
    }

</style>
<body>
<div class="form-content">
    <form id="form-contract-carrier-edit" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" id="id" name="contractCarrierList[0].id" th:value="${contractcarrier.id}">
        <!--要求提货日期-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="titlebg over">
                            <!--<div class="fl">
                                <a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow()" title="新增行">+</a>
                            </div>-->
                            <div class="fl fw ml10" style="line-height: 26px">承运合同录入</div>
                        </div>
                        <div class="over">
                            <!--<div class="fl" style="width: 40px">
                                <a class="close-link del-alink" style="background: #fd8481;border-radius: 50%" onclick="removeRowThree(this,0)" title="删除选择行">x</a>
                            </div>-->
                            <div class="fr" style="width: calc(100% - 50px)">
                                <div class="section-divider"></div>
                                <div class="section-subtitle">基础信息</div>

                                <div class="row no-gutter">
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 编号：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入合同编号" class="form-control" th:value="${contractcarrier.contractNumber}" name="contractCarrierList[0].contractNumber" id="contractNumber" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>

<!--                                    <div class="col-sm-2">-->
<!--                                        <div class="flex">-->
<!--                                            <label class="flex_left"> 合同类型：</label>-->
<!--                                            <div class="flex_right">-->
<!--                                                <div class="flex_right">-->
<!--                                                    <select name="contractCarrierList[0].contractType" id="contractType" class="form-control valid" aria-invalid="false"  aria-required="true" th:with="type=${@dict.getType('contract_type')}">-->
<!--                                                        <option value="">&#45;&#45; 请选择合同类型 &#45;&#45;</option>-->
<!--                                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${dict.dictValue == contractcarrier.contractType}"></option>-->
<!--                                                    </select>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 合同名称：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].contractName" id="contractName" placeholder="请输入合同名称" th:value="${contractcarrier.contractName}"  class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>


                                    <div class="col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"> 甲方：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].partyA" id="partyA" placeholder="请输入甲方名称" th:value="${contractcarrier.partyA}"  class="form-control" type="text" maxlength="50" autocomplete="off">
                                                <!--<input name="contractcarrierList[0].customerId" id="customerId0" type="hidden" th:value="${contractcarrier.customerId}">
                                                <div class="input-group-btn" style="width:1px;">
                                                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                                </div>-->
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"> 乙方：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].partyB" id="partyB" placeholder="请输入乙方名称" th:value="${contractcarrier.partyB}" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>




                                </div>

                                <div class="row no-gutter">
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 版本：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].contractVersion" id="contractVersion" placeholder="请输入版本" th:value="${contractcarrier.contractVersion}" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 期限：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入期限" class="form-control" th:value="${contractcarrier.deadline}"  name="contractCarrierList[0].deadline" id="deadline" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 总页数：</label>
                                            <div class="flex_right">
                                                <input oninput="this.value=this.value.replace(/\D/g,'').slice(0,6);"
                                                       type="text" placeholder="请输入总页数" class="form-control" th:value="${contractcarrier.totalPage}" name="contractCarrierList[0].totalPage" id="totalPage" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 生效日期：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].effectiveDate" id="effectiveDate0" th:value="${#dates.format(contractcarrier.effectiveDate, 'yyyy-MM-dd')}" placeholder="请输入生效日期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 终止日期：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].endDate" id="endDate0" th:value="${#dates.format(contractcarrier.endDate, 'yyyy-MM-dd')}" placeholder="请输入终止日期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 延期日期：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].extensionDate" id="extensionDate0" th:value="${#dates.format(contractcarrier.extensionDate, 'yyyy-MM-dd')}" placeholder="请输入延期日期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>


                                </div>

                                <div class="row no-gutter">
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 对账期：</label>
                                            <div class="flex_right">
                                                <input oninput="this.value=this.value.replace(/\D/g,'').slice(0,6);"
                                                       name="contractCarrierList[0].reconciliationPeriod" id="reconciliationPeriod" th:value="${contractcarrier.reconciliationPeriod}" placeholder="请输入对账期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 账期约定：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入结算账期约定" class="form-control" name="contractCarrierList[0].settlementPeriod" id="settlementPeriod" th:value="${contractcarrier.settlementPeriod}" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 特殊说明：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入特殊事项说明" class="form-control" th:value="${contractcarrier.specialNotes}" name="contractCarrierList[0].specialNotes" id="specialNotes" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left">长期有效：</label>
                                            <div class="flex_right" style="">
                                                <label class="radio-box">
                                                    <input type="radio" checked="" value="0" th:checked="${contractcarrier.longTermEffective == '0'}" id="longTermEffective0" name="contractCarrierList[0].longTermEffective">否
                                                </label>
                                                <label class="radio-box">
                                                    <input type="radio" value="1" th:checked="${contractcarrier.longTermEffective == '1'}" id="longTermEffective1" name="contractCarrierList[0].longTermEffective">是
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 备注 -->
                                <div class="row no-gutter">
                                    <div class="col-sm-12">
                                        <div class="flex">
                                            <label class="flex_left">备注：</label>
                                            <div class="flex_right">
                                                <textarea name="contractCarrierList[0].remark" id="remark" th:text="${contractcarrier.remark}" maxlength="200" class="form-control valid" rows="2"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="section-divider"></div>
                                <div class="section-subtitle">其他配置</div>
                                <div class="row mt10">
                                    <div class="col-sm-6">
                                        <div class="flex">
                                            <label class="" style="width: 120px;text-align: right;line-height: 30px;">信息是否已验证：</label>
                                            <div class="" style="">
                                                <label class="radio-box">
                                                    <input type="radio" th:checked="${contractcarrier.isDataComplete == 0}" value="0" id="isDataComplete0" name="contractCarrierList[0].isDataComplete">否
                                                </label>
                                                <label class="radio-box">
                                                    <input type="radio" th:checked="${contractcarrier.isDataComplete == 1}" value="1" id="isDataComplete1" name="contractCarrierList[0].isDataComplete">是
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="section-divider"></div>
                                <div class="section-subtitle">绑定承运商</div>
                                <div class="row mt10">
                                    <div class="col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"> 承运商：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].carrName" id="carrName" placeholder="请输入承运商名称" th:value="${contractcarrier.carrName}" class="form-control" type="text" maxlength="50" autocomplete="off">
                                                <input name="contractCarrierList[0].partyBId" id="partyBId0" type="hidden" th:value="${contractcarrier.partyBId}">
                                                <div class="input-group-btn" style="width:1px;">
                                                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 合同附件 -->
                                <div class="section-divider"></div>
                                <div class="section-subtitle">合同附件</div>
                                <div class="row no-gutter">
                                    <div class="col-sm-10">
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="layui-form">
                                            <div class="layui-form-item" style="margin-bottom:0px">
                                                <label class="layui-form-label">pdf压缩</label>
                                                <div class="layui-input-block">
                                                    <input type="checkbox" title="开启|关闭" lay-filter="switchTest"  lay-skin="switch">
                                                </div>
                                            </div>
                                        </div>
                                    </div>



                                    <div class="col-sm-12">
                                        <div class="flex">
                                            <label class="flex_left"> 合同附件: </label>
                                            <div class="flex_right">
                                                <!--<input id="file" type="file" multiple name="file">
                                                <input id="contractAttach" name="contractcarrierList[0].contractAttach" type="hidden">-->

                                                <input id="image" class="form-control" name="image" type="file" multiple>
                                                <input id="contractAttach" name="contractCarrierList[0].contractAttach" type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/fileinput.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/locales/zh.js}"></script>
<script th:src="@{/ajax/libs/jquery-editable-select/jquery-editable-select.js}"></script>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput/plugins/piexif.min.js}"></script>
<script th:src="@{'/js/wecom.js'}"></script>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>

<script defer th:src="@{/js/compressPDF/pdfkit-standalone-0.10.0.js}"></script>
<script defer th:src="@{/js/compressPDF/blob-stream-0.1.3.js}"></script>
<script th:src="@{/js/compressPDF/pdf.min-2.5.207.js}"></script>
<!--<script th:src="@{/js/compressPDF/FileSaver.min-2.0.4.js}"></script>-->
<script th:src="@{/js/compressPDF/sortable.min.1.10.2.js}"></script>

<script th:inline="javascript">

    var prefix = ctx + "contract_carrier";
    var uploadCache = {};
    $(function () {
        var contractAttach = [[${contractFiles}]];
        /*var contractAttach = [[${contractFiles}]];
        var image1 = {
            maxFileCount: 5,
            publish: "cmt",
            fileType: "file",
            maxFileSize: 200000
        };

        if(contractAttach == null){
            $.file.initAddFiles("image", "contractAttach", image1);
        }else{
            $.file.loadEditFiles("image", "contractAttach", contractAttach, image1);
        }*/

        var initialPreview = [];

        var initialPreviewConfig = [];

        /*if(pmsFujian[0].fjid == null){
            pmsFujian = [];
        }*/
        if(contractAttach != null && contractAttach != ''){
            for(var i = 0;i<contractAttach.length;i++) {
                var Fujian = contractAttach[i];
                if(Fujian != null && Fujian !=''){
                    var delFujian = new Object();
                    delFujian = generFilDel(Fujian);
                    //把文件的路径传到这个数组里面
                    initialPreview.push(Fujian.filePath);
                    //把文件的信息传到这个数组里面
                    initialPreviewConfig.push(delFujian);

                    //初始化
                    //contractAttachSet.add(Fujian.tid)
                }
            }
        }
        let option = {
            theme: "explorer-fa5", //主题
            language: 'zh',
            uploadUrl: ctx + "common/uploadBatch",  //上传的地址
            //deleteUrl: ctx + "common/deleteImage",
            uploadExtraData: {key: "image"},   //上传id，传入后台的参数
            deleteExtraData: {key: 'fileId'},
            // extra" {key: ''}, // 上面两个一致则可使用该字段？
            enctype: 'multipart/form-data',
            //allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
            allowedFileTypes: ['image', 'pdf'],//配置允许文件上传的类型
            allowedPreviewTypes: ['image', 'pdf'],//配置所有的被预览文件类型
            allowedPreviewMimeTypes: ['jpg', 'png', 'gif', 'jpeg', 'pdf'], //控制被预览的所有mime类型
            allowedFileExtensions: ['jpg', 'png', 'gif', 'jpeg', 'pdf'], //接收的文件后缀
            initialPreviewAsData: true,
            initialPreview: initialPreview,
            initialPreviewConfig: initialPreviewConfig,
            overwriteInitial: false,
            //initialPreviewConfig: [
            //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
            //],
            dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
            // maxFileSize: 1024*20,//单位为kb，如果为0表示不限制文件大小
            maxFileSize: 0,//单位为kb，如果为0表示不限制文件大小
            maxFileCount: 5, //表示允许同时上传的最大文件个数
            showUpload: false,  // 不显示上传按钮，选择后直接上传
            //previewClass:"uploadPreview",
            minFileSize: 5, // 5KB
            previewFileIcon: '<i class="fa fa-file"></i>',
            allowedPreviewTypes: ['image'],
            showClose: false,  //是否显示右上角叉按钮
            showUpload: false, //是否显示下方上传按钮
            showRemove: false, // 是否显示下方移除按钮
            //autoReplace: true,
            //showPreview: false,//是否显示预览(false=只剩按钮)
            showCaption: false,//底部上传按钮左侧文本
            uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
            fileActionSettings: {
                showUpload: false,		//每个文件的上传按钮
                showDrag: false,
                //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
            },
        }
        $("#image").fileinput(option).on('filebatchselected',async function(event,data,previewId,index){ //选中自动上传
            var files = $(this).fileinput('getFileStack'); // 返回选中的文件队列

            //所有文件id
            const keys = Object.keys(data);
            //文件
            const values = Object.values(data);
            //查看是否压缩过  1就是压缩过   都压缩过就上传
            const isCom = values.every(value => value.file.isCom === 1);

            if (isCom || isCompress == 0) {
                //上传
                $(this).fileinput("upload");
            }else {
                var index = layer.load(2, { //icon0-2 加载中,页面显示不同样式
                    shade: [0.5, '#000'], //0.4为透明度 ，#000 为颜色
                    content: '文件压缩中...',
                    success: function (layero) {
                        layero.find('.layui-layer-content').css({
                            'padding-top': '40px',//图标与样式会重合，这样设置可以错开
                            'width': '200px'//文字显示的宽度
                        });
                    }
                });

                //压缩文件
                const compressedPdfList = await compressPDFs(data);

                if(compressedPdfList && compressedPdfList.length > 0) {
                    console.log("上传==========", compressedPdfList);

                    for (let key of keys) {
                        let $file = $('.file-preview-frame[data-fileid="' + key + '"]');
                        let find = $file.find('.kv-file-remove');
                        find.click();
                    }


                    const fileObj = {};

                    compressedPdfList.forEach(file => {
                        fileObj[file.name] = file;
                    });

                    // $(this).fileinput('clearFileStack');

                    for(let i = 0; i < compressedPdfList.length; i++) {
                        $(this).fileinput('addToStack', compressedPdfList[i],i); // `fileObj`是文件blob对象实例
                    }

                    layer.close(index);

                }
            }

        }).on("fileuploaded", function (event, data, previewId, index) {
            //单个上传成功事件
            console.log("fileuploaded", event, data, previewId, index)
            var code = data.response.code;
            if (code !== 0) {
                $.modal.closeLoading();
                $.modal.alertError("上传失败：" + data.response.msg);
                return;
            }
            uploadCache[previewId] = data.response.tid;
            console.log(uploadCache)
        }).on('filesuccessremove', function (event, previewId, index) {
            //上传后删除事件
            console.log("filesuccessremove", event, previewId, index)
            //delete cache[previewId] //bug
            //fileArr.splice(index, 1) //bug
            //$(this).fileinput('clear');
            //$('[name="' + hideName + '"]').val('')
            var tid = uploadCache[previewId];
            $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                console.log(result)
            }, 'json')
            delete uploadCache[previewId]
        }).on('filepredelete', function(event, key, jqXHR, data) {//删除图片之前处罚事件  用于修改时的操作
            /*if(!confirm("确定删除原文件？删除后不可恢复")){
                console.log("1111")
                return;
            }*/
            var abort = true;
            if(confirm("确定删除原文件？删除后不可恢复")){
                abort = false;
            }
            return abort;
        });
    });

    var isCompress = 0

    layui.use('form', function(){
        var form = layui.form
        //监听指定开关
        form.on('switch(switchTest)', function (data) {
            if (this.checked) {
                isCompress = 1
            } else {
                isCompress = 0

            }
        });
    })




    function generFilDel(file) {
        if(file != null && file != ''){
            var type = file.filePath.substr(file.filePath.lastIndexOf('.') + 1);
            if(type=='pdf'){
                return {type: "pdf", size: file.cssize, caption: file.fileName, url:ctx + "common/deleteImage?id="+file.fileId,  key: file.fileId, downloadUrl:file.filePath};
            }else if(type=='text'){
                return {type: "text", size: file.cssize, caption: file.csname, url:ctx + "common/deleteImage?id="+file.fileId, key: file.fileId , downloadUrl:file.filePath};
            }else if(type=='mp4'){
                return  {type: "video", size: file.cssize, filetype: "video/mp4", caption: file.csname, url:ctx + "common/deleteImage?id="+file.fileId, key: file.fileId , downloadUrl:file.filePath};
            }else if(type=='txt'){
                return  {type: "txt", size: file.cssize, caption: file.csname, url:ctx + "common/deleteImage?id="+file.fileId, key: file.fileId , downloadUrl:file.filePath};
            }else if(type=='db'){
                return  {type: "db", size: file.cssize, caption: file.csname, url:ctx + "common/deleteImage?id="+file.fileId, key: file.fileId , downloadUrl:file.filePath};
            }else{
                return	{caption: file.csname, size: file.cssize, url: ctx + "common/deleteImage?id="+file.fileId, key: file.fileId};
            }
        }
    }

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#effectiveDate0").removeAttr("lay-key");
        $("#endDate0").removeAttr("lay-key");
        $("#extensionDate0").removeAttr("lay-key");
        laydate.render({
            elem: '#effectiveDate0',
            theme: 'molv',
            type: 'date',
            format: 'yyyy-MM-dd',

        });
        laydate.render({
            elem: '#endDate0',
            type: 'date',
            format: 'yyyy-MM-dd',

        });
        laydate.render({
            elem: '#extensionDate0',
            type: 'date',
            format: 'yyyy-MM-dd',

        });
    })

    var partyB = $("#carrName").bsSuggest({
        indexId: 4, //data.value 的第几个数据，作为input输入框的内容
        indexKey: 0, //data.value 的第几个数据，作为input输入框的内容
        idField: 'carrierId',                    //每组数据的哪个字段作为 data-id，优先级高于 indexId 设置（推荐）
        keyField: 'carrName',                   //每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        allowNoKeyword: false, //是否允许无关键字时请求数据
        multiWord: false, //以分隔符号分割的多关键字支持separator: ",", //多关键字支持时的分隔符，默认为空格
        getDataMethod: "url", //获取数据的方式，总是从 URL 获取
        effectiveFields:["carrName","contact","provinceName","cityName"],
        effectiveFieldsAlias:{carrName: "承运商名称",contact:"联系人",provinceName:"省份",cityName:"城市"},
        showHeader: true,
        //listAlign:'right',        //提示列表对齐位置，left/right/auto
        hideOnSelect: true,            // 鼠标从列表单击选择了值时，是否隐藏选择列表
        inputWarnColor: 'rgba(255,0,0,.1)', //输入框内容不是下拉列表选择时的警告色
        url: ctx + 'basic/carrier/bsSuggestList', //custAbbr /*优先从url ajax 请求 json 帮助数据，注意最后一个参数为关键字请求参数*/
        fnAdjustAjaxParam: function (keyword, options) {  //该插件默认是GET请求  https://github.com/lzwme/bootstrap-suggest-plugin/issues?q=post
            //if(!isNull(keyword)) {
                console.log("post")
                return {
                    type: 'POST',
                    timeout: 10000,
                    data: {
                        carrName: keyword,
                        pageSize: 20,  //承运商数据较多 默认只查20条
                        pageNum: 1
                    }
                }
            //}
        },
        processData: function(json){// url 获取数据时，对数据的处理，作为 getData 的回调函数
            var i, len, data = {value: []};
            if(!json || json.rows.length == 0) {
                return false;
            }
            len = json.rows.length;
            for (i = 0; i < len; i++) {
                data.value.push({
                    "carrName": json.rows[i].carrName,
                    "contact": json.rows[i].contact,
                    "provinceName":json.rows[i].provinceName,
                    "cityName":json.rows[i].cityName,
                    "carrierId":json.rows[i].carrierId
                });
            }
            return data;
        }
    }).on('onSetSelectValue', function (e, keyword, data) {
        $('#partyBId0').val(data.carrierId);
    }).on("onUnsetSelectValue",function (e) {
        $('#partyBId0').val("");
    });


    $('#carrName').on('blur', function() {
        if ($('#carrName').val() === '') {
            $('#partyBId0').val('');
        }
    });


    function isNull( str ){
        if ( str == "" ) return true;
        var regu = "^[ ]+$";
        var re = new RegExp(regu);
        return re.test(str);
    }

    /**
     * 数字 保留两位小数
     */
    function onlyNumberThreeDecimal (obj) {
        //得到第一个字符是否为负号
        var t = obj.value.charAt(0);
        //先把非数字的都替换掉，除了数字和.
        obj.value = obj.value.replace(/[^\d\.]/g, '');
        //必须保证第一个为数字而不是.
        obj.value = obj.value.replace(/^\./g, '');
        //保证只有出现一个.而没有多个.
        obj.value = obj.value.replace(/\.{2,}/g, '.');
        //保证.只出现一次，而不能出现两次以上
        obj.value = obj.value.replace('.', '$#$').replace(/\./g, '').replace(
            '$#$', '.');
        obj.value = obj.value.slice(0, 15);
        // 只能输入两个小数
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d).*$/,'$1$2.$3');
        //如果第一位是负号，则允许添加
        if (t == '-') {
            obj.value = '-' + obj.value;
        }
    }

    /**
     * 插入行
     */
    let wayBillIndex = 0;
    function insertRow() {
        //获取一共有多少行
        var trTtml = "";
        wayBillIndex += 1;
        if(wayBillIndex % 2 != 0) {
            trTtml = "<div class=\"over mt10 bggray\">\n"
        }else {
            trTtml = "<div class=\"over\">\n"
        }
        trTtml = trTtml +
            "                            <div class=\"fl\" style=\"width: 40px\">\n" +
            "                                <a class=\"close-link del-alink\" style=\"background: #fd8481;border-radius: 50%\" onclick=\"removeRowThree(this,0)\" title=\"删除选择行\">x</a>\n" +
            "                            </div>\n" +
            "                            <div class=\"fr\" style=\"width: calc(100% - 50px)\">\n" +
            "                                <div class=\"infotitle\">货物信息</div>\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-md-5 col-sm-5\">\n" +
            "                                        <div class=\"col-sm-6\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 日期：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <input type=\"text\" class=\" form-control\" id='orderDate" + wayBillIndex  + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].orderDate\" required\n" +
            "                                                           placeholder=\"要求提货日\" autocomplete=\"off\" readonly>\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-6\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 货主：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <input type=\"text\" id='cargoOwnerName"+ wayBillIndex  + "\'"   +"name=\"parkWaybillList[" + wayBillIndex + "].cargoOwnerName\" placeholder=\"请输入货主姓名...\" class=\"form-control\" maxlength=\"50\" autocomplete=\"off\" data-provide=\"typeahead\"/>\n" +
            "                                                    <input id='cargoOwnerId"+ wayBillIndex + "\'" +"name=\"parkWaybillList[" + wayBillIndex + "].cargoOwnerId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-7 col-sm-7\">\n" +
            "                                        <div class=\"col-sm-4\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 车型：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <input id='parkCarmodelName"+ wayBillIndex + "\'" +"name=\"parkWaybillList[" + wayBillIndex + "].parkCarmodelName\" placeholder=\"请填写车型...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                                    <input id='parkCarmodelId"+  wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].parkCarmodelId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +

            "                                        <div class=\"col-sm-4\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 开票：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <select name=\"parkWaybillList[" + wayBillIndex + "].invoicing\" class=\"form-control valid\" aria-invalid=\"false\"  aria-required=\"true\" required>\n" +
            "                                                        <option value=\"1\">否</option>\n" +
            "                                                        <option value=\"2\">是</option>\n" +
            "                                                    </select>\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +

            "                                        <div class=\"col-sm-4\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 货物：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <input id='parkGoodsName" + wayBillIndex + "\'" +"name=\"parkWaybillList[" + wayBillIndex + "].parkGoodsName\" placeholder=\"请填写货物...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                                    <input id='parkGoodsId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].parkGoodsId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                </div>\n" +
            "\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-sm-6\">\n" +
            "                                        <div class=\"col-sm-5\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\">提货方地址：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <select  id='deliProvinceId" + wayBillIndex +  "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliProvinceId\" class=\"form-control valid\" aria-invalid=\"false\">\n" +
            "                                                    </select>\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-2\">\n" +
            "                                            <select id='deliCityId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliCityId\" class=\"form-control valid\" aria-invalid=\"false\"></select>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-2\">\n" +
            "                                            <select id='deliAreaId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliAreaId\" class=\"form-control valid\" aria-invalid=\"false\"></select>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-3\">\n" +
            "                                            <input id='deliDetailAddr" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliDetailAddr\" placeholder=\"请输入详细地址\" class=\"form-control\" type=\"text\"\n" +
            "                                                   maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-sm-6\">\n" +
            "                                        <div class=\"col-sm-5\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\">收货方地址：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <select  id='arriProvinceId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriProvinceId\"  class=\"form-control valid\" aria-invalid=\"false\">\n" +
            "                                                    </select>\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-2\">\n" +
            "                                            <select id='arriCityId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriCityId\" class=\"form-control valid\" aria-invalid=\"false\"></select>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-2\">\n" +
            "                                            <select id='arriAreaId" +  wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriAreaId\" class=\"form-control valid\" aria-invalid=\"false\"></select>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-3\">\n" +
            "                                            <input id='arriDetailAddr" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriDetailAddr\" placeholder=\"请输入详细地址\" class=\"form-control\" type=\"text\"\n" +
            "                                                   maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <input type=\"hidden\" id='deliProvinceName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliProvinceName\">\n" +
            "                                    <input type=\"hidden\" id='deliCityName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliCityName\">\n" +
            "                                    <input type=\"hidden\" id='deliAreaName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliAreaName\">\n" +
            "                                    <input type=\"hidden\" id='arriProvinceName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriProvinceName\">\n" +
            "                                    <input type=\"hidden\" id='arriCityName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriCityName\">\n" +
            "                                    <input type=\"hidden\" id='arriAreaName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriAreaName\">" +
            "                                </div>\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 吨位：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].tonnage\"" + "id=\"tonnage" + wayBillIndex + "\"" + "placeholder=\"请输入吨位...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\"\n" +
            "                                                       onkeyup=calculateByTonnageTotals(this,"+ wayBillIndex + ")" +
            "                                                       oninput=\"onlyNumberThreeDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 单价：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].unitPrice\"" + "id=\"unitPrice" + wayBillIndex + "\"" + "placeholder=\"请输入单价...\" class=\"form-control\" type=\"text\"\n" +
            "                                                       onkeyup=calculateUnitPriceTotals(this,"+ wayBillIndex + ")" +
            "                                                       maxlength=\"50\" autocomplete=\"off\" oninput=\"onlyNumberThreeDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 运费：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='freight" +  wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].freight\" placeholder=\"请输入运费...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\"\n" +
            "                                                        onkeyup=\"synchronizePaidFreight(this)\"" +   "oninput=\"onlyNumberThreeDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 代垫费用：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='prepaidExpenses" +  wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].prepaidExpenses\" placeholder=\"请输入代垫费用...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\"\n" +
            "                                                                                                   oninput=\"onlyNumberThreeDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +

            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 入账：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <select name=\"parkWaybillList[" + wayBillIndex + "].entry\" class=\"form-control valid\" aria-invalid=\"false\"  aria-required=\"true\" required>\n" +
            "                                                    <option value=\"2\">未收</option>\n" +
            "                                                    <option value=\"1\">已收</option>\n" +
            "                                                </select>\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].carTypeName\" type=\"hidden\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "\n" +
            "                                </div>\n" +
            "                                <div class=\"infotitle\">运输信息</div>\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 车号：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='carName"+ wayBillIndex + "\'" +"name=\"parkWaybillList[" + wayBillIndex + "].carName\" placeholder=\"请输入车号...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].carId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 车主：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='vehicleOwnerName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].vehicleOwnerName\" placeholder=\"请输入车主姓名...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                                <input id='vehicleOwnerId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].vehicleOwnerId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 回单：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <select name=\"parkWaybillList[" + wayBillIndex + "].receipt\" id=\"receipt\" class=\"form-control valid\" aria-invalid=\"false\"  aria-required=\"true\" required>\n" +
            "                                                    <option value=\"1\">否</option>\n" +
            "                                                    <option value=\"2\">是</option>\n" +
            "                                                </select>" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-6\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\" style=\"width: 90px\"><span class=\"fcff\">*</span> 回单时间：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input type=\"text\" class=\" form-control\" id='returnOrderTime"+ wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].returnOrderTime\"\n" +
            "                                                       placeholder=\"回单时间\" lay-key=\"2\" autocomplete=\"off\" readonly>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 实付运价：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='paidFreight"+wayBillIndex+ "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].paidFreight\" placeholder=\"请输入实付运价\" class=\"form-control\" type=\"text\"\n" +
            "                                                       maxlength=\"50\" autocomplete=\"off\" oninput=\"onlyNumberThreeDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +

            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\">现金：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].cashMoney\" placeholder=\"请输入现金...\" class=\"form-control\" type=\"text\"\n" +
            "                                                       maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\">油卡：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].oilCard\" placeholder=\"请输入油卡...\" class=\"form-control\" type=\"text\"\n" +
            "                                                       maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                <div class=\"col-md-3 col-sm-6\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\" style=\"width: 90px\"><span class=\"fcff\">*</span> 支付时间：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input type=\"text\" class=\" form-control\" id='paymentTime"+ wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].paymentTime\"\n" +
            "                                                       placeholder=\"支付时间\" lay-key=\"2\" autocomplete=\"off\" readonly>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                </div>\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-sm-12\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\">备注：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                            <textarea name=\"parkWaybillList[" + wayBillIndex + "].remark\" maxlength=\"200\" class=\"form-control valid\"\n" +
            "                                                      rows=\"2\"></textarea>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                </div>\n" +
            "\n" +
            "                            </div>\n" +
            "                        </div>"

        $(".panel-body").append(trTtml);

        //时间初始化
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            //日期时间选择器
            $("#orderDate"+wayBillIndex).removeAttr("lay-key");
            $("#paymentTime"+wayBillIndex).removeAttr("lay-key");
            $("#returnOrderTime"+wayBillIndex).removeAttr("lay-key");
            laydate.render({
                elem: '#orderDate'+wayBillIndex
                , type: 'datetime'
                , value: new Date()
            });

            laydate.render({
                elem: '#paymentTime'+wayBillIndex
                , type: 'datetime'
            });

            laydate.render({
                elem: '#returnOrderTime'+wayBillIndex
                , type: 'datetime'
            });
        })

        /**
         * 货主
         */
        $("#cargoOwnerName"+wayBillIndex).typeahead({
            source: function (query, process) {
                if(query.trim() != null && query.trim() != '') {
                    return $.ajax({
                        url: prefix + "/getParkOnwners",
                        type: 'get',
                        data: {parkOwnerName: query},
                        success: function (result) {
                            $("#cargoOwnerId" + wayBillIndex).val("")
                            var resultList = result.map(function (item) {
                                var aItem = {id: item.id, name: item.ownerName};
                                return JSON.stringify(aItem);
                            });
                            return process(resultList);
                        },
                    });
                }
            },
            /**
             * 在选中数据后的操作，这里的返回值代表了输入框的值
             *
             * @param obj
             * @return 选中后，最终输入框里的值
             */
            updater: function (obj) {
                var item = JSON.parse(obj);
                $("#cargoOwnerId"+wayBillIndex).val(item.id)
                return item.name;
            },
            /**
             * 使用指定的方式，高亮(指出)匹配的部分
             *
             * @param obj 数据源中返回的单个实例
             * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
             */
            highlighter: function (obj) {
                var item = JSON.parse(obj);
                var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
                return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                    return '<strong>' + match + '</strong>'
                });
            },
            items: 'all', //展示全部 不设置默认展示8个
        });
        /**
         * 货物
         */
        $("#parkGoodsName"+wayBillIndex).typeahead({
            items: 'all', //展示全部 不设置默认展示8个
            source: function (query, process) {
                if(query.trim() != null && query.trim() != '') {
                    return $.ajax({
                        url: prefix + "/getParkGoods",
                        type: 'get',
                        data: {parkGoodsName: query},
                        success: function (result) {
                            $("#parkGoodsId" + wayBillIndex).val("")
                            var resultList = result.map(function (item) {
                                var aItem = {id: item.id, name: item.goodsName};
                                return JSON.stringify(aItem);
                            });
                            return process(resultList);
                            //return process(result);
                        },
                    });
                }
            },
            /**
             * 在选中数据后的操作，这里的返回值代表了输入框的值
             *
             * @param obj
             * @return 选中后，最终输入框里的值
             */
            updater: function (obj) {
                var item = JSON.parse(obj);
                $("#parkGoodsId"+wayBillIndex).val(item.id)
                return item.name;
            },
            /**
             * 使用指定的方式，高亮(指出)匹配的部分
             *
             * @param obj 数据源中返回的单个实例
             * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
             */
            highlighter: function (obj) {
                var item = JSON.parse(obj);
                var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
                return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                    return '<strong>' + match + '</strong>'
                });
            },
        });

    }/** end insertRow **/

    /**
     * 删除行
     */
    function removeRowThree(obj, index) {
        $(obj).parent().parent().remove()
        //删除之后  改变后面元素的index
    }


    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            /*$("#image").fileinput('upload');
            jQuery.subscribe("cmt", commit);*/

            var submitIds = []
            for (const uploadCacheKey in uploadCache) {
                submitIds.push(uploadCache[uploadCacheKey])
            }
            /*if (submitIds.length == 0) {
                $.modal.msgError("请选择上传文件");
                return
            }*/
            $("#contractAttach").val(submitIds.join(","))
            commit()
        }
    }

    function commit() {
        $.operate.saveTab(prefix + "/editContractCarrier", $('#form-contract-carrier-edit').serialize());

        /*$.ajax({
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            url: prefix + "/addContractcarrier",
            data: JSON.stringify($('#form-contract-carrier-add').serializeArray()),//JSON.stringify(data.field),
            dataType: 'json',
            success: function(result) {
                console.log(result)
            }
        });*/
    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }



    function compressPDF(file) {
        return new Promise((resolve, reject) => {
            let reader = new FileReader();
            reader.readAsArrayBuffer(file);

            reader.onload = function (e) {
                // 初始化pdf.js
                const pdfjsLib = window['pdfjs-dist/build/pdf'];
                pdfjsLib.disableStream = true;

                // 加载PDF
                let loadingTask = pdfjsLib.getDocument({ data: e.target.result });
                loadingTask.promise.then(pdf => {
                    // 逐页转Canvas
                    let canvasList = [];

                    for (let i = 1; i <= pdf.numPages; i++) {
                        let page = pdf.getPage(i);
                        let viewport = page.getViewport({ scale: 1.0 });

                        let canvas = document.createElement('canvas'); // Create a new canvas for each page
                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        let context = canvas.getContext('2d');
                        page.render({ canvasContext: context, viewport: viewport }).promise.then(() => {
                            canvasList.push(canvas.toDataURL());
                            if (i === pdf.numPages) {
                                // 所有页面渲染完成，生成压缩后的Blob
                                let compressedPDF = new Blob(canvasList, { type: 'application/pdf' });
                                resolve(compressedPDF);
                            }
                        });
                    }
                });
            };
        });
    }

    // 封装PDF压缩函数
    async function compressPDFs(data) {
        async function loadPdf(pdfFile, range, name) {
            const pdfjsLib = window['pdfjs-dist/build/pdf'];
            pdfjsLib.GlobalWorkerOptions.workerSrc = "/js/compressPDF/pdf.worker.min-2.5.207.js";


            const pdfBlob = await pdfToImages(pdfFile, range, name);
            return pdfBlob;
        }

        async function pdfToImages(pdfFile, initialQualityIndex, name) {
            let imgData = [];
            const qualityLevels = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1];
            let qualityIndex = Math.min(Math.max(initialQualityIndex, 0), qualityLevels.length - 1);


            return new Promise(async (resolve) => {
                const arrayBuffer = await pdfFile.arrayBuffer();
                const pdfDoc = await pdfjsLib.getDocument({data: arrayBuffer}).promise;
                const totalPages = pdfDoc.numPages;

                async function processPages(currentQualityIndex) {
                    imgData = [];
                    const currentQuality = qualityLevels[currentQualityIndex];

                    for(let i = 1; i <= totalPages; i++) {
                        const page = await pdfDoc.getPage(i);
                        const viewport = page.getViewport({scale: 1.5});
                        const canvas = document.createElement('canvas');
                        canvas.width = viewport.width;
                        canvas.height = viewport.height;

                        await page.render({
                            canvasContext: canvas.getContext('2d'),
                            viewport
                        }).promise;

                        imgData.push({
                            src: canvas.toDataURL('image/jpeg', currentQuality),
                            width: canvas.width,
                            height: canvas.height
                        });
                    }

                    const pdfBlob = await imgDataToPdf(name, imgData);
                    return pdfBlob;
                }

                while (qualityIndex < qualityLevels.length) {
                    const compressedPdf = await processPages(qualityIndex);
                    if (compressedPdf.size <= 20 * 1024 * 1024) {  // 20MB in bytes
                        resolve(compressedPdf);
                        return;
                    }
                    qualityIndex++;
                }

                // If we can't compress to under 20MB, return the smallest version
                const finalPdf = await processPages(8);
                resolve(finalPdf);
            });
        }

        // 将图片数组生成PDF
        async function imgDataToPdf(name, imgData) {
            const doc = new PDFDocument({compress: true});
            const stream = doc.pipe(blobStream());

            imgData.forEach(img => {
                const {src, width, height} = img;

                doc.addPage({size: [width, height]});
                doc.image(src, 0, 0);
            });

            doc.end();

            return new Promise(resolve => {
                stream.on('finish', function () {
                    const pdfBlob = stream.toBlob('application/pdf');
                    const pdfFile = new File([pdfBlob], name, { type: 'application/pdf' });

                    resolve(pdfFile);
                });
            });
        }

        const compressedPdfList = [];

        const compressPromises = Object.values(data).map(async (fileObj) => {
            const file = fileObj.file;

            if (file.type === 'application/pdf') {
                try {
                    const compressedPdf = await loadPdf(file, 0, file.name);
                    compressedPdf.isCom = 1;
                    compressedPdfList.push(compressedPdf);
                    console.log("处理pdf================：", compressedPdf);
                } catch (error) {
                    console.error("处理pdf时出错：", error);
                }
            } else {
                file.isCom = 1;
                compressedPdfList.push(file);
            }
        });

        await Promise.all(compressPromises);
        console.log("处理完成================：",compressPromises);

        return compressedPdfList;
    }



</script>
</body>

</html>