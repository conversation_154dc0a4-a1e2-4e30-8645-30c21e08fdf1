<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运合同新增')" />
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/bootstrap-icons.min.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/fileinput.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/all.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/jquery-editable-select/jquery-editable-select.min.css}" media="all" type="text/css"/>
</head>
<style>
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: left;
        margin-top: 5px;
    }
    .mt10{
        margin-top: 10px;
    }
    .ml10{
        margin-left: 10px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 80px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .infotitle{
        line-height: 30px;
        font-weight: bold;
    }
    .titlebg{
        background: #eff3f9;
        padding: 5px 10px;

    }
    a.del-alink {
        display: block;
        margin: 120px auto 0;
    }
    .bggray{
        background: #f7f8fa;
    }

    .dropdown-menu {
        width: 50% !important; /* 与输入框宽度一致 */
        left: 0 !important; /* 覆盖Bootstrap默认的left: 0 */
        right: auto;
    }

    .section-subtitle {
        font-size: 14px;
        color: #6c757d;
        margin: 10px 0;
        padding-left: 10px;
        border-left: 3px solid #007bff;
    }
    .section-divider {
        border-top: 1px solid #e9ecef;
        margin: 8px 0;
    }

</style>
<body>
<div class="form-content">
    <form id="form-contract-carrier-add" class="form-horizontal" novalidate="novalidate">
        <!--要求提货日期-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="titlebg over">
                            <div class="fl fw ml10" style="line-height: 26px">业务合同录入</div>
                        </div>
                        <div class="over">
                            <div class="fr" style="width: calc(100% - 50px)">
                                <div class="section-divider"></div>
                                <div class="section-subtitle">基础信息</div>

                                <div class="row no-gutter">
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 编号：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入合同编号" class="form-control" name="contractCarrierList[0].contractNumber" id="contractNumber" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 合同名称：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].contractName" id="contractName" placeholder="请输入合同名称" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>

<!--                                    <div class="col-sm-2">-->
<!--                                        <div class="flex">-->
<!--                                            <label class="flex_left"> 合同类型：</label>-->
<!--                                            <div class="flex_right">-->
<!--                                                <select name="contractRentList[0].contractType" id="contractType" class="form-control valid" aria-invalid="false"  aria-required="true" th:with="type=${@dict.getType('contract_type')}">-->
<!--                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>-->
<!--                                                </select>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                    </div>-->

                                    <div class="col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"> 甲方：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].partyA" id="partyA" placeholder="请输入甲方名称" class="form-control" type="text" maxlength="50" autocomplete="off">
                                                <!--<input name="contractcarrierList[0].customerId" id="customerId0" type="hidden">
                                                <div class="input-group-btn" style="width:1px;">
                                                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                                </div>-->
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left"> 乙方：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].partyB" id="partyB" placeholder="请输入乙方名称" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 版本：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].contractVersion" id="contractVersion" placeholder="请输入版本" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 期限：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入期限" class="form-control" name="contractCarrierList[0].deadline" id="deadline" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 总页数：</label>
                                            <div class="flex_right">
                                                <input type="text" oninput="this.value=this.value.replace(/\D/g,'').slice(0,6);"
                                                       placeholder="请输入总页数" class="form-control" name="contractCarrierList[0].totalPage" id="totalPage" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 生效日期：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].effectiveDate" id="effectiveDate0" placeholder="请输入生效日期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 终止日期：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].endDate" id="endDate0" placeholder="请输入终止日期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 延期日期：</label>
                                            <div class="flex_right">
                                                <input name="contractcarrierList[0].extensionDate" id="extensionDate0" placeholder="请输入延期日期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 对账期：</label>
                                            <div class="flex_right">
                                                <input oninput="this.value=this.value.replace(/\D/g,'').slice(0,6);"
                                                       name="contractCarrierList[0].reconciliationPeriod" id="reconciliationPeriod" placeholder="请输入对账期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 账期约定：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入结算账期约定" class="form-control" name="contractCarrierList[0].settlementPeriod" id="settlementPeriod" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 特殊说明：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入特殊事项说明" class="form-control" name="contractCarrierList[0].specialNotes" id="specialNotes" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-4">
                                        <div class="flex">
                                            <label class="flex_left">长期有效：</label>
                                            <div class="flex_right" style="">
                                                <label class="radio-box">
                                                    <input type="radio" checked="" value="0" id="longTermEffective0" name="contractCarrierList[0].longTermEffective">否
                                                </label>
                                                <label class="radio-box">
                                                    <input type="radio" value="1" id="longTermEffective1" name="contractCarrierList[0].longTermEffective">是
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 备注 -->
                                <div class="row no-gutter">
                                    <div class="col-sm-12">
                                        <div class="flex">
                                            <label class="flex_left">备注：</label>
                                            <div class="flex_right">
                                                <textarea name="contractCarrierList[0].remark" id="remark" maxlength="200" class="form-control valid" rows="2"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="section-divider"></div>
                                <div class="section-subtitle">其他配置</div>
                                <div class="row mt10">
                                    <div class="col-sm-6">
                                        <div class="flex">
                                            <label class="" style="width: 120px;text-align: right;line-height: 30px;">信息是否已验证：</label>
                                            <div class="" style="">
                                                <label class="radio-box">
                                                    <input type="radio" checked="" value="0" id="isDataComplete0" name="contractCarrierList[0].isDataComplete">否
                                                </label>
                                                <label class="radio-box">
                                                    <input type="radio" value="1" id="isDataComplete1" name="contractCarrierList[0].isDataComplete">是
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <div class="section-divider"></div>
                                <div class="section-subtitle">绑定承运商</div>
                                <div class="row mt10">
                                    <div class="col-sm-6">
                                        <div class="flex">
                                            <label class="flex_left"> 承运商：</label>
                                            <div class="flex_right">
                                                <input name="contractCarrierList[0].carrName" id="carrName" placeholder="请输入承运商名称" class="form-control" type="text" maxlength="50" autocomplete="off">
                                                <input name="contractCarrierList[0].partyBId" id="partyBId0" type="hidden">
                                                <div class="input-group-btn" style="width:1px;">
                                                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <!-- 合同附件 -->
                                <div class="section-divider"></div>
                                <div class="section-subtitle">合同附件</div>
                                <div class="row no-gutter">
                                    <div class="col-sm-10">
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="layui-form">
                                            <div class="layui-form-item" style="margin-bottom:0px">
                                                <label class="layui-form-label">pdf压缩</label>
                                                <div class="layui-input-block">
                                                    <input type="checkbox" title="开|关" lay-filter="switchTest"  lay-skin="switch">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-12">
                                        <div class="flex">
                                            <label class="flex_left"> 合同附件: </label>
                                            <div class="flex_right">
                                                <!--<input id="file" type="file" multiple name="file">
                                                <input id="contractAttach" name="contractcarrierList[0].contractAttach" type="hidden">-->

                                                <input id="image" class="form-control" name="image" type="file" multiple>
                                                <input id="contractAttach" name="contractCarrierList[0].contractAttach" type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/fileinput.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/locales/zh.js}"></script>
<script th:src="@{/ajax/libs/jquery-editable-select/jquery-editable-select.js}"></script>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput/plugins/piexif.min.js}"></script>
<script th:src="@{'/js/wecom.js'}"></script>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>

<script defer th:src="@{/js/compressPDF/pdfkit-standalone-0.10.0.js}"></script>
<script defer th:src="@{/js/compressPDF/blob-stream-0.1.3.js}"></script>
<script th:src="@{/js/compressPDF/pdf.min-2.5.207.js}"></script>
<!--<script th:src="@{/js/compressPDF/FileSaver.min-2.0.4.js}"></script>-->
<script th:src="@{/js/compressPDF/sortable.min.1.10.2.js}"></script>

<script th:inline="javascript">

    var prefix = ctx + "contract_carrier";

    var contractAttach = $("#contractAttach").val()
    var contractAttachSet = new Set();
    $(function () {
        /*var image1 = {
            maxFileCount: 5,
            publish: "cmt",
            fileType: "file",
            maxFileSize: 20480
        };
        $.file.initAddFiles("image", "contractAttach", image1);*/

        //new
        function HashMap(){// 自己创建一个HashMap，不然保存到后台顺序是乱的
            this.map = {};
        }
        HashMap.prototype = {
            set : function(key , value){// 向Map中增加元素（key, value)
                this.map[key] = value;
            },
            get : function(key){ //获取指定Key的元素值Value，失败返回Null
                if(this.map.hasOwnProperty(key)){
                    return this.map[key];
                }
                return null;
            },
            remove : function(key){ // 删除指定Key的元素，成功返回True，失败返回False
                if(this.map.hasOwnProperty(key)){
                    return delete this.map[key];
                }
                return false;
            },
            removeAll : function(){  //清空HashMap所有元素
                this.map = {};
            },
            keySet : function() { //获取Map中所有KEY的数组（Array）
                var _keys = [];
                for (var i in this.map) {
                    _keys.push(i);
                }
                return _keys;
            },
            maxSize : function() {
                var length = 0;
                for (var i in this.map) {
                    length = length + 1;
                }
                return length;
            }
        };
        HashMap.prototype.constructor = HashMap;
        window.m = new HashMap();
        window.key;
        window.value;

        $("#image").fileinput({
            theme: "explorer-fa5", //主题
            language: 'zh',
            uploadUrl: ctx + "common/uploadBatch",//上传的地址
            deleteUrl: ctx + "/common/deleteImageByTid",
            uploadExtraData: {key: "image"},   //上传id，传入后台的参数
            deleteExtraData: {key: 'tid'},
            uploadAsync: true, //默认异步上传   多文件上传可配置同步或异步
            showPreview: true, //是否显示预览,不写默认为true
            showUpload: false, //是否显示上传按钮,跟随文本框那个
            showRemove: false, //显示移除按钮,跟随文本框的那个
            showCaption: true,//是否显示标题,就是那个文本框
            dropZoneEnabled: true,//是否显示拖拽区域，默认不写为true，但是会占用很大区域
            showClose: false,  //关闭右上角清空按钮 那个小x
            overwriteInitial: false,  //新增图片追加
            // maxFileSize: 1024*20,//单位为kb，如果为0表示不限制文件大小
            maxFileSize: 0,//单位为kb，如果为0表示不限制文件大小
            minFileCount: 0,
            maxFileCount: 5, //表示允许同时上传的最大文件个数
            enctype: 'multipart/form-data',
            validateInitialCount: true,
            removeFromPreviewOnError:true,//当选择的文件不符合规则时，例如不是指定后缀文件、大小超出配置等，选择的文件不会出现在预览框中，只会显示错误信息
            previewFileType: "image",
            browseClass: "btn btn-default",
            browseIcon: "<i class=\"glyphicon glyphicon-picture\"></i>",
            previewFileIcon: "<i class='glyphicon glyphicon-king'></i>",
            msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！",
            allowedFileTypes: ['image', 'pdf'],//配置允许文件上传的类型
            allowedPreviewTypes: ['image', 'pdf'],//配置所有的被预览文件类型
            allowedPreviewMimeTypes: ['jpg', 'png', 'gif', 'jpeg', 'pdf'], //控制被预览的所有mime类型
            allowedFileExtensions: ['jpg', 'png', 'gif', 'jpeg', 'pdf'], //接收的文件后缀
            previewSettings: {
                //image: {width: "100px", height: "100px"}
            },
            layoutTemplates: {
                // actionDelete:'', //去除上传预览的缩略图中的删除图标
                actionUpload: ''//去除上传预览缩略图中的上传图片；
                //actionZoom: '' //去除上传预览缩略图中的查看详情预览的缩略图标。
            }
        }).on('filebatchselected',async function(event,data,previewId,index){ //选中自动上传
            var files = $(this).fileinput('getFileStack'); // 返回选中的文件队列

            //所有文件id
            const keys = Object.keys(data);
            //文件
            const values = Object.values(data);
            //查看是否压缩过  1就是压缩过   都压缩过就上传
            const isCom = values.every(value => value.file.isCom === 1);

            if (isCom || isCompress == 0) {
                let hasBigFile = false;

                for(let key in data) {
                    if(data[key].size > 20 * 1024 * 1024) {
                        hasBigFile = true;
                        break;
                    }
                }
                if(hasBigFile) {
                    layer.msg('压缩后的文件大于20M，无法上传', {icon: 0,time:3000}, function(){
                        // layer.msg('提示框关闭后的回调');
                    });
                    return
                }

                //上传
                $(this).fileinput("upload");
            }else {
                var index = layer.load(2, { //icon0-2 加载中,页面显示不同样式
                    shade: [0.5, '#000'], //0.4为透明度 ，#000 为颜色
                    content: '文件压缩中...',
                    success: function (layero) {
                        layero.find('.layui-layer-content').css({
                            'padding-top': '40px',//图标与样式会重合，这样设置可以错开
                            'width': '200px'//文字显示的宽度
                        });
                    }
                });

                //压缩文件
                const compressedPdfList = await compressPDFs(data);

                if(compressedPdfList && compressedPdfList.length > 0) {
                    console.log("上传==========", compressedPdfList);

                    for (let key of keys) {
                        let $file = $('.file-preview-frame[data-fileid="' + key + '"]');
                        let find = $file.find('.kv-file-remove');
                        find.click();
                    }


                    const fileObj = {};

                    compressedPdfList.forEach(file => {
                        fileObj[file.name] = file;
                    });

                    // $(this).fileinput('clearFileStack');

                    for(let i = 0; i < compressedPdfList.length; i++) {
                        if(compressedPdfList[i].size > 20 * 1024 * 1024){
                            // 大于20MB
                            layer.close(index);

                            layer.msg('压缩后的文件大于20M，无法上传', {icon: 0,time:3000}, function(){
                                // layer.msg('提示框关闭后的回调');
                            });
                            return
                        }
                        $(this).fileinput('addToStack', compressedPdfList[i],i); // `fileObj`是文件blob对象实例
                    }

                    layer.close(index);

                }
            }

        }).on('filepreupload', function(event, data, previewId, index) {
            var form = data.form, files = data.files, extra = data.extra,
                response = data.response, reader = data.reader;
            console.log("filepreupload", event, data, previewId,index)
        }).on("filebatchuploadsuccess", function(event, data, previewId, index) { //开启同步上传默认走这个

        }).on("fileuploaded", function(event, data, previewId) {//异步图片上传  开启异步默认走这个  //一张图片调用一次
            console.log("fileuploaded", event, data, previewId)
            if(data.response){
                key = previewId;
                value = data.response.tid;
                m.set(key, value);
                //console.log(m)
                contractAttachSet.add(value)
            }
        }).on('filepreremove', function (event, id, index) {//未上传 删除

        }).on('filesuccessremove', function (event, data, index, previewId) {//上传成功后 删除
            //data中存的previewId,找到previewId对用的tid,删除
            console.log("filesuccessremove", event, data, index, previewId)
            let removeTid = m.get(data);
            contractAttachSet.delete(removeTid)
            m.remove(data)
            //服务器删除图片
            $.post(ctx + 'common/deleteImageByTid', {tid: removeTid}, function (result) {
                console.log(result)
            }, 'json')
        }).on('filepredelete', function(event, key, jqXHR, data) {//删除图片之前处罚事件  用于修改时的操作
            if(!confirm("确定删除原文件？删除后不可恢复")){
                return false;
            }
        })
    });

    var isCompress = 0

    layui.use('form', function(){
        var form = layui.form
        //监听指定开关
        form.on('switch(switchTest)', function (data) {
            if (this.checked) {
                isCompress = 1
            } else {
                isCompress = 0

            }
        });
    })




    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#effectiveDate0").removeAttr("lay-key");
        $("#endDate0").removeAttr("lay-key");
        $("#extensionDate0").removeAttr("lay-key");
        laydate.render({
            elem: '#effectiveDate0',
            theme: 'molv',
            type: 'date',
            format: 'yyyy-MM-dd',
            value: new Date()
        });
        laydate.render({
            elem: '#endDate0',
            type: 'date',
            format: 'yyyy-MM-dd',
            value: new Date()
        });
        laydate.render({
            elem: '#extensionDate0',
            type: 'date',
            format: 'yyyy-MM-dd',
            value: new Date()
        });
    })

    var partyB = $("#carrName").bsSuggest({
             indexId: 4, //data.value 的第几个数据，作为input输入框的内容
             indexKey: 0, //data.value 的第几个数据，作为input输入框的内容
             idField: 'carrierId',                    //每组数据的哪个字段作为 data-id，优先级高于 indexId 设置（推荐）
             keyField: 'carrName',                   //每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
             allowNoKeyword: false, //是否允许无关键字时请求数据
             multiWord: false, //以分隔符号分割的多关键字支持separator: ",", //多关键字支持时的分隔符，默认为空格
             getDataMethod: "url", //获取数据的方式，总是从 URL 获取
             effectiveFields:["carrName","contact","provinceName","cityName"],
             effectiveFieldsAlias:{carrName: "承运商名称",contact:"联系人",provinceName:"省份",cityName:"城市"},
             showHeader: true,
             //listAlign:'right',        //提示列表对齐位置，left/right/auto
             hideOnSelect: true,            // 鼠标从列表单击选择了值时，是否隐藏选择列表
             inputWarnColor: 'rgba(255,0,0,.1)', //输入框内容不是下拉列表选择时的警告色
             url: ctx + 'basic/carrier/bsSuggestList', //custAbbr /*优先从url ajax 请求 json 帮助数据，注意最后一个参数为关键字请求参数*/
             fnAdjustAjaxParam: function (keyword, options) {  //该插件默认是GET请求  https://github.com/lzwme/bootstrap-suggest-plugin/issues?q=post
                 //if(!isNull(keyword)) {
                    console.log("post")
                    return {
                        type: 'POST',
                        timeout: 10000,
                        data: {
                            carrName: keyword,
                            pageSize: 20,  //承运商数据较多 默认只查20条
                            pageNum: 1
                        }
                    }
                //}
             },
             processData: function(json){// url 获取数据时，对数据的处理，作为 getData 的回调函数
                 var i, len, data = {value: []};
                 if(!json || json.rows.length == 0) {
                     return false;
                 }
                 len = json.rows.length;
                 for (i = 0; i < len; i++) {
                     data.value.push({
                         "carrName": json.rows[i].carrName,
                         "contact": json.rows[i].contact,
                         "provinceName":json.rows[i].provinceName,
                         "cityName":json.rows[i].cityName,
                         "carrierId":json.rows[i].carrierId
                     });
                 }
                 return data;
             }
    }).on('onSetSelectValue', function (e, keyword, data) {
        $('#partyBId0').val(data.carrierId);
    }).on("onUnsetSelectValue",function (e) {
        $('#partyBId0').val("");
    });

    $('#carrName').on('blur', function() {
        if ($('#carrName').val() === '') {
            $('#partyBId0').val('');
        }
    });

    function isNull( str ){
        if ( str == "" ) return true;
        var regu = "^[ ]+$";
        var re = new RegExp(regu);
        return re.test(str);
    }

    /**
     * 数字 保留两位小数
     */
    function onlyNumberThreeDecimal (obj) {
        //得到第一个字符是否为负号
        var t = obj.value.charAt(0);
        //先把非数字的都替换掉，除了数字和.
        obj.value = obj.value.replace(/[^\d\.]/g, '');
        //必须保证第一个为数字而不是.
        obj.value = obj.value.replace(/^\./g, '');
        //保证只有出现一个.而没有多个.
        obj.value = obj.value.replace(/\.{2,}/g, '.');
        //保证.只出现一次，而不能出现两次以上
        obj.value = obj.value.replace('.', '$#$').replace(/\./g, '').replace(
            '$#$', '.');
        obj.value = obj.value.slice(0, 15);
        // 只能输入两个小数
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d).*$/,'$1$2.$3');
        //如果第一位是负号，则允许添加
        if (t == '-') {
            obj.value = '-' + obj.value;
        }
    }

    /**
     * 插入行
     */
    let wayBillIndex = 0;
    function insertRow() {
        //获取一共有多少行
        var trTtml = "";
        wayBillIndex += 1;
        if(wayBillIndex % 2 != 0) {
           trTtml = "<div class=\"over mt10 bggray\">\n"
        }else {
            trTtml = "<div class=\"over\">\n"
        }
        trTtml = trTtml +
            "                            <div class=\"fl\" style=\"width: 40px\">\n" +
            "                                <a class=\"close-link del-alink\" style=\"background: #fd8481;border-radius: 50%\" onclick=\"removeRowThree(this,0)\" title=\"删除选择行\">x</a>\n" +
            "                            </div>\n" +
            "                            <div class=\"fr\" style=\"width: calc(100% - 50px)\">\n" +
            "                                <div class=\"infotitle\">货物信息</div>\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-md-5 col-sm-5\">\n" +
            "                                        <div class=\"col-sm-6\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 日期：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <input type=\"text\" class=\" form-control\" id='orderDate" + wayBillIndex  + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].orderDate\" required\n" +
            "                                                           placeholder=\"要求提货日\" autocomplete=\"off\" readonly>\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-6\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 货主：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <input type=\"text\" id='cargoOwnerName"+ wayBillIndex  + "\'"   +"name=\"parkWaybillList[" + wayBillIndex + "].cargoOwnerName\" placeholder=\"请输入货主姓名...\" class=\"form-control\" maxlength=\"50\" autocomplete=\"off\" data-provide=\"typeahead\"/>\n" +
            "                                                    <input id='cargoOwnerId"+ wayBillIndex + "\'" +"name=\"parkWaybillList[" + wayBillIndex + "].cargoOwnerId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-7 col-sm-7\">\n" +
            "                                        <div class=\"col-sm-4\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 车型：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <input id='parkCarmodelName"+ wayBillIndex + "\'" +"name=\"parkWaybillList[" + wayBillIndex + "].parkCarmodelName\" placeholder=\"请填写车型...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                                    <input id='parkCarmodelId"+  wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].parkCarmodelId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +

            "                                        <div class=\"col-sm-4\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 开票：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <select name=\"parkWaybillList[" + wayBillIndex + "].invoicing\" class=\"form-control valid\" aria-invalid=\"false\"  aria-required=\"true\" required>\n" +
            "                                                        <option value=\"1\">否</option>\n" +
            "                                                        <option value=\"2\">是</option>\n" +
            "                                                    </select>\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +

            "                                        <div class=\"col-sm-4\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\"><span class=\"fcff\">*</span> 货物：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <input id='parkGoodsName" + wayBillIndex + "\'" +"name=\"parkWaybillList[" + wayBillIndex + "].parkGoodsName\" placeholder=\"请填写货物...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                                    <input id='parkGoodsId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].parkGoodsId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                </div>\n" +
            "\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-sm-6\">\n" +
            "                                        <div class=\"col-sm-5\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\">提货方地址：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <select  id='deliProvinceId" + wayBillIndex +  "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliProvinceId\" class=\"form-control valid\" aria-invalid=\"false\">\n" +
            "                                                    </select>\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-2\">\n" +
            "                                            <select id='deliCityId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliCityId\" class=\"form-control valid\" aria-invalid=\"false\"></select>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-2\">\n" +
            "                                            <select id='deliAreaId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliAreaId\" class=\"form-control valid\" aria-invalid=\"false\"></select>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-3\">\n" +
            "                                            <input id='deliDetailAddr" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliDetailAddr\" placeholder=\"请输入详细地址\" class=\"form-control\" type=\"text\"\n" +
            "                                                   maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-sm-6\">\n" +
            "                                        <div class=\"col-sm-5\">\n" +
            "                                            <div class=\"flex\">\n" +
            "                                                <label class=\"flex_left\">收货方地址：</label>\n" +
            "                                                <div class=\"flex_right\">\n" +
            "                                                    <select  id='arriProvinceId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriProvinceId\"  class=\"form-control valid\" aria-invalid=\"false\">\n" +
            "                                                    </select>\n" +
            "                                                </div>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-2\">\n" +
            "                                            <select id='arriCityId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriCityId\" class=\"form-control valid\" aria-invalid=\"false\"></select>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-2\">\n" +
            "                                            <select id='arriAreaId" +  wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriAreaId\" class=\"form-control valid\" aria-invalid=\"false\"></select>\n" +
            "                                        </div>\n" +
            "                                        <div class=\"col-sm-3\">\n" +
            "                                            <input id='arriDetailAddr" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriDetailAddr\" placeholder=\"请输入详细地址\" class=\"form-control\" type=\"text\"\n" +
            "                                                   maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <input type=\"hidden\" id='deliProvinceName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliProvinceName\">\n" +
            "                                    <input type=\"hidden\" id='deliCityName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliCityName\">\n" +
            "                                    <input type=\"hidden\" id='deliAreaName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].deliAreaName\">\n" +
            "                                    <input type=\"hidden\" id='arriProvinceName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriProvinceName\">\n" +
            "                                    <input type=\"hidden\" id='arriCityName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriCityName\">\n" +
            "                                    <input type=\"hidden\" id='arriAreaName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].arriAreaName\">" +
            "                                </div>\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 吨位：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].tonnage\"" + "id=\"tonnage" + wayBillIndex + "\"" + "placeholder=\"请输入吨位...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\"\n" +
            "                                                       onkeyup=calculateByTonnageTotals(this,"+ wayBillIndex + ")" +
            "                                                       oninput=\"onlyNumberThreeDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 单价：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].unitPrice\"" + "id=\"unitPrice" + wayBillIndex + "\"" + "placeholder=\"请输入单价...\" class=\"form-control\" type=\"text\"\n" +
            "                                                       onkeyup=calculateUnitPriceTotals(this,"+ wayBillIndex + ")" +
            "                                                       maxlength=\"50\" autocomplete=\"off\" oninput=\"onlyNumberThreeDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 运费：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='freight" +  wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].freight\" placeholder=\"请输入运费...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\"\n" +
            "                                                        onkeyup=\"synchronizePaidFreight(this)\"" +   "oninput=\"onlyNumberThreeDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 代垫费用：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='prepaidExpenses" +  wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].prepaidExpenses\" placeholder=\"请输入代垫费用...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\"\n" +
            "                                                                                                   oninput=\"onlyNumberThreeDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +

            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 入账：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <select name=\"parkWaybillList[" + wayBillIndex + "].entry\" class=\"form-control valid\" aria-invalid=\"false\"  aria-required=\"true\" required>\n" +
            "                                                    <option value=\"2\">未收</option>\n" +
            "                                                    <option value=\"1\">已收</option>\n" +
            "                                                </select>\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].carTypeName\" type=\"hidden\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "\n" +
            "                                </div>\n" +
            "                                <div class=\"infotitle\">运输信息</div>\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 车号：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='carName"+ wayBillIndex + "\'" +"name=\"parkWaybillList[" + wayBillIndex + "].carName\" placeholder=\"请输入车号...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].carId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 车主：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='vehicleOwnerName" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].vehicleOwnerName\" placeholder=\"请输入车主姓名...\" class=\"form-control\" type=\"text\" maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                                <input id='vehicleOwnerId" + wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].vehicleOwnerId\" class=\"form-control\" type=\"hidden\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 回单：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <select name=\"parkWaybillList[" + wayBillIndex + "].receipt\" id=\"receipt\" class=\"form-control valid\" aria-invalid=\"false\"  aria-required=\"true\" required>\n" +
            "                                                    <option value=\"1\">否</option>\n" +
            "                                                    <option value=\"2\">是</option>\n" +
            "                                                </select>" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-6\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\" style=\"width: 90px\"><span class=\"fcff\">*</span> 回单时间：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input type=\"text\" class=\" form-control\" id='returnOrderTime"+ wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].returnOrderTime\"\n" +
            "                                                       placeholder=\"回单时间\" lay-key=\"2\" autocomplete=\"off\" readonly>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\"><span class=\"fcff\">*</span> 实付运价：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input id='paidFreight"+wayBillIndex+ "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].paidFreight\" placeholder=\"请输入实付运价\" class=\"form-control\" type=\"text\"\n" +
            "                                                       maxlength=\"50\" autocomplete=\"off\" oninput=\"onlyNumberThreeDecimal(this)\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +

            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\">现金：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].cashMoney\" placeholder=\"请输入现金...\" class=\"form-control\" type=\"text\"\n" +
            "                                                       maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                    <div class=\"col-md-3 col-sm-4\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\">油卡：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input name=\"parkWaybillList[" + wayBillIndex + "].oilCard\" placeholder=\"请输入油卡...\" class=\"form-control\" type=\"text\"\n" +
            "                                                       maxlength=\"50\" autocomplete=\"off\">\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                <div class=\"col-md-3 col-sm-6\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\" style=\"width: 90px\"><span class=\"fcff\">*</span> 支付时间：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                                <input type=\"text\" class=\" form-control\" id='paymentTime"+ wayBillIndex + "\'" + "name=\"parkWaybillList[" + wayBillIndex + "].paymentTime\"\n" +
            "                                                       placeholder=\"支付时间\" lay-key=\"2\" autocomplete=\"off\" readonly>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                </div>\n" +
            "                                <div class=\"row no-gutter\">\n" +
            "                                    <div class=\"col-sm-12\">\n" +
            "                                        <div class=\"flex\">\n" +
            "                                            <label class=\"flex_left\">备注：</label>\n" +
            "                                            <div class=\"flex_right\">\n" +
            "                                            <textarea name=\"parkWaybillList[" + wayBillIndex + "].remark\" maxlength=\"200\" class=\"form-control valid\"\n" +
            "                                                      rows=\"2\"></textarea>\n" +
            "                                            </div>\n" +
            "                                        </div>\n" +
            "                                    </div>\n" +
            "                                </div>\n" +
            "\n" +
            "                            </div>\n" +
            "                        </div>"

        $(".panel-body").append(trTtml);

        //时间初始化
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            //日期时间选择器
            $("#orderDate"+wayBillIndex).removeAttr("lay-key");
            $("#paymentTime"+wayBillIndex).removeAttr("lay-key");
            $("#returnOrderTime"+wayBillIndex).removeAttr("lay-key");
            laydate.render({
                elem: '#orderDate'+wayBillIndex
                , type: 'datetime'
                , value: new Date()
            });

            laydate.render({
                elem: '#paymentTime'+wayBillIndex
                , type: 'datetime'
            });

            laydate.render({
                elem: '#returnOrderTime'+wayBillIndex
                , type: 'datetime'
            });
        })

        /**
         * 货主
         */
        $("#cargoOwnerName"+wayBillIndex).typeahead({
            source: function (query, process) {
                if(query.trim() != null && query.trim() != '') {
                    return $.ajax({
                        url: prefix + "/getParkOnwners",
                        type: 'get',
                        data: {parkOwnerName: query},
                        success: function (result) {
                            $("#cargoOwnerId" + wayBillIndex).val("")
                            var resultList = result.map(function (item) {
                                var aItem = {id: item.id, name: item.ownerName};
                                return JSON.stringify(aItem);
                            });
                            return process(resultList);
                        },
                    });
                }
            },
            /**
             * 在选中数据后的操作，这里的返回值代表了输入框的值
             *
             * @param obj
             * @return 选中后，最终输入框里的值
             */
            updater: function (obj) {
                var item = JSON.parse(obj);
                $("#cargoOwnerId"+wayBillIndex).val(item.id)
                return item.name;
            },
            /**
             * 使用指定的方式，高亮(指出)匹配的部分
             *
             * @param obj 数据源中返回的单个实例
             * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
             */
            highlighter: function (obj) {
                var item = JSON.parse(obj);
                var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
                return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                    return '<strong>' + match + '</strong>'
                });
            },
            items: 'all', //展示全部 不设置默认展示8个
        });
        /**
         * 货物
         */
        $("#parkGoodsName"+wayBillIndex).typeahead({
            items: 'all', //展示全部 不设置默认展示8个
            source: function (query, process) {
                if(query.trim() != null && query.trim() != '') {
                    return $.ajax({
                        url: prefix + "/getParkGoods",
                        type: 'get',
                        data: {parkGoodsName: query},
                        success: function (result) {
                            $("#parkGoodsId" + wayBillIndex).val("")
                            var resultList = result.map(function (item) {
                                var aItem = {id: item.id, name: item.goodsName};
                                return JSON.stringify(aItem);
                            });
                            return process(resultList);
                            //return process(result);
                        },
                    });
                }
            },
            /**
             * 在选中数据后的操作，这里的返回值代表了输入框的值
             *
             * @param obj
             * @return 选中后，最终输入框里的值
             */
            updater: function (obj) {
                var item = JSON.parse(obj);
                $("#parkGoodsId"+wayBillIndex).val(item.id)
                return item.name;
            },
            /**
             * 使用指定的方式，高亮(指出)匹配的部分
             *
             * @param obj 数据源中返回的单个实例
             * @returns {XML|void|string|*} 数据列表中数据的显示方式（如匹配内容高亮等）
             */
            highlighter: function (obj) {
                var item = JSON.parse(obj);
                var query = this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g, '\\$&');
                return item.name.replace(new RegExp('(' + query + ')', 'ig'), function ($1, match) {
                    return '<strong>' + match + '</strong>'
                });
            },
        });

    }/** end insertRow **/

    /**
     * 删除行
     */
    function removeRowThree(obj, index) {
        $(obj).parent().parent().remove()
        //删除之后  改变后面元素的index
    }

    /**
     * 提交
     */
    function submitHandler() {
        /*console.log(JSON.stringify($('#form-contract-carrier-add').serializeArray()))
        console.log("===========")
        console.log($('#form-contract-carrier-add').serializeArray())*/
        if ($.validate.form()) {
            /*$("#image").fileinput('upload');
            jQuery.subscribe("cmt", commit);*/

            var submitIds = []
            for (let x of contractAttachSet) {
                submitIds.push(x)
            }
            /*for (const uploadCacheKey in contractAttachSet) {
                submitIds.push(uploadCacheKey)
            }*/
            /*if (submitIds.length == 0) {
                $.modal.msgError("请选择上传文件");
                return
            }*/
            $("#contractAttach").val(submitIds.join(","))
            commit()
        }
    }

    function commit() {
        $.operate.saveTab(prefix + "/addContractCarrier", $('#form-contract-carrier-add').serialize());

        /*$.ajax({
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            url: prefix + "/addContractcarrier",
            data: JSON.stringify($('#form-contract-carrier-add').serializeArray()),//JSON.stringify(data.field),
            dataType: 'json',
            success: function(result) {
                console.log(result)
            }
        });*/
    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }


    function compressPDF(file) {
        return new Promise((resolve, reject) => {
            let reader = new FileReader();
            reader.readAsArrayBuffer(file);

            reader.onload = function (e) {
                // 初始化pdf.js
                const pdfjsLib = window['pdfjs-dist/build/pdf'];
                pdfjsLib.disableStream = true;

                // 加载PDF
                let loadingTask = pdfjsLib.getDocument({ data: e.target.result });
                loadingTask.promise.then(pdf => {
                    // 逐页转Canvas
                    let canvasList = [];

                    for (let i = 1; i <= pdf.numPages; i++) {
                        let page = pdf.getPage(i);
                        let viewport = page.getViewport({ scale: 1.0 });

                        let canvas = document.createElement('canvas'); // Create a new canvas for each page
                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        let context = canvas.getContext('2d');
                        page.render({ canvasContext: context, viewport: viewport }).promise.then(() => {
                            canvasList.push(canvas.toDataURL());
                            if (i === pdf.numPages) {
                                // 所有页面渲染完成，生成压缩后的Blob
                                let compressedPDF = new Blob(canvasList, { type: 'application/pdf' });
                                resolve(compressedPDF);
                            }
                        });
                    }
                });
            };
        });
    }

    // 封装PDF压缩函数
    async function compressPDFs(data) {
        async function loadPdf(pdfFile, range, name) {
            const pdfjsLib = window['pdfjs-dist/build/pdf'];
            pdfjsLib.GlobalWorkerOptions.workerSrc = "/js/compressPDF/pdf.worker.min-2.5.207.js";


            const pdfBlob = await pdfToImages(pdfFile, range, name);
            return pdfBlob;
        }

        async function pdfToImages(pdfFile, initialQualityIndex, name) {
            let imgData = [];
            const qualityLevels = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1];
            let qualityIndex = Math.min(Math.max(initialQualityIndex, 0), qualityLevels.length - 1);


            return new Promise(async (resolve) => {
                const arrayBuffer = await pdfFile.arrayBuffer();
                const pdfDoc = await pdfjsLib.getDocument({data: arrayBuffer}).promise;
                const totalPages = pdfDoc.numPages;

                async function processPages(currentQualityIndex) {
                    imgData = [];
                    const currentQuality = qualityLevels[currentQualityIndex];

                    for(let i = 1; i <= totalPages; i++) {
                        const page = await pdfDoc.getPage(i);
                        const viewport = page.getViewport({scale: 1.5});
                        const canvas = document.createElement('canvas');
                        canvas.width = viewport.width;
                        canvas.height = viewport.height;

                        await page.render({
                            canvasContext: canvas.getContext('2d'),
                            viewport
                        }).promise;

                        imgData.push({
                            src: canvas.toDataURL('image/jpeg', currentQuality),
                            width: canvas.width,
                            height: canvas.height
                        });
                    }

                    const pdfBlob = await imgDataToPdf(name, imgData);
                    return pdfBlob;
                }

                while (qualityIndex < qualityLevels.length) {
                    const compressedPdf = await processPages(qualityIndex);
                    if (compressedPdf.size <= 20 * 1024 * 1024) {  // 20MB in bytes
                        resolve(compressedPdf);
                        return;
                    }
                    qualityIndex++;
                }

                // If we can't compress to under 20MB, return the smallest version
                const finalPdf = await processPages(8);
                resolve(finalPdf);
            });
        }

        // 将图片数组生成PDF
        async function imgDataToPdf(name, imgData) {
            const doc = new PDFDocument({compress: true});
            const stream = doc.pipe(blobStream());

            imgData.forEach(img => {
                const {src, width, height} = img;

                doc.addPage({size: [width, height]});
                doc.image(src, 0, 0);
            });

            doc.end();

            return new Promise(resolve => {
                stream.on('finish', function () {
                    const pdfBlob = stream.toBlob('application/pdf');
                    const pdfFile = new File([pdfBlob], name, { type: 'application/pdf' });

                    resolve(pdfFile);
                });
            });
        }

        const compressedPdfList = [];

        const compressPromises = Object.values(data).map(async (fileObj) => {
            const file = fileObj.file;

            if (file.type === 'application/pdf') {
                try {
                    const compressedPdf = await loadPdf(file, 0, file.name);
                    compressedPdf.isCom = 1;
                    compressedPdfList.push(compressedPdf);
                    console.log("处理pdf================：", compressedPdf);
                } catch (error) {
                    console.error("处理pdf时出错：", error);
                }
            } else {
                file.isCom = 1;
                compressedPdfList.push(file);
            }
        });

        await Promise.all(compressPromises);
        console.log("处理完成================：",compressPromises);

        return compressedPdfList;
    }


</script>
</body>

</html>