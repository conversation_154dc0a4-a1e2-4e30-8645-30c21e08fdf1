<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('租赁合同新增')" />
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/bootstrap-icons.min.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/fileinput.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/all.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/jquery-editable-select/jquery-editable-select.min.css}" media="all" type="text/css"/>
</head>
<style>
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: left;
        margin-top: 5px;
    }
    .mt10{
        margin-top: 10px;
    }
    .ml10{
        margin-left: 10px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 80px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .infotitle{
        line-height: 30px;
        font-weight: bold;
    }
    .titlebg{
        background: #eff3f9;
        padding: 5px 10px;

    }
    a.del-alink {
        display: block;
        margin: 120px auto 0;
    }
    .bggray{
        background: #f7f8fa;
    }
</style>
<body>
<div class="form-content">
    <form id="form-contract-rent-add" class="form-horizontal" novalidate="novalidate">
        <!--要求提货日期-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="titlebg over">
                            <!--<div class="fl">
                                <a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow()" title="新增行">+</a>
                            </div>-->
                            <div class="fl fw ml10" style="line-height: 26px">租赁合同录入</div>
                        </div>
                        <div class="over">
                            <!--<div class="fl" style="width: 40px">
                                <a class="close-link del-alink" style="background: #fd8481;border-radius: 50%" onclick="removeRowThree(this,0)" title="删除选择行">x</a>
                            </div>-->
                            <div class="fr" style="width: calc(100% - 50px)">
                                <div class="row no-gutter">
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 编号：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入合同编号" class="form-control" name="contractRentList[0].contractNumber" id="contractNumber" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 合同类型：</label>
                                            <div class="flex_right">
                                                <select name="contractRentList[0].contractType" id="contractType" class="form-control valid" aria-invalid="false"  aria-required="true" th:with="type=${@dict.getType('contract_type')}">
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 甲方：</label>
                                            <div class="flex_right">
                                                <input name="contractRentList[0].partyA" id="partyA" placeholder="请输入甲方名称" class="form-control" type="text" maxlength="50" autocomplete="off">
                                                <input name="contractRentList[0].customerId" id="customerId0" type="hidden">
                                                <div class="input-group-btn" style="width:1px;">
                                                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 乙方：</label>
                                            <div class="flex_right">
                                                <input name="contractRentList[0].partyB" id="partyB" placeholder="请输入乙方名称" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 合同名称：</label>
                                            <div class="flex_right">
                                                <input name="contractRentList[0].contractName" id="contractName" placeholder="请输入合同名称" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 版本：</label>
                                            <div class="flex_right">
                                                <input name="contractRentList[0].contractVersion" id="contractVersion" placeholder="请输入版本" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 期限：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入期限" class="form-control" name="contractRentList[0].deadline" id="deadline" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 总页数：</label>
                                            <div class="flex_right">
                                                <input type="text" oninput="this.value=this.value.replace(/\D/g,'').slice(0,6);"
                                                       placeholder="请输入总页数" class="form-control" name="contractRentList[0].totalPage" id="totalPage" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 生效日期：</label>
                                            <div class="flex_right">
                                                <input name="contractRentList[0].effectiveDate" id="effectiveDate0" placeholder="请输入生效日期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 终止日期：</label>
                                            <div class="flex_right">
                                                <input name="contractRentList[0].endDate" id="endDate0" placeholder="请输入终止日期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 对账期：</label>
                                            <div class="flex_right">
                                                <input oninput="this.value=this.value.replace(/\D/g,'').slice(0,6);"
                                                       name="contractRentList[0].reconciliationPeriod" id="reconciliationPeriod" placeholder="请输入对账期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 结算账期约定：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入结算账期约定" class="form-control" name="contractRentList[0].settlementPeriod" id="settlementPeriod" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 金额：</label>
                                            <div class="flex_right">
                                                <input type="text" oninput="onlyNumberThreeDecimal(this)" placeholder="请输入金额" class="form-control" name="contractRentList[0].amount" id="amount" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>
                                    <!--<div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 特殊事项说明：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入特殊事项说明" class="form-control" name="contractRentList[0].specialNotes" id="specialNotes" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>-->

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left">长期有效：</label>
                                            <div class="flex_right" style="">
                                                <label class="radio-box">
                                                    <input type="radio" checked="" value="0" id="longTermEffective0" name="contractRentList[0].longTermEffective">否
                                                </label>
                                                <label class="radio-box">
                                                    <input type="radio" value="1" id="longTermEffective1" name="contractRentList[0].longTermEffective">是
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 合同附件 -->
                                <div class="row no-gutter">
                                    <div class="col-sm-12">
                                        <div class="flex">
                                            <label class="flex_left"> 合同附件: </label>
                                            <div class="flex_right">
                                                <!--<input id="file" type="file" multiple name="file">
                                                <input id="contractAttach" name="contractRentList[0].contractAttach" type="hidden">-->

                                                <input id="image" class="form-control" name="image" type="file" multiple>
                                                <input id="contractAttach" name="contractRentList[0].contractAttach" type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 备注 -->
                                <div class="row no-gutter">
                                    <div class="col-sm-12">
                                        <div class="flex">
                                            <label class="flex_left">备注：</label>
                                            <div class="flex_right">
                                                <textarea name="contractRentList[0].remark" id="remark" maxlength="200" class="form-control valid" rows="2"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<!--<th:block th:include="include :: bootstrap-fileinput-js"/>-->
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/fileinput.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/locales/zh.js}"></script>
<script th:src="@{/ajax/libs/jquery-editable-select/jquery-editable-select.js}"></script>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput/plugins/piexif.min.js}"></script>
<script th:src="@{'/js/wecom.js'}"></script>


<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>

<script th:inline="javascript">

    var prefix = ctx + "contract_rent";
    var uploadCache = {};
    var contractAttach = $("#contractAttach").val()
    var contractAttachSet = new Set();
    $(function () {
        /*var image1 = {
            maxFileCount: 5,
            publish: "cmt",
            fileType: "file",
            maxFileSize: 20480,
        };
        $.file.initAddFiles("image", "contractAttach", image1);*/

        /*let option = {
            theme: "explorer-fa5", //主题
            language: 'zh',
            uploadUrl: ctx + "common/uploadBatch",  //上传的地址
            //deleteUrl: ctx + "common/deleteImage",
            uploadExtraData: {key: "image"},   //上传id，传入后台的参数
            deleteExtraData: {key: 'fileId'},
            // extra" {key: ''}, // 上面两个一致则可使用该字段？
            enctype: 'multipart/form-data',
            allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
            initialPreviewAsData: true,
            overwriteInitial: false,
            //initialPreviewConfig: [
            //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
            //],
            dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
            maxFileSize: 1024*20,//单位为kb，如果为0表示不限制文件大小
            maxFileCount: 5, //表示允许同时上传的最大文件个数
            showUpload: false,  // 不显示上传按钮，选择后直接上传
            //previewClass:"uploadPreview",
            minFileSize: 5, // 5KB
            previewFileIcon: '<i class="fa fa-file"></i>',
            allowedPreviewTypes: ['image'],
            showClose: false,  //是否显示右上角叉按钮
            showUpload: false, //是否显示下方上传按钮
            showRemove: false, // 是否显示下方移除按钮
            //autoReplace: true,
            showPreview: true,//是否显示预览(false=只剩按钮)
            showCaption: false,//底部上传按钮左侧文本
            validateInitialCount: true,
            removeFromPreviewOnError:true,//当选择的文件不符合规则时，例如不是指定后缀文件、大小超出配置等，选择的文件不会出现在预览框中，只会显示错误信息
            uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
            fileActionSettings: {
                showUpload: false,		//每个文件的上传按钮
                showDrag: false,
                //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
            },
        }
        $("#image").fileinput(option).on("filebatchselected", function (e, files) {
            $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
        }).on("fileuploaded", function (event, data, previewId, index) {
            //单个上传成功事件
            console.log("fileuploaded", event, data, previewId, index)
            var code = data.response.code;
            if (code !== 0) {
                $.modal.closeLoading();
                $.modal.alertError("上传失败：" + data.response.msg);
                return;
            }
            uploadCache[previewId] = data.response.tid;
            console.log(uploadCache)
        }).on('filesuccessremove', function (event, previewId, index) {
            //上传后删除事件
            console.log("filesuccessremove", event, previewId, index)
            //delete cache[previewId] //bug
            //fileArr.splice(index, 1) //bug
            //$(this).fileinput('clear');
            //$('[name="' + hideName + '"]').val('')
            var tid = uploadCache[previewId];
            $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                console.log(result)
            }, 'json')
            delete uploadCache[previewId]
        });*/

        //new
        function HashMap(){// 自己创建一个HashMap，不然保存到后台顺序是乱的
            this.map = {};
        }
        HashMap.prototype = {
            set : function(key , value){// 向Map中增加元素（key, value)
                this.map[key] = value;
            },
            get : function(key){ //获取指定Key的元素值Value，失败返回Null
                if(this.map.hasOwnProperty(key)){
                    return this.map[key];
                }
                return null;
            },
            remove : function(key){ // 删除指定Key的元素，成功返回True，失败返回False
                if(this.map.hasOwnProperty(key)){
                    return delete this.map[key];
                }
                return false;
            },
            removeAll : function(){  //清空HashMap所有元素
                this.map = {};
            },
            keySet : function() { //获取Map中所有KEY的数组（Array）
                var _keys = [];
                for (var i in this.map) {
                    _keys.push(i);
                }
                return _keys;
            },
            maxSize : function() {
                var length = 0;
                for (var i in this.map) {
                    length = length + 1;
                }
                return length;
            }
        };
        HashMap.prototype.constructor = HashMap;
        window.m = new HashMap();
        window.key;
        window.value;

        $("#image").fileinput({
            theme: "explorer-fa5", //主题
            language: 'zh',
            uploadUrl: ctx + "common/uploadBatch",//上传的地址
            deleteUrl: ctx + "/common/deleteImageByTid",
            uploadExtraData: {key: "image"},   //上传id，传入后台的参数
            deleteExtraData: {key: 'tid'},
            uploadAsync: true, //默认异步上传   多文件上传可配置同步或异步
            showPreview: true, //是否显示预览,不写默认为true
            showUpload: false, //是否显示上传按钮,跟随文本框那个
            showRemove: false, //显示移除按钮,跟随文本框的那个
            showCaption: true,//是否显示标题,就是那个文本框
            dropZoneEnabled: true,//是否显示拖拽区域，默认不写为true，但是会占用很大区域
            showClose: false,  //关闭右上角清空按钮 那个小x
            overwriteInitial: false,  //新增图片追加
            maxFileSize: 1024*20,//单位为kb，如果为0表示不限制文件大小
            minFileCount: 0,
            maxFileCount: 5, //表示允许同时上传的最大文件个数
            enctype: 'multipart/form-data',
            validateInitialCount: true,
            removeFromPreviewOnError:true,//当选择的文件不符合规则时，例如不是指定后缀文件、大小超出配置等，选择的文件不会出现在预览框中，只会显示错误信息
            previewFileType: "image",
            browseClass: "btn btn-default",
            browseIcon: "<i class=\"glyphicon glyphicon-picture\"></i>",
            previewFileIcon: "<i class='glyphicon glyphicon-king'></i>",
            msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！",
            allowedFileTypes: ['image', 'pdf'],//配置允许文件上传的类型
            allowedPreviewTypes: ['image', 'pdf'],//配置所有的被预览文件类型
            allowedPreviewMimeTypes: ['jpg', 'png', 'gif', 'jpeg', 'pdf'], //控制被预览的所有mime类型
            allowedFileExtensions: ['jpg', 'png', 'gif', 'jpeg', 'pdf'], //接收的文件后缀
            previewSettings: {
                //image: {width: "100px", height: "100px"}
            },
            layoutTemplates: {
                // actionDelete:'', //去除上传预览的缩略图中的删除图标
                actionUpload: ''//去除上传预览缩略图中的上传图片；
                //actionZoom: '' //去除上传预览缩略图中的查看详情预览的缩略图标。
            }
        }).on('filebatchselected',function(event,data,previewId,index){ //选中自动上传
            $(this).fileinput("upload");
        }).on("filebatchuploadsuccess", function(event, data, previewId, index) { //开启同步上传默认走这个

        }).on("fileuploaded", function(event, data, previewId) {//异步图片上传  开启异步默认走这个  //一张图片调用一次
            console.log("fileuploaded", event, data, previewId)
            if(data.response){
                key = previewId;
                value = data.response.tid;
                m.set(key, value);
                //console.log(m)
                contractAttachSet.add(value)
            }
        }).on('filepreremove', function (event, id, index) {//未上传 删除

        }).on('filesuccessremove', function (event, data, index, previewId) {//上传成功后 删除
            //data中存的previewId,找到previewId对用的tid,删除
            console.log("filesuccessremove", event, data, index, previewId)
            let removeTid = m.get(data);
            contractAttachSet.delete(removeTid)
            m.remove(data)
            //服务器删除图片
            $.post(ctx + 'common/deleteImageByTid', {tid: removeTid}, function (result) {
                console.log(result)
            }, 'json')
        }).on('filepredelete', function(event, key, jqXHR, data) {//删除图片之前处罚事件  用于修改时的操作
            if(!confirm("确定删除原文件？删除后不可恢复")){
                return false;
            }
        })
    });


    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#effectiveDate0").removeAttr("lay-key");
        $("#endDate0").removeAttr("lay-key");
        $("#extensionDate0").removeAttr("lay-key");
        laydate.render({
            elem: '#effectiveDate0',
            theme: 'molv',
            type: 'date',
            format: 'yyyy-MM-dd',
            value: new Date()
        });
        laydate.render({
            elem: '#endDate0',
            type: 'date',
            format: 'yyyy-MM-dd',
            value: new Date()
        });
        laydate.render({
            elem: '#extensionDate0',
            type: 'date',
            format: 'yyyy-MM-dd',
            value: new Date()
        });
    })


    /*var partyA = $("#partyA").bsSuggest({
             indexId: 4, //data.value 的第几个数据，作为input输入框的内容
             indexKey: 0, //data.value 的第几个数据，作为input输入框的内容
             idField: 'customerId',                    //每组数据的哪个字段作为 data-id，优先级高于 indexId 设置（推荐）
             keyField: 'custName',                   //每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
             allowNoKeyword: false, //是否允许无关键字时请求数据
             multiWord: false, //以分隔符号分割的多关键字支持separator: ",", //多关键字支持时的分隔符，默认为空格
             getDataMethod: "url", //获取数据的方式，总是从 URL 获取
             effectiveFields:["custName","contact","provinceName","cityName"],
             effectiveFieldsAlias:{custName: "公司名称",contact:"联系人",provinceName:"省份",cityName:"城市"},
             showHeader: true,
             //listAlign:'right',        //提示列表对齐位置，left/right/auto
             hideOnSelect: true,            // 鼠标从列表单击选择了值时，是否隐藏选择列表
             inputWarnColor: 'rgba(255,0,0,.1)', //输入框内容不是下拉列表选择时的警告色
             url: ctx + 'client/listClient?custName=', //custAbbr /!*优先从url ajax 请求 json 帮助数据，注意最后一个参数为关键字请求参数*!/
             processData: function(json){// url 获取数据时，对数据的处理，作为 getData 的回调函数
                 var i, len, data = {value: []};
                 if(!json || json.rows.length == 0) {
                     return false;
                 }
                 len = json.rows.length;
                 for (i = 0; i < len; i++) {
                     data.value.push({
                         "custName": json.rows[i].custName,
                         "contact": json.rows[i].contact,
                         "provinceName":json.rows[i].provinceName,
                         "cityName":json.rows[i].cityName,
                         "customerId":json.rows[i].customerId
                     });
                 }
                 return data;
             }
    }).on('onSetSelectValue', function (e, keyword, data) {
        $('#customerId0').val(data.customerId);
    }).on("onUnsetSelectValue",function (e) {
        $('#customerId0').val("");
    });*/

    /**
     * 数字 保留两位小数
     */
    function onlyNumberThreeDecimal (obj) {
        //得到第一个字符是否为负号
        var t = obj.value.charAt(0);
        //先把非数字的都替换掉，除了数字和.
        obj.value = obj.value.replace(/[^\d\.]/g, '');
        //必须保证第一个为数字而不是.
        obj.value = obj.value.replace(/^\./g, '');
        //保证只有出现一个.而没有多个.
        obj.value = obj.value.replace(/\.{2,}/g, '.');
        //保证.只出现一次，而不能出现两次以上
        obj.value = obj.value.replace('.', '$#$').replace(/\./g, '').replace(
            '$#$', '.');
        obj.value = obj.value.slice(0, 15);
        // 只能输入两个小数
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d).*$/,'$1$2.$3');
        //如果第一位是负号，则允许添加
        if (t == '-') {
            obj.value = '-' + obj.value;
        }
    }

    /**
     * 提交
     */
    function submitHandler() {
        /*console.log(JSON.stringify($('#form-contract-rent-add').serializeArray()))
        console.log("===========")
        console.log($('#form-contract-rent-add').serializeArray())*/
        if ($.validate.form()) {
            //$("#image").fileinput('upload');
            //jQuery.subscribe("cmt", commit);
            var submitIds = []
            for (let x of contractAttachSet) {
                submitIds.push(x)
            }
            /*for (const uploadCacheKey in contractAttachSet) {
                submitIds.push(uploadCacheKey)
            }*/
            /*if (submitIds.length == 0) {
                $.modal.msgError("请选择上传文件");
                return
            }*/
            $("#contractAttach").val(submitIds.join(","))
            commit()
        }
    }

    function commit() {
        $.operate.saveTab(prefix + "/addContractRent", $('#form-contract-rent-add').serialize());
        /*$.ajax({
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            url: prefix + "/addContractRent",
            data: JSON.stringify($('#form-contract-rent-add').serializeArray()),//JSON.stringify(data.field),
            dataType: 'json',
            success: function(result) {
                console.log(result)
            }
        });*/
    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }
</script>
</body>

</html>