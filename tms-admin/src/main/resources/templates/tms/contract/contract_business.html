<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('业务合同列表')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
</head>
<style>
    .row-margin-top {
        margin-top: 10px;
    }
    .select-table .table td {
        /* 超出部分隐藏 */
        overflow:hidden;
        /* 超出部分显示省略号 */
        text-overflow:ellipsis;
        /*规定段落中的文本不进行换行 */
        white-space:nowrap;
        /* 配合宽度来使用 */
        height:40px;
    }
    .fcff3{
        color: #ff3636;
    }
    .sm_icon{
        width: 18px;
        height: 18px;
        display: inline-block;
        line-height: 18px;
        text-align: center;
        font-size: 16px;
        color: #ed5565;
    }
    .sm_text{
        display: inline-block;
        margin-left: 10px;
        line-height: 40px;
        color: #ed5565;
    }
    .menu_label {
        float: left;
        border: 1px rgb(67, 150, 202) solid;
        padding-left: 10px;
        padding-right: 10px;
        padding-bottom: 3px;
        padding-top: 1px;
        border-radius: 15px;
        font-size: 10px;
        margin-left: 15px;
        margin-top: 8px;
        color: rgb(67, 150, 202);
        /* font-weight: bold; */
        margin-bottom: 7px;
    }
    .menu_label:active {
        background-color: rgb(67, 150, 202);
        color: white;
    }
    .menu_label:focus {
        background-color: rgb(67, 150, 202);
        color: white;
    }
    .flex{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .pa2 {
        margin-left: 5px;
        margin-right: 0;
    }
</style>
<body class="gray-bg">

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="park-form">

                    <div class="row no-gutter sm">
                        <div class="col-md-5 col-sm-10">
                            <span class="sm_icon">
                                <i class="fa fa-bell-o"></i>
                            </span>
                            <span class="sm_text" id="warn_msg">共有</span>
                            <span class="fcff3" th:text="${expiredCount}"></span>
                            <span class="sm_text" id="warn_msg">个业务合同即将到期或已到期，请及时更新</span>
                        </div>

                    </div>

                    <div class="row">
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">发货单编号：</label>-->
<!--                                <div class="col-sm-12">-->
                                    <input type="text" class="form-control" placeholder="请输入合同编号"  name="contractNumber">
<!--                                </div>-->
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">客户发货单号：</label>-->
<!--                                <div class="col-sm-12">-->
                                    <input type="text" class="form-control" placeholder="请输入甲方/乙方名称" id="partyAB" name="partyAB">
<!--                                </div>-->
                            </div>
                        </div>
<!--                        <div class="col-md-2 col-sm-4">-->
<!--                            <div class="form-group">-->
<!--                                &lt;!&ndash;                            <label class="col-sm-4">发货单编号：</label>&ndash;&gt;-->
<!--                                <div class="col-sm-12">-->
<!--                                    <input type="text" class="form-control" placeholder="请输入乙方名称" id="partyB" name="partyB">-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->

                        <div class="col-md-4 col-sm-8">
                            <div class="form-group">
                                <!--                            <label class="col-sm-5">要求提货日期：</label>-->
<!--                                <div class="col-sm-12">-->
                                    <div class="input-group">
                                        <span class="input-group-addon">从</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder="生效日期开始" style="font-size: 14px" id="effectiveDateStart" name="effectiveDateStart" >
                                        <span class="input-group-addon">到</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder="生效日期结束" style="font-size: 14px" id="effectiveDateEnd" name="effectiveDateEnd">
                                    </div>
<!--                                </div>-->
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <select name="isDataComplete" id="isDataComplete" class="form-control valid" aria-invalid="false"  aria-required="true">
                                    <option value="">-- 数据是否已验证 --</option>
                                    <option value="0">未验证</option>
                                    <option value="1">已验证</option>
                                </select>

                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">车长：</label>-->
                                <div class="col-sm-12">
                                    <select name="isLinkCust" id="isLinkCust" class="form-control valid" aria-invalid="false"  aria-required="true">
                                        <option value="">-- 是否关联客户 --</option>
                                        <option value="0">未关联客户</option>
                                        <option value="1">已关联客户</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row row-margin-top">
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">车长：</label>-->
<!--                                <div class="col-sm-12">-->
                                    <select name="longTermEffective" id="longTermEffective" class="form-control valid" aria-invalid="false"  aria-required="true" required>
                                        <option value="">-- 是否长期有效 --</option>
                                        <option value="0">非长期</option>
                                        <option value="1">长期</option>
                                    </select>
<!--                                </div>-->
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
<!--                                <div class="col-sm-12">-->
                                    <select name="temporary" id="temporary" class="form-control valid" aria-invalid="false"  aria-required="true" >
                                        <option value="">-- 合同状态 --</option>
                                        <option value="1">已过期</option>
                                        <option value="2">即将过期</option>
                                    </select>
<!--                                </div>-->
                            </div>
                        </div>

                        <div class="col-md-4 col-sm-8">
                            <div class="form-group">
                                <!--                            <label class="col-sm-5">要求提货日期：</label>-->
<!--                                <div class="col-sm-12">-->
                                    <div class="input-group">
                                        <span class="input-group-addon">从</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder="终止日期开始" style="font-size: 14px" id="endDateStart" name="endDateStart" >
                                        <span class="input-group-addon">到</span>
                                        <input autocomplete="off" type="text" class="form-control laydate-icon" placeholder="终止日期结束" style="font-size: 14px" id="endDateEnd" name="endDateEnd">
                                    </div>
<!--                                </div>-->
                            </div>
                        </div>
                        <div class="col-md-2 col-sm-4">
                            <div class="form-group">
                                <select name="existsFile" id="existsFile" class="form-control valid" aria-invalid="false"  aria-required="true" >
                                    <option value="">-- 是否存在合同 --</option>
                                    <option value="0">未上传</option>
                                    <option value="1">已上传</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-2 col-sm-6">
<!--                            <label class="col-sm-4"></label>-->
                            <div class="form-group">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" shiro:hasPermission="tms:contract_business:add">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="tms:contract_business:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="tms:contract_business:import">
                    <i class="fa fa-upload"></i> 导入
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:contract_business:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: ztree-js" />
<th:block th:include="include :: pdf-js" />
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">
    var contract_type = [[${@dict.getType('contract_type')}]];//合同类型
    var editFlag = [[${@permission.hasPermi('tms:contract_business:edit')}]];
    var removeFlag = [[${@permission.hasPermi('tms:contract_business:remove')}]];
    var prefix = ctx + "contract_business";

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#effectiveDateStart").removeAttr("lay-key");
        $("#effectiveDateEnd").removeAttr("lay-key");
        laydate.render({
            elem: '#effectiveDateEnd'
            , type: 'date'
        });

        laydate.render({
            elem: '#effectiveDateStart'
            , type: 'date'
        });
        laydate.render({
            elem: '#endDateStart'
            , type: 'date'
        });

        laydate.render({
            elem: '#endDateEnd'
            , type: 'date'
        });
        laydate.render({
            elem: '#payStartTime'
            , type: 'date'
        });

        laydate.render({
            elem: '#payEndTime'
            , type: 'date'
        });


        // laydate.render({
        //     elem: '#starttime', //指定元素
        //     //format: 'yyyy-MM-dd',
        //     //isInitValue: false,
        //     type: 'datetime',
        //     range: '到'
        //     ,format: 'yyyy年M月d日H时m分s秒'
        // });
        // laydate.render({
        //     elem: '#endtime', //指定元素
        //     //format: 'yyyy-MM-dd',
        //     //isInitValue: false,
        //     type: 'datetime',
        //     range: true,
        //     done: function (value, date, endDate) {
        //     }
        // });
    })

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 185 });

        queryParkWayBillList();
        $.table.hideColumn("id");
    });

    //初始化查询条件传参
    queryParams = function(params) {
        var search = {};
        $.each($("#park-form").serializeArray(), function(i, field) {
            search[field.name] = field.value;
        });
        search.pageSize = params.limit;
        search.pageNum = params.offset / params.limit + 1;
        search.searchValue = params.search;
        search.orderByColumn = params.sort;
        search.isAsc = params.order;
        return search;
    }

    function queryParkWayBillList() {
        var options = {
            url: prefix + "/page",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showToggle:false,
            showColumns:true,
            //sortName: "orderDate",
            //sortOrder: "desc",
            modalName: "业务合同",
            fixedColumns: true,
            showFooter:true,
            fixedNumber:3,
            height:"auto",
            showExport: true,
            clickToSelect: true,
            exportTypes:['excel','csv'],
            exportOptions:{
                ignoreColumn: [0,1],
                fileName:"业务合同列表"
            },
            queryParams: queryParams,
            columns: [
                {
                    checkbox:true,
                },
                {
                    title: '主键',
                    align: 'left',
                    field : 'id'  //主键加载完成就隐藏  多选删除的时候是按照第一列的值是否相同来判断一共有几项的
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a> ');
                        actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="bindCust(\'' + row.id + '\')"><i class=""></i>绑定客户</a> ');
                        return actions.join('');
                    }
                },
               
                {
                    title: '编号',
                    align: 'left',
                    field : 'contractNumber',
                    formatter: function status(value, row, index) {
                        let longTermEffective = "";
                        if (row.longTermEffective === '0') {
                            // longTermEffective = '<span class="label label-danger pa2" style="cursor:pointer;" title="非长期有效">非长期</span>';
                        }else {
                            longTermEffective = '<span class="label label-primary pa2" style="cursor:pointer;" title="长期有效">长期</span>';
                        }

                        let isDataComplete = ""
                        if (row.isDataComplete == 0) {
                            isDataComplete = `<span class="label label-warning pa2" style="cursor:pointer;" onclick="changeIsDataComplete('${row.id}')" title="还有数据未明确，点击可修改为已验证">未验证</span>`;
                        }
                        
                        return `<div class="flex"><div>`+value+`</div>`+longTermEffective+`${isDataComplete}</div>`;
                    }
                },

                // {
                //     title: '是否长期有效',
                //     align: 'left',
                //     field : 'longTermEffective',
                //     formatter: function (value, row, index) {
                //         if (value === '0') {
                //             return '否';
                //         }else {
                //             return '是';
                //         }
                //     }
                // },
                
                {
                    title: '甲方',
                    align: 'left',
                    field : 'partyA'
                },
                {
                    title: '乙方',
                    align: 'left',
                    field : 'partyB'
                },
                {
                    title: '客户',
                    align: 'left',
                    field : 'customerName',
                    formatter: function (value, row, index) {
                        let custAbbrs = '-'
                        if (row.contractBusinessCustList) {
                            let list = row.contractBusinessCustList;
                            custAbbrs = list.map(cust => cust.custAbbr).join('<br>');
                        }
                        return custAbbrs
                    },
                },
                {
                    title: '合同名称',
                    align: 'left',
                    field : 'contractName'
                },
                {
                    title: '版本',
                    align: 'left',
                    field : 'contractVersion'
                },
                {
                    title: '期限',
                    align: 'left',
                    field : 'deadline'
                },
                {
                    title: '总页数',
                    align: 'left',
                    field : 'totalPage'
                },
                {
                    title: '生效日期',
                    align: 'left',
                    field : 'effectiveDate'
                },
                {
                    title: '终止日期',
                    align: 'left',
                    field : 'endDate'
                },
                {
                    title: '合同延期日',
                    align: 'left',
                    field : 'extensionDate'
                },
                {
                    title: '系统下单延期日',
                    align: 'left',
                    field : 'systemExtensionDate'
                },
                {
                    title: '对账期',
                    align: 'left',
                    field : 'reconciliationPeriod'
                },
                {
                    title: '结算账期约定',
                    align: 'left',
                    field : 'settlementPeriod'
                },
                {
                    title: '特殊事项说明',
                    align: 'left',
                    field : 'specialNotes'
                },
                {
                    title: '备注',
                    align: 'left',
                    field : 'remark'
                },
                /*{
                    title: '附件',
                    align: 'left',
                    field : 'contractAttach'
                },*/
                {
                    field: 'sysUploadFileList',
                    title: '合同附件',
                    formatter: function(value, row, index) {
                        var html = ""
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                let filePath = element.filePath
                                let fileName = element.fileName
                                //"<a href=\"javascript:getProductDetail('"+result[i].spec+"','"+result[i].name+"')\">"
                                let downloadHtml = `<span style="cursor: pointer;margin-right: 15px;" title="点击下载文件" onclick="downloadContract('${filePath}','${fileName}')"><i class="fa fa-download"></i></span>`

                                html = html + downloadHtml + "<span><a href='#' onclick=\"openContract('" + filePath + "','" + fileName  +"')\">" + element.fileName + "</a></span><br>"
                            });
                        }
                        return html;
                    }
                },
               
                /*{
                    title: '创建人',
                    align: 'left',
                    field : 'regUserName'
                },*/
                {
                    title: '创建时间',
                    align: 'left',
                    field : 'regDate'
                },
                {
                    title: '系统编号',
                    align: 'left',
                    field : 'serialNumber'
                },
                // {
                //     title: '合同类型',
                //     align: 'left',
                //     field : 'contractType',
                //     formatter: function status(value, row, index) {
                //         return $.table.selectDictLabel(contract_type, value);
                //     }
                // }
            ]
        };
        $.table.init(options);
    }

    function selectTemporary(value) {
        $("#temporary").val(value)
        $.table.search()
    }

    function reset() {
        $("#temporary").val('')
        $.form.reset()
    }

    function bindCust(id) {
        layer.open({
            type: 2,
            title: '绑定客户',
            closeBtn: 0,
            area: ['90%', '80%'],//弹框大小  屏幕宽度的80%，高度的80%；
            content: ctx + `contract_business/bindCust?id=${id}`,
            btn: ['关闭'],
            // 打开弹窗的回调函数，用于回显页面数据
            success: function () {
            },
            yes: function (index, layero) {
                $.table.refresh();
                layer.close(index);
            }
        })
    }

    function changeIsDataComplete(id) {
        $.modal.confirm("确定将该数据改为已验证吗?", function() {
            var data = { "id": id , "isDataComplete": 1};
            $.operate.submit(ctx + `contract_business/changeIsDataComplete`, "post", "json", data);
        });
    }

    //pdf预览
    function openContract(filePath, fileName) {
        window.open( document.location.protocol + "//" + document.location.host + filePath, fileName);
    }

    function downloadContract(filePath, fileName) {
        const protocol = document.location.protocol; // 确保使用与当前页面相同的协议
        const a = document.createElement('a');
        a.href = protocol + "//" + document.location.host + filePath; // 动态选择协议
        a.download = fileName;
        a.click();
    }

 /*   var partyA = $("#partyA").bsSuggest({
        indexId: 4, //data.value 的第几个数据，作为input输入框的内容
        indexKey: 0, //data.value 的第几个数据，作为input输入框的内容
        idField: 'customerId',                    //每组数据的哪个字段作为 data-id，优先级高于 indexId 设置（推荐）
        keyField: 'custName',                   //每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        allowNoKeyword: false, //是否允许无关键字时请求数据
        multiWord: false, //以分隔符号分割的多关键字支持separator: ",", //多关键字支持时的分隔符，默认为空格
        getDataMethod: "url", //获取数据的方式，总是从 URL 获取
        effectiveFields:["custName","contact","provinceName","cityName"],
        effectiveFieldsAlias:{custName: "公司名称",contact:"联系人",provinceName:"省份",cityName:"城市"},
        showHeader: true,
        //listAlign:'right',        //提示列表对齐位置，left/right/auto
        hideOnSelect: true,            // 鼠标从列表单击选择了值时，是否隐藏选择列表
        inputWarnColor: 'rgba(255,0,0,.1)', //输入框内容不是下拉列表选择时的警告色
        url: ctx + "client/listClient?contractSearch="+"contractSearch" + "&custName=", //custAbbr /!*优先从url ajax 请求 json 帮助数据，注意最后一个参数为关键字请求参数*!/
        processData: function(json){// url 获取数据时，对数据的处理，作为 getData 的回调函数
            console.log("查询数据:")
            console.log(json)
            var i, len, data = {value: []};
            if(!json || json.rows.length == 0) {
                return false;
            }
            len = json.rows.length;
            for (i = 0; i < len; i++) {
                data.value.push({
                    "custName": json.rows[i].custName,
                    "contact": json.rows[i].contact,
                    "provinceName":json.rows[i].provinceName,
                    "cityName":json.rows[i].cityName,
                    "customerId":json.rows[i].customerId
                });
            }
            return data;
        }
    }).on('onSetSelectValue', function (e, keyword, data) {
        $('#customerId0').val(data.customerId);
    }).on("onUnsetSelectValue",function (e) {
        $('#customerId0').val("");
    });*/
</script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <!--<input type="checkbox" id="updateSupport" name="updateSupport" title="如果登录账户已经存在，更新这条数据。"> 是否更新已经存在的合同数据-->
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>