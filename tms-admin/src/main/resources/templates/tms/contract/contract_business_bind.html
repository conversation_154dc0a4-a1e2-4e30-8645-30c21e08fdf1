<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>传输组件</title>
    <th:block th:include="include :: header('')"/>
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        body.gray-bg {
            background-color: #f3f3f4;
        }
        .container-div {
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }
        .edit-container {
            flex-grow: 1;
            background-color: #fff;
            border: 1px solid #e7eaec;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        .transfer-container {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }
        .transfer-row {
            display: flex;
            flex-grow: 1;
            /*margin-top: 15px;*/
        }
        .transfer-column {
            display: flex;
            flex-direction: column;
            flex-basis: 0;
            flex-grow: 5;
        }
        .transfer-buttons {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            flex-grow: 2;
        }
        .search-form {
            /*margin-bottom: 15px;*/
        }
        .search-input {
            margin-left: 0;
        }
        .transfer-button {
            margin: 10px 0;
            padding: 0 10px;
            height: 30px;
            line-height: 30px;
        }
        .layui-table {
            margin-top: 0;
        }
        .layui-table-view {
            flex-grow: 1;
        }

        .layui-input {
            height: 30px;
            line-height: 30px;
        }

        .layui-btn {
            height: 30px;
            line-height: 30px;
            padding: 0 10px;
        }


    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="edit-container">
        <div class="transfer-container" id="transfer-container">
            <div class="transfer-row">
                    <div class="layui-form layui-form-pane">
                        <div class="layui-form-item">
                            <div class="layui-input-inline search-input">
                                <input type="text" name="leftSearch" id="leftSearch" placeholder="请输入客户简称" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-input-inline search-input">
                                <input type="text" name="billingPayable" id="billingPayable" placeholder="请输入开票抬头" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-input-inline search-input">
                                <select name="salesDept" id="salesDept" lay-filter="salesDept">
                                    <option value="">请选择运营组</option>
                                    <option th:each="mapS,status:${salesDept}"
                                            th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                            <button class="layui-btn" id="leftSearchBtn">搜索</button>
                        </div>
                    </div>
            </div>
            <div class="transfer-row">
                <div class="transfer-column">
                    <table id="leftTable" lay-filter="leftTable"></table>
                </div>
                <div class="transfer-buttons">
                    <button class="layui-btn transfer-button" id="moveRight">
                        <i class="layui-icon layui-icon-next"></i>
                    </button>
                    <button class="layui-btn transfer-button" id="moveLeft" style="margin-left:0">
                        <i class="layui-icon layui-icon-prev"></i>
                    </button>
                </div>
                <div class="transfer-column">
                    <table id="rightTable" lay-filter="rightTable"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<div th:include="include :: footer"></div>

<script th:inline="javascript">

    let contractBusinessId = [[${ContractBusinessId}]]

    $(function () {
        layui.use(['table', 'jquery','form'], function(){
            var table = layui.table;
            var $ = layui.jquery;
            var form = layui.form;

            form.on('select(salesDept)', function(data){
                // data.value 就是选中的值
                // 如果选择了默认选项，手动设置为空
                if(data.value === ""){
                    $("#salesDept").val("");
                    form.render('select'); // 重新渲染select
                }
            });

            // 左表配置
            var leftTableIns = table.render({
                elem: '#leftTable'
                ,url: ctx + "client/list" // 左表数据接口
                ,method: 'post'  // 使用POST方法
                ,contentType: 'application/x-www-form-urlencoded'
                ,request: {
                    pageName: 'pageNum',  // 页码的参数名称，默认：page
                    limitName: 'pageSize' // 每页数据量的参数名，默认：limit
                }
                ,parseData: function(res){ //res 即为原始返回的数据
                    return {
                        "code": res.code,
                        "msg": "",
                        "count": res.total,
                        "data": res.rows
                    };
                }
                ,page: {
                    layout: ['prev', 'page', 'next', 'count','limit'],
                }
                ,height: 'full-120'  // 动态高度
                ,id: 'leftTable'
                ,cols: [
                    [
                        {type:'checkbox'}
                        ,{field:'custAbbr', title: '全部客户（客户简称）',templet: function(d){
                            let isEnabledHtml = d.isEnabled == 1 ? `<span class="label label-danger ml5">禁用</span>` : ``
                            return d.custAbbr + isEnabledHtml
                        }}
                        ,{field:'salesDeptName', title: '运营组'}
                        ,{field:'billingPayable',width: 200, title: '开票抬头'}
                    ]
                ]
            });

            // 右表配置
            var rightTableIns = table.render({
                elem: '#rightTable'
                // ,page: false
                ,url: ctx + `contract_business/bindCustList` // 右表数据接口
                ,method: 'post'  // 使用POST方法
                ,contentType: 'application/x-www-form-urlencoded'
                ,request: {
                    pageName: 'pageNum',  // 页码的参数名称，默认：page
                    limitName: 'pageSize' // 每页数据量的参数名，默认：limit
                }
                ,parseData: function(res){ //res 即为原始返回的数据
                    return {
                        "code": res.code,
                        "msg": "",
                        "count": res.total,
                        "data": res.rows
                    };
                }
                ,where: {
                    contractBusinessId: contractBusinessId,
                }
                ,page: {
                    layout: ['prev', 'page', 'next', 'count','limit'],
                }
                ,height: 'full-120'  // 动态高度
                ,id: 'rightTable'
                ,cols: [
                    [
                        {type:'checkbox'}
                        ,{field:'custAbbr', title: '已绑定客户（客户简称）'}
                    ]
                ]
            });

            // 左表搜索功能
            $('#leftSearchBtn').on('click', function(){
                var searchValue = $('#leftSearch').val();
                var salesDept = $('#salesDept').val();
                var billingPayable = $('#billingPayable').val();
                leftTableIns.reload({
                    where: {
                        custAbbr: searchValue,
                        customerId: '',
                        permission: 'sales',
                        salesDept: salesDept,
                        billingPayable: billingPayable
                    },
                });
            });

            // 右表搜索功能
            $('#rightSearchBtn').on('click', function(){
                var searchValue = $('#rightSearch').val();
                rightTableIns.reload({
                    where: {
                        custAbbr: searchValue,
                        contractBusinessId: contractBusinessId,
                    },
                });
            });

            // 向右移动
            $('#moveRight').on('click', function(){
                var checkStatus = table.checkStatus('leftTable');
                var data = checkStatus.data;
                if(data.length === 0){
                    layer.msg('请选择要移动的数据');
                    return;
                }
                let custIds = data.map(item => item.customerId).join(",");
                bindCust(custIds, function (){
                    //刷新列表
                    leftTableIns.reload();
                    rightTableIns.reload();
                });
            });

            // 向左移动
            $('#moveLeft').on('click', function(){
                var checkStatus = table.checkStatus('rightTable');
                var data = checkStatus.data;
                if(data.length === 0){
                    layer.msg('请选择要移动的数据');
                    return;
                }
                let custIds = data.map(item => item.customerId).join(",");

                unbindCust(custIds,function (){
                    //刷新列表
                    leftTableIns.reload();
                    rightTableIns.reload();
                })
            });
        });
    });


    function bindCust(custIds, callback) {
        let data = {};
        data["contractBusinessId"] = contractBusinessId
        data["customerIds"] = custIds

        $.ajax({
            url: ctx + "contract_business/bindCust",
            type: "post",
            dataType: "json",
            // contentType: "application/json; charset=utf-8",
            data: data,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
                $.modal.disable();
            },
            success: function (result) {
                if (result.code == 0) {
                    $.modal.msgSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
                $.modal.enable();

                callback()
            }
        });

    }

    function unbindCust(custIds, callback) {
        let data = {};
        data["contractBusinessId"] = contractBusinessId
        data["customerIds"] = custIds
        $.ajax({
            url: ctx + "contract_business/unbindCust",
            type: "post",
            dataType: "json",
            // contentType: "application/json; charset=utf-8",
            data: data,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
                $.modal.disable();
            },
            success: function (result) {
                if (result.code == 0) {
                    $.modal.msgSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
                $.modal.enable();

                callback()
            }
        });
    }

</script>
</body>
</html>