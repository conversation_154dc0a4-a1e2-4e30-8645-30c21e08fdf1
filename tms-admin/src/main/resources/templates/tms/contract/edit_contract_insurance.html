<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('保险合同修改')" />
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/bootstrap-icons.min.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/fileinput.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/css/all.css}" crossorigin="anonymous">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.css}" media="all" type="text/css"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/jquery-editable-select/jquery-editable-select.min.css}" media="all" type="text/css"/>
</head>
<style>
    .fcff{
        color: #ff1f1f;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: left;
        margin-top: 5px;
    }
    .mt10{
        margin-top: 10px;
    }
    .ml10{
        margin-left: 10px;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 80px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
    .fw{
        font-weight: bold;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .panel-body {
        padding: 5px 10px 10px 10px;
    }
    .infotitle{
        line-height: 30px;
        font-weight: bold;
    }
    .titlebg{
        background: #eff3f9;
        padding: 5px 10px;

    }
    a.del-alink {
        display: block;
        margin: 120px auto 0;
    }
    .bggray{
        background: #f7f8fa;
    }
</style>
<body>
<div class="form-content">
    <form id="form-contract-insurance-edit" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" id="id" name="contractInsuranceList[0].id" th:value="${contractInsurance.id}">
        <!--要求提货日期-->
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="titlebg over">
                            <!--<div class="fl">
                                <a class="collapse-link" style="font-size: 22px;color: #1ab394;" onclick="insertRow()" title="新增行">+</a>
                            </div>-->
                            <div class="fl fw ml10" style="line-height: 26px">保险合同录入</div>
                        </div>
                        <div class="over">
                            <!--<div class="fl" style="width: 40px">
                                <a class="close-link del-alink" style="background: #fd8481;border-radius: 50%" onclick="removeRowThree(this,0)" title="删除选择行">x</a>
                            </div>-->
                            <div class="fr" style="width: calc(100% - 50px)">
                                <div class="row no-gutter">
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left">  编号：</label>
                                            <div class="flex_right">
                                                <input type="text" placeholder="请输入合同编号" class="form-control" th:value="${contractInsurance.contractNumber}" name="contractInsuranceList[0].contractNumber" id="contractNumber" maxlength="50" autocomplete="off"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left">  状态：</label>
                                            <div class="flex_right">
                                                <select name="contractInsuranceList[0].status" id="status" class="form-control valid" aria-invalid="false"  aria-required="true">
                                                    <option value="1" th:selected="${contractInsurance.status == '1'}">执行</option>
                                                    <option value="2" th:selected="${contractInsurance.status == '2'}">到期</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left">  保险单号：</label>
                                            <div class="flex_right">
                                                <input th:value="${contractInsurance.insuranceNumber}" name="contractInsuranceList[0].insuranceNumber" id="insuranceNumber" placeholder="请输入保险单号" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left">  保险公司：</label>
                                            <div class="flex_right">
                                                <input name="contractInsuranceList[0].partyA" id="partyA" placeholder="请输入保险公司" th:value="${contractInsurance.partyA}" class="form-control" type="text" maxlength="50" autocomplete="off">
                                                <input name="contractInsuranceList[0].partyAId" id="partyAId0" type="hidden">
                                                <div class="input-group-btn" style="width:1px;">
                                                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left">  被保险人：</label>
                                            <div class="flex_right">
                                                <input name="contractInsuranceList[0].partyB" id="partyB" th:value="${contractInsurance.partyB}" placeholder="请输入被保险人" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left">  保险标的：</label>
                                            <div class="flex_right">
                                                <input name="contractInsuranceList[0].subjectMatter" id="subjectMatter" th:value="${contractInsurance.subjectMatter}" placeholder="请输入保险标的" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row no-gutter">
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 生效日期：</label>
                                            <div class="flex_right">
                                                <input name="contractInsuranceList[0].insuranceStartDate" id="insuranceStartDate0" th:value="${#dates.format(contractInsurance.insuranceStartDate, 'yyyy-MM-dd')}" placeholder="请输入生效日期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left"> 终止日期：</label>
                                            <div class="flex_right">
                                                <input name="contractInsuranceList[0].insuranceEndDate" id="insuranceEndDate0" th:value="${#dates.format(contractInsurance.insuranceEndDate, 'yyyy-MM-dd')}" placeholder="请输入终止日期" class="form-control" type="text" maxlength="50" autocomplete="off">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="flex">
                                            <label class="flex_left">长期有效：</label>
                                            <div class="flex_right">
                                                <label class="radio-box">
                                                    <input type="radio" checked="" value="0" th:checked="${contractInsurance.longTermEffective == '0'}" id="longTermEffective0" name="contractInsuranceList[0].longTermEffective">否
                                                </label>
                                                <label class="radio-box">
                                                    <input type="radio" value="1" th:checked="${contractInsurance.longTermEffective == '1'}" id="longTermEffective1" name="contractInsuranceList[0].longTermEffective">是
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 合同附件 -->
                                <div class="row no-gutter">
                                    <div class="col-sm-12">
                                        <div class="flex">
                                            <label class="flex_left"> 合同附件: </label>
                                            <div class="flex_right">
                                                <!--<input id="file" type="file" multiple name="file">
                                                <input id="contractAttach" name="contractInsuranceList[0].contractAttach" type="hidden">-->

                                                <input id="image" class="form-control" name="image" type="file" multiple>
                                                <input id="contractAttach" name="contractInsuranceList[0].contractAttach" type="hidden">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 备注 -->
                                <div class="row no-gutter">
                                    <div class="col-sm-12">
                                        <div class="flex">
                                            <label class="flex_left">备注：</label>
                                            <div class="flex_right">
                                                <textarea name="contractInsuranceList[0].remark" id="remark" th:text="${contractInsurance.remark}" maxlength="200" class="form-control valid" rows="2"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/fileinput.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/themes/explorer-fa5/theme.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput@5.5.3/locales/zh.js}"></script>
<script th:src="@{/ajax/libs/jquery-editable-select/jquery-editable-select.js}"></script>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-fileinput/plugins/piexif.min.js}"></script>
<script th:src="@{'/js/wecom.js'}"></script>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>

<script th:inline="javascript">

    var prefix = ctx + "contract_insurance";
    var uploadCache = {};
    $(function () {
        var contractAttach = [[${contractFiles}]];
        /*var image1 = {
            maxFileCount: 5,
            publish: "cmt",
            fileType: "file",
            maxFileSize: 200000
        };

        if(contractAttach == null){
            $.file.initAddFiles("image", "contractAttach", image1);
        }else{
            $.file.loadEditFiles("image", "contractAttach", contractAttach, image1);
        }*/

        var initialPreview = [];

        var initialPreviewConfig = [];

        /*if(pmsFujian[0].fjid == null){
            pmsFujian = [];
        }*/
        if(contractAttach != null && contractAttach != ''){
            for(var i = 0;i<contractAttach.length;i++) {
                var Fujian = contractAttach[i];
                if(Fujian != null && Fujian !=''){
                    var delFujian = new Object();
                    delFujian = generFilDel(Fujian);
                    //把文件的路径传到这个数组里面
                    initialPreview.push(Fujian.filePath);
                    //把文件的信息传到这个数组里面
                    initialPreviewConfig.push(delFujian);

                    //初始化
                    //contractAttachSet.add(Fujian.tid)
                }
            }
        }
        let option = {
            theme: "explorer-fa5", //主题
            language: 'zh',
            uploadUrl: ctx + "common/uploadBatch",  //上传的地址
            //deleteUrl: ctx + "common/deleteImage",
            uploadExtraData: {key: "image"},   //上传id，传入后台的参数
            deleteExtraData: {key: 'fileId'},
            // extra" {key: ''}, // 上面两个一致则可使用该字段？
            enctype: 'multipart/form-data',
            //allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
            allowedFileTypes: ['image', 'pdf'],//配置允许文件上传的类型
            allowedPreviewTypes: ['image', 'pdf'],//配置所有的被预览文件类型
            allowedPreviewMimeTypes: ['jpg', 'png', 'gif', 'jpeg', 'pdf'], //控制被预览的所有mime类型
            allowedFileExtensions: ['jpg', 'png', 'gif', 'jpeg', 'pdf'], //接收的文件后缀
            initialPreviewAsData: true,
            initialPreview: initialPreview,
            initialPreviewConfig: initialPreviewConfig,
            overwriteInitial: false,
            //initialPreviewConfig: [
            //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
            //],
            dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
            maxFileSize: 1024*20,//单位为kb，如果为0表示不限制文件大小
            maxFileCount: 5, //表示允许同时上传的最大文件个数
            showUpload: false,  // 不显示上传按钮，选择后直接上传
            //previewClass:"uploadPreview",
            minFileSize: 5, // 5KB
            previewFileIcon: '<i class="fa fa-file"></i>',
            allowedPreviewTypes: ['image'],
            showClose: false,  //是否显示右上角叉按钮
            showUpload: false, //是否显示下方上传按钮
            showRemove: false, // 是否显示下方移除按钮
            //autoReplace: true,
            //showPreview: false,//是否显示预览(false=只剩按钮)
            showCaption: false,//底部上传按钮左侧文本
            uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
            fileActionSettings: {
                showUpload: false,		//每个文件的上传按钮
                showDrag: false,
                //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
            },
        }
        $("#image").fileinput(option).on("filebatchselected", function (e, files) {
            $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
        }).on("fileuploaded", function (event, data, previewId, index) {
            //单个上传成功事件
            console.log("fileuploaded", event, data, previewId, index)
            var code = data.response.code;
            if (code !== 0) {
                $.modal.closeLoading();
                $.modal.alertError("上传失败：" + data.response.msg);
                return;
            }
            uploadCache[previewId] = data.response.tid;
            console.log(uploadCache)
        }).on('filesuccessremove', function (event, previewId, index) {
            //上传后删除事件
            console.log("filesuccessremove", event, previewId, index)
            //delete cache[previewId] //bug
            //fileArr.splice(index, 1) //bug
            //$(this).fileinput('clear');
            //$('[name="' + hideName + '"]').val('')
            var tid = uploadCache[previewId];
            $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                console.log(result)
            }, 'json')
            delete uploadCache[previewId]
        }).on('filepredelete', function(event, key, jqXHR, data) {//删除图片之前处罚事件  用于修改时的操作
            /*if(!confirm("确定删除原文件？删除后不可恢复")){
                console.log("1111")
                return;
            }*/
            var abort = true;
            if(confirm("确定删除原文件？删除后不可恢复")){
                abort = false;
            }
            return abort;
        });
    });

    function generFilDel(file) {
        if(file != null && file != ''){
            var type = file.filePath.substr(file.filePath.lastIndexOf('.') + 1);
            if(type=='pdf'){
                return {type: "pdf", size: file.cssize, caption: file.fileName, url:ctx + "common/deleteImage?id="+file.fileId,  key: file.fileId, downloadUrl:file.filePath};
            }else if(type=='text'){
                return {type: "text", size: file.cssize, caption: file.csname, url:ctx + "common/deleteImage?id="+file.fileId, key: file.fileId , downloadUrl:file.filePath};
            }else if(type=='mp4'){
                return  {type: "video", size: file.cssize, filetype: "video/mp4", caption: file.csname, url:ctx + "common/deleteImage?id="+file.fileId, key: file.fileId , downloadUrl:file.filePath};
            }else if(type=='txt'){
                return  {type: "txt", size: file.cssize, caption: file.csname, url:ctx + "common/deleteImage?id="+file.fileId, key: file.fileId , downloadUrl:file.filePath};
            }else if(type=='db'){
                return  {type: "db", size: file.cssize, caption: file.csname, url:ctx + "common/deleteImage?id="+file.fileId, key: file.fileId , downloadUrl:file.filePath};
            }else{
                return	{caption: file.csname, size: file.cssize, url: ctx + "common/deleteImage?id="+file.fileId, key: file.fileId};
            }
        }
    }

    /**
     * 日期插件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        //日期时间选择器
        $("#insuranceStartDate0").removeAttr("lay-key");
        $("#insuranceEndDate0").removeAttr("lay-key");
        laydate.render({
            elem: '#insuranceStartDate0',
            type: 'date',
            format: 'yyyy-MM-dd',
            //value: new Date()
        });
        laydate.render({
            elem: '#insuranceEndDate0',
            type: 'date',
            format: 'yyyy-MM-dd',
            //value: new Date()
        });
    })

    /*var partyA = $("#partyA").bsSuggest({
        indexId: 4, //data.value 的第几个数据，作为input输入框的内容
        indexKey: 0, //data.value 的第几个数据，作为input输入框的内容
        idField: 'partyAId',                    //每组数据的哪个字段作为 data-id，优先级高于 indexId 设置（推荐）
        keyField: 'custName',                   //每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        allowNoKeyword: false, //是否允许无关键字时请求数据
        multiWord: false, //以分隔符号分割的多关键字支持separator: ",", //多关键字支持时的分隔符，默认为空格
        getDataMethod: "url", //获取数据的方式，总是从 URL 获取
        effectiveFields:["custName","contact","provinceName","cityName"],
        effectiveFieldsAlias:{custName: "公司名称",contact:"联系人",provinceName:"省份",cityName:"城市"},
        showHeader: true,
        //listAlign:'right',        //提示列表对齐位置，left/right/auto
        hideOnSelect: true,            // 鼠标从列表单击选择了值时，是否隐藏选择列表
        inputWarnColor: 'rgba(255,0,0,.1)', //输入框内容不是下拉列表选择时的警告色
        url: ctx + 'client/listClient?custName=', //custAbbr /!*优先从url ajax 请求 json 帮助数据，注意最后一个参数为关键字请求参数*!/
        processData: function(json){// url 获取数据时，对数据的处理，作为 getData 的回调函数
            var i, len, data = {value: []};
            if(!json || json.rows.length == 0) {
                return false;
            }
            len = json.rows.length;
            for (i = 0; i < len; i++) {
                data.value.push({
                    "custName": json.rows[i].custName,
                    "contact": json.rows[i].contact,
                    "provinceName":json.rows[i].provinceName,
                    "cityName":json.rows[i].cityName,
                    "partyAId":json.rows[i].partyAId
                });
            }
            return data;
        }
    }).on('onSetSelectValue', function (e, keyword, data) {
        $('#partyAId0').val(data.partyAId);
    }).on("onUnsetSelectValue",function (e) {
        $('#partyAId0').val("");
    });*/

    /**
     * 数字 保留两位小数
     */
    function onlyNumberThreeDecimal (obj) {
        //得到第一个字符是否为负号
        var t = obj.value.charAt(0);
        //先把非数字的都替换掉，除了数字和.
        obj.value = obj.value.replace(/[^\d\.]/g, '');
        //必须保证第一个为数字而不是.
        obj.value = obj.value.replace(/^\./g, '');
        //保证只有出现一个.而没有多个.
        obj.value = obj.value.replace(/\.{2,}/g, '.');
        //保证.只出现一次，而不能出现两次以上
        obj.value = obj.value.replace('.', '$#$').replace(/\./g, '').replace(
            '$#$', '.');
        obj.value = obj.value.slice(0, 15);
        // 只能输入两个小数
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d\d).*$/,'$1$2.$3');
        //如果第一位是负号，则允许添加
        if (t == '-') {
            obj.value = '-' + obj.value;
        }
    }


    /**
     * 提交
     */
    function submitHandler() {
        /*console.log(JSON.stringify($('#form-contract-insurance-add').serializeArray()))
        console.log("===========")
        console.log($('#form-contract-insurance-add').serializeArray())*/
        if ($.validate.form()) {
            var submitIds = []
            for (const uploadCacheKey in uploadCache) {
                submitIds.push(uploadCache[uploadCacheKey])
            }
            /*if (submitIds.length == 0) {
                $.modal.msgError("请选择上传文件");
                return
            }*/
            $("#contractAttach").val(submitIds.join(","))
            commit()

        }
    }

    function commit() {
        $.operate.saveTab(prefix + "/editContractInsurance", $('#form-contract-insurance-edit').serialize());
        /*$.ajax({
            type: "POST",
            contentType: "application/json;charset=UTF-8",
            url: prefix + "/addContractBusiness",
            data: JSON.stringify($('#form-contract-insurance-add').serializeArray()),//JSON.stringify(data.field),
            dataType: 'json',
            success: function(result) {
                console.log(result)
            }
        });*/
    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }
</script>
</body>

</html>