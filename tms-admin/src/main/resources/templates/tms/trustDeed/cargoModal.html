<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('货量更新')"/>
</head>
<style type="text/css">
    .td td {
        position: relative
    }
</style>
<body>
<div class="form-content">
    <form id="form-goods-edit" class="form-horizontal" novalidate="novalidate" >
        <!--承运商结算方式-->
        <input type="hidden" id="balaType" th:value="${carrier.balaType}">
        <!--运费标记-->
        <input type="hidden" id="shippingFlag" name="shippingFlag">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h5 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseOne">基础信息</a>
                </h5>
            </div>
            <div id="collapseOne" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!--基础信息 begin-->
                    <input name="deptId" type="hidden" id="treeId">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">
                                    委托单号：</label>
                                <div class="col-sm-7">
                                    [[${entrust.vbillno}]]
                                    <input type="hidden" id="entrustId" name="entrustId" th:value="${entrust.entrustId}">
                                    <input type="hidden" id="orderno" name="orderno" th:value="${entrust.orderno}">
                                    <input name="corDate" id="corDate" th:value="${#dates.format(entrust.corDate, 'yyyy-MM-dd HH:mm:ss')}" type="hidden">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">
                                    委托单状态：</label>
                                <div class="col-sm-7">
                                    <span class="form-control-static" th:text="${entrust.vbillstatus}"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">
                                    发货单票据号：</label>
                                <div class="col-sm-7">
                                    [[${entrust.invoiceVbillno}]]
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">
                                    客户名称：</label>
                                <div class="col-sm-7">
                                    [[${entrust.custName}]]
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">
                                    运段号：</label>
                                <div class="col-sm-7">
                                    [[${entrust.segmentVbillno}]]
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">
                                    运单号：</label>
                                <div class="col-sm-7">
                                    [[${entrust.lot}]]
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-5">客户发货单号：</label>
                                <div class="col-sm-7">
                                    <input th:field="${invoice.custOrderno}" placeholder="客户发货单号" class="form-control" type="text"
                                           maxlength="50" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row no-gutter">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-md-1 col-sm-2">发货单备注：</label>
                                <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" th:field="*{invoice.memo}" maxlength="200" class="form-control valid"
                                                      rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--基础信息 end-->
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFive">货品明细</a>
                </h4>
            </div>
            <div id="collapseFive" class="panel-collapse collapse in">
                <div class="panel-body">

                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" class="custom-tab table td">
                            <thead>
                            <tr>
                                <th style="width: 10%;">货品名称</th>
                                <th style="width: 10%;">数量</th>
                                <th style="width: 10%;">重量</th>
                                <th style="width: 10%;">体积</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="entPackGoods,entPackGoodsStat : ${entPackGoodsList}">
                                <input type="hidden" th:value="${entPackGoodsStat.size}">
                                <td>
                                    <input th:name="|entPackGoodsList[${entPackGoodsStat.index}].goodsName|"
                                           th:value="${entPackGoods.goodsName}" class="form-control"
                                           th:id="|entPackGoodsList[${entPackGoodsStat.index}].goodsName|" disabled>
                                    <input th:name="|entPackGoodsList[${entPackGoodsStat.index}].invPackGoodsId|"
                                           th:value="${entPackGoods.invPackGoodsId}" class="form-control"
                                           th:id="|entPackGoodsList[${entPackGoodsStat.index}].invPackGoodsId|" type="hidden">
                                </td>
                                <td>
                                    <div class="input-group">
                                        <input th:name="|entPackGoodsList[${entPackGoodsStat.index}].num|"
                                           th:value="${entPackGoods.num}" class="form-control"
                                           th:id="|entPackGoodsList[${entPackGoodsStat.index}].num|"
                                           type="text" min="0" oninput="" maxlength="10" th:data-id="${entPackGoods.invPackGoodsId}"
                                           th:oninput="|$.numberUtil.onlyNumber(this);changeEnt(this,'num')|">
                                        <span class="input-group-addon">件</span>
                                    </div>
                                </td>

                                <td>
                                    <div class="input-group">
                                    <input th:name="|entPackGoodsList[${entPackGoodsStat.index}].weight|"
                                            th:value="${entPackGoods.weight}" class="form-control"
                                            th:id="|entPackGoodsList[${entPackGoodsStat.index}].weight|"
                                           type="text" min="0" maxlength="10" th:data-id="${entPackGoods.invPackGoodsId}"
                                           th:oninput="|$.numberUtil.onlyNumber(this);changeEnt(this,'weight')|" ><span
                                            class="input-group-addon">吨</span>
                                        <input type="hidden" th:id="${entPackGoods.invPackGoodsId}+_weight_old"
                                               th:value="${entPackGoods.weight}">
                                    </div>
                                </td>
                                <td>
                                    <div class="input-group">
                                    <input th:name="|entPackGoodsList[${entPackGoodsStat.index}].volume|"
                                            th:value="${entPackGoods.volume}" class="form-control"
                                            th:id="|entPackGoodsList[${entPackGoodsStat.index}].volume|"
                                           type="text" min="0" maxlength="10" th:data-id="${entPackGoods.invPackGoodsId}"
                                           th:oninput="|$.numberUtil.onlyNumber(this);changeEnt(this,'volume')|" > <span
                                            class="input-group-addon">m³</span>
                                        <input type="hidden" th:id="${entPackGoods.invPackGoodsId}+_volume_old"
                                               th:value="${entPackGoods.volume}">
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseTwo">相关委托单</a>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in">
                <div class="panel-body">

                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" class="custom-tab table td">
                            <thead>
                            <tr>
                                <th style="width: 20%;">委托单号</th>
                                <th style="width: 20%;">货品名称</th>
                                <th style="width: 20%;">数量</th>
                                <th style="width: 20%;">重量</th>
                                <th style="width: 20%;">体积</th>

                            </tr>
                            </thead>
                            <tbody>
                            <div th:remove="tag" th:each="entVO,entVOStat : ${entPackGoodsVOList}">
                                <tr th:if="${entVO.vbillno != entrust.vbillno} " th:each="entPackGoods,entPackGoodsStat : ${entVO.entPackGoodsList}">
                                    <div th:if="${entPackGoodsStat.index == 0}">
                                        <td th:rowspan="${entPackGoodsStat.size}" th:text="${entVO.vbillno}"></td>
                                        <td th:text="${entPackGoods.goodsName}"></td>
                                        <td th:name="${entPackGoods.invPackGoodsId}+_num" th:text="${entPackGoods.num}"></td>
                                        <td th:name="${entPackGoods.invPackGoodsId}+_weight" th:text="${entPackGoods.weight}"></td>
                                        <td th:name="${entPackGoods.invPackGoodsId}+_volume" th:text="${entPackGoods.volume}"></td>
                                    </div>
                                    <div th:unless="${entPackGoodsStat.index == 0}">
                                        <td th:text="${entPackGoods.goodsName}"></td>
                                        <td th:name="${entPackGoods.invPackGoodsId}+_num" th:text="${entPackGoods.num}"></td>
                                        <td th:name="${entPackGoods.invPackGoodsId}+_weight" th:text="${entPackGoods.weight}"></td>
                                        <td th:name="${entPackGoods.invPackGoodsId}+_volume" th:text="${entPackGoods.volume}"></td>
                                    </div>
                                </tr>
                            </div>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseThree">发货单货品信息</a>
                </h4>
            </div>
            <div id="collapseThree" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">总件数：</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input name="numCount" th:field="${invoice.numCount}" type="text" class="form-control" disabled> <span
                                            class="input-group-addon">件</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">总重量：</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input name="weightCount" th:field="${invoice.weightCount}" type="text" class="form-control" disabled> <span
                                            class="input-group-addon">吨</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">总体积：</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="text" th:field="${invoice.volumeCount}" name="volumeCount" class="form-control" disabled> <span
                                            class="input-group-addon">m3</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">总金额：</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <span class="input-group-addon">¥</span>
                                        <input type="text" th:field="${invoice.costAmount}" name="costAmount" class="form-control" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="fixed-table-body" style="margin: 0px -5px;">

                        <table border="0" id="infoTab" class="custom-tab table td">
                            <thead>
                            <tr>
                                <th style="width: 20%;">货品名称</th>
                                <th style="width: 15%;">数量</th>
                                <th style="width: 15%;">重量</th>
                                <th style="width: 15%;">体积</th>
                                <th style="width: 15%;">计价方式</th>
                                <th style="width: 10%;">单价</th>
                                <th style="width: 10%;">金额</th>
                            </tr>
                            </thead>
                            <tbody>
                                <tr th:each="invPackGoods,invPackGoodsStat : ${invPackGoodsList}">
                                <td>
                                    <div>
                                        <input th:value="${invPackGoods.goodsName}" type="text" class="form-control valid" disabled>
                                        <input th:value="${invPackGoods.goodsCharacter}"
                                               th:id="${invPackGoods.invPackGoodsId}+_goodsCharacter" type="hidden">
                                        <input th:value="${invPackGoods.invPackGoodsId}" type="hidden"
                                               th:name="|invPackGoodsList[${invPackGoodsStat.index}].invPackGoodsId|">
                                    </div>
                                </td>
                                <td>
                                    <input th:name="|invPackGoodsList[${invPackGoodsStat.index}].num|"
                                           th:id="${invPackGoods.invPackGoodsId}+_num"
                                           th:value="${invPackGoods.num}" class="form-control" type="text" disabled>
                                    <input type="hidden" th:id="${invPackGoods.invPackGoodsId}+_num_base">
                                </td>
                                <td>
                                    <input th:name="|invPackGoodsList[${invPackGoodsStat.index}].weight|"
                                           th:id="${invPackGoods.invPackGoodsId}+_weight"
                                           th:value="${invPackGoods.weight}" class="form-control" type="text" disabled>
                                    <input type="hidden" th:id="${invPackGoods.invPackGoodsId}+_weight_base">
                                </td>
                                <td>
                                    <input th:name="|invPackGoodsList[${invPackGoodsStat.index}].volume|"
                                           th:id="${invPackGoods.invPackGoodsId}+_volume"
                                           th:value="${invPackGoods.volume}" class="form-control" type="text" disabled>
                                    <input type="hidden" th:id="${invPackGoods.invPackGoodsId}+_volume_base">
                                </td>
                                <td>
                                    <div>
                                        <select th:onchange="getPrice(this)"
                                                th:name="|invPackGoodsList[${invPackGoodsStat.index}].billingMethod|"
                                                th:id="${invPackGoods.invPackGoodsId}+_billingMethod" th:data-id="${invPackGoods.invPackGoodsId}"
                                                class="form-control valid" aria-invalid="false"
                                                th:disabled="${!changeAmount}"
                                                th:required="${changeAmount}">
                                            <option value=""></option>
                                            <option th:each="billingMethod:${billingMethods}" th:value="${billingMethod.value}"
                                                    th:text="${billingMethod.context}"
                                                    th:selected="${invPackGoods.billingMethod} == ${billingMethod.value}"></option>
                                        </select>
                                    </div>
                                </td>
                                <td><input th:name="|invPackGoodsList[${invPackGoodsStat.index}].pc|"
                                           th:value="${invPackGoods.pc}" th:id="${invPackGoods.invPackGoodsId}+_pc"
                                           placeholder="" class="form-control" type="text" autocomplete="off"
                                           th:data-id="${invPackGoods.invPackGoodsId}"
                                           th:oninput="|$.numberUtil.onlyNumber(this);calculateSum(this)|"
                                           th:disabled="${!changeAmount}"
                                           th:required="${changeAmount}">
                                </td>
                                <td>
                                    <div class="input-group">
                                        <input th:name="|invPackGoodsList[${invPackGoodsStat.index}].sum|"
                                               th:value="${invPackGoods.sum}" th:id="${invPackGoods.invPackGoodsId}+_sum"
                                               th:oninput="$.numberUtil.onlyNumber(this);calculateTotals()"
                                               placeholder="" class="form-control" type="text" autocomplete="off"
                                               th:required="${!changeAmount}"
                                               th:disabled="${changeAmount}">
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default" id="shippingIsShow">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseSix">运费</a>

                </h4>
            </div>
            <div id="collapseSix" class="panel-collapse collapse in">
                <div class="panel-body">
                    <!--订单货品费用明细 begin-->
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <input type="hidden" id="indexLot" name="params[indexLot]">
                        <table border="0" id="lotTable" class="custom-tab table">
                            <thead>
                            <tr>
                                <th style="width: 3%;"><a class="collapse-link add-alink" onclick="insertLotRow()" title="新增行">+</a></th>
                                <th style="width: 10%;color: red">费用类型</th>
                                <th style="width: 10%;color: red">费用(元)</th>
                                <th style="width: 10%">油卡号</th>
                                <th style="width: 20%;">备注</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr name="regRowsLot" th:each="payDetail,payDetailStat:${payDetailList}">
                                <td><a class="close-link del-alink" onclick="removeRowLot(this)" title="删除行">-</a></td>
                                <td>
                                    <select th:name="'params[costTypeFreight'+${payDetailStat.index}+']'"  class="form-control valid" aria-invalid="false" th:with="type=${@dict.getType('cost_type_freight')}" required>
                                        <option value="">-- 请选择 --</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${payDetail.costTypeFreight eq dict.dictValue}"></option>
                                    </select>
                                </td>
                                <td><input th:name ="'params[moneyFreight'+${payDetailStat.index}+']'" placeholder="" class="form-control" oninput="$.numberUtil.onlyNumberNegative(this)" type="text" th:value="${payDetail.transFeeCount}" required></td>
                                <td><input th:name="'params[oilFreight'+${payDetailStat.index}+']'" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off" th:value="${payDetail.oilCardNumber}"></td>
                                <td><input th:name="'params[memoFreight'+${payDetailStat.index}+']'" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off" th:value="${payDetail.memo}"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/template" id="rowTmplLot">
    <tr name="regRowsLot">
        <td><a class="close-link del-alink" onclick="removeRowLot(this)" title="删除行">-</a></td>
        <td>
            <select name="costTypeFreight" class="form-control valid" aria-invalid="false" th:with="type=${@dict.getType('cost_type_freight')}" required>
                <option value="">-- 请选择 --</option>
                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
            </select>
        </td>
        <td><input name ="moneyFreight" placeholder="" class="form-control" oninput="$.numberUtil.onlyNumberNegative(this)" type="text" required></td>
        <td><input name="oilFreight" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off"></td>
        <td><input name="memoFreight" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off"></td>
    </tr>
</script>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var entPackGoodsList = [[${entPackGoodsList}]];
    // 承运商协议价
    var carrierProtocols = [[${carrierProtocols}]];
    // 委托单
    var entrust = [[${entrust}]];

    var payDetailList = [[${payDetailList}]];
    var indexLot = payDetailList.length;

    //获取是否车队数据
    var isFleetData = [[${isFleetData}]];
    //获取是否分配给车队数据
    var isFleetAssign = [[${isFleetAssign}]]
    //能否更新金额
    var changeAmount = [[${changeAmount}]]

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');

        var options = {};
        $.table.init(options);

        /**
         * 没有协议价且选择计费方式  展示运费
         */
        if(carrierProtocols.length == 0 && $.common.isNotEmpty(entrust.pricingMethod)){
            $("#shippingIsShow").show();
            $("#shippingFlag").val(1);
        }else{
            $("#shippingIsShow").hide();
            $("#shippingFlag").val(0);
        }


        $.each(entPackGoodsList, function (i, item) {
            var id = item.invPackGoodsId + "_";

            //运单货品
            var numEnt = item.num;
            var weightEnt = item.weight;
            var volumeEnt = item.volume;

            //发货单件数
            var numInv = $("#" + id + "num").val() == "" ? 0 : $("#" + id + "num").val();
            //发货单重量
            var weightInv = $("#" + id + "weight").val() == "" ? 0 : $("#" + id + "weight").val();
            //发货单体积
            var volumeInv = $("#" + id + "volume").val() == "" ? 0 : $("#" + id + "volume").val();

            //用发货单的值 减去 运单的值 作为基础数据
            $("#" + id + "num_base").val(decimal(numInv - numEnt, 5));
            $("#" + id + "weight_base").val(decimal(weightInv - weightEnt, 5));
            $("#" + id + "volume_base").val(decimal(volumeInv - volumeEnt, 5));
        });
    });



    /**
     * 当货品明细的值修改时，同步更新相关委托单值
     */
    function changeEnt(obj,type) {
        var id = obj.getAttribute('data-id') + "_";
        var value = obj.value === "" ? 0 : obj.value;

        //车队新建的单子是可以货量更新的  只有1:业务在途0和1的  以及车队在途1和1的不可以
        //可以的状态0和0 以及  1和0
        if(changeAmount) {
            //改变 相关委托单里的值
            $("td[name='" + id + type + "']").text(value);

            //发货单对应的值 用基础的值加上用户输入的值
            var base = $("#" + id + type + "_base").val();
            $("#" + id + type).val(parseFloat(base) + parseFloat(value));

            //获取价格
            getPrice(obj);
        }
    }

    /**
     * 获取发货单货品单价 根据 发货单id、计价方式、货品特性、件数/重量/体积 获取单价
     */
    function getPrice(obj) {
        var id = obj.getAttribute('data-id') + "_";

        //获取合同价
        var param = {};
        //发货单id
        param.invoiceId = $("#orderno").val();
        //计价方式
        param.billingMethod = $("#" + id + "billingMethod").val();
        //货品特性
        param.goodsCharacter = $("#" + id + "goodsCharacter").val();

        //件数
        param.num = $("#" + id + "num").val();
        //重量
        param.weight = $("#" + id + "weight").val();
        //件数
        param.volume = $("#" + id + "volume").val();

        if (param.billingMethod === undefined) {
            return;
        }

        $.ajax({
            url: ctx + "trustDeed/cargoGetPrice",
            type: "post",
            dataType: "json",
            data: param,
            success: function(result) {
                if (result.code == 0) {
                    //单价
                    $("#" + id + "pc").val(result.data.price);
                    //金额sum
                    $("#" + id + "sum").val(result.data.totalPrice);
                    //计算合计
                    calculateTotals();
                }
            }
        });

    }


    /**
     * 计算金额
     *  @param
     */
    function calculateSum(obj) {
        var id = obj.getAttribute('data-id') + "_";

        //计价方式为
        var billingMethod = $("#" + id + "billingMethod").val();
        //单价
        var pc = $("#" + id + "pc").val() == "" ? 0 : $("#" + id + "pc").val();
        //件数
        var num = $("#" + id + "num").val() == "" ? 0 : $("#" + id + "num").val();
        //重量
        var weight = $("#" + id + "weight").val() == "" ? 0 : $("#" + id + "weight").val();
        //体积
        var volume = $("#" + id + "volume").val() == "" ? 0 : $("#" + id + "volume").val();

        //计算重量
        if (billingMethod == 1 || billingMethod == 6) {
            $("#" + id + "sum").val(decimal(parseFloat(parseFloat(pc) * parseFloat(weight)),2));

        } else if (billingMethod == 2) {
            //计算体积
            $("#" + id + "sum").val(decimal(parseFloat(parseFloat(pc) * parseFloat(volume)),2));
        }else if (billingMethod == 5) {
            //计算件数
            $("#" + id + "sum").val(decimal(parseFloat(parseFloat(pc) * parseFloat(num)),2));
        } else {
            //整车固定价  总价等于单价
            $("#" + id + "sum").val(pc);
        }
        //计算合计
        calculateTotals();
    }

    /**
     * 计算合计
     */
    function calculateTotals() {
        //件数
        var numList = $("[name^='invPackGoodsList'][name$='num']");
        var numCount = 0;
        numList.each(function(){
            if ($(this).val() != "") {
                numCount = parseFloat(numCount + parseFloat($(this).val()));
            }

        });
        $("#numCount").val(decimal(numCount, 5));

        //重量
        var weightList = $("[name^='invPackGoodsList'][name$='weight']");
        var weightCount = 0;
        weightList.each(function(){
            if ($(this).val() != "") {
                weightCount = parseFloat(weightCount + parseFloat($(this).val()))
            }
        });
        $("#weightCount").val(decimal(weightCount, 5));

        //体积
        var volumeList = $("[name^='invPackGoodsList'][name$='volume']");
        var volumeCount = 0;
        volumeList.each(function(){
            if ($(this).val() != "") {
                volumeCount = parseFloat(volumeCount + parseFloat($(this).val()))
            }
        });
        $("#volumeCount").val(decimal(volumeCount, 5));

        //总金额
        var costAmountList = $("[name^='invPackGoodsList'][name$='sum']");
        var costAmount = 0;
        costAmountList.each(function(){
            if ($(this).val() != "") {
                costAmount = parseFloat(costAmount + parseFloat($(this).val()))
            }
        });
        $("#costAmount").val(decimal(costAmount, 2));
    }


    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }

    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            var dis = $(":disabled");
            dis.attr("disabled", false);
            var flag = true;
            //运费标记
            var shippingFlag = $("#shippingFlag").val();
            if(shippingFlag === 1){
                var money = $("#settlementAmount").val();
                //只能回复现金
                if($("#balaType").val() == '2'){
                    $("tr[name='regRowsLot']").each(function () {
                        var costTypeFreight = $(this).find("select[name^='params[costTypeFreight']").val();
                        if(costTypeFreight != '4'){
                            flag = false;
                            return false;
                        }
                    });
                }
                $("tr[name='regRowsLot']").each(function () {
                    var moneyFreight = parseFloat($(this).find("input[name^='params[moneyFreight']").val());
                    money = money - moneyFreight;
                });
                if(flag){
                    if(money == 0){
                        $.operate.saveTab(ctx + "trustDeed/cargoEdit",  $("#form-goods-edit").serializeArray(),function (result) {
                            if (result.code != 0) {
                                dis.attr("disabled", true);
                            }
                        });
                    }else {
                        $.modal.alertWarning("结算金额与运费总金额不一致")
                    }
                }else{
                    $.modal.alertWarning("月结承运商运费类型只能选择回付现金")
                }
            }else{
                $.operate.saveTab(ctx + "trustDeed/cargoEdit",  $("#form-goods-edit").serializeArray(),function (result) {
                    if (result.code != 0) {
                        dis.attr("disabled", true);
                    }
                });
            }
        }
    }

</script>
</body>

</html>