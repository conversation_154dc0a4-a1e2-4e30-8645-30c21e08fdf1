<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('发货单明细')" />
</head>
<style>
    html,body{
        padding: 0px;
        margin: 0px;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
    .form-content{
        width: 100%;
        height: 100%;
        padding: 0px;
    }

    .iframe-main{
        width: 100%;
        height: 60%;
    }
    .amap-marker-label{
        border: 0;
        background-color: transparent;
    }

    .custom-box{
        width: 100%;
        height: 40%;
        background-color: #fff;
        border-top: 1px #d9dadb solid;
        padding: 10px;
        box-shadow: 0px 2px 2px rgba(0,0,0,.1);
        overflow: hidden;
        overflow-y: auto;
    }

    .custom-box .panel{
        background-color: transparent !important;
        border: none !important;
    }

    .custom-box  label{
        font-weight: bold;
    }

    /*2020-07-31 add*/
    .custom-box{
        position: relative;
        padding-top: 28px;
    }
    .custom-con{
        width: 100%;
        height: 100%;
        overflow: hidden;
        overflow-y: auto;
    }

    .updown{
        position: absolute;
        width: 70px;
        height: 26px;
        top: -1px;
        left: 50%;
        margin-left: -35px;
        line-height: 26px;
        text-align: center;
        background-color: #fff;
        color: #1b90f4;
        border-top-left-radius: 2px;
        border-top-right-radius: 2px;
        box-shadow: 0px 2px 2px rgba(0,0,0,.1);
        cursor: pointer;
        z-index: 1000;
        border: 1px #d9dadb solid;
        border-top: none;
    }

    .updown img{
        display: inline-block;
        width: 18px;
        height: 18px;
        margin-top: 4px;
    }


    .updown img.down{
        transform: rotate(-180deg);
    }


</style>


<body>
<div class="form-content">
    <div class="iframe-main">
        <iframe th:src="@{'carLocation/'+${entrust.entrustId}}" border="0" width="100%" height="100%"
                scrolling="no"></iframe>
    </div>
    <div class="custom-box">
        <div class="updown"><img th:src="@{/img/up.png}" alt=""></div>
        <div class="custom-con">
            <form id="form-invoice-add" class="form-horizontal" novalidate="novalidate" th:object="${invoice}">

                <div class="panel-group" id="accordionOne">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapseOne">实际承运人</a>
                            </h5>
                        </div>
                        <div id="collapseOne" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">承运人名称：</label>
                                            <div class="col-sm-8" th:text="${cfcCarrierinfo.carriername}"></div>
                                        </div>
                                    </div>

                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">
                                                <span>承运人类别：</span></label>
                                            <div class="col-md-8 col-sm-8"
                                                 th:if="${cfcCarrierinfo.carriertype !='' and cfcCarrierinfo.carriertype!=null}"
                                                 th:text="${@dict.getLabel('carriertype',cfcCarrierinfo.carriertype)}"></div>
                                        </div>
                                    </div>


                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">道路运输许经营许可证号：</label>
                                            <div class="col-sm-8" th:text="${cfcCarrierinfo.permitnumber}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">联系人：</label>
                                            <div class="col-sm-8">
                                                <div class="input-group" th:text="${cfcCarrierinfo.contactname}"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">联系电话：</label>
                                            <div class="col-sm-8"
                                                 th:text="${cfcCarrierinfo.contactmobiletelephonenumber}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">注册时间：</label>
                                            <div class="col-sm-8"
                                                 th:text="${#dates.format(cfcCarrierinfo.registrationdatetime, 'yyyy-MM-dd ')}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">统一社会信用代码：</label>
                                            <div class="col-sm-8"
                                                 th:text="${cfcCarrierinfo.unifiedsocialcreditldentifier}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">所属辖区：</label>
                                            <div class="col-sm-8" id="provinceCode"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4">是否符合无车承运人：</label>
                                            <div class="col-sm-8"
                                                 th:if="${cfcCarrierinfo.isVehicleFreeCarrier !='' and cfcCarrierinfo.isVehicleFreeCarrier!=null}"
                                                 th:text="${@dict.getLabel('is_vehicle_free_carrier',cfcCarrierinfo.isVehicleFreeCarrier)}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4">有效期起：</label>
                                            <div class="col-sm-8"
                                                 th:text="${#dates.format(cfcCarrierinfo.effectiveFrom, 'yyyy-MM-dd ')}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">有效期至：</label>
                                            <div class="col-sm-8"
                                                 th:text="${#dates.format(cfcCarrierinfo.validUntil, 'yyyy-MM-dd ')}"></div>
                                        </div>
                                    </div>
                                </div>
                                <!--<div class="row no-gutter">
                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                        <div class="form-group">
                                            <label class="col-sm-2">道路经营许可证：</label>
                                            <div class="col-sm-10">
                                                <ul>
                                                    <li th:each="pic:${cfcCarrierinfo.sysUploadFileList}">
                                                        <a th:href="${pic.filePath}" class="galpop" data-galpop-group="gallery"
                                                           title="图片预览" target="_blank">
                                                            <img style="width:70px; height:50px" th:src="${pic.filePath}"/>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>-->

                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-group" id="accordionTwo">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapseTwo">车辆信息</a>
                            </h5>
                        </div>
                        <div id="collapseTwo" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <!--基础信息 begin-->
                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4">车牌号：</label>
                                            <div class="col-md-8 col-sm-8" th:text="${car.carno}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4">营运车辆类型代码：</label>
                                            <div class="col-md-8 col-sm-8"
                                                 th:if="${car.operationType !='' and car.operationType!=null}"
                                                 th:text="${@dict.getLabel('OPERATION_TYPE',car.operationType)}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4">牌照类型代码：</label>
                                            <div class="col-md-8 col-sm-8"
                                                 th:if="${car.licenseplatetypecode !='' and car.licenseplatetypecode!=null}"
                                                 th:text="${@dict.getLabel('car_plate_type',car.licenseplatetypecode)}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4">车牌颜色：</label>
                                            <div class="col-md-8 col-sm-8"
                                                 th:if="${car.vehiclelicenseplatecolor !='' and car.vehiclelicenseplatecolor!=null}"
                                                 th:text="${@dict.getLabel('car_plate_color',car.vehiclelicenseplatecolor)}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4 ">车辆能源类型：</label>
                                            <div class="col-md-8 col-sm-8"
                                                 th:if="${car.vehicleenergytype !='' and car.vehicleenergytype!=null}"
                                                 th:text="${@dict.getLabel('vehicleenergytype',car.vehicleenergytype)}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4">车辆载质量：</label>
                                            <div class="col-md-8 col-sm-8" th:text="${car.vehicletonnage}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4">满载车辆质量：</label>
                                            <div class="col-md-8 col-sm-8" th:text="${car.vehicleladenweight}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4">道路运输证号：</label>
                                            <div class="col-md-8 col-sm-8"
                                                 th:text="${car.roadtransportcertificatenumber}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4 ">车轴数：</label>
                                            <div class="col-md-8 col-sm-8" th:text="${car.axlenum}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4 checkTotalQualityColor">所有人：</label>
                                            <div class="col-md-8 col-sm-8" th:text="${car.owner}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4 checkTotalQualityColor">使用性质：</label>
                                            <div class="col-md-8 col-sm-8" th:text="${car.usercharacter}">

                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4 checkTotalQualityColor">车辆识别代号：</label>
                                            <div class="col-md-8 col-sm-8 " th:text="${car.vin}">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4 checkTotalQualityColor">发证机关：</label>
                                            <div class="col-md-8 col-sm-8" th:text="${car.vin}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4 checkTotalQualityColor">发证日期：</label>
                                            <div class="col-md-8 col-sm-8 checkTotalQuality"
                                                 th:value="${#dates.format(car.issuedate, 'yyyy-MM-dd')}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4 checkTotalQualityColor">注册日期：</label>
                                            <div class="col-md-8 col-sm-8 checkTotalQuality"
                                                 th:text="${#dates.format(car.registerdate, 'yyyy-MM-dd')}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4 checkTotalQualityColor">有效期起：</label>
                                            <div class="col-md-8 col-sm-8 checkTotalQuality"
                                                 th:text="${#dates.format(car.effectiveFrom, 'yyyy-MM-dd')}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-md-4 col-sm-4 checkTotalQualityColor">有效期至：</label>
                                            <div class="col-md-8 col-sm-8 checkTotalQuality"
                                                 th:text="${#dates.format(car.validUntil, 'yyyy-MM-dd')}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-md-1 col-sm-2">备注：</label>
                                            <div class="col-md-11 col-sm-10" th:text="${car.memo}"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-group" id="accordionThree">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapseThree">驾驶员基础信息</a>
                            </h5>
                        </div>
                        <div id="collapseThree" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <!--基础信息 begin-->
                                <input name="deptId" type="hidden" id="treeId">
                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">司机编码：</label>
                                            <div class="col-sm-8" th:text="${driver.driverCode }">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">司机名称：</label>
                                            <div class="col-sm-8">
                                                [[${driver.driverName }]]
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">性别：</label>
                                            <div class="col-sm-8" th:if="${null!=driver.sex}"
                                                 th:text="${@dict.getLabel('sys_user_sex',driver.sex)}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">手机：</label>
                                            <div class="col-sm-8">
                                                [[${driver.phone }]]
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">准驾车型：</label>
                                            <div class="col-sm-8"
                                                 th:if="${driver.licType!=null and driver.licType!='' }"
                                                 th:text="${@dict.getLabel('lic_type',driver.licType)}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">发证机关：</label>
                                            <div class="col-sm-8" th:text="${driver.issuingorganizations}">

                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">有效期起：</label>
                                            <div class="col-sm-8"
                                                 th:text="${#dates.format(driver.validperiodfrom, 'yyyy-MM-dd')}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">有效期至：</label>
                                            <div class="col-sm-8"
                                                 th:text="${#dates.format(driver.validperiodto, 'yyyy-MM-dd')}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">身份证：</label>
                                            <div class="col-sm-8">
                                                [[${driver.cardId }]]
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">从业资格信息证号：</label>
                                            <div class="col-sm-8">
                                                [[${driver.qualificationcertificatenumber }]]
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">注册日期：</label>
                                            <div class="col-sm-8"
                                                 th:text="${#dates.format(driver.registrationdatetime, 'yyyy-MM-dd')}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div class="form-group">
                                            <label class="col-md-1 col-sm-2">备注：</label>
                                            <div class="col-md-11 col-sm-10" th:text="${driver.memo}">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!--基础信息 end-->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapseFour">货源信息</a>
                        </h5>
                    </div>
                    <div id="collapseFour" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">托运人名称：</label>
                                        <div class="col-sm-8" th:text="${shipperinfo.shippername}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">联系人：</label>
                                        <div class="col-sm-8" th:text="${shipperinfo.contactname}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">托运人统一社会信用代码：</label>
                                        <div class="col-sm-8" th:text="${shipperinfo.unifiedsocialcreditldentifier}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">电话号码：</label>
                                        <div class="col-sm-8" th:text="${shipperinfo.contactmobiletelephonenumber}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">国家行政区划代码：</label>
                                        <div class="col-sm-8" th:text="${entrust.deliAreaId}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">国家行政区名称：</label>
                                        <div class="col-sm-8" th:text="${entrust.deliAreaName}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">装货地点：</label>
                                        <div class="col-sm-8" th:text="${entrust.deliDetailAddr}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">收货人：</label>
                                        <div class="col-sm-8" th:text="${entrust.arriContact}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">收货联系人姓名：</label>
                                        <div class="col-sm-8" th:text="${entrust.arriContact}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">收货人电话号码：</label>
                                        <div class="col-sm-8" th:text="${entrust.arriMobile}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">收货人国家行政区划代码：</label>
                                        <div class="col-sm-8" th:text="${entrust.arriAreaId}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">收货人国家行政区划名称：</label>
                                        <div class="col-sm-8">
                                            <div class="input-group" th:text="${entrust.arriAreaName}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row no-gutter">

                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">卸货地点：</label>
                                        <div class="col-sm-8">
                                            <div class="input-group" th:text="${entrust.arriDetailAddr}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">保险公司名称：</label>
                                        <div class="col-sm-8" th:text="${invoice.insuranceCompany}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">保险公司代码：</label>
                                        <div class="col-sm-8" th:text="其他保险公司">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">保险单号：</label>
                                        <div class="col-sm-8" th:text="${invoice.insuranceNo}">
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">保险合同地址：</label>
                                        <div class="col-sm-8">
                                            <ul>
                                                <li th:each="file:${insuranceSysUploadFiles}">
                                                    <a target="view_window" th:href="@{${file.filePath}}"
                                                       th:text="${file.fileName}"></a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">托运合同编号：</label>
                                        <div class="col-sm-8" th:text="${entrust.invoiceVbillno}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapseFive">货品明细</a>
                        </h4>
                    </div>
                    <div id="collapseFive" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <!--订单货品费用明细 begin-->
                            <div class="fixed-table-body" style="margin: 0px -5px;">
                                <table border="0" id="infoTab" class="custom-tab table">
                                    <thead>
                                    <tr>
                                        <th style="width: 15%;">货品编码</th>
                                        <th style="width: 12%;">货品名称</th>
                                        <th style="width: 8%;">件数</th>
                                        <th style="width: 8%;">重量</th>
                                        <th style="width: 8%;">体积</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr th:each="goods:${entPackGoodsList}">
                                        <td th:text="${goods.goodsCode}"></td>
                                        <td th:text="${goods.goodsName}"></td>
                                        <td th:text="${goods.num}"></td>
                                        <td th:text="${goods.weight}"></td>
                                        <td th:text="${goods.volume}"></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!--订单货品费用明细 end-->
                        </div>
                    </div>
                </div>


                <div class="panel-group" id="accordion">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapseSix">托运合同信息</a>
                            </h5>
                        </div>
                        <div id="collapseSix" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <!--订单基础信息 begin-->
                                <div class="row no-gutter">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">
                                                合同编号：</label>
                                            <div class="col-sm-8" th:text="*{vbillno}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">
                                                合同类型：</label>
                                            <div class="col-sm-8">托运合同</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">
                                                甲方：</label>
                                            <div class="col-sm-8" th:text="${shipperinfo.shippername}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">
                                                乙方：</label>
                                            <div class="col-sm-8" th:text="南通琦欣供应链管理有限公司"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row no-gutter">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">合同金额(元)：</label>
                                            <div class="col-sm-8" th:text="${transFeeCount}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">合同签订时间：</label>
                                            <div class="col-sm-8"
                                                 th:text="${#dates.format(invoice.regDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row no-gutter">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">合同访问地址：</label>
                                            <div class="col-sm-8">
                                                <ul>
                                                    <li th:each="file:${contractPhotoAccessAddressList}">
                                                        <a target="view_window" th:href="@{${file.filePath}}"
                                                           th:text="${file.fileName}"></a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="panel-group" id="accordionSeven">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion"
                                   href="tabs_panels.html#collapseSeven">承运运合同信息</a>
                            </h5>
                        </div>
                        <div id="collapseSeven" class="panel-collapse collapse in">
                            <div class="panel-body">
                                <!--订单基础信息 begin-->
                                <div class="row no-gutter">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">
                                                合同编号：</label>
                                            <div class="col-sm-8" th:text="${entrustLot.lot}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">
                                                合同类型：</label>
                                            <div class="col-sm-8">承运合同</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">
                                                甲方：</label>
                                            <div class="col-sm-8" th:text="南通琦欣供应链管理有限公司"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">
                                                乙方：</label>
                                            <div class="col-sm-8" th:text="${driver.driverName}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row no-gutter">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">合同金额(元)：</label>
                                            <div class="col-sm-8" th:text="${transFeeCount}"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">合同签订时间：</label>
                                            <div class="col-sm-8"
                                                 th:text="${#dates.format(entrustLot.regDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row no-gutter">
                                    <div class="col-md-3 col-sm-6">
                                        <div class="form-group">
                                            <label class="col-sm-4">合同访问地址：</label>
                                            <div class="col-sm-8">
                                                <ul>
                                                    <li th:each="file:${sysUploadFiles}">
                                                        <a target="view_window" th:href="@{${file.filePath}}"
                                                           th:text="${file.filePath}"></a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapseEight">运单信息</a>
                        </h5>
                    </div>
                    <div id="collapseEight" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <!--订单基础信息 begin-->
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">
                                            运单号：</label>
                                        <div class="col-sm-8" th:text="${entrustLot.lot}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">托运时间：</label>
                                        <div class="col-sm-8"
                                             th:text="${#dates.format(entrustLot.regDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">
                                            业务类型代码：</label>
                                        <div class="col-sm-8"
                                             th:each="dict : ${@dict.getType('BUSINESSTYPENAME')}"
                                             th:if="${dict.dictValue} == ${invoice.businesstypename}"
                                             th:text="${dict.dictLabel}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">统计类型：</label>
                                        <div class="col-sm-8" th:text="整车"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">
                                            实际承运人：</label>
                                        <div class="col-sm-8" th:text="${cfcCarrierinfo.carriername}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">承运合同编号：</label>
                                        <div class="col-sm-8" th:text="${entrustLot.lot}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">状态变更日期：</label>
                                        <div class="col-sm-8"
                                             th:text="${#dates.format(entrust.receiptDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">发运实际日期：</label>
                                        <div class="col-sm-8"
                                             th:text="${#dates.format(entrust.receiptDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">收货日期：</label>
                                        <div class="col-sm-8"
                                             th:text="${#dates.format(entrust.receiptDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">运单生成时间：</label>
                                        <div class="col-sm-8"
                                             th:text="${#dates.format(entrustLot.regDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">运单生成类型：</label>
                                        <div class="col-sm-8" th:text="一口价成交"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">业务类型名称：</label>
                                        <div class="col-sm-8" th:text="城市配送"></div>
                                    </div>
                                </div>

                            </div>
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">运输总车辆数：</label>
                                        <div class="col-sm-8" th:text="1"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">运输组织类型代码：</label>
                                        <div class="col-sm-8" th:text="公路运输"></div>
                                    </div>
                                </div>

                            </div>
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">备注：</label>
                                        <div class="col-sm-8" th:text="${entrustLot.memo}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapseNine">派车单信息</a>
                        </h5>
                    </div>
                    <div id="collapseNine" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <!--订单基础信息 begin-->
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">派车单号：</label>
                                        <div class="col-sm-8" th:text="${entrustLot.lot}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">车牌号：</label>
                                        <div class="col-sm-8" th:text="${car.carno}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">运单号：</label>
                                        <div class="col-sm-8" th:text="${entrustLot.lot}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">驾驶员：</label>
                                        <div class="col-sm-8" th:text="${driver.driverName}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">派车单生成时间：</label>
                                        <div class="col-sm-8"
                                             th:text="${#dates.format(entrust.regDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">实际发车时间：</label>
                                        <div class="col-sm-8"
                                             th:text="${#dates.format(entrust.actDeliDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">实际行驶公里数：</label>
                                        <div class="col-sm-8" th:text="${entrust.actualmileage}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapseTen">定位信息</a>
                        </h5>
                    </div>
                    <div id="collapseTen" class="panel-collapse collapse in">
                        <div class="panel-body" th:each="carLocus:${carLocusList}">
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">
                                            车牌号码：</label>
                                        <div class="col-sm-8" th:text="${carLocus.vehiclenumber}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">
                                            经度：</label>
                                        <div class="col-sm-8" th:text="${carLocus.longitudedegree}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">
                                            纬度：</label>
                                        <div class="col-sm-8" th:text="${carLocus.latitudedegree}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">
                                            定位时间：</label>
                                        <div class="col-sm-8"
                                             th:text="${#dates.format(carLocus.getlocationtime, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapseEleven">回执单信息</a>
                        </h5>
                    </div>
                    <div id="collapseEleven" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">派车单号：</label>
                                        <div class="col-sm-8" th:text="${entrustLot.lot}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">运输价格：</label>
                                        <div class="col-sm-8" th:text="${transFeeCount}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion"
                               href="tabs_panels.html#collapseTwelve">资金流水信息</a>
                        </h5>
                    </div>
                    <div id="collapseTwelve" class="panel-collapse collapse in">
                        <div class="panel-body" th:each="payRecord:${payRecordList}">
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">
                                            流水号：</label>
                                        <div class="col-sm-8" th:text="${payRecord.tradeNo}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">
                                            参与方：</label>
                                        <div class="col-sm-8">CA</div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">
                                            参与方名称：</label>
                                        <div class="col-sm-8" th:text="${driver.driverName}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">实际承运人：</label>
                                        <div class="col-sm-8" th:text="${cfcCarrierinfo.carriername}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row no-gutter">
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">
                                            日期时间：</label>
                                        <div class="col-sm-8"
                                             th:text="${#dates.format(payRecord.payDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">总金额：</label>
                                        <div class="col-sm-8" th:if="${payRecord.lotAmount != null}"
                                             th:text="${payRecord.lotAmount}"></div>
                                        <div class="col-sm-8" th:if="${payRecord.lotAmount == null}"
                                             th:text="${payRecord.payAmount}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">实际支付金额：</label>
                                        <div class="col-sm-8" th:text="${payRecord.payAmount}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">付款方式：</label>
                                        <div class="col-sm-8" th:text="${payRecord.payMethodName}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">派车单号：</label>
                                        <div class="col-sm-8" th:text="${entrustLot.lot}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">分段分单号：</label>
                                        <div class="col-sm-8" th:text="单一"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">收款方名称：</label>
                                        <div class="col-sm-8" th:text="${payRecord.recAccount}"></div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-4">收款账户：</label>
                                        <div class="col-sm-8" th:text="${payRecord.recCardNo}"></div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer" />
<script th:inline="javascript">
    $(function () {

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');
        $('#collapseSeven').collapse('show');
        $('#collapseEight').collapse('show');
        $('#collapseNine').collapse('show');
        $('#collapseTen').collapse('show');
        $('#collapseEleven').collapse('show');
        $('#collapseTwelve').collapse('show');

        $(".updown").click(function(){
            if(!$(this).find("img").hasClass("down")){
                $(".iframe-main").animate({'height':'0'});
                $(".custom-box").animate({'height':'100%'});
                $(this).find("img").addClass("down");
            }else{
                $(".iframe-main").animate({'height':'60%'});
                $(".custom-box").animate({'height':'40%'});
                $(this).find("img").removeClass("down");
            }
        });

        //所属省展示
        var provinceCode =  [[${cfcCarrierinfo.countrysubdivisioncode}]];
        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            success: function (result) {
                for (var i in result) {
                    if (result[i].PROVINCE_CODE === provinceCode){
                        $('#provinceCode').text(result[i].PROVINCE_NAME );
                    }
                }
            }
        });

    });
</script>
</body>
</html>