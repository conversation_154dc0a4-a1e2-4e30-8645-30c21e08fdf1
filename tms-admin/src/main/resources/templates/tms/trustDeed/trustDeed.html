<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('委托单列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: bootstrap-editable-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">发货单号：</label>-->
                            <div class="col-sm-12">
                                <input name="invoiceVbillno" id="invoiceVbillno" placeholder="请输入发货单号"
                                       class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">委托单号：</label>-->
                            <div class="col-sm-12">
                                <input name="vbillno" id="vbillno" placeholder="请输入委托单号" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
<!--                        <div class="form-group">-->
<!--                            <label class="col-sm-4">委托单状态：</label>-->
                            <div class="col-sm-12">
                                <select name="vbillstatus" id="vbillstatus" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="委托单状态" multiple>
                                    <option th:each="dict : ${entrustStatusEnum}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>

                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">车牌号：</label>-->
                            <div class="col-sm-12">
                                <input name="carno" id="carno" placeholder="请输入车牌号" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">车长：</label>-->
                            <div class="col-sm-12">
                                <select name="carLenId" id="carLenId" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="车长" multiple  th:with="type=${@dict.getType('car_len')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">车型：</label>-->
                            <div class="col-sm-12">
                                <select name="carTypeCode" id="carTypeCode" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="车型" multiple  th:with="type=${@dict.getType('car_type')}">
                                    <option></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">客户简称：</label>-->
                            <div class="col-sm-12">
                                <input name="custAbbr" id="custAbbr" placeholder="请输入客户简称" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">司机名称：</label>-->
                            <div class="col-sm-12">
                                <input class="form-control" id="driverName" name="driverName"
                                       placeholder="请输入司机名称">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">承运商：</label>-->
                            <div class="col-sm-12">
                                <input name="carrName" id="carrName" placeholder="请输入承运商" class="form-control valid"
                                       type="text">
                            </div>
                        </div>
                    </div>


                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">运单号：</label>-->
                            <div class="col-sm-12">
                                <input name="lot" id="lot" placeholder="请输入运单号"
                                       class="form-control" type="text" th:value="${lot}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">司机手机：</label>-->
                            <div class="col-sm-12">
                                <input type="text" class=" form-control" id="driverMobile"
                                       placeholder="请输入司机手机" name="driverMobile">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">调度组：</label>-->
                            <div class="col-sm-12">
                                <select name="transLineId" id="transLineId" data-none-selected-text="调度组" class="form-control valid selectpicker"
                                        aria-invalid="false" required>
                                    <option value=""></option>
                                    <option th:each="mapS,status:${dispatcherDeptList}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-2">要求提货日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="pickStartDate" placeholder="请输入提货开始日期">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="time-input form-control" name="pickEndDate" placeholder="请输入提货结束日期">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-4">实际到货时间：</label>-->
                            <div class="col-sm-12">
                                <input type="text"  class="time-input form-control" name="actArriDate" id="actArriDate" placeholder="请输入实际到货时间">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-4">运输方式：</label>-->
                            <div class="col-sm-12">
                                <select name="transCode" id="transCode" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运输方式" multiple th:with="type=${@dict.getType('trans_code')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
<!--                            <label class="col-sm-4">是否推送：</label>-->
                            <div class="col-sm-12">
                                <select name="isNtocc" id="isNtocc" data-none-selected-text="是否推送" class="form-control valid selectpicker">
                                    <option></option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">提货地址：</label>
                                <div class="col-sm-8">
                                    <select name="deliProvinceId" id="deliProvinceId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-2">
                            <select name="deliCityId" id="deliCityId" class="form-control valid"
                                    aria-invalid="false"></select>
                        </div>

                        <div class="col-sm-2">
                            <select name="deliAreaId" id="deliAreaId" class="form-control valid"
                                    aria-invalid="false"></select>
                        </div>

                        <div class="col-sm-2">
                            <input name="deliDetailAddr" id="deliDetailAddr" placeholder="请输入详细地址" class="form-control"
                                   type="text"
                                   maxlength="30">
                        </div>

                    </div>



                </div>
                <div class="row">
                    <div>
                        <div class="col-md-3 col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4">收货地址：</label>
                                <div class="col-sm-8">
                                    <select name="arriProvinceId" id="arriProvinceId"
                                            class="form-control valid"></select>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-2">
                            <select name="arriCityId" id="arriCityId" class="form-control valid"></select>
                        </div>

                        <div class="col-sm-2">
                            <select name="arriAreaId" id="arriAreaId" class="form-control valid"></select>
                        </div>

                        <div class="col-sm-2">
                            <input name="arriDetailAddr" id="arriDetailAddr" placeholder="请输入详细地址" class="form-control"
                                   type="text" maxlength="30">
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
          <!--  <a class="btn btn-primary single disabled" onclick="cargo()"  shiro:hasPermission="tms:trustDeed:cargo">
                <i class="fa fa-pencil"></i> 货量更新
            </a>-->
            <a class="btn btn-primary single disabled" onclick="affirm()" shiro:hasPermission="tms:trustDeed:affirm" >
                <i class="fa fa-check-circle-o"></i> 确认
            </a>
            <a class="btn btn-danger single disabled" onclick="oppositeAffirm()" shiro:hasPermission="tms:trustDeed:oppositeAffirm">
                <i class="fa fa-reply"></i> 反确认
            </a>
            <a class="btn btn-danger single disabled" onclick="pickRevocation()" shiro:hasPermission="tms:trace:pickOppositeAffirm">
                <i class="fa fa-file single disabled"></i> 业务反确认
            </a>
            <a class="btn btn-danger single disabled" onclick="removeAll()" shiro:hasPermission="tms:trustDeed:removeAll">
                <i class="fa fa-trash"></i> 删除
            </a>
            <a class="btn btn-danger single disabled" onclick="entrustClose()" shiro:hasPermission="tms:trustDeed:entrustClose">
                <i class="fa fa-remove"></i> 关闭
            </a>
            <a class="btn btn-primary single disabled" onclick="repealClose()" shiro:hasPermission="tms:trustDeed:repealClose">
                <i class="fa fa-reply"></i> 撤销关闭
            </a>
           <!-- <a class="btn btn-primary">
                <i class="fa fa-download"></i> 导出
            </a>-->
            <!--<a class="btn btn-primary single disabled" onclick="editCarrier()" shiro:hasPermission="tms:trustDeed:editCarrier">
                <i class="fa fa-pencil"></i> 修改承运商
            </a>-->
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: bootstrap-table-editable-js" />
<script th:inline="javascript">
    var prefix = ctx + "trustDeed";

    var carLen = [[${@dict.getType('car_len')}]];
    var urgentLevel = [[${@dict.getType('urgent_level')}]];
    var CloseAccountList = [[${CloseAccountList}]];//关账记录


    $(function () {
        var options = {
            url: prefix + "/list",
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            fixedNumber: 4,
            showToggle: false,
            showColumns: true,
            modalName: "委托单",
            uniqueId: "entrustId",
            height: 560,
            clickToSelect: true,
            onEditableSave: function (field, row, oldValue, $el) {
                var data;
                if (field === 'noReason') {
                    data = {entrustId: row.entrustId,noReason:row.noReason}
                    editNoReason(data);
                }
            },
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:trustDeed:detail')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="明细"  onclick="detail(\'' + row.entrustId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        }
                        //运输方式编码为0公路整车，并且没推送过，才能推送无车承运人平台
                        if (row.transCode === '0' && row.isNtocc == 0 && row.ntoccFlag === 1) {
                            if ([[${@permission.hasPermi('tms:trustDeed:pushNtocc')}]] !== "hidden") {
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="无车承运人明细"  onclick="detailNtocc(\'' + row.entrustId + '\')"><i class="fa fa-object-group" style="font-size: 15px;"></i></a>');
                                actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="推送无车承运人平台"'
                                    +'  onclick="pushNtocc(\'' + row.entrustId
                                    + '\')"><i class="fa fa-send" style="font-size: 15px;"></i></a>');
                            }
                        }
                        return actions.join('');
                    }
                },{
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceVbillno'
                },
                {
                    title: '委托单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '<span class="label label-primary">待确认</label>';
                            case 1:
                                return '<span class="label label-warning">已确认</label>';
                            case 2:
                                return '<span class="label label-info">已提货</label>';
                            case 3:
                                return '<span class="label label-success">已到货 </label>';

                            case 5:
                                return '<span class="label label-inverse">关闭 </label>';
                            default:
                                break;
                        }
                    }
                },
                {title: '不符合原因', align: 'left', field: 'noReason',
                    editable:{
                        type: 'select',
                        source: [
                            {value:"轨迹偏差距离过大",text:"轨迹偏差距离过大"},
                            {value:"无轨迹",text:"无轨迹"},
                            {value:"超载",text:"超载"},
                            {value:"数据异常",text:"数据异常"},
                            {value:"其它",text:"其它"}],
                        title: '修改不符合原因',
                        validate:  function (v) {
                            //判断是否为空
                            if (v === "") {
                                return "不能为空！";
                            }
                            //判断长度
                            if (getStringLen(v) > 100) {
                                return "长度过长！";
                            }
                        }
                    }},
                {
                    title: '推送无车承运人平台',
                    align: 'center',
                    field: 'isNtocc',
                    formatter: function status(value, row, index) {
                        var context = '';
                        if (value === '1') {
                            context = '<span class="label label-primary"> 是 </span>';
                        } else {
                            context = '<span class="label label-default"> 否 </span>';
                            var clickPushNtocc = row.clickPushNtocc;
                            if(clickPushNtocc == '1'){
                                context += '<span class="label label-danger" style="margin-left: 3px;"> 失败 </span>';
                            }
                        }
                        return context;
                    }
                },

                {
                    title: '客户简称',
                    align: 'left',
                    field: 'custAbbr'
                },
                {
                    title: '车牌号',
                    field: 'carNo',
                    align: 'left'
                },
                {
                    title: '承运商名称',
                    align: 'left',
                    field: 'carrName'
                },
                {
                    title: '运输方式',
                    align: 'left',
                    field: 'transName'
                },

                {
                    title: '车长车型',
                    align: 'left',
                    field: 'carLenId',
                    formatter: function status(value, row, index) {
                        if(row.carTypeName == null || row.carTypeName == ''){
                            row.carTypeName = '';
                        }
                        return $.table.selectDictLabel(carLen, value)+row.carTypeName;
                    }
                },
                // {
                //     title: '车型',
                //     align: 'left',
                //     field: 'carTypeName'
                // },
                {
                    title: '要求提货日期',
                    align: 'left',
                    field: 'reqDeliDate',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,10);
                    }
                },
                {
                    title: '要求到货日期',
                    align: 'left',
                    field: 'reqArriDate',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,10);
                    }
                },
                {
                    title: '实际到货日期',
                    align: 'left',
                    field: 'actArriDate',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,10);
                    }
                },
                {
                    title: '提货地址',
                    align: 'left',
                    field: 'deliAddrName',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.deliAddrName);
                    }
                },
                {
                    title: '收货地址',
                    align: 'left',
                    field: 'arriAddrName',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.arriAddrName);
                    }
                },
                {
                    title: '提货联系人',
                    align: 'left',
                    field: 'deliContact'
                },
                {
                    title: '到货联系人',
                    align: 'left',
                    field: 'arriContact'
                },

                {
                    title: '总件数',
                    field: 'numCount',
                    align: 'left'
                },
                {
                    title: '总重量(吨)',
                    field: 'weightCount',
                    align: 'left'
                },
                {
                    title: '总体积(m3)',
                    field: 'volumeCount',
                    align: 'left'
                },
                {
                    title: '总金额',
                    field: 'costAmount',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },

                {
                    title: '运段号',
                    field: 'segmentVbillno',
                    align: 'left'
                },

                {
                    title: '运单号',
                    field: 'lot',
                    align: 'left'
                },

                {
                    title: '紧急程度',
                    field: 'urgentLevel',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(urgentLevel, value);
                    }
                },
                {
                    title: '驻场人',
                    field: 'actDeliMan',
                    align: 'left'
                },
                {
                    title: '调度人',
                    field: 'regUserName',
                    align: 'left'
                },
                {
                    title: '调度时间',
                    field: 'regDate',
                    align: 'left'
                },
                {
                    title: '委托单号',
                    field: 'vbillno',
                    align: 'left'
                }
            ]
        };

        $.table.init(options);

        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });
    });

    function searchPre() {
        var data = {};
        data.carLenId = $.common.join($('#carLenId').selectpicker('val'));
        data.carTypeCode = $.common.join($('#carTypeCode').selectpicker('val'));
        data.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));
        data.transCode = $.common.join($('#transCode').selectpicker('val'));
        $.table.search('role-form', data);
    }

    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }
    //删除
    function removeAll() {
        var vbillstatus =  $.table.selectColumns("vbillstatus");
        for (var i = 0; i < vbillstatus.length ; i++) {
            if (vbillstatus[i] != '0' && vbillstatus[i] !== undefined) {
                $.modal.alertWarning("请选择待确认状态的委托单");
                return;
            }
        }
        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }


        var invoiceId = $.table.selectColumns("orderNo").join();

        //验证发货单是否超过五天
        $.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.confirm("确认要删除选中的" + rows.length + "条数据吗?", function() {
                        var entrustId =  $.table.selectColumns("entrustId");
                        var data = { "entrustId": entrustId.join()};
                        $.operate.submit($.table._option.removeUrl, "post", "json", data);
                    });
                }
            }
        });
    }

    /**
     * 跳转反确认页面
     */
    function oppositeAffirm() {
        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        var vbillstatus =  $.table.selectColumns("vbillstatus");
        for (var i = 0; i < vbillstatus.length ; i++) {
            if (vbillstatus[i] != '1' && vbillstatus[i] !== undefined) {
                $.modal.alertWarning("请选择已确认状态的委托单");
                return;
            }
        }
        if (vbillstatus.length === 0 ) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }

        var invoiceId = $.table.selectColumns("orderNo").join();

        //验证发货单是否超过五天
        $.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    var entrustId =  $.table.selectColumns("entrustId");
                    $.modal.open("反确认", prefix + "/back_confirm/" + entrustId,500,300);
                }
            }
        });
    }

    /**
     * 确认
     */
    function affirm() {
        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        var vbillstatus =  $.table.selectColumns("vbillstatus");
        for (var i = 0; i < vbillstatus.length ; i++) {
            if (vbillstatus[i] != '0' && vbillstatus[i] !== undefined) {
                $.modal.alertWarning("请选择待确认状态的委托单");
                return;
            }
        }
        if (vbillstatus.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var entrustId =  $.table.selectColumns("entrustId");
        $.operate.post(prefix + "/affirm", { "entrustId": entrustId.join()});
    }

    /**
     * 跳转委托单详情页
     */
    function detail(entrustId) {
        var url = prefix + "/detail?entrustId="+entrustId;
        $.modal.openTab($.table._option.modalName + "详细", url);
    }

    /**
     * 跳转委托单无车承运儿详情页
     */
    function detailNtocc(entrustId) {
        var url = prefix + "/detailNtocc/"+entrustId;
        $.modal.openTab($.table._option.modalName + "无车承运人详细", url);
    }

    /**
     * 推送无车承运人平台
     */
    function pushNtocc(entrustId) {
        $.modal.confirm("确认要推送到无车承运人平台吗?", function () {
            var data = {entrustId: entrustId};
            var url = prefix + "/pushNtocc";
            var config = {
                url: url,
                type: "post",
                dataType: "json",
                data: data,
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    if (result.code == web_status.SUCCESS && $.table._option.type == table_type.bootstrapTable) {
                        $.modal.msgSuccess(result.msg);
                        $.table.refresh();
                    } else if (result.code == web_status.SUCCESS && $.table._option.type == table_type.bootstrapTreeTable) {
                        $.modal.msgSuccess(result.msg);
                        $.treeTable.refresh();
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    }  else {
                        layer.alert(result.msg, {
                            icon: 2,
                            title: "系统提示",
                            btn: ['确认'],
                            btnclass: ['btn btn-primary'],
                        },function (index) {
                            $.table.refresh();
                            layer.close(index)
                        });
                    }
                    $.modal.closeLoading();
                }
            };
            $.ajax(config)
        })
    }

    /**
     * 委托单关闭
     */
    function entrustClose() {
        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        var vbillstatus =  $.table.selectColumns("vbillstatus");

        for (var i = 0; i < vbillstatus.length;i++ ) {
            if ( vbillstatus[i] != '0' && vbillstatus[i] !== undefined) {
                $.modal.alertWarning("请选择待确认状态的委托单");
                return;
            }
        }
        if (vbillstatus.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var entrustId =  $.table.selectColumns("entrustId");
        $.operate.post(prefix + "/updateStatus", { "entrustId": entrustId.join()});
    };



    /**
     * 撤销关闭
     */
    function repealClose() {
        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        var vbillstatus =  $.table.selectColumns("vbillstatus");

        for (var i = 0; i < vbillstatus.length;i++ ) {
            if ( vbillstatus[i] != '5' && vbillstatus[i] !== undefined) {
                $.modal.alertWarning("请选择关闭状态的委托单");
                return;
            }
        }
        if (vbillstatus.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var entrustId =  $.table.selectColumns("entrustId");
        $.modal.confirm("是否确认撤销关闭？", function() {
            $.operate.post(prefix + "/cancelCloseEntrust", { "entrustId": entrustId.join(),"vbillstatus": '0' });
        })
    }

    /**
     * 货量更新
     */
    function cargo() {

        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }
        var entrustId =  $.table.selectColumns("entrustId");
        //验证是否可以货量更新
        $.ajax({
            type: "POST",
            url: prefix + "/checkCargo?entrustId="+entrustId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.openTab("货量更新", prefix + "/cargo?entrustId="+entrustId);
                }
            }
        });

    }

    /**
     * 修改承运商
     */
    function editCarrier() {

        var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }

        //判断委托单状态为新建，已确认 后台要验证 运单对应下的 应付为新建状态
        var vbillstatus = $.table.selectColumns("vbillstatus").join();
        if(vbillstatus != 0 && vbillstatus != 1){
            $.modal.alertWarning("委托单状态为待确认和已确认才能修改承运商！");
            return false;
        }

        var entrustId =  $.table.selectColumns("entrustId");
        $.modal.openTab("修改承运商", prefix + "/editCarrier?entrustId="+entrustId);
    }



    /**
     * 修改客户发货单号
     */
    function editNoReason(data) {
        $.ajax({
            url: prefix + "/updateEntrust",
            type: "post",
            dataType: "json",
            data: data,
            success: function (result) {
                if (result.code === 0) {
                    var data = result.data;
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                } else {
                    $.modal.msgError(result.msg);
                    //刷新
                    $.btTable.bootstrapTable('refresh', {
                        silent: true
                    });
                }
            }
        });
    }

    //反确认
    function pickRevocation() {
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);

      /*  var isClose = $.table.selectColumns("isClose");
        //关账判断
        for (var i = 0; i < isClose.length ; i++) {
            if(isClose == 1){
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }*/

        var invoiceId = $.table.selectColumns("orderNo").join();

        //验证发货单是否超过五天
        $.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    var  vbillstatus = $.table.selectColumns("vbillstatus");
                    //是否回单标记
                    var ifReceipt = $.table.selectColumns("ifReceipt");
                    //是否回单确认标记
                    var receiptConfirmFlag = $.table.selectColumns("receiptConfirmFlag");
                    //提货到货回单反确认
                    if(vbillstatus == 2){
                        //验证是否可以货量更新
                        $.ajax({
                            type: "POST",
                            url: ctx + "trace/checkConfirmPick?entrustId="+rows.join(),
                            async: false,
                            success: function(r){
                                if(r.code != 0){
                                    $.modal.alertError(r.msg);
                                    return false;
                                }else{
                                    $.modal.open("提货反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join(),500,300);
                                }
                            }
                        });
                        //到货反确认
                    }else if(vbillstatus == 3 && ifReceipt != '1'){
                        if(receiptConfirmFlag == '1'){
                            $.modal.open("回单确认反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join(),500,300);
                        }else{
                            $.modal.open("到货反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join(),500,300);
                        }
                        //回单反确认
                    }else if(vbillstatus == 3 && ifReceipt == '1'){
                        $.modal.open("回单反确认", ctx + "trustDeed/back_confirm_pick/" + rows.join(),500,300);
                    }else{
                        $.modal.alertError("委托单状态需要进行业务操作后才能进行反确认！");
                    }
                }
            }
        });
    }

    //获取字符长度
    function getStringLen(str) {
        var len = 0;
        for (var i = 0; i < str.length; i++) {
            str.charCodeAt(i) > 255 ? len += 2 : len += 1;
        }
        return len;
    }


</script>
</body>
</html>