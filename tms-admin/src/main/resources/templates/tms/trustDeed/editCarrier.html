<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('修改承运商')"/>
    <style>
        td{
            position:relative
        }
        label.error{
            top:10px !important;
        }
    </style>
</head>

<body>
<div class="form-content">
    <form id="form-carrier-edit" class="form-horizontal" novalidate="novalidate">
        <input id="entrustId" name="entrustId" type="hidden" th:value="${entrust.entrustId}" >
        <input id="isNtocc" name="isNtocc" type="hidden" th:value="${entrust.isNtocc}" >
        <input id="customerId" name="customerId" type="hidden" th:value="${entrust.customerId}" >
        <input name="corDate" id="corDate" th:value="${#dates.format(entrust.corDate, 'yyyy-MM-dd HH:mm:ss')}" type="hidden">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">承运商信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-3">
                                <div class="form-group">
                                    <label class="col-sm-5"><font color="red">承运商：</font></label>
                                    <div class="col-sm-7">
                                        <div class="input-group">
                                            <span class="input-group-addon"><i
                                                    class="fa fa-search"></i></span>
                                            <input name="carrName" id="carrName" class="form-control valid" type="text" readonly
                                                   required onclick="selectCarrier()" th:value="${entrust.carrName}">
                                            <input id="carrCode" name="carrCode" type="hidden" th:value="${entrust.carrCode}">
                                            <input id="carrierId" name="carrierId" type="hidden" th:value="${entrust.carrierId}" >
                                            <input name="carrType" id="carrType" type="hidden" th:value="${carrier.carrType}">
                                            <input name="carrBalaType" id="carrBalaType" type="hidden" th:value="${carrier.balaType}">
                                            <input name="params[costAmountOld]"  type="hidden" th:value="${entrustLot.costAmount}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">

                                <div class="form-group">
                                    <label class="col-sm-5" id="carnoSpan">车牌号：</label>
                                    <div class="col-sm-7">
                                        <div class="input-group">
                                            <span class="input-group-addon"><i
                                                    class="fa fa-search"></i></span>
                                            <input name="carno" id="carno" class="form-control valid" type="text"
                                                   readonly onclick="selectCar()" th:value="${entrust.carno}" >
                                            <input name="carnoId" id="carnoId" type="hidden" th:value="${entrust.carnoId}">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-3">
                                <div class="form-group">
                                    <label class="col-sm-5">司机手机：</label>
                                    <div class="col-sm-7">
                                        <input type="text" class="form-control" disabled
                                               name="driverMobile" id="driverMobile" th:value="${entrust.driverMobile}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class="form-group">
                                    <label class="col-sm-5">司机结算方式：</label>
                                    <div class="col-sm-7">
                                        <select name="balatype" id="balatype" class="form-control valid"
                                                th:with="type=${@dict.getType('driver_bala_type')}">
                                            <option></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:field="${entrust.balatype}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="row">

                            <div class="col-md-3 col-sm-3">
                                <div class="form-group">
                                    <label class="col-sm-5" id="driverNameSpan">司机：</label>
                                    <div class="col-sm-7">
                                        <div class="input-group">
                                            <span class="input-group-addon"><i
                                                    class="fa fa-search"></i></span>
                                            <input name="driverName" id="driverName" class="form-control valid" readonly
                                                   type="text" onclick="selectDriver()" th:value="${entrust.driverName}">
                                            <input id="driverId" name="driverId" type="hidden" th:value="${driverIds}" >
                                            <input id="lotId" name="lotId" type="hidden" th:value="${entrust.lotId}" >

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class="form-group">
                                    <label class="col-sm-5">车长：</label>
                                    <div class="col-sm-7">
                                        <input id="carLen" name="carLen" class="form-control valid"
                                               type="text" disabled >
                                        <input name="carLenId" id="carLenId" class="form-control valid"
                                               th:value="${entrust.carLenId}" type="hidden">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-3">
                                <div class="form-group">
                                    <label class="col-sm-5">车型：</label>
                                    <div class="col-sm-7">
                                        <input name="carTypeName" id="carTypeName" class="form-control valid"
                                               th:value="${entrust.carTypeName}" type="text" disabled>
                                        <input name="carTypeCode" id="carTypeCode" class="form-control valid"
                                               th:value="${entrust.carTypeCode}" type="hidden">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-3">
                                <div class="form-group">
                                    <label class="col-sm-5"><font color="red">运输方式：</font></label>
                                    <div class="col-sm-7">
                                        <select name="transCode" id="transCode" class="form-control valid" required
                                                th:with="type=${@dict.getType('trans_code')}">
                                            <option></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:field="${entrust.transCode}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5"><span style="color: red">结算金额：</span></label>
                                    <div class="col-sm-7">
                                        <div class="input-group">
                                            <span class="input-group-addon">¥</span>
                                            <input name="params[costAmount]" id="costAmount" type="text" class="form-control valid" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                                   required aria-required="true" autocomplete="off" onblur="calOilAmount(1)" th:value="${entrustLot.costAmount}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5 oilRatioColor">油卡比例：</label>
                                    <div class="col-sm-7">
                                        <div class="input-group">
                                            <input name="params[oilRatio]" id="oilRatio" oninput="$.numberUtil.onlyNumberCustom(this,100,0,5,2);" type="text"
                                                   class="form-control" onblur="calOilAmount(2)" autocomplete="off" th:value="${entrustLot.oilRatio}">
                                            <span class="input-group-addon">%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5 " >油卡金额：</label>
                                    <div class="col-sm-7">
                                        <input type="text" id="oilAmount" name="params[oilAmount]" oninput="$.numberUtil.onlyNumberTwoDecimal(this);"
                                               onblur="calOilAmount(3)" class="form-control" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseTwo">运费</a>

                    </h4>
                </div>
                <div id="collapseTwo" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--订单货品费用明细 begin-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <input type="hidden" id="indexLot" name="params[indexLot]">
                            <table border="0" id="lotTable" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 3%;"><a class="collapse-link add-alink" onclick="insertLotRow()" title="新增行">+</a></th>
                                    <th style="width: 10%;color: red">费用类型</th>
                                    <th style="width: 10%;color: red">费用(元)</th>
                                    <th style="width: 10%">油卡号</th>
                                    <th style="width: 20%;">备注</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr name="regRowsLot" th:each="payDetail,payDetailStat:${payDetailList}">
                                    <td><a class="close-link del-alink" onclick="removeRowLot(this)" title="删除行">-</a></td>
                                    <td>
                                        <select th:name="'params[costTypeFreight'+${payDetailStat.index}+']'"  class="form-control valid" aria-invalid="false" th:with="type=${@dict.getType('cost_type_freight')}" required>
                                            <option value="">-- 请选择 --</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${payDetail.costTypeFreight eq dict.dictValue}"></option>
                                        </select>
                                    </td>
                                    <td><input th:name ="'params[moneyFreight'+${payDetailStat.index}+']'" placeholder="" class="form-control" oninput="$.numberUtil.onlyNumberNegative(this)" type="text" th:value="${payDetail.transFeeCount}" required></td>
                                    <td><input th:name="'params[oilFreight'+${payDetailStat.index}+']'" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off" th:value="${payDetail.oilCardNumber}"></td>
                                    <td><input th:name="'params[memoFreight'+${payDetailStat.index}+']'" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off" th:value="${payDetail.memo}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-offset-5 col-sm-10">
                <button type="button" class="btn btn-sm btn-primary" onclick="commit()"><i class="fa fa-check"></i>保
                    存
                </button>&nbsp;
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
                </button>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
</body>
<script type="text/template" id="rowTmplLot">
    <tr name="regRowsLot">
        <td><a class="close-link del-alink" onclick="removeRowLot(this)" title="删除行">-</a></td>
        <td>
            <select name="costTypeFreight" class="form-control valid" aria-invalid="false" th:with="type=${@dict.getType('cost_type_freight')}" required>
                <option value="">-- 请选择 --</option>
                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
            </select>
        </td>
        <td><input name ="moneyFreight" placeholder="" class="form-control" oninput="$.numberUtil.onlyNumberNegative(this)" type="text" required></td>
        <td><input name="oilFreight" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off"></td>
        <td><input name="memoFreight" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off"></td>
    </tr>
</script>
<script th:inline="javascript">
    var carType = [[${@dict.getType('car_type')}]];//车辆类型
    var carLen = [[${@dict.getType('car_len')}]];//车长
    var privateCar = [[${privateCar}]];//自有承运商类别id
    var segmentIds = [[${entrust.segmentId}]];//运段id

    var outsourcTeam = [[${outsourcTeam}]];//外协车队id
    var logisticsLine = [[${logisticsLine}]];//物流专线id

    var payDetailList = [[${payDetailList}]];
    var indexLot = payDetailList.length;

    $(function () {
        var carlenId =  $("#carLenId").val();
        carLen.forEach(function (value, index, array) {
            if (value.dictValue == carlenId) {

                $("#carLen").val(value.dictLabel)
            }
        });

        if (privateCar === $("#carrType").val()) {
            // $("#driverName").attr("disabled", true);
            // $("#driverName").attr("readonly", false);
        } else {
            // $("#driverName").attr("disabled", false);
            // $("#driverName").attr("readonly", true);
        }

        if($("#carrBalaType").val() == "1"){
            //单笔
            $(".oilRatioColor").css("color", "");
            $("#oilRatio").removeAttr("required");
        }else if($("#carrBalaType").val() == '2'){
            //月结
            $(".oilRatioColor").css("color", "red")
            $("#oilRatio").attr("required", "required");
        }

        $("#indexLot").val(indexLot);
    });

    //选择承运商
    function selectCarrier() {
        $.modal.open("选择承运商", ctx + "basic/carrier/selectCarrier",'','420',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //清空车牌号信息
            $("#carnoId").val("");
            $("#carno").val("");
            //清空车长
            $("#carLenId").val("");
            $("#carLen").val("");
            //清空车型
            $("#carTypeCode").val("");
            $("#carTypeName").val("");

            //清空司机
            $("#driverName").val("");
            $("#driverId").val("");
            //清空司机手机
            $("#driverMobile").val("");

            //赋值
            $("#carrName").val(rows[0]["carrName"]);
            $("#carrCode").val(rows[0]["carrCode"]);
            $("#carrierId").val(rows[0]["carrierId"]);
            //承运商类别  结算方式
            $("#carrType").val(rows[0]["carrType"]);
            $("#carrBalaType").val(rows[0]["balaType"]);
            //判断月结和单笔
            if(rows[0]["balaType"] == "1"){
                //单笔
                $(".oilRatioColor").css("color", "");
                $("#oilRatio").removeAttr("required");
            }else if(rows[0]["balaType"] == '2'){
                //月结
                $(".oilRatioColor").css("color", "red")
                $("#oilRatio").attr("required", "required");
            }

            //如果为自有车队  则无法选择司机
            if (privateCar === rows[0]["carrType"]) {
                /**
                 * todo:暂注释 2020-06-03
                 */
                // $("#driverName").attr("disabled", true);
                // $("#driverName").attr("readonly", false);
            } else if (outsourcTeam === rows[0]["carrType"]) {

                //外协车队
                //车辆司机必填
                $("#carnoSpan").css("color", "red");
                $("#driverNameSpan").css("color", "red");
                $("#carno").attr("required", "required");
                $("#driverName").attr("required", "required");
                //校验车牌 用于去除红框
                $("#form-carrier-edit").validate().element($("#carno"));
                //校验司机 用于去除红框
                $("#form-carrier-edit").validate().element($("#driverName"));
            } else if (logisticsLine === rows[0]["carrType"]) {
                //物流专线车辆司机非必填
                $("#carnoSpan").css("color", "");
                $("#driverNameSpan").css("color", "");
                $("#carno").removeAttr("required");
                $("#driverName").removeAttr("required");

                //校验车牌 用于去除红框
                $("#form-carrier-edit").validate().element($("#carno"));
                //校验司机 用于去除红框
                $("#form-carrier-edit").validate().element($("#driverName"));
            } else {
                $("#driverName").attr("disabled", false);
                $("#driverName").attr("readonly", true);
            }
            $("#form-carrier-edit").validate().element($("#carrName"))
            layer.close(index);
        });
    }

    //选择司机
    function selectDriver() {
        //承运商id
        var carrierId = $("#carrierId").val();
        $.modal.open("选择司机", ctx + "basic/driver/checkboxSelectDriver?type=1&carrierId=" + carrierId, 1000, '', function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //清空数据
            $("#driverMobile").val("");

            //拼接司机名称
            var driverName = $.map(rows,function (row) {
                return row["driverName"];
            }).join(",");
            //拼接司机id
            var driverId = $.map(rows,function (row) {
                return row["driverId"];
            }).join(",");
            //拼接司机手机
            var driverMobile = $.map(rows,function (row) {
                return row["phone"];
            }).join(",");

            //回填数据
            $("#driverName").val(driverName);
            $("#driverId").val(driverId);
            $("#driverMobile").val(driverMobile);
            layer.close(index);
        });
    }
    /**
     *  选择选择车辆
     */
    function selectCar(){
        var carrierId = $("#carrierId").val();
        var carrType = $("#carrType").val();
        $.modal.open("选择车辆", ctx + "basic/car/selectCarLic?segmentIds=" + segmentIds + "&carrierId=" + carrierId + "&carrType=" + carrType + "&type=1", '', '', function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            //清空车长
            $("#carLenId").val("");
            $("#carLen").val("");
            //清空车型
            $("#carTypeCode").val("");
            $("#carTypeName").val("");
            //清空司机
            $("#driverName").val("");
            $("#driverId").val("");
            //清空司机手机
            $("#driverMobile").val("");

            //车辆id
            $("#carnoId").val(rows[0]["carId"]);
            //车牌
            $("#carno").val(rows[0]["carno"]);
            //车长
            var carLengthId = rows[0]["carLengthId"];
            carLen.forEach(function (value, index, array) {
                if (value.dictValue == carLengthId) {
                    $("#carLenId").val(carLengthId);
                    $("#carLen").val(value.dictLabel)
                }
            });
            //车型
            var carTypeId = rows[0]["vehicleclassificationcode"];
            carType.forEach(function (value, index, array) {
                if (value.dictValue == carTypeId) {
                    $("#carTypeCode").val(carTypeId);
                    $("#carTypeName").val(value.dictLabel)
                }
            });

            var carrType = $("#carrType").val();
            //根据承运商查询对应的司机
            $.ajax({
                url: ctx + "basic/driver/getDriverByCarrTypeAndCarId",
                type: "post",
                dataType: "json",
                data: {carId: rows[0]["carId"],carrType:carrType},
                success: function (result) {
                    if (result.code == 0) {
                        $("#driverName").val(result.data.driverName);
                        $("#driverId").val(result.data.driverId);
                        $("#driverMobile").val(result.data.driverMobile);
                    }
                }
            });
            layer.close(index);
        });
    }

    /**
     * 计算油卡金额
     */
    function calOilAmount(type) {
        //结算金额
        var costAmount = parseFloat($("#costAmount").val());
        //油卡比例
        var oilRatio = parseFloat($("#oilRatio").val());
        //油卡金额
        var oilAmount = parseFloat($("#oilAmount").val());
        //回填回付现金
        if(!isNaN(costAmount)){
            $("#transFeeCount_4").val(costAmount);
        }
        if(type == 2 || type == 1){
            //计算油卡金额
            if(!isNaN(costAmount) && !isNaN(oilRatio)){
                $("#oilAmount").val((costAmount * oilRatio / 100).toFixed(2));
                return ;
            }
        }
        if(type == 3 || type == 1){
            //计算油卡比列
            if(!isNaN(costAmount) && !isNaN(oilAmount)){
                $("#oilRatio").val((oilAmount/costAmount*100).toFixed(2));
                return ;
            }
        }
    }



    /* 新增运费表格行 */
    function insertLotRow() {
        var rowTmplLot = $("#rowTmplLot").html();
        rowTmplLot = rowTmplLot.replace("costTypeFreight","params[costTypeFreight"+indexLot+"]");
        rowTmplLot = rowTmplLot.replace("moneyFreight","params[moneyFreight"+indexLot+"]");
        rowTmplLot = rowTmplLot.replace("oilFreight","params[oilFreight"+indexLot+"]");
        rowTmplLot = rowTmplLot.replace("memoFreight","params[memoFreight"+indexLot+"]");
        $("#lotTable tbody").append(rowTmplLot);
        indexLot++;
        $("#indexLot").val(indexLot);
    }

    /* 删除指定表格行 */
    function removeRowLot(obj) {
        $("#lotTable tbody").find(obj).closest("tr").remove();
    }

    /**
     * 表单提交
     */
    function commit() {
        if ($.validate.form()) {
            var flag = true;
            var money = $("#costAmount").val();
            //只能回复现金
            if($("#carrBalaType").val() == '2'){
                $("tr[name='regRowsLot']").each(function () {
                    var costTypeFreight = $(this).find("select[name^='params[costTypeFreight']").val();
                    if(costTypeFreight != '4'){
                        flag = false;
                        return false;
                    }
                });
            }
            $("tr[name='regRowsLot']").each(function () {
                var moneyFreight = parseFloat($(this).find("input[name^='params[moneyFreight']").val());
                money = money - moneyFreight;
            });
            if(flag){
                if(money == 0){
                    $(":disabled").attr("disabled", false);
                    $.operate.saveTab(ctx + "trustDeed/updateCarrier",  $("#form-carrier-edit").serializeArray());
                }else {
                    $.modal.alertWarning("结算金额与运费总金额不一致")
                }
            }else{
                $.modal.alertWarning("月结承运商运费类型只能选择回付现金")
            }

        }
    }
</script>
</html>