<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('委托单列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--委托单id-->
                <input type="hidden" name="entrustId" id="entrustId" th:value="${entrustId}">
                <input type="hidden" name="depositId"  th:value="${depositId}">
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">定金状态 ：</label>
                            <div class="col-sm-8">
                                <select name="depositStatus" class="form-control" th:with="type=${depositStatus}">
                                    <option value="">--请选择--</option>
                                    <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <input id="hiddenText" type="text" style="display:none" />
                    <div class="col-md-6 col-sm-6">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="add()"  shiro:hasPermission="tms:trustDeed:deposit:add">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single disabled" onclick="checkRecord()" shiro:hasPermission="tms:deposit:checkRecord">
                <i class="fa fa-calculator"></i> 审核记录
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "tms/deposit";

    //定金状态
    var depositStatus = [[${depositStatus}]]
    //已收状态
    var receivedStatus = [[${receivedStatus}]];
    //申请状态
    var applyStatus = [[${applyStatus}]];
    //申请退定金状态
    var applyBackStatus = [[${applyBackStatus}]];



    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search()
            }
        });
        var options = {
            url: prefix + "/entrust/list",
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            fixedNumber: 0,
            showToggle: false,
            showColumns: true,
            modalName: "定金",
            uniqueId: "depositId",
            height: 560,
            clickToSelect: true,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:trustDeed:deposit:edit')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="修改"  onclick="edit(\'' + row.depositId + '\',\''+row.depositStatus+'\')"><i class="fa fa-edit" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:trustDeed:deposit:detail')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="明细" onclick="detail(\'' + row.depositId + '\')"><i class="fa fa-newspaper-o" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:trustDeed:deposit:applyBackDeposit')}]] != "hidden" && row.depositStatus == receivedStatus) {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="申请退定金"  onclick="applyBackDeposit(\'' + row.depositId + '\',\''+row.depositStatus+'\')"><i class="fa fa-reply" style="font-size: 15px;"></i></a>');
                        }
                        if ([[${@permission.hasPermi('tms:trustDeed:deposit:neverDeposit')}]] != "hidden" && row.depositStatus == receivedStatus) {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="不退"  onclick="neverDeposit(\'' + row.depositId + '\',\'' + row.depositStatus + '\')"><i class="fa fa-remove" style="font-size: 15px;"></i></a>');
                        }
                       /* if ([[${@permission.hasPermi('tms:trustDeed:deposit:backDeposit')}]] != "hidden" && row.depositStatus === applyBackStatus) {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="退定金"  onclick="backDeposit(\'' + row.depositId + '\',\'' + row.depositStatus + '\')"><i class="fa fa-reply" style="font-size: 15px;"></i></a>');
                        }*/
                        return actions.join('');
                    }
                },
                {
                    title: '定金状态',
                    align: 'left',
                    field: 'depositStatus',
                    formatter: function status(row,value) {
                        var context = '';
                        depositStatus.forEach(function (v) {
                            if (v.value == value.depositStatus) {
                                context = v.context;
                                return false;
                            }
                        });
                        return context;
                    }
                },

                {
                    title: '定金金额',
                    field: 'depositAmount',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }
            ]
        };
        $.table.init(options);
    });

    /**
     * 新增
     * @param depositId
     */
    function add() {
        var entrustId = $("#entrustId").val();
        $.modal.open("新增", prefix + "/add?entrustId="+entrustId);
    }

    /**
     * 修改
     * @param depositId
     */
    function edit(depositId,depositStatus) {
        if(depositStatus != applyStatus){
            $.modal.alertWarning("该定金不是申请状态！");
            return;
        }
        $.modal.open("修改", prefix + "/edit/"+depositId);
    }

    /**
     * 明细
     */
    function detail(depositId) {
        var url = prefix + "/detail/"+depositId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "定金明细",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }
    /**
     * 申请退定金
     * @param depositId
     */
    function applyBackDeposit(depositId,depositStatus) {
        if(depositStatus != receivedStatus){
            $.modal.alertWarning("该定金不是已收状态！");
            return;
        }
        $.modal.open("申请退定金", ctx + "tms/deposit/back_deposit/"+depositId+"/"+3);
    }


    /**
     * 审核记录
     */
    function checkRecord() {
        var depositId = $.table.selectColumns("depositId");
        var url = prefix + "/check_record?depositId="+depositId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核记录",
            area: ['800px','600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 不退定金
     * @param depositId
     * @param depositStatus
     */
    function neverDeposit(depositId,depositStatus) {
        if(depositStatus != receivedStatus){
            $.modal.alertWarning("该定金不是已收状态！");
            return;
        }
        var data = {};
        data.depositId = depositId;
        $.modal.confirm("是否确认不退定金？", function () {
            $.operate.post(prefix + "/never_deposit", data);
        });
    }







</script>
</body>
</html>