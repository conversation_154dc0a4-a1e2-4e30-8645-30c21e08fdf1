<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('定金-add')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-deposit-add" class="form-horizontal" novalidate="novalidate">
        <!--委托单id-->
        <input type="hidden" th:value="${entrustId}" name="entrustId">
        <!--定金状态：默认申请-->
        <input type="hidden" th:value="0" name="depositStatus">

        <input type="hidden" th:value="${entrust.carrierId}" name="carrierId">
        <input type="hidden" th:value="${entrust.carrName}" name="carrierName">
        <!--行号-->
        <input type="hidden" name="bankNo" id="bankNo">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span
                                            style="color: red; ">定金金额：</span></label>
                                    <div class="col-sm-8">
                                        <input name="depositAmount" id="depositAmount" oninput="$.numberUtil.onlyNumber(this);" class="form-control" required type="text"
                                               maxlength="25">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款账户：</label>
                                    <div class="col-sm-8">
                                        <select name="recAccount" id="recAccount" class="form-control"  >
                                            <option value=""></option>
                                            <option th:each="dict : ${carrBankList}"
                                                    th:text="${dict.bankAccount}"
                                                    th:id="${dict.carrBankId}"
                                                    th:value="${dict.carrBankId}">
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款卡号：</label>
                                    <div class="col-sm-8">
                                        <input name="recCardNo" id="recCardNo" class="form-control"  type="text" required disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款银行：</label>
                                    <div class="col-sm-8">
                                        <input name="recBank" id="recBank" class="form-control"  type="text"  disabled>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row ">
                            <div class="col-sm-12 col-md-6 ">
                                <div class="form-group">
                                    <label class="col-sm-2">凭证：</label>
                                    <div class="col-sm-10">
                                        <input name="permitTidIx" id="permitTidIx" class="form-control" type="file" multiple>
                                        <input type="hidden" id="permitTid" name="permitTid">
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">

</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script type="text/javascript">
    var prefix = ctx + "tms/deposit";

    $(function () {
        $('#collapseOne').collapse('show');

        //图片功能
        var picParam = {
            maxFileCount: 0,
            publish: "cmt",
            fileType: null
        };
        $.file.initAddFiles("permitTidIx", "permitTid", picParam);

        //加载收款信息
        $('#recAccount').change(function(){
            var id = $(this).find(":selected").attr("id");
            var data = {carrBankId:id};
            var url = ctx + "payManage/selectReceInfo";
            $.ajax({
                url : url,
                method : 'POST',
                data : data,
                success:function (data) {
                    $("#recCardNo").val(data.bankCard);
                    $("#recBank").val(data.bankName);
                    $("#bankNo").val(data.bankNo);

                    //手动校验
                    $("#form-deposit-add").validate().element($("#recCardNo"));
                    $("#form-deposit-add").validate().element($("#recBank"));
                }
            })

        });


    });


    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.modal.loading("正在处理中，请稍后...");
            $.modal.disable();
            $('#permitTidIx').fileinput('upload');
            jQuery.subscribe("cmt", commit);
        }
    }

    function commit() {
        var dis = $(":disabled");
        dis.attr("disabled", false);
        $.operate.save(prefix + "/add", $('#form-deposit-add').serialize(),function (result) {
            if (result.code != 0) {
                dis.attr("disabled", true);
            }
        });
    }

</script>
</body>

</html>