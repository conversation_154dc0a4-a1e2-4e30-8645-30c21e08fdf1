<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('定金-detail')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>

<body>
<div class="form-content">
    <form id="form-deposit-edit" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="depositId" th:value="${deposit.depositId}">
        <input type="hidden" name="depositStatus" id="depositStatus" th:value="${deposit.depositStatus}">

        <input type="hidden" name="bankNo"  th:value="${deposit.bankNo}">
        <input type="hidden" name="recCardNo"  th:value="${deposit.recCardNo}">
        <input type="hidden" name="recAccount"  th:value="${deposit.recAccount}">
        <input type="hidden" name="recBank"  th:value="${deposit.recBank}">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">基础信息</a>
                    </h5>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>委托单号：</span></label>
                                    <div class="col-sm-8" th:text="${deposit.entrustVbillno}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>承运商：</span></label>
                                    <div class="col-sm-8" th:text="${deposit.carrierName}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>发货单号：</span></label>
                                    <div class="col-sm-8" th:text="${deposit.invoiceVbillno}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>定金金额：</span></label>
                                    <div class="col-sm-8" th:text="${deposit.depositAmount}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row ">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>定金状态：</span></label>
                                    <div class="col-sm-8" th:each="dict : ${@dict.getType('deposit_status')}"
                                         th:if="${dict.dictValue} == ${deposit.depositStatus}" th:text="${dict.dictLabel}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>收款账户：</span></label>
                                    <div class="col-sm-8" th:each="dict : ${carrBankList}"
                                         th:if="${dict.carrBankId} == ${deposit.recAccount}" th:text="${dict.bankAccount}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>收款银行：</span></label>
                                    <div class="col-sm-8" th:text="${deposit.recBank}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span>收款卡号：</span></label>
                                    <div class="col-sm-8" th:text="${deposit.recCardNo}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row ">
                            <div class="col-sm-12 col-md-6 ">
                                <div class="form-group">
                                    <label class="col-sm-2">凭证：</label>
                                    <div class="col-sm-10">
                                        <div  th:each="pic:${sysUploadFiles}">
                                            <img modal="zoomImg" style="width:70px; height:50px" th:src="@{${pic.filePath}}"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">

</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>

</body>
<script th:inline="javascript">
    /**
     * 收取
     *
     */
    function submit() {
        var depositStatus = $("#depositStatus").val();
        if(depositStatus != 0){
            $.modal.alertWarning("该定金不是申请状态！");
            return;
        }
        var url = ctx + "tms/deposit/finance/get_deposit"
        var data = $("#form-deposit-edit").serializeArray();
        $.operate.save(url, data);
    }
</script>

</html>