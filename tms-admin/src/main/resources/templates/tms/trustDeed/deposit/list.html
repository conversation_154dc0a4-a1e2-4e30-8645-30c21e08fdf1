<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('定金管理列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!--委托单id-->
                <input type="hidden" name="entrustId" id="entrustId" th:value="${entrustId}">
                <div class="row">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">发货单号：</label>-->
                            <div class="col-sm-12">
                                <input name="invoiceVbillno" id="invoiceVbillno" placeholder="请输入发货单号"
                                       class="form-control" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">承运商：</label>-->
                            <div class="col-sm-12">
                                <input name="carrierName" id="carrierName" placeholder="请输入承运商" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">定金状态 ：</label>-->
                            <div class="col-sm-12">
                                <select name="searchStatus" id="searchStatus" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="定金状态" multiple th:with="type=${depositStatus}">
                                    <option value="">--请选择--</option>
                                    <option th:each="dict : ${type}" th:text="${dict.context}" th:value="${dict.value}"></option>
                                </select>

                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-2">收取定金日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control time-input"
                                       id="startDate"  name="params[recDateStart]" placeholder="收取定金开始日期">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control time-input" placeholder="收取定金结束日期"
                                       id="endtDate"  name="params[recDateEnd]">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-2">退定金日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control time-input"
                                       id="backDateStart"  name="params[backDateStart]" placeholder="退定金开始日期">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control time-input" placeholder="退定金结束日期"
                                       id="backDateEnd"  name="params[backDateEnd]">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-6"></label>-->
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger multiple disabled" onclick="removeAll()" shiro:hasPermission="tms:finance:deposit:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-primary single disabled" onclick="checkRecord()" shiro:hasPermission="tms:deposit:checkRecord">
                <i class="fa fa-calculator"></i> 审核记录
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:deposit:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "tms/deposit/finance";

    //定金状态
    var depositStatus = [[${depositStatus}]];
    //申请状态
    var applyStatus = [[${applyStatus}]];
    //申请退定金状态
    var applyBackStatus = [[${applyBackStatus}]];
    //已收状态
    var receivedStatus = [[${receivedStatus}]];
    //付款类型
    var payMethod = [[${@dict.getType('pay_method')}]];


    $(function () {
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });
        var options = {
            url: prefix + "/list",
            removeUrl: prefix + "/remove",
            fixedColumns: true,
            exportUrl: prefix + "/export",
            fixedNumber: 0,
            showToggle: false,
            showColumns: true,
            modalName: "定金",
            uniqueId: "depositId",
            height: 560,
            clickToSelect: true,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if ([[${@permission.hasPermi('tms:finance:deposit:detail')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs" href="javascript:void(0)" title="收取"onclick="detail(\'' + row.depositId + '\',\'' + row.depositStatus + '\')"><i class="fa fa-dollar" style="font-size: 15px;"></i></a>');
                        }
                        // if ([[${@permission.hasPermi('tms:finance:deposit:backDeposit')}]] != "hidden") {
                        //     actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="收取"  onclick="getDeposit(\'' + row.depositId + '\',\'' + row.depositStatus + '\')"><i class="fa fa-dollar" style="font-size: 15px;"></i></a>');
                        // }
                        if ([[${@permission.hasPermi('tms:finance:deposit:backDeposit')}]] != "hidden" && row.depositStatus === applyBackStatus) {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="退定金"  onclick="backDeposit(\'' + row.depositId + '\',\'' + row.depositStatus + '\')"><i class="fa fa-reply" style="font-size: 15px;"></i></a>');
                        }
                        return actions.join('');
                    }
                },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceVbillno'
                },
                {
                    title: '客户简称',
                    align: 'left',
                    field: 'custAbbr'
                },
                {
                    title: '要求提货日',
                    align: 'left',
                    field: 'reqDeliDate'
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.deliAddressName+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriAddressName;
                    }
                },
                {
                    title: '车牌号',
                    align: 'left',
                    field: 'carno'
                },
                {
                    title: '总运费',
                    align: 'right',
                    field: 'costAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '承运商名称',
                    align: 'left',
                    field: 'carrierName'
                },
                {
                    title: '定金状态',
                    align: 'left',
                    field: 'depositStatus',
                    formatter: function status(row,value) {
                        var context = '';
                        depositStatus.forEach(function (v) {
                            if (v.value == value.depositStatus) {
                                context = v.context;
                                return false;
                            }
                        });
                        return context;
                    }
                },
                {
                    title: '付款类型',
                    align: 'left',
                    field: 'payType',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(payMethod, value);
                    }
                },
                {
                    title: '定金金额',
                    field: 'depositAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '收取人',
                    align: 'left',
                    field: 'recUserName'
                },
                {
                    title: '收取时间',
                    align: 'left',
                    field: 'recDate'
                },
                {
                    title: '退定金人员',
                    align: 'left',
                    field: 'backDepositUserName'
                },
                {
                    title: '退定金时间',
                    align: 'left',
                    field: 'backDepositDate'
                },

                {
                    title: '创建人',
                    align: 'left',
                    field: 'regUserName'
                },
                {
                    title: '创建时间',
                    align: 'left',
                    field: 'regDate'
                },
                {
                    title: '委托单号',
                    align: 'left',
                    field: 'entrustVbillno'
                }
            ]
        };
        $.table.init(options);
    });

    /**
     * 明细
     */
    function detail(depositId,depositStatus) {
        var url = ctx + "tms/deposit/detail/"+depositId;
        var btn = ['<i class="fa fa-dollar"></i> 收取','<i class="fa fa-close"></i> 关闭'];
        if(depositStatus != applyStatus){
            btn = ['<i class="fa fa-close"></i> 关闭'];
        }
        layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "定金收取",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: btn,
            btn1: function (index, layero) {
                if(depositStatus == applyStatus){
                    var iframeWin = layero.find('iframe')[0];
                    iframeWin.contentWindow.submit(index, layero);
                }else{
                    layer.close(index);
                }
            },
            btn2: function (index, layero) {
                return true;
            }
        });
    }

    /**
     * 删除
     */
    function removeAll(){
        //判断定金是否是新建状态
        var vbillstatusList = $.map($.btTable.bootstrapTable('getSelections'), function (row) {
            return row["depositStatus"];
        });
        var b = false;
        $.each(vbillstatusList, function (i, v) {
            if (v != 0) {
                b = true;
                return false;
            }
        });
        if (b) {
            $.modal.alertWarning("只有申请状态下才能删除！");
            return;
        }

        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix = "/remove";
        $.modal.confirm("确认要删除选中的" + rows.length + "条数据吗?", function() {
            var url = $.table._option.removeUrl;
            var data = { "ids": rows.join() };
            $.operate.submit(url, "post", "json", data);
        });
    }


    /**
     * 退定金
     * @param depositId
     */
    function backDeposit(depositId,depositStatus) {
        if(!(depositStatus == receivedStatus || depositStatus == applyBackStatus)){
            $.modal.alertWarning("该定金不是已收/申请退定金状态！");
            return;
        }
        $.modal.open("退定金", ctx + "tms/deposit/back_deposit/"+depositId+"/"+0);
    }

    /**
     * 不退定金
     * @param depositId
     * @param depositStatus
     */
    function neverDeposit(depositId,depositStatus) {
        if(depositStatus != receivedStatus){
            $.modal.alertWarning("该定金不是已收状态！");
            return;
        }
        var data = {};
        data.depositId = depositId;
        $.modal.confirm("是否确认不退定金？", function () {
            $.operate.post(prefix + "/never_deposit", data);
        });
    }

    /**
     * 审核记录
     */
    function checkRecord() {
        var depositId = $.table.selectColumns("depositId");
        var url = ctx + "tms/deposit/check_record?depositId="+depositId;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核记录",
            area: ['800px',   '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.searchStatus = $.common.join($('#searchStatus').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }








</script>
</body>
</html>