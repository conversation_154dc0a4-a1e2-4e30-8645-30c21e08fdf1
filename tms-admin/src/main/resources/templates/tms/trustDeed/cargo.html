<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('货量更新')"/>
</head>
<style type="text/css">
    .td td {
        position: relative
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 24px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 24px;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .tl th{
        text-align: left;
    }
    .tl td{
        text-align: left;
    }
    .form-control-static{
        word-break: break-word;
    }

    .btnT{
        border-radius: 50px;
        padding: 3px 10px;
    }
    .f18{
        font-size: 18px;
    }
    .fw{
        font-weight: 600;
    }
    .toBut{
        vertical-align: middle;
        display: inline-block;
    }
    .tl{
        text-align: left;
        display: inline-block;
    }
    .tr{
        text-align: right;
        display: inline-block;
        /* width: 100px; */
        margin-left: 10px;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .th_img{
        max-width:100px;
        max-height:48px;
    }
    .topInNum{
        border: none;
        background-color: #ffffff;
        width: 7em;
    }
    .topInNumA{
        border: none;
        background-color: #ffffff;
        width: 4em;
        text-align: right;
    }
    .cur{
        cursor: pointer;
    }
</style>
<body>
<div class="form-content">
    <form id="form-goods-edit" class="form-horizontal" novalidate="novalidate" >
        <!--承运商结算方式-->
        <input type="hidden" id="balaType" th:value="${carrier.balaType}">
        <!--运费标记-->
        <input type="hidden" id="shippingFlag" name="shippingFlag">

        <input type="hidden" id="orderno" name="orderno" th:value="${entrust.orderno}">
        <input type="hidden" id="entrustId" name="entrustId" th:value="${entrust.entrustId}">
        <input name="corDate" id="corDate" th:value="${#dates.format(entrust.corDate, 'yyyy-MM-dd HH:mm:ss')}" type="hidden">

       

        <input th:field="*{invoice.memo}" type="hidden">
        <div class="row">
            <div class="col-md-12 toBut">
                <span class="toBut f18 fw">[[${entrust.custName}]]</span><span class="btnT btn-white ml5 toBut">客户</span>
<!--                <div class="form-group" style="display: inline-block;margin-left: 5px !important;vertical-align: middle;margin-bottom: 0;">-->
<!--                    <div class="tl cur" style="border-bottom: 1px dashed #23c6c8;" onclick="onCustOrderno(this)">-->
<!--                        <span th:text="${ (invoice.custOrderno !=null && invoice.custOrderno !='')  ? invoice.custOrderno : '暂无客户发货单号' }"></span>-->
<!--                    </div>-->
<!--                    <div class="tl hide">-->
<!--                       <input class="form-control" th:field="${invoice.custOrderno}">-->
<!--                    </div>-->
<!--                </div>-->
            </div>
        </div>

        <div class="mt10" style="position: relative;">
            <div class="flex">
                <div style="margin-left: -10px;">
                    <label class="tr">承运商：</label>
                    <div class="tl" th:text="${carrier.carrName}"></div>
                </div>

                <div>
                    <label class="tr">司机：</label>
                    <div class="tl" th:text="${entrust.driverName+'/'+entrust.driverMobile}"></div>
                </div>

                <div>
                    <label class="tr">车牌号：</label>
                    <div class="tl" th:text="${entrust.carno}"></div>
                </div>
            </div>

            <div style="position: absolute;right: 0;top: -100%;">
                <div class="flex_right" th:if=" ${entrust.vbillstatus} eq '待确认'">
                    <img th:src="@{/img/daiqueren.png}" style="width: 80px;height: 65px">
                </div>
                <div class="flex_right" th:if=" ${entrust.vbillstatus} eq '已确认'">
                    <img th:src="@{/img/yqr.png}" style="width: 80px;height: 65px">
                </div>
                <div class="flex_right" th:if=" ${entrust.vbillstatus} eq '已提货'">
                    <img th:src="@{/img/yitihuo.png}" style="width: 80px;height: 65px">
                </div>
                <div class="flex_right" th:if=" ${entrust.vbillstatus} eq '已到货'">
                    <img th:src="@{/img/yidaohuo.png}" style="width: 80px;height: 65px">
                </div>
                <div class="flex_right" th:if=" ${entrust.vbillstatus} eq '关闭'">
                    <img th:src="@{/img/guanbi.png}" style="width: 80px;height: 65px">
                </div>
                <div class="flex_right" th:if=" ${entrust.vbillstatus} eq '分配车队'">
                    <img th:src="@{/img/fpcd.png}" style="width: 80px;height: 65px">
                </div>
            </div>
        </div>

        <div class="mt10">
            <div>
                <span class="label label-warning pa2">装</span> 
                <div style="display: inline-block;">
                    <span th:text="${#dates.format(entrust.reqDeliDate, 'yyyy-MM-dd HH:mm:ss')}"></span> <span th:text="${entrust.deliProName+entrust.deliCityName+entrust.deliAreaName+entrust.deliAddrName}"></span>
                </div>
            </div>
            <div class="mt5">
                <span class="label label-success pa2">卸</span>
                <div style="display: inline-block;">
                    <span th:text="${#dates.format(entrust.reqArriDate, 'yyyy-MM-dd HH:mm:ss')}"></span> <span th:text="${entrust.arriProName+entrust.arriCityName+entrust.arriAreaName+entrust.arriAddrName}"></span>
                </div>
            </div>
        </div>

        <div class="mt10" style="display: flex;justify-content: space-between">
            <div class="">
                <div class="form-group">
                    <label class="tr">提货照片：</label>
                    <div class="tl">
                        <div class="fl picviewer" th:if="${not #lists.isEmpty(tihuoList)}">
                            <span th:each="tihuopic:${tihuoList}" th:if="${tihuopic.filePath!=null}">
                                <img class="th_img" th:src="@{${tihuopic.filePath}}"/>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="">
                <div class="form-group">
                    <label class="tr">到货照片：</label>
                    <div class="tl">
                        <div class="fl picviewer" th:if="${not #lists.isEmpty(daohuoList)}">
                            <span th:each="daohuopic:${daohuoList}" th:if="${daohuopic.filePath!=null}">
                                <img class="th_img" th:src="@{${daohuopic.filePath}}"/>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="">
                <div class="form-group">
                    <label class="tr">回单照片：</label>
                    <div class="tl">
                        <div class="fl picviewer" th:if="${not #lists.isEmpty(uploadFileList)}">
                            <span th:each="file:${uploadFileList}" th:if="${file.filePath!=null}">
                                <img class="th_img" th:src="@{${file.filePath}}"/>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt10" style="display: flex;justify-content: space-between">
            <div class="">
                <span class="label label-coral">应收</span> <span class="fw">货品信息</span>
            </div>


            <div class="">
                合计货量：
                <input name="numCount" th:field="${invoice.numCount}" type="text" class="topInNumA" disabled>件 |
                <input name="weightCount" th:field="${invoice.weightCount}" type="text" class="topInNumA" disabled>吨 |
                <input type="text" th:field="${invoice.volumeCount}" name="volumeCount" class="topInNumA" disabled>m³ |
                <span class="text-warning">
                    合计金额：￥<input type="text" th:value="${allAmount}" name="totalFee" id="totalFee" class="topInNum" disabled>
                </span>
            </div>
        </div>

        <div class="mt10" style="display: flex;justify-content: space-between">
            <div style="display: flex;white-space: nowrap;">
                <!-- 计价方式-->
                <div style="display: inline-block;">
                    <select th:onchange="getPrice()"
                            name="billingMethod" id="billingMethod"
                            class="form-control valid" aria-invalid="false"
                            disabled >
                        <!--                        <option value=""></option>-->
                        <option th:each="billingMethod:${billingMethods}" th:value="${billingMethod.value}"
                                th:text="${billingMethod.context}"
                                th:selected="${invoice.billingMethod} == ${billingMethod.value}"></option>
                    </select>
                </div>
                <!-- 单价-->
                <div style="display: inline-block;margin-left: 15px;" >
                    <div class="input-group" style="width: 175px;">
                        <span class="input-group-addon">单价</span>
                        <input name="unitPrice" id="unitPrice"
                               th:value="${invoice.unitPrice}"
                               placeholder="" class="form-control" type="text" autocomplete="off"
                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateSum()"
                               disabled/>
                    </div>
                </div>
                <!-- 运费-->
                <div style="display: inline-block;margin-left: 15px;" >
                    <div class="input-group" style="width: 175px;">
                        <span class="input-group-addon">运费</span>
                        <input name="costAmount" id="costAmount"
                               th:value="${freightAmount}"
                               placeholder="" class="form-control" type="text" autocomplete="off"
                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);changeCostAmount()"
                               disabled/>
                    </div>
                </div>
                <!-- 在途-->
                <div style="display: inline-block;margin-left: 15px;" >
                    <div class="input-group" style="width: 175px;">
                        <span class="input-group-addon">在途</span>
                        <input name="onWayAmountFee" id="onWayAmountFee"
                               th:value="${onWayAmount}"
                               placeholder="" class="form-control" type="text" autocomplete="off"
                               disabled
                        />
                    </div>
                </div>
            </div>
        </div>

        <div class="fixed-table-body tl" style="margin: 10px 0 0;">
            <table id="infoTab" class="custom-tab table table-bordered">
                <thead style="background: #f4f6f7;">
                <tr>
                    <th style="width: 10%;">货品名称</th>
                    <th colspan="3" style="width: 20%;">原始单数量|重量|体积</th>
                    <th style="width: 13%;">数量</th>
                    <th style="width: 13%;">重量</th>
                    <th style="width: 13%;">体积</th>
<!--                    <th style="width: 15%;">计价方式</th>-->
<!--                    <th style="width: 7%;">单价</th>-->
<!--                    <th style="width: 7%;">金额</th>-->
                </tr>
                </thead>
                <tbody>
                <tr th:each="invPackGoods,invPackGoodsStat : ${invPackGoodsList}">
<!--                                <td rowspan="2">xxxxxx</td>-->
                    <td>
                        <!-- 货品名称-->
                        <input th:value="${invPackGoods.goodsName}" type="text" class="form-control valid" disabled>
                        <input th:value="${invPackGoods.goodsCharacter}"
                               th:id="${invPackGoods.invPackGoodsId}+_goodsCharacter" type="hidden">
                        <input th:value="${invPackGoods.invPackGoodsId}" type="hidden"
                               th:name="|invPackGoodsList[${invPackGoodsStat.index}].invPackGoodsId|">
                    </td>
                    <td>
                        <!--原始单数量|重量|体积-->
                        <input th:name="|invPackGoodsList[${invPackGoodsStat.index}].num|"
                               th:id="${invPackGoods.invPackGoodsId}+_num"
                               th:value="${invPackGoods.num}"
                               class="form-control" type="text" disabled>
                        <input type="hidden" th:id="${invPackGoods.invPackGoodsId}+_num_base">
                    </td>
                    <td>
                        <!--原始单数量|重量|体积-->
                        <input th:name="|invPackGoodsList[${invPackGoodsStat.index}].weight|"
                               th:id="${invPackGoods.invPackGoodsId}+_weight"
                               th:value="${invPackGoods.weight}"
                               class="form-control" type="text" disabled>
                        <input type="hidden" th:id="${invPackGoods.invPackGoodsId}+_weight_base">
                    </td>
                    <td>
                        <!--原始单数量|重量|体积-->
                        <input th:name="|invPackGoodsList[${invPackGoodsStat.index}].volume|"
                               th:id="${invPackGoods.invPackGoodsId}+_volume"
                               th:value="${invPackGoods.volume}"
                               class="form-control" type="text" disabled>
                        <input type="hidden" th:id="${invPackGoods.invPackGoodsId}+_volume_base">
                    </td>
                    <td>
                        <!--数量-->
                        <div th:each="entPackGoods,entPackGoodsStat : ${entPackGoodsList}"
                             th:if="${entPackGoods.invPackGoodsId} == ${invPackGoods.invPackGoodsId}"
                             class="input-group">
                            <input th:name="|entPackGoodsList[${entPackGoodsStat.index}].num|"
                                   th:value="${entPackGoods.num}" class="form-control"
                                   th:id="|entPackGoodsList[${entPackGoodsStat.index}].num|"
                                   type="text" min="0" oninput="" maxlength="10" th:data-id="${entPackGoods.invPackGoodsId}"
                                   th:oninput="|$.numberUtil.onlyNumber(this);changeEnt(this,'num')|" disabled>
                            <span class="input-group-addon">件</span>

                            <!--invPackGoodsId-->
                            <input th:name="|entPackGoodsList[${entPackGoodsStat.index}].invPackGoodsId|"
                                   th:value="${entPackGoods.invPackGoodsId}" class="form-control"
                                   th:id="|entPackGoodsList[${entPackGoodsStat.index}].invPackGoodsId|" type="hidden">

                        </div>
                    </td>
                    <td>
                        <!--重量-->
                        <div th:each="entPackGoods,entPackGoodsStat : ${entPackGoodsList}"
                             th:if="${entPackGoods.invPackGoodsId} == ${invPackGoods.invPackGoodsId}"
                             class="input-group">
                            <input th:name="|entPackGoodsList[${entPackGoodsStat.index}].weight|"
                                   th:value="${entPackGoods.weight}" class="form-control"
                                   th:id="|entPackGoodsList[${entPackGoodsStat.index}].weight|"
                                   type="text" min="0" maxlength="10" th:data-id="${entPackGoods.invPackGoodsId}"
                                   th:oninput="|$.numberUtil.onlyNumber(this);changeEnt(this,'weight')|" disabled >
                            <span class="input-group-addon">吨</span>
<!--                                        <input type="hidden" th:id="${entPackGoods.invPackGoodsId}+_weight_old"-->
<!--                                               th:value="${entPackGoods.weight}">-->
                        </div>
                    </td>
                    <td>
                        <!--体积-->
                        <div th:each="entPackGoods,entPackGoodsStat : ${entPackGoodsList}"
                             th:if="${entPackGoods.invPackGoodsId} == ${invPackGoods.invPackGoodsId}"
                             class="input-group">
                            <input th:name="|entPackGoodsList[${entPackGoodsStat.index}].volume|"
                                   th:value="${entPackGoods.volume}" class="form-control"
                                   th:id="|entPackGoodsList[${entPackGoodsStat.index}].volume|"
                                   type="text" min="0" maxlength="10" th:data-id="${entPackGoods.invPackGoodsId}"
                                   th:oninput="|$.numberUtil.onlyNumber(this);changeEnt(this,'volume')|" disabled >
                            <span class="input-group-addon">m³</span>
<!--                                        <input type="hidden" th:id="${entPackGoods.invPackGoodsId}+_volume_old"-->
<!--                                               th:value="${entPackGoods.volume}">-->
                        </div>
                    </td>
                  <!--  <td>
                        &lt;!&ndash;计价方式&ndash;&gt;
                        <div>
                            <select th:onchange="getPrice(this)"
                                    th:name="|invPackGoodsList[${invPackGoodsStat.index}].billingMethod|"
                                    th:id="${invPackGoods.invPackGoodsId}+_billingMethod" th:data-id="${invPackGoods.invPackGoodsId}"
                                    class="form-control valid" aria-invalid="false"
                                    th:disabled="${!changeAmount || invoice.settlementCheck == 1}"
                                    th:required="${changeAmount}">
                                <option value=""></option>
                                <option th:each="billingMethod:${billingMethods}" th:value="${billingMethod.value}"
                                        th:text="${billingMethod.context}"
                                        th:selected="${invPackGoods.billingMethod} == ${billingMethod.value}"></option>
                            </select>
                        </div>
                    </td>
                    <td>
                        &lt;!&ndash;单价&ndash;&gt;
                        <div class="input-group">
                            <input th:name="|invPackGoodsList[${invPackGoodsStat.index}].pc|"
                                   th:value="${invPackGoods.pc}" th:id="${invPackGoods.invPackGoodsId}+_pc"
                                   placeholder="" class="form-control" type="text" autocomplete="off"
                                   th:data-id="${invPackGoods.invPackGoodsId}"
                                   th:oninput="|$.numberUtil.onlyNumberTwoDecimal(this);calculateSum(this)|"
                                   th:disabled="${!changeAmount || invPackGoods.billingMethod == '3' || invPackGoods.billingMethod == '4'
                                   || invoice.settlementCheck == 1}"
                                   />
                        </div>
                    </td>
                    <td>
                        &lt;!&ndash;金额&ndash;&gt;
                        <div class="input-group">
                            <input th:name="|invPackGoodsList[${invPackGoodsStat.index}].sum|"
                                   th:value="${invPackGoods.sum}" th:id="${invPackGoods.invPackGoodsId}+_sum"
                                   th:oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculateTotals()"
                                   placeholder="" class="form-control" type="text" autocomplete="off"
                                   th:disabled="${!changeAmount || invPackGoods.billingMethod == '3' || invPackGoods.billingMethod == '4'
                                   || invoice.settlementCheck == 1}"
                                   th:required="${changeAmount}">
                        </div>
                    </td>-->
                </tr>
                </tbody> 
            </table>
        </div>

        <div class="mt10" style="display: flex;justify-content: space-between">
            <div class="">
                <span class="label label-info">应付</span>
            </div>

            <div>
                结算方式：
                <span th:if="${entrustLot.pricingMethod == 0}">按吨</span>
                <span th:if="${entrustLot.pricingMethod == 1}">按方</span>
                <span th:if="${entrustLot.pricingMethod == 2}">按件</span>
                <span th:if="${entrustLot.pricingMethod == 3}">按票</span>
                <span th:if="${entrustLot.pricingMethod == 4}">按吨(包车)</span>
                <span th:if="${entrustLot.pricingMethod == 5}">按方(包车)</span>
                <span th:if="${entrustLot.pricingMethod == 6}">按件(包车)</span>
            </div>
            <div style="margin-left: -60px;">
                单价：
                <span th:text="${entrustLot.unitPrice}"></span>
            </div>

            <div class="text-warning">
                <!-- 合计货量： -->
                合计金额：￥<input id="transFeeCountSum" class="topInNum" th:value="${transFeeCountSum}" disabled>
                已付金额：￥<span th:text="${gotAmountSum}"></span>
                未付金额：￥<span th:text="${ungotAmountSum}"></span>
            </div>
        </div>
        <!-- <div class="fixed-table-body" style="margin: 10px 0 0;">
            <table class="custom-tab table table-bordered">
                <thead style="background: #f4f6f7;">
                <tr>
                    <th style="width: 12%;">货品名称</th>
                    <th colspan="3" style="width: 34%;">原始单数量|重量|体积</th>
                    <th style="width: 13%;">数量</th>
                    <th style="width: 13%;">重量</th>
                    <th style="width: 13%;">体积</th>
                    <th style="width: 15%;">计价方式</th>
                </tr>
                </thead>
                <tbody>
                <tr>

                </tr>
                </tbody> 
            </table>
        </div> -->

        <div class="fixed-table-body" style="margin: 10px 0 0;">
            <table class="custom-tab table">
                <thead style="background: #f4f6f7;">
                <tr>
                    <!-- <th style="width: 3%;">
                        <a class="collapse-link" onclick="register()"
                           shiro:hasAnyPermissions="tms:trace:register,tms:fleet:trace:register"
                           style="font-size: 20px;color: #2ab594;" title="新增行">+</a>
                    </th> -->
                    <!--<th style="width: 20%;">应付单号</th>
                    <th style="width: 10%;">应付单状态</th>-->
                    <th style="width: 10%;">费用类型</th>
                    <th style="width: 10%;">付款类型</th>
                    <th style="width: 10%;">总金额(元)</th>
                    <th style="width: 10%;">已付金额(元)</th>
                    <th style="width: 10%;">未付金额(元)</th>
<!--                     <th style="width: 17%;">时间</th>-->
                </tr>
                </thead>
                <tbody>
                <tr th:each="payDetail,payDetailStat : ${payDetailGroupList}">
                    <!--<td th:text="${payDetail.vbillno}"></td>
                    <td>
                        <span class="label label-default" th:if="${payDetail.vbillstatus} == 0">新建</span>
                        <span class="label label-warning" th:if="${payDetail.vbillstatus} == 1">已确认</span>
                        <span class="label label-coral" th:if="${payDetail.vbillstatus} == 2">已对账</span>
                        <span class="label label-info" th:if="${payDetail.vbillstatus} == 3">部分核销</span>
                        <span class="label label-success" th:if="${payDetail.vbillstatus} == 4">已核销</span>
                        <span class="label label-inverse" th:if="${payDetail.vbillstatus} == 5">关闭</span>
                        <span class="label label-success" th:if="${payDetail.vbillstatus} == 6">已申请</span>
                        <span class="label label-info" th:if="${payDetail.vbillstatus} == 7">核销中</span>
                        <span class="label label-default" th:if="${payDetail.vbillstatus} == 8">审核中</span>
                    </td>-->
                    <td>
                        <div th:if="${payDetail.freeType == '0'}">运费</div>
                        <div th:if="${payDetail.freeType == '1'}">在途费用</div>
                        <div th:if="${payDetail.freeType == '2'}">调整费用</div>
                    </td>
                    <td>
                        <div th:each="dict : ${@dict.getType('cost_type_freight')}"
                             th:if="${dict.dictValue == payDetail.costTypeFreight and payDetail.freeType == '0'}"
                             th:text="${dict.dictLabel}"></div>
                        <div th:each="dict : ${@dict.getType('cost_type_on_way')}"
                             th:if="${dict.dictValue == payDetail.costTypeOnWay and payDetail.freeType == '1'}"
                             th:text="${dict.dictLabel}"></div>
                    </td>
                    <!--<td th:text="${payDetail.transFeeCount}"></td>-->
                    <input class="form-control" th:value="${payDetail.lotId}" th:name="|payDetailList[${payDetailStat.index}].lotId|" type="hidden">
                    <input class="form-control" th:value="${payDetail.freeType}" th:name="|payDetailList[${payDetailStat.index}].freeType|" type="hidden">
                    <input class="form-control" th:value="${payDetail.costTypeFreight}" th:name="|payDetailList[${payDetailStat.index}].costTypeFreight|" type="hidden">
                    <td><input class="form-control" th:value="${payDetail.transFeeCount}" th:name="|payDetailList[${payDetailStat.index}].transFeeCount|"
                               th:oninput="$.numberUtil.onlyNumberTwoDecimal(this);calculatePayDetail()" autocomplete="off"></td>
                    <td th:text="${payDetail.gotAmount}"></td>
                    <td th:text="${payDetail.ungotAmount}"></td>
                    <!--<td th:text="${#dates.format(payDetail.regDate, 'yyyy-MM-dd')}"></td>-->
                </tr>
                </tbody>
            </table>
        </div>

        <input name="deptId" type="hidden" id="treeId">
      
        <input type="hidden" id="indexLot" name="params[indexLot]">
      

    </form>
</div>
<script type="text/template" id="rowTmplLot">
    <tr name="regRowsLot">
        <td><a class="close-link del-alink" onclick="removeRowLot(this)" title="删除行">-</a></td>
        <td>
            <select name="costTypeFreight" class="form-control valid" aria-invalid="false" th:with="type=${@dict.getType('cost_type_freight')}" required>
                <option value="">-- 请选择 --</option>
                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
            </select>
        </td>
        <td><input name ="moneyFreight" placeholder="" class="form-control" oninput="$.numberUtil.onlyNumberNegative(this)" type="text" required></td>
        <td><input name="oilFreight" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off"></td>
        <td><input name="memoFreight" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off"></td>
    </tr>
</script>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var entPackGoodsList = [[${entPackGoodsList}]];
    // 承运商协议价
    var carrierProtocols = [[${carrierProtocols}]];
    // 委托单
    var entrust = [[${entrust}]];

    var payDetailList = [[${payDetailList}]];
    var indexLot = payDetailList.length;

    //获取是否车队数据
    var isFleetData = [[${isFleetData}]];
    //获取是否分配给车队数据
    var isFleetAssign = [[${isFleetAssign}]]

    //能否更新金额
    var changeAmount = [[${changeAmount}]]

    var settlementCheck = [[${invoice.settlementCheck}]]

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');


        // console.log([[${entrust}]])
        var options = {};
        $.table.init(options);

        $("#indexLot").val(indexLot);

        /**
         * 没有协议价且选择计费方式  展示运费
         */
        if(carrierProtocols.length == 0 && $.common.isNotEmpty(entrust.pricingMethod)){
            $("#shippingIsShow").show();
            $("#shippingFlag").val(1);
        }else{
            $("#shippingIsShow").hide();
            $("#shippingFlag").val(0);
        }

        $.each(entPackGoodsList, function (i, item) {
            var id = item.invPackGoodsId + "_";

            //运单货品
            var numEnt = item.num;
            var weightEnt = item.weight;
            var volumeEnt = item.volume;

            //发货单件数
            var numInv = $("#" + id + "num").val() == "" ? 0 : $("#" + id + "num").val();
            //发货单重量
            var weightInv = $("#" + id + "weight").val() == "" ? 0 : $("#" + id + "weight").val();
            //发货单体积
            var volumeInv = $("#" + id + "volume").val() == "" ? 0 : $("#" + id + "volume").val();

            //用发货单的值 减去 运单的值 作为基础数据
            $("#" + id + "num_base").val(decimal(numInv - numEnt, 5));
            $("#" + id + "weight_base").val(decimal(weightInv - weightEnt, 5));
            $("#" + id + "volume_base").val(decimal(volumeInv - volumeEnt, 5));
        });
    });



    /**
     * 当货品明细的值修改时，同步更新相关委托单值
     */
    function changeEnt(obj,type) {
        //如果是车队的数据  并且是分配车队的
        //debugger;
        var id = obj.getAttribute('data-id') + "_";
        var value = obj.value === "" ? 0 : obj.value;
        if(changeAmount) {
            //改变 相关委托单里的值
            $("td[name='" + id + type + "']").text(value);
            //发货单对应的值 用基础的值加上用户输入的值
            var base = $("#" + id + type + "_base").val();
            $("#" + id + type).val(parseFloat(base) + parseFloat(value));
            //计算货量合计
            calculateTotals();

            var billingMethod = $("#billingMethod").val();


            if (settlementCheck != 1 && (billingMethod == 1 || billingMethod == 2 || billingMethod == 5)) {
                //获取价格
                getPrice(obj);
            }
        }
    }

    /**
     * 获取发货单货品单价 根据 发货单id、计价方式、货品特性、件数/重量/体积 获取单价
     */
    function getPrice(obj) {
        var id = obj.getAttribute('data-id') + "_";

        var billingMethod = $("#billingMethod").val();
        // if (billingMethod == 3 || billingMethod == 4) {
        //     $("#" + id + "pc").attr("disabled", "disabled");
        //     $("#" + id + "sum").attr("disabled", "disabled");
        //
        //     calculateTotals();
        //
        //     return;
        // }else {
        //     $("#" + id + "pc").removeAttr("disabled");
        //     $("#" + id + "sum").removeAttr("disabled");
        // }


        //获取合同价
        var param = {};
        //发货单id
        param.invoiceId = $("#orderno").val();
        //计价方式
        param.billingMethod = billingMethod;
        // //货品特性
        // param.goodsCharacter = $("#" + id + "goodsCharacter").val();

        //件数
        param.num = $("#numCount").val();
        //重量
        param.weight = $("#weightCount").val();
        //件数
        param.volume = $("#volumeCount").val();

        if (param.billingMethod === undefined) {
            return;
        }

        $.ajax({
            url: ctx + "trustDeed/cargoGetPrice",
            type: "post",
            dataType: "json",
            data: param,
            success: function(result) {
                if (result.code == 0) {
                    //单价
                    $("#unitPrice").val(result.data.price);
                    //金额sum
                    $("#costAmount").val(result.data.totalPrice);

                    let onWayAmountFee = $("#onWayAmountFee").val() == "" ? 0 : $("#onWayAmountFee").val();
                    let costAmount = $("#costAmount").val() == "" ? 0 : $("#costAmount").val();
                    $("#totalFee").val(decimal(parseFloat(costAmount) + parseFloat(onWayAmountFee),2));

                    //计算合计
                    // calculateTotals();
                }
            }
        });

    }


    /**
     * 计算金额
     *  @param
     */
    function calculateSum() {
        // var id = obj.getAttribute('data-id') + "_";

        //计价方式为
        var billingMethod = $("#billingMethod").val();

        //单价
        var unitPrice = $("#unitPrice").val() == "" ? 0 : $("#unitPrice").val();

        //件数
        var num = $("#numCount").val() == "" ? 0 : $("#numCount").val();
        //重量
        var weight = $("#weightCount").val() == "" ? 0 : $("#weightCount").val();
        //体积
        var volume = $("#volumeCount").val() == "" ? 0 : $("#volumeCount").val();

        //计算重量
        if (billingMethod == 1) {
            $("#costAmount").val(decimal(parseFloat(parseFloat(unitPrice) * parseFloat(weight)),2));

        } else if (billingMethod == 2) {
            //计算体积
            $("#costAmount").val(decimal(parseFloat(parseFloat(unitPrice) * parseFloat(volume)),2));
        }else if (billingMethod == 5) {
            //计算件数
            $("#costAmount").val(decimal(parseFloat(parseFloat(unitPrice) * parseFloat(num)),2));
        } else if (billingMethod == 6) {

            // $("#costAmount").val(decimal(parseFloat(parseFloat(weight) * parseFloat(mileage) * parseFloat(unitPrice)), 2));
        }else if (billingMethod == 7) {

            // $("#costAmount").val(decimal(parseFloat(parseFloat(mileage) * parseFloat(unitPrice)), 2));
        } else {
            //整车固定价  总价等于单价
            $("#costAmount").val(unitPrice);
        }

        var onWayAmountFee = $("#onWayAmountFee").val() == "" ? 0 : $("#onWayAmountFee").val();
        let costAmount = $("#costAmount").val() == "" ? 0 : $("#costAmount").val();
        $("#totalFee").val(decimal(parseFloat(costAmount) + parseFloat(onWayAmountFee),2));

        //计算合计
        // calculateTotals();
    }

    function changeCostAmount(){
        //计价方式为
        var billingMethod = $("#billingMethod").val();

        if (billingMethod == 3) {
            //整车固定价  总价等于单价
            $("#unitPrice").val($("#costAmount").val());
        }
        let onWayAmountFee = $("#onWayAmountFee").val() == "" ? 0 : $("#onWayAmountFee").val();
        let costAmount = $("#costAmount").val() == "" ? 0 : $("#costAmount").val();
        $("#totalFee").val(decimal(parseFloat(costAmount) + parseFloat(onWayAmountFee),2));
    }


    /**
     * 计算货物合计
     */
    function calculateTotals() {
        //件数
        var numList = $("[name^='invPackGoodsList'][name$='num']");
        var numCount = 0;
        numList.each(function(){
            if ($(this).val() != "") {
                numCount = parseFloat(numCount + parseFloat($(this).val()));
            }

        });
        $("#numCount").val(decimal(numCount, 5));

        //重量
        var weightList = $("[name^='invPackGoodsList'][name$='weight']");
        var weightCount = 0;
        weightList.each(function(){
            if ($(this).val() != "") {
                weightCount = parseFloat(weightCount + parseFloat($(this).val()))
            }
        });
        $("#weightCount").val(decimal(weightCount, 5));

        //体积
        var volumeList = $("[name^='invPackGoodsList'][name$='volume']");
        var volumeCount = 0;
        volumeList.each(function(){
            if ($(this).val() != "") {
                volumeCount = parseFloat(volumeCount + parseFloat($(this).val()))
            }
        });
        $("#volumeCount").val(decimal(volumeCount, 5));

        //总金额
        // var costAmountList = $("[name^='invPackGoodsList'][name$='sum']");
        // var costAmount = 0;
        // costAmountList.each(function(){
        //     if ($(this).val() != "") {
        //         costAmount = parseFloat(costAmount + parseFloat($(this).val()))
        //     }
        // });
        // $("#costAmount").val(decimal(costAmount, 2));
    }

    function calculatePayDetail() {
        //总金额
        var costAmountList = $("[name^='payDetailList'][name$='transFeeCount']");
        var costAmount = 0;
        costAmountList.each(function(){
            if ($(this).val() != "") {
                costAmount = parseFloat(costAmount + parseFloat($(this).val()))
            }
        });
        $("#transFeeCountSum").val(decimal(costAmount, 2));
    }

    /* 新增运费表格行 */
    function insertLotRow() {
        var rowTmplLot = $("#rowTmplLot").html();
        rowTmplLot = rowTmplLot.replace("costTypeFreight","params[costTypeFreight"+indexLot+"]");
        rowTmplLot = rowTmplLot.replace("moneyFreight","params[moneyFreight"+indexLot+"]");
        rowTmplLot = rowTmplLot.replace("oilFreight","params[oilFreight"+indexLot+"]");
        rowTmplLot = rowTmplLot.replace("memoFreight","params[memoFreight"+indexLot+"]");
        $("#lotTable tbody").append(rowTmplLot);
        indexLot++;
        $("#indexLot").val(indexLot);
    }

    /* 删除指定表格行 */
    function removeRowLot(obj) {
        $("#lotTable tbody").find(obj).closest("tr").remove();
    }

    /**
     *     费用登记
     */
    function register() {
        // var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);

        // var isClose = $.table.selectColumns("isClose");
        let isClose = [[${entrustLot.isClose}]]
        //关账判断
        if(isClose == 1){
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }

        let receiptConfirmFlag = [[${entrust.receiptConfirmFlag}]]
        if(receiptConfirmFlag == 1){
            $.modal.alertWarning("该发货单已回单确认，无法进行操作！");
            return false;
        }

        var invoiceId = [[${entrust.orderno}]]
        var entrustId = [[${entrust.entrustId}]]

        var url;
        if(isFleetData == 0) {  //0不是  1是
            //alert("不是车队数据")  跳转TraceController
            url = ctx + "trace/register/"+entrustId+"/1";
        }else {
            //alert("车队数据") 跳转FleetTraceController
            url = ctx + "fleettrace/register/"+entrustId+"/1";
        }

        //验证发货单是否超过五天
        $.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.open("费用登记", url);
                }
            }
        });
    }

    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }

    function insertRow() {
        $.modal.open("费用登记", '');
    }
    /**
     * 提交
     */
    function submitHandler() {
        if ($.validate.form()) {
            // var b = false;
            // $("[id$='_pc']").each(function (){
            //     let val4 = $(this).parent().parent().prev().children('div').children("[id$='_billingMethod']").val();
            //     let val = $(this).val()
            //     if (val == null || val == '' && (val4 != 3 && val4 != 4)) {
            //         b = true
            //     }
            // })
            // if (b && settlementCheck != 1) {
            //     $.modal.alertWarning("请输入单价！");
            //     return;
            // }

            // //总金额
            // var costAmountList = $("[name^='invPackGoodsList'][name$='sum']");
            // var costAmount = 0;
            // costAmountList.each(function(){
            //     if ($(this).val() != "") {
            //         costAmount = parseFloat(costAmount + parseFloat($(this).val()))
            //         costAmount = decimal(costAmount, 2)
            //     }
            // });
            // if ($("#costAmount").val() != costAmount) {
            //     $.modal.alertWarning("合计金额与运费金额不同。");
            //     return;
            // }

            //计价方式为
            var billingMethod = $("#billingMethod").val();


            if ($("#costAmount").val() == '' || $("#costAmount").val() == 0) {
                $.modal.alertWarning("运费不能为空。");
                return;
            }

            if (($("#unitPrice").val() == '' || $("#unitPrice").val() == 0) && settlementCheck != 1 && billingMethod !=3) {
                $.modal.alertWarning("单价不能为空。");
                return;
            }


            var dis = $(":disabled");
            dis.attr("disabled", false);
            var flag = true;
            //运费标记
            var shippingFlag = $("#shippingFlag").val();
            if(shippingFlag === 1){
                var money = $("#settlementAmount").val();
                //只能回复现金
                if($("#balaType").val() == '2'){
                    $("tr[name='regRowsLot']").each(function () {
                        var costTypeFreight = $(this).find("select[name^='params[costTypeFreight']").val();
                        if(costTypeFreight != '4'){
                            flag = false;
                            return false;
                        }
                    });
                }
                $("tr[name='regRowsLot']").each(function () {
                    var moneyFreight = parseFloat($(this).find("input[name^='params[moneyFreight']").val());
                    money = money - moneyFreight;
                });
                if(flag){
                    if(money == 0){
                        $.operate.saveTab(ctx + "trustDeed/cargoEdit",  $("#form-goods-edit").serializeArray(),function (result) {
                            if (result.code != 0) {
                                dis.attr("disabled", true);
                            }
                        });
                    }else {
                        $.modal.alertWarning("结算金额与运费总金额不一致")
                    }
                }else{
                    $.modal.alertWarning("月结承运商运费类型只能选择回付现金")
                }
            }else{
                $.operate.saveTab(ctx + "trustDeed/cargoEdit",  $("#form-goods-edit").serializeArray(),function (result) {
                    if (result.code != 0) {
                        dis.attr("disabled", true);
                    }
                });
            }
        }
    }
    function onCustOrderno(obj) {
        $(obj).addClass("hide");
        $(obj).next().removeClass("hide");
    }
</script>
</body>

</html>