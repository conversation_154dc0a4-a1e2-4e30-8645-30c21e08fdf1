<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('货量更新')"/>
</head>
<style type="text/css">
    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .tooltip-inner {
        width:500px !important;
        max-width: 600px !important;
    }
    .tooltipBody{
        width:500px !important;
    }
    .td td {
        position: relative
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 24px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 24px;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .tl th{
        text-align: left;
    }
    .tl td{
        text-align: left;
    }
    .form-control-static{
        word-break: break-word;
    }

    .btnT{
        border-radius: 50px;
        padding: 3px 10px;
    }
    .f18{
        font-size: 18px;
    }
    .fw{
        font-weight: 600;
    }
    .toBut{
        vertical-align: middle;
        display: inline-block;
    }
    .tl{
        text-align: left;
        display: block;
    }
    .tr{
        text-align: right;
        display: inline-block;
        /* width: 100px; */
        margin-left: 10px;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .th_img{
        max-width:100px;
        max-height:48px;
    }
    .topInNum{
        border: none;
        background-color: #ffffff;
        width: 4em;
    }
    .cur{
        cursor: pointer;
    }
    .fixedfooter{
        position: fixed;
        bottom: 0;
        left: 50%;
        width: 160px;
        margin-left: -80px;
    }
    .borderRadius{
        border-radius:4px
    }
    .input-group-addon{
        background-color: #eeeeee;
    }
    .select-table{
        padding-left:0px
    }

</style>
<body>
<div class="form-content">
    <form id="form-goods-edit" class="form-horizontal" novalidate="novalidate" >
        <!--承运商结算方式-->
        <input type="hidden" id="balaType" th:value="${carrier.balaType}">
        <!--运费标记-->
        <div class="row">
            <div class="col-md-12 toBut">
                <span class="toBut fw">操作指引：①货量更新 → ②回单确认 → ③申请打款</span>
            </div>
        </div>

        <div class="mt10" style="display: flex;justify-content: space-between">
            <div class="">
                <span class="label label-coral">货源信息</span>

            </div>

            <div style="font-weight: bolder;">
                总货量：
                <span id="myNumSpan_old"></span>件|
                <span id="myWeightSpan_old"></span>吨|
                <span id="myVolumeSpan_old"></span>m³
                →
                <span id="myNumSpan"></span>|
                <span id="myWeightSpan"></span>|
                <span id="myVolumeSpan"></span>
                <th:block th:if="${ifHide == 0}">
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    总应收：
                    ¥<span id="totalSpan_Old"></span>
                    →
                    <span id="totalSpan"></span>
                </th:block>
            </div>
        </div>

        <div class="fixed-table-body tl" style="margin: 10px 0 0;">
            <table  class="custom-tab table table-bordered">
                <thead style="background: #f4f6f7;">
                <tr>
                    <th style="width: 10%;">客户信息</th>
                    <th style="width: 18%;">装卸货信息</th>
                    <th style="width: 6%;">车长车型</th>
                    <th style="width: 3%;">异常</th>
                    <th style="width: 9%;">回单上传</th>
                    <th style="width: 7%;">客户单号</th>
                    <th style="width: 9%;">货品名称</th>
                    <th style="width: 10%;">应收货量</th>
                    <th style="width: 8%;" th:if="${ifHide == 0}">计价方式/单价</th>
                    <th style="width: 10%;" th:if="${ifHide == 0}">应收合同价</th>
                    <th style="width: 6%;">其他应收</th>
                    <th style="width: 6%;" th:if="${ifHide == 0}">应收合计</th>
                </tr>
                </thead>
                <tbody>
                <div th:each="item,entStat : ${entrusts}">
                    <input type="hidden" th:name="|cargoGoodsVOList[${entStat.index}].entrustId|" th:value="${item.entrustId}">
                    <input type="hidden" th:id="|invoiceNum${entStat.index}|" th:value="${item.invoice.numCount}">
                    <input type="hidden" th:id="|invoiceWeight${entStat.index}|" th:value="${item.invoice.weightCount}">
                    <input type="hidden" th:id="|invoiceVolume${entStat.index}|" th:value="${item.invoice.volumeCount}">
                    <input type="hidden" th:id="|invoiceCarLen${entStat.index}|" th:value="${item.invoice.carLen}">
                    <input type="hidden" th:id="|invoiceCarType${entStat.index}|" th:value="${item.invoice.carType}">
                    <input type="hidden" th:id="|invoiceTransCode${entStat.index}|" th:value="${item.invoice.transCode}">
                    <input type="hidden" th:id="|invoiceCustomerId${entStat.index}|" th:value="${item.invoice.customerId}">

                    <input th:id="|contractIsFixedPrice${entStat.index}|" th:name="|cargoGoodsVOList[${entStat.index}].contractIsFixedPrice|"  type="hidden">
                    <input th:id="|contractVersionId${entStat.index}|" th:name="|cargoGoodsVOList[${entStat.index}].contractVersionId|" type="hidden">
                    <input th:id="|contractTotalCostPrice${entStat.index}|" th:name="|cargoGoodsVOList[${entStat.index}].contractTotalCostPrice|" type="hidden">

                    <tr th:each="goods,stat : ${item.multipleGoodsConns}">
                        <td th:if="${stat.index} == 0" th:rowspan="${stat.size}" >
                            <div style="display: inline-block;">[[${item.custAbbr}]]
                                <span class="label label-default" th:if="${item.vbillstatus} == 0">待确认</span>
                                <span class="label label-warning" th:if="${item.vbillstatus} == 1">已确认</span>
                                <span class="label label-coral" th:if="${item.vbillstatus} == 2">已提货</span>
                                <span class="label label-info" th:if="${item.vbillstatus} == 3">已到货</span>
                                <span class="label label-inverse" th:if="${item.vbillstatus} == 5">关闭</span>
                                <span class="label label-success" th:if="${item.vbillstatus} == 6">分配车队</span>
                            </div>
                            <div style="display: inline-block;">
                                <span>[[${item.invoiceVbillno}]]</span>
                            </div>
                            <div style="display: inline-block;">
                                <span><a th:onclick="|changDivStatus(${entStat.index})|">操作记录</a></span>
                            </div>
                        </td>
                        <td th:if="${stat.index} == 0" th:rowspan="${stat.size}">
                            <div style="display: inline-block;" th:each="addr:${item.multipleShippingAddressList}" th:if="${addr.addressType == 0}">
                                <span class="label label-warning pa2">装</span>
                                <span th:text="${#dates.format(item.reqDeliDate, 'yyyy-MM-dd HH')}+'点'"></span> <span th:text="${addr.provinceName+addr.cityName+addr.areaName+addr.detailAddr}"></span>
                            </div>
                            <div style="display: inline-block;" th:each="addr:${item.multipleShippingAddressList}" th:if="${addr.addressType == 1}">
                                <span class="label label-success pa2">卸</span>
                                <span th:text="${#dates.format(item.reqArriDate, 'yyyy-MM-dd HH')}+'点'"></span> <span th:text="${addr.provinceName+addr.cityName+addr.areaName+addr.detailAddr}"></span>
                                <div style="display: inline-block;text-decoration:line-through" th:if="${addr.isChangeAddress ==1}">
                                    <span class="label label-danger pa2">旧</span>
                                    <span th:text="${addr.caArriProName+addr.caArriCityName+addr.caArriAreaName+addr.caArriAddrName}"></span>
                                </div>
                            </div>

                        </td>
                        <td th:if="${stat.index} == 0" th:rowspan="${stat.size}" >[[${item.invoice.carLenName}]]<br>[[${item.invoice.carTypeName}]]</td>
                        <td th:if="${stat.index} == 0" th:rowspan="${stat.size}" th:onclick="abnormal([[${item.entrustId}]])" ><a>[[${item.expSize}]]</a></td>
                        <td th:if="${stat.index} == 0" th:rowspan="${stat.size}">
                             <span  th:each="file:${item.arriPickList}" th:if="${file.workAppendixType == '3'}">
                                    <img class="th_img picviewer" th:src="@{${file.filePath}}"/>
                                </span>
                            <a th:onclick="editArriPic([[${item.entrustId}]])" th:if="${item.vbillstatus == '3' && item.receiptConfirmFlag == 0}"><i class="fa fa-edit"></i></a>
                            <div th:if="${item.receiptConfirmFlag == 1}" style="line-height:5px">
                                <br>
                                <span class="btn-xs btn-info" >已确认</span>
                            </div>

                        </td>
                        <td><input type="text" th:name="|cargoGoodsVOList[${entStat.index}].multipleGoodsConnList[${stat.index}].custOrderno|" class="form-control borderRadius" th:value="${goods.custOrderno}"></td>
                       <input type="hidden" th:name="|cargoGoodsVOList[${entStat.index}].multipleGoodsConnList[${stat.index}].goodsName|" th:value="${goods.goodsName}" >
                        <td><input type="text" class="form-control borderRadius" th:value="${goods.goodsName}" disabled>
                            <div style="margin-top: 5px">
                            <span th:id="|adjustHistroy${entStat.index}${goods.invGoodsFromId}${goods.invGoodsToId}|" class="btn-xs btn-info" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" title="加载中..." >调整记录</span>
                            <span th:if="${item.multipleGoodsConns.size() > 1}" class="btn-xs btn-warning" data-toggle="tooltip" data-container="body" data-placement="bottom" data-html="true" th:title="|${goods.arriAddr}|" >卸货地</span>
                            </div>
                        </td>
                        <td>
                            <div style="display:inline-table;">
                                <input type = "hidden" th:name="|cargoGoodsVOList[${entStat.index}].multipleGoodsConnList[${stat.index}].invGoodsFromId|" th:value="${goods.invGoodsFromId}">
                                <input type = "hidden" th:name="|cargoGoodsVOList[${entStat.index}].multipleGoodsConnList[${stat.index}].invGoodsToId|" th:value="${goods.invGoodsToId}">
                                <input type = "hidden" th:name="|cargoGoodsVOList[${entStat.index}].multipleGoodsConnList[${stat.index}].segmentId|" th:value="${goods.segmentId}">
                                <input type = "hidden" th:name="|cargoGoodsVOList[${entStat.index}].multipleGoodsConnList[${stat.index}].entrustId|" th:value="${goods.entrustId}">

                                <div style="display: inherit">
                                    <input th:id="|myNum${entStat.index}${stat.index}|" th:name="|cargoGoodsVOList[${entStat.index}].multipleGoodsConnList[${stat.index}].num|" th:value="${goods.num}" class="form-control " type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);computeTotalGoods();computeAmountLate(${entStat.index})|" maxlength="10" required>
                                    <span class="input-group-addon ">件</span>
                                </div>
                                <div style="display: inherit;margin-top: 5px">
                                    <input th:id="|myWeight${entStat.index}${stat.index}|" th:name="|cargoGoodsVOList[${entStat.index}].multipleGoodsConnList[${stat.index}].weight|" th:value="${goods.weight}" class="form-control " type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);computeTotalGoods();computeAmountLate(${entStat.index})|" maxlength="10" required>
                                    <span class="input-group-addon ">吨</span>
                                </div>

                                <div style="display: inherit;margin-top: 5px">
                                    <input th:id="|myVolume${entStat.index}${stat.index}|" th:name="|cargoGoodsVOList[${entStat.index}].multipleGoodsConnList[${stat.index}].volume|" th:value="${goods.volume}" class="form-control " type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);computeTotalGoods();computeAmountLate(${entStat.index})|" maxlength="10"  required>
                                    <span class="input-group-addon">m³</span>
                                </div>
                            </div>
                        </td>
                        <th:block th:if="${ifHide == 0}">
                            <td th:if="${stat.index} == 0" th:rowspan="${stat.size}">
                                <div style="display: block;">
                                    <span th:if="${item.pricingMethod} == 1">重量(元/吨)</span>
                                    <span th:if="${item.pricingMethod} == 2">体积(元/立方米)</span>
                                    <span th:if="${item.pricingMethod} == 3">包车(车型)</span>
                                    <span th:if="${item.pricingMethod} == 5">件(元/件)</span>
                                    <span th:if="${item.pricingMethod} == 7">单价(元/公里)</span>
                                    <span th:if="${item.pricingMethod} == 8">单价（元/重量/公里）</span>
                                    <span th:if="${item.pricingMethod} == 9">单价（元/件/公里）</span>
                                </div>
                                <div style="display: inline-table;" >
                                    <input th:id="|unitPrice${entStat.index}|" th:name="|cargoGoodsVOList[${entStat.index}].unitPrice|"  th:value="${item.unitPrice}" class="form-control  needEnable" type="text" min="0"   maxlength="10"  disabled>
                                    <span class="input-group-addon">元</span>
                                </div>
                                <div style="display: inline-table;margin-top: 5px" th:if="${item.pricingMethod == 8 || item.pricingMethod == 9} ">
                                    <input th:id="|mileage${entStat.index}|"   th:value="${item.invoice.mileage}" class="form-control  needEnable" type="text" min="0"   maxlength="10"  disabled>
                                    <span class="input-group-addon">公里</span>
                                </div>
                            </td>
                        </th:block>
                        <input th:id="|billingType${entStat.index}|"   th:value="${item.billingType}" type="hidden" >
                        <input th:id="|billingRate${entStat.index}|"   th:value="${item.billingRate}" type="hidden" >

                        <th:block th:if="${ifHide == 1}">
                            <input th:id="|unitPrice${entStat.index}|" th:name="|cargoGoodsVOList[${entStat.index}].unitPrice|"  th:value="${item.unitPrice}" class="form-control  needEnable" type="hidden" min="0"   maxlength="10"  disabled>
                            <input th:id="|mileage${entStat.index}|"   th:value="${item.invoice.mileage}" type="hidden">
                        </th:block>

                        <th:block th:if="${ifHide == 0}">
                            <td th:if="${stat.index} == 0" th:rowspan="${stat.size}">
                                <div style="display:inline-table;">
                                    <input th:id="|frightAmountOld${entStat.index}|" th:name="|cargoGoodsVOList[${entStat.index}].frightAmountOld|" class="form-control  needEnable" type="hidden" min="0"  >
                                    <input th:id="|frightAmount${entStat.index}|" th:name="|cargoGoodsVOList[${entStat.index}].frightAmount|"  class="form-control  needEnable" type="text" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);changeEnt(this,'num')|" maxlength="10"  disabled>
                                    <span class="input-group-addon ">元</span>
                                </div>
                            </td>
                        </th:block>
                        <th:block th:if="${ifHide == 1}">
                            <input th:id="|frightAmountOld${entStat.index}|" th:name="|cargoGoodsVOList[${entStat.index}].frightAmountOld|"  class="form-control  needEnable" type="hidden" min="0"  >
                            <input th:id="|frightAmount${entStat.index}|" th:name="|cargoGoodsVOList[${entStat.index}].frightAmount|"  class="form-control  needEnable" type="hidden" min="0"  th:oninput="|$.numberUtil.onlyNumber(this);changeEnt(this,'num')|" maxlength="10"  disabled>

                        </th:block>
                        <td th:if="${stat.index} == 0" th:rowspan="${stat.size}" th:id="|onWayAmount${entStat.index}|"> ¥[[${item.onWayAmount}]]
                            <span th:if="${item.onWayAmount != null && item.onWayAmount != 0}"><br/><a th:onclick="jumpReceiveDetail([[${item.invoiceVbillno}]])">明细</a></span>
                            <div style="line-height: 0px"  th:if="${item.waitCheckReceive != 0}">
                                <br>
                                <span class="btn-xs btn-warning" >审核中 ¥[[${item.waitCheckReceive}]]</span>
                            </div>
                        </td>
                        <th:block th:if="${ifHide == 0}">
                            <td th:if="${stat.index} == 0" th:rowspan="${stat.size}" th:id="|totalAmount${entStat.index}|" > </td>
                        </th:block>
                    </tr>
                    <tr th:id="|trDiv${entStat.index}|"  style="display: none">
                        <td>操作记录</td>
                        <td>
                            <div style="display: block;">调度：[[${item.regUserName}]]</div>
                            <div style="display: block;">[[${#dates.format(item.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                        </td>
                        <td colspan="4">
                            <div style="display: block;">提货：[[${item.pickEntrustWork.regUserName}]]/[[${#dates.format(item.pickEntrustWork.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                            <div style="display: block;">[[${item.deliProName+item.deliCityName+item.deliAreaName+item.deliAddrName}]]</div>
                            <div class="form-group fl picviewer">
                                <label class="tr">提货照片：</label>
                                <span th:each="file:${item.pickPickList}" th:if="${file.filePath!=null}">
                                    <img class="th_img" th:src="@{${file.filePath}}"/>
                                </span>
                            </div>
                        </td>
                        <td colspan="2">
                            <div style="display: block;">到货：[[${item.arriEntrustWork.regUserName}]]/[[${#dates.format(item.arriEntrustWork.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                            <div style="display: block;">[[${item.arriProName+item.arriCityName+item.arriAreaName+item.arriAddrName}]]</div>
                            <div class="form-group fl picviewer">
                                <label class="tr">到货照片：</label>
                                <span th:each="file:${item.arriPickList}" th:if="${file.filePath!=null}">
                                    <img class="th_img" th:src="@{${file.filePath}}"/>
                                </span>
                            </div>
                        </td>
                        <td colspan="4">
                            <div style="display: block;">回单确认：[[${item.receiptConfirmUser}]]</div>
                            <div style="display: block;">[[${#dates.format(item.receiptConfirmTime, 'yyyy-MM-dd HH:mm:ss')}]]</div>
                        </td>
                    </tr>
                </div>

                </tbody>
            </table>
        </div>

        <div class="mt10" style="display: flex;justify-content: space-between">
            <div class="">
                <span class="label label-info">承运信息</span>
            </div>

            <div style="font-weight: bolder;">
                总货量：
                <span id="lotNum_old">[[${entrustLot.numCnt}]]</span>件|
                <span id="lotWeight_old">[[${entrustLot.weightCnt}]]</span>吨|
                <span id="lotVolume_old">[[${entrustLot.volumeCnt}]]</span>m³
                →
                <span id="lotNum"></span>|
                <span id="lotWeight"></span>|
                <span id="lotVolume"></span>
                &nbsp;&nbsp;&nbsp;&nbsp;
                总应付：
                ¥<span id="lotAmount_old"></span>
                →
                <span id="lotAmount"></span>
            </div>
        </div>

        <div class="fixed-table-body tl" style="margin: 10px 0 0;">
            <table  class="custom-tab table table-bordered">
                <thead style="background: #f4f6f7;">
                <tr>
                    <th style="width: 8%;">承运信息</th>
                    <th style="width: 8%;">司机信息</th>
                    <th style="width: 8%;">车辆信息</th>
                    <th style="width: 6%;">计价方式/单价</th>
                    <th style="width: 20%;">应付货量</th>
                    <th style="width: 9%;">应付运费</th>
                    <th style="width: 5%;">尾差扣减</th>
                    <th style="width: 5%;">其他费用</th>
                    <th style="width: 6%;">总应付</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>
                        <div style="display: block;">[[${carrier.carrName}]]
                            <span class="label label-success pa2" th:if="${carrier.balaType == 1}">单</span>
                            <span class="label label-warning pa2" th:if="${carrier.balaType == 2}">月</span>

                        </div>
                        <div style="display: block;">[[${carrier.contact}]]/[[${carrier.phone}]]</div>
                        <div style="display: block;">[[${entrustLot.lot}]]</div>
                    </td>
                    <td>
                        <div style="display: block;">[[${entrustLot.driverName}]]</div>
                        <div style="display: block;">[[${entrustLot.driverMobile}]]</div>
                    </td>
                    <td>
                        <div style="display: block;">[[${car.carno}]]</div>
                        <div style="display: block;">[[${car.carLenName}]][[${car.carTypeName}]]</div>
                    </td>
                    <td>
                        <span th:if="${entrustLot.pricingMethod} == 0">按吨</span>
                        <span th:if="${entrustLot.pricingMethod} == 1">按方</span>
                        <span th:if="${entrustLot.pricingMethod} == 2">按件</span>
                        <span th:if="${entrustLot.pricingMethod} == 3">按票</span>
                        <span th:if="${entrustLot.pricingMethod} == 4">按吨(包车)</span>
                        <span th:if="${entrustLot.pricingMethod} == 5">按方(包车)</span>
                        <span th:if="${entrustLot.pricingMethod} == 6">按件(包车)</span>
                        <div th:if="${entrustLot.pricingMethod == 0 || entrustLot.pricingMethod == 1 || entrustLot.pricingMethod == 2}" style="display: block;" id="unitPriceDiv">¥[[${entrustLot.unitPrice}]]</div>
                    </td>
                    <td>
                        <div style="display:inline-table;">
                            <input id="lotId" name="lotId" type="hidden" th:value="${entrustLot.entrustLotId}">
                            <input id="deliArea" type="hidden" th:value="${entrustLot.deliArea}">
                            <input id="arriArea" type="hidden" th:value="${entrustLot.arriArea}">
                            <input id="carrierId" type="hidden" th:value="${entrustLot.carrierId}">
                            <input id="pricingMethod" type="hidden" th:value="${entrustLot.pricingMethod}">
                            <input id="unitPrice" name="unitPrice" type="hidden" th:value="${entrustLot.unitPrice}">
                            <input id="transType" type="hidden" th:value="${entrustLot.transType}">
                            <input id="autoDisVersion" type="hidden" th:value="${entrustLot.autoDisVersion}">
                            <input id="numCount" name="numCount"  th:value="${entrustLot.numCnt}" class="form-control needEnable" type="text" min="0" th:disabled="${entrustLot.spStatus == 1 || entrustLot.spStatus == 3}"  th:oninput="|$.numberUtil.onlyNumber(this);getLotPayAmount()|" maxlength="10" required>
                            <span class="input-group-addon ">件</span>
                            <input id="weightCount" name="weightCount" th:value="${entrustLot.weightCnt}" class="form-control needEnable" type="text" min="0" th:disabled="${entrustLot.spStatus == 1 || entrustLot.spStatus == 3}"  th:oninput="|$.numberUtil.onlyNumber(this);getLotPayAmount()|" maxlength="10" style="margin-left: 5px" required>
                            <span class="input-group-addon ">吨</span>
                            <input id="volumeCount" name="volumeCount" th:value="${entrustLot.volumeCnt}" class="form-control needEnable" type="text" min="0" th:disabled="${entrustLot.spStatus == 1 || entrustLot.spStatus == 3}" th:oninput="|$.numberUtil.onlyNumber(this);getLotPayAmount()|" maxlength="10" style="margin-left: 5px" required>
                            <span class="input-group-addon ">m³</span>
                        </div>
                    </td>
                    <td>
                        <div style="display:inline-table;">
                            <input id="frightPayAmount" name="frightPayAmount" th:value="${frightPayAmount}" class="form-control  needEnable" type="text" min="0" th:oninput="|$.numberUtil.onlyNumber(this);changeFrightPayAmount()|" maxlength="10" disabled required>
                            <span class="input-group-addon">元</span>
                        </div>
                    </td>
                    <td>
                        <div style="display:inline-table;">
                            <input id="payDeduction" name="payDeduction"  class="form-control "  type="number" min="0" step="0.01" max="150"  oninput="changePayDeduction(this)" th:disabled="${entrustLot.pricingMethod != 0 && entrustLot.pricingMethod != 1 && entrustLot.pricingMethod != 2}">
                            <span class="input-group-addon">元</span>
                        </div>
                    </td>
                    <td>在途：¥[[${onWayPayAmount}]]
                        <div style="line-height:0px"  th:if="${waitCheckPay != 0}">
                            <br>
                            <span class="btn-xs btn-warning" >审核中 ¥[[${waitCheckPay}]]</span>
                        </div>
                        </br>
                        三方：<span id="otherFeeAmountSpan">¥[[${otherFeeAmount}]]</span>
                        <div style="line-height:0px" th:if="${waitCheckFee != 0}">
                            <br>
                            <span class="btn-xs btn-warning" >审核中 ¥[[${waitCheckFee}]]</span>
                        </div>
                    </td>
                    <input type="hidden" id="otherFeeAmount" name="otherFeeAmount" th:value="${otherFeeAmount}">
                    <input type="hidden" id="otherFeeAmountNew" name="otherFeeAmountNew" th:value="${otherFeeAmount}">
                    <td id="lotTotalAmount">¥[[${otherFeeAmount+onWayPayAmount+frightPayAmount}]]</td>
                </tr>

                </tbody>
            </table>
        </div>


    </form>
    <div class="btn-group-sm" id="toolbar" role="group">
        <a class="btn btn-primary" onclick="register()" shiro:hasAnyPermissions="tms:trace:register,tms:fleet:trace:register">
            <i class="fa fa-jpy"></i> 费用登记
        </a>
        <a class="btn btn-warning" onclick="affirm()" shiro:hasAnyPermissions="tms:trace:register">
            <i class="fa fa-mail-reply"></i> 确认
        </a>
        <a class="btn btn-primary single disabled" onclick="applyPay()" shiro:hasAnyPermissions="trace:confirmPayment:apply,fleet:trace:confirmPayment:apply" th:if="${carrier.balaType == 1}">
            <i class="fa fa-calculator"></i> 申请付款
        </a>
        <a class="btn btn-primary" onclick="checking()" shiro:hasAnyPermissions="tms:trace:register" th:if="${carrier.balaType == 1}">
            <i class="fa fa-file-text-o"></i> 生成对账单
        </a>
        <a class="btn btn-primary" onclick="insertChecking()" shiro:hasAnyPermissions="tms:trace:register"  th:if="${carrier.balaType == 1}">
            <i class="fa fa-file-text-o"></i> 加入对账单
        </a>
    </div>
    <div class="col-sm-12 select-table table-striped">
        <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
    </div>
    <div class="fixedfooter">
        <div class="">
            <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()" id="submitBtn" th:if="${ifView == 0}"
                    shiro:hasPermission="tms:trace:pick:add"><i class="fa fa-check"></i>保
                存
            </button>&nbsp;
            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
            </button>
        </div>
    </div>
</div>
<script type="text/template" id="rowTmplLot">
    <tr name="regRowsLot">
        <td><a class="close-link del-alink" onclick="removeRowLot(this)" title="删除行">-</a></td>
        <td>
            <select name="costTypeFreight" class="form-control valid" aria-invalid="false" th:with="type=${@dict.getType('cost_type_freight')}" required>
                <option value="">-- 请选择 --</option>
                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
            </select>
        </td>
        <td><input name ="moneyFreight" placeholder="" class="form-control" oninput="$.numberUtil.onlyNumberNegative(this)" type="text" required></td>
        <td><input name="oilFreight" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off"></td>
        <td><input name="memoFreight" placeholder="" class="form-control" type="text" maxlength="100" autocomplete="off"></td>
    </tr>
</script>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var entPackGoodsList = [[${entPackGoodsList}]];
    // 承运商协议价
    var carrierProtocols = [[${carrierProtocols}]];
    // 委托单
    var entrust = [[${entrust}]];
    var lot = [[${entrustLot.lot}]];
    var prefix = ctx + "payDetail";

    //获取是否车队数据
    var isFleetData = [[${isFleetData}]];
    //获取是否分配给车队数据
    var isFleetAssign = [[${isFleetAssign}]]

    //能否更新金额
    var changeAmount = [[${changeAmount}]]

    var settlementCheck = [[${invoice.settlementCheck}]]

    //委托单列表
    var entrusts = [[${entrusts}]];
    var payDetailStatusMap = [[${payDetailStatusMap}]];
    var deliAreaList = [[${deliAreaList}]];
    var arriAreaList = [[${arriAreaList}]];
    var arriAddrName = [[${arriAddrName}]];

    let onWayPayAmount = [[${onWayPayAmount}]];
    let otherFeeAmount = [[${otherFeeAmount}]];
    let payDetailStatusEnum = [[${payDetailStatusEnum}]];
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var balatype = [[${@dict.getType('bala_type')}]];

    $(function () {
        for(let i = 0 ; i < entrusts.length ; i++) {
            let multipleGoodsConns = entrusts[i].multipleGoodsConns;
            for(let j = 0 ; j < multipleGoodsConns.length ; j++){
                $("#adjustHistroy"+i+multipleGoodsConns[j].invGoodsFromId+multipleGoodsConns[j].invGoodsToId).attr('title', getAdjustTip(i,multipleGoodsConns[j].invGoodsFromId,multipleGoodsConns[j].invGoodsToId));
            }

            //获取合同价
            computeAmount(i);
        }


        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');


        var options = {
            url: ctx + "payDetail/list",
            showToggle: false,
            showColumns: true,
            modalName: "应付明细",
            fixedColumns: true,
            rememberSelected: false,
            fixedNumber: 0,
            clickToSelect:true,
            showFooter:false,
            columns: [{
                checkbox: true
            },
               /* {
                    title: '操作',
                    align: 'left',
                    field: 'payDetailId',
                    formatter: function (value, row, index) {

                    }
                },*/

                {
                    title: '应付单号/应付单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function (row,value) {
                        var context = '';
                        payDetailStatusEnum.forEach(function (v) {
                            if (v.value == value.vbillstatus) {
                                if (value.vbillstatus == payDetailStatusMap.NEW) {
                                    //新建
                                    context = '<span class="label label-primary">'+v.context+'</span>';
                                }else if (value.vbillstatus == payDetailStatusMap.AFFIRM) {
                                    //已确认
                                    context = '<span class="label label-warning">'+v.context+'</span>';
                                } else if(value.vbillstatus == payDetailStatusMap.HAS_BEEN_WRITTEN_OFF){
                                    //已核销
                                    context = '<span class="label label-success">' + v.context + '</span>';
                                } else if(value.vbillstatus == payDetailStatusMap.APPLY){
                                    //已申请
                                    context = '<span class="label label-success">' + v.context + '</span>';
                                }else{
                                    context = '<span class="label label-info">' + v.context + '</span>';
                                }
                                if (value.errorFlag == 1) {
                                    context = context + ' <i class="fa fa-question-circle" data-toggle="tooltip" data-container="body" style="font-size: 15px;color:#ff6161" data-html="true" title="'+value.errorMsg+'"></i>'
                                }
                                return false;
                            }
                        });
                        //return context;
                        var result = value.vbillno;
                        result += ' <span class="label label-primary" style="padding:1px;">'+value.balaCorp +'</span>'

                        if (value.lotG7End == 2) {
                            result += ' <span class="label label-success" style="padding:1px;">G7</span>'
                        } else if (value.lotG7Syn != null) {
                            result += ' <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-container="body" data-placement="left" data-html="true" title="'
                            if (value.lotG7Syn == 0) {
                                result += '等待G7审验'
                            } else if (value.lotG7Syn == 1) {
                                result += value.lotG7Msg
                            } else if (value.lotG7Syn == 2) {
                                if (value.lotG7Start == null || value.lotG7Start == 0) {
                                    result += '等待推送【发车】'
                                } else if (value.lotG7Start == 1) {
                                    result += '【发车】推送失败'
                                } else if (value.lotG7End == null || value.lotG7End == 0) {
                                    result += '等待推送【到达】'
                                } else if (value.lotG7End == 1) {
                                    result += '【到达】推送失败'
                                }
                            } else if (value.lotG7Syn == 7) {
                                result += '运单已作废'
                            }
                            result += '">G7</span>'
                        } else if (value.payWay == 'Y') {
                            result += ' <span class="label label-warning" title="预支" style="padding:1px 5px;">Y</span>'
                        }
                        return result + '<br />' + context
                    }
                },
                {
                    title: '运费类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let a = ''
                        if (row.freeType === '0'){
                            a = $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                        }else if (row.freeType === '2')  {
                            a = '调整费'
                        }else{
                            a = $.table.selectDictLabel(costTypeOnWay, row.costTypeOnWay);
                        }

                        switch(value - 0) {
                            case 0:
                                return '<span>运费</label>' + '<br />' + a;
                            case 1:
                                return '<span>在途费用</label>'+ '<br />' + a;
                            case 2:
                                return '<span>调整费用</label>'+ '<br />' + a;
                            default:
                                break;
                        }
                    }

                },

                {
                    title: '总金额/调整额',
                    align: 'right',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        let a = ''
                        if (value === null) {
                            a = '-'
                        }
                        a = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        let b = ''
                        if (row.taxAmount === null) {
                            b = '-'
                        }
                        b = row.taxAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        return a + '<br />' + b

                    }

                },

                {
                    title: '已付/未付',
                    align: 'right',
                    field: 'gotAmount',
                    formatter: function (value, row, index) {
                        let a = ''
                        if (value === null) {
                            a = '-';
                        }
                        a = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        let b =''
                        if (row.ungotAmount === null) {
                            b = '-' ;
                        }
                        b = row.ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        return a + '<br />' + b
                    }
                },


                {
                    title: '油卡卡号',
                    align: 'left',
                    field: 'oilCardNumber'
                },
                {
                    title: '收款人',
                    align: 'left',
                    field: 'recAccount'
                }
                ,
                {
                    title: '收款银行/收款卡号',
                    align: 'left',
                    field: 'recBank',
                    formatter: function (value, row, index){
                        return getValue(value) + '<br />' + getValue(row.recCardNo)
                    }
                },


                {
                    title: '创建人/创建时间',
                    align: 'left',
                    field: 'regUserId',
                    formatter: function (value, row, index) {
                        return value + '<br />' + row.regDate
                    }
                },

                {
                    title: '申请人/申请时间',
                    align: 'left',
                    field: 'applyUser',
                    formatter: function (value, row, index){
                        return getValue(value) + '<br />' + getValue(row.applyTime);
                    }
                },

                {
                    title: '复核人/复核时间',
                    align: 'left',
                    field: 'checkUserName',
                    formatter: function (value, row, index){
                        return getValue(value) + '<br />' + getValue(row.checkDate)
                    }
                },


                {
                    title: '付款记录',
                    field: '',
                    align: 'left'
                },

            ]
        };

        $.table.init(options);



        /**
         * 没有协议价且选择计费方式  展示运费
         */
        if(carrierProtocols.length == 0 && $.common.isNotEmpty(entrust.pricingMethod)){
            $("#shippingIsShow").show();
            $("#shippingFlag").val(1);
        }else{
            $("#shippingIsShow").hide();
            $("#shippingFlag").val(0);
        }

        $.each(entPackGoodsList, function (i, item) {
            var id = item.invPackGoodsId + "_";

            //运单货品
            var numEnt = item.num;
            var weightEnt = item.weight;
            var volumeEnt = item.volume;

            //发货单件数
            var numInv = $("#" + id + "num").val() == "" ? 0 : $("#" + id + "num").val();
            //发货单重量
            var weightInv = $("#" + id + "weight").val() == "" ? 0 : $("#" + id + "weight").val();
            //发货单体积
            var volumeInv = $("#" + id + "volume").val() == "" ? 0 : $("#" + id + "volume").val();

            //用发货单的值 减去 运单的值 作为基础数据
            $("#" + id + "num_base").val(decimal(numInv - numEnt, 5));
            $("#" + id + "weight_base").val(decimal(weightInv - weightEnt, 5));
            $("#" + id + "volume_base").val(decimal(volumeInv - volumeEnt, 5));
        });

        computeTotalGoodsOld();
        computeTotalGoods();

        let totalAmount = 0;
        $("td[id^='totalAmount']").each(function () {
            totalAmount += Number($(this).html().replace('¥',''));
        });
        $("#totalSpan_Old").html(handleCutZero(totalAmount.toFixed(2)));
        $("#totalSpan").html('¥'+handleCutZero(totalAmount.toFixed(2)));

        let numCount = $("#numCount").val();
        let weightCount = $("#weightCount").val();
        let volumeCount = $("#volumeCount").val();
        $("#lotNum").html(`<span>`+numCount+`件</span>`);
        $("#lotWeight").html(`<span>`+weightCount+`吨</span>`);
        $("#lotVolume").html(`<span>`+volumeCount+`m³</span>`);

        let lotTotalAmount = $("#lotTotalAmount").html();
        $("#lotAmount_old").html(lotTotalAmount.replace('¥',''));
        $("#lotAmount").html(lotTotalAmount);


    });

    function getAdjustTip(index,invGoodsFromId1,invGoodsToId1){
        let entrust = entrusts[index];
        let cargoEditHistories = entrust.cargoEditHistories;
        let html=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'>`;

        if(cargoEditHistories.length > 0){
            html+=`<table class='custom-tab tab table'> <thead style='background: #f4f6f7;'><tr>
                    <th>调整时间</th>
                    <th>发货单调整</th>
                    <th>调整信息</th>
                    <th>应收单价</th>
                    <th>调整人</th>
                    <th>类型</th>
                </tr></thead><tbody> `;
            cargoEditHistories.forEach(res=>{
                if(res.invGoodsFromId == invGoodsFromId1 && res.invGoodsToId == invGoodsToId1){
                    if(res.isAdjust == 0){
                        res.isAdjust = '货量更新'
                    }else{
                        res.isAdjust = '调账'
                    }
                    let invInfo = '';
                    let nvmInfo = '';

                    if(res.numDistance != 0){
                        if(res.numDistance > 0){
                            nvmInfo += '件数(+'+res.numDistance +')</br>';
                        }else{
                            nvmInfo += '件数('+res.numDistance +')</br>';
                        }
                        invInfo += '件数('+res.numBeforeDJ+'→'+res.numAfterDJ+')</br>';
                    }
                    if(res.weightDistance != 0){
                        if(res.weightDistance > 0){
                            nvmInfo += '重量(+'+res.weightDistance +')</br>';
                        }else{
                            nvmInfo += '重量('+res.weightDistance +')</br>';
                        }
                        invInfo += '重量('+res.weightBeforeDJ+'→'+res.weightAfterDJ+')</br>';
                    }
                    if(res.volumeDistance != 0){
                        if(res.volumeDistance > 0){
                            nvmInfo += '体积(+'+res.volumeDistance +')</br>';
                        }else{
                            nvmInfo += '体积('+res.volumeDistance +')</br>';
                        }
                        invInfo += '体积('+res.volumeBeforeDJ+'→'+res.volumeAfterDJ+')</br>';
                    }

                html+=`<tr>
                            <td>`+res.regDate.slice(0,10)+`</td>
                            <td>`+invInfo+`</td>
                            <td>`+nvmInfo+`</td>
                            <td>`+res.unitPrice+`</td>

                            <td>`+res.regUserName+`</td>
                            <td>`+res.isAdjust+`</td>
                        </tr>`
                }
            })
            html+=`</tbody></table>`;

        }else{
            html+=`暂无数据`;
        }
        html+=`</div></div></div>`;
        return html
    }

    function computeTotalGoods(){
        var totalNum = 0;
        var totalWeight = 0;
        var totalVolume = 0;
        $("input[id^='myNum']").each(function () {
            totalNum += Number($(this).val());
        })
        $("input[id^='myWeight']").each(function () {
            totalWeight += Number($(this).val());
        })
        $("input[id^='myVolume']").each(function () {
            totalVolume += Number($(this).val());
        })

        let myNumSpan_old = Number($("#myNumSpan_old").html());
        let myWeightSpan_old = Number($("#myWeightSpan_old").html());
        let myVolumeSpan_old = Number($("#myVolumeSpan_old").html());

        totalNum = Number(handleCutZero(totalNum.toFixed(4)));
        totalWeight = Number(handleCutZero(totalWeight.toFixed(4)));
        totalVolume = Number(handleCutZero(totalVolume.toFixed(4)));

        if(myNumSpan_old != totalNum){
            $("#myNumSpan").html(`<span style="color: #61ae96">`+totalNum+`件</span>`);
        }else{
            $("#myNumSpan").html(totalNum+'件');
        }

        if(myWeightSpan_old != totalWeight){
            $("#myWeightSpan").html(`<span style="color: #61ae96">`+totalWeight+`吨</span>`);
        }else{
            $("#myWeightSpan").html(totalWeight+'吨');
        }

        if(myVolumeSpan_old != totalVolume){
            $("#myVolumeSpan").html(`<span style="color: #61ae96">`+totalVolume+`m³</span>`);
        }else{
            $("#myVolumeSpan").html(totalVolume+'m³');
        }

        let handlingChargesType = [[${handlingChargesType}]];
        let handlingCharges = [[${handlingCharges}]];

        //修改三方费用
        if(handlingChargesType != "" && handlingCharges != ""){
            let otherFeeAmount = $("#otherFeeAmount").val();
            let dsf = $("#otherFeeAmountNew").val();
            //0按件 1按方 2按吨
            if(handlingChargesType == '0'){
                dsf = (Number(handlingCharges)*Number(totalNum)).toFixed(2);
            }else if(handlingChargesType == '1'){
                dsf = (Number(handlingCharges)*Number(totalWeight)).toFixed(2);
            }else if(handlingChargesType == '2'){
                dsf = (Number(handlingCharges)*Number(totalVolume)).toFixed(2);
            }
            $("#otherFeeAmountNew").val(dsf);
            if(Number(otherFeeAmount) > dsf){
                $("#otherFeeAmountSpan").html(`<span style="color: #61ae96">¥`+dsf+`</span>`);
            }else if(Number(otherFeeAmount) < dsf){
                $("#otherFeeAmountSpan").html(`<span style="color: #e69a47">¥`+dsf+`</span>`);
            }else{
                $("#otherFeeAmountSpan").html('¥'+dsf);
            }
            changeFrightPayAmount();
        }

    }

    function computeTotalGoodsOld(){
        var totalNum = 0;
        var totalWeight = 0;
        var totalVolume = 0;
        $("input[id^='myNum']").each(function () {
            totalNum += Number($(this).val());
        })
        $("input[id^='myWeight']").each(function () {
            totalWeight += Number($(this).val());
        })
        $("input[id^='myVolume']").each(function () {
            totalVolume += Number($(this).val());
        })
        $("#myNumSpan_old").html(handleCutZero(totalNum.toFixed(4)));
        $("#myWeightSpan_old").html(handleCutZero(totalWeight.toFixed(4)));
        $("#myVolumeSpan_old").html(handleCutZero(totalVolume.toFixed(4)));



    }

    function changePayDeduction(ref) {
        let val = $(ref).val();
        if(val < 0){
            $(ref).val(0);
        }else if(val > 100){
            $(ref).val(100);
        }
        changeFrightPayAmount();
    }

    function handleCutZero(num) {
        //拷贝一份 返回去掉零的新串
        let newstr = num;
        //循环变量 小数部分长度
        let leng = num.length - num.indexOf('.') - 1;
        //判断是否有效数
        if (num.indexOf('.') > -1) {
            //循环小数部分
            for (let i = leng; i > 0; i--) {
                //如果newstr末尾有0
                if (
                    newstr.lastIndexOf('0') > -1 &&
                    newstr.substr(newstr.length - 1, 1) == 0
                ) {
                    let k = newstr.lastIndexOf('0');
                    //如果小数点后只有一个0 去掉小数点
                    if (newstr.charAt(k - 1) == '.') {
                        return newstr.substring(0, k - 1);
                    } else {
                        //否则 去掉一个0
                        newstr = newstr.substring(0, k);
                    }
                } else {
                    //如果末尾没有0
                    return newstr;
                }
            }
        }
        return num;
    }

    let timer = null;
    let jobFlag = false;
    function computeAmountLate(index){
        jobFlag = true;
        clearTimeout(timer); // 清除上一个定时器
        timer = setTimeout(function () {
            computeAmount(index); // 这里是我们的请求之类的操作，我这里做个打印，方便看结果
        }, 300);
    }

    function computeAmount(index){
        //计算应收合同价

        var ent = entrusts[index];

        //0合同价 1议价
        let ifBargain= ent.ifBargain;

        //原本件数重量体积
        let numCount = ent.numCount;
        let weightCount = ent.weightCount;
        let volumeCount = ent.volumeCount;

        let onWayAmount = ent.onWayAmount;

        let inv = ent.invoice;

        var param = {};

        let invoiceNum = $("#invoiceNum"+index).val();
        let invoiceWeight = $("#invoiceWeight"+index).val();
        let invoiceVolume = $("#invoiceVolume"+index).val();

        let invoiceTransCode = $("#invoiceTransCode"+index).val();

        //货品名称
        let goodsName = ''
        for(let i = 0 ; i < ent.multipleGoodsConns.length ; i++) {
            if(i == 0){
                goodsName = ent.multipleGoodsConns[i].goodsName;
            }else{
                if(goodsName != ent.multipleGoodsConns[i].goodsName){
                    goodsName = '';
                    break;
                }
            }
        }
        param.versionId = inv.contractpcVersionId

        param.goodsName = goodsName;

        //客户id
        param.customerId = $("#invoiceCustomerId"+index).val();
        param.isOversize = inv.isOversize;
        param.deliAreaIdList = ent.deliAreaIdList;
        param.arriAreaIdList = ent.arriAreaIdList;

        param.deliAddrNameList = ent.deliAddrNameList
        param.arriAddrNameList = ent.arriAddrNameList

        /*    if(inv.isChangeAddress == 1){
                param.arriAreaIdList = [inv.caArriAreaId];
            }*/

        //车长
        param.carLen = $("#invoiceCarLen"+index).val();
        param.carType = $("#invoiceCarType"+index).val();
        //公里数
        param.mileage = $("#mileage"+index).val();

        //计价方式
        param.billingMethod = inv.billingMethod;
        let transCode = invoiceTransCode;
        //货品特性   0普通 1危化
        param.goodsCharacter = transCode == '15' || transCode == '16' ? 1 : 0;

        //运输方式
        param.transCode = transCode;
        //是否往返
        param.isRoundTrip = inv.isRoundTrip

        if (param.billingMethod === undefined || param.billingMethod ==null || param.billingMethod =='') {
            if($("#frightAmountOld"+index).val() == null || $("#frightAmountOld"+index).val() == ''){
                $("#frightAmountOld"+index).val(ent.frightAmount);
                $("#frightAmount"+index).val(ent.frightAmount);
            }
            jobFlag = false;
            return;
        }

        var pricingMethod = ent.pricingMethod;
        //计算合同价
        var unitPrice = ent.unitPrice;

        let mileage = $("#mileage"+index).val();

        let tempWeight = 0;
        let tempVolume = 0;
        let tempNum = 0;

        let weightDistance = 0;
        let volumeDistance = 0;
        let numDistance = 0;



        //计价方式件方吨 才修改合同价
        if(pricingMethod == '1' || pricingMethod == '8'){
            $("input[id^='myWeight"+index+"']").each(function () {
                tempWeight += Number($(this).val());
            })
            //重量
            param.weight = (Number(invoiceWeight)+Number(tempWeight)-Number(weightCount)).toFixed(5);
            invoiceWeight = param.weight;
            weightDistance = (Number(tempWeight)-Number(weightCount)).toFixed(5);
        }else if(pricingMethod == '2' ){
            $("input[id^='myVolume"+index+"']").each(function () {
                tempVolume += Number($(this).val());
            })
            //件数
            param.volume = (Number(invoiceVolume) + Number(tempVolume) - Number(volumeCount)).toFixed(5);
            invoiceVolume =param.volume;
            tempNum = (Number(tempVolume) - Number(volumeCount)).toFixed(5);
        }else if(pricingMethod == '5' || pricingMethod == '9'){
            $("input[id^='myNum"+index+"']").each(function () {
                tempNum += Number($(this).val());
            })
            //件数
            param.num = (Number(invoiceNum) + Number(tempNum) - Number(numCount)).toFixed(5);
            invoiceNum = param.num;
            numDistance = (Number(tempNum) - Number(numCount)).toFixed(5);
        }else{
            if($("#frightAmountOld"+index).val() == null || $("#frightAmountOld"+index).val() == ''){
                $("#frightAmountOld"+index).val(ent.frightAmount);
                $("#frightAmount"+index).val(ent.frightAmount);
            }
            jobFlag = false;
            return;
        }

        console.log("weightDistance:"+weightDistance);
        console.log("volumeDistance:"+volumeDistance);
        console.log("numDistance:"+numDistance);

        console.log("ent.invCostPrice:"+ent.invCostPrice);
        console.log("ent.costUnitPrice:"+ent.costUnitPrice);

        if(ifBargain == 1){
            let invCostPrice = ent.invCostPrice;
            let costUnitPrice = ent.costUnitPrice;
           if(pricingMethod == '1'){
               $("#frightAmount"+index).val(handleCutZero((Number(unitPrice)*tempWeight).toFixed(2)));
               $("#totalAmount"+index).html(handleCutZero((Number(unitPrice)*tempWeight+Number(onWayAmount)).toFixed(2)));
               let costPrice = (invCostPrice + costUnitPrice*weightDistance).toFixed(2);
               $("#contractTotalCostPrice"+index).val(costPrice);
           }else if(pricingMethod == '2' ){
               $("#frightAmount"+index).val(handleCutZero((Number(unitPrice)*tempVolume).toFixed(2)));
               $("#totalAmount"+index).html(handleCutZero((Number(unitPrice)*tempVolume+Number(onWayAmount)).toFixed(2)));
               let costPrice = (invCostPrice + costUnitPrice*volumeDistance).toFixed(2);
               $("#contractTotalCostPrice"+index).val(costPrice);
           }else if(pricingMethod == '5'){
               $("#frightAmount"+index).val(handleCutZero((Number(unitPrice)*tempNum).toFixed(2)));
               $("#totalAmount"+index).html(handleCutZero((Number(unitPrice)*tempNum+Number(onWayAmount)).toFixed(2)));
               let costPrice = (invCostPrice + costUnitPrice*tempNum).toFixed(2);
               $("#contractTotalCostPrice"+index).val(costPrice);
           }else if(pricingMethod == '8'){
               $("#frightAmount"+index).val(handleCutZero((Number(unitPrice)*tempWeight*Number(mileage)).toFixed(2)));
               $("#totalAmount"+index).html(handleCutZero((Number(unitPrice)*tempWeight*Number(mileage)+Number(onWayAmount)).toFixed(2)));
           }else if(pricingMethod == '9'){
               $("#frightAmount"+index).val(handleCutZero((Number(unitPrice)*tempNum*Number(mileage)).toFixed(2)));
               $("#totalAmount"+index).html(handleCutZero((Number(unitPrice)*tempNum*Number(mileage)+Number(onWayAmount)).toFixed(2)));
           }
            computeAmountTotal();
            jobFlag = false;
        }else{
            $.ajax({
                url: ctx + "invoice/getPrice",
                type: "post",
                dataType: "json",
                async: false,
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(param),
                success: function(result) {
                    console.log(param);
                    console.log(result);
                    if (result.code == 0) {
                        if (result.data.type == 0) {
                            $("#unitPrice"+index).val(unitPrice);
                            if(pricingMethod == '1' || pricingMethod == '2' || pricingMethod == '5' ){
                                $("#submitBtn").css("display",'none');
                                $.modal.alertWarning("未查询到对应合同价，无法更新费用，需要完善合同价配置");
                            }
                        }else if (result.data.type == 1) {
                            $("#contractIsFixedPrice"+index).val(result.data.isFixedPrice);
                            $("#contractVersionId"+index).val(result.data.versionId);
                            $("#contractTotalCostPrice"+index).val(result.data.totalCostPrice);
                            if (result.data.isIncludeTax === "1") {
                                //单价
                                $("#unitPrice"+index).val(result.data.price);
                                //总金额sum
                                $("#frightAmount"+index).val(handleCutZero((Number(result.data.totalPrice)).toFixed(2)));
                                $("#totalAmount"+index).html(handleCutZero((Number(result.data.totalPrice)+Number(onWayAmount)).toFixed(2)));

                            }else if (result.data.isIncludeTax === "0") {
                                let price = result.data.price
                                let totalPrice = result.data.totalPrice;

                                let billingType = $("#billingType"+index).val();
                                let billingRate = $("#billingRate"+index).val();

                                let costAmount;
                                let unitPrice;

                                if (billingType != "") {
                                    costAmount = Number(totalPrice) * Number(billingRate)
                                    unitPrice = Number(price) * Number(billingRate)
                                }else {
                                    costAmount = totalPrice
                                    unitPrice = price
                                }

                                // 四舍五入保留两位小数
                                costAmount = parseFloat(costAmount).toFixed(2);
                                unitPrice = parseFloat(unitPrice).toFixed(2);
                                console.log(costAmount)
                                //单价
                                $("#unitPrice"+index).val(unitPrice);
                                //总金额sum
                                $("#frightAmount"+index).val(handleCutZero(costAmount));
                                $("#totalAmount"+index).html(handleCutZero(parseFloat(costAmount+Number(onWayAmount)).toFixed(2)));
                            }
                            $("#submitBtn").css("display",'inline');
                        }
                        computeAmountTotal();
                    }
                    jobFlag = false;
                }
            });
        }
        if($("#frightAmountOld"+index).val() == null || $("#frightAmountOld"+index).val() == ''){
            $("#frightAmountOld"+index).val($("#frightAmount"+index).val());
        }
    }

    function computeAmountTotal() {
        let totalAmount = 0;
        $("td[id^='totalAmount']").each(function () {
            totalAmount += Number($(this).html().replace('¥',''));
        });

        let totalAmount_Old = Number($("#totalSpan_Old").html());
        if(totalAmount_Old > totalAmount){
            $("#totalSpan").html(`<span style="color: #e69a47">¥`+handleCutZero(totalAmount.toFixed(2))+`</span>`);
        }else if(totalAmount_Old < totalAmount){
            $("#totalSpan").html(`<span style="color: #61ae96">¥`+handleCutZero(totalAmount.toFixed(2))+`</span>`);
        }else{
            $("#totalSpan").html('¥'+handleCutZero(totalAmount.toFixed(2)));
        }

    }

    /**
     * 获取合同价
     *
     */
    function getPrice(index) {
        let ent = entrusts[index];
        let inv = ent.invoice;

        var param = {};

        let invoiceNum = $("#invoiceNum"+index).val();
        let invoiceWeight = $("#invoiceWeight"+index).val();
        let invoiceVolume = $("#invoiceVolume"+index).val();
        let invoiceTransCode = $("#invoiceTransCode"+index).val();

        //客户id
        param.customerId = $("#invoiceCustomerId"+index).val();

        param.deliAreaIdList = ent.deliAreaIdList;
        param.arriAreaIdList = ent.arriAreaIdList;

        param.deliAddrNameList = ent.deliAddrNameList
        param.arriAddrNameList = ent.arriAddrNameList

        //车长
        param.carLen = $("#invoiceCarLen"+index).val();
        param.carType = $("#invoiceCarType"+index).val();

        //计价方式
        param.billingMethod = inv.billingMethod;
        param.isOversize = inv.isOversize;

        let transCode = invoiceTransCode;
        //货品特性   0普通 1危化
        param.goodsCharacter = transCode == '15' || transCode == '16' ? 1 : 0;

        //运输方式
        param.transCode = transCode;

        //是否往返
        param.isRoundTrip = inv.isRoundTrip

        //件数
        param.num = invoiceNum;
        //重量
        param.weight = invoiceWeight;
        //件数
        param.volume = invoiceVolume;
        if (param.billingMethod === undefined || param.billingMethod ==null || param.billingMethod =='') {
            return;
        }
        console.log(param);
        $.ajax({
            url: ctx + "invoice/getPrice",
            type: "post",
            dataType: "json",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify(param),
            success: function(result) {
                console.log(result);
                if (result.code == 0) {
                    if (result.data.type == 0) {

                    }else if (result.data.type == 1) {
                        //单价
                        $("#unitPrice"+index).val(result.data.price);
                        //金额sum
                        $("#costAmount"+index).val(result.data.totalPrice);
                    }
                }
            }
        });
    }


    /**
     * 保留小数 四舍五入
     * @param num
     * @param v
     * @returns {number}
     */
    function decimal(num,v) {
        var vv = Math.pow(10,v);
        return Math.round(num*vv)/vv;
    }

    /**
     * 提交
     */
    function submitHandler() {
        if(jobFlag){
            $.modal.alertWarning("应收合同价查询中，请稍等...")
            return;
        }
        if ($.validate.form()) {
            $(".needEnable").attr("disabled", false);
            $.operate.saveTab(ctx + "trustDeed/cargoEditNew",$("#form-goods-edit").serializeArray(),function (result) {
                if (result.code != 0) {
                    $(".needEnable").attr("disabled", true);
                }
            });
        }
    }

    //异常跟踪
    function abnormal(entrustId) {
        var url = ctx + "trace/abnormal/"+entrustId;
        $.modal.openTab("异常跟踪" , url);
    }

    function changeFrightPayAmount(){
        let frightPayAmount = $("#frightPayAmount").val();
        let payDeduction = $("#payDeduction").val();
        let otherFeeAmountNew = $("#otherFeeAmountNew").val()
        let amount = (Number(frightPayAmount)+Number(onWayPayAmount)+Number(otherFeeAmountNew)-Number(payDeduction)).toFixed(2);
        $("#lotTotalAmount").html("¥"+amount);
        let lotAmount_old = Number($("#lotAmount_old").html());
        if(lotAmount_old > amount){
            $("#lotAmount").html(`<span style="color: #61ae96">¥`+amount+`</span>`);
        }else if(lotAmount_old < amount){
            $("#lotAmount").html(`<span style="color: #e69a47">¥`+amount+`</span>`);
        }else{
            $("#lotAmount").html("¥"+amount);
        }
    }

    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }

    //费用登记
    function register() {
        if (entrust == null) {
            $.modal.alertWarning("未查询到委托单信息，无法费用登记！");
            return false;
        }

        var entrustId = entrust.entrustId;
        if(entrust.receiptConfirmFlag == 1){
            var url = ctx + "trace/adjust/"+entrustId;
            layer.open({
                type: 2,
                area: ['1000px', ($(window).height() - 50)+'px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "费用调整",
                content: url,
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });

            return false;
        }

        var url = entrust.isFleetData == '0' ? ctx + "trace/register/"+entrustId+"/0" : ctx + "fleettrace/register/"+entrustId+"/0";
        $.modal.open("费用登记", url);
    }

    /**
     * 确认应付明细
     */
    function affirm() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0) {
                $.modal.alertWarning("应付单据只能为新建状态下才能确认");
                return;
            }
            if (bootstrapTable[i]["lotSpLock"] == 1) {
                $.modal.alertWarning("审批中或驳回的运单不能确认费用");
                return;
            }
            if (bootstrapTable[i]["consumbleBack"] === 0) {
                $.modal.alertWarning("应付单据存在耗材费用!");
                return;
            }
        }
        var payDetailIds = $.table.selectColumns("payDetailId");
        if (payDetailIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }


        $.modal.confirm("是否确认？", function () {
            $.operate.post(ctx + "payDetail/affirm", {"payDetailIds": payDetailIds.join()});
        });
    }

    /**
     * 生成对账单的方法
     */
    function checking() {

        var rows = $.table.selectColumns("payDetailId");

        var balaCorps = $.table.selectColumns("balaCorp");
        if (balaCorps.length > 1) {
            $.modal.alertWarning("请选择相同的结算公司");
            return;
        }

        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var  isNtocc = bootstrapTable[0]["isNtocc"];

        var lotG7End = bootstrapTable[0]["lotG7End"];

        let flag = false;
        let msg = '';

        for (var i = 0; i < bootstrapTable.length; i++) {
            /*if (bootstrapTable[i]["isNtocc"] !== isNtocc ) {
                $.modal.alertWarning("请选择同为符合或不符合无车承运人的应付单");
                return;
            }*/

            if (bootstrapTable[i]["vbillstatus"] !== 1 && bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("生成对账单的应付单据只能为新建或已确认状态");
                return;
            }

            if (bootstrapTable[i]["lotG7End"] !== lotG7End ) {
                $.modal.alertWarning("G7与非G7的应付单无法加入相同对账包");
                return;
            }

            if (bootstrapTable[i]["consumbleBack"] === 0) {
                $.modal.alertWarning("应付单据存在耗材费用!");
                return;
            }

            if (bootstrapTable[i]["freeType"] == 0) {
                if (bootstrapTable[i]["costTypeFreight"] == 2 || bootstrapTable[i]["costTypeFreight"] == 3 || bootstrapTable[i]["costTypeFreight"] == 4 || bootstrapTable[i]["costTypeFreight"] == 5) {
                    //验证运单下委托单是否都确认
                    $.ajax({
                        type: "POST",
                        url: prefix + "/checkEntrustReceiptConfirm?lot="+lot,
                        async: false,
                        success: function(r){
                            if(r.code != 0){
                                msg = '到/回付款必须回单确认之后才可提交';
                                flag = true;
                            }
                        }
                    });
                }
            }

            if (bootstrapTable[i]["freeType"] == 0 && (bootstrapTable[i]["costTypeFreight"] == 4 || bootstrapTable[i]["costTypeFreight"] == 5)) {
                $.ajax({
                    type: "POST",
                    url: prefix + "/checkEntrustIfReceipt?lot="+lot,
                    async: false,
                    success: function(r){
                        if(r.code != 0){
                            msg = '回付款必须正本回单之后才可提交';
                            flag = true;
                        }
                    }
                });
            }


        }

        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        if(flag){
            $.modal.alertWarning(msg);
            return false;
        }

        /*let flag = false
        $.ajax({
            type: "POST",
            url: prefix + "/checkEntrustIfReceipt?lot="+lot,
            async: false,
            success: function(r){
                if(r.code != 0){
                    flag = true;
                }
            }
        });

        if (flag) {
            $.modal.alertWarning("正本回单后才可以生成对账单。");
            return;
        }*/

        //验证是否存在异常记录
        $.ajax({
            type: "POST",
            url: prefix + "/checkEntrustExp?payDetailIds="+rows.join(),
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    $.modal.openTab("生成对账单", prefix + "/checking?payDetailIds=" + rows.join()+"&lotG7End="+lotG7End);
                }
            }
        });
    }


    /**
     * 加入对账单的方法
     */
    function insertChecking() {

        var rows = $.table.selectColumns("payDetailId");
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        var  isNtocc = bootstrapTable[0]["isNtocc"];

        var balaCorps = $.table.selectColumns("balaCorp");
        if (balaCorps.length > 1) {
            $.modal.alertWarning("请选择相同的结算公司");
            return;
        }
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        let flag = false;
        let msg = '';

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 1 && bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("加入对账单的应付单据只能为新建或已确认状态");
                return;
            }

            if (bootstrapTable[i]["consumbleBack"] === 0) {
                $.modal.alertWarning("应付单据存在耗材费用!");
                return;
            }
            if (bootstrapTable[i]["freeType"] == 0) {
                if (bootstrapTable[i]["costTypeFreight"] == 2 || bootstrapTable[i]["costTypeFreight"] == 3 || bootstrapTable[i]["costTypeFreight"] == 4 || bootstrapTable[i]["costTypeFreight"] == 5) {
                    //验证运单下委托单是否都确认
                    $.ajax({
                        type: "POST",
                        url: prefix + "/checkEntrustReceiptConfirm?lot="+lot,
                        async: false,
                        success: function(r){
                            if(r.code != 0){
                                msg = '到/回付款必须回单确认之后才可提交';
                                flag = true;
                            }
                        }
                    });
                }
            }

            if (bootstrapTable[i]["freeType"] == 0 && (bootstrapTable[i]["costTypeFreight"] == 4 || bootstrapTable[i]["costTypeFreight"] == 5)) {
                $.ajax({
                    type: "POST",
                    url: prefix + "/checkEntrustIfReceipt?lot="+lot,
                    async: false,
                    success: function(r){
                        if(r.code != 0){
                            msg = '回付款必须正本回单之后才可提交';
                            flag = true;
                        }
                    }
                });
            }

        }



        if(flag){
            $.modal.alertWarning(msg);
            return false;
        }

        /* let flag = false
         $.ajax({
             type: "POST",
             url: prefix + "/checkEntrustIfReceipt?lot="+lot,
             async: false,
             success: function(r){
                 if(r.code != 0){
                     flag = true;
                 }
             }
         });

         if (flag) {
             $.modal.alertWarning("正本回单后才可以加入对账单。");
             return;
         }
 */
        //验证是否存在异常记录
        $.ajax({
            type: "POST",
            url: prefix + "/checkEntrustExp?payDetailIds="+rows.join(),
            async: false,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertError(r.msg);
                    return false;
                }else{
                    var carrierId =  bootstrapTable[0]["carrierId"];
                    // $.modal.open("加入对账单", prefix + "/insertChecking?carrierId=" + carrierId + "&payDetailIds=" + rows + "&isNtocc=" + isNtocc);

                    layer.open({
                        type: 2,
                        area: ['80%', '90%'],
                        fix: false,
                        maxmin: true,
                        shade: 0.3,
                        title: "加入对账单",
                        content: prefix + "/insertChecking?carrierId=" + carrierId + "&payDetailIds=" + rows + "&isNtocc=" + isNtocc,
                        btn: ['油卡加入', '现金加入', '关闭'],
                        shadeClose: true,            // 弹层外区域关闭
                        btn1: function (index, layero) {
                            var iframeWin = layero.find('iframe')[0];
                            iframeWin.contentWindow.submitHandler(index, layero, 0);
                        },
                        btn2: function (index, layero) {
                            var iframeWin = layero.find('iframe')[0];
                            iframeWin.contentWindow.submitHandler(index, layero, 1);
                            return false;
                        },
                        btn3: function (index, layero) {
                            return true;
                        }
                    });

                }
            }
        });

    }



    function getLotPayAmount(){
        /*let handlingChargesType = [[${handlingChargesType}]];
        let handlingCharges = [[${handlingCharges}]];
*/
        let lotId = $("#lotId").val();
        let deliArea = $("#deliArea").val();
        let arriArea = $("#arriArea").val();
        let carrierId = $("#carrierId").val();
        let numCount = $("#numCount").val();
        let weightCount = $("#weightCount").val();
        let volumeCount = $("#volumeCount").val();
        let pricingMethod = $("#pricingMethod").val();
        //单价
        let unitPrice = $("#unitPrice").val();
        let transType = $("#transType").val();
        let autoDisVersion = $("#autoDisVersion").val();

        let lotNum_old = $("#lotNum_old").html();
        let lotWeight_old = $("#lotWeight_old").html();
        let lotVolume_old = $("#lotVolume_old").html();

        //修改三方费用
        /*if(handlingChargesType != "" && handlingCharges != ""){
            let otherFeeAmount = $("#otherFeeAmount").val();
            let dsf = $("#otherFeeAmountNew").val();
            //0按件 1按方 2按吨
            if(handlingChargesType == '0'){
                 dsf = (Number(handlingCharges)*Number(numCount)).toFixed(2);
            }else if(handlingChargesType == '1'){
                 dsf = Number(handlingCharges)*Number(volumeCount);
            }else if(handlingChargesType == '2'){
                 dsf = Number(handlingCharges)*Number(weightCount);
            }
            $("#otherFeeAmountNew").val(dsf);
            if(Number(otherFeeAmount) > dsf){
                $("#otherFeeAmountSpan").html(`<span style="color: #61ae96">¥`+dsf+`</span>`);
            }else if(Number(otherFeeAmount) < dsf){
                $("#otherFeeAmountSpan").html(`<span style="color: #e69a47">¥`+dsf+`</span>`);
            }else{
                $("#otherFeeAmountSpan").html('¥'+dsf);
            }
            changeFrightPayAmount();
        }*/

        if(Number(numCount) != Number(lotNum_old)){
            $("#lotNum").html(`<span style="color: #61ae96">`+Number(numCount)+`件</span>`);
        }else{
            $("#lotNum").html(`<span>`+numCount+`件</span>`);
        }

        if(Number(weightCount) != Number(lotWeight_old)){
            $("#lotWeight").html(`<span style="color: #61ae96">`+Number(weightCount)+`吨</span>`);
        }else{
            $("#lotWeight").html(`<span>`+weightCount+`吨</span>`);
        }

        if(Number(volumeCount) != Number(lotVolume_old)){
            $("#lotVolume").html(`<span style="color: #61ae96">`+Number(volumeCount)+`m³</span>`);
        }else{
            $("#lotVolume").html(`<span>`+volumeCount+`m³</span>`);
        }

        let data = {};
        data.lotId = lotId;
        data.deliArea = deliArea;
        data.arriArea = arriArea;
        data.carrierId = carrierId;
        data.numCount = numCount;
        data.weightCount = weightCount;
        data.volumeCount = volumeCount;
        data.pricingMethod = pricingMethod;
        data.autoDisVersion = autoDisVersion;
        data.deliAreaList = deliAreaList;
        data.arriAreaList = arriAreaList;
        data.arriAddrName = arriAddrName;

        if(transType == '15' || transType == '16'){
            data.goodsKind = '2'
        }else{
            data.goodsKind = '1'
        }

        if(pricingMethod != null && ( pricingMethod == '0' || pricingMethod == '1' || pricingMethod == '2' )) {
            console.log(data)
            $.ajax({
                type: "POST",
                url: ctx + "trace/carrierPeriodQuery",
                data: JSON.stringify(data),
                contentType: "application/json",
                dataType: "json",
                async: false,
                traditional: true,
                success: function(r){
                    console.log(r);
                    if(r.code == 0){
                        let frightAmount = r.data;
                        $("#frightPayAmount").val(frightAmount);

                        if(pricingMethod == '0'){
                            let priceNew = (Number(frightAmount/Number(weightCount))).toFixed(2);
                            $("#unitPriceDiv").html('¥'+priceNew);
                            $("#unitPrice").val(priceNew);
                        }else if(pricingMethod == '1'){
                            let priceNew = (Number(frightAmount/Number(volumeCount))).toFixed(2);
                            $("#unitPriceDiv").html('¥'+priceNew);
                            $("#unitPrice").val(priceNew);
                        }else if(pricingMethod == '2'){
                            let priceNew = (Number(frightAmount/Number(numCount))).toFixed(2);
                            $("#unitPriceDiv").html('¥'+priceNew);
                            $("#unitPrice").val(priceNew);
                        }
                        changeFrightPayAmount();
                        return false;
                    }else if(r.code == 301){
                        $("#submitBtn").css("display",'none');
                        $.modal.alertWarning(r.msg);
                    }else{
                        if(pricingMethod == '0'){
                            //按吨
                            $("#frightPayAmount").val((Number(unitPrice)*Number(weightCount)).toFixed(2));
                        }else if(pricingMethod == '1'){
                            //按方
                            $("#frightPayAmount").val((Number(unitPrice)*Number(volumeCount)).toFixed(2));
                        }else if(pricingMethod == '2'){
                            //按件
                            $("#frightPayAmount").val((Number(unitPrice)*Number(numCount)).toFixed(2));
                        }
                        changeFrightPayAmount();
                    }
                }
            });
        }



    }

    function editArriPic(entrustId){
        var url =
        layer.open({
            type: 2,
            area: ['500px', '500px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "回单确认",
            content: ctx + "trace/editArriPic?entrustId=" + entrustId,
            btn: ['回单确认','关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            //取消按钮
            btn2: function(index, layero){

            }
        });
    }

    function jumpReceiveDetail(invoiceVbillno){
        $.modal.openTab("应收明细",ctx + "receive/business?invoiceVbillno="+invoiceVbillno);
    }

    function changDivStatus(index){
       if($("#trDiv"+index).css('display')== 'none'){
           $("#trDiv"+index).css('display', 'contents')
       }else{
           $("#trDiv"+index).css('display', 'none')
       }
    }


    /**
     * 申请付款
     */
    function applyPay() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        var lotId = bootstrapTable[0].lotId
        if (bootstrapTable[0]["vbillstatus"] !== 1) {
            $.modal.alertWarning("请选择已确认的应付单");
            return;
        }

        var balaMethod = bootstrapTable[0]["balaMethod"];
        if(balaMethod == null){
            $.modal.alertWarning("请在基础承运商维护结算方式");
            return;
        }
        //非物流专线允许申请油卡
        if (bootstrapTable[0]["balaMethod"] == 2 && bootstrapTable[0]["carrType"] == 3 ) {
            $.modal.alertWarning("该应付单下的承运商未选择单笔付款");
            return;
        }
        if (bootstrapTable[0]["transFeeCount"] < 0) {
            $.modal.alertWarning("为负数的应付单请加入对账付款");
            return;
        }
        if (bootstrapTable[0]["consumbleBack"] === 0) {
            $.modal.alertWarning("应付单据存在耗材费用!");
            return;
        }
        if (bootstrapTable[0]["balaMethod"] == 2 && bootstrapTable[0]["carrType"] != 3 &&
            (bootstrapTable[0]["costTypeFreight"] == 0 || bootstrapTable[0]["costTypeFreight"] == 2 || bootstrapTable[0]["costTypeFreight"] == 4 || bootstrapTable[0]["freeType"] == 1)) {
            $.modal.alertWarning("该应付单现金无法在此页面申请");
            return;
        }

        var flag = false;
        var msg = "";
        //单笔到付现金或者到付油卡 提交付款申请，必须回单确认之后才可提交
        /*  if(addrType != '3'){
              if (bootstrapTable[0]["balaMethod"] == 1) {*/
        if (bootstrapTable[0]["freeType"] == 0) {
            if (bootstrapTable[0]["costTypeFreight"] == 2 || bootstrapTable[0]["costTypeFreight"] == 3 || bootstrapTable[0]["costTypeFreight"] == 4 || bootstrapTable[0]["costTypeFreight"] == 5) {
                //验证运单下委托单是否都确认
                $.ajax({
                    type: "POST",
                    url: prefix + "/checkEntrustReceiptConfirm?lot="+lot,
                    async: false,
                    success: function(r){
                        if(r.code != 0){
                            msg = '到/回付款必须回单确认之后才可提交';
                            flag = true;
                        }
                    }
                });
            }
        }

        if (bootstrapTable[0]["freeType"] == 0 && (bootstrapTable[0]["costTypeFreight"] == 4 || bootstrapTable[0]["costTypeFreight"] == 5)) {
            $.ajax({
                type: "POST",
                url: prefix + "/checkEntrustIfReceipt?lot="+lot,
                async: false,
                success: function(r){
                    if(r.code != 0){
                        msg = '回付款必须正本回单之后才可提交';
                        flag = true;
                    }
                }
            });
        }
        if (bootstrapTable[0]["freeType"] == 0 && (bootstrapTable[0]["costTypeFreight"] == 0 || bootstrapTable[0]["costTypeFreight"] == 1)) {
            $.ajax({
                type: "POST",
                url: prefix + "/checkEntrustPickUp?lot="+lot,
                async: false,
                success: function(r){
                    if(r.code != 0){
                        msg = '预付款必须提货之后才可提交';
                        flag = true;
                    }
                }
            });
        }

        /*      }else{
                  //验证全部到货
                  if (bootstrapTable[0]["freeType"] == 0) {
                      if (bootstrapTable[0]["costTypeFreight"] == 2 || bootstrapTable[0]["costTypeFreight"] == 3 || bootstrapTable[0]["costTypeFreight"] == 4 ||  bootstrapTable[0]["costTypeFreight"] == 5) {
                          //验证运单下委托单是否都确认
                          $.ajax({
                              type: "POST",
                              url: prefix + "/checkEntrustArrival?lot="+lot,
                              async: false,
                              success: function(r){
                                  if(r.code != 0){
                                      msg = r.msg;
                                      flag = true;
                                  }
                              }
                          });
                      }
                  }
              }*/
        if(flag){
            $.modal.alertWarning(msg);
            return false;
        }

        /* if (bootstrapTable[0]["freeType"] === '0'){
             if(bootstrapTable[0]["costTypeFreight"] != '1' && bootstrapTable[0]["costTypeFreight"] != '3' && bootstrapTable[0]["costTypeFreight"] != '5'){
                 if (bootstrapTable[0]["carrBankId"] == null || bootstrapTable[0]["carrBankId"] == "" || bootstrapTable[0]["carrBankId"] == 'undefined') {
                     $.modal.alertWarning("收款人信息为空无法进行付款申请");
                     return;
                 }
             }
         }*/

        var pass = false;
        $.ajax({
            url: ctx + 'check/exp/checkLockLot',
            data: "lotId="+ lotId,
            type: "post",
            async: false,
            success: function(r){
                if (r.code == 0) {
                    pass = true;
                } else {
                    $.modal.alertError(r.msg);
                }
            }
        })
        if (!pass) {
            return
        }
        //验证是否可以付款
        $.ajax({
            type: "POST",
            url: prefix + "/checkAllowPay?payDetailId="+bootstrapTable[0]["payDetailId"],
            async: false,
            success: function(r){
                if(r.code == 500){
                    $.modal.alertError(r.msg);
                    return false;
                }else if(r.code == 301){
                    //拆分应付
                    $.modal.confirm(r.msg, function () {
                        var url = prefix + "/splitPayDetail";
                        var data = {"payDetailId": bootstrapTable[0]["payDetailId"],"deposit":r.data};
                        $.operate.submit(url, "post", "json", data);
                    });
                }else{

                    var url = prefix + "/applyPay?payDetailId="+bootstrapTable[0]["payDetailId"];
                    $.modal.open("申请付款", url, 700);
                    //验证是否存在异常记录
                    // $.ajax({
                    //     type: "POST",
                    //     url: prefix + "/checkEntrustExp?payDetailIds="+bootstrapTable[0]["payDetailId"],
                    //     async: false,
                    //     success: function(r){
                    //         if(r.code != 0){
                    //             $.modal.alertError(r.msg);
                    //             return false;
                    //         }else{
                    //
                    //         }
                    //     }
                    // });
                }
            }
        });
    }

</script>
</body>

</html>