<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反确认')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
</head>
<style>
    .file-drop-zone{
        height: 100% !important;
    }
    .dropdown-menu{
        left:0px !important;
    }
</style>
<body>
<div class="form-content">
    <form id="form-trustDeed-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="entrustId" name="entrustId" type="hidden" th:value="${entrustId}">
            <div class="panel-body">
                <!--基础信息 begin-->
                <div class="row">
                    <div class="col-xs-6">
                        <div class="form-group" th:if="${entrust ne null}">
                            <label class="col-sm-5">承运商：</label>
                            <div class="col-sm-7" >
                                <input id="carrName" name="carrName" type="text" class="form-control valid">
                                <input id="carrierId" name="carrierId" type="hidden" class="form-control valid">
                                <div class="input-group-btn">
                                    <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                        <span class="caret"></span>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-right" role="menu"  style="left: 0px">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="form-group">
                            <label class="col-sm-5">修改类型：</label>
                            <div class="col-sm-7">
                                <select name="unconfirmType" id="unconfirmType" th:with="type=${@dict.getType('back_confirm_type')}" class="form-control valid" aria-invalid="false" onchange="typeChange()">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-sm-5">修改说明：</label>
                            <div class="col-sm-12">
                                <textarea name="unconfirmMemo" id="unconfirmMemo" class="form-control" type="text"
                                          maxlength="200" required="" aria-required="true"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="col-sm-5">申请人：</label>
                            <div class="col-sm-12">
                                <input name="unconfirmApplicationName" id="unconfirmApplicationName" class="form-control" type="text"
                                          maxlength="30">
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12" id="responsibleUserNameDiv" style="display: none">
                        <div class="form-group">
                            <label class="col-sm-5"><label style="color: red">*</label>责任人：</label>
                            <div class="col-sm-12">
                                <input name="responsibleUserName" id="responsibleUserName" class="form-control" type="text"
                                       maxlength="30" onclick="selectSalesDept()" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <div class="">申请照片：</div>
                        <div class="">
                            <div class="">
                                <div class="">
                                    <input id="image" class="form-control" name="image" type="file" multiple>
                                    <input id="appendixId" name="appendixId" type="hidden">
                                </div>
                                <label id="error-panel" class="error" for="image" style="display: none;"></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script th:inline="javascript">

    var prefix = ctx + "trace";

    $(function() {
        var image = {
            maxFileCount: 5,
            publish: "imgDone",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        $.file.initAddFiles("image", "appendixId", image);

        // 图片上传成功后
        $("#image").on('filebatchuploadsuccess', function (event, data) {
            var tid = data.response.tid;
            $("#appendixId").val(tid);
            commit();
        });
    })
    /**
     * 校验
     */
    $("#form-trustDeed-unconfirm").validate({
        onkeyup: false,
        focusCleanup: true,
        rules:{
            unconfirmType:{
                required:true,
            },
            unconfirmMemo:{
                required:true,
            },
         /*   unconfirmApplicationName: {
                required:true
            },*/
            // image:{
            //     required:true
            // }
        }
    });

    /**
     * 提交
     */
    function submitHandler() {
        $("#error-panel").css('display', 'none')
        //判断图片是否上传
        // var title = $(".file-caption-name").attr('title');
        // if(title == undefined) {
        //     $("#error-panel").text("这是必填字段")
        //     $("#error-panel").css('display', 'block')
        //     return
        // }
        if ($.validate.form()) {
    /*        var reg= /^(([a-zA-Z+\.?\·?a-zA-Z+]{2,30}$)|([\u4e00-\u9fa5+\·?\u4e00-\u9fa5+]{2,30}$))/;
            var unconfirmApplicationName = $("#unconfirmApplicationName").val()
            if(!reg.test(unconfirmApplicationName)){
                $.modal.alertWarning("申请人姓名输入有误，请重新输入")
                return false;
            }*/

      /*      var title = $(".explorer-caption").attr("title")
            if(title == null) {
                $.modal.alertWarning("请先选择文件路径")
                return  false;
            }*/

            // if($("#image")[0].files[0] == null) {
            //     $.modal.alertWarning("请先选择文件路径")
            //     return  false;
            // }
            var errorMsg = $(".kv-fileinput-error ul li").text()
            if(errorMsg != null  && errorMsg != '') {
                $.modal.alertWarning("请上传有效的图片格式")
                return  false;
            }
            // console.log($("#form-trustDeed-unconfirm").serializeArray());
            if ($("#image").val() != "") {
                $("#image").fileinput('upload');
            }else {
                commit();
            }
            //$.operate.save(prefix + "/backConfirmPick", $("#form-trustDeed-unconfirm").serializeArray());
        }
    }

    function commit() {
        // var unconfirm = new Object();
        // unconfirm.entrustId = $("#entrustId").val()
        // unconfirm.unconfirmType = $("#unconfirmType").val()
        // unconfirm.unconfirmApplicationName = $("#unconfirmApplicationName").val()
        // unconfirm.unconfirmMemo = $("#unconfirmMemo").val()
        // unconfirm.appendixId = $("#appendixId").val();
        // unconfirm.deConfirmation = $("#deConfirmation").val();
        let query = $("#form-trustDeed-unconfirm").serializeArray();
        //query.push({"name" : "unconfirmType", "value" : $("#unconfirmType").val()})
        //disabled的select框不会被序列化
        //query.push({"name" : "deConfirmation", "value" : $("#deConfirmation").val()})
        //console.log($("#form-trustDeed-unconfirm").serializeArray())
        console.log(query)
        //console.log(unconfirm)
        layer.confirm('确定进行更换承运商操作？', {
            btn: ['确认','取消'] //按钮
        }, function(){
            //操作完成后,trace页面的条件还需要带着一起查询    不是直接刷新父页面
            $.operate.save(prefix + "/backConfirmCarrier", query);
            // $.operate.saveTabJson(prefix + "/backConfirmPick", unconfirm, function (result) {
            //     if (result.code == web_status.SUCCESS) {
            //         parent.location.reload();
            //     }
            // });
        }, function(){
            //取消操作后清空
            clearUploadFile()
        });
    }
    //验证反确认的类型是否正确
    function checkAuthority(value) {
        //获取vbillstatus
        /*var oldDeconfirmation = $("#oldDeconfirmation").val()
        if(value > oldDeconfirmation && value != 8) {
            $.modal.alertError("您不可以这么操作!")
            //复原原来的值
            $("#deConfirmation").val(oldDeconfirmation)
            return;
        }*/
        // else {
        //     $.modal.confirm("确定要跨多级操作吗？", function () {
        //         return true;
        //     });
        // }
    }

    $('.kv-file-remove').click(function () {
        const title = $(this).parent().parent().parent().parent().find('.file-footer-caption').attr('title')

        console.log(`>>>>>>移除文件`)
    })

    $('.fileinput-remove-button').click(function () {
        console.log(`>>>>>>移除文件`)
        clearUploadFile()
    })

    function clearUploadFile() {
        $('input[name=iamge]').val("")
        $('input[name=appendixId]').val("")
    }

    function typeChange(){
        let unconfirmType = $("#unconfirmType").val();
        if(unconfirmType == 1){
            $("#responsibleUserNameDiv").css("display",'block');
        }else{
            $("#responsibleUserNameDiv").css("display",'none');
        }
    }

    function selectSalesDept() {
        layer.open({
            type: 2,
            area: ['90%', '90%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "选择责任人",
            content: ctx + "system/dept/opsGroupTreeZRR",
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function (index, layero){
                //获取整行
                var rows = layero.find('iframe')[0].contentWindow.getChecked();
                if (rows.length === 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                let arr = [];
                console.log(rows)
                for(let i = 0 ; i < rows.length ; i++){
                    arr.push(rows[i].userName);
                }
                $(`#responsibleUserName`).val(arr.join(","));
                layer.close(index);
            },
            cancel: function(index) {
                return true;
            }
        });
    }

    /**
     * 关键字提示查询
     */
    //模糊查询
    $("#carrName").bsSuggest('init', {
        url: ctx + "backLedger/findCarrierInfo?keyword=",
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "carrName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["carrCode","carrName","legalCard","phone"],
        effectiveFieldsAlias: {"carrCode":"承运商编号","carrName":"承运商名称","legalCard":"身份证","phone":"联系电话"},
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#carrierId").val(data.carrierId);
        $("#carrName").val(data.carrName);
    })
</script>
</body>
</html>