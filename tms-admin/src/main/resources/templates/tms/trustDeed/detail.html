<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('委托单详细')"/>
</head>

<style>
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fr{
        float: right;
    }
    .ml10{
        margin-left: 10px;
    }
    .boxs{
        background: #F2F9FF;
        border: 1px solid #CFE0F4;
        padding: 10px 10px;
    }
    .fw{
        font-weight: bold;
    }
    .fc1a{
        color: #1a1a1a;
    }
    .fc80{
        color: #808080;
    }
    .mt10{
        margin-top: 10px;
    }
    .mt20{
        margin-top: 20px;
    }
    .table th, .table td {
        text-align: center;
    }
    .flex{
        display: flex;
    }
    .flex_left{
        width: 80px;
        line-height: 26px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 26px;
    }
    .trans{
        border: 1px solid #E1E2E3;
        padding: 10px 20px;
    }
    .trans_left{
        width: 35%;
        box-sizing: border-box;
    }
    .trans_right{
        min-width:0;
        flex:1;
        box-sizing: border-box;
    }
    .trans_box{
        width: 200px;
    }
    .tc{
        text-align: center;
    }
    .round{
        width: 60px;
        height: 60px;
        text-align: center;
        border-radius: 50%;
        line-height: 50px;
        margin-left: 60px;
        box-sizing: border-box;
    }
    .th{
        border: 6px solid #00A9FF;
        color: #00A9FF;
    }
    .dh{
        border: 6px solid #FF8900;
        color: #FF8900;
    }
    .dis {
        border: 6px solid #CCCCCC;
        color: #cccccc;
    }
    .hd{
        border: 6px solid #1BA356;
        color: #1BA356;
    }
    .line{
        width: 100%;
        /*padding: 20px 10px;*/
        /*border: 4px solid;*/
        /*border-image: linear-gradient(270deg, rgba(255, 137, 0, 1), rgba(0, 169, 255, 1)) 4 4;*/
        box-sizing: border-box;
        height: 3px;
        background: linear-gradient(to right, #FF8900, #00A9FF 7px, transparent 7px, transparent);
        background-size: 10px 100%;
    }
    .th_img{
        width: 30px;
        height: 30px;
    }
    .hd_text{
        padding: 10px 10px;
        border: 1px solid #D9D9D9;
    }
    .sj{
        width: 6px;
        height: 6px;
        background: linear-gradient(to top, #ddd, #ddd) no-repeat, linear-gradient(to right, #ddd, #ddd) no-repeat, linear-gradient(
                135deg, #fff, #fff 6px, hsla(0,0%,100%,0) 6px) no-repeat;
        background-size: 10px 1px, 1px 10px, 10px 10px;
        transform: rotate(-45deg);
        position: absolute;
        top: 10px;
        left: 12px;
    }
    .input-group {
        width: 100%;
        text-align: center;
    }
    .btn_th {
        /*width: 100px;*/
        height: 30px;
        line-height: 30px;
        border-radius: 15px;
        text-align: center;
        background: #1ab394;
        color: #fff !important;
        cursor: pointer;
        margin: 10px 0 0 0;
    }
    .car_id{
        text-align: center;
        line-height: 20px;
    }
    .btn-gz{
        background: url("../img/gz.png") no-repeat left;
        background-size: 20px 20px;
        background-position: 10px 3px;
        padding: 0px 10px 0 30px;
        cursor: pointer;
    }
    .btn-dg{
        background: url("../img/dg.png") no-repeat left;
        background-size: 15px 15px;
        background-position: 10px 4px;
        padding: 0px 10px 0 30px;
    }
    .btn-fh{
        background: #1ab394 url("../img/fh.png") no-repeat left;
        background-size: 15px 15px;
        background-position: 10px 9px;
        padding: 8px 10px 8px 30px;
        cursor: pointer;
    }
    .btn_gre{
        /*width: 100px;*/
        border: 1px #1ab394 solid;
        /*padding: 0px 10px;*/
        color: #1ab394;
        text-align: center;
        border-radius: 13px;
        cursor: pointer;
    }
    .btn-err{
        background: url("../img/danger.png") no-repeat left;
        background-size: 20px 20px;
        padding: 0px 10px 0 35px;
        background-position: 10px 2px;
        cursor: pointer;
    }
    .btn_red{
        /*width: 100px;*/
        border: 1px #f25f6f solid;
        /*padding: 0px 10px;*/
        color: #f25f6f;
        text-align: center;
        border-radius: 13px;
    }
    .bg_green{
        /*background: #1ab394;*/
        color: #fff;
        text-align: center;
        border-radius: 15px;
    }
    .btn_yf{
        color: #1ab394;
        border: 1px #1ab394 solid;
        width: 100px;
        text-align: center;
        line-height: 26px;
        border-radius: 15px;
        margin: 10px 0 10px 0;
        cursor: pointer;
    }
    .fcgr{
        color: #1ab394;
    }

    .f16{
        font-size: 16px;
    }
    .role{
        width: 60px;
        text-align: center;
        height: 24px;
        line-height: 24px;
        border-radius: 10px;
        border: 1px #eee solid;
    }
    .line24{
        line-height: 24px;
    }
    .wraps{
        padding: 10px 10px;
        background: #fff;
        border-radius: 2px;
    }
    .addr{
        width: 24px;
        height: 24px;
        border-radius: 4px;
        line-height: 24px;
        text-align: center;
        color: #fff;
        font-size: 15px;
    }
    .bg009{
        background: #009AFE;
    }
    .bgff9{
        background: #FF9008;
    }
    .underline{
        text-decoration: underline;
    }
    .note{
        background: rgba(239,77,64,0.05);
        border-radius: 4px;
        border: 1px solid rgba(239,77,64,0.08);
        padding: 5px 10px;
        color: #F94242;
    }
    .goods{
        padding: 15px;
        background: #F4F5F7;
        border-radius: 2px;
        width: 100%;
        min-height: 150px;
    }
    .gflex{
        display: flex;
        justify-content: space-between;
    }
    .cys{
        background: #FFFFFF;
        box-shadow: 0px 8px 16px 0px rgba(0,36,70,0.1);
        border-radius: 8px;
    }
    /*.flex{*/
    /*    display: flex;*/
    /*}*/
    /*.flex_left{*/
    /*    width: 80px;*/
    /*    line-height: 26px;*/
    /*    text-align: right;*/
    /*}*/
    /*.flex_right{*/
    /*    min-width:0;*/
    /*    flex:1;*/
    /*    line-height: 26px;*/
    /*}*/
    .ys{
        width: 100px;
        /*text-align: right;*/
        line-height: 40px;
        height: 50px;
        color: #fff;
        font-size: 13px;
        padding: 0 0px 0 35px;
        cursor: pointer;
    }
    .zh{
        background: url('../../img/zhuang.png') no-repeat center;
        background-size: 100% 100%;

    }
    .xie{
        background: url('../../img/xie.png') no-repeat center;
        background-size: 100% 100%;
    }
    .la{
        background: url('../../img/la.png') no-repeat center;
        background-size: 100% 100%;
    }
    .line1 {
        width:calc(100% - 100px);
        box-sizing: border-box;
        height: 3px;
        border-top: 1px #c4c4c4 dashed;
        margin-top: 20px;
        position: relative;
    }
    .carid{
        position: absolute;
        width: 100px;
        height: 30px;
        line-height: 30px;
        background: url('../../img/ye.png') no-repeat center;
        background-size: 100px 30px;
        margin-left: -50px;
        left: 50%;
        top: -20px;
        text-align: center;
        font-weight: bold;
        font-size: 16px;
    }
    .tjdg{
        border-radius: 16px;
        border: 1px dashed #0084FF;
        width: 100px;
        height: 40px;
        color: #0084FF;
        font-size: 14px;
        line-height: 40px;
        text-align: center;
        cursor: pointer;
    }
    .hl{
        border-radius: 4px;
        border: 1px solid #CFD0D1;
        padding: 4px 8px;
        cursor: pointer;
    }
    .fc23{
        color: #F23333;
    }
    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .vertical-timeline-icon{
        width: 10px;
        height: 10px;
        background-color: #333;
        border-color: #333;
        top: 20px;
    }
    .vertical-timeline-content{
        margin-left: 16px;
    }
    .leftIcon{
        position: relative;
        margin: 0;
        font-size: 14px;
    }
    .leftIcon::after{
        content:" ";
        position: absolute;
        width: 1px;
        height: 100%;
        background-color: #333;
        top: 20px;
        left: 4px;
    }
    .leftIcon:last-child::after{
        height: 50%;
    }
    .fc008{
        color: #0084FF;
    }
    .textun{
        text-decoration: underline;
    }
    .cys{
        background: url("../../img/cys.png") no-repeat right;
        background-size: 60px 60px;
    }
    ::-webkit-scrollbar-track {
        background-color: #fff;
    }
    .hdsc{
        background: #666666;
        color: #fff;
        padding: 5px 8px;
        border-radius: 13px;
        display: inline-block;
        text-align: center;
    }
</style>
<body style="background: #F5F5F5;">
<div class="form-content" style="background: #F5F5F5;">
    <form id="form-user-add" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion" >


            <!--             运输跟踪 -->
            <div class="panel panel-default" th:each="entrustlist:${list}" style="border: 0">
                <!--                <div class="panel-heading">-->
                <!--                    <h4 class="panel-title">-->
                <!--                        <a data-toggle="collapse" data-parent="#accordion"-->
                <!--                           href="tabs_panels.html#collapseTwo">运输跟踪</a>-->
                <!--                    </h4>-->
                <!--                </div>-->
                <div id="collapseThree" class="panel-collapse collapse in">
                    <div class="panel-body" style="background: #F5F5F5;">
                        <div class="wraps over row">
                            <div class="col-sm-8">
                                <div class="over">
                                    <div class="fl">
                                        <img th:src="@{/img/hu.png}" style="width: 50px;height: 50px" />
                                    </div>
                                    <div class="fl ml10">
                                        <div class="over">
                                            <div class="fl line24 fw f16">[[${entrustlist.entrust.custName}]]</div>
                                            <div class="fl ml20 role">客户</div>
                                        </div>
                                        <div class="over">
                                            <div class="fl line24">
                                                <span>运单号：</span>
                                                <span>[[${entrustlist.entrust.lot}]]</span>
                                            </div>
                                            <div class="fl ml20 line24">
                                                <span>发货单号：</span>
                                                <span>[[${entrustlist.entrust.invoiceVbillno}]]</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="fl ml10">
                                        <div class="" th:if=" ${entrustlist.entrust.vbillstatus} eq '待确认'">
                                            <img th:src="@{/img/daiqueren.png}" style="width: 80px;height: 65px">
                                        </div>
                                        <div class="" th:if=" ${entrustlist.entrust.vbillstatus} eq '已确认'">
                                            <img th:src="@{/img/yqr.png}" style="width: 80px;height: 65px">
                                        </div>
                                        <div class="" th:if=" ${entrustlist.entrust.vbillstatus} eq '已提货'">
                                            <img th:src="@{/img/yitihuo.png}" style="width: 80px;height: 65px">
                                        </div>
                                        <div class="" th:if=" ${entrustlist.entrust.vbillstatus} eq '已到货'">
                                            <img th:src="@{/img/yidaohuo.png}" style="width: 80px;height: 65px">
                                        </div>
                                        <div class="" th:if=" ${entrustlist.entrust.vbillstatus} eq '关闭'">
                                            <img th:src="@{/img/guanbi.png}" style="width: 80px;height: 65px">
                                        </div>
                                        <div class="" th:if=" ${entrustlist.entrust.vbillstatus} eq '分配车队'">
                                            <img th:src="@{/img/fpcd.png}" style="width: 80px;height: 65px">
                                        </div>
                                    </div>
                                </div>
                                <div class="mt10">
                                    <div class="over">
                                        <div class="fl addr bgff9">装</div>
                                        <div class="fl ml10 line24">[[${#dates.format(entrustlist.entrust.reqDeliDate, 'yyyy-MM-dd')}]]</div>
                                        <div class="fl ml20 line24">
                                            <span>[[${entrustlist.entrust.deliProName + entrustlist.entrust.deliCityName + entrustlist.entrust.deliAreaName + entrustlist.entrust.deliDetailAddr}]]</span>
                                        </div>
                                    </div>
                                    <div style="width: 1px;height: 15px;border-left: 1px dashed #C4C4C4;margin-left: 10px"></div>
                                    <div class="over">
                                        <div class="fl addr bg009">卸</div>
                                        <div class="fl ml10 line24">[[${#dates.format(entrustlist.entrust.reqArriDate, 'yyyy-MM-dd')}]]</div>
                                        <div class="fl ml20 line24">
                                            <span  >[[${entrustlist.entrust.arriProName + entrustlist.entrust.arriCityName + entrustlist.entrust.arriAreaName + entrustlist.entrust.arriDetailAddr}]]</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="note mt10" th:if="${entrustlist.entrust.memo!=''&&entrustlist.entrust.memo!=null}">
                                    <span >备注：[[${entrustlist.entrust.memo}]]</span>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="goods">
                                    <div class="fw f16">货品信息</div>
                                    <div class="gflex mt10" th:each="mapS,status:${entrustlist.entPackGoodsList}">
                                        <div class=""><span th:text="${mapS.goodsName}"></span> - <span th:text="${mapS.goodsCode}"></span></div>
                                        <div class="" th:text="${@dict.getLabel('package_type',mapS.packId)}"></div>
                                        <div class="">
                                            <span th:if="${mapS.num>0}" th:text="${mapS.num}+'件 ｜'"></span>
                                            <span th:if="${mapS.weight>0}" th:text="${mapS.weight}+'(吨) ｜'"></span>
                                            <span th:if="${mapS.volume>0}" th:text="${mapS.volume}+'(m³)'"></span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt10 row" style="position: relative">
                            <div class="col-sm-9" style="padding-left: 0">
                                <div class="wraps over">
                                    <div class="over">
                                        <div class="cys fl" style="width: 60%">
                                            <div class="">
                                                <div class="over">
                                                    <div class="fl">
                                                        <img th:src="@{/img/pe.png}" style="width: 50px;height: 50px" />
                                                    </div>
                                                    <div class="fl ml10">
                                                        <div class="line24 fw f16">[[${entrustlist.entrust.carrName}]]</div>
                                                        <div class="over">
                                                            <div class="fl line24">联系人：[[${entrustlist.carrier.contact}]]/[[${entrustlist.carrier.phone}]]</div>
                                                            <div class="fl line24" style="margin-left: 20px">司机：[[${entrustlist.entrust.driverName}]]/[[${entrustlist.entrust.driverMobile}]]</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="fl ml10">
                                            <div class="over">
                                                <div class="fl bgff9" style="width: 10px;height: 10px;border-radius: 50%"></div>
                                                <div class="fl ml10" style="line-height: 15px">提货联系人：[[${entrustlist.entrust.deliContact}]]/[[${entrustlist.entrust.deliMobile}]]</div>
                                            </div>
                                            <div style="width: 1px;height: 15px;border-left: 1px dashed #C4C4C4;margin-left: 4px"></div>
                                            <div class="over">
                                                <div class="fl bg009" style="width: 10px;height: 10px;border-radius: 50%"></div>
                                                <div class="fl ml10">到货联系人：[[${entrustlist.entrust.arriContact}]]/[[${entrustlist.entrust.arriMobile}]]</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt10">
                                        <div class="col-sm-4" th:if="${not #lists.isEmpty(entrustlist.tihuowork)}">
                                            <div class="flex">
                                                <div class="ys zh">
                                                    <span th:if="${entrustlist.entrust.deliCityName!='市辖区'}">[[${entrustlist.entrust.deliCityName}]]</span>
                                                    <span th:if="${entrustlist.entrust.deliCityName=='市辖区'}">[[${entrustlist.entrust.deliProName}]]</span>
                                                </div>
                                                <div class="line1" th:if="${not #lists.isEmpty(entrustlist.daohuowork)}">
                                                    <div class="carid" th:if="${entrustlist.entrust.carno!=''&&entrustlist.entrust.carno!=undefined}">[[${entrustlist.entrust.carno}]]</div>
                                                </div>
                                                <div class="line1" th:if="${#lists.isEmpty(entrustlist.daohuowork)}">
                                                    <div class="carid" th:if="${entrustlist.entrust.carno!=''&&entrustlist.entrust.carno!=undefined}">[[${entrustlist.entrust.carno}]]</div>
                                                </div>
                                            </div>
                                            <div>
                                                <div style="width: 80%" th:if="${entrustlist.entrust.isMultiple == 0}">
                                                    <div class="mt10">[[${entrustlist.entrust.deliProName + entrustlist.entrust.deliCityName + entrustlist.entrust.deliAreaName + entrustlist.entrust.deliDetailAddr}]]</div>
                                                </div>
                                                <div style="width: 80%" th:if="${entrustlist.entrust.isMultiple == 1}" >
                                                    <div class="mt10" th:each="shippingAddress,status:${entrustlist.deliShippingAddressList}">[[${shippingAddress.provinceName + shippingAddress.cityName + shippingAddress.areaName + shippingAddress.detailAddr}]]</div>
                                                </div>
                                                <!--                                                <div class="flex" th:if="${not #lists.isEmpty(entrustlist.daohuowork)}">-->
                                                <!--                                                    <div class="tjdg" th:onclick="tacking([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]])">跟踪</div>-->
                                                <!--                                                    <div class="tjdg ml10" th:onclick="abnormal([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]])">异常跟踪</div>-->
                                                <!--                                                </div>-->
                                                <!--                                                <div class="flex" th:if="${#lists.isEmpty(entrustlist.daohuowork)}">-->
                                                <!--                                                    <div class="tjdg" th:onclick="tacking([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]])">跟踪</div>-->
                                                <!--                                                    <div class="tjdg ml10" th:onclick="abnormal([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]])">异常跟踪</div>-->
                                                <!--                                                </div>-->
                                                <p class="mt10">实际提货日期：[[${#dates.format(entrustlist.entrust.actDeliDate, 'yyyy-MM-dd HH:mm:ss')}]]</p>
                                                <p class="mt10">操作人：[[${entrustlist.tihuowork[0].regUserName}]]</p>
                                                <p>操作日期：[[${#dates.format(entrustlist.tihuowork[0].regDate, 'yyyy-MM-dd HH:mm:ss')}]]</p>
                                                <div style="display: flex">
                                                   <span class="picviewer" >
                                                        <img class="th_img" th:each="tihuopic:${entrustlist.tihuoList}" th:if="${tihuopic.filePath!=null and tihuopic.workAppendixType != '7'}" th:src="@{${tihuopic.filePath}}"/>
                                                    </span>
                                                    <span style="width:200px"  th:each="tihuopic:${entrustlist.tihuoList}">
                                                        <video controls style="width: 100%; " th:if="${tihuopic.filePath!=null and tihuopic.workAppendixType == '7'}" th:src="@{${tihuopic.filePath}}"/>
                                                    </span>
                                                </div>
                                            </div>

                                        </div>

                                        <div class="col-sm-4"  th:if="${#lists.isEmpty(entrustlist.tihuowork)}">
                                            <div class="flex">
                                                <div class="ys zh">
                                                    <span th:if="${entrustlist.entrust.deliCityName!='市辖区'}">[[${entrustlist.entrust.deliCityName}]]</span>
                                                    <span th:if="${entrustlist.entrust.deliCityName=='市辖区'}">[[${entrustlist.entrust.deliProName}]]</span>
                                                </div>
                                                <div class="line1" th:if="${not #lists.isEmpty(entrustlist.daohuowork)}">
                                                    <div class="carid" th:if="${entrustlist.entrust.carno!=''&&entrustlist.entrust.carno!=undefined}">[[${entrustlist.entrust.carno}]]</div>
                                                </div>
                                                <div class="line1" th:if="${#lists.isEmpty(entrustlist.daohuowork)}">
                                                    <div class="carid" th:if="${entrustlist.entrust.carno!=''&&entrustlist.entrust.carno!=undefined}">[[${entrustlist.entrust.carno}]]</div>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="tjdg" th:onclick="pick([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]],[[${entrustlist.entrust.isFleetAssign}]])">添加提货</div>
                                                <!--                                                <p class="mt10">[[${entrustlist.entrust.deliContact}]]/[[${entrustlist.entrust.deliMobile}]]</p>-->
                                                <div style="width: 80%" th:if="${entrustlist.entrust.isMultiple == 0}">
                                                    <div class="mt10">[[${entrustlist.entrust.deliProName + entrustlist.entrust.deliCityName + entrustlist.entrust.deliAreaName + entrustlist.entrust.deliDetailAddr}]]</div>
                                                </div>
                                                <div style="width: 80%" th:if="${entrustlist.entrust.isMultiple == 1}" >
                                                    <div class="mt10" th:each="shippingAddress,status:${entrustlist.deliShippingAddressList}">[[${shippingAddress.provinceName + shippingAddress.cityName + shippingAddress.areaName + shippingAddress.detailAddr}]]</div>
                                                </div>
                                            </div>

                                        </div>

                                        <div class="col-sm-5" th:if="${not #lists.isEmpty(entrustlist.daohuowork)}">
                                            <div class="flex">
                                                <div class="ys xie">
                                                    <span th:if="${entrustlist.entrust.arriCityName!='市辖区'}">[[${entrustlist.entrust.arriCityName}]]</span>
                                                    <span th:if="${entrustlist.entrust.arriCityName=='市辖区'}">[[${entrustlist.entrust.arriProName}]]</span>
                                                </div>
                                                <!--                                                    <div class="tjdg">+ 添加到货</div>-->
                                                <div class="line1"></div>
                                            </div>
                                            <div class="mt10" th:if="${entrustlist.entrust.isMultiple == 0}">
                                                <div class="">[[${entrustlist.entrust.arriProName + entrustlist.entrust.arriCityName + entrustlist.entrust.arriAreaName + entrustlist.entrust.arriDetailAddr}]]</div>
                                            </div>
                                            <div class="mt10" th:if="${entrustlist.entrust.isMultiple == 1}" >
                                                <div class="" th:each="shippingAddress,status:${entrustlist.arriShippingAddressList}">[[${shippingAddress.provinceName + shippingAddress.cityName + shippingAddress.areaName + shippingAddress.detailAddr}]]</div>
                                            </div>
                                            <div class="row mt10">
                                                <div class="col-sm-6">
                                                    <div class="hdsc">到货</div>
                                                    <p class="mt10">实际到货日期：[[${#dates.format(entrustlist.entrust.actArriDate, 'yyyy-MM-dd HH:mm:ss')}]]</p>
                                                    <p class="mt10">操作人：[[${entrustlist.daohuowork[0].regUserName}]]</p>
                                                    <p>操作日期：[[${#dates.format(entrustlist.daohuowork[0].regDate, 'yyyy-MM-dd HH:mm:ss')}]]</p>
                                                    <!--                                                <p>[[${#dates.format(entrustlist.entrust.actArriDate, 'yyyy-MM-dd HH:mm:ss')}]]</p>-->

                                                    <div style="display: flex">
                                                       <span class="picviewer" >
                                                            <img class="th_img" th:each="daohuopic:${entrustlist.daohuoList}" th:if="${daohuopic.filePath!=null and daohuopic.workAppendixType != '4'}" th:src="@{${daohuopic.filePath}}"/>
                                                        </span>
                                                            <span style="width:200px" th:each="daohuopic:${entrustlist.daohuoList}">
                                                            <video controls style="width: 100%; "  th:if="${daohuopic.filePath!=null and daohuopic.workAppendixType == '4'}" th:src="@{${daohuopic.filePath}}"/>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-sm-6" th:if="${entrustlist.receiptPicInfo!=null}">
                                                    <div class="hdsc">回单上传</div>
                                                    <p class="mt10" >[[${entrustlist.receiptPicInfo.regUserName}]]</p>
                                                    <p>[[${#dates.format(entrustlist.receiptPicInfo.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</p>

                                                    <div class="mt10">
                                                       <span class="picviewer" >
                                                            <img class="th_img" th:each="pic:${entrustlist.receiptUploadFiles}" th:if="${pic.filePath!=null}" th:src="@{${pic.filePath}}"/>
                                                        </span>
                                                    </div>
                                                    <!--                                                <div class="mt10">[[${entrustlist.arrivalPicUser.regUserName}]] [[${#dates.format(entrustlist.arrivalPicUser.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</div>-->
                                                </div>

                                            </div>


                                        </div>
                                        <div class="col-sm-5" th:if="${#lists.isEmpty(entrustlist.daohuowork)}">
                                            <div>
                                                <div class="flex">
                                                    <div class="ys xie">
                                                        <span th:if="${entrustlist.entrust.arriCityName!='市辖区'}">[[${entrustlist.entrust.arriCityName}]]</span>
                                                        <span th:if="${entrustlist.entrust.arriCityName=='市辖区'}">[[${entrustlist.entrust.arriProName}]]</span>
                                                    </div>
                                                    <div class="line1">
                                                        <!--                                                <div class="carid"></div>-->
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="tjdg" th:onclick="arrive([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]])">
                                                        添加到货
                                                    </div>
                                                    <!--                                                    <p class="mt10">[[${entrustlist.entrust.arriContact}]]/[[${entrustlist.entrust.arriMobile}]]</p>-->

                                                    <div th:if="${entrustlist.entrust.isMultiple == 0}">
                                                        <div class="mt10">[[${entrustlist.entrust.arriProName + entrustlist.entrust.arriCityName + entrustlist.entrust.arriAreaName + entrustlist.entrust.arriDetailAddr}]]</div>
                                                    </div>
                                                    <div th:if="${entrustlist.entrust.isMultiple == 1}" >
                                                        <div class="mt10" th:each="shippingAddress,status:${entrustlist.arriShippingAddressList}">[[${shippingAddress.provinceName + shippingAddress.cityName + shippingAddress.areaName + shippingAddress.detailAddr}]]</div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>

                                        <div class="col-sm-3">
                                            <div>
                                                <div class="ys la" style="text-align: center;padding: 0">回单</div>

                                            </div>
                                            <div th:if="${not #lists.isEmpty(entrustlist.daohuowork)}">
                                                <div class="">
                                                    <div class="">
                                                        <div class="tjdg"
                                                             th:if="${entrustlist.entrust.receiptConfirmFlag == 0}"
                                                             th:onclick="receiptConfirm([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]],[[${entrustlist.entrust.isFleetAssign}]],[[${entrustlist.entrust.ifReceipt}]])">
                                                            回单影像确认
                                                        </div>
                                                        <div th:if="${entrustlist.entrust.receiptConfirmFlag == 1}">
                                                            <div class="hdsc">回单影像确认</div>
                                                            <div class="mt10">[[${entrustlist.entrust.receiptConfirmUser}]]</div>
                                                            <p>[[${#dates.format(entrustlist.entrust.receiptConfirmTime, 'yyyy-MM-dd HH:mm:ss')}]]</p>
                                                            <!--                                                            <div class="mt10">-->
                                                            <!--                                                               <span class="picviewer" th:each="pic:${entrustlist.uploadFileList}" th:if="${pic.filePath!=null}">-->
                                                            <!--                                                                    <img class="th_img" th:src="@{${pic.filePath}}"/>-->
                                                            <!--                                                                </span>-->
                                                            <!--                                                            </div>-->
                                                        </div>
                                                    </div>
                                                    <div class="">
                                                        <div class="tjdg mt10" th:if="${entrustlist.entrust.ifReceipt == '0'}" style="text-align: center;padding: 0" th:onclick="receipt([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],2,[[${entrustlist.entrust.ifReceipt}]],[[${entrustlist.entrust.receiptConfirmFlag}]],[[${entrustlist.entrust.isFleetData}]],[[${entrustlist.entrust.isFleetAssign}]])">正本回单</div>
                                                        <div th:if="${entrustlist.entrust.ifReceipt == '1'}">
                                                            <div class="hdsc">正本回单</div>
                                                            <div class="mt10">[[${entrustlist.entrust.receiptMan}]]</div>
                                                            <p>[[${#dates.format(entrustlist.entrust.receiptDate, 'yyyy-MM-dd HH:mm:ss')}]]</p>
                                                            <!--                                                            <div class="mt10">-->
                                                            <!--                                                               <span class="picviewer" >-->
                                                            <!--                                                                    <img class="th_img" th:each="pic:${entrustlist.uploadFileList}" th:if="${pic.filePath!=null}" th:src="@{${pic.filePath}}"/>-->
                                                            <!--                                                                </span>-->
                                                            <!--                                                            </div>-->
                                                        </div>
                                                    </div>
                                                </div>


                                            </div>
                                            <!--                                            <div th:if="${lists.isEmpty(entrustlist.daohuowork)}"></div>-->
                                        </div>
                                        <!--回单end-->

                                        <!--                                        <div th:if="${entrustlist.entrust.ifReceipt == '1'}">-->
                                        <!--                                            <div>-->
                                        <!--                                                <div class="ys la" style="text-align: center;padding: 0">回单</div>-->

                                        <!--                                            </div>-->
                                        <!--                                            <div>-->
                                        <!--                                                <div th:if="${not #lists.isEmpty(entrustlist.daohuowork)}">-->
                                        <!--                                                    <div class="tjdg"-->
                                        <!--                                                         th:if="${entrustlist.entrust.receiptConfirmFlag == 0}"-->
                                        <!--                                                         th:onclick="receiptConfirm([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]],[[${entrustlist.entrust.isFleetAssign}]],[[${entrustlist.entrust.ifReceipt}]])">-->
                                        <!--                                                        回单影像确认-->
                                        <!--                                                    </div>-->
                                        <!--                                                </div>-->
                                        <!--                                            </div>-->

                                        <!--                                            <div class="row">-->
                                        <!--                                                <div class="col-sm-6">-->
                                        <!--                                                    <div class="mt10" style="color: #0e9aef">回单影像确认</div>-->
                                        <!--                                                    <p>[[${entrustlist.entrust.receiptMan}]]/[[${entrustlist.entrust.receiptConfirmUser}]]</p>-->
                                        <!--                                                    <p>[[${#dates.format(entrustlist.entrust.receiptDate, 'yyyy-MM-dd HH:mm:ss')}]]</p>-->
                                        <!--                                                    <p class="mt10">回单件数：[[${entrustlist.entrust.receiptNum}]]件</p>-->
                                        <!--                                                    <div class="mt10">-->
                                        <!--                                                       <span class="picviewer" th:each="pic:${entrustlist.uploadFileList}" th:if="${pic.filePath!=null}">-->
                                        <!--                                                            <img class="th_img" th:src="@{${pic.filePath}}"/>-->
                                        <!--                                                        </span>-->
                                        <!--                                                    </div>-->
                                        <!--                                                    <div>回单时间：</div>-->
                                        <!--                                                </div>-->
                                        <!--                                                <div class="col-sm-6">-->

                                        <!--                                                    <div class="mt10" style="color: #0e9aef">正本回单</div>-->
                                        <!--                                                    <p>[[${entrustlist.entrust.receiptMan}]]/[[${entrustlist.entrust.receiptConfirmUser}]]</p>-->
                                        <!--                                                    <p>[[${#dates.format(entrustlist.entrust.receiptDate, 'yyyy-MM-dd HH:mm:ss')}]]</p>-->
                                        <!--                                                    <p class="mt10">回单件数：[[${entrustlist.entrust.receiptNum}]]件</p>-->
                                        <!--                                                    <div class="mt10">-->
                                        <!--                                                       <span class="picviewer" th:each="pic:${entrustlist.uploadFileList}" th:if="${pic.filePath!=null}">-->
                                        <!--                                                            <img class="th_img" th:src="@{${pic.filePath}}"/>-->
                                        <!--                                                        </span>-->
                                        <!--                                                    </div>-->
                                        <!--                                                </div>-->
                                        <!--                                            </div>-->
                                        <!--                                        </div>-->
                                        <!--                                        <div th:if="${entrustlist.entrust.ifReceipt == '0'}">-->
                                        <!--                                            <div>-->
                                        <!--                                                <div class="ys la" style="text-align: center;padding: 0">回单</div>-->

                                        <!--                                            </div>-->
                                        <!--                                            <div class="over">-->
                                        <!--                                                <div class="fl" th:if="${not #lists.isEmpty(entrustlist.daohuowork)}">-->
                                        <!--                                                    <div class="tjdg"-->
                                        <!--                                                         th:if="${entrustlist.entrust.receiptConfirmFlag == 0}"-->
                                        <!--                                                         th:onclick="receiptConfirm([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]],[[${entrustlist.entrust.isFleetAssign}]],[[${entrustlist.entrust.ifReceipt}]])">-->
                                        <!--                                                        回单影像确认-->
                                        <!--                                                    </div>-->
                                        <!--                                                </div>-->
                                        <!--                                                <div class="tjdg fl ml10" style="text-align: center;padding: 0" th:onclick="receipt([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],2,[[${entrustlist.entrust.ifReceipt}]],[[${entrustlist.entrust.receiptConfirmFlag}]],[[${entrustlist.entrust.isFleetData}]],[[${entrustlist.entrust.isFleetAssign}]])">正本回单</div>-->

                                        <!--                                            </div>-->
                                        <!--                                        </div>-->
                                    </div>
                                </div>
                                <div class="wraps mt10" style="padding: 10px 20px">
                                    <div class="over">
                                        <!--                                        <div class="fl hl">货量更新</div>-->
                                        <!--                                        <div class="fl hl ml10">费用确认</div>-->
                                        <!--                                        <div class="fl hl ml10">费用登记</div>-->
                                        <div class="hl fl"
                                             th:onclick="cargo([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.orderno}]],[[${entrustlist.entrust.isClose}]],[[${entrustlist.entrust.isFleetData}]],[[${entrustlist.entrust.isFleetAssign}]])">
                                            货量更新
                                        </div>
                                        <div class="hl fl ml10"
                                             th:onclick="confirmPayment([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.lot}]],2,[[${entrustlist.carrier.balaType}]],[[${entrustlist.entrust.isFleetData}]],[[${entrustlist.entrust.isFleetAssign}]])">
                                            费用确认</div>
                                        <div class="hl fl ml10"
                                             th:onclick="register([[${entrustlist.entrust.isClose}]],[[${entrustlist.entrust.receiptConfirmFlag}]],[[${entrustlist.entrust.orderno}]],[[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.isFleetData}]])">
                                            费用登记</div>

<!--                                        <div class="fr ml10" style="line-height: 22px">-->
<!--                                            <span>预估毛利：</span>-->
<!--                                            <span class="fc23" id="yg">[[${entrustlist.sumReceiveDetail-entrustlist.transFeeCount-entrustlist.sumOtherFee}]]</span>-->
<!--                                            <span class="label label-danger pa2" th:if="${entrustlist.sumReceiveDetail-entrustlist.transFeeCount-entrustlist.sumOtherFee < 0}">负</span>-->
<!--                                        </div>-->
<!--                                        <div class="fr ml10">-->
<!--                                            <span style="display: inline-block;background: #499c68;padding: 2px 2px;color: #fff">三方</span>-->
<!--                                            <span class="fw" id="sf">￥[[${entrustlist.sumOtherFee}]]</span>-->
<!--                                        </div>-->
<!--                                        <div class="fr ml10">-->
<!--                                            <span style="display: inline-block;background: #61c5c5;padding: 2px 2px;color: #fff">应付</span>-->
<!--                                            <span class="fw" id="yf">￥[[${entrustlist.transFeeCount}]]</span>-->
<!--                                        </div>-->
<!--                                        <div class="fr ">-->
<!--                                            <span style="display: inline-block;background: #f0835c;padding: 2px 2px;color: #fff">应收</span>-->
<!--                                            <span class="fw" id="ys">￥[[${entrustlist.sumReceiveDetail}]]</span>-->
<!--                                        </div> -->
<!--                                        -->
<!--                                        <div class="fr ml10" style="line-height: 22px">-->
<!--                                            <span>预估毛利：</span>-->
<!--                                            <span class="fc23" id="yg">[[${entrustlist.profit}]]</span>-->
<!--                                            <span class="label label-danger pa2" th:if="${entrustlist.profit < 0}">负</span>-->
<!--                                        </div>-->
                                        <div class="fr ml10">
                                            <span style="display: inline-block;background: #499c68;padding: 2px 2px;color: #fff">平台费</span>
                                            <span class="fw" id="ptf">￥[[${entrustlist.ptf}]]</span>
                                        </div>
<!--                                        <div class="fr ml10">-->
<!--                                            <span style="display: inline-block;background: #499c68;padding: 2px 2px;color: #fff">三方税金</span>-->
<!--                                            <span class="fw" id="dsfsf">￥[[${entrustlist.dsfsf}]]</span>-->
<!--                                        </div>-->
                                        <div class="fr ml10">
                                            <span style="display: inline-block;background: #499c68;padding: 2px 2px;color: #fff">三方</span>
                                            <span class="fw" style="display: inline-block; vertical-align: middle;">
                                                <div class="" id="sf" style=" font-size: smaller;">￥[[${entrustlist.dsf}]]</div>
                                                <div class="" id="dsfsf" style=" font-size: smaller;">￥[[${entrustlist.dsfsf}]](税)</div>
                                            </span>
                                        </div>
<!--                                        <div class="fr ml10">-->
<!--                                            <span style="display: inline-block;background: #61c5c5;padding: 2px 2px;color: #fff">应付税金</span>-->
<!--                                            <span class="fw" id="yfsf">￥[[${entrustlist.yfsf}]]</span>-->
<!--                                        </div>-->
                                        <div class="fr ml10">
                                            <span style="display: inline-block;background: #61c5c5;padding: 2px 2px;color: #fff">总应付</span>
                                            <span class="fw" style="display: inline-block; vertical-align: middle;">
                                                <div class="" id="yf" style=" font-size: smaller;">￥[[${entrustlist.yf}]]</div>
                                                <div class="" id="yfsf" style=" font-size: smaller;">￥[[${entrustlist.yfsf}]](税)</div>
                                            </span>

                                        </div>
                                        <div class="fr ">
                                            <span style="display: inline-block;background: #f0835c;padding: 2px 2px;color: #fff">在途应收</span>
                                            <span class="fw" id="ys">￥[[${entrustlist.sumOnWayRece}]]</span>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="row mt10">
                                            <div th:if="${lossResaon != null}" style="padding:0 5px;font-weight: bold;color:red;display: flex">
                                                <div>亏本原因：</div>
                                                <div style="white-space: pre-line;">[[${lossReason}]]</div>
                                            </div>

                                            <div style="padding-left: 0" class="col-md-4 col-sm-4">

                                                <table class="table table-bordered mt10">
                                                    <thead style="background: #f7f8fa">
                                                    <tr>
                                                        <th style="color: #f0835c">应收类型</th>
                                                        <th style="color: #f0835c">应收金额</th>
                                                        <th style="color: #f0835c">应收状态</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr th:each="mapS,status:${entrustlist.receiveDetailList}">

                                                        <td>
<!--                                                            <div class="input-group"  th:if="${mapS.freeType} eq '0'">运费</div>-->
<!--                                                            <div class="input-group"  th:if="${mapS.freeType} eq '1'">在途</div>-->

                                                            <div th:each="costTypeFreight:${@dict.getType('cost_type_freight')}"
                                                                 th:if="${ mapS.freeType == '0' and costTypeFreight.dictValue==mapS.costTypeFreight}"
                                                                 th:text="${costTypeFreight.dictLabel}"></div>
                                                            <div th:each="costTypeOnWay:${@dict.getType('cost_type_on_way')}"
                                                                 th:if="${mapS.freeType == '1' and costTypeOnWay.dictValue==mapS.costTypeOnWay}"
                                                                 th:text="${costTypeOnWay.dictLabel}"></div>

                                                        </td>

                                                        <td>
                                                            <div class="input-group" th:if="${mapS.adjustAmount == null}" th:text="${mapS.transFeeCount}" style="display: inline-block;width: 50%;"></div>
                                                            <div class="input-group" th:if="${mapS.adjustAmount != null}" th:text="${mapS.adjustAmount}" style="display: inline-block;width: 50%;"></div>
                                                            <span th:if="${mapS != null and mapS.params != null and mapS.params['costSpNo'] != null and mapS.params['costSpNo'] != ''}"
                                                                  class="history-icon"
                                                                  th:attr="data-costspno=${mapS.params['costSpNo']}"
                                                                  onclick="wecom_process(this.getAttribute('data-costspno'))"
                                                                  style="cursor: pointer; margin-left: 5px; display: inline-block;">📜</span>
                                                        </td>
                                                        <td>
                                                            <div class="input-group" th:each="statusEnum:${receiveDetailStatusEnum}" th:if="${mapS.vbillstatus} == ${statusEnum.value}"
                                                                 th:text="${statusEnum.context}"></div>
                                                        </td>

                                                    </tr>
                                                    <tr th:if="${entrustlist.sumReceiveDetail == 0}">
                                                        <td colspan="3">暂无在途应收</td>
                                                    </tr>

                                                    </tbody>
                                                </table>
                                            </div>
                                            <div style="padding: 0" class="col-md-4 col-sm-4">
                                                <table class="table table-bordered mt10">
                                                    <thead style="background: #F7F8FA">
                                                    <tr>

                                                        <th style="color: #61c5c5">应付类型</th>
                                                        <th style="color: #61c5c5">应付金额(元)</th>
                                                        <th style="color: #61c5c5">状态</th>
                                                        <th style="color: #61c5c5">操作</th>


                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr th:each="mapS:${entrustlist.payDetailList}">
                                                        <td>
                                                            <div th:each="costTypeFreight:${@dict.getType('cost_type_freight')}"
                                                                 th:if="${ mapS.freeType == '0' and costTypeFreight.dictValue==mapS.costTypeFreight}"
                                                                 th:text="${costTypeFreight.dictLabel}"></div>
                                                            <div th:each="costTypeOnWay:${@dict.getType('cost_type_on_way')}"
                                                                 th:if="${mapS.freeType == '1' and costTypeOnWay.dictValue==mapS.costTypeOnWay}"
                                                                 th:text="${costTypeOnWay.dictLabel}"></div>

                                                            <!--                                                <div class="input-group"  th:if="${mapS.freeType} eq '0'">运费</div>-->
                                                            <!--                                                <div class="input-group"  th:if="${mapS.freeType} eq '1'">在途</div>-->
                                                        </td>

                                                        <td>
                                                            <div class="input-group" th:text="${mapS.transFeeCount}" style="display: inline-block;width: 50%;"></div>
                                                            <span th:if="${mapS != null and mapS.params != null and mapS.params['costSpNo'] != null and mapS.params['costSpNo'] != ''}"
                                                                  class="history-icon"
                                                                  th:attr="data-costspno=${mapS.params['costSpNo']}"
                                                                  onclick="wecom_process(this.getAttribute('data-costspno'))"
                                                                  style="cursor: pointer; margin-left: 5px; display: inline-block;">📜</span>

                                                        </td>
                                                        <td>
                                                            <div class="input-group" th:each="statusEnum:${payDetailStatusEnum}" th:if="${mapS.vbillstatus} == ${statusEnum.value}">
                                                                <span th:if="${mapS.vbillstatus} == 0" class="label label-primary" th:text="${statusEnum.context}"></span>
                                                                <span th:if="${mapS.vbillstatus} == 1" class="label label-warning" th:text="${statusEnum.context}"></span>
                                                                <span th:if="${mapS.vbillstatus} == 2" class="label label-info" th:text="${statusEnum.context}"></span>
                                                                <span th:if="${mapS.vbillstatus} == 3" class="label label-success" th:text="${statusEnum.context}"></span>
                                                                <span th:if="${mapS.vbillstatus} == 4" class="label label-success" th:text="${statusEnum.context}"></span>
                                                                <span th:if="${mapS.vbillstatus} == 5" class="label label-inverse" th:text="${statusEnum.context}"></span>
                                                                <span th:if="${mapS.vbillstatus} == 6" class="label label-warning" th:text="${statusEnum.context}"></span>
                                                                <span th:if="${mapS.vbillstatus} == 7" class="label label-warning" th:text="${statusEnum.context}"></span>
                                                                <span th:if="${mapS.vbillstatus} == 8" class="label label-warning" th:text="${statusEnum.context}"></span>
                                                                <span th:if="${mapS.vbillstatus} == 9" class="label label-info" th:text="${statusEnum.context}"></span>
                                                                <span th:if="${mapS.vbillstatus} == 10" class="label label-warning" th:text="${statusEnum.context}"></span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="btn_yf" style="margin: 0 auto;width: 80px" th:if="${mapS.vbillstatus} == 1"
                                                                 th:onclick="applyPay([[${mapS.vbillstatus}]],[[${mapS.balaMethod}]]
                                                     ,[[${mapS.transFeeCount}]],2,[[${entrustlist.entrust.lot}]]
                                                     ,[[${mapS.freeType}]],[[${mapS.costTypeFreight}]],[[${mapS.payDetailId}]],[[${mapS.consumbleBack}]])"
                                                                 shiro:hasAnyPermissions="trace:confirmPayment:apply,fleet:trace:confirmPayment:apply">
                                                                申请付款</div>
                                                            <div class="btn_yf" style="margin: 0 auto;width: 80px" th:if="${mapS.vbillstatus} == 0"
                                                                 th:onclick="affirm([[${mapS.vbillstatus}]],[[${mapS.payDetailId}]])"
                                                                 shiro:hasAnyPermissions="trace:confirmPayment:comfirm,fleet:trace:confirmPayment:comfirm">
                                                                确认</div>
                                                        </td>

                                                    </tr>
                                                    <tr th:if="${entrustlist.deposit != null && entrustlist.deposit.amount != 0}" th:with="deposit=${entrustlist.deposit}">
                                                        <td>定金</td>
                                                        <td th:text="${deposit.amount}"></td>
                                                        <td><div class="input-group" th:each="statusEnum:${payDetailStatusEnum}" th:if="${deposit.vbillstatus} == ${statusEnum.value}">
                                                            <span th:if="${deposit.vbillstatus} == 0" class="label label-primary" th:text="${statusEnum.context}"></span>
                                                            <span th:if="${deposit.vbillstatus} == 1" class="label label-warning" th:text="${statusEnum.context}"></span>
                                                            <span th:if="${deposit.vbillstatus} == 2" class="label label-info" th:text="${statusEnum.context}"></span>
                                                            <span th:if="${deposit.vbillstatus} == 3" class="label label-success" th:text="${statusEnum.context}"></span>
                                                            <span th:if="${deposit.vbillstatus} == 4" class="label label-success" th:text="${statusEnum.context}"></span>
                                                            <span th:if="${deposit.vbillstatus} == 5" class="label label-inverse" th:text="${statusEnum.context}"></span>
                                                            <span th:if="${deposit.vbillstatus} == 6" class="label label-warning" th:text="${statusEnum.context}"></span>
                                                        </div></td>
                                                        <td>
                                                            <div class="btn_yf" style="margin: 0 auto;width: 80px" th:if="${deposit.vbillstatus == 0 || deposit.vbillstatus == 3}"
                                                                 th:onclick="applyDepositPay([[${deposit.id}]],[[${deposit.vbillstatus}]],[[${deposit.lotSpLock}]],[[${deposit.amount}]],[[${deposit.lotno}]],[[${deposit.lotId}]])"
                                                                 shiro:hasAnyPermissions="trace:confirmPayment:apply,fleet:trace:confirmPayment:apply">
                                                                申请付款</div>
                                                        </td>
                                                    </tr>
                                                    <tr th:if="${entrustlist.transFeeCount == 0 && entrustlist.deposit == null}">
                                                        <td colspan="4">暂无应付</td>
                                                    </tr>

                                                    </tbody>
                                                </table>
                                            </div>
                                            <div style="padding-right: 0" class="col-md-4 col-sm-4">
                                                <div class="">
                                                    <div>
                                                        <table class="table table-bordered mt10">
                                                            <thead style="background: #F7F8FA">
                                                            <tr>

                                                                <th style="color: #499c68">三方类型</th>

                                                                <th style="color: #499c68">三方金额</th>
                                                                <th style="color: #499c68">三方状态</th>

                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            <tr th:each="otherFee:${entrustlist.otherFeeList}">
                                                                <td th:each="dict:${@dict.getType('cost_type_on_way')}"
                                                                    th:if="${dict.dictValue} == ${otherFee.feeType}" th:text="${dict.dictLabel}">
                                                                </td>
                                                                <td>
                                                                    <div class="input-group" th:text="${otherFee.feeAmount}" style="display: inline-block;width: 50%;"></div>
                                                                    <span th:if="${otherFee != null and otherFee.params != null and otherFee.params['costSpNo'] != null and otherFee.params['costSpNo'] != ''}"
                                                                          class="history-icon"
                                                                          th:attr="data-costspno=${otherFee.params['costSpNo']}"
                                                                          onclick="wecom_process(this.getAttribute('data-costspno'))"
                                                                          style="cursor: pointer; margin-left: 5px; display: inline-block;">📜</span>

                                                                </td>
                                                                <td>
                                                                    <div class="input-group"  th:if="${otherFee.vbillstatus} eq 0">新建</div>
                                                                    <div class="input-group"  th:if="${otherFee.vbillstatus} eq 1">已付款</div>
                                                                    <div class="input-group"  th:if="${otherFee.vbillstatus} eq 2">申请</div>
                                                                    <div class="input-group"  th:if="${otherFee.vbillstatus} eq 3">已对账</div>
                                                                </td>
                                                            </tr>
                                                            <tr th:if="${entrustlist.sumOtherFee == 0}">
                                                                <td colspan="3">暂无三方</td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt10">
                                            <div style="padding-left: 0" class="col-md-6 col-sm-6">

                                                <table class="table table-bordered mt10">
                                                    <thead style="background: #f7f8fa">
                                                    <tr>
                                                        <th style="width: 10%;">类别</th>
                                                        <th style="width: 20%;">申请人</th>
                                                        <th style="width: 25%;">费用类型</th>
                                                        <th style="width: 10%;">费用(元)</th>
                                                        <th style="width: 15%;">备注</th>
                                                        <th style="width: 15%;">审核状态</th>
                                                        <th style="width: 10%;">开票类型</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr th:each="entrustCost:${entrustlist.entrustCostList}" th:if="${entrustCost.isLotFee == 0 and entrustCost.checkStatus != 2}">
                                                        <td>在途</td>
                                                        <td th:text="${entrustCost.regUserName}"></td>
                                                        <td th:text="${@dict.getLabel('pay_or_collect',entrustCost.budgetType) + '/' + @dict.getLabel('cost_type_on_way',entrustCost.costType)}"></td>
                                                        <td th:text="${entrustCost.cost}"></td>
                                                        <td th:text="${entrustCost.memo}"></td>

                                                        <td th:if="${entrustCost.checkStatus == 0}"><a style="text-decoration:underline;" th:href="|javascript:wecom_process('${entrustCost.spNo}')|">待审核</a ></td>
                                                        <td th:if="${entrustCost.checkStatus == 2}">审核通过</td>
                                                        <td th:if="${entrustCost.checkStatus == 3}"><a style="text-decoration:underline;" th:href="|javascript:wecom_process('${entrustCost.spNo}')|">审核不通过</a ></td>
                                                        <td th:each="dict : ${@dict.getType('billing_type')}"
                                                            th:if="${dict.dictValue == entrustCost.billingType}"
                                                            th:text="${dict.dictLabel}"></td>
                                                    </tr>
                                                    <tr th:each="entrustCost:${entrustlist.entrustCostList}" th:if="${entrustCost.isLotFee == 1 and entrustCost.checkStatus != 2}">
                                                        <td>三方</td>
                                                        <td th:text="${entrustCost.regUserName}"></td>
                                                        <td th:text="${@dict.getLabel('cost_type_on_way',entrustCost.feeType)}"></td>
                                                        <td th:text="${entrustCost.feeAmount}"></td>
                                                        <td th:text="${entrustCost.memo}"></td>
                                                        <td th:if="${entrustCost.checkStatus == 0}"><a style="text-decoration:underline;" th:href="|javascript:wecom_process('${entrustCost.spNo}')|">待审核</a ></td>
                                                        <td th:if="${entrustCost.checkStatus == 2}">审核通过</td>
                                                        <td th:if="${entrustCost.checkStatus == 3}"><a style="text-decoration:underline;" th:href="|javascript:wecom_process('${entrustCost.spNo}')|">审核不通过</a ></td>
                                                        <td th:each="dict : ${@dict.getType('billing_type')}"
                                                            th:if="${dict.dictValue == entrustCost.billingType}"
                                                            th:text="${dict.dictLabel}"></td>
                                                    </tr>
                                                    <tr th:each="singleAdjustment:${entrustlist.singleAdjustmentList}">
                                                        <td>单笔调帐</td>
                                                        <td th:text="${singleAdjustment.regUserName}"></td>
                                                        <td>
                                                            <div th:each="costTypeFreight:${@dict.getType('cost_type_freight')}"
                                                                 th:if="${ singleAdjustment.freeType == '0' and costTypeFreight.dictValue==singleAdjustment.costTypeFreight}"
                                                                 th:text="${costTypeFreight.dictLabel}"></div>
                                                            <div th:each="costTypeOnWay:${@dict.getType('cost_type_on_way')}"
                                                                 th:if="${singleAdjustment.freeType == '1' and costTypeOnWay.dictValue==singleAdjustment.costTypeOnWay}"
                                                                 th:text="${costTypeOnWay.dictLabel}"></div>
                                                        </td>
                                                        <td th:text="${singleAdjustment.transFeeCount}"></td>
                                                        <td th:text="${singleAdjustment.memo}"></td>
                                                        <td><a style="text-decoration:underline;" th:href="|javascript:wecom_process('${singleAdjustment.spNo}')|">待审核</a ></td>
<!--                                                        <td >待审核</td>-->
                                                        <td th:each="dict : ${@dict.getType('billing_type')}"
                                                            th:if="${dict.dictValue == singleAdjustment.billingType}"
                                                            th:text="${dict.dictLabel}"></td>
                                                    </tr>
                                                    <tr th:each="batchAdjustment:${entrustlist.batchAdjustmentList}">
                                                        <td>批量调帐</td>
                                                        <td th:text="${batchAdjustment.applyUser}"></td>
                                                        <td>
                                                            <div th:each="costTypeFreight:${@dict.getType('cost_type_freight')}"
                                                                 th:if="${ batchAdjustment.freeType == '0' and costTypeFreight.dictValue==batchAdjustment.costTypeFreight}"
                                                                 th:text="${costTypeFreight.dictLabel}"></div>
                                                            <div th:each="costTypeOnWay:${@dict.getType('cost_type_on_way')}"
                                                                 th:if="${batchAdjustment.freeType == '1' and costTypeOnWay.dictValue==batchAdjustment.costTypeOnWay}"
                                                                 th:text="${costTypeOnWay.dictLabel}"></div>
                                                        </td>
                                                        <td th:text="${batchAdjustment.transFeeCount}"></td>
                                                        <td th:text="${batchAdjustment.memo}"></td>
                                                        <td ><a style="text-decoration:underline;" th:href="|javascript:show_process('${batchAdjustment.batchAdjustId}')|">待审核</a ></td>
                                                        <td th:each="dict : ${@dict.getType('billing_type')}"
                                                            th:if="${dict.dictValue == batchAdjustment.billingType}"
                                                            th:text="${dict.dictLabel}"></td>
                                                    </tr>
                                                    <tr th:if="${entrustlist.entrustCostList.size() == 0}">
                                                        <td colspan="5">暂无审核数据</td>
                                                    </tr>

                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-3" style="background: #fff;height: 100%;overflow: scroll;position: absolute;right: 0;">
                                <div class="fw" style="font-size: 18px" th:if="${entrustlist.entrust.carno!=''&&entrustlist.entrust.carno!=undefined}">
                                    [[${entrustlist.entrust.carno}]]/[[${entrustlist.entrust.carLen}]]米[[${entrustlist.entrust.carTypeName}]]</div>
                                <div class="mt10" th:if="${not #lists.isEmpty(entrustlist.tihuowork)}">
                                    <div class="over" th:if="${not #lists.isEmpty(entrustlist.daohuowork)}">
                                        <div class="tjdg fl" th:onclick="tacking([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]],[[${vbillstatus}]])">添加跟踪</div>
                                        <div class="tjdg fl ml10" th:if="${entrustExpCount!=0}" style="color: red" th:onclick="abnormal([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]])">添加异常([[${entrustExpCount}]])</div>
                                        <div class="tjdg fl ml10" th:if="${entrustExpCount==0}" style="color: red" th:onclick="abnormal([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]])">添加异常</div>
                                    </div>
                                    <div class="over" th:if="${#lists.isEmpty(entrustlist.daohuowork)}">
                                        <div class="tjdg fl" th:onclick="tacking([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]],[[${vbillstatus}]])">添加跟踪</div>
                                        <div class="tjdg fl ml10" th:if="${entrustExpCount!=0}" style="color: red" th:onclick="abnormal([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]])">添加异常([[${entrustExpCount}]])</div>
                                        <div class="tjdg fl ml10" th:if="${entrustExpCount==0}" style="color: red" th:onclick="abnormal([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]])">添加异常</div>
                                    </div>
                                </div>
                                <div class="mt10" th:if="${#lists.isEmpty(entrustlist.tihuowork)}">
                                    <div class="over">
                                        <div class="tjdg fl" th:onclick="tacking([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]],[[${vbillstatus}]])">添加跟踪</div>
                                        <div class="tjdg fl ml10" th:if="${entrustExpCount!=0}" style="color: red" th:onclick="abnormal([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]])">添加异常([[${entrustExpCount}]])</div>
                                        <div class="tjdg fl ml10" th:if="${entrustExpCount==0}" style="color: red" th:onclick="abnormal([[${entrustlist.entrust.entrustId}]],[[${entrustlist.entrust.vbillstatus}]],[[${entrustlist.entrust.isFleetData}]])">添加异常</div>
                                    </div>
                                </div>
                                <div class="">
                                    <div th:if="${not #lists.isEmpty(entrustlist.carLocusList)}">
                                        <div class='vertical-timeline-block leftIcon'  th:each="carLocus,stat:${entrustlist.carLocusList}">
                                            <div class='vertical-timeline-icon'>
                                                <img src='/img/pep.png' style='width: 100%;height: 100%'>
                                            </div>
                                            <div class='vertical-timeline-content'>
                                                <div class=''>
                                                    <div class='fc008 f18 textun' th:text="${#dates.format(carLocus.trackingTime, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                                    <div class='mt10' >
                                                        <span class="fw f16">【[[${carLocus.regUserName}]]】</span>
                                                        <span th:text="${carLocus.detailAddr}"></span>
                                                    </div>
                                                    <div class="mt10" style='white-space: nowrap;'>
                                                        <span>预计到达时间：</span>
                                                        <span th:text="${#dates.format(carLocus.estArrivalTime, 'yyyy-MM-dd HH:mm:ss')}"></span>
                                                    </div>
                                                    <div class="mt10" style='white-space: nowrap;'>
                                                        <span>备注：</span>
                                                        <span th:text="${carLocus.trackingMemo}"></span>
                                                    </div>
                                                </div>
                                            </div>



                                        </div>

                                    </div>
                                    <div th:if="${#lists.isEmpty(entrustlist.carLocusList)}" >
                                        <div style="padding: 60px 0">
                                            <div style="width: 100px;height: 150px;margin: 0px auto;">
                                                <img th:src="@{/img/jy.png}" style="width: 100px;height: 150px">
                                            </div>
                                            <div class="tc mt10" style="color: #666666">暂无跟踪记录，请及时跟踪</div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col-sm-offset-5 col-sm-10">
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
                </button>
            </div>
        </div>
    </form>
</div>

<th:block th:include="include :: footer"/>
<script th:src="@{'/js/wecom.js'}"></script>

<script>
    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');

        var options = {};
        $.table.init(options);


    });

    /**
     * 提货作业
     */
    function pick(id, vbillstatus, isFleetData, isFleetAssign) {
        var url
        if (isFleetData == 0) {
            url = ctx + "trace";
        }else {
            url = ctx + "fleettrace";
        }
        //跳转提货作业详情页面
        if (vbillstatus == "已提货" || vbillstatus == "已到货" || vbillstatus == "已回单" || vbillstatus == "关闭") {
            url = url + "/pickDetail/" + id;
            $.modal.openTab("提货作业", url);
            // layer.open({
            //     type: 2,
            //     area: ['70%', '100%'],
            //     fix: false,
            //     //不固定
            //     maxmin: true,
            //     shade: 0.3,
            //     title: "提货作业",
            //     content: url,
            //     btn: ['确定', '关闭'],
            //     // 弹层外区域关闭
            //     shadeClose: true,
            //     yes: function(index, layero) {
            //         var iframeWin = layero.find('iframe')[0];
            //         iframeWin.contentWindow.submitHandler(index, layero);
            //     },
            //     cancel: function(index) {
            //         return true;
            //     }
            // })

        } else if (vbillstatus == "已确认") {
            url = url + "/pick/" + id+"?isRefresh=1&openType=";
            // $.modal.openTab("提货作业", url);

            layer.open({
                type: 2,
                area: ['70%', '100%'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "提货作业",
                content: url,
                btn: ['确定', '关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                yes: function(index, layero) {
                    var iframeWin = layero.find('iframe')[0];
                    iframeWin.contentWindow.submitHandler(index, layero);
                },
                cancel: function(index) {
                    return true;
                }
            })

        } else {
            $.modal.alertWarning("委托单状态需要为已确认才能进行提货作业！");
        }
    }

    /**
     * 跟踪
     */
    function tacking(id, vbillstatus, isFleetData,status) {
        console.log(status)
        //除待确认状态下才可以追踪
        if (vbillstatus == '待确认') {
            $.modal.alertWarning("该状态下无法追踪！");
            return false;
        }

        if (isFleetData == 0) {
            var url = ctx + "trace/tacking/" + id+"/1"+ "/" + status;
        }else {
            var url = ctx + "fleettrace/tacking/" + id+"/1";
        }

        // $.modal.openTab("跟踪", url);

        layer.open({
            type: 2,
            area: ['70%', '100%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "跟踪",
            content: url,
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function(index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function(index) {
                return true;
            }
        })

    }

    /**
     * 异常跟踪
     */
    function abnormal(id, vbillstatus, isFleetData) {
        if (isFleetData == 0) {
            var url = ctx + "trace/abnormal/" + id;
        }else {
            var url = ctx + "fleettrace/abnormal/" + id;
        }

        $.modal.openTab("异常跟踪" , url);
    }

    /**
     *  到货作业
     */
    function arrive(id, vbillstatus, isFleetData) {
        var url
        if (isFleetData == 0) {
            url = ctx + "trace";
        }else {
            url = ctx + "fleettrace";
        }

        //判断勾选一条的情况
        if (vbillstatus == '已到货' || vbillstatus == '已回单' || vbillstatus == '关闭') {
            url = url + "/arrivalDetail/" + id;
            $.modal.openTab("到货作业", url);

            // layer.open({
            //     type: 2,
            //     area: ['70%', '100%'],
            //     fix: false,
            //     //不固定
            //     maxmin: true,
            //     shade: 0.3,
            //     title: "到货作业",
            //     content: url,
            //     btn: ['确定', '关闭'],
            //     // 弹层外区域关闭
            //     shadeClose: true,
            //     yes: function(index, layero) {
            //         var iframeWin = layero.find('iframe')[0];
            //         iframeWin.contentWindow.submitHandler(index, layero);
            //     },
            //     cancel: function(index) {
            //         return true;
            //     }
            // })

        } else if (vbillstatus == '已提货') {
            url = url + "/arrival/" + id +"?isRefresh=1&openType=";
            // $.modal.openTab("到货作业", url);

            layer.open({
                type: 2,
                area: ['70%', '100%'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "到货作业",
                content: url,
                btn: ['到货','到货并回单确认','关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                yes: function(index, layero) {
                    var iframeWin = layero.find('iframe')[0];
                    iframeWin.contentWindow.submitHandler(0);
                    return false;
                },
                btn2: function(index, layero) {
                    var iframeWin = layero.find('iframe')[0];
                    iframeWin.contentWindow.submitHandler(1);
                    return false;
                },
                cancel: function(index) {
                    return true;
                }
            })

        } else {
            $.modal.alertError("委托单状态需要为已提货才能进行到货作业！");
        }
    }

    //回单确认
    function receiptConfirm(id, vbillstatus, isFleetData, isFleetAssign, ifReceipt) {
        //判断委托单状态
        if (vbillstatus == "待确认" || vbillstatus == "已确认" || vbillstatus == "已提货") {
            $.modal.alertWarning("委托单状态需要为已到货状态才能进行回单确认！");
            return false;
        }

        //判断是否回单
        if (ifReceipt == 1) {
            $.modal.alertWarning("委托单已经进行过回单，无法再确认！");
            return false;
        }

        var url
        if (isFleetData == 0) {
            url = ctx + "trace";
        }else {
            url = ctx + "fleettrace";
        }

        //跳转回单确认界面
        url = url + "/receiptConfirm/" + id + "/" + isFleetData + "/" + isFleetAssign+"/1";
        // $.modal.openTab("回单确认", url);
        layer.open({
            type: 2,
            area: ['85%', '100%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "回单确认",
            content: url,
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function(index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function(index) {
                return true;
            }
        })

    }

    /**
     *  正本回单
     * @returns {boolean}
     */
    function receipt(id, vbillstatus, addrType, ifReceipt, receiptConfirmFlag, isFleetData, isFleetAssign) {
        var isFleetIn = [[${isFleetIn}]]

        if (isFleetIn == 1 && isFleetData == 1 && isFleetAssign != 0) {
            $.modal.alertWarning("分配车队的数据无法正本回单！");
            return;
        }
     /*   if (addrType == '3') {
            $.modal.alertWarning("委托单到货地址为终点站时才能进行回单！");
            return false;
        }*/

        if (vbillstatus == '待确认' || vbillstatus == '已确认' || vbillstatus == '已提货') {
            $.modal.alertWarning("委托单状态需要为已到货状态才能进行回单！");
            return false;
        }



        // if (ifReceipt == '1') {
        //     $.modal.alertWarning("请勾选单条查看回单信息");
        //     return false;
        // }

        if (receiptConfirmFlag == '0') {
            $.modal.alertWarning("请先回单确认，再进行正本回单！");
            return false;
        }

        var url
        if (isFleetData == 0) {
            url = ctx + "trace";
        } else {
            url = ctx + "fleettrace";
        }

        var url = url + "/receipt/" + id + "?monthFlag=1&isRefresh=1";
        // $.modal.openTab("回单", url);
        layer.open({
            type: 2,
            area: ['85%', '100%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: "回单",
            content: url,
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            yes: function(index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function(index) {
                return true;
            }
        })

    }

    /**
     *  打开详情也
     */
    function openDetailTab(carLocusId) {
        $.modal.open("详情", ctx + "trace/tacking/detail/" + carLocusId);
    }

    /**
     * 费用确认
     * @returns {boolean}
     */
    function confirmPayment(entrustId,lot,addrType,carrBalaType,isFleetData, isFleetAssign){
        //运单号
        if(carrBalaType == null || carrBalaType == "" || carrBalaType == "undefined"){
            $.modal.alertWarning("该承运商结算方式为空，无法进行费用确认！");
            return false;
        }

        var url
        if (isFleetData == 0) {
            url = ctx + "trace";
        } else {
            url = ctx + "fleettrace";
        }
        var url = url + "/confirmPayment/"+lot+"/"+entrustId+"/"+carrBalaType+"/" +addrType+"/"+isFleetAssign;
        $.modal.openTab("费用确认", url);
    }

    /**
     * 货量更新
     */
    function cargo(id, orderno, isClose, isFleetData, isFleetAssign) {

        $.ajax({
            type: "POST",
            url: ctx + "trustDeed/checkCargo?entrustId=" + id,
            async: false,
            success: function (r) {
                if (r.code != 0) {
                    $.modal.alertError(r.msg);
                    return false;
                } else {
                    $.modal.openTab("货量更新", ctx + "trustDeed/cargoNew?entrustId=" + id + "&isFleetData=" + isFleetData + "&isFleetAssign=" + isFleetAssign);
                }
            }
        });

        //验证发货单是否超过五天
        /*$.ajax({
            type: "POST",
            url: ctx + "invoice/checkInvoiceOverDate?invoiceId=" + orderno,
            async: false,
            success: function (r) {
                if (r.code != 0) {
                    $.modal.alertError(r.msg);
                    return false;
                } else {
                    //验证是否可以货量更新
                    $.ajax({
                        type: "POST",
                        url: ctx + "trustDeed/checkCargo?entrustId=" + id,
                        async: false,
                        success: function (r) {
                            if (r.code != 0) {
                                $.modal.alertError(r.msg);
                                return false;
                            } else {
                                $.modal.open("货量更新", ctx + "trustDeed/cargo?entrustId=" + id + "&isFleetData=" + isFleetData + "&isFleetAssign=" + isFleetAssign);
                            }
                        }
                    });
                }
            }
        });*/
    }

    /**
     * 申请付款
     */
    function applyPay(vbillstatus,balaMethod,transFeeCount,addrType,lot,freeType,costTypeFreight,payDetailId,consumbleBack) {
        if (vbillstatus !== 1) {
            $.modal.alertWarning("请选择已确认的应付单");
            return;
        }

        if(balaMethod == null){
            $.modal.alertWarning("请在基础承运商维护结算方式");
            return;
        }
        console.log("balaMethod:"+balaMethod);
        console.log("costTypeFreight:"+costTypeFreight);
        if (balaMethod == 2) {
            if(costTypeFreight != 1 && costTypeFreight != 3 && costTypeFreight != 5){
                $.modal.alertWarning("该应付单下的承运商未选择单笔付款");
                return;
            }
        }
        if (transFeeCount < 0) {
            $.modal.alertWarning("为负数的应付单请加入对账付款");
            return;
        }

        if (consumbleBack === 0) {
            $.modal.alertWarning("应付单据存在耗材费用!");
            return;
        }

        var flag = false;
        var msg = "";
        //单笔到付现金或者到付油卡 提交付款申请，必须回单确认之后才可提交
        if (balaMethod == 1) {
            if (freeType == 0) {
                if (costTypeFreight == 2 || costTypeFreight == 3 || costTypeFreight == 4 ||  costTypeFreight == 5) {
                    //验证运单下委托单是否都确认
                    $.ajax({
                        type: "POST",
                        url: ctx + "payDetail/checkEntrustReceiptConfirm?lot="+lot,
                        async: false,
                        success: function(r){
                            if(r.code != 0){
                                msg = r.msg;
                                flag = true;
                            }
                        }
                    });
                }
                if (costTypeFreight == 4 ||  costTypeFreight == 5) {
                    $.ajax({
                        type: "POST",
                        url: ctx + "payDetail/checkEntrustIfReceipt?lot="+lot,
                        async: false,
                        success: function(r){
                            if(r.code != 0){
                                msg = '回付款必须正本回单之后才可提交';
                                flag = true;
                            }
                        }
                    });
                }
            }
        }

        if(flag){
            $.modal.alertWarning(msg);
            return false;
        }

        //验证是否可以付款
        $.ajax({
            type: "POST",
            url: ctx + "payDetail/checkAllowPay?payDetailId="+payDetailId,
            async: false,
            success: function(r){
                if(r.code == 500){
                    $.modal.alertError(r.msg);
                    return false;
                }else if(r.code == 301){
                    //拆分应付
                    $.modal.confirm(r.msg, function () {
                        var url = ctx + "payDetail/splitPayDetail";
                        var data = {"payDetailId": payDetailId,"deposit":r.data};
                        $.operate.submit(url, "post", "json", data);
                    });
                }else{
                    //验证是否存在异常记录
                    $.ajax({
                        type: "POST",
                        url: ctx + "payDetail/checkEntrustExp?payDetailIds="+payDetailId,
                        async: false,
                        success: function(r){
                            if(r.code != 0){
                                $.modal.alertError(r.msg);
                                return false;
                            }else{
                                var url = ctx + "payDetail/applyPay?payDetailId="+payDetailId;
                                $.modal.open("申请付款", url,600);
                            }
                        }
                    });
                }
            }
        });
    }

    function applyDepositPay(id,vbillstatus,lotSpLock,amount,lot,lotId) {
        if (vbillstatus !== 0 && vbillstatus !== 3) {
            $.modal.alertWarning("请选择新建状态下的定单");
            return;
        }
        if (lotSpLock == 1) {
            $.modal.alertWarning("运费调度主管审核中，无法操作");
            return;
        }
        var flag = false;
        var msg = "";
        function checkEntrustReceiptConfirm(msgx) {
            //验证运单下委托单是否都确认
            $.ajax({
                type: "POST",
                url: ctx + "payDetail/checkEntrustReceiptConfirm?lot="+lot,
                async: false,
                success: function(r){
                    if(r.code != 0){
                        msg = msgx;
                        flag = true;
                    }
                }
            });
        }
        function checkEntrustIfReceipt(msgx) {
            $.ajax({
                type: "POST",
                url: ctx + "payDetail/checkEntrustIfReceipt?lot="+lot,
                async: false,
                success: function(r){
                    if(r.code != 0){
                        msg = msgx;
                        flag = true;
                    }
                }
            });
        }
        // 判断是否需要回单并且是末端段
        $.ajax({
            url: ctx + "payDetail/judgeNeedReceipt?lot=" + lot,
            async: false,
            cache: false,
            success: function(r) {
                if (r.code == 0) {
                    if (r.data === 1) {// 是：判断正本回单
                        checkEntrustIfReceipt('该定金需【正本回单】后才可申请付款');
                    } else if (r.data === 0) {// 否：判断回单确认
                        checkEntrustReceiptConfirm('该定金需【回单确认】后才可申请付款');
                    }
                } else {
                    msg = r.msg;
                    flag = true;
                }
            },
            error: function (err) {
                flag = true;
                if (err.responseJSON && err.responseJSON.message) {
                    msg = err.responseJSON.message
                } else {
                    msg = err.responseText;
                }
            }
        })


        if (flag){
            $.modal.alertWarning(msg);
            return false;
        }

        var pass = false;
        $.ajax({
            url: ctx + 'check/exp/checkLockLot',
            data: "lotId="+ lotId,
            type: "post",
            async: false,
            success: function(r){
                if (r.code == 0) {
                    pass = true;
                } else {
                    $.modal.alertError(r.msg);
                }
            }
        })
        if (!pass) {
            return
        }
        var url = ctx + "new-deposit/applyPay?id=" + id;
        $.modal.openCallAfterSubmit("申请付款", url, 700, null, function(result) {
            if(result.code == 0) {
                location.reload()
            }
        });
    }

    /**
     * 费用登记
     * @returns {boolean}
     */
    function register(isClose,receiptConfirmFlag,invoiceId,entrustId,isFleetData) {

        //关账判断
       /* if(isClose == 1){
            $.modal.alertWarning("该月份已关账，无法进行操作！");
            return false;
        }*/

        if(receiptConfirmFlag == 1){
            var url = ctx + "trace/adjust/"+entrustId;
            // $.modal.open("费用调整", url,'1000');
            layer.open({
                type: 2,
                area: ['1000px', ($(window).height() - 50)+'px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "费用调整",
                content: url,
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });

            return false;
        }

        var url;
        if(isFleetData == 0) {  //0不是  1是
            //alert("不是车队数据")  跳转TraceController
            url = ctx + "trace/register/"+entrustId+"/1";
        }else {
            //alert("车队数据") 跳转FleetTraceController
            url = ctx + "fleettrace/register/"+entrustId+"/1";
        }

        $.modal.open("费用登记", url,'1200');

        //验证发货单是否超过五天
        /* $.ajax({
             type: "POST",
             url: ctx + "invoice/checkInvoiceOverDate?invoiceId="+invoiceId,
             async: false,
             success: function(r){
                 if(r.code != 0){
                     $.modal.alertError(r.msg);
                     return false;
                 }else{
                     $.modal.open("费用登记", url);
                 }
             }
         });*/
    }

    /**
     * 确认应付明细
     */
    function affirm(vbillstatus,payDetailId) {
        if (vbillstatus !== 0) {
            $.modal.alertWarning("应付单据只能为新建状态下才能确认");
            return;
        }
        if (payDetailId.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.ajax({
            url:ctx + "payDetail/checkIfLock?payDetailId="+payDetailId,
            type:"get",
            dataType:"json",
            success: function (result) {
                if(result.code == 0){
                    $.modal.confirm("是否确认？", function () {
                        $.operate.post(ctx + "payDetail/affirm", {"payDetailIds": payDetailId});
                    });
                }else{
                    $.modal.alertError(result.msg);
                }
            }
        });

    }

    function show_process(adjustId) {
        var formHtml = []
        formHtml.push("<table class='table'>")
        formHtml.push("<thead>")
        formHtml.push("<tr><th style='width: 40px'>№</th><th>审批节点</th><th>审批人</th><th>审批时间</th><th>审批意见</th></tr>")
        formHtml.push("</thead>")
        formHtml.push("<tbody id='spRecordTrs'>")
        formHtml.push("</tbody></table>");
        layer.open({
            type: 1,
            title: '审批进度',
            content: formHtml.join(''),
            area: ['800px', '300px'],
            fix: false,
            maxmin: false,
            shade: 0.3,
            zIndex: 4,
            btn: ['', '关闭'],
            success: function (layero, index) {
                layero.find('.layui-layer-btn0').hide() // 隐藏第一个按钮
                $.ajax({
                    url: '/batch-adjust/processOfAdjust?batchId=' + adjustId,
                    cache: false,
                    success: function (result) {
                        if (result.code == 0) {
                            // assign:null,beginTime:'yyyy-MM-dd hh:mm:ss',comment:'等待liuming审批...',taskName:'',user:null
                            console.log(result.data)
                            let tmp = [];
                            for (let i = 0; i < result.data.length; i++) {
                                tmp.push("<tr><td>",i+1,"</td><td>",result.data[i].taskName,"</td><td>",result.data[i].user||result.data[i].assign,"</td><td>",result.data[i].time,"</td><td>",result.data[i].comment,"</td></tr>");
                            }
                            $('#spRecordTrs').html(tmp.join(''))
                        } else {
                            $('#spRecordTrs').html("<tr><td colspan='5'>"+result.msg+"</td></tr>")
                        }
                    }
                })
            }
        })

    }

</script>
</body>

</html>