<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('付款油卡申请')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
</head>
<style>
    .felx{
        display: flex;
        align-items: center;
    }
    .piao{
        padding: 8px 30px 8px 10px;;
        background: url("/img/kd.png") no-repeat center 100%/100%;
    }
    .ed5565{
        color: #D61D1D;
    }
    .ml10{
        margin-left: 10px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-payAppl-add" class="form-horizontal" novalidate="novalidate">
        <!--对账单id-->
        <input name="payCheckSheetId" type="hidden" th:value="${payCheckSheet?.payCheckSheetId}">
        <!--承运商-->
        <input name="carrierId" type="hidden" th:value="${payCheckSheet?.carrierId}">
        <!--是否为油卡申请-->
        <input name="isOil" type="hidden" value="1">

        <div class="panel-group" id="accordion" th:if="${maxAmount > 0}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">申请信息&nbsp;&nbsp;&nbsp;  铭源总应付：[[${MYSum}]]&nbsp;&nbsp;&nbsp;  吉华总应付：[[${JHSum}]]&nbsp;&nbsp; (金额若有偏差请手动核实)</a>

                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">申请金额：</label>
                                    <input name="totalAmount" id="money" type="hidden" th:value="${maxAmount}" class="form-control">
                                    <!--不符合无车承运人-->
                                    <div class="col-sm-8" th:if="${payCheckSheet.isNtocc == 0}">
                                        <input name="payAmount" id="payAmount" type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this),onReality(this)" required  maxlength="15" th:value="${maxAmount}" class="form-control">
                                    </div>
                                    <!--符合无车承运人-->
                                    <div class="col-sm-8" th:if="${payCheckSheet.isNtocc == 1}">
                                        <input name="payAmount" id="payAmount" type="text"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this),onReality(this)" required  maxlength="15"
                                               th:value="${maxAmount}"
                                               class="form-control" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <div class="felx ed5565">
                                        <div class="piao">
                                            <div th:text="${carrier.advancePayMoney}"></div>
                                        </div>
                                        <div class="ml10">
                                            <div>实际支付 <span id="reality">0</span> </div>
                                            <div>系统会优先抵扣预付款</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" style="color: red">申请日期：</label>
                                    <div class="col-sm-8">
                                        <input name="payDate" id="payDate" class="form-control" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付款类型：</label>
                                    <div class="col-sm-8">
                                        <input name="payType" class="form-control" value="0" type="hidden">
                                        <input class="form-control" value="对账付款" disabled >
                                    </div>
                                </div>
                            </div>

                            
                        </div>
                        <div class="row">

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 cardLabel" style="color: red" >油卡卡号：</label>
                                    <div class="col-sm-8">
                                        <input id="fuelCard" name="fuelCard" class="form-control" placeholder=""
                                               type="text" maxlength="20" autocomplete="off" required>
                                        <input id="oilAccount" name="oilAccount" class="form-control" type="hidden">

                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款账户：</label>
                                    <div class="col-sm-8">
                                        <select name="recAccount" id="recAccount" class="form-control" >
                                            <option value=""></option>
                                            <option th:each="dict : ${carrBankList}"
                                                    th:text="${dict.bankAccount}"
                                                    th:id="${dict.carrBankId}"
                                                    th:value="${dict.carrBankId}">
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">收款卡号：</label>
                                    <div class="col-sm-8">
                                        <input name="recCardNo" id="recCardNo" class="form-control"  type="text" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >收款银行：</label>
                                    <div class="col-sm-8">
                                        <input name="recBank" id="recBank" class="form-control"  type="text" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">支付凭证：</label>
                                    <div class="col-md-11 col-sm-10">
                                        <input id="image" class="form-control" name="image" type="file" multiple >
                                        <input id="tid" name="tid" type="hidden">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">票据备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="checkRemark" maxlength="250" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label class="col-md-1 col-sm-2">备注：</label>
                                    <div class="col-md-11 col-sm-10">
                                            <textarea name="memo" maxlength="250" class="form-control valid"
                                                      rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseThree">申请记录</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseThree">
                <div class="panel-body">
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabThree" class="custom-tab table" >

                            <thead>
                            <tr>
                                <th>申请单据号</th>
                                <th>申请金额</th>
                                <th>申请日期</th>
                                <th>油卡卡号</th>
                                <th>票据备注</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>

                            </thead>
                            <tbody>

                            <tr th:each="paySheetRecord:${paySheetRecordList}">
                                <td th:text="${paySheetRecord.vbillno}" style="text-align: left"></td>
                                <td th:text="￥+${#numbers.formatDecimal(paySheetRecord.payAmount,1,'COMMA',2,'POINT')}" style="text-align: right"></td>
                                <td th:text="${#dates.format(paySheetRecord.payDate, 'yyyy-MM-dd HH:mm:ss')}" style="text-align: left"></td>
                                <td th:text="${paySheetRecord.fuelCard}" style="text-align: left"></td>
                                <td th:text="${paySheetRecord.checkRemark}" style="text-align: left"></td>
                                <td th:text="${paySheetRecord.memo}" style="text-align: left"></td>
                                <td><a class="btn btn-warning btn-rounded btn-xs" th:onclick="prints([[${paySheetRecord.paySheetRecordId}]])"><i class="fa fa-pencil"></i>&nbsp;打印</a></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>

    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary"  onclick="submitHandler()" th:if="${maxAmount > 0}"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger"  onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "payCheckSheet";
    //最大申请金额
    var money = parseFloat($("#money").val());
    //默认当日日期
    var time = new Date();
    var day = ("0" + time.getDate()).slice(-2);
    var month = ("0" + (time.getMonth() + 1)).slice(-2);
    var h=time.getHours();       //获取当前小时数(0-23)
    var m=time.getMinutes();     //获取当前分钟数(0-59)
    var s=time.getSeconds();     //获取当前秒数
    var today = time.getFullYear() + "-" + (month) + "-" + (day) + " "+h+":" +m+ ":"+s;
    $("#payDate").val(today);

    $(function () {
        $('#collapseOne').collapse('show');
        $('#collapseThree').collapse('show');
        /** 校验 */
        $("#form-payAppl-add").validate({
            focusCleanup: true
        });
        //油卡添加校验
        $("#fuelCard").rules("add", {
            remote: {
                url: ctx + "basic/fuelCard/getOilCardNumber",
                type: "post",
                dataType: "json",
                data: {
                    fuelcardNo : function() {
                        return $.common.trim($("#fuelCard").val());
                    }
                },
                dataFilter: function(data, type) {
                    if (data != '') {
                        $("#oilAccount").val(data);
                        return true;
                    } else {
                        return false;
                    }
                }
            },
            messages: {
                remote: "无效油卡",
            }
        });
        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#payDate',
                type: 'datetime',
                trigger: 'click'
            });
        });

        var picParam = {
            maxFileCount:0,
            publish: "uploadSuccess",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        var tid = "tid";
        var imageId = "image";
        $.file.initAddFiles(imageId, tid, picParam);

        // 图片上传成功后
        $("#image").on('filebatchuploadsuccess', function (event, data) {
            var tid = data.response.tid;
            $("#tid").val(tid);
            submit();
        });
        $('#image').on('filesuccessremove', function(event, key, index) {//filepreremove（未上传文件删除前）、fileremoved（未上传文件删除后）
            console.log(event, key, index)
            //return false; 阻止删除
            // 可根据index和tid（或配合filebatchuploadsuccess的回调参数data）删除后台数据，根据后台返回剩余附件数，控制是否清空tid，当前采用直接清空所有的方式
            $('#image').fileinput('clear');
            $('#tid').val('');
        });

        //加载收款信息
        $('#recAccount').change(function(){
            var id = $(this).find(":selected").attr("value");
            if(id == ""){
                $("#fuelCard").attr("required","true");
                $(".cardLabel").attr("style","color: red");
                $("#recCardNo").val("");
                $("#recBank").val("");
                $("#bankNo").val("");
            }else{
                var data = {carrBankId:id};
                var url = ctx + "payManage/selectReceInfo";
                $.ajax({
                    url : url,
                    method : 'POST',
                    data : data,
                    success:function (data) {
                        $("#recCardNo").val(data.bankCard);
                        $("#recBank").val(data.bankName);
                        $("#bankNo").val(data.bankNo);

                        //手动校验
                        $("#form-payAppl-add").validate().element($("#recCardNo"));
                        $("#form-payAppl-add").validate().element($("#recBank"));

                        //油卡非必填
                        $("#fuelCard").removeAttr("required");
                        $(".cardLabel").removeAttr("style");
                    }
                })
            }


        });


        let maxAmount=[[${maxAmount}]];
        if(maxAmount){
            let advancePayMoney=[[${carrier.advancePayMoney}]];
            let num=0;
            if(parseFloat(maxAmount) > parseFloat(advancePayMoney) ){
                num= parseFloat(maxAmount) - parseFloat(advancePayMoney);
            }else{
                num=0;
            }

            $("#reality").text(num); 
        }
    });

    function onReality(obj) {
        let val =$(obj).val();
        
        let advancePayMoney=[[${carrier.advancePayMoney}]];
        let num=0;
        if(parseFloat(val) > parseFloat(advancePayMoney) ){
            num= parseFloat(val) - parseFloat(advancePayMoney);
        }else{
            num=0;
        }

        $("#reality").text(num);
    }
    //提交
    function submitHandler() {
        var payAmount = parseFloat($("#payAmount").val());
        if(payAmount>money){
            $.modal.alertError("付款金额必须<="+money);
            return ;
        }
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗?", function () {
                if($('#image').fileinput('getFilesCount') == 0 ) {
                    submit();
                } else if ($('#image').fileinput('getFilesCount') > 0) {
                    // 表单提交
                    $.modal.loading("正在处理中，请稍后...");
                    $("#image").fileinput('upload');
                }
            });
        }
    }

    function submit(){
        //移除disabled
        $('#recCardNo').removeAttr("disabled");
        $('#recBank').removeAttr("disabled");
        $('#payAmount').removeAttr("disabled");
        $.operate.saveTab(prefix + "/savePayAppl", $('#form-payAppl-add').serialize(),function (r) {
            if (r.code != 0) {
                $('#recCardNo').attr("disabled", true);
                $('#recBank').attr("disabled", true);
                $('#payAmount').attr("disabled", true);
            }
        });
    }


    function prints(paySheetRecordId){
        var url =  prefix + "/payPrintFee?paySheetRecordId="+paySheetRecordId;
        $.modal.openTab('月度对账费用申请单',url);
    }

</script>
</body>
</html>