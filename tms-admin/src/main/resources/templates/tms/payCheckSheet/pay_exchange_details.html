<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商往来明细')"/>
</head>

<body>
<div class="form-content">
    <form id="form-user-add" class="form-horizontal" novalidate="novalidate">

        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">往来明细</a>
                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTab" class="custom-tab table"  >
                                <thead>
                                <tr>
                                    <th style="width: 8%;text-align: left">单据日期</th>
                                    <th style="width: 8%;text-align: left">单据编号</th>
                                   <!-- <th style="width: 8%;text-align: left">单据类型</th>-->
                                    <th style="width: 10%;text-align: left">期初金额</th>
                                    <th style="width: 12%;text-align: left">本期增加</th>
                                    <th style="width: 8%;text-align: left">本期减少</th>
                                    <th style="width: 8%;text-align: left">余额</th>

                                </tr>
                                </thead>
                                <tbody>

                                <tr th:each="payDealings,payDealingsStat: ${payDealingsList}">
                                    <td style="text-align: left">-</td>
                                    <td style="text-align: left">-</td>
                                    <td style="text-align: right" th:text="￥+${#numbers.formatDecimal(payDealings.startMoney,1,'COMMA',2,'POINT')}" id="startMoney"></td>
                                    <td style="text-align: right" th:text="￥+${#numbers.formatDecimal(payDealings?.addMoney,1,'COMMA',2,'POINT')}" id="addMoney"></td>
                                    <td style="text-align: right" th:text="￥+${#numbers.formatDecimal(payDealings?.reduceMoney,1,'COMMA',2,'POINT')}" id="reduceMoney"></td>
                                    <td style="text-align: right" th:text="￥+${#numbers.formatDecimal(payDealings?.balanceMoney,1,'COMMA',2,'POINT')}" id="balanceMoney"></td>
                                </tr>

                                <tr th:each="payDealingsDetail,payDealingsDetailStat : ${payDealingsDetailList}">
                                    <td style="text-align: left" th:text="${#dates.format(payDealingsDetail.vbillDate,'yyyy-MM-dd')}"></td>
                                    <td style="text-align: left" th:text="${payDealingsDetail.vbillno}"></td>
                                    <td style="text-align: right">-</td>
                                    <td style="text-align: right" th:text="￥+${#numbers.formatDecimal(payDealingsDetail.billAdd,1,'COMMA',2,'POINT')}"></td>
                                    <td style="text-align: right" th:text="￥+${#numbers.formatDecimal(payDealingsDetail.billReduce,1,'COMMA',2,'POINT')}"></td>
                                    <td style="text-align: right">-</td>
                                </tr>


                                <tr>
                                    <th style="width: 8%;text-align: left">合计</th>
                                    <td style="text-align: left">-</td>
                                    <td id="sumStartMoney" style="text-align: right"></td>
                                    <td id="sumAddMoney" style="text-align: right" th:if="${sumAdd != null}" th:text="￥+${#numbers.formatDecimal(sumAdd,1,'COMMA',2,'POINT')}"></td>
                                    <td id="sumReduceMoney" style="text-align: right" th:if="${sumReduce != null}" th:text="￥+${#numbers.formatDecimal(sumReduce,1,'COMMA',2,'POINT')}"></td>
                                    <td id="sumBalanceMoney" style="text-align: right"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>

        </div>

        <div class="row">
            <div class="col-sm-offset-5 col-sm-10">
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
                </button>
            </div>
        </div>

    </form>
</div>


<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    //期初金额
    var startMoney = $("#startMoney").text();
    //总计期初金额
    $("#sumStartMoney").text(startMoney);

    //余额
    var balanceMoney = $("#balanceMoney").text();
    $("#sumBalanceMoney").text(balanceMoney);


   /* //往来明细
    var payDealingsDetailList = [[${payDealingsDetailList}]];
    for(var i=0;i<payDealingsDetailList.length;i++){
        if(isNaN(parseFloat(payDealingsDetailList[i].billAdd))){
            payDealingsDetailList[i].billAdd= 0;
        }
        if(isNaN(parseFloat(payDealingsDetailList[i].billReduce))){
            payDealingsDetailList[i].billReduce = 0;
        }
        addMoney += parseFloat(payDealingsDetailList[i].billAdd);
        reduceMoney += parseFloat(payDealingsDetailList[i].billReduce);
    }
    //合计
    $("#sumAddMoney").text(addMoney);
    $("#sumReduceMoney").text(reduceMoney);*/

    $(function () {
        $('#collapseOne').collapse('show');
    });

</script>
</body>

</html>