<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .table-striped {
        height: calc(100% - 80px);
    }

    .ml10 {
        margin-left: 10px;
    }

    .ml20 {
        margin-left: 20px;
    }

    .over {
        overflow: hidden;
    }

    .fl {
        float: left;
    }

    .fr {
        float: right;
    }

    .fw {
        font-weight: bold;
    }

    .fc1a {
        color: #1a1a1a;
    }

    .fc80 {
        color: #808080;
    }

    .mt10 {
        margin-top: 10px;
    }

    .mt20 {
        margin-top: 20px;
    }

    .table th,
    .table td {
        text-align: center;
    }

    .input-group {
        width: 100%;
        text-align: center;
    }

    .round {
        width: 50px;
        height: 50px;
        /*text-align: center;*/
        /*border-radius: 50%;*/
        /*line-height: 50px;*/
        box-sizing: border-box;
        /*background: url('../../../img/pep.png') no-repeat center;*/
        /*background-size: 60px 60px;*/
    }

    .line {
        width: 1px;
        min-height: 20px;
        max-height: 50px;
        border-left: 2px dashed #cdcdcd;
        margin-left: 25px;
    }

    .fc00a {
        color: #00A9FF;
    }

    .fcff8 {
        color: #FF8900;
    }

    .th {
        /*border: 6px solid #00A9FF;*/
        /*color: #00A9FF;*/
    }

    .dh {
        /*border: 6px solid #FF8900;*/
        /*color: #FF8900;*/
    }

    .padt20 {
        padding: 20px 0;
    }

    .warp {
        background: #f2f9ff;
        padding: 10px 20px;
        border: 1px solid #e7eaec;
        border-bottom: 0;
    }

    .printitle {
        font-size: 16px;
        line-height: 30px;
        text-align: center;
    }

    .printtable .table tbody tr td {
        font-size: 12px;
    }
    .lot td {
        text-align: left;
    }
    .table>tbody>tr>td{
        border-top: 0 !important;
    }
    .table>tbody+tbody {
        border-top: 1px solid #ddd!important;
    }
    .qd{
        background: #fffcd3;
        padding: 3px 6px;
    }
    .qd_title{
        font-weight: bold;
        line-height: 24px;
    }
    .qd>table>thead>tr>th{
        background: #eff3f9 !important;
        line-height: 24px !important;
        height: 24px;
        padding: 0;
    }
    .qd>table>tbody>tr>td{
        background-color: #fff !important;
        line-height: 24px !important;
        height: 24px;
        padding: 0 !important;
        text-align: center;
        border-bottom: 0;
    }
</style>

<body class="">
    <div class="form-content">
        <a class="btn btn-primary" onclick="dayin()">
            <i class="fa fa-print"></i> 打印
        </a>
        <div>
            <div class="mt20">
                <div class="mt10" id='div_print'>
                    <div class="fw printitle">月度对账费用申请单</div>
                    <div class="row" style="margin: 0 -10px 0 -10px;">
                        <div class="col-sm-7">
                            <div class="panel panel-default">
                                <div class="panel-collapse collapse in" id="collapseThree">
                                    <div class="panel-body">
                                        <div class="padt20">
                                            <div class="over">
                                                <div class="fl">月份：<span class="fw">[[${payCheckSheet.year}]]年[[${payCheckSheet.month}]]月</span>
                                                </div>
                                                <div class="fl ml20">承运商：<span class="fw">[[${payCheckSheet.carrName}]]</span></div>
                                                <div class="fl ml20">总金额：<span class="fw">[[${paySheetRecord.payAmount}]]元</span>（<span th:if="${paySheetRecord.isOil == 0}">现金</span>
                                                    <span th:if="${paySheetRecord.isOil == 1}">油卡</span> ）</div>
                                            </div>
                                            <div class="mt10">
                                                申请备注：<span>[[${paySheetRecord.memo}]]</span>
                                            </div>

                                            <div class="over mt20">
                                                <div class="fl printtable" style="width: 100%">
                                                    <table class="table table-bordered">
                                                        <thead style="background: #F7F8FA">
                                                            <tr>
                                                                <th >申请单号</th>
                                                                <th >申请类型</th>
                                                                <th >金额</th>
                                                                <th >收款账户</th>
                                                                <th >收款卡号</th>
                                                                <th >收款银行</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>
                                                                    <div class="input-group">[[${paySheetRecord.vbillno}]]</div>
                                                                </td>
                                                                <td>
                                                                    <div class="input-group" th:if="${paySheetRecord.isOil == 0}">现金</div>
                                                                    <div class="input-group" th:if="${paySheetRecord.isOil == 1}">油卡</div>
                                                                </td>
                                                                <td>
                                                                    <div class="input-group">[[${paySheetRecord.payAmount}]]元</div>
                                                                </td>
                                                                <td>
                                                                    <div class="input-group"> [[${paySheetRecord.recAccount}]]</div>
                                                                </td>
                                                                <td>
                                                                    <div class="input-group"> [[${paySheetRecord.recBank}]]</div>
                                                                </td>
                                                                <td>
                                                                    <div class="input-group"> [[${paySheetRecord.recCardNo}]]</div>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>

<!--                                                <div class="fl printtable ml10" style="width: 200px;" th:if="${paySheetRecord.isOil == 1}">-->
<!--                                                    <table class="table table-bordered">-->
<!--                                                        <thead style="background: #F7F8FA">-->
<!--                                                            <tr>-->
<!--                                                                <th>油卡卡号</th>-->
<!--                                                            </tr>-->
<!--                                                        </thead>-->
<!--                                                        <tbody>-->
<!--                                                            <tr>-->
<!--                                                                <td>-->
<!--                                                                    <div class="input-group">[[${paySheetRecord.fuelCard}]]</div>-->
<!--                                                                </td>-->
<!--                                                            </tr>-->
<!--                                                        </tbody>-->
<!--                                                    </table>-->
<!--                                                </div>-->
                                            </div>

                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="col-sm-5" th:if="${not #lists.isEmpty(businessChecks)}">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#accordion">审核记录</a>
                                    </h4>
                                </div>
                                <div class="panel-collapse collapse in" id="collapseTwo">
                                    <div class="panel-body">
                                        <div class="padt20">
                                            <div class="over"  th:each="businessCheck:${businessChecks}">
                                                <div class="fl" style="width: 50px;height: 100%">
                                                    <div class="round th">
                                                        <img th:src="@{/img/pep.png}" style="width: 50px;height: 50px">
                                                    </div>
                                                    <!--<div class="line"></div>-->
                                                </div>
                                                <div class="fr over ml20" style="width: calc(100% - 70px)">
                                                    <div class="fl" style="width: 140px">
                                                        <div class="over">
                                                            <div class="fl fc00a">
                                                                [[${businessCheck.checkMan}]]&nbsp;</div>
                                                            <div class="fc00a fl">
                                                                <span th:if="${businessCheck.checkStatus == 0}">待审核</span>
                                                                <span th:if="${businessCheck.checkStatus == 1}">审核通过</span>
                                                                <span th:if="${businessCheck.checkStatus == 2}">未通过</span>
                                                                <span th:if="${businessCheck.checkStatus == 3}">财务未通过</span>
                                                            </div>
                                                        </div>
                                                        <div class="mt10 fc80" th:text="${#dates.format(businessCheck.checkDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                                                    </div>
                                                    <div class="fl ml20 selector" style="width: calc(100% - 160px)">
                                                        <div
                                                                style="border: 1px #eee solid;padding: 10px 10px;box-sizing: border-box;width: 100%">
                                                            [[${businessCheck.memo}]]</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                            </div>
                        </div>

                    </div>
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion">单据明细</a>
                            </h4>
                        </div>
                        <div class="panel-collapse collapse in" id="collapseOne">
                            <div class="panel-body">
                                <div class="padt20">
                                    <div class="warp">
                                        <div class="over" style="width: 100%;display: flex;justify-content: space-between">
                                            <div class="fl" style="line-height: 20px;">[[${payCheckSheet.vbillno}]] &nbsp; </div>
                                            <div class="fl" style="line-height: 20px;">
                                                货量:<span th:if="${numTotal!=0  && numTotal!=null}">
                                                    [[${numTotal}]]件|
                                                </span>
                                                <span th:if="${wightTotal!=0 && wightTotal!=null}">
                                                    [[${wightTotal}]]吨|
                                                </span>
                                                <span th:if="${volumeTotal!=0 && volumeTotal!=null}">
                                                    [[${volumeTotal}]]m³
                                                </span>
                                                &nbsp;
                                            </div>
                                            <div class="fl" style="line-height: 20px;" > 总金额：[[${totalAmount}]]元 &nbsp; </div>
                                            <div class="fl" style="line-height: 20px;" th:if="${totalAmount-oilAmount!=0}">
                                                现金：[[${totalAmount-oilAmount}]]元 &nbsp;
                                            </div>
                                            <div class="fl" style="line-height: 20px;" th:if="${oilAmount!=0}">
                                                油卡：[[${oilAmount}]]元 &nbsp;
                                            </div>

                                        </div>
                                    </div>
                                    <!--startprint-->
                                    <table class="table table-bordered lot" id="infoTabFour">
                                        <thead style="background: #F7F8FA">
                                        <tr>
                                            <th style="width: 20px"></th>
                                            <th style="width: 20px"></th>
                                            <th>日期/单号</th>


                                            <!--                                                <th>要求提货日期</th>-->
                                            <th>提货 → 到货</th>
                                            <!--                                                <th>货品</th>-->
                                            <th>货量</th>
                                            <th>合计金额</th>

                                        </tr>
                                        </thead>
                                        <tbody th:each="entrustLot,estatus : ${entrustLots}">
                                        <tr>
                                            <td>
                                                <a class="detail-icon reTa" th:onclick="'removeRowFour(this,'+${estatus.index}+')'"><i class="glyphicon glyphicon-minus icon-minus"></i></a>
                                                <a class="detail-icon addTa" th:onclick="'removeRowFour(this,'+${estatus.index}+')'"><i class="glyphicon glyphicon-plus icon-plus"></i></a>

                                            </td>
                                            <td>[[${estatus.index+1}]]</td>
                                            <td>
                                                <div class="">[[${#dates.format(entrustLot.reqDeliDate, 'yyyy-MM-dd')}]]</div>
                                                <div class="">[[${entrustLot.lot}]]</div>


                                            </td>

                                            <td>
                                                <div class="">
                                                    <div th:if='${entrustLot.deliProvinceName!="上海市"&&entrustLot.deliProvinceName!="北京市"&&entrustLot.deliProvinceName!="天津市"&&entrustLot.deliProvinceName!="重庆市"}'>
                                                        提：[[${entrustLot.deliProvinceName}]][[${entrustLot.deliCityName}]]
                                                    </div>
                                                    <div th:if='${entrustLot.deliProvinceName=="上海市"||entrustLot.deliProvinceName=="北京市"||entrustLot.deliProvinceName=="天津市"||entrustLot.deliProvinceName=="重庆市"}'>
                                                        提：[[${entrustLot.deliProvinceName}]][[${entrustLot.deliAreaName}]]
                                                    </div>
                                                    <div th:if='${entrustLot.arriProvinceName!="上海市"&&entrustLot.arriProvinceName!="北京市"&&entrustLot.arriProvinceName!="天津市"&&entrustLot.arriProvinceName!="重庆市"}'>
                                                        到：[[${entrustLot.arriProvinceName}]][[${entrustLot.arriCityName}]]
                                                    </div>
                                                    <div th:if='${entrustLot.arriProvinceName=="上海市"||entrustLot.arriProvinceName=="北京市"||entrustLot.arriProvinceName=="天津市"||entrustLot.arriProvinceName=="重庆市"}'>
                                                        到：[[${entrustLot.arriProvinceName}]][[${entrustLot.arriAreaName}]]
                                                    </div>
                                                </div>
                                            </td>

                                            <td>
                                                <div class="">
                                                    <span>[[${entrustLot.goodsName}]]/</span>
                                                    <span th:if="${entrustLot.numCount!=0 && entrustLot.numCount!=null}">
                                                            [[${entrustLot.numCount}]]件|
                                                        </span>
                                                    <span th:if="${entrustLot.weightCount!=0 && entrustLot.weightCount!=null}">
                                                            [[${entrustLot.weightCount}]]吨|
                                                        </span>
                                                    <span th:if="${entrustLot.volumeCount!=0 && entrustLot.volumeCount!=null}">
                                                            [[${entrustLot.volumeCount}]]m³
                                                        </span>
                                                    <span>/[[${entrustLot.unitPrice}]]</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="" >
                                                    现金：[[${entrustLot.cashAmountPayDetail}]]元
                                                </div>
                                                <div class="" th:if="${entrustLot.oilAmountPayDetail!=0}">
                                                    油卡：[[${entrustLot.oilAmountPayDetail}]]元
                                                </div>

                                            </td>

                                        </tr>
                                        <tr style="background: #fffcd3;">
                                            <td colspan="6" class="qd">
                                                <div class="qd_title">费用明细</div>
                                                <table class="custom-tab tab table table-bordered">
                                                    <thead style="background: #F7F8FA">
                                                    <tr>

                                                        <th >日期</th>
                                                        <th >状态</th>
                                                        <th >金额</th>
                                                        <th >类型</th>
                                                        <th >备注</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody >
                                                    <tr th:each="payDetail,status : ${entrustLot.payDetailList}">


                                                        <td>
                                                            <div >
                                                                <span>[[${#dates.format(payDetail.regDate, 'yyyy-MM-dd HH:mm:ss')}]]</span>
                                                            </div>

                                                        </td>
                                                        <td>
                                                            <div >
                                                                <span th:if="${payDetail.vbillstatus==0}">新建</span>
                                                                <span th:if="${payDetail.vbillstatus==1}">已确认</span>
                                                                <span th:if="${payDetail.vbillstatus==2}">已对账</span>
                                                                <span th:if="${payDetail.vbillstatus==3}">部分核销</span>
                                                                <span th:if="${payDetail.vbillstatus==4}">已核销</span>
                                                                <span th:if="${payDetail.vbillstatus==5}">关闭</span>
                                                                <span th:if="${payDetail.vbillstatus==6}">已申请</span>
                                                                <span th:if="${payDetail.vbillstatus==7}">核销中</span>
                                                                <span th:if="${payDetail.vbillstatus==8}">审核中</span>
                                                                <span th:if="${payDetail.vbillstatus==9}">复核通过</span>
                                                            </div>

                                                        </td>
                                                        <td>
                                                            <div >
                                                                <!--                                                        <span>[[${payDetail.vbillno}]]</span>：-->
                                                                <span th:if="${payDetail.transFeeCount<0}" style="color: red">[[${payDetail.transFeeCount}]]元</span>
                                                                <span th:if="${payDetail.transFeeCount>=0}">[[${payDetail.transFeeCount}]]元</span>
                                                            </div>

                                                        </td>
                                                        <td>
                                                            <div >
                                                        <span th:if="${payDetail.freeType} == 0"
                                                              th:text="${@dict.getLabel('cost_type_freight',payDetail.costTypeFreight)}"></span>
                                                                <span th:if="${payDetail.freeType} == 1"
                                                                      th:text="${@dict.getLabel('cost_type_on_way',payDetail.costTypeOnWay)}"></span>
                                                            </div>

                                                        </td>
                                                        <td style="text-align: left">
                                                            <div >
                                                                <span>[[${payDetail.memo}]]</span>
                                                            </div>

                                                        </td>

                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>

                                    <!--endprint-->
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="over mt10">
                        <div class="fr mt20" id="time" style="width:150px"></div>
                        <div class="fr" style="width:150px">
                            <div class="mt10">总办签字：</div>
                            <div class="mt10">总经理签字：</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var height = $('.selector').height()
        console.log(height)
        var entrustLots= [[${entrustLots}]];
        var payCheckSheet = [[${payCheckSheet}]];
        var paySheetRecord = [[${paySheetRecord}]];
        var payDetailList = [[${payDetailList}]];
        var businessChecks = [[${businessChecks}]];
        console.log('entrustLots',entrustLots)
        console.log('payDetailList',payDetailList)
        console.log('businessChecks',businessChecks)
        var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
        console.log('costTypeFreight',costTypeFreight)
        $('.addTa').hide()
        var height1 = height - 60
        if (height > 40) {
            $(".line").height(height);
        }
        var myDate = new Date;
        var year = myDate.getFullYear(); //获取当前年
        var mon = myDate.getMonth() + 1; //获取当前月
        var date = myDate.getDate(); //获取当前日
        var week = myDate.getDay();
        console.log(year, mon, date)
        $("#time").html("日期：" + year + "年" + mon + "月" + date + "日");
        function dayin() {
             printdiv(div_print);
        }

        function printdiv(printpage) {
            var newstr = printpage.innerHTML;
            var oldstr = document.body.innerHTML;
            document.body.innerHTML = newstr;
            window.print();
            document.body.innerHTML = oldstr;
            return false;
        }
        function removeRowFour(obj,index){
            //$('.addTa').toggle()
            //$('.reTa').toggle()
            $("#infoTabFour tbody:eq("+index+")").find('.addTa').toggle()
            $("#infoTabFour tbody:eq("+index+")").find('.reTa').toggle()
            //$("#infoTabFour tbody:eq("+index+")").find("tr").eq(1).toggle();
            $("#infoTabFour tbody:eq("+index+")").find(obj).closest("tr").next().toggle();

        }
    </script>
</body>

</html>