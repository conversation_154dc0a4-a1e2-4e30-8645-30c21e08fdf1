<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('油卡占比调整')"/>
</head>

<body>
<div class="form-content">
    <form id="form-payCheck-add" class="form-horizontal" novalidate="novalidate" th:object="${payCheckSheet}">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <input type="hidden" class="form-control" readonly th:field="*{payCheckSheetId}">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">对账单名称：</label>
                                    <div class="col-sm-7" th:text="*{carrName}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">对账单号：</label>
                                    <div class="col-sm-7" th:text="*{vbillno}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">对账年：</label>
                                    <div class="col-sm-7" th:text="*{year+ '年'}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">对账月：</label>
                                    <div class="col-sm-7" th:text="*{month + '月'}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">总金额：</label>
                                    <div class="col-sm-7" th:text="*{totalAmount}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">现金调整额：</label>
                                    <div class="col-sm-7" th:text="*{taxAmount}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">油卡调整额：</label>
                                    <div class="col-sm-7" th:text="*{taxAmountOil}"></div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5" style="color: red; ">油卡金额：</label>
                                    <div class="col-sm-7">
                                        <input th:field="*{oilAmount}" type="text" class="form-control" required autocomplete="off"
                                               th:oninput="|$.numberUtil.onlyNumberCustom(this,*{totalAmount},0,15,2);|">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-5">原先油卡金额：</label>
                                    <div class="col-sm-7" th:text="${oldOilAmount}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div  class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFive">应付明细</a>
                    </h4>
                </div>
                <div id="collapseFive" class="panel-collapse collapse in">
                    <div class="panel-body">

                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabThree" class="custom-tab table" >
                                <thead>
                                <tr>
                                    <th style="width: 18%;text-align:center">应付单据号</th>
                                    <th style="width: 18%;text-align:center">运单号</th>
                                    <th style="width: 18%;text-align:center">运费类型</th>
                                    <th style="width: 10%;text-align:center">总金额</th>
                                    <th style="width: 10%;text-align:center">调整额</th>
                                    <th style="width: 10%;text-align:center">已收金额</th>
                                    <th style="width: 10%;text-align:center">未收金额</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="mapS,status:${payDetailList}">
                                    <td>
                                        <div class="input-group" th:text="${mapS.vbillno}"></div>
                                    </td>
                                    <td>
                                        <div class="input-group" th:text="${mapS.lotno}"></div>
                                    </td>
                                    <td th:if="${mapS.costTypeFreight} != null" th:text="${@dict.getLabel('cost_type_freight',mapS.costTypeFreight)}"></td>
                                    <td th:if="${mapS.costTypeFreight} == null"></td>
                                    <td th:align="right" th:text="￥+${#numbers.formatDecimal(mapS.transFeeCount,1,'COMMA',2,'POINT')}"></td>
                                    <td th:align="right" th:text="￥+${#numbers.formatDecimal(mapS.taxAmount,1,'COMMA',2,'POINT')}"></td>
                                    <td th:align="right" th:text="￥+${#numbers.formatDecimal(mapS.gotAmount,1,'COMMA',2,'POINT')}"></td>
                                    <td th:align="right" th:text="￥+${#numbers.formatDecimal(mapS.ungotAmount,1,'COMMA',2,'POINT')}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary" onclick="commit()"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    $(function () {
        $('#collapseFive').collapse('show');
    });

    var oldOilAmount = [[${payCheckSheet.oilAmount}]];

    /**
     * 提交的方法
     */
    function commit() {
        if ($.validate.form()){
            if ($("#oilAmount").val() == oldOilAmount) {
                $.modal.msgError("与原先油卡金额相等，无法更新！");
                return;
            }
            $.operate.saveTab(ctx +"payCheckSheet/oil_adjust",  {payCheckSheetId:$("#payCheckSheetId").val(), oilAmount: $("#oilAmount").val()});
        }

    }



</script>
</body>

</html>