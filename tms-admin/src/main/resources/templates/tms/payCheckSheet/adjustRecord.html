<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付对账')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .table-striped{
        height: calc(100% - 80px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">

                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">审核状态：</label>
                            <div class="col-sm-8">
                                <select name="adjustCheckStatusArr" id="adjustCheckStatusArr" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="审核状态" multiple>
                                    <option th:each="dict : ${adjustCheckStatus}" th:text="${dict.context}"
                                            th:value="${dict.value}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <!--<div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运营组：</label>
                            <div class="col-sm-8">
                                <select name="salesDeptReceiveCheckSheet" id="salesDeptReceiveCheckSheet" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运营组" multiple>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>-->

                    <div class="col-md-offset-6 col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <!--<div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary single disabled" onclick="invoiceCheck()" shiro:hasPermission="tms:receiveCheckSheet:invoiceCheck">
                <i class="fa fa-check"></i> 业务审核
            </a>
            <a class="btn btn-primary single disabled" onclick="leaderCheck()" shiro:hasPermission="tms:receiveCheckSheet:leaderCheck">
                <i class="fa fa-check"></i> 总经办审核
            </a>
            <a class="btn btn-primary single disabled" onclick="financeCheck()" shiro:hasPermission="tms:receiveCheckSheet:financeCheck">
                <i class="fa fa-check"></i> 财务确认
            </a>
        </div>-->

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    //运单状态
    var entrustLotStatus = [[${entrustLotStatus}]];
    var prefix = ctx + "payCheckSheetAdjustCheck";

    $(function () {
        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/adjustImport",
            showToggle: false,
            showColumns: true,
            modalName: "应付对账",
            uniqueId: "adjustRecordId",
            fixedColumns: true,
            height: 580,
            rememberSelected: false,
            fixedNumber: 0,
            clickToSelect:true,
            showFooter:true,
            columns: [{
                checkbox: true
            },
                {
                    title: '操作',
                    align: 'center',
                    field: 'adjustRecordId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-xs  " href="javascript:void(0)" title="明细"onclick="jumpAdjustRecordId(\'' + row.adjustRecordId + '\')"><i class="fa fa-list" style="font-size: 15px;"></i></a>');
                        return actions.join('');
                    }
                },
                {
                    title: '审核状态',
                    field: 'adjustCheckStatus',
                    align: 'left',
                    formatter: function status(value,row) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-primary">待业务审核</label>';
                            case 1:
                                return '<span class="label label-warning">待总经办审核</label>';
                            case 2:
                                return '<span class="label label-info">待财务确认</label>';
                            case 3:
                                return '<span class="label label-success">审核通过</label>';
                            case 4:
                                return '<span class="label label-inverse">审核不通过</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '调整人',
                    align: 'left',
                    field: 'regUserName'
                },

                {
                    title: '调整原因',
                    align: 'left',
                    field: 'memo',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '收入调整',
                    align: 'left',
                    field: 'isCostAdjust',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '收入调整描述',
                    align: 'left',
                    field: 'costAdjustMemo',
                    formatter: function status(value,row) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '总金额(元)',
                    align: 'right',
                    field: 'sumTransFeeCount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '调整后总金额(元)',
                    align: 'right',
                    field: 'sumTransFeeCountAfter',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '申请时间',
                    align: 'left',
                    field: 'regDate'
                },
                {
                    title: '业务审核人',
                    align: 'left',
                    field: 'firstCheckUserName'
                },
                {
                    title: '业务审核时间',
                    align: 'left',
                    field: 'firstCheckDate'
                },
                {
                    title: '业务审核备注',
                    align: 'left',
                    field: 'firstCheckMemo'
                },
                {
                    title: '总经办审核人',
                    align: 'left',
                    field: 'secondCheckUserName'
                },
                {
                    title: '总经办审核时间',
                    align: 'left',
                    field: 'secondCheckDate'
                },
                {
                    title: '总经办审核备注',
                    align: 'left',
                    field: 'secondCheckMemo'
                },
                {
                    title: '财务确认人',
                    align: 'left',
                    field: 'thirdCheckUserName'
                },
                {
                    title: '财务确认时间',
                    align: 'left',
                    field: 'thirdCheckDate'
                },
                {
                    title: '财务确认备注',
                    align: 'left',
                    field: 'thirdCheckMemo'
                }

            ]
        };
        $.table.init(options);

        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                searchPre();
            }
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#endtDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
    });



    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        data.adjustCheckStatusArr = $.common.join($('#adjustCheckStatusArr').selectpicker('val'));
/*        data.salesDeptReceiveCheckSheet = $.common.join($('#salesDeptReceiveCheckSheet').selectpicker('val'));*/
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 审核
     */
    function invoiceCheck() {
        var adjustRecordId = $.table.selectColumns('adjustRecordId').join();
        var adjustCheckStatus = $.table.selectColumns('adjustCheckStatus').join();
        if(adjustCheckStatus != 0){
            $.modal.alertWarning("当前审核状态不需要进行业务审核！");
            return false;
        }
        var url = prefix + "/invoiceCheck?adjustRecordId=" + adjustRecordId;
        $.modal.openTab("业务审核" , url);
    }
    

    
    function leaderCheck() {
        var adjustRecordId = $.table.selectColumns('adjustRecordId').join();
        var adjustCheckStatus = $.table.selectColumns('adjustCheckStatus').join();
        if(adjustCheckStatus != 1){
            $.modal.alertWarning("当前审核状态不需要进行总经办审核！");
            return false;
        }
        var url = prefix + "/leaderCheck?adjustRecordId=" + adjustRecordId;
        $.modal.openTab("总经办审核" , url);
    }
    
    function financeCheck() {
        var adjustRecordId = $.table.selectColumns('adjustRecordId').join();
        var adjustCheckStatus = $.table.selectColumns('adjustCheckStatus').join();
        if(adjustCheckStatus != 2){
            $.modal.alertWarning("当前审核状态不需要进行财务确认！");
            return false;
        }
        var url = prefix + "/financeCheck?adjustRecordId=" + adjustRecordId;
        $.modal.openTab("财务审核" , url);
    }

    function jumpAdjustRecordId(adjustRecordId) {
        var url = prefix + "/listDetail?adjustRecordId="+adjustRecordId;
        $.modal.openTab("应收对账申请信息" , url);
    }

</script>
</body>
</html>