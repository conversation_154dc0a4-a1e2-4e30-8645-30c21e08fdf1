<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('含税明细')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    /*.left-fixed-body-columns{
        height: calc(100% - 120px) !important;
    }*/
    .table-striped {
        height: calc(100% - 20px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary multiple disabled" onclick="view_batch()">
                批量调整
            </a>
            <span id="adjustMsg"></span>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>

<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/js/bignumber.min.js}"></script>
<script th:inline="javascript">

    var prefix = ctx + "payCheckSheet";
    var billingType = [[${@dict.getType('billing_type')}]];//开票类型[{"dictLabel":"****","dictValue":"***"}]
    var sumAmount = 0;
    var globalRows = [];

    $(function() {


        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });


        var columns = [
            {
                checkbox: true,
                title: '',
                formatter: function(value, row, index) {
                    let option = $("#bootstrap-table").bootstrapTable('getOptions');
                    let pageSize = option.pageSize;
                    let pageNumber = option.pageNumber;
                    return pageSize * (pageNumber - 1) + index + 1
                },
                footerFormatter: function (row) {
                    return "统计：<span id='computeDiv'></span>";
                }
            },
            {
                title: '应付单号',
                field: 'VBILLNO'
            },
            {
                title: '运单号',
                field: 'LOT'
            },
            {
                title: '提货到货地址',
                field: 'DELI_PROVINCE_NAME',
                formatter: function (value, row, index) {
                    return $.table.tooltip(row.DELI_PROVINCE_NAME + row.DELI_CITY_NAME + row.DELI_AREA_NAME + " ~ " + row.ARRI_PROVINCE_NAME + (row.ARRI_CITY_NAME||'') + (row.ARRI_AREA_NAME||''))
                }
            },
            {
                title: '要求提货日期',
                field: 'REQ_DELI_DATE',
                formatter: function (value, row, index) {
                    return value.substring(0, 16)
                }
            },
            {
                title: '支付费用',
                field: 'AMOUNT',
                formatter: function(value, row, index) {
                    return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
                }
            },
            {
                title: '费用含税构成',
                field: 'TAX_TXT',
                formatter: function(value, row, index) {
                    var arr = value.split(",")
                    var temp = [];
                    var sum = new BigNumber("0");
                    for (var i = 0; i < arr.length; i++) {
                        var tt = arr[i].split(":");
                        var find = false;
                        for (let j = 0; j < billingType.length; j++) {
                            if (tt[0] == billingType[j].dictValue) {
                                find = true;
                                temp.push(i>0?" [":"[", billingType[j].dictLabel, ":", tt[1], "]")
                                sum = sum.plus(new BigNumber(tt[1]))
                                break;
                            }
                        }
                        if (!find) {
                            temp.push(temp.length > 0 ? " [":"[", tt[0], ":", tt[1], "]")
                            sum = sum.plus(new BigNumber(tt[1]))
                        }
                    }
                    if (!sum.eq(new BigNumber(row.AMOUNT))) {
                        var msg = "合计不等于支付费用, 请手动调整";
                        temp.splice(0,0,"<span style='color:red' err-tax data-toggle='tooltip' title='" + msg + "' data-trigger='manual'>")
                        temp.push("</span>")
                    }
                    if (row.G7_PAY == 2) {
                        return temp.join("") // + ' <a href="javascript:;">G7不可调</a>';
                    } else {
                        return temp.join("") + (row.AMOUNT != 0 ? " <a href='javascript:adjust(" + index + ")'>调</a>" : "");
                    }
                }
            }
        ]

        var options = {
            url: prefix + "/listDetailTax?payCheckSheetId=[(${payCheckSheetId})]",
            fixedColumns: false,
            fixedNumber: 0,
            showToggle: false,
            showFooter: true,
            pagination: false,
            height: 760,
            onLoadSuccess:function(result){
                globalRows = result.rows;
                if (parent.adjustCache != null) {
                    var cache = JSON.parse(parent.adjustCache);
                    if (globalRows.length != cache.length) {
                        $.modal.msgError("对账包与应付明细关系变更，请重新打开申请付款页面");
                        return;
                    }
                    for (let i = 0; i < cache.length; i++) {
                        if (cache[i].PAY_DETAIL_ID != globalRows[i].PAY_DETAIL_ID || cache[i].AMOUNT != globalRows[i].AMOUNT) {
                            $.modal.msgError("应付明细变更，请重新打开申请付款页面");
                            return
                        }
                        globalRows[i].TAX_TXT = cache[i].TAX_TXT;
                    }
                    $("#bootstrap-table").bootstrapTable("load", globalRows);
                }
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            onPostBody: function (rows) {
                globalRows = rows;
                //合并页脚
                merge_footer();
                getAmountCount(rows);
                $('[err-tax]').tooltip('show')
            },
            columns: columns
        };
        $.table.init(options);

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#yearMonth',
                type: 'month',
                trigger: 'click'
            });
        });

    });

    function searchx() {
        var data = {};
        data.salesDept = $.common.join($('#salesDeptId').selectpicker('val'));//运营组
        $.table.search('role-form', data);
    }
    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    function getAmountCount(data) {
        var tempObj = {}
        for (let i = 0; i < data.length; i++) {
            var arr = data[i].TAX_TXT.split(",")
            for (var k = 0; k < arr.length; k++) {
                var tt = arr[k].split(":");
                var taxSum = tempObj[tt[0]];
                if (!taxSum) {
                    taxSum = new BigNumber(tt[1]);
                } else {
                    taxSum = taxSum.plus(new BigNumber(tt[1]));
                }
                tempObj[tt[0]] = taxSum;
            }
        }
        var temp = []
        for (var bt in tempObj) {
            for (let j = 0; j < billingType.length; j++) {
                if (bt == billingType[j].dictValue) {
                    temp.push(temp.length > 0 ? " [":"[", billingType[j].dictLabel, ":", tempObj[bt], "]")
                    break;
                }
            }
        }

        $("#computeDiv").text(temp.join(""));
    }



    /**
     * 将总计金额清零
     */
    function clearTotal() {
        //开票金额合计
        sumAmount = 0
    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        sumAmount += row.amount;
    }

    /**
     *
     */
    function subTotal(row) {
        sumAmount -= row.amount;
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        //$("#sumAmount").text(sumAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
    }

    function adjust(index) {
        var row = globalRows[index]
        var tmp = [`<div style="padding: 8px 20px;">
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-sm-4 control-label">当前费用：</label>
							<div class="col-sm-3">
							  	<p class="form-control-static">${row.AMOUNT}</p>
							</div>
							<div class="col-sm-4">
							  	<p class="form-control-static" id="result" style="color:#ff0000"></p>
							</div>
							<div class="col-sm-1">
							    <i class="fa fa-plus-circle" style="height: 34px;vertical-align: middle;padding: 7px 0;color:#007dcc" onclick="addRow(this,${index})"></i>
							</div>
						</div>`]
        var arr = row.TAX_TXT.split(",");
        for (let i = 0; i < arr.length; i++) {
            var tt = arr[i].split(":");
            tmp.push('<div class="form-group">')
            tmp.push('<label class="col-sm-7">')
            tmp.push('<select billingtype class="form-control">');
            for (let j = 0; j < billingType.length; j++) {
                tmp.push("<option value='",billingType[j].dictValue,"'",tt[0]==billingType[j].dictValue?' selected':'',">",billingType[j].dictLabel,"</option>");
            }
            tmp.push("</select>");
            tmp.push('</label>')
            tmp.push('<div class="col-sm-4">')
            tmp.push('<input class="form-control" tax oninput="$.numberUtil.onlyNumberTwoDecimal(this);check(',index,');" value="',tt[1],'" autocomplete="off">')
            tmp.push('</div>')
            tmp.push('<div class="col-sm-1">')
            tmp.push('<i class="fa fa-minus-circle" style="height: 34px;vertical-align: middle;padding: 7px 0;color:#ff0000" onclick="$(this).closest(\'.form-group\').remove();check(',index,');"></i>')
            tmp.push('</div>')
            tmp.push('</div>')
        }
        tmp.push('</form>')
        tmp.push('* 所有含税额合计必须<span style="color:blue;font-weight: bold;">等于当前费用</span>, 且必须与当前费用<span style="color:blue;font-weight: bold;">同为正数或负数</span>')
        tmp.push('</div>')
        layer.open({
            type: 1,
            area: ['450px', '403px'],
            fix: false,
            //不固定
            maxmin: false,
            shade: 0.3,
            shadeClose: false,
            title: '应收明细含税额拆分',
            content: tmp.join(""),
            btn: ['<i class="fa fa-check"></i> 保存', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(idx, layero){
                var chk = check(index);
                var objTemp = {}
                var tmp = []
                if (chk) {
                    var amount_origin = new BigNumber(row.AMOUNT)
                    var flag = true;
                    $("[billingtype]").each(function(){
                        if (objTemp[$(this).val()] != null) {
                            $.modal.msgError("相同开票类型的请合并");
                            flag = false;
                            return;
                        } else {
                            objTemp[$(this).val()] = 1;
                            var n = new BigNumber($(this).closest('.form-group').find('[tax]').val()).toNumber();
                            if (n === 0 && amount_origin.toNumber() != 0) {
                                $.modal.msgError("请输入非0的数字");
                                flag = false;
                                return;
                            } else if (n < 0 && amount_origin.toNumber() > 0) {
                                $.modal.msgError("当前费用大于0，各税额必须都大于0");
                                flag = false;
                                return;
                            } else if (n > 0 && amount_origin.toNumber() < 0) {
                                $.modal.msgError("当前费用小于0，各税额必须都小于0");
                                flag = false;
                                return;
                            }
                            tmp.push($(this).val()+":"+$(this).closest('.form-group').find('[tax]').val())
                        }
                    })
                    if (flag) {
                        globalRows[index].TAX_TXT = tmp.join(",")
                        $("#bootstrap-table").bootstrapTable("load", globalRows)
                        layer.close(idx)
                    }
                } else {
                    $.modal.msgError("所有含税额合计必须等于当前费用")
                }
            }
        })
        check(index)
    }
    function addRow(i, index) {
        var tmp = [];
        tmp.push('<div class="form-group">')
        tmp.push('<label class="col-sm-7">')
        tmp.push('<select billingtype class="form-control">');
        for (let j = 0; j < billingType.length; j++) {
            tmp.push("<option value='",billingType[j].dictValue,"'>",billingType[j].dictLabel,"</option>");
        }
        tmp.push("</select>");
        tmp.push('</label>')
        tmp.push('<div class="col-sm-4">')
        tmp.push('<input class="form-control" tax oninput="$.numberUtil.onlyNumberTwoDecimal(this);check(',index,');" autocomplete="off">')
        tmp.push('</div>')
        tmp.push('<div class="col-sm-1">')
        tmp.push('<i class="fa fa-minus-circle" style="height: 34px;vertical-align: middle;padding: 7px 0;color:#ff0000" onclick="$(this).closest(\'.form-group\').remove();check(',index,');"></i>')
        tmp.push('</div>')
        tmp.push('</div>')
        $(i).closest("form").append(tmp.join(''))
    }
    function check(i) {
        var row = globalRows[i];
        var amount_origin = new BigNumber(row.AMOUNT)
        var sum = calc1(i);
        if (sum.eq(amount_origin)) {
            $("#result").text("")
            return true;
        } else if (sum.gt(amount_origin)) {
            $("#result").text("超" + sum.minus(amount_origin))
            return false;
        } else if (sum.lt(amount_origin)) {
            $("#result").text("低" + amount_origin.minus(sum))
            return false;
        }
    }
    function calc1(i) {
        var sum = new BigNumber("0")
        $("[tax]").each(function(){
            var amount = new BigNumber(this.value.trim());
            if (!amount.isNaN()) {
                sum = sum.plus(amount);
            }
        })
        return sum;
    }
    function submitHandler(index, layero) {
        if (globalRows.length == 0) {
            $.modal.msgError("未找到可提交数据！");
            return;
        }
        if ($('[err-tax]').length > 0) {
            $.modal.msgError("请调整错误的含税额构成");
            return;
        }
        var cache = [];
        for (let i = 0; i < globalRows.length; i++) {
            cache.push({"PAY_DETAIL_ID":globalRows[i].PAY_DETAIL_ID,"AMOUNT":globalRows[i].AMOUNT, "TAX_TXT":globalRows[i].TAX_TXT});
        }
        window.parent.adjustCache = JSON.stringify(cache);
        window.parent.layer.close(index);
        window.parent.sumTaxTxtChange && window.parent.sumTaxTxtChange($("#computeDiv").text());
    }
    function view_batch() {
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        var arr = [];
        for (let i = 0; i < bootstrapTable.length; i++) {
            var txt = bootstrapTable[i].TAX_TXT;
            var sp = txt.split(',');
            for (let j = 0; j < sp.length; j++) {
                var t = sp[j].split(":")[0]
                if (arr.indexOf(t) < 0) {
                    arr.push(t)
                }
            }
        }

        var tmp = ['<div style="padding: 8px 20px;">']
        for (let i = 0; i < arr.length; i++) {
            tmp.push('<div class="row">')
            tmp.push('<div class="col-sm-5">')
            for (let j = 0; j < billingType.length; j++) {
                if (arr[i] == billingType[j].dictValue) {
                    tmp.push('<p class="form-control-static">', billingType[j].dictLabel, '</p>');
                    break;
                }
            }
            tmp.push('</div>')
            tmp.push('<div class="col-sm-2"><p class="form-control-static" style="text-align: center">=&gt;</p></div>')
            tmp.push('<div class="col-sm-5">')
            tmp.push('<select billingtype orig_tax="',arr[i],'" class="form-control">');
            for (let j = 0; j < billingType.length; j++) {
                tmp.push("<option value='",billingType[j].dictValue,"'",billingType[j].dictValue==arr[i]?" selected":"",">",billingType[j].dictLabel,"</option>");
            }
            tmp.push("</select>");
            tmp.push(`</div>
                </div>`)
        }

        tmp.push("</div>")
        layer.open({
            type: 1,
            area: ['450px', '403px'],
            fix: false,
            //不固定
            maxmin: false,
            shade: 0.3,
            shadeClose: false,
            title: '批量调整',
            content: tmp.join(""),
            btn: ['<i class="fa fa-check"></i> 保存', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(idx, layero){
                var changed = false;
                for (let i = 0; i < globalRows.length; i++) {
                    for (let j = 0; j < bootstrapTable.length; j++) {
                        if (globalRows[i].PAY_DETAIL_ID == bootstrapTable[j].PAY_DETAIL_ID) {
                            var sp = globalRows[i].TAX_TXT.split(",");
                            for (let k = 0; k < sp.length; k++) {
                                $("[orig_tax]").each(function(){
                                    var orig_tax = $(this).attr('orig_tax')
                                    var cur_tax = $(this).find('option:selected').attr("value")
                                    if (orig_tax != cur_tax && sp[k].split(':')[0] == orig_tax) {
                                        // 发生修改，执行
                                        changed = true;
                                        sp[k] = cur_tax + ":" + sp[k].split(':')[1]
                                        return false;
                                    }
                                })
                            }
                            for (let k = 0; k < sp.length - 1; k++) { // 合并相同税率的
                                var tax = sp[k].split(":")[0]
                                var amount = new BigNumber(sp[k].split(":")[1])
                                for (let l = k + 1; l < sp.length; l++) {
                                    if (tax == sp[l].split(":")[0]) {
                                        amount = amount.plus(new BigNumber(sp[l].split(":")[1]))
                                        sp.splice(l, 1);
                                        l--;
                                    }
                                }
                                sp[k] = tax + ":" + amount.toNumber()
                            }
                            globalRows[i].TAX_TXT = sp.join(',')
                            break;
                        }
                    }
                }

                if (changed) {
                    $("#bootstrap-table").bootstrapTable("load", globalRows)
                }
                layer.close(idx)
            }
        })
    }
</script>
</body>
</html>