<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('应付明细列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <!-- <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">

                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运单号：</label>
                            <div class="col-sm-8">
                                <input name="lotno" id="lotno" class="form-control"
                                       placeholder="请输入运单号" maxlength="30">
                                <input id="hiddenText" type="text" style="display:none" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运营组：</label>
                            <div class="col-sm-8">
                                <select name="salesDept" placeholder="运营组" id="salesDept" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="运营组" multiple>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">付款类型：</label>
                            <div class="col-sm-8">
                                <select name="type" placeholder="运营组" id="type" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="付款类型" >
                                    <option value=""></option>
                                    <option value="0">现金</option>
                                    <option value="1">油卡</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div> -->
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger multiple disabled" onclick="removeAll()">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-warning" onclick="exportExcel()">
                <i class="fa fa-download"></i> 账单导出
            </a>

        </div>


        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var payCheckSheetId = [[${payCheckSheetId}]];
    var prefix = ctx + "payCheckSheet";

    var transFeeCount = 0;
    var gotAmountCount = 0;
    var ungotAmountCount = 0;

    $(function () {
        var options = {
            url: prefix + "/payDetailList?payCheckSheetId="+payCheckSheetId,
            removeUrl: prefix + "/remove",
            showToggle: false,
            showColumns: true,
            exportUrl: prefix + "/exportPayDetail?payCheckSheetId="+payCheckSheetId,
            modalName: "应付明细",
            rememberSelected: true,
            uniqueId: "payDetailId",
            clickToSelect:true,
            showFooter:true,
            onPostBody:function () {
                //查询合计总金额
                getAmountCount();
            },
            onRefresh:function(params){
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
            },
            onCheck: function (row,$element) {
                var transFee = row.transFeeCount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数加上本行数值
                transFeeCount = transFeeCount + transFee;
                gotAmountCount = gotAmountCount + gotAmount;
                ungotAmountCount = ungotAmountCount + ungotAmount;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheck: function (row, $element) {
                var transFee = row.transFeeCount;
                var gotAmount = row.gotAmount;
                var ungotAmount = row.ungotAmount;
                //总数减去本行数值
                transFeeCount = transFeeCount - transFee;
                gotAmountCount = gotAmountCount - gotAmount;
                ungotAmountCount = ungotAmountCount - ungotAmount;

                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onCheckAll: function (rowsAfter) {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                //循环累加
                for (var row of rowsAfter) {
                    transFeeCount = transFeeCount + row.transFeeCount;
                    gotAmountCount = gotAmountCount + row.gotAmount;
                    ungotAmountCount = ungotAmountCount + row.ungotAmount;
                }
                //赋值
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            onUncheckAll: function () {
                //总数清0
                transFeeCount = 0;
                gotAmountCount = 0;
                ungotAmountCount = 0;
                $("#transFeeCountTotal").text(transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#gotAmountCountTotal").text(gotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                $("#ungotAmountCountTotal").text(ungotAmountCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "总金额合计：<nobr id='transFeeCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "已付金额合计：<nobr id='gotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "未付金额合计：<nobr id='ungotAmountCountTotal'>￥0</nobr></br>"+
                        "总合计：总金额合计：<nobr id='sumTransFeeCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "已付金额合计：<nobr id='sumGotAmountCountTotal'>￥0</nobr>&nbsp&nbsp" +
                        "未付金额合计：<nobr id='sumUngotAmountCountTotal'>￥0</nobr>";
                }
            },
                {
                    title: '应付单号',
                    field: 'vbillno',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var result = value;
                        if (row.lotG7End == 2) {
                            result += ' <span class="label label-success" style="padding:1px;">G7</span>'
                        } else if (row.lotG7Syn != null) {
                            result += ' <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true" title="'
                            if (row.lotG7Syn == 0) {
                                result += '等待G7审验'
                            } else if (row.lotG7Syn == 1) {
                                result += row.lotG7Msg
                            } else if (row.lotG7Syn == 2) {
                                if (row.lotG7Start == null || row.lotG7Start == 0) {
                                    result += '等待推送【发车】'
                                } else if (row.lotG7Start == 1) {
                                    result += '【发车】推送失败'
                                } else if (row.lotG7End == null || row.lotG7End == 0) {
                                    result += '等待推送【到达】'
                                } else if (row.lotG7End == 1) {
                                    result += '【到达】推送失败'
                                }
                            } else if (row.lotG7Syn == 7) {
                                result += '运单已作废'
                            }
                            result += '">G7</span>'
                        } else if (row.payWay == 'Y') {
                            result += ' <span class="label label-warning" title="预支" style="padding:1px 5px;">Y</span>'
                        }

                        if (row.lotLockPay === '1') {
                            result += `<span class="label label-danger ml5"  data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="运单锁定">锁</span>`

                        }
                        return result;
                    }
                },
                {
                    title: '应付单状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value) {
                            case 0:
                                return '<span class="label label-default">新建</label>';
                            case 1:
                                return '<span class="label label-primary">已确认</label>';
                            case 2:
                                return '<span class="label label-coral">已对账</label>';
                            case 3:
                                return '<span class="label label-info">部分核销 </label>';
                            case 4:
                                return '<span class="label label-success">已核销 </label>';
                            case 5:
                                return '<span class="label label-inverse">关闭 </label>';
                            default:
                                break;
                        }
                    }

                },
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceVbillno',
                    formatter: function status(row,value) {
                        return $.table.tooltip(value.invoiceVbillno);
                    }
                },
                {
                    title: '运营组',
                    align: 'left',
                    field: 'salesDeptName'
                },
                {
                    title: '承运商',
                    align: 'left',
                    field: 'carrName'
                },
                {
                    title: '提货日期',
                    align: 'left',
                    field: 'reqDeliDate'
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if(row.deliAddr == null || row.deliAddr == ""){
                            row.deliAddr = '';
                        }
                        if(row.arriAddr == null || row.arriAddr.arriAddr == ""){
                            row.arriAddr = ''
                        }
                        if(row.deliAddr == "" && row.arriAddr == ""){
                            return "";
                        }else{
                            return row.deliAddr+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriAddr;
                        }

                    }
                },

                {
                    title: '费用类型',
                    field: 'freeType',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value - 0) {
                            case 0:
                                return '<span>运费</label>';
                            case 1:
                                return '<span>在途费用</label>';
                            case 2:
                                return '<span>调整费用</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '付款类型',
                    field: 'costTypeOnWay',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if (row.freeType === '0'){
                            return $.table.selectDictLabel(costTypeFreight, row.costTypeFreight);
                        }else if (row.freeType === '2')  {
                            return '调整费'
                        }
                        return $.table.selectDictLabel(costTypeOnWay, value);
                    }

                },
                {
                    title: '付款类型2',
                    align: 'left',
                    field: 'costTypeFreightName'
                },
                {
                    title: '车辆',
                    align: 'left',
                    field: 'carno'
                },
                {
                    title: '司机',
                    align: 'left',
                    field: 'driverName'
                },

                {
                    title: '司机电话',
                    align: 'left',
                    field: 'driverMobile',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '总金额',
                    align: 'right',
                    field: 'transFeeCount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '调整额',
                    align: 'right',
                    field: 'taxAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '已付金额',
                    align: 'right',
                    field: 'gotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '未付金额',
                    align: 'right',
                    field: 'ungotAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '总件数',
                    align: 'left',
                    field: 'numCount'
                },
                {
                    title: '总体积(m³)',
                    align: 'left',
                    field: 'volumeCount'
                },
                {
                    title: '总重量(吨)',
                    align: 'left',
                    field: 'weightCount'
                },
                {   field: 'writeOffTime',
                    title: '核销时间',
                    align: 'left'
                },
                {
                    title: '到货日期',
                    align: 'left',
                    field: 'reqArriDate'
                },
                {
                    title: '运单号',
                    align: 'left',
                    field: 'lotno'
                },
                {
                    title: '备注',
                    align: 'left',
                    field: 'memo',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                }

            ]
        };

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });


</script>
<script>

    function searchPre() {
        var data = {};
        data.salesDept = $.common.join($('#salesDept').selectpicker('val'));
        $.table.search('role-form', data);
    }

    function resetPre() {
        $(".selectpicker").selectpicker('deselectAll');
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 跳转应付修改页面
     * @param id
     */
    function edit(id,vbillstatus) {
        if (vbillstatus != 0) {
            $.modal.alertWarning("只能修改新建状态的应付单");
            return;
        }
        var url = prefix + "/edit?payDetailId=" + id;
        $.modal.openTab("应付明细修改", url);
    }
    
    function removeAll() {

        /*var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isClose"] == 1 ) {
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return false;
            }
        }*/

        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要删除选中的" + rows.length + "条数据吗?", function() {
            var url = $.table._option.removeUrl;
            var data = { "ids": rows.join() };
            $.operate.submit(url, "post", "json", data);
        });
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        data.payCheckSheetId = payCheckSheetId;
        $.ajax({
            url: prefix + "/getPayDetailCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $("#sumTransFeeCountTotal").text(data.TRANS_FEE_COUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumGotAmountCountTotal").text(data.GOT_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#sumUngotAmountCountTotal").text(data.UNGOT_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }

    /**
     * 导出
     */
    function exportExcel() {
        $.modal.confirm("确定导出所有数据吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            var search = $.common.formToJSON("role-form");
            search.params = new Map();
            search.payCheckSheetId = payCheckSheetId
            $.post(prefix + "/bill_export", search, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });

    }


    /**
     * 批量删除
     */
    function remove() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0 ) {
                $.modal.alertWarning("只能删除新建状态下的应付单据");
                return;
            }
        }
        var rows = $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要删除选中的数据吗?", function () {
            var url = prefix + "/remove";
            var data = {"ids": rows.join()};
            $.operate.submit(url, "post", "json", data);
        });
    }
</script>
</body>
</html>