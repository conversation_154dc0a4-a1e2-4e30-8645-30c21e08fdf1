<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('付款申请')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .fcff{
        color: #ff1f1f;
    }
    .col-sm-4{
        line-height: 26px;
    }
    .file-drop-zone{
        overflow-y: auto;
    }
    .imageBox2 {
        border: 2px transparent solid;
    }
    .imageBox2:hover {
        border-color: #dddddd;
    }
    .felx{
        display: flex;
        align-items: center;
    }
    .piao{
        padding: 8px 30px 8px 10px;;
        background: url("/img/kd.png") no-repeat center 100%/100%;
    }
    .ed5565{
        color: #D61D1D;
    }
    .ml10{
        margin-left: 10px;
    }
</style>
<body>
<div class="form-content">
    <form id="form-payAppl-add" class="form-horizontal" novalidate="novalidate">
        <!--对账单id-->
        <input name="payCheckSheetId" type="hidden" th:value="${payCheckSheet?.payCheckSheetId}">
        <!--承运商id-->
        <input name="carrierId" type="hidden" th:value="${payCheckSheet?.carrierId}">
        <input id="balaType" type="hidden" th:value="${carrier?.balaType}">
        <!-- 申请金额-->
        <input name="applicationAmount"  type="hidden" th:value="${payCheckSheet?.applicationAmount}" class="form-control">
        <!--行号-->
        <input type="hidden" name="bankNo" id="bankNo">
        <!--是否为油卡申请-->
        <input name="isOil" type="hidden" value="0">
        <div class="panel-group" id="accordion" th:if="${maxAmount != 0}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">申请信息&nbsp;&nbsp;&nbsp;
                            <span style="color:#f8ac59">铭源总应付：[[${MYSum}]]&nbsp;&nbsp;&nbsp;  吉华总应付：[[${JHSum}]]&nbsp;&nbsp; (金额若有偏差请手动核实)</span>
                            <span style="color:#ed5565" th:if="${cardNum != 0}">&nbsp;&nbsp;&nbsp; 存在[[${cardNum}]]张油卡未核销</span>
                        </a>

                    </h4>
                </div>
                <div id="collapseOne" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <!--基础信息 begin-->
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span class="fcff">* </span>账户类型：</label>
                                    <div class="col-sm-8">
                                        <select name="accountType" id="accountType" class="form-control valid"  th:with="type=${@dict.getType('collection_type')}" onchange="accountTypeChange()" >
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span class="fcff">* </span>申请金额：</label>
                                    <input name="totalAmount" id="money" type="hidden" th:value="${maxAmount}" class="form-control" >
                                    <!--不符合无车承运人-->
                                    <div class="col-sm-8" th:if="${payCheckSheet.isNtocc == 0}">
                                        <input name="payAmount" id="payAmount" type="text" oninput="$.numberUtil.onlyNumberTwoDecimal(this);calReality()" required  maxlength="15" th:value="${maxAmount}" class="form-control">
                                    </div>
                                    <!--符合无车承运人-->
                                    <div class="col-sm-8" th:if="${payCheckSheet.isNtocc == 1}">
                                        <input name="payAmount" id="payAmount" type="text"
                                               oninput="$.numberUtil.onlyNumberTwoDecimal(this);calReality()" required  maxlength="15"
                                               th:value="${maxAmount}"
                                               class="form-control" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <div class="felx ed5565">
                                        <div class="piao">
                                            <div th:text="${carrier.advancePayMoneyXj}"></div>
                                        </div>
                                        <div class="ml10">
                                            <div>实际支付 <span id="reality">0</span> </div>
                                            <div>系统会优先抵扣预付款</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span class="fcff">* </span>申请日期：</label>
                                    <div class="col-sm-8">
                                        <input name="payDate" id="payDate" class="form-control" readonly>
                                    </div>
                                </div>
                            </div>


                            <!--<div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4" >转出账户：</label>
                                    &lt;!&ndash;<div class="col-sm-8">
                                        <select name="outAccount" class="form-control">
                                            <option value=""></option>
                                            <option th:each="dict : ${account}" th:text="${dict.accountName}" th:value="${dict.accountId}" ></option>
                                        </select>
                                    </div>&ndash;&gt;
                                    <div class="col-sm-8">
                                        <div class="input-group">
                                            &lt;!&ndash;账户名称&ndash;&gt;
                                            <input name="accountName" id="accountName" class="form-control valid"
                                                   type="text" aria-required="true" >
                                            &lt;!&ndash;账户id&ndash;&gt;
                                            <input name="outAccount" id="outAccount"  required class="form-control valid"
                                                   type="hidden" aria-required="true">
                                            <div class="input-group-btn">
                                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>-->
                            
                        </div>

                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">付款类型：</label>
                                    <div class="col-sm-8">
                                        <input name="payType" class="form-control" value="0" type="hidden">
                                        <input class="form-control" value="对账付款" disabled >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span class="fcff zhClass">* </span>收款账户：</label>
                                    <div class="col-sm-8">
                                        <select name="recAccount" id="recAccount" class="form-control" required >
                                            <option value=""></option>
                                            <option th:each="dict : ${carrBankList}"
                                                    th:text="${dict.bankAccount}"
                                                    th:id="${dict.carrBankId}"
                                                    th:value="${dict.carrBankId}">
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span class="fcff zhClass">* </span>收款卡号：</label>
                                    <div class="col-sm-8">
                                        <input name="recCardNo" id="recCardNo" class="form-control"  type="text" required disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4"><span class="fcff zhClass">* </span>收款银行：</label>
                                    <div class="col-sm-8">
                                        <input name="recBank" id="recBank" class="form-control"  type="text" required disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6" id="writeFuelcardDiv" style="display: none">
                                <div class="form-group">
                                    <label class="col-sm-4">
                                        核销油卡：</label>
                                    <div class="col-sm-8">
                                        <select name="writeFuelcardId" id="writeFuelcardId" class="form-control selectpicker valid" multiple data-none-selected-text="--请选择--" aria-invalid="false">
                                            <option th:each="fuelcard : ${fuelcardList}" th:text="${fuelcard.fuelcardNo}" th:value="${fuelcard.fuelcardId}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                       <!-- <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4">发票类型：</label>
                                    <div class="col-sm-8">
                                        <select name="checkType" class="form-control" th:with="type=${@dict.getType('billing_type')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" ></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>-->
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-md-2 col-sm-2">支付凭证：</label>
                                        <div class="col-md-8 col-sm-8">
                                            <input id="image" class="form-control" name="image" type="file" multiple >
                                            <input id="tid" name="tid" type="hidden">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-md-2 col-sm-2">票据备注：</label>
                                        <div class="col-md-8 col-sm-8">
                                                <textarea name="checkRemark" maxlength="250" class="form-control valid"
                                                          rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-md-2 col-sm-2">上传发票：</label>
                                        <div class="col-md-8 col-sm-8">
                                            <input type="file" name="receipt" id="receipt" multiple>
                                            <input name="tidReceipt" id="tid_receipt" type="hidden" />
                                        </div>
                                        <div class="col-md-2 col-sm-2">
                                            <a href="javascript:adjustTax()" style="font-weight: bold;cursor: pointer" th:if="${receiptCheck?.status != 1}">
                                                <i class="fa fa-sliders"></i> 调整税额占比
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label class="col-md-2 col-sm-2">其他备注：</label>
                                        <div class="col-md-8 col-sm-8">
                                                <textarea name="memo" maxlength="250" class="form-control valid"
                                                          rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseThree">申请记录</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseThree">
                <div class="panel-body" style="padding: 0 5px 0;">
                    <div class="fixed-table-body" style="margin: 0px -5px;">
                        <table border="0" id="infoTabThree" class="custom-tab table" >

                            <thead>
                            <tr>
                                <th >申请单据号</th>
                                <th >申请金额</th>
                                <th >申请日期</th>
                                <th >转出账户</th>
                                <th >收款账户</th>
                                <th >收款卡号</th>
                                <th >收款银行</th>
                                <th >票据备注</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>

                            </thead>
                            <tbody>

                            <tr th:each="paySheetRecord:${paySheetRecordList}">
                                <td th:text="${paySheetRecord.vbillno}" style="text-align: left"></td>
                                <td th:text="￥+${#numbers.formatDecimal(paySheetRecord.payAmount,1,'COMMA',2,'POINT')}" style="text-align: right"></td>
                                <td th:text="${#dates.format(paySheetRecord.payDate, 'yyyy-MM-dd HH:mm:ss')}" style="text-align: left"></td>
                                <td th:text="${paySheetRecord.accountName}" style="text-align: left"></td>
                                <td th:text="${paySheetRecord.recAccount}" style="text-align: left"></td>
                                <td th:text="${paySheetRecord.recCardNo}" style="text-align: left"></td>
                                <td th:text="${paySheetRecord.recBank}" style="text-align: left"></td>
                                <td th:text="${paySheetRecord.checkRemark}" style="text-align: left"></td>
                                <td th:text="${paySheetRecord.memo}" style="text-align: left"></td>
                                <td><a class="btn btn-warning btn-rounded btn-xs" th:onclick="prints([[${paySheetRecord.paySheetRecordId}]])"><i class="fa fa-pencil"></i>&nbsp;打印</a></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFive">已上传发票</a>
                    <a href="javascript:;" onclick="showDelCheckBox(this)" th:if="${receiptList.size() > 0 && receiptCheck?.status != 1}"
                       del-btn style="font-weight: bold;cursor: pointer;margin-left: 5px;color:rgb(64,158,255)">编辑</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseFive">
                <div class="panel-body">
                    <div th:if="${receiptList.size() > 0}">
                        <span th:each="receipt: ${receiptList}" class="imageBox2 picviewer" style="display: inline-block;margin:0 5px 5px 0;" deleter>
                            <input type="checkbox" del-btn style="display: none" th:file-id="${receipt.fileId}" th:tid="${receipt.tid}">
                            <img style="height:50px;" th:src="${receipt.filePath}"/>
                        </span>
                        <div style="float:right;" th:if="${receiptCheck?.status != 1}">
                            <a href="javascript:;" del-btn style="display: none;font-weight: bold" onclick="delImg()">删除勾选</a>
                            <br>
                            <span id="delImgInfo" style="color:rgb(64,158,255)"></span>
                        </div>
                    </div>
                    <div th:if="${receiptList.size() == 0}"><span style="margin:0 5px 5px 0">无</span></div>
                </div>
            </div>
        </div>

        <div class="panel panel-default" th:if="${maxAmount == 0}"><!-- 全部申请完了再显示 -->
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion"
                       href="tabs_panels.html#collapseFour">税票调整</a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" id="collapseFour">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-md-1 col-sm-2">税额占比：</label>
                                <div class="col-md-11 col-sm-10">
                                    <span id="sumTaxTxt"></span>
                                    <a href="javascript:adjustTax()" style="font-weight: bold;cursor: pointer;margin-left: 10px;" th:if="${receiptCheck?.status != 1}">
                                        <i class="fa fa-sliders"></i> 调整税额占比
                                    </a>
                                </div>
                            </div>
                            <div class="form-group" th:if="${receiptCheck?.status != 1}">
                                <label class="col-md-1 col-sm-2">追加发票：</label>
                                <div class="col-md-11 col-sm-10">
                                    <input type="file" name="receipt2" id="receipt2" multiple>
                                    <input name="tidReceipt" id="tid_receipt2" type="hidden" />
                                </div>
                            </div>
                            <div class="form-group" th:unless="${receiptCheck?.status != 1}">
                                <label class="col-md-1 col-sm-2"></label><div class="col-md-11 col-sm-10"><span style="color: lightcoral;font-weight: bold">发票已确认</span></div>
                            </div>
                        </div>
                        <div style="margin-bottom: 5px;text-align: center">
                            <input type="button" class="btn btn-sm btn-success" value="提交税票调整" onclick="saveTaxReceipt()" th:if="${receiptCheck?.status != 1}">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <input type="hidden" name="taxSplitJson">
        <input type="hidden" name="deletedImageJson">
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary"  onclick="submitHandler()" th:if="${maxAmount != 0}"><i class="fa fa-check"></i>保
            存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger"  onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "payCheckSheet";
    //最大申请金额
    var money = parseFloat($("#money").val());
    //默认当日日期
    var time = new Date();
    var day = ("0" + time.getDate()).slice(-2);
    var month = ("0" + (time.getMonth() + 1)).slice(-2);
    var h=time.getHours();       //获取当前小时数(0-23)
    var m=time.getMinutes();     //获取当前分钟数(0-59)
    var s=time.getSeconds();     //获取当前秒数
    var today = time.getFullYear() + "-" + (month) + "-" + (day) + " "+h+":" +m+ ":"+s;
    $("#payDate").val(today);

    var carrBankList = [[${carrBankList}]];
    var specialCarrBankList = [[${specialCarrBankList}]];
    var oilCarrBankList = [[${oilCarrBankList}]];
    var oilPayCarrBankList = [[${oilPayCarrBankList}]];
    var exceptionPayCarrBanks = [[${exceptionPayCarrBanks}]];
    var billingType = [[${@dict.getType('billing_type')}]];//开票类型[{"dictLabel":"****","dictValue":"***"}]

    $(function () {


        $('#collapseOne').collapse('show');
        $('#collapseThree').collapse('show');
        /** 校验 */
        $("#form-payAppl-add").validate({
            focusCleanup: true
        });

        /**
         * 初始化日期控件
         */
        layui.use('laydate', function() {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#payDate',
                type: 'datetime',
                trigger: 'click'
            });
        });

        //加载收款信息
        $('#recAccount').change(function(){
            var id = $(this).find(":selected").attr("value");
            var data = {carrBankId:id};
            var url = ctx + "payManage/selectReceInfo";
            $.ajax({
                url : url,
                method : 'POST',
                data : data,
                success:function (data) {
                    $("#recCardNo").val(data.bankCard);
                    $("#recBank").val(data.bankName);
                    $("#bankNo").val(data.bankNo);

                    //手动校验
                    $("#form-payAppl-add").validate().element($("#recCardNo"));
                    $("#form-payAppl-add").validate().element($("#recBank"));
                }
            })

        });
        /*[# th:if="${maxAmount > 0}"]*/
        var picParam = {
            maxFileCount:0,
            publish: "uploadSuccess",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        var tid = "tid";
        var imageId = "image";
        $.file.initAddFiles(imageId, tid, picParam);

        // 图片上传成功后
        $("#image").on('filebatchuploadsuccess', function (event, data) {
            var tid = data.response.tid;
            $("#tid").val(tid);
            if ($('#receipt').fileinput('getFilesCount') > 0) {
                $("#receipt").fileinput('upload');
            } else {
                //表单提交
                submit();
            }
        });
        $('#image').on('filesuccessremove', function(event, key, index) {//filepreremove（未上传文件删除前）、fileremoved（未上传文件删除后）
            console.log(event, key, index)
            //return false; 阻止删除
            // 可根据index和tid（或配合filebatchuploadsuccess的回调参数data）删除后台数据，根据后台返回剩余附件数，控制是否清空tid，当前采用直接清空所有的方式
            $('#image').fileinput('clear');
            $('#tid').val('');
        });

        $.file.initAddFiles("receipt", "tid_receipt", {
            maxFileCount:0,
            fileType: null,
            //dropZoneTitle: [[${@dict.getLabel('billing_type', lotBillingType)}]]
        });
        $("#receipt").on('filebatchuploadsuccess', function (event, data) {
            console.log(event, data)
            //var tid = data.response.tid;
            //$("#tid_receipt").val(tid);
            //表单提交
            submit();
        });
        $('#receipt').on('filesuccessremove', function(event, key, index) {
            $('#receipt').fileinput('clear');
            $('#tid_receipt').val('');
        });
        /*[/]*/

        /*[# th:if="${maxAmount == 0}"]*/
        $.file.initAddFiles("receipt2", "tid_receipt2", {
            maxFileCount:0,
            fileType: null,
        });
        $("#receipt2").on('filebatchuploadsuccess', function (event, data) {
            $.operate.saveTab(prefix + "/saveTaxReceipt", $('#form-payAppl-add').serialize());
        });
        $('#receipt2').on('filesuccessremove', function(event, key, index) {
            $('#receipt2').fileinput('clear');
            $('#tid_receipt2').val('');
        });
        /*[/]*/

        var sumTaxTxt = [[${sumTaxTxt}]];
        if (sumTaxTxt) {
            var result = [];
            result.push("[")
            var sumTaxTxtArr = sumTaxTxt.split(",");
            for (let i = 0; i < sumTaxTxtArr.length; i++) {
                if (i > 0) {
                    result.push("] [");
                }
                var type_amount = sumTaxTxtArr[i].split(":");
                var find = false;
                for (let j = 0; j < billingType.length; j++) {
                    if (type_amount[0] == billingType[j].dictValue) {
                        find = true;
                        result.push(billingType[j].dictLabel,":",type_amount[1]);
                        break;
                    }
                }
                if (!find) {
                    result.push(type_amount[0],":",type_amount[1]);
                }
            }
            result.push("]")
            $("#sumTaxTxt").text(result.join(""))
        }
        calReality();
    });

    function calReality(){
        let maxAmount=$("#payAmount").val();
        if(maxAmount){
            let advancePayMoney=[[${carrier.advancePayMoneyXj}]];
            let num=0;
            if(parseFloat(maxAmount) > parseFloat(advancePayMoney) ){
                num= parseFloat(maxAmount) - parseFloat(advancePayMoney);
            }else{
                num=0;
            }

            $("#reality").text(num);
        }
    }


    //提交
    function submitHandler() {
        
        $("[name=taxSplitJson]").val(adjustCache)
        $("[name=deletedImageJson]").val(JSON.stringify(deletedImage))
        var payAmount = parseFloat($("#payAmount").val());
        if(payAmount>money){
            $.modal.alertError("付款金额必须<="+money);
            return ;
        }
        if ($.validate.form()) {
            let tip = "确认提交吗?";
            if($("#accountType").val() == 4 && $("#balaType").val() == 2){
                tip = "月结承运商异常核销将由系统自动处理，确认提交吗?";
            }
            $.modal.confirm(tip, function() {
                if($('#image').fileinput('getFilesCount') == 0 && ($('#receipt').length == 0 || $('#receipt').fileinput('getFilesCount') == 0)) {
                    submit();
                } else if ($('#image').fileinput('getFilesCount') > 0) {
                    // 表单提交
                    $.modal.loading("正在处理中，请稍后...");
                    $("#image").fileinput('upload');
                } else if ($('#receipt').fileinput('getFilesCount') > 0) {
                    // 表单提交
                    $.modal.loading("正在处理中，请稍后...");
                    $("#receipt").fileinput('upload');
                }
            });

        }
    }

    function submit(){
        //移除disabled
        $('#recCardNo').removeAttr("disabled");
        $('#recBank').removeAttr("disabled");
        $('#payAmount').removeAttr("disabled");
        var payAmount = parseFloat($("#payAmount").val());
        if(payAmount>money){
            $.modal.alertError("付款金额必须<="+money);
            $('#recCardNo').attr("disabled","true");
            $('#recBank').attr("disabled","true");
            $('#payAmount').attr("disabled","true");
            return ;
        }
        var data = $.common.formToJSON("form-payAppl-add");
        if($("#writeFuelcardId").val() != null && $("#writeFuelcardId").val().length != 0){
            //console.log($("#writeFuelcardId").val().join(','))
            var length = $("#writeFuelcardId").val().length;
            var payAmountSmallest = (length-1)*20;
            var payAmountBiggest = length*20;
            if(money < payAmountSmallest){
                $.modal.alertError("付款金额不够核销"+length+"张油卡");
                $('#recCardNo').attr("disabled","true");
                $('#recBank').attr("disabled","true");
                $('#payAmount').attr("disabled","true");
                return ;
            }else if(money > payAmountBiggest){
                if(payAmount != payAmountBiggest){
                    $.modal.alertError("油卡核销付款金额必须为"+payAmountBiggest+"元");
                    $('#recCardNo').attr("disabled","true");
                    $('#recBank').attr("disabled","true");
                    $('#payAmount').attr("disabled","true");
                    return ;
                }
            }else{
                if(payAmount != money){
                    $.modal.alertError("油卡核销付款金额必须为"+money+"元");
                    $('#recCardNo').attr("disabled","true");
                    $('#recBank').attr("disabled","true");
                    $('#payAmount').attr("disabled","true");
                    return ;
                }
            }
            data.writeFuelcardId = $("#writeFuelcardId").val().join(',');
        }

        $.operate.saveTab(prefix + "/savePayAppl", data,function (r) {
            if (r.code != 0) {
                $('#recCardNo').attr("disabled", true);
                $('#recBank').attr("disabled", true);
                /*$('#payAmount').attr("disabled", true);*/
            }
        });
    }

    //选择账户
    function selectAccount(){
        var url = "/finance/account/selectAccount";
        $.modal.open("选择账户", url);
    }

    //收款类型调整
    function accountTypeChange(){
        $("#recCardNo").val("");
        $("#recBank").val("");
        $("#bankNo").val("");

        $(".zhClass").css('display', 'inline');
        $("#recAccount").attr("required","true");
        $("#recCardNo").attr("required","true");
        $("#recBank").attr("required","true");


        var str = '<option value="">--请选择--</option>';
        var accountType = $("#accountType").val();
        if(accountType == 0){
            $("#writeFuelcardDiv").css('display', 'none');
            //承运商收款人
            for(var i = 0 ; i < carrBankList.length ; i++){
                str = str +  '<option value="'+carrBankList[i].carrBankId+'">'+carrBankList[i].bankAccount+"</option>";
            }

        }else if(accountType == 1){
            $("#writeFuelcardDiv").css('display', 'none');
            //专项收款人
            for(var i = 0 ; i < specialCarrBankList.length ; i++){
                str = str +  '<option value="'+specialCarrBankList[i].carrBankId+'">'+specialCarrBankList[i].bankAccount+"</option>";
            }
        }else if(accountType == 2){
            $("#writeFuelcardDiv").css('display', 'none');
            //异常收款人
            for(var i = 0 ; i < oilCarrBankList.length ; i++){
                str = str +  '<option value="'+oilCarrBankList[i].carrBankId+'">'+oilCarrBankList[i].bankAccount+"</option>";
            }
        }else if(accountType == 3){
            $("#writeFuelcardDiv").css('display', 'block');
            //异常收款人
            for(var i = 0 ; i < oilPayCarrBankList.length ; i++){
                str = str +  '<option value="'+oilPayCarrBankList[i].carrBankId+'">'+oilPayCarrBankList[i].bankAccount+"</option>";
            }
        }else if(accountType == 4){
            $("#writeFuelcardDiv").css('display', 'block');
            //异常收款人
            for(var i = 0 ; i < exceptionPayCarrBanks.length ; i++){
                str = str +  '<option value="'+exceptionPayCarrBanks[i].carrBankId+'">'+exceptionPayCarrBanks[i].bankAccount+"</option>";
            }
        }else  if(accountType == 5) {

            $("#writeFuelcardDiv").css('display', 'none');
            //异常收款人
            for(var i = 0 ; i < oilCarrBankList.length ; i++){
                str = str +  '<option value="'+oilCarrBankList[i].carrBankId+'">'+oilCarrBankList[i].bankAccount+"</option>";
            }
            $(".zhClass").css('display', 'none');
            $("#recAccount").removeAttr("required")
            $("#recCardNo").removeAttr("required")
            $("#recBank").removeAttr("required")

        }
        $("#recAccount").html(str);
    }

    /**
     * 关键字提示查询 转入账户
     */
    $("#accountName").bsSuggest('init', {
        url: ctx + "finance/account/findAccount?paymentType=1&keyword=",
        indexId: 0,
        showBtn: false,
        allowNoKeyword: false,
        getDataMethod: "url",
        keyField: "accountName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        effectiveFields: ["accountName"],
        delay: 300,
        searchingTip: '搜索中...',
        hideOnSelect: true,
        maxOptionCount: 10,
        inputWarnColor: '',
    }).on('onSetSelectValue', function (e, keyword, data) {//当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        $("#accountName").val(data.accountName);
        $("#outAccount").val(data.accountId);
    })

    function prints(paySheetRecordId){
        var url =  prefix + "/payPrintFee?paySheetRecordId="+paySheetRecordId;
        $.modal.openTab('月度对账费用申请单',url);
    }

    var adjustCache = null;
    function adjustTax() {
        $.modal.openFull("调整税额占比", prefix + "/detailTax/[(${payCheckSheet.payCheckSheetId})]")
    }

    function saveTaxReceipt() {
        if (deletedImage.length==0 && !adjustCache && $('#receipt2').fileinput('getFilesCount') == 0) {
            $.modal.msgWarning("税额占比和发票至少要提交一项");
            return;
        }
        $.modal.confirm("确认提交吗?", function() {
            $("[name=taxSplitJson]").val(adjustCache)
            $("[name=deletedImageJson]").val(JSON.stringify(deletedImage))
            if($('#receipt2').fileinput('getFilesCount') == 0) {
                $.operate.saveTab(prefix + "/saveTaxReceipt", $('#form-payAppl-add').serialize());
            } else {
                // 表单提交
                $.modal.loading("正在处理中，请稍后...");
                $("#receipt2").fileinput('upload');
            }
        });
    }

    function sumTaxTxtChange(sumTaxTxt) {
        if ($("#sumTaxTxt").text() != sumTaxTxt) {
            $.modal.msgSuccess("税额占比已暂存")
            $("#sumTaxTxt").html(sumTaxTxt + "<span style='color:blue'>（暂存，提交后生效）</span>")
        }
    }

    function showDelCheckBox(a) {
        $("[del-btn]").toggle()
    }

    var deletedImage = []

    function delImg() {
        if ($(":checked[del-btn]").length == 0) {
            $.modal.msgWarning("尚未选择要删除的发票")
        } else {
            $.modal.confirm("确认删除吗？", function(){
                $(":checked[del-btn]").each(function (i) {
                    deletedImage.push({fileId:$(this).attr("file-id"), tid:$(this).attr("tid")})
                    $(this).parent().remove()
                })
                $("#delImgInfo").text("已删除" + deletedImage.length + "张图片，提交后生效")
                $("[del-btn]").toggle()
            })
        }
    }
</script>
</body>
</html>