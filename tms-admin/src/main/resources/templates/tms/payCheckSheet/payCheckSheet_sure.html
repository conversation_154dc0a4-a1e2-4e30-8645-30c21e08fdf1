<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商对账')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: bootstrap-select-css"/>
</head>
<style>
    .fc1ab{
        color: #1ab394;
    }
    .fc3c{
        color: #3c83f5;
    }
    .fccd3{
        color: #cd321e;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .fw{
        font-weight: bold;
    }
    .tc{
        text-align: center;
    }
    .mt10{
        margin-top: 10px;
    }
    .no-gutter{
        margin: 0 -5px;
    }

    .label-primaryT{
        color: #1ab394;
        background-color: transparent;
        border: 1px solid #1ab394;
    }
    .label-successT{
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }
    .label-defaultT{
        color: #5e5e5e;
        background-color: transparent;
        border: 1px solid #5e5e5e;
    }
    .label-warningT{
        color: #f8ac59;
        background-color: transparent;
        border: 1px solid #f8ac59;
    }
    .label-infoT{
        color: #23c6c8;
        background-color: transparent;
        border: 1px solid #23c6c8;
    }
    .label-dangerT{
        color: #ed5565;
        background-color: transparent;
        border: 1px solid #ed5565;
    }
    .label-inverseT{
        color: #262626;
        background-color: transparent;
        border: 1px solid #262626;
    }

    td{
        position:relative
    }
    label.error{
        top:10px !important;
    }
    .flex{
        display: flex;
        align-items: center;
        justify-content: flex-start;
    }
    .flex_left{
        /* width: 80px; */
        line-height: 26px;
        /* text-align: right; */
        color: #808080;
        margin: 0;
    }
    .flex_right{
        /* min-width:0; */
        /* flex:1; */
        line-height: 26px;
    }
    .tear{
        text-align: right;
        padding-right: 0;
    }
    .tear+div{
        padding-left: 0;
    }
    a.add-alink {
        /*display: inline-block;*/
        /*width: 20px;*/
        /*height: 20px;*/
        /*border-radius: 2px;*/
        /*text-align: center;*/
        /*line-height: 20px;*/
        color: #1ab394;
        font-size: 25px;
        background-color: #eff3f8;
    }
    a.del-alink {
        display: inline-block;
        /*width: 20px;*/
        /*height: 20px;*/
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
        /*color: #fff;*/
        /*font-size: 16px;*/
        background-color: #fd8481;
    }
    .fcff3{
        color: #ff3636;
    }
    .ab394{color: #1ab394;}
    /*table{table-layout:fixed;width:100%;}*/
    .eclipse{
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .fy th{
        text-align: left;
    }
    .fy td{
        text-align: left;
    }


    .ontooltip {
        /* width: 500px; */
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }
    .leftIcon{
        position: relative;
        margin: 0;
    }
    .leftIcon::after{
        content:" ";
        position: absolute;
        width: 1px;
        height: 100%;
        border-left: 1px dashed #cdcdcd;
        top: 20px;
        left: 4px;
    }
    .leftIcon:last-child::after{
        height: 50%;
    }
    /* .leftIcon:first-child{
        font-size: 14px;
        color: #1ab394;
    } */
    .vertical-timeline-icon{
        /* width: 10px;
        height: 10px; */
        background-color: transparent;
        border: 0;
        top: 16px;
        left: -16px;
        z-index: 1;
    }
    .vertical-timeline-content{
        margin-left: 30px;
        padding: 2em 1em;
    }
    .panel-body {
        padding: 5px 10px;
    }
    .tooltip-inner{
        min-width: 500px !important;
    }
    .textNum{
        color: #1ab394;
        border-bottom: 1px dashed #1ab394;
        cursor:pointer;
    }

    .tobox{
        padding: 10px;
        background-color: #eff3f8;
        margin: 0 10px 10px;
        width: calc(100% - 20px);
        border-radius: 5px;
    }
    .f8ac59{
        color: #f8ac59;
    }
    .ec4758{
        color: #ec4758;
    }
    .file-drop-zone{
        overflow: auto;
    }
    .btn-warningT{
        color: #f7a54a;
        background-color: transparent;
        border: 1px solid #f7a54a;
    }
    .btn-primaryT{
        color: #18a689;
        background-color: transparent;
        border: 1px solid #18a689;
    }
    .f16{
        font-size: 16px;
    }
    .pa2{
        padding: 2px;
        font-weight: 100;
        margin-bottom: 4px;
        display: inline-block;
        margin-right: 5px;
    }
    .container-div{
        padding: 0px 15px;
    }
    .search-collapse, .select-table{
        margin: 0;
        border-radius:0;
        padding: 5px;
    }
    .search-collapse{
        background-color: #F7F7F7;
    }
    .form-group{
        margin: 0;
    }
    .row + .row{
        margin-top: 5px;
    }
    .btn-group-sm>.btn, .btn-sm{
        padding: 3px 10px;
    }
    .table-striped {
        height: calc(100% - 40px);
        padding-top: 0;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                           <label class="col-sm-4">承运商名称：</label>-->
                            <div class="col-sm-12">
                                <input type="hidden" name="carrCode" th:value="${carrCode}">
                                <input name="carrierName"  id="carrierName" placeholder="请输入承运商名称" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <div class="form-group">
                            <!--                                <label class="col-sm-4">要求提货日：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateStart" name="params[reqDeliDateStart]" placeholder="要求提货开始日">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="reqDeliDateEnd" name="params[reqDeliDateEnd]" placeholder="要求提货结束日">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-6">
                        <div class="form-group">
                            <!--                                <label class="col-sm-4">要求提货日：</label>-->
                            <div class="col-sm-12">
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="carrierSubmitTimeStart" name="params[carrierSubmitTimeStart]" placeholder="提交时间开始">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" style="width: 45%; float: left;" class="form-control"
                                       id="carrierSubmitTimeEnd" name="params[carrierSubmitTimeEnd]" placeholder="提交时间结束">
                            </div>
                        </div>
                    </div>
                    <!-- </div>
                    <div class="row"> -->
                    <!-- <div class="col-md-6 col-sm-6"></div> -->
                    <div class="col-md-2 col-sm-4">
                        <!-- <label class="col-sm-6"></label> -->
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary multiple disabled" onclick="affirm()" shiro:hasPermission="finance:payCheckSheet:affirm">
                <i class="fa fa-check-circle-o"></i> 确认
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                <i class="fa fa-download"></i> 导出
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<script id="scanCodeHtml" type="text/template">
    <div class="form-content">
        <div class="row">
            <div class="col-md-12 col-sm-12 tobox">
                原金额：<span class="f8ac59">￥<span id="originalFee">0</span> </span>
                <span style="margin: 0 10px;"><i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i></span>
                调整后金额：<span class="ec4758">￥<span id="adjustFee">0</span> </span>
            </div>
        </div>

        <div class="row" id="feeTypeDiv">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-3 tear"><span class="fcff3">*</span>运费类型：</label>
                    <div class="col-xs-6">

                        <select name="freeType" id="freeType" class="form-control valid" onchange="onFeeTypeChange(this)">
                            <option value="">-- 请选择 --</option>
                            <option value="0">运费</option>
                            <option value="1">在途费用</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt5" id="costTypeDiv">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-3 tear"><span class="fcff3">*</span>费用明细：</label>
                    <div class="col-xs-6">
                        <select name="costType" id="costType" class="form-control valid costType" >
                            <option value="">-- 请选择 --</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt5">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-3 tear"><span class="fcff3">*</span>调整金额（元）：</label>
                    <div class="col-xs-6">
                        <input name="adjustAmount" id="adjustAmount"  class="form-control" type="text" autocomplete="off" onkeyup="value=value.replace(/[^\-?\d.]/g,'')" onchange="adjustment(this)">
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt5">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-3 tear"><span>附件上传：</span></label>
                    <div class="col-xs-6">
                        <div class="form-group">
                            <input id="image" class="form-control" name="image" type="file" multiple>
                            <input id="tid" name="tid" type="hidden" >
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-xs-3 tear"><span class="fcff3">*</span><span>修改原因：</span></label>
                    <div class="col-xs-6">
                        <textarea name="adjustMemo" id="adjustMemo" placeholder="请输入" maxlength="200" class="form-control valid" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>
<script th:inline="javascript">

    var status = [[${status}]];

    var prefix = ctx + "payCheckSheet";
    console.log(prefix)
    var totalAmount = 0;//总金额
    var oilAmount = 0;//油卡金额
    var cashAmount = 0;//现金金额
    var applicationAmountOil = 0;//油卡申请金额
    var cashApplicationAmount = 0;//现金申请金额
    var gotAmount = 0;//已付金额
    var ungotAmount = 0;//未付金额
    var amountApplied = 0;//已申请金额

    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;
    $(function () {
        var options = {
            url: prefix + "/sureList?carrierConfirmStatus=0",
            createUrl: prefix + "/add",
            showToggle:false,
            showColumns:false,
            exportUrl: prefix + "/exportSureList",
            modalName: "对账确认",
            fixedColumns: false,
            fixedNumber:8,
            height: 560,
            clickToSelect:true,
            showFooter:true,
            onPostBody:function () {
                //合并页脚
                merge_footer();
                //查询合计总金额
                getAmountCount();
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            columns: [{
                checkbox: true,
            },
                {
                    title: '操作',
                    align: 'left',
                    field: 'entrustLotId',
                    formatter: function(value, row, index) {

                    },
                    visible:false

                },
                {
                    title: '承运商信息',
                    align: 'left',
                    field: 'carrierName',
                    formatter:function status(value, row) {
                        return $.table.tooltip(value)+'<br/>'+ $.table.tooltip(row.legalCard);
                    }
                },
                {
                    title: '运单号/对账单状态',
                    field: 'carrierConfirmStatus',
                    align: 'left',
                    formatter: function(value, row, index){
                        var status = ''
                        switch(value) {
                            case 0:
                                status = '<span>已确认</label>';
                            case 1:
                                status = '<span>未确认</label>';
                            default:
                                break;
                        }
                        let htmlText;
                        htmlText= '<span class="fc3c">' + row.lot + '</span>'+"<br/>" +status
                        return htmlText
                    }

                },
                {
                    title: '要求提货/到货日期',
                    field: 'reqDeliDate',
                    align: 'left',
                    formatter: function(value, row, index){
                        let htmlText;
                        htmlText= getValue(value)+"<br/>" +getValue(row.reqArriDate)
                        return htmlText
                    }
                },
                {
                    title: '装卸货地址',
                    align: 'left',
                    formatter: function status(value,row) {
                        let deliAddr=row.deliProvinceName+row.deliCityName+row.deliAreaName;
                        let arriAddr=row.arriProvinceName+row.arriCityName+row.arriAreaName;
                        return '<span class="label label-warning pa2">装</span>'+$.table.tooltip(deliAddr)+'<br/><span class="label label-success pa2">卸</span>'+$.table.tooltip(arriAddr);
                    }
                },
                {
                    title: '货品信息',
                    align: 'left',
                    field: 'goodsName',
                    width: '140px',
                    formatter: function (value, row, index) {
                        let goods=[]
                        if(row.numCount){
                            goods.push(row.numCount+'件');
                        }
                        if(row.weightCount){
                            goods.push(row.weightCount+'吨');
                        }
                        if(row.volumeCount){
                            goods.push(row.volumeCount+'m³');
                        }
                        return $.table.tooltip(value)+"<br/>"+goods.join(' | ');
                    }
                },
                {
                    title: '车辆信息',
                    align: 'left',
                    field: 'carLenName',
                    formatter: function status(row,value) {
                        if(value.carNo == null || value.carNo == ''){
                            value.carNo = '';
                        }
                        if(value.carLenName == null || value.carLenName == ''){
                            value.carLenName = '';
                        }
                        if(value.carTypeName == null || value.carTypeName == ''){
                            value.carTypeName = '';
                        }

                        return value.carNo+"<br/>"+value.carLenName+value.carTypeName;
                    }
                },/*{
                    title: '对账重量/体积/件数',
                    field: 'carrierWeight',
                    formatter:function(value,row){
                        var weight     = value             == null ? 0 : value;
                        var weightCnt  = row.weightCount   == null ? 0 : row.weightCount;
                        var volume     = row.carrierVolume == null ? 0 : row.carrierVolume;
                        var volumeCnt  = row.volumeCount   == null ? 0 : row.volumeCount;
                        var num        = row.carrierNum    == null ? 0 : row.carrierNum;
                        var numCount   = row.numCount      == null ? 0 : row.numCount;

                        var retArr = [];
                        retArr.push(compareValue(weight,weightCnt));
                        retArr.push(compareValue(volume,volumeCnt));
                        retArr.push(compareValue(num,numCount));
                        return retArr.join("/");
                    }
                },*/
                {
                    title: '用户对账现金/油卡/在途(元)',
                    field: 'carrierCashFee',
                    align: 'right',
                    formatter: function(value, row, index){
                        var total = value+ row.carrierOilFee + row.carrierOnWayFee
                        let htmlText;
                        htmlText= '总'+'&nbsp;&nbsp;'+ total +"<br/>" +
                            '<span>' + value+'&nbsp;&nbsp;'+ '/'+ '&nbsp;&nbsp;'+row.carrierOilFee +'&nbsp;&nbsp;'+ '/' +'&nbsp;&nbsp;'+row.carrierOnWayFee + '</span>'
                        return htmlText
                    }
                },
                {
                    title: '系统结算现金/油卡/在途(元)',
                    field: 'frightFee',
                    align: 'right',
                    formatter: function(value, row, index){
                        let data = JSON.stringify(row)
                        var total = value+ row.oilFee + row.onWayFee
                        var total1 =  row.carrierCashFee+ row.carrierOilFee + row.carrierOnWayFee
                        var lotId = "'"+row.entrustLotId+"'"
                        var span1 = ''
                        var span2 = ''
                        var span3 = ''
                        var span4 = ''
                        if(total > total1){
                            span1 = '总'+'&nbsp;&nbsp;'+ '<span class="fccd3">'+total +'</span>'+"<br/>"
                        }else {
                            span1 = '总'+'&nbsp;&nbsp;'+ '<span class="fc1ab">'+total +'</span>'+"<br/>"
                        }
                        if(value > row.carrierCashFee){
                            span2 = '<font class="fccd3">'+value+'</font>'
                        }else {
                            span2 = '<font class="fc1ab">'+value+'</font>'
                        }
                        if(row.oilFee > row.carrierOilFee){
                            span3 = '&nbsp;&nbsp;'+ '/'+'&nbsp;&nbsp;'+'<font class="fccd3">'+row.oilFee+'</font>'
                        }else {
                            span3 = '&nbsp;&nbsp;'+ '/'+'&nbsp;&nbsp;'+'<font class="fc1ab">'+row.oilFee+'</font>'
                        }
                        if(row.onWayFee > row.carrierOnWayFee){
                            span4 = '&nbsp;&nbsp;'+ '/' +'<font class="fccd3">'+'&nbsp;&nbsp;'+row.onWayFee+'</font>'
                        }else {
                            span4 = '&nbsp;&nbsp;'+ '/' +'<font class="fc1ab">'+'&nbsp;&nbsp;'+row.onWayFee+'</font>'
                        }
                        let htmlText;
                        htmlText= span1+
                            '<span class="" style="cursor: pointer;border-bottom: 1px #4381e8 dashed;display: inline-block" onclick="reviseNum(' + total + ','+lotId+')">' + span2+span3 +span4 + '</span>'
                        return htmlText
                    }
                },
                {
                    title: '提交日期',
                    field: 'carrierSubmitTime',
                    align: 'left',
                }
            ]
        };

        function compareValue(v1,v2){
            if(v1 > v2){
                return "<span class='fccd3' >" + v1 + "</span>";
            }else if(v1 < v2){
                return "<span class='fc1ab' >" + v1 + "</span>";
            }else{
                return v1;
            }
        }

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });
    });

    /**
     * 初始化日期控件
     */
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#reqDeliDateStart',
            type: 'date',
            trigger: 'click'
        });
        laydate.render({
            elem: '#reqDeliDateEnd',
            type: 'date',
            trigger: 'click'
        });
        laydate.render({
            elem: '#carrierSubmitTimeStart',
            type: 'date',
            trigger: 'click'
        });
        laydate.render({
            elem: '#carrierSubmitTimeEnd',
            type: 'date',
            trigger: 'click'
        });
    });

    function getValue(val){
        if(val == null){
            val = "-";
        }
        return val
    }

    //往来明细
    function payExchangeDetails(carrierId) {
        if(carrierId == null){
            var year = $.table.selectColumns('year');
            var month = $.table.selectColumns('month');
            var carrierId = $.table.selectColumns('carrierId');
        }
        var url = prefix + "/payExchangeDetails?carrierId="+carrierId+"&year="+year+"&month="+month;
        $.modal.openTab("往来明细", url);
    }

    //付款现金申请
    function payAppl(){
        var id = $.table.selectColumns('payCheckSheetId').join();//id
        var totalAmount = parseFloat($.table.selectColumns('totalAmount').join());//总金额
        var oilAmount = $.table.selectColumns('oilAmount').join();
        oilAmount = parseFloat(oilAmount == '' ? '0' : oilAmount);//油卡金额

        var applicationAmount = parseFloat($.table.selectColumns('applicationAmount').join());//已申请金额（现金）

        var balaType =  $.table.selectColumns('balaType').join();
        if(status != '1' && balaType != '1'){
            $.modal.alertWarning("该页面无法进行付款现金申请");
            return ;
        }

        var vbillstatus = $.table.selectColumns('vbillstatus').join();//对账单状态
        if(vbillstatus == 0){
            $.modal.alertError("该账单在新建状态下，不能付款申请");
            return ;
        }


        //暂注释：无法申请时 只展示申请记录
        // if(totalAmount-oilAmount == applicationAmount){
        //     $.modal.alertError("该账单已申请完成");
        //     return ;
        // }
        if (checklock(id)) {
            var url = prefix + "/payAppl/" + id;
            $.modal.openTab('付款现金申请', url);
        }
    }

    /**
     * 油卡申请
     */
    function payApplOil() {
        var id = $.table.selectColumns('payCheckSheetId').join();//id
        var oilAmount = $.table.selectColumns('oilAmount').join();
        oilAmount = parseFloat(oilAmount == '' ? '0' : oilAmount);//油卡金额

        var applicationAmountOil = $.table.selectColumns('applicationAmountOil').join();
        applicationAmountOil = parseFloat(applicationAmountOil == '' ? '0' : applicationAmountOil);//已申请金额(油卡)

        var balaType =  $.table.selectColumns('balaType').join();
        if(status != '1' && balaType != '1'){
            $.modal.alertWarning("该页面无法进行付款油卡申请");
            return ;
        }

        var vbillstatus = $.table.selectColumns('vbillstatus').join();//对账单状态
        if(vbillstatus == 0){
            $.modal.alertError("该账单在新建状态下，不能付款申请");
            return ;
        }

        //暂注释：无法申请时 只展示申请记录
        // if(oilAmount == applicationAmountOil){
        //     $.modal.alertError("该账单已申请完成");
        //     return ;
        // }
        if (checklock(id)) {
            var url = prefix + "/payApplOil/" + id;
            $.modal.openTab('付款油卡申请', url);
        }
    }

    function checklock(payCheckSheetid) {
        var pass = false;
        $.ajax({
            url: prefix + "/checkLotLock",
            data: "payCheckSheetId=" + payCheckSheetid,
            type: "post",
            async: false,
            success: function(r) {
                if(r.code===0) {
                    pass = true;
                } else {
                    var arr = r.data;// VBILLNO,LOTNO,LOCK_PAY,SINGLE_LOCK
                    var msg = []
                    for (let i = 0; i < arr.length; i++) {
                        msg.push(arr[i]['VBILLNO'], "对应运单", arr[i]['LOTNO'], arr[i]['LOCK_PAY'] == '1' ? '存在异常跟踪信息，已被锁定' : '已被系统锁定', "；<br>");
                    }
                    msg.push("需要先解锁才能申请付款")
                    $.modal.alertError(msg.join(""));
                }
            }
        });
        return pass;
    }

    // 核销
    function verification() {
        var payCheckSheetIds = $.table.selectColumns('payCheckSheetId');

        var gotAmountList = $.table.selectColumns('gotAmount');
        var totalAmountList = $.table.selectColumns('totalAmount');
        var handVerificationList = $.table.selectColumns('handVerification');
        for (var i = 0; i < gotAmountList.length;i++ ) {
            if (gotAmountList[i] !== totalAmountList[i]) {
                $.modal.alertWarning("请选择已付清的对账单");
                return;
            }
            if (handVerificationList[i] === '0') {
                $.modal.alertWarning("请选择状态为手动核销的对账单");
                return;
            }
        }
        $.operate.post(prefix + "/verification", { "payCheckSheetIds": payCheckSheetIds.join()},location.reload());
    }

    //付款记录
    function payRecord() {
        var id = $.table.selectColumns('payCheckSheetId');
        var url = prefix + "/payRecord/"+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "付款记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });

    }



    // 跳转对应的应付明细页面
    function payDetail(payCheckSheetId) {
        var url = prefix + "/payDetail?payCheckSheetId="+payCheckSheetId;
        $.modal.openTab('应付明细',url);
    }
    /**
     * 确认应付明细
     */
    function affirm() {


        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        var entrustLotIds = $.table.selectColumns("entrustLotId");
        console.log(bootstrapTable)
        if (entrustLotIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var arr = []
        for(var i=0;i<bootstrapTable.length;i++){
            if(bootstrapTable[i].carrierConfirmStatus!=0){
                $.modal.alertWarning("请选择未确认记录");
                return;
            }
            arr[i] = bootstrapTable[i].lot
            if(bootstrapTable[i].carrierCashFee!=undefined && bootstrapTable[i].carrierOilFee!=undefined &&bootstrapTable[i].carrierOnWayFee!=undefined){
                bootstrapTable[i].carrierTotal = bootstrapTable[i].carrierCashFee + bootstrapTable[i].carrierOilFee + bootstrapTable[i].carrierOnWayFee
            }
            if(bootstrapTable[i].frightFee!=undefined && bootstrapTable[i].oilFee!=undefined &&bootstrapTable[i].onWayFee!=undefined){
                bootstrapTable[i].total = bootstrapTable[i].frightFee + bootstrapTable[i].oilFee + bootstrapTable[i].onWayFee
            }
        }
        var str = arr.join(',')
        var html = [];
        html.push('<div style="width: 100%;padding: 10px 10px;box-sizing: border-box">',
            '<table class="custom-tab tab table table-bordered">',
            '<thead>',
            '<tr>',
            '<th style="width: 33%;">运单号</th>',
            '<th style="width: 33%;">用户对账金额(元)</th>',
            '<th>系统结算金额(元)</th>',
            '</tr>',
            '</thead>',
            '<tbody>')
        for (let i = 0; i < bootstrapTable.length; i++) {
            html.push('<tr>',
                '<td>', bootstrapTable[i].lot, '</td>',
                '<td>', bootstrapTable[i].carrierTotal, '</td>',
                '<td>', bootstrapTable[i].total, '</td>',
                '</tr>')
        }
        html.push('</tbody>',
            '</table>',
            '</div>');

        // var html =
        //     '<div style="width: 100%;padding: 10px 10px;box-sizing: border-box">' +
        //     '<table border="0" className="custom-tab table" style="width: 100%">' +
        //     '<thead>'+
        //     '<tr>'+
        //     '<th style="width: 33%;" class="tc">运单号</th>'+
        //     '<th style="width: 33%;" class="tc">用户对账金额(元)</th>'+
        //     '<th class="tc">系统结算金额(元)</th>'+
        //     '</tr>'+
        //     '</thead>'+
        //     ' <tbody>'+
        //     '<tr th:each="list : ${bootstrapTable}">'+
        //     ' <td class="tc" th:text="${list.lot}"></td>'+
        //     ' <td class="tc" th:text="${list.lot}"></td>'+
        //     ' <td class="tc" th:text="${list.lot}"></td>'+
        //     '</tr>'+
        //     '</tbody>'+
        //     '</table>'+
        //     '</div>'
        var text = html.join('');
            layer.open({
            type: 1,
            title: '确认',
            shadeClose: true,
            area: ['400px', '400px'],
            content: text, //这里content是一个普通的String
            btn: ['确认', '取消'],
            btn1: function(index, layero){

                $.ajax({
                    url: ctx + 'trace/confirmCarrierFee',
                    method : 'POST',
                    data: { lotIds : entrustLotIds.join(',') },
                    async: false,
                    success: function(result) {
                        if (result.code == 0) {
                            layer.close(index)
                        }

                    }
                })
            }
        });
        // $.modal.confirm("是否确认？", function () {
        //     $.operate.post(prefix + "/affirm", {"payCheckSheetIds": payCheckSheetIds.join()});
        // });
    }

    /**
     * 反确认
     */
    function reverse() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        var adjustCnt = 0
        for (var i = 0; i < bootstrapTable.length; i++) {
            /* if (bootstrapTable[i]["isClose"] == 1) {
                 $.modal.alertWarning("该月份已关账，无法进行操作！");
                 return;
             }*/
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("应付对账单状态为已确认才能进行反确认");
                return;
            }
            if(bootstrapTable[i].taxAmountOil != null || bootstrapTable[i].taxAmount != null){
                adjustCnt++;
            }
        }
        var payCheckSheetIds = $.table.selectColumns("payCheckSheetId").join();
        if(adjustCnt > 0){
            $.modal.confirm("对账单存在调整额，反确认调整额也将删除，确认进行反确认操作吗?", function () {
                $.modal.open("反确认", prefix + "/back_confirm/" + payCheckSheetIds,500,300);
            });
        }else{
            $.modal.open("反确认", prefix + "/back_confirm/" + payCheckSheetIds,500,300);
        }
    }

    function remove() {
        // 选中的行
        var rows = $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var payCheckSheetIds = $.table.selectColumns("payCheckSheetId").join();

        $.modal.confirm("确认要删除选中的数据吗?", function () {
            var url = prefix + "/removeCheckSheet";
            var data = {"payCheckSheetIds": payCheckSheetIds};
            $.operate.submit(url, "post", "json", data);
        });
    }

    /**
     * 油卡调整
     */
    function oilAdjust() {

        var balaType =  $.table.selectColumns('balaType').join();
        if(status != '1' && balaType != '1'){
            $.modal.alertWarning("该页面无法进行油卡比例调整");
            return ;
        }

        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0 && bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("应付对账单状态为新建或已确认才能进行调整！");
                return;
            }
        }

        var payCheckSheetId = $.table.selectColumns("payCheckSheetId").join();

        //判断是否申请过
        $.ajax({
            type: "POST",
            url: prefix + "/checkApplyHistory?payCheckSheetId="+payCheckSheetId,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertWarning("单据存在申请记录，无法进行油卡比例调整");
                }else{
                    $.modal.openTab("油卡占比调整", prefix + "/oil_adjust/" + payCheckSheetId);
                }
            }
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        totalAmount = 0;//总金额
        oilAmount = 0;//油卡金额
        cashAmount = 0;//现金金额
        applicationAmountOil = 0;//油卡申请金额
        cashApplicationAmount = 0;//现金申请金额
        gotAmount = 0;//已付金额
        ungotAmount = 0;//未付金额
        amountApplied = 0;//已申请金额
    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        totalAmount = totalAmount + row.totalAmount;
        oilAmount = oilAmount + row.oilAmount;
        cashAmount = cashAmount + (row.totalAmount - row.oilAmount);
        applicationAmountOil = applicationAmountOil + row.applicationAmountOil;
        cashApplicationAmount = cashApplicationAmount + row.applicationAmount;
        gotAmount = gotAmount + row.gotAmount;
        ungotAmount = ungotAmount + row.ungotAmount;
        amountApplied = amountApplied+(row.applicationAmountOil+row.applicationAmount)
    }

    function subTotal(row) {
        totalAmount = totalAmount - row.totalAmount;
        oilAmount = oilAmount - row.oilAmount;
        cashAmount = cashAmount - (row.totalAmount - row.oilAmount);
        applicationAmountOil = applicationAmountOil - row.applicationAmountOil;
        cashApplicationAmount = cashApplicationAmount - row.applicationAmount;
        gotAmount = gotAmount - row.gotAmount;
        ungotAmount = ungotAmount - row.ungotAmount;
        amountApplied = amountApplied-(row.applicationAmountOil + row.applicationAmount);
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#totalAmount").text(totalAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#oilAmount").text(oilAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#cashAmount").text(cashAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#applicationAmountOil").text(applicationAmountOil.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#cashApplicationAmount").text(cashApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#gotAmount").text(gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#ungotAmount").text(ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#amountApplied").text(amountApplied.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: prefix + "/getCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $("#totalAmounts").text(data.TOTAL_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#oilAmounts").text(data.OIL_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#cashAmounts").text(data.CASH_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#applicationAmountOils").text(data.APPLICATION_AMOUNT_OIL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#cashApplicationAmounts").text(data.APPLICATION_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#gotAmounts").text(data.GOT_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#ungotAmounts").text(data.UNGOT_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#applicationAmounts").text(data.AMOUNT_APPLIED.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }



    /**
     * 结算公司
     */
    function checkInfo(id) {
        var url = prefix + "/checkInfo?payCheckSheetId=" + id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核信息",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 税额增加
     */
    function taxAdd() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isExistFleetData"] == 1) {
                $.modal.alertWarning("分配车队的对账单无法调整！");
                return;
            }
        }

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] == 0 || bootstrapTable[i]["vbillstatus"] == 3) {
                $.modal.alertWarning("应付对账单状态为新建或已核销状态无法填写调整额");
                return;
            }
        }
        var taxAmount = $.table.selectColumns("taxAmount").join();
        var taxAmountOil = $.table.selectColumns("taxAmountOil").join();
        if(taxAmount != null && taxAmount != "" && taxAmountOil != null && taxAmountOil != ""){
            $.modal.alertWarning("调整额已填写，请先撤销调整额");
            return;
        }
        var payCheckSheetIds = $.table.selectColumns("payCheckSheetId").join();
        $.ajax({
            type: "POST",
            url: prefix + "/checkApplyHistory?payCheckSheetId="+payCheckSheetIds,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertWarning("单据存在申请记录，无法进行调整额");
                }else{
                    $.modal.openTab("调整额登记", prefix + "/taxAdd/" + payCheckSheetIds,600,400);
                }
            }
        });
    }

    function taxRevoke() {

        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isExistFleetData"] == 1) {
                $.modal.alertWarning("分配车队的对账单无法调整额撤销！");
                return;
            }
        }

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] != 1 ) {
                $.modal.alertWarning("应付对账单状态为已确认才能撤销调整额");
                return;
            }
        }

        var payCheckSheetIds = $.table.selectColumns("payCheckSheetId").join();
        layer.confirm("是否撤销调整额？", {
            icon: 3,
            title: "系统提示",
            btn: ['现金', '油卡', '返回']
        }, function (index) {
            $.operate.post(prefix + "/taxRevoke/" + payCheckSheetIds+"/0");
        }, function (index) {
            $.operate.post(prefix + "/taxRevoke/" + payCheckSheetIds+"/1");
            layer.close(index);
        }, function (index) {
            layer.close(index);
        });
    }

    function taxRecord() {
        $.modal.openTab("调整记录", prefix + "/adjustRecord");
    }
    function prints(payCheckSheetId){
        var url =  prefix + "/payPrintFee?payCheckSheetId="+payCheckSheetId;
        $.modal.openTab('月度对账费用申请单',url);
    }
    function reviseNum(total,lotId) {
        console.log(total)
        layer.open({
            type: 1,
            area: ['60%', '500px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "修改价格",
            content: $("#scanCodeHtml").html(),
            btn: ['确定', '关闭'],
            shadeClose: true,
            success: function (layero, index) {
                let count=0
                count = total
                // data.forEach(item=>{
                //     count+=item.transFeeCount
                // })
                $("#originalFee").html(count)
                $("#adjustFee").html(count)

                $('#feeTypeDiv').css('display', 'block')
                $('#costTypeDiv').css('display', 'block')


                var picParam = {
                    maxFileCount:0,
                    publish: "cmt_1",  //用于绑定下一步方法
                    fileType: null//文件类型
                };
                var tid = "tid";
                var imageId = "image";
                $.file.initAddFilesOCR(imageId, tid, picParam);
            },
            yes: function(index, layero) {
                let dataObj={
                    invoiceLotId:lotId,
                    freeType:null,
                    costType:null,
                    adjustAmount:null,
                    tid:null,
                    adjustMemo:null
                }


                let freeType=$("#freeType").val();
                let costType=$("#costType").val();



                if (freeType==""||freeType==null||freeType==undefined) {
                    $.modal.msgError("运费类型不能为空！");
                    return;
                }
                dataObj.freeType=freeType;


                if (costType==""||costType==null||costType==undefined) {
                    $.modal.msgError("费用明细不能为空！");
                    return;
                }
                dataObj.costType=costType;


                let adjustAmount=$("#adjustAmount").val();
                if (adjustAmount==""||adjustAmount==null||adjustAmount==undefined||adjustAmount==0) {
                    $.modal.msgError("调整金额不能为空和为零！");
                    return;
                }
                dataObj.adjustAmount=adjustAmount;

                dataObj.tid=$("#tid").val();
                let adjustMemo=$("#adjustMemo").val();
                if (adjustMemo==""||adjustMemo==null||adjustMemo==undefined) {
                    $.modal.msgError("修改原因不能为空！");
                    return;
                }

                dataObj.adjustMemo=adjustMemo;
                $.modal.layerLoading("加载中...")
                //$.operate.saveModalNoCloseAndRefush(ctx + "trace/addAdjustAndChecked", dataObj);
                // $.operate.saveModalNoCloseAndRefush(ctx + "trace/addAdjustAndChecked",dataObj,function(res){
                //
                // });
                $.ajax({
                    url: ctx + "trace/addAdjustAndChecked",
                    data: dataObj,
                    dataType: "json",
                    type: 'POST',
                    success: function(result) {
                        $.table.refresh();
                        $.modal.closeAll();
                        $.modal.alertSuccess(result.msg);
                    }

                });
            },
            cancel: function(index) {
                return true;
            }
        })
    }
    function onFeeTypeChange(obj){
        let val = $(obj).val();
        if(val == ''){
            $("#costType").html('<option value="">-- 请选择 --</option>');
            return;
        }

        if(val==0){
            $("#costType").html('<option value="">-- 请选择 --</option>');

            $("#costType").append('<option value="4">回付现金</option>');
            $("#costType").append('<option value="5">回付油卡</option>');
        }else{
            $("#costType").html('<option value="">-- 请选择 --</option>');

            $("#costType").append('<option value="1">装卸费</option>');
            $("#costType").append('<option value="3">放空费</option>');
            $("#costType").append('<option value="14">客户赔偿</option>');
            $("#costType").append('<option value="19">货损扣款</option>');
            $("#costType").append('<option value="10">其他</option>');
        }
    }
    function adjustment(obj){
        let val = $(obj).val();
        let yj= $("#originalFee").html()
        $("#adjustFee").html( new Number(yj)+new Number(val) );
    }
</script>

</body>
</html>