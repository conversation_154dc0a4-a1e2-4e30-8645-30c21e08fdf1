<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('反确认')"/>
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <th:block th:include="include :: jasny-bootstrap-css" />
</head>

<body>
<div class="form-content">
    <form id="form-receive-unconfirm" class="form-horizontal" novalidate="novalidate">
        <div class="panel-group" id="accordion">
            <input id="payCheckSheetId" name="payCheckSheetId" type="hidden" th:value="${payCheckSheetId}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseFive">对账单信息</a>

                    </h4>
                </div>
                <div id="collapseFive" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">承运商名称：</label>
                                    <div class="col-md-8 col-sm-8" th:text="${payCheckSheet.carrName}"></div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">对账年月：</label>
                                    <div class="col-md-8 col-sm-8">[[${payCheckSheet.year}]]年[[${payCheckSheet.month}]]月</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">调整前金额：</label>
                                    <div class="col-md-8 col-sm-8" th:text="${payCheckSheet.cashAmount}" id="beforeAdjust"></div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group">
                                    <label class="col-md-4 col-sm-4">调整后金额：</label>
                                    <div class="col-md-8 col-sm-8" th:text="${payCheckSheet.cashAmount}" id="afterAdjust"></div>
                                </div>
                            </div>
                        </div>
                <!--基础信息 begin-->
                <div class="row">
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2" style="color: red">调整类型：</label>
                            <div class="col-sm-7">
                                <select type="text" name="adjustType" id="adjustType" class="form-control valid" onchange="adjustTypeChange()">
                                    <option value="0">现金</option>
                                    <option value="1">油卡</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2" style="color: red">调整额：</label>
                            <div class="col-sm-7">
                                <input oninput="$.numberUtil.onlyNumberNegative(this)" type="text" name="taxAmount"
                                       id="taxAmount" class="form-control valid" autocomplete="off" aria-autocomplete="none" required onchange="adjustTypeChange()"/>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="row">
                    <div class="col-md-12 col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-1" style="color: red">调整原因：</label>
                            <div class="col-sm-9">
                                    <textarea name="taxMemo" id="taxMemo" class="form-control" type="text"
                                              maxlength="300" rows="6" required></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2" style="color: red">收入是否调整：</label>
                            <div class="col-sm-7">
                                <select type="text" name="isCostAdjust" id="isCostAdjust" class="form-control valid">
                                    <option value="是">是</option>
                                    <option value="否">否</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-1" id="costAdjustMemoLabel">收入调整描述：</label>
                            <div class="col-sm-9">
                            <textarea name="costAdjustMemo" id="costAdjustMemo" class="form-control" type="text"
                                      maxlength="300" rows="6"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-2">发票号：</label>
                            <div class="col-sm-7">
                                <input type="text" name="invoiceNumber" id="invoiceNumber" class="form-control valid" maxlength="50"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 col-sm-12">
                        <div class="form-group">
                            <label class="col-sm-1">相关附件：</label>
                            <div class="col-sm-9">
                                <input id="taxFile" class="form-control" name="taxFile" type="file" multiple >
                                <input id="tid" name="tid" type="hidden">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-offset-5 col-sm-10">
                <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保
                    存
                </button>&nbsp;
                <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
                </button>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script th:inline="javascript">
    var prefix = ctx + "payCheckSheet";

    var payCheckSheet = [[${payCheckSheet}]];

    $(function () {
        var picParam = {
            maxFileCount:0,
            publish: "uploadSuccess",  //用于绑定下一步方法
            fileType: null//文件类型
        };
        var tid = "tid";
        var imageId = "taxFile";
        $.file.initAddFiles(imageId, tid, picParam);

        // 图片上传成功后
        $("#taxFile").on('filebatchuploadsuccess', function (event, data) {
            var tid = data.response.tid;
            $("#tid").val(tid);
            //表单提交
            $.operate.saveTab(prefix + "/taxAdd", $('#form-receive-unconfirm').serialize());
        });

        $('#collapseOne').collapse('show');
        $('#collapseTwo').collapse('show');
        $('#collapseThree').collapse('show');
        $('#collapseFour').collapse('show');
        $('#collapseFive').collapse('show');
        $('#collapseSix').collapse('show');

    });

    /**
     * 提交
     */
    function submitHandler() {
        if($("#isCostAdjust").val() == '是'){
            $("#costAdjustMemo").attr("required","");
            $("#costAdjustMemoLabel").css("color","red");
        }else{
            $("#costAdjustMemo").removeAttr("required","");
            $("#costAdjustMemoLabel").css("color","");
        }

        if ($.validate.form()) {
         /*   if($("#taxFile").val() == "" || $("#taxFile").val() == null){
                $.operate.saveTab(prefix + "/taxAdd", $("#form-receive-unconfirm").serializeArray());
            }else{*/
                // 表单提交
                $.modal.loading("正在处理中，请稍后...");
                $("#taxFile").fileinput('upload');
           /* }*/

        }
    }
    
    function adjustTypeChange() {
        var adjustType = $("#adjustType").val();
        if(adjustType == 0){
            //现金
            var cashAmount = payCheckSheet.cashAmount;
            $("#beforeAdjust").html(cashAmount);

            var taxAmount = $("#taxAmount").val();
            if(taxAmount != null && taxAmount != ""){
                var afterAdjust = parseFloat(cashAmount)+parseFloat(taxAmount);
                $("#afterAdjust").html(afterAdjust);
            }else{
                $("#afterAdjust").html(cashAmount);
            }
        }else if(adjustType == 1){
            //油卡
            var oilAmount = payCheckSheet.oilAmount;
            $("#beforeAdjust").html(oilAmount);

            var taxAmount = $("#taxAmount").val();
            if(taxAmount != null && taxAmount != ""){
                var afterAdjust = parseFloat(oilAmount)+parseFloat(taxAmount);
                $("#afterAdjust").html(afterAdjust);
            }else{
                $("#afterAdjust").html(oilAmount);
            }
        }
    }
    
    

</script>
</body>
</html>