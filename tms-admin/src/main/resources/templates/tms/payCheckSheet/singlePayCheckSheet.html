<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('承运商对账')"/>

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
<!--                    <div class="col-md-2 col-sm-4">-->
<!--                        <div class="form-group">-->
<!--                            <div class="col-sm-12">-->
<!--                                <select name="ifBilling" id="ifBilling" class="form-control valid"-->
<!--                                        aria-invalid="false">-->
<!--                                    <option value="">&#45;&#45;对账单状态&#45;&#45;</option>-->
<!--                                    <option value="0">11</option>-->
<!--                                    <option value="1">22</option>-->
<!--                                </select>-->

<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-4">应付对账单号：</label>-->
                            <div class="col-sm-12">
                                <select name="vbillstatus"  id="vbillstatus" placeholder="请选择应付对账单状态" class="form-control valid" type="text"
                                       aria-required="true">
                                    <option value="">-- 请选择状态 --</option>
                                    <option value="0">新建</option>
                                    <option value="1">已确认</option>
                                    <option value="2">部分核销</option>
                                    <option value="3">已核销</option>
                                    <option value="4">调整待审核</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">应付对账单号：</label>-->
                            <div class="col-sm-12">
                                <input name="vbillno"  id="vbillno" placeholder="请输入应付对账单号" class="form-control valid" type="text"
                                       aria-required="true" th:value="${vbillno}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 col-sm-2">
                        <div class="form-group">
                            <!--                           <label class="col-sm-4">年份：</label>-->
                            <div class="col-sm-12">

                                <input name="year" id="year"  placeholder="请输入年份" class="form-control valid" type="text"
                                       aria-required="true" autocomplete="off">

                            </div>
                        </div>
                    </div>
                    <div class="col-md-1 col-sm-2">
                        <div class="form-group">
                            <!--                           <label class="col-sm-4">月份：</label>-->
                            <div class="col-sm-12">

                                <input name="month" id="month" placeholder="请输入月份" class="form-control valid" type="text"
                                       aria-required="true"  autocomplete="off">

                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                           <label class="col-sm-4">承运商名称：</label>-->
                            <div class="col-sm-12">
                                <input type="hidden" name="carrCode" th:value="${carrCode}">
                                 <input name="carrName"  id="carrName" placeholder="请输入承运商名称" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-4">
                        <div class="form-group">
<!--                            <label class="col-sm-4">创建人：</label>-->
                            <div class="col-sm-12">
                                <input name="regUserName"  id="regUserName" placeholder="请输入创建人" class="form-control valid" type="text"
                                       aria-required="true">
                            </div>
                        </div>
                    </div>
                <!-- </div>
                <div class="row"> -->
                    <!-- <div class="col-md-6 col-sm-6"></div> -->
                    <div class="col-md-2 col-sm-4">
                        <!-- <label class="col-sm-6"></label> -->
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">

        </div>

        <div class="col-sm-12 select-table table-striped" >
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">

    var status = [[${status}]];

    var prefix = ctx + "payCheckSheet";

    var totalAmount = 0;//总金额
    var oilAmount = 0;//油卡金额
    var cashAmount = 0;//现金金额
    var applicationAmountOil = 0;//油卡申请金额
    var cashApplicationAmount = 0;//现金申请金额
    var gotAmount = 0;//已付金额
    var ungotAmount = 0;//未付金额
    var amountApplied = 0;//已申请金额

    var height = document.documentElement.clientHeight - 50;
    var width = document.documentElement.clientWidth - 320;
    $(function () {
        var options = {
            url: prefix + "/singleList",
            createUrl: prefix + "/add",
            showToggle:false,
            showColumns:false,
            exportUrl: prefix + "/export",
            modalName: "承运商对账",
            fixedColumns: true,
            fixedNumber:8,
            height: 560,
            clickToSelect:true,
            showFooter:true,
            onPostBody:function () {
                //合并页脚
                merge_footer();
                //查询合计总金额
                getAmountCount();
            },
            onRefresh:function(params){
                clearTotal();
            },
            onCheck: function (row,$element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            columns: [{
                checkbox: true,
                footerFormatter: function (row) {
                    return "合计:&nbsp&nbsp"
                        + "总金额:<nobr id='totalAmount'>￥0</nobr>&nbsp&nbsp"
                        + "油卡金额:<nobr id='oilAmount'>￥0</nobr>&nbsp&nbsp"
                        + "现金金额:<nobr id='cashAmount'>￥0</nobr>&nbsp&nbsp"
                        + "已付金额:<nobr id='gotAmount'>￥0</nobr>&nbsp&nbsp"
                        + "未付金额:<nobr id='ungotAmount'>￥0</nobr>&nbsp&nbsp"
                        + "油卡申请金额:<nobr id='applicationAmountOil'>￥0</nobr>&nbsp&nbsp"
                        + "现金申请金额:<nobr id='cashApplicationAmount'>￥0</nobr>&nbsp&nbsp"
                        + "已申请金额:<nobr id='amountApplied'>￥0</nobr>&nbsp&nbsp<br>"
                    +"总合计:&nbsp&nbsp"
                    + "总金额:<nobr id='totalAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "油卡金额:<nobr id='oilAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "现金金额:<nobr id='cashAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "已付金额:<nobr id='gotAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "未付金额:<nobr id='ungotAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "油卡申请金额:<nobr id='applicationAmountOils'>￥0</nobr>&nbsp&nbsp"
                    + "现金申请金额:<nobr id='cashApplicationAmounts'>￥0</nobr>&nbsp&nbsp"
                    + "已申请金额:<nobr id='applicationAmounts'>￥0</nobr>&nbsp&nbsp"
                }
            },
                {
                    title: '操作',
                    align: 'left',
                    field: 'payCheckSheetId',
                    formatter: function(value, row, index) {
                        var actions = [];
                        // actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  title="往来明细" onclick="payExchangeDetails(\''+row.payCheckSheetId+'\')"><i  class="fa fa-exchange" style="font-size: 15px;" ></i></a>');
                        actions.push('<a class="btn  btn-xs" href="javascript:void(0)" style="padding: 1px 0"  title="应付明细" onclick="payDetail(\''+row.payCheckSheetId+'\')"><i  class="fa fa fa-list" style="font-size: 15px;" ></i></a>');
                        if(row.checkStatus == 1){
                            actions.push('<a class="btn  btn-xs" href="javascript:void(0)"  style="padding: 1px 0" title="审核记录" onclick="checkInfo(\'' + row.payCheckSheetId + '\')"><i  class="fa fa-commenting-o" style="font-size: 15px;" ></i></a>');
                        }
                        /*if(true){
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="打印" onclick="prints(\'' + row.payCheckSheetId + '\')"><i class="fa fa-print" style="font-size: 15px;"></i></a> ');
                        }*/
                        if ([[${@permission.hasPermi('finance:paySheetRecord:invoice')}]] != "hidden") {
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="发货单详情" onclick="onLotCopy(\'' + row.payCheckSheetId + '\',\''+row.vbillno+'\')"><i class="fa fa-bar-chart" style="font-size: 15px;"></i></a>');
                            actions.push('<a class="btn btn-xs " href="javascript:void(0)" title="运单详情" onclick="onLotInfo(\'' + row.payCheckSheetId + '\',\''+row.vbillno+'\')"><i class="fa fa-book" style="font-size: 15px;"></i></a>');
                        }

                        return actions.join('');
                    }

                },
                {
                    title: '应付对账单号',
                    field: 'vbillno',
                    align: 'left',
                    formatter: function (value, row, index) {
                        var result = value;
                        if (row.lotG7End == 2) {
                            result += ' <span class="label label-success" style="padding:1px;">G7</span>'
                        } else if (row.lotG7End == 3) {
                            result += ' <span class="label label-warning" title="预支" style="padding:1px 5px;">Y</span>'
                        }
                        return result
                    }
                },
                {
                    title: '状态',
                    field: 'vbillstatus',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        switch(value) {
                            case 0:
                                return '<span class="label label-default">新建</label>';
                            case 1:
                                return '<span class="label label-primary">已确认</label>';
                            case 2:
                                return '<span class="label label-info">部分核销</label>';
                            case 3:
                                return '<span class="label label-success">已核销</label>';
                            case 4:
                                return '<span class="label label-warning">调整待审核</label>';
                            default:
                                break;
                        }
                    }
                },
                {
                    title: '承运商',
                    field: 'carrName',
                    align: 'left'
                },
                {
                    title: '对账年月',
                    field: 'year',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return value + '年' + row.month+'月';
                    }
                },
                // {
                //     title: '对账月份',
                //     field: 'month',
                //     align: 'left'
                // },
                {
                    title: '总金额(元)',
                    field: 'totalAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: 'G7归属',
                    field: 'balaCorp',
                    align: 'left',
                    formatter: function (value, row, index) {
                     /*   if (row.lotG7End == 2) {
                            return value
                        }*/
                        return value
                    }
                },
                /*{
                    title: '现金调整额(元)',
                    field: 'taxAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '油卡调整额(元)',
                    field: 'taxAmountOil',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },*/
                {
                    title: '油卡金额(元)',
                    field: 'oilAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        var res =value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        if(row.taxAmountOil != null){
                            res = res + ' <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true" title="油卡调整额：' +row.taxAmountOil + '元"> ? </span>';
                        }
                        return res;

                    }

                },
                {
                    title: '现金金额(元)',
                    field: 'oilAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (row.totalAmount === null) {
                            return ;
                        }
                        var oilAmount = row.oilAmount === null ? 0 : row.oilAmount;
                        var v = row.totalAmount - oilAmount;
                        var res = v.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        if(row.taxAmount   != null){
                            res = res + ' <span class="label label-danger" style="padding:1px;" data-toggle="tooltip" data-placement="left" data-html="true" title="现金调整额：' +row.taxAmount + '元"> ? </span>';
                        }
                        return res;
                    }

                },
                {
                    title: '已付金额(元)',
                    field: 'gotAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '未付金额(元)',
                    field: 'ungotAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },

                {
                    title: '油卡申请金额(元)',
                    field: 'applicationAmountOil',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '现金申请金额(元)',
                    align: 'right',
                    field: 'applicationAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },
                {
                    title: '已申请金额(元)',
                    field: 'applicationAmount',
                    align: 'right',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        var applicationAmountOil = row.applicationAmountOil === null ? 0 : row.applicationAmountOil;
                        var applicationAmount = row.applicationAmount === null ? 0 : row.applicationAmount;
                        var applicationAmountCash = applicationAmountOil + applicationAmount
                        return applicationAmountCash.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }

                },

                {
                    title: '创建人',
                    align: 'left',
                    field: 'regUserId'
                },
                {
                    title: '备注',
                    field: 'memo',
                    align: 'left'
                },
                // {
                //     title: '是否符合无车承运人',
                //     align: 'left',
                //     field: 'isNtocc',
                //     formatter: function status(value, row, index) {
                //         if (value === 0) {
                //             return '<span class="label label-default">否</label>';
                //         }
                //         return '<span class="label label-primary">是</label>';
                //     }
                // }
            ]
        };
        var date = new Date();
        let year = date.getFullYear();
        $("#year").val(year)

        $.table.init(options);
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                //查询方法
                $.table.search();
            }
        });


    });

    layui.use('laydate', function(){
        var laydate = layui.laydate;
        laydate.render({
            elem: '#month',
            type: 'month',
            format:"MM"
        });
        laydate.render({
            elem: '#year',
            type: 'year'
        });
    });

    //往来明细
    function payExchangeDetails(carrierId) {
        if(carrierId == null){
            var year = $.table.selectColumns('year');
            var month = $.table.selectColumns('month');
            var carrierId = $.table.selectColumns('carrierId');
        }
        var url = prefix + "/payExchangeDetails?carrierId="+carrierId+"&year="+year+"&month="+month;
        $.modal.openTab("往来明细", url);
    }

    //付款现金申请
    function payAppl(){
        var id = $.table.selectColumns('payCheckSheetId').join();//id
        var totalAmount = parseFloat($.table.selectColumns('totalAmount').join());//总金额
        var oilAmount = $.table.selectColumns('oilAmount').join();
        oilAmount = parseFloat(oilAmount == '' ? '0' : oilAmount);//油卡金额

        var applicationAmount = parseFloat($.table.selectColumns('applicationAmount').join());//已申请金额（现金）

        var balaType =  $.table.selectColumns('balaType').join();
        if(status != '1' && balaType != '1'){
            $.modal.alertWarning("该页面无法进行付款现金申请");
            return ;
        }

        var vbillstatus = $.table.selectColumns('vbillstatus').join();//对账单状态
        if(vbillstatus == 0){
            $.modal.alertError("该账单在新建状态下，不能付款申请");
            return ;
        }


        //暂注释：无法申请时 只展示申请记录
        // if(totalAmount-oilAmount == applicationAmount){
        //     $.modal.alertError("该账单已申请完成");
        //     return ;
        // }
        if (checklock(id)) {
            var url = prefix + "/payAppl/" + id;
            $.modal.openTab('付款现金申请', url);
        }
    }

    /**
     * 油卡申请
     */
    function payApplOil() {
        var id = $.table.selectColumns('payCheckSheetId').join();//id
        var oilAmount = $.table.selectColumns('oilAmount').join();
        oilAmount = parseFloat(oilAmount == '' ? '0' : oilAmount);//油卡金额

        var applicationAmountOil = $.table.selectColumns('applicationAmountOil').join();
        applicationAmountOil = parseFloat(applicationAmountOil == '' ? '0' : applicationAmountOil);//已申请金额(油卡)

        var balaType =  $.table.selectColumns('balaType').join();
        if(status != '1' && balaType != '1'){
            $.modal.alertWarning("该页面无法进行付款油卡申请");
            return ;
        }

        var vbillstatus = $.table.selectColumns('vbillstatus').join();//对账单状态
        if(vbillstatus == 0){
            $.modal.alertError("该账单在新建状态下，不能付款申请");
            return ;
        }

        //暂注释：无法申请时 只展示申请记录
        // if(oilAmount == applicationAmountOil){
        //     $.modal.alertError("该账单已申请完成");
        //     return ;
        // }
        if (checklock(id)) {
            var url = prefix + "/payApplOil/" + id;
            $.modal.openTab('付款油卡申请', url);
        }
    }

    function checklock(payCheckSheetid) {
        var pass = false;
        $.ajax({
            url: prefix + "/checkLotLock",
            data: "payCheckSheetId=" + payCheckSheetid,
            type: "post",
            async: false,
            success: function(r) {
                if(r.code===0) {
                   pass = true;
                } else {
                    var arr = r.data;// VBILLNO,LOTNO,LOCK_PAY,SINGLE_LOCK
                    var msg = []
                    for (let i = 0; i < arr.length; i++) {
                        msg.push(arr[i]['VBILLNO'], "对应运单", arr[i]['LOTNO'], arr[i]['LOCK_PAY'] == '1' ? '存在异常跟踪信息，已被锁定' : '已被系统锁定', "；<br>");
                    }
                    msg.push("需要先解锁才能申请付款")
                    $.modal.alertError(msg.join(""));
                }
            }
        });
        return pass;
    }

    // 核销
    function verification() {
        var payCheckSheetIds = $.table.selectColumns('payCheckSheetId');

        var gotAmountList = $.table.selectColumns('gotAmount');
        var totalAmountList = $.table.selectColumns('totalAmount');
        var handVerificationList = $.table.selectColumns('handVerification');
        for (var i = 0; i < gotAmountList.length;i++ ) {
            if (gotAmountList[i] !== totalAmountList[i]) {
                $.modal.alertWarning("请选择已付清的对账单");
                return;
            }
            if (handVerificationList[i] === '0') {
                $.modal.alertWarning("请选择状态为手动核销的对账单");
                return;
            }
        }
        $.operate.post(prefix + "/verification", { "payCheckSheetIds": payCheckSheetIds.join()},location.reload());
    }

    //付款记录
    function payRecord() {
        var id = $.table.selectColumns('payCheckSheetId');
        var url = prefix + "/payRecord/"+id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "付款记录",
            area: ['800px', '600px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });

    }



    // 跳转对应的应付明细页面
    function payDetail(payCheckSheetId) {
        var url = prefix + "/payDetail?payCheckSheetId="+payCheckSheetId;
        $.modal.openTab('应付明细',url);
    }
    /**
     * 确认应付明细
     */
    function affirm() {
        var balaTypeArr =  $.table.selectColumns('balaType');
        for(var i=0 ; i < balaTypeArr.length ; i++){
            var balaType = balaTypeArr[i];
            if(status != '1' && balaType != '1'){
                $.modal.alertWarning("该页面无法进行确认");
                return ;
            }
        }


        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0) {
                $.modal.alertWarning("应付单据只能为新建状态下才能确认");
                return;
            }
        }
        var payCheckSheetIds = $.table.selectColumns("payCheckSheetId");
        if (payCheckSheetIds.length === 0 ) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("是否确认？", function () {
            $.operate.post(prefix + "/affirm", {"payCheckSheetIds": payCheckSheetIds.join()});
        });
    }

    /**
     * 反确认
     */
    function reverse() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        var adjustCnt = 0
        for (var i = 0; i < bootstrapTable.length; i++) {
           /* if (bootstrapTable[i]["isClose"] == 1) {
                $.modal.alertWarning("该月份已关账，无法进行操作！");
                return;
            }*/
            if (bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("应付对账单状态为已确认才能进行反确认");
                return;
            }
            if(bootstrapTable[i].taxAmountOil != null || bootstrapTable[i].taxAmount != null){
                adjustCnt++;
            }
        }
        var payCheckSheetIds = $.table.selectColumns("payCheckSheetId").join();
        if(adjustCnt > 0){
            $.modal.confirm("对账单存在调整额，反确认调整额也将删除，确认进行反确认操作吗?", function () {
                $.modal.open("反确认", prefix + "/back_confirm/" + payCheckSheetIds,500,300);
            });
        }else{
            $.modal.open("反确认", prefix + "/back_confirm/" + payCheckSheetIds,500,300);
        }
    }

    function remove() {
        // 选中的行
        var rows = $.table.selectFirstColumns();
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var payCheckSheetIds = $.table.selectColumns("payCheckSheetId").join();

        $.modal.confirm("确认要删除选中的数据吗?", function () {
            var url = prefix + "/removeCheckSheet";
            var data = {"payCheckSheetIds": payCheckSheetIds};
            $.operate.submit(url, "post", "json", data);
        });
    }

    /**
     * 油卡调整
     */
    function oilAdjust() {

        var balaType =  $.table.selectColumns('balaType').join();
        if(status != '1' && balaType != '1'){
            $.modal.alertWarning("该页面无法进行油卡比例调整");
            return ;
        }

        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');
        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] !== 0 && bootstrapTable[i]["vbillstatus"] !== 1) {
                $.modal.alertWarning("应付对账单状态为新建或已确认才能进行调整！");
                return;
            }
        }

        var payCheckSheetId = $.table.selectColumns("payCheckSheetId").join();

        //判断是否申请过
        $.ajax({
            type: "POST",
            url: prefix + "/checkApplyHistory?payCheckSheetId="+payCheckSheetId,
            success: function(r){
                if(r.code != 0){
                    $.modal.alertWarning("单据存在申请记录，无法进行油卡比例调整");
                }else{
                    $.modal.openTab("油卡占比调整", prefix + "/oil_adjust/" + payCheckSheetId);
                }
            }
        });
    }

    /**
     * 将总计金额清零
     */
    function clearTotal() {
        totalAmount = 0;//总金额
        oilAmount = 0;//油卡金额
        cashAmount = 0;//现金金额
        applicationAmountOil = 0;//油卡申请金额
        cashApplicationAmount = 0;//现金申请金额
        gotAmount = 0;//已付金额
        ungotAmount = 0;//未付金额
        amountApplied = 0;//已申请金额
    }

    /**
     * 累计总金额
     */
    function addTotal(row) {
        totalAmount = totalAmount + row.totalAmount;
        oilAmount = oilAmount + row.oilAmount;
        cashAmount = cashAmount + (row.totalAmount - row.oilAmount);
        applicationAmountOil = applicationAmountOil + row.applicationAmountOil;
        cashApplicationAmount = cashApplicationAmount + row.applicationAmount;
        gotAmount = gotAmount + row.gotAmount;
        ungotAmount = ungotAmount + row.ungotAmount;
        amountApplied = amountApplied+(row.applicationAmountOil+row.applicationAmount)
    }

    function subTotal(row) {
        totalAmount = totalAmount - row.totalAmount;
        oilAmount = oilAmount - row.oilAmount;
        cashAmount = cashAmount - (row.totalAmount - row.oilAmount);
        applicationAmountOil = applicationAmountOil - row.applicationAmountOil;
        cashApplicationAmount = cashApplicationAmount - row.applicationAmount;
        gotAmount = gotAmount - row.gotAmount;
        ungotAmount = ungotAmount - row.ungotAmount;
        amountApplied = amountApplied-(row.applicationAmountOil + row.applicationAmount);
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for(var i=1;i<footer_td.length;i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    /**
     *
     * 给页脚总计赋值
     */
    function setTotal() {
        $("#totalAmount").text(totalAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#oilAmount").text(oilAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#cashAmount").text(cashAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#applicationAmountOil").text(applicationAmountOil.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#cashApplicationAmount").text(cashApplicationAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#gotAmount").text(gotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#ungotAmount").text(ungotAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
        $("#amountApplied").text(amountApplied.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
    }

    /**
     * 获取所有数据金额合计（根据查询条件）
     */
    function getAmountCount() {
        var data = $.common.formToJSON("role-form");
        $.ajax({
            url: prefix + "/getSingleCount",
            type: "post",
            dataType: "json",
            data: data,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $("#totalAmounts").text(data.TOTAL_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#oilAmounts").text(data.OIL_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#cashAmounts").text(data.CASH_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#applicationAmountOils").text(data.APPLICATION_AMOUNT_OIL.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#cashApplicationAmounts").text(data.APPLICATION_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#gotAmounts").text(data.GOT_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#ungotAmounts").text(data.UNGOT_AMOUNT.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    $("#applicationAmounts").text(data.AMOUNT_APPLIED.toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                }
            }
        });
    }



    /**
     * 结算公司
     */
    function checkInfo(id) {
        var url = prefix + "/checkInfo?payCheckSheetId=" + id;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "审核信息",
            area: [width + 'px', height + 'px'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        });
    }

    /**
     * 税额增加
     */
    function taxAdd() {
        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isExistFleetData"] == 1) {
                $.modal.alertWarning("分配车队的对账单无法调整！");
                return;
            }
        }

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] == 0 || bootstrapTable[i]["vbillstatus"] == 3) {
                $.modal.alertWarning("应付对账单状态为新建或已核销状态无法填写调整额");
                return;
            }
        }
        var taxAmount = $.table.selectColumns("taxAmount").join();
        var taxAmountOil = $.table.selectColumns("taxAmountOil").join();
        if(taxAmount != null && taxAmount != "" && taxAmountOil != null && taxAmountOil != ""){
            $.modal.alertWarning("调整额已填写，请先撤销调整额");
            return;
        }

        var payCheckSheetIds = $.table.selectColumns("payCheckSheetId").join();
        $.modal.openTab("调整额登记", prefix + "/taxAdd/" + payCheckSheetIds,600,400);
    }
    
    function taxRevoke() {

        // 选中的行
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["isFleetData"] == 0 && bootstrapTable[i]["isExistFleetData"] == 1) {
                $.modal.alertWarning("分配车队的对账单无法调整额撤销！");
                return;
            }
        }

        for (var i = 0; i < bootstrapTable.length; i++) {
            if (bootstrapTable[i]["vbillstatus"] != 1 ) {
                $.modal.alertWarning("应付对账单状态为已确认才能撤销调整额");
                return;
            }
        }

        var payCheckSheetIds = $.table.selectColumns("payCheckSheetId").join();
        layer.confirm("是否撤销调整额？", {
            icon: 3,
            title: "系统提示",
            btn: ['现金', '油卡', '返回']
        }, function (index) {
            $.operate.post(prefix + "/taxRevoke/" + payCheckSheetIds+"/0");
        }, function (index) {
            $.operate.post(prefix + "/taxRevoke/" + payCheckSheetIds+"/1");
            layer.close(index);
        }, function (index) {
            layer.close(index);
        });
    }
    
    function taxRecord() {
        $.modal.openTab("调整记录", prefix + "/adjustRecord");
    }
    function prints(payCheckSheetId){
         var url =  prefix + "/payPrintFee?payCheckSheetId="+payCheckSheetId;
         $.modal.openTab('月度对账费用申请单',url);
    }

    function advancePay(){
        var carrierId = $.table.selectColumns("carrierId").join();
        var url = ctx + "trace/advancePay?carrierId="+carrierId;

        layer.open({
            type: 2,
            area: ['550px','600px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '新增预付',
            content: url,
            btn: ['确定', '关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero) {
                $(layero).find("iframe")[0].contentWindow.submitHandler();
                console.log(1);
                return;
            },
            //取消按钮
            btn3: function(index, layero){
                layer.close(index);
            }
        });

    }

    function onLotCopy(payCheckSheetId,paySheetRecordVbillno) {
        console.log(payCheckSheetId,paySheetRecordVbillno)
        var url = ctx + "payManage/invoice?payCheckSheetId="+payCheckSheetId;
        $.modal.openTab('发货单详情',url);
    }

    function onLotInfo(payCheckSheetId,paySheetRecordVbillno) {
        var url = ctx + "payManage/lotCp?payCheckSheetId="+payCheckSheetId;
        $.modal.openTab('运单详情',url);
    }



</script>

</body>
</html>