<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('详情记录')" />
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<style>
    .flex{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .cur{
        cursor: pointer;
        color: #1ab394;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <!-- <input name="referencePriceId" type="hidden" th:value="${referencePriceId}"> -->
                
                <div class="row no-gutter">
                    <div class="col-sm-5 col-xs-5" >
                        <div class="form-group">
                            <div class="col-sm-4 col-xs-4">
                                <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                            <div class="col-sm-4 col-xs-4">
                                <select name="deliCityId" id="deliCityId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4 col-xs-4">
                                <select name="deliAreaId" id="deliAreaId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                            
                        </div>
                    </div>
                    <div class="col-sm-1 col-xs-1" onclick="changeDiv()">
                        <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                    </div>
                    <div class="col-sm-6 col-xs-6">
                        <div class="form-group">
                            <div class="col-sm-4 col-xs-4">
                                <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                            <div class="col-sm-4 col-xs-4">
                                <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4 col-xs-4">
                                <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row no-gutter mt5">
                    <div class="col-md-4 col-sm-4 col-xs-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="carLen" placeholder="车长" id="carLen" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="车长" multiple th:with="type=${@dict.getType('car_len')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-3 col-xs-3">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="carType" placeholder="车型" id="carType" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="车型" multiple th:with="type=${@dict.getType('car_type')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 col-sm-3 col-xs-3">
                        <input type="text" placeholder="要求提货日期" class="form-control" id="reqDeliDateStart"  name="reqDeliDateStart" readonly>
                        <input type="hidden" name="startDate" id="startDate">
                        <input type="hidden" name="endDate" id="endDate">
                    </div>

                    <div class="col-sm-2 col-xs-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<th:block th:include="include :: bootstrap-select-js" />

<script th:inline="javascript">
    var prefix = ctx + "tms/reference_price";

    $(function() {
        let startDate=getDate(7);
        let endDate=getDate(0);

        $("#reqDeliDateStart").val( startDate+" - "+endDate )
        $("#startDate").val( startDate )
        $("#endDate").val( endDate )

        let deliProvinceId= [[${priceVO.deliProvinceId}]];
        let deliCityId= [[${priceVO.deliCityId}]];
        let deliAreaId= [[${priceVO.deliAreaId}]];
        let arriProvinceId= [[${priceVO.arriProvinceId}]];
        let arriCityId= [[${priceVO.arriCityId}]];
        let arriAreaId= [[${priceVO.arriAreaId}]];
   
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",deliProvinceId,deliCityId,deliAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",arriProvinceId,arriCityId,arriAreaId);

        var options = {
            url: prefix + "/detail/list",
            modalName: "详情记录",
            showToggle:false,
            showColumns:false,
            clickToSelect: true,
            showSearch:false,
            showRefresh:false,
            uniqueId: "referencePriceId",
            columns: [
                {
                    title: '提货',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let deli="";
                        if(row.deliProName){
                            deli+=row.deliProName;
                        }
                        if(row.deliCityName&&row.deliCityName!='市辖区'){
                            deli+=row.deliCityName;
                        }
                        if(row.deliAreaName){
                            deli+=row.deliAreaName;
                        }

                        // let arri="";
                        // if(row.arriProName){
                        //     arri+=row.arriProName;
                        // }
                        // if(row.arriCityName&&row.arriCityName!='市辖区'){
                        //     arri+=row.arriCityName;
                        // }
                        // if(row.arriAreaName){
                        //     arri+=row.arriAreaName;
                        // }

                      
                        
                        // return `<span class="label label-warning pa2">提</span>`+$.table.tooltip(deli,9)+`<br/><span class="label label-success pa2">到</span>`+$.table.tooltip(arri,9);
                        return `<span class="label label-warning pa2">提</span>`+deli;

                    }
                },
                {
                    title: '到货',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let arri="";
                        if(row.arriProName){
                            arri+=row.arriProName;
                        }
                        if(row.arriCityName&&row.arriCityName!='市辖区'){
                            arri+=row.arriCityName;
                        }
                        if(row.arriAreaName){
                            arri+=row.arriAreaName;
                        }

                        return `<span class="label label-success pa2">到</span>` + arri;

                    }
                },
                {
                    field : 'carLenName',
                    title: '车长车型',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let car=[];
                        if (row.carLenName) {
                            car.push(row.carLenName)
                        }
                        if (row.carTypeName) {
                            car.push(row.carTypeName)
                        }
                        return car.join('米')
                    }
                },

                {
                    field : 'priceBasic',
                    title : '最新指导价',
                    align: 'right',
                    formatter: function (value, row, index) {
                        return getBasic(value);
                    }
                },
                {
                    field : 'priceDangerousGoods',
                    title : '危化最新指导价',
                    align: 'right',
                    formatter: function (value, row, index) {
                        return getBasic(value);
                    }
                },
                // {
                //     field : 'avgPriceBasic',
                //     title : '平均指导价',
                //     align: 'right',
                //     formatter: function (value, row, index) {
                //         return getBasic(value);
                //     }
                // },
                // {
                //     field : 'avgPriceDangerousGoods',
                //     title : '危化平均指导价',
                //     align: 'right',
                //     formatter: function (value, row, index) {
                //         return getBasic(value);
                //     }
                // },

                {
                    field : 'avgActualPriceBasic',
                    title : '平均运费',
                    align: 'right',
                    formatter: function (value, row, index) {
                        let html=[];
                        html.push( getBasic(value) )
                        if(row.countActualPriceBasic){
                            // html.push( row.countActualPriceBasic+'次' )
                            html.push( `<span class="cur" onclick="detail( \``+row.deliProvinceId+`\`,\``+row.deliCityId+`\`,\``+row.deliAreaId+`\`,
                                \``+row.arriProvinceId+`\`,\``+row.arriCityId+`\`,\``+row.arriAreaId+`\`,
                                \``+row.carLen+`\`,\``+row.carType+`\`, \``+row.startDate+`\`,\``+row.endDate+`\`,0 )">`+row.countActualPriceBasic+`</span>次` )
                        }
                        return html.join('/');
                    }
                },               
                {
                    field : 'avgActualPriceDangerousGoods',
                    title : '危化平均运费',
                    align: 'right',
                    formatter: function (value, row, index) {
                        let html=[];
                        html.push( getBasic(value) )
                        if(row.countActualPriceDangerousGoods){
   
                            html.push( `<span class="cur" onclick="detail( \``+row.deliProvinceId+`\`,\``+row.deliCityId+`\`,\``+row.deliAreaId+`\`,
                                \``+row.arriProvinceId+`\`,\``+row.arriCityId+`\`,\``+row.arriAreaId+`\`,
                                \``+row.carLen+`\`,\``+row.carType+`\`, \``+row.startDate+`\`,\``+row.endDate+`\`,15 )">`+row.countActualPriceDangerousGoods+`</span>次` )
                        }
                        return html.join('/');
                    }
                },
                
            ]
        };
        $.table.init(options);
    });

    function getBasic(value) {
        var s = value;
        if(checkNumber(s)){
            var type = 1;
            if (/[^0-9\.]/.test(s))
                return "0";
            if (s == null || s == "")
                return "0";
            s = s.toString().replace(/^(\d*)$/, "$1.");
            s = (s + "00").replace(/(\d*\.\d\d)\d*/, "$1");
            s = s.replace(".", ",");
            var re = /(\d)(\d{3},)/;
            while (re.test(s))
                s = s.replace(re, "$1,$2");
            s = s.replace(/,(\d\d)$/, ".$1");
            if (type == 0) {// 不带小数位(默认是有小数位)
                var a = s.split(".");
                if (a[1] == "00") {
                    s = a[0];
                }
            }
            return "￥"+s;
        }else{
            return "-";
        }
        
    }

    function getDate(day) {
        var now = new Date();
        now.setDate(now.getDate() - day);
        return dateFormat(now,"yyyy-MM-dd")

    }
    function dateFormat (date, pattern) {
        var o = {
            "M+" : date.getMonth()+1,     //月份
            "d+" : date.getDate(),     //日
            "h+" : date.getHours(),     //小时
            "m+" : date.getMinutes(),     //分
            "s+" : date.getSeconds(),     //秒
            "q+" : Math.floor((date.getMonth()+3)/3), //季度
            "S" : date.getMilliseconds()    //毫秒
        };
        if(/(y+)/.test(pattern))
            pattern=pattern.replace(RegExp.$1, (date.getFullYear()+"").substr(4 - RegExp.$1.length));
        for(var k in o)
            if(new RegExp("("+ k +")").test(pattern))
                pattern = pattern.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
        return pattern;
    }

     /**
     * 日期插件
     */
     layui.use('laydate', function(){
        var laydate = layui.laydate;
         laydate.render({
             elem: '#reqDeliDateStart',
             type: 'date',
             range: true,
             done: function (value, date, endDate) {
                 let time=value.split(" - ");
                 $("#startDate").val(time[0]);
                 $("#endDate").val(time[1]);
             }
         });
     });

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()

        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        searchPre()
    }

    /**
     * 搜索
     */
     function searchPre() {
        var data = {};
        data.carLen = $.common.join($('#carLen').selectpicker('val'));
        data.carType = $.common.join($('#carType').selectpicker('val'));
        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
     function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $("#role-form")[0].reset();
        $("#carLen").selectpicker('refresh');
        $("#carType").selectpicker('refresh');
        searchPre();
    }


    //验证字符串是否是数字
    function checkNumber(val) {
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val)){
            return true;
        }
        else{
            return false;
        }
    }

    function detail(deliProvinceId,deliCityId,deliAreaId,arriProvinceId,arriCityId,arriAreaId,carLen,carType,startDate,endDate,priceType) {
        let deli= "&deliProvince="+deliProvinceId+"&deliCity="+deliCityId+"&deliArea="+deliAreaId+"&arriProvince="+arriProvinceId+"&arriCity="+arriCityId+"&arriArea="+arriAreaId;
        deli += "&reqCarLen="+carLen+"&reqCarType="+carType+"&startDate="+startDate+"&endDate="+endDate+"&transType="+priceType;

        layer.open({
            type: 2,
            area: ['95%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "历史运价详情",
            content: ctx + "tms/reference_price/history_lot?"+deli,
            btn: ['关闭'],
            shadeClose: true,            // 弹层外区域关闭
            cancel: function (index) {
                return true;
            }
        });
    }
</script>
</body>
</html>