<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户运输线路指导价主列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: bootstrap-editable-css" />
</head>
<style>
    .fixed-table-toolbar .bs-bars{
        width: 100%;
        margin-top: 0;
    }
    .alert{
        padding: 5px;
        margin-bottom: 0px;
    }
    .table-striped {
        height: calc(100% - 158px);
    }
    .noBtn{
        padding: 2px 12px;
    }
    .noBtn:focus{
        outline:none;
    }

    .btn.active.focus,
    .btn.active:focus,.btn.focus,
    .btn:active.focus,.btn:active:focus,
    .btn:focus{ outline:none; }
    button{outline:none;}
    .w100{
        width: 100px;
    }
    .left-fixed-body-columns{
        z-index: 9;
    }
    .cop{
        cursor:pointer;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row no-gutter">
                    <div class="col-sm-1">
                        <select name="salesDept" id="salesDept" class="form-control valid"
                                        aria-invalid="false" required>
                                    <option value="">-- 运营组 --</option>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                    </div>
                    <div class="col-sm-1">
                        <div class="form-group">
                            <div class="flex">
                                <div class="flex_right" style="padding: 0 5px">
                                    <input name="custAbbr" id="custAbbr" placeholder="客户简称" class="form-control" type="text"
                                    maxlength="50" aria-required="true">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">

                        <div class="form-group">
                            <div class="col-sm-4">
                                <select  name="startProvinceId" id="deliProvinceId"  class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                            <div class="col-sm-4">
                                <select name="startCityId" id="deliCityId"  class="form-control valid" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4">
                                <select name="startAreaId" id="deliAreaId"  class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>

                    </div>
                    <div class="col-sm-3">
                      <div class="form-group">
                            <div class="col-sm-4">
                                <select  name="endProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                            <div class="col-sm-4">
                                <select name="endCityId" id="arriCityId" class="form-control" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4">
                                <select name="endAreaId" id="arriAreaId" class="form-control" aria-invalid="false"></select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2">

                        <div class="form-group input-group">
                            <div class="col-sm-12 input-group date">
                                <input type="text" placeholder="调度起始时间"  class="form-control"
                                id="datetimepicker"  name="params[datetimepicker]" autocomplete="off"
                                th:value="${datetimepicker}">
                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>

                    </div>


                    <div class="col-md-2">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>

                <div class="row no-gutter">
                    <div class="col-sm-3">
                        <select name="carName" placeholder="类型" id="carName" class="form-control valid noselect2 selectpicker" onchange="setCarName()"
                                aria-invalid="false" data-none-selected-text="类型" multiple>
                            <option value="公路整车">公路整车</option>
                            <option value="冷链整车">冷链整车</option>
                            <option value="危化整车">危化整车</option>
                        </select>
                    </div>
                    
                    <div class="col-sm-3">
                        <select name="carLen" placeholder="车长" id="carLen" class="form-control valid noselect2 selectpicker" onchange="setCarLenName()"
                                aria-invalid="false" data-none-selected-text="车长" multiple th:with="type=${@dict.getType('car_len')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
    
                    <div class="col-sm-3">
                        <select name="carType" placeholder="车型" id="carType" class="form-control valid noselect2 selectpicker" onchange="setCarTypeName()"
                                aria-invalid="false" data-none-selected-text="车型" multiple th:with="type=${@dict.getType('car_type')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <div style="color: #a94442;">
                <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
                <span>当前指导价时间段为：2022-5-9 ~ 2022-5-9</span>
            </div>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
        <div class="col-sm-12 select-table" style="text-align: center;">
            <button type="button" class="btn btn-primary" onclick="leading()">导入指导价</button>
            <button type="button" class="btn btn-default" onclick="closeItem()">关闭</button>
        </div>
    </div>

</div>

<script id="dNo" type="text/html">
    <div>
        <div class="row no-gutter" style="padding: 0;margin: 10px 0 0 0;">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4">指导价有效期：</label>
                    <div class="col-sm-8">
                        <input type="text" placeholder="开始日期" style="width: 45%; float: left;" class="form-control"
                        id="regDateStart"  name="params[regDateStart]" autocomplete="off"
                        th:value="${regDateStart}">
                        <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                        <input type="text" placeholder="结束日期" style="width: 45%; float: left;" class="form-control"
                                id="regDateEnd"  name="params[regDateEnd]" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>


<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: bootstrap-table-editable-js" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:guidePrice:edit')}]];
    var prefix = ctx + "tms/transport_price";

    $(function () {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        var options = {
            url: prefix + "/list",
            modalName: "客户运输线路指导价",
            showToggle: false,
            showColumns: false,
            showSearch: false,
            showRefresh: false,
            uniqueId: "custGuidePriceId",
            height: 580,  
            fixedColumns: true,
            fixedNumber:2,
            clickToSelect: true,
            onEditableSave: function (field, row, oldValue, $el) {
                let list=oldValue.split('_');
                console.log(list[1])
            },
            columns: [
                [
                    // {
                    //     title: '运营组',
                    //     field : 'DEPT_NAME',
                    //     align: 'left',
                    //     valign: 'middle',
                    // },
                    {
                        title: '客户简称',
                        field : 'CUST_ABBR',
                        align: 'left',
                        width:'100px'
                    },
                    {
                        title: '提到货地址',
                        field : 'LINE_NAME',
                        align: 'left',
                        width:'200px'
                    },

                    // {
                    //     title: "公路整车",
                    //     halign:"center",
                    //     align:"center",
                    //     colspan: 48,
                    //     rowspan: 1
                    // },
                    // {
                    //     title: "冷链整车",
                    //     halign:"center",
                    //     align:"center",
                    //     colspan: 48,
                    //     rowspan: 1
                    // },
                    // {
                    //     title: "危化整车",
                    //     halign:"center",
                    //     align:"center",
                    //     colspan: 48,
                    //     rowspan: 1
                    // }
                // ],
                // [
                    {
                        title: "公路整车-13米厢式货车",
                        field: '公路整车_1_H02',
                        
                        align: "center",
                        visible: false,

                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_1_H02T',
                        
                        align: "center",
                        visible: false,
                        
                        formatter: function(value, row, index) {
                            return toDataO(row,'公路整车_1_H02')
                        }
                    },
                    {
                        title: "公路整车-13米平板货车",
                        field: '公路整车_1_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_1_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toDataO(row,'公路整车_1_H05')
                        }
                    },
                    {
                        title: "公路整车-13米仓栅式货车",
                        field: '公路整车_1_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_1_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toDataO(row,'公路整车_1_H09')
                        }
                    },
                    {
                        title: "公路整车-13米危险品车",
                        field: '公路整车_1_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_1_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toDataO(row,'公路整车_1_W01')
                        }
                    },
                    {
                        title: "公路整车-13.75米厢式货车",
                        field: '公路整车_2_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_2_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_2_H02')
                        }
                    },
                    {
                        title: "公路整车-13.75米平板货车",
                        field: '公路整车_2_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_2_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_2_H05')
                        }
                    },
                    {
                        title: "公路整车-13.75米仓栅式货车",
                        field: '公路整车_2_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_2_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_2_H09')
                        }
                    },
                    {
                        title: "公路整车-13.75米危险品车",
                        field: '公路整车_2_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_2_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_2_W01')
                        }
                    },
                    {
                        title: "公路整车-17.5米厢式货车",
                        field: '公路整车_5_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_5_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_5_H02')
                        }
                    },
                    {
                        title: "公路整车-17.5米平板货车",
                        field: '公路整车_5_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_5_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_5_H05')
                        }
                    },
                    {
                        title: "公路整车-17.5米仓栅式货车",
                        field: '公路整车_5_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_5_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_5_H09')
                        }
                    },
                    {
                        title: "公路整车-17.5米危险品车",
                        field: '公路整车_5_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_5_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_5_W01')
                        }
                    },
                    {
                        title: "公路整车-4.2米厢式货车",
                        field: '公路整车_8_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_8_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_8_H02')
                        }
                    },
                    {
                        title: "公路整车-4.2米平板货车",
                        field: '公路整车_8_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_8_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_8_H05')
                        }
                    },
                    {
                        title: "公路整车-4.2米仓栅式货车",
                        field: '公路整车_8_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_8_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_8_H09')
                        }
                    },
                    {
                        title: "公路整车-4.2米危险品车",
                        field: '公路整车_8_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_8_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_8_W01')
                        }
                    },
                    {
                        title: "公路整车-6.8米厢式货车",
                        field: '公路整车_12_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_12_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_12_H02')
                        }
                    },
                    {
                        title: "公路整车-6.8米平板货车",
                        field: '公路整车_12_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_12_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_12_H05')
                        }
                    },
                    {
                        title: "公路整车-6.8米仓栅式货车",
                        field: '公路整车_12_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_12_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_12_H09')
                        }
                    },
                    {
                        title: "公路整车-6.8米危险品车",
                        field: '公路整车_12_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_12_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_12_W01')
                        }
                    },
                    {
                        title: "公路整车-9.6米厢式货车",
                        field: '公路整车_16_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_16_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_16_H02')
                        }
                    },
                    {
                        title: "公路整车-9.6米平板货车",
                        field: '公路整车_16_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_1_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_1_W01')
                        }
                    },
                    {
                        title: "公路整车-9.6米仓栅式货车",
                        field: '公路整车_16_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_16_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_16_H09')
                        }
                    },
                    {
                        title: "公路整车-9.6米危险品车",
                        field: '公路整车_16_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '公路整车_16_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'公路整车_16_W01')
                        }
                    },
                    {
                        title: "冷链整车-13米厢式货车",
                        field: '冷链整车_1_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_1_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_1_H02')
                        }
                    },
                    {
                        title: "冷链整车-13米平板货车",
                        field: '冷链整车_1_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_1_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_1_H05')
                        }
                    },
                    {
                        title: "冷链整车-13米仓栅式货车",
                        field: '冷链整车_1_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_1_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_1_H09')
                        }
                    },
                    {
                        title: "冷链整车-13米危险品车",
                        field: '冷链整车_1_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_1_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_1_W01')
                        }
                    },
                    {
                        title: "冷链整车-13.75米厢式货车",
                        field: '冷链整车_2_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_2_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_2_H02')
                        }
                    },
                    {
                        title: "冷链整车-13.75米平板货车",
                        field: '冷链整车_2_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_2_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_2_H05')
                        }
                    },
                    {
                        title: "冷链整车-13.75米仓栅式货车",
                        field: '冷链整车_2_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_2_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_2_H09')
                        }
                    },
                    {
                        title: "冷链整车-13.75米危险品车",
                        field: '冷链整车_2_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_2_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_2_W01')
                        }
                    },
                    {
                        title: "冷链整车-17.5米厢式货车",
                        field: '冷链整车_5_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_5_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_5_H02')
                        }
                    },
                    {
                        title: "冷链整车-17.5米平板货车",
                        field: '冷链整车_5_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_5_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_5_H05')
                        }
                    },
                    {
                        title: "冷链整车-17.5米仓栅式货车",
                        field: '冷链整车_5_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_5_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_5_H09')
                        }
                    },
                    {
                        title: "冷链整车-17.5米危险品车",
                        field: '冷链整车_5_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_5_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_5_W01')
                        }
                    },
                    {
                        title: "冷链整车-4.2米厢式货车",
                        field: '冷链整车_8_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_8_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_8_H02')
                        }
                    },
                    {
                        title: "冷链整车-4.2米平板货车",
                        field: '冷链整车_8_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_8_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_8_H05')
                        }
                    },
                    {
                        title: "冷链整车-4.2米仓栅式货车",
                        field: '冷链整车_8_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_8_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_8_H09')
                        }
                    },
                    {
                        title: "冷链整车-4.2米危险品车",
                        field: '冷链整车_8_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_8_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_8_W01')
                        }
                    },
                    {
                        title: "冷链整车-6.8米厢式货车",
                        field: '冷链整车_12_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_12_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_12_H02')
                        }
                    },
                    {
                        title: "冷链整车-6.8米平板货车",
                        field: '冷链整车_12_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_12_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_12_H05')
                        }
                    },
                    {
                        title: "冷链整车-6.8米仓栅式货车",
                        field: '冷链整车_12_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_12_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_12_H09')
                        }
                    },
                    {
                        title: "冷链整车-6.8米危险品车",
                        field: '冷链整车_12_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_12_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_12_W01')
                        }
                    },
                    {
                        title: "冷链整车-9.6米厢式货车",
                        field: '冷链整车_16_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_16_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_16_H02')
                        }
                    },
                    {
                        title: "冷链整车-9.6米平板货车",
                        field: '冷链整车_16_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_16_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_16_H05')
                        }
                    },
                    {
                        title: "冷链整车-9.6米仓栅式货车",
                        field: '冷链整车_16_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_16_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_16_H09')
                        }
                    },
                    {
                        title: "冷链整车-9.6米危险品车",
                        field: '冷链整车_16_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '冷链整车_16_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'冷链整车_16_W01')
                        }
                    },
                    {
                        title: "危化整车-13米厢式货车",
                        field: '危化整车_1_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_1_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_1_H02')
                        }
                    },
                    {
                        title: "危化整车-13米平板货车",
                        field: '危化整车_1_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_1_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_1_H05')
                        }
                    },
                    {
                        title: "危化整车-13米仓栅式货车",
                        field: '危化整车_1_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_1_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_1_H09')
                        }
                    },
                    {
                        title: "危化整车-13米危险品车",
                        field: '危化整车_1_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_1_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_1_W01')
                        }
                    },
                    {
                        title: "危化整车-13.75米厢式货车",
                        field: '危化整车_2_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_2_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_2_H02')
                        }
                    },
                    {
                        title: "危化整车-13.75米平板货车",
                        field: '危化整车_2_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_2_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_2_H05')
                        }
                    },
                    {
                        title: "危化整车-13.75米仓栅式货车",
                        field: '危化整车_2_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_2_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_2_H09')
                        }
                    },
                    {
                        title: "危化整车-13.75米危险品车",
                        field: '危化整车_2_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_2_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_2_W01')
                        }
                    },
                    {
                        title: "危化整车-17.5米厢式货车",
                        field: '危化整车_5_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_5_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_5_H02')
                        }
                    },
                    {
                        title: "危化整车-17.5米平板货车",
                        field: '危化整车_5_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_5_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_5_H05')
                        }
                    },
                    {
                        title: "危化整车-17.5米仓栅式货车",
                        field: '危化整车_5_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_5_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_5_H09')
                        }
                    },
                    {
                        title: "危化整车-17.5米危险品车",
                        field: '危化整车_5_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_5_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_5_W01')
                        }
                    },
                    {
                        title: "危化整车-4.2米厢式货车",
                        field: '危化整车_8_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_8_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_8_H02')
                        }
                    },
                    {
                        title: "危化整车-4.2米平板货车",
                        field: '危化整车_8_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_8_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_8_H05')
                        }
                    },
                    {
                        title: "危化整车-4.2米仓栅式货车",
                        field: '危化整车_8_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_8_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_8_H09')
                        }
                    },
                    {
                        title: "危化整车-4.2米危险品车",
                        field: '危化整车_8_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_8_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_8_W01')
                        }
                    },
                    {
                        title: "危化整车-6.8米厢式货车",
                        field: '危化整车_12_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_12_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_12_H02')
                        }
                    },
                    {
                        title: "危化整车-6.8米平板货车",
                        field: '危化整车_12_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_12_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_12_H05')
                        }
                    },
                    {
                        title: "危化整车-6.8米仓栅式货车",
                        field: '危化整车_12_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_12_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_12_H09')
                        }
                    },
                    {
                        title: "危化整车-6.8米危险品车",
                        field: '危化整车_12_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_12_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_12_W01')
                        }
                    },
                    {
                        title: "危化整车-9.6米厢式货车",
                        field: '危化整车_16_H02',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_16_H02T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_16_H02')
                        }
                    },
                    {
                        title: "危化整车-9.6米平板货车",
                        field: '危化整车_16_H05',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_16_H05T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_16_H05')
                        }
                    },
                    {
                        title: "危化整车-9.6米仓栅式货车",
                        field: '危化整车_16_H09',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_16_H09T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_16_H09')
                        }
                    },
                    {
                        title: "危化整车-9.6米危险品车",
                        field: '危化整车_16_W01',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                            return toData(value)
                        },
                        editable: {
                            type: 'text',
                            title: '修改指导价',
                            emptytext: '暂无数据',
                            validate: function(v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            },
                            noeditFormatter: function (value,row,index) {
                                if(value){
                                    return false;
                                } else{
                                    return '';
                                }
                            }
                        }
                    },
                    {
                        title: "次数",
                        field: '危化整车_16_W01T',
                        
                        align: "center",
                        visible: false,
                         
                        formatter: function(value, row, index) {
                        return toDataO(row,'危化整车_16_W01')
                        }
                    },
                ]
               ],
           
        };
        $.table.init(options);

        $.provinces.init("startProvince", "startCity", "startArea", "", "", "");
        $.provinces.init("endProvince", "endCity", "endArea", "", "", "");

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            var datetimepicker = laydate.render({
                elem: '#datetimepicker', //指定元素
                isInitValue : false,
                trigger: 'click',
                type: 'datetime'
            });
        });


        $('#carName').val(["公路整车"]);
        $('#carLen').val(["1","2","5","8","12","16"])
        $('#carType').val(["H05"])
        setCarName()
        setCarLenName()
        setCarTypeName()


    });

    function leading(){
        layer.open({
                type: 1,
                area: ['55%', '40%'],
                fix: false,
                maxmin: true,
                shade: 0.3,
                title: '选择导入指导价时间段',
                content: $('#dNo').html(),
                btn: ['确定', '取消'],
                shadeClose: true,
                btn1: function(index, layero){
                    
                },
                success: function (layero, index) {
                    var laydate = layui.laydate;
                    var regDateStart = laydate.render({
                        elem: '#regDateStart', //指定元素
                        isInitValue : false,
                        trigger: 'click',
                        type: 'date'
                    });
                    var regDateEnd = laydate.render({
                        elem: '#regDateEnd', //指定元素
                        isInitValue : false,
                        trigger: 'click',
                        type: 'date'
                    });
                }
            });
    }
    
    function checkNumber(val) {
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val)){
            return true;
        }
        else{
            return false;
        }
    }

    function toData(data){
        let datas;
        if(data){
            let list=data.split('_');
            datas  = list[0]
        }else{
            datas=null
        }
        return datas
    }
    function toDataO(data,name){
        let row=data[name]
        let datas='';
        if(row){
            let list=row.split('_');
            datas  = `<span class='cop' onclick="historyPriceBtn('\ `+name+` \')">`+list[2]+`</span>`
        }
        return datas
    }

    var carNameList=[]
    var carLenList=[]
    var carTypeList=[]

    function setCarName(){
        carNameList=$('#carName').selectpicker('val')
        toTable()
    }
    function setCarLenName() {
        carLenList=$('#carLen').selectpicker('val')
        toTable()
    }
    function setCarTypeName() {
        carTypeList=$('#carType').selectpicker('val')
        toTable()
    }

    var carNameOldList=[]
    var carLenOldList=[]
    var carTypeOldList=[]

    function toTable(){
        // let typeList=['公路整车','冷链整车','危化整车']
        let list=[]
        let oldList=[]
       

        if(carLenOldList&&carTypeOldList&&carNameOldList&&carLenOldList.length!=0&&carTypeOldList.length!=0&&carNameOldList.length!=0){
            carNameOldList.forEach(reo=>{
                carLenOldList.forEach(ret=>{
                    carTypeOldList.forEach(res=>{
                        oldList.push(reo+"_"+ret+"_"+res)
                        oldList.push(reo+"_"+ret+"_"+res+"T")
                    })
                })
            })


            if(oldList.length!=0){
                oldList.forEach(res=>{
                    $.table.hideColumn(res);
                })
            }
        }

        if(carLenList&&carTypeList&&carNameList&&carLenList.length!=0&&carTypeList.length!=0&&carNameList.length!=0){

            carNameList.forEach(reo=>{
                carLenList.forEach(ret=>{
                    carTypeList.forEach(res=>{
                        list.push(reo+"_"+ret+"_"+res)
                        list.push(reo+"_"+ret+"_"+res+"T")
                    })
                })
            })
            carNameOldList=carNameList
            carLenOldList=carLenList
            carTypeOldList=carTypeList

            if(list.length!=0){
                list.forEach(res=>{
                    $.table.showColumn(res);
                })
            }
        }

        
    }

    /**
     * 查询历史价格,不带任何条件
     */
     function historyPriceBtn(data) {
        let split = data.split('_');
        var url = ctx + "tms/segment/segmentHistoryPriceBtn?carLen=" + split[1] + "&carType=" + split[2]
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "历史价格",
            area: ['85%','100%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        }); 

    }

</script>
</body>
</html>