<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户运输线路指导价主列表')"/>
    <th:block th:include="include :: bootstrap-editable-css" />

</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 70px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
    }
    .info button {
        color: inherit;
        background-color: transparent;
        -webkit-transition: all .5s;
        transition: all .5s;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .bgw{
        background: #fff;
    }
    .clearfix{
        height: 48px;
        position: absolute;
        bottom: 0;
    }
    .cop{
        cursor:pointer;
        color: #409eff;
    }
    .bootstrap-table{
        height: calc(100vh - 4px);overflow-y: auto;
    }
    .table-striped {
        height: 100vh ;padding: 0;
    }

    .lf{
        /* float: left; */
        margin-right: 10px;
        background-color: #ffffff;
        color: #1ab394;
        border: 1px solid #1ab394;
        border-radius: 3px;
        cursor:pointer;
    }
    .fr{
        float: right;
    }
    .label-success{
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }
</style>
<body class="gray-bg">
<div class="container-fluid">
    <div class="row">

        <div class="col-sm-12 bgw table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>

    </div>

</div>
<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<script id="dNo" type="text/html">
    <div>
        <div class="row no-gutter" style="padding: 0;margin: 10px 0 0 0;">
            <div class="col-sm-12">
                <div class="form-group">
                    <label class="col-sm-4">指导价有效期：</label>
                    <div class="col-sm-8">
                        <input type="text" placeholder="开始日期" style="width: 45%; float: left;" class="form-control"
                        id="regDateStart"  name="params[regDateStart]" autocomplete="off"
                        th:value="${regDateStart}">
                        <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                        <input type="text" placeholder="结束日期" style="width: 45%; float: left;" class="form-control"
                                id="regDateEnd"  name="params[regDateEnd]" autocomplete="off">
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>

<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:guidePrice:edit')}]];
    var delFlag = [[${@permission.hasPermi('tms:guidePrice:removePrice')}]];
    var prefix = ctx + "tms/transport_price";

    //客户id
    // var customerId = [[${ customerId }]];
    var customerId = [[${ param.customerId }]];
    var custAbbr = [[${ param.custAbbr }]];
    //运输方式
    var trans_code = [[${@dict.getType('trans_code')}]];

    var carLen = [[${@dict.getType('car_len')}]];
    var carType = [[${@dict.getType('car_type')}]];


    $(function () {
        let data=""
        if(window.parent.$("#carLen").val()){
            data+='&carLen='+window.parent.$("#carLen").val()
        }
        if(window.parent.$("#carType").val()){
            data+='&carType='+window.parent.$("#carType").val()
        }
        if(window.parent.$("#startProvince").val()){
            data+='&startProvinceId='+window.parent.$("#startProvince").val()
        }
        if(window.parent.$("#startCity").val()){
            data+='&startCityId='+window.parent.$("#startCity").val()
        }
        if(window.parent.$("#startCity").val()){
            data+='&startCityId='+window.parent.$("#startCity").val()
        }
        if(window.parent.$("#endProvince").val()){
            data+='&endProvinceId='+window.parent.$("#endProvince").val()
        }
        if(window.parent.$("#endCity").val()){
            data+='&endCityId='+window.parent.$("#endCity").val()
        }
        if(window.parent.$("#endArea").val()){
            data+='&endAreaId='+window.parent.$("#endArea").val()
        }
        if(window.parent.$("#isReview").val()){
            data+='&isReview='+window.parent.$("#isReview").val()
        }

        var options = {
            url: prefix + "/price_list?customerId=" + customerId+data,
            modalName: "客户运输线路指导价",
            showToggle: false,
            showColumns: false,
            showSearch: false,
            showRefresh:false,
            // uniqueId: "custGuidePriceId",
            idField: "id",
            clickToSelect: true,
            height: 580,  
            onLoadSuccess: function (data) {
                mergeCells(data.rows, "transLineName","transLineName", 1, $('#bootstrap-table')); //行合并
                // mergeCells(data.rows, "transLineName","a", 1, $('#bootstrap-table')); //行合并
                // mergeCells(data.rows, "transType","transType", 1, $('#bootstrap-table')); //行合并
            },
            columns: [
                 {
                    title: '',
                    field: 'id',
                    align: 'left',
                    formatter: function status(value,row) {
                        // let data =''
                        // if(row.isReview==0){
                        //     data=`<span class="glyphicon glyphicon glyphicon-book" style="color: #1ab394;cursor:pointer;" onclick="onRead(' `+value+` ');"></span>`
                        // }
                        return `<span class="glyphicon glyphicon-import lf" data-toggle="tooltip" data-placement="top" title="导入当前数据指导价" style="color: #1ab394;cursor:pointer;" onclick="leading('`+value+`');"></span>`
                    }
                },
                {
                    title: '提到货地址',
                    field: 'transLineName',
                    align: 'left',
                    // formatter: function status(value, row,index) {
                    //     let data =''
                    //     if(row.isImport==1){
                    //         data=value+`<span class="label label-success fr" >已导</span>`
                    //     }else{
                    //         data=value
                    //     }
                    //     return data
                    // }
                },
                {
                    title: '状态',
                    field: 'carLen',
                    align: 'left',
                    formatter: function status(value,row) {
                        let data =''
                        if(row.isReview==1){
                            data=`<span class="label lf" onclick="onReadT(' `+row.id+` ')">已阅</span>`
                        }else{
                            data=`<span class="label lf" onclick="onRead(' `+row.id+` ')">未阅</span>`
                        }
                        if(row.isImport==1){
                            data+=`<span class="label lf label-success" style="cursor: default;">已导</span>`
                        }
                        return data
                    }
                },
                {
                    title: '车型',
                    field: 'carType',
                    align: 'left',
                    formatter: function status(value, row) {
                        let html =$.table.selectDictLabel(carLen, row.carLen)+"米"+ $.table.selectDictLabel(carType, row.carType)

                        let img;
                        if (row.transType == 15) {
                            img = '<img src="/img/danger.jpg" title="危化整车" style="width: 15px;height: 15px"> ' + html;
                        }else if (row.transType == 4) {
                            img = '<img src="/img/cold_chain.png" title="冷链整车" style="width: 15px;height: 15px"> ' + html;
                        }else if (row.transType == 0) {
                            img = '<img src="/img/vehicle.png" title="公路整车" style="width: 15px;height: 15px"> ' + html;
                        }else {
                            img = html;
                        }

                        return img
                    }
                },
                {
                    title: '实际平均价',
                    field: 'guidePrice',
                    align: 'left',
                    
                    editable: {
                        type: 'text',
                        title: '修改实际平均价',
                        emptytext: '暂无数据',
                        validate:  function (v) {
                            if (!$.numberUtil.checkNumber(v)) {
                                return "请输入数字！";
                            }
                        },
                        params:function (params){
                            let data= {};
                            data.id = params.pk
                            data[params.name] = params.value
                            return data;
                        },
                        url:ctx + "tms/transport_price/edit",
                        success: function (response, newValue,row) {
                            if (response.code == 500) {
                                return response.msg;
                            }else {
                                $.btTable.bootstrapTable('refresh');
                            }
                        }
                    }
                },
                {
                    title: '次数',
                    field: 'lotCnt',
                    align: 'left',
                    formatter: function(value, row, index) {
                        return toDataO(row)
                    }
                }
            ]
        };
        $.table.init(options);
        $.provinces.init("startProvince", "startCity", "startArea", "", "", "");
        $.provinces.init("endProvince", "endCity", "endArea", "", "", "");

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            var datetimepicker = laydate.render({
                elem: '#datetimepicker', //指定元素
                isInitValue : false,
                trigger: 'click',
                type: 'datetime'
            });
        });

       
    });

    /**
     * 合并单元格
     * @param data  原始数据（在服务端完成排序）
     * @param fieldName 合并属性名称
     * @param colspan   合并列
     * @param target    目标表格对象
     */
    function mergeCells(data,exhibitionName,fieldName,colspan,target){
        //声明一个map计算相同属性值在data对象出现的次数和
        var sortMap = {};
        for(var i = 0 ; i < data.length ; i++){
            for(var prop in data[i]){
                if(prop == exhibitionName){
                    var key = data[i][prop]
                    if(sortMap.hasOwnProperty(key)){
                        sortMap[key] = sortMap[key] * 1 + 1;
                    } else {
                        sortMap[key] = 1;
                    }
                    break;
                }
            }
        }
        for(var prop in sortMap){
            // console.log(prop,sortMap[prop])
        }
        var index = 0;
        for(var prop in sortMap){
            var count = sortMap[prop] * 1;
            $(target).bootstrapTable('mergeCells',{index:index, field:fieldName, colspan: colspan, rowspan: count});
            index += count;
        }
    }

    function changeDiv(){
        var startProvince= $('#startProvince').val()
        var startCity= $('#startCity').val()
        var startArea= $('#startArea').val()
        var endProvince= $('#endProvince').val()
        var endCity= $('#endCity').val()
        var endArea= $('#endArea').val()
        $.provinces.init("startProvince", "startCity", "startArea", endProvince, endCity, endArea);
        $.provinces.init("endProvince", "endCity", "endArea", startProvince, startCity, startArea);
        //searchPre()

    }

    function toDataO(data){
        let datas='';
        if(data){
            datas  = `<span class='cop' onclick="historyPriceBtn(' `+data.carLen+`','`+data.carType+` ','` +data.transLineId +`')">`+data.lotCnt+`</span>`
        }
        return datas
    }
     /**
     * 查询历史价格,不带任何条件
     */
     function historyPriceBtn(carLen,carType,transLineId) {
         let lastDay = getMonthLast();
         let lastMonthFirst = getLastMonthFirst();

         var url = ctx + "tms/segment/segmentHistoryPriceBtn?carLen=" + carLen + "&carType=" + carType
             + "&custAbbr=" +custAbbr + "&transLineId=" + transLineId + "&dispatcherDateStart="+lastMonthFirst + "&dispatcherDateEnd="+lastDay;
        parent.layer.open({
            type: 2,
            maxmin: true,
            shade: false,
            title: "历史价格",
            area: ['85%','100%'],
            content: url,
            shadeClose: true,
            btn: ['<i class="fa fa-close"></i> 关闭'],
            yes: function (index, layero) {
                parent.layer.close(index);
            }
        }); 

    }



    //获取这个月的最后一天
    function getMonthLast(){
        var time=new Date();
        var year=time.getFullYear();
        var month=time.getMonth();
        if(month<10){
            month="0"+month;
        }
        var lastday=new Date(year,month,0).getDate();
        var v=year+'-'+month+'-'+lastday;
        return v ;
    }



    //获取上个月第一天
    function getLastMonthFirst() {
        var time=new Date();
        var year=time.getFullYear();
        var month=time.getMonth()-1;
        var day=time.getDate();
        if(month<10){
            month="0"+month;
        }
        if(month==0){
            year=year-1;
            month = 12;
        }
        //上个月的第一天
        var v=year+'-'+month+'-'+'0'+1;
        return v;
    }

    function onRead(id){
        $.operate.submit(ctx + "tms/transport_price/review", "post", "json",{id});
    }
    function onReadT(id){
        $.operate.submit(ctx + "tms/transport_price/unreview", "post", "json",{id});
    }

    function leading(val){
        layer.open({
                type: 1,
                area: ['50%', '30%'],
                fix: false,
                maxmin: true,
                shade: 0.3,
                title: '选择导入指导价时间段',
                content: $('#dNo').html(),
                btn: ['确定', '取消'],
                shadeClose: true,
                btn1: function(index, layero){
                    let s = $("#regDateStart").val();
                    let e = $("#regDateEnd").val();  
                    
                    let data ={
                        startDate:s,
                        endDate:e
                    }

                    if (val) {
                        data.transportPriceId= val
                    }

                    $.operate.submit(ctx + "tms/transport_price/add_guide_pirce_single", "post", "json",data,callback);
                    layer.close(index);
                },
                success: function (layero, index) {
                    var laydate = layui.laydate;
                    var regDateStart = laydate.render({
                        elem: '#regDateStart', //指定元素
                        isInitValue : false,
                        trigger: 'click',
                        type: 'date',
                    });
                    var regDateEnd = laydate.render({
                        elem: '#regDateEnd', //指定元素
                        isInitValue : false,
                        trigger: 'click',
                        type: 'date',
                    });
                }
            });
    }

    function callback(params) {
        if(params.code==0){
            $.ajax({
                url: ctx + "tms/transport_price/import_rate",
                method: 'get',
                dataType: "json",
                data: {customerId:customerId[0]},
                success: function (result) { 
                    if(result.code==0){
                        $('#importCount', parent.document).html(  params.data.importCount+'/'+params.data.ct )
                    }
                }
            });    
        }
    }

</script>
</body>
</html>