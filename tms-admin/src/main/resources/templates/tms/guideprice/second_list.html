<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户运输线路指导价主列表')"/>
    <th:block th:include="include :: bootstrap-editable-css" />

</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 70px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
    }
    .info button {
        color: inherit;
        background-color: transparent;
        -webkit-transition: all .5s;
        transition: all .5s;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .bgw{
        background: #fff;
    }

</style>
<body class="gray-bg">
<div class="container-fluid">
    <div class="row">

        <div class="col-sm-12 bgw" style="height: 100vh;margin-top: 0;overflow-y: auto;">
            <div class="col-sm-12 search-collapse" style="display: block">
                <form id="role-form" class="form-horizontal">
                    <input type="hidden" name="customerId" th:value="${customerId}">
                    <div class="over">
                        <div class="row">
                            

                            <div class="col-xs-12 col-sm-12 col-md-12 ">
                                <div class="flex">
                                    <label class="flex_left">提货地址：</label>
                                    <div class="flex_right over">
                                        <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                            <select id="startProvince" name="startProvinceId"
                                                    class="form-control valid" aria-invalid="false"></select>
                                            </select>
                                        </div>
                                        <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                            <select id="startCity" name="startCityId" class="form-control valid"
                                                    aria-invalid="false"></select>
                                        </div>
                                        <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                            <select id="startArea" name="startAreaId" class="form-control valid"
                                                    aria-invalid="false"></select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-12 col-sm-12 col-md-12 ">
                               <div class="fl" onclick="changeDiv()" style="width: 60px">
                                    <img th:src="@{/img/change.png}" style="width: 20px;height: 20px;display: block;margin: auto 20px ">
                                </div>
                            </div>

                            <div class="col-xs-12 col-sm-12 col-md-12 ">
                               
                                <div class="flex">
                                    <label class="flex_left">收货地址：</label>
                                    <div class="flex_right over">
                                        <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                            <select id="endProvince" name="endProvinceId"
                                                    class="form-control valid" aria-invalid="false"></select>
                                            </select>
                                        </div>
                                        <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                            <select id="endCity" name="endCityId" class="form-control valid"
                                                    aria-invalid="false"></select>
                                        </div>
                                        <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                            <select id="endArea" name="endAreaId" class="form-control valid"
                                                    aria-invalid="false"></select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-12 col-sm-12 col-md-12">
                                <div class="">
                                    <div class="flex">
                                        <div class="flex_left">是否过期：</div>
                                        <div class="flex_right over">
                                            <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                <select name="isExpired" id="isExpired" class="form-control valid"
                                                        aria-invalid="false" required>
                                                    <option value=""></option>
                                                    <option value="0">未过期</option>
                                                    <option value="1">已过期</option>
                                                </select>
                                            </div>

                                            <div class="fl" style="width: 200px;padding: 0 10px">
                                                <!--                            <div>-->
                                                <!--                                <div class="flex">-->
                                                <!--                                    <div class="flex_left">审核条数：</div>-->
                                                <!--                                    <div class="flex_right">-->
                                                <!--                                        <select name="params[checkTypeTimes]" class="form-control">-->
                                                <!--                                            <option value="">&#45;&#45;请选择&#45;&#45;</option>-->
                                                <!--                                            <option th:value="0">待审核条数</option>-->
                                                <!--                                            <option th:value="1">审核通过条数</option>-->
                                                <!--                                            <option th:value="2">审核不通过条数</option>-->
                                                <!--                                        </select>-->
                                                <!--                                    </div>-->
                                                <!--                                </div>-->
                                                <!--                            </div>-->
                                                                            
                                                <div class="form-group">
                                                    <a class="btn btn-primary btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                                    <a class="btn btn-default btn-outline btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                                </div>
                                                    
                                            </div>
                                            
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        
                        
                       
                    </div>
                </form>
            </div>

            <!--        <div class="btn-group-sm" id="toolbar" role="group">-->
            <!--            <a class="btn btn-danger single disabled" shiro:hasPermission="tms:guidePrice:secondList:remove"-->
            <!--               onclick="remove()">-->
            <!--                <i class="fa fa-remove"></i> 删除-->
            <!--            </a>-->
            <!--        </div>-->
            <div class="col-sm-12 select-table">
                <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
            </div>
        </div>

    </div>

</div>
<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-table-editable-js"/>

<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:guidePrice:edit')}]];
    var delFlag = [[${@permission.hasPermi('tms:guidePrice:removePrice')}]];
    var prefix = ctx + "tms/guide_price/second_list";

    //客户id
    var customerId = [[${ customerId }]];
    //运输方式
    var trans_code = [[${@dict.getType('trans_code')}]];

    var carLen = [[${@dict.getType('car_len')}]];
    var carType = [[${@dict.getType('car_type')}]];



    $(function () {
        var options = {
            url: prefix + "/list",
            modalName: "客户运输线路指导价",
            showToggle: false,
            showColumns: false,
            showSearch: false,
            showRefresh:false,
            uniqueId: "custGuidePriceId",
            idField: "guidePriceDetailId",
            clickToSelect: true,
            onLoadSuccess: function (data) {
                mergeCells(data.rows, "lineName","lineName", 1, $('#bootstrap-table')); //行合并
                mergeCells(data.rows, "lineName","a", 1, $('#bootstrap-table')); //行合并
                // mergeCells(data.rows, "transType","transType", 1, $('#bootstrap-table')); //行合并
            },
            columns: [
                // {
                //     checkbox: true
                // },
                {
                    title: '操作',
                    field: 'a',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs ' + editFlag + '" href="javascript:void(0)" '
                            + 'onclick="editPrice(\'' + row.custGuidePriceId + '\',\'' + row.customerId
                            + '\')" title="修改"><i class="glyphicon glyphicon-eye-open" style="font-size: 15px;"></i></a> ');
                        actions.push('<a class="btn  btn-xs ' + delFlag + '" href="javascript:void(0)" '
                            + 'onclick="removePrice(\'' + row.custGuidePriceId + '\')" title="删除"><i class="glyphicon glyphicon-minus" style="font-size: 15px;color: #ffffff;background-color: #ed5565;border-radius: 4px;font-weight: 100;"></i></a> ');
                        return actions.join('');
                    }
                },
                {
                    title: '线路名称',
                    field: 'lineName',
                    align: 'left',
                },
                // {
                //     title: '运输方式',
                //     field: 'transType',
                //     align: 'left',
                //     formatter: function status(row,value) {
                //         return $.table.selectDictLabel(trans_code, value.transType);
                //     }
                // },
                {
                    title: '',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs ' + delFlag + '" href="javascript:void(0)" '
                            + 'onclick="removePriceDetail(\'' + row.guidePriceDetailId + '\')" title="删除"><i class="glyphicon glyphicon-minus" style="font-size: 15px;color: #ffffff;background-color: #ed5565;border-radius: 3px;font-weight: 100;"></i></a> ');
                        return actions.join('');
                    }
                },
                {
                    title: '',
                    formatter: function status(value,row) {
                        if (row.isExpired == 1) {
                            return `<span class="label label-danger">过期</span>`
                        }else {
                            return ''
                        }
                    }
                },
                {
                    title: '车长',
                    // field: 'carLen',
                    align: 'left',
                    formatter: function status(value,row) {
                        return $.table.selectDictLabel(carLen, row.carLen)
                    }
                    // editable: {
                    //     type: 'select',
                    //     title: '车长',
                    //     source: function () {
                    //         let data = []
                    //         for (const len of carLen) {
                    //             data.push({value:len.dictValue,text:len.dictLabel})
                    //         }
                    //         return data
                    //
                    //     },
                    //     emptytext: '暂无数据',
                    //     params:function (params){
                    //         params.id = params.pk
                    //         params.type = 0
                    //
                    //         return params;
                    //     },
                    //     url:ctx + "tms/guide_price/adjust_guide_price",
                    //     success: function (response, newValue,row) {
                    //         if (response.code == 500) {
                    //             return response.msg;
                    //         }
                    //
                    //     }
                    // }
                },
                {
                    title: '车型',
                    field: 'carType',
                    align: 'left',
                    formatter: function status(value, row) {
                        let html = $.table.selectDictLabel(carType, row.carType)

                        let img;
                        if (row.transType == 15) {
                            html = '<img src="/img/danger.jpg" title="危化整车" style="width: 15px;height: 15px"> ' + html;
                        }else if (row.transType == 4) {
                            html = '<img src="/img/cold_chain.png" title="冷链整车" style="width: 15px;height: 15px"> ' + html;
                        }else if (row.transType == 0) {
                            html = '<img src="/img/vehicle.png" title="公路整车" style="width: 15px;height: 15px"> ' + html;
                        }

                        return html
                    }
                    // editable: {
                    //     type: 'select',
                    //     title: '车型',
                    //     source: function () {
                    //         let data = []
                    //         for (const type of carType) {
                    //             data.push({value:type.dictValue,text:type.dictLabel})
                    //         }
                    //         return data
                    //     },
                    //     emptytext: '暂无数据',
                    //     params:function (params){
                    //         params.id = params.pk
                    //         params.type = 1
                    //
                    //         return params;
                    //     },
                    //     url:ctx + "tms/guide_price/adjust_guide_price",
                    //     success: function (response, newValue,row) {
                    //
                    //
                    //         return "qingl"
                    //     }
                    // }
                },
                {
                    title: '指导价',
                    field: 'guidingPrice',
                    align: 'left',
                    editable: {
                        type: 'text',
                        title: '修改指导价',
                        emptytext: '暂无数据',
                        validate:  function (v) {
                            if (!$.numberUtil.checkNumber(v)) {
                                return "请输入数字！";
                            }
                        },
                        params:function (params){
                            params.id = params.pk
                            params.type = 2

                            return params;
                        },
                        url:ctx + "tms/guide_price/adjust_guide_price",
                        success: function (response, newValue,row) {
                            if (response.code == 500) {
                                return response.msg;
                            }else {
                                $.btTable.bootstrapTable('refresh');
                            }
                        }
                    }
                },
                {
                    title: '开始日期',
                    field: 'startDate',
                    align: 'left',
                    editable: {
                        type: 'date',
                        title: '修改开始日期',
                        emptytext: '暂无数据',
                        placement: 'left',
                        clear: false,
                        datepicker: {
                            language: 'zh-CN',//中文支持
                        },
                        params:function (params){
                            params.id = params.pk
                            params.type = 3

                            return params;
                        },
                        url:ctx + "tms/guide_price/adjust_guide_price",
                        success: function (response, newValue,row) {
                            if (response.code == 500) {
                                return response.msg;
                            }else {
                                $.btTable.bootstrapTable('refresh');
                            }
                        }
                    }
                },
                {
                    title: '结束日期',
                    field: 'endDate',
                    align: 'left',
                    // formatter: function (value, row) {
                    //     if (row.isExpired == 1) {
                    //         return `<span class="label label-danger">`+row.endDate+`</span>`;
                    //     }else {
                    //         return row.endDate
                    //     }
                    // },
                    editable: {
                        type: 'date',
                        title: '修改结束日期',
                        emptytext: '暂无数据',
                        placement: 'left',
                        clear: false,
                        datepicker: {
                            language: 'zh-CN',//中文支持
                        },
                        params:function (params){
                            params.id = params.pk
                            params.type = 4

                            return params;
                        },
                        url:ctx + "tms/guide_price/adjust_guide_price",
                        success: function (response, newValue,row) {
                            if (response.code == 500) {
                                return response.msg;
                            }else {
                                $.btTable.bootstrapTable('refresh');
                            }
                        }
                    }
                },
                {
                    title: '备注',
                    field: 'memo',
                    align: 'left'
                }
            ]
        };
        $.table.init(options);
        $.provinces.init("startProvince", "startCity", "startArea", "", "", "");
        $.provinces.init("endProvince", "endCity", "endArea", "", "", "");
    });

    /**
     * 合并单元格
     * @param data  原始数据（在服务端完成排序）
     * @param fieldName 合并属性名称
     * @param colspan   合并列
     * @param target    目标表格对象
     */
    function mergeCells(data,exhibitionName,fieldName,colspan,target){
        //声明一个map计算相同属性值在data对象出现的次数和
        var sortMap = {};
        for(var i = 0 ; i < data.length ; i++){
            for(var prop in data[i]){
                if(prop == exhibitionName){
                    var key = data[i][prop]
                    if(sortMap.hasOwnProperty(key)){
                        sortMap[key] = sortMap[key] * 1 + 1;
                    } else {
                        sortMap[key] = 1;
                    }
                    break;
                }
            }
        }
        for(var prop in sortMap){
            console.log(prop,sortMap[prop])
        }
        var index = 0;
        for(var prop in sortMap){
            var count = sortMap[prop] * 1;
            $(target).bootstrapTable('mergeCells',{index:index, field:fieldName, colspan: colspan, rowspan: count});
            index += count;
        }
    }

    function editPrice(id, customerId) {
        var url = ctx + "tms/guide_price/edit_price?custGuidePriceId=" + id + "&customerId=" + customerId;
        // console.log('111222',url)
        window.parent.parent.$.modal.openTab("编辑指导价", url);
    }

    /** 删除 */
    function remove() {
        var id = $.table.selectColumns($.table._option.uniqueId);
        $.modal.confirm("确认要删除选中的数据吗?", function () {
            $.operate.submit(prefix + "/remove", "post", "json", {"id": id.join()});
        });
    }
    /** 删除 */
    function removePrice(custGuidePriceId) {
        $.modal.confirm("确认要删除选中的数据吗?", function () {
            $.operate.submit(ctx + "tms/guide_price/remove", "post", "json", {"custGuidePriceId": custGuidePriceId});
        });
    }
    /** 删除明细 */
    function removePriceDetail(guidePriceDetailId) {
        $.modal.confirm("确认要删除选中的数据吗?", function () {
            $.operate.submit(ctx + "tms/guide_price/remove_price", "post", "json", {"id": guidePriceDetailId});
        });
    }
    function changeDiv(){
        var startProvince= $('#startProvince').val()
        var startCity= $('#startCity').val()
        var startArea= $('#startArea').val()
        var endProvince= $('#endProvince').val()
        var endCity= $('#endCity').val()
        var endArea= $('#endArea').val()
        $.provinces.init("startProvince", "startCity", "startArea", endProvince, endCity, endArea);
        $.provinces.init("endProvince", "endCity", "endArea", startProvince, startCity, startArea);
        //searchPre()

    }
</script>
</body>
</html>