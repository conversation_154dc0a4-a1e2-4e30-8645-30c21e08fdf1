<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户运输线路指导价主列表')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="row no-gutter">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">客户简称：</label>
                            <div class="col-sm-8">
                                <input name="custAbbr" id="custAbbr" placeholder="客户简称" class="form-control" type="text"
                                       maxlength="50" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">运营组：</label>
                            <div class="col-sm-8">
                                <select name="params[salesDept]" id="salesDept" class="form-control valid"
                                        aria-invalid="false" required>
                                    <option value=""></option>
                                    <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                            th:text="${mapS.deptName}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">

                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger single disabled" shiro:hasPermission="tms:guidePrice:remove" onclick="remove()" >
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-primary" onclick="$.table.importExcel()" shiro:hasPermission="tms:guidePrice:import">
                <i class="fa fa-upload"></i> 导入
            </a>
            <a class="btn btn-warning single disabled" onclick="exportExcel()" shiro:hasPermission="tms:guidePrice:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-warning " onclick="exportAll()" shiro:hasPermission="tms:guidePrice:export">
                <i class="fa fa-download"></i> 导出实际指导价
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="tms:guidePrice:export">
                <i class="fa fa-download"></i> 导出指导价模板
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:guidePrice:edit')}]];
    var prefix = ctx + "tms/guide_price";

    $(function() {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });

        var options = {
            url: prefix + "/list",
            modalName: "客户运输线路指导价",
            showToggle:false,
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showColumns:false,
            uniqueId: "customerId",
            clickToSelect: true,
            columns: [
                {
                    checkbox: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="secondList(\'' + row.customerId + '\')" title="修改"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                        return actions.join('');
                    }
                },
                {
                    title: '线路条数',
                    field : 'countTransLine',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return "<div style='color:#f7a54a'>" + value + "</div>";
                    }
                },
                {
                    field : 'custAbbr',
                    title : '客户简称',
                    align: 'left'
                },
                {
                    field : 'salesDeptName',
                    title : '运营组',
                    align: 'left'
                }
            ]
        };
        $.table.init(options);
    });

    function secondList(customerId) {
        $.modal.openTab("线路", ctx + "tms/guide_price/second_list/" + customerId);
    }

    /** 删除 */
    function remove() {
        var id = $.table.selectColumns("customerId");
        $.modal.confirm("确认要删除选中的数据吗?", function() {
            $.operate.submit(ctx + "tms/guide_price/remove", "post", "json", {"customerId": id.join()});
        });
    }

    /**
     * 指导价导出
     */
    function exportExcel() {
        var customerId = $.table.selectColumns("customerId").join();
        var url = ctx + "report/guidePriceExport/"+customerId;
        $.modal.openTab("指导价导出", url);
    }

    function exportAll(){
        $.modal.confirm("确定导出所有指导价吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(ctx + "tms/guide_price/exportAll", null, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

</script>
</body>

<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>