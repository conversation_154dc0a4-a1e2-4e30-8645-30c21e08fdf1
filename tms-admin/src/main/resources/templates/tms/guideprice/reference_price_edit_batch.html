<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('批量修改指导价')" />
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<body>
    <div class="form-content">
        <form id="form-edit-price" class="form-horizontal" novalidate="novalidate">
  
            <div class="row">

                <div class="col-md-12 col-sm-12 col-xs-12">
                    <div class="flex">
                        <label class="flex_left"><span class="fcff">*</span>  <span>提货省市区：</span></label>
                        <div class="flex_right">
                            <div class="form-group">
                                <div class="col-sm-4 col-xs-4">
                                    <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" aria-invalid="false" required>
                                    </select>
                                </div>
                                <div class="col-sm-4 col-xs-4">
                                    <select name="deliCityId" id="deliCityId" class="form-control valid" aria-invalid="false" required></select>
                                </div>
                                <div class="col-sm-4 col-xs-4">
                                    <select name="deliAreaId" id="deliAreaId" class="form-control selectpicker valid"
                                            aria-invalid="false" multiple placeholder="请选择" data-none-selected-text="请选择">
                                        </select>
                                    <!-- <select name="deliAreaId" id="deliAreaId" class="form-control valid" aria-invalid="false"></select> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
    
                <div class="col-md-12 col-sm-12 col-xs-12">
                    <div class="flex">
                        <label class="flex_left"><span class="fcff">*</span>  <span>到货省市区：</span></label>
                        <div class="flex_right">
                            <div class="form-group">
                                <div class="col-sm-4 col-xs-4">
                                    <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false" required>
                                    </select>
                                </div>
                                <div class="col-sm-4 col-xs-4">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false" required></select>
                                </div>
                                <div class="col-sm-4 col-xs-4">
                                    <select name="arriAreaId" id="arriAreaId" class="form-control selectpicker valid"
                                        aria-invalid="false" multiple placeholder="请选择" data-none-selected-text="请选择">
                                    </select>
                                    <!-- <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false"></select> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 col-sm-6 col-xs-6">
                    <div class="flex">
                        <label class="flex_left"><span class="fcff">*</span>  <span>车长：</span></label>
                        <div class="flex_right">
                            <select id="carLen" name="carLen" class="form-control valid checkVal" aria-invalid="false" required
                                    th:with="type=${@dict.getType('car_len')}">
                                <option value="">-- 请选择 --</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-sm-6 col-xs-6">
                    <div class="flex">
                        <label class="flex_left"> <span>车型：</span></label>
                        <div class="flex_right">

                            <select id="carType" name="carType" class="form-control valid checkVal noselect2 selectpicker" aria-invalid="false"
                                    th:with="type=${@dict.getType('car_type')}" data-none-selected-text="车型" placeholder="车型" multiple>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
    
            </div>

            <div class="row">
    
                <div class="col-md-6 col-sm-6 col-xs-6">
                    <div class="flex">
                        <label class="flex_left"><span class="fcff">*</span>  <span>指导价：</span></label>
                        <div class="flex_right">
                            <input name="price" id="price" placeholder="指导价" class="form-control"  oninput="$.numberUtil.onlyNumberTwoDecimal(this);" type="text" autocomplete="off" aria-required="true" required>
                        </div>
                    </div>
                </div>
    
                <div class="col-md-6 col-sm-6 col-xs-6">
                    <div class="flex">
                        <label class="flex_left"><span class="fcff">*</span>  <span>价格类型：</span></label>
                        <div class="flex_right">
                            <select name="priceType" id="priceType" class="form-control" required>
                                <option value="">-- 请选择 --</option>
                                <option th:value="0">普货</option>
                                <option th:value="1">危险品</option>
                            </select>
                        </div>
                    </div>
                </div>
    
            </div>
    
        </form>
    </div>
<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<th:block th:include="include :: bootstrap-select-js" />

<script th:inline="javascript">
    var prefix = ctx + "tms/reference_price";

    $(function() {
         // 表单验证
         $("#form-edit-price").validate({
            focusCleanup: true
        });
        init("deliProvinceId", "deliCityId", "deliAreaId");
        init("arriProvinceId", "arriCityId", "arriAreaId");
        // $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        // $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
      
    });

    /**
     * 提交
     */
     function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-edit-price").serializeArray();
            $.operate.save(prefix + "/edit_price_batch", data);
        }
    }

    //验证字符串是否是数字
    function checkNumber(val) {
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val)){
            return true;
        }
        else{
            return false;
        }
    }

    function init(provinceId, cityId, areaId, province, city, area) {
            $('#'+ provinceId +' option').remove();
            $('#'+ cityId +' option').remove();
            $('#'+ areaId +' option').remove();
            $('#' + provinceId).append("<option value=''>-- 请选择 --</option>");
            $('#'+ cityId).append("<option value=''>-- 请选择 --</option>");
            $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");

            $.ajax({
                type: "get",
                url: ctx + "province/city?method=0&code=0",
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#' + provinceId).append("<option value='" + result[i].PROVINCE_CODE + "'>" + result[i].PROVINCE_NAME + "</option>")
                    }
                    $('#'+ provinceId +' option').each(function () {
                        if ($(this).val() == province) {
                            $(this).attr("selected", true)
                        }
                    })
                }
            });
            if (province !== undefined) {
                $.ajax({
                    type: "get",
                    url: ctx + "province/city?method=1&code=" + province,
                    async: false,
                    success: function (result) {
                        for (var i in result) {
                            $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                        }
                        $('#'+ cityId +' option').each(function () {
                            if ($(this).val() == city) {
                                $(this).attr("selected", true)
                            }

                        })
                    }
                });
					$.ajax({
						type: "get",
						url: ctx + "province/city?method=2&code=" + city,
                        async: false,
						success: function (result) {
							for (var i in result) {
								$('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
							}
							$('#'+ areaId +' option').each(function () {
								if ($(this).val() == area) {
									$(this).attr("selected", true)
								}

							})
						}
					});
				}

            $('#'+ provinceId).change(function () {
                $.ajax({
                    type: "get",
                    url: ctx + "province/city?method=1&code=" + $(this).val(),
                    success: function (result) {
                        $('#'+ cityId +' option').remove();
                        $('#'+ areaId +' option').remove();
                        $('#'+ cityId).append("<option value=''>-- 请选择 --</option>");
                        $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");
                        for (var i in result) {
                            $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                        }

                    }
                });
            });

            $('#'+ cityId).change(function () {
                $.ajax({
                    type: "get",
                    url: ctx + "province/city?method=2&code=" + $(this).val(),
                    success: function (result) {
                        $('#'+ areaId +' option').remove();
                        $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");
                        for (var i in result) {
                            $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                        }
                        $('#'+ areaId).selectpicker('refresh');
                    }
                });
            });
        }

</script>
</body>
</html>