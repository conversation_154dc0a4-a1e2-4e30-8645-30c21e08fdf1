<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价')"/>
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .flex{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .cur{
        cursor: pointer;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-5 col-sm-5">
                        <div class="form-group">
                            <div class="col-sm-4">
                                <select name="deliProvinceId" id="deliProvinceId" class="form-control valid"
                                        aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4">
                                <select name="deliCityId" id="deliCityId" class="form-control valid"
                                        aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4">
                                <select name="deliAreaId" id="deliAreaId" class="form-control valid"
                                        aria-invalid="false"></select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-2" onclick="changeDiv()">
                        <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                    </div>
                    <div class="col-md-5 col-sm-5">
                        <div class="form-group">
                            <div class="col-sm-4">
                                <select name="arriProvinceId" id="arriProvinceId"
                                        class="form-control valid"></select>
                            </div>
                            <div class="col-sm-4">
                                <select name="arriCityId" id="arriCityId" class="form-control valid"></select>
                            </div>

                            <div class="col-sm-4">
                                <select name="arriAreaId" id="arriAreaId" class="form-control valid"></select>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-sm-2">
<!--                        <div class="form-group">-->
                            <!--                            <label class="col-sm-2">要求提货日期：</label>-->
                            <div class="">
                                <input type="text" placeholder="调度开始日" style="width: 45%; float: left;" class="form-control"
                                       id="regDateStart"  name="regDateStart" autocomplete="off" th:value="${regDateStart}">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" placeholder="调度截止日" style="width: 45%; float: left;" class="form-control"
                                       id="regDateEnd"  name="regDateEnd" autocomplete="off" th:value="${regDateEnd}">
                            </div>
<!--                        </div>-->
                    </div>
                    <div class="col-sm-1">
<!--                        <div class="form-group">-->
                            <div class="">
                                <input name="lot" class="form-control" type="text" placeholder="运单号"
                                       maxlength="30" required="" aria-required="true">
                            </div>
<!--                        </div>-->
                    </div>
                    <div class="col-sm-1">
<!--                        <div class="form-group">-->
                            <div class="">
                                <input name="dispatchUser" class="form-control" type="text" placeholder="调度人"
                                       maxlength="30" required="" aria-required="true">
                            </div>
<!--                        </div>-->
                    </div>

                    <div class="col-sm-1">
                        <select name="carLen" id="carLen" class="form-control noselect2 selectpicker"
                                aria-invalid="false" data-none-selected-text="车长" multiple th:with="type=${@dict.getType('car_len')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                    <div class="col-sm-1">
                        <select name="carType" id="carType" class="form-control noselect2 selectpicker"
                                aria-invalid="false" data-none-selected-text="车型" multiple th:with="type=${@dict.getType('car_type')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                    <div class="col-sm-1">
                        <select name="balaType" id="balaType" class="form-control" >
                            <option value="">-- 结算方式 --</option>
                            <option value="1">单笔</option>
                            <option value="2">月结</option>
                        </select>
                    </div>
                    <div class="col-sm-1">
                        <select name="disDeptId" id="disDeptId" class="form-control" >
                            <option value="">-- 调度部 --</option>
                            <option th:each="dict : ${disDepts}" th:text="${dict.deptName}" th:value="${dict.deptId}"></option>
                        </select>
                    </div>
                    <div class="col-md-2 col-sm-2">
                        <div class="form-group" style="text-align: center;">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>

                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
<!--            <a class="btn btn-primary multiple disabled" onclick="getGuidePrice()" shiro:hasPermission="tms:reference_price:fix">-->
<!--                <i class="fa fa-dollar"></i> 获取最新指导价-->
<!--            </a>-->
            <a class="btn btn-primary multiple " onclick="fixGuidePrice()" shiro:hasPermission="tms:reference_price:fix">
                <i class="fa fa-dollar"></i> 指导价处理
            </a>
            <a class="btn btn-primary" onclick="importAllData()" shiro:hasPermission="tms:reference_price:fix">
                <i class="fa fa-upload"></i> 指导价导入修复
            </a>

        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap table-bordered " id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<th:block th:include="include :: bootstrap-select-js" />

<script id="fixHtml" type="text/template">
    <div class="form-content">
        <div class="row" >
            <div class="col-md-12 col-sm-12">
                <div class="form-group" style="margin-bottom: 45px;">
                    <label class="col-sm-3">
                        修复类型：
                    </label>
                    <div class="col-sm-9">
                        <label class="radio-inline">
                            <input type="radio" name="fixType" value="0" checked> 加入特殊指导价
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="fixType" value="1"> 忽略指导价统计
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="row" >
            <div class="col-md-12 col-sm-12">
                <div class="form-group">
                    <label class="col-sm-3">
                        备注：
                    </label>
                    <div class="col-sm-9">
                        <textarea id="memo" name="memo" class="form-control" type="text" placeholder="备注"
                                  maxlength="250" required="" aria-required="true"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>

<script th:inline="javascript">
    var prefix = ctx + "tms/reference_price";

    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre()
            }
        });

        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        var options = {
            url: prefix + "/fix/list",
            showToggle:false,
            showColumns:false,
            clickToSelect:true,
            modalName: "指导价修复",
            height: 560,
            uniqueId: "entrustLotId",
            showExport: true,
            exportTypes:['excel','csv'],

            onEditableSave: function (field, row, oldValue, $el){
                // let data = {};
                // if(field=="priceBasic"){
                //     data = {referencePriceId: row.id, price: row.priceBasic,priceType:0}
                // }else{
                //     data = {referencePriceId: row.id, price: row.priceDangerousGoods,priceType:1}
                // }
                //
                //
                // $.ajax({
                //     url: prefix + "/edit_price",
                //     type: "post",
                //     dataType: "json",
                //     data: data,
                //     success: function (result) {
                //         if (result.code === 0) {
                //             var data = result.data;
                //             //刷新
                //             $.btTable.bootstrapTable('refresh', {
                //                 silent: true
                //             });
                //         } else {
                //             $.modal.msgError(result.msg);
                //             //刷新
                //             $.btTable.bootstrapTable('refresh', {
                //                 silent: true
                //             });
                //         }
                //     }
                // });

            },
            columns: [
                {
                    checkbox: true
                },
                {
                    title: '运单号',
                    align: 'left',
                    field : 'lot',
                },
                {
                    title: '客户',
                    align: 'left',
                    field : 'custAbbr',
                },
                {
                    title: '提货日期',
                    align: 'left',
                    field : 'reqDeliDate',
                },
                {
                    title: '提货省市区',
                    align: 'left',
                    field : 'deliaddr',
                },
                {
                    title: '到货省市区',
                    align: 'left',
                    field : 'arriaddr',
                },
                {
                    title: '货品',
                    align: 'left',
                    field : 'goodsName',
                    formatter: function(value, row, index) {
                        let data=[];
                        if (row.goodsName) {
                            data.push(row.goodsName);
                        }
                        if(row.numCount){
                            data.push(row.numCount + '件');
                        }
                        if(row.weightCount){
                            data.push(row.weightCount + '吨');
                        }
                        if(row.volumeCount){
                            data.push(row.volumeCount + 'm³');
                        }
                        return data.join('/')
                    }
                },
                {
                    title: '车长车型',
                    align: 'left',
                    field : 'reqCarLenName',
                    formatter: function(value, row, index) {
                        return row.carLenName + row.carTypeName
                    }

                },
                {
                    title: '调度人/日期',
                    align: 'left',
                    field : 'dispatchUser',
                    formatter: function(value, row, index) {
                        return row.dispatchUser + '</br>' + row.dispatchDate
                    }

                },
                {
                    title: '现指导价',
                    align: 'right',
                    field : 'guidingPriceOld',
                    formatter: function(value, row, index) {
                        if (value) {
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else {
                            return '-'
                        }
                    }
                },
                {
                    title: '单据指导价',
                    align: 'right',
                    field : 'guidingPrice',
                    formatter: function(value, row, index) {
                        if (value) {
                            let html = ``
                            let v = value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});

                            if (row.guidingPriceOld != value) {
                                html = `<div style="color: red;">${v}</div>`
                            }else {
                                html = `<div>${v}</div>`
                            }
                            return html;
                        }else {
                            return '-'
                        }
                    }
                },
                {
                    title: '实际成交价',
                    align: 'right',
                    field : 'yf',
                    formatter: function(value, row, index) {
                        if (value) {
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else {
                            return '-'
                        }
                    }
                },
                {
                    title: '偏差额',
                    align: 'right',
                    field : 'pcrate',
                    formatter: function(value, row, index) {
                        if (value) {
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else {
                            return '-'
                        }
                    }
                },
                {
                    title: '偏差率',
                    align: 'left',
                    formatter: function(value, row, index) {
                        if (row.guidingPrice) {
                           return parseFloat(row.pcrate/row.guidingPrice * 100).toFixed(2) + '%';
                        }
                    }
                },
                {
                    title: '在途费用',
                    align: 'left',
                    field: 'zt',
                    formatter: function(value, row, index) {
                        if (value) {
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else {
                            return '-'
                        }
                    }
                },
                {
                    title: '油卡金额',
                    align: 'left',
                    field: 'yk',
                    formatter: function(value, row, index) {
                        if (value) {
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})
                        }else {
                            return '-'
                        }
                    }
                },
                {
                    title: '是否开票',
                    align: 'left',
                    field: 'kp',
                },
                {
                    title: 'G7支付金额',
                    align: 'left',
                    field: 'g7Amount',
                    formatter: function(value, row, index) {
                        if (value) {
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else {
                            return '-'
                        }
                    }
                },
                {
                    title: '结算方式',
                    align: 'left',
                    field: 'balaType',
                },
                {
                    title: '承运商',
                    align: 'left',
                    field: 'carrierName',
                },
                {
                    title: '运输方式',
                    align: 'left',
                    field: 'transName',
                },
                {
                    title: '备注',
                    align: 'left',
                    field: 'memo',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value, 20)
                    },
                },
                {
                    title: '调度组',
                    align: 'left',
                    field: 'transLineName',
                },
            ],
            onLoadSuccess: function(data) {
            }
        };

        $.table.init(options);

        $('#bootstrap-table').on('all.bs.table', function () {
            // var data = $('#bootstrap-table').bootstrapTable('getData', true);
            // let fieldList = ["deliName","arriName"];
            // mergeCells(data, 1,  $('#bootstrap-table'), fieldList);
        });

    });

    function searchPre() {
        var data = {};
        data.carLen = $.common.join($('#carLen').selectpicker('val'));
        data.carType = $.common.join($('#carType').selectpicker('val'));

        $.table.search('role-form', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 获取指导价
     */
    // function getGuidePrice() {
    //     var bootstrapTable = $.btTable.bootstrapTable('getSelections');
    //
    //     var ids = $.table.selectColumns($.table._option.uniqueId);
    //     var url = ctx + "tms/segment/getGuidePrice/" + ids;
    //
    //     // $.modal.open("获取指导价", url);
    //     layer.open({
    //         type: 2,
    //         area: ['800px', '400px'],
    //         fix: false,
    //         //不固定
    //         maxmin: true,
    //         shade: 0.3,
    //         title: "获取指导价",
    //         content: url,
    //         btn: ['更新指导价', '关闭'],
    //         shadeClose: true,
    //         yes: function (index, layero) {
    //             var iframeWin = layero.find('iframe')[0];
    //             iframeWin.contentWindow.submitHandler(index, layero);
    //         },
    //         cancel: function(index) {
    //             return true;
    //         }
    //     });
    // }

    function fixGuidePrice() {
        var bootstrapTable = $.btTable.bootstrapTable('getSelections');

        let idList = $.table.selectColumns($.table._option.uniqueId);
        if (idList.length > 20) {
            //最多勾选20条数据
            $.modal.alertWarning("最多勾选20条数据。");
            return;

        }
        var id = idList.join();

        layer.open({
                type: 1,
                area: ['40%', '30%'],
                fix: false,
                maxmin: true,
                shade: 0.3,
                title: "指导价处理",
                content: $("#fixHtml").html(),
                btn: ['保存', '关闭'],
                shadeClose: true,
                success: function (layero, index) {

                },
                yes: function (index, layero){
                    var data = {}

                    data = {
                        fixType: $('input[name="fixType"]:checked').val(),
                        memo: $("#memo").val(),
                        lotIds: id
                    };

                    $.ajax({
                        url: ctx + "tms/reference_price/fix/fix_reference",
                        type: "post",
                        dataType: "json",
                        data: data,
                        success: function (result) {
                            if (result.code === 0) {
                                $.modal.msgSuccess("调整成功");
                                var data = result.data;
                                //刷新
                                $.btTable.bootstrapTable('refresh', {
                                    silent: true
                                });
                            } else {
                                $.modal.msgError(result.msg);
                                //刷新
                                $.btTable.bootstrapTable('refresh', {
                                    silent: true
                                });
                            }
                        }
                    });


                    layer.close(index);
                },
                cancel: function(index) {
                    return true;
                }
            })
    }


    /**
     * 日期插件
     */
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        var regDateStart = laydate.render({
            elem: '#regDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var regDateEnd = laydate.render({
            elem: '#regDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
    });

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()

        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);

        searchPre();
    }



    /**
     * 导入数据
     */
    function importAllData(priceType) {
        layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入数据',
            content: $('#importTpl').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var index = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#file')[0].files[0]);
                $.ajax({
                    url: ctx + "tms/reference_price/fix/fix_import",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code == web_status.WARNING) {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });
    }
</script>

<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a th:href="@{/file/fixReferencePrice.xlsx}" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</body>
</html>