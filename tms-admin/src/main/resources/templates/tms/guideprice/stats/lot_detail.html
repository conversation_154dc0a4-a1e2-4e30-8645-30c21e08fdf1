<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价')"/>
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .table-group-divider tbody tr:first-child {
        border-top: 1px solid #dee2e6;
    }

    .table-group-divider tbody tr:last-child {
        border-bottom: 1px solid #dee2e6;
    }
    .ontooltip {
        border: 1px solid #cdcdcd;
        border-radius: 5px;
        background-color: #FFFFFF;
        color: #000;
        text-align: left;
        margin: 4px 0;
    }

</style>
<body class="gray-bg">
<form id="role-form" class="form-horizontal">
    <div>
        <input type="hidden" name="deliArea" th:value="${deliArea}">
        <input type="hidden" name="arriArea" th:value="${arriArea}">
        <input type="hidden" name="carLen" th:value="${carLen}">
        <input type="hidden" name="carType" th:value="${carType}">
        <input type="hidden" name="transType" th:value="${transType}">
        <input type="hidden" name="month" th:value="${month}">
        <input type="hidden" name="amount" th:value="${amount}">
    </div>
</form>
<div class="col-sm-12 select-table  ">
    <table id="bootstrap-table" class="table table-striped table-responsive table-hover"></table>
</div>

<div class="container-div">
    <div class="row">

    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "tms/reference_price";

    var costTypeOnWay = [[${@dict.getType('cost_type_on_way')}]];
    var costTypeFreight = [[${@dict.getType('cost_type_freight')}]];
    var balatype = [[${@dict.getType('bala_type')}]];

    $(function () {

        var options = {
            url: ctx + "tms/reference_price/avg_stats/lot_detail",
            showToggle:false,
            showColumns:false,
            showSearch:false,
            pagination:false,
            showRefresh:false,
            height: 560,
            detailView: true,
            uniqueId: "entrustLotId",
            onExpandRow: function (index, row, $detail) {
                InitSubTable(index, row, $detail);
            },
            onPostBody:function () {
            },
            columns: [
                {
                    title: '运单号',
                    align: 'left',
                    field: 'lot',
                },
                {
                    title: '客户',
                    align: 'left',
                    field: 'custAbbrList',
                    formatter: function status(value, row, index) {
                        return value.join('<br>');
                    }
                },
                {
                    title: '提到货地址',
                    align: 'left',
                    field: 'deliProvinceName',
                    formatter: function status(value, row, index) {
                        let html = '<div>' +
                            '            <span class="label label-warning pa2">提</span>' +
                            '            <span class="label label-default" title="要求提货日期">'+ row.reqDeliDate +'</span>' +
                            '            <div class="ml5" style="display: inline-block;vertical-align: middle;">' +
                                            row.deliProvinceName+row.deliCityName+row.deliAreaName +
                            '            </div>' +
                            '        </div>'
                        html = html + '<div class="mt5">' +
                            '              <span class="label label-success pa2">到</span>' +
                            '              <span class="label label-default" title="要求到货日期">'+ row.reqArriDate +'</span>' +
                            '              <div class="ml5" style="display: inline-block;vertical-align: middle;">' +
                                              row.arriProvinceName+row.arriCityName+row.arriAreaName +
                            '              </div>' +
                            '          </div>'

                        return html;
                    }
                },
                // {
                //     title: '提货|到货省市区',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         if(row.deliAddr == null || row.deliAddr == ""){
                //             row.deliAddr = '';
                //         }
                //         if(row.arriAddr == null || row.arriAddr.arriAddr == ""){
                //             row.arriAddr = ''
                //         }
                //         if(row.deliAddr == "" && row.arriAddr == ""){
                //             return "";
                //         }else{
                //             return row.deliAddr+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriAddr;
                //         }
                //
                //     }
                // },
                {
                    title: '货量',
                    align: 'left',
                    field: 'goodsName',
                    formatter: function (value, row, index) {
                        let html=[]
                        if(row.numCount != null && row.numCount != ""&& row.numCount != 0){
                            html.push(row.numCount+"件")
                        }
                        if(row.weightCount != null && row.weightCount != ""&& row.weightCount != 0){
                            html.push(row.weightCount+"吨")
                        }
                        if(row.volumeCount != null && row.volumeCount != ""&& row.volumeCount != 0){
                            html.push(row.volumeCount+"方")
                        }
                        return row.goodsName+html.join("|");
                    }
                },
                {
                    title: '承运商/司机',
                    align: 'left',
                    field: 'carrierName',
                    formatter: function status(value, row, index) {
                        let html = '';
                        if (row.driverName) {
                            html = row.driverName + ' / '
                        }else {
                            html = '- / ';
                        }
                        if (row.driverMobile) {
                            html = html + row.driverMobile
                        }

                        return row.carrierName + '<br>' + html;

                    }
                },
                {
                    title: '车牌',
                    align:'left',
                    field:'carNo',
                    formatter: function status(value, row, index) {
                        let html = '';
                        if (row.carLenName) {
                            html = row.carLenName + '米 / '
                        }else {
                            html = '- / ';
                        }
                        if (row.carTypeName) {
                            html = html + row.carTypeName
                        }

                        return row.carNo + '<br>' + html;
                    }
                },

                {
                    title: '运费',
                    align: 'right',
                    field: 'costAmount',
                    formatter: function (value, row, index) {
                        if (value === null) {
                            return ;
                        }
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 20,
                    switchable: false,
                    formatter: function (value, row, index) {
                        if (row.isAvgStats == 1) {
                            return '<button type="button" class="btn btn-xs btn-warning" onclick="neglect(\''+ row.entrustLotId +'\')">忽略</button>'
                        }else {
                            return '已忽略'
                        }
                    }
                }

            ]
        };

        $.table.init(options);
    });

    function InitSubTable(index, row, $detail) {
        var childTable = $detail.html('<div class="table-responsive"><table id="child-table" class="table table-bordered table-striped taCl"></table></div>').find('table');
        $(childTable).bootstrapTable({
            url: ctx + "tms/reference_price/avg_stats/lot_detail/invoice",
            method: 'post',
            sidePagination: "server",
            contentType: "application/x-www-form-urlencoded",
            queryParams:{
                lotId:row.entrustLotId
            },
            columns: [
                {
                    title: '发货单号',
                    field: 'invoiceVbillno',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        var result = '<a href="javascript:void(0)" onclick="detail(\'' + row.entrustId + '\',\''+ row.isFleetData +'\')">'+value+'</a>';

                        var val = ''
                        if(row.ltlType == 0) {
                            val = '提货段'
                        }else if(row.ltlType == 1) {
                            val = '干线段'
                        }else if(row.ltlType == 2) {
                            val = '送货段'
                        }
                        result += ' <br /><span class="label label-successT" data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="零担运段类型">'+val+'</span>';
                        return result
                    }

                },
                {
                    title: '客户',
                    field: 'custAbbr',
                    align: 'left'
                },
                {
                    title: '提货|到货省市区',
                    field: 'deliName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let deli="",arri="";
                        deli=row.deliProName+row.deliCityName+row.deliAreaName+row.deliDetailAddr;
                        arri=row.arriProName+row.arriCityName+row.arriAreaName+row.arriDetailAddr;
                        return '<span class="label label-warning pa2">提</span>'+deli+'<br/><span class="label label-success pa2">到</span>'+arri;
                    }
                },
                {
                    title: '货量',
                    field: 'goodsName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let html=[]
                        if(row.numCount != null && row.numCount != ""&& row.numCount != 0){
                            html.push(row.numCount+"件")
                        }
                        if(row.weightCount != null && row.weightCount != ""&& row.weightCount != 0){
                            html.push(row.weightCount+"吨")
                        }
                        if(row.volumeCount != null && row.volumeCount != ""&& row.volumeCount != 0){
                            html.push(row.volumeCount+"方")
                        }
                        return row.goodsName+html.join("|");
                    }
                },
                {
                    title: '要求车型',
                    field: 'carTypeName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return row.carLenName+value;
                    }

                },
                {
                    title: '应收',
                    field: 'receivableAmount',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return `<span data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`+getDailList(row.receiveDetailList)+`">`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;

                    }
                },
                {
                    title: '应付',
                    field: 'payAmount',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return `<span data-toggle="tooltip" data-container="body" data-placement="top" data-html="true" title="`+getDailList(row.payDetailList)+`">`+value.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</span>`;
                    }
                },
                // {
                //     title: '运费',
                //     field: 'costAmount',
                //     align: 'left',
                //     formatter: function status(value, row, index) {
                //         return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                //     }
                // },
                {
                    title: '回单照片',
                    field: 'receiptFlies',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var html = "<div class='picviewer'>"
                        if(value != null && value != '') {
                            value.forEach(function (element, index) {
                                html += `<img style="height:32px" src="`+element.filePath+`"/>`
                            });
                        }else {
                            html = '-';
                        }
                        html +="</div>"
                        return html;
                    }
                },
                {
                    title: '客服',
                    field: 'serviceName',
                    align: 'left'
                }
            ],
            onLoadSuccess: function(data) {
                $('.picviewer').viewer({
                    url: 'data-original',
                    title: false,
                    navbar:false,
                });

                $('[data-toggle="tooltip"]').tooltip()
            }
        });
    }


    function neglect(lotId) {
        //
        $.modal.confirm("确定忽略该条数据吗？", function () {
            $.ajax({
                url: prefix + "/avg_stats/neglect",
                type: "post",
                dataType: "json",
                data: { "lotId": lotId },
                success: function (result) {
                    if (result.code === 0) {
                        $.modal.msgSuccess(result.msg);
                        window.location.reload();
                    } else {
                        $.modal.msgError(result.msg);

                    }
                }
            });
        });
    }

    function getDailList(list) {
        let html=`<div class='ontooltip'><div class='panel-body tooltipBody'><div class='padt5'>`;

        if(list){
            html+=`<table class='custom-tab tab table'> <thead style='background: #f4f6f7;'><tr>
                    <th>费用类型</th>
                    <th>金额</th>
                    <th>状态</th>
                </tr></thead><tbody> `;
            list.forEach(res=>{
                let vbillstatus="";
                if(res.vbillstatus==0){
                    vbillstatus= `<span class='carve carve-inverse'>新建</span>` ;
                }else if(res.vbillstatus==1){
                    vbillstatus = `<span class='carve carve-primary'>已确认</span>` ;
                }else if(res.vbillstatus==2){
                    vbillstatus = `<span class='carve carve-success'>已对账</span>` ;
                }else if(res.vbillstatus==3){
                    vbillstatus = `<span class='carve carve-coral'>部分核销</span>` ;
                }else if(res.vbillstatus==4){
                    vbillstatus = `<span class='carve carve-warning'>已核销</span>` ;
                }else if(res.vbillstatus==5){
                    vbillstatus = `<span class='carve carve-danger'>关闭</span>` ;
                }else if(res.vbillstatus==6){
                    vbillstatus = `<span class='carve carve-primary'>已申请</span>` ;
                }else if(res.vbillstatus==7){
                    vbillstatus = `<span class='carve carve-warning'>核销中</span>` ;
                }else if(res.vbillstatus==8){
                    vbillstatus = `<span class='carve carve-yellow'>审核中</span>` ;
                }

                let costType="";
                if(res.freeType=='1'){
                    costType = $.table.selectDictLabel(costTypeOnWay, res.costTypeOnWay);
                }else  if(res.freeType=='0'){
                    costType = $.table.selectDictLabel(costTypeFreight, res.costTypeFreight);
                }

                html+=`<tr>
                            <td>`+(costType?costType:'-')+`</td>
                            <td>`+res.transFeeCount.toLocaleString('zh', {style: 'currency', currency: 'CNY'})+`</td>
                            <td>`+vbillstatus+`</td>
                        </tr>`
            })


            html+=`</tbody></table>`;

        }else{
            html+=`暂无数据`;
        }
        html+=`</div></div></div>`;
        return html
    }


</script>

</body>
</html>
