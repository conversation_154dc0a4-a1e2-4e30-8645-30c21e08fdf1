<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价')"/>
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .custom-cell-background {
        background-color: rgb(255 0 0 / 27%) !important; /* 自定义的背景颜色 */
    }
</style>
<body class="gray-bg">
    <div class="col-sm-12 search-collapse">
    <form id="role-form" class="form-horizontal">
        <div class="row">
            <div class="col-md-1 col-sm-1">
                <div class="form-group">
                    <div class="col-sm-12">
                        <select name="reqCarLen" class="form-control custom-select" th:with="type=${@dict.getType('car_len')}">
                            <option value="" disabled selected hidden>车长</option>
                            <option value=""></option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-1 col-sm-1">
                <div class="form-group">
                    <div class="col-sm-12" style="padding-right: 0">
                        <select name="reqCarType" class="form-control custom-select" th:with="type=${@dict.getType('car_type')}">
                            <option value="" disabled selected hidden>车型</option>
                            <option value=""></option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="col-md-4 col-sm-4">
                <div class="form-group">
                    <div class="col-sm-12">
                        <div class="col-sm-4">
                            <select name="deliProvince" id="deliProvince" class="form-control valid"
                                    aria-invalid="false"></select>
                        </div>
                        <div class="col-sm-4">
                            <select name="deliCity" id="deliCity" class="form-control valid"
                                    aria-invalid="false"></select>
                        </div>
                        <div class="col-sm-4">
                            <select name="deliArea" id="deliArea" class="form-control valid"
                                    aria-invalid="false"></select>
                        </div>
                    </div>

                </div>
            </div>

            <div class="col-md-4 col-sm-4">
                <div class="form-group">
                    <div class="col-sm-1" onclick="changeDiv()">
                        <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                    </div>
                    <div class="col-sm-11">
                        <div class="col-sm-4">
                            <select name="arriProvince" id="arriProvince"
                                    class="form-control valid"></select>
                        </div>
                        <div class="col-sm-4">
                            <select name="arriCity" id="arriCity" class="form-control valid"></select>
                        </div>

                        <div class="col-sm-4">
                            <select name="arriArea" id="arriArea" class="form-control valid"></select>
                        </div>
                    </div>


                </div>
            </div>

            <div class="col-md-2 col-sm-2">
                <!--                        <label class="col-sm-4"></label>-->
                <div class="form-group" style="text-align: center;">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                </div>
            </div>




        </div>
    </form>
</div>
    <div class="col-sm-12 select-table  ">
        <table id="bootstrap-table" class="table table-striped table-responsive table-hover">
        </table>
    </div>

<div class="container-div">
    <div class="row">

    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "tms/reference_price";

    $(function () {
        $.provinces.init("deliProvince","deliCity","deliArea","320000","320600","");
        $.provinces.init("arriProvince","arriCity","arriArea","310000","310100","");

        var options = {
            url: prefix + "/avg_stats",
            showToggle:false,
            showColumns:false,
            showSearch:false,
            pagination:false,
            showRefresh:false,
            modalName: "指导价",
            height: 560,
            // onEditableSave: function (field, row, oldValue, $el){
            //     let data = {};
            //     if(field=="priceBasic"){
            //         data = {referencePriceId: row.id, price: row.priceBasic,priceType:0}
            //     }else{
            //         data = {referencePriceId: row.id, price: row.priceDangerousGoods,priceType:1}
            //     }
            //
            //
            //     $.ajax({
            //         url: prefix + "/edit_price",
            //         type: "post",
            //         dataType: "json",
            //         data: data,
            //         success: function (result) {
            //             if (result.code === 0) {
            //                 var data = result.data;
            //                 //刷新
            //                 $.btTable.bootstrapTable('refresh', {
            //                     silent: true
            //                 });
            //             } else {
            //                 $.modal.msgError(result.msg);
            //                 //刷新
            //                 $.btTable.bootstrapTable('refresh', {
            //                     silent: true
            //                 });
            //             }
            //         }
            //     });
            //
            // },
            columns: [
                [
                    {
                        title: '提货地址',
                        align: 'center',
                        valign: 'middle',
                        field : 'deliAreaName',
                        rowspan:2
                    },
                    {
                        title: '到货地址',
                        align: 'center',
                        valign: 'middle',
                        field: 'arriAddr',
                        rowspan:2
                    },
                    {
                        field : 'carLenType',
                        title: '车长车型',
                        valign: 'middle',
                        align: 'center',
                        rowspan:2

                    },
                    {
                        title: [[${month1}]],
                        align: 'left',
                        colspan:4
                    },
                    {
                        title: [[${month2}]],
                        align: 'left',
                        colspan:4
                    },
                    {
                        title: [[${month3}]],
                        align: 'left',
                        colspan:4
                    },
                    {
                        title: '平均指导价',
                        align: 'center',
                        valign: 'middle',
                        rowspan:2,
                        formatter: function (value, row, index) {
                            let ct = 0
                            row.avgAmount1 == 0 ? null : ct ++
                            row.avgAmount2 == 0 ? null : ct ++
                            row.avgAmount3 == 0 ? null : ct ++

                            let avgAmount = 0
                            if (ct != 0 ) {
                                avgAmount = parseFloat((row.avgAmount1 + row.avgAmount2 + row.avgAmount3) / ct).toFixed(2);
                            }

                            return avgAmount == 0 ? '-' : avgAmount;
                        }
                    },
                    {
                        field: 'priceBasic',
                        title: '指导价',
                        align: 'center',
                        valign: 'middle',
                        rowspan:2,
                        cellStyle: function(value, row, index, field) {
                            let ct = 0
                            row.avgAmount1 == 0 ? null : ct ++
                            row.avgAmount2 == 0 ? null : ct ++
                            row.avgAmount3 == 0 ? null : ct ++

                            let avgAmount = 0
                            if (ct != 0 ) {
                                avgAmount = parseFloat((row.avgAmount1 + row.avgAmount2 + row.avgAmount3) / ct).toFixed(2);
                            }

                            if (value && avgAmount != 0) {
                                if (value > avgAmount) {
                                    return {
                                        classes: 'custom-cell-background'
                                    };
                                }
                            }
                            return {}
                        },

                    },
                ],
                [
                    {
                        title: '次数',
                        align: 'left',
                        field: 'cnt1',
                    },
                    {
                        title: '最高',
                        align: 'left',
                        field: 'maxAmount1',
                        cellStyle: function(value, row, index, field) {
                            if (value && row.avgAmount1) {
                                if (value > row.avgAmount1 * 1.5 || value < row.avgAmount1 * 0.5) {
                                    return {
                                        classes: 'custom-cell-background'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            if (value) {
                                if (value > row.avgAmount1 * 1.5 || value < row.avgAmount1 * 0.5) {
                                    return `<div><a href="#" style="text-decoration: underline;" ` +
                                        `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.reqCarLen +`','`+ row.reqCarType +`','`+value+`','`+row.month1+`')">` + value + `</a></div>`;
                                }else {
                                    return value
                                }
                            }else {
                                return '<div>-</div>';
                            }
                        }
                    },
                    {
                        title: '最低',
                        align: 'left',
                        field: 'minAmount1',
                        cellStyle: function(value, row, index, field) {
                            if (value && row.avgAmount1) {
                                if (value > row.avgAmount1 * 1.5 || value < row.avgAmount1 * 0.5) {
                                    return {
                                        classes: 'custom-cell-background'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            if (value) {
                                if (value > row.avgAmount1 * 1.5 || value < row.avgAmount1 * 0.5) {
                                    return `<div><a href="#" style="text-decoration: underline;" ` +
                                        `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.reqCarLen +`','`+ row.reqCarType +`','`+value+`','`+row.month1+`')">` + value + `</a></div>`;
                                }else {
                                    return value
                                }
                            }else {
                                return '<div>-</div>';
                            }
                        }


                    },
                    {
                        title: '平均',
                        align: 'left',
                        field: 'avgAmount1',
                    },
                    {
                        title: '次数',
                        align: 'left',
                        field: 'cnt2',
                    },
                    {
                        title: '最高',
                        align: 'left',
                        field: 'maxAmount2',
                        cellStyle: function(value, row, index, field) {
                            if (value && row.avgAmount2) {
                                if (value > row.avgAmount2 * 1.5 || value < row.avgAmount2 * 0.5) {
                                    return {
                                        classes: 'custom-cell-background'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            if (value) {
                                if (value > row.avgAmount2 * 1.5 || value < row.avgAmount2 * 0.5) {
                                    return `<div><a href="#" style="text-decoration: underline;" ` +
                                        `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.reqCarLen +`','`+ row.reqCarType +`','`+value+`','`+row.month2+`')">` + value + `</a></div>`;
                                }else {
                                    return value
                                }
                            }else {
                                return '<div>-</div>';
                            }
                        }


                    },
                    {
                        title: '最低',
                        align: 'left',
                        field: 'minAmount2',
                        cellStyle: function(value, row, index, field) {
                            if (value && row.avgAmount2) {
                                if (value > row.avgAmount2 * 1.5 || value < row.avgAmount2 * 0.5) {
                                    return {
                                        classes: 'custom-cell-background'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            if (value) {
                                if (value > row.avgAmount2 * 1.5 || value < row.avgAmount2 * 0.5) {
                                    return `<div><a href="#" style="text-decoration: underline;" ` +
                                        `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.reqCarLen +`','`+ row.reqCarType +`','`+value+`','`+row.month2+`')">` + value + `</a></div>`;
                                }else {
                                    return value
                                }
                            }else {
                                return '<div>-</div>';
                            }
                        }

                    },
                    {
                        title: '平均',
                        align: 'left',
                        field: 'avgAmount2',
                    },
                    {
                        title: '次数',
                        align: 'left',
                        field: 'cnt3',
                    },
                    {
                        title: '最高',
                        align: 'left',
                        field: 'maxAmount3',
                        cellStyle: function(value, row, index, field) {
                            if (value && row.avgAmount3) {
                                if (value > row.avgAmount3 * 1.5 || value < row.avgAmount3 * 0.5) {
                                    return {
                                        classes: 'custom-cell-background'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            if (value) {
                                if (value > row.avgAmount3 * 1.5 || value < row.avgAmount3 * 0.5) {
                                    return `<div><a href="#" style="text-decoration: underline;" ` +
                                        `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.reqCarLen +`','`+ row.reqCarType +`','`+value+`','`+row.month3+`')">` + value + `</a></div>`;
                                }else {
                                    return value
                                }
                            }else {
                                return '<div>-</div>';
                            }
                        }
                    },
                    {
                        title: '最低',
                        align: 'left',
                        field: 'minAmount3',
                        cellStyle: function(value, row, index, field) {
                            if (value && row.avgAmount3) {
                                if (value > row.avgAmount3 * 1.5 || value < row.avgAmount3 * 0.5) {
                                    return {
                                        classes: 'custom-cell-background'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            if (value) {
                                if (value > row.avgAmount3 * 1.5 || value < row.avgAmount3 * 0.5) {
                                    return `<div><a href="#" style="text-decoration: underline;" ` +
                                        `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.reqCarLen +`','`+ row.reqCarType +`','`+value+`','`+row.month3+`')">` + value + `</a></div>`;
                                }else {
                                    return value
                                }
                            }else {
                                return '<div>-</div>';
                            }
                        }
                    },
                    {
                        title: '平均',
                        align: 'left',
                        field: 'avgAmount3',
                    },
                ]
            ],
            onLoadSuccess: function(data) {
                let fieldList = ["deliAreaName"];
                mergeCells(data.rows, 1,  $('#bootstrap-table'), fieldList);

            }
        };

        $.table.init(options);
    });

    /**
     * 合并单元格
     *
     */
    function mergeCells(data, colspan, target, sameFiled) {
        sameFiled.forEach(res=>{
            for (var i = 0; i < data.length; i++) {
                data[i][res+'_rows'] = 1;
                for (let j = i + 1; j < data.length; j++) {
                    if (res == 'deliName') {
                        if (data[i][res] == data[j][res]) {
                            data[i][res+'_rows'] = (data[i][res+'_rows'] || 1) + 1;
                            data[i]['f'] = i;
                            data[j]['f'] = i;
                        } else {
                            break;
                        }
                    } else {
                        if (data[i][res] == data[j][res] && data[i]['f'] == data[j]['f']) {
                            data[i][res+'_rows'] = (data[i][res+'_rows'] || 1) + 1;
                        } else {
                            break;
                        }
                    }
                }
                i = i+ data[i][res+'_rows'] - 1;
            }
            for (var i = 0; i < data.length; i++) {
                if (data[i][res+ "_rows"] > 1) {
                    target.bootstrapTable('mergeCells', {index: i, field: res, colspan: 1, rowspan: data[i][res+ "_rows"]});
                }
            }
        })
    }


    /**
     * 搜索的方法
     */
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }

    function openEntrust(deliArea, arriArea, reqCarLen, reqCarType, amount, month) {
        layer.open({
            type: 2,
            area: ['95%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "历史运单",
            content: ctx + "tms/reference_price/avg_stats/lot_detail?deliArea=" + deliArea+"&arriArea=" + arriArea + "&reqCarLen="
                + reqCarLen + "&reqCarType=" + reqCarType + "&amount=" + amount + "&month=" + month,
            btn: ['关闭'],
            shadeClose: true,            // 弹层外区域关闭
            cancel: function (index) {
                return true;
            }
        });
    }


</script>

</body>
</html>