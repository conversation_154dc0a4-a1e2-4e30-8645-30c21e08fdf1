<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/css/bootstrap-editable.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.css}" rel="stylesheet"/>

</head>
<style>
    .custom-cell-background-red {
        background-color: rgb(255 0 0 / 43%) !important; /* 自定义的背景颜色 */
    }
    .custom-cell-background-red a {
        color: #FFFFFF;
    }

    .custom-cell-background-green {
        background-color: #8DBF8B !important; /* 自定义的背景颜色 */
    }
    .custom-cell-background-green a {
        color: #FFFFFF;
    }

    .lf{
        margin-left: 10px;
        background-color: #ffffff;
        border-radius: 3px;
        cursor:pointer;
    }

    .label-success{
        color: #1ab394;
        background-color: transparent;
        border: 1px solid #1ab394;
    }
    .label-primary{
        color: #1c84c6;
        background-color: transparent;
        border: 1px solid #1c84c6;
    }
    .label-warning{
        color: #f8ac59;
        background-color: transparent;
        border: 1px solid #f8ac59;
    }
    .sticky-header-container {
        pointer-events: none;
    }

</style>
<body class="gray-bg">
    <div class="col-sm-12 search-collapse">
    <form id="role-form" class="form-horizontal">
        <div class="row no-gutter">
            <div class="col-md-3 col-sm-3">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <select id="carLenS" class="form-control valid noselect2 selectpicker"  data-none-selected-text="车长"
                                onchange="changeCarLen()" multiple th:with="type=${@dict.getType('car_len')}">
                            <option value=""></option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                        <input name="carLen" id="carLen" type="hidden">
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-3">
                <div class="form-group flex">
                    <div class="col-sm-12">
                        <select id="carTypeS" class="form-control valid noselect2 selectpicker" data-none-selected-text="要求车型"
                                onchange="changeCarType()" multiple th:with="type=${@dict.getType('car_type')}">
                            <option value=""></option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                        <input name="carType" id="carType" type="hidden">

                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-3">
                <div class="form-group">
                    <div class="col-sm-12">
                        <input id="month" type="text" class=" form-control"
                               placeholder="起始月份 - 结束月份" autocomplete="off">
                        <input name="month1" id="month1" type="hidden" th:value="${month1}">
                        <input name="month2" id="month2" type="hidden" th:value="${month2}">
                        <input name="month3" id="month3" type="hidden" th:value="${month3}">
                    </div>
                </div>
            </div>

        </div>
        <div class="row no-gutter">
            <div class="col-md-5 col-sm-5">
                <div class="form-group">
                    <div class="col-sm-12">
                        <div class="col-sm-4">
                            <select name="deliProvince" id="deliProvince" class="form-control valid"
                                    aria-invalid="false"></select>
                        </div>
                        <div class="col-sm-4">
                            <select name="deliCity" id="deliCity" class="form-control valid"
                                    aria-invalid="false"></select>
                        </div>
                        <div class="col-sm-4">
                            <select id="deliArea" class="form-control valid noselect2 selectpicker"
                                    onchange="changeDeliArea()" data-none-selected-text="请选择区" multiple
                                    aria-invalid="false"></select>
                            <input name="deliArea" type="hidden">

                        </div>
                    </div>

                </div>
            </div>

            <div class="col-md-5 col-sm-5">
                <div class="form-group">
                    <div class="col-sm-1" onclick="changeDiv()">
                        <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                    </div>
                    <div class="col-sm-11">
                        <div class="col-sm-4">
                            <select name="arriProvince" id="arriProvince"
                                    class="form-control valid"></select>
                        </div>
                        <div class="col-sm-4">
                            <select name="arriCity" id="arriCity" class="form-control valid"></select>
                        </div>

                        <div class="col-sm-4">
                            <select id="arriArea" class="form-control noselect2 selectpicker"
                                    onchange="changeArriArea()" data-none-selected-text="请选择区" multiple></select>
                            <input name="arriArea" type="hidden">

                        </div>
                    </div>


                </div>
            </div>

            <div class="col-md-2 col-sm-2">
                <!--                        <label class="col-sm-4"></label>-->
                <div class="form-group" style="text-align: center;">
                    <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                </div>
            </div>




        </div>
    </form>
</div>
    <div class="col-sm-12 select-table  ">
        <table id="bootstrap-table" class="table table-striped table-responsive table-hover">
        </table>
    </div>

<div class="container-div">
    <div class="row">

    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/bootstrap-table.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/bootstrap3-editable/js/bootstrap-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/editable/bootstrap-table-editable.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/extensions/sticky-header/bootstrap-table-sticky-header.min.js}"></script>
<script th:src="@{/ajax/libs/bootstrap-table-1.22.1/locale/bootstrap-table-zh-CN.min.js}"></script>

<script th:inline="javascript">
    var prefix = ctx + "tms/reference_price";

    $(function () {
        init("deliProvince","deliCity","deliArea","320000","320600","");
        init("arriProvince","arriCity","arriArea","310000","310100","");

        let options = initOptions([[${month1}]],[[${month2}]],[[${month3}]]);
        $.table.init(options);
    });

    function initOptions(month1,month2,month3) {
        return {
            url: prefix + "/avg_stats",
            showToggle:false,
            showColumns:false,
            showSearch:false,
            pagination:false,
            showRefresh:false,
            modalName: "指导价",
            height: 560,
            stickyHeader: true,  // 启用固定表头功能
            stickyHeaderOffsetY: 0,  // 可选，调整固定表头距离顶部的距离

            onEditableSave: function (field, row, oldValue, $el){
                let data = {};
                if(field=="priceBasic"){
                    data = {referencePriceId: row.referencePriceId, price: row.priceBasic,priceType:0}
                }else{
                    data = {referencePriceId: row.referencePriceId, price: row.priceDangerousGoods,priceType:1}
                }

                $.ajax({
                    url: ctx + "tms/reference_price/edit_price",
                    type: "post",
                    dataType: "json",
                    data: data,
                    success: function (result) {
                        if (result.code === 0) {
                            var data = result.data;
                            //刷新
                            $.btTable.bootstrapTable('refresh', {
                                silent: true
                            });
                        } else {
                            $.modal.msgError(result.msg);
                            //刷新
                            $.btTable.bootstrapTable('refresh', {
                                silent: true
                            });
                        }
                    }
                });

            },
            columns: [
                [
                    {
                        title: '提货地址',
                        align: 'center',
                        valign: 'middle',
                        field : 'deliAreaName',
                        rowspan:2
                    },
                    {
                        title: '到货地址',
                        align: 'center',
                        valign: 'middle',
                        field: 'arriAddr',
                        rowspan:2
                    },
                    {
                        field : 'carLenName',
                        title: '车长车型',
                        valign: 'middle',
                        align: 'center',
                        rowspan:2,
                        formatter: function (value, row, index) {
                            let html = '';
                            if (value) {
                                html = value + '米'
                            }
                            if (row.carTypeName) {
                                html = html + row.carTypeName
                            }
                            html = '<span>' + html + '</span>'

                            // if (row.transType == '0') {
                            //     html = html + '<span class="label lf label-success" style="cursor: default;">公路</span>'
                            // }
                            // if (row.transType == '4') {
                            //     html = html + '<span class="label lf label-primary" style="cursor: default;">冷链</span>'
                            // }
                            // if (row.transType == '15') {
                            //     html = html + '<span class="label lf label-warning" style="cursor: default;">危化</span>'
                            // }

                            if (row.transType == '0') {
                                html = html + '<img src="/img/vehicle.png" title="公路整车" style="width: 15px;height: 15px;margin-left: 10px">'
                            }
                            if (row.transType == '4') {
                                html = html + '<img src="/img/cold_chain.png" title="冷链整车" style="width: 15px;height: 15px;margin-left: 10px">'
                            }
                            if (row.transType == '15') {
                                html = html + '<img src="/img/danger.jpg" title="危化整车" style="width: 15px;height: 15px;margin-left: 10px">'
                            }

                            return html;
                        }

                    },
                    {
                        title: month1,
                        align: 'left',
                        colspan:4
                    },
                    {
                        title: month2,
                        align: 'left',
                        colspan:4
                    },
                    {
                        title: month3,
                        align: 'left',
                        colspan:4
                    },
                    {
                        title: '平均指导价',
                        align: 'center',
                        valign: 'middle',
                        rowspan:2,
                        formatter: function (value, row, index) {
                            let ct = 0
                            row.avgAmount1 == 0 ? null : ct ++
                            row.avgAmount2 == 0 ? null : ct ++
                            row.avgAmount3 == 0 ? null : ct ++

                            let avgAmount = 0
                            if (ct != 0 ) {
                                avgAmount = parseFloat((row.avgAmount1 + row.avgAmount2 + row.avgAmount3) / ct).toFixed(2);
                            }

                            return avgAmount == 0 ? '-' : avgAmount;
                        }
                    },
                    {
                        field: 'priceBasic',
                        title: '指导价',
                        align: 'center',
                        valign: 'middle',
                        rowspan:2,
                        cellStyle: function(value, row, index, field) {
                            let price = 0
                            if (row.transType == '0') {
                                price = row.priceBasic
                            }
                            if (row.transType == '4') {
                                price = row.priceColdChain
                            }
                            if (row.transType == '15') {
                                price = row.priceDangerousGoods
                            }

                            let ct = 0
                            row.avgAmount1 == 0 ? null : ct ++
                            row.avgAmount2 == 0 ? null : ct ++
                            row.avgAmount3 == 0 ? null : ct ++

                            let avgAmount = 0
                            if (ct != 0 ) {
                                avgAmount = parseFloat((row.avgAmount1 + row.avgAmount2 + row.avgAmount3) / ct).toFixed(2);
                            }

                            if (price && avgAmount != 0) {
                                if (price > avgAmount) {
                                    return {
                                        classes: 'custom-cell-background-red'
                                    };
                                }else if (price < avgAmount){
                                    return {
                                        classes: 'custom-cell-background-green'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            let price = ''
                            if (row.transType == '0') {
                                price = row.priceBasic
                            }
                            if (row.transType == '4') {
                                price = row.priceColdChain
                            }
                            if (row.transType == '15') {
                                price = row.priceDangerousGoods
                            }
                            return price
                        },
                        editable:{
                            type: 'text',
                            title: '修改价格',
                            emptytext: '',
                            validate:  function (v) {
                                if (!checkNumber(v)) {
                                    return "请输入数字！";
                                }
                            }
                        }

                    },
                ],
                [
                    {
                        title: '次数',
                        align: 'left',
                        field: 'cnt1',
                        formatter: function (value, row, index) {
                            if (value != null && value != 0) {
                                return `<div><a href="#" style="text-decoration: underline;" ` +
                                    `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.carLen +`','`
                                    + row.carType +`',null,'`+row.month1+`','`+row.transType+`')">` + value + `</a></div>`;
                            }else {
                                return '-'
                            }
                        }
                    },
                    {
                        title: '最高',
                        align: 'left',
                        field: 'maxAmount1',
                        cellStyle: function(value, row, index, field) {
                            if (value && row.avgAmount1) {
                                if (value > row.avgAmount1 * 1.5 || value < row.avgAmount1 * 0.5) {
                                    return {
                                        classes: 'custom-cell-background-red'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            if (value) {
                                if (value > row.avgAmount1 * 1.5 || value < row.avgAmount1 * 0.5) {
                                    return `<div><a href="#" style="text-decoration: underline;" ` +
                                        `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.carLen +`','`
                                        + row.carType +`','`+value+`','`+row.month1+`','`+row.transType+`')">` + value + `</a></div>`;
                                }else {
                                    return value
                                }
                            }else {
                                return '<div>-</div>';
                            }
                        }
                    },
                    {
                        title: '最低',
                        align: 'left',
                        field: 'minAmount1',
                        cellStyle: function(value, row, index, field) {
                            if (value && row.avgAmount1) {
                                if (value > row.avgAmount1 * 1.5 || value < row.avgAmount1 * 0.5) {
                                    return {
                                        classes: 'custom-cell-background-red'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            if (value) {
                                if (value > row.avgAmount1 * 1.5 || value < row.avgAmount1 * 0.5) {
                                    return `<div><a href="#" style="text-decoration: underline;" ` +
                                        `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.carLen +`','`
                                        + row.carType +`','`+value+`','`+row.month1+`','`+row.transType+`')">` + value + `</a></div>`;
                                }else {
                                    return value
                                }
                            }else {
                                return '<div>-</div>';
                            }
                        }


                    },
                    {
                        title: '平均',
                        align: 'left',
                        field: 'avgAmount1',
                    },
                    {
                        title: '次数',
                        align: 'left',
                        field: 'cnt2',
                        formatter: function (value, row, index) {
                            if (value != null && value != 0) {
                                return `<div><a href="#" style="text-decoration: underline;" ` +
                                    `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.carLen +`','`
                                    + row.carType +`',null,'`+row.month2+`','`+row.transType+`')">` + value + `</a></div>`;
                            }else {
                                return '-'
                            }
                        }

                    },
                    {
                        title: '最高',
                        align: 'left',
                        field: 'maxAmount2',
                        cellStyle: function(value, row, index, field) {
                            if (value && row.avgAmount2) {
                                if (value > row.avgAmount2 * 1.5 || value < row.avgAmount2 * 0.5) {
                                    return {
                                        classes: 'custom-cell-background-red'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            if (value) {
                                if (value > row.avgAmount2 * 1.5 || value < row.avgAmount2 * 0.5) {
                                    return `<div><a href="#" style="text-decoration: underline;" ` +
                                        `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.carLen +`','`
                                        + row.carType +`','`+value+`','`+row.month2+`','`+row.transType+`')">` + value + `</a></div>`;
                                }else {
                                    return value
                                }
                            }else {
                                return '<div>-</div>';
                            }
                        }


                    },
                    {
                        title: '最低',
                        align: 'left',
                        field: 'minAmount2',
                        cellStyle: function(value, row, index, field) {
                            if (value && row.avgAmount2) {
                                if (value > row.avgAmount2 * 1.5 || value < row.avgAmount2 * 0.5) {
                                    return {
                                        classes: 'custom-cell-background-red'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            if (value) {
                                if (value > row.avgAmount2 * 1.5 || value < row.avgAmount2 * 0.5) {
                                    return `<div><a href="#" style="text-decoration: underline;" ` +
                                        `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.carLen +`','`
                                        + row.carType +`','`+value+`','`+row.month2+`','`+row.transType+`')">` + value + `</a></div>`;
                                }else {
                                    return value
                                }
                            }else {
                                return '<div>-</div>';
                            }
                        }

                    },
                    {
                        title: '平均',
                        align: 'left',
                        field: 'avgAmount2',
                    },
                    {
                        title: '次数',
                        align: 'left',
                        field: 'cnt3',
                        formatter: function (value, row, index) {
                            if (value != null && value != 0) {
                                return `<div><a href="#" style="text-decoration: underline;" ` +
                                    `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.carLen +`','`
                                    + row.carType +`',null,'`+row.month3+`','`+ row.transType +`')">` + value + `</a></div>`;
                            }else {
                                return '-'
                            }
                        }

                    },
                    {
                        title: '最高',
                        align: 'left',
                        field: 'maxAmount3',
                        cellStyle: function(value, row, index, field) {
                            if (value && row.avgAmount3) {
                                if (value > row.avgAmount3 * 1.5 || value < row.avgAmount3 * 0.5) {
                                    return {
                                        classes: 'custom-cell-background-red'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            if (value) {
                                if (value > row.avgAmount3 * 1.5 || value < row.avgAmount3 * 0.5) {
                                    return `<div><a href="#" style="text-decoration: underline;" ` +
                                        `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.carLen +`','`
                                        + row.carType +`','`+value+`','`+row.month3+`','`+ row.transType + `')">` + value + `</a></div>`;
                                }else {
                                    return value
                                }
                            }else {
                                return '<div>-</div>';
                            }
                        }
                    },
                    {
                        title: '最低',
                        align: 'left',
                        field: 'minAmount3',
                        cellStyle: function(value, row, index, field) {
                            if (value && row.avgAmount3) {
                                if (value > row.avgAmount3 * 1.5 || value < row.avgAmount3 * 0.5) {
                                    return {
                                        classes: 'custom-cell-background-red'
                                    };
                                }
                            }
                            return {}
                        },
                        formatter: function (value, row, index) {
                            if (value) {
                                if (value > row.avgAmount3 * 1.5 || value < row.avgAmount3 * 0.5) {
                                    return `<div><a href="#" style="text-decoration: underline;" ` +
                                        `onclick="openEntrust('`+ row.deliArea +`','`+ row.arriArea +`','`+ row.carLen +`','`
                                        + row.carType +`','`+value+`','`+row.month3+`','`+ row.transType +`')">` + value + `</a></div>`;
                                }else {
                                    return value
                                }
                            }else {
                                return '<div>-</div>';
                            }
                        }
                    },
                    {
                        title: '平均',
                        align: 'left',
                        field: 'avgAmount3',
                    },
                ]
            ],
            onLoadSuccess: function(data) {
                let fieldList = ["deliAreaName"];
                mergeCells(data.rows, 1,  $('#bootstrap-table'), fieldList);

            }
        };
    }

    /**
     * 合并单元格
     *
     */
    function mergeCells(data, colspan, target, sameFiled) {
        sameFiled.forEach(res=>{
            for (var i = 0; i < data.length; i++) {
                data[i][res+'_rows'] = 1;
                for (let j = i + 1; j < data.length; j++) {
                    if (res == 'deliName') {
                        if (data[i][res] == data[j][res]) {
                            data[i][res+'_rows'] = (data[i][res+'_rows'] || 1) + 1;
                            data[i]['f'] = i;
                            data[j]['f'] = i;
                        } else {
                            break;
                        }
                    } else {
                        if (data[i][res] == data[j][res] && data[i]['f'] == data[j]['f']) {
                            data[i][res+'_rows'] = (data[i][res+'_rows'] || 1) + 1;
                        } else {
                            break;
                        }
                    }
                }
                i = i+ data[i][res+'_rows'] - 1;
            }
            for (var i = 0; i < data.length; i++) {
                if (data[i][res+ "_rows"] > 1) {
                    target.bootstrapTable('mergeCells', {index: i, field: res, colspan: 1, rowspan: data[i][res+ "_rows"]});
                }
            }
        })
    }

    /**
     * 搜索的方法
     */
    function searchPre() {
        let options = initOptions($("#month1").val(),$("#month2").val(),$("#month3").val());

        $.table.destroy()
        $.table.init(options);

        // var data = {};
        // $.table.search('role-form', data);
    }

    let flg = 1
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        //要求提货日期
        let myDate = laydate.render({
            elem: "#month",
            type: "month",
            id: "month",
            range: true,
            rangeLinked: true,
            value: [[${month1}]] + " - " + [[${month3}]],
            change: function(value, date, endDate){
                let sDate = new Date(date.year + '-' + date.month);
                let eDate = new Date(endDate.year + '-' + endDate.month);

                // 获取开始日期的年份和月份
                let startYear = sDate.getFullYear();
                let startMonth = sDate.getMonth();

                // 获取结束日期的年份和月份
                let endYear = eDate.getFullYear();
                let endMonth = eDate.getMonth();

                // 计算日期差距的月份数
                let monthsDiff = (endYear - startYear) * 12 + (endMonth - startMonth) + 1;

                // 判断是否跨度三个月
                if (monthsDiff !== 3) {
                    flg = 0;
                    $(".laydate-btns-confirm").addClass("laydate-disabled");
                    // myDate.hint('日期跨度必须为三个月',1000);

                    laydate.hint('month', {
                        content: '日期跨度必须为三个月',
                        ms: 1000
                    });
                } else {
                    if (flg==0){$(".laydate-btns-confirm").removeClass("laydate-disabled");}
                    flg = 1;
                }
            },
            done: function(value, date, endDate){
                if (date.year) {
                    let month1 = date.month;
                    let month2 = date.month + 1;
                    let month3 = date.month + 2;

                    let year1 = date.year;
                    let year2 = date.year;
                    let year3 = date.year;

                    if (month2 > 11) {
                        month2 -= 12;
                        year2++;
                    }

                    if (month3 > 11) {
                        month3 -= 12;
                        year3++;
                    }

                    if (month1 < 10) {
                        month1 = '0' + month1;
                    }
                    // month2 = month2 + 1
                    if (month2 < 10) {
                        month2 = '0' + month2;
                    }

                    // month3 = month3 + 1;
                    if (month3 < 10) {
                        month3 = '0' + month3;
                    }


                    $("#month1").val(year1 + '-' + (month1));
                    $("#month2").val(year2 + '-' + (month2));
                    $("#month3").val(year3 + '-' + (month3));
                } else {
                    $("#month1").val("");
                    $("#month2").val("");
                    $("#month3").val("");
                }

            }

        });
    });

    function changeCarLen() {
        $("#carLen").val($.common.join($('#carLenS').selectpicker('val')));
    }
    function changeCarType() {
        $("#carType").val($.common.join($('#carTypeS').selectpicker('val')));
    }
    function changeDeliArea() {
        $('input[name="deliArea"]').val($.common.join($('#deliArea').selectpicker('val')));
    }
    function changeArriArea() {
        $('input[name="arriArea"]').val($.common.join($('#arriArea').selectpicker('val')));
    }

    // 弹框打开时禁用父页面的滚动条
    function disableScroll() {
        $('body').addClass('no-scroll');
    }

    // 弹框关闭时启用父页面的滚动条
    function enableScroll() {
        $('body').removeClass('no-scroll');
    }

    function openEntrust(deliArea, arriArea, carLen, carType, amount, month, transType) {
        let url = ctx + "tms/reference_price/avg_stats/lot_detail?deliArea=" + deliArea + "&arriArea=" + arriArea + "&carLen="
            + carLen + "&carType=" + carType + "&month=" + month + "&transType=" + transType;


        if (amount) {
            url = url + "&amount=" + amount
        }
        var scrollTop = $(window).scrollTop();
        layer.open({
            type: 2,
            area: ['95%', '95%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "历史运单",
            content: url,
            btn: ['关闭'],
            // scrollbar: true,
            cancel: function (index) {
                return true;
            },
            success: function () {
                $(window).scrollTop(scrollTop);
            },
            // end: function () {
            //     enableScroll();
            // }
        });
    }

    //验证字符串是否是数字
    function checkNumber(val) {
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val)){
            return true;
        }
        else{
            return false;
        }
    }

    function init(provinceId, cityId, areaId, province, city, area) {
        $('#'+ provinceId +' option').remove();
        $('#'+ cityId +' option').remove();
        $('#'+ areaId +' option').remove();
        $('#' + provinceId).append("<option value=''>-- 请选择 --</option>");
        $('#'+ cityId).append("<option value=''>-- 请选择 --</option>");
        // $('#'+ areaId).append("<option value=''></option>");

        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            async: false,
            success: function (result) {
                for (var i in result) {
                    $('#' + provinceId).append("<option value='" + result[i].PROVINCE_CODE + "'>" + result[i].PROVINCE_NAME + "</option>")
                }
                $('#'+ provinceId +' option').each(function () {
                    if ($(this).val() == province) {
                        $(this).attr("selected", true)
                    }
                })
            }
        });
        if (province !== undefined) {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + province,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }
                    $('#'+ cityId +' option').each(function () {
                        if ($(this).val() == city) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + city,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                    }
                    $('#'+ areaId +' option').each(function () {
                        if ($(this).val() == area) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
        }

        $('#'+ provinceId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ cityId +' option').remove();
                    $('#'+ areaId +' option').remove();
                    $('#'+ cityId).append("<option value=''>-- 请选择 --</option>");
                    // $('#'+ areaId).append("<option value=''></option>");
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }

                }
            });
        });

        $('#'+ cityId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ areaId +' option').remove();
                    // $('#'+ areaId).append("<option value=''></option>");
                    for (var i in result) {
                        $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                    }
                    $('#'+ areaId).selectpicker('refresh');
                }
            });
        });
    }
</script>

</body>
</html>