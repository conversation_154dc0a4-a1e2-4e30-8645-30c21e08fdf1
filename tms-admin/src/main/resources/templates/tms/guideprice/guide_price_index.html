<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户运输线路指导价主列表')" />
</head>
<style>
    .info button {
        color: inherit;
        background-color: transparent;
        -webkit-transition: all .5s;
        transition: all .5s;
    }
    .tables .fixed-table-container .selected {
        background-color: #1ab394 !important;
        color: #fff !important;
    }
    .fixed-table-pagination div.pagination {
        margin: 10px 0;
    }
    iframe{
        border: 0;
    }

</style>
<body class="gray-bg">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-12" style="padding: 0;">
                <div style="width: 100%;text-align: center;margin: 10px 0;">
                    <div class="form-group-top btn-group">
                        <a class="btn btn-primary btn-sm" onclick="change(this,1)">客戶列表模式</a>
                        <a class="btn btn-default btn-outline btn-sm" onclick="change(this,2)">线路列表模式</a>
                    </div>
                </div>
            </div>

            <div class="col-sm-12" style="padding: 0;">
                <iframe style="height: calc(100vh - 54px)" width = "100%" id="iframe"></iframe>
            </div>
        </div>
    </div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    $(function() {
        $('#iframe').attr("src", ctx + "tms/guide_price?type=0");

    });

    //切换列表
    function change(obj,data){
        var thisObj=$(obj)
        thisObj.next().removeClass().addClass("btn btn-default btn-outline btn-sm")
        thisObj.prev().removeClass().addClass("btn btn-default btn-outline btn-sm")
        var urls
        if(data==1){
            urls = ctx + "tms/guide_price?type=0"
            thisObj.removeClass().addClass("btn btn-primary btn-sm")
        }else if(data==2){
            urls = ctx + "tms/guide_price?type=1"
            thisObj.removeClass().addClass("btn btn-primary btn-sm")
        }

        $('#iframe').attr("src", urls);

    }

</script>
</body>
</html>