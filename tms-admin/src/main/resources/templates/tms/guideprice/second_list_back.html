<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户运输线路指导价主列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <input type="hidden" name="customerId" th:value="${customerId}">
                <div class="row no-gutter">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">提货地址：</label>
                            <div class="col-sm-8">
                                <select id="startProvince" name="params[startProvince]"
                                        class="form-control valid" aria-invalid="false"></select>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <select id="startCity" name="params[startCity]" class="form-control valid"
                                aria-invalid="false"></select>
                    </div>
                    <div class="col-sm-2">
                        <select id="startArea" name="params[startArea]" class="form-control valid"
                                aria-invalid="false"></select>
                    </div>
                    <div class="col-sm-2">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">审核条数 ：</label>
                            <div class="col-sm-8">
                                <select name="params[checkTypeTimes]" class="form-control">
                                    <option value="">&#45;&#45;请选择&#45;&#45;</option>
                                    <option th:value="0">待审核条数</option>
                                    <option th:value="1">审核通过条数</option>
                                    <option th:value="2">审核不通过条数</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row no-gutter">
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-4">到货地址：</label>
                            <div class="col-sm-8">
                                <select id="endProvince" name="params[endProvince]"
                                        class="form-control valid" aria-invalid="false"></select>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <select id="endCity" name="params[endCity]" class="form-control valid"
                                aria-invalid="false"></select>
                    </div>
                    <div class="col-sm-2">
                        <select id="endArea" name="params[endArea]" class="form-control valid"
                                aria-invalid="false"></select>
                    </div>
                    <div class="col-sm-2">
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-6"></label>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-danger single disabled" shiro:hasPermission="tms:guidePrice:secondList:remove"
               onclick="remove()">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>

</div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:guidePrice:edit')}]];
    var prefix = ctx + "tms/guide_price/second_list";

    //客户id
    var customerId = [[${ customerId }]];
    //运输方式
    var trans_code = [[${@dict.getType('trans_code')}]];

    $(function () {
        var options = {
            url: prefix + "/list",
            modalName: "客户运输线路指导价",
            showToggle: false,
            showColumns: false,
            uniqueId: "custGuidePriceId",
            clickToSelect: true,
            columns: [
                {
                    checkbox: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn  btn-xs ' + editFlag + '" href="javascript:void(0)" '
                            + 'onclick="editPrice(\'' + row.custGuidePriceId + '\',\'' + customerId
                            + '\')" title="修改"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                        return actions.join('');
                    }
                },
                {
                    title: '线路名称',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        if (row.startAddress == null) {
                            row.startAddress = "";
                        }
                        if (row.endAddress == null) {
                            row.endAddress = "";
                        }
                        if (row.startAddress === "" && row.endAddress === "") {
                            return "";
                        } else {
                            return row.startAddress
                                + '<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'
                                + row.endAddress;
                        }

                    }
                },
                {
                    title: '车型条数',
                    field: 'carTypeCount',
                    align: 'left'
                },
                {
                    field: 'pendingReviewTimes',
                    title: '待审核条数',
                    align: 'left'
                },
                {
                    field: 'passTimes',
                    title: '审核通过条数',
                    align: 'left'
                },
                {
                    field: 'notPassTimes',
                    title: '审核不通过条数',
                    align: 'left'
                }
            ]
        };
        $.table.init(options);
        $.provinces.init("startProvince", "startCity", "startArea", "", "", "");
        $.provinces.init("endProvince", "endCity", "endArea", "", "", "");
    });

    function editPrice(id, customerId) {
        var url = ctx + "tms/guide_price/edit_price?custGuidePriceId=" + id + "&customerId=" + customerId;
        $.modal.openTab("编辑指导价", url);
    }

    /** 删除 */
    function remove() {
        var id = $.table.selectColumns($.table._option.uniqueId);
        $.modal.confirm("确认要删除选中的数据吗?", function () {
            $.operate.submit(prefix + "/remove", "post", "json", {"id": id.join()});
        });
    }
</script>
</body>
</html>