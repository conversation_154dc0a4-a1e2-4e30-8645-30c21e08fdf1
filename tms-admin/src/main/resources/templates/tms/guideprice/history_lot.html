<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('历史运价详情')" />
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<style>
    .table-striped {
        height: calc(100% - 20px);
    }
    .flex{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <form id="role-form" class="form-horizontal">
            <input name="deliProvince" type="hidden" th:value="${historyLotVO.deliProvince}">
            <input name="deliCity" type="hidden" th:value="${historyLotVO.deliCity}">
            <input name="deliArea" type="hidden" th:value="${historyLotVO.deliArea}">

            <input name="arriProvince" type="hidden" th:value="${historyLotVO.arriProvince}">
            <input name="arriCity" type="hidden" th:value="${historyLotVO.arriCity}">
            <input name="arriArea" type="hidden" th:value="${historyLotVO.arriArea}">

            <input name="reqCarLen" type="hidden" th:value="${historyLotVO.reqCarLen}">
            <input name="reqCarType" type="hidden" th:value="${historyLotVO.reqCarType}">

            <input name="startDate" type="hidden" th:value="${historyLotVO.startDate}">
            <input name="endDate" type="hidden" th:value="${historyLotVO.endDate}">

            <input name="transType" type="hidden" th:value="${historyLotVO.transType}">

        </form>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<th:block th:include="include :: bootstrap-select-js" />

<script th:inline="javascript">
    var prefix = ctx + "tms/reference_price";

    $(function() {
        console.log([[${historyLotVO}]])
        

        var options = {
            url: prefix + "/history_lot/list",
            modalName: "历史运价详情",
            showToggle:false,
            showColumns:false,
            clickToSelect: true,
            showSearch:false,
            showRefresh:false,
            uniqueId: "entrustLotId",
            columns: [
                {
                    field : 'custAbbrList',
                    title: '客户名称',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let html=[];
                        value.forEach(element => {
                            if(element){
                                html.push(element)
                            } 
                        });
                        return html.join(" | ")
                    }
                },
                {
                    title: '提到货地址',
                    field: 'deliProName',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        let deli="";
                        if(row.deliProvinceName){
                            deli+=row.deliProvinceName;
                        }
                        if(row.deliCityName&&row.deliCityName!='市辖区'){
                            deli+=row.deliCityName;
                        }
                        if(row.deliAreaName){
                            deli+=row.deliAreaName;
                        }

                        let arri="";
                        if(row.arriProvinceName){
                            arri+=row.arriProvinceName;
                        }
                        if(row.arriCityName&&row.arriCityName!='市辖区'){
                            arri+=row.arriCityName;
                        }
                        if(row.arriAreaName){
                            arri+=row.arriAreaName;
                        }

                      
                        
                        return `<span class="label label-warning pa2">提</span>`+$.table.tooltip(deli,9)+`<br/><span class="label label-success pa2">到</span>`+$.table.tooltip(arri,9);
                      
                    }
                },
                {
                    field : 'goodsName',
                    title: '货量',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let car=[];
                        if(row.numCount){
                            car.push(row.numCount+'件')
                        }
                        if(row.weightCount){
                            car.push(row.weightCount+'吨')
                        }
                        if(row.volumeCount){
                            car.push(row.volumeCount+'方')
                        }
                        return value +"<br/>"+ car.join('|')
                    }
                },
                {
                    field : 'carLenName',
                    title: '要求车长车型',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let car=[];
                        if (row.carLenName) {
                            car.push(row.carLenName)
                        }
                        if (row.carTypeName) {
                            car.push(row.carTypeName)
                        }
                        return car.join('米')
                    }
                },
                {
                    field : 'transType',
                    title: '运输方式',
                    align: 'left',
                    formatter: function (value, row, index) {
                        switch( Number(value) ) {
                            case 0:
                                return '<span class="carve carve-primary">公路整车</span>';
                            case 4:
                                return '<span class="carve carve-warning">冷链整车</span>';
                            case 15:
                                return '<span class="carve carve-success">危化整车</span>';
                            default:
                                break;
                        }      
                    }
                },
                {
                    field : 'carrierName',
                    title: '承运商',
                    align: 'left',
                },
                {
                    field : 'carNo',
                    title: '车辆',
                    align: 'left',
                },
                {
                    field : 'costAmount',
                    title: '金额',
                    align: 'left',
                    formatter: function (value, row, index) {
                        if(checkNumber(value)){
                            return getBasic(value)
                        }else{
                            return "-"
                        }
                        
                    }
                },
                {
                    field : 'regDate',
                    title: '调度日期',
                    align: 'left',
                },
            ]
        };
        $.table.init(options);
    });

    function getBasic(value) {
        var s = value;
        if(checkNumber(s)){
            var type = 1;
            if (/[^0-9\.]/.test(s))
                return "0";
            if (s == null || s == "")
                return "0";
            s = s.toString().replace(/^(\d*)$/, "$1.");
            s = (s + "00").replace(/(\d*\.\d\d)\d*/, "$1");
            s = s.replace(".", ",");
            var re = /(\d)(\d{3},)/;
            while (re.test(s))
                s = s.replace(re, "$1,$2");
            s = s.replace(/,(\d\d)$/, ".$1");
            if (type == 0) {// 不带小数位(默认是有小数位)
                var a = s.split(".");
                if (a[1] == "00") {
                    s = a[0];
                }
            }
            return "￥"+s;
        }else{
            return "-";
        }
        
    }



    //验证字符串是否是数字
    function checkNumber(val) {
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val)){
            return true;
        }
        else{
            return false;
        }
    }
</script>
</body>
</html>