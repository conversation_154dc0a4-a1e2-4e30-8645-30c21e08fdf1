<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户运输线路指导价主列表')" />
</head>
<style>
    .bgw{
        background: #fff;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 70px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
    }
    .info button {
        color: inherit;
        background-color: transparent;
        -webkit-transition: all .5s;
        transition: all .5s;
    }
    .tables .fixed-table-container .selected {
        background-color: #1ab394 !important;
        color: #fff !important;
    }
    .fixed-table-pagination div.pagination {
        margin: 10px 0;
    }
    iframe{
        border: 0;
    }

    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
</style>
<body class="gray-bg">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-5 bgw colDR" style="height: calc(100vh - 4px);overflow-y: auto;">
                <div class="container-div tables">
                    <div class="row">
                        <div class="col-sm-12 search-collapse" style="display: block;border-bottom: 1px #eee solid">
                            <form id="role-form" class="form-horizontal">
                                <div class="over">
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <div class="form-group">
                                            <div class="flex">
                                               <div class="flex_left">提货地址：</div>
                                                <div class="flex_right over">
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select id="startProvince" name="startProvinceId"
                                                                class="form-control valid" aria-invalid="false"></select>
                                                        </select>
                                                    </div>
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select id="startCity" name="startCityId" class="form-control valid"
                                                                aria-invalid="false"></select>
                                                    </div>
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select id="startArea" name="startAreaId" class="form-control valid"
                                                                aria-invalid="false"></select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
        
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <!-- <div class="form-group"> -->
                                            <div class="fl" onclick="changeDiv()" style="width: 60px">
                                                <img th:src="@{/img/change.png}" style="width: 20px;height: 20px;display: block;margin: auto 20px ">
                                            </div>
                                        <!-- </div> -->
                                    </div>
        
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <div class="form-group">
                                            <div class="flex">
                                               <div class="flex_left">到货地址：</div>
                                                <div class="flex_right over">
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select id="endProvince" name="endProvinceId"
                                                                class="form-control valid" aria-invalid="false"></select>
                                                        </select>
                                                    </div>
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select id="endCity" name="endCityId" class="form-control valid"
                                                                aria-invalid="false"></select>
                                                    </div>
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select id="endArea" name="endAreaId" class="form-control valid"
                                                                aria-invalid="false"></select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
        
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <div class="form-group mt10">
                                            <div class="flex">
                                                <div class="flex_left">是否过期：</div>
                                                <div class="flex_right over">
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select name="isExpired" id="isExpired" class="form-control valid"
                                                                aria-invalid="false" required>
                                                            <option value=""></option>
                                                            <option value="0">未过期</option>
                                                            <option value="1">已过期</option>
                                                        </select>
                                                    </div>
                                                    <div class="fl" style="padding: 0 10px">
                                                        <a class="btn btn-primary btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                                        <a class="btn btn-default btn-outline btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
        
                                    <!-- <div class="fl" style="width: calc(100% - 300px);padding-left: 10px">
                                        <div class="mt10">
                                            <div class="flex">
                                                <label class="flex_left">收货地址：</label>
                                                 <div class="flex_right over">
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select id="endProvince" name="endProvinceId"
                                                                class="form-control valid" aria-invalid="false"></select>
                                                        </select>
                                                    </div>
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select id="endCity" name="endCityId" class="form-control valid"
                                                                aria-invalid="false"></select>
                                                    </div>
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select id="endArea" name="endAreaId" class="form-control valid"
                                                                aria-invalid="false"></select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt10">
                                            <div class="flex">
                                                <label class="flex_left">收货地址：</label>
                                                <div class="flex_right over">
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select id="endProvince" name="endProvinceId"
                                                                class="form-control valid" aria-invalid="false"></select>
                                                        </select>
                                                    </div>
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select id="endCity" name="endCityId" class="form-control valid"
                                                                aria-invalid="false"></select>
                                                    </div>
                                                    <div class="fl" style="width: 33%;padding: 0 5px;box-sizing: border-box">
                                                        <select id="endArea" name="endAreaId" class="form-control valid"
                                                                aria-invalid="false"></select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> -->
                                    <!-- <div class="fl" style="width: 200px;padding: 0 10px">
                                        <div class="mt10">
                                            <div class="form-group">
                                                <a class="btn btn-primary btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                                <a class="btn btn-default btn-outline btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                            </div>
                                        </div>
                                    </div> -->
                                </div>
                            </form>
                        </div>
        
        <!--                <div class="btn-group-sm" id="toolbar" role="group">-->
        <!--                    <a class="btn btn-primary" onclick="$.table.importExcel()" shiro:hasPermission="tms:guidePrice:import">-->
        <!--                        <i class="fa fa-upload"></i> 导入-->
        <!--                    </a>-->
        <!--                    <div class="dropdown single disabled" style="display: inline-block;">-->
        <!--                        <button class="btn btn-primary dropdown-toggle" style="padding: 4px 12px;" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">-->
        <!--                            <i class="fa fa-download"></i> 导出-->
        <!--                            <span class="caret"></span>-->
        <!--                        </button>-->
        <!--                        <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">-->
        <!--                            <li>-->
        <!--                                <a class="btn single disabled" onclick="exportExcel()" shiro:hasPermission="tms:guidePrice:export">-->
        <!--                                    <i class="fa fa-download"></i> 导出-->
        <!--                                </a>-->
        <!--                            </li>-->
        <!--                            <li>-->
        <!--                                <a class="btn" onclick="exportAll()" shiro:hasPermission="tms:guidePrice:export">-->
        <!--                                    <i class="fa fa-download"></i> 导出实际指导价-->
        <!--                                </a>-->
        <!--                            </li>-->
        <!--                            <li>-->
        <!--                                <a class="btn" onclick="$.table.exportExcel()" shiro:hasPermission="tms:guidePrice:export">-->
        <!--                                    <i class="fa fa-download"></i> 导出指导价模板-->
        <!--                                </a>-->
        <!--                            </li>-->
        <!--                        </ul>-->
        <!--                    </div>-->
        <!--                    <a class="btn btn-danger single disabled" shiro:hasPermission="tms:guidePrice:remove" onclick="remove()" >-->
        <!--                        <i class="fa fa-remove"></i> 删除-->
        <!--                    </a>-->
        <!--                </div>-->
                        <div class="col-sm-12 select-table">
                            <table id="bootstrap-table" data-mobile-responsive="true"></table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-7" style="padding-right: 0;">
                <iframe style="height: calc(100vh - 4px);" width = "100%" id="iframe"></iframe>
            </div>
        </div>
    </div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    $(function() {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        $('#iframe').attr("src", ctx + "tms/guide_price/second_list?transLineId=1");

        $.provinces.init("startProvince", "startCity", "startArea", "", "", "");
        $.provinces.init("endProvince", "endCity", "endArea", "", "", "");

        var options = {
            url: ctx + "tms/guide_price/list_2",
            modalName: "客户运输线路指导价",
            showToggle:false,
            // exportUrl: prefix + "/export",
            // importUrl: prefix + "/importData",
            // importTemplateUrl: prefix + "/importTemplate",
            showColumns:false,
            showSearch: false,
            showRefresh:false,
            uniqueId: "customerId",
            clickToSelect: true,
            columns: [
                {
                    radio: true
                },
                {
                    field : 'lineName',
                    title : '线路名称',
                    align: 'left'
                },
                {
                    title: '客户数量',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return "<div style='color:#f7a54a'>" + row.customerCount + "</div>";
                    }
                },
                {
                    title: '指导价数量',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return "<div style='color:#f7a54a'>" + row.priceDetailCount + "</div>";
                    }
                },

            ],
            //
            onClickRow(row,ele,field){
                var urls = ctx + "tms/guide_price/second_list?transLineId=" + row.transLineId
                $('#iframe').attr("src", urls);
            }
        };
        $.table.init(options);
    });


    /** 删除 */
    function remove() {
        var id = $.table.selectColumns("customerId");
        $.modal.confirm("确认要删除选中的数据吗?", function() {
            $.operate.submit(ctx + "tms/guide_price/remove", "post", "json", {"customerId": id.join()},function (){
                $('#iframe').attr("src", urls);
            });
        });
    }
    function changeDiv(){
        var startProvince= $('#startProvince').val()
        var startCity= $('#startCity').val()
        var startArea= $('#startArea').val()
        var endProvince= $('#endProvince').val()
        var endCity= $('#endCity').val()
        var endArea= $('#endArea').val()
        $.provinces.init("startProvince","startCity","startArea",endProvince,endCity,endArea);
        $.provinces.init("endProvince","endCity","endArea",startProvince,startCity,startArea);
        searchPre();
    }


    function searchPre(){
        //$.table.search()
    }
    //重置
    function reset() {
        // $.form.reset();
    }

    /**
     * 指导价导出
     */
    function exportExcel() {
        var customerId = $.table.selectColumns("customerId").join();
        var url = ctx + "report/guidePriceExport/"+customerId;
        $.modal.openTab("指导价导出", url);
    }

    function exportAll(){
        $.modal.confirm("确定导出所有指导价吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(ctx + "tms/guide_price/exportAll", null, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

</script>
</body>

<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>