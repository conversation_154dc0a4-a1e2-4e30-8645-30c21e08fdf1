<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增指导价')"/>
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 26px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
</style>
<body>
<div class="form-content">
    <form id="form-invoice-add" class="form-horizontal" novalidate="novalidate">
        <div class="row">
            <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>客户：</span></label>
                    <div class="flex_right">
                        <div class="form-group">
                            <div class="col-sm-12 col-xs-12">
<!--                                <input name="custAbbr" id="custAbbr" onclick="selectClient()" type="text"-->
<!--                                       placeholder="请选择客户" class="form-control valid"-->
<!--                                       aria-required="true" required autocomplete="off" >-->
                                <input name="custAbbr" id="custAbbr" placeholder="请输入客户名称" class="form-control" type="text" maxlength="50" autocomplete="off">

                                <input name="customerId" id="customerId" type="hidden">
                                <div  style="position: relative;top: -1px;">
                                    <ul class="dropdown-menu dropdown-menu-right" role="menu"></ul>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="row">

            <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>提货省市区：</span></label>
                    <div class="flex_right">
                        <div class="form-group">
                            <div class="col-sm-4 col-xs-4">
                                <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" aria-invalid="false" required>
                                </select>
                            </div>
                            <div class="col-sm-4 col-xs-4">
                                <select name="deliCityId" id="deliCityId" class="form-control valid" aria-invalid="false" required></select>
                            </div>
                            <div class="col-sm-4 col-xs-4">
<!--                                <select name="deliAreaId" id="deliAreaId" class="form-control valid" aria-invalid="false" required></select>-->

                                <select name="deliAreaId" id="deliAreaId" class="form-control selectpicker valid"
                                        aria-invalid="false" multiple placeholder="请选择" data-none-selected-text="请选择">
                                </select>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>到货省市区：</span></label>
                    <div class="flex_right">
                        <div class="form-group">
                            <div class="col-sm-4 col-xs-4">
                                <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false" required>
                                </select>
                            </div>
                            <div class="col-sm-4 col-xs-4">
                                <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false" required></select>
                            </div>
                            <div class="col-sm-4 col-xs-4">
<!--                                <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false" required></select>-->
                                <select name="arriAreaId" id="arriAreaId" class="form-control selectpicker valid"
                                        aria-invalid="false" multiple placeholder="请选择" data-none-selected-text="请选择">
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 col-sm-6 col-xs-6">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>车长：</span></label>
                    <div class="flex_right">
                        <select id="carLen" name="carLen" class="form-control noselect2 selectpicker" aria-invalid="false" required
                                th:with="type=${@dict.getType('car_len')}" data-none-selected-text="请选择" multiple>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                        <label id="carLen-error" class="error" for="carLen"></label>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-sm-6 col-xs-6">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>车型：</span></label>
                    <div class="flex_right">
                        <select id="carType" name="carType" class="form-control noselect2 selectpicker" aria-invalid="false" required
                                th:with="type=${@dict.getType('car_type')}" data-none-selected-text="请选择" multiple>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                        <label id="carType-error" class="error" for="carType"></label>

                    </div>
                </div>
            </div>

        </div>
        <div class="row">

            <div class="col-md-6 col-sm-6 col-xs-6">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>指导价：</span></label>
                    <div class="flex_right">
                        <input name="price" id="price" placeholder="指导价" class="form-control"  oninput="$.numberUtil.onlyNumberTwoDecimal(this);" type="text" autocomplete="off" aria-required="true" required>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-sm-6 col-xs-6">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>价格类型：</span></label>
                    <div class="flex_right">
                        <select name="priceType" id="priceType" class="form-control" required>
                            <option value="">-- 请选择 --</option>
                            <option th:value="0">普货</option>
                            <option th:value="1">危险品</option>
                        </select>
                    </div>
                </div>
            </div>

        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<th:block th:include="include :: bootstrap-select-js" />

<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: bootstrap-suggest-js"/>

<script th:inline="javascript">
    var prefix = ctx + "tms/special_reference_price";

    $(function () {
        // 表单验证
        $("#form-invoice-add").validate({
            focusCleanup: true
        });

        init("deliProvinceId","deliCityId","deliAreaId");
        init("arriProvinceId","arriCityId","arriAreaId");
    })

    // function selectClient() {
    //     layer.open({
    //         type: 2,
    //         area: ['90%', '90%'],
    //         fix: false,
    //         //不固定
    //         maxmin: true,
    //         shade: 0.3,
    //         title: "选择客户",
    //         content: ctx + "client/related",
    //         btn: ['确定', '关闭'],
    //         // 弹层外区域关闭
    //         shadeClose: true,
    //         yes: function (index, layero){
    //             //获取整行
    //             var rows = layero.find('iframe')[0].contentWindow.getChecked();
    //             if (rows.length === 0) {
    //                 $.modal.alertWarning("请至少选择一条记录");
    //                 return;
    //             }
    //             //客户id
    //             $("#customerId").val(rows[0]["customerId"]);
    //             //客户简称
    //             $("#custAbbr").val(rows[0]["custAbbr"]);
    //
    //             //选中完需单独校验
    //             $("#form-invoice-add").validate().element($("#custAbbr"));
    //             layer.close(index);
    //
    //
    //
    //         },
    //         cancel: function(index) {
    //             return true;
    //         }
    //     });
    //
    //     // $.modal.open("选择客户", ctx + "client/related?permission=sales",1050,'',function (index, layero) {
    //     //     //获取整行
    //     //     var rows = layero.find('iframe')[0].contentWindow.getChecked();
    //     //     if (rows.length === 0) {
    //     //         $.modal.alertWarning("请至少选择一条记录");
    //     //         return;
    //     //     }
    //     //     //客户id
    //     //     $("#customerId").val(rows[0]["customerId"]);
    //     //     //客户名称
    //     //     $("#custName").val(rows[0]["custName"]);
    //     //     //客户简称
    //     //     $("#custAbbr").val(rows[0]["custAbbr"]);
    //     //
    //     //
    //     //     //选中完需单独校验
    //     //     $("#form-invoice-add").validate().element($("#custAbbr"));
    //     //     layer.close(index);
    //     // });
    // }

    var custAbbr = $("#custAbbr").bsSuggest({
        indexId: 4, //data.value 的第几个数据，作为input输入框的内容
        indexKey: 0, //data.value 的第几个数据，作为input输入框的内容
        idField: 'customerId',                    //每组数据的哪个字段作为 data-id，优先级高于 indexId 设置（推荐）
        keyField: 'custName',                   //每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置（推荐）
        allowNoKeyword: false, //是否允许无关键字时请求数据
        multiWord: false, //以分隔符号分割的多关键字支持separator: ",", //多关键字支持时的分隔符，默认为空格
        getDataMethod: "url", //获取数据的方式，总是从 URL 获取
        effectiveFields:["custName","custAbbr","contact","provinceName","cityName"],
        effectiveFieldsAlias:{custName: "客户名称", custAbbr: "客户简称",contact:"联系人",provinceName:"省份",cityName:"城市"},
        showHeader: true,
        //listAlign:'right',        //提示列表对齐位置，left/right/auto
        hideOnSelect: true,            // 鼠标从列表单击选择了值时，是否隐藏选择列表
        inputWarnColor: 'rgba(255,0,0,.1)', //输入框内容不是下拉列表选择时的警告色
        url: ctx + "client/listClient?contractSearch="+"contractSearch" + "&custName=", //custAbbr /*优先从url ajax 请求 json 帮助数据，注意最后一个参数为关键字请求参数*/
        processData: function(json){// url 获取数据时，对数据的处理，作为 getData 的回调函数
            var i, len, data = {value: []};
            if(!json || json.rows.length == 0) {
                return false;
            }
            len = json.rows.length;
            for (i = 0; i < len; i++) {
                data.value.push({
                    "custName": json.rows[i].custName,
                    "custAbbr": json.rows[i].custAbbr,
                    "contact": json.rows[i].contact,
                    "provinceName":json.rows[i].provinceName,
                    "cityName":json.rows[i].cityName,
                    "customerId":json.rows[i].customerId
                });
            }
            return data;
        }
    }).on('onSetSelectValue', function (e, keyword, data) {
        $('#customerId').val(data.customerId);
    }).on("onUnsetSelectValue",function (e) {
        $('#customerId').val("");
    });



    /**
     * 提交
     */
    function submitHandler() {

        let customerId = $('#customerId').val();
        if (customerId == '') {
            $.modal.alertWarning("请输入正确的客户！")
            return;
        }

        if ($.validate.form()) {
            var data = $("#form-invoice-add").serializeArray();
            $.operate.save(prefix + "/add", data);
        }
    }


    function init(provinceId, cityId, areaId, province, city, area) {
        $('#'+ provinceId +' option').remove();
        $('#'+ cityId +' option').remove();
        $('#'+ areaId +' option').remove();
        $('#' + provinceId).append("<option value=''>-- 请选择 --</option>");
        $('#'+ cityId).append("<option value=''>-- 请选择 --</option>");
        // $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");

        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            async: false,
            success: function (result) {
                for (var i in result) {
                    $('#' + provinceId).append("<option value='" + result[i].PROVINCE_CODE + "'>" + result[i].PROVINCE_NAME + "</option>")
                }
                $('#'+ provinceId +' option').each(function () {
                    if ($(this).val() == province) {
                        $(this).attr("selected", true)
                    }
                })
            }
        });
        if (province !== undefined) {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + province,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }
                    $('#'+ cityId +' option').each(function () {
                        if ($(this).val() == city) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + city,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                    }
                    $('#'+ areaId +' option').each(function () {
                        if ($(this).val() == area) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
        }

        $('#'+ provinceId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ cityId +' option').remove();
                    $('#'+ areaId +' option').remove();
                    $('#'+ cityId).append("<option value=''>-- 请选择 --</option>");
                    // $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }

                }
            });
        });

        $('#'+ cityId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ areaId +' option').remove();
                    // $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");
                    for (var i in result) {
                        $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                    }
                    $('#'+ areaId).selectpicker('refresh');
                }
            });
        });
    }



</script>

</body>

</html>