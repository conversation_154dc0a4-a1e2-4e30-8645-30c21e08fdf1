<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('成交记录')"/>
</head>
<style>
    .table-striped {
        height: calc(100% - 40px);
    }
    .table-striped .bootstrap-table {
        height: calc(100% - 30px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped toofoot cj">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    //参数
    //var param = parent.layui.$('#param').val();

    var carType = [[${param.carType}]];
    var carLen = [[${param.carLen}]];
    var startProvinceId = [[${param.startProvinceId}]];
    var startCityId = [[${param.startCityId}]];
    var startAreaId = [[${param.startAreaId}]];
    var endProvinceId = [[${param.endProvinceId}]];
    var endCityId = [[${param.endCityId}]];
    var endAreaId = [[${param.endAreaId}]];
    var transType = [[${param.transType}]];
    var customerId = [[${param.customerId}]];
    var years = [[${param.years}]];
    var param = "carType=" + carType + "&carLen=" + carLen + "&years=" + years
        + "&startProvinceId=" + startProvinceId + "&startCityId=" + startCityId + "&startAreaId=" + startAreaId
        + "&endProvinceId=" + endProvinceId + "&endCityId=" + endCityId + "&endAreaId=" + endAreaId
        + "&transType=" + transType + "&customerId=" + customerId;

    $(function () {
        var options = {
            url: ctx + "tms/guide_price/price_detail_list?" + param,
            showToggle: false,
            showSearch: false,
            showRefresh: false,
            showColumns: false,
            fixedColumns: true,
            fixedNumber: 0,
            columns: [
                {
                    title: '发货单号',
                    align: 'left',
                    field: 'invoiceVbillno'
                },
                {
                    title: '起始地目的地',
                    align: 'left',
                    field: 'deliAddress',
                    formatter: function status(value, row, index) {
                        if(row.deliAddress == null){
                            row.deliAddress = "";
                        }
                        if(row.arriAddress == null){
                            row.arriAddress = "";
                        }
                        if(row.deliAddress == "" && row.arriAddress == ""){
                            return "";
                        }else{
                            return row.deliAddress+'<i class="fa fa-arrow-circle-right" style="font-size:16px;color: #1ab394"></i>'+row.arriAddress;
                        }

                    }
                },
                {
                    title:'货物名称',
                    align:'left',
                    field:'goodsName'
                },
                {
                    title:'车长/车型',
                    align:'left',
                    field:'carLenName',
                    formatter: function status(value, row, index) {
                        return row.carLenName+'/'+row.carTypeName;
                    }
                },
                {
                    title: '要求提货日期',
                    align: 'left',
                    field: 'reqDeliDate',
                    formatter: function status(value, row, index) {
                        if(value == "" || value == null || value == 'undefined'){
                            return "";
                        }
                        return value.substring(0,10);
                    }
                },
                {
                    title: '总件数|总重量(吨)|总体积(m³)',
                    field: 'numCount',
                    formatter: function (value, row, index) {
                        return row.num+"件"+'<span style="font-weight: 900">|</span>'+row.weight+"吨"+'<span style="font-weight: 900">|</span>'+row.volume+"m³"
                    }
                },
                {
                    title: '运费',
                    align: 'right',
                    field:'receiveFee',
                    formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }
                ,
                {
                    title: '在途费用',
                    align: 'right',
                    field:'wayFee',
                    formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                },
                {
                    title: '油卡金额',
                    align: 'right',
                    field:'oilFee',
                    formatter: function (value, row, index) {
                        return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                    }
                }

            ]
        };
        $.table.init(options);
    });



</script>
</body>
</html>