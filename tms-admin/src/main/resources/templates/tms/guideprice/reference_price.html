<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指导价')"/>
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
   .flex{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .cur{
        cursor: pointer;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    
                    <div class="col-md-1 col-sm-1">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="carLen" placeholder="车长" id="carLen" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="车长" multiple th:with="type=${@dict.getType('car_len')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-1 col-sm-1">
                        <div class="form-group">
                            <div class="col-sm-12" style="padding-right: 0">
                                <select name="carType" placeholder="车型" id="carType" class="form-control valid noselect2 selectpicker"
                                        aria-invalid="false" data-none-selected-text="车型" multiple th:with="type=${@dict.getType('car_type')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <div class="col-sm-4">
                                    <select name="deliProvinceId" id="deliProvinceId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="deliCityId" id="deliCityId" class="form-control valid"
                                            aria-invalid="false"></select>
                                </div>
                                <div class="col-sm-4">
<!--                                    <select name="deliAreaId" id="deliAreaId" class="form-control valid"-->
<!--                                            aria-invalid="false"></select>-->
                                    <select name="deliAreaId" id="deliAreaId" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" multiple placeholder="请选择" data-none-selected-text="请选择">
                                    </select>

                                </div>
                            </div>
                           
                        </div>
                    </div>

                    <div class="col-md-4 col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-1" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 26px;height: 26px;display: block;margin: 0 auto">
                            </div>
                            <div class="col-sm-11">
                                <div class="col-sm-4">
                                    <select name="arriProvinceId" id="arriProvinceId"
                                            class="form-control valid"></select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="arriCityId" id="arriCityId" class="form-control valid"></select>
                                </div>

                                <div class="col-sm-4">
<!--                                    <select name="arriAreaId" id="arriAreaId" class="form-control valid"></select>-->
                                    <select name="arriAreaId" id="arriAreaId" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" multiple placeholder="请选择" data-none-selected-text="请选择">
                                    </select>

                                </div>
                            </div>

                           
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-2">
                        <!--                        <label class="col-sm-4"></label>-->
                        <div class="form-group" style="text-align: center;">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a id="res" class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>




                </div>
            </form> 
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="add()" shiro:hasPermission="tms:reference_price:add">
                <i class="fa fa-plus"></i> 新增
            </a>

            <a class="btn btn-primary" onclick="importAllData(0)" shiro:hasPermission="tms:reference_price:import">
                <i class="fa fa-upload"></i> 普货指导价导入
            </a>
            <a class="btn btn-primary" onclick="importAllData(1)" shiro:hasPermission="tms:reference_price:import">
                <i class="fa fa-upload"></i> 危险品指导价导入
            </a>

            <a class="btn btn-primary" onclick="view_batch()" shiro:hasPermission="tms:reference_price:edit_price">
                <i class="fa fa-dollar"></i> 批量修改指导价
            </a>

            <a class="btn btn-warning" onclick="exportExcel()" shiro:hasPermission="tms:reference_price:export">
	            <i class="fa fa-download"></i> 导出
	        </a>
            <a class="btn btn-primary" onclick="openAvg()" shiro:hasPermission="tms:reference_price:stats">
	            <i class="fa fa-folder-open-o"></i> 历史平均值
	        </a>

        </div>

        <div class="col-sm-12 select-table table-striped table-responsive ">
            <table class="text-nowrap table-bordered " id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "tms/reference_price";
    
    $(function () {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre()
            }
        });

        // $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        // $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        init("deliProvinceId","deliCityId","deliAreaId");
        init("arriProvinceId","arriCityId","arriAreaId");

        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            showToggle:false,
            showColumns:false,
            clickToSelect:true,
            modalName: "指导价",
            height: 560,
            uniqueId: "id",
            onEditableSave: function (field, row, oldValue, $el){
                let data = {};
                if(field=="priceBasic"){
                    data = {referencePriceId: row.id, price: row.priceBasic,priceType:0}
                }else{
                    data = {referencePriceId: row.id, price: row.priceDangerousGoods,priceType:1}
                }
               

                $.ajax({
                    url: prefix + "/edit_price",
                    type: "post",
                    dataType: "json",
                    data: data,
                    success: function (result) {
                        if (result.code === 0) {
                            var data = result.data;
                            //刷新
                            $.btTable.bootstrapTable('refresh', {
                                silent: true
                            });
                        } else {
                            $.modal.msgError(result.msg);
                            //刷新
                            $.btTable.bootstrapTable('refresh', {
                                silent: true
                            });
                        }
                    }
                });

            },
            columns: [
                {
                    checkbox: true
                },
                {
                    title: '提货地址',
                    align: 'left',
                    field : 'deliName',
                },
                {
                    title: '到货地址',
                    align: 'left',
                    field : 'arriName',
                    formatter: function status(value, row, index) {
                        if ([[${@permission.hasAnyPermi('tms:reference_price:detail')}]] != "hidden") {
                            return `<div class="flex"><div>`+value+`</div>
                                <i class="fa fa-search-plus cur" style="font-size: 16px;color: #0ba687" data-delay="150" data-toggle="tooltip" data-placement="top" title="查看历史平均运价"
                                onclick="detail( \``+row.deliProvinceId+`\`,\``+row.deliCityId+`\`,\``+row.deliAreaId+`\`,
                                \``+row.arriProvinceId+`\`,\``+row.arriCityId+`\`,\``+row.arriAreaId+`\` )"></i></div>`
                        }
                        return value;
                    }
                },
                // {
                //     title: '装卸货地址',
                //     align: 'left',
                //     field : 'arriProName',
                //     width: 200,
                //     formatter: function status(value, row, index) {
                //         let deli="",arri="";
                //         if(row.deliProName){
                //             deli+=row.deliProName;
                //         }
                //         if(row.deliCityName&&row.deliCityName != '市辖区'){
                //             deli+=row.deliCityName;
                //         }
                //         if(row.deliAreaName){
                //             deli+=row.deliAreaName;
                //         }

                //         if(row.arriProName){
                //             arri+=row.arriProName;
                //         }
                //         if(row.arriCityName&&row.arriCityName != '市辖区'){
                //             arri+=row.arriCityName;
                //         }
                //         if(row.arriAreaName){
                //             arri+=row.arriAreaName;
                //         }

                //         return `<span class="label label-warning pa2">装</span>`+deli+`<br/><span class="label label-success pa2">卸</span>`+arri;
                //     }
                // },  
                {
                    field : 'carLenName',
                    title: '车长车型',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let car=[];
                        if (row.carLenName) {
                            car.push(row.carLenName)
                        }
                        if (row.carTypeName) {
                            car.push(row.carTypeName)
                        }
                        return car.join('米')
                    }
                },
                {
                    field : 'priceBasic',
                    title : '指导价',
                    align: 'left',
                    editable:{
                        type: 'text',
                        title: '修改价格',
                        emptytext: '暂无数据',
                        validate:  function (v) {
                            if (!checkNumber(v)) {
                                return "请输入数字！";
                            }
                        }
                    }
                },
                {
                    field : 'priceBasicRegUser',
                    title : '指导价创建人',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let html =[];
                        if(row.priceBasicRegUser){
                            html.push(row.priceBasicRegUser)
                        }
                        if(row.priceBasicRegDate){
                            html.push(row.priceBasicRegDate)
                        }
                        if(row.priceBasic){
                            return `<div class="flex"><div>`+html.join(`<br/>`)+`</div><i class="fa fa-database cur" style="font-size: 16px;color: #0ba687" data-delay="150" data-toggle="tooltip" data-placement="top" title="查看历史指导价信息" onclick="deil(\``+row.id+`\`,0)"></i></div>`
                        }
                        return `<div class="flex"><div>`+html.join(`<br/>`)+`</div></div>`
                    }
                },

                {
                    field : 'priceDangerousGoods',
                    title : '危化品指导价',
                    align: 'left',
                    editable:{
                        type: 'text',
                        title: '修改价格',
                        emptytext: '暂无数据',
                        validate:  function (v) {
                            if (!checkNumber(v)) {
                                return "请输入数字！";
                            }
                        }
                    }
                },
                {
                    field : 'priceDangerousGoodsRegUser',
                    title : '危化价创建人',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let html =[];
                        if(row.priceDangerousGoodsRegUser){
                            html.push(row.priceDangerousGoodsRegUser)
                        }
                        if(row.priceDangerousGoodsRegDate){
                            html.push(row.priceDangerousGoodsRegDate)
                        }
                        if(row.priceDangerousGoods){
                            return `<div class="flex"><div>`+html.join(`<br/>`)+`</div><i class="fa fa-exclamation-triangle cur" style="font-size: 16px;color: #0ba687" data-delay="150" data-toggle="tooltip" data-placement="top" title="查看历史危化品指导价信息" onclick="deil(\``+row.id+`\`,1)"></i></div>`
                        }
                        return `<div class="flex"><div>`+html.join(`<br/>`)+`</div></div>`
                    }
                },
                // {
                //     title: '创建人',
                //     field: 'regUserName',
                //     align: 'left'
                // },
                // {
                //     title: '创建时间',
                //     field: 'regDate',
                //     align: 'left'
                // },
            ], 
            onLoadSuccess: function(data) {
                let fieldList = ["deliName","arriName"];
                mergeCells(data.rows, 1,  $('#bootstrap-table'), fieldList);
            }
        };

        $.table.init(options);

        $('#bootstrap-table').on('all.bs.table', function () {
            var data = $('#bootstrap-table').bootstrapTable('getData', true);
            let fieldList = ["deliName","arriName"];
            mergeCells(data, 1,  $('#bootstrap-table'), fieldList);
        });

    });

     /**
     * 合并单元格
     * 
     */
     function mergeCells(data, colspan, target, sameFiled) { 
        sameFiled.forEach(res=>{
            for (var i = 0; i < data.length; i++) {
                data[i][res+'_rows'] = 1;
                for (let j = i + 1; j < data.length; j++) {
                    if (res == 'deliName') {
                        if (data[i][res] == data[j][res]) {
                            data[i][res+'_rows'] = (data[i][res+'_rows'] || 1) + 1;
                            data[i]['f'] = i;
                            data[j]['f'] = i;
                        } else {
                            break;
                        }
                    } else {
                        if (data[i][res] == data[j][res] && data[i]['f'] == data[j]['f']) {
                            data[i][res+'_rows'] = (data[i][res+'_rows'] || 1) + 1;
                        } else {
                            break;
                        }
                    }
                }
                i = i+ data[i][res+'_rows'] - 1;
            }
            for (var i = 0; i < data.length; i++) {
                if (data[i][res+ "_rows"] > 1) {
                    target.bootstrapTable('mergeCells', {index: i, field: res, colspan: 1, rowspan: data[i][res+ "_rows"]});
                }
            }
        })
    }

     /**
     * 搜索的方法
     */
     function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }
    /**
     * 重置
     */
    function resetPre() {
        $("#role-form")[0].reset();
        searchPre();
    }

    /**
     * 导入数据
     */
    function importAllData(priceType) {
        layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入数据',
            content: $('#importTpl').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var index = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#file')[0].files[0]);
                formData.append("priceType", priceType);
                $.ajax({
                    url: ctx + "tms/reference_price/import",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code == web_status.WARNING) {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });
    }

    function add() {
        layer.open({
            type: 2,
            area: ['60%', '85%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "添加" + $.table._option.modalName,
            content: prefix + "/add/",
            btn: ['确认', '关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function(index) {
                return true;
            }
        });
    }

    //验证字符串是否是数字
    function checkNumber(val) {
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val)){
            return true;
        }
        else{
            return false;
        }
    }

    function searchPre() {
        var data = {};
        data.carLen = $.common.join($('#carLen').selectpicker('val'));
        data.carType = $.common.join($('#carType').selectpicker('val'));

        data.deliAreaId = $.common.join($('#deliAreaId').selectpicker('val'));
        data.arriAreaId = $.common.join($('#arriAreaId').selectpicker('val'));


        $.table.search('role-form', data);
    }

    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        $("#role-form")[0].reset();
        $("#carLen").selectpicker('refresh');
        $("#carType").selectpicker('refresh');
        $("#deliAreaId").selectpicker('refresh');
        $("#arriAreaId").selectpicker('refresh');

        searchPre();
    }

    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        // $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        // $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);

        init("deliProvinceId", "deliCityId", "deliAreaId", arriProvinceId, arriCityId, arriAreaId);
        init("arriProvinceId", "arriCityId", "arriAreaId", deliProvinceId, deliCityId, deliAreaId);

        searchPre();
    }
    function deil(referencePriceId,priceType) {
        layer.open({
            type: 2,
            area: ['800px', '600px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "历史记录",
            content: prefix + "/history?referencePriceId="+referencePriceId+"&priceType="+priceType,
            btn: ['关闭'],
            shadeClose: true,            // 弹层外区域关闭
            cancel: function (index) {
                return true;
            }
        });
    }
    function view_batch() {
        layer.open({
            type: 2,
            area: ['60%', '60%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "批量修改指导价",
            content: prefix + "/edit_price_batch",
            btn: ['确认','关闭'],
            shadeClose: true,            // 弹层外区域关闭
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            },
            cancel: function (index) {
                return true;
            }
        });
    }
    function exportExcel() {
        let data = $.common.formToJSON("role-form");
        data.carLen = $.common.join($('#carLen').selectpicker('val'));
        data.carType = $.common.join($('#carType').selectpicker('val'));

        data.deliAreaId = $.common.join($('#deliAreaId').selectpicker('val'));
        data.arriAreaId = $.common.join($('#arriAreaId').selectpicker('val'));

        $.modal.confirm("确定导出当前指导价吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(prefix + "/export", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();

                // $.table.refresh();
            });
        });



        // layer.open({
        //     type: 2,
        //     area: ['60%', '50%'],
        //     fix: false,
        //     maxmin: true,
        //     shade: 0.3,
        //     title: "指导价导出",
        //     content: prefix + "/export",
        //     btn: ['确认','关闭'],
        //     shadeClose: true,            // 弹层外区域关闭
        //     yes: function (index, layero) {
        //         var iframeWin = layero.find('iframe')[0];
        //         iframeWin.contentWindow.submitHandler(index, layero);
        //     },
        //     cancel: function (index) {
        //         return true;
        //     }
        // });
    }

    function detail(deliProvinceId,deliCityId,deliAreaId,arriProvinceId,arriCityId,arriAreaId) {
        let deli= "&deliProvinceId="+deliProvinceId+"&deliCityId="+deliCityId+"&deliAreaId="+deliAreaId+"&arriProvinceId="+arriProvinceId+"&arriCityId="+arriCityId+"&arriAreaId="+arriAreaId;
        layer.open({
            type: 2,
            area: ['95%', '100%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "线路历史运费",
            content: ctx + "tms/reference_price/detail?"+deli,
            btn: ['关闭'],
            shadeClose: true,            // 弹层外区域关闭
            cancel: function (index) {
                return true;
            }
        });
    }

    function openAvg() {
        $.modal.openTab("历史平均值", ctx + "tms/reference_price/avg_stats");
    }

    function init(provinceId, cityId, areaId, province, city, area) {
        $('#'+ provinceId +' option').remove();
        $('#'+ cityId +' option').remove();
        $('#'+ areaId +' option').remove();
        $('#'+ areaId).selectpicker('refresh');

        $('#' + provinceId).append("<option value=''>-- 请选择 --</option>");
        $('#'+ cityId).append("<option value=''>-- 请选择 --</option>");
        // $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");

        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            async: false,
            success: function (result) {
                for (var i in result) {
                    $('#' + provinceId).append("<option value='" + result[i].PROVINCE_CODE + "'>" + result[i].PROVINCE_NAME + "</option>")
                }
                $('#'+ provinceId +' option').each(function () {
                    if ($(this).val() == province) {
                        $(this).attr("selected", true)
                    }
                })
            }
        });
        if (province !== undefined) {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + province,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }
                    $('#'+ cityId +' option').each(function () {
                        if ($(this).val() == city) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + city,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                    }
                    $('#'+ areaId).selectpicker('refresh');

                    $('#'+ areaId).selectpicker('val', area);

                    // $('#'+ areaId +' option').each(function () {
                    //     if ($(this).val() == area) {
                    //         $(this).attr("selected", true)
                    //     }
                    //
                    // })
                }
            });
        }

        $('#'+ provinceId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ cityId +' option').remove();
                    $('#'+ areaId +' option').remove();
                    $('#'+ cityId).append("<option value=''>-- 请选择 --</option>");
                    // $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }

                }
            });
        });

        $('#'+ cityId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ areaId +' option').remove();
                    // $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");
                    for (var i in result) {
                        $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                    }
                    $('#'+ areaId).selectpicker('refresh');
                }
            });
        });
    }

</script>

</body>

<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a th:href="@{/file/importReferencePriceData.xlsx}" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>

</html>