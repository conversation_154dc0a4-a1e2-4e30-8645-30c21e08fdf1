<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户运输线路指导价主列表')" />
</head>
<style>
    .bgw{
        background: #fff;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 70px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
    }
    .info button {
        color: inherit;
        background-color: transparent;
        -webkit-transition: all .5s;
        transition: all .5s;
    }
    .tables .fixed-table-container .selected {
        background-color: #1ab394 !important;
        color: #fff !important;
    }
    .fixed-table-pagination div.pagination {
        margin: 10px 0;
    }
    iframe{
        border: 0;
    }
</style>
<body class="gray-bg">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-5 bgw" style="height: calc(100vh - 4px);overflow-y: auto;">
       <!--    <div th:class="${transLineId == null?'col-sm-5 bgw colDR':'col-sm-12 bgw colDR'}" th:style="${transLineId == null?'':'display: block'}">-->
               <div class="container-div tables">
                   <div class="row">
                       <div class="col-sm-12 search-collapse" style="display: block;border-bottom: 1px #eee solid">
                           <form id="formId">
       
                               <div class="row">
                                   <div class=" col-sm-6 col-md-4">
                                       <div class="mt10">
                                           <div class="flex">
                                               <!-- <div class="flex_left">客户简称：</div> -->
                                               <div class="flex_right" style="padding: 0 5px">
                                                   <input name="custAbbr" id="custAbbr" placeholder="客户简称" class="form-control" type="text"
                                                           maxlength="50" aria-required="true">
                                               </div>
                                           </div>
                                       </div>
                                   </div>
       
                                   <div class=" col-sm-6 col-md-4">
                                       <div class="mt10">
                                           <div class="flex">
                                               <!-- <div class="flex_left">运营组：</div> -->
                                               <div class="flex_right" style="padding: 0 5px">
                                                   <select name="params[salesDept]" id="salesDept" class="form-control valid"
                                                           aria-invalid="false" required>
                                                       <option value="">--运营组--</option>
                                                       <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                                               th:text="${mapS.deptName}"></option>
                                                   </select>
                                               </div>
                                           </div>
                                       </div>
                                   </div>
       
                                   <div class=" col-sm-6 col-md-4">
                                       <div class="mt10">
                                           <div class="flex">
                                               <div class="flex_right" style="padding: 0 5px">
                                                   <select name="params[isExpired]" id="isExpired" class="form-control valid"
                                                           aria-invalid="false">
                                                       <option value="">--是否过期--</option>
                                                       <option value="0">未过期</option>
                                                       <option value="1">已过期</option>
                                                   </select>
                                               </div>
                                           </div>
                                       </div>
                                   </div>
       
                                   <div class=" col-sm-6 col-md-12">
                                       <div class="mt10">
                                           <div class="" style="width: 200px;padding: 0 10px">
                                               <div class="form-group">
                                                   <a class="btn btn-primary btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                                   <a class="btn btn-default btn-outline btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                               </div>
                                           </div>
                                       </div>
                                   </div>
                               </div>
       
                           </form>
                       </div>
                       <div class="btn-group-sm" id="toolbar" role="group">
                           <a class="btn btn-primary" onclick="importAllData()" shiro:hasPermission="tms:guidePrice:import">
                               <i class="fa fa-upload"></i> 导入
                           </a>
                           <div class="dropdown single disabled" style="display: inline-block;">
                               <button class="btn btn-primary dropdown-toggle" style="padding: 4px 12px;" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                   <i class="fa fa-download"></i> 导出
                                   <span class="caret"></span>
                               </button>
                               <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                                   <li>
                                       <a class="btn single disabled" onclick="exportExcel()" shiro:hasPermission="tms:guidePrice:export">
                                           <i class="fa fa-download"></i> 导出
                                       </a>
                                   </li>
                                   <li>
                                       <a class="btn" onclick="exportAll()" shiro:hasPermission="tms:guidePrice:export">
                                           <i class="fa fa-download"></i> 导出实际指导价
                                       </a>
                                   </li>
                                   <li>
                                       <a class="btn" onclick="$.table.exportExcel()" shiro:hasPermission="tms:guidePrice:export">
                                           <i class="fa fa-download"></i> 导出指导价模板
                                       </a>
                                   </li>
                               </ul>
                           </div>
                           <a class="btn btn-danger single disabled" shiro:hasPermission="tms:guidePrice:remove" onclick="remove()" >
                               <i class="fa fa-remove"></i> 删除
                           </a>
                       </div>
                       <div class="col-sm-12 select-table">
                           <table id="bootstrap-table" data-mobile-responsive="true"></table>
                       </div>
                   </div>
               </div>
           </div>
           <div class="col-sm-7" style="padding-right: 0;">
               <iframe style="height: calc(100vh - 4px);" width = "100%" id="iframe"></iframe>
           </div>
       </div>
    </div>
<div th:include="include :: footer"></div>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:guidePrice:edit')}]];
    var prefix = ctx + "tms/guide_price";
    var customerId = '1'
    var urls = ctx + "tms/guide_price/second_list?customerId=" + customerId

    $(function() {
        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                $.table.search();
            }
        });
        $('#iframe').attr("src", urls);

        $.provinces.init("startProvince", "startCity", "startArea", "", "", "");
        $.provinces.init("endProvince", "endCity", "endArea", "", "", "");
        var options = {
            url: prefix + "/list",
            modalName: "客户运输线路指导价",
            showToggle:false,
            showRefresh:false,
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            showColumns:false,
            showSearch: false,
            uniqueId: "customerId",
            clickToSelect: true,
            columns: [
                {
                    radio: true
                },
                // {
                //     title: '操作',
                //     align: 'center',
                //     formatter: function(value, row, index) {
                //         var actions = [];
                //         actions.push('<a class="btn  btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="secondList(\'' + row.customerId + '\')" title="修改"><i class="fa fa-edit" style="font-size: 15px;"></i></a> ');
                //         return actions.join('');
                //     }
                // },
                {
                    field : 'custAbbr',
                    title : '客户简称',
                    align: 'left'
                },
                {
                    field : 'salesDeptName',
                    title : '运营组',
                    align: 'left'
                },
                {
                    title: '线路条数',
                    field : 'countTransLine',
                    align: 'left',
                    formatter: function (value, row, index) {
                        return "<div style='color:#f7a54a'>" + value + "</div>";
                    }
                },
            ],
            //
            onClickRow(row,ele,field){
                var urls = ctx + "tms/guide_price/second_list?customerId=" + row.customerId
                $('#iframe').attr("src", urls);
            }
        };
        $.table.init(options);
    });


    function secondList(customerId) {
        $.modal.openTab("线路", ctx + "tms/guide_price/second_list/" + customerId);
    }

    /** 删除 */
    function remove() {
        var id = $.table.selectColumns("customerId");
        $.modal.confirm("确认要删除选中的数据吗?", function() {
            $.operate.submit(ctx + "tms/guide_price/remove", "post", "json", {"customerId": id.join()},function (){
                $('#iframe').attr("src", urls);
            });
        });
    }

    function searchPre(){
        //$.table.search()
    }
    //重置
    function reset() {
        // $.form.reset();
    }

    /**
     * 指导价导出
     */
    function exportExcel() {
        var customerId = $.table.selectColumns("customerId").join();
        var url = ctx + "report/guidePriceExport/"+customerId;
        $.modal.openTab("指导价导出", url);
    }

    function exportAll(){
        $.modal.confirm("确定导出所有指导价吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            $.post(ctx + "tms/guide_price/exportAll", null, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    /**
     * 导入数据
     */
    function importAllData() {
        layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '导入数据',
            content: $('#importTpl').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var index = layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData();
                formData.append("file", $('#file')[0].files[0]);
                formData.append("updateSupport", $("input[name='updateSupport']").is(':checked'));
                $.ajax({
                    url: ctx + "tms/guide_price/importData",
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code == web_status.WARNING) {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            layer.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    }
                });
            }
        });
    }
</script>
</body>

<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10" >
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                导入模板 ：
                &nbsp;	<a th:href="@{/file/importGuidePrice.xlsx}" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>