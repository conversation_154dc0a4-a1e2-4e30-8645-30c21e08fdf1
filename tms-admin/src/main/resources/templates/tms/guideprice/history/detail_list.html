<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('历史详情指导价')"/>
</head>
<style>
    .table-striped {
        height: calc(100% - 40px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 select-table table-striped toofoot pm">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">

    //参数
    var param = parent.layui.$('#historicalDetailParam').val();

    $(function () {
        var options = {
            url: ctx + "tms/guide_price/history/detail?" + param,
            showToggle: false,
            showSearch: false,
            showColumns: false,
            fixedColumns: true,
            showRefresh: false,
            fixedNumber: 0,
            columns: [
                {
                    title: '指导价',
                    align: 'left',
                    field: 'guidingPrice',
                    formatter: function (value, row, index) {
                        if (value != null) {
                            return value.toLocaleString('zh', {style: 'currency', currency: 'CNY'});
                        }else {
                            return '';
                        }
                    }

                },
                {
                    title:'状态',
                    align:'left',
                    field:'checkStatus',
                    formatter: function (value, row, index) {
                        switch (value) {
                            case 0:
                                return '<span class="label label-primary">待审核</span>'
                            case 1:
                                return '<span class="label label-warning">审核通过</span>';
                            case 2:
                                return '<span class="label label-coral">审核不通过</span>';
                            default:
                                break;
                        }
                    }
                },
                {
                    field : 'checkManName',
                    title : '审核人',
                    align: 'left'
                },
                {
                    title: '审核时间',
                    field: 'checkDate',
                    align: 'left'
                },
                {
                    field : 'regUserName',
                    title : '创建人',
                    align: 'left'
                },

                {
                    title:'创建时间',
                    align:'left',
                    field:'regDate'
                },
            ]
        };
        $.table.init(options);
    });

</script>
</body>
</html>