<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('历史指导价')" />
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId" class="form-horizontal">
                <input id="historicalDetailParam" type="hidden">

                <div class="row no-gutter">
                    <div class="col-sm-5">
                        <div class="col-md-4 col-sm-4" style="padding-left: 0">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input name="custAbbr" id="custAbbr" placeholder="客户简称" class="form-control" type="text"
                                           maxlength="50" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">车长：</label>-->
                                <div class="col-sm-12">
                                    <select name="carLen" placeholder="车长" id="carLen" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" data-none-selected-text="车长" multiple th:with="type=${@dict.getType('car_len')}">
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <div class="form-group">
                                <!--                            <label class="col-sm-4">车型：</label>-->
                                <div class="col-sm-12">
                                    <select name="carType" placeholder="车型" id="carType" class="form-control valid noselect2 selectpicker"
                                            aria-invalid="false" data-none-selected-text="车型" multiple th:with="type=${@dict.getType('car_type')}">
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="form-group">
                            <!--                            <label class="col-sm-2">要求提货日期：</label>-->
                            <div class="col-sm-12">
                                <input type="text" placeholder="创建开始日期" style="width: 45%; float: left;" class="form-control"
                                       id="regDateStart"  name="params[regDateStart]" autocomplete="off"
                                       th:value="${regDateStart}">
                                <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                                <input type="text" placeholder="创建截止日期" style="width: 45%; float: left;" class="form-control"
                                       id="regDateEnd"  name="params[regDateEnd]" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <div class="col-md-6 col-sm-6">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input name="corUserName" id="corUserName" placeholder="修改人名称" class="form-control" type="text"
                                           maxlength="50" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-6">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input name="checkManName" id="checkManName" placeholder="审核人名称" class="form-control" type="text"
                                           maxlength="50" aria-required="true">
                                </div>
                            </div>
                        </div>
                    </div>

<!--                    <div class="col-md-3 col-sm-6">-->
<!--                        <div class="form-group">-->
<!--                            <label class="col-sm-4">审核状态 ：</label>-->
<!--                            <div class="col-sm-8">-->
<!--                                <select name="checkStatus" id="checkStatus" class="form-control">-->
<!--                                    <option value="">&#45;&#45;请选择&#45;&#45;</option>-->
<!--                                    <option th:value="0" selected>待审核</option>-->
<!--                                    <option th:value="1">审核通过</option>-->
<!--                                    <option th:value="2">审核不通过</option>-->
<!--                                </select>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>

                <div class="row no-gutter" >
                    <div class="col-sm-4">
                        <div class="form-group">
                            <div class="col-sm-4">
                                <select  name="startProvinceId" id="deliProvinceId"  class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                            <div class="col-sm-4">
                                <select name="startCityId" id="deliCityId"  class="form-control valid" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-4">
                                <select name="startAreaId" id="deliAreaId"  class="form-control valid" aria-invalid="false"></select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-5">
                        <div class="form-group">

                            <div class="col-sm-1" onclick="changeDiv()">
                                <img th:src="@{/img/change.png}" style="width: 40px;height: 40px;display: block;margin: 0 auto">
                            </div>

                            <div class="col-sm-4">
                                    <!--                                <label class="col-sm-6">收货方地址：</label>-->
                                <select  name="endProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false">
                                </select>
                            </div>
                            <div class="col-sm-4">
                                <select name="endCityId" id="arriCityId" class="form-control" aria-invalid="false"></select>
                            </div>
                            <div class="col-sm-3">
                                <select name="endAreaId" id="arriAreaId" class="form-control" aria-invalid="false"></select>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-3">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
<!--            <a class="btn btn-info multiple disabled" onclick="check('1')" shiro:hasPermission="tms:check:guidePrice:check">-->
<!--                <i class="fa fa-check"></i> 通过-->
<!--            </a>-->
<!--            <a class="btn btn-warning multiple disabled" onclick="check('2')" shiro:hasPermission="tms:check:guidePrice:check">-->
<!--                <i class="fa fa-check"></i> 不通过-->
<!--            </a>-->
            <a class="btn btn-warning" onclick="exportExcel()" shiro:hasPermission="tms:guidePrice:history:export">
                <i class="fa fa-download"></i> 导出
            </a>

        </div>
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<th:block th:include="include :: bootstrap-select-js" />

<script th:inline="javascript">

    var editFlag = [[${@permission.hasPermi('tms:check:guidePrice:secondList')}]];
    var carLen = [[${@dict.getType('car_len')}]];//车长
    var carType = [[${@dict.getType('car_type')}]];//车辆类型

    var prefix = ctx + "tms/guide_price/history";

    $(function() {
        // 初始化省市区
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");

        //监听回车事件 回车搜索
        $(document).keyup(function(e){
            var key = e.which;
            if(key==13){
                searchPre()();
            }
        });

        //默认待审核条数
        $("#checkTypeTimes").val(0);
        var options = {
            url: prefix + "/list",
            modalName: "客户运输线路指导价",
            showToggle:false,
            showColumns:false,
            clickToSelect: true,
            uniqueId: "custGuidePriceId",
            columns: [
                {
                    checkbox: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        let data = JSON.stringify(row)
                        var actions = [];
                        actions.push("<a class='btn  btn-xs' href='javascript:void(0)' onclick='getHistoricalDetail(" + data +")' title='历史价格'><i class='fa fa-newspaper-o' style='font-size: 15px;'></i></a>");
                        return actions.join('');
                    }
                },
                {
                    title: '客户简称',
                    field : 'custAbbr',
                    align: 'left'
                },
                {
                    title: '审核状态',
                    align: 'left',
                    field: 'checkStatus',
                    formatter: function(value, item, index) {
                        if (item.checkStatus == 0) {
                            return '<span class="label label-default">待审核</span>';
                        }
                        if (item.checkStatus == 1) {
                            return '<span class="label label-primary">审核通过</span>';
                        }
                        if (item.checkStatus == 2) {
                            return '<span class="label label-danger">审核未通过</span>';
                        }
                    }
                },
                {
                    field : 'lineName',
                    title : '线路名称',
                    align: 'left'
                },
                {
                    field : 'carLen',
                    title: '车长',
                    align: 'left',
                    formatter: function status(value,row) {
                        return $.table.selectDictLabel(carLen, value);
                    }

                },
                {
                    field : 'carType',
                    title : '车型',
                    align: 'left',
                    formatter: function status(value, row, index) {
                        return $.table.selectDictLabel(carType, value);
                    }
                },
                {
                    field : 'guidingPrice',
                    title : '指导价',
                    align: 'left',
                },
                {
                    field: 'lastGuidingPrice',
                    title: '上次指导价',
                    align: 'left',
                },
                {
                    field : 'checkManName',
                    title : '审核人',
                    align: 'left'
                },
                {
                    title: '审核时间',
                    field: 'checkDate',
                    align: 'left'
                },
                {
                    field : 'corUserName',
                    title : '修改人',
                    align: 'left'
                },
                {
                    title: '修改时间',
                    field: 'corDate',
                    align: 'left'
                },


            ]
        };
        $.table.init(options);
    });

    /**
     * 日期插件
     */
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        var regDateStart = laydate.render({
            elem: '#regDateStart', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
        var regDateEnd = laydate.render({
            elem: '#regDateEnd', //指定元素
            isInitValue : false,
            trigger: 'click',
            type: 'date'
        });
    });


    /**
     * 搜索
     */
    function searchPre() {
        var data = {};
        data.params = new Map();

        data.carLen = $.common.join($('#carLen').selectpicker('val'));
        data.carType = $.common.join($('#carType').selectpicker('val'));
        // data.transCode = $.common.join($('#transCode').selectpicker('val'));
        // data.vbillstatus = $.common.join($('#vbillstatus').selectpicker('val'));
        // data.salesDept = $.common.join($('#salesDept').selectpicker('val'));

        // data.params.receivableWriteOffStatusList = $.common.join($('#receivableWriteOffStatusList').selectpicker('val'));

        $.table.search('formId', data);
    }

    /**
     * 重置
     */
    function resetPre() {
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId");
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId");
        $(".selectpicker").selectpicker('deselectAll');
        $("#formId")[0].reset();
        searchPre();
    }
    function changeDiv(){
        var deliProvinceId= $('#deliProvinceId').val()
        var arriProvinceId= $('#arriProvinceId').val()
        var deliCityId= $('#deliCityId').val()
        var arriCityId= $('#arriCityId').val()
        var deliAreaId= $('#deliAreaId').val()
        var arriAreaId= $('#arriAreaId').val()
        $.provinces.init("deliProvinceId","deliCityId","deliAreaId",arriProvinceId,arriCityId,arriAreaId);
        $.provinces.init("arriProvinceId","arriCityId","arriAreaId",deliProvinceId,deliCityId,deliAreaId);
        searchPre();
    }
    /**
     * 指导价审核
     */
    function check(status) {
        var ids = $.table.selectColumns("guidePriceDetailId").join();
        var url = prefix + "/checkStatus?guidePriceDetailIds="+ids+"&checkStatus="+status;
        $.modal.confirm("确定提交吗？", function () {
            $.operate.submit(url, "get", "json", "", function (){

            });
        });
    }


    /**
     * 获取历史价格
     * @param id
     */
    function getHistoricalDetail(data){
        console.log(data)
        console.log(data.lastGuidingPrice)

        var carType = data.carType
        var carLen = data.carLen
        var custGuidePriceId = data.custGuidePriceId
        //将参数传递给弹窗
        var param = "carType=" + carType + "&carLen=" + carLen + "&custGuidePriceId=" + custGuidePriceId
        $("#historicalDetailParam").val(param);

        var url = prefix + "/detail";
        layer.open({
            type: 2,
            area: ['85%', '100%'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '历史价格',
            content: url,
            btn: ['关闭'],
            // 弹层外区域关闭
            shadeClose: true,
            cancel: function(index) {
                return true;
            }
        });
    }

    function exportExcel(){

        $.modal.confirm("确定导出吗？", function() {
            $.modal.loading("正在导出数据，请稍后...");
            var data = $("#formId").serializeArray();

            data.params = new Map();

            data.carLen = $.common.join($('#carLen').selectpicker('val'));
            data.carType = $.common.join($('#carType').selectpicker('val'));

            $.post(ctx + "tms/guide_price/history/export", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }


    //验证字符串是否是数字
    function checkNumber(val) {
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val)){
            return true;
        }
        else{
            return false;
        }
    }

</script>
</body>
</html>