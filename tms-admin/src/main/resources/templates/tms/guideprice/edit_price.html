<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('修改指导价')"/>
</head>
<body>
<div class="form-content">
    <form id="form-edit-price" class="form-horizontal" novalidate="novalidate">
        <input id="custGuidePriceId" th:value="${custGuidePriceId}" type="hidden">
        <div class="panel-body">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           href="tabs_panels.html#collapseOne">指导价</a>
                        <nobr>&nbsp&nbsp&nbsp&nbsp&nbsp客户名称：[[${custGuidePrice.custName}]]</nobr>
                        <nobr>&nbsp&nbsp&nbsp&nbsp&nbsp线路：[[${custGuidePrice.lineName}]]</nobr>
                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="collapseOne">
                    <div class="panel-body">
                        <!-- begin-->
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" id="infoTabEight" class="custom-tab table">
                                <thead>
                                <tr>
<!--                                    <th style="width: 3%;">-->
<!--                                        <a class="collapse-link add-alink" onclick="insertRowEight()" title="新增行">+</a>-->
<!--                                    </th>-->
                                    <th style="width: 10%;">计价方式</th>
                                    <th style="width: 8%;">车长</th>
                                    <th style="width: 12%;">车型</th>
                                    <th style="width: 10%;">指导价</th>
                                    <th style="width: 10%;">开始时间</th>
                                    <th style="width: 10%;">结束时间</th>
                                    <th style="width: 25%;">备注</th>
<!--                                    <th style="width: 25%;">操作</th>-->
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="mapS,status:${guidePriceDetailList}">
                                    <input type="hidden" id="guideLineSize" th:value="${status.size}">
<!--                                    <td>-->
<!--                                        <a class="close-link del-alink" onclick="removeRowEight(this,0)"-->
<!--                                           title="删除选择行">-</a>-->
<!--                                        <input type="hidden" th:id="isCmt_+${status.index}" value="1">-->
<!--                                        <input type="hidden" th:id="guidePriceDetailId_+${status.index}"-->
<!--                                               th:value="${mapS.guidePriceDetailId}">-->
<!--                                    </td>-->
                                    <td>
                                        <select th:name="|custGuidancepcList[${status.index}].pricingMethod|"
                                                th:id="pricingMethod_+${status.index}"
                                                class="form-control valid" th:data-old="${mapS.pricingMethod}"
                                                th:onchange="|isEdit(${status.index})|"
                                                th:with="type=${pricingMethodList}" disabled >
                                            <option value="">-- 请选择 --</option>
                                            <option th:each="item:${type}" th:text="${item.context}"
                                                    th:value="${item.value}"
                                                    th:selected="${item.value==mapS.pricingMethod}"></option>
                                        </select>
                                    </td>

                                    <td>
                                        <select th:name="|custGuidancepcList[${status.index}].carLen|"
                                                th:id="carLen_+${status.index}"
                                                class="form-control valid" th:data-old="${mapS.carLen}"
                                                th:onchange="|isEdit(${status.index})|"
                                                th:with="type=${@dict.getType('car_len')}" disabled>
                                            <option value="">-- 请选择 --</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"
                                                    th:selected="${dict.dictValue==''+mapS.carLen}"></option>
                                        </select>
                                    </td>
                                    <td>
                                        <select th:name="|custGuidancepcList[${status.index}].carType|"
                                                th:id="carType_+${status.index}"
                                                class="form-control valid" th:data-old="${mapS.carType}"
                                                th:onchange="|isEdit(${status.index})|"
                                                th:with="type=${@dict.getType('car_type')}" disabled>
                                            <option value="">-- 请选择 --</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}"
                                                    th:selected="${dict.dictValue==''+mapS.carType}"></option>
                                        </select>
                                    </td>
                                    <td><input th:name="|custGuidancepcList[${status.index}].guidingPrice|"
                                               th:id="guidingPrice_+${status.index}" class=" form-control"
                                               th:value="${mapS.guidingPrice}" th:data-old="${mapS.guidingPrice}"
                                               th:oninput="|$.numberUtil.onlyNumber(this);isEdit(${status.index})|"
                                               autocomplete="off" type="text" min="0" maxlength="10" disabled></td>
                                    <td>
                                        <input th:name="|custGuidancepcList[${status.index}].startDate|" type="text"
                                               th:id="startDate_+${status.index}" class="form-control"
                                               th:data-old="${#dates.format(mapS.startDate, 'yyyy-MM-dd')}"
                                               th:value="${#dates.format(mapS.startDate, 'yyyy-MM-dd')}"
                                               placeholder="开始时间" autocomplete="off" disabled>
                                    </td>
                                    <td>
                                        <input th:name="|custGuidancepcList[${status.index}].endDate|"
                                               th:id="endDate_+${status.index}" class="form-control" type="text"
                                               th:data-old="${#dates.format(mapS.endDate, 'yyyy-MM-dd')}"
                                               th:value="${#dates.format(mapS.endDate, 'yyyy-MM-dd')}"
                                               id="endDate_0" placeholder="结束时间" autocomplete="off" disabled>
                                    </td>
                                    <td>
                                        <textarea type="text" maxlength="500" class="form-control "
                                                  th:name="|custGuidancepcList[${status.index}].remark|"
                                                  th:id="remark_+${status.index}" th:text="${mapS.remark}" disabled>
                                        </textarea>
                                    </td>

<!--                                    <td>-->
<!--                                        <button class="btn btn-primary btn-sm" type="button" disabled-->
<!--                                                th:id="btn_+${status.index}"-->
<!--                                                th:onclick="|addPrice(${status.index})|"><i class="fa fa-check"></i>&nbsp;提交-->
<!--                                        </button>-->
<!--                                        <button class="btn btn-default btn-sm" type="button"-->
<!--                                                th:id="check_+${status.index}" th:value="${mapS.guidePriceDetailId}"-->
<!--                                                th:onclick="|checkRecord(this.value)|"><i class="fa fa-list"></i>&nbsp;审核记录-->
<!--                                        </button>-->
<!--                                    </td>-->
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--end-->
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-body" th:each="mapS,status:${guidePriceDetailList}" th:if="${mapS.pricingMethod == 1}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion"
                           th:href="|tabs_panels.html#guide${status.index}|">整车实际成交运费</a>
                        <nobr th:each="dict : ${@dict.getType('car_len')}" th:if="${dict.dictValue} == ${mapS.carLen}"
                              th:text="|${dict.dictLabel}米|"></nobr>
                        <nobr th:each="dict : ${@dict.getType('car_type')}" th:if="${dict.dictValue} == ${mapS.carType}"
                              th:text="${dict.dictLabel}"></nobr>
                        <input type="hidden" id="guidePriceDetailListSize" th:value="${status.size}">
                    </h4>
                </div>
                <div class="panel-collapse collapse in" th:id="|guide${status.index}|">
                    <div class="panel-body">
                        <div class="fixed-table-body" style="margin: 0px -5px;">
                            <table border="0" class="custom-tab table">
                                <thead>
                                <tr>
                                    <th style="width: 8%;">时间</th>
                                    <th style="width: 10%;" th:each="mapS,status:${years}" th:text="${mapS}"></th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>成交次数|平均价</td>
                                    <td th:each="dict,status:${actualSixMonth}"
                                        th:if="${mapS.carLen == dict.carLen && mapS.carType == dict.carType}">
                                        <a class="btn  btn-xs" href="javascript:void(0)" title="明细"
                                           th:onclick="detail([[${dict.carType}]],[[${dict.carLen}]],[[${dict.years}]])"
                                           th:if="${dict.TIMES != 0}">
                                            <i class="fa fa fa-list" style="font-size: 15px;"></i>
                                        </a>
                                        <nobr th:text="|${dict.TIMES}次|"></nobr>
                                        <nobr>|</nobr>
                                        <nobr th:text="￥+${#numbers.formatDecimal(dict.ACTUAL_TRANS_FEE,1,'COMMA',2,'POINT')}"></nobr>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <input type="hidden" id="param">
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </form>
</div>

<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var guideLineIndex = $("#guideLineSize").val() - 1;
    var guidePriceIndex = $("#guidePriceDetailListSize").val() - 0;


    $(function () {
        $('#collapseOne').collapse('show');
        for (var i = 0; i <= guideLineIndex; i++) {
            initDate(i);
        }
        for (var i = 0; i < guidePriceIndex; i++) {
            $('#guide' + i).collapse('show');
        }


        //加载审核记录 根据状态进行颜色变化
        for (var i = 0; i <= guideLineIndex; i++) {
            var guidePriceDetailId = $('#guidePriceDetailId_' + i).val();
            var data = {guidePriceDetailId: guidePriceDetailId};
            $.ajax({
                url: ctx + "tms/guide_price/checkRecordColorChange",
                type: 'post',
                dataType: "json",
                data: data,
                async: false, //设为同步
                success: function (result) {
                    if (result.checkStatus === 0) {
                        //待审核
                    } else if (result.checkStatus === 1) {
                        $('#check_' + i).removeClass("btn-default");
                        $('#check_' + i).addClass("btn-primary");
                    } else if (result.checkStatus === 2) {
                        $('#check_' + i).removeClass("btn-default");
                        $('#check_' + i).addClass("btn-danger");
                    }
                }
            });
        }
        ;
    });





    function initDate(ind) {
        var i = ind;
        /**
         * 日期插件
         */
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate_' + i, //指定元素
                format: "yyyy-MM-dd",
                trigger: 'click',
                type: 'date',
                done: function (value, date, endDate) {
                    $("#startDate_" + i).val(value);
                    isEdit(i);
                }
            });
            laydate.render({
                elem: '#endDate_' + i, //指定元素
                trigger: 'click',
                format: "yyyy-MM-dd",
                type: 'date',
                done: function (value, date, endDate) {
                    $("#endDate_" + i).val(value);
                    isEdit(i);
                }
            });
        });
    }

    // 计价方式
    var pricingMethodList = [[${pricingMethodList}]];
    var pricingMethodHTML = '';
    for (var i = 0; i < pricingMethodList.length; i++) {
        pricingMethodHTML += '<option  value=' + pricingMethodList[i].value + ' >' + pricingMethodList[i].context + '</option>'
    }

    // 车长
    var carLenHTML = [[${@dict.getType('car_len')}]];
    var clHTML = '';
    for (var i = 0; i < carLenHTML.length; i++) {
        clHTML += '<option  value=' + carLenHTML[i].dictValue + ' >' + carLenHTML[i].dictLabel + '</option>'
    }
    // 车型
    var carTypeHTML = [[${@dict.getType('car_type')}]];
    var ctHTML = '';
    for (var i = 0; i < carTypeHTML.length; i++) {
        ctHTML += '<option  value=' + carTypeHTML[i].dictValue + ' >' + carTypeHTML[i].dictLabel + '</option>'
    }


    /** 新增行 */
    function insertRowEight() {
        guideLineIndex += 1;
        var guideLineName = 'guideLineName_' + guideLineIndex;
        var trTtml = '<tr>'
            + '<td><a class="close-link del-alink" onclick="removeRowEight(this,' + guideLineIndex
            + ')" title="删除选择行">-</a>'
            + '<input type="hidden" id="isCmt_' + guideLineIndex + '" value="0">'
            + '<input type="hidden" id="guidePriceDetailId_' + guideLineIndex + '" value=""></td>'

            + '<td>'
            + '<select name="custGuidancepcList[' + guideLineIndex + '].pricingMethod" id="pricingMethod_'
            + guideLineIndex + '"onchange="isEdit(' + guideLineIndex + ')" class="form-control" data-old="" disabled>'
            + '<option value="1" selected="selected">整车</option></select></td>'

            + '<td><select name="custGuidancepcList[' + guideLineIndex + '].carLen" id="carLen_' + guideLineIndex
            + '" onchange="isEdit(' + guideLineIndex + ')" data-old="" class=" form-control" autocomplete="off" >'
            + '<option value="" selected="selected">-- 请选择 --</option>' + clHTML + '   </select></td>'

            + '<td><select name="custGuidancepcList[' + guideLineIndex + '].carType" id="carType_' + guideLineIndex
            + '" onchange="isEdit(' + guideLineIndex + ')" data-old="" class=" form-control" autocomplete="off" >'
            + '<option value="" selected="selected">-- 请选择 --</option>' + ctHTML + '   </select></td>'

            + '<td><input name="custGuidancepcList[' + guideLineIndex + '].guidingPrice" id="guidingPrice_'
            + guideLineIndex + '" data-old="" ' + 'class=" form-control" autocomplete="off"  type="text" min="0" '
            + 'oninput="$.numberUtil.onlyNumber(this);isEdit(' + guideLineIndex + ')" maxlength="10" ></td>'

            + ' <td> <input type="text" class="form-control" id="startDate_' + guideLineIndex
            + '" name="custGuidancepcList[' + guideLineIndex + '].startDate" data-old="" placeholder="开始时间" '
            + 'autocomplete="off" readonly>' + '</td>'

            + '<td><input type="text" class="form-control" id="endDate_' + guideLineIndex
            + '" name="custGuidancepcList[' + guideLineIndex + '].endDate" data-old="" placeholder="结束时间" '
            + 'autocomplete="off" readonly></td>'

            + '<td><textarea type="text" name="custGuidancepcList[' + guideLineIndex + '].remark" id="remark_'
            + guideLineIndex + '" class="form-control "></textarea></td>'

            + '<td><button class="btn btn-warning btn-sm" type="button" onclick="addPrice(' + guideLineIndex
            + ')" id="btn_' + guideLineIndex + '"><i class="fa fa-check"></i>&nbsp;提交</button></td>'

            + '</tr>';
        $("#infoTabEight tbody").append(trTtml);

        var i = guideLineIndex;
        layui.use('laydate', function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: '#startDate_' + i, //指定元素
                trigger: 'click',
                format: "yyyy-MM-dd",
                type: 'date',
                done: function (value, date, endDate) {
                    $("#startDate_" + i).val(value);
                    isEdit(i);
                }
            });
            laydate.render({
                elem: '#endDate_' + i, //指定元素
                trigger: 'click',
                format: "yyyy-MM-dd",
                type: 'date',
                done: function (value, date, endDate) {
                    $("#endDate_" + i).val(value);
                    isEdit(i);
                }
            });
        });
    }

    /** 删除指定表格行 */
    function removeRowEight(obj, index) {
        //后台是否有相关数据 0 没用 1有
        var isCmt = $("#isCmt_" + index).val();
        if (isCmt == 0) {
            $("#infoTabEight tbody").find(obj).closest("tr").remove();
        } else {
            var guidePriceDetailId = $("#guidePriceDetailId_" + index).val();
            $.modal.confirm("确定删除这条指导价吗？", function () {
                $.ajax({
                    url: ctx + "tms/guide_price/remove_price",
                    type: "post",
                    dataType: "json",
                    data: {"id": guidePriceDetailId},
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $("#infoTabEight tbody").find(obj).closest("tr").remove();
                        $.modal.msgSuccess(result.msg);
                        $.modal.closeLoading();
                    }
                });
            });
        }

    }

    /**
     * 修改添加
     */
    function addPrice(ind) {
        //车长
        var pricingMethod = $("#pricingMethod_" + ind).val();
        //车长
        var carLen = $("#carLen_" + ind).val();
        //车型
        var carType = $("#carType_" + ind).val();
        //指导价
        var guidingPrice = $("#guidingPrice_" + ind).val();
        //开始日期
        var startDate = $("#startDate_" + ind).val();
        //结束日期
        var endDate = $("#endDate_" + ind).val();
        //备注
        var remark = $("#remark_" + ind).val();
        //主表id
        var custGuidePriceId = $("#custGuidePriceId").val();
        //明细表id
        var guidePriceDetailId = $("#guidePriceDetailId_" + ind).val();

        //校验
        if (pricingMethod === '') {
            $.modal.alertWarning("请选择计价方式！");
            return;
        }
        if (pricingMethod === '1') {
            if (carLen === '') {
                $.modal.alertWarning("请选择车长！");
                return;
            }
            if (carType === '') {
                $.modal.alertWarning("请选择车型！");
                return;
            }
        }
        if (guidingPrice === '') {
            $.modal.alertWarning("请输入指导价！");
            return;
        }
        if (startDate !== '' && endDate !== '' && endDate < startDate) {
            $.modal.alertWarning("结束时间不能小于开始时间！");
            return;
        }

        var data = {
            "custGuidePriceId": custGuidePriceId, "pricingMethod": pricingMethod,
            "guidePriceDetailId": guidePriceDetailId, "carLen": carLen, "carType": carType,
            "guidingPrice": guidingPrice, "startDate": startDate, "endDate": endDate,"remark": remark
        };

        $.modal.confirm("确定提交这条指导价吗？", function () {
            $.ajax({
                url: ctx + "tms/guide_price/add_price",
                type: "post",
                dataType: "json",
                data: data,
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    if (result.code === 0) {
                        $("#guidePriceDetailId_" + ind).val(result.data);
                        //代表后台已经存在该条数据
                        $("#isCmt_" + ind).val(1);
                        //将按钮设为失效
                        $("#btn_" + ind).removeClass("btn-warning");
                        $("#btn_" + ind).addClass("btn-primary");
                        $("#btn_" + ind).attr("disabled", true);
                        //将旧值存入 data-old中
                        $("#pricingMethod_" + ind).data("old", pricingMethod);
                        $("#carLen_" + ind).data("old", carLen);
                        $("#carType_" + ind).data("old", carType);
                        $("#guidingPrice_" + ind).data("old", guidingPrice);
                        $("#startDate_" + ind).data("old", startDate);
                        $("#endDate_" + ind).data("old", endDate);
                        $("#remark_" + ind).data("old", remark);

                        $.modal.msgSuccess(result.msg);
                        $.modal.closeLoading();
                        window.location.reload();
                    } else {
                        $.modal.alertError(result.msg);
                        $.modal.closeLoading();
                    }
                }
            });
        });
    }

    /**
     * 判断值是否发生过改变
     */
    function isEdit(ind) {
        //计价方式
        var pricingMethodOld = $("#pricingMethod_" + ind).data("old") === undefined ? "" : $("#pricingMethod_" + ind).data("old");
        var pricingMethod = $("#pricingMethod_" + ind).val();
        //车长
        var carLenOld = $("#carLen_" + ind).data("old") === undefined ? "" : $("#carLen_" + ind).data("old");
        var carLen = $("#carLen_" + ind).val();
        //车型
        var carTypeOld = $("#carType_" + ind).data("old") === undefined ? "" : $("#carType_" + ind).data("old");
        var carType = $("#carType_" + ind).val();
        //指导价
        var guidingPriceOld = $("#guidingPrice_" + ind).data("old") === undefined ? "" : $("#guidingPrice_" + ind).data("old");
        var guidingPrice = $("#guidingPrice_" + ind).val();

        // $("#guidingPrice_" + ind).attr("data-old", "");
        //开始日期
        var startDateOld = $("#startDate_" + ind).data("old") === undefined ? "" : $("#startDate_" + ind).data("old");
        var startDate = $("#startDate_" + ind).val();
        //结束日期
        var endDateOld = $("#endDate_" + ind).data("old") === undefined ? "" : $("#endDate_" + ind).data("old");
        var endDate = $("#endDate_" + ind).val();

        //判断各个值，是否和旧值不相等
        var isNotEqu = pricingMethodOld !== pricingMethod || carLenOld !== carLen || carTypeOld !== carType
            || guidingPriceOld !== guidingPrice || startDateOld !== startDate || endDateOld !== endDate;
        //是否提交过
        var isCmt = $("#isCmt_" + ind).val();

        if (!isNotEqu && isCmt == 1) {
            $("#btn_" + ind).removeClass("btn-warning");
            $("#btn_" + ind).addClass("btn-primary");
            $("#btn_" + ind).attr("disabled", true);
        } else {
            $("#btn_" + ind).removeClass("btn-primary");
            $("#btn_" + ind).addClass("btn-warning");
            $("#btn_" + ind).attr("disabled", false);
        }
    }


    /**
     * 详情
     * @param carType
     * @param carLen
     * @param years
     */
    function detail(carType, carLen, years) {
        var transLine = [[${transLine}]];
        var startProvinceId = transLine.startProvinceId;
        var startCityId = transLine.startCityId;
        var startAreaId = transLine.startAreaId;
        var endProvinceId = transLine.endProvinceId;
        var endCityId = transLine.endCityId;
        var endAreaId = transLine.endAreaId;
        var transType = transLine.transType;

        var customerId = [[${customerId}]];

        //将参数传递给弹窗
        var param = "carType=" + carType + "&carLen=" + carLen + "&years=" + years
            + "&startProvinceId=" + startProvinceId + "&startCityId=" + startCityId + "&startAreaId=" + startAreaId
            + "&endProvinceId=" + endProvinceId + "&endCityId=" + endCityId + "&endAreaId=" + endAreaId
            + "&transType=" + transType + "&customerId=" + customerId;
        $("#param").val(param);

        layer.open({
            type: 2,
            area: ['80%', '90%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "成交记录",
            content: ctx + "tms/guide_price/price_detail?" + param,
            btn: ['关闭'],
            shadeClose: true,            // 弹层外区域关闭
            cancel: function (index) {
                return true;
            }
        });
    }

    /**
     * 审核记录
     * @param id 指导价明细id
     */
    function checkRecord(id) {
        layer.open({
            type: 2,
            area: ['80%', '90%'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: "审核记录",
            content: ctx + "tms/guide_price/check_record?id=" + id,
            btn: ['关闭'],
            shadeClose: true,            // 弹层外区域关闭
            cancel: function (index) {
                return true;
            }
        });

    }

</script>
</body>

</html>
