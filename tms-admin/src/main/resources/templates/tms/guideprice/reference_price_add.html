<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增指导价')"/>
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<style>
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 100px;
        line-height: 26px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        /*line-height: 26px;*/
    }
</style>
<body>
<div class="form-content">
    <form id="form-invoice-add" class="form-horizontal" novalidate="novalidate">
        <div class="row">

            <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>提货省市区：</span></label>
                    <div class="flex_right">
                        <div class="form-group">
                            <div class="col-sm-4 col-xs-4">
                                <select  name="deliProvinceId" id="deliProvinceId" class="form-control valid" aria-invalid="false" required>
                                </select>
                            </div>
                            <div class="col-sm-4 col-xs-4">
                                <select name="deliCityId" id="deliCityId" class="form-control valid" aria-invalid="false" required></select>
                            </div>
                            <div class="col-sm-4 col-xs-4">
<!--                                <select name="deliAreaId" id="deliAreaId" class="form-control valid" aria-invalid="false" required></select>-->
                                <select name="deliAreaId" id="deliAreaId" class="form-control selectpicker valid"
                                        aria-invalid="false" multiple placeholder="请选择" data-none-selected-text="请选择">
                                </select>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>到货省市区：</span></label>
                    <div class="flex_right">
                        <div class="form-group">
                            <div class="col-sm-4 col-xs-4">
                                <select  name="arriProvinceId" id="arriProvinceId"  class="form-control valid" aria-invalid="false" required>
                                </select>
                            </div>
                            <div class="col-sm-4 col-xs-4">
                                <select name="arriCityId" id="arriCityId" class="form-control valid" aria-invalid="false" required></select>
                            </div>
                            <div class="col-sm-4 col-xs-4">
<!--                                <select name="arriAreaId" id="arriAreaId" class="form-control valid" aria-invalid="false" required></select>-->
                                <select name="arriAreaId" id="arriAreaId" class="form-control selectpicker valid"
                                        aria-invalid="false" multiple placeholder="请选择" data-none-selected-text="请选择">
                                </select>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 col-sm-6 col-xs-6">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>车长：</span></label>
                    <div class="flex_right">
                        <select id="carLen" name="carLen" class="form-control noselect2 selectpicker" aria-invalid="false" required
                                th:with="type=${@dict.getType('car_len')}">
                            <option value="">-- 请选择 --</option>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                        <label id="carLen-error" class="error" for="carLen"></label>

                    </div>
                </div>
            </div>
            <div class="col-md-6 col-sm-6 col-xs-6">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>车型：</span></label>
                    <div class="flex_right">
                        <select id="carType" name="carType" class="form-control noselect2 selectpicker" multiple aria-invalid="false" data-none-selected-text="请选择" required
                                th:with="type=${@dict.getType('car_type')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                        <label id="carType-error" class="error" for="carType"></label>

                    </div>
                </div>
            </div>

        </div>
        <div class="row">

            <div class="col-md-6 col-sm-6 col-xs-6">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>指导价：</span></label>
                    <div class="flex_right">
                        <input name="price" id="price" placeholder="指导价" class="form-control"  oninput="$.numberUtil.onlyNumberTwoDecimal(this);" type="text" autocomplete="off" aria-required="true" required>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-sm-6 col-xs-6">
                <div class="flex">
                    <label class="flex_left"><span class="fcff">*</span>  <span>价格类型：</span></label>
                    <div class="flex_right">
                        <select name="priceType" id="priceType" class="form-control" required>
                            <option value="">-- 请选择 --</option>
                            <option th:value="0">普货</option>
                            <option th:value="1">危险品</option>
                        </select>
                    </div>
                </div>
            </div>

        </div>
    </form>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "tms/reference_price";
    
    $(function () {
        // 表单验证
        $("#form-invoice-add").validate({
            focusCleanup: true
        });

        init("deliProvinceId","deliCityId","deliAreaId");
        init("arriProvinceId","arriCityId","arriAreaId");
    })

    /**
     * 提交
     */
     function submitHandler() {
        if ($.validate.form()) {
            var data = $("#form-invoice-add").serializeArray();
            $.operate.save(prefix + "/add", data);
        }
    }

    function init(provinceId, cityId, areaId, province, city, area) {
        $('#'+ provinceId +' option').remove();
        $('#'+ cityId +' option').remove();
        $('#'+ areaId +' option').remove();
        $('#' + provinceId).append("<option value=''>-- 请选择 --</option>");
        $('#'+ cityId).append("<option value=''>-- 请选择 --</option>");
        // $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");

        $.ajax({
            type: "get",
            url: ctx + "province/city?method=0&code=0",
            async: false,
            success: function (result) {
                for (var i in result) {
                    $('#' + provinceId).append("<option value='" + result[i].PROVINCE_CODE + "'>" + result[i].PROVINCE_NAME + "</option>")
                }
                $('#'+ provinceId +' option').each(function () {
                    if ($(this).val() == province) {
                        $(this).attr("selected", true)
                    }
                })
            }
        });
        if (province !== undefined) {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + province,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }
                    $('#'+ cityId +' option').each(function () {
                        if ($(this).val() == city) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + city,
                async: false,
                success: function (result) {
                    for (var i in result) {
                        $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                    }
                    $('#'+ areaId +' option').each(function () {
                        if ($(this).val() == area) {
                            $(this).attr("selected", true)
                        }

                    })
                }
            });
        }

        $('#'+ provinceId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=1&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ cityId +' option').remove();
                    $('#'+ areaId +' option').remove();
                    $('#'+ cityId).append("<option value=''>-- 请选择 --</option>");
                    // $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");
                    for (var i in result) {
                        $('#'+ cityId).append("<option value='" + result[i].CITY_CODE + "'>" + result[i].CITY_NAME + "</option>")
                    }

                }
            });
        });

        $('#'+ cityId).change(function () {
            $.ajax({
                type: "get",
                url: ctx + "province/city?method=2&code=" + $(this).val(),
                success: function (result) {
                    $('#'+ areaId +' option').remove();
                    // $('#'+ areaId).append("<option value=''>-- 请选择 --</option>");
                    for (var i in result) {
                        $('#'+ areaId).append("<option value='" + result[i].AREA_CODE + "'>" + result[i].AREA_NAME + "</option>")
                    }
                    $('#'+ areaId).selectpicker('refresh');
                }
            });
        });
    }

</script>

</body>

</html>