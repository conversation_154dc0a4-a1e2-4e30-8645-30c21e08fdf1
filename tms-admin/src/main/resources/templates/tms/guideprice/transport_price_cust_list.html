<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('客户运输线路指导价主列表')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: bootstrap-editable-css" />

</head>
<style>
    .table-striped {
        height: calc(100vh - 196px);
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 70px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
    }
    .info button {
        color: inherit;
        background-color: transparent;
        -webkit-transition: all .5s;
        transition: all .5s;
    }
    .over{
        overflow: hidden;
    }
    .fl{
        float: left;
    }
    .bgw{
        background: #fff;
    }
    /* .fixed-table-pagination div.pagination{
        display:none;
    } */
    .borR{
        padding-right: 5px !important;
    }
    .borR:after {
        content: '';
        position: absolute;
        top: 20px;
        right: 2px;
        height: calc(100% - 40px);
        width: 1px;
        background-color: #e7eaec;
    }
    .borL{
        padding-left: 5px !important;
    }
    .lf{
        float: right;
    }
    .A94{
        color: #a94442;
    }
</style>
<body class="gray-bg">
<div class="container-fluid">
    <div class="row">

        <div class="col-sm-12 bgw">
            <div class="col-sm-12 search-collapse" style="display: block">
                <form id="role-form" class="form-horizontal">
                    <input type="hidden" name="customerId" th:value="${customerId}">
                        <row class="row no-gutter">
                            <div class="col-sm-2">
                                <select name="salesDept" id="salesDept" class="form-control valid noselect2 selectpicker"
                                                aria-invalid="false" data-none-selected-text="运营组" multiple>
                                            <!-- <option value="">-- 运营组 --</option> -->
                                            <option th:each="mapS,status:${salesDept}" th:value="${mapS.deptId}"
                                                    th:text="${mapS.deptName}"></option>
                                        </select>
                            </div>
                            <div class="col-sm-2">
                                <div class="form-group">
                                    <div class="flex">
                                        <div class="flex_right" style="padding: 0 5px">
                                            <input name="custAbbr" id="custAbbr" placeholder="客户简称" class="form-control" type="text"
                                            maxlength="50" aria-required="true">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- <div class="col-sm-2">
        
                                <div class="form-group input-group">
                                    <div class="col-sm-12 input-group date">
                                        <input type="text" placeholder="调度起始时间"  class="form-control"
                                        id="datetimepicker"  name="params[datetimepicker]" autocomplete="off"
                                        th:value="${datetimepicker}">
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
        
                            </div> -->

                            <div class="col-md-2 ">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <select name="carLen" placeholder="车长" id="carLen" class="form-control valid noselect2 selectpicker"
                                                aria-invalid="false" data-none-selected-text="车长" multiple th:with="type=${@dict.getType('car_len')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <select name="carType" placeholder="车型" id="carType" class="form-control valid noselect2 selectpicker"
                                                aria-invalid="false" data-none-selected-text="车型" multiple th:with="type=${@dict.getType('car_type')}">
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-2">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <select name="isReview" placeholder="是否已阅" id="isReview" class="form-control valid noselect2 selectpicker"
                                                aria-invalid="false" data-none-selected-text="是否已阅">
                                            <option value=""></option>
                                            <option value="0">未阅</option>
                                            <option value="1">已阅</option>
                                        </select>

                                        <!--                                        <select name="isReview" id="isReview" class="form-control valid">-->
<!--                                            <option>&#45;&#45;是否已阅&#45;&#45;</option>-->
<!--                                            <option value="0">未阅</option>-->
<!--                                            <option value="1">已阅</option>-->
<!--                                        </select>-->
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-2">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <select name="isImport" placeholder="是否导入" id="isImport" class="form-control valid noselect2 selectpicker"
                                                aria-invalid="false" data-none-selected-text="是否导入">
                                            <option value=""></option>
                                            <option value="0">否</option>
                                            <option value="1">是</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                        </row>
                        <row>
                            <div class="col-xs-10 col-sm-10 col-md-10 ">
                                <div class="flex" style="width: 48%;display: inline-block;">
                                    <!-- <label class="flex_left">提货地址：</label> -->
                                    <div class="flex_right over">
                                        <div class="fl" style="width: 33%;padding: 0 5px 0 0;box-sizing: border-box">
                                            <select id="startProvince" name="startProvinceId"
                                                    class="form-control valid" aria-invalid="false"></select>
                                            </select>
                                        </div>
                                        <div class="fl" style="width: 33%;padding: 0 5px 0 0;box-sizing: border-box">
                                            <select id="startCity" name="startCityId" class="form-control valid"
                                                    aria-invalid="false"></select>
                                        </div>
                                        <div class="fl" style="width: 33%;padding: 0 5px 0 0;box-sizing: border-box">
                                            <select id="startArea" name="startAreaId" class="form-control valid"
                                                    aria-invalid="false"></select>
                                        </div>
                                    </div>
                                </div>
                            
                               <div class="flex" onclick="changeDiv()" style="width: 3%;display: inline-block;">
                                    <img th:src="@{/img/change.png}" style="width: 20px;height: 20px;display: block;margin: 0 auto ">
                                </div>
                            
                               
                                <div class="flex" style="width: 48%;display: inline-block;">
                                    <!-- <label class="flex_left">收货地址：</label> -->
                                    <div class="flex_right over">
                                        <div class="fl" style="width: 33%;padding: 0 5px 0 0;box-sizing: border-box">
                                            <select id="endProvince" name="endProvinceId"
                                                    class="form-control valid" aria-invalid="false"></select>
                                            </select>
                                        </div>
                                        <div class="fl" style="width: 33%;padding: 0 5px 0 0;box-sizing: border-box">
                                            <select id="endCity" name="endCityId" class="form-control valid"
                                                    aria-invalid="false"></select>
                                        </div>
                                        <div class="fl" style="width: 33%;padding: 0 5px 0 0;box-sizing: border-box">
                                            <select id="endArea" name="endAreaId" class="form-control valid"
                                                    aria-invalid="false"></select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-2 col-sm-2 col-md-2">                        
                                <div class="form-group">
                                    <a class="btn btn-primary btn-sm" onclick="searchPre()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-default btn-outline btn-sm" onclick="reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </div>          
                            </div>
                        </row>

                </form>
            </div>

           
            <div class="col-sm-12 select-table">
                <div class="btn-group-sm">
                    

                    <div>
                        <a class="btn btn-info btn-sm" onclick="$.table.exportExcel()">
                            <i class="fa fa-download"></i> 导出已阅
                        </a>
                        <span class="glyphicon glyphicon-info-sign A94" aria-hidden="true"></span>
                        <span class="A94">当前实际成交价时间段为：<span id="startTime"></span> ~ <span id="endTime"></span></span>

                        <div style="float: right;">
                            导入统计：<span class="A94" id="importCount"></span>
                        </div>
                    </div>
                </div>

                <div class="col-sm-4 table-striped borR" style="padding: 0;">
                    <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
                </div>

                <div class="col-sm-8 table-striped borL" style="padding: 0;">
                    <iframe style="height: 100%" width = "100%" id="iframe"  frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no" allowtransparency="yes"></iframe>
                </div>
            </div>

            
        </div>

        <div class="col-sm-12 select-table" style="text-align: center;">
<!--            <button type="button" class="btn btn-primary" onclick="leading()">全部数据一键导入</button>-->
            <button type="button" class="btn btn-default" onclick="closeItem()">关闭</button>
        </div>

    </div>

</div>

<script id="dNo" type="text/html">
    <div>
        <div class="row no-gutter" style="padding: 0;margin: 10px 0 0 0;">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="col-sm-4">指导价有效期：</label>
                    <div class="col-sm-8">
                        <input type="text" placeholder="开始日期" style="width: 45%; float: left;" class="form-control"
                        id="regDateStart"  name="params[regDateStart]" autocomplete="off"
                        th:value="${regDateStart}">
                        <span style="display: block; line-height: 20px; float: left; text-align:center; width:5%;">-</span>
                        <input type="text" placeholder="结束日期" style="width: 45%; float: left;" class="form-control"
                                id="regDateEnd"  name="params[regDateEnd]" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="col-sm-12" style="margin-top: 10px;font-size: 16px;">
                <div>
<!--                    <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>-->
                    <span id="topText">一键导入的数据量较大，导入时间较长，请勿关闭该页面耐心等待</span>
                </div>
            </div>
        </div>
    </div>
</script>

<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-select-js" />
<th:block th:include="include :: bootstrap-table-editable-js"/>

<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('tms:guidePrice:edit')}]];
    var delFlag = [[${@permission.hasPermi('tms:guidePrice:removePrice')}]];

    var prefix = ctx + "tms/transport_price";
    var urls = ctx + "tms/transport_price/price_list"

    //客户id
    var transLineId = [[${transLineId }]];
    //运输方式
    var trans_code = [[${@dict.getType('trans_code')}]];

    var carLen = [[${@dict.getType('car_len')}]];
    var carType = [[${@dict.getType('car_type')}]];
    var onId=""

    $(function () {
        $('#iframe').attr("src", urls);
        
        var options = {
            url: prefix + "/cust_list",
            exportUrl: prefix + "/export",
            modalName: "已阅指导价",
            showToggle:false,
            showRefresh:false,
            showColumns:false,
            showSearch: false,
            uniqueId: "customerId",
            clickToSelect: true,
            height: 580,  
            columns: [
                {
                    radio: true,
                    formatter:function(value, row, index) {
                        let data=false
                        if(onId){
                            if(onId==row.customerId){
                                var urls = ctx + "tms/transport_price/price_list?customerId=" + row.customerId + "&custAbbr=" + row.custAbbr
                                $('#iframe').attr("src", urls);
                                data=true
                            }

                        }else if(index==0){
                            var urls = ctx + "tms/transport_price/price_list?customerId=" + row.customerId + "&custAbbr=" + row.custAbbr
                            $('#iframe').attr("src", urls);
                            data=true
                            importRate(row.customerId)
                        } 
                        return data
                    }
                },
                {
                    field : 'custAbbr',
                    title : '客户简称',
                    align: 'left',
                },
                {
                    field : 'deptName',
                    title : '运营组',
                    align: 'left',
                },
                {
                    title: '线路条数',
                    field : 'transLineCount',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let data = `<div style='color:#f7a54a;display: inline-block'>` + value + `</div>`
                            + `<span class="glyphicon glyphicon-import lf" data-toggle="tooltip" data-placement="top" title="导入当前数据指导价" style="color: #1ab394;cursor:pointer;margin-left: 5px;" onclick="leading('`+row.customerId+`','`+row.custAbbr+`');"></span>`
                            + `<span class="glyphicon glyphicon-export lf" data-toggle="tooltip" data-placement="top" title="导出当前已阅数据" style="color: #1ab394;cursor:pointer;" onclick="exportCustomer('`+row.customerId+`');"></span>`

                        return data
                    }
                },


            ],
            //
            onClickRow(row,ele,field){
                onId=row.customerId
                var urls = ctx + "tms/transport_price/price_list?customerId=" + row.customerId + "&custAbbr=" + row.custAbbr
                $('#iframe').attr("src", urls); 
                importRate(row.customerId)
            }
        };
        $.table.init(options);
        $.provinces.init("startProvince", "startCity", "startArea", "", "", "");
        $.provinces.init("endProvince", "endCity", "endArea", "", "", "");

        layui.use('laydate', function(){
            var laydate = layui.laydate;
            var datetimepicker = laydate.render({
                elem: '#datetimepicker', //指定元素
                isInitValue : false,
                trigger: 'click',
                type: 'datetime'
            });
        });



        $("#startTime").html(toTime(2))
        $("#endTime").html(toTime(1))

    });

    function toTime(time){
        let dataTime=''
        var myDate = new Date;
        var year = myDate.getFullYear(); //获取当前年
        var mon = myDate.getMonth() + 1; //获取当前月
        // var date = myDate.getDate();
        if(time){
            if(mon>time){
                dataTime=year+'-'+(mon-time)
            }else{
                let num=time-mon
                dataTime=(year-1)+'-'+(12-num)
            }
        }else{
            dataTime=year+'-'+mon
        }
        

        return dataTime
    }


    function leading(val,custAbbr){
        layer.open({
                type: 1,
                area: ['55%', '26%'],
                fix: false,
                maxmin: true,
                shade: 0.3,
                title: '选择导入指导价时间段',
                content: $('#dNo').html(),
                btn: ['确定', '取消'],
                shadeClose: true,
                btn1: function(index, layero){
                    let s = $("#regDateStart").val();
                    let e = $("#regDateEnd").val();
                    $("#startTime").html(s)
                    $("#endTime").html(e)
                    
                    let data ={
                        startDate:s,
                        endDate:e
                    }

                    if (val) {
                        data.customerId= val
                    }

                    $.operate.submit(ctx + "tms/transport_price/add_guide_pirce", "post", "json",data);
                    layer.close(index);
                },
                success: function (layero, index) {
                    var laydate = layui.laydate;
                    var regDateStart = laydate.render({
                        elem: '#regDateStart', //指定元素
                        isInitValue : false,
                        trigger: 'click',
                        type: 'date',
                        // value: $("#startTime").html()
                    });
                    var regDateEnd = laydate.render({
                        elem: '#regDateEnd', //指定元素
                        isInitValue : false,
                        trigger: 'click',
                        type: 'date',
                        // value: $("#endTime").html()
                    });
                    if(val){
                        $("#topText").html("导入客户【"+custAbbr+"】数据，请勿关闭该页面耐心等待。")
                    }
                }
            });
    }

    function searchPre() {
        var data = {};
        data.salesDept = $.common.join($('#salesDept').selectpicker('val'));
        data.carLen = $.common.join($('#carLen').selectpicker('val'));
        data.carType = $.common.join($('#carType').selectpicker('val'));
        $.table.search('role-form', data);
    }

    function changeDiv(){
        var startProvince= $('#startProvince').val()
        var startCity= $('#startCity').val()
        var startArea= $('#startArea').val()
        var endProvince= $('#endProvince').val()
        var endCity= $('#endCity').val()
        var endArea= $('#endArea').val()
        $.provinces.init("startProvince", "startCity", "startArea", endProvince, endCity, endArea);
        $.provinces.init("endProvince", "endCity", "endArea", startProvince, startCity, startArea);
        //searchPre()

    }
    function reset() {
        $.form.reset();
        $("#salesDept").selectpicker('refresh');
        $("#carLen").selectpicker('refresh');
        $("#carType").selectpicker('refresh');
        onId=""
        searchPre()
    }
    function importRate(customerId) {
        $.ajax({
            url: ctx + "tms/transport_price/import_rate",
            method: 'get',
            dataType: "json",
            data: {customerId},
            success: function (result) { 
                if(result.code==0){
                  $("#importCount").html(  result.data.importCount+'/'+result.data.ct )
                }
            }
        });
    }

    /*
     * 导出数据
     */
    function exportCustomer(customerId) {
        $.modal.confirm("确定导出当前已阅数据吗？", function() {
            var data = {customerId:customerId};
            $.modal.loading("正在导出数据，请稍后...");
            $.post(ctx + "tms/transport_price/export", data, function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

</script>
</body>
</html>