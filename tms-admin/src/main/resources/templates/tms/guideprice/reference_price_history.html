<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('历史记录')" />
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: bootstrap-select-css" />

</head>
<style>
    .table-striped {
        height: calc(100% - 20px);
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        
            <form id="formId" class="form-horizontal">
                <input id="referencePriceId" name="referencePriceId" type="hidden" th:value="${referencePriceId}">
                <input id="priceType" name="priceType" type="hidden" th:value="${priceType}">
            </form>
        
        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div th:include="include :: footer"></div>
<th:block th:include="include :: bootstrap-table-editable-js"/>
<th:block th:include="include :: bootstrap-select-js" />

<script th:inline="javascript">
    var prefix = ctx + "tms/reference_price";

    $(function() {

        var options = {
            url: prefix + "/history/list",
            modalName: "历史记录",
            showToggle:false,
            showColumns:false,
            clickToSelect: true,
            showSearch:false,
            showRefresh:false,
            uniqueId: "referencePriceId",
            columns: [

                {
                    field : 'price',
                    title : '价格',
                    align: 'right',
                    formatter: function (value, row, index) {
                        var s = value;
                        var type = 1;
                        if (/[^0-9\.]/.test(s))
                            return "0";
                        if (s == null || s == "")
                            return "0";
                        s = s.toString().replace(/^(\d*)$/, "$1.");
                        s = (s + "00").replace(/(\d*\.\d\d)\d*/, "$1");
                        s = s.replace(".", ",");
                        var re = /(\d)(\d{3},)/;
                        while (re.test(s))
                            s = s.replace(re, "$1,$2");
                        s = s.replace(/,(\d\d)$/, ".$1");
                        if (type == 0) {// 不带小数位(默认是有小数位)
                            var a = s.split(".");
                            if (a[1] == "00") {
                                s = a[0];
                            }
                        }
                        return "￥"+s;
                    }
                },
                {
                    field : 'checkStatus',
                    title : '状态',
                    align: 'left',
                    formatter: function(value, item, index) {
                        if (item.checkStatus == 0) {
                            return '<span class="label label-default">待审核</span>';
                        }
                        if (item.checkStatus == 2) {
                            return '<span class="label label-primary">审核通过</span>';
                        }
                        if (item.checkStatus == 1) {
                            return '<span class="label label-danger">审核未通过</span>';
                        }
                    }
                },
                
                {
                    field : 'checkUserName',
                    title : '审核人',
                    align: 'left'
                },
                {
                    title: '审核时间',
                    field: 'checkDate',
                    align: 'left'
                },
                {
                    field : 'regUserName',
                    title : '创建人',
                    align: 'left'
                },
                {
                    title: '创建时间',
                    field: 'regDate',
                    align: 'left'
                },


            ]
        };
        $.table.init(options);
    });


    //验证字符串是否是数字
    function checkNumber(val) {
        if(val === "" || val ==null){
            return false;
        }
        if(!isNaN(val)){
            return true;
        }
        else{
            return false;
        }
    }

</script>
</body>
</html>