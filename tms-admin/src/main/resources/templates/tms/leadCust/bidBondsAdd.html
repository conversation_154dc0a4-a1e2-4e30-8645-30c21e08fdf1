<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增投标保证金')"/>
</head>
<style type="text/css">
    .bg_title{
        font-size: 15px;
        font-weight: bold;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 113px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;
    }
    .fcff{
        color: #ff1f1f;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .tc{
        text-align: center;
    }
    .bor{
        border: 1px #eee solid;
    }
    .over{
        overflow: hidden;

    }
    .fl{
        float: left;
    }
    .ml10{
        margin-left: 10px;
    }
    .mt10{
        margin-top: 10px;
    }
    .table>thead>tr>th{
        background: #eff3f9 !important;
        padding: 0;
    }

</style>
<body>
<div class="form-content">
    <form id="form-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="id" th:value="${bb.id}">
        <input type="hidden" name="deletedFj" id="deletedFj">
        <div class="panel-group">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        客户基础信息
                    </h5>
                </div>
                <div class="panel-collapse">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 客户名称：</label>
                                    <div class="flex_right">
                                        <span th:if="${act == 'view'}" th:text="${cust?.custName}"></span>
                                        <input th:unless="${act == 'view'}" class="form-control" type="text" name="custName" id="custName" placeholder="线索客户名称关键字"
                                               maxlength="100" required autocomplete="off" th:value="${cust?.custName}"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">客户简称：</label>
                                    <div class="flex_right" id="custAbbr">
                                        [[${cust?.custAbbr}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">企业类型：</label>
                                    <div class="flex_right" id="type">
                                        [[${cust != null ? @dict.getLabel('qy_lx', cust.type):''}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">业务规模：</label>
                                    <div class="flex_right" id="scale">
                                        [[${cust?.scale}]]
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">物流决策人：</label>
                                    <div class="flex_right" id="decisionMaker">
                                        [[${cust?.decisionMaker}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">联系电话：</label>
                                    <div class="flex_right" id="decisionMakerPhone">
                                        [[${cust?.decisionMakerPhone}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">对接人：</label>
                                    <div class="flex_right" id="contact">
                                        [[${cust?.contact}]]
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">联系电话：</label>
                                    <div class="flex_right" id="contactPhone">
                                        [[${cust?.contactPhone}]]
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 col-sm-12">
                            <div class="flex">
                                <label class="flex_left"><span class="fcff">*</span> 地址：</label>
                                <div class="flex_right" id="addr">
                                    [[${cust?.provinceName}]][[${cust?.cityName}]][[${cust?.areaName}]][[${cust?.detailAddr}]]
                                </div>
                            </div>
                            </div>
                        </div>

                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        投标保证金信息
                    </h5>
                </div>
                <div class="panel-collapse">
                    <div class="panel-body">
                        <input type="hidden" name="leadCustId" id="leadCustId" th:value="${cust?.id}">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 项目名称：</label>
                                    <div class="flex_right">
                                        <span th:if="${act == 'view'}" th:text="${bb.itemName}"></span>
                                        <input th:unless="${act == 'view'}" class="form-control" type="text" name="itemName" id="itemName"
                                               maxlength="100" required autocomplete="off" th:value="${bb.itemName}"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">项目标的金额：</label>
                                    <div class="flex_right">
                                        <span th:if="${act == 'view'}" th:text="${bb.itemAmount}"></span>
                                        <input th:unless="${act == 'view'}" class="form-control" type="text" name="itemAmount" id="itemAmount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)"
                                               maxlength="100" autocomplete="off" th:value="${bb.itemAmount}"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 投标保证金金额：</label>
                                    <div class="flex_right">
                                        <span th:if="${act == 'view'}" th:text="${bb.marginAmount}"></span>
                                        <input th:unless="${act == 'view'}" class="form-control" type="text" name="marginAmount" id="marginAmount" oninput="$.numberUtil.onlyNumberTwoDecimal(this)"
                                               maxlength="100" required autocomplete="off" th:value="${bb.marginAmount}"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left" style="width: 140px"><span class="fcff">*</span> 投标保证金占业务比：</label>
                                    <div class="flex_right">
                                        <span th:if="${act == 'view'}" th:text="${bb.marginRatio + '%'}"></span>
                                        <div class="input-group" th:unless="${act == 'view'}">
                                            <input class="form-control" type="text" name="marginRatio" id="marginRatio" oninput="$.numberUtil.onlyNumberTwoDecimal(this)"
                                                   required autocomplete="off" th:value="${bb.marginRatio}"/>
                                            <div class="input-group-addon">%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left" style="width: 130px"><span class="fcff">*</span> 是否需要同行经验：</label>
                                    <div class="flex_right">
                                        <span th:if="${act == 'view'}" th:text="${bb.reqPeerExp == 0 ? '否':'是'}"></span>
                                        <select th:unless="${act == 'view'}" class="form-control" name="reqPeerExp" id="reqPeerExp" required>
                                            <option value=""></option>
                                            <option value="1" th:selected="${bb.reqPeerExp == 1}">是</option>
                                            <option value="0" th:selected="${bb.reqPeerExp == 0}">否</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        付款信息
                    </h5>
                </div>
                <div class="panel-collapse">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 结算公司：</label>
                                    <div class="flex_right">
                                        <span th:if="${act == 'view'}" th:text="${bb.ssgs}"></span>
                                        <select th:unless="${act == 'view'}" class="form-control" name="balaCorp" id="balaCorp"
                                                required th:with="corps = ${@dict.getType('bala_corp')}">
                                            <option value=""></option>
                                            <option th:each="dict : ${corps}" th:text="${dict.dictLabel}"
                                                    th:value="${dict.dictValue}" th:selected="${dict.dictValue == bb.balaCorp}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 收款单位：</label>
                                    <div class="flex_right">
                                        <span th:if="${act == 'view'}" th:text="${bb.skdw}"></span>
                                        <input th:unless="${act == 'view'}" class="form-control" type="text" name="skdw" id="skdw"
                                               maxlength="100" required autocomplete="off" th:value="${bb.skdw}"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 银行帐号：</label>
                                    <div class="flex_right">
                                        <span th:if="${act == 'view'}" th:text="${bb.yhzh}"></span>
                                        <input th:unless="${act == 'view'}" class="form-control" type="text" name="yhzh" id="yhzh"
                                               required autocomplete="off" th:value="${bb.yhzh}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-sm-12">
                                <div class="flex">
                                    <label class="flex_left">事项说明：</label>
                                    <div class="flex_right">
                                        <div th:if="${act == 'view'}" style="white-space: pre-wrap;" th:text="${bb.memo}"></div>
                                        <textarea style="margin-bottom: 10px" th:unless="${act == 'view'}" class="form-control" name="memo" id="memo"
                                                  autocomplete="off" rows="4" th:text="${bb.memo}"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-sm-12">
                                <div class="flex">
                                    <label class="flex_left">附件：</label>
                                    <div class="flex_right">
                                        <div th:if="${act == 'view' || act == 'edit'}" class="imgPreview">
                                            <x th:each="it : ${bb.fj}" th:src="@{${it.filePath}}" th:fileId="${it.fileId}" th:alt="${it.fileName}" style="width:50px;height:50px;margin-right: 5px;"></x>
                                        </div>
                                        <div th:unless="${act == 'view'}">
                                            <input type="file" name="fj" id="fj" multiple>
                                            <input type="hidden" name="fjTidList">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="row" style="text-align: center">
    <button th:unless="${act == 'view'}" type="button" class="btn btn-sm btn-warning" onclick="submitHandler()"><i class="fa fa-check"></i>提交审批</button>&nbsp;
    <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>
</div>

<th:block th:include="include :: footer"/>
<script th:src="@{/js/bignumber.min.js}"></script>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: bootstrap-fileinput553-css"/>
<th:block th:include="include :: bootstrap-fileinput553-js"/>
<script th:inline="javascript">

    var prefix = ctx + "bid-bonds";
    var qylx = [[${@dict.getType('qy_lx')}]];
    var act = [[${act}]];

    $(function () {
        $('x').each(function(){
            var fName = $(this).attr('alt').toLowerCase();
            var path = $(this).attr('src');
            if (fName.endsWith(".jpg") || fName.endsWith(".png") || fName.endsWith(".gif") || fName.endsWith(".bmp")) {
                $(this).after('<div><img style="width:50px; height:50px;margin-right: 5px" src="' + path + '" alt="'+ fName+'" title="'+fName+'"></div>');
            } else {
                $(this).after('<div style="line-height: 1.7"><a style="margin-right: 5px" href="'+ path+ '" target="_blank">'+ fName+'</a></div>')
            }
            if (act == 'edit') {
                $(this).next().append('<a href="javascript:;" onclick="rmvfj(this,\''+$(this).attr('fileId')+'\')" style="color:red">[删除]</a>')
            }
            $(this).remove();
        })
        $('.imgPreview').viewer({ url: 'data-original', title: false, navbar: false });

        $('#custName').data('cache', $('#custName').val());
        $('#custName').typeahead({
            hint: true,
            source: function (query, process) {//第一个为正在查询的值，第二个参数为回调函数
                $.ajax({
                    url: ctx + "lead-cust/key-lead-cust",
                    method: 'post',
                    data: {"kw": query},
                    type: 'json',
                    success: function (list) {
                        process(list)
                    }
                })
            },
            minLength: 1, // 默认值1，输入最少字符数
            items: 10, // 最多展示条数
            updater: function (item) {
                return item;//这里一定要return，否则选中不显示，外加调用display的时候null reference错误。
            },
            matcher: function (item) {
                return true;
            },
            displayText: function (item) {
                return item['custName'];//返回字符串
            },
            afterSelect: function (item) {
                console.log('afterSelect', item)
                $('#custName').data('cache', item.custName)
                $('#custAbbr').text(item.custAbbr);
                $('#leadCustId').val(item.id)
                $('#type').html($.table.selectDictLabel(qylx, item.type))
                $('#scale').text(item.scale);
                $('#decisionMaker').text(item.decisionMaker);
                $('#decisionMakerPhone').text(item.decisionMakerPhone);
                $('#contact').text(item.contact);
                $('#contactPhone').text(item.contactPhone);
                $('#addr').text(item.provinceName + item.cityName + item.areaName + item.detailAddr)
            },
            delay: 300//延迟时间
        });
        $('#custName').blur(function(){//容错
            $(this).val($(this).data('cache'))
        });
        let uploadCache = {};
        let option = {
            theme: "explorer-fa5", //主题
            language: 'zh',
            uploadUrl: ctx + "common/uploadBatch",  //上传的地址
            //deleteUrl: ctx + "common/deleteImage",
            uploadExtraData: {key: "fj"},   //上传id，传入后台的参数
            deleteExtraData: {key: 'id'},
            // extra" {key: ''}, // 上面两个一致则可使用该字段？
            enctype: 'multipart/form-data',
            //allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
            initialPreviewAsData: true,
            overwriteInitial: false,
            //initialPreviewConfig: [
            //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
            //],
            //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
            maxFileCount: 0, // 0:不限制上传数
            showUpload: false,  // 不显示上传按钮，选择后直接上传
            //previewClass:"uploadPreview",
            minFileSize: 5, // 5KB
            previewFileIcon: '<i class="fa fa-file"></i>',
            allowedPreviewTypes: ['image'],
            showClose: false,  //是否显示右上角叉按钮
            showUpload: false, //是否显示下方上传按钮
            showRemove: false, // 是否显示下方移除按钮
            //autoReplace: true,
            //showPreview: false,//是否显示预览(false=只剩按钮)
            showCaption: false,//底部上传按钮左侧文本
            uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
            fileActionSettings: {
                showUpload: false,		//每个文件的上传按钮
                showDrag: false,
                //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
            },
        }
        $("#fj").fileinput(option).on("filebatchselected", function (e, files) {
            $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
        }).on("fileuploaded", function (event, data, previewId, index) {
            //单个上传成功事件
            //console.log("fileuploaded", event, data, previewId, index)
            var code = data.response.code;
            if (code !== 0) {
                $.modal.closeLoading();
                $.modal.alertError("上传失败：" + data.response.msg);
                return;
            }
            uploadCache[previewId] = data.response.tid;
            let ids = [];
            for(let k in uploadCache) {
                ids.push(uploadCache[k])
            }
            $('[name=fjTidList]').val(ids.join(','));
        }).on('filesuccessremove', function (event, previewId, index) {
            //上传后删除事件
            console.log("filesuccessremove", event, previewId, index)
            //delete cache[previewId] //bug
            //fileArr.splice(index, 1) //bug
            //$(this).fileinput('clear');
            //$('[name="' + hideName + '"]').val('')
            var tid = uploadCache[previewId];
            $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                console.log(result)
            }, 'json')
            delete uploadCache[previewId]
            let ids = [];
            for(let k in uploadCache) {
                ids.push(uploadCache[k])
            }
            $('[name=fjTidList]').val(ids.join(','));
        });

    });


    /*提交表单*/
    function submitHandler() {
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function () {
                var data = $('#form-add').serializeArray()
                // 表单提交
                $.operate.saveTab(prefix + "/save", data);
            })
        }
    }

    function rmvfj(a, fileId) {
        $.modal.confirm("确定移除该附件吗？", function () {
            let deletedFj = $('#deletedFj').val();
            if (deletedFj) {
                deletedFj = deletedFj + "," + fileId;
            } else {
                deletedFj = fileId
            }
            $('#deletedFj').val(deletedFj)
            $(a).parent().remove();
        })
    }
</script>
</body>

</html>