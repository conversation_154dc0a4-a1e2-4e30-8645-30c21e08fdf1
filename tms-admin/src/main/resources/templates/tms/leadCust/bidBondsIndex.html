<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('投标保证金')"/>
    <th:block th:include="include :: bootstrap-select-css" />
    <style type="text/css">
        .flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .flex_left {
            width: 140px;
            text-align: right;
            padding: 5px 10px 0 10px;
        }

        .flex_right {
            min-width: 0;
            flex: 1;
        }
    </style>
</head>

<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="search-collapse" style="padding-bottom: 10px">
            <form id="role-form" class="form-horizontal">
                <div class="row" style="display: flex;align-items: center">

                    <div class="col-md-2 col-sm-3">
                        <input name="custName" class="form-control" type="text"
                               placeholder="客户名称" autocomplete="off" aria-autocomplete="none" aria-required="true">
                    </div>

                    <div class="col-md-2 col-sm-3">
                        <input name="itemName" class="form-control" type="text"
                               placeholder="项目名称" autocomplete="off" aria-autocomplete="none" aria-required="true">
                    </div>

                    <div class="col-md-2 col-sm-3">
                        <select name="params[repaidState]" class="form-control selectpicker" data-none-selected-text="回款状态">
                            <option value=""></option>
                            <option value="0">未回款</option>
                            <option value="1">部分回款</option>
                            <option value="2">全部回款</option>
                            <option value="p1">直接回款</option>
                            <option value="p2">转履约金</option>
                            <option value="p3">转代理费</option>
                        </select>
                    </div>

                    <div class="col-md-2 col-sm-3">
                        <select name="closed" class="form-control selectpicker" data-none-selected-text="关闭状态">
                            <option value=""></option>
                            <option value="0" selected>正常</option>
                            <option value="1">未打款已关闭</option>
                        </select>
                    </div>

                    <div class="col-md-2 col-sm-3">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i
                                class="fa fa-refresh"></i>&nbsp;重置</a>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="$.operate.addTab()" shiro:hasPermission="bb:save">
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-success single" onclick="editFun()" shiro:hasPermission="bb:save">
                <i class="fa fa-edit"></i> 编辑
            </a>
            <a class="btn btn-info" th:onclick="showSpSettings([[${templateId}]], [[${biz}]])" shiro:hasPermission="wecom:sp:setting"><!--勿开权限-->
                <i class="fa fa-cog"></i> 配置
            </a>
            <a class="btn btn-danger multiple" onclick="closeFun()">
                <i class="fa fa-close"></i> 关闭
            </a>
            <a class="btn btn-info single disabled" onclick="process()">
                <i class="fa fa-laptop"></i> 审批进度
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:src="@{'/js/wecom.js'}"></script>
<script th:src="@{/js/bignumber.min.js}"></script>
<th:block th:include="include :: bootstrap-fileinput553-css"/>
<th:block th:include="include :: bootstrap-fileinput553-js"/>
<script th:inline="javascript">
    var prefix = ctx + "bid-bonds";
    $(function(){
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchPre();
            }
        });
        initTable()

        $('#bootstrap-table').on('refresh.bs.table', function (e, args) {
            // 这里写你需要执行的代码
            console.log('表格刷新完成');
        });
    })
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }
    var qylx = [[${qylx}]];

    function initTable() {
        var options = {
            url: prefix + "/list",
            queryParams: function (params) {
                var search = {};
                $.each($("#role-form").serializeArray(), function (i, field) {
                    search[field.name] = field.value;
                });
                search.pageSize = params.limit;
                search.pageNum = params.offset / params.limit + 1;
                search.searchValue = params.search;
                search.orderByColumn = params.sort;
                search.isAsc = params.order;
                return search;
            },
            modalName: "投标保证金",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove/{id}",
            detailUrl: prefix + "/detail/{id}",
            uniqueId: 'id',
            showToggle: false,
            showColumns: true,
            //fixedColumns: true,
            //fixedNumber: 4,
            clickToSelect: true,
            height: 620,
            showFooter: true,
            onPostBody: function () {
                clearTotal()
                //合并页脚
                merge_footer();
                getAmountCount();
            },
            onRefresh: function (params) {
                //总数清0
                // transFeeCount = 0;
                // gotAmountCount = 0;
                // ungotAmountCount = 0;
                // sumOilApplicationAmount = 0;//开票金额
                // sumCashApplicationAmount = 0;//现金金额
                if ($('#logTbody').length == 1) { // 弹出时刷新回款记录
                    layer.close(globalLogLayerId);
                    backlog(globalLogId)
                }
                clearTotal();
            },
            onCheck: function (row, $element) {
                addTotal(row);
                setTotal();
            },
            onUncheck: function (row, $element) {
                subTotal(row);
                setTotal();
            },
            onCheckAll: function (rowsAfter) {
                clearTotal();
                //循环累加
                for (var row of rowsAfter) {
                    addTotal(row);
                }
                //赋值
                setTotal();
            },
            onUncheckAll: function () {
                //总数清0
                clearTotal();
                //赋值
                setTotal();
            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "保证金：<nobr id='totalMarginAmount'>¥0.00</nobr> 已打款：<nobr id='totalPaidAmount'>¥0.00</nobr> 已回款：<nobr id='totalBackedAmount'>¥0.00</nobr>" +
                            "<br>" +
                            "总合计：保证金：<nobr id='sumMarginAmount'>¥0.00</nobr> 已打款：<nobr id='sumPaidAmount'>¥0.00</nobr> 已回款：<nobr id='sumBackedAmount'>¥0.00</nobr>";
                    }
                },
                {
                    title: '操作', field: '',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="fa fa-list" href="javascript:$.operate.detailTab(\'' + row.id + '\')" title="明细"></a>')
                        if (row.status == 2 && row.closed == 0) {
                            /*[# shiro:hasPermission="bb:pay"]*/
                            if (row.paid == 0) {
                                actions.push(' <a class="" href="javascript:payoff(\'' + row.id + '\',' + index + ')" title="打款">打款</a>')
                            }
                            /*[/]*/
                            if (row.paid == 1 && (row.repaid == 0 || row.repaid == 1)) {
                                if (minus(row.marginAmount, plus(row.backedAmount, row.backingAmount)) > 0) { // 有未回款金额
                                    actions.push(' <a class="" href="javascript:payback(' + index + ')" title="核销">核销</a>')
                                    actions.push(' <a class="" href="javascript:turnto(' + index + ')" title="转成履约保证金">转履约</a>')
                                }
                            }
                        }
                        return actions.join('');
                    }
                },
                {field: 'spNo', title: '审批单号', width: 110},
                {field: 'status', title: '状态', width: 60, formatter(value, row, index){
                        let text = $.table.selectDictLabel([{dictValue:'1',dictLabel:'审批中'},{dictValue:'2',dictLabel:'已通过'},{dictValue:'3',dictLabel:'已驳回'}], value);
                        if (row.closed == 1) {
                            text = text + '(已关闭)'
                        }
                        return text
                    }},
                {field: 'custName', title: '客户名称'},
                {field: 'ssgs', title: '所属公司'},
                {field: 'itemName', title: '项目名称'},
                {field: 'itemAmount', title: '项目标的金额'},
                {field: 'marginAmount', title: '保证金金额'},
                {field: 'paid', title: '已打款', formatter(value,row,index){
                    if (row.status == 2) {
                        if (row.paid == 1) {
                            return '<a href="javascript:vPayProof(\''+row.id+'\')">是</a>';
                        }
                    }
                }},
                {field: 'repaid', title: '回款状态', formatter(value,row,index){
                    /*if (value == 1) {
                        if (row.marginId) {
                            return "<a href='javascript:$.modal.openTab(\"履约保证金明细\",\"" + ctx + "tms/margin/detail?marginId=" + row.marginId + "\")'>已转履约金</a>"
                        }
                        return '是'
                    } else if (value == 2) {
                        return "<a href='javascript:paybackProof(\""+row.id+"\")'>已回款</a>"
                    } else if (value == 0) {
                        if (row.marginId) {
                            return "<a href='javascript:$.modal.openTab(\"履约保证金明细\",\"" + ctx + "tms/margin/detail?marginId=" + row.marginId + "\")'>转履约金(未审批)</a>"
                        }
                    }*/
                    if (value == 0) {
                        if (row.backingAmount != 0) {
                            return "<a title='履约保证金付款后记入回款' href='javascript:backlog(\""+row.id+"\")'>未回款："+row.repaidDesc+"</a>"
                        } else {
                            return "<span>未回款</span>"
                        }
                    } else if (value == 1) {
                        return "<a href='javascript:backlog(\""+row.id+"\")'>部分回款："+row.repaidDesc+"</a>"
                    } else if (value == 2) {
                        return "<a href='javascript:backlog(\""+row.id+"\")'>全部回款："+row.repaidDesc+"</a>"
                    }
                }},
                {field: 'backedAmount', title: "已回款金额", visible: false},
                {field: 'backingAmount', title: "回款中金额", visible: false},
                {field: 'applyerName', title: '申请人'},
                {field: 'applyTime', title: '申请时间'},

            ]
        };

        $.table.init(options);
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                //查询方法
                $.table.search();
            }
        });
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for (var i = 1; i < footer_td.length; i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    function process() {
        var spNo = $.table.selectColumns("spNo")[0];
        if (!spNo) {
            $.modal.msgWarning("尚未提交审批")
            return
        }
        wecom_process(spNo);
    }

    function editFun() {
        var status = $.table.selectColumns("status")[0];
        if (status !== 0 && status !== 3) {
            $.modal.msgWarning("新建或已驳回的单据才可修改")
            return
        }
        var id = $.table.selectColumns("id")[0];
        $.operate.editTab(id)
    }

    function closeFun() {
        var closeds = $.table.selectColumns("closed");
        for (let i = 0; i < closeds.length; i++) {
            if (closeds[i] == 1) {
                $.modal.msgWarning("已关闭的单据不可再次关闭")
                return
            }
        }
        var statuss = $.table.selectColumns("status");
        for (let i = 0; i < statuss.length; i++) {
            if (statuss[i] != 2) {
                $.modal.msgWarning("已审核通过且未打款的单据才可关闭")
                return
            }
        }
        var paids = $.table.selectColumns("paid");
        for (let i = 0; i < paids.length; i++) {
            if (paids[i] != 0) {
                $.modal.msgWarning("已审核通过且未打款的单据才可关闭")
                return
            }
        }
        $.modal.confirm("确认关闭吗？", function(){
            var ids = $.table.selectColumns("id");
            $.operate.post(prefix + "/close", {ids: ids.join(',')}, function(res) {
                console.log(res)
            })
        })

    }

    function payoff(id, idx) {
        var row = $.btTable.bootstrapTable('getData')[idx];
        var status = row.status;
        if (status != 2) {
            $.modal.msgWarning("只能对已通过审批的单据付款");
            return;
        }
        var uploadCache = {};
        layer.open({
            type: 1,
            area: ['600px', '500px'],
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            title: '财务付款',
            content: `
<div class="row" style="margin: 5px">
    <form id="offForm">
    <div class="col-sm-6 flex">
        <label class="flex_left" style="width:110px">应支付金额 <span class="fcff3">*</span></label>
        <div class="flex_right">
            <div class="form-control" style="border:1px #fff solid;line-height: 26px">${row.marginAmount.toFixed(2)}元</div>
        </div>
    </div>
    <div class="col-sm-12 flex">
        <label class="flex_left" style="width:110px">支付凭证 <span class="fcff3">*</span></label>
        <div class="flex_right">
            <input type="file" id="proof" name="proof" multiple/>
        </div>
    </div>
    <div class="col-sm-12 flex">
        <label class="flex_left" style="width:110px">备注</label>
        <div class="flex_right">
            <textarea class="form-control" rows="5" id="offMemo"></textarea>
        </div>
    </div>
    </form>
</div>`,
            btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            success: function (layero, index) {
                let option = {
                    theme: "explorer-fa5", //主题
                    language: 'zh',
                    uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                    //deleteUrl: ctx + "common/deleteImage",
                    uploadExtraData: {key: "proof"},   //上传id，传入后台的参数
                    deleteExtraData: {key: 'id'},
                    // extra" {key: ''}, // 上面两个一致则可使用该字段？
                    enctype: 'multipart/form-data',
                    allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                    initialPreviewAsData: true,
                    overwriteInitial: false,
                    //initialPreviewConfig: [
                    //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                    //],
                    //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                    maxFileCount: 0, // 0:不限制上传数
                    showUpload: false,  // 不显示上传按钮，选择后直接上传
                    //previewClass:"uploadPreview",
                    minFileSize: 5, // 5KB
                    previewFileIcon: '<i class="fa fa-file"></i>',
                    allowedPreviewTypes: ['image'],
                    showClose: false,  //是否显示右上角叉按钮
                    showUpload: false, //是否显示下方上传按钮
                    showRemove: false, // 是否显示下方移除按钮
                    //autoReplace: true,
                    //showPreview: false,//是否显示预览(false=只剩按钮)
                    showCaption: false,//底部上传按钮左侧文本
                    uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                    fileActionSettings: {
                        showUpload: false,		//每个文件的上传按钮
                        showDrag: false,
                        //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                    },
                }
                $("[name='proof']").fileinput(option).on("filebatchselected", function (e, files) {
                    $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                }).on("fileuploaded", function (event, data, previewId, index) {
                    //单个上传成功事件
                    console.log("fileuploaded", event, data, previewId, index)
                    var code = data.response.code;
                    if (code !== 0) {
                        $.modal.closeLoading();
                        $.modal.alertError("上传失败：" + data.response.msg);
                        return;
                    }
                    uploadCache[previewId] = data.response.tid;
                }).on('filesuccessremove', function (event, previewId, index) {
                    //上传后删除事件
                    console.log("filesuccessremove", event, previewId, index)
                    //delete cache[previewId] //bug
                    //fileArr.splice(index, 1) //bug
                    //$(this).fileinput('clear');
                    //$('[name="' + hideName + '"]').val('')
                    var tid = uploadCache[previewId];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        console.log(result)
                    }, 'json')
                    delete uploadCache[previewId]
                });
            },
            btn1: function(index, layero) {
                if (!$.validate.form('offForm')) {
                    return;
                }
                var curTids = []
                for (const uploadCacheKey in uploadCache) {
                    curTids.push(uploadCache[uploadCacheKey])
                }
                if (curTids.length == 0) {
                    $.modal.msgError("请选择上传文件");
                    return
                }
                var offMemo = $('#offMemo').val().trim()
                $.modal.confirm("确定提交吗？", function(){
                    $.ajax({
                        url: prefix + "/payProof",
                        data: {id,tids:curTids.join(','),offMemo},
                        type: 'POST',
                        success: function (result) {
                            if (result.code == web_status.SUCCESS) {
                                layer.close(index);
                                $.modal.msgSuccess(result.msg);
                                $.table.refresh();
                            } else {
                                $.modal.msgError(result.msg);
                            }
                        }
                    });
                })
            },
            btn2: function(index, layero) {
                // 删除服务端已上传未使用文件
                for (const uploadCacheKey in uploadCache) {
                    var tid = uploadCache[uploadCacheKey];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        //console.log(result)
                    }, 'json')
                }
            },
            cancel: function(index, layero) {
                // 删除服务端已上传未使用文件
                for (const uploadCacheKey in uploadCache) {
                    var tid = uploadCache[uploadCacheKey];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        //console.log(result)
                    }, 'json')
                }
            }
        });
    }

    let globalLogId = null; // 回款记录弹出层状态下，修改履约保证金后刷新履约保证金状态
    let globalLogLayerId = null;

    function backlog(id) {
        var tt = layer.load(2, { //icon0-2 加载中,页面显示不同样式
            shade: [0.5, '#000'], //0.4为透明度 ，#000 为颜色
            content: '加载中...',
            success: function (layero) {
                layero.find('.layui-layer-content').css({
                    color: '#fff'
                });
            }
        });
        $.ajax({
            url: prefix + "/backlog?id="+id,
            cache: false,
            type: 'get',
            success: function (result) {
                if (result.code != 0) {
                    $.modal.msgError(result.msg)
                    return;
                }
                let list = result.data;
                var formHtml = []
                formHtml.push("<div style='padding: 5px'><div style='padding: 5px 0;color:#888'>※转履约金后，履约金流程结束视为回款完成</div>",
                    "<table class='table table-bordered'>")
                formHtml.push("<thead>")
                formHtml.push("<tr><th style='width: 30px'>№</th><th style='width: 132px'>操作时间</th>",
                    "<th style='width: 86px'>操作人</th><th style='width: 64px'>回款方式</th><th style='width: 110px'>金额</th><th>备注</th></tr>")
                formHtml.push("</thead>")
                formHtml.push("<tbody id='logTbody'>")
                var userid = [];
                for (let i = 0; i < list.length; i++) {
                    let rowspan = (list[i].proofs && list[i].proofs.length > 0) ? 2 : 1;
                    formHtml.push("<tr><td rowspan='",rowspan,"'>",i + 1,"</td><td>",list[i].time,"</td><td>",list[i].userName,"</td><td>");
                    if (list[i].type == 1) {
                        formHtml.push("回款")
                    } else if (list[i].type == 2) {
                        formHtml.push("转履约金")
                    } else if (list[i].type == 3) {
                        formHtml.push("转代理费")
                    }
                    formHtml.push("</td><td align='right'>")
                    if (list[i].type == 2) {
                        let statusText = ['新建','已申请','审核中','已收款','已付款','已驳回'];
                        formHtml.push('<div style="display: flex;justify-content: space-between;">',
                            '<a style="white-space: nowrap;" href="javascript:$.modal.openTab(\'履约保证金明细\',\'');
                        if (list[i].marginVbillstatus == 0) {
                            formHtml.push(ctx + "tms/margin/edit?type=edit&marginId=");
                        } else {
                            formHtml.push(ctx + "tms/margin/detail?marginId=");
                        }
                        formHtml.push(list[i].marginId, '\')">', '[',statusText[list[i].marginVbillstatus],']', '</a>');
                    }
                    formHtml.push('<span>', list[i].amount.toFixed(2), '</span>')
                    formHtml.push("</div></td><td>");
                    formHtml.push(list[i].memo,"</td></tr>");
                    if (list[i].proofs && list[i].proofs.length > 0) {
                        formHtml.push("<tr><td colspan='5' class='imgPreview'>");
                        for (let j = 0; j < list[i].proofs.length; j++) {
                            formHtml.push('<img src="',list[i].proofs[i].filePath,'" style="width:70px;height:50px;" />')
                        }
                        formHtml.push("</td></tr>")
                    }
                }
                formHtml.push("</tbody></table></div>");
                layer.open({
                    type: 1,
                    skin: 'none',
                    area: ['700px', '500px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    title: '回款记录',
                    content: formHtml.join(''),
                    btn: ['<i class="fa fa-remove"></i> 关闭'],
                    success: function(layero, index) {
                        layer.close(tt);
                        layero.find('.imgPreview').viewer({
                            url: 'data-original',
                            title: false,
                            navbar: false,
                        });
                        globalLogLayerId = index;
                        globalLogId = id;
                    }
                })
            }
        })
    }

    function vPayProof(id) {
        $.ajax({
            url: prefix + "/vPayProof?id="+id,
            cache: false,
            success: function(result) {
                if (result.code != 0) {
                    $.modal.msgError(result.msg);
                    return
                }
                let html = []
                for (let i = 0; i < result.data.proofs.length; i++) {
                    html.push('<img src="',result.data.proofs[i].filePath,'" style="width:70px;height:50px;margin:5px;" />')
                }
                layer.open({
                    type: 1,
                    area: ['600px', '300px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    skin: 'proofClass',
                    title: '打款明细查看',
                    content: `
<div class="row" style="margin: 5px">
    <div class="col-sm-6 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">支付人</span>
        <div class="flex_right">
            <div class="form-control">${result.data.PAY_USER_NAME}</div>
        </div>
    </div>
    <div class="col-sm-6 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">支付时间</span>
        <div class="flex_right">
            <div class="form-control">${result.data.PAY_TIME}</div>
        </div>
    </div>
    <div class="col-sm-12 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">支付凭证</span>
        <div class="flex_right imgPreview">
            ${html.join('')}
        </div>
    </div>
    <div class="col-sm-12 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">备注</span>
        <div class="flex_right">
            <div class="form-control" style="white-space: pre-wrap!important;min-height:26px;height:auto;word-wrap: break-word!important;*white-space:normal!important;">${result.data.PAY_MEMO||''}</div>
        </div>
    </div>
</div>`,
                    btn: ['<i class="fa fa-remove"></i> 关闭'

                        //,'<i class="fa fa-dollar"></i> 撤销付款'
                    ],
                    // 弹层外区域关闭
                    shadeClose: true,
                    success: function(layero) {
                        layero.find('.imgPreview').viewer({
                            url: 'data-original',
                            title: false,
                            navbar: false,
                        });
                    },
                    btn1: function (index, layero) {
                        layer.close(index)
                    },

                    /*btn2: function (index, layero) {
                        $.modal.confirm('确认取消该单据支付吗？', function(){
                            $.ajax({
                                url: ctx + "yfk/rejectPayProof",
                                cache: false,
                                data: {id: id},
                                type: 'post',
                                success: function (result) {
                                    if (result.code == 0) {
                                        layer.close(index);
                                        $.modal.msgSuccess(result.msg);
                                        $.table.refresh();
                                    } else {
                                        $.modal.msgError(result.msg);
                                    }
                                }
                            })
                        });
                        return false
                    }*/

                })
            }
        })
    }
    function plus(n1, n2) { // +运算
        let x = new BigNumber(n1);
        if (x.isNaN()) {
            x = new BigNumber(0)
        }
        let y = new BigNumber(n2);
        if (y.isNaN()) {
            y = new BigNumber(0)
        }
        return x.dp(10).plus(y.dp(10)).dp(10).toNumber()
    }
    function minus(n1, n2) { // -运算
        let x = new BigNumber(n1);
        if (x.isNaN()) {
            x = new BigNumber(0)
        }
        let y = new BigNumber(n2);
        if (y.isNaN()) {
            y = new BigNumber(0)
        }
        return x.dp(10).minus(y.dp(10)).dp(10).toNumber()
    }
    function turnto(idx) {
        var row = $.btTable.bootstrapTable('getData')[idx];
        if (minus(row.marginAmount, plus(row.backedAmount, row.backingAmount)) == 0) {
            $.modal.msgWarning("没有可回款金额")
            return
        }
        $.modal.openTab('转成履约保证金', ctx + 'tms/margin/addMargin?bbid=' + row.id)
    }

    function payback(idx) {
        var row = $.btTable.bootstrapTable('getData')[idx];
        var status = row.status;
        if (row.paid != 1) {
            $.modal.msgWarning("只能对已打款的单据回款");
            return;
        }
        let backAble = minus(row.marginAmount, plus(row.backedAmount, row.backingAmount));
        var uploadCache = {};
        layer.open({
            type: 1,
            area: ['600px', '415px'],
            fix: false,
            //不固定
            maxmin: true,
            skin: 'none',
            shade: 0.3,
            title: '保证金回款',
            content: `
<div class="row" style="margin: 5px 20px 5px 0;">
    <form id="offForm">
    <div class="col-sm-6 flex">
        <label class="flex_left" style="width:90px">回款金额 <span class="fcff3">*</span></label>
        <div class="flex_right">
            <input class="form-control" name="backAmount" required id="backAmount" max="${backAble}" value="${backAble}" min="0.01" autocomplete="off" placeholder="#.##" oninput="$.numberUtil.onlyNumberTwoDecimal(this)" />
        </div>
    </div>
    <div class="col-sm-6 flex">
        <label class="flex_left" style="width:90px">类型 <span class="fcff3">*</span></label>
        <div class="flex_right">
            <select class="form-control" id="type" required autocomplete="off">
                <option value=""></option>
                <option value="1">回款</option>
                <option value="3">转代理费</option>
            </select>
        </div>
    </div>
    <div class="col-sm-12 flex">
        <label class="flex_left" style="width:90px">回款凭证 <span class="fcff3">*</span></label>
        <div class="flex_right">
            <input type="file" id="proof" name="proof" multiple/>
        </div>
    </div>
    <div class="col-sm-12 flex">
        <label class="flex_left" style="width:90px">备注</label>
        <div class="flex_right">
            <textarea class="form-control" rows="5" id="offMemo"></textarea>
        </div>
    </div>
    </form>
</div>`,
            btn: ['<i class="fa fa-check"></i> 提交', '<i class="fa fa-remove"></i> 取消'],
            // 弹层外区域关闭
            shadeClose: true,
            success: function (layero, index) {
                let option = {
                    theme: "explorer-fa5", //主题
                    language: 'zh',
                    uploadUrl: ctx + "common/uploadBatch",  //上传的地址
                    //deleteUrl: ctx + "common/deleteImage",
                    uploadExtraData: {key: "proof"},   //上传id，传入后台的参数
                    deleteExtraData: {key: 'id'},
                    // extra" {key: ''}, // 上面两个一致则可使用该字段？
                    enctype: 'multipart/form-data',
                    allowedFileExtensions: ["jpg", "png", "jpeg", "bmp", "pdf", "gif"], //接收的文件后缀
                    initialPreviewAsData: true,
                    overwriteInitial: false,
                    //initialPreviewConfig: [
                    //    { url:'deletefile',key:'fileid', type: "image", fileType: "image", caption: fileName }
                    //],
                    //dropZoneEnabled: true,          // 点击预览区域进行文件上传操作
                    maxFileCount: 0, // 0:不限制上传数
                    showUpload: false,  // 不显示上传按钮，选择后直接上传
                    //previewClass:"uploadPreview",
                    minFileSize: 5, // 5KB
                    previewFileIcon: '<i class="fa fa-file"></i>',
                    allowedPreviewTypes: ['image'],
                    showClose: false,  //是否显示右上角叉按钮
                    showUpload: false, //是否显示下方上传按钮
                    showRemove: false, // 是否显示下方移除按钮
                    //autoReplace: true,
                    //showPreview: false,//是否显示预览(false=只剩按钮)
                    showCaption: false,//底部上传按钮左侧文本
                    uploadAsync: true, // 多文件时是否并行上传(true时file_hide_tid要累加处理)
                    fileActionSettings: {
                        showUpload: false,		//每个文件的上传按钮
                        showDrag: false,
                        //showZoom: param.fileType !== 'file'	 //如果是文件类型，则取消放大按钮
                    },
                }
                $("[name='proof']").fileinput(option).on("filebatchselected", function (e, files) {
                    $(this).fileinput("upload"); // 文件选择完直接调用上传方法。
                }).on("fileuploaded", function (event, data, previewId, index) {
                    //单个上传成功事件
                    console.log("fileuploaded", event, data, previewId, index)
                    var code = data.response.code;
                    if (code !== 0) {
                        $.modal.closeLoading();
                        $.modal.alertError("上传失败：" + data.response.msg);
                        return;
                    }
                    uploadCache[previewId] = data.response.tid;
                }).on('filesuccessremove', function (event, previewId, index) {
                    //上传后删除事件
                    console.log("filesuccessremove", event, previewId, index)
                    //delete cache[previewId] //bug
                    //fileArr.splice(index, 1) //bug
                    //$(this).fileinput('clear');
                    //$('[name="' + hideName + '"]').val('')
                    var tid = uploadCache[previewId];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        console.log(result)
                    }, 'json')
                    delete uploadCache[previewId]
                });
            },
            btn1: function(index, layero) {
                if (!$.validate.form('offForm')) {
                    return;
                }
                var curTids = []
                for (const uploadCacheKey in uploadCache) {
                    curTids.push(uploadCache[uploadCacheKey])
                }
                if (curTids.length == 0) {
                    $.modal.msgError("请选择上传文件");
                    return
                }
                var backAmount = $('#backAmount').val();
                var offMemo = $('#offMemo').val().trim()
                var type = $('#type').val()
                $.modal.confirm("确定提交吗？", function(){
                    $.ajax({
                        url: prefix + "/paybackProof",
                        data: {id:row.id,tids:curTids.join(','),offMemo,backAmount,type},
                        type: 'POST',
                        success: function (result) {
                            if (result.code == web_status.SUCCESS) {
                                layer.close(index);
                                $.modal.msgSuccess(result.msg);
                                $.table.refresh();
                            } else {
                                $.modal.msgError(result.msg);
                            }
                        }
                    });
                })
            },
            btn2: function(index, layero) {
                // 删除服务端已上传未使用文件
                for (const uploadCacheKey in uploadCache) {
                    var tid = uploadCache[uploadCacheKey];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        //console.log(result)
                    }, 'json')
                }
            },
            cancel: function(index, layero) {
                // 删除服务端已上传未使用文件
                for (const uploadCacheKey in uploadCache) {
                    var tid = uploadCache[uploadCacheKey];
                    $.post(ctx + 'common/deleteImageByTid', {tid: tid}, function (result) {
                        //console.log(result)
                    }, 'json')
                }
            }
        });
    }

    /*function paybackProof(id) {
        $.ajax({
            url: prefix + "/vPaybackProof?id="+id,
            cache: false,
            success: function(result) {
                if (result.code != 0) {
                    $.modal.msgError(result.msg);
                    return
                }
                let html = []
                for (let i = 0; i < result.data.proofs.length; i++) {
                    html.push('<img src="',result.data.proofs[i].filePath,'" style="width:70px;height:50px;margin:5px;" />')
                }
                layer.open({
                    type: 1,
                    area: ['600px', '300px'],
                    fix: false,
                    //不固定
                    maxmin: true,
                    shade: 0.3,
                    skin: 'proofClass',
                    title: '回款明细查看',
                    content: `
<div class="row" style="margin: 5px">
    <div class="col-sm-6 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">回款人</span>
        <div class="flex_right">
            <div class="form-control">${result.data.PAY_USER_NAME}</div>
        </div>
    </div>
    <div class="col-sm-6 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">回款时间</span>
        <div class="flex_right">
            <div class="form-control">${result.data.PAYBACK_TIME}</div>
        </div>
    </div>
    <div class="col-sm-12 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">回款凭证</span>
        <div class="flex_right imgPreview">
            ${html.join('')}
        </div>
    </div>
    <div class="col-sm-12 flex">
        <span class="flex_left" style="width:110px;line-height: 16px;">备注</span>
        <div class="flex_right">
            <div class="form-control" style="white-space: pre-wrap!important;min-height:26px;height:auto;word-wrap: break-word!important;*white-space:normal!important;">${result.data.PAYBACK_MEMO||''}</div>
        </div>
    </div>
</div>`,
                    btn: ['<i class="fa fa-remove"></i> 关闭'
                        //,'<i class="fa fa-dollar"></i> 撤销付款'
                    ],
                    // 弹层外区域关闭
                    shadeClose: true,
                    success: function(layero) {
                        layero.find('.imgPreview').viewer({
                            url: 'data-original',
                            title: false,
                            navbar: false,
                        });
                    },
                    btn1: function (index, layero) {
                        layer.close(index)
                    }
                })
            }
        })
    }*/

    let totalMarginAmount = 0;
    let totalPaidAmount = 0;
    let totalBackedAmount = 0;

    function clearTotal() {
        totalMarginAmount = 0;
        totalPaidAmount = 0;
        totalBackedAmount = 0;
    }

    function addTotal(row) {
        totalMarginAmount += row.marginAmount||0;
        totalPaidAmount += (row.paid == 1 ? row.marginAmount: 0);
        totalBackedAmount += row.backedAmount||0;
    }

    function setTotal() {
        $("#totalMarginAmount").text(totalMarginAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
        $("#totalPaidAmount").text(totalPaidAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
        $("#totalBackedAmount").text(totalBackedAmount.toLocaleString('zh', {style: 'currency', currency: 'CNY'}))
    }

    function subTotal(row) {
        totalMarginAmount -= row.marginAmount||0;
        totalPaidAmount -= (row.paid == 1 ? row.marginAmount: 0);
        totalBackedAmount -= row.backedAmount||0;
    }
    var to = 0;
    function getAmountCount() {
        if (to) {
            clearTimeout(to);
        }
        to = setTimeout(function(){
            var data = $.common.formToJSON("role-form");
            $.ajax({
                url: prefix + "/getCount",
                type: "post",
                dataType: "json",
                data: data,
                success: function(result) {
                    if (result.code == 0) {
                        var data = result.data;
                        $("#sumMarginAmount").text((data && data.sumMarginAmount||0).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        $("#sumPaidAmount").text((data && data.sumPaidAmount||0).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                        $("#sumBackedAmount").text((data && data.sumBackedAmount||0).toLocaleString('zh', {style: 'currency', currency: 'CNY'}));
                    } else {
                        $.modal.msgError(result.msg);
                    }
                }
            });
        }, 200)

    }
</script>

</body>
</html>