<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增线索客户信息')"/>
</head>
<style type="text/css">
    .bg_title{
        font-size: 15px;
        font-weight: bold;
    }
    .flex{
        display: flex;
        algin-items:center;
        just-content:space-between;
    }
    .flex_left{
        width: 113px;
        line-height: 30px;
        text-align: right;
    }
    .flex_right{
        min-width:0;
        flex:1;
        line-height: 30px;
    }
    .fcff{
        color: #ff1f1f;
    }
    .panel-default>.panel-heading {
        font-weight: bold;
        background-color: #f7fafc;
    }
    .tc{
        text-align: center;
    }
    .bor{
        border: 1px #eee solid;
    }
    .over{
        overflow: hidden;

    }
    .fl{
        float: left;
    }
    .ml10{
        margin-left: 10px;
    }
    .mt10{
        margin-top: 10px;
    }
    .table>thead>tr>th{
        background: #eff3f9 !important;
        padding: 0;
    }

</style>
<body>
<div class="form-content">
    <form id="form-add" class="form-horizontal" novalidate="novalidate">
        <input type="hidden" name="id" value="id" th:value="${cust?.id}">
        <div class="panel-group">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        基础信息
                    </h5>
                </div>
                <div class="panel-collapse">
                    <div class="panel-body" style="padding: 16px">
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 客户名称：</label>
                                    <div class="flex_right">
                                        <input class="form-control" type="text" name="custName" id="custName"
                                               maxlength="100" required autocomplete="off" th:value="${cust?.custName}"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 客户简称：</label>
                                    <div class="flex_right">
                                        <input class="form-control" type="text" name="custAbbr" id="custAbbr"
                                               autocomplete="off" maxlength="100" required th:value="${cust?.custAbbr}"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 企业类型：</label>
                                    <div class="flex_right">
                                        <select id="type" name="type" class="form-control valid"
                                                th:with="type=${@dict.getType('qy_lx')}" required>
                                            <option value=""></option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                    th:selected="${cust?.type == dict.dictValue}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left"><span class="fcff">*</span> 业务规模：</label>
                                    <div class="flex_right">
                                        <input name="scale" id="scale" class="form-control" type="text"
                                               autocomplete="off" maxlength="100" required th:value="${cust?.scale}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">物流决策人：</label>
                                    <div class="flex_right">
                                        <input name="decisionMaker" id="decisionMaker" class="form-control"
                                               autocomplete="off" maxlength="10" th:value="${cust?.decisionMaker}" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">联系电话：</label>
                                    <div class="flex_right">
                                        <input name="decisionMakerPhone" id="decisionMakerPhone" class="form-control"
                                               autocomplete="off" maxlength="20" th:value="${cust?.decisionMakerPhone}" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">对接人：</label>
                                    <div class="flex_right">
                                        <input name="contact" id="contact" class="form-control" type="text"
                                               autocomplete="off" maxlength="10" th:value="${cust?.contact}" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="flex">
                                    <label class="flex_left">联系电话：</label>
                                    <div class="flex_right">
                                        <input name="contactPhone" id="contactPhone" class="form-control" type="text"
                                               autocomplete="off" maxlength="20" th:value="${cust?.contactPhone}" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="flex">
                                <label class="flex_left"><span class="fcff">*</span> 地址：</label>
                                <div class="flex_right">
                                    <div class="col-sm-2">
                                        <select name="provinceId" id="provinceId" class="form-control valid" required aria-invalid="false"></select>
                                        <input name="provinceName" id="provinceName" class="form-control" type="hidden" th:value="${cust?.provinceName}">
                                    </div>
                                    <div class="col-sm-2">
                                        <select name="cityId" id="cityId" class="form-control valid" required aria-invalid="false"></select>
                                        <input name="cityName" id="cityName" class="form-control" type="hidden" th:value="${cust?.cityName}">
                                    </div>
                                    <div class="col-sm-2">
                                        <select name="areaId" id="areaId" class="form-control valid" required aria-invalid="false"></select>
                                        <input name="areaName" id="areaName" class="form-control" type="hidden" th:value="${cust?.areaName}">
                                    </div>
                                    <div class="col-sm-6">
                                        <input name="detailAddr" id="detailAddr" placeholder="请输入详细地址" autocomplete="off"
                                               aria-autocomplete="none" required class="form-control" type="text" maxlength="50" th:value="${cust?.detailAddr}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="flex">
                                    <label class=" flex_left">备注：</label>
                                    <div class="flex_right">
                                            <textarea name="remark" id="remark" maxlength="100" class="form-control valid"
                                                      rows="3" autocomplete="off" th:text="${cust?.remark}"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!--基础信息 end-->
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>

<div class="row" style="text-align: center">
    <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
    <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>
</div>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    $(function () {
        var provinceId = [[${cust?.provinceId}]];
        var cityId = [[${cust?.cityId}]];
        var areaId = [[${cust?.areaId}]];
        // 初始化省市区
        $.provinces.init("provinceId", "cityId", "areaId", provinceId, cityId, areaId);
        $('#provinceId').change(function () {
            $("#provinceName").val($(this).find(":selected").text());
        });
        $('#cityId').change(function () {
            $("#cityName").val($(this).find(":selected").text());
        });
        $('#areaId').change(function () {
            $("#areaName").val($(this).find(":selected").text());
        });

    });

    var prefix = ctx + "lead-cust";

    /*选择业务员*/
    /*function selectUser(){
        $.modal.open("选择业务员", ctx + "system/user/selectUser/salesUserList",'','',function (index, layero) {
            //获取整行
            var rows = layero.find('iframe')[0].contentWindow.getChecked();
            if (rows.length === 0) {
                $("#psndocName").val("");
                $("#psndoc").val("");
                $("#psncontact").val("");
                layer.close(index);
                return;
            }

            //业务员
            $("#psndocName").val(rows[0]["userName"]);
            $("#psndoc").val(rows[0]["userId"]);
            $("#psncontact").val(rows[0]["phonenumber"]);
            $("#form-client-add").validate().element($("#psndocName"));
            layer.close(index);
        });
    }*/

    /*提交表单*/
    function submitHandler() {
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function () {
                var data = $('#form-add').serializeArray()
                // 表单提交
                $.operate.saveTab(prefix + "/saveCust", data);
            })
        }
    }
</script>
</body>

</html>