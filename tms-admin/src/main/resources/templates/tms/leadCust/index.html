<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('线索客户')"/>
    <th:block th:include="include :: bootstrap-select-css" />
</head>

<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="role-form" class="form-horizontal">
                <div class="row">
                    <div class="col-md-2 col-sm-3">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <input name="custName" class="form-control" type="text"
                                       placeholder="客户名称/简称" autocomplete="off" aria-autocomplete="none" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-3">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <select name="type" class="form-control valid noselect2 selectpicker" data-none-selected-text="企业类型">
                                    <option></option>
                                    <option th:each="dict : ${qylx}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 col-sm-3">
                        <div class="form-group">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchPre()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="resetx()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-primary" onclick="$.operate.addTab()" shiro:hasPermission="leadcust:save">
                <i class="fa fa-plus"></i> 新增
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table class="text-nowrap" id="bootstrap-table" data-advanced-search="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js" />
<script th:inline="javascript">
    var prefix = ctx + "lead-cust";
    $(function(){
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                searchPre();
            }
        });
        initTable()
    })
    function searchPre() {
        var data = {};
        $.table.search('role-form', data);
    }
    var qylx = [[${qylx}]];

    function initTable() {
        var options = {
            url: prefix + "/list",
            queryParams: function (params) {
                var search = {};
                $.each($("#role-form").serializeArray(), function (i, field) {
                    search[field.name] = field.value;
                });
                search.pageSize = params.limit;
                search.pageNum = params.offset / params.limit + 1;
                search.searchValue = params.search;
                search.orderByColumn = params.sort;
                search.isAsc = params.order;
                return search;
            },
            modalName: "线索客户",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove/{id}",
            uniqueId: 'id',
            showToggle: false,
            showColumns: true,
            //fixedColumns: true,
            //fixedNumber: 4,
            clickToSelect: true,
            height: 620,
            showFooter: true,
            onPostBody: function () {
                //合并页脚
                merge_footer();
            },
            onRefresh: function (params) {
                //总数清0
                // transFeeCount = 0;
                // gotAmountCount = 0;
                // ungotAmountCount = 0;
                // sumOilApplicationAmount = 0;//开票金额
                // sumCashApplicationAmount = 0;//现金金额
            },
            onCheck: function (row, $element) {

            },
            onUncheck: function (row, $element) {

            },
            onCheckAll: function (rowsAfter) {

            },
            onUncheckAll: function () {

            },
            onPostBody: function () {
                //合并页脚
                merge_footer();
            },
            columns: [
                {
                    checkbox: true,
                    footerFormatter: function (row) {
                        return "";
                    }
                },
                {
                    title: '操作', field: 'status', width: '80px',
                    formatter: function(value, row, index) {
                        var actions = [];
                        /*[# shiro:hasPermission="leadcust:save"]*/
                        actions.push('<a class="fa fa-edit" href="javascript:$.operate.editTab(\'' + row.id + '\')" title="编辑"></a>')
                        /*[/]*/
                        /*[# shiro:hasPermission="leadcust:del"]*/
                        actions.push(' <a class="fa fa-remove" style="color:red" href="javascript:$.operate.remove(\'' + row.id + '\')" title="删除"></a>')
                        /*[/]*/
                        /*[# shiro:hasPermission="bb:save"]*/
                        actions.push(' <a class="fa fa-star" href="javascript:addbb(\'' + row.id + '\')" title="新增投标保证金"></a>')
                        /*[/]*/
                        return actions.join('');
                    }
                },
                {field: 'custName', title: '客户名称', width: '160px'},
                {field: 'custAbbr', title: '客户简称', width: '70px'},
                {field: 'type', title: '企业类型', width: '70px',formatter(value, row, index){
                    return $.table.selectDictLabel(qylx, value);
                }},
                {field: 'scale', title: '业务规模', width: '70px'},
                {field: 'detailAddr', title: '地址', width: '170px',formatter(value,row,index){
                    return row.provinceName + row.cityName + row.areaName + row.detailAddr;
                }}
                //{field: 'hasMargin', title: '是否有投标保障金', width: '70px',formatter(value, row, index){
                //    return $.table.selectDictLabel([{dictValue:0,dictLabel:'否'},{dictValue:1,dictLabel:'是'}], value);
                //}}
            ]
        };

        $.table.init(options);
        $(document).keyup(function (e) {
            var key = e.which;
            if (key == 13) {
                //查询方法
                $.table.search();
            }
        });
    }

    /**
     * 合并页脚
     */
    function merge_footer() {
        var footer_tbody = $('.fixed-table-footer table tbody');
        var footer_tr = footer_tbody.find('>tr');
        var footer_td = footer_tr.find('>td');
        var footer_td_1 = footer_td.eq(0);
        //除了第一列其他都隐藏
        for (var i = 1; i < footer_td.length; i++) {
            footer_td.eq(i).hide();
        }
        footer_td_1.attr('colspan', 1).show();
    }

    function addbb(id) {
        $.modal.openTab("添加投标保证金", ctx + "bid-bonds/add?leadCustId=" + id);
    }

</script>

</body>
</html>