package com.ruoyi.tms.controller.client;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtilNew;
import com.ruoyi.framework.interceptor.annotation.RepeatSubmit;
import com.ruoyi.framework.web.service.DictService;
import com.ruoyi.tms.constant.BillingMethod;
import com.ruoyi.tms.domain.basic.Address;
import com.ruoyi.tms.domain.basic.Carrier;
import com.ruoyi.tms.domain.client.AutoDispatchSection;
import com.ruoyi.tms.domain.invoice.InvPackGoods;
import com.ruoyi.tms.domain.invoice.Invoice;
import com.ruoyi.tms.domain.invoice.MultipleShippingAddress;
import com.ruoyi.tms.domain.segment.SegPackGoods;
import com.ruoyi.tms.domain.segment.Segment;
import com.ruoyi.tms.service.basic.IAddressService;
import com.ruoyi.tms.service.basic.ICarrierService;
import com.ruoyi.tms.service.client.ClientService;
import com.ruoyi.tms.service.client.IAutoDispatchConfigService;
import com.ruoyi.tms.service.client.IAutoDispatchSectionService;
import com.ruoyi.tms.service.invoice.IInvPackGoodsService;
import com.ruoyi.tms.service.invoice.IInvoiceService;
import com.ruoyi.tms.service.invoice.IMultipleShippingAddressService;
import com.ruoyi.tms.service.segment.ISegPackGoodsService;
import com.ruoyi.tms.service.segment.ISegmentService;
import com.ruoyi.tms.vo.client.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AutoDispatchConfigController
 *
 * <AUTHOR> zjx
 * @version : v1.0.0
 * @date : 2023-08-10 15:18
 */
@Controller
@RequestMapping("/autoDispatchConfig")
public class AutoDispatchConfigController extends BaseController {

    @Resource
    private IAutoDispatchConfigService autoDispatchConfigService;
    @Resource
    private IAutoDispatchSectionService autoDispatchSectionService;
    @Resource
    private ClientService clientService;
    @Resource
    private IAddressService addressService;
    @Resource
    private DictService dictService;
    @Resource
    private IInvoiceService invoiceService;
    @Resource
    private ISegmentService segmentService;
    @Autowired
    private ISegPackGoodsService segPackGoodsService;
    @Autowired
    private IInvPackGoodsService invPackGoodsService;
    @Autowired
    private IMultipleShippingAddressService multipleShippingAddressService;
    @Resource
    private ICarrierService carrierService;


    /**
     * 设置自动调度
     */
    @RequiresPermissions("client:autoDispatch:list")
    @GetMapping(value = "/auto_dispatch")
    public String autoDispatch(@RequestParam("customerId") String customerId, ModelMap map) {
        map.put("customerId", customerId);

        ClientPopupVO clientPopupVO = clientService.selectClientById(customerId);
        map.put("client", clientPopupVO);

        String autoSubsectionAddrId = clientPopupVO.getAutoSubsectionAddrId();

        map.put("subsectionAddr", null);

        if (StringUtils.isNotEmpty(autoSubsectionAddrId)) {
            Address address = addressService.selectAddressById(autoSubsectionAddrId);
            map.put("subsectionAddr", address.getAddrName());
        }

        map.put("billingMethod", BillingMethod.getAllToMap());

        return "tms/client/autoDispatch/auto_dispatch";
    }

    /**
     * 获取配置信息
     *
     * @param
     * @return
     */
    @RequiresPermissions("client:autoDispatch:list")
    @PostMapping("/getConfig")
    @ResponseBody
    public AjaxResult getConfig(AutoDispatchSearchVO autoDispatchSearchVO) {
        startPage();
        AutoDispatchVO data = autoDispatchConfigService.selectListByCustomerIdAndType(autoDispatchSearchVO);
        return AjaxResult.success(data);
    }

    /**
     * 获取配置信息
     *
     * @param sameId sameId
     * @return
     */
    @RequiresPermissions("client:autoDispatch:list")
    @PostMapping("/getSection")
    @ResponseBody
    public AjaxResult getSection(@RequestParam("sameId") String sameId) {
        List<AutoDispatchSection> autoDispatchSections = autoDispatchSectionService.selectBySameId(sameId);
        return AjaxResult.success(autoDispatchSections);
    }

    /**
     * 设置自动调度
     */
    @RepeatSubmit
    @RequiresPermissions("client:autoDispatch:edit")
    @PostMapping("/addConfig")
    @ResponseBody
    public AjaxResult addConfig(@RequestBody AutoDispatchVO autoDispatchVO) {
        return autoDispatchConfigService.addConfig(autoDispatchVO);
    }

    /**
     * 拷贝配置
     */
    @RepeatSubmit
    @RequiresPermissions("client:autoDispatch:copyConfig")
    @PostMapping("/copyConfig")
    @ResponseBody
    public AjaxResult copyConfig(String sourceCustId, String targetCustId, String deliAid) {

        return autoDispatchConfigService.copyConfig(sourceCustId, targetCustId, deliAid);
    }

    /**
     * 添加详情价格
     */
    @RequiresPermissions("client:autoDispatch:edit")
    @PostMapping("/addSection")
    @ResponseBody
    public AjaxResult addSection(@RequestBody AutoDispatchVO autoDispatchVO) {
        return autoDispatchSectionService.addSection(autoDispatchVO);
    }

    /**
     * 修改
     */
    @RequiresPermissions("client:autoDispatch:edit")
    @PostMapping("/editConfig")
    @ResponseBody
    public AjaxResult editConfig(@RequestBody AutoDispatchVO autoDispatchVO) {
        return autoDispatchConfigService.editConfig(autoDispatchVO);
    }



    /**
     * 删除数据
     *
     * @param
     * @return
     */
    @RequiresPermissions("client:autoDispatch:edit")
    @PostMapping("/rmConfig")
    @ResponseBody
    public AjaxResult rmConfig(@RequestParam("sameId") String sameIds) {
        return autoDispatchConfigService.rmConfigBySameIds(sameIds);
    }

    /**
     * 删除数据
     *
     * @param
     * @return
     */
    @RequiresPermissions("client:autoDispatch:edit")
    @PostMapping("/rmSection")
    @ResponseBody
    public AjaxResult rmSection(@RequestParam("sectionIds") String sectionIds) {
        return autoDispatchConfigService.rmSectionByIds(sectionIds);
    }

    /**
     * 切换配置   弃用
     *
     * @param
     * @return
     */
    @RequiresPermissions("client:autoDispatch:edit")
    @PostMapping("/switchConfig")
    @ResponseBody
    public AjaxResult switchConfig(@RequestBody AutoDispatchVO autoDispatchVO) {
        return autoDispatchConfigService.switchConfig(autoDispatchVO);
    }

    /**
     * 切换配置
     *
     * @param
     * @return
     */
    @RequiresPermissions("client:autoDispatch:edit")
    @PostMapping("/setSubsectionAddr")
    @ResponseBody
    public AjaxResult setSubsectionAddr(@RequestBody AutoDispatchVO autoDispatchVO) {
        return autoDispatchConfigService.setSubsectionAddr(autoDispatchVO);
    }

    /**
     * 修改 是否可以录入送货费
     *
     * @param
     * @return
     */
    @RequiresPermissions("client:autoDispatch:edit")
    @PostMapping("/changeAutoDispatchDeliFee")
    @ResponseBody
    public AjaxResult changeAutoDispatchDeliFee(@RequestBody AutoDispatchVO autoDispatchVO) {
        return autoDispatchConfigService.changeAutoDispatchDeliFee(autoDispatchVO);
    }

    /**
     * 修改 是否可以录入提货费
     *
     * @param
     * @return
     */
    @RequiresPermissions("client:autoDispatch:edit")
    @PostMapping("/changeAutoDispatchPickUpFee")
    @ResponseBody
    public AjaxResult changeAutoDispatchPickUpFee(@RequestBody AutoDispatchVO autoDispatchVO) {
        return autoDispatchConfigService.changeAutoDispatchPickUpFee(autoDispatchVO);
    }


    /**
     * 导入
     *
     * @param file 文件对象
     * @return 结果
     */
    @RequiresPermissions("client:autoDispatch:edit")
    @PostMapping("/import")
    @ResponseBody
    public AjaxResult importConfig(@RequestParam MultipartFile file, String customerId) {
        return autoDispatchConfigService.importConfig(file, customerId);
    }

    /**
     * 导出
     *
     * @param
     * @return 结果
     */
    @RequiresPermissions("client:autoDispatch:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(AutoDispatchSearchVO autoDispatchSearchVO) {
        //根据查询条件导出
        Map<String, Object> dataBase = new HashMap<>();

        dataBase.put("billingType", dictService.getDictValueLabelMap("billing_type"));

        Map<String, String> billingMethod = BillingMethod.getAllToMap()
                .stream().collect(Collectors.toMap(x -> String.valueOf(x.get("value")), a -> String.valueOf(a.get("context"))));
        dataBase.put("billingMethod",billingMethod);

        List<AutoDispatchExportVO> list = autoDispatchConfigService.exportList(autoDispatchSearchVO);

        ExcelUtilNew<AutoDispatchExportVO> util = new ExcelUtilNew<>(AutoDispatchExportVO.class);
        return util.exportExcel(list, "客户自动调度配置", dataBase);
    }


    /**
     * 获取配置信息
     *
     * @param   billingMethod   计价方式
     * @param   id              发货单id或者运段id
     * @param   type            类别 0发货单  1运段
     * @return
     */
    @GetMapping("/getPrice")
    @ResponseBody
    public AjaxResult getPrice(String billingMethod, String id, Integer type) {
        Map<String, Object> price = new HashMap<>();
        if (type != null && type == 1) {
            Segment segment = segmentService.selectSegmentById(id);
            Invoice invoice = invoiceService.selectInvoiceById(segment.getInvoiceId());

            List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressService
                    .selectAddrAndGoodsBySegmentId(segment.getSegmentId());

            //获取提货区id
            List<String> deliAreaIdList = multipleShippingAddresses.stream()
                    .filter(x -> x.getAddressType() == 0)
                    .map(MultipleShippingAddress::getAreaId)
                    .collect(Collectors.toList());

            //获取到货区id
            List<String> arriAreaIdList = new ArrayList<>();
            List<String> arriAddrNameList = new ArrayList<>();

            for(MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses){
                if(multipleShippingAddress.getAddressType() == 1){
//                    if(multipleShippingAddress.getIsChangeAddress() != null && multipleShippingAddress.getIsChangeAddress() == 1){
//                        arriAreaIdList.add(multipleShippingAddress.getCaArriAreaId());
//                        arriAddrNameList.add(multipleShippingAddress.getCaArriAddrName());
//                    }else{
//                        arriAreaIdList.add(multipleShippingAddress.getAreaId());
//                        arriAddrNameList.add(multipleShippingAddress.getAddrName());
//                    }
                    //改为取 该送后地址
                    arriAreaIdList.add(multipleShippingAddress.getAreaId());
                    arriAddrNameList.add(multipleShippingAddress.getAddrName());

                }
            }

            String goodsName = null;
            List<SegPackGoods> segPackGoods = segPackGoodsService.selectSegPackGoodsBySegmentId(segment.getSegmentId());
            List<String> goodsNames = segPackGoods.stream().map(SegPackGoods::getGoodsName).distinct().collect(Collectors.toList());
            if (goodsNames.size() == 1) {
                goodsName = goodsNames.get(0);
            }

            price = autoDispatchConfigService.getPrice(invoice.getCustomerId()
                    , deliAreaIdList, arriAreaIdList, billingMethod
                    , invoice.getCarLen(), invoice.getCarType(), invoice.getUnitPrice(), invoice.getCostAmount()
                    , segment.getNumCount() , segment.getWeightCount(), segment.getVolumeCount(), invoice.getMileage()
                    , null , arriAddrNameList, null, goodsName, null
                    , invoice.getOtherFeeType(), invoice.getOtherFee(),invoice.getTransCode(),invoice.getIsRoundTrip());

        } else {
            Invoice invoice = invoiceService.selectInvoiceById(id);

            List<MultipleShippingAddress> multipleShippingAddresses = multipleShippingAddressService
                    .selectAddrAndGoodsByInvoiceId(invoice.getInvoiceId());

            //获取提货区id
            List<String> deliAreaIdList = multipleShippingAddresses.stream()
                    .filter(x -> x.getAddressType() == 0)
                    .map(MultipleShippingAddress::getAreaId)
                    .collect(Collectors.toList());

            //获取到货区id
            List<String> arriAreaIdList = new ArrayList<>();
            List<String> arriAddrNameList = new ArrayList<>();

            for(MultipleShippingAddress multipleShippingAddress : multipleShippingAddresses){
                if(multipleShippingAddress.getAddressType() == 1){
//                    if(multipleShippingAddress.getIsChangeAddress() != null && multipleShippingAddress.getIsChangeAddress() == 1){
//                        arriAreaIdList.add(multipleShippingAddress.getCaArriAreaId());
//                        arriAddrNameList.add(multipleShippingAddress.getCaArriAddrName());
//                    }else{
//                        arriAreaIdList.add(multipleShippingAddress.getAreaId());
//                        arriAddrNameList.add(multipleShippingAddress.getAddrName());
//                    }
                    //改为取 该送后地址
                    arriAreaIdList.add(multipleShippingAddress.getAreaId());
                    arriAddrNameList.add(multipleShippingAddress.getAddrName());

                }
            }

            String goodsName = null;
            List<InvPackGoods> invPackGoods = invPackGoodsService.selectByInvoiceId(id);
            List<String> goodsNames = invPackGoods.stream().map(InvPackGoods::getGoodsName).distinct().collect(Collectors.toList());
            if (goodsNames.size() == 1) {
                goodsName = goodsNames.get(0);
            }

            price = autoDispatchConfigService.getPrice(invoice.getCustomerId()
                    , deliAreaIdList, arriAreaIdList, billingMethod
                    , invoice.getCarLen(), invoice.getCarType(), invoice.getUnitPrice(), invoice.getCostAmount()
                    , invoice.getNumCount() , invoice.getWeightCount(), invoice.getVolumeCount(), invoice.getMileage()
                    , null , arriAddrNameList, null, goodsName, null
                    , invoice.getOtherFeeType(), invoice.getOtherFee(),invoice.getTransCode(),invoice.getIsRoundTrip());

        }

        if ("1".equals(price.get("type"))) {
            List<Map<String, String>> dataList = (List<Map<String, String>>)price.get("dataList");
            for (Map<String, String> stringStringMap : dataList) {
                if (StringUtils.isNotEmpty(stringStringMap.get("carrierId"))) {
                    Carrier carrier = carrierService.selectCarrierById(stringStringMap.get("carrierId"));
                    stringStringMap.put("requiredFlag", Convert.toStr(carrier.getRequiredFlag()));
                }
            }
        }
        return AjaxResult.success(price);
    }


    /**
     *
     */
    @RequiresPermissions("client:autoDispatch:edit")
    @PostMapping("/rmAll")
    @ResponseBody
    public AjaxResult rmAll(String customerId) {
        return autoDispatchConfigService.rmAll(customerId);
    }


    /**
     * 设置自动调度
     */
    @RequiresPermissions("client:autoDispatchAnalyze:list")
    @GetMapping(value = "/auto_dispatch/analyze")
    public String analyze(ModelMap map) {
        map.put("billingMethod",BillingMethod.getAllToMap());
        return "tms/client/autoDispatch/analyze";
    }

    /**
     * 获取配置信息
     *
     * @param
     * @return
     */
    @RequiresPermissions("client:autoDispatchAnalyze:list")
    @PostMapping("/auto_dispatch/analyze")
    @ResponseBody
    public TableDataInfo analyze(@RequestBody  AutoDispatchAnalyzeVO autoDispatchAnalyzeVO) {
        startPage();
        List<AutoDispatchAnalyzeVO> data = autoDispatchConfigService.selectAnalyzeList(autoDispatchAnalyzeVO);
        return getDataTable(data);
    }

    /**
     * 导出
     *
     * @param
     * @return 结果
     */
    @RequiresPermissions("client:autoDispatchAnalyze:list")
    @PostMapping("/auto_dispatch/analyze/export")
    @ResponseBody
    public AjaxResult analyzeExport(AutoDispatchAnalyzeVO autoDispatchAnalyzeVO) {
        //根据查询条件导出
        Map<String, Object> dataBase = new HashMap<>();

        dataBase.put("billingType", dictService.getDictValueLabelMap("billing_type"));

        Map<String, String> billingMethod = BillingMethod.getAllToMap()
                .stream().collect(Collectors.toMap(x -> String.valueOf(x.get("value")), a -> String.valueOf(a.get("context"))));
        dataBase.put("billingMethod",billingMethod);

        List<AutoDispatchAnalyzeVO> list = autoDispatchConfigService.selectAnalyzeList(autoDispatchAnalyzeVO);

        list.forEach(vo -> {
            String sectionAmount = vo.getSectionAmount();
            if (sectionAmount != null) {
                vo.setSectionAmount(sectionAmount.replace(",", "\r\n"));
            }
        });

        list = list.stream()
                .peek(x -> {
                    if (x.getOilRatio() != null) {
                        String oilRatioText = "";
                        //油卡类型 1预付油卡  3到付油卡  5回付油卡
                        if ("1".equals(x.getOilCostType())) {
                            oilRatioText = "预付油卡";
                        } else if ("3".equals(x.getOilCostType())) {
                            oilRatioText = "到付油卡";
                        } else if ("5".equals(x.getOilCostType())) {
                            oilRatioText = "回付油卡";
                        }
                        String oilType = x.getOilType() == 0 ? "%" : "元";
                        double oilRatio = x.getOilType() == 0 ? NumberUtil.mul(x.getOilRatio().doubleValue(), 100) : x.getOilRatio();
                        oilRatioText = oilRatioText + "：" + oilRatio + oilType;

                        x.setOilRatioText(oilRatioText);
                    }

                })
                .collect(Collectors.toList());

        ExcelUtilNew<AutoDispatchAnalyzeVO> util = new ExcelUtilNew<>(AutoDispatchAnalyzeVO.class);
        return util.exportExcel(list, "承运商自动调度分析", dataBase);
    }

}
